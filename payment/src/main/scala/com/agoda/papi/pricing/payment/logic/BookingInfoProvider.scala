package com.agoda.papi.pricing.payment.logic

import com.agoda.papi.constants.CurrencyCode
import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.pricing.discounting.models.response.Downlift
import com.agoda.papi.pricing.models.ExchangeRate
import com.agoda.papi.pricing.payment.api.models.input.{ExchangeRateWithPrecision, PaymentInformation}
import com.agoda.papi.pricing.payment.constants.Constants.defaultPrecision
import com.agoda.papi.pricing.payment.utils.ConvertCurrencyAndRounding.convertedPaymentAmount
import com.agoda.papi.pricing.payment.utils.Implicits.DoubleRounding
import com.agoda.papi.pricing.services.ExchangeDataService
import com.typesafe.scalalogging.LazyLogging
import model.ItemBreakdownType
import models.db.{Currency, PaymentOptions, PointMaxData, PriusOutput}
import models.internal.PaymentResult
import models.pricing.{
  AccountingEntity,
  Cashback,
  FireDrillContract,
  GiftCard,
  RateCategory,
  ExchangeRate => PricingExchangeRate,
}
import models.starfruit._
import models.utils.{ItemBreakdownHelper, ItemBreakdownHelperFilters}
import models.whitelabel.WhitelabelSetting
import com.agoda.papi.pricing.payment.api.models.input.PaymentFlowContext
import models.consts.ABTest
import models.flow.Variant

trait BookingInfoProvider {

  def getBookingInfo(
    searchId: String,
    mandatoryItemBreakdowns: Map[Currency, ItemBreakdownType],
    nonMandatoryItemBreakdowns: Map[Currency, ItemBreakdownType],
    accountingEntity: Option[AccountingEntity],
    chargeDiscount: Option[Double],
    displayDiscount: Option[Double],
    rateCategory: Option[RateCategory],
    downlift: Option[Downlift],
    isFireDrill: Boolean,
    fireDrillContract: Option[FireDrillContract],
    numOfRooms: Int,
    numOfExtraBeds: Int,
    lengthOfStay: Int,
    exchangeRateByCurrency: Map[Currency, ExchangeRateWithPrecision],
    localCurrency: Currency,
    requestCurrency: Currency,
    paymentCurrency: Option[Currency],
    roomExchangeRate: Option[ExchangeRate],
    priusOutputInUsd: Option[PriusOutput],
    pointMaxData: Option[PointMaxData],
    giftCard: Option[GiftCard],
    bookingResult: Map[Currency, BookingResult],
    paymentInformation: Option[PaymentInformation],
    paymentFeatureInput: Map[Currency, Map[String, (PaymentFeature, Option[EBEPaymentAmount])]],
    paymentOption: PaymentOptions,
    paymentModel: PaymentModel,
    cashback: Option[Cashback],
    cashbackUsdAmountFromRewards: Option[Double],
    paymentResult: Map[Currency, PaymentResult],
    sellAllInByCurrency: Map[Currency, Double],
    upliftExchangeRate: Option[ExchangeRate],
    isFit: Option[Boolean],
    isMultipleRoomAssignmentPrice: Boolean,
    isMigrateUsdSellAllInToEbe: Boolean,
    isFixUpliftExchangeForM150: Boolean,
    paymentMethodId: Option[Int] = None,
    whitelabelSetting: WhitelabelSetting,
    ctx: PaymentFlowContext,
  )(implicit exchangeDataService: ExchangeDataService): BookingInfo
}

trait BookingInfoProviderImpl extends BookingInfoProvider with ItemBreakdownHelperFilters with LazyLogging {

  override def getBookingInfo(
    searchId: String,
    mandatoryItemBreakdowns: Map[Currency, ItemBreakdownType],
    nonMandatoryItemBreakdowns: Map[Currency, ItemBreakdownType],
    accountingEntity: Option[AccountingEntity],
    chargeDiscount: Option[Double],
    displayDiscount: Option[Double],
    rateCategory: Option[RateCategory],
    downlift: Option[Downlift],
    isFireDrill: Boolean,
    fireDrillContract: Option[FireDrillContract],
    numOfRooms: Int,
    numOfExtraBeds: Int,
    lengthOfStay: Int,
    exchangeRateByCurrency: Map[Currency, ExchangeRateWithPrecision],
    localCurrency: Currency,
    requestCurrency: Currency,
    paymentCurrency: Option[Currency],
    roomExchangeRate: Option[ExchangeRate],
    priusOutputInUsd: Option[PriusOutput],
    pointMaxData: Option[PointMaxData],
    giftCard: Option[GiftCard],
    bookingResult: Map[Currency, BookingResult],
    paymentInformation: Option[PaymentInformation],
    paymentFeatureInput: Map[Currency, Map[String, (PaymentFeature, Option[EBEPaymentAmount])]],
    paymentOption: PaymentOptions,
    paymentModel: PaymentModel,
    cashback: Option[Cashback],
    cashbackUsdAmountFromRewards: Option[Double],
    paymentResult: Map[Currency, PaymentResult],
    sellAllInByCurrency: Map[Currency, Double],
    upliftExchangeRate: Option[ExchangeRate],
    isFit: Option[Boolean],
    isMultipleRoomAssignmentPrice: Boolean,
    isMigrateUsdSellAllInToEbe: Boolean,
    isFixUpliftExchangeForM150: Boolean,
    paymentMethodId: Option[Int] = None,
    whitelabelSetting: WhitelabelSetting,
    ctx: PaymentFlowContext,
  )(implicit exchangeDataService: ExchangeDataService): BookingInfo = {

    val bookingItems = bookingResult.flatMap { case (currency, _) =>
      Map(currency -> mandatoryItemBreakdowns(currency).left.getOrElse(Map.empty))
    }

    val priceTemplateId = downlift.map(_.id)

    val pricingExchangeRateByCurrency = exchangeRateByCurrency.map { case (curr, exchangeRateWithPrecision) =>
      curr -> exchangeRateWithPrecision.exchange.map(toPricingExchangeRate)
    }

    val bookingResultInUsd = bookingResult.get(CurrencyCode.USD)
    val bookingResultInRequest = bookingResult.get(requestCurrency)

    val upliftExchange = getUpliftExchange(bookingResultInUsd, paymentInformation, roomExchangeRate)

    val ebePaymentAmount = getFXIPaymentFeature(paymentFeatureInput, paymentCurrency).headOption.flatMap {
      case (_, (_, ebe)) => ebe
    }
    val paymentFeature = getFXIPaymentFeature(paymentFeatureInput, paymentCurrency).headOption.map {
      case (_, (paymentFeature, _)) => paymentFeature
    }
    val paymentAmountWithoutUplift = bookingResultInRequest.map(_.paymentAmountWithOutUplift).getOrElse(0d)
    val rawPaymentAmount = ebePaymentAmount.map(_.uplift).getOrElse(paymentAmountWithoutUplift)
    val paymentUsdAmount = bookingResultInUsd.map(_.paymentAmountWithOutUplift).getOrElse(0d)

    // Check if any of the LINE Pay experiments is enabled (B side)
    val isLinePayTwdExperimentEnabled = ABTest.PC_7098_ENABLE_LINEPAY
      .exists(experiment => ctx.determineVariant(experiment) == Variant.B)

    // Apply payment method decimal precision override using whitelabel configuration
    val originalRequestDecimalPlaces = roomExchangeRate.map(_.numReqDecimal).getOrElse(defaultPrecision)
    val overriddenRequestDecimalPlaces =
      if (isLinePayTwdExperimentEnabled) {
        whitelabelSetting.currencyDecimalOverrideConfig.getDecimalPrecision(requestCurrency.toString,
                                                                            paymentMethodId,
                                                                            originalRequestDecimalPlaces)
      } else {
        originalRequestDecimalPlaces
      }

    // Apply rounding to payment amount if decimal override is configured and experiment is enabled
    val paymentAmount =
      if (isLinePayTwdExperimentEnabled &&
        whitelabelSetting.currencyDecimalOverrideConfig.hasDecimalOverride(requestCurrency.toString, paymentMethodId)) {
        rawPaymentAmount.roundAt(overriddenRequestDecimalPlaces)
      } else {
        rawPaymentAmount
      }

    validatePaymentAmountConversion(
      searchId = searchId,
      lengthOfStay = lengthOfStay,
      numberOfRoom = numOfRooms,
      paymentUpliftExchangeRate = paymentFeature.flatMap(_.upliftSiteExchangeRate),
      paymentInfoUplift = upliftExchange,
      roomExchangeRate = roomExchangeRate.map(_.usdToRequestDouble),
      paymentAmount = paymentAmount,
      paymentAmountUSD = paymentUsdAmount,
      paymentCurrency = paymentCurrency.getOrElse("none"),
    )

    val precision = exchangeRateByCurrency.get(localCurrency).map(_.precision).getOrElse(defaultPrecision)
    val (payToAgodaPerBook, payToHotelPerBook) = ItemBreakdownHelper.getPaymentBreakdown(
      ItemBreakdownHelper.finalizeEbeItems(mandatoryItemBreakdowns.getOrElse(localCurrency, Left(Map.empty))),
      ItemBreakdownHelper.finalizeEbeItems(nonMandatoryItemBreakdowns.getOrElse(localCurrency, Left(Map.empty))),
      paymentOption,
      paymentModel,
      precision,
    )

    val localCurrencyUpliftAmount = getM150LocalPaymentAmount(
      payToAgodaPerBook,
      payToHotelPerBook,
      exchangeRateByCurrency.getOrElse(localCurrency, emptyExchangeRateWithPrecision).exchange,
      upliftExchangeRate,
      roomExchangeRate,
      paymentCurrency.getOrElse(requestCurrency),
      isFixUpliftExchangeForM150,
    )

    val mandatoryItemsInUsd = getEbeItemsForCurrency(mandatoryItemBreakdowns, CurrencyCode.USD)
    val nonMandatoryItemsInUsd = getEbeItemsForCurrency(nonMandatoryItemBreakdowns, CurrencyCode.USD)
    val (usdGiftCardMoneyBackAmount, usdCashbackMoneyBackAmount) = getGiftCardAndCashbackMoneyBackAmount(
      mandatoryItemsInUsd,
      nonMandatoryItemsInUsd,
      giftCard,
      cashback,
      numOfRooms,
      isFit,
      isMultipleRoomAssignmentPrice,
      isMigrateUsdSellAllInToEbe,
    )

    val loyaltyAmountInRequest = getAmountWithFallback(bookingResultInRequest,
                                                       paymentFeature,
                                                       bookingResultInUsd.flatMap(_.loyaltyAmount),
                                                       bookingResultInRequest.flatMap(_.loyaltyAmount),
                                                       defaultPrecision)

    val paymentAmountPrecision = ebePaymentAmount.flatMap(_.precision).getOrElse(defaultPrecision)
    val cashbackAmountInUsd = bookingResultInUsd.flatMap(_.cashbackAmount)
    val cashbackAmountInPaymentCurrency = getAmountWithFallback(
      bookingResultInRequest,
      paymentFeature,
      cashbackAmountInUsd,
      bookingResultInRequest.flatMap(_.cashbackAmount),
      paymentAmountPrecision,
    )

    val paymentAmountBeforeLoyaltyInUsd = paymentResult.get(CurrencyCode.USD).flatMap(_.paymentAmountBeforeLoyalty)
    val paymentAmountBeforeLoyalty = getAmountWithFallback(
      bookingResultInRequest,
      paymentFeature,
      paymentAmountBeforeLoyaltyInUsd,
      paymentResult.get(paymentCurrency.getOrElse(requestCurrency)).flatMap(_.paymentAmountBeforeLoyalty),
      paymentAmountPrecision,
    )

    BookingInfo(
      accountingEntity = accountingEntity,
      chargeDiscount = chargeDiscount.getOrElse(0d),
      displayDiscount = displayDiscount.getOrElse(0d),
      priceTemplateId = priceTemplateId,
      rateCategory = rateCategory,
      isAdvanceGuarantee = isFireDrill,
      fireDrillContract = fireDrillContract,
      numberOfRoom = numOfRooms,
      reqExchange = pricingExchangeRateByCurrency.getOrElse(localCurrency, None),
      usdExchange = pricingExchangeRateByCurrency.getOrElse(requestCurrency, None),
      roomExchange = roomExchangeRate.map(rate =>
        toPricingExchangeRate(rate, requestCurrency, paymentMethodId, whitelabelSetting, isLinePayTwdExperimentEnabled)),
      items = bookingItems,
      priusOutput = priusOutputInUsd,
      pointMaxData = pointMaxData,
      usdSellAllIn = sellAllInByCurrency.get(CurrencyCode.USD),
      dfGiftCard = giftCard,
      upliftExchangeRate = upliftExchange,
      paymentAmount = paymentAmount,
      paymentUsdAmount = paymentUsdAmount,
      discountUsdAmount = bookingResultInUsd.flatMap(_.discountAmount).getOrElse(0d),
      downliftAmount = bookingResultInRequest.map(_.downliftAmount).getOrElse(0d),
      downliftUsdAmount = bookingResultInUsd.map(_.downliftAmount).getOrElse(0d),
      loyaltyAmount = loyaltyAmountInRequest,
      loyaltyAmountInUsd = bookingResultInUsd.flatMap(_.loyaltyAmount),
      numberOfExtraBed = numOfExtraBeds,
      dfCashback = cashback,
      externalLoyaltyInfo = bookingResultInUsd.flatMap(_.externalLoyaltyInfo),
      localCurrencyUpliftAmount = localCurrencyUpliftAmount,
      usdGiftCardMoneyBackAmount = usdGiftCardMoneyBackAmount,
      usdCashbackMoneyBackAmount = usdCashbackMoneyBackAmount,
      cashbackPayment = getCashbackPayment(cashbackAmountInUsd, cashbackAmountInPaymentCurrency),
      paymentAmountBeforeLoyalty = paymentAmountBeforeLoyalty,
      paymentUsdAmountBeforeLoyalty = paymentAmountBeforeLoyaltyInUsd,
      cashbackUsdAmount = cashbackUsdAmountFromRewards,
    )
  }

  private def emptyExchangeRateWithPrecision = ExchangeRateWithPrecision(None, defaultPrecision)

  private def getEbeItemsForCurrency(itemBreakdownByCurrency: Map[Currency, ItemBreakdownType],
                                     currency: Currency): Seq[ItemBreakdown] =
    ItemBreakdownHelper.finalizeEbeItems(itemBreakdownByCurrency.getOrElse(currency, Left(Map.empty)))

  // Backward compatibility version - uses default behavior (no decimal override)
  private[payment] def toPricingExchangeRate(exchangeRate: ExchangeRate): PricingExchangeRate = PricingExchangeRate(
    local = exchangeRate.fromCurrency,
    request = exchangeRate.toCurrency,
    toUsd = exchangeRate.toUsd,
    toRequest = exchangeRate.toRequest,
    numLocalDecimal = exchangeRate.numLocalDecimal,
    numReqDecimal = exchangeRate.numReqDecimal,
    numUsdDecimal = exchangeRate.numUsdDecimal,
  )

  // Full version with decimal precision override support
  private[payment] def toPricingExchangeRate(exchangeRate: ExchangeRate,
                                             requestCurrency: Currency,
                                             paymentMethodId: Option[Int],
                                             whitelabelSetting: WhitelabelSetting,
                                             isLinePayTwdExperimentEnabled: Boolean): PricingExchangeRate = {

    val originalRequestDecimalPlaces = exchangeRate.numReqDecimal
    val overriddenRequestDecimalPlaces =
      if (isLinePayTwdExperimentEnabled) {
        whitelabelSetting.currencyDecimalOverrideConfig.getDecimalPrecision(
          requestCurrency.toString,
          paymentMethodId,
          originalRequestDecimalPlaces,
        )
      } else {
        originalRequestDecimalPlaces
      }

    val overriddenExchangeRate = ExchangeRate(
      fromCurrency = exchangeRate.fromCurrency,
      toCurrency = exchangeRate.toCurrency,
      toUsd = exchangeRate.toUsd,
      toRequest = exchangeRate.toRequest,
      numLocalDecimal = exchangeRate.numLocalDecimal,
      numReqDecimal = overriddenRequestDecimalPlaces,
      numUsdDecimal = exchangeRate.numUsdDecimal,
    )

    toPricingExchangeRate(overriddenExchangeRate)
  }

  private[payment] def getUpliftExchange(
    bookingResultInUsd: Option[BookingResult],
    paymentInformation: Option[PaymentInformation],
    siteExchange: Option[ExchangeRate]): Option[Double] = bookingResultInUsd.map { booking =>
    val creditCardUpliftExchangeRate = paymentInformation.flatMap(_.creditCardUpliftExchangeRate)
    val siteExchangeRate = siteExchange.map(_.usdToRequestDouble).getOrElse(0d)
    val defaultUpliftExchangeRate = creditCardUpliftExchangeRate.map(_.usdToRequestDouble).getOrElse(siteExchangeRate)
    booking.upliftExchangeRate.getOrElse(defaultUpliftExchangeRate)
  }

  private[payment] def getFXIPaymentFeature(
    paymentFeatureInput: Map[String, Map[String, (PaymentFeature, Option[EBEPaymentAmount])]],
    paymentCurrency: Option[String] = None): Map[String, (PaymentFeature, Option[EBEPaymentAmount])] = {
    val paymentFeature = paymentFeatureInput.getOrElse(CurrencyCode.USD, Map.empty)
    paymentCurrency
      .map(currency => paymentFeature.filter { case (k, _) => k.equalsIgnoreCase(currency) })
      .getOrElse(paymentFeature)
  }

  private[payment] def getAmountWithFallback(bookingResult: Option[BookingResult],
                                             paymentFeature: Option[PaymentFeature],
                                             amountInUsd: Option[Double],
                                             fallbackAmount: Option[Double],
                                             precision: Int): Option[Double] = bookingResult.map { booking =>
    val amount =
      (amountInUsd.getOrElse(0d) * paymentFeature.flatMap(_.siteExchangeRate).getOrElse(0d)).roundAt(precision)

    if (amount == 0d) fallbackAmount.getOrElse(0d)
    else amount
  }

  private[payment] def getM150LocalPaymentAmount(payToAgoda: Double,
                                                 payToHotel: Double,
                                                 localExchange: Option[ExchangeRate],
                                                 upliftExchange: Option[ExchangeRate],
                                                 siteExchange: Option[ExchangeRate],
                                                 paymentCurrency: String,
                                                 isFixUpliftExchangeForM150: Boolean)(implicit
    exchangeDataService: ExchangeDataService): Option[PaymentBreakdown] = {
    val m150UpliftPercentage =
      if (isFixUpliftExchangeForM150) {
        val paymentCurrencyUpliftExchange = exchangeDataService.getUpliftExchangeRateV2(paymentCurrency, paymentCurrency)
        val paymentCurrencySiteExchange = exchangeDataService.getExchangeRateV2(paymentCurrency, paymentCurrency)

        (paymentCurrencyUpliftExchange, paymentCurrencySiteExchange) match {
          case (Some(uplift), Some(site)) => Some((uplift.toRequest - site.toRequest) / site.toRequest + 1)
          case _ => None
        }
      } else None

    val updatedLocalExchange = m150UpliftPercentage
      .map(upliftPct => localExchange.map(ex => ex.copy(toRequest = ex.toRequest * upliftPct)))
      .getOrElse(upliftExchange)

    if (updatedLocalExchange.isDefined) {
      Some(convertedPaymentAmount(payToAgoda, payToHotel, updatedLocalExchange, siteExchange, defaultPrecision))
    } else None
  }

  private[payment] def getGiftCardAndCashbackMoneyBackAmount(
    mandatoryItems: Seq[ItemBreakdown],
    nonMandatoryItems: Seq[ItemBreakdown],
    giftCard: Option[GiftCard],
    cashback: Option[Cashback],
    numOfRooms: Int,
    isFit: Option[Boolean],
    isMultipleRoomAssignmentPrice: Boolean,
    isMigrateUsdSellAllInToEbe: Boolean): (Option[Double], Option[Double]) = {
    val isFitRoom = isFit.getOrElse(false)
    val (giftCardMoneyBackAmount, cashbackMoneyBackAmount) =
      if (isMigrateUsdSellAllInToEbe) {
        val itemBreakdowns: Seq[ItemBreakdown] = mandatoryItems ++ nonMandatoryItems
        (giftCard.map(gc =>
           ItemBreakdownHelper.getRebateAmountMoneyBack(itemBreakdowns,
                                                        defaultPrecision,
                                                        numOfRooms,
                                                        isMultipleRoomAssignmentPrice,
                                                        isFitRoom,
                                                        gc.actualPercent)),
         cashback.map(cb =>
           ItemBreakdownHelper.getRebateAmountMoneyBack(itemBreakdowns,
                                                        defaultPrecision,
                                                        numOfRooms,
                                                        isMultipleRoomAssignmentPrice,
                                                        isFitRoom,
                                                        cb.actualPercent)))

      } else (None, None)

    (giftCardMoneyBackAmount, cashbackMoneyBackAmount)
  }

  private[payment] def getCashbackPayment(
    cashbackUsdAmount: Option[Double],
    cashbackAmountInPaymentCurrency: Option[Double]): Option[BookingCashbackPayment] =
    (cashbackUsdAmount, cashbackAmountInPaymentCurrency) match {
      case (Some(usd), Some(paymentCurrency)) => Some(BookingCashbackPayment(paymentCurrency, usd))
      case _ => None
    }

  @SuppressWarnings(Array("stryker4s.mutation.EqualityOperator", "stryker4s.mutation.ConditionalExpression"))
  private[payment] def validatePaymentAmountConversion(searchId: String,
                                                       lengthOfStay: Int,
                                                       numberOfRoom: Int,
                                                       paymentUpliftExchangeRate: Option[Double],
                                                       paymentInfoUplift: Option[Double],
                                                       roomExchangeRate: Option[Double],
                                                       paymentAmount: Double,
                                                       paymentAmountUSD: Double,
                                                       paymentCurrency: String) = {
    val PAYMENT_AMT_CONVERT_THRESHOLD_PER_PRICE = 0.1
    val finalizedThreshold = PAYMENT_AMT_CONVERT_THRESHOLD_PER_PRICE * lengthOfStay * numberOfRoom

    paymentUpliftExchangeRate.orElse(roomExchangeRate).foreach { exg =>
      val diffInRequestCurrency = paymentAmountUSD * exg - paymentAmount
      val diffInUSDCurrency = paymentAmountUSD - paymentAmount / exg

      val paymentAmountConversionDiffPercentage = (Math.abs(diffInRequestCurrency) / Math.abs(paymentAmount)) * 100
      if (paymentAmountConversionDiffPercentage > finalizedThreshold) {
        // TODO: fail booking if the diff is exceeding threshold
        logger.warn(
          s"validatePaymentAmountConversion=failed " +
            s"searchId=$searchId " +
            s"paymentAmountConversionDiffPercentage=$paymentAmountConversionDiffPercentage " +
            s"paymentAmount=$paymentAmount " +
            s"paymentAmountUSD=$paymentAmountUSD " +
            s"exchangeRateUsed=$exg " +
            s"diffInUSDCurrency=$diffInUSDCurrency " +
            s"paymentUpliftExchangeRate=${paymentUpliftExchangeRate.getOrElse(0)} " +
            s"roomExchangeRate=${roomExchangeRate.getOrElse(0)} " +
            s"paymentInfoUplift=${paymentInfoUplift.getOrElse(0)} " +
            s"los=$lengthOfStay " +
            s"numRoom=$numberOfRoom " +
            s"paymentCurrency=$paymentCurrency",
        )
      }
    }
  }
}
