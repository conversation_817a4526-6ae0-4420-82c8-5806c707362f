package utilhelpers

import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.core.Fragment

class MeasurementsSpec extends SpecificationWithJUnit {
  "Measurements" should {
    "tag value as logarithm of 2" in {
      Measurements.tagValueAsLog2(-1) should_== "0"
      Measurements.tagValueAsLog2(0) should_== "0"
      Measurements.tagValueAsLog2(1) should_== "0"
      Measurements.tagValueAsLog2(2) should_== "1"
      Measurements.tagValueAsLog2(3) should_== "1"
      Measurements.tagValueAsLog2(4) should_== "2"
      Measurements.tagValueAsLog2(256) should_== "8"
    }

    "tag value as logarithm of 2 steps" in {
      Measurements.tagValueAsLog2Step(-1) should_== "0"
      Measurements.tagValueAsLog2Step(0) should_== "0"
      Measurements.tagValueAsLog2Step(1) should_== "1"
      Measurements.tagValueAsLog2Step(2) should_== "2"
      Measurements.tagValueAsLog2Step(3) should_== "4"
      Measurements.tagValueAsLog2Step(4) should_== "4"
      Measurements.tagValueAsLog2Step(5) should_== "8"
      Measurements.tagValueAsLog2Step(6) should_== "8"
      Measurements.tagValueAsLog2Step(7) should_== "8"
      Measurements.tagValueAsLog2Step(8) should_== "8"
      Measurements.tagValueAsLog2Step(9) should_== "16"
    }

    "tag value as bucket correctly" in {
      val buckets = List(0.0, .3, .95, 1)
      Measurements.tagValueAsBucket(None, buckets) should_== "NA"
      Measurements.tagValueAsBucket(Some(0.0), buckets) should_== "0.0"
      Measurements.tagValueAsBucket(Some(0.1), buckets) should_== "0.3"
      Measurements.tagValueAsBucket(Some(0.3), buckets) should_== "0.3"
      Measurements.tagValueAsBucket(Some(0.9), buckets) should_== "0.95"
      Measurements.tagValueAsBucket(Some(0.99), buckets) should_== "1.0"
      Measurements.tagValueAsBucket(Some(1.0), buckets) should_== "1.0"
      Measurements.tagValueAsBucket(Some(2.0), buckets) should_== "NA"
    }

    "losGroup correctly" in {
      Measurements.losGroup(14) should_== "1-14"
      Measurements.losGroup(30) should_== "15-30"
      Measurements.losGroup(45) should_== "31-45"
      Measurements.losGroup(60) should_== "46-60"
      Measurements.losGroup(90) should_== "61-90"
      Measurements.losGroup(120) should_== "91-120"
      Measurements.losGroup(180) should_== "121-180"
      Measurements.losGroup(256) should_== "181+"
    }
    "toAffiliateIdTag(None) should put explicit default" in {
      Measurements.toAffiliateIdTag(None, Set.empty) should_== "no-affiliate-id"
    }

    "toAffiliateIdTag(Some(123)) should return itself" in {
      Measurements.toAffiliateIdTag(Some("123"), Set.empty) should_== "123"
    }

    "toAttemptTag(5) should return itself" in {
      Measurements.toAttemptTag(5) should_== "5"
    }

    "toAttemptTag(15) should put limit as 10" in {
      Measurements.toAttemptTag(15) should_== ">10"
    }

    "tag value as room count correctly" in {
      Measurements.tagValueAsRoomCount(0) should_== "0"
      Measurements.tagValueAsRoomCount(1) should_== "0"
      Measurements.tagValueAsRoomCount(99) should_== "0"
      Measurements.tagValueAsRoomCount(100) should_== "1"
      Measurements.tagValueAsRoomCount(199) should_== "1"
      Measurements.tagValueAsRoomCount(200) should_== "2"
      Measurements.tagValueAsRoomCount(299) should_== "2"
      Measurements.tagValueAsRoomCount(300) should_== "3"
      Measurements.tagValueAsRoomCount(399) should_== "3"
      Measurements.tagValueAsRoomCount(400) should_== "4"
      Measurements.tagValueAsRoomCount(499) should_== "4"
      Measurements.tagValueAsRoomCount(500) should_== "5"
      Measurements.tagValueAsRoomCount(599) should_== "5"
      Measurements.tagValueAsRoomCount(600) should_== "6"
      Measurements.tagValueAsRoomCount(699) should_== "6"
      Measurements.tagValueAsRoomCount(700) should_== "7"
      Measurements.tagValueAsRoomCount(799) should_== "7"
      Measurements.tagValueAsRoomCount(800) should_== "8"
      Measurements.tagValueAsRoomCount(899) should_== "8"
      Measurements.tagValueAsRoomCount(900) should_== "9"
      Measurements.tagValueAsRoomCount(999) should_== "9"
      Measurements.tagValueAsRoomCount(1000) should_== "10"
      Measurements.tagValueAsRoomCount(1099) should_== "10"
      Measurements.tagValueAsRoomCount(1100) should_== "11"
      Measurements.tagValueAsRoomCount(1199) should_== "11"
      Measurements.tagValueAsRoomCount(1200) should_== "12"
      Measurements.tagValueAsRoomCount(1299) should_== "12"
      Measurements.tagValueAsRoomCount(1300) should_== "13"
      Measurements.tagValueAsRoomCount(1399) should_== "13"
      Measurements.tagValueAsRoomCount(1400) should_== "14"
      Measurements.tagValueAsRoomCount(1499) should_== "14"
      Measurements.tagValueAsRoomCount(1500) should_== "15"
      Measurements.tagValueAsRoomCount(1599) should_== "15"
      Measurements.tagValueAsRoomCount(1600) should_== ">15"
    }

    Fragment.foreach(
      Seq(
        (0, "000"),
        (1, "001"),
        (3, "003"),
        (5, "005"),
        (10, "010"),
        (20, "020"),
        (40, "040"),
        (60, "060"),
        (80, "080"),
        (100, "100"),
        (150, "150"),
        (200, "200"),
        (300, "300"),
        (400, "400"),
        (500, "500"),
        (600, ">500"),
      )) { case (offerCount, bucket) =>
      s"roomCountGroup($offerCount) = $bucket" in {
        Measurements.roomCountGroup(offerCount) should_== bucket
      }
    }
  }
}
