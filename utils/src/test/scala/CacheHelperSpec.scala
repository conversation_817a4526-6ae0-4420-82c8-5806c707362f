import org.specs2.mutable.SpecificationWithJUnit

import utilhelpers.CacheHelpers

import scala.concurrent.duration._

/**
  * Created by <PERSON> on 2017-02-03.
  */
class CacheHelperSpec extends SpecificationWithJUnit {

  class CH extends CacheHelpers

  "rand" should {
    "randomize the duration" in {
      val cacheHelpers = new CH()
      cacheHelpers.rand(10.minutes, 5.minutes, 0.1, 0.5) should beEqualTo((10.minutes, 5.minutes))
      cacheHelpers.rand(10.minutes, 5.minutes, 0.1, 0) should beEqualTo((9.minutes, 270.seconds))
      cacheHelpers.rand(10.minutes, 5.minutes, 0.1, 1) should beEqualTo((11.minutes, 330.seconds))
      cacheHelpers.rand(10.minutes, 5.minutes, 0, 0.7) should beEqualTo((10.minutes, 5.minutes))
      Range(0, 10000)
        .map { _ =>
          val (ttl, tti) = cacheHelpers.rand(100.minutes, 95.minutes, 0.1)
          ttl >= 90.minutes &&
          ttl <= 110.minutes &&
          ttl > tti
        }
        .reduce(_ && _) should beEqualTo(true)
    }
  }
}
