package services

import com.agoda.utils.flow.ExperimentContext
import models.flow.Variant
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 9/13/2016 AD.
  */
class ExperimentSupportSpec extends SpecificationWithJUnit with <PERSON>ckito with ExperimentSupport {
  "Experiment Support" should {
    val expID = "ExpID"

    def mockDetermineVariant(experimentContext: ExperimentContext, result: Char): Unit =
      experimentContext.determineVariant(expID) returns result

    def mockDetermineVariantPreallocation(experimentContext: ExperimentContext, result: Char): Unit =
      experimentContext.determineVariantWithPreallocation(expID) returns result

    "Throw exception if pass incorrect experiment" in {
      val experimentContext = mock[ExperimentContext]
      experimentContext.experiment(null) should throwA[IllegalArgumentException]
      experimentContext.experiment("") should throwA[IllegalArgumentException]
    }

    "Throw exception if pass incorrect experiment in experimentPreallocation" in {
      val experimentContext = mock[ExperimentContext]
      experimentContext.experimentPreallocation(null) should throwA[IllegalArgumentException]
      experimentContext.experimentPreallocation("") should throwA[IllegalArgumentException]
    }

    "Create basic experiment" in {
      val resultA = false
      val resultB = true

      "A variant => result as A user" in {
        val experimentContext = mock[ExperimentContext]
        val exp = experimentContext.experiment(expID).forUserA(resultA).forUserB(resultB)
        mockDetermineVariant(experimentContext, Variant.A)
        val result = exp.run()
        result should_== resultA
      }

      "B variant => result as B user" in {
        val experimentContext = mock[ExperimentContext]
        val exp = experimentContext.experiment(expID).forUserA(resultA).forUserB(resultB)
        mockDetermineVariant(experimentContext, Variant.B)
        val result = exp.run()
        result should_== resultB
      }

      "X variant => result as A user" in {
        val experimentContext = mock[ExperimentContext]
        val exp = experimentContext.experiment(expID).forUserA(resultA).forUserB(resultB)
        mockDetermineVariant(experimentContext, Variant.X)
        val result = exp.run()
        result should_== resultA
      }

      "Z variant => result as A user" in {
        val experimentContext = mock[ExperimentContext]
        val exp = experimentContext.experiment(expID).forUserA(resultA).forUserB(resultB)
        mockDetermineVariant(experimentContext, Variant.Z)
        val result = exp.run()
        result should_== resultA
      }
    }

    "Create basic experiment for experimentPreallocation" in {
      val resultA = false
      val resultB = true

      "A variant => result as A user" in {
        val experimentContext = mock[ExperimentContext]
        val exp = experimentContext.experimentPreallocation(expID).forUserA(resultA).forUserB(resultB)
        mockDetermineVariant(experimentContext, Variant.A)
        val result = exp.run()
        result should_== resultA
      }

      "B variant => result as B user" in {
        val experimentContext = mock[ExperimentContext]
        val exp = experimentContext.experiment(expID).forUserA(resultA).forUserB(resultB)
        mockDetermineVariant(experimentContext, Variant.B)
        val result = exp.run()
        result should_== resultB
      }

      "X variant => result as A user" in {
        val experimentContext = mock[ExperimentContext]
        val exp = experimentContext.experiment(expID).forUserA(resultA).forUserB(resultB)
        mockDetermineVariant(experimentContext, Variant.X)
        val result = exp.run()
        result should_== resultA
      }

      "Z variant => result as A user" in {
        val experimentContext = mock[ExperimentContext]
        val exp = experimentContext.experiment(expID).forUserA(resultA).forUserB(resultB)
        mockDetermineVariant(experimentContext, Variant.Z)
        val result = exp.run()
        result should_== resultA
      }
    }

    "During experiment creation functions shouldn't be evaluated if it's another variant" in {
      "Lazy A variant" in {
        val experimentContext = mock[ExperimentContext]
        mockDetermineVariant(experimentContext, Variant.B)
        experimentContext
          .experiment(expID)
          .forUserA(throw new RuntimeException("You shouldn't execute it"))
          .forUserB(true)
          .run() should_== true
      }

      "Lazy B variant" in {
        val experimentContext = mock[ExperimentContext]
        mockDetermineVariant(experimentContext, Variant.A)
        experimentContext
          .experiment(expID)
          .forUserB(throw new RuntimeException("You shouldn't execute it"))
          .forUserA(true)
          .run() should_== true
      }

      "Evaluation only when running experiment" in {
        val experimentContext = mock[ExperimentContext]
        mockDetermineVariant(experimentContext, Variant.A)
        val resultExp = experimentContext
          .experiment(expID)
          .forUserA[Boolean](throw new RuntimeException("You shouldn't execute it"))
          .forUserB(throw new RuntimeException("You shouldn't execute it"))

        resultExp.run() should throwA[RuntimeException]
      }
    }

    "During experiment creation functions shouldn't be evaluated if it's another variant in experimentPreallocation" in {
      "Lazy A variant" in {
        val experimentContext = mock[ExperimentContext]
        mockDetermineVariant(experimentContext, Variant.B)
        experimentContext
          .experiment(expID)
          .forUserA(throw new RuntimeException("You shouldn't execute it"))
          .forUserB(true)
          .run() should_== true
      }

      "Lazy B variant" in {
        val experimentContext = mock[ExperimentContext]
        mockDetermineVariant(experimentContext, Variant.A)
        experimentContext
          .experiment(expID)
          .forUserB(throw new RuntimeException("You shouldn't execute it"))
          .forUserA(true)
          .run() should_== true
      }

      "Evaluation only when running experiment" in {
        val experimentContext = mock[ExperimentContext]
        mockDetermineVariant(experimentContext, Variant.A)
        val resultExp = experimentContext
          .experiment(expID)
          .forUserA[Boolean](throw new RuntimeException("You shouldn't execute it"))
          .forUserB(throw new RuntimeException("You shouldn't execute it"))

        resultExp.run() should throwA[RuntimeException]
      }
    }
  }
}
