package configuration

import com.typesafe.config.ConfigFactory
import org.specs2.mutable.SpecificationWithJUnit
import ConfigurationImplicits.TypesafeConfigImplicits
import scala.concurrent.duration._

class ConfigurationImplicitsSpec extends SpecificationWithJUnit {
  "ConfigurationImplicits.TypesafeConfigImplicits.getFiniteDuraion" should {
    "return correct duration for days" in {
      val configStr = """
                        |duration = 2 days
                        |""".stripMargin
      val config = ConfigFactory.parseString(configStr)
      config.getFiniteDuration("duration") must beEqualTo(2.days)
    }

    "return correct duration for hours" in {
      val configStr = """
                        |duration = 2 hours
                        |""".stripMargin
      val config = ConfigFactory.parseString(configStr)
      config.getFiniteDuration("duration") must beEqualTo(2.hours)
    }

    "return correct duration for minutes" in {
      val configStr = """
                        |duration = 2 minutes
                        |""".stripMargin
      val config = ConfigFactory.parseString(configStr)
      config.getFiniteDuration("duration") must beEqualTo(2.minutes)
    }

    "return correct duration for seconds" in {
      val configStr = """
                        |duration = 2 seconds
                        |""".stripMargin
      val config = ConfigFactory.parseString(configStr)
      config.getFiniteDuration("duration") must beEqualTo(2.seconds)
    }

    "return correct duration for milliseconds" in {
      val configStr = """
                        |duration = 2 milliseconds
                        |""".stripMargin
      val config = ConfigFactory.parseString(configStr)
      config.getFiniteDuration("duration") must beEqualTo(2.milliseconds)
    }

    "return correct duration for nanoseconds" in {
      val configStr = """
                        |duration = 2 nanoseconds
                        |""".stripMargin
      val config = ConfigFactory.parseString(configStr)
      config.getFiniteDuration("duration") must beEqualTo(2.nanoseconds)
    }

    "return correct duration for microseconds" in {
      val configStr = """
                        |duration = 2 microseconds
                        |""".stripMargin
      val config = ConfigFactory.parseString(configStr)
      config.getFiniteDuration("duration") must beEqualTo(2.microseconds)
    }
  }
}
