package concurrency

import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.{ExecutionContext, Future}

class ExecutionContextsSpec extends SpecificationWithJUnit {
  "ExecutionContexts" should {
    "be able to log exception" in {
      implicit val ec: ExecutionContext = ExecutionContexts.globalContext

      Future(42).flatMap(_ => throw new NoSuchMethodError())

      Thread.sleep(100)

      //  You should see the logging in your output
      done
    }
  }
}
