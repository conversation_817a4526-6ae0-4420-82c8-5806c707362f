package com.agoda.utils.resource

import org.specs2.mutable.SpecificationWithJUnit

/**
  * Created by pprateepamor on 5/26/2017 AD.
  */

case class IntResource(name: String, data: Int) extends Resource[Int]

class SimpleResourceSelectorSpec extends SpecificationWithJUnit {

  private val resources = Seq(IntResource("A", 1), IntResource("B", 2), IntResource("C", 3))

  "ResourceSelector constructor" should {
    "Select first resource when it is not set the default resource" in {
      val selector = new SimpleResourceSelector(resources)

      val resourceOpt = selector.get

      resourceOpt.map(_.name).getOrElse("") mustEqual "A"
    }

    "Select specified resource when set the default resource" in {
      val selector = new SimpleResourceSelector(resources, Some("C"))

      val resourceOpt = selector.get

      resourceOpt.map(_.name).getOrElse("") mustEqual "C"
    }

    "Select first resource when set with the non-existed default resource" in {
      val selector = new SimpleResourceSelector(resources, Some("D"))

      val resourceOpt = selector.get

      resourceOpt.map(_.name).getOrElse("") mustEqual "A"
    }
  }

  "ResourceSelector constructor" should {
    "Select specified resource when select existing resource name" in {
      val selector = new SimpleResourceSelector(resources)

      selector.select("B")

      val resourceOpt = selector.get

      resourceOpt.map(_.name).getOrElse("") mustEqual "B"
    }

    "Select specified resource when select existing resource name in case insensitive" in {
      val selector = new SimpleResourceSelector(resources)

      selector.select("c")

      val resourceOpt = selector.get

      resourceOpt.map(_.name).getOrElse("") mustEqual "C"
    }

    "Do not select any resources when select non-existing resource name" in {
      val selector = new SimpleResourceSelector(resources, Some("C"))

      selector.select("E")

      val resourceOpt = selector.get

      resourceOpt.map(_.name).getOrElse("") mustEqual "C"
    }
  }
}
