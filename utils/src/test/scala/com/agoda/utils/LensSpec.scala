package com.agoda.utils

import com.agoda.utils.lens.Lens
import org.specs2.mutable.SpecificationWithJUnit

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 5/12/17.
  */
class LensSpec extends SpecificationWithJUnit {
  case class Container(hotel: Hotel)
  case class Hotel(name: String, cheapestPrice: Price, rooms: Seq[Room])
  case class Price(netEx: Double, netIn: Double, sellEx: Double, sellIn: Double)
  case class Room(id: Int, price: Price, daily: Seq[Price] = Seq.empty)

  "Lens" should {
    val price = Price(0, 0, 0, 0)
    val hotel = Hotel("Bayok", price, Seq(Room(1, price), Room(2, price)))
    val obj = Container(hotel)
    val hotelLens = Lens[Container, Hotel](_.hotel, (c, h) => c.copy(hotel = h))
    val hotelPriceLens = Lens[Hotel, Price](_.cheapestPrice, (h, p) => h.copy(cheapestPrice = p))
    val roomsLens = Lens[Hotel, Seq[Room]](_.rooms, (h, rs) => h.copy(rooms = rs))
    val roomPriceLens = Lens[Room, Price](_.price, (r, p) => r.copy(price = p))
    val roomDailyPricesLens = Lens[Room, Seq[Price]](_.daily, (r, ps) => r.copy(daily = ps))

    "Get & Set checks" in {
      "Pass base checks: get and set" in {
        hotelLens.get(obj) should_== hotel

        val newHotel = hotel.copy(name = "New name")
        hotelLens.set(obj, newHotel) should_!= obj
        hotelLens.set(obj, newHotel) should_== obj.copy(hotel = newHotel)
      }

      "Pass compose checks: get and set" in {
        val composeLens = hotelLens |> hotelPriceLens
        composeLens.get(obj) should_== hotel.cheapestPrice

        val newPrice = hotel.cheapestPrice.copy(sellIn = 1)
        composeLens.set(obj, newPrice) should_!= obj
        composeLens.set(obj, newPrice) should_== obj.copy(hotel = hotel.copy(cheapestPrice = newPrice))
      }
    }

    "Implicits tests" in {
      import com.agoda.utils.lens.Implicits._

      "Get" in {
        obj.get(hotelLens) should_== obj.hotel
      }

      "Set" in {
        val newHotel = hotel.copy(name = "new")
        obj.set(newHotel)(hotelLens) should_== obj.copy(hotel = newHotel)
      }

      "Single property update" in {
        val newObj = obj.update { h: Hotel => h.copy(name = "new") }(hotelLens)
        newObj.hotel should_!= hotel
        newObj.hotel should_== hotel.copy(name = "new")
      }

      "Seq update" in {
        val newHotel = hotel.updateSeq { r: Room =>
          r.copy(id = r.id + 1)
        }(roomsLens)

        newHotel should_!= hotel
        newHotel.rooms should_== Seq(Room(2, price), Room(3, price))
      }

      "Seq property update" in {
        val newHotel = hotel.updateSeqUnit { r: Room => p: Price =>
          p.copy(sellIn = r.id)
        }(roomsLens, roomPriceLens)

        newHotel should_!= hotel
        newHotel.rooms should_== Seq(Room(1, price.copy(sellIn = 1)), Room(2, price.copy(sellIn = 2)))
      }

      "Seq Seq update" in {
        val price1 = price.copy(netEx = 10)
        val price2 = price.copy(netEx = 20)
        val hotelWithDaily =
          hotel.copy(rooms = Seq(Room(1, price, daily = Seq(price1, price2)), Room(2, price, daily = Seq(price1, price2))))

        val newHotel = hotelWithDaily.updateSeqSeq { r: Room => p: Price =>
          p.copy(sellIn = r.id)
        }(roomsLens, roomDailyPricesLens)

        newHotel should_!= hotelWithDaily
        newHotel.rooms.flatMap(_.daily) should_== Seq(price1.copy(sellIn = 1),
                                                      price2.copy(sellIn = 1),
                                                      price1.copy(sellIn = 2),
                                                      price2.copy(sellIn = 2))
      }
    }
  }
}
