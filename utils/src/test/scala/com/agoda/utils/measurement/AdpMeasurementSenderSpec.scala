package com.agoda.utils.measurement

import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

/**
  * Created by pprateepamor on 7/3/2017 AD.
  */
class AdpMeasurementSenderSpec extends SpecificationWithJUnit with <PERSON><PERSON><PERSON> {
  "AdpMeasurementSender.send" should {
    val appName = "TestApp"
    val appKey = "TestKey"

    val metricName = "TestMetric"
    val metricValue = 10

    "Set application name when send" in {
      val message = mock[AdpMeasurementMessage]

      val sender = setupAdpMeasurementSender(appName, appKey, message)

      sender.send(metricName, metricValue)

      message.application mustEqual appName
    }

    "Set measurement value when send" in {
      val message = mock[AdpMeasurementMessage]

      val sender = setupAdpMeasurementSender(appName, appKey, message)

      sender.send(metricName, metricValue)

      message.measurementValue mustEqual metricValue
    }

    "Set measurement tags when send" in {
      import scala.collection.JavaConverters._

      val message = mock[AdpMeasurementMessage]

      val sender = setupAdpMeasurementSender(appName, appKey, message)

      val tags = Map("tag1" -> "val1", "tag2" -> "val2")

      sender.send(metricName, metricValue, tags)

      there was one(message).setTags(tags.asJava)
    }

    "Call send message asynchronously when send" in {
      val message = mock[AdpMeasurementMessage]

      val sender = setupAdpMeasurementSender(appName, appKey, message)

      sender.send(metricName, metricValue)

      there was one(message).sendAsync(appKey)
    }
  }

  private def setupAdpMeasurementSender(appName: String,
                                        appKey: String,
                                        message: AdpMeasurementMessage): AdpMeasurementSender = {
    val messageFactory = mock[MeasurementMessageFactory[AdpMeasurementMessage]]

    messageFactory.create(anyString) returns message

    new AdpMeasurementSender(appName, appKey, messageFactory)
  }
}
