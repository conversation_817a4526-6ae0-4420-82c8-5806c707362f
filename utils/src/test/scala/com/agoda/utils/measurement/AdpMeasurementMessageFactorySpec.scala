package com.agoda.utils.measurement

import org.specs2.mutable.SpecificationWithJUnit

/**
  * Created by pprateepamor on 6/29/2017 AD.
  */
class AdpMeasurementMessageFactorySpec extends SpecificationWithJUnit {
  "AdpMeasurementMessageFactory.create" should {
    val metricName = "TestMetric"

    "Returns measurement message when create successfully" in {
      val factory = new AdpMeasurementMessageFactory()

      val message = factory.create(metricName)

      message mustNotEqual null
    }

    "Set metric name correctly when create message" in {
      val factory = new AdpMeasurementMessageFactory()

      val message = factory.create(metricName)

      message.getMetricName mustEqual metricName
    }
  }
}
