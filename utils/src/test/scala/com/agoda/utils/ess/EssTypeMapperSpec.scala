package com.agoda.utils.ess

import org.specs2.mutable.SpecificationWithJUnit
import com.agoda.utils.ess.models.{EssContext, EssRoom}

class ESSTypeMapperSpec extends SpecificationWithJUnit {
  "getEssTypeForRoom" should {

    "For Intl dest" in {
      implicit val essContext = EssContext(Some(101L), true)

      "Return PublishPriceEss if US-INTL" in {
        val essRoom = EssRoom(Some(-20.0))
        ESSTypeMapper.getEssTypeForRoom(essRoom) should_== Some(EssTypes.PublishPrice)
      }
      "Return PublishPriceEss if INTL-INTL" in {
        val essRoom = EssRoom(Some(-20.0))
        ESSTypeMapper.getEssTypeForRoom(essRoom) should_== Some(EssTypes.PublishPrice)
      }
      "Not Return PublishPriceEss if no negative downlift" in {
        val essRoom = EssRoom(Some(10.0))
        ESSTypeMapper.getEssTypeForRoom(essRoom) should_== None
      }
    }
    "Not Return PublishPriceEss if INTL-US" in {
      implicit val essContext = EssContext(Some(181), true)
      val essRoom = EssRoom(Some(-20.0))
      ESSTypeMapper.getEssTypeForRoom(essRoom) should_== None
    }
    "Not Return PublishPriceEss if ESSPublishPriceEnabled WL Config is disabled" in {
      implicit val essContext = EssContext(Some(101L), false)
      val essRoom = EssRoom(Some(-20.0))
      ESSTypeMapper.getEssTypeForRoom(essRoom) should_== None
    }
  }

  "getEssTypeForCountry" should {

    "For Intl dest" in {
      implicit val essContext = EssContext(Some(101L), true)

      "Return PublishPriceEss if US-INTL" in {
        ESSTypeMapper.getEssTypeForCountry() should_== Some(EssTypes.PublishPrice)
      }
      "Return PublishPriceEss if INTL-INTL" in {
        ESSTypeMapper.getEssTypeForCountry() should_== Some(EssTypes.PublishPrice)
      }
    }
    "Not Return PublishPriceEss if INTL-US" in {
      implicit val essContext = EssContext(Some(181), true)
      ESSTypeMapper.getEssTypeForCountry() should_== None
    }
    "Not Return PublishPriceEss if ESSPublishPriceEnabled WL Config is disabled" in {
      implicit val essContext = EssContext(Some(101L), false)
      ESSTypeMapper.getEssTypeForCountry() should_== None
    }
  }
}
