package com.agoda.utils.circuitbreaker

import com.typesafe.config.ConfigFactory
import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.duration._

class CircuitBreakerSettingsSpec extends SpecificationWithJUnit {
  "CircuitBreakerSettings" should {
    "be parsed correctly" in {
      val configStr = """
                        |my-circuit-breaker {
                        |    enabled = true
                        |    max-failures = 10
                        |    call-timeout = 100 milliseconds
                        |    reset-timeout = 5 seconds
                        |    delay = 2 minute
                        |    success-count = -1
                        |}
                        |""".stripMargin
      val config = ConfigFactory.parseString(configStr)

      val settings = CircuitBreakerSettings("my-circuit-breaker", "myname", config)
      settings.circuitBreakerName should_== ("myname")
      settings.enabled should_== (true)
      settings.maxFailures should_== (10)
      settings.callTimeout should_== (100 milliseconds)
      settings.resetTimeout should_== (5 seconds)
      settings.delayTime should_== (2 minutes)
      settings.successCount should_== (-1)
    }

    "be parsed correctly and apply fallbacks" in {
      val configStr = """
                        |my-circuit-breaker {
                        |    enabled = true
                        |    max-failures = 10
                        |    success-count = -1
                        |}
                        |""".stripMargin
      val config = ConfigFactory.parseString(configStr)

      val settings = CircuitBreakerSettings("my-circuit-breaker", "myname", config)
      settings.circuitBreakerName should_== ("myname")
      settings.enabled should_== (true)
      settings.maxFailures should_== (10)
      settings.callTimeout should_== (200 milliseconds)
      settings.resetTimeout should_== (10 seconds)
      settings.delayTime should_== (1 minute)
      settings.successCount should_== (-1)
    }
  }
}
