package com.agoda.utils.circuitbreaker

import akka.actor.ActorSystem
import akka.pattern.CircuitBreakerOpenException
import api.routing.dsl.exception.RFlowTimeoutException
import com.typesafe.config.ConfigFactory
import org.specs2.concurrent.ExecutionEnv
import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}

/**
  * Created by pprateepamor on 5/18/2017 AD.
  */
class CircuitBreakerWrapperSpec(implicit env: ExecutionEnv) extends SpecificationWithJUnit {
  sequential

  private val actorSystem = ActorSystem("testsystem",
                                        ConfigFactory.parseString("""
    akka.event-handlers = ["akka.testkit.TestEventListener"]
    """))

  private val fallbackTimeout = (_: RFlowTimeoutException) => Future.successful(1)
  private val fallbackException = (_: Throwable) => Future.successful(2)

  "CircuitBreakerWrapper.execute" should {

    "When disabled" in {
      val settings = CircuitBreakerSettings(enabled = false, 0, 1000 milli, 200 milli, 0 milli, 0, "")

      "Pass successful futures through" in {
        val circuitBreaker =
          CircuitBreakerWrapper(CircuitBreakerFactory.create(settings)(ExecutionContext.global, actorSystem.scheduler),
                                settings)
        val resultFut =
          circuitBreaker.execute[Int](Future.successful(3), fallbackTimeout, fallbackException)(ExecutionContext.global)

        Await.result(resultFut, 100 milli) mustEqual 3
      }

      "Pass failed futures through without opening CB" in {
        val circuitBreaker =
          CircuitBreakerWrapper(CircuitBreakerFactory.create(settings)(ExecutionContext.global, actorSystem.scheduler),
                                settings)
        val resultFut = circuitBreaker.execute[Int](Future.failed(RFlowTimeoutException("", "", 0, 0)),
                                                    fallbackTimeout,
                                                    fallbackException)(ExecutionContext.global)

        Await.ready(resultFut, 100 milli).value.get.get must throwA[RFlowTimeoutException]
      }
    }

    "When enabled" in {
      val settings = CircuitBreakerSettings(enabled = true, 1, 1000 milli, 200 milli, 0 milli, 0, "")

      "Invokes fallback method of timeout exception when execute and timeout" in {
        val circuitBreaker =
          CircuitBreakerWrapper(CircuitBreakerFactory.create(settings)(ExecutionContext.global, actorSystem.scheduler),
                                settings)
        val resultFut = circuitBreaker.execute[Int](Future.failed(RFlowTimeoutException("", "", 1, 1)),
                                                    fallbackTimeout,
                                                    fallbackException)(ExecutionContext.global)

        Await.result(resultFut, 100 milli) mustEqual 1
      }

      "Invokes fallback method of normal exception when execute failed" in {
        val circuitBreaker =
          CircuitBreakerWrapper(CircuitBreakerFactory.create(settings)(ExecutionContext.global, actorSystem.scheduler),
                                settings)
        val resultFut = circuitBreaker
          .execute[Int](Future.failed(new Exception), fallbackTimeout, fallbackException)(ExecutionContext.global)

        Await.result(resultFut, 100 milli) mustEqual 2
      }

      "Open circuit breaker when failure is more than settings" in {
        eventually {
          val circuitBreaker =
            CircuitBreakerWrapper(CircuitBreakerFactory.create(settings)(ExecutionContext.global, actorSystem.scheduler),
                                  settings)
          val handleNothing = (e: Throwable) => Future.failed(e)

          Await.ready(circuitBreaker.execute[Int](Future.failed(new Exception), handleNothing, handleNothing)(
                        ExecutionContext.global),
                      100 milli)

          val resultFut =
            circuitBreaker.execute[Int](Future.successful(5), handleNothing, handleNothing)(ExecutionContext.global)

          Await.ready(resultFut, 5 second).value.get.get must throwA[CircuitBreakerOpenException]
        }
      }

      "Count error only when exception match DefineFailure function" in {
        val circuitBreaker =
          CircuitBreakerWrapper(CircuitBreakerFactory.create(settings)(ExecutionContext.global, actorSystem.scheduler),
                                settings)
        val handleNothing = (e: Throwable) => Future.failed(e)
        val defineFailureFn = CircuitBreakerAPI.exceptionAsFailure

        // Not counted as Failures
        Await
          .ready(circuitBreaker.execute[Int](Future.successful(1), handleNothing, handleNothing, defineFailureFn)(
                   ExecutionContext.global),
                 100 milli)
          .value
          .get
          .get must not(throwA[CircuitBreakerOpenException])
        Await
          .ready(circuitBreaker.execute[Int](Future.successful(1), handleNothing, handleNothing, defineFailureFn)(
                   ExecutionContext.global),
                 100 milli)
          .value
          .get
          .get must not(throwA[CircuitBreakerOpenException])

        Await
          .ready(
            circuitBreaker.execute[Int](Future.failed(RFlowTimeoutException("", "", 0, 0)),
                                        handleNothing,
                                        handleNothing,
                                        defineFailureFn)(ExecutionContext.global),
            100 milli,
          )
          .value
          .get
          .get must throwA[RFlowTimeoutException]
        Await
          .ready(
            circuitBreaker.execute[Int](Future.failed(RFlowTimeoutException("", "", 0, 0)),
                                        handleNothing,
                                        handleNothing,
                                        defineFailureFn)(ExecutionContext.global),
            100 milli,
          )
          .value
          .get
          .get must throwA[RFlowTimeoutException]

        // Counted as Failures
        Await
          .ready(circuitBreaker.execute[Int](Future.failed(new RuntimeException("")),
                                             handleNothing,
                                             handleNothing,
                                             defineFailureFn)(ExecutionContext.global),
                 100 milli)
          .value
          .get
          .get must throwA[RuntimeException]
        Await
          .ready(circuitBreaker.execute[Int](Future.failed(new RuntimeException("")),
                                             handleNothing,
                                             handleNothing,
                                             defineFailureFn)(ExecutionContext.global),
                 100 milli)
          .value
          .get
          .get must throwA[CircuitBreakerOpenException]
      }
    }

  }

  "CircuitBreakerFactory" should {

    val settings = CircuitBreakerSettings(enabled = true, 1, 1000 milli, 200 milli, 0 milli, 0, "")

    "withOpenCallback execute callback when CB changes state from close to open" in {
      var a = 0
      val handleNothing = (e: Throwable) => Future.failed(e)
      val cbWithCallback =
        CircuitBreakerFactory.create(settings)(ExecutionContext.global, actorSystem.scheduler).onOpen {
          a = 1
        }
      val cbWrapper = CircuitBreakerWrapper(cbWithCallback, settings)

      Await
        .ready(
          cbWrapper.execute[Int](Future.failed(new Exception()), handleNothing, handleNothing)(ExecutionContext.global),
          100 milli)
        .value
        .get
        .get must throwA[Exception]
      Await
        .ready(
          cbWrapper.execute[Int](Future.failed(new Exception()), handleNothing, handleNothing)(ExecutionContext.global),
          100 milli)
        .value
        .get
        .get must throwA[CircuitBreakerOpenException]
      eventually(a must_== 1)
    }

    "withHalfOpenCallback execute callback when CB changes state from open to halfOpen" in {
      var a = 0
      val handleNothing = (e: Throwable) => Future.failed(e)
      val cbWithCallback =
        CircuitBreakerFactory.create(settings)(ExecutionContext.global, actorSystem.scheduler).onHalfOpen {
          a = 1
        }
      val cbWrapper = CircuitBreakerWrapper(cbWithCallback, settings)

      Await
        .ready(
          cbWrapper.execute[Int](Future.failed(new Exception()), handleNothing, handleNothing)(ExecutionContext.global),
          100 milli)
        .value
        .get
        .get must throwA[Exception]
      Thread.sleep(400)
      eventually(a must_== 1)
    }

    "withHalfOpenCallback execute callback when CB changes state from halfOpen to close" in {
      var a = 0
      val handleNothing = (e: Throwable) => Future.failed(e)
      val cbWithCallback =
        CircuitBreakerFactory.create(settings)(ExecutionContext.global, actorSystem.scheduler).onClose {
          a = 1
        }
      val cbWrapper = CircuitBreakerWrapper(cbWithCallback, settings)

      Await
        .ready(
          cbWrapper.execute[Int](Future.failed(new Exception()), handleNothing, handleNothing)(ExecutionContext.global),
          100 milli)
        .value
        .get
        .get must throwA[Exception]
      Thread.sleep(400)
      Await.ready(cbWrapper.execute[Int](Future.successful(5), handleNothing, handleNothing)(ExecutionContext.global),
                  100 milli)
      eventually(a must_== 1)
    }

  }

}
