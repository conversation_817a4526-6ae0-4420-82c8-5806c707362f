package com.agoda.utils.String

import org.specs2.mutable.SpecificationWithJUnit

class GZipHelperSpec extends SpecificationWithJUnit {
  "GZipHelper" should {
    "deflate and inflate" in {
      val txt = "test"
      val deflatedTxt = GZipHelper.deflate(txt)
      val inflatedTxt = GZipHelper.inflate(deflatedTxt)
      inflatedTxt must_== txt
    }

    "deflate and inflate empty string" in {
      val txt = ""
      val deflatedTxt = GZipHelper.deflate(txt)
      val inflatedTxt = GZipHelper.inflate(deflatedTxt)
      inflatedTxt must_== txt
    }
  }
}
