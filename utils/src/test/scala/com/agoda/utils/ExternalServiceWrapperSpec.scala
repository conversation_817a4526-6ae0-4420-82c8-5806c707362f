package com.agoda.utils

import akka.pattern.CircuitBreaker
import com.agoda.utils.circuitbreaker.{CircuitBreakerSettings, CircuitBreakerWrapper}
import com.agoda.utils.flow.CommonFlowContext
import com.agoda.utils.monitoring.ReportingService
import com.typesafe.config.ConfigFactory
import org.mockito.{ArgumentMatchers => AM}
import org.specs2.concurrent.ExecutionEnv
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.Future

class ExternalServiceWrapperSpec(implicit env: ExecutionEnv) extends SpecificationWithJUnit with <PERSON><PERSON><PERSON> {

  "ExternalServiceWrapper" should {
    "report endpoint tag to group data" in {
      val commonFlowContext = mock[CommonFlowContext]
      val circuitBreakerMock =
        CircuitBreakerWrapper(mock[CircuitBreaker], CircuitBreakerSettings.fromConfig("false", ConfigFactory.empty()))
      val reportingMock = mock[ReportingService]
      val wrapper = ExternalServiceWrapper(circuitBreakerMock, reportingMock, "pricing")
      val result = wrapper.callClient(Future.successful(1), 0, "dfe.pricing", "cor", true, Map("whitelabelId" -> "1"))(
        commonFlowContext,
        env.ec)
      result must be_==(1).await
      there was one(reportingMock).report(AM.eq("dfe.pricing"),
                                          AM.anyLong(),
                                          AM.eq(Map("endpoint" -> "cor", "status" -> "success", "whitelabelId" -> "1")),
                                          AM.any())
      there was one(commonFlowContext).addExternalServiceStat(
        AM.eq("cor"),
        AM.eq(true),
        AM.anyLong(),
        AM.eq(1),
        AM.eq(1),
      )
    }

    "fallback will be use if something went wrong" in {
      var actualException: Option[Throwable] = None
      val commonFlowContext = mock[CommonFlowContext]
      val thrownException = new Exception("error")
      val circuitBreakerMock =
        CircuitBreakerWrapper(mock[CircuitBreaker], CircuitBreakerSettings.fromConfig("false", ConfigFactory.empty()))
      val reportingMock = mock[ReportingService]
      val wrapper = ExternalServiceWrapper(circuitBreakerMock, reportingMock, "pricing")

      wrapper
        .callClient(Future.failed(thrownException),
                    (t: Throwable) => actualException = Some(t),
                    "dfe.pricing",
                    "cor",
                    true,
                    Map.empty[String, String])(commonFlowContext, env.ec)
        .map { case _ => actualException.get must_== thrownException }
        .await

      there was one(commonFlowContext).addExternalServiceStat(
        AM.eq("cor"),
        AM.eq(false),
        AM.anyLong(),
        AM.eq(1),
        AM.eq(1),
      )
    }
  }
}
