package com.agoda.utils

import com.agoda.utils.FutureUtils._
import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class FutureUtilsSpec(implicit ec: ExecutionContext) extends SpecificationWithJUnit {
  sequential

  "FutureUtils" should {
    val fs1 = Future.successful(24)
    val fs2 = Future.successful(42)
    val exception1 = new RuntimeException("Some special exception #1")
    val exception2 = new RuntimeException("Some special exception #2")
    val ff3 = Future.failed[Int](exception1)
    val ff4 = Future.failed[Int](exception2)

    val timeout = 1.second

    "Successful With Rate" in {
      "Wrong parameters" in {
        List[Future[Int]]().successfulWithRate(-1.0) should throwA[IllegalArgumentException]
        List[Future[Int]]().successfulWithRate(1.1) should throwA[IllegalArgumentException]
      }

      "Empty Future list" in {
        "1. SR = 0%: Return successful Future" in {
          val resultF = List[Future[Int]]().successfulWithRate(0)
          Await.result(resultF, timeout) should_== List()
        }

        "2. SR = 100%: Return successful Future" in {
          val resultF = List[Future[Int]]().successfulWithRate(1.0)
          Await.result(resultF, timeout) should_== List()
        }
      }

      "Single Future" in {
        "1. SR = 0%" in {
          val rate = 0.0
          "Return successful Future for Successful Future (SF)" in {
            val resultF = List(fs1).successfulWithRate(rate)
            Await.result(resultF, timeout) should_== List(24)
          }

          "Return successful Future for Failed Future (FF)" in {
            val resultF = List(ff3).successfulWithRate(rate)
            Await.result(resultF, timeout) should_== List.empty
          }
        }

        "2. SR = 50%" in {
          val rate = 0.5
          "Return successful Future for Successful Future (SF)" in {
            val resultF = List(fs1).successfulWithRate(rate)
            Await.result(resultF, timeout) should_== List(24)
          }

          "Return failed Future for Failed Future (FF)" in {
            val resultF = List(ff3).successfulWithRate(rate)
            Await.result(resultF, timeout) should throwA[LowSuccessRateException]
          }
        }

        "3. SR = 100%" in {
          val rate = 1.0
          "Return successful Future for Successful Future (SF)" in {
            val resultF = List(fs1).successfulWithRate(rate)
            Await.result(resultF, timeout) should_== List(24)
          }

          "Return failed Future for Failed Future (FF)" in {
            val resultF = List(ff3).successfulWithRate(rate)
            Await.result(resultF, timeout) should throwA[LowSuccessRateException]
          }
        }
      }

      "Multiple Futures" in {
        val listFutures = List(fs1, fs2, ff3)
        val successResult = List(24, 42)

        "1. SR = 0% - return success" in {
          val rate = 0.0
          val resultF = listFutures.successfulWithRate(rate)
          Await.result(resultF, timeout) should_== successResult
        }

        "2. SR = 50% - return success" in {
          val rate = 0.5
          val resultF = listFutures.successfulWithRate(rate)
          Await.result(resultF, timeout) should_== successResult
        }

        "3. SR = 66.6% - return success" in {
          val rate = 2 / 3
          val resultF = listFutures.successfulWithRate(rate)
          Await.result(resultF, timeout) should_== successResult
        }

        "4. SR = 100% - return failure" in {
          val rate = 1.0
          val resultF = listFutures.successfulWithRate(rate)
          Await.result(resultF, timeout) should throwA[LowSuccessRateException]
        }
      }

      "Internal exceptions" in {
        "Pass current exception inside LowSuccessRateException" in {
          val resultF = List(ff3).successfulWithRate().recoverWith { case t: Throwable => Future.failed(t.getCause) }
          Await.result(resultF, timeout) should throwA[RuntimeException](exception1)
        }

        "Pass first failed future's exception inside LowSuccessRateException" in {
          val resultF =
            List(ff4, ff3).successfulWithRate().recoverWith { case t: Throwable => Future.failed(t.getCause) }
          Await.result(resultF, timeout) should throwA[RuntimeException](exception2)
        }
      }
    }

    "All successful" in {
      "Return Future of elements if all elements are successful" in {
        val resultF = List(fs1, fs2).allSuccessful
        Await.result(resultF, timeout) should_== List(24, 42)
      }

      "Return Future of only successful elements if some of elements are successful" in {
        val resultF = List(fs1, ff3, fs2, ff4).allSuccessful
        Await.result(resultF, timeout) should_== List(24, 42)

        //  Just FYI - regular Future.sequence
        Await.result(Future.sequence(List(fs1, ff3, fs2, ff4)), timeout) should throwA[RuntimeException](exception1)
      }

      "Return Future of empty elements if there is no successful elements" in {
        val resultF = List(ff3, ff4).allSuccessful
        Await.result(resultF, timeout) should_== List.empty

        //  Just FYI - regular Future.sequence
        Await.result(Future.sequence(List(ff3, ff4)), timeout) should throwA[RuntimeException](exception1)
      }
    }

    "Future[T] -> Future[Try[T]]" in {
      val timeout = 1000.millis

      "Normal case" in {
        val future: Future[Try[Int]] = Future { Thread.sleep(50); 42 } toTry ec
        Await.result(future, timeout) should_== Success(42)
      }
      "Exception case" in {
        val ex = new RuntimeException("test")
        val future: Future[Try[Int]] = Future[Int] { Thread.sleep(50); throw ex } toTry ec
        Await.result(future, timeout) should_== Failure(ex)
      }
    }

    "Future[T] -> Future[(Try[T], Millis)]" in {
      val timeout = 1000

      "Normal case" in {
        val future: Future[(Try[Int], Long)] = Future { Thread.sleep(50); 42 } withTime System.currentTimeMillis()
        val (result, time) = Await.result(future, timeout.millis)
        result should_== Success(42)
        (time >= 50 && time < timeout) should_== true
      }
      "Exception case" in {
        val ex = new RuntimeException("test")
        val future: Future[(Try[Int], Long)] =
          Future[Int] { Thread.sleep(50); throw ex } withTime System.currentTimeMillis()
        val (result, time) = Await.result(future, timeout.millis)
        result should_== Failure(ex)
        (time >= 50 && time < timeout) should_== true
      }
    }
  }
}
