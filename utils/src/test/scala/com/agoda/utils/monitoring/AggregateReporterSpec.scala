package com.agoda.utils.monitoring

import java.util.concurrent.TimeUnit

import akka.actor.{ActorSystem, Scheduler}
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

import scala.collection.JavaConverters._
import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 12/6/17.
  */
class AggregateReporterSpec(implicit ec: ExecutionContext) extends SpecificationWithJUnit with <PERSON><PERSON><PERSON> {

  "AggregateReporter#apply" should {
    "create only one reporter instance for a particular interval" in {
      val interval1 = Duration(1, TimeUnit.MINUTES)
      val interval2 = Duration(60, TimeUnit.SECONDS)
      val interval3 = Duration(2, TimeUnit.MINUTES)

      val reportingService = mock[ReportingService]
      val actorSystem = mock[ActorSystem]
      actorSystem.scheduler returns mock[Scheduler]

      val reportersF = Seq(interval1, interval2, interval3).map { interval =>
        Future(AggregateReporter(interval, reportingService, actorSystem))
      }

      val aggregateReporters = Await.result(Future.sequence(reportersF), Duration(2, TimeUnit.SECONDS))

      aggregateReporters.head must_== aggregateReporters(1)
      aggregateReporters.head must_!= aggregateReporters(2)
      AggregateReporter.aggregateReporters.size() must_== 2
    }
  }

  "AggregateReporter#count" should {

    "aggregate count in an atomic map keyed by metric key and tags" in {
      // given
      val key1 = "Key1"
      val key2 = "Key2"
      val tags1 = Map("tag1" -> "1", "tag2" -> "2")
      val tags2 = Map("tag1" -> "1", "tag3" -> "2")
      val reportingService = mock[ReportingService]
      val aggregateReporter = new AggregateReporterImpl(reportingService)

      // when
      aggregateReporter.count(key1, 1, tags1)
      aggregateReporter.count(key1, 2, tags1)
      aggregateReporter.count(key2, 2, tags1)
      aggregateReporter.count(key2, 1, tags2)

      // then
      aggregateReporter.longAdderMap.get(key1 -> tags1).sum must_== 3
      aggregateReporter.longAdderMap.get(key1 -> tags2) must beNull
      aggregateReporter.longAdderMap.get(key2 -> tags1).sum must_== 2
      aggregateReporter.longAdderMap.get(key2 -> tags2).sum must_== 1
    }

  }

  "AggregateReporter#aggregate" should {

    "aggregate stats in an atomic map keyed by metric key and tags" in {
      // given
      val key1 = "Key1"
      val key2 = "Key2"
      val tags1 = Map("tag1" -> "1", "tag2" -> "2")
      val tags2 = Map("tag1" -> "1", "tag3" -> "2")
      val reportingService = mock[ReportingService]
      val aggregateReporter = new AggregateReporterImpl(reportingService)

      // when
      aggregateReporter.aggregate(key1, 2, tags1)
      aggregateReporter.aggregate(key1, 4, tags1)
      aggregateReporter.aggregate(key2, 2, tags1)
      aggregateReporter.aggregate(key2, 4, tags2)

      // then
      aggregateReporter.statsMap.get(key1 -> tags1).count.longValue must_== 2
      aggregateReporter.statsMap.get(key1 -> tags1).sum.longValue must_== 6
      aggregateReporter.statsMap.get(key1 -> tags1).min.longValue must_== 2
      aggregateReporter.statsMap.get(key1 -> tags1).max.longValue must_== 4

      aggregateReporter.statsMap.get(key1 -> tags2) must beNull

      aggregateReporter.statsMap.get(key2 -> tags1).count.longValue must_== 1
      aggregateReporter.statsMap.get(key2 -> tags1).sum.longValue must_== 2
      aggregateReporter.statsMap.get(key2 -> tags1).min.longValue must_== 2
      aggregateReporter.statsMap.get(key2 -> tags1).max.longValue must_== 2

      aggregateReporter.statsMap.get(key2 -> tags2).count.longValue must_== 1
      aggregateReporter.statsMap.get(key2 -> tags2).sum.longValue must_== 4
      aggregateReporter.statsMap.get(key2 -> tags2).min.longValue must_== 4
      aggregateReporter.statsMap.get(key2 -> tags2).max.longValue must_== 4
    }

  }

  "AggregateReporter#run" should {

    "decrement and report the aggregated count" in {
      // given
      val key1 = "Key1"
      val key2 = "Key2"
      val tags1 = Map("tag1" -> "1", "tag2" -> "2")
      val tags2 = Map("tag1" -> "1", "tag3" -> "2")
      val reportingService = mock[ReportingService]
      val aggregateReporter = new AggregateReporterImpl(reportingService)

      // when
      aggregateReporter.count(key1, 1, tags1)
      aggregateReporter.count(key1, 2, tags1)
      aggregateReporter.count(key1, 5, tags2)
      aggregateReporter.count(key2, -2, tags1)
      aggregateReporter.count(key2, 1, tags2)

      aggregateReporter.run()

      // then
      aggregateReporter.longAdderMap.values().asScala.forall(_.sum <= 0) must beTrue

      there was one(reportingService).report(===(key1), ===(3L), ===(tags1), ===(null))
      there was one(reportingService).report(===(key1), ===(5L), ===(tags2), ===(null))

      there was no(reportingService).report(===(key2), anyLong, ===(tags1), ===(null))
      there was one(reportingService).report(===(key2), ===(1L), ===(tags2), ===(null))
    }

    "remove any unused keys in 2nd run to avoid leaks" in {
      // given
      val key1 = "Key1"
      val tags1 = Map("tag1" -> "1", "tag2" -> "2")
      val reportingService = mock[ReportingService]
      val aggregateReporter = new AggregateReporterImpl(reportingService)

      // when
      aggregateReporter.count(key1, 2, tags1)

      (1 to 2) foreach { _ => aggregateReporter.run() }

      // then
      aggregateReporter.longAdderMap.isEmpty must beTrue

      there was one(reportingService).report(===(key1), ===(2L), ===(tags1), ===(null))
    }

    "AggregateReporter load test" in {

      val reportingService = mock[ReportingService]
      val aggregateReporter = new AggregateReporterImpl(reportingService)

      val f1 = Future {
        for (i <- 1 to 10000) aggregateReporter.aggregate("metric", 10, Map("type" -> "A"))
      }
      val f2 = Future {
        for (i <- 1 to 10000) aggregateReporter.count("metric", 10, Map("type" -> "B"))
      }
      val f3 = Future {
        for (i <- 1 to 10000) {
          aggregateReporter.aggregate("metric", 10, Map("type" -> "A"))
          aggregateReporter.count("metric", 10, Map("type" -> "B"))
        }
      }

      Await.result(Future.sequence(Seq(f1, f2, f3)), Duration(2, TimeUnit.SECONDS))
      aggregateReporter.run()

      there was one(reportingService).report("metric" + AggregateReporter.countSuffix, 20000, Map("type" -> "A"), null)
      there was one(reportingService).report("metric" + AggregateReporter.avgSuffix, 10, Map("type" -> "A"), null)
      there was one(reportingService).report("metric" + AggregateReporter.minSuffix, 10, Map("type" -> "A"), null)
      there was one(reportingService).report("metric" + AggregateReporter.maxSuffix, 10, Map("type" -> "A"), null)

      there was one(reportingService).report("metric", 200000, Map("type" -> "B"), null)
    }

  }

}
