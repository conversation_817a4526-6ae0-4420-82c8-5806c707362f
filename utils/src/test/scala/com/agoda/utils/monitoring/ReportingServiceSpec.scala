package com.agoda.papi.pricing.services

import com.agoda.utils.monitoring.ReportingService
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

class ReportingServiceSpec extends SpecificationWithJUnit with Mockito {
  class ReportingServiceMock(mockReportFn: () => Unit, mockIsPrelive: Boolean) extends ReportingService {
    override val isPrelive: Boolean = mockIsPrelive

    override def report(metric: String,
                        value: Long,
                        tags: Map[String, String],
                        excludePartitionKey: Seq[String]): Unit = mockReportFn()
  }
  "ReportingService" should {
    "reportOnlyPrelive correctly - isPrelive = true" in {
      val mockReportFn = mock[() => Unit]
      val reportingService = new ReportingServiceMock(mockReportFn, true)
      reportingService.reportOnlyPrelive("", 1L, Map.empty, Nil)
      there was one(mockReportFn).apply()
    }

    "reportOnlyPrelive correctly - isPrelive = false" in {
      val mockReportFn = mock[() => Unit]
      val reportingService = new ReportingServiceMock(mockReportFn, false)
      reportingService.reportOnlyPrelive("", 1L, Map.empty, Nil)
      there was no(mockReportFn).apply()
    }
  }

}
