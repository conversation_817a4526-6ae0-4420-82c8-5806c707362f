package com.agoda.utils.hadoop

private[hadoop] case class ScalaTestMessage() extends com.agoda.adp.messaging.scala.message.Message[ScalaTestMessage]
private[hadoop] class JavaTestMessage extends com.agoda.adp.messaging.message.Message

import java.util.concurrent.atomic.AtomicBoolean

import com.agoda.adp.messaging.sender.SendResult
import com.agoda.utils.concurrency.Sampling
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

class HadoopSenderSpec extends SpecificationWithJUnit with <PERSON><PERSON><PERSON> {

  "Hadoop Sender" should {

    val emptyString = ""

    "Correctly get class name" in {
      MessageEnvelope(() => ScalaTestMessage()).msgName should_== "ScalaTestMessage"
      MessageEnvelope(() => new JavaTestMessage()).msgName should_== "JavaTestMessage"
    }

    "Resolve lazyly" in {
      val settings = mock[HadoopForwardSettings]
      settings.getSamplingRatio(emptyString) returns Sampling.ALWAYS_IGNORED
      settings.a<PERSON><PERSON><PERSON> returns emptyString

      val scalaMessage = mock[ScalaTestMessage]
      val gotEvaluated = new AtomicBoolean(false)

      val hadoopSender = HadoopSender(settings)

      val env = MessageEnvelope(emptyString,
                                () => {
                                  gotEvaluated.set(true)
                                  scalaMessage
                                })

      hadoopSender.send(env)
      hadoopSender.sendExtended(env)

      there was no(scalaMessage).sendAsync(emptyString)
      gotEvaluated.get() should_== false
    }

    "Send" in {
      "Skip when sampling = 0" in {
        val settings = mock[HadoopForwardSettings]
        settings.getSamplingRatio(emptyString) returns Sampling.ALWAYS_IGNORED
        settings.apiKey returns emptyString

        val scalaMessage = mock[ScalaTestMessage]
        val javaMessage = mock[JavaTestMessage]

        val hadoopSender = HadoopSender(settings)
        hadoopSender.send(MessageEnvelope(emptyString, () => scalaMessage))
        hadoopSender.send(MessageEnvelope(emptyString, () => javaMessage))

        there was no(scalaMessage).sendAsync(emptyString)
        there was no(javaMessage).sendAsync(emptyString)
      }

      "Execute when sampling = 1" in {
        val settings = mock[HadoopForwardSettings]
        settings.getSamplingRatio(emptyString) returns Sampling.ALWAYS_SAMPLED
        settings.apiKey returns emptyString

        val scalaMessage = mock[ScalaTestMessage]
        scalaMessage.sendAsync(emptyString) returns SendResult.OK
        val javaMessage = mock[JavaTestMessage]
        javaMessage.sendAsync(emptyString) returns SendResult.OK

        val hadoopSender = HadoopSender(settings)
        hadoopSender.send(MessageEnvelope(emptyString, () => scalaMessage))
        hadoopSender.send(MessageEnvelope(emptyString, () => javaMessage))

        there was one(scalaMessage).sendAsync(emptyString)
        there was one(javaMessage).sendAsync(emptyString)
      }
    }

    "Send extended" in {
      "Execute when sampling is skipped" in {
        val settings = mock[HadoopForwardSettings]
        settings.getSamplingRatio(emptyString) returns Sampling.ALWAYS_IGNORED
        settings.apiKey returns emptyString

        val scalaMessage = mock[ScalaTestMessage]
        scalaMessage.sendAsync(emptyString) returns SendResult.OK
        val javaMessage = mock[JavaTestMessage]
        javaMessage.sendAsync(emptyString) returns SendResult.OK

        val hadoopSender = HadoopSender(settings)
        hadoopSender.sendExtended(MessageEnvelope(emptyString, () => scalaMessage), applySampling = false)
        hadoopSender.sendExtended(MessageEnvelope(emptyString, () => javaMessage), applySampling = false)

        there was one(scalaMessage).sendAsync(emptyString)
        there was one(javaMessage).sendAsync(emptyString)
      }
    }

  }

}
