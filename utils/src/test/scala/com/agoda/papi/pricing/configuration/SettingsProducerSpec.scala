package com.agoda.papi.pricing.configuration

import com.agoda.commons.dynamic.state.State
import com.typesafe.config.{Config, ConfigFactory}
import org.specs2.mutable.SpecificationWithJUnit

import scala.collection.JavaConverters._

class SettingsProducerSpec extends SpecificationWithJUnit {

  case class TestSettings(myNumber: Int, suppliers: Set[Int], enabled: Boolean)

  class TestSettingsProducer(externalConfig: State[Config], appConfig: Config)
    extends SettingsProducer[EmptySettings, TestSettings](externalConfig, appConfig, "feature.my-path") {

    override protected def mapStaticConfig(config: Config): EmptySettings = EmptySettings.EMPTY

    override protected def mapDynamicConfig(): TestSettings = TestSettings(
      getOrFail[Int](c => c.getInt("my-number"), _ > 0),
      getOrFail(c => c.getIntList("suppliers").asScala.map(_.toInt).toSet),
      getOrFallback(c => c.getBoolean("enabled"), true),
    )
  }

  private val baseConfig: Config = ConfigFactory.parseString(
    """
      |feature.my-path {
      |  suppliers = [332, 3038]
      |  my-number = 42
      |}
      |""".stripMargin,
  )

  "SettingsProducer" should {

    "consume setting from dynamic config" in {
      val dynamicConfig = State.of(ConfigFactory.parseString("""
                                                               |feature.my-path.my-number = 9001
                                                               |""".stripMargin))

      val producer = new TestSettingsProducer(dynamicConfig, baseConfig)
      producer.current.myNumber must_== 9001
    }

    "consume setting from fallback config when dynamic value doesn't validate" in {
      val dynamicConfig = State.of(ConfigFactory.parseString("""
                                                               |feature.my-path.my-number = -1
                                                               |""".stripMargin))

      val producer = new TestSettingsProducer(dynamicConfig, baseConfig)
      producer.current.myNumber must_== 42
    }

    "support default value when not set in dynamic config" in {
      val producer = new TestSettingsProducer(State.of(ConfigFactory.empty()), baseConfig)
      producer.current.enabled must_== true
    }

    "override default value when set in dynamic config" in {
      val dynamicObject = State.of(
        ConfigFactory.parseString(
          """
            |feature.my-path.enabled = false
            |""".stripMargin,
        ))

      val producer = new TestSettingsProducer(dynamicObject, baseConfig)
      producer.current.enabled must_== false
    }

    "read mandatory value from fallback when not set in dynamic" in {
      val producer = new TestSettingsProducer(State.of(ConfigFactory.empty()), baseConfig)
      producer.current.suppliers must_== Set(332, 3038)
    }

    "throw exception when mandatory setting is missing" in {
      new TestSettingsProducer(State.of(ConfigFactory.empty()), ConfigFactory.empty()) must throwA[
        NoSuchElementException]
    }

  }

}
