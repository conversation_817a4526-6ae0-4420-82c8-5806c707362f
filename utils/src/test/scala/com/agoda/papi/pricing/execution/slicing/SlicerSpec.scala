package com.agoda.papi.pricing.execution.slicing

import org.specs2.mutable.SpecificationWithJUnit

class SlicerSpec extends SpecificationWithJUnit {

  "Slicer" should {

    val slicer = new SlicerImpl {}

    "Produce slices with the number of items not greater than the maximum weight" in {
      slicer.slice(Seq(), 1, 0) should_== Seq.empty
      slicer.slice(Seq(1, 2, 3), 1, 0) should_== Seq(Seq(1), Seq(2), Seq(3))
      slicer.slice(Seq(1, 2, 3), 2, 0) should_== Seq(Seq(1, 2), Seq(3))
      slicer.slice(Seq(1, 2, 3), 3, 0) should_== Seq(Seq(1, 2, 3))
    }
  }

}
