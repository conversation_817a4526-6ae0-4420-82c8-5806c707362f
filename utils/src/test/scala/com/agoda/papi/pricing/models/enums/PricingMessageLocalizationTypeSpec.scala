package com.agoda.papi.pricing.discounting.models.enums

import com.agoda.papi.pricing.models.enums.PricingMessageLocalizationType
import org.specs2.mutable.SpecificationWithJUnit

class PricingMessageLocalizationTypeSpec extends SpecificationWithJUnit {
  "PricingMessageLocalizationType" should {
    "return all fields correctly" in {
      val result = PricingMessageLocalizationType.fields
      result.size should_== 6
    }

    "return hash code correctly" in {
      val result = PricingMessageLocalizationType.Text.hashCode
      result should_== 1
    }
  }
}
