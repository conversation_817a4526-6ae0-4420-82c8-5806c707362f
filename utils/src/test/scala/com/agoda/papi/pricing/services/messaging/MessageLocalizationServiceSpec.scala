package com.agoda.papi.pricing.services.messaging

import com.agoda.localization.Localization
import com.agoda.localization.{LocalizationSettings => LocalizationSettingsCommon}
import com.agoda.papi.pricing.models
import com.agoda.papi.pricing.models.LocalizationSettings
import com.agoda.papi.pricing.models.enums.PricingMessageLocalizationType._
import com.typesafe.config.ConfigFactory
import org.specs2.mutable.SpecificationWithJUnit

class MessageLocalizationServiceSpec extends SpecificationWithJUnit {

  private val impl: MessageLocalizationService = new MessageLocalizationServiceImpl {}

  private val defaultLocale = LocalizationSettings(
    Localization("en-us"),
    "$",
    None,
    1d,
  )

  val localizationSettings =
    Option(LocalizationSettingsCommon(Option(ConfigFactory.parseString("isCheckVariantAndScript = true"))))
  private val localeWithConfig = LocalizationSettings(
    Localization("en-us", localizationSettings),
    "$",
    None,
    1d,
  )

  private val highPrecisionLocale = models.LocalizationSettings(
    Localization("en-us"),
    "$",
    Some(2),
    0.01d,
  )

  "MessageLocalizationService" should {

    "Round percentage to 0 decimal places even if more are requested" in {
      val result = impl.getLocalizedValueByParameter(Percentage, "24.5", highPrecisionLocale)
      result must beSome("25 %")
    }

    "Round currency to the requested precision (0 places)" in {
      val result = impl.getLocalizedValueByParameter(Currency, "24.5", defaultLocale)
      result must beSome("$ 25")
    }

    "Round currency to the requested precision (2 places)" in {
      val result = impl.getLocalizedValueByParameter(Currency, "24.5", highPrecisionLocale)
      result must beSome("$ 24.50")
    }

    "Format date to locale" in {
      val result = impl.getLocalizedValueByParameter(Date, "2020-05-13", highPrecisionLocale)
      result must beSome("13 05 2020")
    }

    "Format number to 0 decimal places" in {
      val result = impl.getLocalizedValueByParameter(Number, "145.3", highPrecisionLocale)
      result must beSome("145")
    }

    "Format number to contain value separator" in {
      val result = impl.getLocalizedValueByParameter(Number, "1000", localeWithConfig)
      result must beSome("1,000")
    }

    "Pass text back as-is" in {
      val result = impl.getLocalizedValueByParameter(Text, "Hello world", defaultLocale)
      result must beSome("Hello world")
    }

    "Reject non-decimal as currency" in {
      val result = impl.getLocalizedValueByParameter(Currency, "Hello world", defaultLocale)
      result must beNone
    }

    "Reject non-decimal as number" in {
      val result = impl.getLocalizedValueByParameter(Number, "Hello world", defaultLocale)
      result must beNone
    }

    "Reject non-decimal as percentage" in {
      val result = impl.getLocalizedValueByParameter(Percentage, "Hello world", defaultLocale)
      result must beNone
    }

    "Reject non-date as date" in {
      val result = impl.getLocalizedValueByParameter(Date, "Hello world", defaultLocale)
      result must beNone
    }
  }

}
