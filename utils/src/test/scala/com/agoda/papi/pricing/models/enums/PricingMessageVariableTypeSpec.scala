package com.agoda.papi.pricing.discounting.models.enums

import com.agoda.papi.pricing.models.enums.PricingMessageVariableType
import org.specs2.mutable.SpecificationWithJUnit

class PricingMessageVariableTypeSpec extends SpecificationWithJUnit {
  "PricingMessageVariableTypeSpec" should {
    "return all fields correctly" in {
      val result = PricingMessageVariableType.fields
      result.size should_== 4
    }

    "return hash code correctly" in {
      val result = PricingMessageVariableType.Price.hashCode
      result should_== 3
    }
  }
}
