package com.agoda.papi.pricing.execution.slicing

import com.agoda.papi.pricing.execution.slicing.WeightExtractors.Weight
import org.specs2.mutable.SpecificationWithJUnit

class WeightedSlicerSpec extends SpecificationWithJUnit {
  "WeightedSlicer" should {
    val slicer = new WeightedSlicerImpl {}
    implicit val intWeightExtractor: WeightExtractor[Int] = new WeightExtractor[Int] {
      override def getWeight(item: Int): Weight = item
    }

    "slice if enabled" in {

      "don't slice if it's maxWeight is 0 or less" in {
        slicer.slice(Seq(10, 10), 0) should_== Seq(Seq(10, 10))
        slicer.slice(Seq(10, 10), -1) should_== Seq(Seq(10, 10))
      }

      "slice otherwise" in {
        slicer.slice(Seq(10, 10), 10) should_== Seq(Seq(10), Seq(10))
      }
    }

    "do balanced slicing by weight" in {
      val max10 = 10

      "No elements" in {
        slicer.slice(Seq.empty[Int], max10) should_== Seq(Seq.empty)
      }

      "Single item cases" in {
        slicer.slice(Seq(1), max10) should_== Seq(Seq(1))
        slicer.slice(Seq(9), max10) should_== Seq(Seq(9))
        slicer.slice(Seq(20), max10) should_== Seq(Seq(20))
      }

      "2 elements" in {
        slicer.slice(Seq(1, 2), max10) should_== Seq(Seq(1, 2))
        slicer.slice(Seq(4, 5), max10) should_== Seq(Seq(4, 5))
        slicer.slice(Seq(5, 5), max10) should_== Seq(Seq(5, 5))
        slicer.slice(Seq(1, 9), max10) should_== Seq(Seq(9, 1))
        slicer.slice(Seq(4, 9), max10) should_== Seq(Seq(9), Seq(4))
        slicer.slice(Seq(9, 9), max10) should_== Seq(Seq(9), Seq(9))
        slicer.slice(Seq(10, 5), max10) should_== Seq(Seq(10), Seq(5))
        slicer.slice(Seq(15, 15), max10) should_== Seq(Seq(15), Seq(15))
        slicer.slice(Seq(15, 1), max10) should_== Seq(Seq(15), Seq(1))

        "buffer cases" in {
          slicer.slice(Seq(5, 6), max10) should_== Seq(Seq(6, 5))
          slicer.slice(Seq(9, 2), max10) should_== Seq(Seq(9, 2))
          slicer.slice(Seq(10, 1), max10) should_== Seq(Seq(10, 1))
        }
      }

      "3 elements" in {
        slicer.slice(Seq(1, 2, 3), max10) should_== Seq(Seq(1, 2, 3))
        slicer.slice(Seq(1, 2, 9), max10) should_== Seq(Seq(9, 1), Seq(2))
        slicer.slice(Seq(4, 10, 3), max10) should_== Seq(Seq(10), Seq(4, 3))
        slicer.slice(Seq(20, 3, 15), max10) should_== Seq(Seq(20), Seq(15), Seq(3))
        slicer.slice(Seq(8, 6, 7), max10) should_== Seq(Seq(8), Seq(7), Seq(6))
        slicer.slice(Seq(7, 9, 3), max10) should_== Seq(Seq(9), Seq(7, 3))
        slicer.slice(Seq(10, 1, 1), max10) should_== Seq(Seq(10), Seq(1, 1))

        "buffer cases" in {
          slicer.slice(Seq(5, 1, 5), max10) should_== Seq(Seq(5, 5, 1))
          slicer.slice(Seq(7, 9, 4), max10) should_== Seq(Seq(9), Seq(7, 4))
        }
      }

      "N elements" in {
        slicer.slice(Seq(1, 1, 10, 1, 1), max10) should_== Seq(Seq(10), Seq(1, 1, 1, 1))
        slicer.slice(Seq(5, 3, 36, 6, 1, 10, 2, 7), max10) should_== Seq(Seq(36),
                                                                         Seq(10),
                                                                         Seq(7, 1, 2),
                                                                         Seq(6, 3),
                                                                         Seq(5))
        slicer.slice(Seq(3, 3, 3, 4, 1, 7, 7, 7), max10) should_== Seq(Seq(7, 1), Seq(7, 3), Seq(7, 3), Seq(4, 3))
        slicer.slice(Seq(9, 5, 0, 6), max10) should_== Seq(Seq(9, 0), Seq(6, 5))
        slicer.slice(Seq(8, 8, 3, 3, 3), max10) should_== Seq(Seq(8), Seq(8), Seq(3, 3, 3))
      }
    }
  }
}
