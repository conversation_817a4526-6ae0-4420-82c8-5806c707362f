package com.agoda.papi.pricing.services.messaging

import com.agoda.papi.constants.CurrencyCode
import com.agoda.papi.pricing.services.ExchangeDataService
import com.agoda.utils.mocks.ExchangeDataServiceMock
import org.specs2.mutable.SpecificationWithJUnit

class ExchangeDataServiceSpec extends SpecificationWithJUnit {

  private val service: ExchangeDataService = new ExchangeDataServiceMock {}

  "ExchangeDataService" should {

    "toUSD correctly" in {
      service.toUSD(0.0, CurrencyCode.USD) shouldEqual 0.0
      service.toUSD(1.0, CurrencyCode.USD) shouldEqual 1.0
      service.toUSD(1.1111111, CurrencyCode.USD) shouldEqual 1.1111111
      service.toUSD(-1.1111111, CurrencyCode.USD) shouldEqual -1.1111111

      service.toUSD(0.0, CurrencyCode.THB) shouldEqual 0.0
      service.toUSD(1.0, CurrencyCode.THB) shouldEqual 0.02811998234065109 // ExchangeDataServiceMock is 0.02812
      service.toUSD(1.1111111, CurrencyCode.THB) shouldEqual 0.03124442451050141
      service.toUSD(-1.1111111, CurrencyCode.THB) shouldEqual -0.03124442451050141
    }
  }

}
