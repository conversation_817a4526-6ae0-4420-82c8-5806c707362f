import org.specs2.concurrent.ExecutionEnv
import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.duration._
import scala.concurrent.{Await, Future}

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 9/13/2016 AD.
  */
class ImplicitsSpec(implicit ec: ExecutionEnv) extends SpecificationWithJUnit {

  import com.agoda.utils.Implicits._

  "Option[Future[A]] implicits" should {
    "convert None to Future[Successful[]]" in {
      val future: Option[Future[Int]] = None
      Await.result(future.toFuture, 10.millis) should_== None
    }

    "convert Some to Future[Option[A]]" in {
      val successOptionFuture = Some(Future.successful(42))
      Await.result(successOptionFuture.toFuture, 100.millis) should_== Some(42)

      val exception = new RuntimeException()
      val failOptionFuture = Some(Future.failed(exception))
      Await.result(failOptionFuture.toFuture, 100.millis) should throwA(exception)
    }
  }

  "Rounding implicits" should {
    "return correct sql parameter from passed list" in {
      9.999.roundAt(2) should beEqualTo(10.0)
      9.997.roundAt(2) should beEqualTo(10.0)
      9.995.roundAt(1) should beEqualTo(10.0)
      9.994.roundAt(1) should beEqualTo(10.0)
    }
  }

  "Object implicits" should {
    class Test
    val notNullObj: Test = new Test
    val nullObj: Test = null

    "isNull check correctly" in {
      nullObj.isNull should_== true
      notNullObj.isNull should_== false
    }

    "isNotNull check correctly" in {
      nullObj.isNotNull should_== false
      notNullObj.isNotNull should_== true
    }
  }

  "Collection implicits" should {
    case class Obj(id: Int, prop: String)
    // default ordering of Obj is by id
    implicit val ordering: Ordering[Obj] = Ordering.by(_.id)

    "Map & set tests" in {
      val emptyList: List[Obj] = Nil

      "getMap func test" in {
        emptyList.getMap(_.id, _.prop) should_== Map.empty
        List(Obj(1, "A"), Obj(2, "B"), Obj(3, "C")).getMap(_.id, _.prop) should_== Map(1 -> "A", 2 -> "B", 3 -> "C")
      }

      "getMap (empty value) func test" in {
        emptyList.getMap(_.id) should_== Map.empty
        List(Obj(1, "A"), Obj(2, "B"), Obj(3, "C")).getMap(_.id) should_== Map(1 -> Obj(1, "A"),
                                                                               2 -> Obj(2, "B"),
                                                                               3 -> Obj(3, "C"))
      }

      "getFilteredMap func test" in {
        emptyList.getFilteredMap(_.id % 2 != 0, _.id, _.prop) should_== Map.empty
        List(Obj(1, "A"), Obj(2, "B"), Obj(3, "C")).getFilteredMap(_.id % 2 != 0, _.id, _.prop) should_== Map(1 -> "A",
                                                                                                              3 -> "C")
      }

      "getSet func test" in {
        emptyList.getSet(_.id) should_== Set.empty
        List(Obj(1, "A"), Obj(2, "B"), Obj(3, "C"), Obj(1, "A"), Obj(2, "B"), Obj(3, "C")).getSet(_.id) should_== Set(1,
                                                                                                                      2,
                                                                                                                      3)
      }

      "getFilteredSet func test" in {
        emptyList.getFilteredSet(_.prop > "A", _.id) should_== Set.empty
        List(Obj(1, "A"), Obj(2, "B"), Obj(3, "C"), Obj(1, "A"), Obj(2, "B"), Obj(3, "C"))
          .getFilteredSet(_.prop > "A", _.id) should_== Set(2, 3)
      }

    }

    "Min and Max tests" in {

      "return None when called on empty Iterable" in {
        val emptyList: List[Obj] = Nil

        emptyList.minOption must beNone
        emptyList.minByOption(_.prop) must beNone
        emptyList.maxOption must beNone
        emptyList.maxByOption(_.prop) must beNone
      }

      "call the standard methods when Iterable is NonEmpty" in {
        val list: List[Obj] = List(Obj(2, "A"), Obj(4, "C"), Obj(1, "B"), Obj(3, "D"))

        list.minOption must beSome(Obj(1, "B"))
        list.minByOption(_.prop) must beSome(Obj(2, "A"))
        list.maxOption must beSome(Obj(4, "C"))
        list.maxByOption(_.prop) must beSome(Obj(3, "D"))
      }
    }

    "doesContainMoreElementsThan tests" in {
      "Lazyness test" in {
        val seq: Seq[Int] = Stream.from(1)
        seq.containsMoreElementsThan(2) should_== true
        seq.containsMoreElementsThan(10) should_== true
      }

      "Func test" in {
        val list = List(Obj(1, "A"), Obj(2, "B"), Obj(3, "C"))

        list.take(0).containsMoreElementsThan(0) should_== false
        list.take(0).containsMoreElementsThan(1) should_== false
        list.take(0).containsMoreElementsThan(2) should_== false

        list.take(1).containsMoreElementsThan(0) should_== true
        list.take(1).containsMoreElementsThan(1) should_== false
        list.take(1).containsMoreElementsThan(2) should_== false

        list.take(2).containsMoreElementsThan(0) should_== true
        list.take(2).containsMoreElementsThan(1) should_== true
        list.take(2).containsMoreElementsThan(2) should_== false

        list.containsMoreElementsThan(0) should_== true
        list.containsMoreElementsThan(1) should_== true
        list.containsMoreElementsThan(2) should_== true
        list.containsMoreElementsThan(3) should_== false
      }
    }
  }

  "distinctByRef" should {
    case class Test(x: Int)

    "remove duplicated elements from list of objects" in {
      val a = Test(0)
      val b = Test(1)
      val c = Test(2)
      List(a, b, a, c, b, b, b, c, c, a, b, c).distinctByRef must_== List(a, b, c)
    }

    "don't remove elements with different references from list of objects" in {
      val a = Test(0)
      val b = Test(1)
      val c = Test(2)
      List(a, b, c, Test(0), Test(1), Test(2)).distinctByRef must_== List(a, b, c, a, b, c)
    }

    "remove duplicated elements from list of Options" in {
      val a = Test(0)
      val b = Test(1)
      val c = Test(2)
      List(a, b, a, c, b, b, b, c, c, null, a, b, c).map(Option(_)).distinctByRef must_== List(a, b, c)
    }

    "don't remove elements with different references from list of Option" in {
      val a = Test(0)
      val b = Test(1)
      val c = Test(2)
      List(a, b, c, Test(0), null, Test(1), null, Test(2)).map(Option(_)).distinctByRef must_== List(a, b, c, a, b, c)
    }
  }

  "Iterable of futures implicits" should {

    val maxWaitTime = 1 second

    "Return a future empty iterable, if the input is empty" in {
      val res = Seq.empty[(Int, Future[Int])].toFutureSequence
      Await.result(res.map(r => r should ===(Iterable.empty)), maxWaitTime)
    }

    "Perform the correct transformation" in {
      val a = 1 -> Future.successful(2)
      val b = 3 -> Future.successful(4)
      val res = Map(a, b).toFutureSequence
      Await.result(res.map(r => r should ===(Iterable((1, 2), (3, 4)))), maxWaitTime)
    }

    "Return a future map when calling futureSequenceMap" in {
      val a = 1 -> Future.successful(2)
      val b = 3 -> Future.successful(4)
      val res = Map(a, b).toFutureSequenceMap
      Await.result(res.map(r => r should beTypedEqualTo(Map(1 -> 2, 3 -> 4))), maxWaitTime)
    }

    "Be a failed future if at least one future fails" in {
      val exception = new RuntimeException()
      val a = (1, Future.successful(1))
      val b = (2, Future.failed(exception))
      Seq(a, b).toFutureSequence should throwAn(exception).await(0, maxWaitTime)
    }

  }
}
