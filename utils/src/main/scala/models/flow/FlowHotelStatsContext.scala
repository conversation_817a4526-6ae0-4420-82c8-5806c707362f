package models.flow

import java.util.concurrent.{ConcurrentHashMap, ConcurrentMap}
import java.util.concurrent.atomic.{AtomicBoolean, LongAdder}
import scala.collection.concurrent.TrieMap
import scala.collection.concurrent.TrieMap
import scala.jdk.CollectionConverters.collectionAsScalaIterableConverter

//TODO: move this to Supply
class FlowHotelStatsContext {
  private val hsbRemovedHotel = new LongAdder
  private val sdaRemovedHotel = new LongAdder
  private val partnerFilteredHotel = new LongAdder
  private val h3Stats: ConcurrentMap[Long, H3Stat] = new ConcurrentHashMap()

  def registerHSBRemovedHotel(removeCount: Int = 1) = hsbRemovedHotel.add(removeCount)
  def registerSDARemovedHotel(removeCount: Int = 1) = sdaRemovedHotel.add(removeCount)
  def registerPartnerFilteredHotel(removeCount: Int = 1) = partnerFilteredHotel.add(removeCount)
  def registerH3Stats(propertyId: Long,
                      isPushFlow: Boolean,
                      enabled: Boolean,
                      isApplicable: Boolean,
                      droppedRoom: Int,
                      usingH3v2: Boolean,
                      masterRoomsBeforeH3: Set[Long],
                      masterRoomsAfterH3: Set[Long]): Unit = {
    val stat = h3Stats.computeIfAbsent(propertyId, _ => H3Stat())
    val s = if (isPushFlow) stat.push else stat.pull
    s.enabled.set(enabled)
    s.applicable.set(isApplicable)
    s.droppedRoom.add(droppedRoom)
    s.usingH3v2.set(usingH3v2)
    masterRoomsBeforeH3.foreach { roomTypeId =>
      s.masterRoomsBeforeH3.putIfAbsent(roomTypeId, ())
    }
    masterRoomsAfterH3.foreach { roomTypeId =>
      s.masterRoomsAfterH3.putIfAbsent(roomTypeId, ())
    }
  }

  def updateH3Stats(key: Long, value: H3Stat): Unit = h3Stats.put(key, value)

  def getHSBRemovedHotel = hsbRemovedHotel.intValue()
  def getSDARemovedHotel = sdaRemovedHotel.intValue()
  def getPartnerFilteredHotel = partnerFilteredHotel.intValue()
  def getH3Stats = h3Stats

  def getH3ImpactFromStats(): (Boolean, String) = {
    val stats = h3Stats.values.asScala.toList

    val h3HasImpact = stats.exists(stat => stat.isH3Applied)

    val hasAVariant = stats.exists(stats => stats.isH3Applied && !stats.isUsingH3v2)
    val hasBVariant = stats.exists(stats => stats.isH3Applied && stats.isUsingH3v2)
    val h3ExpVariants = (hasAVariant, hasBVariant) match {
      case (true, true) => "ab"
      case (true, _) => "a"
      case (_, true) => "b"
      case _ => ""
    }

    (h3HasImpact, h3ExpVariants)
  }
}

case class H3StatsValue(
  enabled: AtomicBoolean = new AtomicBoolean(),
  applicable: AtomicBoolean = new AtomicBoolean(),
  droppedRoom: LongAdder = new LongAdder(),
  usingH3v2: AtomicBoolean = new AtomicBoolean(),
  masterRoomsBeforeH3: TrieMap[Long, Unit] = new TrieMap[Long, Unit].empty,
  masterRoomsAfterH3: TrieMap[Long, Unit] = new TrieMap[Long, Unit].empty,
)

case class H3MasterRoomStats(masterRoomsBeforeH3: Int,
                             masterRoomsAfterH3: Int,
                             masterRoomsBeforeSB: Int,
                             masterRoomsAfterSB: Int)

case class H3Stat(push: H3StatsValue = H3StatsValue(),
                  pull: H3StatsValue = H3StatsValue(),
                  masterRoomStats: Option[H3MasterRoomStats] = None) {
  private def isEnabled: Boolean = push.enabled.get() || pull.enabled.get()
  private def isApplicable: Boolean = push.applicable.get() || pull.applicable.get()

  def isH3Applied: Boolean = push.droppedRoom.intValue() > 0 || pull.droppedRoom.intValue() > 0
  def isH3Applicable: Boolean = isEnabled || isApplicable
  def isUsingH3v2: Boolean = push.usingH3v2.get() || pull.usingH3v2.get()

  def getMasterRoomStats: H3MasterRoomStats = this.masterRoomStats.getOrElse(
    H3MasterRoomStats(
      masterRoomsBeforeH3 = (push.masterRoomsBeforeH3.keySet ++ pull.masterRoomsBeforeH3.keySet).size,
      masterRoomsAfterH3 = (push.masterRoomsAfterH3.keySet ++ pull.masterRoomsAfterH3.keySet).size,
      masterRoomsBeforeSB = -1,
      masterRoomsAfterSB = -1,
    ),
  )
}
