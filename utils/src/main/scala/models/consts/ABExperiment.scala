package models.consts

import com.agoda.papi.constants.PlatformID

// Whenever you add or remove experiment here, do the same in ABTest in cucumber project
trait ABExperiment {

  // Force ALL experiments to specific variant when running ACM
  // by passing AG-HTTP-FORCED-EXPERIMENTS header with FORCE-EXP-ON-ACM=<forced_variant>
  // or passing via experiments field in DF request.
  val FORCE_EXP_ON_ACM = "FORCE-EXP-ON-ACM"

  //  Bugs
  val OVERRIDE_ORIGINAL_TOTAL = "PLTAPI-761"

  //  Infra
  val DISABLE_FEATURES_ON_UNKNOWN_BOT = "SCAL-851"

  //  Features
  val DEFAULT_OCCUPANCY_SEARCH = "DRAGON-2442"

  val NEED_LOCAL_PAYMENT_FOR_AGENCY = "NBF-1398"

  // COR
  val USE_MONTHLY_CHECKIN_COR = "FUSION-4945"

  val DISABLE_PRICESTREAM_BOOKINGCOUNTINFO = "FUSION-4915-1"

  // PFE
  val FIX_AMOUNT_CHECK_SECRET_DEAL = "PFE-7874"
  val FIX_BREAKFAST_UPSELL_SAVING_AMOUNT = "PFE-9248"
  val ENABLE_PRICE_CHANGE_FEATURE = "PFE-10385-KS"
  val FIX_CHEAPEST_LOGIC_FOR_AUTO_APPY_PROMOS = "PFE-11907"
  val FIX_APS_COMPARE_LOGIC = "PFE-12120"

  // YCS
  val PROMOTIONAL_RATE_CATEGORY = "IM-2719"

  // Affiliate
  val HOTEL_MVP_PROMOTIONS = "AFF-1542"
  val HASH_DMCUID_TO_ROOMIDENTIFIER = "AFF-3064"
  // Apply pf on tax weight
  val APPLY_PF_ON_TAX_WEIGHT = "AFFMSE-571"
  val GHOST_ROOM_FILTERING = "PAPI-21972"
  val UPDATE_SURCHARGE_PRICE_BREAKDOWN = "PAPI-22657"
  val FORCE_SKIP_UPDATE_SURCHARGE_PRICE_BREAKDOWN = "PAPI-22832"
  val FIX_TAX_TO_PROPERTY_APPLY_RATE = "PAPI-22947"
  val CALCULATE_MIN_RETAIL_SELL_PRICE = "PAPI-23178"

  val FIX_VARIABLE_TAX_FOR_PARTNER = "MSEP-1426"

  // Promocode
  val DISABLE_EMPLOYEE_DEAL_FOR_WEB = "DISABLE-EMPLOYEE-DEAL-FOR-WEB"
  val CC_CAMP_AUTO_APPLY_LANDING_CID_ON_APP = "PMC-70"
  val PMC_SORT_CC_BY_MAX_DISCOUNT = "PMC-3215"
  val PMC_STOP_FETCHING_CDB_FOR_ENRICHMENT = "PMC-5098"
  // Consolidated applied discounts
  val CONSOLIDATED_APPLIED_DISCOUNTS = "PMC-3576"
  val CONSOLIDATED_APPLIED_DISCOUNTS_FIX = "PMC-3777"
  val CONSOLIDATED_APPLIED_DISCOUNTS_ONLY_AUTO_APPLY = "PMC-3827"
  val AUTO_APPLY_ROUNDING_FIX = "PMC-5254"
  // Consolidated price peek on SSR
  val CONSOLIDATED_PEEK = "PMC-4465"
  // WebView wallet phase two to include auto-apply promos and rate campaigns
  val PMC_UPDATE_DFE_SEARCH_PERFORMANCE_TAGS = "PMC-4165"
  val PMC_GET_PROMO_PRIORITY_FROM_SB = "PMC-4205"
  val PMC_HYBRID_DISCOUNT_PROCEDURE = "PMC-4243"
  val PMC_SUPPORT_OFFER_LEVEL = "PMC-4311"
  val PMC_CONSOLIDATED_APPLIED_DISCOUNTS_METRIC = "PMC-4650"
  val PMC_CONSOLIDATED_APPLIED_DISCOUNTS_PRICE_DISPLAY_TYPE = "PMC-4603"
  val PMC_REDUCE_OFFER_PROMOCODE_SIZE_FROM_SB = "PMC-4808"
  val PMC_AUTO_APPLY_PAYMENT_METHOD_FIX = "PMC-4794"
  val PMC_DEPRECATE_CLIENT_DISCOUNT_FF = "PMC-5283"
  val PMC_AUTO_APPLY_RATE_CAMPAIGN = "PMC-4858"
  val PMC_AUTO_ASSIGN_PROMO_BF_BUG_FIX_AND_PRIORITY_UPDATE = "PMC-5332"
  val PMC_ENABLE_RATE_CAMPAIGN_MESSAGE_INFO = "PMC-5600"
  val PMC_ENABLE_CHECK_NON_EMPTY_PROMOCODE = "PMC-5978"
  val PMC_ENABLE_RATE_CAMPAIGN_FEEDBACK = "PMC-5610"
  val PMC_FIX_FENCES_COMPARING_ROOMS = "PMC-6102"
  val PMC_ENABLE_VALUE_TAG = "PMC-6072"

  val POC_SUPPLIER_NOT_FOUND = "VEL-1319"
  // ag-rule-engine
  val AG_RULE_ENGINE_SOYBEAN = "PRICING-45"

  // POPT
  val RATE_CHANNEL_SWAP = "POPT-9909"

  // TAX
  val DISPLAY_TAX_TO_PROPERTY = "RCT-1353"
  val FIX_TAX_FINANCIAL_BREAKDOWN_QUANTITY_ON_SURCHARGE_EXTRABED = "NEP-19658"
  val TAX_CMS_APPLY_FOR_V2 = "VYG-937"
  val TMAPI_TAX_TYPE = "IMV-2362"
  val CLEAN_HP_TAX = "NEP-25984"

  // Duplicate room metric logging
  val LOG_DUPLICATE_ROOM_OFFERS_METRIC_IN_BOOOKING_REQUEST = "PFZ-1386"

  // Super Aggregator
  val BACKUP_UID_ALGORITHM = "PAPI-12316"
  val INCREASE_RETRY_FOR_BF = "PAPITIMEOUT1"

  val MIX_N_SAVE_SOLDOUT_PP = "DJ-968"

  // PACKAGES
  val DISABLE_MULTI_HOTEL = "MPWPI-REMOVE-MULTIHOTELS-BE" // MPWPI-17

  val ABSORPTION_EXPERIMENT_ID = 1036961

  // CEG-HZ, Cancel & Rebook
  val FIX_ROUNDING_ERROR_FOR_REBOOKING = "CEGHZ-777"

  val UNBLOCK_PRIUS_IN_JP = "PLECS-9725"

  // Affiliate fallback unsupport payment currency
  val AFFILIATE_FALLBACK_CURRENCY = "AFF-3021"

  //  0D1N and 0D100P cancellation Policy fix for PCLN
  val AFFILIATE_CANCELLATION_POLICY_0D = "PAPI-17861"

  //  0DxN (OD1N, OD2N) cancellation Policy CMS ID update
  val CANCELLATION_POLICY_CMSID_UPDATE = "PAPI-18280"

  // xD0N, xD0P cancellation policy fix
  val AFFILIATE_ZERO_FEE_CANCELLATION_POLICY = "PAPI-19005"

  // Key Account
  val ADJUST_COMMISSION_MARRIOTT = "SUPPIO-5595"

  // CashBack
  val ZERO_CASHBACK_ENABLED = "CASH-2179"
  val ZERO_CASHBACK_LOGIC_REMOVAL_EXP = "CASH-2081"
  val FIX_CASHBACK_WITH_SEAMLESS_CROSSSELL = "CASH-3373"

  // Show only pull supply
  val IS_SHOW_ONLY_PULL = "SCP-2869"

  // Escapes
  val FILTER_ASO_BENEFITS = "ESC-408"
  val CHEAPEST_STAY_PACKAGE_RATE_PLAN_FIX = "ESC-930"
  val USEUSDSELLIN_IN_ASOBENEFITDISCOUNT = "ESC-977"

  // Feature degradation
  val APSPeekDegradation = "SCAL-999"
  val MixNSaveDegradation = "SCAL-1000"
  val HeisenbergDegradation = "SCAL-1009"
  val SoybeanDegradation = "SCAL-1008"

  val NegativeSdaFilterLow = "SCAL-1497-LOW"
  val NegativeSdaFilterMedium = "SCAL-1497-MEDIUM"
  val NegativeSdaFilterHigh = "SCAL-1497-HIGH"

  // PricePush optimization
  val PRICE_PUSH_FENCING = "SCAL-1042"

  // MSE Pricing
  val ABO_CID_TO_ORIGIN_MAPPER = "MSEP-336"

  // GoLocal Migration
  val WhiteListStackChannelFlagPlatform = Set(PlatformID.DesktopWeb, PlatformID.MobileWeb, PlatformID.MobileAPI)

  // Operation (SCALABILITY)
  val FIX_PAYMENT_AMOUNT_MISMATCH = "SCAL-1085"

  val BOOKING_HUERISTIC = "SCAL-1102"
  val SKIP_ZERO_ALLOTMENT_ROOMS_FOR_SOYBEAN = "SCAL-1267"
  val SKIP_ZERO_ALLOTMENT_ROOMS_FOR_SOYBEAN_OPENDOOR = "SCAL-1268"
  val ADD_GOVERNMENT_TAX_TO_AFF_MERCHANT_COM_SELLIN = "SCAL-1167"
  val FIX_EXTRA_BED_PAYMENT_AMOUNT_MISMATCH = "SCAL-1289"
  val IS_APPLY_FIX_FOR_PARTNER_CANCELLATION_POLICY = "SCAL-8490"

  val CAP_STAY_OCCUPANCY = "SCAL-1237"
  val CAP_REQUESTED_EXTRABED = "SCAL-1245"
  val OUTPUT_FALLBACK_STAYOCCUPANCY_FOR_BOOKING = "SCAL-1463"
  val REMOVE_ZERO_ALLOTMENT_FEATURE_FLAG = "SCAL-1433"

  val PULL_TO_USE_PUSH_TIMEOUT = "VEL-1232"

  // DFOPS
  val FIX_MISSING_CHILD_STAYOCCUPANCY = "DFOPS-276"
  val FIX_INCORRECT_NOCREDITCARD_CHEAPEST = "DFOPS-994"
  val EXCLUDE_APS_FOR_PRIUS = "VEL-1737"

  // GIN-217168
  val FIX_ELAPI_CURRENCY_UPLIFT = "DFOPS-1538"
  // GIN-344260
  val FIX_DOUBLE_DEDUCT_POINTS = "DFOPS-3864"

  val AFFILIATE_COUNT_EXTRABED_AS_STAYOCC = "DFOPS-1714"
  val STOP_OVERRIDE_ROOM_SELECTION_FOR_AFFILIATE = "DFOPS-2509"

  // GIN-282650
  val VALIDATE_AND_REJECT_INCONSISTENCY_ROOM_ITEM_BREAKDOWN = "DFOPS-2744"

  val FIX_ESS_ITEM_ROOM_NO = "DFOPS-3900"

  // ASQ
  val REMOVE_BNPL_FOR_PHUKET = "OPA-1878"

  // Agoda Japan
  val UPDATE_STAY_OCCUPANCY_FROM_ROOM_ALLOCATION = "JP-1518"

  // JPST
  val IS_PROMO_DISCOUNT_PER_GUEST_VALIDATE = "JPST-573"

  val LAST_MINUTE_BOOKING_INSIDE_POLICY = "HBFP-1656"

  // Loyalty
  val LOYALTY_VIP_PMC = "AM-7115"
  val EXCLUDE_INDIA_HOTEL_FOR_AGODA_CASH_REDEMPTION = "LOY-8916"
  val REMOVE_VIP_BADGE_FOR_APS_RETAIL_CHANNEL = "LOY-8972"

  // Velocity
  val GET_HOTEL_CONTENT_TO_ENRICH_RATEPLAN = "VEL-452"
  val ENABLE_SOYBEAN_CIRCUIT_BREAKER_FOR_ALL_REQUEST = "VEL-1108"
  val MIGRATE_USD_SELLALLIN_TO_USE_EBE = "VEL-1091"
  val DECIMAL_FORMAT_THREAD_LOCAL_CACHE_OPTIMIZATION = "VEL-1750"
  val PARALLEL_FETCH_PAST_BOOKINGINFO = "VEL-1745"
  val UNEXPOSED_MARGIN_PER_NIGHT_USD = "VEL-2227"
  val BOOKING_CUT_OFF_TIME_VALIDATION_WITH_PRICE_GUARANTEE = "VEL-1298"

  val FEE_WAIVER_KILLSWITCH = "VEL-1240"

  val MATCH_ALL_FIELD_FOR_ROOMIDEN_IN_EXACT_MATCH = "VEL-1325"

  val LOG_PRICE_CHANGES_ON_BF = "VEL-1393"
  val CHECKIN_FLIP_AA_TEST = "VEL-1533"

  // Velocity - payment module
  val MIGRATE_PREPARE_INTERNAL_LOYALTY_TO_PAYMENT_MODULE = "VEL-1239"
  val MIGRATE_REDEEM_AMOUNT_TO_USE_EBE = "VEL-1471"

  // Velocity - price calculation module
  val REFACTOR_PRICE_CALCULATION_FUNCTION = "VEL-2070"

  val MARRIOTT_SURCHARGE_ISSUE = "VEL-1935"
  val PASS_ZERO_COMMISSION_FOR_NON_COMMISSIONABLE_SURCHARGE = "VEL-2083"
  // PAPI GraphQL migration
  val USE_REQUIRED_BASIS_FROM_PAPI = "FUSION-4663"

  // Payment
  val BLOCK_BNPL_FOR_INDIA_OUTBOUND = "UPF-423"

  // ExternalLoyalty
  val PASS_ERROR_CODE_IN_LOYALTY_RESPONSE = "BWHF-4335"
  val UPDATE_SPREAD_FOR_PRICING = "BWHF-4466"
  val LOYALTY_PROMOTIONS = "BWHF-4633"
  val INSTANT_ACCRUAL = "INSTANT-ACCRUAL"
  val TIER_PREVIEW = "BWHF-4747"
  val SPREAD_WITH_NUM_OF_ROOMS_EARN = "BWHF-5097"
  val SPREAD_WITH_NUM_OF_ROOMS_BURN = "BWHF-5097-BURN"
  val ELAPI_ROOM_QUANTITY_AND_DURATION = "BWHF-5148"

  // Variable Tax
  val DISABLE_VARIABLE_TAX_FOR_AGENCY = "NEP-19495"
  val ENABLE_MALAYSIA_TOURISM_TAX_FOR_CITIUS = "WLBF-1857"

  // Fix Supplier ID to use room supplier ID instead of hotel supplier ID
  val ENABLE_ROOM_SUPPLIER_ID = "VYG-2610"

  // Fix Uplift Exchange rate for M150
  val FIX_UPLIFT_EXCHANGE_FOR_M150 = "DFOPS-1412"

  val DISABLE_CUMULATIVE_DISCOUNT_FOR_XML = "DFOPS-1542"

  val DEPRECATE_LOW_PRECHECK_ACCURACY = "VEL-760"

  // Price Freeze
  val ENABLE_REFUNDABLE_ALTERNATIVE_PRICE_FREEZE = "PFZ-1037"
  val DISABLE_SUPPLIER_WHITE_LIST_FILTER_PRICE_FREEZE = "PFZ-1370"

  // Consumer fintech
  val ESS_FORCE_COUNTRY = "ESS-TAXCOUNTRY-CHECK"
  val SMART_SAVER = "CFF-800-SMART-SAVER"
  val CONFIN_ALL_SUPPLIER = "CFF-804-ALL-SUPPLIER"
  val DISABLE_CXL_REBOOK_VALIDATION = "CFF-997"
  val SMART_SAVER_NHA = "NHAFE-1148"
  val SKIP_LOCAL_CURRENCY_CONVERSION = "CFF-1117"
  // This experiment is allocated on rebook agent, due to DF does not have correct UserId
  val REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3 = "CFF-1118"
  // RTA Pricing for External Loyalty Flow
  val DF_RTA_PRICING = "DF-RTA-PRICING"
  val REMOVE_PRICE_MATCHING_FOR_REBOOKING = "CFF-1146-REMOVE-PRICE-MATCHING"

  // ESS
  val ESS_PUBLISH_PRICE = "CFF-237"
  val ENABLE_SERVICE_TAX_COUNTRY = "TW-ESS-TAXLIB-UPDATE"

  // To expose per book capacity for exact match occupancy search
  val IS_ENABLE_PER_BOOK_CAPACITY = "PAPI-19144"

  // CHILD
  val FIX_JP_B2B_OCC_DISPLAY = "CHILD-700"
  val FIX_EMPTY_EXTRA_BED_STAY_OCCUPANCY = "CHILD-827"
  val FIX_EMPTY_EXTRA_BED_STAY_OCCUPANCY_CHECKIN_FLIP = "CHILD-827-CHECKIN-FLIP"
  val NEW_OCCUPANCY_LOGIC_TEST = "CHILD-105-TEST"

  val UPDATE_KIDS_STAY_FREE_STRING = "NHAFE-1907"

  // AFFILIATE PRICE ADJUSTMENT REQUEST_MIGRATION
  val AFFILIATE_PRICE_ADJUSTMENT_REQUEST_MIGRATION = "PAPI-19481"

  // Cheapest room logic
  val FIND_CHEAPEST_ROOM_USING_ID = "BWHB-392"

  // ADD ESS PUBLISH PRICE TO PRICE DISPLAY EXCLUSIVE
  val INCLUDE_PUBLISHED_PRICE_ESS_IN_SELL_EXCLUSIVE = "RTAP-159"

  val MAP_AGENCY_ROOM_PROMOCODE_FAILURE_REASON = "RTAP-676"

  // legal tech
  val M150_MAX_TRANSPARENCY_VERSION_FOR_KR = "LT-91-KR"
  val M150_MAX_TRANSPARENCY_VERSION_FOR_JP = "LT-91-JP"
  val DISABLED_M150 = "LT-466"
  val DISABLE_FXI_FOR_US_UK_CA = "LT-658"
  val FIX_PRICE_VIEW_OVERRIDE = "LT-656-FIX-PRICEVIEW-OVERRIDE"
  val POPULATE_EXTRA_INFO_WITH_PRICE_WITHOUT_EXCLUDED_CHARGES = "LT-1503"
  val DISABLE_FXI_FOR_INDIA = "CFF-1040"
  val SHOW_EXCLUSIVE_WITH_FEE_TO_US_DESTINATION = "LT-1446"
  val DO_NOT_CONSIDER_EXCLUDED_TAX_FEE_FOR_EXCLUDED_SURCHARGE = "LT-1581"
  val M150_MAX_TRANSPARENCY_VERSION_FOR_EU_EEA = "LT-1598"
  val ENABLE_PSEUDO_COUPON_COR_FOR_FEE_INCLUSIVE_CASES = "LT-1628"
  val BLOCK_COR_FOR_IL = "LT-1485"
  val DISABLE_REMOVE_YCS_PROMOTION = "LT-1702"

  // Package to cart migration
  val REMOVE_MULTI_HOTEL_LOGIC = "CARTPACK-337"
  // TODO: CARTPACK 510 & 329 - Remove after integration
  val REMOVE_AGENCY_SUPPLY_FF = "CARTPACK-510" // CARTPACK 510 & 329

  val ENABLE_SELLING_DIRECT_CONNECT_SUPPLY_FOR_JTB = "JTBFP-615-V2"

  val APPLY_SOYBEAN_STREAMLINING_EVEN_WHEN_DOWNLIFT_ENTRY_MISSING = "BWHB-424"

  val EXCLUDE_DUPLICATE_COUNTER_FEE_FROM_BCOM = "BWHB-434"
  val EXCLUDE_COUNTER_FEE_INCLUSIVE_FROM_BCOM = "BWHB-457"

  val SAMPLE_MRB_ROOMS_WITH_MAX_OCC = "FLEX-707"

  val FIX_MERGING_ROOM_TYPE_ID = "FLEX-1034"

  val MIX_AND_SAVE_BY_TOP_BPD_HOTEL_WITH_MAX_LOS_4 = "FLEX-1405"

  val FIX_SINGLE_ROOM_ORDERING_EXPONENTIALLY_ROOM_COUNT = "FLEX-1450"

  val EXCLUDE_MULTI_ROOM_ASSIGNMENT_SUPPLIER_FROM_MRB = "FLEX-1561"

  // Partner supplier filter
  val ENABLE_AFFILIATE_SUPPLIER_FILTER_FOR_HSB_MIGRATED_PARTNERS = "PAPI-20396"

  // it will never run on Prod
  val ENABLE_HMC_FETCH_MASTER_HOTEL_ONLY = "ST-3699"
  val ENABLE_HMC_FETCH_MASTER_HOTEL_AND_CHILDS = "ST-4284"

  val FIND_ROOM_SWAP_CANDIDATES = "SCP-5374"
  val SUPERAGG_LOWERNET_ROOM_SWAP = "SCP-6340"

  val M150_BASELINE_TRANSPARENCY_VERSION_FOR_US_UK = "LT-399-M150-BASELINE-US-UK"
  val M150_BASELINE_TRANSPARENCY_VERSION_FOR_CA = "LT-399-M150-BASELINE-CA"
  val M150_BASELINE_TRANSPARENCY_VERSION_FOR_DE = "LT-399-M150-BASELINE-DE"
  val M150_BASELINE_TRANSPARENCY_VERSION_FOR_VN = "LT-399-M150-BASELINE-VN"
  val M150_BASELINE_TRANSPARENCY_VERSION_FOR_IN = "LT-399-M150-BASELINE-IN"
  val M150_BASELINE_TRANSPARENCY_VERSION_FOR_NL = "LT-399-M150-BASELINE-NL"
  val M150_BASELINE_TRANSPARENCY_VERSION_FOR_IL = "LT-399-M150-BASELINE-IL"
  val M150_BASELINE_TRANSPARENCY_VERSION_FOR_REST_OF_EU = "LT-399-M150-BASELINE-EU"

  // new cms client
  val USE_NEW_CMS_CLIENT = "REVIEWS-2144"
  val USE_ENGLISH_FOR_INVALID_LANGUAGE = "DFOPS-4778"

  // supplier funded discount
  val ENABLE_SUPPLIER_FUNDED_DISCOUNT_KILLSWITCH = "OG-149-KILLSWITCH"

  // SDA partial traffic filter experiment
  val PARTIAL_TRAFFIC_FILTER_EXPERIMENT = "SCP-5763"

  /**
    * Calculon's 10 A/A Tests for CityIDLOSFlipWithSalt & HotelIDWeeklyFlipWithSalt allocation types
    * 1. EXP-CITYIDLOSFLIP-DFAPI-HOTELSEARCH-AA-[001 - 005] (5 experiments total)
    * 2. EXP-HIDWEEKLYFLIP-DFAPI-HSEARCH-AA-[001 - 005] (5 experiments total)
    */
  val EXP_CITYIDLOSFLIP_DFAPI_HOTELSEARCH_AA: Seq[String] =
    (1 to 5).map(i => f"EXP-CITYIDLOSFLIP-DFAPI-HOTELSEARCH-AA-$i%03d")
  val EXP_HOTELIDWEEKLYFLIP_DFAPI_HOTELSEARCH_AA: Seq[String] =
    (1 to 5).map(i => f"EXP-HIDWEEKLYFLIP-DFAPI-HSEARCH-AA-$i%03d")

  val CHANNEL_DISCOUNT_PER_APPLIED_DATE = "RC-2381"

  val DYNAMIC_DOWNLIFT = "JP-3013"

  val REMOVE_CONNECTED_TRIP_TO_AVOID_BUG = "VEL-1424"

  val DMC_HARDCODING_REMOVAL = "JTBFP-1295"
  // Growth programs
  val ON_BOARD_GROWTH_PROGRAM_FENCING_RULE = "SGMP-665"

  // Latency Improvement experiments
  val SF_HOTEL_CREATION_IN_PAR = "VEL-2075"
  val DF_HOTEL_CREATION_IN_PAR = "VEL-2076"

  // Chain wholesale distributor
  val UPDATE_PRICE_ADJUSTMENT_ID_TO_ROOM_PROTO = "CWSD-1224"
  val ENABLED_CONSUMERFINTECH_BLOOMFILTER_CLIENT = "CWSD-1431"

  val FIX_DUPLICATE_BENEFITS = "VEL-1502"

  val ENABLE_EXCLUSIVE_PRICE_CA_MN = "LT-1334"

  // OfferFilter in PropertySearchToken
  val OFFER_FILTER_IN_PROPERTY_SEARCH = "JESTER-4705"

  val FIX_ELAPI_SHOW_INCORRECT_AMOUNT = "LT-1451"

  val FIX_CART_BOOKING_FORM_USING_INCLUSIVE_FOR_EXCLUDED_SURCHARGE = "LT-1488"

  val FIX_DUPLICATE_ROOMS_FROM_H4 = "VEL-2093"

  val AFFILIATE_GENERATE_SELL_DISCOUNT_OFFERS = "PAPI-22933"

  val ADD_MANDATORY_RETAIL_OFFERS_AFFILIATE = "AFFSRCH-192"

  val ADD_MANDATORY_RETAIL_OFFERS_CALCULON = "AFFSRCH-478"

  val FIX_DEFAULT_SUPPLIER_BCOM_AGE_POLICY_OVERRIDE = "BSUP-3117"

  // Turning off BNPL v2 For Turkish Lira
  val SHOULD_BLOCK_BNPL_FOR_TURKISH_LIRA = "MH-9433"

  val ENABLE_INDIA_BNPL_FOR_BWZP = "PAYFLEX-106-BWZP-INDIA"

  // Use payment eligibility fields from PaymentResult instead of recalculating
  val USE_PAYMENT_INFO_FROM_PAYMENT_RESULT = "BWHF-5049"

  // Price Display
  val SURCHARGES_IN_HOTEL_CURRENCY = "PFE-12481"

  // Cor Price Enable for Affiliate Partner
  val ENABLE_COR_PRICE_FOR_AFFILIATE_PARTNER = "MSEP-1511"

  // EMI Fencing for Credit Card Offers
  val EMI_FENCING_FOR_CREDIT_CARD_OFFERS = "IN-6"

  // Benefits Filter V2 technical experiment
  val BENEFITS_FILTER_V2 = "OP6-2949"

  // CN-5224: Enable affiliate-specific logic for China accounting rules
  val ENABLE_AFFILIATE_CHINA_ACCOUNTING_RULES = "CN-5224"

  // Direct Supply
  val ENABLE_NEGATIVE_SEGMENT = "DSINNO-1417"

  val H3_V2 = "VEL-2212"

  // Payment Method Decimal Override - LINE Pay experiments
  val PC_7098_ENABLE_LINEPAY_IOS = "PC-7098-ENABLE-LINEPAY-IOS"
  val PC_7098_ENABLE_LINEPAY_ANDROID = "PC-7098-ENABLE-LINEPAY-ANDROID"
  val PC_7098_ENABLE_LINEPAY_DWEB = "PC-7098-ENABLE-LINEPAY-DWEB"
  val PC_7098_ENABLE_LINEPAY_MWEB = "PC-7098-ENABLE-LINEPAY-MWEB"
}

object ABTest extends ABExperiment
