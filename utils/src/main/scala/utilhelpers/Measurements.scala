package utilhelpers

import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.api.common.AttributeKey

/**
  * Created by kliu on 5/12/2017 AD.
  */
object Measurements {
  // Namespaces
  final val invalidRequest = "dfe.search.invalid.requests"
  final val performance = "dfe.search.performance"
  final val bcomlatency = "dfe.search.bcomlatency"
  final val cache = "dfe.search.cache"
  final val cacheFill = s"$cache.fill"
  final val cacheRatio = s"$cache.ratio"
  final val precacheSize = "dfe.search.precache.size"

  final val startTime = "dfe.startup.time"
  final val request = "dfe.request"
  final val dmcFilter = "dfe.search.dmcfilter"
  final val hadoopMsgSent = "dfe.hadoop.msg.count"

  final val sqlPerformance = "dfe.sql.perf"
  final val shardPerformance = "dfe.shard.perf"

  final val scapiPrefix = "dfe.scapi"
  final val asq = "dfe.asq"

  final val hasBase = "hasbase"
  final val hasStack = "hasstack"
  // c is referred as Rate Channel ID here.
  // The official definition is in agoda_ycs.ycs41_channel
  final val c6 = "c6" // Domestic
  final val c2 = "c2" // Private Sale (APS)
  final val c220 = "c220" // Domestic & APS
  final val c27 = "c27" // VIP Gold
  final val c266 = "c266" // Domestic & VIP Gold
  final val c7 = "c7" // Mobile
  final val c251 = "c251" // Domestic & Mobile

  final val hasUserId = "hasuserid"
  final val hasFeatureFlag = "hasfeatureflag"
  final val composite = "composite"

  // Tags
  final val circuitBreakerState = "dfe.search.circuit.breaker.state"
  final val circuitBreakerCount = "dfe.search.circuit.breaker.count"

  final val soybeanCircuitBreakerState = "dfe.soybean.circuit.breaker.state"
  final val soybeanCircuitBreakerCount = "dfe.soybean.circuit.breaker.count"
  final val soybeanCircuitBreakerErrorCount = "dfe.soybean.circuit.breaker.errorcount"

  final val hotelCount = "dfe.search.hotel.count"

  final val manualCalProcessCountViaAriCal = "dfe.dfcal.arical.manual.count"
  final val manualCalProcessTimeViaAriCal = "dfe.dfcal.arical.manual.response"
  final val manualCalHotelResponse = "dfe.dfcal.response"
  final val manualCalRateOfRequest = "dfe.manual.request.rate"

  final val customCalProcessViaAriCal = "dfe.dfcal.arical.customcal"

  /** This metric is used for measuring part of the flow */
  final val flowSteps = "dfe.search.flow.steps"
  final val flowTimeout = "dfe.search.flow.timeout"
  final val flowMaxTime = "dfe.search.flow.maxTime"
  final val roomsAfterYpl = "dfe.search.flow.roomsafterypl"
  final val roomsAfterYplPRPN = "dfe.search.flow.roomsafterypl.prpn"
  final val roomsAfterYplPRPNFencingTotal = "dfe.search.flow.roomsafterypl.fencing.total"
  final val roomsAfterYplPRPNFencingReduction = "dfe.search.flow.roomsafterypl.fencing.dedup"
  final val bookingSupplierMissing = "dfe.search.flow.booking.missing.supplier"

  final val roomsBeforePricing = "dfe.search.flow.roomsbeforepricing"
  final val roomsAfterPricing = "dfe.search.flow.roomsafterpricing"
  final val roomsReturned = "dfe.search.flow.roomsreturned"
  final val roomsIdentifier = "dfe.search.ri"
  final val roomsIdentifierStripped = "dfe.search.ri.stripped"
  final val soybeanMigrationMetrics = "dfe.search.flow.soybean.migration"

  final val fencedRateRoomStatusMismatch = "dfe.search.fencedrate.roomstatusmismatch"
  final val fencedRateRoomStatusNotFound = "dfe.search.fencedrate.roomstatusnotfound"

  final val apiTotalRequests = "dfe.api.requests.total"
  final val apiRejectedRequests = "dfe.api.requests.rejected"
  final val apiRequestsRatio = "dfe.api.requests.ratio"
  final val apiCurrentWeights = "dfe.api.requests.parweight"
  final val apiCurrentRequests = "dfe.api.requests.parallel"

  final val propertyPricingWithFencedRateResponse = "dfe.service.fencedrate.response"

  final val propertyPricingRequestedHotels = "dfe.service.requestedhotels"
  final val bookingAdjustPayment = "dfe.booking.adjust.payment"
  final val bookingFilter = "dfe.booking.filters"
  final val bookingFilterBackup = "dfe.booking.filter.bkp"
  final val bookingFilterIdentifierBackup = "dfe.booking.filter.identifier.bkp"

  final val pricestream = "dfe.pricestream.performance"
  final val propertyAPIFetchingService = "dfe.service.propertyapi"
  final val hotelContentFetchingService = "dfe.service.hotel.content"
  final val gpCommission = "dfe.gpcommission.performance"
  final val ccas = "dfe.ccas.performance"
  final val elapi = "dfe.elapi.performance"
  final val merlin = "dfe.merlin.performance"
  final val roomBundlePriceValidation = "dfe.bundle.price.validation"
  final val numHotelFlexibleMultiRoom = "dfe.flexiblemultiroom.num.hotel"
  final val numOccFlexibleMultiRoom = "dfe.flexiblemultiroom.num.occupancy"
  final val flexibleMultiRoomToFitRoomCompare = "dfe.flexiblemultiroom.fitroomcompare"
  final val flexibleMultiRoomOffersAll = "dfe.flexiblemultiroom.offers.all"
  final val flexibleMultiRoomOffersSampled = "dfe.flexiblemultiroom.offers.sampled"
  final val flexibleMultiRoomOffersBundled = "dfe.flexiblemultiroom.offers.bundled"

  final val numRequestMixAndSave = "dfe.mixandsave.num.request"
  final val timeTakenForMixAndSave = "dfe.mixandsave.time.processing"
  final val mixAndSaveHintCount = "dfe.mixandsave.hint.count"
  final val numFoundHintForMixAndSave = "dfe.mixandsave.num.found.hint"
  final val numSearchResultMixAndSave = "dfe.mixandsave.num.search.result"
  final val numFoundRoomBundleForMixAndSave = "dfe.mixandsave.num.found.room.bundle"
  final val numFoundRoomBundleForMixAndSaveShow = "dfe.mixandsave.num.found.room.bundle.show"
  final val numTimeoutForMixAndSave = "dfe.mixandsave.num.timeout"
  final val numFoundRoomBundleWithFullLos = "dfe.mixandsave.num.full-los.exist"
  final val numMixAndSavePriceCompare = "dfe.mixandsave.num.rate.compare"
  final val numMixAndSavePriceCheaperUsd = "dfe.mixandsave.num.price.cheaperUsd"
  final val numMixAndSavePriceCheaperUsdPercent = "dfe.mixandsave.num.price.cheaperUsdPercent"

  final val canBundleForSingleRoomTypeCount = "dfe.canbundle.singleroomtype.count"
  final val canBundleForFlexibleMultiRoomCount = "dfe.canbundle.flexiblemultiroom.count"

  final val numDynamicRoomMapped = "dfe.dynamicroommapping.num.room.mapped"
  final val numDynamicRoomCandidates = "dfe.dynamicroommapping.num.room.candidates"
  final val tagMappingType = "mappingtype"

  final val numOfAdditionalCheapestRooms = "dfe.cheapestPerPromo.totalRooms"

  final val bookingProtoHashMatch = "dfe.booking.protohash"

  final val numOfRequestLongStayNudge = "dfe.longStayNudge.num.request"
  final val numOfExtraRequestFailureLongStayNudge = "dfe.longStayNudge.num.request.extra.failure"
  final val numOfNormalAndExtraRequestFailureLongStayNudge = "dfe.longStayNudge.num.request.normalAndExtra.failure"

  final val freeOccRequests = "dfe.freeocc.request"

  final val coreCalculation = "dfe.core.calc"
  final val processSPLSiloViaYPL = "dfe.ypl.processSPLSiloViaYPL"

  final val roomTypeId0 = "dfe.rooms.roomTypeId0"

  final val h3ExpRequestLatency = "h3.request.process.time"

  final val tagSuccess = "success"
  final val tagPartialSuccess = "partial-success"
  final val tagFailure = "failure"
  final val tagFatal = "fatal"
  final val tagBackpressure = "backpressure"
  final val tagTimeout = "timeout"
  final val tagTimeoutType = "timeouttype"
  final val tagCircuitBreaker = "circuitbreak"

  final val tagPath = "path"
  final val tagType = "type"
  final val tagFallback = "fallback"
  final val tagStatus = "status"
  final val tagLanguageId = "languageid"
  final val tagRejected = "rejected"
  final val tagPlatformId = "platformid"
  final val tagSiteId = "siteid"
  final val tagAffiliateId = "affiliateid"
  final val tagCid = "cid"
  final val tagDmcId = "dmcid"
  final val tagCities = "citynum"
  final val tagSupplier = "supplier"
  final val tagRoomIdentifier = "roomIdentifier"
  final val tagUid = "uid"
  final val tagHasPromotion = "hasPromotion"
  final val tagHasDmcUid = "hasDmcUid"
  final val tagIsPush = "isPush"
  final val tagIsSync = "sync"
  final val tagCacheMissReason = "cacheMissReason"
  final val tagRetries = "retries"
  final val tagIsBookingForm = "isBookingForm"
  final val tagIsBookingSupplierPresent = "isBookingSupplierPresent"
  final val tagIsCreditCardPresent = "isCreditCardPresent"
  final val tagIsSSR = "isSSR"
  final val tagIsNHA = "isNHA"
  final val tagIsBot = "isBot"
  final val tagIsManualCal = "isManualCal"
  final val tagStep = "step"
  final val tagFlow = "flow"
  final val tagChannel = "channel"
  final val tagPaymentModel = "paymentModel"
  final val tagStoreFront = "storefront"
  final val tagIsPaap = "isPaap"

  // requestType
  // TODO: replace them in def getRequestType
  final val tagRequestType = "requesttype"
  final val tagSsr = "ssr"
  final val tagRebookV1V2 = "rebookv1v2"
  final val tagRebookv3 = "rebookv3"
  final val tagBooking = "booking"
  final val tagProperty = "property"
  final val tagCustomPrice = "customprice"

  final val tagTrue = "true"
  final val tagFalse = "false"
  final val tagRequestOrigin = "requestOrigin"
  final val tagDeviceTypeId = "deviceTypeId"

  final val tagIsRetry = "isRetry"
  final val maxAttemptLog = 10
  final val tagAttempt = "attempt"
  final val tagCheapestOnly = "cheapestOnly"
  final val tagUser = "user"
  final val tagIsYpl = "isYpl"
  final val tagLos = "los"
  final val tagAvail = "avail"
  final val tagEligible = "eligible"
  final val tagBucket = "bucket"
  final val tagOccAdult = "adult"
  final val tagOccChild = "child"
  final val tagIsYcs = "isycs"
  final val tagHasYcs = "hasYcs"
  final val tagIsMse = "isMse"
  final val tagIsAllOcc = "isAllOcc"
  final val tagIsEnabled = "isEnabled"
  final val tagIsApplicable = "isApplicable"
  final val tagIsH3v2 = "isH3v2"
  final val tagIsLogIn = "isLogIn"
  final val h3VariantsTag = "h3variants"

  // Price Guarantee Measurements Tags
  final val isProtoMatched = "isProtoMatched"
  final val isSupplierPriceMatched = "isSupplierPriceMatched"
  final val supplierPriceChangePercentage = "supplierPriceChangePercentage"
  final val supplierPriceChangeAmount = "supplierPriceChangeAmount"
  final val isDownliftMatched = "isDownliftMatched"
  final val downliftChangePercentage = "downliftChangePercentage"
  final val downliftChangeAmount = "downliftChangeAmount"
  final val minutesSinceTokenCreation = "minutesSinceTokenCreation"

  final val tagFeatureFlag = "featureFlag"
  final val tagHotelCount = "hotelCount"
  final val tagRoomCount = "roomCount"
  final val tagIsPromoCodeRequest = "isPromoCodeRequest"
  final val tagIsPriceStateRequest = "isPriceStateRequest"
  final val tagIsApsPeekRequest = "isApsPeekRequest"
  final val tagIsOfferLevelPromocode = "isOfferLevelPromocode"
  final val tagIsReduceOfferPromocodeSize = "isReduceOfferPromocodeSize"
  final val tagIsDynamicDownliftEnabled = "isDynamicDownliftEnabled"
  final val tagIsFixFencesComparingRoomsEnabled = "isFixFencesComparingRoomsEnabled"
  final val tagIsValueTagEnabled = "isValueTagEnabled"

  final val tagLosBucket = "losbucket"
  final val sellAllIn = "sellallin"
  final val netEx = "netex"

  final val supplierMetadata = "supplierMetadata"
  final val preProcessYPLHPFPLOS = "preProcessYPLHPFPLOS"
  final val tagRequestCurrency = "request"
  final val tagLocalCurrency = "local"
  final val tagUsdCurrency = "usd"
  final val tagLessEqual = "le"

  /**
    * Specify the weight of request / metric / etc
    * Be aware that we want to use it only for values from 0 to 7
    */
  final val tagWeight = "weight"
  final val tagTrafficType = "trafficType"
  final val tagSource = "source"
  final val tagReplicated = "replicated"
  final val rateSiloTime = "ratesilo"
  final val totalTime = "total"
  final val tagAdjustAmount = "adjust"
  final val tagExactAmount = "exact"
  final val tagOverThreshold = "over"
  final val tagNoRoom = "noRoom"
  final val tagMultipleRoom = "multipleRooms"
  final val tagNoAllotment = "noAllot"
  final val tagVersion = "version"
  final val whiteLabelId = "whitelabelId"
  final val tagExperimentVariant = "experimentVariant"
  final val tagGpCommissions = "growthProgramCommissions"
  final val tagCcas = "ccas"
  final val whiteLabelKey = "whiteLabelKey"
  final val tagIsCDS = "cds"
  final val tagELAPISearch = "elapiSearch"
  final val tagELAPIBook = "elapiBook"
  final val tagRTAPricing = "rtaPricing"
  final val tagKey = "key"
  final val tagGkPriority = "gkPriority"
  final val tagGkBucket = "gkBucket"
  final val tagFencedRatePairKey = "fencedRatePairKey"
  final val tagRoomStatus = "roomStatus"
  final val tagRoomStatusFromFence = "roomStatusFromFence"

  // Packaging
  final val tagFunnel = "funnel"
  final val tagHotelId = "hotelId"
  final val tagPackageChannelId = "packageChannelId"
  final val tagOtherChannelId = "otherChannelId"
  final val tagRateDiff = "rateDiff"
  // Categories
  final val storeProcedure = "storeprocedure"

  // Supplier API
  //  Debug metric
  final val supplierApiCassRates = "spapi.cassrates"
  final val dfeSPAPICacheHit = "dfe.spapi.cache.hit"
  final val dfeSPAPICacheMiss = "dfe.spapi.cache.miss"
  final val dfeSPAPIRoomUIDFallback = "dfe.spapi.roomuid.fallback"
  final val dfeRoomReturned = "dfe.room.return"

  // structuredBenefits
  final val dfeStructuredBenefit = "dfe.structuredbenefit"
  final val benefitIdTag = "benefitid"

  final val upsellExistInSpTag = "upsellSpCandidate"
  final val upsellHigherOccTag = "upsellHigherOcc"
  final val upsellHigherTotalPriceTag = "upsellHigherPrice"
  final val upselleUnderCapTag = "upsellUnderCap"
  final val upsellMatchedChannelIdTag = "upsellChannel"
  final val upsellMatchedSupplierIdTag = "upsellSupplier"
  final val upsellMatchedPromotionIdTag = "upsellPromotion"
  final val upsellMatchedCancellationTag = "upsellClx"
  final val upsellMatchedPaymentModelTag = "upsellPayment"
  final val upsellMatchedPriusTag = "upsellPrius"
  final val upsellMatchedPaymentOptionTag = "upsellPaymentOpt"
  final val upsellMatchedBreakfastOptionTag = "upsellBreakfast"
  final val upsellBiggerSize = "upsellBiggerSize"
  final val upsellBiggerSizeRegardlessZero = "upsellBiggerSizeAllowZero"
  final val isSameSizeRegardlessZeroAndBetterView = "upsellSameSizeAllowZeroAndBetterView"
  final val upsellViewAndNoneZeroSize = "upsellViewAndNoneZeroSize"

  final val heuristicsRemovalForBooking = "dfe.search.booking.heuristic.removal"
  // roomswap related metrics
  final val validRoomSwap = "dfe.valid.room.swap"
  final val validFilterType = "filterType"
  final val tagIsValidRoom = "isValid"
  // Packaging
  final val dfePackagingErrors = "dfe.packaging.errors"
  final val dfePackagingLatency = "dfe.packaging.latency"
  final val dfePackagingTokenSize = "dfe.packaging.token.size"
  final val dfeUpiErrors = "dfe.upi.errors"
  final val dfeUpiLatency = "dfe.upi.latency"
  final val dfeUpiTokenSize = "dfe.upi.token.size"
  final val dfeUpiArrangementSize = "dfe.upi.arrangement"
  final val dfePackageCancellationFilter = "dfe.packaging.cxlgroupfilter"
  final val dfePackagingRidFilterIsMatched = "dfe.packaging.ridfilter.ismatched"
  final val dfePackageRoomFilter = "dfe.packaging.room.filters"
  final val dfePackagingNumberOfRequest = "dfe.packaging.num.request"
  final val tagRidFilterMatch = "ridfiltermatch"
  final val tagRatePlanStatus = "rateplanstatus"
  final val tagRoomDispatchAvailabilityType = "dispatch_availability_type"
  final val tagAdditionalRoomCount = "additional_count"
  final val tagAdditionalRoomSkipRemovedCount = "additional_skip_count"
  final val tagMatchReason = "match_reason"
  final val packageRatesCompare = "dfe.packaging.packagerates"
  final val totalRooms = "multiHotel.totalRooms"
  final val tagIsPackagingFunnel = "isPackagingFunnel"
  final val tagIsCartFeatureEnabled = "isCartFeatureEnabled"
  final val tagPackagingFlowType = "packagingFlowType"
  final val tagEndpoint = "endpoint"

  final val numRoomIdentifierMismatch = "dfe.room.identifier.mismatch"
  final val numRoomIdentifierDuplicated = "dfe.room.identifier.duplicated"
  final val numDuplicatedRooms = "dfe.room.duplicated"
  final val tagSupplierNotFoundOrFailed = "suppliernotfoundorfailed"
  final val tagCacheExpired = "cacheexpired"
  final val tagRequestedOfferSoldOut = "requestedoffersoldout"
  final val tagSameDmcUid = "samedmcuid"
  final val tagHasSimilarRoom = "hassimilarroom"
  final val tagNoRoomFound = "noroomfound"
  final val tagRoomFound = "roomFound"
  final val tagCancellationGroupFilter = "cxlgroupfilter"
  final val hasPackagingToken = "hasPackagingToken"
  final val hasCartToken = "hasCartToken"
  final val tagIsSkip = "isSkip"
  final val tagIsFixed = "isFixed"
  final val tagRateChannelNotDispatched = "rateChannelNotDispatched"
  final val tagSupplierNotDispatched = "supplierNotDispatched"
  final val tagRoomIdWithPricingToken = "roomIdWithPricingToken"

  // SPAPI ROOM SWAP tags
  final val spapiAlternativeRoomCount = "dfe.spapi.alternativeroom.count"
  final val tagAlternativeRoomFound = "altroomfound"
  final val tagAlternativeRoomFiltered = "altroomfiltered"
  final val tagNoMasterRoomTypeId = "nomasterroomtypeid"
  final val tagAlternativeRoomMoreThanOne = "altroommorethanone"
  final val tagIsPrecheckFlow = "isprecheckflow"

  final val nonTrack = "non-track"

  // app migration team
  final val oldRoomSelectionCount = "dfe.oldroomselection.count"
  final val newRoomSelectionCount = "dfe.newroomselection.count"

  // Dayuse
  final val tagBookingdurationtype = "bookingdurationtype"
  final val numOfRequestCheapestHourlyRate = "dfe.cheapestHourlyRate.num.request"
  final val cheapestHourlyRoomCount = "dfe.cheapestHourlyRoom.count"
  final val noAffiliateId = "no-affiliate-id"
  final val noSiteId = "no-site-id"
  final val tagCityId = "cityId"
  final val tagLeadDays = "leadDays"
  final val hourlyRoomsCount = "dfe.dayuse.hourlyRooms.count"
  final val overnightRoomsCount = "dfe.dayuse.overnightRooms.count"

  // USPA
  final val uspa = "dfe.uspa"
  final val uspaNumOfUspaCampaigns = s"${uspa}.num.campaigns"
  final val uspaParseJsonStatus = s"${uspa}.parse.json.status"
  final val uspaDiscountPct = s"${uspa}.discount.percent"
  final val uspaNumOfRooms = s"${uspa}.num.rooms"
  final val uspaTotalRoomsProcessed = s"${uspa}.total.rooms.processed"
  final val uspaTotalRoomsInUspaFlow = s"${uspa}.total.rooms.in.uspa.flow"
  final val uspaAdjustedRooms = s"${uspa}.adjusted.rooms"
  final val uspaRoomsMatched = s"${uspa}.rooms.matched"
  final val uspaRoomsBenchMarked = s"${uspa}.rooms.benchmarked"
  final val uspaParticipatingCampaigns = s"${uspa}.participating.campaigns"
  final val tagUspaCampaignDataSource = "uspaCampaignDataSource"
  final val tagUspaCampaignId = "uspaCampaignId"
  final val tagUspaProgramId = "uspaProgramId"
  final val tagChainId = "chainId"
  final val tagMatched = "matched"
  final val uspaHotelProcess = s"${uspa}.hotel.process"
  final val uspaTagCampaignCount = "uspaCampaignCount"
  final val uspaStepLatency = s"${uspa}.step.latency"

  // Extra retail offer added
  final val retailOffersoverriden = "dfe.roomselecting.retailOffer.overriden.count"

  // Supplier Funded Discount
  final val supplierFundedDiscountRoom = "dfe.supplierfundeddiscountroom.percent"

  // mse offer reduction
  final val roomRemoveOfferReduction = "dfe.offerReduction.remove"
  final val roomBeforeOfferReduction = "dfe.offerReduction.before"
  final val roomOfferReductionCheapestRoom = "dfe.offerReduction.cheapestRoom"

  // Tax And Fee Calculation
  final val calculateTaxAndFeeWithMarginTaxAndFee = "dfe.calculateTaxAndFeeWithMarginTaxAndFee.diff"

  // cheapest room reduction
  final val roomBeforeCheapestRoomReduction = "dfe.cheapestRoomReduction.before"
  final val roomRemovedCheapestRoomReduction = "dfe.cheapestRoomReduction.removed"
  final val afterRoomHeuristic3Count = "dfe.roomHeuristic3.after"

  final val droppedRoomH3Push = "dfe.roomHeuristic3.droppedRoom.push"
  final val droppedRoomH3Pull = "dfe.roomHeuristic3.droppedRoom.pull"
  final val totalDroppedRoomH3 = "dfe.roomHeuristic3.totalDroppedRoom"
  final val masterRoomsBeforeH3 = "dfe.roomHeuristic3.masterRoomsBeforeH3"
  final val masterRoomsAfterH3 = "dfe.roomHeuristic3.masterRoomsAfterH3"
  final val masterRoomsBeforeSB = "dfe.roomHeuristic3.masterRoomsBeforeSB"
  final val masterRoomsAfterSB = "dfe.roomHeuristic3.masterRoomsAfterSB"
  final val cheapestRoomRefSellInUSD = "dfe.roomHeuristic3.cheapestRoomRefSellInUSD"
  final val cheapestRoomSellInUSD = "dfe.roomHeuristic3.cheapestRoomSellInUSD"

  // new supplier occupancy flow
  final val offersBeforeNewSupplierOccupancyFlow = "dfe.newSupplierOccupancy.before"
  final val offersAfterNewSupplierOccupancyFlow = "dfe.newSupplierOccupancy.after"
  final val offersFilteredOutNewSupplierOccupancyFlow = "dfe.newSupplierOccupancy.filtered"

  // RateChannelSwap
  final val rcsAlternativeMeasurement = "dfe.rcs.alternative"
  final val rcsStatus = "rcsStatus"
  final val rcsValidTag = "valid"
  final val rcsMismatchedTag = "mismatched"
  final val rcsNoBookedPriceTag = "noBookedPrice"

  // Consumer Fintech Smartflex
  final val smartflexOffers = "dfe.smartflex.offers"
  final val smartFlexOffertype = "offertype"
  final val typeSmartFlex = "smartflex"
  final val typeAllOffers = "allOffers"
  final val typeReplacement = "replacement"
  final val typeSmartSaver = "smartsaver"
  final val typeSmartFlexWithEss = "smartflexWithEss"
  final val typeAllOffersBeforeSB = "allOffersBeforeSB"
  final val typeSmartFlexBeforeSB = "smartflexBeforeSB"
  final val typeSmartSaverBeforeSB = "smartsaverBeforeSB"
  final val typeReplacementBeforeSB = "replacementBeforeSB"
  final val tagSmartFlexLeadDays = "leadDays"
  final val tagTaxCountry = "taxCountry"
  final val tagIsPriceFreeze = "isPriceFreeze"

  // Price Display
  final val koreaCORlessthanoneMeasurement = "dfe.cor.korea.lessthanone"
  final val koreaCORonetothreeMeasurement = "dfe.cor.korea.onetothree"
  final val koreaCORthreetofiveMeasurement = "dfe.cor.korea.threetofive"
  final val koreaCORgreaterthanfiveMeasurement = "dfe.cor.korea.greaterthanfive"

  final val stackedCORCount = "dfe.cor.unblocked.stacked.count"
  final val couponCORCount = "dfe.cor.unblocked.coupon.count"

  // Post booking
  final val tagPostBookingActionType = "postBookingActionType"
  final val tagOffercount = "offerCount"
  final val tagZeroValue = "0"
  final val tagOneValue = "1"
  final val tagGreaterThanOneValue = ">1"
  final val filteredPropOffersMetric = "dfe.postbooking.inventory.filtered.propoffers"

  final val serviceName = "propertyapipricingdocker"

  final val consolidatedDiscountMetric = "dfe.display.consolidateddiscount"
  final val consolidatedDiscountDownliftTag = "downlift"
  final val consolidatedDiscountCashbackTag = "cashback"
  final val consolidatedDiscountPromocodeTag = "promocode"
  final val consolidatedDiscountPromocodeTypeTag = "promocodetype"
  final val consolidatedDiscountPriceDisplayType = "pricedisplaytype"

  // Retain Retail Offers
  final val isRetainRetailOffersEnabled = "is_retain_retail_offers_enabled"
  final val doRetailOfferPresentAlready = "do_retail_offer_present_already"
  final val doExtraRetailOffersOverriden = "extra_retail_offers_overriden"

  // Cxl & ReBook
  final val cancelAndReBookV3Metric = "dfe.cxlrebook.v3"
  private final val otelMeter = GlobalOpenTelemetry.get().getMeter(serviceName)

  object ExtendedDiscBadgeRateChannels {
    final val geniusChannelBadgeCount = s"$serviceName.genius.badge.count"
    final val extendedRateChannelBadgeCount = s"$serviceName.extendedrate.badge.count"
    final val superAggRateChannelBadgeCount = s"$serviceName.superagg.badge.count"
    final val apsRateChannelBadgeCount = s"$serviceName.aps.badge.count"
  }

  object Attributes {
    final val requestType = AttributeKey.stringKey(Measurements.tagRequestType)
    final val platformId = AttributeKey.stringKey(Measurements.tagPlatformId)
    final val trafficType = AttributeKey.stringKey(Measurements.tagTrafficType)
    final val isRetry = AttributeKey.stringKey(Measurements.tagIsRetry)
    final val whiteLabelKey = AttributeKey.stringKey(Measurements.whiteLabelKey)
    final val funnel = AttributeKey.stringKey(Measurements.tagFunnel)
    final val bookingDurationType = AttributeKey.stringKey(Measurements.tagBookingdurationtype)
    final val affiliateId = AttributeKey.stringKey(Measurements.tagAffiliateId)
    final val cid = AttributeKey.stringKey(Measurements.tagCid)
    final val sloTier = AttributeKey.stringKey("slo_tier")

    final val reason = AttributeKey.stringKey("reason")
    final val bucket = AttributeKey.stringKey("bucket")
    final val `type` = AttributeKey.stringKey("type")
    final val status = AttributeKey.stringKey(Measurements.tagStatus)
    final val h3Variants = AttributeKey.stringKey(Measurements.h3VariantsTag)

    // Post booking
    final val postBookingActionType = AttributeKey.stringKey(Measurements.tagPostBookingActionType)

  }

  def tagAsBitString(condition: Boolean): String =
    if (condition) "1"
    else "0"
  def tagAsBoolString(condition: Boolean): String = condition.toString.toLowerCase
  def tagAsStatus(condition: Boolean): String =
    if (condition) tagSuccess
    else tagFailure

  def toAffiliateIdTag(affiliateId: Option[String], whitelistAidTags: Set[Long]): String = affiliateId
    .map { aid =>
      if (whitelistAidTags.isEmpty || aid.nonEmpty && whitelistAidTags.contains(aid.toLong)) aid
      else nonTrack
    }
    .getOrElse(noAffiliateId)

  def toSiteIdTag(siteId: Option[Int], whitelistedSiteIdTags: Set[Int]): String = siteId
    .map { siteId =>
      if (whitelistedSiteIdTags.isEmpty || whitelistedSiteIdTags.contains(siteId)) siteId.toString
      else nonTrack
    }
    .getOrElse(noSiteId)

  def toAttemptTag(attempt: Int): String =
    if (attempt > maxAttemptLog) ">" + maxAttemptLog.toString
    else attempt.toString

  private val log10of2: Double = Math.log10(2.0)

  /**
    * Use it to reduce number of magnitude for value tags;
    * Usually value should be 0..2^10
    */
  def tagValueAsLog2(value: Int): String = (Math.log10(Math.max(value, 1)) / log10of2).toInt.toString

  def tagValueAsLog2Step(value: Int): String =
    Math.pow(2, (Math.ceil(Math.log10(Math.max(value, 0)) / log10of2))).toInt.toString

  def tagValueAsBucket(valueOpt: Option[Double], buckets: List[Double]): String =
    valueOpt.flatMap(value => buckets.find(el => value <= el).map(_.toString)).getOrElse("NA")

  /**
    * Calculates the bucket tag for room counts.
    * The buckets provide higher granularity on the lower end.
    */
  def roomCountGroup(rooms: Int): String =
    if (rooms <= 0) {
      "000"
    } else if (rooms <= 1) {
      "001"
    } else if (rooms <= 3) {
      "003"
    } else if (rooms <= 5) {
      "005"
    } else if (rooms <= 10) {
      "010"
    } else if (rooms <= 20) {
      "020"
    } else if (rooms <= 40) {
      "040"
    } else if (rooms <= 60) {
      "060"
    } else if (rooms <= 80) {
      "080"
    } else if (rooms <= 100) {
      "100"
    } else if (rooms <= 150) {
      "150"
    } else if (rooms <= 200) {
      "200"
    } else if (rooms <= 300) {
      "300"
    } else if (rooms <= 400) {
      "400"
    } else if (rooms <= 500) {
      "500"
    } else {
      ">500"
    }

  def losGroup(los: Int): String = los match {
    case it if it < 15 => "1-14"
    case it if it < 31 => "15-30"
    case it if it < 46 => "31-45"
    case it if it < 61 => "46-60"
    case it if it < 91 => "61-90"
    case it if it < 121 => "91-120"
    case it if it < 181 => "121-180"
    case _ => "181+"
  }

  def tagValueAsRoomCount(roomCount: Int): String = {
    val moreThan15 = ">15"
    val div100: Int = roomCount / 100
    if (div100 > 15) moreThan15 else div100.toString
  }
}

object StreamMeasurements {
  private final val streamPrefix = "dfe.stream"
  final val requestLatency = s"$streamPrefix.request.latency"
  final val responseSize = s"$streamPrefix.response.size"

  final val tagSerializationType = "type"
}

object SDAMeasurements {
  private final val prefix = "dfe.sda.client"
  final val allSupplierIds = s"$prefix.supplierids"
  final val removedSupplierIds = s"$prefix.supplieridsremoved"
  final val allProperties = s"$prefix.properties.all"
  final val removedProperties = s"$prefix.properties.removed"
  final val exceptions = s"$prefix.exceptions"
  final val sdaSkippedPriceShopperRequest = s"$prefix.priceshopper.skippedrequest"

  object Tags {
    final val degradation = "degradation"
  }
}
