package api.request

import com.agoda.papi.enums.hotel.{PaymentModel => PaymentModelCommons}
import com.agoda.papi.enums.room.ChildRateType
import com.agoda.styx.botprofile.{BotProfile, TrafficType}
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import pricing.models.common.{BotProfile => HSMBotProfile}
import models._
import models.db._
import models.enums.ReBookingActionType
import models.serialization.OptionalCharMapDeserializer
import models.starfruit._
import pricingapi.utils.BotProfileProtocol._

case class ChildType(childRateType: ChildRateType, quantity: Int)

case class ChildAgeRangeAssignment(childAgeRangeId: Int, quantity: Int)

case class Children(ages: List[Option[Int]] = Nil, childrenTypes: List[ChildType] = List.empty) {
  val amount = ages.size
  val minAge = ages.flatten.reduceLeftOption(_ min _)
  val ageValues = ages.flatten
}

object Children {

  def apply(amount: Int, age: Option[Int], childrenTypes: List[ChildType]) =
    new Children(List.fill(amount)(age), childrenTypes)
}

/**
  * Additional occ filter for all / free occ search
  * @param minOcc min occupancy restriction
  * @param maxOcc max occupancy restriction
  * @param occOrder Additional filter to order and filter rooms by room occupancy of a hotel
  */
case class OccFilter(minOcc: Option[Int] = None, maxOcc: Option[Int] = None, occOrder: Option[List[Int]] = None)

case class RoomAssignment(adults: Int, childrenTypes: List[ChildType], childrenAgeRange: List[ChildAgeRangeAssignment])

/**
  * Occupancy information
  * ToDO: move isAllOcc filter to OccInfo
  * @param occFilter Additional filter for all / free occ search
  */
@SuppressWarnings(Array("stryker4s.mutation.MethodExpression", "stryker4s.mutation.EqualityOperator"))
case class OccInfo(_adults: Option[Int] = None,
                   _children: Option[Children] = None,
                   _rooms: Option[Int] = None,
                   occFilter: Option[OccFilter] = None,
                   roomAssignment: List[RoomAssignment] = List.empty) {

  if (_adults.isDefined) {
    require(_adults.get >= 0, "Adults must not be less than zero")
  }

  if (_rooms.isDefined) {
    require(_rooms.get >= 0, "Rooms must not be less than zero")
  }

  private val haveGuest = _adults.getOrElse(0) > 0 || (_children.isDefined && _children.get.amount > 0)
  private val haveRoom = _rooms.getOrElse(0) > 0

  val isValidOcc: Boolean = haveGuest && haveRoom

  val (adults, children, rooms, childrenAges) = (_adults.getOrElse(0),
                                                 _children.map(_.amount).getOrElse(0),
                                                 _rooms.getOrElse(0),
                                                 _children.map(_.ageValues).getOrElse(Nil))

  def getRooms: Int = Math.max(rooms, 1)

  def minOccFilter: Option[Int] = occFilter.flatMap(_.minOcc)
  def maxOccFilter: Option[Int] = occFilter.flatMap(_.maxOcc)

  /**
    * Is specified occ passed specified filter
    */
  def isValidForOccFilter(occ: Int): Boolean =
    occFilter.forall(of => of.minOcc.forall(_ <= occ) && of.maxOcc.forall(_ >= occ))
  def occOrder = occFilter.map(_.occOrder.getOrElse(List.empty)).getOrElse(List.empty)
}

/* Dragon Fruit client */

case class ClientInfo(language: LanguageId = 1,
                      platform: Option[Int] = None,
                      cid: Option[Int] = None,
                      storeFront: Option[Int] = None,
                      origin: Option[String] = None,
                      deviceTypeId: Option[Int] = None,
                      ipAddress: Option[String] = None,
                      affiliateId: Option[String] = None,
                      locale: Option[String] = None,
                      languageUse: Int = 1,
                      externalPartnerId: Option[String] = None) {
  def isXMLOrMobile: Boolean = platform.exists(p => p == 1008 || p == 1011 || p == 1007)
  def isDomestic(countryCode: Option[String]) = countryCode.isEmpty && origin.isEmpty || countryCode.exists { cc =>
    origin.exists(_.equalsIgnoreCase(cc))
  }
}

/**
  * @param botProfile styx-provided bot information
  */
case class TrafficInfo(botProfile: Option[BotProfile] = None) {
  lazy val isBot: Boolean = botProfile match {
    case Some(info) => info.trafficType != TrafficType.User && info.trafficType != TrafficType.None
    case _ => false
  }

  def getTrafficType: String = botProfile.map(_.trafficType).getOrElse(TrafficType.None).toString

  lazy val isKnownBot: Boolean = botProfile.exists(_.trafficType == TrafficType.KnownBot)

  lazy val isUnknownBot: Boolean = botProfile.exists(_.trafficType == TrafficType.UnknownBot)

  lazy val isUser: Boolean = botProfile.exists(_.trafficType == TrafficType.User)

  @deprecated("bot profile has to be calculated on HSM side, remove from core models.", "???")
  lazy val pricingBotProfile: Option[HSMBotProfile] = botProfile.map(_.toPricingBotProfile)

  @deprecated("stop calculating/sending from df to pricing", "pricing_client > 2.0.51")
  lazy val isPricingBot: Boolean = botProfile.exists(_.isPricingBot)
}

/**
  * Contains all the requested information related to specific features. It's meant to replace [[FeatureFlag]]
  * @param maxSuggestions 0 means that we don't do any suggestion. N means we suggest at most N rooms
  */
case class FeatureRequest(maxSuggestions: Int = 0,
                          isRatePartnerSummaries: Boolean = false,
                          mseHotelIds: List[Int] = List.empty,
                          mseClicked: Option[String] = None,
                          ppLandingHotelIds: List[Int] = List.empty,
                          searchedHotelIds: List[Int] = List.empty,
                          clientCampaignInfos: List[CampaignInfo] = List.empty,
                          calculateCancellationPhases: Option[Boolean] = None,
                          unsupportedPaymentModels: Set[PaymentModelCommons] = Set.empty,
                          enableRatePlanCheckInCheckOut: Option[Boolean] = None,
                          showCouponAmountInUserCurrency: Option[Boolean] = None,
                          excludeVoucherRooms: Option[Boolean] = None,
                          enablePushDayUseRates: Option[Boolean] = None,
                          @deprecated("escapes enabled by default. Use disableEscapesPackage field to disable")
                          enableEscapePackage: Option[Boolean] = None,
                          @deprecated("cheapest room logic now are combined with normal rooms")
                          filterCheapestRoomEscapePackage: Option[Boolean] = None,
                          enableSecretDealImprovement: Option[Boolean] = None,
                          @deprecated("replaced with FeatureFlag CreditCardPromotionPeek")
                          enableCreditCardCampaignPeek: Option[Boolean] = None,
                          calculateRareRoomBadge: Option[Boolean] = None,
                          enableCxlAversion: Option[Boolean] = None,
                          enableDownliftForCxlAversion: Option[Boolean] = None,
                          enableRichContentOffer: Option[Boolean] = None,
                          getAlternativeRoom: Option[Boolean] = None,
                          enableReturnNonApprovedEscapes: Option[Boolean] = None,
                          disableEscapesPackage: Option[Boolean] = None,
                          hasBooking: Option[Boolean] = None,
                          returnCheapestEscapesOfferOnSSR: Option[Boolean] = None,
                          enableBenefitValuationForASO: Option[Boolean] = None,
                          isM150Enabled: Option[Boolean] = None,
                          shouldAddPFInMarketingFee: Option[Boolean] = None,
                          enableDayUseCor: Option[Boolean] = None,
                          showCheapestHourlyRate: Option[Boolean] = None,
                          showPastMidnightSlots: Option[Boolean] = None,
                          sendCancellationSurcharge: Option[Boolean] = None,
                          enableHourlySlotsForDayuseInOvernight: Option[Boolean] = None,
                          enableThirtyMinsSlots: Option[Boolean] = None,
                          sortByCheckInTimeDayUseSSR: Option[Boolean] = None,
                          mergeDayUseOffersWithOvernight: Option[Boolean] = None,
                          ignoreRoomsCountForNha: Option[Boolean] = None)

/**
  * @param filterAPO - we use this flag on Property Page to filter only APO rooms (at secret deal page)
  */
case class FlagInfo(isApsEnabled: Boolean = false,
                    forceDmcResult: Boolean = false,
                    isSync: Boolean = false,
                    filterAPO: Boolean = false,
                    isRPM2Included: Boolean = false,
                    @deprecated("DF doesn't support this any more", "2.22.0") allowOverrideOccupancy: Boolean = false,
                    enableCor: Boolean = false,
                    isAPSPeek: Boolean = false,
                    isAllOcc: Boolean = false,
                    isMSE: Boolean = false,
                    isAllowRoomTypeNotGuarantee: Boolean = false,
                    isUsingHotelCurrency: Boolean = false,
                    isEnableSupplierFinancialInfo: Boolean = false,
                    isSnapshot: Boolean = false,
                    isManualTracingEnabled: Boolean = false)

/* This determines user search session. For example it could be unauthenticated user */
case class SessionInfo(userId: Option[String] = None, memberId: Option[Long] = None, isLogin: Option[Boolean] = None)

case class LoyaltyInfo(isLogin: Option[Boolean] = None,
                       isEligible: Option[Boolean] = None,
                       level: Option[Int] = None,
                       trafficGroup: Option[Int] = None,
                       vipLevel: Option[Int] = None)

case class SupplierPullMetadata(requiredPrecheckAccuracy: PrecheckAccuracy = PrecheckAccuracies.HIGH,
                                numOfRetries: Option[Int] = None) {
  lazy val canToleratePrecheckFailures = requiredPrecheckAccuracy == PrecheckAccuracies.NONE

  //  ToDO: Remove once delete http endpoint
  lazy val getPrecheckAccuracy: Int = requiredPrecheckAccuracy.level
}

case class DFExperimentInfo(trafficGroup: String,
                            @JsonDeserialize(using = classOf[OptionalCharMapDeserializer])
                            overridenAllocationVariantMap: Option[Map[String, Char]] = None,
                            @JsonDeserialize(contentAs = classOf[Char])
                            forceUserVariant: Option[Char] = None,
                            forceOnIntegrationRun: Boolean = false,
                            forceOnZeroTraffic: Boolean = false)

/**
  * This class contain criteria that can matched the room without uid
  */
@SerialVersionUID(1306909219664206373L)
case class MatchedRoomCriteria(uid: Option[String] = None,
                               hotelId: HotelId,
                               roomTypeId: RoomTypeId,
                               ratePlan: Int,
                               channelId: Int,
                               rateCategoryId: Int,
                               cxlCode: String,
                               paymentModel: Int,
                               dmcId: SupplierId,
                               dmcRoomId: String,
                               promotionId: Option[Int] = None,
                               pointMaxPoint: Option[Int] = None) {
  /*
  Explicitly match this case class since there are some parameters [uid, hotelId] that are not needed to compare.
  In addition, pointMaxPoint is an Option[Int] that None and 0 are same.
  ToDo:: Obsoletes this once Room Identifiers (New UID) is live, BF gonna move to New UID instead.
   * */
  override def equals(that: Any): Boolean = that match {
    case roomCriteria: MatchedRoomCriteria => this.hotelId == roomCriteria.hotelId &&
      this.roomTypeId == roomCriteria.roomTypeId &&
      this.channelId == roomCriteria.channelId &&
      this.rateCategoryId == roomCriteria.rateCategoryId &&
      this.cxlCode == roomCriteria.cxlCode &&
      this.paymentModel == roomCriteria.paymentModel &&
      this.dmcId == roomCriteria.dmcId &&
      this.promotionId.getOrElse(0) == roomCriteria.promotionId.getOrElse(0) &&
      this.dmcRoomId == roomCriteria.dmcRoomId &&
      this.pointMaxPoint.getOrElse(0) == roomCriteria.pointMaxPoint.getOrElse(0)
    case _ => false
  }
}

case class ReBookingRequest(roomTypeId: RoomTypeId,
                            masterRoomTypeId: Option[RoomTypeId],
                            customerPaidPrice: Double,
                            originalNetIn: Option[Double],
                            cashbackAmount: Option[Double],
                            promoAmount: Option[Double],
                            originalSellIn: Option[Double],
                            originalUsdToRequestExchangeRate: Option[Double],
                            actionType: ReBookingActionType,
                            originalSellInLocal: Option[Double] = None,
                            promoAmountInLocal: Option[Double] = None,
                            cashbackInLocal: Option[Double] = None) {
  def matchLocalCurrency: Boolean = actionType == ReBookingActionType.MatchLocal
  def matchUSD: Boolean = actionType == ReBookingActionType.MatchUSD
}

case class BackupRoomCriteria(roomTypeId: Long,
                              rateCategoryId: Int,
                              paymentModel: Int,
                              promotionId: Option[Int],
                              isRoomTypeNotGuarantee: Boolean)

case class PaymentInfo(creditCardInfo: Option[CreditCardInfo],
                       paymentMethod: Option[Int],
                       installmentInfo: Option[InstallmentInfo],
                       customerTaxCountryCode: Option[String],
                       paymentOption: Option[Int])

case class CreditCardInfo(ccof: Option[Long], ccToken: Option[String])

case class ExternalLoyaltyProfile(externalProgramId: Option[String] = None,
                                  loyaltyAccountNumber: Option[String] = None,
                                  remainingBenefitCredit: Option[Double] = None,
                                  promocode: Option[String] = None)
