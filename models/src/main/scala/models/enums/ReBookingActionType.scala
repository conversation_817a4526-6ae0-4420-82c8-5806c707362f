package models.enums

import enumeratum.{Enum, EnumEntry}

sealed abstract class ReBookingActionType(val value: String) extends EnumEntry with Serializable {
  override def hashCode: Int = value.hashCode
  def this() = this(hashCode.toString)
}

object ReBookingActionType extends Enum[ReBookingActionType] {
  val values = findValues
  private val fields: Map[String, ReBookingActionType] = values.map(x => (x.value, x)).toMap
  def getFromValue(value: String): ReBookingActionType = fields.getOrElse(value, MatchUSD)

  case object MatchUSD extends ReBookingActionType("MatchUSD")
  case object MatchLocal extends ReBookingActionType("MatchLocal")

  def fromString(s: String): ReBookingActionType = getFromValue(s)
}
