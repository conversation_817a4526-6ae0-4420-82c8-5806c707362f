package models.starfruit

/**
  * Created by schoud<PERSON><PERSON> on 6/9/16.
  */

import scala.concurrent.duration.FiniteDuration
import com.agoda.commons.models.rpc.request.RequestMeta
import com.agoda.commons.traffic.TrafficInfo
import api.request._
import com.agoda.papi.enums.campaign.{CampaignDiscountType, CampaignType}
import com.agoda.papi.enums.request.{ContractFilterType, FilterCriteria}
import com.agoda.papi.enums.room.{CancellationGroup, ChildRateType}
import com.agoda.upi.models.request.CartBaseRequest
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import models.PrecheckAccuracies
import models.db._
import models.enums.{FunnelType, ReBookingActionType}
import models.pricing.PriceGuaranteeRequest
import models.pricing.enums.{RequestedPrice, _}
import models.serialization.JacksonSerializers.ListOfLongDeserializer
import models.serialization.OptionalCharMapDeserializer
import models.utils.CartHelper
import org.joda.time.{DateTime, Days}

/**
  * Search context
  */
@SerialVersionUID(71L)
case class Experiment(name: String, variant: Char)

@SerialVersionUID(60L)
case class ABTest(testId: Int, abUser: Char)

/**
  * Contains all information about client
  *
  * @param cid
  * @param userId
  * @param languageId
  * @param languageUse
  * @param platform
  * @param origin
  * @param storefront
  * @param searchId
  * @param trafficData
  * @param deviceTypeId
  * @param ipAddress
  * @param affiliateId
  */
@SerialVersionUID(61L)
case class ClientInfo(cid: Int,
                      userId: String,
                      languageId: Int,
                      languageUse: Int,
                      platform: Int,
                      origin: String,
                      storefront: Int,
                      searchId: String,
                      trafficData: Option[TrafficInfo] = None,
                      deviceTypeId: Option[Int] = None,
                      ipAddress: Option[String] = None,
                      affiliateId: Option[String] = None,
                      locale: Option[String] = None,
                      externalPartnerId: Option[String] = None)

// Contains about session and user who is using the system
@SerialVersionUID(62L)
case class SessionInfo(sessionId: Int, memberId: Int, isLogin: Boolean)

/**
  * ExperimentInfo contain experiment parameter for experiment platform's client
  *
  * @param trafficGroup
  * @param overridenAllocationVariantMap
  * @param forceUserVariant
  * @param forceOnIntegrationRun
  * @param forceOnZeroTraffic
  */
@SerialVersionUID(-723531742975466949L)
case class ExperimentInfo(trafficGroup: String,
                          @JsonDeserialize(using = classOf[OptionalCharMapDeserializer])
                          overridenAllocationVariantMap: Option[Map[String, Char]] = None,
                          @JsonDeserialize(contentAs = classOf[Char])
                          forceUserVariant: Option[Char] = None,
                          forceOnIntegrationRun: Boolean = false,
                          forceOnZeroTraffic: Boolean = false,
                          overridenAllocationVariant: Option[List[StringAndString]] = None) {
  def fetchAllocationVariant(): Option[Map[String, Char]] =
    if (overridenAllocationVariantMap.isDefined) {
      overridenAllocationVariantMap
    } else if (overridenAllocationVariant.isDefined) {
      overridenAllocationVariant.map { o =>
        val filtered = o.filter(_.key.nonEmpty)
        filtered.map(i => i.key -> i.value.charAt(0)).toMap
      }
    } else {
      None
    }
}

case class StringAndString(key: String, value: String)

/**
  * Context is for holding the information of client and user
  *
  * @param isDebug Whether we provide debug information back to the client or not
  * @param isSnapshot Whether we activate RFlow capture snapshot for each data going out of flow/step
  */
@SerialVersionUID(63L)
case class ContextRequest(sessionInfo: SessionInfo,
                          clientInfo: ClientInfo,
                          abTests: List[ABTest],
                          experiment: List[Experiment],
                          requestMeta: Option[RequestMeta] = None,
                          isAllowBookOnRequest: Option[Boolean] = None,
                          @deprecated("field moved to new requestMeta model", "2.48.0") ttl: Option[FiniteDuration] =
                            None,
                          @deprecated("field moved to new requestMeta model", "2.48.0") isRetry: Option[Boolean] = None,
                          experimentInfo: Option[ExperimentInfo] = None,
                          isDebug: Option[Boolean] = None,
                          userContext: Option[String] = None,
                          tracingContext: Option[String] = None,
                          packaging: Option[PackagingRequest] = None,
                          showCMS: Option[Boolean] = Some(false),
                          cartRequest: Option[CartBaseRequest] = None,
                          pollingInfoRequest: Option[PollingInfoRequest] = None,
                          isSnapshot: Option[Boolean] = None,
                          externalUserContext: Option[String] = None,
                          isManualTracingEnabled: Option[Boolean] = None) {

  def isRetryRequest: Boolean = requestMeta.exists(_.isRetry)
}

/**
  * @param minOcc   minimal occupancy for all-occ-search
  * @param maxOcc   maximal occupancy for all-occ-search
  * @param occOrder Additional filter to order and filter rooms by room occupancy of a hotel
  */
@SerialVersionUID(1L)
case class OccFilter(minOcc: Option[Int] = None, maxOcc: Option[Int] = None, occOrder: Option[List[Int]] = None)

/**
  * @param adults   adults in room
  * @param childrenTypes   childType map with quantity
  * @param childrenAgeRange   childAgeRangeId map with quantity
  */
@SerialVersionUID(1L)
case class RoomAssignment(adults: Int,
                          childrenTypes: List[ChildType],
                          childrenAgeRange: Option[List[ChildAgeRangeAssignment]] = None)

@SerialVersionUID(1L)
case class ChildType(childRateType: ChildRateType, quantity: Int)

@SerialVersionUID(1L)
case class ChildAgeRangeAssignment(childAgeRangeId: Int, quantity: Int)

/**
  * Search criteria for price
  */
@SerialVersionUID(64L)
case class OccupancyRequest(adults: Int,
                            children: Int,
                            rooms: Int,
                            childAges: List[Option[Int]] = Nil,
                            occFilter: Option[OccFilter] = None,
                            roomsAssignment: Option[List[RoomAssignment]] = None,
                            childrenTypes: Option[List[ChildType]] = None)

case class RoomIdentifierFilter(roomIdentifiers: List[RoomIdentifier])

case class SymmetricUidFilter(uids: List[RoomUID])

case class CancellationGroupFilter(cancellationGroup: CancellationGroup)

case class ContractTypeFilter(contractType: ContractFilterType)

// Filter is for filtering the property
@SerialVersionUID(65L)
case class FilterRequest(ratePlans: List[Int],
                         suppliers: List[Int],
                         secretDealOnly: Boolean,
                         filterAPO: Boolean = false,
                         @deprecated("Please use nosOfBedrooms instead")
                         numberOfBedrooms: Option[Int] = None,
                         cheapestRoomFilters: List[CheapestRoomFilterId] = Nil,
                         nosOfBedrooms: Option[List[NoOfBedrooms]] = None,
                         unsupportedPaymentModels: Option[List[PaymentModel]] = None,
                         roomIdentifierFilter: Option[RoomIdentifierFilter] = None,
                         packagingFilterContext: Option[PackagingFilterContext] = None,
                         contractsFilter: Option[List[ContractsFilterContext]] = None,
                         benefitIds: Option[List[BenefitId]] = None,
                         hourlyDurations: Option[List[Int]] = None)

// Detail is for choosing which price data that client needs to return
@SerialVersionUID(66L)
case class DetailRequest(priceBreakdown: Boolean, itemBreakdown: Boolean, cheapestPriceOnly: Boolean)

/**
  * Feature is for enable pricing features
  *
  * @param isMSE With is MSE flag on, soybean will ignore streamlining and group the rooms based on
  *              list of criterias (master room id, benefits, cxl type, occupancy, etc).
  *              Needs to be optional until PAPI starts to send it
  */
@SerialVersionUID(67L)
case class FeatureRequest(@deprecated("everything is newrate model at this point")
                          newRateModel: Boolean,
                          @deprecated("DF doesn't support this any more", "2.22.0") overrideOccupancy: Boolean,
                          @deprecated("use enableCOR instead") crossOutRate: Boolean,
                          priusId: Int,
                          isApsEnabled: Boolean = false,
                          isRPM2Included: Boolean = false,
                          isIncludeUsdAndLocalCurrency: Boolean = false,
                          isAPSPeek: Boolean = false,
                          isUsingHotelCurrency: Option[Boolean] = None,
                          synchronous: Boolean = false,
                          isAllOcc: Boolean = false,
                          isMSE: Option[Boolean] = None,
                          isAllowRoomTypeNotGuarantee: Option[Boolean] = None,
                          maxSuggestions: Option[Int] = None,
                          turnOffFxi: Option[Boolean] = None,
                          isEnableSupplierFinancialInfo: Option[Boolean] = None,
                          calculateCancellationPhases: Option[Boolean] = None,
                          isLoggingAuctionData: Option[Boolean] = None,
                          enableRatePlanCheckInCheckOut: Option[Boolean] = None,
                          showCouponAmountInUserCurrency: Option[Boolean] = None,
                          excludeVoucherRooms: Option[Boolean] = None,
                          showOnlyPullSupplier: Option[Boolean] = None,
                          enableCOR: Option[Boolean] = None,
                          enablePushDayUseRates: Option[Boolean] = None,
                          @deprecated("escapes enabled by default. Use disableEscapesPackage field to disable")
                          enableEscapesPackage: Option[Boolean] = None,
                          filterCheapestRoomEscapesPackage: Option[Boolean] = None,
                          enableSecretDealImprovement: Option[Boolean] = None,
                          @deprecated("replaced with feature flag CreditCardPromotionPeek")
                          enableCreditCardCampaignPeek: Option[Boolean] = None,
                          calculateRareRoomBadge: Option[Boolean] = None,
                          enableCxlAversion: Option[Boolean] = None,
                          enableDownliftForCxlAversion: Option[Boolean] = None,
                          enableRichContentOffer: Option[Boolean] = None,
                          getAlternativeRoom: Option[Boolean] = None,
                          enableReturnNonApprovedEscapes: Option[Boolean] = None,
                          disableEscapesPackage: Option[Boolean] = None,
                          hasBooking: Option[Boolean] = None,
                          returnCheapestEscapesOfferOnSSR: Option[Boolean] = None,
                          enableBenefitValuationForASO: Option[Boolean] = None,
                          shouldAddPFInMarketingFee: Option[Boolean] = None,
                          enableDayUseCor: Option[Boolean] = None,
                          showCheapestHourlyRate: Option[Boolean] = None,
                          showPastMidnightSlots: Option[Boolean] = None,
                          sendCancellationSurcharge: Option[Boolean] = None,
                          enableHourlySlotsForDayuseInOvernight: Option[Boolean] = None,
                          enableThirtyMinsSlots: Option[Boolean] = None,
                          sortByCheckInTimeDayUseSSR: Option[Boolean] = None,
                          mergeDayUseOffersWithOvernight: Option[Boolean] = None,
                          ignoreRoomsCountForNha: Option[Boolean] = None,
                          enableSuggestPriceExclusiveWithFees: Option[Boolean] = None)

// Pricing is the main class to hold the search criteria
/**
  * PricingRequest
  *
  * @param whiteLabelKey whitelabel key is a key to call white label service to get wl's settings
  * @param clientCampaignInfos client pmc campaigns stored in local device
  */
@SerialVersionUID(68L)
case class PricingRequest(
  occupancy: OccupancyRequest,
  filters: FilterRequest,
  details: DetailRequest,
  features: FeatureRequest,
  checkIn: DateTime,
  checkout: DateTime,
  currency: Currency,
  bookingDate: DateTime,
  roomRequest: Option[PricingRoomRequest],
  partner: Option[PartnerRequest] = None,
  refId: Option[Int] = None,
  paymentId: Option[Int] = None,
  featureFlag: List[FeatureFlag] = Nil,
  maxRooms: Option[Int] = None,
  includedPriceInfo: Option[Boolean] = None,
  discountRequest: Option[DiscountRequest] = None,
  payment: Option[PaymentRequest] = None,
  supplierPullMetadata: Option[SupplierPullMetadataRequest] = None,
  mseHotelIds: Option[List[Int]] = None,
  mseClicked: Option[String] = None, // New version of mseHotelIds
  fencedRate: Option[FencedRate] = None,
  @deprecated // this roomSelection field will be deprecated, please use simplifiedRoomSelectionRequest.
  roomSelection: Option[RoomSelectionRequest] = None,
  whiteLabelKey: Option[String] = None,
  ppLandingHotelIds: Option[List[Int]] = None,
  searchedHotelIds: Option[List[Int]] = None,
  localCheckInDate: Option[String] = None,
  localCheckoutDate: Option[String] = None,
  bookingDurationType: Option[List[String]] = None,
  roomBundleHints: Option[List[RoomBundleRequest]] = None,
  priceHistory: Option[PriceHistoryRequest] = None,
  clientCampaignInfos: Option[List[CampaignInfo]] = None,
  simplifiedRoomSelectionRequest: Option[SimplifiedRoomSelectionRequest] = None,
  excludeFilters: Option[FilterOutRequest] = None,
  priceAdjustmentRequest: Option[List[PriceAdjustment]] = None,
  externalLoyaltyRequest: Option[ExternalLoyaltyRequest] = None,
  selectedRewardOption: Option[String] = None,
  stateId: Option[Int] = None,
  requiredBasis: Option[ApplyType] = None,
  selectedCheckInTime: Option[String] = None,
  externalLoyaltyProfile: Option[ExternalLoyaltyProfile] = None,
  metadata: Option[List[StringAndString]] = None,
)

/**
  * PriceAdjustment
  * This feature is to adjust the ebe financial breakdown as a request
  * if you want to adjust other item please set up a meeting with DF to modify the whitelist
  * @param roomId          roomUID or room identifier
  * @param requestedPrice  requested price of the item that client want to change
  * @param chargeType      charge type Support: BookingItemTypes.Room, BookingItemTypes.ExtraBed, BookingItemTypes.Surcharge
  * @param rateType        rate type Support: BookingRateTypes.SellInclusive, BookingRateTypes.NetInclusive
  * @param applyType       apply type Support: ApplyTypes.PRPB, ApplyTypes.PRPN, ApplyTypes.PN, ApplyTypes.PB
  * @param chargeOption    charge option Support: ChargeOptions.Mandatory, ChargeOptions.Optional, ChargeOptions.Excluded, ChargeOptions.Included
  * @param surchargeId
  * @param requestCurrency must be booking request currency only
  */
case class PriceAdjustment(roomId: RoomUID,
                           requestedPrice: Double,
                           chargeType: Int,
                           rateType: Int,
                           applyType: String,
                           chargeOption: Int,
                           surchargeId: Option[Int],
                           requestCurrency: Currency)

@SerialVersionUID(1L)
case class RoomSelectionRequest(occupancy: Int,
                                children: Int,
                                extraBed: Int,
                                mandatoryExtraBed: Int,
                                room: Int,
                                isRespectMaxOcc: Boolean,
                                needOccupancySearch: Boolean,
                                masterRoomOccupancy: Option[Int] = None,
                                isFit: Boolean,
                                requestExtraBedForRoomNumbers: Option[List[Int]] = None)

@SerialVersionUID(1L)
case class SimplifiedRoomSelectionRequest(roomIdentifier: String,
                                          room: Option[Int],
                                          requestExtraBedForRoomNumbers: Option[List[Int]] = None,
                                          selectedUpSellingRoomType: Option[SwapRoomType] = None)

@SerialVersionUID(69L)
case class FencedRate(@deprecated("Please use fencedRatePairs instead", "2.62.x")
                      origins: List[String] = Nil,
                      @deprecated("Please use fencedRatePairs instead", "2.62.x")
                      fencedOriginMap: Option[Map[String, FencedOriginObject]] = None,
                      fencedRatePairs: Option[List[FencedRatePair]] = None,
                      fencedOrigin: Option[List[StringAndFencedOriginObject]] = None) {

  def isEnabled(): Boolean = origins.nonEmpty || fencedRatePairs.getOrElse(Nil).nonEmpty

  def fetchFencedOrigin(): Option[Map[String, FencedOriginObject]] =
    if (fencedOriginMap.isDefined) {
      fencedOriginMap
    } else if (fencedOrigin.isDefined) {
      fencedOrigin.map(o => o.map(i => i.key -> i.value).toMap)
    } else {
      None
    }
}

case class StringAndFencedOriginObject(key: String, value: FencedOriginObject)

@SerialVersionUID(70L)
case class FencedRateKey(origin: Option[String] = None, siteId: Option[Int] = None, language: Option[Int] = None)

@SerialVersionUID(71L)
case class FencedRatePair(key: FencedRateKey, value: FencedOriginObject)

@SerialVersionUID(1L)
case class BookingFilter(
  @JsonDeserialize(contentUsing = classOf[ListOfLongDeserializer]) masterRoomIds: Option[List[RoomTypeId]] = None,
  uidList: Option[List[String]] = None,
  @deprecated("Please use roomIdentifiers instead", "2.28.x")
  matchedCriteria: Option[List[MatchedRoomCriteria]] = None,
  roomIdentifiers: List[RoomIdentifier] = Nil,
  backupCriteria: Option[List[BackupRoomCriteria]] = None,
)

@SerialVersionUID(1L)
case class RoomIdentifier(uid: RoomUID, overrideIdentifier: OverrideRoomIdentifier)

@SerialVersionUID(77L)
case class SelectedHourlySlot(from: String, duration: Int)

/**
  * Since UID is generated by DF only. Client cannot re-generate or cannot understand what criterias inside.
  * If client need to change some parameter/criterias for each room, they have to send UID of old room and [OverrideRoomIdentifier]
  * DF will produce new UID with [OverrideRoomIdentifier]
  * This model is duplicated from ypl model
  */
@SerialVersionUID(1L)
case class OverrideRoomIdentifier(numberOfExtrabed: Option[Int] = None)

@SerialVersionUID(1L)
case class BookingRequest(
  @deprecated("Please use BookingFilter.uidList instead", "2.25.8")
  uid: List[String] = Nil,
  filters: List[BookingFilter] = Nil,
  partner: Option[PartnerRequest] = None,
  priceState: Option[PriceStateRequest] = None,
  @deprecated("Logging propose, no side effect here, will be deprecated after collecting more information")
  loggingRoomIdentifier: Option[String] = None,
  reBookingRequest: Option[ReBookingRequest] = None,
  rebookAndCancelRequest: Option[RebookAndCancelRequest] = None,
  selectedHourlySlot: Option[SelectedHourlySlot] = None,
  @deprecated("Deprecated as this field is specifically use for thai gov project")
  @JsonDeserialize(contentAs = classOf[java.lang.Double])
  overrideBookingSellAllIn: Option[Double] = None,
  priceFreeze: Option[PriceFreeze] = None,
  @JsonDeserialize(contentAs = classOf[java.lang.Long])
  priceAdjustmentId: Option[Long] = None,
)

@SerialVersionUID(1L)
case class PaymentMethod(paymentType: Int, bin: String)

/**
  * Discount for Client
  *
  * @param campaignInfo     Information of campaign to resolve the promotion
  * @param overrideCampaign Campaign information included constraint and percentage
  * @param campaignInfos    List of Information of campaign to resolve the promotion
  */

@SerialVersionUID(1L)
case class DiscountRequest(@deprecated("Will be deprecated soon. Please use campaignInfos instead", "2.20.9")
                           campaignInfo: Option[CampaignInfo] = None,
                           overrideCampaign: Option[OverrideCampaign] = None,
                           hashedBinNo: Option[Set[String]] = None,
                           email: Option[String] = None,
                           campaignInfos: Option[List[CampaignInfo]] = None) {

  @JsonIgnore
  lazy val getRequestedPromoCode: Set[String] = campaignInfos
    .map { cInfos =>
      cInfos.collect {
        case c if c.promotionCode.nonEmpty => c.promotionCode
      }(collection.breakOut): Set[String]
    }
    .getOrElse(Set.empty)
}

/**
  * Campaign Information
  *
  * @param id            campaign id
  * @param cid           site id of campaign
  * @param promotionCode promotion code that is belonged to this campaign
  */
@SerialVersionUID(1L)
case class CampaignInfo(id: Int,
                        cid: Int,
                        promotionCode: String,
                        promotionId: Option[Int] = None,
                        childPromotions: Option[List[ChildPromotion]] = None,
                        description: Option[String] = None,
                        isMarkUse: Option[Boolean] = None,
                        campaignType: Option[CampaignType] = None)

/**
  * Campaign Discount
  *
  * @param campaignInfo     Infomation of campaign to resolve the promotion
  * @param campaignDiscount constraint and percentage of campaign
  */

@SerialVersionUID(1L)
case class OverrideCampaign(campaignInfo: CampaignInfo, campaignDiscount: CampaignDiscount)

/**
  * Campaign Discount
  *
  * @param amount               discount percentage or amount depend on discountType
  * @param currency             currency of discount amount
  * @param discountType         stand for type of promotion (amount,pencentage)
  * @param cmsId                cmsId
  * @param minBookingValue      minimum booking amount that is applicabled for discount
  * @param minTotalBookingValue minimum booking amount after applying discount
  */

@SerialVersionUID(1L)
case class CampaignDiscount(amount: Double,
                            currency: Currency,
                            discountType: CampaignDiscountType,
                            cmsId: Option[Int] = None,
                            @JsonDeserialize(contentAs = classOf[java.lang.Double])
                            minBookingValue: Option[Double] = None,
                            @deprecated("This will be obsoleted, EBE will validate this instead.")
                            @JsonDeserialize(contentAs = classOf[java.lang.Double])
                            minTotalBookingValue: Option[Double] = None)

/**
  * Property Token Request
  *
  * @param token Token string, DF generate it then client have to send this back
  */
@SerialVersionUID(1L)
case class PriceStateRequest(token: String)

/**
  * ReBooking Request
  *
  * Field usage by flow:
  *   - Search Request:                roomTypeId, masterRoomTypeId
  *   - Rebooking Request V1/V2:       originalSellIn, originalUsdToRequestExchangeRate, actionType
  *   - Rebooking Request V3:          roomTypeId, originalCashbackAmount, originalPromoAmount, originalSellIn, originalUsdToRequestExchangeRate, actionType
  *
  * @param roomTypeId               Room type ID of the original booking.
  * @param customerPaidPrice        Original paid price by the customer. Does not equal sell inclusive.
  *                                 This is used for up/downlift calculation to make the new price match. Used in v1/v2 logic.
  * @param originalNetIn            Original total Net Inclusive amount in req currency.
  *                                 This is used to create new item breakdown: OptimizationGain which is Old NetIn - New NetIn. Used in v1/v2 logic.
  * @param originalSellIn           The total Sell Inclusive amount that the customer has paid for the previous booking. Used in v3 logic.
  * @param originalPromoAmount      Original Promo Code amount.
  * @param originalCashbackAmount   Original cashback earnings amount.
  */
@SerialVersionUID(1L)
case class ReBookingRequest(roomTypeId: RoomTypeId,
                            @JsonDeserialize(contentAs = classOf[java.lang.Long])
                            masterRoomTypeId: Option[RoomTypeId],
                            @deprecated("Not used anymore")
                            customerPaidPrice: Double,
                            @deprecated("Not used anymore")
                            @JsonDeserialize(contentAs = classOf[java.lang.Double])
                            originalNetIn: Option[Double],
                            @deprecated("Will be removed")
                            @JsonDeserialize(contentAs = classOf[java.lang.Double])
                            originalCashback: Option[Double],
                            @JsonDeserialize(contentAs = classOf[java.lang.Double])
                            originalPromoAmount: Option[Double],
                            @JsonDeserialize(contentAs = classOf[java.lang.Double])
                            originalUsdToRequestExchangeRate: Option[Double],
                            @deprecated("Will be removed")
                            @JsonDeserialize(contentAs = classOf[java.lang.Double])
                            originalSellInUsd: Option[Double],
                            @JsonDeserialize(contentAs = classOf[java.lang.Double])
                            originalSellIn: Option[Double],
                            @JsonDeserialize(contentAs = classOf[java.lang.Double])
                            originalCashbackAmount: Option[Double],
                            @JsonDeserialize(contentAs = classOf[ReBookingActionType])
                            actionType: Option[ReBookingActionType])

/**
  * RebookAndCancelRequest
  *
  * @param transferablePayment    This is the transferable payment from old booking to new booking. It contains transferable amounts in usd and local currency
  */
@SerialVersionUID(1L)
case class RebookAndCancelRequest(transferablePayment: TransferablePayment)

/**
  * PaymentTransferItem
  *
  * @param usdSourceAmount       This is the usd amount that can be transferred from old booking
  * @param paymentSourceAmount   This is the local amount that can be transferred from old booking in the specified currency
  * @param currency              The local currency for the booking
  */
@SerialVersionUID(1L)
case class TransferablePayment(@JsonDeserialize(contentAs = classOf[java.lang.Double])
                               usdSourceAmount: Double,
                               @JsonDeserialize(contentAs = classOf[java.lang.Double])
                               paymentSourceAmount: Double,
                               currency: String)

/**
  * Pricing Request Parameters which is without propertyIds
  */
case class PricingRequestParameters(context: ContextRequest,
                                    pricing: PricingRequest,
                                    cheapestOnly: Boolean = false,
                                    suggestedPrice: String,
                                    isSSR: Option[Boolean],
                                    booking: Option[BookingRequest] = None,
                                    simulateRequestData: Option[SimulateRequestData] = None,
                                    roomSortingStrategy: Option[RoomSortingStrategy] = None,
                                    rocketmilesPublishPriceRequest: Option[RocketmilesPublishPriceRequest] = None,
                                    perOccupancyBreakdownType: Option[ApplyType] = None)

/**
  * Keep Params to Override DF request from Clay
  * @param filterAPO
  */
case class OverridePricingRequest(filterAPO: Option[Boolean],
                                  userContext: Option[String] = None,
                                  emailDomain: Option[String] = None,
                                  ratePlans: Option[List[Int]] = None,
                                  isApsEnabled: Option[Boolean] = None,
                                  checkInDate: Option[DateTime] = None,
                                  checkOutDate: Option[DateTime] = None,
                                  cheapestOnly: Option[Boolean] = None,
                                  cid: Option[Int] = None,
                                  enableSecretDealImprovement: Option[Boolean] = None,
                                  externalUserContext: Option[String] = None,
                                  hasHourlyRates: Option[Boolean] = None,
                                  additionalFeatureFlags: Option[List[FeatureFlag]] = None,
                                  requiredBasis: Option[ApplyType] = None)

@deprecated("Not supported anymore")
case class OverridePricingContext(cid: Option[Int] = None)

/**
  * Pricing Properties Request which is used in new endpoint
  */
case class PricingPropertiesRequest(@JsonDeserialize(contentAs = classOf[java.lang.Long])
                                    propertyIds: Seq[Long],
                                    pricingRequestParameters: PricingRequestParameters,
                                    overridePricingRequest: Option[OverridePricingRequest] = None)

/**
  * Property search request which is used as the main object for searching property
  *
  * simulateRequestData - YCS needs to simulate Hotel Search result One Property at a time for Promotions for now.
  *
  * TODO -> Any new field in PropertySearchRequest should also be added in PricingRequestParameters request
  */
@SerialVersionUID(69L)
case class PropertySearchRequest(context: ContextRequest,
                                 @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                 propertyIds: Seq[Long],
                                 pricing: PricingRequest,
                                 cheapestOnly: Boolean = false,
                                 suggestedPrice: String,
                                 isSSR: Option[Boolean],
                                 booking: Option[BookingRequest] = None,
                                 simulateRequestData: Option[SimulateRequestData] = None,
                                 roomSortingStrategy: Option[RoomSortingStrategy] = None,
                                 isMixNSaveSegmentSearch: Option[Boolean] = Some(false),
                                 rocketmilesPublishPriceRequest: Option[RocketmilesPublishPriceRequest] = None,
                                 perOccupancyBreakdownType: Option[ApplyType] = None,
                                 propertySearchToken: Option[String] = None) {
  @JsonIgnore
  def getRequestedPrice: RequestedPrice = {
    val requestedPrice = RequestedPrice.namesToValuesMap.getOrElse(suggestedPrice, RequestedPrice.Unknown)

    if (requestedPrice == RequestedPrice.ExclusiveWithFees) {
      RequestedPrice.Exclusive
    } else {
      requestedPrice
    }
  }

  @JsonIgnore
  def getRequestedBasis: Option[ApplyType] = pricing.requiredBasis

  @JsonIgnore
  def getPriceGuaranteeRequest: PriceGuaranteeRequest = PriceGuaranteeRequest(
    propertyIds = this.propertyIds,
    cid = this.context.clientInfo.cid,
    pricing = this.pricing,
    uidFilters = this.booking.map(_.uid).getOrElse(Nil),
  )

  @JsonIgnore
  def isBookingRequest: Boolean = booking.isDefined

  @JsonIgnore
  def isRebookingRequestV3: Boolean = booking.exists(_.reBookingRequest.exists(_.originalSellInUsd.exists(_ > 0d)))

  @JsonIgnore
  def isRebookingRequestV1V2: Boolean =
    booking.exists(_.reBookingRequest.exists(_.customerPaidPrice > 0d)) && !isRebookingRequestV3

  @JsonIgnore
  def isCreditCardPresent: Boolean = {
    val creditCard = pricing.payment.flatMap(_.creditCard)
    isBookingRequest && (creditCard.flatMap(_.ccToken).isDefined || creditCard.flatMap(_.ccOf).isDefined)
  }

  @JsonIgnore
  def isHourlyBookingRequest: Boolean = booking.isDefined && booking.flatMap(_.selectedHourlySlot).isDefined

  @JsonIgnore
  def isPackagingRequest: Boolean = context.packaging.nonEmpty || CartHelper.isPackagingFunnel(context.cartRequest)

  /**
    * Get request type; in the future we may have different definitions
    */
  @JsonIgnore
  def getRequestType: String =
    if (isSSR.getOrElse(false)) "ssr"
    else if (isBookingRequest) {
      if (isRebookingRequestV3) "rebookv3"
      else if (isRebookingRequestV1V2) "rebookv1v2"
      else "booking"
    } else "property"

  @JsonIgnore
  def isSortRoomsByNetPrice: Boolean =
    roomSortingStrategy.contains(RoomSortingStrategies.SupplierFinancialsWithTotalNetPrice) ||
    roomSortingStrategy.contains(RoomSortingStrategies.PopawiFirstSupplierFinancialsWithTotalNetPrice) ||
    roomSortingStrategy.contains(RoomSortingStrategies.BMPFirstSupplierFinancialsWithTotalNetPrice)

  @JsonIgnore
  def getLos: Int = Days.daysBetween(pricing.checkIn, pricing.checkout).getDays()

  @JsonIgnore
  def isCartFeatureEnabled: Boolean = CartHelper.isCartFeatureEnabled(context.cartRequest)

  @JsonIgnore
  @deprecated(message = "Deprecated", since = "2024-10-31")
  def isHotelPlusHotelRequest: Boolean = pricing.featureFlag.contains(FeatureFlag.MultiHotel)

  @JsonIgnore
  def isFlightPlusHotelRequest: Boolean = !isHotelPlusHotelRequest && isPackagingRequest

  @JsonIgnore
  def isHotelPlusSecondHotelRequest: Boolean = isHotelPlusHotelRequest && context.packaging.flatMap(_.token).nonEmpty

  @JsonIgnore
  def getFunnel: String =
    if (isHotelPlusHotelRequest) {
      FunnelType.MultiHotel.toString
    } else if (isFlightPlusHotelRequest) {
      FunnelType.Packages.toString
    } else if (isCartFeatureEnabled) {
      FunnelType.Cart.toString
    } else {
      FunnelType.Hotel.toString
    }

  @JsonIgnore
  def getBookingDurationType: String = pricing.bookingDurationType
    .map { bookingDurationType =>
      bookingDurationType.sortBy(identity).mkString(",")
    }
    .getOrElse("unknown")

  def isSSRRequest(): Boolean = this.isSSR.getOrElse(false)

  @JsonIgnore
  def getCorrelationId(fallback: Option[String]): String =
    context.clientInfo.trafficData.flatMap(_.correlationId).map(_.toString).getOrElse(fallback.getOrElse(""))
}

@SerialVersionUID(70L)
case class PartnerRequest(partnerRoomRateType: Option[Int] = None,
                          partnerSurchargeRateType: Option[Int] = None,
                          ratePartnerSummaries: Option[Boolean] = None,
                          discountType: Option[Int] = None,
                          isExcludedPfFromTax: Option[Boolean] = None,
                          returnDailyRates: Option[Boolean] = Some(true),
                          filterCriteria: Option[List[FilterCriteria]] = None,
                          minBookingCountForSuperAgg: Option[Seq[MinBookingCountForSuperAgg]] = None)

@SerialVersionUID(-4028944854211973653L)
case class PricingRoomRequest(uid: String, dmcRoomId: String)

@SerialVersionUID(71L)
case class LoyaltyPaymentRequest(applyAmountInRequestCurrency: Double,
                                 availableAmountInUSD: Double,
                                 `type`: LoyaltyType,
                                 pointsRedeemed: Option[Int])

@SerialVersionUID(1L)
case class PaymentCreditCard(paymentCurrency: String,
                             creditCardCurrency: String,
                             paymentChargeOption: PaymentChargeType,
                             countryIdOfIssuingBank: Option[Int] = None,
                             ccOf: Option[Long] = None,
                             ccToken: Option[String] = None)

@SerialVersionUID(72L)
/**
  * PaymentRequest
  *
  * @param paymentOption The selected payment charge type.
  *                      - 0: None
  *                      - 1: Pay Now
  *                      - 2: Pay Later
  *                      - 3: Pay at Check-In
  *                      - 4: Pay with Installment
  */
case class PaymentRequest(loyalty: Option[LoyaltyPaymentRequest] = None,
                          creditCard: Option[PaymentCreditCard] = None,
                          installmentInfo: Option[InstallmentInfo] = None,
                          customerTaxCountryCode: Option[String] = None,
                          cashback: Option[LoyaltyPaymentRequest] = None,
                          isCashbackRedemptionEligible: Option[Boolean] = None,
                          paymentOption: Option[Int] = None)

/**
  * SPAPI metadata
  * requiredPrecheckAccuracy can have values HIGH or NONE (HIGH means clients cannot tolerate price mismatch)
  */
@SerialVersionUID(73L)
case class SupplierPullMetadataRequest(requiredPrecheckAccuracyLevel: Int = PrecheckAccuracies.HIGH.level,
                                       numOfRetries: Option[Int] = None)

@SerialVersionUID(74L)
case class FencedOriginObject(ratePlans: RatePlans)

@SerialVersionUID(75L)
case class PackagingRequest(token: Option[PackagingRequestToken])

@SerialVersionUID(76L)
case class PackagingRequestToken(clientToken: String, interSystemToken: Option[String])

/**
  * RoomBundleRequest hint for bundled searches
  *
  * @param checkin checking for the bundle segment
  * @param los los for the bundle segment
  * @param roomIdentifier roomIdentifier for filtering the room required in the segment for booking request
  */
@SerialVersionUID(1L)
case class RoomBundleRequest(checkin: DateTime, los: Int, roomIdentifier: Option[RoomIdentifier] = None)

@SerialVersionUID(1L)
case class PriceHistoryRequest(daysToLookBack: Option[Int])

@SerialVersionUID(1L)
case class PollingInfoRequest(pollId: Option[String], pollAttempt: Option[Int])

@SerialVersionUID(78L)
case class FilterOutRequest(symmetricUidFilter: Option[SymmetricUidFilter] = None)

@SerialVersionUID(79L)
case class PackagingFilterContext(cancellationGroupFilter: Option[CancellationGroupFilter] = None)

case class ContractsFilterContext(contractTypeFilter: Option[ContractTypeFilter] = None)

/**
  * ExternalLoyaltyRequest
  *
  * @param selectedOffersIdentifier : The selected offer identifier from ELAPI downstream
  * @param points : The amount of points the user wants to redeem
  * @param partnerClaimToken : The token containing the user context and partner settings for external partner
  */
@SerialVersionUID(1L)
case class ExternalLoyaltyRequest(
  selectedOffersIdentifier: Option[String],
  @JsonDeserialize(contentAs = classOf[java.lang.Double])
  points: Option[Double],
  itemPriceInPoints: Option[Double] = None,
  partnerClaimToken: Option[String],
  loyaltySearchType: Option[String],
)

/**
  * PriceFreeze
  * @param frozenPrice The price of the room at which it was claimed
  * @param deposit The initial amount paid for the claim
  * @param maxCap The maximum bound until which Agoda can absorb the price increase
  */
@SerialVersionUID(1L)
case class PriceFreeze(@JsonDeserialize(contentAs = classOf[java.lang.Double])
                       frozenPrice: Double,
                       @JsonDeserialize(contentAs = classOf[java.lang.Double])
                       deposit: Double,
                       @JsonDeserialize(contentAs = classOf[java.lang.Double])
                       maxCap: Double)

/**
  * InstallmentInfo
  * @param installmentPlanCode The Installment planCode
  *  @param isInstallmentPayment Installment Payment Toggle on Payment Page
  */
@SerialVersionUID(1L)
case class InstallmentInfo(installmentPlanCode: String, isInstallmentPayment: Option[Boolean])

@SerialVersionUID(1L)
case class SupplyEquitySimulationParameters(simulationContextId: String)

@SerialVersionUID(1L)
case class MinBookingCountForSuperAgg(inDays: Int, count: Int)
