package com.agoda.platform.pricing.models.utils

import api.request._
import com.agoda.finance.tax.enums.TaxLevelCalculationType
import com.agoda.finance.tax.models.TaxPrototypeInfo
import com.agoda.papi.enums.campaign.{IneligiblePromotionReason, PromoApplyType, PromotionCodeType}
import com.agoda.papi.enums.hotel.{FireDrillContractType, PaymentModel}
import com.agoda.papi.enums.request.StackDiscountOption
import com.agoda.papi.enums.room.{
  ApplyTaxOver,
  CancellationGroup,
  ExternalLoyaltyItem,
  GeoType,
  PaymentChannel,
  SubChargeType,
  ValueCalculationMethodType,
  ValueMethodType,
  WhomToPayType,
  ApplyType => CommonApplyType,
  ChargeOption => CommonChargeOption,
  ChargeType => CommonChargeType,
}
import com.agoda.papi.pricing.metadata._
import com.agoda.papi.pricing.pricecalculation.models.CommonProcessingFeeBreakdown
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes, Tax, TaxWithValue}
import com.agoda.papi.ypl.models.GUIDGenerator.{
  CoreFields,
  ExtendFields,
  PricingFields,
  RoomIdentifiers,
  RoomSelectionFields,
  StayOccupancyFields,
}
import com.agoda.papi.ypl.models.enums.BreakdownStep
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.pricing.{BookingPriceBreakdown, DiscountMessage => YplDiscountMessage}
import com.agoda.papi.ypl.models.{LanguageId, Quantity, RateSilo, RoomTypeId, SupplierId, SurchargeInfo, TaxMetaData}
import com.agoda.platform.pricing.models.utils.Utils._
import com.agoda.upi.models.common.Arrangement
import com.agoda.upi.models.enums.{ArrangementEntries, ItemEntries, ProductEntries}
import com.agoda.upi.models.request.{CartBaseRequest, CartItemMetadata, CartMetadata}
import models._
import models.consts.Whitelabel
import models.db._
import models.enums.ReBookingActionType
import models.pricing.enums._
import models.pricing.{TimeInterval => _, _}
import models.starfruit.{
  DetailRequest,
  DiscountRequest,
  Display,
  FilterRequest,
  HotelFundedInfo,
  LoyaltyPaymentRequest,
  LoyaltyTypes,
  OccupancyRequest,
  PackagingRequest,
  PackagingRequestToken,
  PartnersPriceBreakdown,
  PayType,
  PaymentRequest,
  PriceAdjustment,
  PricingRequest,
  PromotionDiscounts,
  TaxReceipt,
  Charge => SFCharge,
  Daily => SFDaily,
  FeatureRequest => SfFeatureRequest,
  PayTypes => SFPayTypes,
  Price => SFPrice,
}
import models.whitelabel._
import org.joda.time.{DateTime, LocalDate}
import org.specs2.mock.Mockito

import scala.language.implicitConversions

object DFModelTestDataBuilders extends DFModelTestDataBuilders

trait DFModelTestDataBuilders extends Mockito {

  /* =================================
   *        EXPOSED BUILDERS
   * ================================= */
  lazy val aValidPrice = PriceBuilder(examplePrice)
  lazy val aValidRefPrice = PriceBuilder(exampleRefPrice)
  lazy val aValidTaxBreakdown = TaxBreakdownBuilder(exampleTaxBreakdown)
  lazy val aValidDailyTaxes = DailyTaxesBuilder(exampleDailyTaxes)
  lazy val aValidHPDailyTaxes = DailyTaxesBuilder(exampleHPDailyTaxes)
  lazy val aValidTaxWithValue = TaxWithValueBuilder(exampleTaxWithValue)
  lazy val aValidTax = TaxBuilder(exampleTax)

  lazy val aValidPriceGuaranteeRequest = PriceGuaranteeRequestBuilder(examplePriceGuaranteeRequest)

  lazy val aValidSupplierHotel = SupplierHotelBuilder(exampleSupplierHotel)
  lazy val aValidPromotion = PromotionBuilder(examplePromotion)
  lazy val aValidRateCategory = RateCategoryBuilder(exampleRateCategory)
  lazy val aValidFireDrillContract = FireDrillContractBuilder(exampleFireDrillContract)

  lazy val aValidPriceEntry = PriceEntryBuilder(examplePriceEntry)
  lazy val aValidDailyPrice = DailyPriceBuilder(exampleDailyPrice)

  lazy val aValidCampaignDiscount = PromotionCampaignBuilder(examplePromotionCampaign)
  lazy val aValidPromocodeInfo = PromocodeInfoBuilder(examplePromocodeInfo)
  lazy val aValidExchangeRate = ExchangeRateBuilder(exampleExchangeRate)
  lazy val aValidSFCharge = SFChargeBuilder(aSFCharge)
  lazy val aValidSFPrice = SFPriceBuilder(aSFPrice)

  lazy val aValidPromotionDiscounts = PromotionDiscountsBuilder(examplePromotionDiscounts)
  lazy val aValidCrossOutRate = CrossOutRateBuilder(exampleCrossOutRate)
  lazy val aValidCOR = CORBuilder(exampleCor)
  lazy val aValidDiscountInfo = DiscountInfoBuilder(exampleDiscountInfo)
  lazy val aValidAccountingEntity = AccountingEntityBuilder(exampleAccountEntity)
  lazy val aValidConvertPrice = ConvertedPriceBuilder(exampleConvertedPrice)
  lazy val aValidCalculationFlowStats = CalculationFlowStatsBuilder(aCalculationFlowStats)
  lazy val aValidHotelDataBuilder = HotelDataBuilder(aValidHotelData)
  lazy val aValidTaxMetaData = TaxMetaDataBuilder(exampleTaxMetaData)
  lazy val aValidFeatureRequest = FeatureRequest()
  lazy val aValidBackupRoomCriteria = BackupRoomCriteriaBuilder(backupRoomCriteria)
  lazy val aValidReBookingRequestV2 = reBookingRequestV2
  lazy val aValidReBookingRequestV3 = reBookingRequestV3
  lazy val aValidPackagingRequest = PackagingRequestBuilder(packagingRequest)
  lazy val aValidCartRequest = CartRequestBuilder(cartRequest)
  lazy val aValidPackagingRequestToken = PackagingRequestTokenBuilder(packagingToken)
  lazy val aValidUUID = defaultUUID
  lazy val aValidDmcData = DmcDataBuilder(exampleDmcData)
  lazy val aValidPaymentInfo = PaymentInfoBuilder(paymentInfo)
  lazy val aValidPartialRefundInfo = PartialRefundInfoBuilder(examplePartiallyRefundable)
  lazy val agodaWhiteLabelSetting = WhitelabelSetting(
    1,
    Map(332 -> true),
    Set(1, 2, 3),
    SupplierConfiguration(CommissionAndMarginOverride(true, true, true, true)),
    Some(LoyaltyProgram(Some(1), Some(false), Some(100), Some(0.0), Map("USD" -> 1, "JPY" -> 100))),
    Coupon(None),
    ExchangeRateConfiguration(false),
    Nil,
    Nil,
    false,
    enabledCashbackRedemption = true,
    isMixAndSaveEnabled = true,
  )

  lazy val agodaWhiteLabelEnableCorSetting = WhitelabelSetting(
    1,
    Map(332 -> true),
    Set(1, 2, 3),
    SupplierConfiguration(CommissionAndMarginOverride(true, true, true, true)),
    Some(LoyaltyProgram(Some(1), Some(false), Some(100), Some(0.0), Map("USD" -> 1, "JPY" -> 100))),
    Coupon(None),
    ExchangeRateConfiguration(false),
    Nil,
    Nil,
    false,
    enabledCashbackRedemption = true,
    isMixAndSaveEnabled = true,
    isPartnerEnabledForCor = true,
  )

  lazy val allWhiteLabelSetting = WhitelabelSetting(
    0,
    Map.empty,
    Set.empty,
    SupplierConfiguration(CommissionAndMarginOverride(false, false, false, false)),
    Some(LoyaltyProgram(Some(1), Some(true), Some(100), Some(0.0), Map("USD" -> 1, "JPY" -> 100))),
    Coupon(None),
    ExchangeRateConfiguration(false),
    Nil,
    Nil,
    false,
  )

  lazy val rurubuWhiteLabelSetting = WhitelabelSetting(
    4,
    Map.empty,
    Set.empty,
    SupplierConfiguration(
      CommissionAndMarginOverride(isCommissionOverride = false,
                                  isCommissionAdjustment = false,
                                  isMarginAdjustment = false,
                                  isAdjustCommissionFromHotelContract = false)),
    Some(LoyaltyProgram(Some(1), Some(true), Some(100), Some(0), Map("USD" -> 1, "JPY" -> 100))),
    Coupon(Some(List(1, 2))),
    ExchangeRateConfiguration(false),
    Nil,
    List(3, 4),
    true,
  )

  lazy val japanicanWhiteLabelSetting = WhitelabelSetting(
    3,
    Map.empty,
    Set.empty,
    SupplierConfiguration(
      CommissionAndMarginOverride(isCommissionOverride = false,
                                  isCommissionAdjustment = false,
                                  isMarginAdjustment = false,
                                  isAdjustCommissionFromHotelContract = true)),
    Some(LoyaltyProgram(Some(1), Some(true), Some(100), Some(0))),
    Coupon(None),
    ExchangeRateConfiguration(true),
    Nil,
    Nil,
    true,
  )

  lazy val anaWhiteLabelSetting = WhitelabelSetting(
    Whitelabel.Ana,
    Map.empty,
    Set.empty,
    SupplierConfiguration(
      CommissionAndMarginOverride(isCommissionOverride = false,
                                  isCommissionAdjustment = false,
                                  isMarginAdjustment = false,
                                  isAdjustCommissionFromHotelContract = true)),
    Some(LoyaltyProgram(Some(1), Some(true), Some(100), Some(0))),
    Coupon(None),
    ExchangeRateConfiguration(true),
    Nil,
    Nil,
    true,
  )

  lazy val bessieWhiteLabelSetting = WhitelabelSetting(
    id = Whitelabel.CitiUs,
    defaultDMCSellability = Map.empty,
    supportedSuppliers = Set.empty,
    supplierConfiguration = SupplierConfiguration(
      CommissionAndMarginOverride(isCommissionOverride = false,
                                  isCommissionAdjustment = false,
                                  isMarginAdjustment = false,
                                  isAdjustCommissionFromHotelContract = true)),
    loyaltyProgramSetting = Some(LoyaltyProgram(Some(1), Some(true), Some(100), Some(0))),
    couponSetting = Coupon(None),
    exchangeRateSetting = ExchangeRateConfiguration(true),
    paymentChannelsSetting = Nil,
    logInInventoryTypeListSetting = Nil,
    isBreakfastAndDinnerIncludeEnable = true,
    externalVipDisplayConfigs = Seq(
      ExternalVipDisplayConfigs(Some(ExternalLoyaltyItem.TierZero.i),
                                Some(CitiTierZeroBadgeName),
                                Some(CitiTierZeroBadgeDescription),
                                None),
      ExternalVipDisplayConfigs(Some(ExternalLoyaltyItem.TierOne.i),
                                Some(CitiTierOneBadgeName),
                                Some(CitiTierOneBadgeDescription),
                                None),
    ),
    enablePublishPriceESS = true,
  )

  lazy val CitiTierZeroBadgeName = 224382
  lazy val CitiTierZeroBadgeDescription = 224383
  lazy val CitiTierOneBadgeName = 224381
  lazy val CitiTierOneBadgeDescription = 224383

  lazy val aValidBenefit = BenefitBuilder(benefit)

  lazy val aValidRateSilo: RateSilo = RateSilo(
    hotelId = 10637,
    checkIn = new DateTime(),
    lengthOfStay = 1,
    prices = Map.empty,
  )

  lazy val aValidRoomIdentifiers = RoomIdentifierBuilder(exampleRoomIdentifier)

  lazy val aValidPriceAdjustmentReq = PriceAdjustment("roomid", 200, 1, 1, "PB", 1, Some(1), "THB")

  lazy val aValidLoyaltyPaymentRequest = LoyaltyPaymentRequest(applyAmountInRequestCurrency = 100.5,
                                                               availableAmountInUSD = 999.99,
                                                               `type` = LoyaltyTypes.GiftCard,
                                                               pointsRedeemed = Some(50))
  lazy val aValidCashbackPaymentRequest = LoyaltyPaymentRequest(applyAmountInRequestCurrency = 100.5,
                                                                availableAmountInUSD = 100.5,
                                                                `type` = LoyaltyTypes.Cashback,
                                                                pointsRedeemed = None)

  // RoomEnrichment
  lazy val aValidRoomEnrichment = RoomEnrichment()
  lazy val aValidEnrichedRatePlan = EnrichedRatePlan("englishName", "name", "")

  // HotelContent
  lazy val aValidRateCategoryContent =
    RateCategoryContent(description = Some("englishNameDesc"), cmsId = Some(234221), externalUrl = Some("externalUrl"))
  lazy val aValidHotelContent =
    HotelContent(propertyId = 12345L, rateCategoryIdContentMap = Map(1 -> aValidRateCategoryContent))

  lazy val aValidSFDaily = SFDailyBuilder(exampleDaily)
  lazy val aValidPromotionContext = PromotionContextBuilder(examplePromotionContext)
  lazy val aValidDFClientInfo = ClientInfoBuilder(exampleClientInfo)
  lazy val aValidCartBaseRequest = exampleCartBaseRequest
  lazy val aValidWhitelabelSetting = WhitelabelSettingBuilder(agodaWhiteLabelSetting)
  lazy val aValidRegulationFeatureEnabledSetting =
    RegulationFeatureEnabledSettingBuilder(RegulationFeatureEnabledSetting())
  lazy val aValidDFBookingInfo = DFBookingInfoBuilder(exampleDFBookingInfo)
  lazy val aValidCashback = CashbackBuilder(exampleCashback)
  lazy val aValidGiftCard = GiftCardBuilder(exampleGiftCard)
  lazy val aValidRewardOptions = RewardOptionsBuilder(exampleRewardOptions)
  lazy val aValidDFPricingInfo = DFPricingInfoBuilder(exampleDFPricingInfo)
  lazy val aValidHotelFeatures = HotelFeaturesBuilder(exampleHotelFeatures)
  lazy val aValidHotelAggregatedOptions = HotelAggregatedOptionsBuilder(exampleHotelAggregatedOptions)
  lazy val aValidHotelAggregatedPayment = HotelAggregatedPaymentBuilder(exampleHotelAggregatedPayment)

  /* =================================
   *        PRIVATE CONSTRUCTORS
   * ================================= */

  private val checkIn = parseDateTime("2015-10-10")
  private val los = 3
  private val hotelId = 12345L
  private val exampleSupplierHotel = SupplierHotel(hotelId, -1, "dmc hotel", Nil)
  private val examplePrice = Price.createWithDefault(
    roomNumber = None,
    date = parseDateTime("2024-04-25"),
    quantity = 1,
    chargeType = CommonChargeType.Room,
    subChargeType = SubChargeType.None,
    applyType = CommonApplyType.PB,
    chargeOption = CommonChargeOption.Mandatory,
    refId = 1,
    netExclusive = 1000,
    tax = 50,
    fee = 30,
    margin = 150,
    processingFee = 20,
    discount = 10,
    downliftAmount = 100,
    percent = 10,
    taxBreakDown = List.empty,
    downliftExAmount = Some(3.0),
    referenceCommissionPercent = 0d,
    optimizationGain = None,
    dailyTaxes = DailyTaxes(List.empty[TaxWithValue], isCleanedUpHospitalityTax = true),
  )
  private val exampleRefPrice = examplePrice.copy(
    margin = examplePrice.refMargin,
    processingFee = examplePrice.refProcessingFee,
    processingFeeBreakdown = None,
    downliftPercent = 0d,
    downliftAmount = 0d,
    downliftExAmount = None,
    discountMessages = Map.empty,
    cashbackPercent = None,
    cashbackAmount = None,
  )

  private val exampleTaxBreakdown = CommonTaxBreakdown(1,
                                                       isFee = false,
                                                       quantity = 1,
                                                       amount = 10,
                                                       applyTo = "PRPN",
                                                       percentage = 0d,
                                                       noAdjust = true,
                                                       include = false,
                                                       orderNumber = 0)
  private val exampleTax = Tax(id = 1,
                               applyTo = "PRPN",
                               isAmount = false,
                               isFee = false,
                               isTaxable = false,
                               value = 5.0,
                               option = CommonChargeOption.Mandatory,
                               protoTypeId = 1)
  private val exampleHPTax = exampleTax.copy(value = 5.0, option = CommonChargeOption.HospitalityPrice)
  private val exampleMATax = exampleTax.copy(value = 5.0, applyOver = Some(ApplyTaxOver.MarginCommission))
  private val exampleTaxWithValue = TaxWithValue(exampleTax, 5.0)
  private val exampleHPTaxWithValue = TaxWithValue(exampleHPTax, 5.0)
  private val exampleMATaxWithValue = TaxWithValue(exampleMATax, 5.0)
  private val exampleDailyTaxes = DailyTaxes(List(exampleTaxWithValue), isCleanedUpHospitalityTax = true)
  private val exampleHPDailyTaxes =
    DailyTaxes(List(exampleHPTaxWithValue, exampleMATaxWithValue), isCleanedUpHospitalityTax = true)
  private val exampleDiscountInfo = DiscountInfo()

  private val exampleDmcData = DmcData()
  val exampleRoomIdentifier = RoomIdentifiers(
    CoreFields(
      roomId = 1,
      paymentModel = 1,
      rateCategoryId = 1,
      supplierId = 1,
      channelId = 1,
      dmcUid = "None|54635107|1|7622801_93766198_2_1_0|2|1|List(2, 4, 1, 3)|64D100P_100P|Some()",
      isRepurposed = false,
      srcChannelId = 1,
      refChannelId = 1,
      occupancy = 1,
      extrabed = 1,
      cxlCode = "",
      isPriusOutput = false,
      promotionId = 1,
      breakfast = false,
    ),
    ExtendFields(
      None,
      false,
      false,
      Nil,
      Nil,
      None,
    ),
  )
  val aValidStayOccupancyFields = StayOccupancyFields(2, List.empty, 1, 0)
  val aValidRoomSelectionField = RoomSelectionFields(
    children = 0,
    maxExtraBed = 0,
    isRespectMaxOcc = false,
    needOccupancySearch = false,
    masterRoomOccupancy = 1,
    isFit = false,
  )
  val aValidPricingFields = PricingFields(Map("some-key" -> "some-value".getBytes))

  private val exampleArrangement = Arrangement(`type` = ArrangementEntries.Bundle, refId = None)
  private val exampleCartMetadata = CartMetadata(productEntry = Some(ProductEntries.CartProduct),
                                                 itemMetadata = List(CartItemMetadata(ItemEntries.Property, 2)))
  private val exampleCartBaseRequest = CartBaseRequest(token = None,
                                                       arrangement = Some(exampleArrangement),
                                                       sourceId = None,
                                                       srcId = None,
                                                       meta = Some(exampleCartMetadata))
  private val examplePromotion = Promotion()
  private val exampleRateCategory =
    RateCategory(111, None, None, false, "", 0.0, List(), "moola", None, false, None, None, Some(1))

  private val examplePriceEntry =
    PriceEntry(parseDateTime("2015-10-10"), CommonChargeType.Unknown, CommonChargeOption.Unknown, "PB", 0, 0.0)
  private val exampleDailyPrice =
    DailyPrice(date = parseDateTime("2015-10-10"), taxes = Map(), isPromotionBlackOut = false, prices = List())
  private val exampleExchangeRate = ExchangeRate()
  private val exampleFireDrillContract = FireDrillContract(1, FireDrillContractType.AGP, advancePay = true)

  private val currentDate = DateTime.now()
  private val examplePromotionCampaign = PromotionCampaign(
    campaignId = 1,
    promotionId = 0,
    promotionCampaignTypeId = 1,
    displayCmsId = 12345,
    discountType = 1,
    name = "",
    value = 10,
    currencyCode = "THB",
    blockListEnable = false,
    siteId = 0,
    paymentId = 1,
    emailDomain = "",
    hashedBinNo = "",
    binNo = "",
    numberOfDigit = 0,
    promotionCode = "",
    minBookingValue = 0,
    bookingDateFrom = Option(currentDate.minusDays(10)),
    bookingDateUntil = Option(currentDate.plusDays(10)),
    dateOfArrivalFrom = Option(currentDate.minusDays(10)),
    dateOfArrivalUntil = Option(currentDate.plusDays(10)),
    validDateType = 1,
    fromDate = Option(currentDate.minusDays(10)),
    toDate = Option(currentDate.plusDays(10)),
    countryId = 106,
    cityId = 9395,
    languageId = 1,
    countriesIncludedFlag = false,
    countriesIncluded = "106",
    countriesExcluded = "",
    singleUseFlag = false,
    noOfUsing = 1,
    noOfUsed = 1,
    minimumBookingCurrencyCode = "USD",
    useHotelCurrencyForMinBookAmt = false,
    childTypes = Seq.empty,
    applyType = PromoApplyType.TotalBooking,
    verifyPerNight = false,
    isAutoApply = false,
    discountCapType = 1,
    applyToSellInclusive = false,
    promotionCodeType = PromotionCodeType.Unknown,
    isCashback = false,
  )

  private val examplePromocodeInfo = PromocodeInfo(
    100,
    "",
    1,
    true,
    10,
    None,
    validDateType = 1,
    cid = 1,
    minimumBookingCurrencyCode = "USD",
    minBookingValue = 0,
    displayCmsId = 1,
    promotionCode = "ABC",
    applyType = PromoApplyType.TotalBooking,
    verifyPerNight = false,
    isAutoApply = false,
    isAutoApplyBookingForm = false,
    discountCapType = 0,
    applyToSellInclusive = false,
    useHotelCurrencyForMinBookAmt = false,
    blockListEnable = true,
    hotelFundedInfo = None,
    cofundingAmount = None,
  )

  private val examplePartiallyRefundable = PartialRefundInfo("AAA", "1D1N_100P", "365D50P_1D1N_1OOP", true)

  private val aSFPrice = SFPrice(0, 0)
  private val aSFCharge = SFCharge(`type` = ChargeTypes.Room,
                                   pay = SFPayTypes.Agoda,
                                   basis = ApplyTypes.PRPB,
                                   quantity = 1,
                                   percentage = 0d,
                                   price = SFPrice(0, 0),
                                   daily = Seq())

  private val examplePromotionDiscounts = PromotionDiscounts(0d, 0d, 0d, 0d, 0d)
  private val exampleCrossOutRate = CrossOutRates(11.11, 22.22, 33.33, CorTypeFlags.HotelLoadCOR)
  private val exampleCor = COR(corPrices = List(examplePrice),
                               crossOutRates = Some(exampleCrossOutRate),
                               secondaryCrossOutRates = Some(exampleCrossOutRate),
                               displayedRackRate = 11.12345)
  private val exampleAccountEntity = AccountingEntity(1, 2, 3)

  private val aCalculationFlowStats = CalculationFlowStats("", "", 0)

  private val exampleOccupancyRequest = OccupancyRequest(1, 1, 1, Nil, None, Some(List.empty), Some(List.empty))
  private val exampleFilterRequest = FilterRequest(List.empty, List.empty, false)
  private val exampleDetailRequest = DetailRequest(false, false, false)
  private val exampleFeatureRequest = SfFeatureRequest(false, false, false, 1)
  private val examplePaymentRequest = PaymentRequest(customerTaxCountryCode = Some("NL"))
  private val examplePricingRequest = PricingRequest(
    occupancy = exampleOccupancyRequest,
    filters = exampleFilterRequest,
    details = exampleDetailRequest,
    features = exampleFeatureRequest,
    checkIn = DateTime.now(),
    checkout = DateTime.now(),
    currency = "THB",
    bookingDate = DateTime.now(),
    roomRequest = None,
    partner = None,
    payment = Some(examplePaymentRequest),
  )
  private val examplePriceGuaranteeRequest = PriceGuaranteeRequest(Seq(hotelId), 1, examplePricingRequest, List.empty)

  private val exampleConvertedPrice = ConvertedPrice(
    tax = 10d,
    fee = 10d,
    margin = 10d,
    processingFee = 1d,
    discount = 0d,
    downLift = 0d,
    netExclusive = 70d,
    netInclusive = 80d,
    sellExclusive = 100d,
    sellInclusive = 120d,
    refSellInclusive = 120d,
    refSellExclusive = 100d,
    downLiftEx = 0d,
    optimizationGain = None,
  )

  private val aValidHotelData = HotelData.defaultInstance

  private val exampleTaxMetaData = TaxMetaData(1,
                                               Some(WhomToPayType.Government),
                                               Some(ValueCalculationMethodType.Amount),
                                               Some(ValueMethodType.Variable),
                                               Some(19726),
                                               Some(GeoType.City))

  private val backupRoomCriteria = BackupRoomCriteria(roomTypeId = 0,
                                                      rateCategoryId = 0,
                                                      paymentModel = 0,
                                                      promotionId = None,
                                                      isRoomTypeNotGuarantee = false)

  private val reBookingRequestV2 = ReBookingRequest(
    roomTypeId = 1,
    masterRoomTypeId = None,
    customerPaidPrice = 1000.0,
    originalNetIn = None,
    cashbackAmount = None,
    promoAmount = None,
    originalSellIn = None,
    originalUsdToRequestExchangeRate = None,
    actionType = ReBookingActionType.MatchUSD,
  )

  private val reBookingRequestV3 = ReBookingRequest(
    roomTypeId = 1,
    masterRoomTypeId = None,
    customerPaidPrice = 1000.0,
    originalNetIn = None,
    cashbackAmount = None,
    promoAmount = None,
    originalSellIn = Some(1000.0),
    originalUsdToRequestExchangeRate = Some(1.0),
    actionType = ReBookingActionType.MatchUSD,
  )

  private val packagingToken = PackagingRequestToken("clienttoken", Some("intertoken"))

  private val packagingRequest = PackagingRequest(Some(packagingToken))

  private val cartRequestToken = "cartRequestToken"

  private val cartRequest = CartBaseRequest(Some(cartRequestToken), None, None, None, None)

  private val defaultUUID = "00000000-0000-0000-0000-000000000000"
  private val paymentInfo =
    DFPaymentInfo(noCreditCard = true, prepaymentRequired = true, None, CancellationGroup.Unknown, Nil)
  private val benefit = Benefit(1)

  private val exampleDaily = SFDaily(
    date = DateTime.now(),
    percentage = 99,
    basis = ApplyTypes.PRPN,
    quantity = 1,
    price = SFPrice(50, 50),
    subTypeId = SubChargeType.None,
  )

  private val examplePromotionContext = PromotionContext(
    bookingDate = DateTime.now(),
    checkIn = DateTime.now().plusDays(1),
    countryId = 106,
    cityId = 9395,
    languageId = 1,
    localCurrency = "THB",
    isHotelBlock = false,
    platformId = 1,
    landingCid = 1,
    paymentId = Some(1),
    emailDomain = None,
    discountRequest = None,
    featureFlags = List(FeatureFlag.ClientDiscount),
    isLandingCcCid = false,
    needPromotionPricePeek = false,
  )

  private val exampleClientInfo = ClientInfo()
  private val exampleRoomExtraInfo = RoomExtraInfo(None, None, None)
  private val exampleRewardOptions = RewardOptions(Map.empty, None)
  private val exampleCashback = Cashback()
  private val exampleGiftCard = GiftCard()
  private val exampleByPassCurrencyExchanger = new CurrencyExchanger {
    def toUSD(price: Double) = price
    def toReq(price: Double) = price
    def exchange = None
  }
  private val exampleDFPricingInfo = DFPricingInfo(
    marginPercentage = 0,
  )
  private val exampleDFBookingInfo = DFBookingInfo(
    ratePlan = 1,
    npclnChannel = None,
    roomExtraInfo = exampleRoomExtraInfo,
    pseudoCoupon = None,
    lengthOfStay = 1,
    checkIn = new DateTime(),
    isDayUse = true,
    discountInfo = exampleDiscountInfo,
  )
  private val exampleHotelFeatures = HotelFeatures()
  private val exampleTaxReceipt = TaxReceipt(false, false, false)
  private val exampleHotelAggregatedPayment = HotelAggregatedPayment(taxReceipt = exampleTaxReceipt, false, false)
  private val exampleHotelAggregatedOptions =
    HotelAggregatedOptions(payment = exampleHotelAggregatedPayment, childPolicy = Some(ChildPolicy(1, 2)))

  /* =================================
   *        PRIVATE BUILDERS
   * ================================= */

  case class PackagingRequestBuilder(build: PackagingRequest) {
    def withPackagingRequestToken(token: Option[PackagingRequestToken]): B = build.copy(token = token)
    type B = PackagingRequestBuilder
  }

  case class CartRequestBuilder(build: CartBaseRequest) {
    def withToken(token: Option[String]): B = build.copy(token = token)
    def withMeta(cartMetadata: CartMetadata = exampleCartMetadata): B = build.copy(meta = Some(cartMetadata))
    def withArrangement(arrangement: Option[Arrangement]): B = build.copy(arrangement = arrangement)
    type B = CartRequestBuilder
  }

  case class PackagingRequestTokenBuilder(build: PackagingRequestToken) {
    def withClientToken(token: String) = build.copy(clientToken = token)
    def withInterSystemToken(token: Option[String]) = build.copy(interSystemToken = token)
    type B = PackagingRequestTokenBuilder
  }

  case class DmcDataBuilder(build: DmcData) {
    def withDmcUID(dmcUID: String): B = build.copy(dmcUID = dmcUID)
    def withDmcTaxList(dmcTaxList: Map[Int, DmcTax]): B = build.copy(dmcTaxList = dmcTaxList)
    type B = DmcDataBuilder
  }

  case class RoomIdentifierBuilder(build: RoomIdentifiers) {
    def withCxlCode(cxlCode: String) = build.copy(coreFields = build.coreFields.copy(cxlCode = cxlCode))
    def withRoomId(roomId: Long) = build.copy(coreFields = build.coreFields.copy(roomId = roomId))
    def withChannelId(channelId: Int) = build.copy(coreFields = build.coreFields.copy(channelId = channelId))
    def withDMCUid(dmcUID: String) = build.copy(coreFields = build.coreFields.copy(dmcUid = dmcUID))
    def withOccupancy(occupancy: Int) = build.copy(coreFields = build.coreFields.copy(occupancy = occupancy))
    def withExtrabed(extrabed: Int) = build.copy(coreFields = build.coreFields.copy(extrabed = extrabed))
    def withIsFit(isFit: Boolean) =
      build.copy(roomSelectionFields = build.roomSelectionFields.map(_.copy(isFit = isFit)))
    def withRoomSelectionChildren(children: Int) = build.copy(roomSelectionFields =
      Some(build.roomSelectionFields.getOrElse(aValidRoomSelectionField).copy(children = children)))
    def withRoomSelectionMaxExtraBed(maxExtraBed: Int) = build.copy(roomSelectionFields =
      Some(build.roomSelectionFields.getOrElse(aValidRoomSelectionField).copy(maxExtraBed = maxExtraBed)))
    def withRoomSelection(roomSelectionFields: Option[RoomSelectionFields]) =
      build.copy(roomSelectionFields = roomSelectionFields)
    def withPaidChildAges(paidChildAges: List[Int]) =
      build.copy(extendFields = build.extendFields.copy(paidChildAges = paidChildAges))
    def withCompositeChannelIds(compositeChannelIds: List[Int]) =
      build.copy(extendFields = build.extendFields.copy(compositeChannelIds = compositeChannelIds))
    def withStayOccupancyFields(stayOccupancyFields: Option[StayOccupancyFields]) =
      build.copy(stayOccupancyFields = stayOccupancyFields)
    def withPricingFields(pricingFields: Option[PricingFields]) = build.copy(pricingFields = pricingFields)
    type B = RoomIdentifierBuilder
  }

  case class PaymentInfoBuilder(build: DFPaymentInfo) {
    def withPrepaymentRequired(prepayment: Boolean) = build.copy(prepaymentRequired = prepayment)
    def withNoCreditCard(noCC: Boolean) = build.copy(noCreditCard = noCC)
    def withCancellationGroup(cg: CancellationGroup) = build.copy(cancellationGroup = cg)
    def withPaymentChannels(channels: List[PaymentChannel]) = build.copy(paymentChannels = channels)
    type B = DFPaymentInfo
  }

  case class ReBookingRequestBuilder(build: ReBookingRequest) {
    def withRoomTypeId(id: RoomTypeId): B = build.copy(roomTypeId = id)
    def withMasterRoomTypeId(masterRoomTypeId: Option[RoomTypeId]): B = build.copy(masterRoomTypeId = masterRoomTypeId)
    def withCustomerPaidPrice(price: Double): B = build.copy(customerPaidPrice = price)
    def withOriginalNetIn(price: Option[Double]): B = build.copy(originalNetIn = price)
    def withOriginalSellIn(price: Option[Double]): B = build.copy(originalSellIn = price)
    def withPromoAmount(promoAmount: Option[Double]): B = build.copy(promoAmount = promoAmount)
    def withCashback(cashback: Option[Double]): B = build.copy(cashbackAmount = cashback)
    def withOriginalUsdToRequestExchangeRate(exchangeRate: Option[Double]): B =
      build.copy(originalUsdToRequestExchangeRate = exchangeRate)
    def withActionType(actionType: ReBookingActionType): B = build.copy(actionType = actionType)
    type B = ReBookingRequestBuilder
  }

  case class PromotionDiscountsBuilder(build: PromotionDiscounts) {
    def withPercent(percent: Double): B = build.copy(percent = percent)
    def withPerBook(perBook: Double): B = build.copy(perBook = perBook)
    def withPerRoom(perRoom: Double): B = build.copy(perRoom = perRoom)
    def withPerRoomPerNight(perRoomPerNight: Double): B = build.copy(perRoomPerNight = perRoomPerNight)
    def withPerNight(perNight: Double): B = build.copy(perNight = perNight)
    def withCofundingAgodaPartPackaging(cofundingAgodaPartPackaging: Option[Double]): B =
      build.copy(cofundingAgodaPartPackaging = cofundingAgodaPartPackaging)
    type B = PromotionDiscountsBuilder
  }

  case class CrossOutRateBuilder(build: CrossOutRates) {
    def withSellEx(d: Double): B = build.copy(sellEx = d)
    def withSellAllIn(d: Double): B = build.copy(sellAllIn = d)
    def withCorTypeFlag(corTypeFlag: CorTypeFlag): B = build.copy(corTypeFlag = corTypeFlag)
    type B = CrossOutRateBuilder
  }

  case class PartialRefundInfoBuilder(build: PartialRefundInfo) {
    def withRelationUID(d: String): B = build.copy(relationUID = d)
    def withSrcCxl(d: String): B = build.copy(srcCxl = d)
    def withTargetCxl(d: String): B = build.copy(targetCxl = d)
    def withIsDuplicated(d: Boolean): B = build.copy(isDuplicated = d)
    type B = PartialRefundInfoBuilder
  }

  case class CORBuilder(build: COR) {
    def withPrices(prices: List[Price]): B = build.copy(corPrices = prices)
    def withCrossOutRate(cor: CrossOutRates): B = build.copy(crossOutRates = Some(cor))
    def withSecondatyCrossOutRate(cor: CrossOutRates): B = build.copy(secondaryCrossOutRates = Some(cor))
    type B = CORBuilder
  }

  case class AccountingEntityBuilder(build: AccountingEntity) {
    def withMerchantOfRecord(i: Int): B = build.copy(merchantOfRecord = i)
    def withRevenue(i: Int): B = build.copy(revenue = i)
    def withRateContract(i: Int): B = build.copy(rateContract = i)
    type B = AccountingEntityBuilder
  }

  case class TaxMetaDataBuilder(build: TaxMetaData) {
    def withTaxProtoTypeId(i: TaxProtoTypeID): B = build.copy(taxProtoTypeId = i)
    def withWhomToPay(w: Option[WhomToPayType]): B = build.copy(whomToPay = w)
    def withValueCalculationMethod(v: Option[ValueCalculationMethodType]): B = build.copy(valueCalculationMethod = v)
    def withValueMethod(v: Option[ValueMethodType]): B = build.copy(valueMethod = v)
    def withGeoId(g: Option[Int]): B = build.copy(geoId = g)
    def withGeoType(g: Option[GeoType]): B = build.copy(geoType = g)
    type B = TaxMetaDataBuilder
  }

  case class BackupRoomCriteriaBuilder(build: BackupRoomCriteria) {
    def withRoomTypeId(i: Int): B = build.copy(roomTypeId = i)
    def withRateCategoryId(i: Int): B = build.copy(rateCategoryId = i)
    def withPaymentModel(i: Int): B = build.copy(paymentModel = i)
    def withPromotionId(i: Option[Int]): B = build.copy(promotionId = i)
    def withRoomTypeNotGuarantee(i: Boolean): B = build.copy(isRoomTypeNotGuarantee = i)
    type B = BackupRoomCriteriaBuilder
  }

  case class ConvertedPriceBuilder(build: ConvertedPrice) {
    def withNetExclusive(value: Double): B = build.copy(netExclusive = value)
    def withTax(value: Double): B = build.copy(tax = value)
    def withFee(value: Double): B = build.copy(fee = value)
    def withMargin(value: Double): B = build.copy(margin = value)
    def withDiscount(value: Double): B = build.copy(discount = value)
    def withDownLift(value: Double): B = build.copy(downLift = value)
    def withDownLiftEx(value: Double): B = build.copy(downLiftEx = value)
    def withNetInclusive(value: Double): B = build.copy(netInclusive = value)
    def withSellExclusive(value: Double): B = build.copy(sellExclusive = value)
    def withSellInclusive(value: Double): B = build.copy(sellInclusive = value)
    def withRefCommission(value: Double): B = build.copy(refCommission = value)
    def withProcessingFee(value: Double): B = build.copy(processingFee = value)
    def withIsApplyRefCommissionOnRefSellEx(value: Boolean): B = build.copy(isApplyRefCommissionOnRefSellEx = value)
    def withConvertedTaxBreakdown(value: List[CommonTaxBreakdown]): B = build.copy(convertedTaxBreakDown = value)
    def withSupplierFundedDiscountAmount(value: Option[Double]): B = build.copy(supplierFundedDiscountAmount = value)
    def withUspaDiscountAmount(value: Option[Double]): B = build.copy(uspaDiscountAmount = value)
    def withUspaProgramId(value: Option[Int]): B = build.copy(uspaProgramId = value)
    def withProcessingFeeBreakdown(value: Option[CommonProcessingFeeBreakdown]): B =
      build.copy(processingFeeBreakdown = value)
    type B = ConvertedPriceBuilder
  }

  case class DiscountInfoBuilder(build: DiscountInfo) {
    def withPromotion(p: Promotion): B = build.copy(promotion = Some(p))
    def withPromotionsBreakdown(pb: Map[LocalDate, List[Promotion]]): B = build.copy(promotionsBreakdown = pb)
    def withDiscountMessages(dm: Seq[DiscountMessage]): B = build.copy(discountMessages = dm)
    def withTotalDiscount(i: Int): B = build.copy(totalDiscount = i)
    def withChannelDiscountSummary(c: ChannelDiscountSummary): B = build.copy(channelDiscountSummary = Some(c))

    type B = DiscountInfoBuilder
  }
  case class SFPriceBuilder(build: SFPrice) {
    def withExc(exc: Double): B = build.copy(exc = exc)
    def withInc(inc: Double): B = build.copy(inc = inc)
    type B = SFPriceBuilder
  }

  case class SFChargeBuilder(build: SFCharge) {
    def withChargeType(chargeType: ChargeType): B = build.copy(`type` = chargeType)
    def withBasisApplyType(applyType: ApplyType): B = build.copy(basis = applyType)
    def withQuantity(quantity: Int): B = build.copy(quantity = quantity)
    def withChargeOption(chargeOption: ChargeOption): B = build.copy(option = Some(chargeOption))
    def withPrice(price: SFPrice): B = build.copy(price = price)
    def withNetPrice(price: SFPrice): B = build.copy(netPrice = price)
    def withIsInclude(value: Boolean): B = build.copy(isInclude = Some(value))
    def withPercentage(percentage: Double): B = build.copy(percentage = percentage)
    def withId(id: Int): B = build.copy(id = id)
    def withDaily(daily: Seq[SFDaily]): B = build.copy(daily = daily)
    def withDisplay(display: Display): B = build.copy(display = Some(display))
    def withWhomToPayType(whomToPay: Option[WhomToPayType]): B = build.copy(whomToPay = whomToPay)
    def withPayType(payType: PayType): B = build.copy(pay = payType)
    def withDisplayChargeGroup(group: Option[ChargeGroup]): B = build.copy(chargeGroup = group)
    def withPriceBreakdown(breakdown: Option[PartnersPriceBreakdown]): B = build.copy(priceBreakdown = breakdown)

    type B = SFChargeBuilder
  }

  case class SupplierHotelBuilder(build: SupplierHotel) {
    def withSupplierId(id: SupplierId): B = build.copy(dmcId = id)

    type B = SupplierHotel
  }

  case class PriceBuilder(build: Price) {
    def withDate(dateString: String): B = build.copy(date = DateTime.parse(dateString))
    def withDate(date: DateTime): B = build.copy(date = date)
    def withDailyTaxes(taxes: DailyTaxes): B = build.copy(dailyTaxes = taxes)
    def withNetExclusive(value: Double): B = build.copy(netExclusive = value)
    def withMargin(value: Double): B = build.copy(margin = value)
    def withProcessingFee(value: Double): B = build.copy(processingFee = value)
    def withRefMargin(value: Double): B = build.copy(refMargin = value)
    def withRefProcessingFee(value: Double): B = build.copy(refProcessingFee = value)
    def withFee(value: Double): B = build.copy(fee = value)
    def withDownliftAmount(value: Double): B = build.copy(downliftAmount = value)
    def withDownliftExAmount(value: Double): B = build.copy(downliftExAmount = Some(value))
    def withDownliftExAmount(value: Option[Double]): B = build.copy(downliftExAmount = value)
    def withDownliftPercent(value: Double): B = build.copy(percent = value)
    def withTax(value: Double): B = build.copy(tax = value)
    def withChargeType(chargeType: CommonChargeType): B = build.copy(chargeType = chargeType)
    def withSubChargeType(subChargeType: SubChargeType): B = build.copy(subChargeType = subChargeType)
    def withApplyType(applyType: CommonApplyType): B = build.copy(applyType = applyType)
    def withChargeOption(chargeOption: CommonChargeOption): B = build.copy(chargeOption = chargeOption)
    def withValue(value: Double): B = build.copy(value = value)
    def withQuantity(quantity: Int): B = build.copy(quantity = quantity)
    def withTaxBreakdown(tb: CommonTaxBreakdown): B = build.copy(taxBreakDown = List(tb))
    def withTaxBreakdown(tbs: List[CommonTaxBreakdown]): B = build.copy(taxBreakDown = tbs)
    def withRefId(refId: Int): B = build.copy(refId = refId)
    def withDiscountMessages(dMsgs: List[DiscountMessage]): B =
      build.copy(discountMessages = dMsgs.groupBy(_.discountType).mapValues(_.head))
    def withConvertPrice(price: ConvertedPrice): B = build.copy(reqRatePrice = Some(price))
    def withReferenceCommissionPercent(value: Double): B = build.copy(referenceCommissionPercent = value)
    def withDiscount(value: Double): B = build.copy(discount = value)
    def withProcessingFeeBreakdown(value: Option[CommonProcessingFeeBreakdown]): B =
      build.copy(processingFeeBreakdown = value)
    def withIsConfigProcessingFee(value: Boolean): B = build.copy(isConfigProcessingFee = value)
    def withChannelDiscountBreakdown(breakdown: List[PriceChannelDiscountBreakdown]): B =
      build.copy(channelDiscountBreakdown = breakdown)
    def withResellRefSell(value: Option[Double]): B = build.copy(resellRefSell = value)
    def withBreakdownStep(value: BreakdownStep): B = build.copy(currentBreakdownStep = value)
    def withCashbackPercent(value: Double): B = build.copy(cashbackPercent = Some(value))
    def withCashbackAmount(value: Double): B = build.copy(cashbackAmount = Some(value))
    def withRoomNumber(value: Int): B = build.copy(roomNumber = Some(value))
    def withPercent(percent: Double): B = build.copy(percent = percent)
    def withVariableTax(value: Option[Double]): B = build.copy(variableTax = value)
    def withPayAsYouGoCommission(value: Option[Double]): B = build.copy(payAsYouGoCommission = value)
    def withPrepaidCommission(value: Option[Double]): B = build.copy(prepaidCommission = value)
    def withReqRatePrice(value: Option[ConvertedPrice]): B = build.copy(reqRatePrice = value)
    def withPriceBreakdownHistory(value: BookingPriceBreakdown): B = build.copy(priceBreakdownHistory = value)
    def withIsApplyRefCommissionOnRefSellEx(value: Boolean): B = build.copy(isApplyRefCommissionOnRefSellEx = value)
    def withSupplierFundedDiscountAmount(value: Option[Double]): B = build.copy(supplierFundedDiscountAmount = value)
    def withUspaDiscountAmount(value: Option[Double]): B = build.copy(uspaDiscountAmount = value)
    def withUspaProgramId(value: Option[Int]): B = build.copy(uspaProgramId = value)
    def withAppliedServiceTaxCountry(value: Option[String]): B = build.copy(appliedServiceTaxCountry = value)
    def withSurchargeInfo(value: Option[SurchargeInfo]): B = build.copy(surchargeInfo = value)
    type B = PriceBuilder
  }

  case class TaxBreakdownBuilder(build: CommonTaxBreakdown) {
    def withTypeId(id: Int): B = build.copy(typeId = id)
    def withIsFee(isFee: Boolean): B = build.copy(isFee = isFee)
    def withQuantity(q: Int): B = build.copy(quantity = q)
    def withAmount(amount: Double): B = build.copy(amount = amount)
    def withApplyType(applyType: CommonApplyType): B = build.copy(applyTo = applyType.toString)
    def withPercentage(perc: Double): B = build.copy(percentage = perc)
    def withTaxProtoTypeId(taxProtoTypeId: Int): B = build.copy(taxProtoTypeId = taxProtoTypeId)
    def withInclude(include: Boolean): B = build.copy(include = include)
    def withNoAdjust(noAdjust: Boolean): B = build.copy(noAdjust = noAdjust)
    def withTaxInclude(include: Boolean): B = build.copy(taxIncluded = include)
    def withChargeOptions(c: CommonChargeOption): B = build.copy(option = c)
    def withWhomToPay(w: Option[WhomToPayType]): B = build.copy(whomToPay = w)
    def withConvertedAmount(convertedAmount: Double): B = build.copy(convertedAmount = Option(convertedAmount))
    def withConvertedAmount(convertedAmount: Option[Double]): B = build.copy(convertedAmount = convertedAmount)
    type B = TaxBreakdownBuilder
  }

  case class DailyTaxesBuilder(build: DailyTaxes) {
    def withTaxes(value: List[TaxWithValue]): B = build.copy(taxes = value)
    type B = DailyTaxesBuilder
  }

  case class TaxWithValueBuilder(build: TaxWithValue) {
    def withTax(value: Tax): B = build.copy(tax = value)
    def withTaxValue(value: Double): B = build.copy(taxValue = value)
    type B = TaxWithValueBuilder
  }

  case class TaxBuilder(build: Tax) {
    def withTaxId(id: Int): B = build.copy(id = id)
    def withApplyTo(value: String): B = build.copy(applyTo = value)
    def withIsFee(value: Boolean): B = build.copy(isFee = value)
    def withIsTaxable(value: Boolean): B = build.copy(isTaxable = value)
    def withValue(value: Double): B = build.copy(value = value)
    def withOption(value: CommonChargeOption): B = build.copy(option = value)
    def withPrototypeId(value: Int): B = build.copy(protoTypeId = value)
    def withTaxPrototypeInfo(value: TaxPrototypeInfo): B = build.copy(taxPrototypeInfo = Some(value))
    def withApplyOver(value: ApplyTaxOver): B = build.copy(applyOver = Some(value))
    def withWhomToPay(value: WhomToPayType): B = build.copy(whomToPay = Some(value))
    def withOrderNumber(value: Int): B = build.copy(orderNumber = Some(value))
    def withTaxLevelCalculateType(value: TaxLevelCalculationType): B = build.copy(taxLevelCalculationType = Some(value))
    def withValueMethod(value: ValueMethodType): B = build.copy(valueMethod = Some(value))
    def withValueCalculationMethodType(value: ValueCalculationMethodType): B =
      build.copy(valueCalculationMethodType = Some(value))
    def withGeoId(value: Int): B = build.copy(geoId = Some(value))
    def withGeoType(value: GeoType): B = build.copy(geoType = Some(value))
    type B = TaxBuilder
  }

  case class FeatureRequestBuilder(build: FeatureRequest) {
    def withLandingHotelIds(hotelIds: Set[HotelId]) = build.copy(ppLandingHotelIds = hotelIds.toList.map(_.toInt))
    def withSearchedHotelIds(hotelIds: Set[HotelId]) = build.copy(searchedHotelIds = hotelIds.toList.map(_.toInt))
    def withMSEHotelIds(hotelIds: Set[HotelId]) = build.copy(mseHotelIds = hotelIds.toList.map(_.toInt))
    def withUnsupportedPaymentModels(unsupportedPaymentModels: Set[PaymentModel]) =
      build.copy(unsupportedPaymentModels = unsupportedPaymentModels)
    def withGetAlternativeRoom(getAlternativeRoom: Boolean) = build.copy(getAlternativeRoom = Some(getAlternativeRoom))
    def withBenefitValuationForASO(enableBenefitValuationForASO: Option[Boolean]) =
      build.copy(enableBenefitValuationForASO = enableBenefitValuationForASO)
    def withPushDayUseRates(enablePushDayUseRates: Option[Boolean]) =
      build.copy(enablePushDayUseRates = enablePushDayUseRates)
    def withDayUseCor(enableDayUseCor: Option[Boolean]) = build.copy(enableDayUseCor = enableDayUseCor)
    def withEnableRichContentOffer(enableRichContentOffer: Option[Boolean]) =
      build.copy(enableRichContentOffer = enableRichContentOffer)

    def withEnableHourlySlotsForDayuseInOvernight(enableHourlySlotsForDayuseInOvernight: Option[Boolean]) =
      build.copy(enableHourlySlotsForDayuseInOvernight = enableHourlySlotsForDayuseInOvernight)

    type B = FeatureRequestBuilder
  }

  case class PriceEntryBuilder(build: PriceEntry) {
    def withChargeType(chargeType: CommonChargeType): B = build.copy(chargeType = chargeType)
    def withValue(value: Double): B = build.copy(value = value)
    def withQuantity(quantity: Int): B = build.copy(quantity = quantity)
    def withChannelDiscount(value: Double): B = build.copy(channelDiscount = value)
    def withRateLoadedPrice(rateLoadedPrice: Double): B = build.copy(rateLoadedPrice = rateLoadedPrice)
    def withPromoDiscount(value: Double): B = build.copy(promoDiscount = value)
    def withDiscountMessage(value: DiscountMessage): B = build.copy(discountMessages = build.getUpdatedDiscountMessages(
      YplDiscountMessage(
        value.discountType,
        value.value,
        value.cmsID,
        value.isOriginalAmount,
      ),
    ))
    def withOccupancy(value: Int): B = build.copy(occupancy = value)
    def withDate(value: DateTime): B = build.copy(date = value)

    type B = PriceEntryBuilder
  }

  case class DailyPriceBuilder(build: DailyPrice) {
    def withPrices(prices: List[PriceEntry]): B = build.copy(prices = prices)

    type B = DailyPriceBuilder
  }

  case class OccInfoBuilder(build: OccInfo) {
    def withAdults(adults: Int): B = build.copy(_adults = adults.toOption(adults != 0))
    def withChildren(children: Children): B = build.copy(_children = children.toOption(children.amount != 0))
    def withRooms(rooms: Int): B = build.copy(_rooms = rooms.toOption(rooms != 0))
    def withRoomAssignment(roomsAssignment: List[RoomAssignment]): B = build.copy(roomAssignment = roomsAssignment)
    type B = OccInfoBuilder
  }

  case class PromotionBuilder(build: Promotion) {
    def withApplicableUntil(date: DateTime): B = build.copy(applicableUntil = Some(date))
    def withDiscountType(dt: DiscountType = DiscountTypes.Other): B = build.copy(discountType = dt)
    def withMinRooms(minimumRoom: Int = 0): B = build.copy(minRooms = minimumRoom)
    def withValue(value: Double): B = build.copy(value = value)
    def withMinNightStay(minNightStay: Int): B = build.copy(minNightStay = minNightStay)
    def withCmsDiscountTypeId(cmsDiscountTypeId: Int): B = build.copy(cmsDiscountTypeId = cmsDiscountTypeId)
    type B = PromotionBuilder
  }

  case class RateCategoryBuilder(build: RateCategory) {
    def withId(id: Int): B = build.copy(id = id)
    def withOfferType(offerType: Option[Int]): B = build.copy(offerType = offerType)
    def withIsAmount(isAmount: Boolean): B = build.copy(isAmount = isAmount)
    def withParentID(parentID: Int): B = build.copy(parentId = if (parentID == -1) None else Some(parentID))
    def withValue(value: Double): B = build.copy(value = value)
    def withPromotionTypeId(id: Int): B = build.copy(promotionTypeId = Option(id))
    def withPromotionTypeCmsId(cmsId: Int): B = build.copy(promotionTypeCmsId = Option(cmsId))
    def withInventoryType(inventoryTypeId: Option[Int]): B = build.copy(inventoryType = inventoryTypeId)
    def withBenefits(bebnfits: List[Benefit]): B = build.copy(benefits = bebnfits)
    def withCurrencyCode(currencyCode: String): B = build.copy(currencyCode = currencyCode)

    type B = RateCategoryBuilder
  }

  case class FireDrillContractBuilder(build: FireDrillContract) {
//    def withContractType(contractType: FireDrillContractType) = build.copy(contractType= contractType)
    type B = FireDrillContractBuilder
  }

  case class PromotionCampaignBuilder(build: PromotionCampaign) {
    def withCampaignId(campaignId: Int): B = build.copy(campaignId = campaignId)
    def withDiscountType(discountType: Int): B = build.copy(discountType = discountType)
    def withPaymentId(paymentId: Int): B = build.copy(paymentId = paymentId)
    def withPromotionCode(promoCode: String): B = build.copy(promotionCode = promoCode)
    def withCountryId(countryId: Int): B = build.copy(countryId = countryId)
    def withCityId(cityId: Int): B = build.copy(cityId = cityId)
    def withLanguageId(languageId: Int): B = build.copy(languageId = languageId)
    def withSiteId(siteId: Int): B = build.copy(siteId = siteId)
    def withEmailDomain(email: String): B = build.copy(emailDomain = email)
    def withHashedBin(hashedBin: String): B = build.copy(hashedBinNo = hashedBin)
    def withBinNo(binNo: String): B = build.copy(binNo = binNo)
    def withNumberOfDigit(numberOfDigit: Int): B = build.copy(numberOfDigit = numberOfDigit)
    def withCurrencyCode(cc: String): B = build.copy(currencyCode = cc)
    def withBookingDateFrom(bookingDateFrom: Option[DateTime]): B = build.copy(bookingDateFrom = bookingDateFrom)
    def withBookingDateFrom(bookingDateFrom: String): B =
      build.copy(bookingDateFrom = Some(DateTime.parse(bookingDateFrom)))
    def withBookingDateUntil(bookingDateUntil: Option[DateTime]): B = build.copy(bookingDateUntil = bookingDateUntil)
    def withBookingDateUntil(bookingDateUntil: String): B =
      build.copy(bookingDateUntil = Some(DateTime.parse(bookingDateUntil)))
    def withBookingDate(from: String, until: String): B = build.copy(
      bookingDateFrom = Some(DateTime.parse(from)),
      bookingDateUntil = Some(DateTime.parse(until)),
    )
    def withDateOfArrivalFrom(dateOfArrivalFrom: Option[DateTime]): B = build.copy(dateOfArrivalFrom = dateOfArrivalFrom)
    def withDateOfArrivalFrom(dateOfArrivalFrom: String): B =
      build.copy(dateOfArrivalFrom = Some(DateTime.parse(dateOfArrivalFrom)))
    def withDateOfArrivalUntil(dateOfArrivalUntil: Option[DateTime]): B =
      build.copy(dateOfArrivalUntil = dateOfArrivalUntil)
    def withDateOfArrivalUntil(dateOfArrivalUntil: String): B =
      build.copy(dateOfArrivalUntil = Some(DateTime.parse(dateOfArrivalUntil)))
    def withDateOfArrival(from: String, until: String): B = build.copy(
      dateOfArrivalFrom = Some(DateTime.parse(from)),
      dateOfArrivalUntil = Some(DateTime.parse(until)),
    )
    def withPromotionId(promotionId: Int): B = build.copy(promotionId = promotionId)
    def withCampaignTypeIdFromDB(typeId: Int): B = build.copy(promotionCampaignTypeId = typeId)
    def withEnableBlock(isEnableBlock: Boolean): B = build.copy(blockListEnable = isEnableBlock)
    def withValue(value: Double): B = build.copy(value = value)
    def withCountriesIncludedFlag(countriesIncludedFlag: Boolean): B =
      build.copy(countriesIncludedFlag = countriesIncludedFlag)
    def withCountriesIncluded(countriesIncluded: String): B = build.copy(countriesIncluded = countriesIncluded)
    def withCountriesExcluded(countriesExcluded: String): B = build.copy(countriesExcluded = countriesExcluded)
    def withMinimumBookingCurrencyCode(minimumBookingCurrencyCode: String): B =
      build.copy(minimumBookingCurrencyCode = minimumBookingCurrencyCode)
    def withUseHotelCurrencyForMinBookAmt(useHotelCurrencyForMinBookAmt: Boolean): B =
      build.copy(useHotelCurrencyForMinBookAmt = useHotelCurrencyForMinBookAmt)
    def withNoOfUsed(noOfUsed: Int): B = build.copy(noOfUsed = noOfUsed)
    def withNoOfUsing(noOfUsing: Int): B = build.copy(noOfUsing = noOfUsing)
    def withSingleUseFlag(singleUseFlag: Boolean): B = build.copy(singleUseFlag = singleUseFlag)
    def withAutoApplyFlag(isAutoApply: Boolean): B = build.copy(isAutoApply = isAutoApply)
    def withPromotionCodeType(promotionCodeType: PromotionCodeType): B =
      build.copy(promotionCodeType = promotionCodeType)
    def withMinBookingValue(minBookingValue: Int): B = build.copy(minBookingValue = minBookingValue)
    def withDiscountCapAmount(discountCapAmount: Option[Double]): B = build.copy(discountCapAmount = discountCapAmount)
    def withDiscountCapType(discountCapType: Int): B = build.copy(discountCapType = discountCapType)
    def withDiscountCapCurrencyCode(discountCapCurrencyCode: Option[String]): B =
      build.copy(discountCapCurrencyCode = discountCapCurrencyCode)
    def withDisplayCmsId(cmsId: Int): B = build.copy(displayCmsId = cmsId)
    def withName(name: String): B = build.copy(name = name)

    type B = PromotionCampaignBuilder
  }

  case class PromocodeInfoBuilder(build: PromocodeInfo) {
    def withCampaignId(campaignId: Int): B = build.copy(campaignId = campaignId)
    def withConstraint(constraint: Option[DiscountConstraint]): B = build.copy(constraint = constraint)
    def withCampaignContents(campaignContents: List[CampaignContent]): B =
      build.copy(campaignContents = campaignContents)
    def withChildPromotions(childPromotionIds: Array[Int]): B = build.copy(childPromotionIds = childPromotionIds)
    def withIsPercentage(isPercentage: Boolean): B = build.copy(isPercentage = isPercentage)
    def withPromocodeType(promocodeType: Int): B = build.copy(promocodeType = promocodeType)
    def withPromocodeType(promocodeType: PromotionCodeType): B = build.copy(promocodeType = promocodeType.i)
    def withRounding(rounding: Option[DiscountRounding]): B = build.copy(rounding = rounding)
    def withCid(cid: Int): B = build.copy(cid = cid)
    def withPromotionCode(promotionCode: String): B = build.copy(promotionCode = promotionCode)
    def withApplyType(applyType: PromoApplyType): B = build.copy(applyType = applyType)
    def withIsAutoApply(isAutoApply: Boolean): B = build.copy(isAutoApply = isAutoApply)
    def withDisplayCmsId(displayCmsId: Int): B = build.copy(displayCmsId = displayCmsId)
    def withIneligibleReason(ineligibleReason: Option[IneligiblePromotionReason]): B =
      build.copy(ineligibleReason = ineligibleReason)
    def withBlockListEnable(blockListEnable: Boolean): B = build.copy(blockListEnable = blockListEnable)
    def withUseHotelCurrencyForMinBookAmt(useHotelCurrencyForMinBookAmt: Boolean): B =
      build.copy(useHotelCurrencyForMinBookAmt = useHotelCurrencyForMinBookAmt)
    def withMinimumBookingCurrencyCode(currencyCode: String): B = build.copy(minimumBookingCurrencyCode = currencyCode)
    def withCampaignName(campaignName: String): B = build.copy(campaignName = campaignName)
    def withIsStateIdRequired(isStateIdRequired: Boolean): B = build.copy(isStateIdRequired = Some(isStateIdRequired))
    def withCampaignGroupId(campaignGroupId: Option[Int]): B = build.copy(campaignGroupId = campaignGroupId)
    def withStackDiscountOption(stackDiscountOption: Option[StackDiscountOption]): B =
      build.copy(stackDiscountOption = stackDiscountOption)
    def withValidDateType(validDateType: Int): B = build.copy(validDateType = validDateType)
    def withDateValidFrom(dateValidFrom: Option[DateTime]): B = build.copy(dateValidFrom = dateValidFrom)
    def withDateValidUntil(dateValidUntil: Option[DateTime]): B = build.copy(dateValidUntil = dateValidUntil)
    def withIsAllowJtbAgency(isAllowJtbAgency: Boolean): B = build.copy(isAllowJtbAgency = isAllowJtbAgency)
    def withCofundingAmount(cofundingAmount: Option[Double]): B = build.copy(cofundingAmount = cofundingAmount)
    def withHotelFundedInfo(hotelFundedInfo: Option[HotelFundedInfo]): B = build.copy(hotelFundedInfo = hotelFundedInfo)
    def withIsCashback(isCashback: Boolean): B = build.copy(isCashback = isCashback)
    def withIsAllowPricePeek(isAllowPricePeek: Boolean): B = build.copy(isAllowPricePeek = Some(isAllowPricePeek))
    def withEnableCurrencyRounding(enableCurrencyRounding: Boolean): B =
      build.copy(enableCurrencyRounding = Some(enableCurrencyRounding))
    type B = PromocodeInfoBuilder
  }

  case class ChildrenBuilder(build: Children) {
    def withChildren(children: List[Int]): B = build.copy(ages = children.map(age => age.toOption(true)))
    def withChildrenTypes(childrenType: List[ChildType]): B = build.copy(childrenTypes = childrenType)
    type B = ChildrenBuilder
  }

  case class ExchangeRateBuilder(build: ExchangeRate) {
    def withLocal(local: String): B = build.copy(local = local)

    def withRequest(request: String): B = build.copy(request = request)

    def withToUsd(toUsd: BigDecimal): B = build.copy(toUsd = toUsd)

    def withToRequest(toRequest: BigDecimal): B = build.copy(toRequest = toRequest)

    def withNumLocalDecimal(numLocalDecimal: Int): B = build.copy(numLocalDecimal = numLocalDecimal)

    def withNumReqDecimal(numReqDecimal: Int): B = build.copy(numReqDecimal = numReqDecimal)

    def withNumUsdDecimal(numUsdDecimal: Int): B = build.copy(numUsdDecimal = numUsdDecimal)

    type B = ExchangeRateBuilder
  }

  case class CalculationFlowStatsBuilder(build: CalculationFlowStats) {
    def withFlow(flow: String): B = build.copy(flow = flow)
    def withStatus(status: String): B = build.copy(status = status)
    def withTime(time: Long): B = build.copy(time = time)

    type B = CalculationFlowStatsBuilder
  }

  case class HotelDataBuilder(build: HotelData) {
    def withHotelId(hotelId: HotelId): B = build.copy(hotelId = hotelId)
    def withSuppliers(suppliers: Seq[SupplierCCMapping]): B = build.copy(suppliers = suppliers)
    def withCityId(cityId: CityId): B = build.copy(cityId = cityId)
    def withCountryCode(countryCode: String): B = build.copy(countryCode = countryCode)
    type B = HotelDataBuilder
  }

  case class BenefitBuilder(build: Benefit) {
    def withID(id: Int): B = build.copy(id = id)
    def withValue(value: Double): B = build.copy(value = value)
    def withUnit(unit: Int): B = build.copy(unit = unit)
    def withBenefitValue(benefitValue: Option[com.agoda.papi.ypl.models.hotel.BenefitValue]): B =
      build.copy(benefitValuation = benefitValue)
    def withParameters(parameters: Seq[BenefitParameter]): B = build.copy(parameters = parameters)

    type B = BenefitBuilder
  }

  case class SFDailyBuilder(build: SFDaily) {
    def withPrice(price: SFPrice): B = build.copy(price = price)
    def withQuantity(quantity: Quantity): B = build.copy(quantity = quantity)
    def withSubChargeType(subChargeType: SubChargeType): B = build.copy(subTypeId = subChargeType)
    def withPartnersPriceBreakdown(priceBreakdown: Option[PartnersPriceBreakdown]): B =
      build.copy(priceBreakdown = priceBreakdown)
    def withDate(date: DateTime): B = build.copy(date = date)
    def withRoomNo(roomNo: Option[Int]): B = build.copy(roomNo = roomNo)

    type B = SFDailyBuilder
  }

  case class PromotionContextBuilder(build: PromotionContext) {
    def withDiscountRequest(discountRequest: Option[DiscountRequest]): B = build.copy(discountRequest = discountRequest)
    def withFeatureFlags(featureFlags: List[FeatureFlag]): B = build.copy(featureFlags = featureFlags)
    def withLandingCid(landingCid: Int): B = build.copy(landingCid = landingCid)
    def withLocalCurrency(localCurrency: String): B = build.copy(localCurrency = localCurrency)
    def withEmailDomain(emailDomain: Option[String]): B = build.copy(emailDomain = emailDomain)
    def withPaymentId(paymentId: Option[Int]): B = build.copy(paymentId = paymentId)
    def withCountryId(countryId: CountryId): B = build.copy(countryId = countryId)
    def withLanguageId(languageId: Int): B = build.copy(languageId = languageId)
    def withCheckIn(checkIn: DateTime): B = build.copy(checkIn = checkIn)
    def withBookingDate(bookingDate: DateTime): B = build.copy(bookingDate = bookingDate)
    def withNeedPromotionPricePeek(needPromotionPricePeek: Boolean): B =
      build.copy(needPromotionPricePeek = needPromotionPricePeek)
    def withIsHotelBlock(isHotelBlock: Boolean): B = build.copy(isHotelBlock = isHotelBlock)
    def withIsCurrencyValidatorinSB(isCurrencyValidatorinSB: Boolean): B =
      build.copy(isCurrencyValidatorinSB = isCurrencyValidatorinSB)
    def withIsCreditCardInBookingRequest(isCreditCardInBookingRequest: Boolean): B =
      build.copy(isCreditCardInBookingRequest = isCreditCardInBookingRequest)
    def withIsBookingRequest(isBookingRequest: Boolean): B = build.copy(isBookingRequest = isBookingRequest)
    def withIsLandingCcCid(isLandingCcCid: Boolean): B = build.copy(isLandingCcCid = isLandingCcCid)
    type B = PromotionContextBuilder
  }

  case class ClientInfoBuilder(build: ClientInfo) {
    def withOrigin(origin: Option[String]): B = build.copy(origin = origin)
    def withLanguage(language: LanguageId): B = build.copy(language = language)
    def withCid(cid: Option[Int]): B = build.copy(cid = cid)
    def withStoreFront(storeFront: Option[Int]): B = build.copy(storeFront = storeFront)
    def withIpAddress(ipAddress: Option[String]): B = build.copy(ipAddress = ipAddress)
    def withPlatformId(platform: Option[Int]): B = build.copy(platform = platform)
    def withAffiliateId(affiliateId: Option[String]): B = build.copy(affiliateId = affiliateId)

    type B = ClientInfoBuilder
  }

  case class WhitelabelSettingBuilder(build: WhitelabelSetting) {
    def withId(id: WhitelabelID): B = build.copy(id = id)
    def withBlockBnplForJapanOutbound(blockBnplForJapanOutbound: Boolean): B =
      build.copy(blockBNPLForJapanOutboundEnabled = blockBnplForJapanOutbound)
    def withIsBreakfastAndDinnerIncludeEnable(isBreakfastAndDinnerIncludeEnable: Boolean): B =
      build.copy(isBreakfastAndDinnerIncludeEnable = isBreakfastAndDinnerIncludeEnable)
    def withIsCustomerSegmentValidationEnabled(isCustomerSegmentValidationEnabled: Boolean): B =
      build.copy(isCustomerSegmentValidationEnabled = isCustomerSegmentValidationEnabled)
    def withBlockYCSPromotionsEnabled(blockYCSPromotions: Boolean): B =
      build.copy(blockYCSPromotions = blockYCSPromotions)
    def withIsPromotionPeekAllowedOnBFOnly(isPromotionPeekAllowedOnBFOnly: Boolean): B =
      build.copy(isPromotionPeekAllowedOnBFOnly = isPromotionPeekAllowedOnBFOnly)

    def withexactMatchOccupancy(exactMatchOccupancy: Boolean): B = build.copy(exactMatchOccupancy = exactMatchOccupancy)

    def withCouponSetting(coupon: Coupon): B = build.copy(couponSetting = coupon)
    type B = WhitelabelSettingBuilder
    def withFilterOutAgencyPaymentModel(filterOutAgencyPaymentModel: Boolean): B =
      build.copy(filterOutAgencyPaymentModel = filterOutAgencyPaymentModel)
    def withIsSellingDifferentSuppliersForJtbEnabled(isSellingDifferentSuppliersForJtbEnabled: Boolean): B =
      build.copy(isSellingDifferentSuppliersForJtbEnabled = isSellingDifferentSuppliersForJtbEnabled)

    def withExternalSuppliers(externalSuppliers: List[Int]): B = build.copy(externalSuppliers = externalSuppliers)

    def withPublishPriceESSEnabled(enablePublishPriceESS: Boolean): B =
      build.copy(enablePublishPriceESS = enablePublishPriceESS)

    def withM150TransparencyVersion(enableM150MaxTransparency: Boolean): B =
      build.copy(enableM150MaxTransparency = enableM150MaxTransparency)

    def withEnabledCashbackRedemption(enabledCashbackRedemption: Boolean): B =
      build.copy(enabledCashbackRedemption = enabledCashbackRedemption)

    def withM150BaselineTransparencyVersion(enableM150BaselineTransparency: Boolean): B =
      build.copy(enableM150BaselineTransparency = enableM150BaselineTransparency)

    def withPriceViewDestinationOverride(priceViewDestinationOverrideConfigs: PriceViewDestinationOverrideConfigs): B =
      build.copy(priceViewDestinationOverrideConfigs = List(priceViewDestinationOverrideConfigs))
  }

  case class RegulationFeatureEnabledSettingBuilder(build: RegulationFeatureEnabledSetting) {
    def withIsCorAllowed(isCorAllowed: Boolean): B = build.copy(isCorAllowed = isCorAllowed)
    def withIsCCorAllowed(isCCorAllowed: Boolean): B = build.copy(isCCorAllowed = isCCorAllowed)
    def withIsPricePeekEnabled(isPricePeekEnabled: Boolean): B = build.copy(isPricePeekEnabled = isPricePeekEnabled)
    def withIsAllowExclusivePrice(isAllowExclusivePrice: Boolean): B =
      build.copy(isAllowExclusivePrice = isAllowExclusivePrice)
    def withIsBreakfastUpsellEnabled(isBreakfastUpsellEnabled: Boolean): B =
      build.copy(isBreakfastUpsellEnabled = isBreakfastUpsellEnabled)
    def withIsClaimPromotionEnabled(isClaimPromotionEnabled: Boolean): B =
      build.copy(isClaimPromotionEnabled = isClaimPromotionEnabled)
    def withIsShowPriceOfBreakfast(isShowPriceOfBreakfast: Boolean): B =
      build.copy(isShowPriceOfBreakfast = isShowPriceOfBreakfast)
    def withIsRemoveYCSPromotionEnabled(isRemoveYCSPromotionEnabled: Boolean): B =
      build.copy(isRemoveYCSPromotionEnabled = isRemoveYCSPromotionEnabled)
    def withIsOverrideOriginalTotalPrice(isOverrideOriginalTotalPrice: Boolean): B =
      build.copy(isOverrideOriginalTotalPrice = isOverrideOriginalTotalPrice)
    def withIsAutoApplyAllPromosEnabled(isAutoApplyAllPromosEnabled: Boolean): B =
      build.copy(isAutoApplyAllPromosEnabled = isAutoApplyAllPromosEnabled)
    def withIsRemovePulseBadgeTooltip(isRemovePulseBadgeTooltip: Boolean): B =
      build.copy(isRemovePulseBadgeTooltip = isRemovePulseBadgeTooltip)
    def withIsAutoApplyPromoApplyFirst(isAutoApplyPromoApplyFirst: Boolean): B =
      build.copy(isAutoApplyPromoApplyFirst = isAutoApplyPromoApplyFirst)
    def withIsExternalLoyaltyBurnDisablePayLater(isExternalLoyaltyBurnDisablePayLater: Boolean): B =
      build.copy(isExternalLoyaltyBurnDisablePayLater = isExternalLoyaltyBurnDisablePayLater)
    def withIsExternalLoyaltyBurnFilterAgencyModelOut(isExternalLoyaltyBurnFilterAgencyModelOut: Boolean): B =
      build.copy(isExternalLoyaltyBurnFilterAgencyModelOut = isExternalLoyaltyBurnFilterAgencyModelOut)
    def withIsDisabledFxi(isDisabledFxi: Boolean): B = build.copy(isDisabledFxi = isDisabledFxi)
    def withIsDisabledM150(isDisabledM150: Boolean): B = build.copy(isDisabledM150 = isDisabledM150)
    def withIsDisplayVariableTax(isDisplayVariableTax: Boolean): B =
      build.copy(isDisplayVariableTax = isDisplayVariableTax)
    def withIsDsaLicenseBlockingDisabled(isDsaLicenseBlockingDisabled: Boolean): B =
      build.copy(isDsaLicenseBlockingDisabled = isDsaLicenseBlockingDisabled)
    def withIsShowExclusivePriceWithFeeEnabled(isShowExclusivePriceWithFeeEnabled: Boolean): B =
      build.copy(isShowExclusivePriceWithFeeEnabled = isShowExclusivePriceWithFeeEnabled)
    def withIsShowExclusivePriceWithFeesForDestination(isShowExclusivePriceWithFeesForDestination: Boolean): B =
      build.copy(isShowExclusivePriceWithFeesForDestination = isShowExclusivePriceWithFeesForDestination)
    type B = RegulationFeatureEnabledSettingBuilder
  }

  case class PriceGuaranteeRequestBuilder(build: PriceGuaranteeRequest) {
    def withPricing(pricing: PricingRequest): B = build.copy(pricing = pricing)
    type B = PriceGuaranteeRequestBuilder
  }

  case class CashbackBuilder(build: Cashback) {
    def withUsePostCashbackPriceForDiscountPercent(usePostCashbackPriceForDiscountPercent: Option[Boolean]): B =
      build.copy(usePostCashbackPriceForDiscountPercent = usePostCashbackPriceForDiscountPercent)
    def withPromoEligible: B = build.copy(isPromocodeEligible = Option(true))
    def withCashbackVersion(version: String): B = build.copy(cashbackVersion = Option(version))
    def withShowPostCashbackPrice(showPostCashbackPrice: Boolean): B =
      build.copy(showPostCashbackPrice = showPostCashbackPrice)
    def withPercent(percent: Double): B = build.copy(percent = percent)
    type B = CashbackBuilder
  }

  case class GiftCardBuilder(build: GiftCard) {
    def withPercent(value: Double): B = build.copy(percent = value)

    type B = GiftCardBuilder
  }

  case class RewardOptionsBuilder(build: RewardOptions) {
    def withOptions(options: Map[String, RewardSet]) = build.copy(options = options)

    def withDefaultOption(defaultOption: Option[String]) = build.copy(defaultOption = defaultOption)

    type B = RewardOptionsBuilder
  }

  case class DFBookingInfoBuilder(build: DFBookingInfo)

  case class DFPricingInfoBuilder(build: DFPricingInfo) {
    type B = DFPricingInfoBuilder

    def withContractedCommission(commission: Double): B = build.copy(contractedCommissionPercentage = Some(commission))

    def withExpiresAt(expiresAtEpoch: Long): B = build.copy(expiresAt = Some(expiresAtEpoch))

    def withPriceAdjustmentId(priceAdjustmentId: Option[Long]): B = build.copy(priceAdjustmentId = priceAdjustmentId)
  }

  case class HotelFeaturesBuilder(build: HotelFeatures) {
    def withIsContainChinaBOR(isContainChinaBOR: Option[Boolean]): B = build.copy(isContainChinaBOR = isContainChinaBOR)
    def withIsApplyNewOccupancyLogic(isApplyNewOccupancyLogic: Boolean): B =
      build.copy(isApplyNewOccupancyLogic = isApplyNewOccupancyLogic)
    type B = HotelFeaturesBuilder
  }

  case class HotelAggregatedOptionsBuilder(build: HotelAggregatedOptions) {
    def withPayment(payment: pricing.HotelAggregatedPayment): B = build.copy(payment = payment)
    def withAvailableRoomIds(availableRoomIds: Option[Set[RoomTypeId]]): B =
      build.copy(availableRoomIds = availableRoomIds)
    type B = HotelAggregatedOptions
  }

  case class HotelAggregatedPaymentBuilder(build: pricing.HotelAggregatedPayment) {
    def withTaxReceipt(taxReceipt: TaxReceipt): B = build.copy(taxReceipt = taxReceipt)
    def withCreditCardRequired(creditCardRequired: Boolean) = build.copy(creditCardRequired = creditCardRequired)
    def withPrePaymentRequired(prePaymentRequired: Boolean) = build.copy(prePaymentRequired = prePaymentRequired)
    type B = pricing.HotelAggregatedPayment
  }

  /* =================================
   *        IMPLCIT CONVERSIONS
   * ================================= */

  implicit def toSupplierHotel(builder: SupplierHotelBuilder): SupplierHotel = builder.build
  implicit def toSupplierHotelBuilder(build: SupplierHotel): SupplierHotelBuilder = SupplierHotelBuilder(build)
  implicit def toPrice(builder: PriceBuilder): Price = builder.build
  implicit def toPriceBuilder(build: Price): PriceBuilder = PriceBuilder(build)
  implicit def toTaxBreakdown(builder: TaxBreakdownBuilder): CommonTaxBreakdown = builder.build
  implicit def toTaxBreakdownBuilder(build: CommonTaxBreakdown): TaxBreakdownBuilder = TaxBreakdownBuilder(build)
  implicit def toTax(builder: Tax): Tax = builder.build
  implicit def toTaxBuilder(build: Tax): TaxBuilder = TaxBuilder(build)
  implicit def toTaxWithValue(builder: TaxWithValue): TaxWithValue = builder.build
  implicit def toTaxWithValueBuilder(build: TaxWithValue): TaxWithValueBuilder = TaxWithValueBuilder(build)
  implicit def toDailyTaxes(builder: DailyTaxes): DailyTaxes = builder.build
  implicit def toDailyTaxesBuilder(build: DailyTaxes): DailyTaxesBuilder = DailyTaxesBuilder(build)
  implicit def toPromotion(builder: PromotionBuilder): Promotion = builder.build
  implicit def toPromotionBuilder(build: Promotion): PromotionBuilder = PromotionBuilder(build)
  implicit def toRateCategory(builder: RateCategoryBuilder): RateCategory = builder.build
  implicit def toRateCategoryBuilder(build: RateCategory): RateCategoryBuilder = RateCategoryBuilder(build)

  implicit def toPriceEntry(builder: PriceEntryBuilder): PriceEntry = builder.build
  implicit def toPriceEntryBuilder(build: PriceEntry): PriceEntryBuilder = PriceEntryBuilder(build)
  implicit def toDailyPrice(builder: DailyPriceBuilder): DailyPrice = builder.build
  implicit def toDailyPriceBuilder(build: DailyPrice): DailyPriceBuilder = DailyPriceBuilder(build)
  implicit def toOccInfo(builder: OccInfoBuilder): OccInfo = builder.build
  implicit def toOccInfoBuilder(build: OccInfo): OccInfoBuilder = OccInfoBuilder(build)
  implicit def toChildren(builder: ChildrenBuilder): Children = builder.build
  implicit def toChildrenBuilder(build: Children): ChildrenBuilder = ChildrenBuilder(build)
  implicit def toPromotionCampaign(build: PromotionCampaign): PromotionCampaignBuilder = PromotionCampaignBuilder(build)
  implicit def toPromocodeInfoBuilder(build: PromocodeInfo): PromocodeInfoBuilder = PromocodeInfoBuilder(build)
  implicit def toPromocodeInfo(builder: PromocodeInfoBuilder): PromocodeInfo = builder.build
  implicit def toExchangeRate(builder: ExchangeRateBuilder): ExchangeRate = builder.build
  implicit def toExchangeRateBuilder(build: ExchangeRate): ExchangeRateBuilder = ExchangeRateBuilder(build)
  implicit def toSFCharge(builder: SFChargeBuilder): SFCharge = builder.build
  implicit def toSFChargeBuilder(build: SFCharge): SFChargeBuilder = SFChargeBuilder(build)
  implicit def toSFPrice(builder: SFPriceBuilder): SFPrice = builder.build
  implicit def toSFPriceBuilder(build: SFPrice): SFPriceBuilder = SFPriceBuilder(build)
  implicit def toPromotionDiscountsBuilder(build: PromotionDiscounts): PromotionDiscountsBuilder =
    PromotionDiscountsBuilder(build)
  implicit def toCrossOutRateBuilderr(build: CrossOutRates): CrossOutRateBuilder = CrossOutRateBuilder(build)
  implicit def toCORBuilder(build: COR): CORBuilder = CORBuilder(build)
  implicit def toDiscountInfoBuilder(build: DiscountInfo): DiscountInfoBuilder = DiscountInfoBuilder(build)
  implicit def toDiscountInfo(builder: DiscountInfoBuilder): DiscountInfo = builder.build
  implicit def toAccountingEntityBuilder(build: AccountingEntity): AccountingEntityBuilder =
    AccountingEntityBuilder(build)
  implicit def toConvertedPriceBuilder(build: ConvertedPrice): ConvertedPriceBuilder = ConvertedPriceBuilder(build)
  implicit def toConvertedPrice(builder: ConvertedPriceBuilder): ConvertedPrice = builder.build
  implicit def toCalculationFlowStats(builder: CalculationFlowStatsBuilder): CalculationFlowStats = builder.build
  implicit def toCalculationFlowStatsBuilder(build: CalculationFlowStats): CalculationFlowStatsBuilder =
    CalculationFlowStatsBuilder(build)
  implicit def toTaxMetaData(builder: TaxMetaData): TaxMetaData = builder.build
  implicit def toTaxMetaDataBuilder(build: TaxMetaData): TaxMetaDataBuilder = TaxMetaDataBuilder(build)
  implicit def toHotelData(builder: HotelDataBuilder): HotelData = builder.build
  implicit def toHotelDataBuilder(build: HotelData): HotelDataBuilder = HotelDataBuilder(build)
  implicit def toBackupRoomCriteria(builder: BackupRoomCriteriaBuilder): BackupRoomCriteria = builder.build
  implicit def toBackupRoomCriteriaBuilder(build: BackupRoomCriteria): BackupRoomCriteriaBuilder =
    BackupRoomCriteriaBuilder(build)
  implicit def toReBookingRequest(builder: ReBookingRequestBuilder): ReBookingRequest = builder.build
  implicit def toReBookingRequestBuilder(build: ReBookingRequest): ReBookingRequestBuilder =
    ReBookingRequestBuilder(build)
  implicit def toPackagingRequest(builder: PackagingRequestBuilder): PackagingRequest = builder.build
  implicit def toPackagingRequestBuilder(build: PackagingRequest): PackagingRequestBuilder =
    PackagingRequestBuilder(build)
  implicit def toPackagingRequestToken(builder: PackagingRequestTokenBuilder): PackagingRequestToken = builder.build
  implicit def toCartRequest(builder: CartRequestBuilder): CartBaseRequest = builder.build
  implicit def toCartRequestBuilder(build: CartBaseRequest): CartRequestBuilder = CartRequestBuilder(build)
  implicit def toDmcDataBuilder(build: DmcData): DmcDataBuilder = DmcDataBuilder(build)
  implicit def toDmcData(builder: DmcDataBuilder): DmcData = builder.build
  implicit def toPackagingRequestTokenBuilder(build: PackagingRequestToken): PackagingRequestTokenBuilder =
    PackagingRequestTokenBuilder(build)
  implicit def toRoomIdentifier(builder: RoomIdentifierBuilder): RoomIdentifiers = builder.build
  implicit def toRoomIdentifier(build: RoomIdentifiers): RoomIdentifierBuilder = RoomIdentifierBuilder(build)
  implicit def toPaymentInfo(builder: PaymentInfoBuilder): DFPaymentInfo = builder.build
  implicit def toPaymentInfoBuilder(build: DFPaymentInfo): PaymentInfoBuilder = PaymentInfoBuilder(build)
  implicit def toPartialRefundInfoBuilder(build: PartialRefundInfo): PartialRefundInfoBuilder =
    PartialRefundInfoBuilder(build)
  implicit def toFeatureRequest(builder: FeatureRequestBuilder): FeatureRequest = builder.build
  implicit def toFeatureRequestBuilder(build: FeatureRequest): FeatureRequestBuilder = FeatureRequestBuilder(build)

  implicit def toBenefit(builder: BenefitBuilder): Benefit = builder.build
  implicit def toBenefitBuilder(build: Benefit): BenefitBuilder = BenefitBuilder(build)
  implicit def toSFDaily(builder: SFDailyBuilder): SFDaily = builder.build
  implicit def toSFDailyBuilder(build: SFDaily): SFDailyBuilder = SFDailyBuilder(build)
  implicit def toPromotionContextBuilder(build: PromotionContext): PromotionContextBuilder =
    PromotionContextBuilder(build)
  implicit def toPromotionContext(builder: PromotionContextBuilder): PromotionContext = builder.build
  implicit def toClientInfo(builder: ClientInfoBuilder): ClientInfo = builder.build
  implicit def toClientInfoBuilder(build: ClientInfo): ClientInfoBuilder = ClientInfoBuilder(build)
  implicit def toWhitelabelSettingBuilder(build: WhitelabelSetting): WhitelabelSettingBuilder =
    WhitelabelSettingBuilder(build)
  implicit def toWhitelabelSetting(builder: WhitelabelSettingBuilder): WhitelabelSetting = builder.build
  implicit def toRegulationFeatureEnabledSettingBuilder(
    build: RegulationFeatureEnabledSetting): RegulationFeatureEnabledSettingBuilder =
    RegulationFeatureEnabledSettingBuilder(build)
  implicit def toRegulationFeatureEnabledSetting(
    builder: RegulationFeatureEnabledSettingBuilder): RegulationFeatureEnabledSetting = builder.build
  implicit def toPriceGuaranteeRequestBuilder(build: PriceGuaranteeRequest): PriceGuaranteeRequestBuilder =
    PriceGuaranteeRequestBuilder(build)
  implicit def toPriceGuaranteeRequest(builder: PriceGuaranteeRequestBuilder): PriceGuaranteeRequest = builder.build
  implicit def toDFBookingInfoBuilder(build: DFBookingInfo): DFBookingInfoBuilder = DFBookingInfoBuilder(build)
  implicit def toDFBookingInfo(builder: DFBookingInfoBuilder): DFBookingInfo = builder.build
  implicit def toCashbackBuilder(build: Cashback): CashbackBuilder = CashbackBuilder(build)
  implicit def toCashback(builder: CashbackBuilder): Cashback = builder.build
  implicit def toGiftCardBuilder(build: GiftCard): GiftCardBuilder = GiftCardBuilder(build)
  implicit def toGiftCard(builder: GiftCardBuilder): GiftCard = builder.build
  implicit def toRewardOptionsBuilder(build: RewardOptions): RewardOptionsBuilder = RewardOptionsBuilder(build)
  implicit def toRewardOptions(builder: RewardOptionsBuilder): RewardOptions = builder.build
  implicit def toDFPricingInfoBuilder(build: DFPricingInfo): DFPricingInfoBuilder = DFPricingInfoBuilder(build)
  implicit def toDFPricingInfo(builder: DFPricingInfoBuilder): DFPricingInfo = builder.build
  implicit def toHotelFeaturesBuilder(build: HotelFeatures): HotelFeaturesBuilder = HotelFeaturesBuilder(build)
  implicit def toHotelFeatures(builder: HotelFeaturesBuilder): HotelFeatures = builder.build
  implicit def toHotelAggregatedOptionsBuilder(build: HotelAggregatedOptions) = HotelAggregatedOptionsBuilder(build)
  implicit def toHotelAggregatedOptions(builder: HotelAggregatedOptionsBuilder) = builder.build
  implicit def toHotelAggregatedPaymentBuilder(build: pricing.HotelAggregatedPayment): HotelAggregatedPaymentBuilder =
    HotelAggregatedPaymentBuilder(build)
  implicit def toHotelAggregatedPayment(builder: HotelAggregatedPaymentBuilder): pricing.HotelAggregatedPayment =
    builder.build

}
