package com.agoda.platform.pricing.models.utils

import api.request.{BackupRoomCriteria, Children, ExternalLoyaltyProfile, FeatureFlag, OccInfo, PaymentChargeTypes}
import com.agoda.commons.models.rpc.request.RequestMeta
import com.agoda.commons.traffic.TrafficInfo
import com.agoda.papi.enums.campaign.{IneligiblePromotionReason, PromotionCodeType}
import com.agoda.papi.enums.hotel.StayPackageType
import com.agoda.papi.enums.request.PostBookingActionType
import com.agoda.papi.enums.room.{CancellationGroup, SubChargeType, SurchargeTypeClassification}
import com.agoda.papi.enums.simulation.{SupplyEquitySimulationMode, SupplyEquitySimulationSide}
import com.agoda.platform.pricing.models.utils.Utils._
import com.agoda.upi.models.cart.Cart
import com.agoda.upi.models.common.Arrangement
import com.agoda.upi.models.enums.{ItemEntries, ProductEntries}
import com.agoda.upi.models.request.{CartBaseRequest, CartItemMetadata, CartMetadata}
import com.agoda.papi.ypl.models.GUIDGenerator.CoreFields
import models.consts.DMC
import models.ratechannel.SimulateRateChannelData
import models.db._
import models.pricecalculation.{
  ChannelDiscountCalculationRequest,
  ChannelDiscountRequest,
  ChannelDiscountTaxRequest,
  Identifiers,
  PriceCalculationRequest,
}
import models.pricing.enums._
import models.pricing.{
  CORInfo,
  DiscountTypes,
  DmcData,
  LoyaltyItemType,
  PriceChange,
  ProcessingFeeBreakdown,
  RoomExtraInfo,
  TaxBreakDown,
}
import models.starfruit.PropertyPricingJsonProtocol.SupplierFinancialData
import models.starfruit.simulation.SimulationPropertySearchRequest
import models.starfruit.{
  ChildPolicy,
  DisplayBasis,
  OverridePricingRequest,
  PricingMessage,
  PricingMessageText,
  RocketMilesPublishedPricing,
  RocketmilesPublishPriceRequest,
  TimeInterval => SFTimeInterval,
  _,
}
import models.starfruit.postbooking._
import models.{CheapestStayPackageRatePlans, DayuseCheckInTime, DayuseInfo}
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import com.agoda.supply.calc.proto
import models.enums.{
  AffiliateModelType,
  AffiliatePaymentType,
  CancellationByType,
  CancellationOptionType,
  CancellationReasonType,
  RefundOptionType,
}
import models.starfruit.ItemBreakdownImplicits.Converters
import models.starfruit.postbooking.cancellation._
import models.starfruit.supplysimulation.SupplyEquitySimulationPropertySearchRequest

import scala.concurrent.duration.FiniteDuration
import scala.language.implicitConversions

/**
  * Created by kliu on 7/13/2017 AD.
  */

/*
Companion object, to avoid conflict name
 * */
object SFTestDataBuilders extends SFTestDataBuilders

sealed trait SFTestDataBuilders {

  /* =================================
   *        EXPOSED BUILDERS
   * ================================= */

  lazy val aValidLoyaltyOffersSummary = LoyaltyOffersSummaryBuilder(aLoyaltyOfferSummary)
  lazy val aValidDisplayPrice = DisplayPriceBuilder(aDisplayPrice)
  lazy val aValidDisplayBasis = DisplayBasisBuilder(aDisplayBasis)
  lazy val aValidDisplayPricePercentage = DisplayPricePercentageBuilder(aDisplayPricePercentage)
  lazy val aValidDisplayBasisInt = DisplayBasisIntBuilder(aDisplayBasisInt)
  lazy val aValidSummaryElement = SummaryElementBuilder(aSummaryElement)
  lazy val aValidDisplaySummary = DisplaySummaryBuilder(aDisplaySummary)
  lazy val aValidPricing = PricingBuilder(aPricing)
  lazy val aValidPackaging = PackagingBuilder(aPackaging)
  lazy val aValidPackagePricing = PackagePricingBuilder(aPackagePricing)
  lazy val aValidPackageProductDisplayPrice = PackageProductDisplayPriceBuilder(aPackageProductDisplayPrice)
  lazy val aValidPackageAdditionalPriceAndSaving =
    PackageAdditionalPriceAndSavingBuilder(aPackageAdditionalPriceAndSaving)
  lazy val aValidPackageDetailedSaving = PackageDetailedSavingBuilder(aPackageDetailedSaving)
  lazy val aValidClientInfo = ClientInfoBuilder(aClientInfo)
  lazy val aValidSessionInfo = SessionInfoBuilder(aSessionInfo)
  lazy val aValidContextRequest = ContextRequestBuilder(aContextRequest)
  lazy val aValidOccupancyRequest = OccupancyRequestBuilder(anOccupancyRequest)
  lazy val aValidFilterRequest = FilterRequestBuilder(aFilterRequest)
  lazy val aValidDiscountRequest = DiscountRequestBuilder(aDiscountRequest)
  lazy val aValidFencedRatePair = FencedRatePairBuilder(aFencedRatePair)
  lazy val aValidFencedRate = FencedRateBuilder(aFencedRate)
  lazy val aValidDetailRequest = DetailRequestBuilder(aDetailRequest)
  lazy val aValidFeatureRequest = FeatureRequestBuilder(aFeatureRequest)
  lazy val aValidPricingRequest = PricingRequestBuilder(aPricingRequest)
  lazy val aValidPropertySearchRequest = PropertySearchRequestBuilder(aPropertySearchRequestNoSSR)
  lazy val aValidSimulationProertyRequest =
    SimulationPropertySearchRequest(search = aPropertySearchRequestNoSSR, simulation = SimulateRateChannelData())
  lazy val aValidSupplyEquitySimulationPropertySearchRequest = SupplyEquitySimulationPropertySearchRequest(
    propertySearchRequest = aPropertySearchRequestNoSSR,
    supplyEquitySimulationMode = SupplyEquitySimulationMode.WithoutSimulation,
    supplyEquitySimulationParameters = None,
    supplyEquitySimulationSide = Some(SupplyEquitySimulationSide.A),
  )
  lazy val aValidPropertyHourlySearchRequest = PropertySearchRequestBuilder(aPropertyHourlySearchRequest)
  lazy val aValidPropertyPricingRequest = PricingPropertiesRequestBuilder(aPricingPropertiesRequest)
  lazy val aValidPricingRequestParametersBuilder = PricingRequestParametersBuilder(aPricingRequestParameters)
  lazy val aValidOccInfo = OccInfoBuilder(anOccInfo)
  lazy val aValidChildren = ChildrenBuilder(aChild)
  lazy val aValidRateModel = RateModelBuilder(aRateModel)
  lazy val aValidRatePlan = RatePlanBuilder(aRatePlan)
  lazy val aValidChannel = ChannelBuilder(aChannel)
  lazy val aValidCapacity = CapacityBuilder(aCapacity)
  lazy val aValidTaxReceipt = TaxReceiptBuilder(aTaxReceipt)
  lazy val aValidPayLater = PaylaterBuilder(aPayLater)
  lazy val aValidCancellation = CancellationBuilder(aCancellation)
  lazy val aValidCreditCard = CreditCardBuilder(aCreditCard)
  lazy val aValidPrePayment = PrePaymentBuilder(aPrePayment)
  lazy val aValidPayAtHotel = PayAtHotelBuilder(aPayAtHotel)
  lazy val aValidPayment = PaymentBuilder(aPayment)
  lazy val aValidRoom = RoomBuilder(aRoom)
  lazy val aValidDisplayCharge = DisplayChargeBuilder(aDisplayCharge)
  lazy val aValidCharge = ChargeBuilder(aCharge)
  lazy val aValidDaily = DailyBuilder(aDaily)
  lazy val aValidPriceSummary = SummaryElementBuilder(aPriceSummaryElement)
  lazy val aValidPointsMax = PointsMaxBuilder(aPointMax)
  lazy val aValidProperties = PropertiesBuilder(aProperties)
  lazy val aValidHotel = HotelBuilder(aHotel)
  lazy val aValidHotelFeatures = HotelFeaturesBuilder(aHotelFeatures)
  lazy val aValidHotelAggregatedPayment = HotelAggregatedPaymentBuilder(aHotelAggregatedPayment)
  lazy val aValidHotelAggregatedOptions = HotelAggregatedOptionsBuilder(aHotelAggregatedOptions)
  lazy val aValidBenefit = BenefitBuilder(aBenefit)
  lazy val aValidBookingRequest = BookingRequestBuilder(aBookingRequest)
  lazy val aValidReBookingRequest = ReBookingRequestBuilder(aReBookingRequest)
  lazy val aValidPriceFreeze = PriceFreezeBuilder(examplePriceFreeze)
  lazy val aValidHourlyBookingRequest =
    BookingRequestBuilder(aBookingRequest).withHourlySlots(SelectedHourlySlot("16:00", 4))
  lazy val aValidRoomSelectionRequest = RoomSelectionRequestBuilder(aRoomSelectionRequest)
  lazy val aValidPartner = PartnersBuilder(aPartner)
  lazy val aValidPartnersSurcharges = PartnersSurchargesBuilder(aPartnersSurcharges)
  lazy val aValidPartnerTaxBreakdown = PartnerTaxBreakdownBuilder(aPartnersTaxBreakdown)
  lazy val aValidPartnersPrice = PartnersPricesBuilder(aPartnersPrice)
  lazy val aValidPartnersDisplayPrice = PartnersDisplayPriceBuilder(aPartnersDisplayPrice)
  lazy val aValidPartnersCharge = PartnersChargeBuilder(aPartnersCharge)
  lazy val aValidPartnersPriceBreakdown = PartnersPriceBreakdownBuilder(aPartnersPriceBreakdown)
  lazy val aValidPricingMessageRequest = PricingMessageBuilder(aPricingMessage)
  lazy val aValidGiftCard = GiftCardBuilder(aGiftCard)
  lazy val aValidConsolidatedAppliedDiscount = ConsolidatedAppliedDiscountBuilder(aConsolidatedAppliedDiscount)
  lazy val aValidCashback = CashbackBuilder(aCashback)
  lazy val aValidDiscountMessage = DiscountMessageBuilder(aDiscountMessage)
  lazy val aValidRoomBundles = RoomBundlesBuilder(aRoomBundles)
  lazy val aValidBundleSegment = BundleSegmentBuilder(aBundleSegment1)
  lazy val aValidExtraInformation = ExtraInformationBuilder(aExtraInformation)
  lazy val aValidSFPromotion = SFPromotionBuilder(aValidPromotion)
  lazy val aValidPseudoCoupon = PseudoCouponBuilder(aPseudoCoupon)
  lazy val aValidCorBreakdown = CorBreakdownBuilder(aCorBreakdown)
  lazy val aValidCorBreakdownItem = CorBreakdownItemBuilder(aCorBreakdownItem)
  lazy val aValidPricingMessage = PricingMessageBuilder(aPricingMessage)
  lazy val aValidPricingMessageText = PricingMessageTextBuilder(aPricingMessageText)
  lazy val aValidPricingMessageRoomParameter = PricingMessageRoomParameterBuilder(aPricingMessageRoomParameters)
  lazy val aValidAPSPeek = APSPeekBuilder(aAPSPeek)
  lazy val aValidPromotionPricePeek = PromotionPricePeekBuilder(aPromotionPricePeek)
  lazy val aValidItemBreakdown = ItemBreakdownBuilder(aItemBreakdown)
  lazy val aValidPackagePriceAndSaving = PackagePriceAndSavingBuilder(aPackagePriceAndSaving)
  lazy val aValidAdditionalPriceDisplayBasis = AdditionalPriceDisplayBasisBuilder(aAdditionalPriceDisplayBasis)
  lazy val aValidPriceWithChannel = PriceWithChannelBuilder(aPriceWithChannel)

  lazy val aValidSuggestedRoom = SuggestedRoomBuilder(aSuggestedRoom)
  lazy val aValidMultiRoomSuggestion = MultiRoomSuggestionBuilder(aMultiRoomSuggestion)
  lazy val aValidRocketmilesPricing = RocketmilesPricing(
    RocketmilesPrice(
      RocketmilesSellPrice(11.0d, 16.0d),
      6.0d,
      5.0d,
      RocketmilesForAffiliate(5.0d, 0.0d, 1.0d),
      11.0d,
      3.0d,
      2.0d,
      Some(
        RocketmilesBcomDetails(blockId = Some("509905601_194080758_1_0_0"),
                               incrementalPrice = Some(75.00),
                               priceMode = Some(0))),
      None,
      Some(4.0d),
      Some(0.0d),
    ))
  lazy val aValidChannelDiscountSummary = ChannelDiscountSummary(
    channelId = 1,
    totalPercent = 10.0,
    totalAmount = aValidSimplePriceBasis,
    channelDiscountBreakdown = List(aValidChannelDiscountBreakdown),
  )
  lazy val aValidChannelDiscountBreakdown = RoomChannelDiscountBreakdown(
    channelId = 1,
    discountPercent = 10.0,
    discountAmount = aValidSimplePriceBasis,
    isBaseChannel = true,
    display = true,
    discountPerAppliedDate = None,
  )
  lazy val aValidSimplePriceBasis = SimplePriceBasis(11.0, 5.50)

  lazy val aValidCampaign = CampaignBuilder(aCampaign)

  lazy val aValidCartRequest = CartBaseRequestBuilder(aCartBaseRequest)

  lazy val aValidSupplierFinancialData = SupplierFinancialDataBuilder(aSupplierFinancialData)
  lazy val aValidPartnerRequest = PartnerRequestBuilder(aPartnerRequest)

  lazy val aValidPackagingRequest = PackagingRequest(
    Some(PackagingRequestToken("clientToken", None)),
  )

  lazy val aValidAdditionalRate = AdditionalRateBuilder(aAdditionalRate)

  lazy val aValidExternalLoyaltyRequest = ExternalLoyaltyRequestBuilder(aExternalLoyaltyRequest)
  lazy val aValidBookingRoom = BookingRoomBuilder(aBookingRoom)

  lazy val aValidLoyaltyDisplayPrice = LoyaltyDisplayPriceBuilder(aLoyaltyDisplayPrice)

  lazy val aValidExternalLoyaltyPricing = ExternalLoyaltyPricingBuilder(aExternalLoyaltyPricing)

  lazy val aValidCommissionRequest = GetCommissionRequest(
    hotelId = 1234,
    supplierId = 29014,
    checkIn = DateTime.parse("2022-02-02", DateTimeFormat.forPattern("yyyy-MM-dd")),
    checkOut = DateTime.parse("2022-02-03", DateTimeFormat.forPattern("yyyy-MM-dd")),
    rateCategoryId = 123,
    inventoryType = None,
    occupancyInfo = None,
    contractTypeId = None,
    externalRateCategoryId = Some("roomCode|mock|rateCode"),
    roomTypeCode = Some("roomCode"),
    rateCode = Some("rateCode"),
    whiteLabelToken = "",
  )
  lazy val aValidRoomIdentifierRequest = RoomIdentifierRequest(
    coreFields = CoreFields(
      roomId = 1234,
      paymentModel = 0,
      promotionId = 0,
      channelId = 1,
      rateCategoryId = 123,
      occupancy = 1,
      extrabed = 0,
      breakfast = false,
      cxlCode = "cxlCode",
      supplierId = 29014,
      isPriusOutput = false,
      isRepurposed = false,
      srcChannelId = 0,
      refChannelId = 0,
      dmcUid = "",
    ),
  )
  lazy val aValidPostBookingContext = PostBookingContextBuilder(aPostBookingContext)
  lazy val aValidPostBookingAffiliateInfo = PostBookingAffiliateInfoBuilder(
    PostBookingAffiliateInfo(1, AffiliateModelType.B2B, AffiliatePaymentType.CorporateCard))
  lazy val aValidPostBookingCancellation = PostBookingCancellationBuilder(
    PostBookingCancellation(
      Some(CancellationReasonType.NoShow),
      CancellationByType.Hotel,
      CancellationOptionType.Unknown,
      RefundOptionType.OriginalPaymentMethod,
      None,
      isAllotmentReject = false,
      overriddenCancellation = None,
      aDateTime.minusDays(1),
      checkInDateAtHotelTimeZone = aDateTime.minusDays(1),
    ))
  lazy val aValidFinancialBreakdown = List(
    aBookingItemBreakdown.copy(typeId = 3,
                               itemId = BookingRateTypes.NetInclusive.i,
                               localAmount = 12600,
                               usdAmount = 90,
                               reqAmount = 3150),
    aBookingItemBreakdown.copy(typeId = 3,
                               itemId = BookingRateTypes.NetInclusive.i,
                               localAmount = 12600,
                               usdAmount = 90,
                               reqAmount = 3150),
    aBookingItemBreakdown.copy(chargeDate = None,
                               typeId = 1,
                               itemId = BookingRateTypes.NetInclusive.i,
                               localAmount = 140,
                               usdAmount = 1,
                               reqAmount = 35),
    aBookingItemBreakdown.copy(chargeDate = None,
                               typeId = 1,
                               itemId = BookingRateTypes.ReferenceSalesInclusive.i,
                               localAmount = 140,
                               usdAmount = 1,
                               reqAmount = 35),
    aBookingItemBreakdown.copy(chargeDate = None,
                               typeId = 1,
                               itemId = BookingRateTypes.SellInclusive.i,
                               localAmount = 140,
                               usdAmount = 1,
                               reqAmount = 35),
    aBookingItemBreakdown.copy(typeId = 1,
                               itemId = BookingRateTypes.NetInclusive.i,
                               localAmount = 12600,
                               usdAmount = 90,
                               reqAmount = 3150),
    aBookingItemBreakdown.copy(typeId = 4,
                               itemId = BookingRateTypes.NetInclusive.i,
                               localAmount = 12600,
                               usdAmount = 90,
                               reqAmount = 3150),
    aBookingItemBreakdown.copy(typeId = 1,
                               itemId = BookingRateTypes.ReferenceSalesInclusive.i,
                               localAmount = 15400,
                               usdAmount = 110,
                               reqAmount = 3850),
    aBookingItemBreakdown.copy(typeId = 4,
                               itemId = BookingRateTypes.ReferenceSalesInclusive.i,
                               localAmount = 15400,
                               usdAmount = 110,
                               reqAmount = 3850),
    aBookingItemBreakdown.copy(typeId = 1,
                               itemId = BookingRateTypes.SellInclusive.i,
                               localAmount = 14000,
                               usdAmount = 100,
                               reqAmount = 3500),
    aBookingItemBreakdown.copy(typeId = 4,
                               itemId = BookingRateTypes.NetExclusive.i,
                               localAmount = 11900,
                               usdAmount = 85,
                               reqAmount = 2975),
    aBookingItemBreakdown.copy(typeId = 4,
                               itemId = BookingRateTypes.SellExclusive.i,
                               localAmount = 13300,
                               usdAmount = 95,
                               reqAmount = 3325),
    aBookingItemBreakdown.copy(typeId = 4,
                               itemId = BookingRateTypes.Margin.i,
                               localAmount = 1400,
                               usdAmount = 10,
                               reqAmount = 350),
    aBookingItemBreakdown.copy(typeId = 4,
                               itemId = BookingRateTypes.Tax.i,
                               localAmount = 700,
                               usdAmount = 5,
                               reqAmount = 175),
    aBookingItemBreakdown.copy(typeId = 4,
                               itemId = BookingRateTypes.VariableTax.i,
                               localAmount = 140,
                               usdAmount = 1,
                               reqAmount = 35),
    aBookingItemBreakdown.copy(typeId = 4,
                               itemId = BookingRateTypes.PriceFreezeDeposit.i,
                               localAmount = 140,
                               usdAmount = 1,
                               reqAmount = 35),
    aBookingItemBreakdown.copy(typeId = 4,
                               itemId = BookingRateTypes.PriceFreezeSavings.i,
                               localAmount = 140,
                               usdAmount = 1,
                               reqAmount = 35),
  )

  lazy val aValidPriceCalculationRequest: PriceCalculationRequestBuilder =
    PriceCalculationRequestBuilder(aPriceCalculationRequest)

  lazy val aValidRoomRate: RoomRateCategoryBuilder = RoomRateCategoryBuilder(aRoomRateCategory)

  lazy val aValidChannelRate: ChannelRateBuilder = ChannelRateBuilder(aChannelRate)

  lazy val aValidChannelDiscountCalculationRequest: ChannelDiscountCalculationRequestBuilder =
    ChannelDiscountCalculationRequestBuilder(aChannelDiscountCalculationRequest)

  lazy val aValidPaymentRequest = PaymentRequest(
    loyalty = None,
    creditCard = Some(
      PaymentCreditCard(paymentCurrency = "USD",
                        creditCardCurrency = "USD",
                        paymentChargeOption = PaymentChargeTypes.PayNow,
                        ccToken = Some("TOKEN"),
                        ccOf = Some(10201L))),
    installmentInfo = Some(InstallmentInfo(installmentPlanCode = "PLAN-1", isInstallmentPayment = Some(true))),
    customerTaxCountryCode = Some("TH"),
    paymentOption = None,
  )
  lazy val aValidPostBookingRequest: PostBookingRequestBuilder = PostBookingRequestBuilder(aPostBookingRequest)

  lazy val aValidPostBookingSearchRequest: PostBookingSearchRequestBuilder =
    PostBookingSearchRequestBuilder(aPostBookingSearchRequest)

  lazy val aValidExternalLoyaltyProfile = ExternalLoyaltyProfile(externalProgramId = Some("22"),
                                                                 loyaltyAccountNumber = Some("testAccount"),
                                                                 remainingBenefitCredit = Some(10.00),
                                                                 promocode = Some("testPromoCode"))

  lazy val aValidPostBookingPrice: PostBookingPriceBuilder = PostBookingPriceBuilder(aPostBookingPrice)

  lazy val aValidPostBookingSummary: PostBookingSummaryBuilder = PostBookingSummaryBuilder(aPostBookingSummary)

  lazy val aValidPostBookingInfo: PostBookingInfoBuilder = PostBookingInfoBuilder(aPostBookingInfo)

  lazy val aValidPostBookingCancellationRequest: PostBookingCancellationRequestBuilder =
    PostBookingCancellationRequestBuilder(aPostBookingRequest)

  /* =================================
   *        PRIVATE CONSTRUCTORS
   * ================================= */

  private val aLoyaltyOfferSummary = LoyaltyOfferSummary(
    basePrice = DisplayPrice(9912.0, 9923.0),
    offers = Seq(
      LoyaltyOffer(
        identifier = "loyaltyOfferIdentifier1",
        status = Some("some loyalty offer status1"),
        burn = Some(
          LoyaltyBurnInfo(
            points = 10099012.0,
            payableAmount = 1008812.3,
            itemPriceInPoints = Some(1001239999.0),
            pointsRange = Some(PointsRange(Some(-1005551231.0), Some(100555100.0))),
          )),
        earn = None,
        offerType = "someOfferType1",
        isSelected = true,
        externalPartnerProgramCode = Some("someExternalPartnerProgramCode1"),
        programCategory = Some("someProgramCategory1"),
      ),
      LoyaltyOffer(
        identifier = "loyaltyOfferIdentifier2",
        status = Some("some loyalty offer status2"),
        burn = None,
        earn = Some(
          LoyaltyEarnInfo(
            points = 20099012.0,
          )),
        offerType = "someOfferType2",
        isSelected = false,
        externalPartnerProgramCode = Some("someExternalPartnerProgramCode2"),
        programCategory = Some("someProgramCategory2"),
      ),
    ),
    status = Some("some status"),
  )
  private val aDiscountDisplayPrice = DisplayPrice(0.0, 0.0)
  private val aPeekDiscountDisplayBasis =
    DisplayBasis(aDiscountDisplayPrice, aDiscountDisplayPrice, aDiscountDisplayPrice)

  private val aDisplayPrice = DisplayPrice(0.0, 0.0)
  private val aPackageDisplayPrice = PackageDisplayPrice(aDisplayPrice, Some(aDisplayPrice))
  private val aDateTime = parseDateTime("2015-10-10")
  private val aDisplayBasis = DisplayBasis(aDisplayPrice, aDisplayPrice, aDisplayPrice)
  private val aPackageDisplayBasis = PackageDisplayBasis(aPackageDisplayPrice,
                                                         aPackageDisplayPrice,
                                                         Some(aPackageDisplayPrice),
                                                         Some(aPackageDisplayPrice))
  private val aDisplayPricePercentage = DisplayPricePercentage(0, 0)
  private val aDisplayBasisInt =
    DisplayBasisInt(aDisplayPricePercentage, aDisplayPricePercentage, aDisplayPricePercentage)
  private val aSummaryElement = SummaryElement(aDisplayPrice,
                                               aDisplayPrice,
                                               aDisplayPrice,
                                               aDisplayPrice,
                                               aDisplayPrice,
                                               aDisplayPrice,
                                               Some(aDisplayPrice))
  private val aDisplaySummary = DisplaySummary(aSummaryElement, aSummaryElement, aSummaryElement, aSummaryElement)
  private val aPricing = Pricing(aDisplayBasis, aDisplayBasis, Seq(), Seq(), CorTypeFlags.NoCOR, aDisplaySummary)
  private val aPackagingResponseToken = PackagingResponseToken("CLEINT_TOKEN", "INTER_SYSTEM_TOKEN")
  private val aPackaging = Packaging(token = aPackagingResponseToken, Map("USD" -> aPackagePricing))
  private val aPackagePricing =
    PackagePricing(aPackageDisplayBasis, aPackageDisplayBasis, aPackageDisplayBasis, Seq(), totalDiscount = 50)
  private val aPackageProductDisplayPrice = PackageProductDisplayPrice("roomUUID",
                                                                       PackageProductTypes.Property,
                                                                       None,
                                                                       None,
                                                                       None,
                                                                       None,
                                                                       None,
                                                                       None,
                                                                       None,
                                                                       None,
                                                                       None,
                                                                       None,
                                                                       Seq())
  private val aPackageSaving = PackageSaving(aPackageDisplayBasis, aDisplayPricePercentage)
  private val aPackageAdditionalPriceAndSaving =
    PackageAdditionalPriceAndSaving(Some(1), aPackageDisplayBasis, Some(aPackageSaving))
  private val aPackageDetailedSaving = PackageDetailedSaving(Some(aPackageSaving), Some(aPackageSaving))
  private val aCartMetadata = CartMetadata(productEntry = Some(ProductEntries.CartProduct),
                                           itemMetadata = List(CartItemMetadata(ItemEntries.Property, 2)))
  private val aClientInfo = ClientInfo(cid = 0,
                                       userId = "",
                                       languageId = 0,
                                       languageUse = 0,
                                       platform = 0,
                                       origin = "",
                                       storefront = 0,
                                       searchId = "",
                                       trafficData = None,
                                       ipAddress = None)
  private val aSessionInfo = SessionInfo(sessionId = 0, memberId = 0, isLogin = false)
  private val aExperimentInfo = ExperimentInfo(trafficGroup = "1")
  private val aContextRequest = ContextRequest(
    sessionInfo = aSessionInfo,
    clientInfo = aClientInfo,
    abTests = List(),
    experiment = List(),
    isAllowBookOnRequest = None,
    experimentInfo = Some(aExperimentInfo),
    userContext = Some(""),
  )
  private val anOccupancyRequest = OccupancyRequest(adults = 0,
                                                    children = 0,
                                                    rooms = 0,
                                                    childAges = List(),
                                                    roomsAssignment = Some(List.empty),
                                                    childrenTypes = Some(List.empty))
  private val aFilterRequest = FilterRequest(ratePlans = List(), suppliers = List(), secretDealOnly = false)
  private val aDiscountRequest = DiscountRequest(hashedBinNo = Option(Set()), campaignInfos = Option(List()))
  private val aFencedRateKey = FencedRateKey(Some("TH"), Some(-1))
  private val aFencedRatePair = FencedRatePair(key = aFencedRateKey, value = FencedOriginObject(Set()))
  private val aFencedRate = FencedRate(origins = List(),
                                       fencedOriginMap = Option(Map()),
                                       fencedRatePairs = Option(List(aFencedRatePair)),
                                       fencedOrigin = Option(List()))
  private val aDetailRequest = DetailRequest(priceBreakdown = false, itemBreakdown = false, cheapestPriceOnly = false)
  private val aFeatureRequest = FeatureRequest(newRateModel = false,
                                               overrideOccupancy = false,
                                               crossOutRate = false,
                                               priusId = 0,
                                               isMSE = None,
                                               isAllowRoomTypeNotGuarantee = None)
  private val aPricingRequest = PricingRequest(
    occupancy = anOccupancyRequest,
    filters = aFilterRequest,
    details = aDetailRequest,
    features = aFeatureRequest,
    checkIn = aDateTime,
    checkout = aDateTime.plusDays(1),
    currency = "",
    bookingDate = aDateTime,
    roomRequest = None,
    partner = None,
    refId = None,
    paymentId = None,
    featureFlag = List(),
    maxRooms = None,
    includedPriceInfo = None,
    discountRequest = None,
  )
  private val aPricingRequestWithBookingDurationType = aPricingRequest.copy(bookingDurationType = Some(List("hourly")))
  private val aPropertySearchRequestNoSSR = new PropertySearchRequest(context = aContextRequest,
                                                                      propertyIds = Seq(),
                                                                      pricing = aPricingRequest,
                                                                      suggestedPrice = "",
                                                                      isSSR = None,
                                                                      booking = None)
  private val aPropertyHourlySearchRequest =
    aPropertySearchRequestNoSSR.copy(pricing = aPricingRequestWithBookingDurationType)
  private val aPricingRequestParameters: PricingRequestParameters = PricingRequestParameters(context = aContextRequest,
                                                                                             pricing = aPricingRequest,
                                                                                             suggestedPrice = "",
                                                                                             isSSR = None,
                                                                                             booking = None)
  private val aPricingPropertiesRequest =
    PricingPropertiesRequest(pricingRequestParameters = aPricingRequestParameters, propertyIds = Seq())

  private val anOccInfo = OccInfo(_adults = None, _children = None, _rooms = None)
  private val aChild = Children()
  private val aPointMax = PointsMax(isEligible = false, value = 0.0, point = 0L, channelId = 0)
  private val aRateModel = RateModel(0)
  private val aRatePlan = RatePlan(0)
  private val aChannel = Channel(0)
  private val aCapacity = Capacity(
    adults = 0,
    children = 0,
    extraBed = 0,
    occupancy = 0,
    maxExtraBed = 0,
    allowedFreeChildrenAndInfants = 0,
    numberOfGuestsWithoutRoom = 0,
    symbol = None,
    description = None,
    perOfferMaxFreeChildren = Some(0),
    perOfferFreeChildren = Some(0),
  )
  private val aTaxReceipt = TaxReceipt(false, false, false)
  private val aPayLater = PayLater(false, aDateTime, aDateTime)
  private val aCancellation = Cancellation("", None, CancellationGroup.Unknown, None, None)
  private val aCreditCard = CreditCard(false)
  private val aPrePayment = PrePayment(false)
  private val aPayAtHotel = PayAtHotel(false)
  private val aGiftCard = GiftCard()
  private val aCashback = Cashback()
  private val aConsolidatedAppliedDiscount = ConsolidatedAppliedDiscount(
    "THB 123 off",
    "3 discounts applied",
    "123 B off",
    "Total rewards",
    "Already applied",
    123.2,
    List(ConsolidatedAppliedDiscountBreakdownItem("Cashback", "Earned after booking", 11.2, 1)),
  )
  private val aPayment = Payment(
    model = PaymentModels.Unknown,
    taxReceipt = aTaxReceipt,
    payLater = aPayLater,
    cancellation = aCancellation,
    creditCard = aCreditCard,
    prePayment = aPrePayment,
    payAtHotel = PayAtHotel(true),
  )
  private val aRoom = Room(
    uid = "",
    dmcRoomId = "",
    typeId = 0,
    masterTypeId = 0,
    rateModel = aRateModel,
    ratePlan = aRatePlan,
    benefits = Seq(),
    isBreakfastIncluded = false,
    channel = aChannel,
    supplierId = 0,
    availableRooms = 0,
    capacity = aCapacity,
    localCurrencyCode = "",
    pricing = Map(),
    promotions = Seq(),
    promotionsBreakdown = Map(),
    pointsMax = None,
    payment = aPayment,
    dmcPolicyText = None,
    gmtOffset = 0,
    isYCS = false,
    isFit = false,
    needOccupancySearch = false,
    giftCard = None,
    corTypeFlag = CorTypeFlags.NoCOR,
    pseudoCoupon = None,
    isAveragePrice = false,
    saveUpTo = 0d,
    rateRepurposeInfos = Seq(),
    isRepurposed = false,
    bookingInfo = None,
    npclnChannel = None,
    roomExtraInfo = RoomExtraInfo(),
    priceInfo = Map(),
    isFiredrill = false,
    features = RoomFeatures(),
    campaignInfo = None,
    subSupplierId = 0,
    effectiveNetPerBook = 0,
    gmtOffsetMinutes = 0,
    cashback = None,
    perBookCapacity = None,
    uspa = None,
    valueTag = None,
  )
  private val aBookingRoom = BookingRoom(
    uid = "",
    roomTypeId = 0,
    rateModel = aRateModel,
    breakfast = BookingBreakfast(false, ""),
    capacity = aCapacity,
    pricing = BookingPrice(Nil),
    payment = BookingRoomPayment(false, false),
    dmcData = None,
    availableRooms = 1,
    ycsRatePlanId = 1,
    paymentModels = PaymentModels.Unknown,
    promotion = Seq.empty,
    accountingEntity = None,
    sellInfo = BookingSellInfo(0, 0, None, "", 0, false, None),
    chargeDiscount = 0,
    cancellation = aCancellation,
    benefit = Seq.empty,
    rateCategory = BookingRateCategory(1, "", "", 0),
    supplierId = DMC.YCS,
    displayAmount = 100,
    numberOfRoom = 1,
    numberOfExtraBed = 0,
    displayCurrency = "THB",
    exchange = BookingPayment(1, 1, 1, 2, 2, 2, "THB", 1, 1),
    partnerLoyaltyPoint = None,
    paymentChannels = Nil,
  )
  private val aDisplayCharge =
    DisplayCharge(`type` = ChargeTypes.Room, toAgoda = Price(0, 0), toHotel = Price(0, 0), total = Price(0, 0))
  private val aDaily = Daily(date = aDateTime,
                             percentage = 10,
                             basis = ApplyTypes.PRPB,
                             quantity = 1,
                             price = Price(8, 10),
                             priceBreakdown = None,
                             netPrice = Price(7, 9),
                             subTypeId = SubChargeType.None)
  private val aCharge = Charge(`type` = ChargeTypes.Room,
                               pay = PayTypes.Agoda,
                               basis = ApplyTypes.PRPB,
                               quantity = 1,
                               percentage = 0d,
                               price = Price(0, 0),
                               daily = Seq())

  private val aPriceSummaryElement = SummaryElement(
    DisplayPrice(0, 0),
    DisplayPrice(0, 0),
    DisplayPrice(0, 0),
    DisplayPrice(0, 0),
    DisplayPrice(0, 0),
    DisplayPrice(0, 0),
    Some(DisplayPrice(0, 0)),
    Some(DisplayPrice(0, 0)),
    Some(DisplayPrice(0, 0)),
  )
  private val aHotelFeatures = HotelFeatures(isMyStaySupported = false, bookOnRequest = false)
  private val aHotelAggregatedPayment =
    HotelAggregatedPayment(aTaxReceipt, aPayLater, aCreditCard, aPrePayment, aPayAtHotel)
  private val aHotelAggregatedOptions = HotelAggregatedOptions(payment = aHotelAggregatedPayment)
  private val aHotelPayment = HotelPayment(PayLater(false),
                                           TaxReceipt(false, false, false, false, TaxReceiptType.None),
                                           NoCreditCard(false),
                                           Cancellation(""),
                                           PayAtHotel(false))
  private val aHotel = Hotel(
    hotelId = 0,
    cheapestRoomId = "",
    needOccupancySearch = false,
    rooms = Seq(),
    isReady = true,
    taxType = TaxTypes.Unknown,
    supplierId = 0,
    features = aHotelFeatures,
    payment = Some(aHotelPayment),
  )
  private val aBenefit = Benefit(0)
  private val aBookingRequest = BookingRequest()
  private val aReBookingRequest = ReBookingRequest(1, None, 1000.0, None, None, None, None, None, None, None, None)
  private val examplePriceFreeze = PriceFreeze(100, 40, 35)
  private val aProperties = Properties(Nil, None)
  private val aRoomSelectionRequest = RoomSelectionRequest(1, 0, 0, 0, 1, false, false, None, true)
  private val aPartnersPrice = PartnersPrices(None, None, None, None)
  private val aPartnersSurcharges = PartnersSurcharges(None, None, None, None)
  private val aPartnersPromotionSavings = PartnersPromotionSavings(0.0, 0.0, 0.0, 0.0)
  private val aPartnersTaxBreakdown = PartnerTaxBreakdown(None, None, None, None)
  private val aPartner = Partners(aPartnersPrice,
                                  aPartnersSurcharges,
                                  aPartnersPromotionSavings,
                                  None,
                                  Some(aPartnersTaxBreakdown),
                                  2,
                                  None,
                                  None,
                                  None,
                                  Some(false))
  private val aPartnersCharge =
    PartnersCharge(ChargeTypes.Unknown, ChargeOptions.Unknown, ApplyTypes.Unknown, 0, 0.0, 0.0)
  private val aPartnersDisplayPrice = PartnersDisplayPrice(aPartnersCharge, RateApplyTypes.Unknown)
  private val aPartnersPriceBreakdown = PartnersPriceBreakdown(Price(0.0, 0.0))
  private val aPricingMessageText = PricingMessageText(0, Map.empty[Int, CMSId])
  private val aPricingMessageRoomParameters =
    PricingMessageRoomParameter(Some(""), PricingMessageVariableType.Unknown, "", PricingMessageLocalizationType.Unknown)
  private val aPricingMessage = PricingMessage(Seq(aPricingMessageText), Seq(aPricingMessageRoomParameters))
  private val aDiscountMessage = DiscountMessage(1, 12345, 10.0)
  private val aBundleSegment1 =
    BundleSegment(1, DFModelTestDataBuilders.aValidUUID, aDateTime, 1, aRoom.copy(pricing = Map("USD" -> aPricing)))
  private val aBundleSegment2 = BundleSegment(2,
                                              DFModelTestDataBuilders.aValidUUID,
                                              aDateTime.plusDays(1),
                                              1,
                                              aRoom.copy(typeId = 1L, pricing = Map("USD" -> aPricing)))
  private val aRoomBundles = RoomBundles(
    "MixAndSave",
    DFModelTestDataBuilders.aValidUUID,
    List(aBundleSegment1, aBundleSegment2),
    aRoom.copy(pricing = Map("USD" -> aPricing)),
    false,
    Some(aDisplayBasis),
  )
  private val aExtraInformation = ExtraInformation(0, 0, 0, 0, 0, 0, 0, 0, None, None, None, None, None)
  private val aValidPromotion = Promotion(1L, Discount(40d, DiscountTypes.PercentDiscount), 1, 1, 1, "")
  private val aPseudoCoupon = PseudoCoupon()
  private val aCorBreakdown = CorBreakdown()
  private val aCorBreakdownItem = CorBreakdownItem(0, 0, 0.0, isDiscount = false)
  private val aAPSPeek = APSPeek(aDisplayPrice, aDisplayPrice)
  private val aPromotionPricePeek =
    PromotionPricePeek(aDisplayBasis, aPeekDiscountDisplayBasis, 5, Some("DISCOUNTNOW"), None, None, None)
  private val aSuggestedRoom =
    SuggestedRoom(DFModelTestDataBuilders.aValidUUID, 1, Map("USD" -> aValidPricing), None, CorBreakdown(), None, None)
  private val aMultiRoomSuggestion = MultiRoomSuggestion(List(aSuggestedRoom), Map("USD" -> aValidPricing))
  private val aCampaign: Campaign = Campaign(0, 0, 0, false, false)
  private val aSupplierFinancialData: SupplierFinancialData = SupplierFinancialData(Some(1.25), Some(1.65), Some(2.25))
  private val aPartnerRequest: PartnerRequest = PartnerRequest()
  private val aAdditionalRate: AdditionalRate = AdditionalRate(1, None, aDisplayBasis, None, None, None)
  private val aExternalLoyaltyRequest: ExternalLoyaltyRequest = ExternalLoyaltyRequest(
    selectedOffersIdentifier = Some("selectedOffersIdentifier"),
    points = Some(100.5),
    partnerClaimToken = Some("partnerClaimToken"),
    loyaltySearchType = Some("BURN"),
  )
  private val aItemBreakdown = ItemBreakdown(
    date = None,
    id = BookingRateTypes.NetExclusive,
    typeId = BookingItemTypes.Room,
    surchargeId = 0,
    quantity = 1,
    local = None,
    option = None,
    applyTo = ApplyTypes.PRPN,
  )

  private val aPackagePriceAndSaving = PackagePriceAndSaving(
    packagePrice = aPriceWithChannel,
    savings = Some(aPriceWithChannel),
    savingPercent = Some(DisplayPricePercentage(50, 50)),
  )

  private val aPriceWithChannel = PriceWithChannel(
    price = aAdditionalPriceDisplayBasis,
    channel = 1,
  )

  private val aAdditionalPriceDisplayBasis = AdditionalPriceDisplayBasis(
    perBook = DisplayPrice(
      exclusive = 100d,
      allInclusive = 150d,
    ),
    perPax = DisplayPrice(
      exclusive = 50d,
      allInclusive = 75d,
    ),
  )

  private val aCartBaseRequest = CartBaseRequest(None, None, None, None, None)

  private val aLoyaltyDisplayPrice = LoyaltyDisplayPrice(
    displayPrice = DisplayPrice(
      exclusive = 100d,
      allInclusive = 150d,
    ),
    pointsToDisplayPrice = DisplayPrice(
      exclusive = 10d,
      allInclusive = 10d,
    ),
    points = 100d,
    pointsToEarn = 10d,
  )

  private val aExternalLoyaltyPricing = ExternalLoyaltyPricing(
    perBook = aLoyaltyDisplayPrice,
    perRoomPerNight = aLoyaltyDisplayPrice,
    perNight = aLoyaltyDisplayPrice,
    rewardUnit = Some(LoyaltyRewardUnit(Some("Points"), Some(0))),
  )

  lazy val aPriceCalculationRequest: PriceCalculationRequest = PriceCalculationRequest(
    propertyOffer = models.pricecalculation.PropertyOffer(
      hotelId = 10637,
      paymentMode = proto.PaymentMode.Merchant,
      taxes = Map(
        1 -> models.pricecalculation.Tax(taxId = 1,
                                         applyTo = "PB",
                                         applyType = proto.ApplyType.Mandatory,
                                         isFee = false,
                                         isAmount = false,
                                         value = 7,
                                         isTaxable = false,
                                         taxPrototypeId = 0,
                                         taxApplyOnId = 0),
        2 -> models.pricecalculation.Tax(taxId = 2,
                                         applyTo = "PB",
                                         applyType = proto.ApplyType.Mandatory,
                                         isFee = true,
                                         isAmount = false,
                                         value = 10,
                                         isTaxable = true,
                                         taxPrototypeId = 0,
                                         taxApplyOnId = 0),
      ),
      commissions = Map(
        1 -> models.pricecalculation
          .Commission(channelId = 1, languageId = 0, contractedCommission = 20, value = None, marginAdjustment = None)),
      roomRates = Seq(aRoomRateCategory),
      isPromotionCombinable = None,
      isUseConfiguredProcessingFee = None,
    ),
    propertySearchRequest = None,
    hotelData = None,
    isMockYCSSupplierCCMapping = None,
  )

  lazy val aRoomRateCategory: models.pricecalculation.RoomRateCategory = models.pricecalculation.RoomRateCategory(
    currencyCode = "USD",
    channelRates = Seq(aChannelRate),
  )

  lazy val aChannelRate: models.pricecalculation.ChannelRate = models.pricecalculation.ChannelRate(
    channelId = 1,
    supplierRateCode = "rateload_1216809354_15478587",
    prices = Seq(
      models.pricecalculation.PriceDaily(occupancyPrices = Seq(models.pricecalculation.OccupancyPrice(amount = 10000)))),
    taxPerDay = Map(-1 -> models.pricecalculation.Identifiers(Seq(1, 2))),
    commissionPerDay = Map(-1 -> models.pricecalculation.Identifiers(Seq(1))),
    channelDiscountPerDay = None,
    rateLoadType = proto.PriceType.NetExc,
    channelDiscountRateLoadType = None,
    paymentMode = proto.PaymentMode.Merchant,
  )

  lazy val aChannelDiscountCalculationRequest: ChannelDiscountCalculationRequest = ChannelDiscountCalculationRequest(
    1,
    PriceTypes.NetExc,
    Seq(ChannelDiscountRequest(channelId = 1, 20, 0), ChannelDiscountRequest(channelId = 2, 10, 20)),
    taxes = Seq.empty)
  private val aBookingItemBreakdown = PostBkgBookingItemBreakdown(Some(aDateTime),
                                                                  140,
                                                                  6,
                                                                  0,
                                                                  0,
                                                                  0,
                                                                  "THB",
                                                                  1,
                                                                  None,
                                                                  Option(1),
                                                                  0,
                                                                  subTypeId = None,
                                                                  1,
                                                                  ChargeOptions.Mandatory,
                                                                  0)

  private val aPostBookingPrice = PostBookingPrice(100d, 3500d, 14000d)

  private val aPostBookingSummary = PostBookingSummary(aPostBookingPrice,
                                                       aPostBookingPrice,
                                                       aPostBookingPrice,
                                                       aPostBookingPrice,
                                                       aPostBookingPrice,
                                                       aPostBookingPrice)

  private val aPostBookingContext =
    PostBookingContext(whiteLabelId = 100, actionType = PostBookingActionType.CancellationFee)

  private val aPostBookingInfo = PostBookingInfo(
    checkInDate = aDateTime,
    checkOutDate = aDateTime.plusDays(1),
    dmcId = 332,
    cancellationPolicyCode = "1D1N_1N",
    requestCurrency = "JPY",
    requestCurrencyExchange = Some(1d),
    payment = PostBookingPayment(PostBookingPrice(0d, 0d, 0d), "THB", None),
    paymentModel = models.pricing.enums.PaymentModels.Merchant,
    isAgodaAgencyNoCancellationFee = false,
    localPrecision = 2,
    requestPrecision = Some(2),
  )

  private val aPostBookingRequest: PostBookingRequest = PostBookingRequest(
    context = aPostBookingContext,
    bookingInfo = Some(aPostBookingInfo),
    financialBreakdown = aValidFinancialBreakdown,
    affiliateInfo = Some(aValidPostBookingAffiliateInfo.build),
    cancellationRequest = Some(aValidPostBookingCancellation.build),
  )
  val aPostBookingSearchRequest =
    PostBookingSearchRequest(Some(aValidPropertySearchRequest.build), Some(aPostBookingRequest))
  private def parseDateTime(date: String, format: String = "yyyy-MM-dd"): DateTime = {
    val jodaFormat = DateTimeFormat.forPattern(format)
    jodaFormat.parseDateTime(date)
  }
  /* =================================
   *        PRIVATE BUILDERS
   * ================================= */

  case class ChargeBuilder(build: Charge) {
    def withChargeType(chargeType: ChargeType): B = build.copy(`type` = chargeType)
    def withBasisApplyType(applyType: ApplyType): B = build.copy(basis = applyType)
    def withQuantity(quantity: Int): B = build.copy(quantity = quantity)
    def withDaily(daily: Seq[Daily]): B = build.copy(daily = daily)
    def withChargeOption(chargeOption: ChargeOption): B = build.copy(option = Some(chargeOption))
    def withChargeOptionOpt(chargeOption: Option[ChargeOption]): B = build.copy(option = chargeOption)
    def withPayType(payType: PayType): B = build.copy(pay = payType)
    def withPrice(price: Price): B = build.copy(price = price)
    def withIsIncluded(isInclude: Option[Boolean]): B = build.copy(isInclude = isInclude)
    def withPartnersPriceBreakdown(partnersPriceBreakdown: PartnersPriceBreakdown): B =
      build.copy(priceBreakdown = Some(partnersPriceBreakdown))
    def withId(id: Int): B = build.copy(id = id)
    def withDisplay(displayPrice: Option[Display]): B = build.copy(display = displayPrice)
    def withDescription(description: Option[String]): B = build.copy(description = description)
    def withPercentage(percentage: Double): B = build.copy(percentage = percentage)

    type B = ChargeBuilder
  }

  case class DailyBuilder(build: Daily) {
    def withDate(date: DateTime): B = build.copy(date = date)
    def withPrice(price: Price): B = build.copy(price = price)
    def withPriceBreakdown(priceBreakdown: PartnersPriceBreakdown): B = build.copy(priceBreakdown = Some(priceBreakdown))
    def withNetPrice(netPrice: Price): B = build.copy(netPrice = netPrice)
    def withSubChargeType(subTypeId: SubChargeType): B = build.copy(subTypeId = subTypeId)
    type B = DailyBuilder
  }

  case class DisplayChargeBuilder(build: DisplayCharge) {
    def withChargeType(chargeType: models.pricing.enums.ChargeType): B = build.copy(`type` = chargeType)
    def withToAgodaPrice(price: Price): B = build.copy(toAgoda = price)
    def withToHotelPrice(price: Price): B = build.copy(toHotel = price)
    def withTotalPrice(price: Price): B = build.copy(total = price)
    def withBreakdown(charge: Charge): B = build.copy(breakdown = Seq(charge))
    def withBreakdown(charges: Seq[Charge]): B = build.copy(breakdown = charges)
    type B = DisplayChargeBuilder
  }

  case class LoyaltyOffersSummaryBuilder(build: LoyaltyOfferSummary) {
    type B = LoyaltyOfferSummary
  }

  case class DisplayPriceBuilder(build: DisplayPrice) {
    def withExclusive(exclusiveAmount: Double): B = build.copy(exclusive = exclusiveAmount)
    def withInclusive(allInclusiveAmount: Double): B = build.copy(allInclusive = allInclusiveAmount)
    type B = DisplayPriceBuilder
  }

  case class DisplayBasisBuilder(build: DisplayBasis) {
    def withPerBook(perBook: DisplayPrice): B = build.copy(perBook = perBook)
    def withPerRoomPerNight(perRoomPerNight: DisplayPrice): B = build.copy(perRoomPerNight = perRoomPerNight)
    def withPerNight(perNight: DisplayPrice): B = build.copy(perNight = perNight)
    type B = DisplayBasisBuilder
  }

  case class DisplayPricePercentageBuilder(build: DisplayPricePercentage) {
    def withExclusive(exclusive: Int): B = build.copy(exclusive = exclusive)
    def withAllInExclusive(allInclusive: Int): B = build.copy(allInclusive = allInclusive)
    type B = DisplayPricePercentageBuilder
  }

  case class DisplayBasisIntBuilder(build: DisplayBasisInt) {
    def withPerBook(pB: DisplayPricePercentage): B = build.copy(perBook = pB)
    def withPerRoomPerNight(pRPN: DisplayPricePercentage): B = build.copy(perRoomPerNight = pRPN)
    type B = DisplayBasisIntBuilder
  }

  case class SummaryElementBuilder(build: SummaryElement) {
    def withChargeTotal(chargeTotal: DisplayPrice): B = build.copy(chargeTotal = chargeTotal)
    def withRebateTotal(rebateTotal: DisplayPrice): B = build.copy(rebateTotal = rebateTotal)
    def withRebateExtraBed(rebateExtraBed: DisplayPrice): B = build.copy(rebateExtraBed = rebateExtraBed)
    def withDisplayTotal(displayTotal: DisplayPrice): B = build.copy(displayTotal = displayTotal)
    def withPseudoCoupon(pseudoCoupon: DisplayPrice): B = build.copy(pseudoCoupon = pseudoCoupon)
    def withOriginalTotal(originalTotal: DisplayPrice): B = build.copy(originalTotal = originalTotal)
    def withPayToAgoda(payToAgoda: DisplayPrice): B = build.copy(payToAgoda = Some(payToAgoda))
    def withPayAtHotel(payAtHotel: DisplayPrice): B = build.copy(payAtHotel = Some(payAtHotel))
    def withAutoAppliedPromoDiscount(autoAppliedPromoDiscount: DisplayPrice): B =
      build.copy(autoAppliedPromoDiscount = Some(autoAppliedPromoDiscount))
    def withRoomTotal(roomTotal: DisplayPrice): B = build.copy(roomTotal = Some(roomTotal))
    def withCashbackTotal(cashbackTotal: DisplayPrice): B = build.copy(cashbackTotal = Some(cashbackTotal))
    def withCashbackExtraBed(cashbackExtraBed: DisplayPrice): B = build.copy(cashbackExtraBed = Some(cashbackExtraBed))
    def withDisplayAfterCashback(displayAfterCashback: DisplayPrice): B =
      build.copy(displayAfterCashback = Some(displayAfterCashback))
    def withLoyaltyOffersSummary(loyaltyOffersSummary: Option[LoyaltyOfferSummary]): B =
      build.copy(loyaltyOffersSummary = loyaltyOffersSummary)

    type B = SummaryElementBuilder
  }

  case class DisplaySummaryBuilder(build: DisplaySummary) {
    def withPerBook(perBook: SummaryElement): B = build.copy(perBook = perBook)
    def withPerRoomPerBook(perRoomPerBook: SummaryElement): B = build.copy(perRoomPerBook = perRoomPerBook)
    def withPerRoomPerNight(perRoomPerNight: SummaryElement): B = build.copy(perRoomPerNight = perRoomPerNight)
    def withPerNight(perNight: SummaryElement): B = build.copy(perNight = perNight)
    type B = DisplaySummaryBuilder
  }

  case class PricingBuilder(build: Pricing) {
    def withDisplay(display: DisplayBasis): B = build.copy(display = display)
    def withCharges(charges: Seq[DisplayCharge]): B = build.copy(charges = charges)
    def withCharges(charge: DisplayCharge): B = build.copy(charges = Seq(charge))
    def withOptionalCharges(charges: Seq[DisplayCharge]): B = build.copy(options = charges)
    def withDisplaySummary(displaySummary: DisplaySummary): B = build.copy(displaySummary = displaySummary)
    def withPartner(partner: Option[Partners]): B = build.copy(partner = partner)
    def withCrossedOutRates(cor: DisplayBasis): B = build.copy(crossedOut = cor)
    def withDiscountMessage(discountMessages: Seq[DiscountMessage]): B = build.copy(discountMessages = discountMessages)
    def withExtraInformation(extraInformation: ExtraInformation): B = build.copy(extraInfo = Some(extraInformation))
    def withRocketmilesPricing(rocketmilesPricing: Option[RocketmilesPricing]): B =
      build.copy(rocketmiles = rocketmilesPricing)
    def withAPSPeek(apsPeek: APSPeek): B = build.copy(apsPeek = Some(apsPeek))
    def withPackagePriceAndSaving(packagePriceAndSaving: PackagePriceAndSaving): B =
      build.copy(packagePriceAndSaving = Some(packagePriceAndSaving))
    def withPromotionPricePeek(promotionPricePeek: PromotionPricePeek): B =
      build.copy(promotionPeekPrice = Some(promotionPricePeek))
    def withPromotionsCumulative(promotionsCumulative: Seq[PromotionCumulative]): B =
      build.copy(promotionsCumulative = promotionsCumulative)
    def withAdditionalPrices(additionalPrices: Map[ChannelId, DisplayBasis]): B =
      build.copy(additionalPrices = additionalPrices)
    def withAdditionalRateInfo(additionalRateInfo: List[AdditionalRate]): B =
      build.copy(additionalRateInfo = additionalRateInfo)
    def withDisplayPriceAfterAppliedAgodaCash(display: Option[DisplayBasis]): B =
      build.copy(priceAfterAppliedAgodaCash = display)
    def withDiscountAmount(amount: Option[Double]): B = build.copy(discountAmount = amount)
    def withSelectedAdditionalRate(additionalRate: AdditionalRate): B =
      build.copy(selectedAdditionalRate = Some(additionalRate))
    def withExternalLoyaltyPricing(externalLoyaltyPricing: Option[ExternalLoyaltyPricing]): B =
      build.copy(externalLoyaltyPricing = externalLoyaltyPricing)
    def withTotalDiscount(totalDiscount: Int): B = build.copy(totalDiscount = totalDiscount)
    type B = PricingBuilder
  }

  case class APSPeekBuilder(build: APSPeek) {
    def withPerBook(price: DisplayPrice): B = build.copy(perBook = price)
    def withPerRoomPerNight(price: DisplayPrice): B = build.copy(perRoomPerNight = price)
    type B = APSPeekBuilder
  }

  case class PackagePriceAndSavingBuilder(build: PackagePriceAndSaving) {
    def withPrice(price: PriceWithChannel): B = build.copy(packagePrice = price)
    def withSavings(savings: PriceWithChannel): B = build.copy(savings = Some(savings))
    def withSavingPercent(savings: DisplayPricePercentage): B = build.copy(savingPercent = Some(savings))
    type B = PackagePriceAndSavingBuilder
  }

  case class PriceWithChannelBuilder(build: PriceWithChannel) {
    def withPrice(price: AdditionalPriceDisplayBasis): B = build.copy(price = price)
    def withChannel(channel: ChannelId): B = build.copy(channel = channel)
    type B = PriceWithChannelBuilder
  }

  case class AdditionalPriceDisplayBasisBuilder(build: AdditionalPriceDisplayBasis) {
    def withPerBook(perBook: DisplayPrice): B = build.copy(perBook = perBook)
    def withPerPax(perPax: DisplayPrice): B = build.copy(perPax = perPax)
    def withPerRoomPerNight(perRoomPerNight: DisplayPrice): B = build.copy(perRoomPerNight = Some(perRoomPerNight))
    def withPerNight(perNight: DisplayPrice): B = build.copy(perNight = Some(perNight))
    type B = AdditionalPriceDisplayBasisBuilder
  }

  case class PromotionPricePeekBuilder(build: PromotionPricePeek) {
    def withDisplay(display: DisplayBasis): B = build.copy(display = display)
    def withPromotionCode(code: String): B = build.copy(promotionCode = Some(code))
    def withCampaignId(campaignId: Option[Int]): B = build.copy(campaignId = campaignId)
    def withCampaignName(campaignName: String): B = build.copy(campaignName = Some(campaignName))
    def withPromocodeType(promotionCodeType: PromotionCodeType): B = build.copy(promotionCodeType = promotionCodeType)
    def withPromoAppliedOnFinalPrice(promoAppliedOnFinalPrice: Boolean): B =
      build.copy(promoAppliedOnFinalPrice = promoAppliedOnFinalPrice)
    def withAutoAppliedOnBookingForm(autoAppliedOnBookingForm: Boolean): B =
      build.copy(autoAppliedOnBookingForm = autoAppliedOnBookingForm)
    def withAnalyticsInfo(analyticsInfo: Option[Map[String, String]]): B = build.copy(analyticsInfo = analyticsInfo)
    type B = PromotionPricePeekBuilder
  }

  case class SuggestedRoomBuilder(build: SuggestedRoom) {
    def withUID(uid: String): B = build.copy(roomUid = uid)

    type B = SuggestedRoomBuilder
  }

  case class MultiRoomSuggestionBuilder(build: MultiRoomSuggestion) {
    def withSuggestedRoom(suggestedRooms: List[SuggestedRoom]): B = build.copy(roomSuggestions = suggestedRooms)
    type B = MultiRoomSuggestionBuilder
  }

  case class PackagingBuilder(build: Packaging) {
    def withToken(token: PackagingResponseToken): B = build.copy(token = token)
    def withPricing(string: String, packagePricing: PackagePricing): B =
      build.copy(pricing = Map(string -> packagePricing))
    type B = PackagingBuilder
  }

  case class PackagePricingBuilder(build: PackagePricing) {
    def withDisplay(display: PackageDisplayBasis): B = build.copy(display = display)
    def withCrossedOut(crossedOut: PackageDisplayBasis): B = build.copy(crossedOut = crossedOut)
    def withSaving(saving: PackageDisplayBasis): B = build.copy(saving = saving)
    def withCharges(charges: Seq[DisplayCharge]): B = build.copy(charges = charges)
    def withTotalDiscount(totalDiscount: Int): B = build.copy(totalDiscount = totalDiscount)
    def withHotelOriginalRate(hotelOriginalRate: Option[PackageDisplayBasis]): B =
      build.copy(hotelOriginalRate = hotelOriginalRate)
    def withPriceChange(priceChange: Option[PackageDisplayBasis]): B = build.copy(priceChange = priceChange)
    def withOriginalPriceAndSaving(originalPriceAndSaving: Option[PackageOriginalPriceAndSaving]): B =
      build.copy(originalPriceAndSaving = originalPriceAndSaving)
    def withOriginalPrice(originalPrice: Option[PackageDisplayBasis]): B = build.copy(originalPrice = originalPrice)
    def withPseudoCoupon(pseudoCoupon: Option[PackageDisplayBasis]): B = build.copy(pseudoCoupon = pseudoCoupon)
    def withRebate(rebate: Option[PackageRebate]): B = build.copy(rebate = rebate)
    def withProductBreakDown(productBreakDown: Seq[PackageProductDisplayPrice]): B =
      build.copy(productBreakDown = productBreakDown)
    def withCrossedOutSaving(crossedOutSaving: Option[PackageDetailedSaving]): B =
      build.copy(crossedOutSaving = crossedOutSaving)
    def withAdditionalRateSaving(additionalRateSaving: Option[PackageDetailedSaving]): B =
      build.copy(additionalRateSaving = additionalRateSaving)
    def withAdditionalRate(additionalRate: Option[PackageDisplayBasis]): B = build.copy(additionalRate = additionalRate)
    type B = PackagePricingBuilder
  }

  case class PackageDetailedSavingBuilder(build: PackageDetailedSaving) {
    def withTotalSaving(totalSaving: Option[PackageSaving]): B = build.copy(totalSaving = totalSaving)
    def withOriginalSaving(originalSaving: Option[PackageSaving]): B = build.copy(originalSaving = originalSaving)
    type B = PackageDetailedSavingBuilder
  }

  case class PackageAdditionalPriceAndSavingBuilder(build: PackageAdditionalPriceAndSaving) {
    def withChannelId(channel: Option[Int]): B = build.copy(channelId = channel)
    def withPrice(additionalPrice: PackageDisplayBasis): B = build.copy(price = additionalPrice)
    def withSaving(saving: Option[PackageSaving]): B = build.copy(saving = saving)
    type B = PackageAdditionalPriceAndSavingBuilder
  }

  case class PackageProductDisplayPriceBuilder(build: PackageProductDisplayPrice) {
    type B = PackageProductDisplayPriceBuilder
    def withProductId(productId: String): B = build.copy(productId = productId)
    def withProductType(productType: PackageProductType): B = build.copy(productType = productType)
    def withTotal(total: PackageDisplayBasis): B = build.copy(total = Some(total))
    def withOriginalPrice(originalPrice: PackageDisplayBasis): B = build.copy(originalPrice = Some(originalPrice))
    def withTotalSaving(totalSaving: Option[PackageSaving]): B = build.copy(totalSaving = totalSaving)
    def withChannelSaving(channelSaving: Option[PackageSaving]): B = build.copy(channelSaving = channelSaving)
    def withCrossedOut(crossedOut: Option[PackageDisplayBasis]): B = build.copy(crossedOut = crossedOut)
    def withPseudoCoupon(pseudoCoupon: Option[PackageDisplayBasis]): B = build.copy(pseudoCoupon = pseudoCoupon)
    def withRebate(rebate: Option[PackageRebate]): B = build.copy(rebate = rebate)
    def withCharges(charges: Seq[DisplayCharge]): B = build.copy(charges = charges)
    def withAdditionalRate(additionalRate: Option[PackageDisplayBasis]): B = build.copy(additionalRate = additionalRate)
    def withAdditionalRateSaving(additionalRateSaving: Option[PackageDetailedSaving]): B =
      build.copy(additionalRateSaving = additionalRateSaving)
    def withCrossedOutSaving(crossedOutSaving: Option[PackageDetailedSaving]): B =
      build.copy(crossedOutSaving = crossedOutSaving)
  }

  case class ClientInfoBuilder(build: ClientInfo) {
    def withCID(cid: Int): B = build.copy(cid = cid)
    def withTrafficData(trafficData: Option[TrafficInfo]): B = build.copy(trafficData = trafficData)
    def withOrigin(origin: String): B = build.copy(origin = origin)
    def withLanguageUse(languageUse: Int): B = build.copy(languageUse = languageUse)
    def withLanguageId(languageId: Int): B = build.copy(languageId = languageId)
    def withUserId(userId: String): B = build.copy(userId = userId)
    def withPlatform(platform: Int): B = build.copy(platform = platform)
    def withAffiliateId(aid: Option[String]): B = build.copy(affiliateId = aid)
    def withSearchId(searchId: String): B = build.copy(searchId = searchId)

    type B = ClientInfoBuilder
  }

  case class SessionInfoBuilder(build: SessionInfo) {
    def withSessionID(sessionID: Int): B = build.copy(sessionId = sessionID)
    def withIsLogin(isLogin: Boolean): B = build.copy(isLogin = isLogin)
    type B = SessionInfoBuilder
  }

  case class ContextRequestBuilder(build: ContextRequest) {
    def withClientInfo(clientInfo: ClientInfo): B = build.copy(clientInfo = clientInfo)
    def withSessionInfo(sessionInfo: SessionInfo): B = build.copy(sessionInfo = sessionInfo)
    def withExperiment(experiment: List[Experiment]): B = build.copy(experiment = experiment)
    def withExperimentInfo(experimentInfo: ExperimentInfo): B = build.copy(experimentInfo = Some(experimentInfo))
    def withRequestMeta(requestMeta: Option[RequestMeta]): B = build.copy(requestMeta = requestMeta)
    def withUserContext(userContext: Option[String]): B = build.copy(userContext = userContext)
    def withTracingContext(tracingContext: Option[String]): B = build.copy(tracingContext = tracingContext)
    def withPackaging(packaging: Option[PackagingRequest]): B = build.copy(packaging = packaging)
    def withCartRequest(cartRequest: Option[CartBaseRequest]): B = build.copy(cartRequest = cartRequest)
    def withTTL(ttl: Option[FiniteDuration]): B = build.copy(ttl = ttl)
    def withIsAllowBookOnRequest(isAllowBookOnRequest: Option[Boolean]): B =
      build.copy(isAllowBookOnRequest = isAllowBookOnRequest)
    type B = ContextRequestBuilder
  }

  case class OccupancyRequestBuilder(build: OccupancyRequest) {
    def withAdults(adults: Int): B = build.copy(adults = adults)
    def withChildren(children: Int): B = build.copy(children = children)
    def withChildAges(childAges: List[Option[Int]]): B = build.copy(childAges = childAges)
    def withRooms(rooms: Int): B = build.copy(rooms = rooms)
    def withRoomsAssignment(roomsAssignment: Option[List[RoomAssignment]]): B =
      build.copy(roomsAssignment = roomsAssignment)
    def withChildrenTypes(childrenTypes: Option[List[ChildType]]): B = build.copy(childrenTypes = childrenTypes)
    type B = OccupancyRequest
  }

  case class FilterRequestBuilder(build: FilterRequest) {
    def withRatePlans(ratePlans: List[Int]): B = build.copy(ratePlans = ratePlans)
    def withCheapestRoomFilters(cheapestRoomFilters: List[CheapestRoomFilterId]): B =
      build.copy(cheapestRoomFilters = cheapestRoomFilters)
    def withPackagingFilterContext(packagingFilterContext: Option[PackagingFilterContext]): B =
      build.copy(packagingFilterContext = packagingFilterContext)
    def withSuppliers(suppliers: List[Int]): B = build.copy(suppliers = suppliers)
    def withNosOfBedrooms(nosOfBedrooms: Option[List[NoOfBedrooms]]): B = build.copy(nosOfBedrooms = nosOfBedrooms)
    def withUnsupportedPaymentModels(models: Option[List[PaymentModel]]): B =
      build.copy(unsupportedPaymentModels = models)
    def withBenefitIds(benefitIds: Option[List[BenefitId]]): B = build.copy(benefitIds = benefitIds)
    def withFilterAPO(filterAPO: Boolean): B = build.copy(filterAPO = filterAPO)
    type B = FilterRequestBuilder

  }

  case class DiscountRequestBuilder(build: DiscountRequest) {
    def withHashedBinNo(hashedBinNo: Option[Set[String]]): B = build.copy(hashedBinNo = hashedBinNo)
    def withCampaignInfo(campaignInfo: Option[CampaignInfo]): B = build.copy(campaignInfo = campaignInfo)
    def withCampaignInfos(campaignInfos: Option[List[CampaignInfo]]): B = build.copy(campaignInfos = campaignInfos)
    def withOverrideCampaign(overrideCampaign: Option[OverrideCampaign]): B =
      build.copy(overrideCampaign = overrideCampaign)
    def withEmail(email: Option[String]): B = build.copy(email = email)
    type B = DiscountRequest
  }

  case class FencedRateBuilder(build: FencedRate) {
    def withOrigins(origins: List[String]): B = build.copy(origins = origins)
    def withFencedOriginMap(fencedOriginMap: Option[Map[String, FencedOriginObject]]): B =
      build.copy(fencedOriginMap = fencedOriginMap)
    def withFencedRatePairs(fencedRatePairs: Option[List[FencedRatePair]]): B =
      build.copy(fencedRatePairs = fencedRatePairs)
    def withFencedOrigin(fencedOrigin: Option[List[StringAndFencedOriginObject]]): B =
      build.copy(fencedOrigin = fencedOrigin)
    type B = FencedRate
  }

  case class FencedRatePairBuilder(build: FencedRatePair) {
    type B = FencedRatePair
  }

  case class DetailRequestBuilder(build: DetailRequest) {
    def withPriceBreakDown(priceBreakdown: Boolean): B = build.copy(priceBreakdown = priceBreakdown)
    type B = DetailRequestBuilder
  }

  case class FeatureRequestBuilder(build: FeatureRequest) {
    def withNewRateModel(newRateModel: Boolean): B = build.copy(newRateModel = newRateModel)
    def withMSE(isMse: Option[Boolean]): B = build.copy(isMSE = isMse)
    def withMaxSuggestions(maxSuggested: Option[Int]): B = build.copy(maxSuggestions = maxSuggested)
    def withIsAllOcc(isAllOcc: Boolean): B = build.copy(isAllOcc = isAllOcc)
    def withEnableSupplierFinancialInfo(enableSupplierFinancialInfo: Option[Boolean]): B =
      build.copy(isEnableSupplierFinancialInfo = enableSupplierFinancialInfo)
    def withCalculateCancellationPhases(calculateCancellationPhases: Option[Boolean]): B =
      build.copy(calculateCancellationPhases = calculateCancellationPhases)
    def withEnableCOR(enableCOR: Option[Boolean]): B = build.copy(enableCOR = enableCOR)
    def withDisableEscapesPackage(disableEscapesPackage: Option[Boolean]): B =
      build.copy(disableEscapesPackage = disableEscapesPackage)
    def withReturnCheapestEscapesOfferOnSSR(returnCheapestEscapesOfferOnSSR: Option[Boolean]): B =
      build.copy(returnCheapestEscapesOfferOnSSR = returnCheapestEscapesOfferOnSSR)
    def withBenefitValuationForASO(enableBenefitValuationForASO: Option[Boolean]) =
      build.copy(enableBenefitValuationForASO = enableBenefitValuationForASO)
    def withShowCheapestHourlyRate(showCheapestHourlyRate: Option[Boolean]): B =
      build.copy(showCheapestHourlyRate = showCheapestHourlyRate)
    def withTurnOffFxi(turnOffFxi: Option[Boolean]): B = build.copy(turnOffFxi = turnOffFxi)

    def withIsIncludeUsdAndLocalCurrency(isIncludeUsdAndLocalCurrency: Boolean): B =
      build.copy(isIncludeUsdAndLocalCurrency = isIncludeUsdAndLocalCurrency)
    type B = FeatureRequestBuilder
  }

  case class PricingRequestBuilder(build: PricingRequest) {
    def withCheckIn(checkInDate: DateTime): B = build.copy(checkIn = checkInDate)
    def withCheckout(checkoutDate: DateTime): B = build.copy(checkout = checkoutDate)
    def withBookingDate(bookingDate: DateTime): B = build.copy(bookingDate = bookingDate)
    def withOccupancy(occupancy: OccupancyRequest): B = build.copy(occupancy = occupancy)
    def withFeatureFlag(ff: FeatureFlag): B = build.copy(featureFlag = List(ff))
    def withFeatureFlag(ff: List[FeatureFlag]): B = build.copy(featureFlag = ff)
    def withMseHotelIds(mseHotelIds: Option[List[Int]]): B = build.copy(mseHotelIds = mseHotelIds)
    def withSearchedHotelId(searchedHotelIds: Option[List[Int]]): B = build.copy(searchedHotelIds = searchedHotelIds)
    def withBookingDurationType(bookingDurationType: Option[List[String]]): B =
      build.copy(bookingDurationType = bookingDurationType)
    def withRoomBundleHints(roomBundleHints: Option[List[RoomBundleRequest]]): B =
      build.copy(roomBundleHints = roomBundleHints)
    def withSupplierPullMetadata(metadata: SupplierPullMetadataRequest): B =
      build.copy(supplierPullMetadata = Some(metadata))
    def withFencedRate(fencedRate: Option[FencedRate]): B = build.copy(fencedRate = fencedRate)
    def withFilters(filterRequest: FilterRequest): B = build.copy(filters = filterRequest)
    def withRoomSelection(roomSelection: Option[RoomSelectionRequest]): B = build.copy(roomSelection = roomSelection)
    def withWhiteLabelKey(whiteLabelKey: Option[String]): B = build.copy(whiteLabelKey = whiteLabelKey)
    def withFeatureRequest(feature: FeatureRequest): B = build.copy(features = feature)
    def withLocalCheckInDate(checkInDate: Option[String]): B = build.copy(localCheckInDate = checkInDate)
    def withLocalCheckoutDate(checkoutDate: Option[String]): B = build.copy(localCheckoutDate = checkoutDate)
    def withCurrency(cur: String): B = build.copy(currency = cur)
    def withDiscountRequest(discountRequest: Option[DiscountRequest]): B = build.copy(discountRequest = discountRequest)
    def withClientCampaignInfos(clientCapmaignInfos: Option[List[CampaignInfo]]): B =
      build.copy(clientCampaignInfos = clientCapmaignInfos)
    def withPPLandingHotelIds(ppLandingHotelIds: Option[List[Int]]): B =
      build.copy(ppLandingHotelIds = ppLandingHotelIds)
    def withIncludedPriceInfo(includedPriceInfo: Boolean): B = build.copy(includedPriceInfo = Some(includedPriceInfo))
    def withPartner(partner: PartnerRequest): B = build.copy(partner = Some(partner))
    def withSimplifiedRoomSelection(req: Option[SimplifiedRoomSelectionRequest]): B =
      build.copy(simplifiedRoomSelectionRequest = req)
    def withExcludeFilters(excludeFilters: Option[FilterOutRequest]): B = build.copy(excludeFilters = excludeFilters)
    def withExternalLoyaltyRequest(externalLoyaltyRequest: Option[ExternalLoyaltyRequest]): B =
      build.copy(externalLoyaltyRequest = externalLoyaltyRequest)
    def withPayment(payment: Option[PaymentRequest]): B = build.copy(payment = payment)
    def withPaymentId(paymentId: Option[Int]): B = build.copy(paymentId = paymentId)
    def withRequiredBasis(requiredBasis: Option[ApplyType]): B = build.copy(requiredBasis = requiredBasis)
    def withSelectedRewardOption(selectedRewardOption: Option[String]): B =
      build.copy(selectedRewardOption = selectedRewardOption)
    def withSelectedCheckInTime(selectedCheckInTime: Option[String]): B =
      build.copy(selectedCheckInTime = selectedCheckInTime)
    def withPaymentOption(paymentOption: Option[Int]): B =
      build.copy(payment = build.payment.map(_.copy(paymentOption = paymentOption)))
    type B = PricingRequestBuilder

    def withExternalLoyaltyProfile(externalLoyaltyProfile: Option[ExternalLoyaltyProfile]): B =
      build.copy(externalLoyaltyProfile = externalLoyaltyProfile)

    def withMetadata(metadata: Option[List[StringAndString]]): B = build.copy(metadata = metadata)
  }

  case class PropertySearchRequestBuilder(build: PropertySearchRequest) {
    def withContextRequest(contextRequest: ContextRequest): B = build.copy(context = contextRequest)
    def withoutIsSSR(): B = build.copy(isSSR = None)
    def withIsSSR(isSSR: Boolean): B = build.copy(isSSR = Some(isSSR))
    def withIsCheapestOnly(cheapestOnly: Boolean): B = build.copy(cheapestOnly = cheapestOnly)
    def withIsMixNSaveSegmentSearch(IsMixNSaveSegmentSearch: Boolean): B =
      build.copy(isMixNSaveSegmentSearch = Some(IsMixNSaveSegmentSearch))
    def withSuggestedPrice(suggestedPrice: String): B = build.copy(suggestedPrice = suggestedPrice)
    def withoutBookingRequest(): B = build.copy(booking = None)
    def withBookingRequest(bookingRequest: BookingRequest): B = build.copy(booking = Some(bookingRequest))
    def withPricing(pricing: PricingRequest): B = build.copy(pricing = pricing)
    def withOccupancy(occupancy: OccupancyRequest): B = build.copy(pricing = build.pricing.copy(occupancy = occupancy))
    def withPaymentCreditCard(paymentCreditCard: PaymentCreditCard): B =
      build.copy(pricing = build.pricing.copy(payment = Some(PaymentRequest(creditCard = Some(paymentCreditCard)))))
    def withWhiteLabelKey(whiteLabelKey: Option[String]): B =
      build.copy(pricing = build.pricing.copy(whiteLabelKey = whiteLabelKey))
    def withPropertyIds(propertyIds: Seq[Long]): B = build.copy(propertyIds = propertyIds)
    def withSortingStrategy(strategy: RoomSortingStrategy): B = build.copy(roomSortingStrategy = Some(strategy))
    def withoutSortingStrategy(): B = build.copy(roomSortingStrategy = None)
    def withRocketmilesPublishPriceRequest(rocketmilesPublishPriceRequest: Option[RocketmilesPublishPriceRequest]): B =
      build.copy(rocketmilesPublishPriceRequest = rocketmilesPublishPriceRequest)
    def withPropertySearchToken(propertySearchToken: Option[String]): B =
      build.copy(propertySearchToken = propertySearchToken)
    def withCartRequest(cart: Option[CartBaseRequest]): B =
      build.copy(context = build.context.withCartRequest(cart).build)
    type B = PropertySearchRequestBuilder
  }

  case class PricingRequestParametersBuilder(build: PricingRequestParameters) {
    type B = PricingRequestParameters
    def withContextRequest(contextRequest: ContextRequest): B = build.copy(context = contextRequest)
    def withoutIsSSR(): B = build.copy(isSSR = None)
    def withIsSSR(isSSR: Boolean): B = build.copy(isSSR = Some(isSSR))
    def withSuggestedPrice(suggestedPrice: String): B = build.copy(suggestedPrice = suggestedPrice)
    def withoutBookingRequest(): B = build.copy(booking = None)
    def withBookingRequest(bookingRequest: BookingRequest): B = build.copy(booking = Some(bookingRequest))
    def withPricing(pricing: PricingRequest): B = build.copy(pricing = pricing)
    def withOccupancy(occupancy: OccupancyRequest): B = build.copy(pricing = build.pricing.copy(occupancy = occupancy))
    def withPaymentCreditCard(paymentCreditCard: PaymentCreditCard): B =
      build.copy(pricing = build.pricing.copy(payment = Some(PaymentRequest(creditCard = Some(paymentCreditCard)))))
    def withSortingStrategy(strategy: RoomSortingStrategy): B = build.copy(roomSortingStrategy = Some(strategy))
    def withoutSortingStrategy(): B = build.copy(roomSortingStrategy = None)
    def withCheapestOnly(cheapestOnly: Boolean): B = build.copy(cheapestOnly = cheapestOnly)
    def withRocketmilesPublishPriceRequest(rocketmilesPublishPriceRequest: RocketmilesPublishPriceRequest): B =
      build.copy(rocketmilesPublishPriceRequest = Some(rocketmilesPublishPriceRequest))
  }
  case class PricingPropertiesRequestBuilder(build: PricingPropertiesRequest) {
    type B = PricingPropertiesRequest
    def withPricingRequestParameters(pricingRequestParameters: PricingRequestParameters): B =
      build.copy(pricingRequestParameters = pricingRequestParameters)
    def withPropertyIds(propertyIds: Seq[Long]): B = build.copy(propertyIds = propertyIds)
    def withOverridePricingRequest(overridePricingRequest: OverridePricingRequest): B =
      build.copy(overridePricingRequest = Some(overridePricingRequest))
  }

  case class OccInfoBuilder(build: OccInfo) {
    def withAdults(adults: Int): B = build.copy(_adults = if (adults > 0) Some(adults) else None)
    def withChildren(children: Children): B = build.copy(_children = children.toOption(children.amount != 0))
    def withRooms(rooms: Int): B = build.copy(_rooms = if (rooms > 0) Some(rooms) else None)
    def withZeroRoom(): B = build.copy(_rooms = Some(0))
    type B = OccInfoBuilder
  }

  case class ChildrenBuilder(build: Children) {
    def withChildren(children: List[Int]): B = build.copy(ages = children.map(age => age.toOption(true)))
    type B = ChildrenBuilder
  }
  case class RateModelBuilder(build: RateModel) {
    def withID(id: Int): B = build.copy(id = id)
    type B = RateModelBuilder
  }

  case class RatePlanBuilder(build: RatePlan) {
    def withID(id: Long): B = build.copy(id = id)
    def withEnrichedRatePlan(enrichedRatePlan: EnrichedRatePlan): B =
      build.copy(enrichedRatePlan = Some(enrichedRatePlan))
    type B = RatePlanBuilder
  }

  case class ChannelBuilder(build: Channel) {
    def withID(id: Long): B = build.copy(id = id)
    type B = ChannelBuilder
  }

  case class CapacityBuilder(build: Capacity) {
    def withAdults(adults: Int): B = build.copy(adults = adults)
    def withChildren(children: Int): B = build.copy(children = children)
    def withOccupancy(occupancy: Int): B = build.copy(occupancy = occupancy)
    def withExtraBeds(extraBeds: Int): B = build.copy(extraBed = extraBeds)
    def withMaxExtraBeds(maxExtraBeds: Int): B = build.copy(maxExtraBed = maxExtraBeds)
    def withPerOfferFreeChildren(perOfferFreeChildren: Int): B =
      build.copy(perOfferFreeChildren = Some(perOfferFreeChildren))
    def withPerOfferMaxFreeChildren(perOfferMaxFreeChildren: Int): B =
      build.copy(perOfferMaxFreeChildren = Some(perOfferMaxFreeChildren))
    def withRoomAllocationInfo(roomAllocationInfo: Seq[RoomAllocationInfo]): B =
      build.copy(roomAllocationInfo = Some(roomAllocationInfo))
    type B = CapacityBuilder
  }

  case class TaxReceiptBuilder(build: TaxReceipt) {
    def withAvailable(available: Boolean): B = build.copy(available = available)
    def withIsDomestic(isDomestic: Boolean): B = build.copy(isDomestic = isDomestic)
    def withIsCommission(isCommission: Boolean): B = build.copy(isCommission = isCommission)
    def withIsEligible(isEligible: Boolean): B = build.copy(isEligible = isEligible)
    type B = TaxReceiptBuilder
  }

  case class PaylaterBuilder(build: PayLater) {
    def withValue(payLater: Boolean): B = build.copy(payLater = payLater)
    type B = PaylaterBuilder
  }

  case class CancellationBuilder(build: Cancellation) {
    def withCancellationCode(cxlCode: String): B = build.copy(cxlCode = cxlCode)
    def withCancellationGroup(group: CancellationGroup): B = build.copy(cancellationGroup = group)
    type B = CancellationBuilder
  }

  case class CreditCardBuilder(build: CreditCard) {
    def withRequired(required: Boolean): B = build.copy(required = required)
    type B = CreditCardBuilder
  }

  case class PrePaymentBuilder(build: PrePayment) {
    def withRequired(required: Boolean): B = build.copy(required = required)
    type B = PrePaymentBuilder
  }

  case class PayAtHotelBuilder(build: PayAtHotel) {
    def withIsEligible(isEligible: Boolean): B = build.copy(isEligible = isEligible)
    type B = PayAtHotelBuilder
  }

  case class PaymentBuilder(build: Payment) {
    def withModel(paymentModel: PaymentModel): B = build.copy(model = paymentModel)
    def withPrePayment(prePayment: PrePayment): B = build.copy(prePayment = prePayment)
    def withCreditCard(creditCard: CreditCard): B = build.copy(creditCard = creditCard)
    def withTaxReceipt(taxReceipt: TaxReceipt): B = build.copy(taxReceipt = taxReceipt)
    def withCancellationCode(cancellation: Cancellation): B = build.copy(cancellation = cancellation)
    def withPayLater(payLater: PayLater): B = build.copy(payLater = payLater)
    type B = PaymentBuilder
  }

  case class RoomBuilder(build: Room) {
    def withRoomTypeId(typeId: Long): B = build.copy(typeId = typeId)
    def withMasterRoomTypeId(typeId: Long): B = build.copy(masterTypeId = typeId)
    def withCapacity(capacity: Capacity): B = build.copy(capacity = capacity)
    def withPricing(pricingMap: Map[String, Pricing]): B = build.copy(pricing = pricingMap)
    def withPackaging(packaging: Option[Packaging]): B = build.copy(packaging = packaging)
    def withSupplierID(supplierID: Long): B = build.copy(supplierId = supplierID)
    def withSubSupplierID(subSupplierID: Int): B = build.copy(subSupplierId = subSupplierID)
    def withPointsMax(pointsMax: PointsMax): B = build.copy(pointsMax = Option(pointsMax))
    def withPayment(payment: Payment): B = build.copy(payment = payment)
    def withBenefits(benefits: Seq[Benefit]): B = build.copy(benefits = benefits)
    def withRoomIdentifier(id: Option[String]): B = build.copy(roomIdentifier = id)
    def withIsFit(isFit: Boolean): B = build.copy(isFit = isFit)
    def withAgodaBrand(agodaBrand: Boolean): B = build.copy(isAgodaBrand = agodaBrand)
    def withOrigin(origin: Option[String]): B = build.copy(origin = origin)
    def withFencedRateKey(fencedRateKey: Option[FencedRateKey]): B = build.copy(fencedRateKey = fencedRateKey)
    def withDMCRoomId(dmcRoomId: String): B = build.copy(dmcRoomId = dmcRoomId)
    def withUid(uid: String): B = build.copy(uid = uid)
    def withAveragePrice(isAveragePrice: Boolean): B = build.copy(isAveragePrice = isAveragePrice)
    def withPricingMessages(pricingMessage: Option[PricingMessage]): B = build.copy(pricingMessages = pricingMessage)
    def withGiftCard(giftCard: Option[GiftCard]): B = build.copy(giftCard = giftCard)
    def withCashback(cashback: Option[Cashback]): B = build.copy(cashback = cashback)
    def withAvailableRoom(available: Int): B = build.copy(availableRooms = available)
    def withRatePlan(ratePlan: RatePlan): B = build.copy(ratePlan = ratePlan)
    def withPromotions(promotions: Seq[Promotion]): B = build.copy(promotions = promotions)
    def withChannel(channel: Channel): B = build.copy(channel = channel)
    def withNPCLNChannel(channel: Option[Int]): B = build.copy(npclnChannel = channel)
    def withLoyaltyDisplay(loyaltyDisplay: Option[LoyaltyDisplay]): B = build.copy(loyaltyDisplay = loyaltyDisplay)
    def withHourlyAvailableSlots(slots: Option[Seq[SFTimeInterval]]): B = build.copy(hourlyAvailableSlots = slots)
    def withCorInfo(corInfo: Option[CORInfo]): B = build.copy(corInfo = corInfo)
    def withFeatures(roomFeature: RoomFeatures): B = build.copy(features = roomFeature)
    def withIsFireDrill(isFireDrill: Boolean): B = build.copy(isFiredrill = isFireDrill)
    def withPartialRefund(partialRefundInfo: Option[PartialRefundInfo]): B =
      build.copy(partialRefundInfo = partialRefundInfo)
    def withIsRepurposed(isRepurposed: Boolean): B = build.copy(isRepurposed = isRepurposed)
    def withIsSingleRoomNHA(isSingleRoomNHA: Boolean): B = build.copy(isSingleRoomNHA = isSingleRoomNHA)
    def withIsYcs(isYcs: Boolean): B = build.copy(isYCS = isYcs)
    def withIsAverage(isAvg: Boolean): B = build.copy(isAveragePrice = isAvg)
    def withDmcPolicyText(dmcPolicyText: Option[DmcData]): B = build.copy(dmcPolicyText = dmcPolicyText)
    def withCart(cart: Option[Cart]): B = build.copy(cart = cart)
    def withSupplierFinancialData(supplierFinancialData: Option[SupplierFinancialData]): B =
      build.copy(supplierFinancialData = supplierFinancialData)
    def withCampaignFeature(campaignFeature: Option[CampaignFeatures]): B = build.copy(campaignFeature = campaignFeature)
    def withStayPackageType(sp: Option[StayPackageType]) = build.copy(stayPackageType = sp)
    def withPulseCampaignMetadata(pm: Option[PulseCampaignMetadata]) = build.copy(pulseCampaignMetadata = pm)
    def withIsBreakfastIncluded(isBreakfastIncluded: Boolean): B = build.copy(isBreakfastIncluded = isBreakfastIncluded)
    def withBookingInfo(bookingInfo: Option[BookingInfo]): B = build.copy(bookingInfo = bookingInfo)
    def withLocalCurrencyCode(localCurrencyCode: String): B = build.copy(localCurrencyCode = localCurrencyCode)
    def withNeedOccupancySearch(needOccupancySearch: Boolean): B = build.copy(needOccupancySearch = needOccupancySearch)
    def withExternalLoyaltyDisplay(externalLoyaltyDisplay: Option[ExternalLoyaltyDisplay]): B =
      build.copy(externalLoyaltyDisplay = externalLoyaltyDisplay)
    def withToolTip(toolTip: Option[DFRoomToolTip]): B = build.copy(toolTip = toolTip)
    def withConsolidatedAppliedDiscount(consolidatedAppliedDiscount: Option[ConsolidatedAppliedDiscount]): B =
      build.copy(consolidatedAppliedDiscount = consolidatedAppliedDiscount)
    def withCheckInInformation(checkInInformation: Option[CheckInInformation]): B =
      build.copy(checkInInformation = checkInInformation)
    def withValueTag(valueTag: Option[ValueTag]): B = build.copy(valueTag = valueTag)
    type B = RoomBuilder
  }

  case class PriceSummaryElementBuilder(build: SummaryElement) {
    def withRebateTotal(displayPrice: DisplayPrice): B = build.copy(rebateTotal = displayPrice)
    def withRebateExtrabed(displayPrice: DisplayPrice): B = build.copy(rebateExtraBed = displayPrice)
    def withDisplayTotal(displayPrice: DisplayPrice): B = build.copy(displayTotal = displayPrice)
    def withPseudoCoupon(displayPrice: DisplayPrice): B = build.copy(pseudoCoupon = displayPrice)
    def withOriginalTotal(displayPrice: DisplayPrice): B = build.copy(originalTotal = displayPrice)
    def withPayToAgoda(displayPrice: DisplayPrice): B = build.copy(payToAgoda = Some(displayPrice))
    def withPayAtHotel(displayPrice: DisplayPrice): B = build.copy(payAtHotel = Some(displayPrice))
    type B = PriceSummaryElementBuilder
  }

  case class PointsMaxBuilder(build: PointsMax) {
    def withPoints(point: Long): B = build.copy(point = point)
    type B = PointsMaxBuilder
  }

  case class HotelBuilder(build: Hotel) {
    def withID(hotelId: Long): B = build.copy(hotelId = hotelId)
    def withRooms(rooms: Seq[Room]): B = build.copy(rooms = rooms)
    def withRoom(room: Room): B = build.copy(rooms = Seq(room))
    def withHotelFeatures(features: HotelFeatures): B = build.copy(features = features)
    def withCheapestRoomId(roomId: String): B = build.copy(cheapestRoomId = roomId)
    def withPsuedoCouponMessage(pseudoCouponMessage: Option[PseudoCouponMessage]): B =
      build.copy(pseudoCouponMessage = pseudoCouponMessage)
    def withSupplierSummary(supplierSummary: Option[SuppliersSummary]): B =
      build.copy(suppliersSummary = supplierSummary)
    def withRoomLevelPayLaterData(roomLevelPayLaterData: Option[Seq[PayLaterData]]): B =
      build.copy(roomLevelPayLaterData = roomLevelPayLaterData)
    def withAvailableRoomIds(availableRoomIds: Set[RoomTypeId]): B = build.copy(availableRoomIds = availableRoomIds)
    def withRoombundles(roomBundles: RoomBundles): B = build.copy(roomBundles = Some(roomBundles))
    def withIsReady(isReady: Boolean): B = build.copy(isReady = isReady)
    def withSuggestedRoom(rooms: List[Room]): B = build.copy(suggestedRooms = rooms)
    def withRoomBundleList(roomBundleList: List[RoomBundles]): B = build.copy(roomBundleList = roomBundleList)
    def withMultiRoomSuggestions(multiRoomSuggestions: List[MultiRoomSuggestion]): B =
      build.copy(multiRoomSuggestions = multiRoomSuggestions)
    def withPriceChange(priceChange: PriceChange): B = build.copy(priceChange = Some(priceChange))
    def withRocketMilesPublishedPricing(rocketMilesPublishedPricing: RocketMilesPublishedPricing): B =
      build.copy(rocketmiles = Some(rocketMilesPublishedPricing))
    def withCheapestStayPackageRatePlans(cheapestStayPackageRatePlans: List[CheapestStayPackageRatePlans]) =
      build.copy(cheapestStayPackageRatePlans = Some(cheapestStayPackageRatePlans))
    def withCheapestEscapesRoom(cheapestEscapesRoom: Option[Room]) =
      build.copy(cheapestEscapesRoom = cheapestEscapesRoom)
    def withPropertySearchToken(propertySearchToken: Option[String]): B =
      build.copy(propertySearchToken = propertySearchToken)
    def withBookingSummary(bookingSummary: Option[BookingSummary]): B = build.copy(bookingSummary = bookingSummary)
    def withStayOccupancy(stayOccupancy: Option[StayOccupancy]): B = build.copy(stayOccupancy = stayOccupancy)
    def withExternalLoyaltyDisplay(externalLoyaltyDisplay: Option[ExternalLoyaltyDisplay]): B =
      build.copy(externalLoyaltyDisplay = externalLoyaltyDisplay)
    def withDayuseCheckInTimes(dayuseCheckInTimes: Seq[DayuseCheckInTime], isAvailable: Boolean, isReady: Boolean): B =
      build.copy(dayuseInfo = Some(DayuseInfo(dayuseCheckInTimes, None, Some(isReady), Some(isAvailable))))
    def withCheapestHourlyRoom(cheapestHourlyRoom: Option[Room]): B = build.copy(cheapestHourlyRoom = cheapestHourlyRoom)
    def withConsolidatedPeekMessage(consolidatedPeekMessage: Option[ConsolidatedPeekMessage]): B =
      build.copy(consolidatedPeekMessage = consolidatedPeekMessage)
    type B = HotelBuilder
  }

  case class HotelFeaturesBuilder(build: HotelFeatures) {
    def withOptions(options: HotelAggregatedOptions): B = build.copy(options = Option(options))
    def withIsEasyCancel(value: Boolean): B = build.copy(isEasyCancel = value)
    def withIsMultiHotelEligible(value: Boolean): B = build.copy(isMultiHotelEligible = value)
    def withIsPackageEligible(value: Boolean): B = build.copy(isPackageEligible = value)
    def withIsCartEligible(value: Boolean): B = build.copy(isCartEligible = value)
    type B = HotelFeaturesBuilder
  }

  case class HotelAggregatedOptionsBuilder(build: HotelAggregatedOptions) {
    def withPayment(payment: HotelAggregatedPayment): B = build.copy(payment = payment)
    def withBenefits(b: Seq[Benefit]): B = build.copy(benefits = b)
    def withPointsMax(p: Option[PointsMax]): B = build.copy(maxPointMax = p)
    def withChannel(b: Option[Set[Long]]): B = build.copy(channels = b)
    def withLoyaltyItems(b: Option[Set[LoyaltyItemType]]): B = build.copy(loyaltyItems = b)
    def withChildPolicy(b: Option[ChildPolicy]): B = build.copy(childPolicy = b)
    type B = HotelAggregatedOptionsBuilder
  }

  case class HotelAggregatedPaymentBuilder(build: HotelAggregatedPayment) {
    def withPrePayment(prePayment: PrePayment): B = build.copy(prePayment = prePayment)
    def withCreditCard(creditCard: CreditCard): B = build.copy(creditCard = creditCard)
    def withTaxReceipt(taxReceipt: TaxReceipt): B = build.copy(taxReceipt = taxReceipt)
    def withPayLater(payLater: PayLater): B = build.copy(payLater = payLater)
    type B = HotelAggregatedPaymentBuilder
  }

  case class BenefitBuilder(build: Benefit) {
    def withId(id: Long): B = build.copy(id = id)
    def withDescription(description: String): B = build.copy(description = description)
    def withDefaultDescription(defaultDescription: Option[String]): B =
      build.copy(defaultDescription = defaultDescription)
    def withSymbol(symbol: String): B = build.copy(symbol = symbol)
    def withRemark(remark: String): B = build.copy(remark = remark)
    def withValue(value: Double): B = build.copy(value = value)
    def withUnit(unit: Int): B = build.copy(unit = unit)
    def withTargetType(targetType: Int): B = build.copy(targetType = targetType)
    def withBenefitRank(benefitRank: Option[Int]): B = build.copy(benefitRank = benefitRank)
    type B = BenefitBuilder
  }

  case class BookingRequestBuilder(build: BookingRequest) {
    def withPriceFreeze(priceFreeze: PriceFreeze): B = build.copy(priceFreeze = Some(priceFreeze))
    def withFilters(filters: BookingFilter*): B = build.copy(filters = filters.toList)
    def withPartnerRequest(request: PartnerRequest): B = build.copy(partner = Some(request))
    def withReBookingRequest(request: ReBookingRequest): B = build.copy(reBookingRequest = Some(request))
    def withHourlySlots(selectedHourlySlot: SelectedHourlySlot): B =
      build.copy(selectedHourlySlot = Some(selectedHourlySlot))
    def withPriceAdjustmentId(id: Long): B = build.copy(priceAdjustmentId = Some(id))

    type B = BookingRequestBuilder
  }

  case class ReBookingRequestBuilder(build: ReBookingRequest) {
    def withRoomTypeId(id: RoomTypeId): B = build.copy(roomTypeId = id)
    def withCustomerPaidPrice(price: Double): B = build.copy(customerPaidPrice = price)
    def withOriginalNetIn(price: Option[Double]): B = build.copy(originalNetIn = price)
    def withOriginalSellInUsd(price: Option[Double]): B = build.copy(originalSellInUsd = price)
    type B = ReBookingRequestBuilder
  }

  case class PriceFreezeBuilder(build: PriceFreeze) {
    def withMaxCap(cap: Double): B = build.copy(maxCap = cap)
    def withFrozenPrice(price: Double): B = build.copy(frozenPrice = price)
    def withDeposit(amount: Double): B = build.copy(deposit = amount)

    type B = PriceFreezeBuilder
  }

  case class RoomSelectionRequestBuilder(build: RoomSelectionRequest) {
    def withOccupancy(occupancy: Int): B = build.copy(occupancy = occupancy)
    def withChildren(children: Int): B = build.copy(children = children)
    def withExtraBed(extraBed: Int): B = build.copy(extraBed = extraBed)
    def withMandatoryExtraBed(mandatoryExtraBed: Int): B = build.copy(mandatoryExtraBed = mandatoryExtraBed)
    def withRoom(room: Int): B = build.copy(room = room)
    def withIsRespectMaxOcc(isRespectMaxOcc: Boolean): B = build.copy(isRespectMaxOcc = isRespectMaxOcc)
    def withNeedOccupancySearch(needOccupancySearch: Boolean): B = build.copy(needOccupancySearch = needOccupancySearch)
    def withMasterRoomOccupancy(masterRoomOccupancy: Option[Int]): B =
      build.copy(masterRoomOccupancy = masterRoomOccupancy)
    def withIsFit(isFit: Boolean): B = build.copy(isFit = isFit)
    def withRequestExtraBedForRoomNumbers(reqExtraBed: Option[List[Int]]): B =
      build.copy(requestExtraBedForRoomNumbers = reqExtraBed)
    type B = RoomSelectionRequestBuilder
  }

  case class PropertiesBuilder(build: Properties) {
    def withHotels(hotels: Hotel*): B = build.copy(hotels = hotels.toSeq)
    type B = PropertiesBuilder
  }
  case class BackupRoomCriteriaBuilder(build: BackupRoomCriteria) {
    def withRoomTypeId(i: Int): B = build.copy(roomTypeId = i)
    def withRateCategoryId(i: Int): B = build.copy(rateCategoryId = i)
    def withPaymentModel(i: Int): B = build.copy(paymentModel = i)
    def withPromotionId(i: Option[Int]): B = build.copy(promotionId = i)
    def withRoomTypeNotGuarantee(i: Boolean): B = build.copy(isRoomTypeNotGuarantee = i)
    type B = BackupRoomCriteriaBuilder
  }

  case class PartnersBuilder(build: Partners) {
    def withPartnersPrices(prices: PartnersPrices): B = build.copy(prices = prices)
    def withPartnersSurcharges(surcharges: PartnersSurcharges): B = build.copy(surcharges = surcharges)
    def withPartnersPromotionSavings(promotions: PartnersPromotionSavings): B = build.copy(promotions = promotions)
    def withPartnersTaxAndFee(taxAndFee: Option[PartnerTaxBreakdown]): B = build.copy(taxAndFee = taxAndFee)
    type B = PartnersBuilder
  }

  case class PartnersPricesBuilder(build: PartnersPrices) {
    def withPerBook(perBook: Option[PartnersDisplayPrice]): B = build.copy(perBook = perBook)
    def withPerRoomPerNight(perRoomPerNight: Option[PartnersDisplayPrice]): B =
      build.copy(perRoomPerNight = perRoomPerNight)
    type B = PartnersPricesBuilder
  }

  case class PartnersDisplayPriceBuilder(build: PartnersDisplayPrice) {
    def withPartnersCharge(partnersCharge: PartnersCharge): B = build.copy(partnersCharge = partnersCharge)
    def withDaily(dailyPrices: Seq[PartnersDailyRate]): B = build.copy(daily = dailyPrices)
    type B = PartnersDisplayPriceBuilder
  }

  case class PartnersChargeBuilder(build: PartnersCharge) {
    def withPartnersPriceBreakdown(breakdown: Option[PartnersPriceBreakdown]): B =
      build.copy(pricesBreakdown = breakdown)
    def withAmount(amount: Double): B = build.copy(amount = amount)
    def withId(id: Int): B = build.copy(id = id)
    def withType(`type`: ChargeType): B = build.copy(`type` = `type`)
    def withDescription(description: Option[String]): B = build.copy(description = description)
    def withTaxable(taxable: Option[Boolean]): B = build.copy(taxable = taxable)
    def withAffiliatePrices(affiliatePrices: Option[AffiliatePrices] = None): B =
      build.copy(affiliatePrices = affiliatePrices)
    def withTaxProtoTypeId(taxProtoTypeId: Option[Int]): B = build.copy(taxProtoTypeId = taxProtoTypeId)

    type B = PartnersChargeBuilder
  }

  case class PartnersSurchargesBuilder(build: PartnersSurcharges) {
    def withPerBook(perBook: Option[Seq[PartnersCharge]]): B = build.copy(perBook = perBook)
    def withPerRoomPerNight(perRoomPerNight: Option[Seq[PartnersCharge]]): B =
      build.copy(perRoomPerNight = perRoomPerNight)
    type B = PartnersSurchargesBuilder
  }

  case class PartnerTaxBreakdownBuilder(build: PartnerTaxBreakdown) {
    def withPerBook(perBook: Option[Seq[PartnersCharge]]): B = build.copy(perBook = perBook)
    type B = PartnerTaxBreakdownBuilder
  }

  case class PartnersPriceBreakdownBuilder(build: PartnersPriceBreakdown) {
    def withNetEx(netEx: Double): B = build.copy(netExc = netEx)
    def withTax(tax: Double): B = build.copy(tax = tax)
    def withMargin(margin: Double): B = build.copy(margin = margin)
    def withFee(fees: Double): B = build.copy(fees = fees)
    def withPf(pf: Double): B = build.copy(pf = pf)
    def withTaxBreakdown(taxes: Option[List[TaxBreakDown]] = None): B = build.copy(taxBreakdown = taxes)
    def withProcessingFeeBreakdown(breakdown: Option[ProcessingFeeBreakdown] = None): B =
      build.copy(processingFeeBreakdown = breakdown)
    def withRefSellInc(refSellInc: Option[Double] = None): B = build.copy(refSellInc = refSellInc)
    def withRefSellEx(refSellEx: Option[Double] = None): B = build.copy(refSellEx = refSellEx)
    def withDiscount(discount: Option[Double] = None): B = build.copy(discount = discount)
    def withAgxFreeTrial(agxFreeTrial: Option[Double] = None): B = build.copy(agxFreeTrial = agxFreeTrial)
    def withReferenceAgpFee(referenceAgpFee: Option[Double] = None): B = build.copy(referenceAgpFee = referenceAgpFee)
    def withReferenceAgpInvoiceFee(referenceAgpInvoiceFee: Option[Double] = None): B =
      build.copy(referenceAgpInvoiceFee = referenceAgpInvoiceFee)
    def withRefCommission(refCommission: Double): B = build.copy(refCommission = refCommission)
    type B = PartnersPriceBreakdownBuilder
  }

  case class PricingMessageBuilder(build: PricingMessage) {
    def withTexts(texts: Seq[PricingMessageText]): B = build.copy(texts = texts)
    def withParameters(parameters: Seq[PricingMessageRoomParameter]): B = build.copy(parameters = parameters)
    type B = PricingMessageBuilder
  }

  case class GiftCardBuilder(build: GiftCard) {
    def withShowBadge(showBadge: Boolean): B = build.copy(showBadge = showBadge)
    def withGiftcardGuid(giftcardGuid: String): B = build.copy(giftcardGuid = giftcardGuid)
    def withDayToEarn(dayToEarn: Int): B = build.copy(dayToEarn = dayToEarn)
    def withEarnId(earnId: Int): B = build.copy(earnId = earnId)
    def withExpiryDay(expiryDay: Int): B = build.copy(expiryDay = expiryDay)
    def withPercentage(percentage: Double): B = build.copy(percentage = percentage)
    type B = GiftCardBuilder
  }
  case class ConsolidatedAppliedDiscountBuilder(build: ConsolidatedAppliedDiscount) {
    def withAppliedDiscountBannerMessage(appliedDiscountBannerMessage: String): B =
      build.copy(appliedDiscountBannerMessage = appliedDiscountBannerMessage)
    def withTotalDiscountBannerMessage(totalDiscountBannerMessage: String): B =
      build.copy(totalDiscountBannerMessage = totalDiscountBannerMessage)
    def withTotalDiscountJacketMessage(totalDiscountJacketMessage: String): B =
      build.copy(totalDiscountJacketMessage = totalDiscountJacketMessage)
    def withBreakdownsTitle(breakdownsTitle: String): B = build.copy(breakdownsTitle = breakdownsTitle)
    def withBreakdownsSubtitle(breakdownsSubtitle: String): B = build.copy(breakdownsSubtitle = breakdownsSubtitle)
    def withTotalDiscountAmount(totalDiscountAmount: Double): B = build.copy(totalDiscountAmount = totalDiscountAmount)
    def withBreakdowns(breakdowns: List[ConsolidatedAppliedDiscountBreakdownItem]): B =
      build.copy(breakdowns = breakdowns)
    type B = ConsolidatedAppliedDiscountBuilder
  }
  case class CashbackBuilder(build: Cashback) {
    def withCashbackGuid(cashbackGuid: String): B = build.copy(cashbackGuid = cashbackGuid)
    def withEarnId(earnId: Int): B = build.copy(earnId = earnId)
    def withPercentage(percentage: Double): B = build.copy(percentage = percentage)
    def withDayToEarn(dayToEarn: Int): B = build.copy(dayToEarn = dayToEarn)
    def withExpiryDay(expiryDay: Int): B = build.copy(expiryDay = expiryDay)
    def withShowPostCashbackPrice(showPostCashbackPrice: Boolean): B =
      build.copy(showPostCashbackPrice = showPostCashbackPrice)
    def withCashbackVersion(cashbackVersion: String): B = build.copy(cashbackVersion = Some(cashbackVersion))
    type B = CashbackBuilder
  }

  case class DiscountMessageBuilder(build: DiscountMessage) {
    def withMessageType(messageType: Int): B = build.copy(messageType = messageType)
    def withCmsId(cMSId: Int): B = build.copy(cmsId = cMSId)
    def withValue(value: Double): B = build.copy(value = value)
    type B = DiscountMessageBuilder
  }

  case class RoomBundlesBuilder(build: RoomBundles) {
    def withTypeId(typeId: String): B = build.copy(typeId = typeId)
    def withBundleId(bundleId: String): B = build.copy(bundleId = bundleId)
    def withSegments(segments: List[BundleSegment]): B = build.copy(segments = segments)
    def withMergedRoom(mergedRoom: Room): B = build.copy(mergedRoom = mergedRoom)
    def withIsOnlyExistOffer(isOnlyExistingOffer: Boolean): B = build.copy(isOnlyExistingOffer = isOnlyExistingOffer)
    def withSaveAmount(saveAmount: DisplayBasis): B = build.copy(saveAmount = Some(saveAmount))
    type B = RoomBundlesBuilder
  }

  case class BundleSegmentBuilder(build: BundleSegment) {
    def withSegmentId(segmentId: Int): B = build.copy(segmentId = segmentId)
    def withHintId(hintId: String): B = build.copy(hintId = hintId)
    def withCheckInDate(checkIn: DateTime): B = build.copy(checkIn = checkIn)
    def withLengthOfStay(lengthOfStay: Int): B = build.copy(lengthOfStay = lengthOfStay)
    def withRoom(room: Room): B = build.copy(room = room)
    type B = BundleSegmentBuilder
  }

  case class SFPromotionBuilder(build: Promotion) {
    def withId(id: Long): B = build.copy(id = id)
    def withDiscount(discount: models.starfruit.Discount): B = build.copy(discount = discount)
    def withCmsId(cmsId: Int): B = build.copy(cmsDiscountTypeId = cmsId)
    def withTypeId(id: Int): B = build.copy(typeId = id)
    def withEnrichedPromotion(enrichedPromotion: EnrichedPromotion): B =
      build.copy(enrichedPromotion = Some(enrichedPromotion))
    type B = SFPromotionBuilder
  }

  case class ExtraInformationBuilder(build: ExtraInformation) {
    def withRoomTaxAndFeePRPN(roomTaxAndFeePRPN: Double): B = build.copy(roomTaxAndFeePRPN = roomTaxAndFeePRPN)
    def withRoomTaxAndFeePN(roomTaxAndFeePN: Double): B = build.copy(roomTaxAndFeePN = roomTaxAndFeePN)
    def withRoomTaxAndFeeWithExcludedPB(roomTaxAndFeeWithExcludedPB: Double): B =
      build.copy(roomTaxAndFeeWithExcludedPB = roomTaxAndFeeWithExcludedPB)
    def withRoomTaxAndFeeWithAllChargesPRPN(roomTaxAndFeeWithAllChargesPRPN: Double): B =
      build.copy(roomTaxAndFeeWithAllChargesPRPN = roomTaxAndFeeWithAllChargesPRPN)
    def withRoomTaxAndFeeWithAllChargesPB(roomTaxAndFeeWithAllChargesPB: Double): B =
      build.copy(roomTaxAndFeeWithAllChargesPB = roomTaxAndFeeWithAllChargesPB)
    def withMarginPerNightUSD(marginPerNightUSD: Double): B = build.copy(marginPerNightUSD = marginPerNightUSD)
    def withRoomWithExtraBeds(roomWithExtraBeds: Double): B = build.copy(roomWithExtraBeds = roomWithExtraBeds)
    def withTotalPriceWithoutTaxAndFee(totalPriceWithoutTaxAndFee: Double): B =
      build.copy(totalPriceWithoutTaxAndFee = totalPriceWithoutTaxAndFee)
    type B = ExtraInformationBuilder
  }

  case class PseudoCouponBuilder(build: PseudoCoupon) {
    def withShowBadge(showBadge: Boolean): B = build.copy(showBadge = showBadge)
    type B = PseudoCouponBuilder
  }

  case class CorBreakdownBuilder(build: CorBreakdown) {
    def withPerRoomPerNightTaxExclusive(perRoomPerNightTaxExclusive: List[CorBreakdownItem]): B =
      build.copy(perRoomPerNightTaxExclusive = perRoomPerNightTaxExclusive)
    def withPerRoomPerNightTaxInclusive(perRoomPerNightTaxInclusive: List[CorBreakdownItem]): B =
      build.copy(perRoomPerNightTaxInclusive = perRoomPerNightTaxInclusive)
    def withPerNightTaxExclusive(perNightTaxExclusive: List[CorBreakdownItem]): B =
      build.copy(perNightTaxExclusive = perNightTaxExclusive)
    def withPerNightTaxInclusive(perNightTaxInclusive: List[CorBreakdownItem]): B =
      build.copy(perNightTaxInclusive = perNightTaxInclusive)
    type B = CorBreakdownBuilder
  }

  case class CorBreakdownItemBuilder(build: CorBreakdownItem) {
    def withId(id: Int): B = build.copy(id = id)
    def withCmsId(cmsId: Int): B = build.copy(cmsId = cmsId)
    def withPrice(price: Double): B = build.copy(price = price)
    def withIsDiscount(isDiscount: Boolean): B = build.copy(isDiscount = isDiscount)
    type B = CorBreakdownItemBuilder
  }

  case class PricingMessageTextBuilder(build: PricingMessageText) {
    def withComponentId(componentId: Int): B = build.copy(componentId = componentId)
    def withCmsIds(cmsIds: Map[Int, CMSId]): B = build.copy(cmsIds = cmsIds)
    type B = PricingMessageTextBuilder
  }

  case class PricingMessageRoomParameterBuilder(build: PricingMessageRoomParameter) {
    def withValue(value: Option[String]): B = build.copy(value = value)
    def withVariableType(variableType: PricingMessageVariableType): B = build.copy(variableType = variableType)
    def withPlaceHolder(placeHolder: String): B = build.copy(placeHolder = placeHolder)
    def withLocalizationType(localizationType: PricingMessageLocalizationType): B =
      build.copy(localizationType = localizationType)
    type B = PricingMessageRoomParameterBuilder
  }

  case class ItemBreakdownBuilder(build: ItemBreakdown) {
    def withDate(date: DateTime): B = build.update(date = Some(date))
    def withBookingRateType(id: BookingRateType): B = build.update(id = id)
    def withBookingItemType(typeId: BookingItemType): B = typeId match {
      case BookingItemTypes.None => build.toNoneItemBreakdown
      case BookingItemTypes.Room => build.toRoomItemBreakdown
      case BookingItemTypes.Surcharge => build.toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
      case BookingItemTypes.CancellationFee => build.toCancellationFeeItemBreakdown
      case BookingItemTypes.ExtraBed => build.toExtraBedItemBreakdown
      case BookingItemTypes.Discount => build.toDiscountItemBreakdown
      case BookingItemTypes.Other => build.toOtherItemBreakdown
      case BookingItemTypes.RtaEss => build.toRtaEssItemBreakdown
      case BookingItemTypes.EssPublishPriceCancellationFee => build.toEssPublishPriceCancellationFeeItemBreakdown
      case BookingItemTypes.SmartFlexEssType => build.toSmartFlexEssTypeItemBreakdown
      case BookingItemTypes.CancelRebook => build.toCancelRebookItemBreakdown
      case BookingItemTypes.ExternalLoyalty => build.toExternalLoyaltyItemBreakdown
      case BookingItemTypes.InternalLoyalty => build.toInternalLoyaltyItemBreakdown
      case BookingItemTypes.CashbackExtrabedRebate => build.toCashbackExtrabedRebateItemBreakdown
      case BookingItemTypes.GiftCardTotalRebate => build.toGiftCardTotalRebateItemBreakdown
      case BookingItemTypes.GiftCardExtrabedRebate => build.toGiftCardExtrabedRebateItemBreakdown
      case BookingItemTypes.CashbackRedemption => build.toCashbackRedemptionItemBreakdown
      case BookingItemTypes.DownliftEx => build.toDownliftExItemBreakdown
    }
    def withQuantity(quantity: Int): B = build.update(quantity = quantity)
    def withUsd(usd: Double): B = build.update(usd = Some(usd))
    def withLocal(local: Double): B = build.update(local = Some(local))
    def withRoomNo(number: Int): B = build.update(roomNo = number)
    def withOption(option: Option[ChargeOption]): B = build.update(option = option)
    def withTaxOrFeeQuantity(quantity: Int): B = build.update(taxOrFeeQuantity = Some(quantity))
    def withTaxFeeId(taxFeeId: Int): B = build.update(taxFeeId = taxFeeId)
    def withSurchargeId(surchargeId: Int): B = build.update(surchargeId = surchargeId)
    def withSubTypeId(subTypeId: Int): B = build.update(subTypeId = Some(subTypeId))

    type B = ItemBreakdownBuilder
  }

  case class CampaignBuilder(build: Campaign) {
    def withCampaignId(id: Int): B = build.copy(campaignId = id)
    def withCid(cid: Int): B = build.copy(cid = cid)
    def withPromotionId(promoId: Int): B = build.copy(promotionId = promoId)
    def withIsValid(isValid: Boolean): B = build.copy(isValid = isValid)
    def withIsSelected(isSelected: Boolean): B = build.copy(isSelected = isSelected)
    def withPromoCode(promotionCode: Option[String]): B = build.copy(promotionCode = promotionCode)
    def withInEligibleReason(reason: Option[IneligiblePromotionReason]): B = build.copy(ineligibleReason = reason)
    def withPromotionResultInfo(promoInfo: Option[PromotionResultInfo]): B = build.copy(promotionResultInfo = promoInfo)
    def withMessages(messages: Option[PromotionInfoMessage]): B = build.copy(messages = messages)
    def withStateIdRequired(isRequired: Option[Boolean]): B = build.copy(isStateIdRequired = isRequired)
    def withCampaignGroupId(campaignGroupId: Option[Int]): B = build.copy(campaignGroupId = campaignGroupId)
    def withAnalyticsInfo(analyticsInfo: Option[Map[String, String]]): B = build.copy(analyticsInfo = analyticsInfo)
    type B = CampaignBuilder
  }

  case class CartBaseRequestBuilder(build: CartBaseRequest) {
    def withToken(token: Option[String]): B = build.copy(token = token)

    def withArrangement(arrangement: Option[Arrangement]): B = build.copy(arrangement = arrangement)
    def withSourceId(sourceId: Option[Int]): B = build.copy(sourceId = sourceId)
    def withMeta(cartMetadata: CartMetadata = aCartMetadata): B = build.copy(meta = Some(cartMetadata))
    type B = CartBaseRequestBuilder
  }

  case class SupplierFinancialDataBuilder(build: SupplierFinancialData) {
    def withProviderFee(providerFee: Option[Double]): B = build.copy(providerFee = providerFee)

    def withProviderOverridesAndCashback(providerOverridesAndCashback: Option[Double]): B =
      build.copy(providerOverridesAndCashback = providerOverridesAndCashback)

    def withHandicap(handicap: Option[Double]): B = build.copy(handicap = handicap)

    type B = SupplierFinancialDataBuilder
  }

  case class PartnerRequestBuilder(build: PartnerRequest) {
    def withPartnerRoomRateType(partnerRoomRateType: Option[Int]): B =
      build.copy(partnerRoomRateType = partnerRoomRateType)
    def withPartnerSurchargeRateType(partnerSurchargeRateType: Option[Int]): B =
      build.copy(partnerSurchargeRateType = partnerSurchargeRateType)
    def withRatePartnerSummaries(ratePartnerSummaries: Option[Boolean]): B =
      build.copy(ratePartnerSummaries = ratePartnerSummaries)
    def withDiscountType(discountType: Option[Int]): B = build.copy(discountType = discountType)
    def withIsExcludedPfFromTax(isExcludedPfFromTax: Option[Boolean]): B =
      build.copy(isExcludedPfFromTax = isExcludedPfFromTax)

    type B = PartnerRequestBuilder
  }

  case class AdditionalRateBuilder(build: AdditionalRate) {
    def withDisplayBasis(displayPrice: DisplayBasis): B = build.copy(prices = displayPrice)
    def withRatePlanStatus(ratePlanStatus: RatePlanStatus): B = build.copy(ratePlanStatus = Some(ratePlanStatus))
    type B = AdditionalRateBuilder
  }

  case class ExternalLoyaltyRequestBuilder(build: ExternalLoyaltyRequest) {
    def withPartnerClaimToken(partnerClaimToken: Option[String]): B = build.copy(partnerClaimToken = partnerClaimToken)
    type B = ExternalLoyaltyRequestBuilder
  }

  case class BookingRoomBuilder(build: BookingRoom) {
    def withUid(uid: String) = build.copy(uid = uid)
    def withBookingPayment(payment: Option[BookingPayment]): B = build.copy(bookingPayment = payment)
    def withCapacity(capacity: Capacity): B = build.copy(capacity = capacity)
    type B = BookingRoomBuilder
  }

  case class LoyaltyDisplayPriceBuilder(build: LoyaltyDisplayPrice) {
    def withDisplayPrice(displayPrice: DisplayPrice): B = build.copy(displayPrice = displayPrice)
    def withPointsToDisplayPrice(pointsToDisplayPrice: DisplayPrice): B =
      build.copy(pointsToDisplayPrice = pointsToDisplayPrice)
    def withPoints(points: Double): B = build.copy(points = points)
    def withPointsToEarn(pointsToEarn: Double): B = build.copy(pointsToEarn = pointsToEarn)
    type B = LoyaltyDisplayPriceBuilder
  }

  case class ExternalLoyaltyPricingBuilder(build: ExternalLoyaltyPricing) {
    def withPerBook(perBook: LoyaltyDisplayPrice): B = build.copy(perBook = perBook)
    def withPerRoomPerNight(perRoomPerNight: LoyaltyDisplayPrice): B = build.copy(perRoomPerNight = perRoomPerNight)
    def withPerNight(perNight: LoyaltyDisplayPrice): B = build.copy(perNight = perNight)
    def withRewardUnit(rewardUnit: Option[LoyaltyRewardUnit]): B = build.copy(rewardUnit = rewardUnit)
    type B = ExternalLoyaltyPricingBuilder
  }

  case class PriceCalculationRequestBuilder(build: PriceCalculationRequest) {
    def withRoomRates(roomRates: Seq[models.pricecalculation.RoomRateCategory]): B =
      build.copy(propertyOffer = build.propertyOffer.copy(roomRates = roomRates))

    def withCommission(commissions: Map[Int, models.pricecalculation.Commission]): B =
      build.copy(propertyOffer = build.propertyOffer.copy(commissions = commissions))

    def withIsMockYCSSupplierCCMapping(isMockYCSSupplierCCMapping: Option[Boolean]): B =
      build.copy(isMockYCSSupplierCCMapping = isMockYCSSupplierCCMapping)

    type B = PriceCalculationRequestBuilder
  }

  case class RoomRateCategoryBuilder(build: models.pricecalculation.RoomRateCategory) {
    def withChannelRates(channelRates: Seq[models.pricecalculation.ChannelRate]): B =
      build.copy(channelRates = channelRates)

    type B = RoomRateCategoryBuilder
  }

  case class ChannelRateBuilder(build: models.pricecalculation.ChannelRate) {
    def withChannelId(channelId: Int): B = build.copy(channelId = channelId)

    def withSupplierRateCode(supplierRateCode: String): B = build.copy(supplierRateCode = supplierRateCode)

    def withCommissionPerDay(commissionPerDay: Map[Int, Identifiers]): B =
      build.copy(commissionPerDay = commissionPerDay)

    def withPrices(prices: Seq[models.pricecalculation.PriceDaily]): B = build.copy(prices = prices)

    def withChannelDiscountPerDay(channelDiscountPerDay: Option[Map[Int, Double]]): B =
      build.copy(channelDiscountPerDay = channelDiscountPerDay)

    def withChannelDiscountRateLoadType(channelDiscountRateLoadType: Option[proto.PriceType]): B =
      build.copy(channelDiscountRateLoadType = channelDiscountRateLoadType)

    def withRateLoadType(rateLoadType: proto.PriceType): B = build.copy(rateLoadType = rateLoadType)

    type B = ChannelRateBuilder
  }

  case class ChannelDiscountCalculationRequestBuilder(build: ChannelDiscountCalculationRequest) {
    def withRateLoadType(rateLoadType: PriceType): B = build.copy(rateLoadType = rateLoadType)

    def withTaxes(taxes: Seq[ChannelDiscountTaxRequest]): B = build.copy(taxes = taxes)

    type B = ChannelDiscountCalculationRequestBuilder
  }

  case class PostBookingContextBuilder(build: PostBookingContext) {
    def withWhiteLabelId(whiteLabelId: Int) = build.copy(whiteLabelId = whiteLabelId)
    def withActionType(actionType: PostBookingActionType) = build.copy(actionType = actionType)

    type B = PostBookingContextBuilder
  }

  case class PostBookingInfoBuilder(build: PostBookingInfo) {
    def withCxlPolicyCode(cancellationPolicyCode: String) = build.copy(cancellationPolicyCode = cancellationPolicyCode)
    def withCheckInDate(checkInDate: DateTime) = build.copy(checkInDate = checkInDate)
    def withCheckOutDate(checkOutDate: DateTime) = build.copy(checkOutDate = checkOutDate)
    def withDmcId(dmcId: Int) = build.copy(dmcId = dmcId)
    def withPostBookingPayment(payment: PostBookingPayment) = build.copy(payment = payment)
    def withPaymentModel(paymentModel: models.pricing.enums.PaymentModel) = build.copy(paymentModel = paymentModel)

    type B = PostBookingInfoBuilder
  }
  case class PostBookingAffiliateInfoBuilder(build: PostBookingAffiliateInfo) {
    def withSiteId(siteId: Int) = build.copy(siteId = siteId)
    def withAffiliateModelType(model: AffiliateModelType) = build.copy(model = model)
    def withAffiliatePaymentType(paymentMethod: AffiliatePaymentType) = build.copy(paymentMethod = paymentMethod)

    type B = PostBookingAffiliateInfoBuilder

  }

  case class PostBookingCancellationBuilder(build: PostBookingCancellation) {
    def withReason(reason: Option[CancellationReasonType]) = build.copy(reason = reason)
    def withCancelBy(cancelBy: CancellationByType) = build.copy(cancelBy = cancelBy)
    def withOptionType(optionType: CancellationOptionType) = build.copy(optionType = optionType)
    def withFeeWaiveRatio(feeWaiveRatio: Option[BigDecimal]) = build.copy(feeWaiveRatio = feeWaiveRatio)
    def withRefundType(refundType: RefundOptionType) = build.copy(refundType = refundType)
    def withAllotmentReject(isAllotmentReject: Boolean) = build.copy(isAllotmentReject = isAllotmentReject)
    def withOverriddenCancellation(overriddenCancellation: Option[PostBookingOverrideCancellation]) =
      build.copy(overriddenCancellation = overriddenCancellation)
    def withCancellationDateAtHotelTimeZone(cancellationDateAtHotelTimeZone: DateTime) =
      build.copy(cancellationDateAtHotelTimeZone = cancellationDateAtHotelTimeZone)
    def withCheckInDateAtHotelTimeZone(checkInDateAtHotelTimeZone: DateTime) =
      build.copy(checkInDateAtHotelTimeZone = checkInDateAtHotelTimeZone)

    type B = PostBookingCancellationBuilder

  }
  case class PostBookingRequestBuilder(build: PostBookingRequest) {
    def withContext(context: PostBookingContext) = build.copy(context = context)

    def withBookingInfo(bookingInfo: Option[PostBookingInfo]) = build.copy(bookingInfo = bookingInfo)

    def withPostBookingAffiliateInfo(affiliateInfo: Option[PostBookingAffiliateInfo]) =
      build.copy(affiliateInfo = affiliateInfo)

    def withPostBookingCancellation(cancellationRequest: Option[PostBookingCancellation]) =
      build.copy(cancellationRequest = cancellationRequest)

    def withFinancialBreakdown(financialBreakdown: List[PostBkgBookingItemBreakdown]) =
      build.copy(financialBreakdown = financialBreakdown)

    def withActionType(actionType: PostBookingActionType) =
      build.copy(context = build.context.withActionType(actionType).build)

    type B = PostBookingRequestBuilder
  }

  case class PostBookingPriceBuilder(build: PostBookingPrice) {
    def withUsd(usd: BigDecimal) = build.copy(usd = usd)

    def withRequest(request: BigDecimal) = build.copy(request = request)

    def withLocal(local: BigDecimal) = build.copy(local = local)

    type B = PostBookingPriceBuilder

  }

  case class PostBookingSummaryBuilder(build: PostBookingSummary) {
    def withCancellationFee(cancellationFee: PostBookingPrice) = build.copy(cancellationFee = cancellationFee)

    def withRefSellInclusive(refSellInclusive: PostBookingPrice) = build.copy(refSellInclusive = refSellInclusive)

    def withNetInclusive(netInclusive: PostBookingPrice) = build.copy(netInclusive = netInclusive)

    type B = PostBookingSummaryBuilder

  }

  case class PostBookingSearchRequestBuilder(build: PostBookingSearchRequest) {
    def withPropertySearchRequest(searchRequest: Option[PropertySearchRequest]) =
      build.copy(searchRequest = searchRequest)

    def withPostBookingRequest(postBookingRequest: Option[PostBookingRequest]) =
      build.copy(postBookingRequest = postBookingRequest)

    type B = PostBookingSearchRequestBuilder

  }

  case class PostBookingCancellationRequestBuilder(buildBase: PostBookingRequest) {
    private def getContext(context: PostBookingContext): CancellationContext =
      CancellationContext(whiteLabelId = context.whiteLabelId)

    private def getCancellationInfo(cancellationInfo: Option[PostBookingInfo]): Option[CancellationBookingInfo] =
      cancellationInfo.map(info =>
        CancellationBookingInfo(
          checkInDate = info.checkInDate,
          checkOutDate = info.checkOutDate,
          dmcId = info.dmcId,
          cancellationPolicyCode = info.cancellationPolicyCode,
          paymentModel = info.paymentModel,
          isAgodaAgencyNoCancellationFee = info.isAgodaAgencyNoCancellationFee,
          bookingToken = "",
        ))

    private def getOverriddenCancellation(
      overriddenCancellation: Option[PostBookingOverrideCancellation]): Option[OverrideCancellation] =
      overriddenCancellation.map(overrideCxl =>
        OverrideCancellation(
          supplierInfo = overrideCxl.supplierInfo.map(supplierInfo =>
            CancellationSupplierInfo(
              costUsd = supplierInfo.costUsd,
              costLocal = supplierInfo.costLocal,
              taxUsd = supplierInfo.taxUsd,
              taxLocal = supplierInfo.taxLocal,
              feeUsd = supplierInfo.feeUsd,
              feeLocal = supplierInfo.feeLocal,
              currency = supplierInfo.currency,
            )),
          customCalculation = overrideCxl.customCalculation.map(customCalculation =>
            CustomCancellationCalculation(jtbSetting = customCalculation.jtbSetting.map(settings =>
              JtbCancellationCalculationSetting(calculationMethod = settings.calculationMethod,
                                                targetItemId = settings.targetItemId,
                                                isCalculatedFeeFromUSD = settings.isCalculatedFeeFromUSD)))),
        ))

    private def getPostBookingCancellation(cancellationRequest: Option[PostBookingCancellation]): CancellationDetails =
      cancellationRequest
        .map(cancellationRequest =>
          CancellationDetails(
            reason = cancellationRequest.reason,
            cancelBy = cancellationRequest.cancelBy,
            optionType = cancellationRequest.optionType,
            feeWaiveRatio = cancellationRequest.feeWaiveRatio,
            overriddenCancellation = getOverriddenCancellation(cancellationRequest.overriddenCancellation),
            cancellationDateAtHotelTimeZone = cancellationRequest.cancellationDateAtHotelTimeZone,
            checkInDateAtHotelTimeZone = cancellationRequest.checkInDateAtHotelTimeZone,
          ))
        .getOrElse(CancellationDetails(
          reason = None,
          cancelBy = CancellationByType.Unknown,
          optionType = CancellationOptionType.AutoCalculate,
          feeWaiveRatio = None,
          overriddenCancellation = None,
          cancellationDateAtHotelTimeZone = DateTime.now(),
          checkInDateAtHotelTimeZone = DateTime.now(),
        ))

    private def getFinancialBreakdown(
      financialBreakdown: List[PostBkgBookingItemBreakdown]): List[CancellationItemBreakdown] =
      financialBreakdown.map(fb =>
        CancellationItemBreakdown(
          chargeDate = fb.chargeDate,
          exchangeRate = fb.exchangeRate,
          itemId = BookingRateTypes.getRateType(fb.itemId.toInt),
          localAmount = fb.localAmount,
          usdAmount = fb.usdAmount,
          localCurrency = fb.localCurrency,
          quantity = fb.quantity,
          typeId = BookingItemTypes.getBookingItemType(fb.typeId.toInt),
          roomNo = fb.roomNo,
          option = fb.option,
          taxProtoTypeId = fb.taxProtoTypeId,
        ))

    private def getAffiliateInfo(affiliateInfo: Option[PostBookingAffiliateInfo]): Option[CancellationAffiliateInfo] =
      affiliateInfo.map(info =>
        CancellationAffiliateInfo(
          siteId = info.siteId,
          model = info.model,
          paymentMethod = info.paymentMethod,
        ))

    def build: CancellationRequest = CancellationRequest(
      context = getContext(buildBase.context),
      cancellationBookingInfo = getCancellationInfo(buildBase.bookingInfo),
      financialBreakdown = getFinancialBreakdown(buildBase.financialBreakdown),
      affiliateInfo = getAffiliateInfo(buildBase.affiliateInfo),
      cancellationDetails = getPostBookingCancellation(buildBase.cancellationRequest),
    )

    def withContext(context: PostBookingContext): CancellationRequest = build.copy(context = getContext(context))

    def withBookingInfo(bookingInfoOpt: Option[PostBookingInfo]): CancellationRequest =
      build.copy(cancellationBookingInfo = getCancellationInfo(bookingInfoOpt))

    def withPostBookingAffiliateInfo(affiliateInfo: Option[PostBookingAffiliateInfo]): CancellationRequest =
      build.copy(affiliateInfo = getAffiliateInfo(affiliateInfo))

    def withPostBookingCancellation(cancellationRequestOpt: Option[PostBookingCancellation]): CancellationRequest =
      build.copy(cancellationDetails = getPostBookingCancellation(cancellationRequestOpt))

    def withFinancialBreakdown(financialBreakdowns: List[PostBkgBookingItemBreakdown]): CancellationRequest =
      build.copy(financialBreakdown = getFinancialBreakdown(financialBreakdowns))

    type B = PostBookingCancellationRequestBuilder
  }

  /* =================================
   *        IMPLCIT CONVERSIONS
   * ================================= */

  implicit def toDisplayPrice(builder: DisplayPriceBuilder): DisplayPrice = builder.build
  implicit def toDisplayPriceBuilder(build: DisplayPrice): DisplayPriceBuilder = DisplayPriceBuilder(build)

  implicit def toDisplayBasis(builder: DisplayBasisBuilder): DisplayBasis = builder.build
  implicit def toDisplayBasisBuilder(build: DisplayBasis): DisplayBasisBuilder = DisplayBasisBuilder(build)

  implicit def toDisplayPricePercentage(builder: DisplayPricePercentageBuilder): DisplayPricePercentage = builder.build
  implicit def toDisplayPricePercentageBuilder(build: DisplayPricePercentage): DisplayPricePercentageBuilder =
    DisplayPricePercentageBuilder(build)

  implicit def toDisplayBasisInt(builder: DisplayBasisIntBuilder): DisplayBasisInt = builder.build
  implicit def toDisplayBasisIntBuilder(build: DisplayBasisInt): DisplayBasisIntBuilder = DisplayBasisIntBuilder(build)

  implicit def toSummaryElement(builder: SummaryElementBuilder): SummaryElement = builder.build
  implicit def toSummaryElementBuilder(build: SummaryElement): SummaryElementBuilder = SummaryElementBuilder(build)

  implicit def toDisplaySummary(builder: DisplaySummaryBuilder): DisplaySummary = builder.build
  implicit def toDisplaySummaryBuilder(build: DisplaySummary): DisplaySummaryBuilder = DisplaySummaryBuilder(build)

  implicit def toPricing(builder: PricingBuilder): Pricing = builder.build
  implicit def toPricingBuilder(build: Pricing): PricingBuilder = PricingBuilder(build)

  implicit def toPackagingBuilder(build: Packaging): PackagingBuilder = PackagingBuilder(build)
  implicit def toPackagePricingBuilder(build: PackagePricing): PackagePricingBuilder = PackagePricingBuilder(build)

  implicit def toClientInfo(builder: ClientInfoBuilder): ClientInfo = builder.build
  implicit def toClientInfoBuilder(build: ClientInfo): ClientInfoBuilder = ClientInfoBuilder(build)

  implicit def toSessionInfo(builder: SessionInfoBuilder): SessionInfo = builder.build
  implicit def toSessionInfoBuilder(build: SessionInfo): SessionInfoBuilder = SessionInfoBuilder(build)

  implicit def toContextRequest(builder: ContextRequestBuilder): ContextRequest = builder.build
  implicit def toContextRequestBuilder(build: ContextRequest): ContextRequestBuilder = ContextRequestBuilder(build)

  implicit def toOccupancyRequestBuilder(builder: OccupancyRequestBuilder): OccupancyRequest = builder.build
  implicit def toOccupancyRequest(build: OccupancyRequest): OccupancyRequestBuilder = OccupancyRequestBuilder(build)

  implicit def toFilterRequest(builder: FilterRequestBuilder): FilterRequest = builder.build
  implicit def toFilterRequestBuilder(build: FilterRequest): FilterRequestBuilder = FilterRequestBuilder(build)

  implicit def toDiscountRequest(builder: DiscountRequestBuilder): DiscountRequest = builder.build
  implicit def toDiscountRequestBuilder(build: DiscountRequest): DiscountRequestBuilder = DiscountRequestBuilder(build)

  implicit def toFencedRate(builder: FencedRateBuilder): FencedRate = builder.build
  implicit def toFencedRateBuilder(build: FencedRate): FencedRateBuilder = FencedRateBuilder(build)

  implicit def toFencedRatePair(builder: FencedRatePairBuilder): FencedRatePair = builder.build
  implicit def toFencedRatePairBuilder(build: FencedRatePair): FencedRatePairBuilder = FencedRatePairBuilder(build)

  implicit def toDetailRequest(builder: DetailRequestBuilder): DetailRequest = builder.build
  implicit def toDetailRequestBuilder(build: DetailRequest): DetailRequestBuilder = DetailRequestBuilder(build)

  implicit def toFeatureRequest(builder: FeatureRequestBuilder): FeatureRequest = builder.build
  implicit def toFeatureRequestBuilder(build: FeatureRequest): FeatureRequestBuilder = FeatureRequestBuilder(build)

  implicit def toPricingRequest(builder: PricingRequestBuilder): PricingRequest = builder.build
  implicit def toPricingRequestBuilder(build: PricingRequest): PricingRequestBuilder = PricingRequestBuilder(build)

  implicit def toPropertySearchRequest(builder: PropertySearchRequestBuilder): PropertySearchRequest = builder.build
  implicit def toPropertySearchRequestBuilder(build: PropertySearchRequest): PropertySearchRequestBuilder =
    PropertySearchRequestBuilder(build)

  implicit def toOccInfo(builder: OccInfoBuilder): OccInfo = builder.build
  implicit def toOccInfoBuilder(build: OccInfo): OccInfoBuilder = OccInfoBuilder(build)

  implicit def toChildren(builder: ChildrenBuilder): Children = builder.build
  implicit def toChildrenBuilder(build: Children): ChildrenBuilder = ChildrenBuilder(build)

  implicit def toRateModel(builder: RateModelBuilder): RateModel = builder.build
  implicit def toRateModelBuilder(build: RateModel): RateModelBuilder = RateModelBuilder(build)

  implicit def toRatePlan(builder: RatePlanBuilder): RatePlan = builder.build
  implicit def toRatePlanBuilder(build: RatePlan): RatePlanBuilder = RatePlanBuilder(build)

  implicit def toChannel(builder: ChannelBuilder): Channel = builder.build
  implicit def toChannelBuilder(build: Channel): ChannelBuilder = ChannelBuilder(build)

  implicit def toCapacity(builder: CapacityBuilder): Capacity = builder.build
  implicit def toCapacityBuilder(build: Capacity): CapacityBuilder = CapacityBuilder(build)

  implicit def toTaxReceipt(builder: TaxReceiptBuilder): TaxReceipt = builder.build
  implicit def toTaxReceiptBuilder(build: TaxReceipt): TaxReceiptBuilder = TaxReceiptBuilder(build)

  implicit def toPayLater(builder: PaylaterBuilder): PayLater = builder.build
  implicit def toPayLaterBuilder(build: PayLater): PaylaterBuilder = PaylaterBuilder(build)

  implicit def toCancellation(builder: CancellationBuilder): Cancellation = builder.build
  implicit def toCancellationBuilder(build: Cancellation): CancellationBuilder = CancellationBuilder(build)

  implicit def toCreditCard(builder: CreditCardBuilder): CreditCard = builder.build
  implicit def toCreditCardBuilder(build: CreditCard): CreditCardBuilder = CreditCardBuilder(build)

  implicit def toPrePayment(builder: PrePaymentBuilder): PrePayment = builder.build
  implicit def toPrePaymentBuilder(build: PrePayment): PrePaymentBuilder = PrePaymentBuilder(build)

  implicit def toPayAtHotel(builder: PayAtHotelBuilder): PayAtHotel = builder.build
  implicit def toPayAtHotelBuilder(build: PayAtHotel): PayAtHotelBuilder = PayAtHotelBuilder(build)

  implicit def toPayment(builder: PaymentBuilder): Payment = builder.build
  implicit def toPaymentBuilder(build: Payment): PaymentBuilder = PaymentBuilder(build)

  implicit def toRoom(builder: RoomBuilder): Room = builder.build
  implicit def toRoomBuilder(build: Room): RoomBuilder = RoomBuilder(build)

  implicit def toCharge(builder: ChargeBuilder): Charge = builder.build
  implicit def toChargeBuilder(build: Charge): ChargeBuilder = ChargeBuilder(build)

  implicit def toDaily(builder: DailyBuilder): Daily = builder.build
  implicit def toDailyBuilder(build: Daily): DailyBuilder = DailyBuilder(build)

  implicit def toDisplayCharge(builder: DisplayChargeBuilder): DisplayCharge = builder.build
  implicit def toDisplayChargeBuilder(build: DisplayCharge): DisplayChargeBuilder = DisplayChargeBuilder(build)
  implicit def toSummaryPrice(build: SummaryElement): PriceSummaryElementBuilder = PriceSummaryElementBuilder(build)

  implicit def toPointsMax(builder: PointsMaxBuilder): PointsMax = builder.build
  implicit def toPointsMaxBuilder(build: PointsMax): PointsMaxBuilder = PointsMaxBuilder(build)

  implicit def toProperties(builder: PropertiesBuilder): Properties = builder.build
  implicit def toPropertiesBuilder(build: Properties): PropertiesBuilder = PropertiesBuilder(build)

  implicit def toHotel(builder: HotelBuilder): Hotel = builder.build
  implicit def toHotelBuilder(build: Hotel): HotelBuilder = HotelBuilder(build)

  implicit def toHotelFeatures(builder: HotelFeaturesBuilder): HotelFeatures = builder.build
  implicit def toHotelFeaturesBuilder(build: HotelFeatures): HotelFeaturesBuilder = HotelFeaturesBuilder(build)

  implicit def toHotelAggregatedOptions(builder: HotelAggregatedOptionsBuilder): HotelAggregatedOptions = builder.build
  implicit def toHotelAggregatedOptionsBuilder(build: HotelAggregatedOptions): HotelAggregatedOptionsBuilder =
    HotelAggregatedOptionsBuilder(build)

  implicit def toHotelAggregatedPayment(builder: HotelAggregatedPaymentBuilder): HotelAggregatedPayment = builder.build
  implicit def toHotelAggregatedPaymentBuilder(build: HotelAggregatedPayment): HotelAggregatedPaymentBuilder =
    HotelAggregatedPaymentBuilder(build)

  implicit def toBenefit(builder: BenefitBuilder): Benefit = builder.build
  implicit def toBenefitBuilder(build: Benefit): BenefitBuilder = BenefitBuilder(build)

  implicit def toBookingRequest(builder: BookingRequestBuilder): BookingRequest = builder.build
  implicit def toBookingRequestBuilder(build: BookingRequest): BookingRequestBuilder = BookingRequestBuilder(build)

  implicit def toReBookingRequest(builder: ReBookingRequestBuilder): ReBookingRequest = builder.build
  implicit def toReBookingRequestBuilder(build: ReBookingRequest): ReBookingRequestBuilder =
    ReBookingRequestBuilder(build)

  implicit def toBackupRoomCriteria(builder: BackupRoomCriteriaBuilder): BackupRoomCriteriaBuilder = builder.build
  implicit def toBackupRoomCriteriaBuilder(build: BackupRoomCriteria): BackupRoomCriteriaBuilder =
    BackupRoomCriteriaBuilder(build)

  implicit def toRoomSelectionRequest(builder: RoomSelectionRequestBuilder): RoomSelectionRequestBuilder = builder.build
  implicit def toRoomSelectionRequestBuilder(build: RoomSelectionRequest): RoomSelectionRequestBuilder =
    RoomSelectionRequestBuilder(build)

  implicit def toPartners(builder: PartnersBuilder): PartnersBuilder = builder.build
  implicit def toPartnersBuilder(build: Partners): PartnersBuilder = PartnersBuilder(build)

  implicit def toPartnersPrices(builder: PartnersPricesBuilder): PartnersPricesBuilder = builder.build
  implicit def toPartnersPricesBuilder(build: PartnersPrices): PartnersPricesBuilder = PartnersPricesBuilder(build)

  implicit def toPartnersDisplayPrice(builder: PartnersDisplayPriceBuilder): PartnersDisplayPriceBuilder = builder.build
  implicit def toPartnersDisplayPriceBuilder(build: PartnersDisplayPrice): PartnersDisplayPriceBuilder =
    PartnersDisplayPriceBuilder(build)

  implicit def toPartnersCharge(builder: PartnersChargeBuilder): PartnersChargeBuilder = builder.build
  implicit def toPartnersChargeBuilder(build: PartnersCharge): PartnersChargeBuilder = PartnersChargeBuilder(build)
  implicit def toPartnersSurchargesBuilder(build: PartnersSurcharges): PartnersSurchargesBuilder =
    PartnersSurchargesBuilder(build)
  implicit def toPartnerTaxBreakdown(build: PartnerTaxBreakdown): PartnerTaxBreakdownBuilder =
    PartnerTaxBreakdownBuilder(build)

  implicit def toPartnersPriceBreakdown(builder: PartnersPriceBreakdownBuilder): PartnersPriceBreakdownBuilder =
    builder.build
  implicit def toPartnersPriceBreakdownBuilder(build: PartnersPriceBreakdown): PartnersPriceBreakdownBuilder =
    PartnersPriceBreakdownBuilder(build)

  implicit def toPricingMessage(builder: PricingMessageBuilder): PricingMessage = builder.build
  implicit def toPricingMessageBuilder(build: PricingMessage): PricingMessageBuilder = PricingMessageBuilder(build)

  implicit def toGiftCard(builder: GiftCardBuilder): GiftCard = builder.build
  implicit def toGiftCardBuilder(build: GiftCard): GiftCardBuilder = GiftCardBuilder(build)

  implicit def toConsolidatedAppliedDiscount(builder: ConsolidatedAppliedDiscountBuilder): ConsolidatedAppliedDiscount =
    builder.build
  implicit def toConsolidatedAppliedDiscountBuilder(
    build: ConsolidatedAppliedDiscount): ConsolidatedAppliedDiscountBuilder = ConsolidatedAppliedDiscountBuilder(build)
  implicit def toCashback(builder: CashbackBuilder): Cashback = builder.build
  implicit def toCashbackBuilder(build: Cashback): CashbackBuilder = CashbackBuilder(build)

  implicit def toDiscountMessage(builder: DiscountMessageBuilder): DiscountMessageBuilder = builder.build
  implicit def toDiscountMessageBuilder(build: DiscountMessage): DiscountMessageBuilder = DiscountMessageBuilder(build)

  implicit def toRoomBundles(builder: RoomBundlesBuilder): RoomBundles = builder.build
  implicit def toRoomBundlesBuilder(build: RoomBundles): RoomBundlesBuilder = RoomBundlesBuilder(build)

  implicit def toBundleSegment(builder: BundleSegmentBuilder): BundleSegment = builder.build
  implicit def toBundleSegmentBuilder(build: BundleSegment): BundleSegmentBuilder = BundleSegmentBuilder(build)

  implicit def toExtraInformation(builder: ExtraInformationBuilder): ExtraInformationBuilder = builder.build
  implicit def toExtraInformationBuilder(build: ExtraInformation): ExtraInformationBuilder =
    ExtraInformationBuilder(build)

  implicit def toSFPromotion(builder: SFPromotionBuilder): SFPromotionBuilder = builder.build
  implicit def toSFPromotionBuilder(build: Promotion): SFPromotionBuilder = SFPromotionBuilder(build)

  implicit def toPseudoCoupon(builder: PseudoCouponBuilder): PseudoCoupon = builder.build
  implicit def toPseudoCouponBuilder(build: PseudoCoupon): PseudoCouponBuilder = PseudoCouponBuilder(build)

  implicit def toCorBreakdown(builder: CorBreakdownBuilder): CorBreakdown = builder.build
  implicit def toCorBreakdownBuilder(build: CorBreakdown): CorBreakdownBuilder = CorBreakdownBuilder(build)

  implicit def toCorBreakdownItem(builder: CorBreakdownItemBuilder): CorBreakdownItem = builder.build
  implicit def toCorBreakdownItemBuilder(build: CorBreakdownItem): CorBreakdownItemBuilder =
    CorBreakdownItemBuilder(build)

  implicit def toPricingMessageText(builder: PricingMessageTextBuilder): PricingMessageText = builder.build
  implicit def toPricingMessageTextBuilder(build: PricingMessageText): PricingMessageTextBuilder =
    PricingMessageTextBuilder(build)

  implicit def toPricingMessageRoomParameter(builder: PricingMessageRoomParameterBuilder): PricingMessageRoomParameter =
    builder.build
  implicit def toPricingMessageRoomParameterBuilder(
    build: PricingMessageRoomParameter): PricingMessageRoomParameterBuilder = PricingMessageRoomParameterBuilder(build)

  implicit def toAPSPeekBuilder(build: APSPeek): APSPeekBuilder = APSPeekBuilder(build)
  implicit def toAPSPeek(builder: APSPeekBuilder): APSPeek = builder.build

  implicit def toPackagePriceAndSavingBuilder(build: PackagePriceAndSaving): PackagePriceAndSavingBuilder =
    PackagePriceAndSavingBuilder(build)
  implicit def toPackagePriceAndSaving(builder: PackagePriceAndSavingBuilder): PackagePriceAndSaving = builder.build

  implicit def toPackageProductDisplayPriceBuilder(
    build: PackageProductDisplayPrice): PackageProductDisplayPriceBuilder = PackageProductDisplayPriceBuilder(build)
  implicit def toPackageProductDisplayPrice(builder: PackageProductDisplayPriceBuilder): PackageProductDisplayPrice =
    builder.build

  implicit def toPriceWithChannelBuilder(build: PriceWithChannel): PriceWithChannelBuilder =
    PriceWithChannelBuilder(build)
  implicit def toPriceWithChannel(builder: PriceWithChannelBuilder): PriceWithChannel = builder.build

  implicit def toAdditionalPriceDisplayBasisBuilder(
    build: AdditionalPriceDisplayBasis): AdditionalPriceDisplayBasisBuilder = AdditionalPriceDisplayBasisBuilder(build)
  implicit def toAdditionalPriceDisplayBasis(builder: AdditionalPriceDisplayBasisBuilder): AdditionalPriceDisplayBasis =
    builder.build

  implicit def toPromotionPricePeekBuilder(build: PromotionPricePeek): PromotionPricePeekBuilder =
    PromotionPricePeekBuilder(build)
  implicit def toPromotionPricePeek(builder: PromotionPricePeekBuilder): PromotionPricePeek = builder.build

  implicit def toSuggestedRoomBuilder(build: SuggestedRoom): SuggestedRoomBuilder = SuggestedRoomBuilder(build)
  implicit def toSuggestedRoom(builder: SuggestedRoomBuilder): SuggestedRoom = builder.build

  implicit def toPromotionPricePeekBuilder(build: MultiRoomSuggestion): MultiRoomSuggestionBuilder =
    MultiRoomSuggestionBuilder(build)
  implicit def toMultiRoomSuggestion(builder: MultiRoomSuggestionBuilder): MultiRoomSuggestion = builder.build

  implicit def toItemBreakdownBuilder(build: ItemBreakdown): ItemBreakdownBuilder = ItemBreakdownBuilder(build)
  implicit def toItemBreakdown(builder: ItemBreakdownBuilder): ItemBreakdown = builder.build

  implicit def toPricingPropertiesRequestBuilder(build: PricingPropertiesRequest): PricingPropertiesRequestBuilder =
    PricingPropertiesRequestBuilder(build)
  implicit def toPricingPropertiesRequest(builder: PricingPropertiesRequestBuilder): PricingPropertiesRequest =
    builder.build

  implicit def toCampaignBuilder(build: Campaign): CampaignBuilder = CampaignBuilder(build)
  implicit def toCampaign(builder: CampaignBuilder): Campaign = builder.build

  implicit def toCartBaseRequestBuilder(build: CartBaseRequest): CartBaseRequestBuilder = CartBaseRequestBuilder(build)
  implicit def toCartBaseRequest(builder: CartBaseRequestBuilder): CartBaseRequest = builder.build

  implicit def toSupplierFinancialDataBuilder(build: SupplierFinancialData): SupplierFinancialDataBuilder =
    SupplierFinancialDataBuilder(build)
  implicit def toSupplierFinancialData(builder: SupplierFinancialDataBuilder): SupplierFinancialData = builder.build

  implicit def toPartnerRequestBuilder(build: PartnerRequest): PartnerRequestBuilder = PartnerRequestBuilder(build)
  implicit def toPartnerRequest(builder: PartnerRequestBuilder): PartnerRequest = builder.build

  implicit def toPackageAdditionalPriceAndSaving(
    builder: PackageAdditionalPriceAndSavingBuilder): PackageAdditionalPriceAndSaving = builder.build
  implicit def toPackageAdditionalPriceAndSavingBuilder(
    build: PackageAdditionalPriceAndSaving): PackageAdditionalPriceAndSavingBuilder =
    PackageAdditionalPriceAndSavingBuilder(build)

  implicit def toPackageDetailedSavingBuilder(builder: PackageDetailedSavingBuilder): PackageDetailedSaving =
    builder.build
  implicit def toPackageDetailedSaving(build: PackageDetailedSaving): PackageDetailedSavingBuilder =
    PackageDetailedSavingBuilder(build)

  implicit def toAdditionalRate(builder: AdditionalRateBuilder): AdditionalRate = builder.build
  implicit def toAdditionalRateBuilder(build: AdditionalRate): AdditionalRateBuilder = AdditionalRateBuilder(build)

  implicit def toExternalLoyaltyRequest(builder: ExternalLoyaltyRequestBuilder): ExternalLoyaltyRequest = builder.build
  implicit def toExternalLoyaltyRequestBuilder(build: ExternalLoyaltyRequest): ExternalLoyaltyRequestBuilder =
    ExternalLoyaltyRequestBuilder(build)

  implicit def toBookingRoom(builder: BookingRoomBuilder): BookingRoom = builder.build
  implicit def toBookingRoomBuilder(build: BookingRoom): BookingRoomBuilder = BookingRoomBuilder(build)

  implicit def toLoyaltyDisplayPrice(builder: LoyaltyDisplayPriceBuilder): LoyaltyDisplayPrice = builder.build
  implicit def toLoyaltyDisplayPriceBuilder(build: LoyaltyDisplayPrice): LoyaltyDisplayPriceBuilder =
    LoyaltyDisplayPriceBuilder(build)

  implicit def toExternalLoyaltyPricing(builder: ExternalLoyaltyPricingBuilder): ExternalLoyaltyPricing = builder.build
  implicit def toExternalLoyaltyPricingBuilder(build: ExternalLoyaltyPricing): ExternalLoyaltyPricingBuilder =
    ExternalLoyaltyPricingBuilder(build)

  implicit def toPriceFreeze(builder: PriceFreezeBuilder): PriceFreeze = builder.build
  implicit def toPriceFreezeBuilder(build: PriceFreeze): PriceFreezeBuilder = PriceFreezeBuilder(build)

  implicit def toPriceCalculationRequest(builder: PriceCalculationRequestBuilder): PriceCalculationRequest =
    builder.build
  implicit def toPriceCalculationRequestBuilder(build: PriceCalculationRequest): PriceCalculationRequestBuilder =
    PriceCalculationRequestBuilder(build)

  implicit def toRoomRateCategory(builder: RoomRateCategoryBuilder): models.pricecalculation.RoomRateCategory =
    builder.build
  implicit def toRoomRateCategoryBuilder(build: models.pricecalculation.RoomRateCategory): RoomRateCategoryBuilder =
    RoomRateCategoryBuilder(build)

  implicit def toChannelRate(builder: ChannelRateBuilder): models.pricecalculation.ChannelRate = builder.build
  implicit def toChannelRateBuilder(build: models.pricecalculation.ChannelRate): ChannelRateBuilder =
    ChannelRateBuilder(build)

  implicit def toChannelDiscountCalculationRequest(
    builder: ChannelDiscountCalculationRequestBuilder,
  ): ChannelDiscountCalculationRequest = builder.build

  implicit def toChannelDiscountCalculationRequestBuilder(
    build: ChannelDiscountCalculationRequest,
  ): ChannelDiscountCalculationRequestBuilder = ChannelDiscountCalculationRequestBuilder(build)

  implicit def toPostBookingAffiliateInfoBuilder(build: PostBookingAffiliateInfo): PostBookingAffiliateInfoBuilder =
    PostBookingAffiliateInfoBuilder(build)

  implicit def toPostBookingContextBuilder(build: PostBookingContext): PostBookingContextBuilder =
    PostBookingContextBuilder(build)
  implicit def toPostBookingInfoBuilder(build: PostBookingInfo): PostBookingInfoBuilder = PostBookingInfoBuilder(build)

  implicit def toPostBookingCancellationBuilder(build: PostBookingCancellation): PostBookingCancellationBuilder =
    PostBookingCancellationBuilder(build)
  implicit def toPostBookingRequestBuilder(build: PostBookingRequest): PostBookingRequestBuilder =
    PostBookingRequestBuilder(build)

  implicit def toPostBookingSearchRequestBuilder(build: PostBookingSearchRequest): PostBookingSearchRequestBuilder =
    PostBookingSearchRequestBuilder(build)

  implicit def toPostBookingPriceBuilder(build: PostBookingPrice): PostBookingPriceBuilder =
    PostBookingPriceBuilder(build)

  implicit def toPostBookingSummaryBuilder(build: PostBookingSummary): PostBookingSummaryBuilder =
    PostBookingSummaryBuilder(build)

  def toCancellationRequestBuilder(build: PostBookingRequest): PostBookingCancellationRequestBuilder =
    PostBookingCancellationRequestBuilder(build)
}
