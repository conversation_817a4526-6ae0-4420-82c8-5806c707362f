package steps

import api.request.{FeatureFlag, MatchedRoomCriteria}
import com.agoda.commons.http.client.v2.{ClientSettings, RequestSettings}
import com.agoda.papi.enums.campaign.CampaignDiscountTypes
import com.agoda.papi.enums.request.ContractFilterType
import com.agoda.papi.enums.room.{CancellationGroup, ChildRateType}
import com.agoda.platform.papi.pricing.client.DragonfruitClient
import com.agoda.upi.models.common.Arrangement
import com.agoda.upi.models.enums.{ArrangementEntries, ItemEntries, ProductEntries}
import com.agoda.upi.models.request.{CartBaseRequest, CartItemMetadata, CartMetadata}
import com.agoda.utils.Implicits._
import config_utils.TestUtils
import io.cucumber.datatable.DataTable
import io.cucumber.scala.{EN, ScalaDsl, Scenario}
import models.db.NoOfBedrooms
import models.flow.Variant
import models.pricing.enums.{ApplyTypes, SwapRoomTypes}
import models.ratechannel.SimulateRateChannelData
import models.starfruit.RoomSortingStrategies.{
  BMPFirstSupplierFinancialsWithTotalNetPrice,
  SupplierFinancialsWithTotalNetPrice,
}
import models.starfruit._
import models.starfruit.simulation.SimulationPropertySearchRequest
import org.joda.time.DateTime
import steps.v1.fenced_rates.{FencedLanguageMapping, FencedOriginMapping, FencedRatePairMapping}
import utils.Conversions._
import utils.PapiUtils._

import java.util.Optional
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters._
import scala.concurrent.{Await, Future}
import scala.concurrent.duration._
import scala.util.Try

/**
  * Created by pbruzzo on 01/26/17.
  */
trait CommonSteps extends CommonResources {

  self: ScalaDsl with EN with SharedState with DFClient =>

  After { scenario: Scenario =>
    val status = scenario.getStatus.name()
    println(
      s"""$status scenario: ${scenario.getName}
         |  request(s): ${requestJsons.mkString("\n,")}
         |  traceId(s): ${traceIdHeaders.mkString("\n,")}
         |  """.stripMargin,
    )
  }

  Given("""^All Default Parameters$""") { () =>
    setCriteria { _ =>
      def getExperiments(variant: Char): List[Experiment] = {
        val parameter: String = "agoda.experiment.force." + variant.toLower
        Option(System.getProperty(parameter))
          .withFilter(_.nonEmpty)
          .map(forced => forced.split('|').withFilter(_.nonEmpty).map(Experiment(_, variant)).toList)
          .getOrElse(Nil)
      }

      val experiments = getExperiments(Variant.A) ++ getExperiments(Variant.B) ++ getExperiments(Variant.Z)

      SearchCriteria(experiments = experiments.map(exp => exp.name.toUpperCase -> exp.variant).toMap)
    }
    () => setSimulateDataCriteria(_ => SimulateDataCriteria())
  }

  Given("""^Expected result as CSV file: (.+)$""") { (path: String) =>
    expectedResult = csvToDatatable("./cucumber/src/test/resources/csv_files/" + path)
  }

  Given("""^Hotels (.+) checkin (\d{4}-\d{2}-\d{2}) los (\d+)$""") { (h: String, ck: String, l: Int) =>
    setCriteria(sc => sc.copy(hotels = h, checkIn = ck, los = l))
  }

  Given("""^BookingTime (\d{2}:\d{2}:\d{2})$""") { (t: String) =>
    setCriteria(sc => sc.copy(bookingTime = Some(t)))
  }
  // ********************************************************************************************

  Given("""^Adults (\d+)$""") { (ad: Int) =>
    setCriteria(sc => sc.copy(adults = ad))
  }

  Given("""^Children (\d+)$""") { (ch: Int) =>
    setCriteria(sc => sc.copy(children = ch))
  }

  Given("""^ChildAges (.+)$""") { (cha: String) =>
    setCriteria(sc => sc.copy(childAges = cha))
  }

  Given("""^Rooms (\d+)$""") { (r: Int) =>
    setCriteria(sc => sc.copy(rooms = r))
  }

  Given("""^OccFilter \{([\d{2}],[\d{2}])\}$""") { (occFilter: String) =>
    setCriteria(sc => sc.copy(occFilter = occFilter))
  }

  Given("""^Currency (.{3})$""") { (cur: String) =>
    setCriteria(sc => sc.copy(currency = cur))
  }

  Given("""^CreditCardCurrency (.{3})$""") { (cur: String) =>
    setCriteria(sc => sc.copy(creditCardCurrency = cur))
  }

  Given("""^PaymentCurrency (.{3})$""") { (cur: String) =>
    setCriteria(sc => sc.copy(paymentCurrency = cur))
  }

  Given("""^PopulatePaymentRequestForBookingWithCurrency (.{3})$""") { (cur: String) =>
    setCriteria(sc => sc.copy(creditCardCurrency = cur, paymentCurrency = cur))
  }

  Given("""^NoBookingPaymentMethodCurrency""") { () =>
    setCriteria(sc => sc.copy(creditCardCurrency = "", paymentCurrency = ""))
  }

  Given("""^Platform (\d+)$""") { (plt: Int) =>
    setCriteria(sc => sc.copy(platformId = plt))
  }

  Given("""^Traffic Group (\d+)$""") { (trafficGroup: Int) =>
    setCriteria(sc => sc.copy(trafficGroup = Some(trafficGroup)))
  }

  Given("""^Locale (.+)$""") { (locale: String) =>
    setCriteria(sc => sc.copy(locale = Some(locale)))
  }

  Given("""^RatePlans (.+)$""") { (rps: String) =>
    setCriteria(sc => sc.copy(ratePlans = rps))
  }

  Given("""^CID (-?\d+)$""") { (cid: Int) =>
    setCriteria(sc => sc.copy(CID = cid))
  }

  Given("""AID {int}""") { (aid: Int) =>
    setCriteria(sc => sc.copy(AID = Some(aid.toString)))
  }

  Given("""^PriusId (-?\d+)$""") { (pid: Int) =>
    setCriteria(sc => sc.copy(priusId = pid))
  }

  Given("""^Language (-?\d+)$""") { (languageID: Int) =>
    setCriteria(sc => sc.copy(languageId = languageID))
  }

  Given("""^LanguageUse (-?\d+)$""") { (languageUse: Int) =>
    setCriteria(sc => sc.copy(languageUse = languageUse))
  }

  Given("""^Origin ($|.{2}|None)$""") { (o: String) =>
    val overrideOrigin = if (o == "None") "" else o
    setCriteria(sc => sc.copy(origin = overrideOrigin))
  }

  Given("""^RequiredBasis (PB|PRPB|PRPN|PN)$""") { (requiredBasis: String) =>
    val applyType = ApplyTypes.getApplyType(requiredBasis)
    setCriteria(sc => sc.copy(requiredBasis = Some(applyType)))
  }

  Given("""^MaxRooms (\d+)$""") { (maxRooms: Int) =>
    setCriteria(sc => sc.copy(maxRooms = Some(maxRooms)))
  }

  Given("""^Experiment (.+) User ([ABZ])$""") {
    (experiment: String,
     user: String) => // Once the issue: https://github.com/cucumber/cucumber/issues/1405 is fixed, user `Type` can be Char again.
      setCriteria { sc =>
        sc.copy(experiments = sc.experiments + (experiment.toUpperCase -> user.charAt(0)))
      }
  }

  Given("""^ForcedActiveExperiments User ([ABZ])$""") { (user: String) =>
    setCriteria { sc =>
      sc.copy(forcedActiveExperimentsUser = Some(user.charAt(0)))
    }
  }

  Given("""^isAllOcc (true|false)$""") { (isAllOCC: String) =>
    setCriteria(sc => sc.copy(isAllOcc = isAllOCC.toBoolean))
  }

  Given("""^isIncludeUsdAndLocalCurrency (true|false)$""") { (isIncludeUsdAndLocalCurrencyInput: String) =>
    setCriteria(sc => sc.copy(isIncludeUsdAndLocalCurrency = isIncludeUsdAndLocalCurrencyInput.toBoolean))
  }

  Given("""^TurnOffFxi (true|false)$""") { (turnOffFxi: String) =>
    setCriteria(sc => sc.copy(turnOffFxi = turnOffFxi.toBoolean))
  }

  Given("""^isHotelForBooking (true|false)$""") { (value: String) =>
    setCriteria(sc => sc.copy(isHotelForBooking = value.toBoolean))
  }

  Given("""^FilterByMasterRoomIds (\d+)$""") { (value: Long) =>
    setCriteria(sc => sc.copy(filterByMasterRoomIds = List(value)))
  }

  Given("""^FilterByMasterRoomIds -$""") { () =>
    setCriteria(sc => sc.copy(filterByMasterRoomIds = List.empty))
  }

  Given("""^FilterByMasterRoomIds (\d+),\s*(\d+)$""") { (value1: Long, value2: Long) =>
    setCriteria(sc => sc.copy(filterByMasterRoomIds = List(value1, value2)))
  }

  Given("""PriceFreezeInfo with FrozenPrice {double} Deposit {double} and MaxCap {double}""") {
    (price: Double, deposit: Double, maxCap: Double) =>
      setCriteria(sc => sc.copy(priceFreeze = Some(PriceFreeze(price, deposit, maxCap))))
  }

  Given("""PriceMetadata (.+)$""") { (metadata: String) =>
    setCriteria(sc => sc.copy(priceMetaData = Map("DYNAMIC_PRICING" -> metadata)))
  }

  Given("""^FilterByUID (.+)$""") { (value: String) =>
    setCriteria { sc =>
      val uids = value.split(",").toList
      sc.copy(filterByUIDs = uids)
    }
  }

  Given("""^FilterByRoomIdentifiers (.+)$""") { value: String =>
    setCriteria { sc =>
      val roomIdentifiers = value.split(",").toList
      sc.copy(filterByRoomIdentifiers = roomIdentifiers)
    }
  }

  Given("""^FilterByPriceAdjustmentId (.+)$""") { value: String =>
    setCriteria { sc =>
      sc.copy(priceAdjustmentId = Some(value.toLong))
    }
  }

  Given("""^ExternalPartnerId (\d+)$""") { value: String =>
    setCriteria { sc =>
      sc.copy(externalPartnerId = Some(value))
    }
  }

  Given("""^RoomIdentifierFilter with id (.+)$""") { (value: String) =>
    setCriteria { sc =>
      val roomIdentifierFilters = value.split(",").map(r => r).toList
      sc.copy(roomIdentifierFilter =
        Option(RoomIdentifierFilter(roomIdentifierFilters.map(r => RoomIdentifier(r, OverrideRoomIdentifier(None))))))
    }
  }

  Given("""^SymmetricUidFilterOut with id (.+)$""") { (value: String) =>
    setCriteria { sc =>
      val symmetricUidFiltersOut = value.split(",").map(r => r).toList
      sc.copy(symmetricUidFilterOut = Option(SymmetricUidFilter(symmetricUidFiltersOut)))
    }
  }

  Given("""^CancellationGroupFilter (.+)$""") { cancellationGroup: String =>
    setCriteria { sc =>
      sc.copy(cancellationGroupFilter =
        Option(CancellationGroupFilter(CancellationGroup.namesToValuesMap(cancellationGroup))))
    }
  }

  Given("""^FilterByContractFilter (.+)$""") { value: String =>
    setCriteria { sc =>
      val contractFilter = value.split(",").toList
      sc.copy(contractsFilter =
        Some(contractFilter.map(ct => ContractsFilterContext(Some(ContractTypeFilter(ContractFilterType.withName(ct)))))))
    }
  }

  Given("""^requiredPrecheckAccuracyLevel (\d+)$""") { (value: Int) =>
    val supplierPullData = SupplierPullMetadataRequest(requiredPrecheckAccuracyLevel = value)
    setCriteria(sc => sc.copy(supplierPullMetadata = Option(supplierPullData)))
  }

  val defaultRoomCriteria = MatchedRoomCriteria(None, -1, -1, -1, -1, -1, "", -1, -1, "")

  Given("""^FilterByHotelId (\d+)$""") { (value: Long) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria.getOrElse(defaultRoomCriteria).copy(hotelId = value)
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^FilterByChildRoomId (\d+)$""") { (value: Long) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria.getOrElse(defaultRoomCriteria).copy(roomTypeId = value)
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^FilterByChannelId (\d+)$""") { (value: Int) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria.getOrElse(defaultRoomCriteria).copy(channelId = value)
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^FilterByRateCategoryId (\d+)$""") { (value: Int) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria.getOrElse(defaultRoomCriteria).copy(rateCategoryId = value)
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^FilterByCancellation (.*)$""") { (value: String) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria.getOrElse(defaultRoomCriteria).copy(cxlCode = if (value == "-") "" else value)
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^FilterByPaymentModel (\d+)$""") { (value: Int) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria.getOrElse(defaultRoomCriteria).copy(paymentModel = value)
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^FilterByDmcId (\d+)$""") { (value: Int) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria.getOrElse(defaultRoomCriteria).copy(dmcId = value)
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^FilterByDmcRoomId (.*)$""") { (value: String) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria.getOrElse(defaultRoomCriteria).copy(dmcRoomId = value)
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^FilterByPromotionId (.*)$""") { (value: String) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria
        .getOrElse(defaultRoomCriteria)
        .copy(promotionId = if (value == "-") None else Option(value.toInt))
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^FilterByPointMaxPoint (\d+)$""") { (value: Int) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria.getOrElse(defaultRoomCriteria).copy(pointMaxPoint = Option(value))
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^FilterByRateCategory (\d+)$""") { (value: Int) =>
    setCriteria { sc =>
      val criteria = sc.roomCriteria.getOrElse(defaultRoomCriteria).copy(rateCategoryId = value)
      sc.copy(roomCriteria = Option(criteria))
    }
  }

  Given("""^IsCrossSell (true|false)$""") { (isCrossSell: String) =>
    setCriteria { sc =>
      if (isCrossSell.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.CrossSell)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.CrossSell))
    }
  }

  Given("""^IsAlternativeRoom (true|false)$""") { (IsAlternativeRoom: String) =>
    setCriteria { sc =>
      if (IsAlternativeRoom.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.AlternativeRoom)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.AlternativeRoom))
    }
  }

  Given("""^IsNeedAPSRateSwap (true|false)$""") { (isNeedAPS: String) =>
    setCriteria { sc =>
      if (isNeedAPS.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.APSRate)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.APSRate))
    }
  }

  Given("""^IsNeedRetailRateSwap (true|false)$""") { (isNeedRetail: String) =>
    setCriteria { sc =>
      if (isNeedRetail.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.RetailSwap)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.RetailSwap))
    }
  }

  Given("""^IsRoomAmenityFilter (true|false)$""") { (isRoomAmenityFilter: String) =>
    setCriteria { sc =>
      if (isRoomAmenityFilter.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.RoomAmenityFilter)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.RoomAmenityFilter))
    }
  }

  Given("""^IsRateChannelSwap (true|false)$""") { (isRateChannelSwap: String) =>
    setCriteria { sc =>
      if (isRateChannelSwap.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.RateChannelSwap)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.RateChannelSwap))
    }
  }

  Given("""^ApplyDiscountToAffiliatePrices (true|false)$""") { (isApplyDiscountToAffiliatePrices: String) =>
    setCriteria { sc =>
      if (isApplyDiscountToAffiliatePrices.toBoolean)
        sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.ApplyDiscountToAffiliatePrices)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.ApplyDiscountToAffiliatePrices))
    }
  }

  Given("""^IsPulseDiscountBreakdown (true|false)$""") { (isRoomAmenityFilter: String) =>
    setCriteria { sc =>
      if (isRoomAmenityFilter.toBoolean)
        sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.ShowPulseDiscountBreakdown)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.ShowPulseDiscountBreakdown))
    }
  }

  Given("""^IsChildRateSetting (true|false)$""") { (isChildRateSetting: String) =>
    setCriteria { sc =>
      if (isChildRateSetting.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.ChildRateSetting)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.ChildRateSetting))
    }
  }

  Given("""^DomesticTaxReceipt (true|false)$""") { (isDomesticTaxReceipt: String) =>
    setCriteria { sc =>
      if (isDomesticTaxReceipt.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.DomesticTaxReceipt)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.DomesticTaxReceipt))
    }
  }

  Given("""^IsEnablePapiEnrichment (true|false)$""") { (IsEnablePapiEnrichment: String) =>
    setCriteria { sc =>
      if (IsEnablePapiEnrichment.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.EnablePapiEnrichment)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.EnablePapiEnrichment))
    }
  }

  Given("""^IsBreakfastUpsell (true|false)$""") { (isBreakfastUpsell: String) =>
    setCriteria { sc =>
      if (isBreakfastUpsell.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.BreakfastUpsell)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.BreakfastUpsell))
    }
  }

  Given("""^isStackChannelDiscount (true|false)$""") { (isStackChannelDiscount: String) =>
    setCriteria { sc =>
      if (isStackChannelDiscount.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.StackChannelDiscount)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.StackChannelDiscount))
    }
  }

  Given("""^isDisableHeisenberg (true|false)$""") { (isDisableHeisenberg: String) =>
    setCriteria { sc =>
      if (isDisableHeisenberg.toBoolean) {
        sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.DisableHeisenberg)
      } else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.DisableHeisenberg))
    }
  }

  Given("""^IsMultiHotel (true|false)$""") { (isStackChannelDiscount: String) =>
    setCriteria { sc =>
      if (isStackChannelDiscount.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.MultiHotel)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.MultiHotel))
    }
  }

  Given("""^IsConnectedTrip (true|false)$""") { (isStackChannelDiscount: String) =>
    setCriteria { sc =>
      if (isStackChannelDiscount.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.ConnectedTrip)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.ConnectedTrip))
    }
  }

  Given("""^IsMultipleExtraBeds (true|false)$""") { (IsMultipleExtraBeds: String) =>
    setCriteria { sc =>
      if (IsMultipleExtraBeds.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.MultipleExtraBeds)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.MultipleExtraBeds))
    }
  }

  Given("""^IsEnableAgencySupplyForPackages (true|false)$""") { (isStackChannelDiscount: String) =>
    setCriteria { sc =>
      if (isStackChannelDiscount.toBoolean)
        sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.EnableAgencySupplyForPackages)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.EnableAgencySupplyForPackages))
    }
  }

  Given("""^IsEnableCashback (true|false)$""") { (enableCashback: String) =>
    setCriteria { sc =>
      if (enableCashback.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.EnableCashback)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.EnableCashback))
    }
  }

  Given("""^EnableCofundedCashback (true|false)$""") { (enableCofundedCashback: String) =>
    setCriteria { sc =>
      if (enableCofundedCashback.toBoolean)
        sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.EnableCofundedCashback)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.EnableCofundedCashback))
    }
  }

  Given("""^DisableSoybean (true|false)$""") { (disableSoybean: String) =>
    setCriteria { sc =>
      if (disableSoybean.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.DisableSoybean)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.DisableSoybean))
    }
  }

  Given("""^SelectedRewardOption (.+)$""") { (selectedRewardOption: String) =>
    setCriteria { sc =>
      sc.copy(selectedRewardOption = Some(selectedRewardOption))
    }
  }

  Given("""^DispatchGoLocalForInternational (true|false)$""") { (dispatchGoLocalForInternational: String) =>
    setCriteria { sc =>
      if (dispatchGoLocalForInternational.toBoolean)
        sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.DispatchGoLocalForInternational)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.DispatchGoLocalForInternational))
    }
  }

  Given("""^CalculateTaxAndFeeWithMarginTaxAndFee (true|false)$""") { (calculateTaxAndFeeWithMarginTaxAndFee: String) =>
    setCriteria { sc =>
      if (calculateTaxAndFeeWithMarginTaxAndFee.toBoolean)
        sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.CalculateTaxAndFeeWithMarginTaxAndFee)
      else sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.CalculateTaxAndFeeWithMarginTaxAndFee))
    }
  }

  Given("""^BookingDate (\d{4}-\d{2}-\d{2})$""") { (booking: String) =>
    setCriteria(sc => sc.copy(bookingDate = booking))
  }

  Given("""^BookingDate (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})$""") { (booking: String) =>
    setCriteria(sc => sc.copy(bookingDate = booking))
  }

  Given("""^bookingDurationType (.+)$""") { (durationType: String) =>
    setCriteria(sc => sc.copy(bookingDurationType = Some(List(durationType.toLowerCase()))))
  }

  Given("""^SelectedCheckInTime (.+)$""") { (selectedCheckInTime: String) =>
    setCriteria(sc => sc.copy(selectedCheckInTime = Some(selectedCheckInTime)))
  }

  Given("""^userSelectedSlot from (.+) and duration (\d+)$""") { (from: String, duration: Int) =>
    setCriteria(sc => sc.copy(userSelectedSlots = Some(SelectedHourlySlot(from, duration))))
  }

  Given("""^Suppliers ([\d,]+)$""") { (suppliersStr: String) =>
    setCriteria(sc => sc.copy(suppliers = suppliersStr))
  }

  Given("""^SearchId (.*)$""") { (id: String) =>
    setCriteria(sc => sc.copy(searchId = id))
  }

  Given("""^UserId (.*)""") { (id: String) =>
    setCriteria(sc => sc.copy(userId = id))
  }

  Given("""^IsSync (true|false)$""") { (value: String) =>
    setCriteria(sc => sc.copy(isSync = value.toBoolean))
  }

  Given("""^IsRPM2Included (true|false)$""") { (isRPM2Included: String) =>
    setCriteria(sc => sc.copy(isRPM2Included = isRPM2Included.toBoolean))
  }

  Given("""^StoreFront (-?\d+)$""") { (storeFrontId: Int) =>
    setCriteria(sc => sc.copy(storeFrontId = storeFrontId))
  }

  Given("""^isPartnerRequest (true|false)$""") { (isPartnerRequest: String) =>
    setCriteria(sc => sc.copy(isPartnerRequest = isPartnerRequest.toBoolean))
  }

  Given("""^RatePartnerSummaries (true|false)$""") { (ratePartnerSummaries: String) =>
    setCriteria(sc => sc.copy(ratePartnerSummaries = ratePartnerSummaries.toBoolean))
  }

  Given("""^Partner Daily Rates (true|false)$""") { (partnerDailyRates: String) =>
    setCriteria(sc => sc.copy(partnerDailyRates = partnerDailyRates.toBoolean))
  }

  Given("""^ShouldAddPFInMarketingFee (true|false)$""") { (shouldAddPFInMarketingFee: String) =>
    setCriteria(sc => sc.copy(shouldAddPFInMarketingFee = Some(shouldAddPFInMarketingFee.toBoolean)))
  }

  Given("""^PartnerRoomRateType (\d+)$""") { (partnerRoomRateType: Int) =>
    setCriteria(sc => sc.copy(partnerRoomRateType = partnerRoomRateType))
  }

  Given("""^PartnerSurchargeRateType (\d+)$""") { (partnerSurchargeRateType: Int) =>
    setCriteria(sc => sc.copy(partnerSurchargeRateType = partnerSurchargeRateType))
  }

  Given("""^IsExcludedPfFromTax (true|false)$""") { (isExcludedPfFromTax: String) =>
    setCriteria(sc => sc.copy(isExcludedPfFromTax = isExcludedPfFromTax.toBoolean))
  }

  Given("""^IsIncludedPriceInfo (true|false)$""") { (isIncludedPriceInfo: String) =>
    setCriteria(sc => sc.copy(isIncludedPriceInfo = Some(isIncludedPriceInfo.toBoolean)))
  }

  Given("""^IsUserLoggedIn (true|false)$""") { (isUserLoggedIn: String) =>
    setCriteria(sc => sc.copy(isUserLoggedIn = isUserLoggedIn.toBoolean))
  }

  Given("""^OnlyCheapestRoom (true|false)""") { (onlyCheapestRoom: Boolean) =>
    setCriteria(sc => sc.copy(onlyCheapestRoom = onlyCheapestRoom))
  }

  Given("""^NumberOfBedrooms (.+)$""") { (nosOfBedroomsStr: String) =>
    val nosOfBedrooms = nosOfBedroomsStr.split(",").map(_.toInt.asInstanceOf[NoOfBedrooms]).toList
    setCriteria(sc => sc.copy(nosOfBedrooms = Some(nosOfBedrooms)))

  }

  Given("""^IsSSR (true|false)""") { (isSSR: Boolean) =>
    setCriteria(sc => sc.copy(isSSR = Some(isSSR)))
  }

  Given("""^IsMSE (true|false)""") { (isMSE: Boolean) =>
    setCriteria(sc => sc.copy(isMSE = Some(isMSE)))
  }

  Given("""^MseHotelIds (.*)$""") { (mseHotelIds: String) =>
    val value = mseHotelIds.split(",").map(_.toInt).toList
    setCriteria(sc => sc.copy(mseHotelIds = value))
  }

  Given("""^SupplierFinancialsWithTotalNetPriceSorting (true|false)$""") {
    (SupplierFinancialsWithTotalNetPriceSorting: Boolean) =>
      setCriteria { sc =>
        sc.copy(roomSortingStrategy =
          if (SupplierFinancialsWithTotalNetPriceSorting) Some(SupplierFinancialsWithTotalNetPrice) else None)
      }
  }

  Given("""^BMPFirstSupplierFinancialsWithTotalNetPriceSorting (true|false)$""") {
    (BMPFirstSupplierFinancialsWithTotalNetPriceSorting: Boolean) =>
      setCriteria { sc =>
        sc.copy(roomSortingStrategy =
          if (BMPFirstSupplierFinancialsWithTotalNetPriceSorting) Some(BMPFirstSupplierFinancialsWithTotalNetPrice)
          else None)
      }
  }

  Given("""^isEnableSupplierInfo (true|false)$""") { (isEnable: Boolean) =>
    setCriteria(sc => sc.copy(isEnableSupplierInfo = isEnable))
  }

  Given("""^enableRatePlanCheckInCheckOut (true|false)$""") { (isEnable: Boolean) =>
    setCriteria(sc => sc.copy(enableRatePlanCheckInCheckOut = isEnable))
  }

  Given("""^showPastMidnightSlots (true|false)$""") { (isEnable: Boolean) =>
    setCriteria(sc => sc.copy(showPastMidnightSlots = isEnable))
  }

  Given("""^sortByCheckInTimeDayUseSSR (true|false)$""") { (isEnable: Boolean) =>
    setCriteria(sc => sc.copy(sortByCheckInTimeDayUseSSR = isEnable))
  }

  Given("""^enablePushDayUseRates (true|false)$""") { (isEnable: Boolean) =>
    setCriteria(sc => sc.copy(enablePushDayUseRates = isEnable))
  }

  Given("""^showCheapestHourlyRate (true|false)$""") { (isEnable: Boolean) =>
    setCriteria(sc => sc.copy(showCheapestHourlyRate = Some(isEnable)))
  }

  Given("""^enableSecretDealImprovement (true|false)$""") { (isEnable: Boolean) =>
    setCriteria(sc => sc.copy(enableSecretDealImprovement = Some(isEnable)))
  }

  Given("""^enableCreditCardCampaignPeek (true|false)$""") { (isEnable: Boolean) =>
    setCriteria(sc => sc.copy(enableCreditCardCampaignPeek = Some(isEnable)))
  }

  Given("""^calculateRareRoomBadge (true|false)$""") { (isEnable: Boolean) =>
    setCriteria(sc => sc.copy(calculateRareRoomBadge = Some(isEnable)))
  }

  Given("""^enableCxlAversion (true|false)$""") { (isEnable: Boolean) =>
    setCriteria(sc => sc.copy(enableCxlAversion = Some(isEnable)))
  }

  Given("""^ABTest (.+) User ([AB])$""") { (abTest: Int, user: String) =>
    setCriteria { sc =>
      sc.copy(abTests = sc.abTests :+ ABTest(abTest, user.charAt(0)))
    } // Once the issue: https://github.com/cucumber/cucumber/issues/1405 is fixed, user `Type` can be Char again.
  }

  Given("""^MemberID (-?\d+)$""") { (memberId: Int) =>
    setCriteria(sc => sc.copy(memberId = memberId))
  }

  Given("""^ExactMatchOccupancy (true|false)$""") { (exactMatchOccupancy: String) =>
    if (exactMatchOccupancy.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.ExactMatchOccupancy)
    }
  }

  Given("""^ShowFitAndBiggerRooms (true|false)$""") { (showFitAndBiggerRooms: String) =>
    if (showFitAndBiggerRooms.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.ShowFitAndBiggerRooms)
    }
  }

  Given("""^FlexibleMultiRoom (true|false)$""") { (flexibleMultiRoom: String) =>
    if (flexibleMultiRoom.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.FlexibleMultiRoom)
    }
  }

  Given("""^isUserLoggedIn (true|false)$""") { (isUserLoggedIn: String) =>
    setCriteria(_.copy(isUserLoggedIn = isUserLoggedIn.toBoolean))
  }

  Given("""^RequiredPrecheckAccuracy (high|none)$""") { (requiredPrecheckAccuracy: String) =>
    val level: Int = requiredPrecheckAccuracy match {
      case "none" => 1
      case _ => 0
    }
    val supplierPullMetadata = Option(SupplierPullMetadataRequest(level))
    setCriteria(sc => sc.copy(supplierPullMetadata = supplierPullMetadata))
  }

  Given("""^isAllowRoomTypeNotGuarantee (true|false)$""") { (isAllowRoomTypeNotGuarantee: String) =>
    setCriteria(sc => sc.copy(isAllowRoomTypeNotGuarantee = Option(isAllowRoomTypeNotGuarantee.toBoolean)))
  }

  Given("""^DiscountValue (\d+\.\d+) Currency (.*) Type (\d+) MinValue (\d+\.\d+) MinAppliedValue (\d+\.\d+)$""") {
    (value: Double, rawCurrency: String, discountType: Int, minValue: Double, minAppliedValue: Double) =>
      val currency: String = (rawCurrency) match {
        case "-" => ""
        case _ => rawCurrency
      }
      val campaignDiscount = CampaignDiscount(value,
                                              currency,
                                              CampaignDiscountTypes.getDiscountType(discountType),
                                              None,
                                              Option(minValue),
                                              Option(minAppliedValue))
      setCriteria(sc => sc.copy(campaignDiscount = Option(campaignDiscount)))
  }

  Given("""^EmailDomain (.*)$""") { (emailDomain: String) =>
    val email = emailDomain.toOption(emailDomain != "-")
    setCriteria(sc => sc.copy(emailDomain = email))
  }

  Given("""^CampaignId (.*) CampaignCid (.*) CampaignPromotionId (.*) PromotionCode (.*)$""") {
    (campaignId: String, campaignCid: String, campaignPromotionId: String, promoCode: String) =>
      val campaingInfo = (campaignId, campaignCid) match {
        case ("-", "-") => Nil
        case _ => List(
            CampaignInfo(campaignId.toInt,
                         campaignCid.toInt,
                         if (promoCode == "-") "" else promoCode,
                         promotionId = campaignPromotionId.toOption(campaignPromotionId != "-").map(_.toInt)))
      }
      setCriteria(sc => sc.copy(campaignInfos = sc.campaignInfos ++ campaingInfo))
  }

  Given("""^ClientCampaignId (.*) CampaignCid (.*) CampaignPromotionId (.*) PromotionCode (.*)$""") {
    (campaignId: String, campaignCid: String, campaignPromotionId: String, promoCode: String) =>
      val campaingInfo = (campaignId, campaignCid) match {
        case ("-", "-") => Nil
        case _ => List(
            CampaignInfo(campaignId.toInt,
                         campaignCid.toInt,
                         promoCode,
                         promotionId = campaignPromotionId.toOption(campaignPromotionId != "-").map(_.toInt)))
      }
      setCriteria(sc => sc.copy(clientCampaignInfos = sc.clientCampaignInfos ++ campaingInfo))
  }

  Given("""^CCCampaignId (.*) CampaignCid (.*) CampaignPromotionId (.*)$""") {
    (campaignId: String, campaignCid: String, campaignPromotionId: String) =>
      val campaingInfo = (campaignId, campaignCid) match {
        case ("-", "-") => Nil
        case _ => List(
            CampaignInfo(campaignId.toInt,
                         campaignCid.toInt,
                         promotionCode = "",
                         promotionId = campaignPromotionId.toOption(campaignPromotionId != "-").map(_.toInt)))
      }
      setCriteria(sc => sc.copy(campaignInfos = sc.campaignInfos ++ campaingInfo))
  }

  Given("""^No Credit Card Campaign$""") { () =>
    setCriteria(sc => sc.copy(campaignInfos = List.empty))
  }

  Given("""^Credit Card CampaignId (.*) CampaignCid (.*)$""") { (campaignId: String, campaignCid: String) =>
    val campaingInfo = (campaignId, campaignCid) match {
      case ("-", "-") => Nil
      case _ => List(CampaignInfo(campaignId.toInt, campaignCid.toInt, promotionCode = "", promotionId = None))
    }
    setCriteria(sc => sc.copy(campaignInfos = sc.campaignInfos ++ campaingInfo))
  }

  Given("""^ClientDiscount (true|false)$""") { (clientDiscount: String) =>
    if (clientDiscount.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.ClientDiscount)
    }
  }

  Given("""^PromotionEligibleFromClient (true|false)$""") { (promotionEligibleFromClient: String) =>
    if (promotionEligibleFromClient.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.PromotionEligibleFromClient)
    }
  }

  Given("""^EnableCidPromoPeek (true|false)$""") { (enableCidPromoPeek: String) =>
    if (enableCidPromoPeek.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.EnableCidPromoPeek)
    }
  }

  Given("""^EnableGoToTravelCampaign (true|false)$""") { (enableGoToTravelCampaign: String) =>
    if (enableGoToTravelCampaign.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.EnableGoToTravelCampaign)
    }
  }

  Given("""^DoRoomSuggestion (true|false)$""") { (doRoomSuggestion: String) =>
    if (doRoomSuggestion.toBoolean)
      setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.RoomSuggestion))
  }

  Given("""^AddNetRates (true|false)$""") { (doRoomSuggestion: String) =>
    if (doRoomSuggestion.toBoolean)
      setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.NetRateDisplay))
  }

  Given("""^MaxSuggestions (\d+)$""") { (maxSuggestedRooms: Int) =>
    setCriteria(sc => sc.copy(maxSuggestions = Some(maxSuggestedRooms)))
  }

  Given("""^IgnoreRequestedNumberOfRoomsForNha (true|false)$""") { (ignoreRequestedNumberOfRoomsForNha: String) =>
    if (ignoreRequestedNumberOfRoomsForNha.toBoolean)
      setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.IgnoreRequestedNumberOfRoomsForNha))
    else setCriteria(sc =>
      sc.copy(featureFlags = sc.featureFlags.filter(_ != FeatureFlag.IgnoreRequestedNumberOfRoomsForNha)))
  }

  Given("""^SwitchOffExtraBed (true|false)$""") { (disableExtraBed: String) =>
    if (disableExtraBed.toBoolean)
      setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.SwitchOffExtraBed))
  }

  Given("""^RefId (-?\d+)$""") { (refId: Int) =>
    setCriteria(sc => sc.copy(refId = Option(refId)))
  }

  Given("""^PaymentId (.*)$""") { (rawPaymentId: String) =>
    val paymentId = rawPaymentId.toOption(rawPaymentId != "-").map(_.toInt)
    setCriteria(sc => sc.copy(paymentId = paymentId))
  }

  Given("""^Credit card CCOF (.*)$""") { (rawCCof: String) =>
    val ccOf = rawCCof.toOption(rawCCof != "-").map(_.toLong)
    setCriteria(sc => sc.copy(ccOf = ccOf))
  }

  Given("""^No Credit card CCOF""") { () =>
    setCriteria(sc => sc.copy(ccOf = None))
  }

  Given("""^SuggestedPrice (AllInclusive|Exclusive)$""") { (suggestedPrice: String) =>
    setCriteria(sc => sc.copy(suggestedPrice = suggestedPrice))
  }

  Given("""^DoAPSPeek (true|false)$""") { (doApsPeek: String) =>
    if (doApsPeek.toBoolean) setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.APSPeek))
  }

  Given("""^isAgodaRefund (true|false)$""") { isAgodaRefund: String =>
    if (isAgodaRefund.toBoolean) setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.AgodaRefund))
  }

  Given("""^DoAPSSwap (true|false)$""") { (doAPSSwap: String) =>
    if (doAPSSwap.toBoolean) setCriteria(sc => sc.copy(isApsEnabled = true))
  }

  Given("""^AutoApplyPromos (true|false)$""") { AutoApplyPromos: String =>
    if (AutoApplyPromos.toBoolean)
      setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.AutoApplyPromos))
  }

  Given("""^CreditCardPromotion (true|false)$""") { CreditCardPromotion: String =>
    if (CreditCardPromotion.toBoolean)
      setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.CreditCardPromotion))
  }

  Given("""^CreditCardPromotionPeek (true|false)$""") { CreditCardPromotionPeek: String =>
    if (CreditCardPromotionPeek.toBoolean)
      setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.CreditCardPromotionPeek))
  }

  Given("""^DisableDegradedSupplier (true|false)$""") { DisableDegradedSupplier: String =>
    if (DisableDegradedSupplier.toBoolean)
      setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.DisableDegradedSupplier))
  }

  Given("""^AutoApplyCreditCardOnLandingCid (true|false)$""") { (autoApplyCreditCardOnLandingCid: String) =>
    if (autoApplyCreditCardOnLandingCid.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.AutoApplyCreditCardOnLandingCid)
    }
  }

  Given("""^UserLoyaltyAmount (\d+\.?\d*) UserLoyaltyType (1|2) LoyaltyPaymentAmount (\d+\.?\d*)$""") {
    (userAmount: Double, loyaltyType: Int, loyaltyPayment: Double) =>
      val request = LoyaltyPaymentRequest(applyAmountInRequestCurrency = loyaltyPayment,
                                          availableAmountInUSD = userAmount,
                                          `type` = LoyaltyTypes.getLoyaltyType(loyaltyType),
                                          pointsRedeemed = None)
      setCriteria(sc => sc.copy(loyaltyPayment = Some(request)))
  }

  Given(
    """^UserLoyaltyAmount (\d+\.?\d*) UserLoyaltyType (1|2) LoyaltyPaymentAmount (-?\d+\.?\d*) RewardPoint (\d*)$""") {
    (userAmount: Double, loyaltyType: Int, loyaltyPayment: Double, rewardPoints: Int) =>
      val request = LoyaltyPaymentRequest(
        applyAmountInRequestCurrency = loyaltyPayment,
        availableAmountInUSD = userAmount,
        `type` = LoyaltyTypes.getLoyaltyType(loyaltyType),
        pointsRedeemed = Some(rewardPoints),
      )
      setCriteria(sc => sc.copy(loyaltyPayment = Some(request)))
  }

  Given("""^ChildAgeSwap (true|false)$""") { (childAge: String) =>
    if (childAge.toBoolean) setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.ChildAge))
    else setCriteria(sc => sc.copy(featureFlags = sc.featureFlags.filterNot(f => f == FeatureFlag.ChildAge)))
  }

  Given("""^FilterAPO (true|false)$""") { (value: String) =>
    setCriteria(sc => sc.copy(filterAPO = value.toBoolean))
  }

  Given("""^PriceStateToken (.*)$""") { (token: String) =>
    setCriteria(sc => sc.copy(previousResponseToken = token))
  }

  Given("""^IsAllowBookOnRequest (true|false)$""") { (value: String) =>
    setCriteria(sc => sc.copy(isAllowBookOnRequest = Some(value.toBoolean)))
  }

  Given("""^ConnectedTrip (true|false)$""") { (value: String) =>
    if (value.toBoolean) setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.ConnectedTrip))
  }

  Given("""^DeviceTypeId (-?\d+)$""") { (deviceTypeId: Int) =>
    setCriteria(sc => sc.copy(deviceTypeId = Some(deviceTypeId)))
  }

  Given("""^IgnoreCoupon (true|false)$""") { (ignoreCoupon: String) =>
    if (ignoreCoupon.toBoolean) setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.IgnoreCoupon))
  }

  Given("""^IsBot (true|false)$""") { (isBot: String) =>
    setCriteria(sc => sc.copy(isBot = isBot.toBoolean))
  }

  Given("""^IsUsingHotelCurrency (true|false)$""") { (isUsingHotelCurrency: String) =>
    setCriteria(sc => sc.copy(isUsingHotelCurrency = Some(isUsingHotelCurrency.toBoolean)))
  }

  Given("""^CheapestRoomFilters (\d+)$""") { (value: Int) =>
    setCriteria(sc => sc.copy(cheapestRoomFilters = List(value)))
  }

  Given("""^CouponSellEx (true|false)$""") { (couponSellEx: String) =>
    if (couponSellEx.toBoolean) setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.CouponSellEx))
  }

  Given("""^Fenced Rate Origins (.*)$""") { (value: String) =>
    val origins = value.split(",").map(_.trim).toList
    setCriteria(sc => sc.copy(fencedRate = Some(FencedRate(origins))))
  }
  DataTableType { entry: Map[String, String] =>
    FencedRatePairMapping(entry("Origin"), entry("SiteId").toInt, entry("RatePlans"))
  }
  Given("""^Fenced Rate Pairs$""") { (data: DataTable) =>
    val fencedRatePairMapping = data.asList[FencedRatePairMapping](classOf[FencedRatePairMapping]).asScala.toList
    val fencedRatePairs = fencedRatePairMapping.map { e =>
      FencedRatePair(
        FencedRateKey(Some(e.origin), Some(e.siteId)),
        FencedOriginObject(e.ratePlans.split(",").map(_.trim.toInt).toSet),
      )
    }
    setCriteria(sc => sc.copy(fencedRate = Some(FencedRate(fencedRatePairs = Some(fencedRatePairs)))))
  }
  DataTableType { entry: Map[String, String] =>
    FencedLanguageMapping(entry("Origin"), entry("SiteId").toInt, entry("Language").toInt, entry("RatePlans"))
  }
  Given("""^Fenced Rate Pairs Language$""") { (data: DataTable) =>
    val fencedRatePairMapping = data.asList[FencedLanguageMapping](classOf[FencedLanguageMapping]).asScala.toList
    val fencedRatePairs = fencedRatePairMapping.map { e =>
      FencedRatePair(
        FencedRateKey(Some(e.origin), Some(e.siteId), Some(e.language)),
        FencedOriginObject(e.ratePlans.split(",").map(_.trim.toInt).toSet),
      )
    }
    setCriteria(sc => sc.copy(fencedRate = Some(FencedRate(fencedRatePairs = Some(fencedRatePairs)))))
  }

  DataTableType { entry: Map[String, String] => FencedOriginMapping(entry("Origin"), entry("RatePlans")) }
  Given("""^Fenced Rate Mapping Origin$""") { (data: DataTable) =>
    val fencedOriginObjects = data.asList[FencedOriginMapping](classOf[FencedOriginMapping]).asScala.toList
    val origins = fencedOriginObjects.map(_.origin)
    val originMapping = fencedOriginObjects.map { fencedObj =>
      fencedObj.origin -> FencedOriginObject(fencedObj.ratePlans.split(",").map(_.trim.toInt).toSet)
    }.toMap
    setCriteria(sc => sc.copy(fencedRate = Some(FencedRate(origins, Some(originMapping)))))
  }

  Given("""^IsDebug (true|false)$""") { (value: String) =>
    setCriteria(sc => sc.copy(isDebug = Option(value.toBoolean)))
  }

  Given("""^UserContext (.*)""") { (value: String) =>
    setCriteria(sc => sc.copy(userContext = Option(value)))
  }

  Given("""^RoomSelection (\d+),(\d+),(\d+),(\d+),(\d+),(true|false),(true|false),(none|\d+),(true|false)""") {
    (roomSelecionOccupancy: Int,
     roomSelectionChildren: Int,
     roomSelectionExtraBed: Int,
     roomSelectionMandatoryExtraBed: Int,
     roomSelectionNoOfRoom: Int,
     roomSelectionRespectMaxOccupancy: Boolean,
     roomSelectionNeedOccupancySearch: Boolean,
     roomSelectionMasterRoomOccupancy: String,
     roomSelectionIsFit: Boolean) =>
      val roomSelectionMasterRoomOccupancyVal = roomSelectionMasterRoomOccupancy match {
        case ("none") => None
        case _ => Some(roomSelectionMasterRoomOccupancy.toInt)
      }

      setCriteria { sc =>
        sc.copy(roomSelection = Some(
          RoomSelectionRequest(
            roomSelecionOccupancy,
            roomSelectionChildren,
            roomSelectionExtraBed,
            roomSelectionMandatoryExtraBed,
            roomSelectionNoOfRoom,
            roomSelectionRespectMaxOccupancy,
            roomSelectionNeedOccupancySearch,
            roomSelectionMasterRoomOccupancyVal,
            roomSelectionIsFit,
          )))
      }
  }

  Given("""^WhitelabelKey (.*)$""") { key: String =>
    setCriteria(sc => sc.copy(whiteLabelKey = if (key == "empty") None else Some(key)))
  }

  Given("""^ReBook with roomTypeID (\d+) customerPaidPrice (\d+\.\d+)$""") { (r: Long, p: Double) =>
    setCriteria(sc =>
      sc.copy(reBookingRequest = Some(ReBookingRequest(r, None, p, None, None, None, None, None, None, None, None))))
  }

  Given("""^ReBook with roomTypeID (\d+) masterRoomTypeID (\d+) customerPaidPrice (\d+\.\d+)$""") {
    (r: Long, mr: Long, p: Double) =>
      setCriteria(sc =>
        sc.copy(reBookingRequest = Some(ReBookingRequest(r, Some(mr), p, None, None, None, None, None, None, None, None))))
  }

  Given(
    """^ReBook with roomTypeID (\d+) originalSellIn (\d+\.\d+)(?:\s+originalUsdToRequestExchangeRate (\d+\.\d+))?(?:\s+originalCashback (\d+\.\d+))?(?:\s+originalPromoAmount (\d+\.\d+))?$""") {
    (r: Long,
     originalSellIn: Double,
     exchangeRate: Optional[String],
     cashback: Optional[String],
     promoAmount: Optional[String]) =>
      setCriteria(sc =>
        sc.copy(reBookingRequest = Some(
          ReBookingRequest(
            roomTypeId = r,
            masterRoomTypeId = None,
            customerPaidPrice = 0.0,
            originalNetIn = None,
            originalCashback = cashback.asScala.map(_.toDouble),
            originalPromoAmount = promoAmount.asScala.map(_.toDouble),
            originalUsdToRequestExchangeRate = exchangeRate.asScala.map(_.toDouble),
            originalSellInUsd = Some(originalSellIn),
            originalSellIn = None,
            originalCashbackAmount = None,
            actionType = None,
          ),
        )))
  }

  Given("""^ReBook with roomTypeID (\d+) customerPaidPrice (\d+\.\d+) originalNetIn (\d+\.\d+)$""") {
    (r: Long, p: Double, o: Double) =>
      setCriteria(sc =>
        sc.copy(reBookingRequest = Some(ReBookingRequest(r, None, p, Some(o), None, None, None, None, None, None, None))))
  }

  Given("""^SelectedUpSellingRoomType (BreakfastUpSell|BreakfastWithCxlUpSell)$""") {
    selectedUpSellRoomTypeStr: String =>
      val selectedUpSellRoomType = SwapRoomTypes.withName(selectedUpSellRoomTypeStr)
      val roomSelectionRequest = SimplifiedRoomSelectionRequest("", None, None, Some(selectedUpSellRoomType))
      setCriteria(sc => sc.copy(simplifiedRoomSelectionRequest = Some(roomSelectionRequest)))
  }

  Given("""^MixAndSave (true|false)$""") { mixAndSave: String =>
    if (mixAndSave.toBoolean) setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.MixAndSave))
  }

  Given("""^RoomBundleHints$""") { (data: DataTable) =>
    data
      .asMaps[String, String](classOf[String], classOf[String])
      .asScala
      .toList
      .map { seg =>
        val checkIn = DateTime.parse(seg.get("CheckIn"))
        val los = seg.get("LOS").toInt
        val roomIdent = Try(RoomIdentifier(uid = seg.get("roomIdentifier"), OverrideRoomIdentifier(None))).toOption
        RoomBundleRequest(checkIn, los, roomIdent)
      }
      .foreach { rbr =>
        setCriteria(sc => sc.copy(roomBundleRequests = sc.roomBundleRequests :+ rbr))
      }
  }

  Given("""^Cart Token -$""") { () =>
    setCriteria { sc =>
      val updatedCartRequest = sc.cartRequest match {
        case Some(cReq) => cReq.copy(token = None)
        case None => CartBaseRequest(token = None,
                                     arrangement = Some(Arrangement(ArrangementEntries.withName("Bundle"), None)),
                                     None,
                                     None,
                                     None)
      }
      sc.copy(cartRequest = Some(updatedCartRequest))
    }
  }

  Given("""^Cart Token (.*) and Arrangement (.*)$""") { (tk: String, ar: String) =>
    setCriteria { sc =>
      val newToken = if (tk.equalsIgnoreCase("-")) None else Some(tk)
      val updatedCartRequest = sc.cartRequest match {
        case Some(cReq) =>
          cReq.copy(token = newToken, arrangement = Some(Arrangement(ArrangementEntries.withName(ar), None)))
        case None => CartBaseRequest(token = newToken,
                                     arrangement = Some(Arrangement(ArrangementEntries.withName(ar), None)),
                                     None,
                                     None,
                                     None)
      }
      sc.copy(cartRequest = Some(updatedCartRequest))
    }
  }

  Given("""^CartMeta with ([0-9]+) number of (Property|Flight) selected""") { (itemsSelected: Int, itemType: String) =>
    setCriteria { sc =>
      val updatedCartRequest = sc.cartRequest match {
        case Some(cReq) =>
          val updatedMeta = cReq.meta match {
            case Some(cMeta) =>
              val itemEntry = ItemEntries.withName(itemType)
              val updatedItemMeta =
                cMeta.itemMetadata.filter(_.item != itemEntry) :+ CartItemMetadata(itemEntry, itemsSelected)
              cMeta.copy(itemMetadata = updatedItemMeta)
            case None => CartMetadata(productEntry = Some(ProductEntries.CartProduct),
                                      itemMetadata =
                                        List(CartItemMetadata(ItemEntries.withName(itemType), itemsSelected)))
          }
          cReq.copy(meta = Some(updatedMeta))
        case None => CartBaseRequest(
            token = None,
            arrangement = None,
            sourceId = None,
            srcId = None,
            meta = Some(
              CartMetadata(
                productEntry = Some(ProductEntries.CartProduct),
                itemMetadata = List(
                  CartItemMetadata(
                    ItemEntries.withName(itemType),
                    itemsSelected,
                  )),
              )),
          )
      }
      sc.copy(cartRequest = Some(updatedCartRequest))
    }
  }

  Given("""^CartMeta with (.*) product entry""") { (productType: String) =>
    setCriteria { sc =>
      val productEntry = ProductEntries.withName(productType)
      val updatedCartRequest = sc.cartRequest match {
        case Some(cReq) =>
          val updatedMeta = cReq.meta match {
            case Some(cMeta) => cMeta.copy(productEntry = Some(productEntry))
            case None => CartMetadata(productEntry = Some(productEntry), itemMetadata = List.empty)
          }
          cReq.copy(meta = Some(updatedMeta))
        case None => CartBaseRequest(
            token = None,
            arrangement = None,
            sourceId = None,
            srcId = None,
            meta = Some(
              CartMetadata(
                productEntry = Some(productEntry),
                itemMetadata = List.empty,
              )),
          )
      }
      sc.copy(cartRequest = Some(updatedCartRequest))
    }
  }

  Given("""^Packaging Token -$""") { () =>
    setCriteria { sc =>
      sc.copy(packaging = Some(PackagingRequest(token = None)))
    }
  }

  Given("""^Packaging ClientToken (.*) and InterSystemToken (.*)$""") { (ct: String, ist: String) =>
    val istToken = ist match {
      case "-" => None
      case _ => Some(ist)
    }

    setCriteria { sc =>
      sc.copy(
        packaging = Some(
          PackagingRequest(
            token = Some(
              PackagingRequestToken(
                clientToken = ct,
                interSystemToken = istToken,
              ),
            ),
          ),
        ),
      )
    }
  }

  Given("""^PackagingCalculateDiffAgainstPrevPrice (true|false)$""") {
    (PackagingCalculateDiffAgainstPrevPrice: String) =>
      if (PackagingCalculateDiffAgainstPrevPrice.toBoolean)
        setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.PackagingCalculateDiffAgainstPrevPrice))
  }

  Given(
    """^RoomAssignment No (.*) and Adult (.*) and GradeSchool (.*) and PreSchool (.*) and Toddler (.*) and Baby (.*)$""") {
    (roomNo: Int, adult: Int, c1: Int, c2: Int, c3: Int, c4: Int) =>
      val childReq = List(ChildType(ChildRateType.GradeSchool, c1),
                          ChildType(ChildRateType.PreSchool, c2),
                          ChildType(ChildRateType.Toddler, c3),
                          ChildType(ChildRateType.Baby, c4))
      val roomAssignmentReq = RoomAssignment(adults = adult, childrenTypes = childReq)
      setCriteria { sc =>
        sc.copy(
          roomsAssignment = sc.roomsAssignment :+ roomAssignmentReq,
        )
      }
  }

  Given("""^Unsupported Payment Model (.*)""") { (unsupportedPaymentModel: Int) =>
    setCriteria { sc =>
      sc.copy(unsupportedPaymentModel = Set(unsupportedPaymentModel))
    }
  }

  Given("""^ChildrenTypes GradeSchool (.*) and PreSchool (.*) and Toddler (.*) and Baby (.*)$""") {
    (c1: Int, c2: Int, c3: Int, c4: Int) =>
      val childReq = List(ChildType(ChildRateType.GradeSchool, c1),
                          ChildType(ChildRateType.PreSchool, c2),
                          ChildType(ChildRateType.Toddler, c3),
                          ChildType(ChildRateType.Baby, c4))
      setCriteria { sc =>
        sc.copy(
          childrenTypes = sc.childrenTypes ++ childReq,
        )
      }
  }

  Given("""^CalculateCancellationPhases (true|false)$""") { (calculateCancellationPhases: Boolean) =>
    setCriteria { sc =>
      sc.copy(
        calculateCancellationPhases = Some(calculateCancellationPhases),
      )
    }
  }

  Given("""^GMTOffset ([-+]?\d+)$""") { (gmtOffset: String) =>
    setCriteria { sc =>
      sc.copy(
        gmtOffsetOpt = Some(gmtOffset.toInt),
      )
    }
  }

  Given("""^KTBConfirmSellAllIn (\d+\.?\d*)$""") { value: Double =>
    setCriteria { sc =>
      sc.copy(overrideKTBSellAllIn = Some(value))
    }
  }

  Given("""^GMTOffsetMinutes ([-+]?\d+)$""") { (gmtOffsetMinutes: String) =>
    setCriteria { sc =>
      sc.copy(
        gmtOffsetMinutesOpt = Some(gmtOffsetMinutes.toInt),
      )
    }
  }

  Given("""^Enable Enrich content offer (true|false)$""") { (enableRichContentOffer: Boolean) =>
    setCriteria { sc =>
      sc.copy(
        enableRichContentOffer = Some(enableRichContentOffer),
      )
    }
  }

  Given("""^Enable Escapes Package (true|false)$""") { (enableEscapesPackage: Boolean) =>
    setCriteria { sc =>
      sc.copy(
        enableEscapesPackage = Some(enableEscapesPackage),
      )
    }
  }

  Given("""^Disable Escapes Package (true|false)$""") { (disableEscapesPackage: Boolean) =>
    setCriteria { sc =>
      sc.copy(
        disableEscapesPackage = Some(disableEscapesPackage),
      )
    }
  }

  Given("""^Enable Benefit Discount (true|false)$""") { (enableBenefitValuationForASO: Boolean) =>
    setCriteria { sc =>
      sc.copy(
        enableBenefitValuationForASO = Some(enableBenefitValuationForASO),
      )
    }
  }

  Given("""^Enable Return Non Approved Escapes (true|false)$""") { (enableReturnNonApprovedEscapes: Boolean) =>
    setCriteria { sc =>
      sc.copy(
        enableReturnNonApprovedEscapes = Some(enableReturnNonApprovedEscapes),
      )
    }
  }

  Given("""^Filter Cheapest Room Escapes Package (true|false)$""") { (filterCheapestRoomEscapesPackage: Boolean) =>
    setCriteria { sc =>
      sc.copy(
        filterCheapestRoomEscapesPackage = Some(filterCheapestRoomEscapesPackage),
      )
    }
  }

  Given(
    """^PriceAdjustmentRequest RoomID (.*) RequestedPrice (.*) ChargeType (.*) RateType (.*) ApplyType (.*) ChargeOption (.*) SurchargeID (.*) Currency (.*)$""") {
    (roomId: String,
     requestedPrice: Double,
     chargeType: Int,
     rateType: Int,
     applyType: String,
     chargeOption: Int,
     surchargeId: Int,
     currency: String) =>
      val item = PriceAdjustment(
        roomId,
        requestedPrice.toDouble,
        chargeType,
        rateType,
        applyType,
        chargeOption,
        if (surchargeId >= 0) Some(surchargeId) else None,
        currency,
      )
      setCriteria { sc =>
        sc.copy(
          priceAdjustmentRequest = Some(List(item)),
        )
      }
  }

  Given(
    """^PriceAdjustmentRequests RoomID (.*) RequestedPrice (.*) ChargeType (.*) RateType (.*) ApplyType (.*) ChargeOption (.*) SurchargeID (.*) Currency (.*) and RoomID (.*) RequestedPrice (.*) ChargeType (.*) RateType (.*) ApplyType (.*) ChargeOption (.*) SurchargeID (.*) Currency (.*)$""") {

    (roomId: String,
     requestedPrice: Double,
     chargeType: Int,
     rateType: Int,
     applyType: String,
     chargeOption: Int,
     surchargeId: Int,
     currency: String,
     roomId2: String,
     requestedPrice2: Double,
     chargeType2: Int,
     rateType2: Int,
     applyType2: String,
     chargeOption2: Int,
     surchargeId2: Int,
     currency2: String) =>
      val item1 = PriceAdjustment(
        roomId,
        requestedPrice.toDouble,
        chargeType,
        rateType,
        applyType,
        chargeOption,
        if (surchargeId >= 0) Some(surchargeId) else None,
        currency,
      )
      val item2 = PriceAdjustment(
        roomId2,
        requestedPrice2.toDouble,
        chargeType2,
        rateType2,
        applyType2,
        chargeOption2,
        if (surchargeId2 >= 0) Some(surchargeId2) else None,
        currency2,
      )
      setCriteria { sc =>
        sc.copy(
          priceAdjustmentRequest = Some(List(item1, item2)),
        )
      }
  }

  Given("""^Rocketmiles PublishPriceLogic Type (.*) SortByField (.*)$""") {
    (publishPriceLogicType: String, sortByField: String) =>
      val rocketmilesPublishPriceRequest = (publishPriceLogicType, sortByField) match {
        case ("Cheapest", "PriceOnly") => Some(
            RocketmilesPublishPriceRequest(Some(RocketmilePublishPriceLogicTypes.Cheapest),
                                           Some(RocketmileSortByFields.PriceOnly)))
        case ("Cheapest", "Inclusive") => Some(
            RocketmilesPublishPriceRequest(Some(RocketmilePublishPriceLogicTypes.Cheapest),
                                           Some(RocketmileSortByFields.Inclusive)))
        case ("Highest", "Inclusive") => Some(
            RocketmilesPublishPriceRequest(Some(RocketmilePublishPriceLogicTypes.Highest),
                                           Some(RocketmileSortByFields.Inclusive)))
        case ("BcomOnly", "Inclusive") => Some(
            RocketmilesPublishPriceRequest(Some(RocketmilePublishPriceLogicTypes.BcomOnly),
                                           Some(RocketmileSortByFields.Inclusive)))
        case ("BcomFirst", "Inclusive") => Some(
            RocketmilesPublishPriceRequest(Some(RocketmilePublishPriceLogicTypes.BcomFirst),
                                           Some(RocketmileSortByFields.Inclusive)))
        case ("AgodaOnly", "Inclusive") => Some(
            RocketmilesPublishPriceRequest(Some(RocketmilePublishPriceLogicTypes.AgodaOnly),
                                           Some(RocketmileSortByFields.Inclusive)))
        case ("AgodaFirst", "Inclusive") => Some(
            RocketmilesPublishPriceRequest(Some(RocketmilePublishPriceLogicTypes.AgodaFirst),
                                           Some(RocketmileSortByFields.Inclusive)))
        case (_, _) => Some(
            RocketmilesPublishPriceRequest(Some(RocketmilePublishPriceLogicTypes.Cheapest),
                                           Some(RocketmileSortByFields.PriceOnly)))
      }
      setCriteria(sc => sc.copy(rocketmilesPublishPriceRequest = rocketmilesPublishPriceRequest))
  }

  Given("""^Rocketmiles RoomAwarePublishPriceLogic Type (.*)$""") { (publishPriceLogicType: String) =>
    val rocketmilesPublishPriceRequest = publishPriceLogicType match {
      case "Cheapest" => Some(
          RocketmilesPublishPriceRequest(roomAwarePublishPriceLogic = Some(RocketmilePublishPriceLogicTypes.Cheapest)))
      case "Highest" =>
        Some(RocketmilesPublishPriceRequest(roomAwarePublishPriceLogic = Some(RocketmilePublishPriceLogicTypes.Highest)))
      case "BcomOnly" => Some(
          RocketmilesPublishPriceRequest(roomAwarePublishPriceLogic = Some(RocketmilePublishPriceLogicTypes.BcomOnly)))
      case "BcomFirst" => Some(
          RocketmilesPublishPriceRequest(roomAwarePublishPriceLogic = Some(RocketmilePublishPriceLogicTypes.BcomFirst)))
      case "AgodaOnly" => Some(
          RocketmilesPublishPriceRequest(roomAwarePublishPriceLogic = Some(RocketmilePublishPriceLogicTypes.AgodaOnly)))
      case "AgodaFirst" => Some(
          RocketmilesPublishPriceRequest(roomAwarePublishPriceLogic = Some(RocketmilePublishPriceLogicTypes.AgodaFirst)))
      case _ => Some(
          RocketmilesPublishPriceRequest(roomAwarePublishPriceLogic = Some(RocketmilePublishPriceLogicTypes.Cheapest)))
    }
    setCriteria(sc => sc.copy(rocketmilesPublishPriceRequest = rocketmilesPublishPriceRequest))
  }

  Given("""^PromosCumulative (true|false)$""") { promosCumulative: String =>
    if (promosCumulative.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.PromosCumulative)
    }
  }

  Given("""^LongStayNudge (true|false)$""") { longStay: String =>
    if (longStay.toBoolean) setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.LongStayNudge))
  }

  Given("""^LongStayPromotionPriceBreakdown (true|false)$""") { longStayPromotionPriceBreakdown: String =>
    if (longStayPromotionPriceBreakdown.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.LongStayPromotionPriceBreakdown)
    }
  }

  Given("""^RatePlanPromosCumulative (true|false)$""") { ratePlanPromosCumulative: String =>
    if (ratePlanPromosCumulative.toBoolean) setCriteria { sc =>
      sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.RatePlanPromosCumulative)
    }
  }

  Given("""^Points (-?\d+\.?\d*) PartnerClaimToken (.*) SearchType (.*)$""") {
    (points: Double, partnerClaimToken: String, searchType: String) =>
      val request = ExternalLoyaltyRequest(
        selectedOffersIdentifier = None,
        points = Some(points),
        partnerClaimToken = if (partnerClaimToken == "-") None else Some(partnerClaimToken),
        loyaltySearchType = if (searchType == "-") None else Some(searchType),
      )
      setCriteria(sc => sc.copy(externalLoyaltyRequest = Some(request)))
  }

  Given("""^Header (.*) Value (.*)$""") { (header: String, value: String) =>
    setCriteria { sc =>
      sc.copy(headers = sc.headers + (header.toUpperCase -> value))
    }
  }

  Given("""^M150 (true|false)$""") { m150: String =>
    if (m150.toBoolean) setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.M150))
  }

  Given("""^IsPriceFreezeExercise (true|false)$""") { priceFreezeExercise: String =>
    if (priceFreezeExercise.toBoolean)
      setCriteria(sc => sc.copy(featureFlags = sc.featureFlags :+ FeatureFlag.PriceFreezeExercise))
  }

  Given("""^CountryIdOfIssueBank (.{3})$""") { c: String =>
    setCriteria(sc => sc.copy(countryIdOfIssuingBank = Some(c.toInt)))
  }

  Given("""^FilterByBenefitIds (.+)$""") { value: String =>
    setCriteria { sc =>
      val benefitIds = value.split(",").map(v => v.toInt).toList
      sc.copy(benefitIdsFilter = Some(benefitIds))
    }
  }

  Given("""^HourlyDurationFilter (.+)$""") { value: String =>
    setCriteria { sc =>
      val durations = value.split(",").map(v => v.toInt).toList
      sc.copy(hourlyDurationFilter = Some(durations))
    }
  }

  Given("""^UserCashbackAvailableAmountUsd (\d+\.?\d*) CashbackRedeemLocalAmount (\d+\.?\d*)$""") {
    (availableCashbackAmountUsd: Double, cashbackRedeemAmount: Double) =>
      val request = LoyaltyPaymentRequest(applyAmountInRequestCurrency = cashbackRedeemAmount,
                                          availableAmountInUSD = availableCashbackAmountUsd,
                                          `type` = LoyaltyTypes.Cashback,
                                          pointsRedeemed = None)

      setCriteria(sc => sc.copy(cashbackPayment = Some(request)))
  }

  Given(
    """^UserCashbackAvailableAmountUsd (\d+\.?\d*) UserAgodaCashAvailableAmountUsd (\d+\.?\d*)  CashbackRedeemLocalAmount (\d+\.?\d*) AgodaCashRedeemLocalAmount (\d+\.?\d*)$""") {
    (availableCashbackAmountUsd: Double,
     availableAgodaCashAmountUsd: Double,
     cashbackRedeemAmount: Double,
     agodaCashRedeemAmount: Double) =>
      val cashbackPaymentRequest = LoyaltyPaymentRequest(applyAmountInRequestCurrency = cashbackRedeemAmount,
                                                         availableAmountInUSD = availableCashbackAmountUsd,
                                                         `type` = LoyaltyTypes.Cashback,
                                                         pointsRedeemed = None)
      val agodaCashPaymentRequest = LoyaltyPaymentRequest(
        applyAmountInRequestCurrency = agodaCashRedeemAmount,
        availableAmountInUSD = availableAgodaCashAmountUsd,
        `type` = LoyaltyTypes.GiftCard,
        pointsRedeemed = None,
      )

      setCriteria { sc =>
        sc.copy(cashbackPayment = Some(cashbackPaymentRequest), loyaltyPayment = Some(agodaCashPaymentRequest))
      }
  }

  Given("""^IsCashbackRedemptionEligible (true|false|None)$""") { isCashbackRedemptionEligible: String =>
    setCriteria(sc =>
      sc.copy(isCashbackRedemptionEligible = isCashbackRedemptionEligible match {
        case "true" => Some(true)
        case "false" => Some(false)
        case _ => None
      }))
  }

  Given("""^UserCashbackBalanceInUsd (\d+\.?\d*) CashbackPaymentAmount (\d+\.?\d*)$""") {
    (cashbackBalanceInUsd: Double, cashbackPaymentAmount: Double) =>
      val request = LoyaltyPaymentRequest(applyAmountInRequestCurrency = cashbackPaymentAmount,
                                          availableAmountInUSD = cashbackBalanceInUsd,
                                          `type` = LoyaltyTypes.Cashback,
                                          pointsRedeemed = None)
      setCriteria(sc => sc.copy(cashbackPayment = Some(request)))
  }

  Given("""^isBookingRequest (.*)$""") { (isBookingRequest: Boolean) =>
    setCriteria(sc => sc.copy(isHotelForBooking = isBookingRequest))
  }

  // ********************************************************************************************

  def getExperimentsToForce(experiments: Map[String, Char], forcedActiveExperimentsUser: Option[Char]) =
    forcedActiveExperimentsUser match {
      case Some(f) if (f == 'A' || f == 'B') =>
        experiments.map { case (exp, user) => Experiment(exp, user) }.toList ++
          ABTests.getActiveExpList.filterNot(experiments.keySet).map(Experiment(_, f))
      case _ => experiments.map { case (exp, user) => Experiment(exp, user) }.toList ++
          ABTests.forcedAExperiments.filterNot(experiments.keySet).map(Experiment(_, 'A')) ++
          ABTests.forcedBExperiments.filterNot(experiments.keySet).map(Experiment(_, 'B'))
    }
  def getFinalizeDFClient: DragonfruitClient[Future] = getDFClient().getOrElse(dfapiClient)
  def getPropertyRequest(): PropertySearchRequest = {
    val cr = getCriteria
    val simulateDataCriteria = getSimulateDataCriteria

    // Build request
    val request = createRequest(
      hotels = cr.hotels,
      platformId = cr.platformId,
      checkInString = cr.checkIn,
      los = cr.los,
      adults = cr.adults,
      children = cr.children,
      childAges = cr.childAges,
      rooms = cr.rooms,
      occFilter = cr.occFilter,
      currency = cr.currency,
      CID = cr.CID,
      affiliateId = cr.AID,
      storeFrontId = cr.storeFrontId,
      priusId = cr.priusId,
      // Temporary fix waiting https://gitlab.agodadev.io/IT-Platform/dfapi/-/merge_requests/4282 to be merged to move away from SPL
      experiments = getExperimentsToForce(cr.experiments, cr.forcedActiveExperimentsUser),
      isAllOcc = cr.isAllOcc,
      isIncludedUSDAndLocalCurrency = cr.isIncludeUsdAndLocalCurrency,
      isUsingHotelCurrency = cr.isUsingHotelCurrency,
      languageId = cr.languageId,
      languageUse = cr.languageUse,
      origin = cr.origin,
      ratePlans = cr.ratePlans,
      bookingDate = cr.bookingDate,
      bookingTime = cr.bookingTime,
      isHotelForBooking = cr.isHotelForBooking,
      isSync = cr.isSync,
      suppliers = cr.suppliers,
      searchId = cr.searchId,
      bookingRequest =
        if (cr.isHotelForBooking) Option(
          BookingRequest(
            filters = List(BookingFilter(
              masterRoomIds = if (cr.filterByMasterRoomIds.isEmpty) None else Option(cr.filterByMasterRoomIds),
              matchedCriteria = cr.roomCriteria.map(List(_)),
              roomIdentifiers = cr.filterByRoomIdentifiers.map(r => RoomIdentifier(r, OverrideRoomIdentifier(None))),
            )),
            uid = cr.filterByUIDs,
            reBookingRequest = cr.reBookingRequest,
            selectedHourlySlot = cr.userSelectedSlots,
            priceFreeze = cr.priceFreeze,
            priceAdjustmentId = cr.priceAdjustmentId,
          ))
        else None,
      partnerRequest =
        if (cr.isPartnerRequest) Option(
          PartnerRequest(
            ratePartnerSummaries = Option(cr.ratePartnerSummaries),
            partnerRoomRateType = Option(cr.partnerRoomRateType),
            isExcludedPfFromTax = Option(cr.isExcludedPfFromTax),
            returnDailyRates = Some(cr.partnerDailyRates),
            minBookingCountForSuperAgg = Some(cr.minBookingCountForSuperAgg),
          ))
        else None,
      isIncludedPriceInfo = cr.isIncludedPriceInfo.getOrElse(false),
      abTests = cr.abTests,
      isUserLoggedIn = cr.isUserLoggedIn,
      isRPM2Included = cr.isRPM2Included,
      userId = cr.userId,
      onlyCheapestRoom = Option(cr.onlyCheapestRoom),
      isSSR = cr.isSSR,
      isMSE = cr.isMSE,
      memberId = cr.memberId,
      allowRoomTypeNotGuarantee = cr.isAllowRoomTypeNotGuarantee,
      simulateDataCriteria = Option(simulateDataCriteria),
      campaignDiscount = cr.campaignDiscount,
      clientCampaignInfos = cr.clientCampaignInfos,
      refId = cr.refId,
      suggestedPrice = cr.suggestedPrice,
      featureFlags = cr.featureFlags,
      maxRooms = cr.maxRooms,
      paymentId = cr.paymentId,
      discountRequest = cr.discountRequest,
      maxSuggestions = cr.maxSuggestions,
      loyaltyPayment = cr.loyaltyPayment,
      creditCardCurrency = cr.creditCardCurrency,
      ccOf = cr.ccOf,
      emailDomain = cr.emailDomain,
      campaignInfos = cr.campaignInfos,
      filterAPO = cr.filterAPO,
      paymentCurrency = cr.paymentCurrency,
      supplierPullMetadataRequest = cr.supplierPullMetadata,
      token = cr.previousResponseToken,
      isAllowBookOnRequest = cr.isAllowBookOnRequest.getOrElse(false),
      isApsEnabled = cr.isApsEnabled,
      deviceTypeId = cr.deviceTypeId,
      isBot = cr.isBot,
      nosOfBedrooms = cr.nosOfBedrooms,
      cheapestRoomFilters = cr.cheapestRoomFilters,
      fencedRate = cr.fencedRate,
      roomSelection = cr.roomSelection,
      roomSortingStrategy = cr.roomSortingStrategy,
      whitelabelKey = cr.whiteLabelKey,
      isEnableSupplierFinancialInfo = Some(cr.isEnableSupplierInfo),
      enableRatePlanCheckInCheckOut = Some(cr.enableRatePlanCheckInCheckOut),
      userContext = cr.userContext,
      packaging = cr.packaging,
      cartRequest = cr.cartRequest,
      roomAssignmentReq = cr.roomsAssignment,
      childrenReq = cr.childrenTypes,
      roomBundleRequests = cr.roomBundleRequests,
      bookingDurationType = cr.bookingDurationType,
      unsupportedPaymentModel = cr.unsupportedPaymentModel,
      locale = cr.locale,
      calculateCancellationPhases = cr.calculateCancellationPhases,
      gmtOffsetOpt = cr.gmtOffsetOpt,
      overrideKTBSellAllIn = cr.overrideKTBSellAllIn,
      roomIdentifierFilter = cr.roomIdentifierFilter,
      symmetricUidFilterOut = cr.symmetricUidFilterOut,
      trafficGroup = cr.trafficGroup,
      priceHistoryLookBack = cr.priceHistoryLookBack,
      cancellationGroupFilter = cr.cancellationGroupFilter,
      enableEscapesPackage = cr.enableEscapesPackage,
      filterCheapestRoomEscapesPackage = cr.filterCheapestRoomEscapesPackage,
      contractsFilter = cr.contractsFilter,
      enableSecretDealImprovement = cr.enableSecretDealImprovement,
      enableCreditCardCampaignPeek = cr.enableCreditCardCampaignPeek,
      calculateRareRoomBadge = cr.calculateRareRoomBadge,
      priceAdjustmentRequest = cr.priceAdjustmentRequest,
      enableCxlAversion = cr.enableCxlAversion,
      enableRichContentOffer = cr.enableRichContentOffer,
      rocketmilesPublishPriceRequest = cr.rocketmilesPublishPriceRequest,
      enableReturnNonApprovedEscapes = cr.enableReturnNonApprovedEscapes,
      disableEscapesPackage = cr.disableEscapesPackage,
      externalLoyaltyRequest = cr.externalLoyaltyRequest,
      simplifiedRoomSelectionRequest = cr.simplifiedRoomSelectionRequest,
      selectedRewardOption = cr.selectedRewardOption,
      enableBenefitValuationForASO = cr.enableBenefitValuationForASO,
      countryIdOfIssueBank = cr.countryIdOfIssuingBank,
      perOccupancyBreakdownType = cr.perOccupancyBreakdownType,
      shouldAddPFInMarketingFee = cr.shouldAddPFInMarketingFee,
      showPastMidnightSlots = Some(cr.showPastMidnightSlots),
      sortByCheckInTimeDayUseSSR = Some(cr.sortByCheckInTimeDayUseSSR),
      enablePushDayUseRates = Some(cr.enablePushDayUseRates),
      externalUserContext = cr.externalUserContext,
      showCheapestHourlyRate = cr.showCheapestHourlyRate,
      mseHotelIds = cr.mseHotelIds,
      benefitIdsFilter = cr.benefitIdsFilter,
      selectedCheckInTime = cr.selectedCheckInTime,
      hourlyDurationFilter = cr.hourlyDurationFilter,
      customerTaxCountry = cr.customerTaxCountry,
      cashbackPayment = cr.cashbackPayment,
      isCashbackRedemptionEligible = cr.isCashbackRedemptionEligible,
      priceMetaData = cr.priceMetaData,
      requiredBasis = cr.requiredBasis,
      externalPartnerId = cr.externalPartnerId,
      ignoreRoomsCountForNha = cr.ignoreRoomsCountForNha,
    )

    import com.agoda.pricing.models.circe.Encoders._
    import io.circe.syntax._

    requestJsons = request.asJson.noSpaces :: requestJsons

    request
  }

  private def getRateChannelSimulationData: SimulateRateChannelData =
    getCriteria.rateChannelSimulation.getOrElse(SimulateRateChannelData())

  def processPropertyRequest(request: PropertySearchRequest, requestSettings: RequestSettings) = {
    val responseWithHeader = Await.result(
      getFinalizeDFClient.getPropertiesWithHeader(request, requestSettings, TestUtils.tracingHeaders),
      testTimeout)
    traceIdHeaders = responseWithHeader.metadata.header("ag-atf-trace-id").toList ++ traceIdHeaders
    Some(responseWithHeader.response)
  }

  private def doGetProperty(timeout: Option[FiniteDuration] = None): Unit = {
    val clientSettings = ClientSettings(timeout)
    val requestSettings = RequestSettings(clientSettings, Map.empty)
    val request = getPropertyRequest()
    // Wait for response
    response = processPropertyRequest(request, requestSettings)
  }

  private def doGetPropertyPull(timeout: Option[FiniteDuration] = None,
                                showOnlyPullSupplierExp: List[Experiment] = Nil): Unit = {
    val clientSettings = ClientSettings(timeout)
    val requestSettings = RequestSettings(clientSettings, Map.empty)
    val request = getPropertyRequest()
    val context = request.context.copy(experiment = showOnlyPullSupplierExp)
    val modifiedRequest = request.copy(context = context)
    // Wait for response
    response = processPropertyRequest(modifiedRequest, requestSettings)
  }

  When("""^The user search$""") { () =>
    doGetProperty()
  }

  Given("""^The user keeps price state token from previous request$""") { () =>
    val priceToken = response.flatMap(r => r.responseToken.map(_.token))
    setCriteria(sc => sc.copy(previousResponseToken = priceToken.getOrElse("")))
  }

  When("""^The user search with price state token$""") { () =>
    // do first request to get price state token
    doGetProperty()

    val priceToken = response.flatMap(r => r.responseToken.map(_.token))
    setCriteria(sc => sc.copy(previousResponseToken = priceToken.getOrElse("")))

    // then do second request with captured token
    doGetProperty()
  }

  When("""^The user search when Pull Feature Experiment is forced""") {

    import models.consts.ABTest.IS_SHOW_ONLY_PULL

    () => doGetPropertyPull(showOnlyPullSupplierExp = List(Experiment(IS_SHOW_ONLY_PULL, 'B')))
  }

  When("""^The user search when Pull Feature Experiment is not forced$""") { () =>
    doGetPropertyPull()
  }

  When("""^The user search with (\d+) seconds timeout$""") { (timeoutInSecond: Int) =>
    doGetProperty(Some(timeoutInSecond.seconds))
  }

  Given("""Allow optional dependencies failure""") {
    setDFClient(dfapiClientIgnoreOptionalDependencyFailure)
  }

  Given("""^External VIP Level is (\d+)$""") { (externalVipLevel: Int) =>
    setCriteria { sc =>
      sc.copy(externalUserContext = Some(s"""{\"externalLoyaltyProfile\":{\"externalVipLevel\": $externalVipLevel}}"""))
    }
  }

  Given("""^Feature flag (.+) (true|false)$""") { (flagName: String, flagValue: String) =>
    setCriteria { sc =>
      FeatureFlag.values.find(_.toString == flagName) match {
        case Some(flag) =>
          if (flagValue.toBoolean) sc.copy(featureFlags = sc.featureFlags :+ flag)
          else sc.copy(featureFlags = sc.featureFlags.filter(_ != flag))
        case None => fail(s"Feature flag $flagName doesn't exist")
      }
    }
  }

  Given("""^Feature request IgnoreRoomsCountForNha (true|false)$""") { (requestValue: String) =>
    setCriteria { sc =>
      sc.copy(ignoreRoomsCountForNha = Some(requestValue.toBoolean))
    }
  }
  When("""^Rate channel Simulation Search$""") { () =>
    import com.agoda.pricing.models.circe.Encoders._
    import io.circe.syntax._
    val searchRequest = getPropertyRequest()
    val simulation = getRateChannelSimulationData
    println(simulation.asJson.noSpaces)
    val request = SimulationPropertySearchRequest(search = searchRequest, simulation = simulation)

    println(request.asJson.noSpaces)
    val clientSettings = ClientSettings(None)
    val requestSettings = RequestSettings(clientSettings, Map.empty)
    response = Some(Await.result(getFinalizeDFClient.getSimulatedProperties(request, requestSettings), testTimeout))
  }

  Given("""^CustomerTaxCountry (.*)$""") { (customerTaxCountry: String) =>
    val customerTaxCountryOpt = customerTaxCountry.toOption(customerTaxCountry != "-")
    setCriteria(sc => sc.copy(customerTaxCountry = customerTaxCountryOpt))
  }

}
