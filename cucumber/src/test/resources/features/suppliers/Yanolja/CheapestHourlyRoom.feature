@CheapestHourlyRoom
Feature: Verify hourly cheapest room

  Background:
    Given All Default Parameters
    And Origin KR
    And CID 1831843
    And Suppliers 27989
    And Adults 2
    And Children 0
    And OnlyCheapestRoom true

#  Scenario: <PERSON><PERSON><PERSON> check hourly rates when bookingDurationType is hourly and los 1
#    Given bookingDurationType hourly
#    And Hotels 202059486 checkin 2020-05-24 los 1
#    And BookingDate 2020-04-24 10:29:00
#    And showPastMidnightSlots true
#    When The user search
#    Then Yanolja Hourly room should be
#      | RoomID     | SupplierId | ChannelID | Adults |display.perBook.exclusive  | display.perBook.allInclusive | display.perRoomPerNight.exclusive | display.perRoomPerNight.allInclusive | hourlyAvailableSlots.duration | hourlyAvailableSlots.from                              |
#      | 1196982560 | 27989      | 1         | 2      | 20661                     | 25000                        | 20661                             | 25000                                | 3, 3, 3, 4, 4, 3, 2, 1        | 11:00, 17:00, 18:00, 19:00, 20:00, 21:00, 22:00, 23:00 |

#  Scenario: <PERSON><PERSON><PERSON> check past hourly rates
#    Given bookingDurationType hourly
#    And Hotels 202059486 checkin 2020-05-24 los 1
#    And BookingDate 2020-05-24 15:00:00
#    And showPastMidnightSlots true
#    When The user search
#    Then Yanolja Hourly room should be
#      | RoomID     | SupplierId | ChannelID | Adults |display.perBook.exclusive  | display.perBook.allInclusive | display.perRoomPerNight.exclusive | display.perRoomPerNight.allInclusive | hourlyAvailableSlots.duration    | hourlyAvailableSlots.from                       |
#      | 1196982560 | 27989      | 1         | 2      | 20661                     | 25000                        | 20661                             | 25000                                | 3, 3, 4, 4, 3, 2, 1              | 17:00, 18:00, 19:00, 20:00, 21:00, 22:00, 23:00 |

#  Scenario: Yanolja check past hourly rates 2
#    Given bookingDurationType hourly
#    And Hotels 202059486 checkin 2020-05-24 los 1
#    And BookingDate 2020-04-24 15:00:00
#    And showPastMidnightSlots false
#    When The user search
#    Then Yanolja Hourly room should be
#      | RoomID     | SupplierId | ChannelID | Adults |display.perBook.exclusive  | display.perBook.allInclusive | display.perRoomPerNight.exclusive | display.perRoomPerNight.allInclusive | hourlyAvailableSlots.duration | hourlyAvailableSlots.from  |
#      | 1196982560 | 27989      | 1         | 2      | 20661                     | 25000                        | 20661                             | 25000                                | 3, 3, 3, 4                    | 11:00, 17:00, 18:00, 19:00 |

#  #Expect behaviour need update after LOVE-201 done
#  Scenario: Yanolja check hourly rates when bookingDurationType is hourly and BookingDate in the middle of time available
#   Given bookingDurationType hourly
#   And Hotels 202059486 checkin 2020-05-24 los 1
#   And BookingDate 2020-04-24 22:00:00
#   And showPastMidnightSlots true
#   When The user search
#   Then Yanolja Hourly room should be
#     | RoomID     | SupplierId | ChannelID | Adults |display.perBook.exclusive  | display.perBook.allInclusive | display.perRoomPerNight.exclusive | display.perRoomPerNight.allInclusive | hourlyAvailableSlots.duration | hourlyAvailableSlots.from                              |
#     | 1196982560 | 27989      | 1         | 2      | 20661                     | 25000                        | 20661                             | 25000                                | 3, 3, 3, 4, 4, 3, 2, 1        | 11:00, 17:00, 18:00, 19:00, 20:00, 21:00, 22:00, 23:00 |

  #Expect behaviour need update after LOVE-201 done
#  Scenario: Yanolja check hourly rates when bookingDurationType is hourly and BookingDate more than available time
#    Given bookingDurationType hourly
#    And Hotels 202059486 checkin 2020-05-24 los 1
#    And BookingDate 2020-04-24 23:30:00
#    And showPastMidnightSlots true
#    When The user search
#    Then Yanolja Hourly room should be
#      | RoomID     | SupplierId | ChannelID | Adults |display.perBook.exclusive  | display.perBook.allInclusive | display.perRoomPerNight.exclusive | display.perRoomPerNight.allInclusive | hourlyAvailableSlots.duration | hourlyAvailableSlots.from                              |
#      | 1196982560 | 27989      | 1         | 2      | 20661                     | 25000                        | 20661                             | 25000                                | 3, 3, 3, 4, 4, 3, 2, 1        | 11:00, 17:00, 18:00, 19:00, 20:00, 21:00, 22:00, 23:00 |

  Scenario: Yanolja check hourly rates when bookingDurationType is hourly
    Given bookingDurationType hourly
    And Hotels 202059486 checkin 2020-05-24 los 2
    And BookingDate 2020-04-24 10:00:00
    When The user search
    Then Yanolja Hourly room should be
      | RoomID    | SupplierId | ChannelID | Adults |display.perBook.exclusive | display.perBook.allInclusive | display.perRoomPerNight.exclusive | display.perRoomPerNight.allInclusive | hourlyAvailableSlots.duration | hourlyAvailableSlots.from |

#  Scenario: Yanolja check hourly rates when bookingDurationType is nightly
#    Given bookingDurationType nightly
#    And Hotels 202059486 checkin 2020-05-24 los 1
#    And BookingDate 2020-04-24 10:00:00
#    When The user search
#    Then Yanolja Nightly room should be
#      | RoomID     | SupplierId | ChannelID | Adults |display.perBook.exclusive  | display.perBook.allInclusive | display.perRoomPerNight.exclusive | display.perRoomPerNight.allInclusive | hourlyAvailableSlots.duration | hourlyAvailableSlots.from |
#      | 1196822612 | 27989      | 1         | 2      | 37190                     | 45000                        | 37190                             | 45000                                | -                             | -                         |

#  Scenario: Yanolja check hourly rates when bookingDurationType is empty
#    Given bookingDurationType empty
#    And Hotels 202059486 checkin 2020-05-24 los 2
#    And BookingDate 2020-04-24 10:00:00
#    When The user search
#    Then Yanolja Nightly room should be
#      | RoomID     | SupplierId | ChannelID | Adults |display.perBook.exclusive  | display.perBook.allInclusive | display.perRoomPerNight.exclusive | display.perRoomPerNight.allInclusive | hourlyAvailableSlots.duration | hourlyAvailableSlots.from |
#      | 1196822612 | 27989      | 1         | 2      | 74380                     | 90000                        | 37190                             | 45000                                | -                             | -                         |
