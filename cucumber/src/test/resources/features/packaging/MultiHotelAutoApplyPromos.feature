@Package
@MultiHotelAutoApplyPromos
Feature: MultiHotelAutoApplyPromos

  # TODO: Remove these tests when integrating CARTPACK-337 since there's no multi hotel logic
  # PackagesAAP: Packages Auto Apply Promo Campaign Discount

  Background:
    Given All Default Parameters
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Origin TH
    And Currency THB
    And CID 1831843
    And Language 1
    And BookingDate 2017-08-29
    And UserId f4707a91-3415-4ee6-bf1b-c004d5326332
    

  Scenario: 0(Control Scenario) Normal Property Search - No PackageAAP & No packaging object
    Given Hotels 46749 checkin 2022-09-04 los 1
    And BookingDate 2022-08-15
    And CID 31755
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Origin TH
    And Currency THB
    And ClientDiscount true
    And AutoApplyPromos true
    And IsIncludedPriceInfo true
    When The user search
    Then Return Promotion Price Peek For AutoApplyPromos
      | Hotel | PromoEligible | SupplierId | RoomPrice(Ex) | RoomPrice(AllIn) | PseudoCoupon(Ex) | PseudoCoupon(AllIn) | Cid | PromoAppliedOnFinalPrice | GoToTravel Discount | PromotionPricePeekPerBook(Ex) | PromotionPricePeekPerBook(AllIn) | OriginalTotal(Ex) | OriginalTotal(AllIn) |
      | 46749 | false         | 332        | 3302.01       | 3632.22          | 0.0              | 0.0                 | 0.0 | false                    | 0.0                 | 0.0                           | 0.0                              | 3302.01           | 3632.22              |
    Then Actual display price should match the expected result
      | roomid   | channel | ratecategory | occupancy | adults | children | Price.Display.PerBook.exclusive | Price.Display.PerBook.inclusive | Price.Display.PerRoomPerNight.exclusive | Price.Display.PerRoomPerNight.inclusive | Price.Display.PerNight.exclusive | Price.Display.PerNight.inclusive |
      | 13194322 | 1       | 585739       | 2         | 2      | 0        | 3302.01                         | 3632.22                         | 3302.01                                 | 3632.22                                 | 3302.01                          | 3632.22                          |

  Scenario: 1.1 MultiHotelSearch - CARTPACK-337 user A, 2nd Property having PackageAAP & 1st Property (as Packaging Token)
    Given Hotels 46749 checkin 2022-09-04 los 1
    And BookingDate 2022-08-15
    And CID 31755
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Origin TH
    And Currency THB
    And Packaging ClientToken JAzOkO913KyuYxxm09uVY91LgS4b2ewXDrJMMWy72St82I9zhwjPWxs3/Nu96HcO4KsdvwRVT7aGmrCpOz67rfdY+9bG2/d2nCviFopXy9vwhPEnPg4Gu+G5Dnl4lL5NVftAzLbyzyukveAW9Yy+mqaOcxl4vGj/1aBLL33b3lhoUezy9JZBOuQA0D8pKI8dA/xyv2un3EpNEWIObzz5Tq81NFYw8wGIxoT7To9/mZ82QgWRvfD5UM+v8G6nXSlbaAPkJDYMHWSBOFuGlthkiuB/MM7DYt5CIZ+q32V13QhtG7MPQqHjcbF/14NaoR8siDzIqGixzRSxnkdeNxHH5BwOcz44St3hHoPAv5e7myt4gQdkweKsOYMvJinWNYORDk1cPiJhSDlaAmODSWZe2tI2rlskV and InterSystemToken NAAADUPAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjM5OTQuMTcsImFsIjo0NzAxLjE0fSwiY3IhAHI0NTE2LjUyIQDUNTIyMy40OX0sInBzQyIAMjAuMB4AkDAuMH0sInJlYhoAP21kQSEABDFvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmenAA0yaHJnIAHtY3QiOiJSb29tIiwidHQ3AChpbjcAT3RBZ2QjAA05SHRsmQAkaW7AACFjaHIAAHEAEjRTAfABIkFnb2RhIiwiYiI6IlBSUGoBEXHUAUBpIjotBwDzBWkiOnRydWUsImNvIjoiMSJ9XX0stQCZVGF4QW5kRmVlugBTNzA2LjmWAAIMAAu4AA4hAA+2ABYfOLYAAQm0ABYyswAifSw1AB83NQAOHjE1AC8xMDYADggfAYF9XX1dLCJwbRwA4XNyIjpmYWxzZSwibG9zEwBgbm9SIjoxJAA/dEJQNwIBUH19fX19
    And IsMultiHotel true
    And ClientDiscount true
    And AutoApplyPromos true
    And IsIncludedPriceInfo true
    And Experiment CARTPACK-337 User A
    When The user search
    Then Return Promotion Price Peek For AutoApplyPromos
      | Hotel | PromoEligible | SupplierId | RoomPrice(Ex) | RoomPrice(AllIn) | PseudoCoupon(Ex) | PseudoCoupon(AllIn) | Cid   | PromoAppliedOnFinalPrice | GoToTravel Discount | PromotionPricePeekPerBook(Ex) | PromotionPricePeekPerBook(AllIn) | OriginalTotal(Ex) | OriginalTotal(AllIn) |
      | 46749 | true          | 332        | 2351.03       | 2615.19          | 0.0              | 0.0                 | 31755 | true                     | 290.58              | 2351.03                       | 2615.19                          | 2641.61           | 2905.77              |
    Then Actual multi hotel display price should match the expected result
      | roomid   | channel | ratecategory | occupancy | adults | children | isFit | Promotion | Cancellation | displayPerBookBundledExc | displayPerBookBundledAllInc | displayPerBookDiffExc | displayPerBookDiffAllInc | displayPerPaxBundledExc | displayPerPaxBundledAllInc | displayPerPaxDiffExc | displayPerPaxDiffAllInc | crossedOutPerBookBundledExc | crossedOutPerBookBundledAllInc | crossedOutPerPaxBundledExc | crossedOutPerPaxBundledAllInc | savingsPerBookBundledExc | savingsPerBookBundledAllInc | savingsPerPaxBundledExc | savingsPerPaxBundledAllInc | totalDiscountPercent | Display.Perbook.exc | Display.Perbook.allinc | CrossedOut.Perbook.exc | CrossedOut.Perbook.allInc |
      | 13194322 | 8       | 585739       | 2         | 2      | 0        | true  | 0         | 1D1N_100P    | 6345.2                   | 7316.33                     | 0.0                   | 0.0                      | 3172.6                  | 3658.16                    | 0.0                  | 0.0                     | 11120.55                    | 11827.52                       | 5560.27                    | 5913.76                       | 4775.35                  | 4511.19                     | 2387.68                 | 2255.59                    | 38                   | 2351.03             | 2615.19                | 6604.03                | 6604.03                   |
    Then Actual new multi product bundle total saving per book should match the expected result
      | uid                                  | crossedOut.exc | crossedOut.allInc | crossedOutSaving.amount.exc | crossedOutSaving.amount.allInc | crossedOutSaving.percent.exc | crossedOutSaving.percent.allInc | additionalRate.exc | additionalRate.allInc | additionalRateSaving.amount.exc | additionalRateSaving.amount.allInc | additionalRateSaving.percent.exc | additionalRateSaving.percent.allInc | display.exc | display.allInc | original.exc | original.allInc |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | 11120.55       | 11827.52          | 4775.35                     | 4511.19                        | 42                           | 38                              | 7296.18            | 8333.36               | 950.98                          | 1017.03                            | 13                               | 12                                  | 6345.2      | 7316.33        | 6635.78      | 7606.91         |
    Then Actual packaging display charges price should match the expected result
      | roomid   | channel | ratecategory | Promotion | chargesTypeId    | quantity | payToAgodaExc | payToAgodaInc | payToHotelExc | payToHotelInc | totalExc | totalInc |
      | 13194322 | 8       | 585739       | 0         | Room             | 1        | 6635.78       | 7606.91       | 0             | 0             | 6635.78  | 7606.91  |
      | 13194322 | 8       | 585739       | 0         | CampaignDiscount | 1        | -290.58       | -290.58       | 0             | 0             | -290.58  | -290.58  |
      | 13194322 | 8       | 585739       | 0         | TaxAndFee        | 1        | 971.13        | 971.13        | 0             | 0             | 971.13   | 971.13   |
    Then Packaging token should match the expected result
      | clientToken | interSystemToken                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
      | true        | NAAAHGvAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjM5OTQuMTcsImFsIjo0NzAxLjE0fSwiY3IhAHI0NTE2LjUyIQDUNTIyMy40OX0sInBzQyIAMjAuMB4AkDAuMH0sInJlYhoAP21kQSEABDFvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmenAA0yaHJnIAHtY3QiOiJSb29tIiwidHQ3AChpbjcAT3RBZ2QjAA05SHRsmQAkaW7AACFjaHIAAHEAEjRTAfABIkFnb2RhIiwiYiI6IlBSUGoBEXHUAUBpIjotBwDzBWkiOnRydWUsImNvIjoiMSJ9XX0stQCZVGF4QW5kRmVlugBTNzA2LjmWAAIMAAu4AA4hAA+2ABYfOLYAAQm0ABYyswAifSw1AB83NQAOHjE1AC8xMDYADggfAYF9XX1dLCJwbRwA4XNyIjpmYWxzZSwibG9zEwCDbm9SIjoxfSwPA/8waDBJcE5IS0RCQUNJQkFvbHNCSE1BUktDVEZFTVU1Zk1UQXdVRkNZQlJJQ0NBRWFCQ2dFTUFFaUJBZ0VHQUk9DwMBcjIzNTEuMDNoAnkyNjE1LjE5DwNFNjYwNCEAAw0AUH0sImFSIQAVcCcAcjMzMDIuMDEnAHMzNjMyLjIyhQEfMT4DX2MyNjQxLjaOAH8yOTA1Ljc3PgMQBTcAKGluNwAIhgIPIwABDz4DcGMyNjQuMTaWAAEMAA64AA4hAA+2AA0PCQNaBOEA+QFDYW1wYWlnbkRpc2NvdW506AByLTI5MC41OKoAAw0AC+oADyMAAQ/sABYvMTLtAA9oNzU1NTQ3nAEKxAMCugELwwNVXSwidEJ2A1M2MzQ1LmMGwDczMTYuMzN9fX19fQ== |
    Then Actual multi product breakdown should match the expected per book result
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | total.pb.exc | total.pb.allInc | crossedOut.pb.exc | crossedOut.pb.allInc | originalPrice.pb.exc | originalPrice.pb.allInc |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | -            | -               | 4516.52           | 5223.49              | -                    | -                       |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | -            | -               | 6604.03           | 6604.03              | -                    | -                       |
    Then Actual multi product breakdown additional rate per book
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | additionalRate.pb.exc | additionalRate.pb.allInc |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | 3994.17               | 4701.14                  |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | 3302.01               | 3632.22                  |

  Scenario: 1.1 MultiHotelSearch - CARTPACK-337 user B, 2nd Property no PackageAAP & 1st Property (as Packaging Token). Get same rate channel with normal property search
    Given Hotels 46749 checkin 2022-09-04 los 1
    And BookingDate 2022-08-15
    And CID 31755
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Origin TH
    And Currency THB
    And Packaging ClientToken JAzOkO913KyuYxxm09uVY91LgS4b2ewXDrJMMWy72St82I9zhwjPWxs3/Nu96HcO4KsdvwRVT7aGmrCpOz67rfdY+9bG2/d2nCviFopXy9vwhPEnPg4Gu+G5Dnl4lL5NVftAzLbyzyukveAW9Yy+mqaOcxl4vGj/1aBLL33b3lhoUezy9JZBOuQA0D8pKI8dA/xyv2un3EpNEWIObzz5Tq81NFYw8wGIxoT7To9/mZ82QgWRvfD5UM+v8G6nXSlbaAPkJDYMHWSBOFuGlthkiuB/MM7DYt5CIZ+q32V13QhtG7MPQqHjcbF/14NaoR8siDzIqGixzRSxnkdeNxHH5BwOcz44St3hHoPAv5e7myt4gQdkweKsOYMvJinWNYORDk1cPiJhSDlaAmODSWZe2tI2rlskV and InterSystemToken NAAADUPAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjM5OTQuMTcsImFsIjo0NzAxLjE0fSwiY3IhAHI0NTE2LjUyIQDUNTIyMy40OX0sInBzQyIAMjAuMB4AkDAuMH0sInJlYhoAP21kQSEABDFvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmenAA0yaHJnIAHtY3QiOiJSb29tIiwidHQ3AChpbjcAT3RBZ2QjAA05SHRsmQAkaW7AACFjaHIAAHEAEjRTAfABIkFnb2RhIiwiYiI6IlBSUGoBEXHUAUBpIjotBwDzBWkiOnRydWUsImNvIjoiMSJ9XX0stQCZVGF4QW5kRmVlugBTNzA2LjmWAAIMAAu4AA4hAA+2ABYfOLYAAQm0ABYyswAifSw1AB83NQAOHjE1AC8xMDYADggfAYF9XX1dLCJwbRwA4XNyIjpmYWxzZSwibG9zEwBgbm9SIjoxJAA/dEJQNwIBUH19fX19
    And IsMultiHotel true
    And ClientDiscount true
    And AutoApplyPromos true
    And IsIncludedPriceInfo true
    And Experiment CARTPACK-337 User B
    When The user search
    Then Return Promotion Price Peek For AutoApplyPromos
      | Hotel | PromoEligible | SupplierId | RoomPrice(Ex) | RoomPrice(AllIn) | PseudoCoupon(Ex) | PseudoCoupon(AllIn) | Cid | PromoAppliedOnFinalPrice | GoToTravel Discount | PromotionPricePeekPerBook(Ex) | PromotionPricePeekPerBook(AllIn) | OriginalTotal(Ex) | OriginalTotal(AllIn) |
      | 46749 | false         | 332        | 3302.01       | 3632.22          | 0.0              | 0.0                 | 0.0 | false                    | 0.0                 | 0.0                           | 0.0                              | 3302.01           | 3632.22              |
    Then Actual multi hotel display price should match the expected result
      | roomid   | channel | ratecategory | occupancy | adults | children | isFit | Promotion | Cancellation | displayPerBookBundledExc | displayPerBookBundledAllInc | displayPerBookDiffExc | displayPerBookDiffAllInc | displayPerPaxBundledExc | displayPerPaxBundledAllInc | displayPerPaxDiffExc | displayPerPaxDiffAllInc | crossedOutPerBookBundledExc | crossedOutPerBookBundledAllInc | crossedOutPerPaxBundledExc | crossedOutPerPaxBundledAllInc | savingsPerBookBundledExc | savingsPerBookBundledAllInc | savingsPerPaxBundledExc | savingsPerPaxBundledAllInc | totalDiscountPercent | Display.Perbook.exc | Display.Perbook.allinc | CrossedOut.Perbook.exc | CrossedOut.Perbook.allInc |
      | 13194322 | 1       | 585739       | 2         | 2      | 0        | true  | 0         | 1D1N_100P    | 7296.18                  | 8333.36                     | 0.0                   | 0.0                      | 3648.09                 | 4166.68                    | 0.0                  | 0.0                     | 11120.55                    | 11827.52                       | 5560.27                    | 5913.76                       | 3824.37                  | 3494.16                     | 1912.18                 | 1747.08                    | 29                   | 3302.01             | 3632.22                | 6604.03                | 6604.03                   |
    Then Actual new multi product bundle total saving per book should match the expected result
      | uid                                  | crossedOut.exc | crossedOut.allInc | crossedOutSaving.amount.exc | crossedOutSaving.amount.allInc | crossedOutSaving.percent.exc | crossedOutSaving.percent.allInc | additionalRate.exc | additionalRate.allInc | additionalRateSaving.amount.exc | additionalRateSaving.amount.allInc | additionalRateSaving.percent.exc | additionalRateSaving.percent.allInc | display.exc | display.allInc | original.exc | original.allInc |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | 11120.55       | 11827.52          | 3824.37                     | 3494.16                        | 34                           | 29                              | -                  | -                     | -                               | -                                  | -                                | -                                   | 7296.18     | 8333.36        | 7296.18      | 8333.36         |
    Then Actual packaging display charges price should match the expected result
      | roomid   | channel | ratecategory | Promotion | chargesTypeId | quantity | payToAgodaExc | payToAgodaInc | payToHotelExc | payToHotelInc | totalExc | totalInc |
      | 13194322 | 1       | 585739       | 0         | Room          | 1        | 7296.18       | 8333.36       | 0             | 0             | 7296.18  | 8333.36  |
      | 13194322 | 1       | 585739       | 0         | TaxAndFee     | 1        | 1037.18       | 1037.18       | 0             | 0             | 1037.18  | 1037.18  |
    Then Packaging token should match the expected result
      | clientToken | interSystemToken                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
      | true        | NAAAGMvAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjM5OTQuMTcsImFsIjo0NzAxLjE0fSwiY3IhAHI0NTE2LjUyIQDUNTIyMy40OX0sInBzQyIAMjAuMB4AkDAuMH0sInJlYhoAP21kQSEABDFvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmenAA0yaHJnIAHtY3QiOiJSb29tIiwidHQ3AChpbjcAT3RBZ2QjAA05SHRsmQAkaW7AACFjaHIAAHEAEjRTAfABIkFnb2RhIiwiYiI6IlBSUGoBEXHUAUBpIjotBwDzBWkiOnRydWUsImNvIjoiMSJ9XX0stQCZVGF4QW5kRmVlugBTNzA2LjmWAAIMAAu4AA4hAA+2ABYfOLYAAQm0ABYyswAifSw1AB83NQAOHjE1AC8xMDYADggfAYF9XX1dLCJwbRwA4XNyIjpmYWxzZSwibG9zEwCDbm9SIjoxfSwPA7BoMElwTkhLREJBQwcD/yFsc0JITUFSS0NURkVNVTVmTVRBd1VGQ1lCUklDQ0FFYUJDZ0VNQUVpQkFnRUdBST0PAwJiMzAyLjAxaAJ5MzYzMi4yMg8DcjY2MDQuMDMhAAMNAA8PA2APpwAADw8DEQQ3AChpbjcACQ8DDyMAAA8PA3BUMzMwLjKWAAEMAA64AA4hAA+2AA0P2gJ+VV0sInRCjQJyNzI5Ni4xOOYBwDgzMzMuMzZ9fX19fQ== |
    Then Actual multi product breakdown should match the expected per book result
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | total.pb.exc | total.pb.allInc | crossedOut.pb.exc | crossedOut.pb.allInc | originalPrice.pb.exc | originalPrice.pb.allInc |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | 3994.17      | 4701.14         | 4516.52           | 5223.49              | 3994.17              | 4701.14                 |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | 3302.01      | 3632.22         | 6604.03           | 6604.03              | 3302.01              | 3632.22                 |
    Then Actual multi product breakdown additional rate per book
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | additionalRate.pb.exc | additionalRate.pb.allInc |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | -                     | -                        |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | -                     | -                        |

  Scenario: 1.2 MultiHotelSearch - 2nd Property having PackageAAP & 1st Property (as Packaging Token having PackageAAP)
    Given Hotels 46749 checkin 2022-09-04 los 1
    And BookingDate 2022-08-15
    And CID 31755
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Origin TH
    And Currency THB
    And Packaging ClientToken Ji+384Mbp3/czraQgbko8HIbLlqColy2yW2mx2Bf9uywdAfPMwOV1wBQmfqxoPF+qjPWcPhTJDUPp4+1Lm9AV3FrrLByPzTsL3cGfBwlj9aaKvBcdSorVHzPacSbJzScUYev5l6E44lEcS8Oxl0PsKySL4Wj/nrEPuz3Q6pSizGiJLoh50Si2gOlZ0BOf6/4d+BGLRISJmkkQYNenIakeLecmIGxCVq0R4fdPm1kh+mEApUsQDTcJtg5jucFyisUYB7RpgRC9mmezWRNVXJL52GVV0eLEQZo3jQMWqCpcyXhPdveq8QUx1uhvtV9w+Qz0AzBgbkaZ1JFbXL2AAfIGgcj3QYR8efUSsVlaIy+f9EWcUZZBT5ZXgoFJtF8L8IjG03KlrcIjgKsOe5WRoMBvB47S+39F0z8raZ4y7bWG1GeTbwVJtAaWah6MgujXx0K8VkUbNQSwtq4= and InterSystemToken NAAAEOvAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjM1MjQuMDYsImFsIjo0MjMxLjAzfSwiY3IhAHI0NTE2LjUyIQDANTIyMy40OX0sImFSIQAVcCcAczM5OTQuMTdIAGA3MDEuMTRIALRoIjotMX0sInBzQysAMjAuMCcAkDAuMH0sInJlYhoAP21kQSEABDFvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmePAA4icmdQAe1jdCI6IlJvb20iLCJ0dDcAKGluNwBPdEFnZCMADTlIdGyZACRpbsAAIWNocgAAcQASNIMB8AEiQWdvZGEiLCJiIjoiUFJQmgERcQQCEGkvAfMILCJpaSI6dHJ1ZSwiY28iOiIxIn1dfSy1AJlUYXhBbmRGZWW6AFM3MDYuOZYAAgwAC7gADiEAD7YAFh84tgABCbQAFjKzACJ9LDUAHzc1AA4eMTUALzEwNgAOCB8BBxYB+QFDYW1wYWlnbkRpc2NvdW50HQFyLTQ3MC4xMd8AAw0ACx8BDyMAAQ8hARYvMTLtAA9oNzU1NTQ30QFRXSwicG0gACRzcu8BMWxvcxIAYG5vUiI6MSMAP3RCUPECAVB9fX19fQ==
    And IsMultiHotel true
    And ClientDiscount true
    And AutoApplyPromos true
    And IsIncludedPriceInfo true
    And Experiment CARTPACK-337 User A
    When The user search
    Then Actual hotel price info should match the expected result
      | roomid   | channel | ratecategory | Promotion | priceInfo.netExclusive | priceInfo.netInclusive | priceInfo.margin | priceInfo.processingFee |
      | 13194322 | 8       | 585739       | 0         | 1981.21                | 2179.33                | 660.4            | 66.04                   |
    Then Return Promotion Price Peek For AutoApplyPromos
      | Hotel | PromoEligible | SupplierId | RoomPrice(Ex) | RoomPrice(AllIn) | PseudoCoupon(Ex) | PseudoCoupon(AllIn) | Cid   | PromoAppliedOnFinalPrice | GoToTravel Discount | PromotionPricePeekPerBook(Ex) | PromotionPricePeekPerBook(AllIn) | OriginalTotal(Ex) | OriginalTotal(AllIn) |
      | 46749 | true          | 332        | 2351.03       | 2615.19          | 0.0              | 0.0                 | 31755 | true                     | 290.58              | 2351.03                       | 2615.19                          | 2641.61           | 2905.77              |
    Then Actual multi hotel display price should match the expected result
      | roomid   | channel | ratecategory | occupancy | adults | children | isFit | Promotion | Cancellation | displayPerBookBundledExc | displayPerBookBundledAllInc | displayPerBookDiffExc | displayPerBookDiffAllInc | displayPerPaxBundledExc | displayPerPaxBundledAllInc | displayPerPaxDiffExc | displayPerPaxDiffAllInc | crossedOutPerBookBundledExc | crossedOutPerBookBundledAllInc | crossedOutPerPaxBundledExc | crossedOutPerPaxBundledAllInc | savingsPerBookBundledExc | savingsPerBookBundledAllInc | savingsPerPaxBundledExc | savingsPerPaxBundledAllInc | totalDiscountPercent | Display.Perbook.exc | Display.Perbook.allinc | CrossedOut.Perbook.exc | CrossedOut.Perbook.allInc |
      | 13194322 | 8       | 585739       | 2         | 2      | 0        | true  | 0         | 1D1N_100P    | 5875.09                  | 6846.22                     | 0.0                   | 0.0                      | 2937.55                 | 3423.11                    | 0.0                  | 0.0                     | 11120.55                    | 11827.52                       | 5560.27                    | 5913.76                       | 5245.46                  | 4981.3                      | 2622.73                 | 2490.65                    | 42                   | 2351.03             | 2615.19                | 6604.03                | 6604.03                   |
    Then Actual new multi product bundle total saving per book should match the expected result
      | uid                                  | crossedOut.exc | crossedOut.allInc | crossedOutSaving.amount.exc | crossedOutSaving.amount.allInc | crossedOutSaving.percent.exc | crossedOutSaving.percent.allInc | additionalRate.exc | additionalRate.allInc | additionalRateSaving.amount.exc | additionalRateSaving.amount.allInc | additionalRateSaving.percent.exc | additionalRateSaving.percent.allInc | display.exc | display.allInc | original.exc | original.allInc |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | 11120.55       | 11827.52          | 5245.46                     | 4981.3                         | 47                           | 42                              | 7296.18            | 8333.36               | 1421.09                         | 1487.14                            | 19                               | 17                                  | 5875.09     | 6846.22        | 6635.78      | 7606.91         |
    Then Actual packaging display charges price should match the expected result
      | roomid   | channel | ratecategory | Promotion | chargesTypeId    | quantity | payToAgodaExc | payToAgodaInc | payToHotelExc | payToHotelInc | totalExc | totalInc |
      | 13194322 | 8       | 585739       | 0         | Room             | 1        | 6635.78       | 7606.91       | 0             | 0             | 6635.78  | 7606.91  |
      | 13194322 | 8       | 585739       | 0         | CampaignDiscount | 1        | -760.69       | -760.69       | 0             | 0             | -760.69  | -760.69  |
      | 13194322 | 8       | 585739       | 0         | TaxAndFee        | 1        | 971.13        | 971.13        | 0             | 0             | 971.13   | 971.13   |
    Then Packaging token should match the expected result
      | clientToken | interSystemToken                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
      | true        | NAAAIBfAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjM1MjQuMDYsImFsIjo0MjMxLjAzfSwiY3IhAHI0NTE2LjUyIQDANTIyMy40OX0sImFSIQAVcCcAczM5OTQuMTdIAGA3MDEuMTRIALRoIjotMX0sInBzQysAMjAuMCcAkDAuMH0sInJlYhoAP21kQSEABDFvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmePAA4icmdQAe1jdCI6IlJvb20iLCJ0dDcAKGluNwBPdEFnZCMADTlIdGyZACRpbsAAIWNocgAAcQASNIMB8AEiQWdvZGEiLCJiIjoiUFJQmgERcQQCEGkvAfMILCJpaSI6dHJ1ZSwiY28iOiIxIn1dfSy1AJlUYXhBbmRGZWW6AFM3MDYuOZYAAgwAC7gADiEAD7YAFh84tgABCbQAFjKzACJ9LDUAHzc1AA4eMTUALzEwNgAOCB8BBxYB+QFDYW1wYWlnbkRpc2NvdW50HQFyLTQ3MC4xMd8AAw0ACx8BDyMAAQ8hARYvMTLtAA9oNzU1NTQ30QFRXSwicG0gACRzcu8BMWxvcxIAg25vUiI6MX0s+QP/MGgwSXBOSEtEQkFDSUJBb2xzQkhNQVJLQ1RGRU1VNWZNVEF3VUZDWUJSSUNDQUVhQkNnRU1BRWlCQWdFR0FJPfkDATAyMzXsAwIiA3kyNjE1LjE5+QNFNjYwNCEAAw0AD/kDAWIzMDIuMDEnAHMzNjMyLjIyHgEP+ANgYzI2NDEuNo4AfzI5MDUuNzf4AxAFNwAoaW43AAhAAw8jAAEP+ANwYzI2NC4xNpYAAQwADrgADiEAD7YADQ/DA4BjMjkwLjU4oAMCDQAMwwMPIwAAD8MDb1VdLCJ0QnYDczU4NzUuMDlVA7A4NDYuMjJ9fX19fQ== |
    Then Actual multi product breakdown should match the expected per book result
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | total.pb.exc | total.pb.allInc | crossedOut.pb.exc | crossedOut.pb.allInc | originalPrice.pb.exc | originalPrice.pb.allInc |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | -            | -               | 4516.52           | 5223.49              | -                    | -                       |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | -            | -               | 6604.03           | 6604.03              | -                    | -                       |
    Then Actual multi product breakdown additional rate per book
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | additionalRate.pb.exc | additionalRate.pb.allInc |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | 3994.17               | 4701.14                  |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | 3302.01               | 3632.22                  |

  Scenario: 1.2 MultiHotelSearch - 2nd Property having PackageAAP & 1st Property (as Packaging Token having PackageAAP), CARTPACK-337 User B. Get same rate channel with normal property search
    Given Hotels 46749 checkin 2022-09-04 los 1
    And BookingDate 2022-08-15
    And CID 31755
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Origin TH
    And Currency THB
    And Packaging ClientToken Ji+384Mbp3/czraQgbko8HIbLlqColy2yW2mx2Bf9uywdAfPMwOV1wBQmfqxoPF+qjPWcPhTJDUPp4+1Lm9AV3FrrLByPzTsL3cGfBwlj9aaKvBcdSorVHzPacSbJzScUYev5l6E44lEcS8Oxl0PsKySL4Wj/nrEPuz3Q6pSizGiJLoh50Si2gOlZ0BOf6/4d+BGLRISJmkkQYNenIakeLecmIGxCVq0R4fdPm1kh+mEApUsQDTcJtg5jucFyisUYB7RpgRC9mmezWRNVXJL52GVV0eLEQZo3jQMWqCpcyXhPdveq8QUx1uhvtV9w+Qz0AzBgbkaZ1JFbXL2AAfIGgcj3QYR8efUSsVlaIy+f9EWcUZZBT5ZXgoFJtF8L8IjG03KlrcIjgKsOe5WRoMBvB47S+39F0z8raZ4y7bWG1GeTbwVJtAaWah6MgujXx0K8VkUbNQSwtq4= and InterSystemToken NAAAEOvAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjM1MjQuMDYsImFsIjo0MjMxLjAzfSwiY3IhAHI0NTE2LjUyIQDANTIyMy40OX0sImFSIQAVcCcAczM5OTQuMTdIAGA3MDEuMTRIALRoIjotMX0sInBzQysAMjAuMCcAkDAuMH0sInJlYhoAP21kQSEABDFvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmePAA4icmdQAe1jdCI6IlJvb20iLCJ0dDcAKGluNwBPdEFnZCMADTlIdGyZACRpbsAAIWNocgAAcQASNIMB8AEiQWdvZGEiLCJiIjoiUFJQmgERcQQCEGkvAfMILCJpaSI6dHJ1ZSwiY28iOiIxIn1dfSy1AJlUYXhBbmRGZWW6AFM3MDYuOZYAAgwAC7gADiEAD7YAFh84tgABCbQAFjKzACJ9LDUAHzc1AA4eMTUALzEwNgAOCB8BBxYB+QFDYW1wYWlnbkRpc2NvdW50HQFyLTQ3MC4xMd8AAw0ACx8BDyMAAQ8hARYvMTLtAA9oNzU1NTQ30QFRXSwicG0gACRzcu8BMWxvcxIAYG5vUiI6MSMAP3RCUPECAVB9fX19fQ==
    And IsMultiHotel true
    And ClientDiscount true
    And AutoApplyPromos true
    And IsIncludedPriceInfo true
    And Experiment CARTPACK-337 User B
    When The user search
    Then Actual hotel price info should match the expected result
      | roomid   | channel | ratecategory | Promotion | priceInfo.netExclusive | priceInfo.netInclusive | priceInfo.margin | priceInfo.processingFee |
      | 13194322 | 1       | 585739       | 0         | 2476.51                | 2724.16                | 825.5            | 82.56                   |
    Then Return Promotion Price Peek For AutoApplyPromos
      | Hotel | PromoEligible | SupplierId | RoomPrice(Ex) | RoomPrice(AllIn) | PseudoCoupon(Ex) | PseudoCoupon(AllIn) | Cid | PromoAppliedOnFinalPrice | GoToTravel Discount | PromotionPricePeekPerBook(Ex) | PromotionPricePeekPerBook(AllIn) | OriginalTotal(Ex) | OriginalTotal(AllIn) |
      | 46749 | false         | 332        | 3302.01       | 3632.22          | 0.0              | 0.0                 | 0.0 | false                    | 0.0                 | 0.0                           | 0.0                              | 3302.01           | 3632.22              |
    Then Actual multi hotel display price should match the expected result
      | roomid   | channel | ratecategory | occupancy | adults | children | isFit | Promotion | Cancellation | displayPerBookBundledExc | displayPerBookBundledAllInc | displayPerBookDiffExc | displayPerBookDiffAllInc | displayPerPaxBundledExc | displayPerPaxBundledAllInc | displayPerPaxDiffExc | displayPerPaxDiffAllInc | crossedOutPerBookBundledExc | crossedOutPerBookBundledAllInc | crossedOutPerPaxBundledExc | crossedOutPerPaxBundledAllInc | savingsPerBookBundledExc | savingsPerBookBundledAllInc | savingsPerPaxBundledExc | savingsPerPaxBundledAllInc | totalDiscountPercent | Display.Perbook.exc | Display.Perbook.allinc | CrossedOut.Perbook.exc | CrossedOut.Perbook.allInc |
      | 13194322 | 1       | 585739       | 2         | 2      | 0        | true  | 0         | 1D1N_100P    | 6826.07                  | 7863.25                     | 0.0                   | 0.0                      | 3413.03                 | 3931.62                    | 0.0                  | 0.0                     | 11120.55                    | 11827.52                       | 5560.27                    | 5913.76                       | 4294.48                  | 3964.27                     | 2147.24                 | 1982.13                    | 33                   | 3302.01             | 3632.22                | 6604.03                | 6604.03                   |
    Then Actual new multi product bundle total saving per book should match the expected result
      | uid                                  | crossedOut.exc | crossedOut.allInc | crossedOutSaving.amount.exc | crossedOutSaving.amount.allInc | crossedOutSaving.percent.exc | crossedOutSaving.percent.allInc | additionalRate.exc | additionalRate.allInc | additionalRateSaving.amount.exc | additionalRateSaving.amount.allInc | additionalRateSaving.percent.exc | additionalRateSaving.percent.allInc | display.exc | display.allInc | original.exc | original.allInc |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | 11120.55       | 11827.52          | 4294.48                     | 3964.27                        | 38                           | 33                              | 7296.18            | 8333.36               | 470.11                          | 470.11                             | 6                                | 5                                   | 6826.07     | 7863.25        | 7296.18      | 8333.36         |
    Then Actual packaging display charges price should match the expected result
      | roomid   | channel | ratecategory | Promotion | chargesTypeId    | quantity | payToAgodaExc | payToAgodaInc | payToHotelExc | payToHotelInc | totalExc | totalInc |
      | 13194322 | 1       | 585739       | 0         | Room             | 1        | 7296.18       | 8333.36       | 0             | 0             | 7296.18  | 8333.36  |
      | 13194322 | 1       | 585739       | 0         | TaxAndFee        | 1        | 1037.18       | 1037.18       | 0             | 0             | 1037.18  | 1037.18  |
      | 13194322 | 1       | 585739       | 0         | CampaignDiscount | 1        | -470.11       | -470.11       | 0             | 0             | -470.11  | -470.11  |
    Then Packaging token should match the expected result
      | clientToken | interSystemToken                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
      | true        | NAAAHHPAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjM1MjQuMDYsImFsIjo0MjMxLjAzfSwiY3IhAHI0NTE2LjUyIQDANTIyMy40OX0sImFSIQAVcCcAczM5OTQuMTdIAGA3MDEuMTRIALRoIjotMX0sInBzQysAMjAuMCcAkDAuMH0sInJlYhoAP21kQSEABDFvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmePAA4icmdQAe1jdCI6IlJvb20iLCJ0dDcAKGluNwBPdEFnZCMADTlIdGyZACRpbsAAIWNocgAAcQASNIMB8AEiQWdvZGEiLCJiIjoiUFJQmgERcQQCEGkvAfMILCJpaSI6dHJ1ZSwiY28iOiIxIn1dfSy1AJlUYXhBbmRGZWW6AFM3MDYuOZYAAgwAC7gADiEAD7YAFh84tgABCbQAFjKzACJ9LDUAHzc1AA4eMTUALzEwNgAOCB8BBxYB+QFDYW1wYWlnbkRpc2NvdW50HQFyLTQ3MC4xMd8AAw0ACx8BDyMAAQ8hARYvMTLtAA9oNzU1NTQ30QFRXSwicG0gACRzcu8BMWxvcxIAg25vUiI6MX0s+QOwaDBJcE5IS0RCQUPxA/8hbHNCSE1BUktDVEZFTVU1Zk1UQXdVRkNZQlJJQ0NBRWFCQ2dFTUFFaUJBZ0VHQUk9+QMCYjMwMi4wMSIDeTM2MzIuMjL5A3I2NjA0LjAzIQADDQAPyQNgD6cAAA/JAxEENwAoaW43AAnJAw8jAAAPyQNwVDMzMC4ylgABDAAOuAAOIQAPqAIND5QDWgrZAk5mYWxz2gJVXSwidEKNAmM2ODI2LjCvBcA3ODYzLjI1fX19fX0= |
    Then Actual multi product breakdown should match the expected per book result
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | total.pb.exc | total.pb.allInc | crossedOut.pb.exc | crossedOut.pb.allInc | originalPrice.pb.exc | originalPrice.pb.allInc |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | -            | -               | 4516.52           | 5223.49              | -                    | -                       |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | -            | -               | 6604.03           | 6604.03              | -                    | -                       |
    Then Actual multi product breakdown additional rate per book
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | additionalRate.pb.exc | additionalRate.pb.allInc |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | 3994.17               | 4701.14                  |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | 3302.01               | 3632.22                  |

  Scenario: 1.3 MultiHotelSearch - 2nd Property having PackageAAP & 1st Property (as Packaging Token having PackageAAP & Normal Downlift)
    Given Hotels 46749 checkin 2022-09-04 los 1
    And BookingDate 2022-08-15
    And CID 31755
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Origin TH
    And Currency THB
    And Packaging ClientToken JZqn0T2HJXtho+nbLSDiypVcpvxKS4jShWm/Z/sQJCZqbsJPGskBTIZxnczWHrzEgwe7YmsyN6LhiQVoIUyLHWKhM8Tul1c4ub2MGAoiuzA3Xc/VwhFPgMALsQaULaHUHZCXVB7vVavXWTzuUoyqzO+LE9iL63uDYvOOB+Mf11imftAHjWuu3b7Hrfpqv5Q/VFEXhhxF/Q0qBY7h2ZgdTJq41g5hRgOtzTbyJFyE9DdLB2hUNrWyAsoADqG2GXIKEpah9XUjMxXQmGOazvVoaYv5IoECoot/rg7YlWB6zFj7USZu6lQQv1pYtW23FUUwZUSpyL35MowmkJm91htWMP8IS+XkDPBfyKnIVbff9moSlM3oPBpYMkd5WpuslyDQ7loj5U/jCwEtVe+0ZX5i+WhTB7F8gkY8Wp00gZff1fOoQIzHWZg0cIcvyCRu9kTC33qPd28rm0ztwfHoFBr+bcIoj/J5RBVU= and InterSystemToken NAAAEQPAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjMzMTIuNjEsImFsIjozOTc3LjE2fSwiY3IhAHI0NTU4Ljk0IQDANTIyMy40OX0sImFSIQAVcCcAcjM3NTQuNTInAHA0NDE5LjA3SAC0aCI6LTF9LCJwc0MrAGIyODIuMDcqAAIMAGB9LCJyZWIgADRtZEEnADIwLjAkAJEwLjB9LCJvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmeVAA4icmdWAe1jdCI6IlJvb20iLCJ0dDcAKGluNwBPdEFnZCMADTlIdGyZACRpbsAAIWNocgAAcQASNIkB8AEiQWdvZGEiLCJiIjoiUFJQoAERcQoCEGk1AfMILCJpaSI6dHJ1ZSwiY28iOiIxIn1dfSy1AJlUYXhBbmRGZWW6AGI2NjQuNTV3AAIMAAu4AA4hAA+2ABYfOLYAAQm0ABYyswAifSw1AB83NQAOHjE1AC8xMDYADggfAQcWAfkBQ2FtcGFpZ25EaXNjb3VudB0Bci00NDEuOTHfAAMNAAsfAQ8jAAEPIQEWLzEy7QAPaDc1NTU0N9EBUV0sInBtIAAkc3LvATFsb3MSAGBub1IiOjEjAD90QlDxAgFQfX19fX0=
    And IsMultiHotel true
    And ClientDiscount true
    And AutoApplyPromos true
    And IsIncludedPriceInfo true
    And Experiment CARTPACK-337 User A
    When The user search
    Then Actual hotel price info should match the expected result
      | roomid   | channel | ratecategory | Promotion | priceInfo.netExclusive | priceInfo.netInclusive | priceInfo.margin | priceInfo.processingFee |
      | 13194322 | 8       | 585739       | 0         | 1981.21                | 2179.33                | 660.4            | 66.04                   |
    Then Return Promotion Price Peek For AutoApplyPromos
      | Hotel | PromoEligible | SupplierId | RoomPrice(Ex) | RoomPrice(AllIn) | PseudoCoupon(Ex) | PseudoCoupon(AllIn) | Cid   | PromoAppliedOnFinalPrice | GoToTravel Discount | PromotionPricePeekPerBook(Ex) | PromotionPricePeekPerBook(AllIn) | OriginalTotal(Ex) | OriginalTotal(AllIn) |
      | 46749 | true          | 332        | 2351.03       | 2615.19          | 0.0              | 0.0                 | 31755 | true                     | 290.58              | 2351.03                       | 2615.19                          | 2641.61           | 2905.77              |
    Then Actual multi hotel display price should match the expected result
      | roomid   | channel | ratecategory | occupancy | adults | children | isFit | Promotion | Cancellation | displayPerBookBundledExc | displayPerBookBundledAllInc | displayPerBookDiffExc | displayPerBookDiffAllInc | displayPerPaxBundledExc | displayPerPaxBundledAllInc | displayPerPaxDiffExc | displayPerPaxDiffAllInc | crossedOutPerBookBundledExc | crossedOutPerBookBundledAllInc | crossedOutPerPaxBundledExc | crossedOutPerPaxBundledAllInc | savingsPerBookBundledExc | savingsPerBookBundledAllInc | savingsPerPaxBundledExc | savingsPerPaxBundledAllInc | totalDiscountPercent | Display.Perbook.exc | Display.Perbook.allinc | CrossedOut.Perbook.exc | CrossedOut.Perbook.allInc |
      | 13194322 | 8       | 585739       | 2         | 2      | 0        | true  | 0         | 1D1N_100P    | 5663.64                  | 6592.35                     | 0.0                   | 0.0                      | 2831.82                 | 3296.18                    | 0.0                  | 0.0                     | 11162.97                    | 11827.52                       | 5581.48                    | 5913.76                       | 5499.33                  | 5235.17                     | 2749.66                 | 2617.59                    | 44                   | 2351.03             | 2615.19                | 6604.03                | 6604.03                   |
    Then Actual new multi product bundle total saving per book should match the expected result
      | uid                                  | crossedOut.exc | crossedOut.allInc | crossedOutSaving.amount.exc | crossedOutSaving.amount.allInc | crossedOutSaving.percent.exc | crossedOutSaving.percent.allInc | additionalRate.exc | additionalRate.allInc | additionalRateSaving.amount.exc | additionalRateSaving.amount.allInc | additionalRateSaving.percent.exc | additionalRateSaving.percent.allInc | display.exc | display.allInc | original.exc | original.allInc |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | 11162.97       | 11827.52          | 5499.33                     | 5235.17                        | 49                           | 44                              | 7056.53            | 8051.29               | 1392.89                         | 1458.94                            | 19                               | 18                                  | 5663.64     | 6592.35        | 6396.13      | 7324.84         |
    Then Actual packaging display charges price should match the expected result
      | roomid   | channel | ratecategory | Promotion | chargesTypeId    | quantity | payToAgodaExc | payToAgodaInc | payToHotelExc | payToHotelInc | totalExc | totalInc |
      | 13194322 | 8       | 585739       | 0         | Room             | 1        | 6396.13       | 7324.84       | 0             | 0             | 6396.13  | 7324.84  |
      | 13194322 | 8       | 585739       | 0         | CampaignDiscount | 1        | -732.49       | -732.49       | 0             | 0             | -732.49  | -732.49  |
      | 13194322 | 8       | 585739       | 0         | TaxAndFee        | 1        | 928.71        | 928.71        | 0             | 0             | 928.71   | 928.71   |
    Then Packaging token should match the expected result
      | clientToken | interSystemToken                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
      | true        | NAAAIC/AEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjMzMTIuNjEsImFsIjozOTc3LjE2fSwiY3IhAHI0NTU4Ljk0IQDANTIyMy40OX0sImFSIQAVcCcAcjM3NTQuNTInAHA0NDE5LjA3SAC0aCI6LTF9LCJwc0MrAGIyODIuMDcqAAIMAGB9LCJyZWIgADRtZEEnADIwLjAkAJEwLjB9LCJvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmeVAA4icmdWAe1jdCI6IlJvb20iLCJ0dDcAKGluNwBPdEFnZCMADTlIdGyZACRpbsAAIWNocgAAcQASNIkB8AEiQWdvZGEiLCJiIjoiUFJQoAERcQoCEGk1AfMILCJpaSI6dHJ1ZSwiY28iOiIxIn1dfSy1AJlUYXhBbmRGZWW6AGI2NjQuNTV3AAIMAAu4AA4hAA+2ABYfOLYAAQm0ABYyswAifSw1AB83NQAOHjE1AC8xMDYADggfAQcWAfkBQ2FtcGFpZ25EaXNjb3VudB0Bci00NDEuOTHfAAMNAAsfAQ8jAAEPIQEWLzEy7QAPaDc1NTU0N9EBUV0sInBtIAAkc3LvATFsb3MSAINub1IiOjF9LP8D/zBoMElwTkhLREJBQ0lCQW9sc0JITUFSS0NURkVNVTVmTVRBd1VGQ1lCUklDQ0FFYUJDZ0VNQUVpQkFnRUdBST3/AwFzMjM1MS4wM40DaTYxNS4xOf8DRTY2MDQhAAMNAA//AwFUMzAyLjBHBGM2MzIuMjIeAQv+AwvXAw/4A0JFMjY0MdUEbzI5MDUuN/gDEQU3AChpbjcACPgDDyMAAA/4A3FjMjY0LjE2lgABDAAOuAAOIQAPtgAND8MDgGMyOTAuNTigAwINAAzDAw8jAAAPwwNvVV0sInRCdgNjNTY2My42VAfANjU5Mi4zNX19fX19 |
    Then Actual multi product breakdown should match the expected per book result
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | total.pb.exc | total.pb.allInc | crossedOut.pb.exc | crossedOut.pb.allInc | originalPrice.pb.exc | originalPrice.pb.allInc |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | -            | -               | 4558.94           | 5223.49              | -                    | -                       |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | -            | -               | 6604.03           | 6604.03              | -                    | -                       |
    Then Actual multi product breakdown additional rate per book
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | additionalRate.pb.exc | additionalRate.pb.allInc |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | 3754.52               | 4419.07                  |
      | 9d7394d5-aff4-2cf4-d48b-539469e17e58 | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIBAolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | 3302.01               | 3632.22                  |

  Scenario: 1.3 MultiHotelSearch - 2nd Property having PackageAAP & 1st Property (as Packaging Token having PackageAAP & Normal Downlift), CARTPACK-337 User B. Get same rate channel with normal property search
    Given Hotels 46749 checkin 2022-09-04 los 1
    And BookingDate 2022-08-15
    And CID 31755
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Origin TH
    And Currency THB
    And Packaging ClientToken JZqn0T2HJXtho+nbLSDiypVcpvxKS4jShWm/Z/sQJCZqbsJPGskBTIZxnczWHrzEgwe7YmsyN6LhiQVoIUyLHWKhM8Tul1c4ub2MGAoiuzA3Xc/VwhFPgMALsQaULaHUHZCXVB7vVavXWTzuUoyqzO+LE9iL63uDYvOOB+Mf11imftAHjWuu3b7Hrfpqv5Q/VFEXhhxF/Q0qBY7h2ZgdTJq41g5hRgOtzTbyJFyE9DdLB2hUNrWyAsoADqG2GXIKEpah9XUjMxXQmGOazvVoaYv5IoECoot/rg7YlWB6zFj7USZu6lQQv1pYtW23FUUwZUSpyL35MowmkJm91htWMP8IS+XkDPBfyKnIVbff9moSlM3oPBpYMkd5WpuslyDQ7loj5U/jCwEtVe+0ZX5i+WhTB7F8gkY8Wp00gZff1fOoQIzHWZg0cIcvyCRu9kTC33qPd28rm0ztwfHoFBr+bcIoj/J5RBVU= and InterSystemToken NAAAEQPAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjMzMTIuNjEsImFsIjozOTc3LjE2fSwiY3IhAHI0NTU4Ljk0IQDANTIyMy40OX0sImFSIQAVcCcAcjM3NTQuNTInAHA0NDE5LjA3SAC0aCI6LTF9LCJwc0MrAGIyODIuMDcqAAIMAGB9LCJyZWIgADRtZEEnADIwLjAkAJEwLjB9LCJvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmeVAA4icmdWAe1jdCI6IlJvb20iLCJ0dDcAKGluNwBPdEFnZCMADTlIdGyZACRpbsAAIWNocgAAcQASNIkB8AEiQWdvZGEiLCJiIjoiUFJQoAERcQoCEGk1AfMILCJpaSI6dHJ1ZSwiY28iOiIxIn1dfSy1AJlUYXhBbmRGZWW6AGI2NjQuNTV3AAIMAAu4AA4hAA+2ABYfOLYAAQm0ABYyswAifSw1AB83NQAOHjE1AC8xMDYADggfAQcWAfkBQ2FtcGFpZ25EaXNjb3VudB0Bci00NDEuOTHfAAMNAAsfAQ8jAAEPIQEWLzEy7QAPaDc1NTU0N9EBUV0sInBtIAAkc3LvATFsb3MSAGBub1IiOjEjAD90QlDxAgFQfX19fX0=
    And IsMultiHotel true
    And ClientDiscount true
    And AutoApplyPromos true
    And IsIncludedPriceInfo true
    And Experiment CARTPACK-337 User B
    When The user search
    Then Actual hotel price info should match the expected result
      | roomid   | channel | ratecategory | Promotion | priceInfo.netExclusive | priceInfo.netInclusive | priceInfo.margin | priceInfo.processingFee |
      | 13194322 | 1       | 585739       | 0         | 2476.51                | 2724.16                | 825.5            | 82.56                   |
    Then Return Promotion Price Peek For AutoApplyPromos
      | Hotel | PromoEligible | SupplierId | RoomPrice(Ex) | RoomPrice(AllIn) | PseudoCoupon(Ex) | PseudoCoupon(AllIn) | Cid | PromoAppliedOnFinalPrice | GoToTravel Discount | PromotionPricePeekPerBook(Ex) | PromotionPricePeekPerBook(AllIn) | OriginalTotal(Ex) | OriginalTotal(AllIn) |
      | 46749 | false         | 332        | 3302.01       | 3632.22          | 0.0              | 0.0                 | 0.0 | false                    | 0.0                 | 0.0                           | 0.0                              | 3302.01           | 3632.22              |
    Then Actual multi hotel display price should match the expected result
      | roomid   | channel | ratecategory | occupancy | adults | children | isFit | Promotion | Cancellation | displayPerBookBundledExc | displayPerBookBundledAllInc | displayPerBookDiffExc | displayPerBookDiffAllInc | displayPerPaxBundledExc | displayPerPaxBundledAllInc | displayPerPaxDiffExc | displayPerPaxDiffAllInc | crossedOutPerBookBundledExc | crossedOutPerBookBundledAllInc | crossedOutPerPaxBundledExc | crossedOutPerPaxBundledAllInc | savingsPerBookBundledExc | savingsPerBookBundledAllInc | savingsPerPaxBundledExc | savingsPerPaxBundledAllInc | totalDiscountPercent | Display.Perbook.exc | Display.Perbook.allinc | CrossedOut.Perbook.exc | CrossedOut.Perbook.allInc |
      | 13194322 | 1       | 585739       | 2         | 2      | 0        | true  | 0         | 1D1N_100P    | 6614.62                  | 7609.38                     | 0.0                   | 0.0                      | 3307.31                 | 3804.69                    | 0.0                  | 0.0                     | 11162.97                    | 11827.52                       | 5581.48                    | 5913.76                       | 4548.35                  | 4218.14                     | 2274.18                 | 2109.07                    | 35                   | 3302.01             | 3632.22                | 6604.03                | 6604.03                   |
    Then Actual new multi product bundle total saving per book should match the expected result
      | uid                                  | crossedOut.exc | crossedOut.allInc | crossedOutSaving.amount.exc | crossedOutSaving.amount.allInc | crossedOutSaving.percent.exc | crossedOutSaving.percent.allInc | additionalRate.exc | additionalRate.allInc | additionalRateSaving.amount.exc | additionalRateSaving.amount.allInc | additionalRateSaving.percent.exc | additionalRateSaving.percent.allInc | display.exc | display.allInc | original.exc | original.allInc |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | 11162.97       | 11827.52          | 4548.35                     | 4218.14                        | 40                           | 35                              | 7056.53            | 8051.29               | 441.91                          | 441.91                             | 6                                | 5                                   | 6614.62     | 7609.38        | 7056.53      | 8051.29         |
    Then Actual packaging display charges price should match the expected result
      | roomid   | channel | ratecategory | Promotion | chargesTypeId    | quantity | payToAgodaExc | payToAgodaInc | payToHotelExc | payToHotelInc | totalExc | totalInc |
      | 13194322 | 1       | 585739       | 0         | Room             | 1        | 7056.53       | 8051.29       | 0             | 0             | 7056.53  | 8051.29  |
      | 13194322 | 1       | 585739       | 0         | TaxAndFee        | 1        | 994.76        | 994.76        | 0             | 0             | 994.76   | 994.76   |
      | 13194322 | 1       | 585739       | 0         | CampaignDiscount | 1        | -441.91       | -441.91       | 0             | 0             | -441.91  | -441.91  |
    Then Packaging token should match the expected result
      | clientToken | interSystemToken                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
      | true        | NAAAHIvAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8EhocyI6W3siaSI6IkNpUUl0TE90K1FnUUFoalFpY1pISUFJbzVLS1BBekFFU2dreFJERk9YekV3TUZCUW1BVVNBZ2dCR2dRb0JEQUIiLCJwIjoyLCJ0SVBbAPQOZXgiOjMzMTIuNjEsImFsIjozOTc3LjE2fSwiY3IhAHI0NTU4Ljk0IQDANTIyMy40OX0sImFSIQAVcCcAcjM3NTQuNTInAHA0NDE5LjA3SAC0aCI6LTF9LCJwc0MrAGIyODIuMDcqAAIMAGB9LCJyZWIgADRtZEEnADIwLjAkAJEwLjB9LCJvcHQbAI9FeHRyYUJlZCcAAhB9KQAvcmeVAA4icmdWAe1jdCI6IlJvb20iLCJ0dDcAKGluNwBPdEFnZCMADTlIdGyZACRpbsAAIWNocgAAcQASNIkB8AEiQWdvZGEiLCJiIjoiUFJQoAERcQoCEGk1AfMILCJpaSI6dHJ1ZSwiY28iOiIxIn1dfSy1AJlUYXhBbmRGZWW6AGI2NjQuNTV3AAIMAAu4AA4hAA+2ABYfOLYAAQm0ABYyswAifSw1AB83NQAOHjE1AC8xMDYADggfAQcWAfkBQ2FtcGFpZ25EaXNjb3VudB0Bci00NDEuOTHfAAMNAAsfAQ8jAAEPIQEWLzEy7QAPaDc1NTU0N9EBUV0sInBtIAAkc3LvATFsb3MSAINub1IiOjF9LP8DsGgwSXBOSEtEQkFD9wP/IWxzQkhNQVJLQ1RGRU1VNWZNVEF3VUZDWUJSSUNDQUVhQkNnRU1BRWlCQWdFR0FJPf8DA0QwMi4w/wNpNjMyLjIy/wNyNjYwNC4wMyEAAw0ACs8DC6gDD8kDQw+nAAAPyQMRBDcAKGluNwAJyQMPIwAAD8kDcFQzMzAuMpYAAQwADrgADiEAD6gCDQ+UA1oK2QJOZmFsc9oCZl0sInRCUGwCQzE0LjavBcA3NjA5LjM4fX19fX0= |
    Then Actual multi product breakdown should match the expected per book result
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | total.pb.exc | total.pb.allInc | crossedOut.pb.exc | crossedOut.pb.allInc | originalPrice.pb.exc | originalPrice.pb.allInc |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | -            | -               | 4558.94           | 5223.49              | -                    | -                       |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | -            | -               | 6604.03           | 6604.03              | -                    | -                       |
    Then Actual multi product breakdown additional rate per book
      | uid                                  | roomIdentifier                                                   | productId                                                        | productType | additionalRate.pb.exc | additionalRate.pb.allInc |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | CiQItLOt+QgQAhjQicZHIAIo5KKPAzAESgkxRDFOXzEwMFBQmAUSAggBGgQoBDAB | Property    | 3754.52               | 4419.07                  |
      | 5a3d0343-716a-b51d-c088-4a620a402630 | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Ch0IpNHKDBACIAIolsBHMARKCTFEMU5fMTAwUFCYBRICCAEaBCgEMAEiBAgEGAI= | Property    | 3302.01               | 3632.22                  |
