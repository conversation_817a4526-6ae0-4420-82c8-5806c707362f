@BookingSwap
Feature: Swap Room
  Customer would see same room that was selected on Property Page regardless of Login/Logout status

  # TODO: Remove these tests when integrating CARTPACK-337 since there's no multi hotel logic
  Background:
    Given All Default Parameters
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Origin TH
    And Currency THB
    And Language 1
    And CID 0
    And DoAPSPeek true
    And isHotelForBooking true

  Scenario: Testcase 1.1, still return Retail Room though after login actual room should be APS Room
    Given Hotels 11662 checkin 2017-09-10 los 1
    And FilterByUID ChsIxI7ZDhACIAIozIpPMARKBzFEMU5fMU5QmAUSAggB
    And RatePlans 2
    And IsNeedAPSRateSwap true
    And IsUserLoggedIn true
    And Packaging ClientToken JAmVZ2yHVmBKbd1ngEkKhj9q+O8UnXo87MjM0FqbM7NXU13+k/jULLTBIdIBtQINRw3qYPPjolYuJV4tHwZj1Ae6JI2FXkyjKkNduMfWdtwx6Bc3Idl34sLw672wfE3hrz/lGLf5j7VzILP7i2TaFcBS+vs713LYethuu9qI4nwRGPFyixrMgXNsoHAIQrjpIsZ3d+dBwU/cbCrhUdwXgeUUB0au4tc6oA2M8rvBEcnq732DV+57v4AdN/Ofbjzc3XwkFgG8REPzHy5hChLW6vqs6jED3Kya+EC9S753HL61duVHw+wtLkGNoLZTAvkn52O9mbL9iKdUd3PU4dJWARoPz/YxSj6KrlR9VYJgW and InterSystemToken NAAACQPAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8jRocyI6W3siaSI6IkNpQUk3S2J4Q1JBQ0dJaWFvRWNnQkNpbXFFWXdBa29ITVVReFRsOHhUbENZQlJJQ0NBRT0iLCJwXgAgSVBPAPQQZXgiOjE1ODg5LjAzLCJhbCI6MTgzNjEuMzd9LCJjciMAgjIxOTU4LjQ3IwDkMjQ0MzAuODN9LCJwc0MkADIwLjAfAJAwLjB9LCJyZWIaAD9tZEEhAAQxb3B0GwCPRXh0cmFCZWQnAAIQfSkAL3JnqwAPMmhyZxoB7mN0IjoiUm9vbSIsInR0OQApaW45AE90QWdkJQAPOUh0bJ8AI2lunwATLHEAmVRheEFuZEZlZXYAcjI0NzIuMzQwAAMNAAt0AA8jAAEPcgAKj119XSwidEJQLwEBUH19fX19
    And IsMultiHotel true
    When The user search
    Then DF Should find Alternative Room
      | Selected UID                                 | Alternative UID                                      | Swap Type |
      | ChsIxI7ZDhACIAIozIpPMARKBzFEMU5fMU5QmAUSAggB | ChsIxI7ZDhACIAQozIpPMARKBzFEMU5fMU5QmAUSAggBIgQIBBgC | APSRate   |

  Scenario: Testcase 1.2, still return Retail Room though after login actual room should be APS Room
    Given Hotels 11662 checkin 2017-09-10 los 1
    And FilterByUID ChsIxI7ZDhACIAIozIpPMARKBzFEMU5fMU5QmAUSAggBGgQoBDAB
    And RatePlans 2
    And IsNeedAPSRateSwap true
    And IsUserLoggedIn true
    And Packaging ClientToken JAmVZ2yHVmBKbd1ngEkKhj9q+O8UnXo87MjM0FqbM7NXU13+k/jULLTBIdIBtQINRw3qYPPjolYuJV4tHwZj1Ae6JI2FXkyjKkNduMfWdtwx6Bc3Idl34sLw672wfE3hrz/lGLf5j7VzILP7i2TaFcBS+vs713LYethuu9qI4nwRGPFyixrMgXNsoHAIQrjpIsZ3d+dBwU/cbCrhUdwXgeUUB0au4tc6oA2M8rvBEcnq732DV+57v4AdN/Ofbjzc3XwkFgG8REPzHy5hChLW6vqs6jED3Kya+EC9S753HL61duVHw+wtLkGNoLZTAvkn52O9mbL9iKdUd3PU4dJWARoPz/YxSj6KrlR9VYJgW and InterSystemToken NAAACQPAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8jRocyI6W3siaSI6IkNpQUk3S2J4Q1JBQ0dJaWFvRWNnQkNpbXFFWXdBa29ITVVReFRsOHhUbENZQlJJQ0NBRT0iLCJwXgAgSVBPAPQQZXgiOjE1ODg5LjAzLCJhbCI6MTgzNjEuMzd9LCJjciMAgjIxOTU4LjQ3IwDkMjQ0MzAuODN9LCJwc0MkADIwLjAfAJAwLjB9LCJyZWIaAD9tZEEhAAQxb3B0GwCPRXh0cmFCZWQnAAIQfSkAL3JnqwAPMmhyZxoB7mN0IjoiUm9vbSIsInR0OQApaW45AE90QWdkJQAPOUh0bJ8AI2lunwATLHEAmVRheEFuZEZlZXYAcjI0NzIuMzQwAAMNAAt0AA8jAAEPcgAKj119XSwidEJQLwEBUH19fX19
    And IsMultiHotel true
    When The user search
    Then DF Should find Alternative Room
      | Selected UID                                         | Alternative UID                                              | Swap Type |
      | ChsIxI7ZDhACIAIozIpPMARKBzFEMU5fMU5QmAUSAggBGgQoBDAB | ChsIxI7ZDhACIAQozIpPMARKBzFEMU5fMU5QmAUSAggBGgQoBDABIgQIBBgC | APSRate   |

  Scenario: Testcase 2.1, still return APS Room though after logout actual room should be Retail Room
    Given Hotels 11662 checkin 2017-09-10 los 1
    And FilterByUID ChsIxI7ZDhACIAQozIpPMARKBzFEMU5fMU5QmAUSAggB
    And RatePlans 1
    And IsNeedRetailRateSwap true
    And IsUserLoggedIn false
    And Packaging ClientToken JAmVZ2yHVmBKbd1ngEkKhj9q+O8UnXo87MjM0FqbM7NXU13+k/jULLTBIdIBtQINRw3qYPPjolYuJV4tHwZj1Ae6JI2FXkyjKkNduMfWdtwx6Bc3Idl34sLw672wfE3hrz/lGLf5j7VzILP7i2TaFcBS+vs713LYethuu9qI4nwRGPFyixrMgXNsoHAIQrjpIsZ3d+dBwU/cbCrhUdwXgeUUB0au4tc6oA2M8rvBEcnq732DV+57v4AdN/Ofbjzc3XwkFgG8REPzHy5hChLW6vqs6jED3Kya+EC9S753HL61duVHw+wtLkGNoLZTAvkn52O9mbL9iKdUd3PU4dJWARoPz/YxSj6KrlR9VYJgW and InterSystemToken NAAACQPAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8jRocyI6W3siaSI6IkNpQUk3S2J4Q1JBQ0dJaWFvRWNnQkNpbXFFWXdBa29ITVVReFRsOHhUbENZQlJJQ0NBRT0iLCJwXgAgSVBPAPQQZXgiOjE1ODg5LjAzLCJhbCI6MTgzNjEuMzd9LCJjciMAgjIxOTU4LjQ3IwDkMjQ0MzAuODN9LCJwc0MkADIwLjAfAJAwLjB9LCJyZWIaAD9tZEEhAAQxb3B0GwCPRXh0cmFCZWQnAAIQfSkAL3JnqwAPMmhyZxoB7mN0IjoiUm9vbSIsInR0OQApaW45AE90QWdkJQAPOUh0bJ8AI2lunwATLHEAmVRheEFuZEZlZXYAcjI0NzIuMzQwAAMNAAt0AA8jAAEPcgAKj119XSwidEJQLwEBUH19fX19
    And IsMultiHotel true
    When The user search
    Then DF Should find Alternative Room
      | Selected UID                                 | Alternative UID                                      | Swap Type  |
      | ChsIxI7ZDhACIAQozIpPMARKBzFEMU5fMU5QmAUSAggB | ChsIxI7ZDhACIAIozIpPMARKBzFEMU5fMU5QmAUSAggBIgQIBBgC | RetailRate |

  Scenario: Testcase 2.2, still return APS Room though after logout actual room should be Retail Room
    Given Hotels 11662 checkin 2017-09-10 los 1
    And FilterByUID ChsIxI7ZDhACIAQozIpPMARKBzFEMU5fMU5QmAUSAggBGgQoBDAB
    And RatePlans 1
    And IsNeedRetailRateSwap true
    And IsUserLoggedIn false
    And Packaging ClientToken JAmVZ2yHVmBKbd1ngEkKhj9q+O8UnXo87MjM0FqbM7NXU13+k/jULLTBIdIBtQINRw3qYPPjolYuJV4tHwZj1Ae6JI2FXkyjKkNduMfWdtwx6Bc3Idl34sLw672wfE3hrz/lGLf5j7VzILP7i2TaFcBS+vs713LYethuu9qI4nwRGPFyixrMgXNsoHAIQrjpIsZ3d+dBwU/cbCrhUdwXgeUUB0au4tc6oA2M8rvBEcnq732DV+57v4AdN/Ofbjzc3XwkFgG8REPzHy5hChLW6vqs6jED3Kya+EC9S753HL61duVHw+wtLkGNoLZTAvkn52O9mbL9iKdUd3PU4dJWARoPz/YxSj6KrlR9VYJgW and InterSystemToken NAAACQPAEeyJ2IjoxLCJ0T2JqIjp7ImJJVAcAMFRIQgcA8jRocyI6W3siaSI6IkNpQUk3S2J4Q1JBQ0dJaWFvRWNnQkNpbXFFWXdBa29ITVVReFRsOHhUbENZQlJJQ0NBRT0iLCJwXgAgSVBPAPQQZXgiOjE1ODg5LjAzLCJhbCI6MTgzNjEuMzd9LCJjciMAgjIxOTU4LjQ3IwDkMjQ0MzAuODN9LCJwc0MkADIwLjAfAJAwLjB9LCJyZWIaAD9tZEEhAAQxb3B0GwCPRXh0cmFCZWQnAAIQfSkAL3JnqwAPMmhyZxoB7mN0IjoiUm9vbSIsInR0OQApaW45AE90QWdkJQAPOUh0bJ8AI2lunwATLHEAmVRheEFuZEZlZXYAcjI0NzIuMzQwAAMNAAt0AA8jAAEPcgAKj119XSwidEJQLwEBUH19fX19
    And IsMultiHotel true
    When The user search
    Then DF Should find Alternative Room
      | Selected UID                                         | Alternative UID                                              | Swap Type  |
      | ChsIxI7ZDhACIAQozIpPMARKBzFEMU5fMU5QmAUSAggBGgQoBDAB | ChsIxI7ZDhACIAIozIpPMARKBzFEMU5fMU5QmAUSAggBGgQoBDABIgQIBBgC | RetailRate |
