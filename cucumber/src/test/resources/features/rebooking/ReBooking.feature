@ReBooking
@pricingIntegration
Feature: If request with ReBooking, DF skip Soybean call, the create manual downlift to make the price match with given price of customer paid price. Also we filter by specified room type id.

#Ref Item ID:
#[ 3]Margin
#[ 9]DownLift
#[12]SellInclusive
#[41]ReferenceSalesInclusive
#[55]OptimizationGain

  Background:
    Given All Default Parameters
    And Experiment PFE-12481 User B

  Scenario: Testcase 1.1.1, Rebooking return correct price breakdown with manual downlift with 1 Room 1 LOS, with customer paid price USD: 2000.0, return Uplift and Sell In USD: 2000.0 per room per date
    Given Hotels 184553 checkin 2017-04-01 los 1
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And ReBook with roomTypeID 10363614 customerPaidPrice 2000.00
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 4d77505a-d0b5-b1fc-16b6-501dc4339be3 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 0      | 9      | 10363614   | 332      | 572149          | Merchant     | 0      | -1591.7   | -5847.6     | 1        | Mandatory | 0           | 2           |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000      | 7347.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.95   | 6072.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |

  Scenario: Testcase 1.1.2, Rebooking return correct price breakdown with manual downlift with 1 Room 1 LOS, with customer paid price USD: 2000.0, return Uplift and Sell In USD: 2000.0 and Original Net In USD: 1000 per room per date
    Given Hotels 184553 checkin 2017-04-01 los 1
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And ReBook with roomTypeID 10363614 customerPaidPrice 2000.00 originalNetIn 1500.00
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 4d77505a-d0b5-b1fc-16b6-501dc4339be3 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 0      | 9      | 10363614   | 332      | 572149          | Merchant     | 0      | -1591.7   | -5847.6     | 1        | Mandatory | 0           | 2           |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000      | 7347.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.95   | 6072.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 55     | 10363614   | 332      | 572149          | Merchant     | 1      | 1152.95   | 4235.7      | 1        | Mandatory | 0           | 0           |

  Scenario: Testcase 1.2.1, Rebooking return correct price breakdown with manual downlift with 1 Room 1 LOS, with customer paid price USD: 1000.0, return Downlift and Sell In USD: 1000.0 per room per date
    Given Hotels 184553 checkin 2017-04-01 los 1
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And ReBook with roomTypeID 10363614 customerPaidPrice 1000.00
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 4d77505a-d0b5-b1fc-16b6-501dc4339be3 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 0      | 9      | 10363614   | 332      | 572149          | Merchant     | 0      | -591.7    | -2173.8     | 1        | Mandatory | 0           | 2           |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 1000      | 3673.8      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 652.95    | 2398.8      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |

  Scenario: Testcase 1.2.2, Rebooking return correct price breakdown with manual downlift with 1 Room 1 LOS, with customer paid price USD: 1000.0, return Downlift and Sell In USD: 1000.0 and original Net In USD: 500 per room per date
    Given Hotels 184553 checkin 2017-04-01 los 1
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And ReBook with roomTypeID 10363614 customerPaidPrice 1000.00 originalNetIn 500.00
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 4d77505a-d0b5-b1fc-16b6-501dc4339be3 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 0      | 9      | 10363614   | 332      | 572149          | Merchant     | 0      | -591.7    | -2173.8     | 1        | Mandatory | 0           | 2           |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 1000      | 3673.8      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 652.95    | 2398.8      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 55     | 10363614   | 332      | 572149          | Merchant     | 1      | 152.95    | 561.9       | 1        | Mandatory | 0           | 0           |

  Scenario: Testcase 2.1, Rebooking return correct price breakdown with manual downlift with 2 Room 2 LOS, with customer paid price USD: 8000.0, return SellIn (8000.0/(2*2)) = 2000.0 per room per date
    Given Hotels 184553 checkin 2017-04-01 los 2
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 4
    And Children 0
    And Rooms 2
    And isHotelForBooking true
    And ReBook with roomTypeID 10363614 customerPaidPrice 8000.00
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 4d77505a-d0b5-b1fc-16b6-501dc4339be3 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 0      | 9      | 10363614   | 332      | 572149          | Merchant     | 0      | -1591.7   | -5847.6     | 2        | Mandatory | 0           | 2           |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000      | 7347.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.95   | 6072.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000      | 7347.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.95   | 6072.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 0      | 9      | 10363614   | 332      | 572149          | Merchant     | 0      | -1591.7   | -5847.6     | 2        | Mandatory | 0           | 2           |
      | 2017-04-02 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000      | 7347.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.95   | 6072.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 2      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 2      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000      | 7347.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 2      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.95   | 6072.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 2      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |

  Scenario: Testcase 2.2, Rebooking return correct price breakdown with manual downlift with 2 Room 2 LOS, with customer paid price USD: 8000.0, with original Net In USD: 6000.0
    Given Hotels 184553 checkin 2017-04-01 los 2
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 4
    And Children 0
    And Rooms 2
    And isHotelForBooking true
    And ReBook with roomTypeID 10363614 customerPaidPrice 8000.00 originalNetIn 6000.00
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 4d77505a-d0b5-b1fc-16b6-501dc4339be3 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 0      | 9      | 10363614   | 332      | 572149          | Merchant     | 0      | -1591.7   | -5847.6     | 2        | Mandatory | 0           | 2           |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000      | 7347.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.95   | 6072.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 55     | 10363614   | 332      | 572149          | Merchant     | 1      | 1152.95   | 4235.7      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000      | 7347.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.95   | 6072.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 55     | 10363614   | 332      | 572149          | Merchant     | 1      | 1152.95   | 4235.7      | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 0      | 9      | 10363614   | 332      | 572149          | Merchant     | 0      | -1591.7   | -5847.6     | 2        | Mandatory | 0           | 2           |
      | 2017-04-02 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000      | 7347.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.95   | 6072.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 1      | 55     | 10363614   | 332      | 572149          | Merchant     | 1      | 1152.95   | 4235.7      | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 2      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 2      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000      | 7347.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 2      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.95   | 6072.6      | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 2      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-02 | 2      | 55     | 10363614   | 332      | 572149          | Merchant     | 1      | 1152.95   | 4235.7      | 1        | Mandatory | 0           | 0           |

  Scenario: Testcase 3.1, Rebooking return correct price breakdown with manual downlift with 1 Room 1 LOS, with taxAsPercent and taxAsAmount
    Given Hotels 279329 checkin 2017-12-15 los 1
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-12-14
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And ReBook with roomTypeID 3094960 customerPaidPrice 500.00
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 2f6f476b-7f18-41cd-d8c1-6f65eb2ec099 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-12-15 | 0      | 9      | 3094960    | 332      | 581560          | Merchant     | 0      | -221.84   | -17297.68   | 1        | Mandatory | 0           | 2           |
      | 2017-12-15 | 1      | 10     | 3094960    | 332      | 581560          | Merchant     | 1      | 230.92    | 18005       | 1        | Mandatory | 0           | 0           |
      | 2017-12-15 | 1      | 12     | 3094960    | 332      | 581560          | Merchant     | 1      | 500       | 38986.35    | 1        | Mandatory | 0           | 0           |
      | 2017-12-15 | 1      | 3      | 3094960    | 332      | 581560          | Merchant     | 1      | 224.42    | 17499.04    | 1        | Mandatory | 0           | 0           |
      | 2017-12-15 | 1      | 41     | 3094960    | 332      | 581560          | Merchant     | 1      | 278.16    | 21688.67    | 1        | Mandatory | 0           | 0           |
      | 2017-12-15 | 0      | 10     | 3094960    | 332      | 581560          | Merchant     | 2      | 69.21     | 5396.4      | 2        | Mandatory | 0           | 77          |
      | 2017-12-15 | 0      | 12     | 3094960    | 332      | 581560          | Merchant     | 2      | 83.38     | 6501.69     | 2        | Mandatory | 0           | 77          |
      | 2017-12-15 | 0      | 3      | 3094960    | 332      | 581560          | Merchant     | 2      | 11.82     | 921.84      | 2        | Mandatory | 0           | 77          |
      | 2017-12-15 | 0      | 41     | 3094960    | 332      | 581560          | Merchant     | 2      | 83.38     | 6501.69     | 2        | Mandatory | 0           | 77          |

  Scenario: Testcase 3.2, Rebooking return correct price breakdown with manual downlift with 1 Room 1 LOS, with taxAsPercent and taxAsAmount and OriginalNetIn
    Given Hotels 279329 checkin 2017-12-15 los 1
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-12-14
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And ReBook with roomTypeID 3094960 customerPaidPrice 500.00 originalNetIn 200.00
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 2f6f476b-7f18-41cd-d8c1-6f65eb2ec099 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-12-15 | 0      | 9      | 3094960    | 332      | 581560          | Merchant     | 0      | -221.84   | -17297.68   | 1        | Mandatory | 0           | 2           |
      | 2017-12-15 | 1      | 10     | 3094960    | 332      | 581560          | Merchant     | 1      | 230.92    | 18005       | 1        | Mandatory | 0           | 0           |
      | 2017-12-15 | 1      | 12     | 3094960    | 332      | 581560          | Merchant     | 1      | 500       | 38986.35    | 1        | Mandatory | 0           | 0           |
      | 2017-12-15 | 1      | 3      | 3094960    | 332      | 581560          | Merchant     | 1      | 224.42    | 17499.04    | 1        | Mandatory | 0           | 0           |
      | 2017-12-15 | 1      | 41     | 3094960    | 332      | 581560          | Merchant     | 1      | 278.16    | 21688.67    | 1        | Mandatory | 0           | 0           |
      | 2017-12-15 | 1      | 55     | 3094960    | 332      | 581560          | Merchant     | 1      | -30.91    | -2410.46    | 1        | Mandatory | 0           | 0           |
      | 2017-12-15 | 0      | 10     | 3094960    | 332      | 581560          | Merchant     | 2      | 69.21     | 5396.4      | 2        | Mandatory | 0           | 77          |
      | 2017-12-15 | 0      | 12     | 3094960    | 332      | 581560          | Merchant     | 2      | 83.38     | 6501.69     | 2        | Mandatory | 0           | 77          |
      | 2017-12-15 | 0      | 3      | 3094960    | 332      | 581560          | Merchant     | 2      | 11.82     | 921.84      | 2        | Mandatory | 0           | 77          |
      | 2017-12-15 | 0      | 41     | 3094960    | 332      | 581560          | Merchant     | 2      | 83.38     | 6501.69     | 2        | Mandatory | 0           | 77          |

  Scenario: Testcase 4.1.1, Rebooking return correct price breakdown with manual downlift with 1 Room 1 LOS, with customer paid price USD: 4000.1, not apply rounding error as Exp-A
    Given Hotels 184553 checkin 2017-04-01 los 1
    And Experiment CEGHZ-777 User A
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 4
    And Children 0
    And Rooms 2
    And isHotelForBooking true
    And ReBook with roomTypeID 10363614 customerPaidPrice 4000.01
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 4d77505a-d0b5-b1fc-16b6-501dc4339be3 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 0      | 9      | 10363614   | 332      | 572149          | Merchant     | 0      | -1591.71  | -5847.62    | 2        | Mandatory | 0           | 2           |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000.01   | 7347.62     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.96   | 6072.62     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000.01   | 7347.62     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.96   | 6072.62     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |

  Scenario: Testcase 4.1.2, Rebooking return correct price breakdown with manual downlift with 1 Room 1 LOS, with customer paid price USD: 4000.1, not apply rounding error number as Exp B but the error cannot be applied due to over 2 USD precision
    Given Hotels 184553 checkin 2017-04-01 los 1
    And Experiment CEGHZ-777 User B
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 4
    And Children 0
    And Rooms 2
    And isHotelForBooking true
    And ReBook with roomTypeID 10363614 customerPaidPrice 4000.01
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 4d77505a-d0b5-b1fc-16b6-501dc4339be3 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 0      | 9      | 10363614   | 332      | 572149          | Merchant     | 0      | -1591.71  | -5847.62    | 2        | Mandatory | 0           | 2           |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000.01   | 7347.62     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.96   | 6072.62     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 347.05    | 1275        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 2000.01   | 7347.62     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 1652.96   | 6072.62     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 2      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 408.3     | 1500        | 1        | Mandatory | 0           | 0           |

  Scenario: Testcase 4.2.1, Rebooking return correct price breakdown with manual downlift with 1 Room 3 LOS, with customer paid price USD: 350.0, not apply rounding error as Exp A
    Given Hotels 98998 checkin 2017-11-24 los 3
    And Experiment CEGHZ-777 User A
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And ReBook with roomTypeID 14024855 customerPaidPrice 350.0
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID eb59ba51-6756-907e-d132-481466e64756 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-11-24 | 0      | 9      | 14024855   | 332      | 0               | Merchant     | 0      | -13.74    | -353.78     | 1        | Mandatory | 0           | 2           |
      | 2017-11-24 | 1      | 10     | 14024855   | 332      | 0               | Merchant     | 1      | 80.28     | 2067        | 1        | Mandatory | 0           | 0           |
      | 2017-11-24 | 1      | 12     | 14024855   | 332      | 0               | Merchant     | 1      | 116.67    | 3003.78     | 1        | Mandatory | 0           | 0           |
      | 2017-11-24 | 1      | 3      | 14024855   | 332      | 0               | Merchant     | 1      | 30.91     | 795.91      | 1        | Mandatory | 0           | 0           |
      | 2017-11-24 | 1      | 41     | 14024855   | 332      | 0               | Merchant     | 1      | 102.93    | 2650        | 1        | Mandatory | 0           | 0           |
      | 2017-11-25 | 0      | 9      | 14024855   | 332      | 0               | Merchant     | 0      | -13.74    | -353.78     | 1        | Mandatory | 0           | 2           |
      | 2017-11-25 | 1      | 10     | 14024855   | 332      | 0               | Merchant     | 1      | 80.28     | 2067        | 1        | Mandatory | 0           | 0           |
      | 2017-11-25 | 1      | 12     | 14024855   | 332      | 0               | Merchant     | 1      | 116.67    | 3003.78     | 1        | Mandatory | 0           | 0           |
      | 2017-11-25 | 1      | 3      | 14024855   | 332      | 0               | Merchant     | 1      | 30.91     | 795.91      | 1        | Mandatory | 0           | 0           |
      | 2017-11-25 | 1      | 41     | 14024855   | 332      | 0               | Merchant     | 1      | 102.93    | 2650        | 1        | Mandatory | 0           | 0           |
      | 2017-11-26 | 0      | 9      | 14024855   | 332      | 0               | Merchant     | 0      | -13.74    | -353.78     | 1        | Mandatory | 0           | 2           |
      | 2017-11-26 | 1      | 10     | 14024855   | 332      | 0               | Merchant     | 1      | 80.28     | 2067        | 1        | Mandatory | 0           | 0           |
      | 2017-11-26 | 1      | 12     | 14024855   | 332      | 0               | Merchant     | 1      | 116.67    | 3003.78     | 1        | Mandatory | 0           | 0           |
      | 2017-11-26 | 1      | 3      | 14024855   | 332      | 0               | Merchant     | 1      | 30.91     | 795.91      | 1        | Mandatory | 0           | 0           |
      | 2017-11-26 | 1      | 41     | 14024855   | 332      | 0               | Merchant     | 1      | 102.93    | 2650        | 1        | Mandatory | 0           | 0           |

  Scenario: Testcase 4.2.2, Rebooking return correct price breakdown with manual downlift with 1 Room 3 LOS, with customer paid price USD: 350.0, apply rounding error on the first room as Exp B
    Given Hotels 98998 checkin 2017-11-24 los 3
    And Experiment CEGHZ-777 User B
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And ReBook with roomTypeID 14024855 customerPaidPrice 350.0
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID eb59ba51-6756-907e-d132-481466e64756 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-11-24 | 0      | 9      | 14024855   | 332      | 0               | Merchant     | 0      | -13.75    | -353.78     | 1        | Mandatory | 0           | 2           |
      | 2017-11-24 | 1      | 10     | 14024855   | 332      | 0               | Merchant     | 1      | 80.28     | 2067        | 1        | Mandatory | 0           | 0           |
      | 2017-11-24 | 1      | 12     | 14024855   | 332      | 0               | Merchant     | 1      | 116.66    | 3003.78     | 1        | Mandatory | 0           | 0           |
      | 2017-11-24 | 1      | 3      | 14024855   | 332      | 0               | Merchant     | 1      | 30.92     | 795.91      | 1        | Mandatory | 0           | 0           |
      | 2017-11-24 | 1      | 41     | 14024855   | 332      | 0               | Merchant     | 1      | 102.93    | 2650        | 1        | Mandatory | 0           | 0           |
      | 2017-11-25 | 0      | 9      | 14024855   | 332      | 0               | Merchant     | 0      | -13.74    | -353.78     | 1        | Mandatory | 0           | 2           |
      | 2017-11-25 | 1      | 10     | 14024855   | 332      | 0               | Merchant     | 1      | 80.28     | 2067        | 1        | Mandatory | 0           | 0           |
      | 2017-11-25 | 1      | 12     | 14024855   | 332      | 0               | Merchant     | 1      | 116.67    | 3003.78     | 1        | Mandatory | 0           | 0           |
      | 2017-11-25 | 1      | 3      | 14024855   | 332      | 0               | Merchant     | 1      | 30.91     | 795.91      | 1        | Mandatory | 0           | 0           |
      | 2017-11-25 | 1      | 41     | 14024855   | 332      | 0               | Merchant     | 1      | 102.93    | 2650        | 1        | Mandatory | 0           | 0           |
      | 2017-11-26 | 0      | 9      | 14024855   | 332      | 0               | Merchant     | 0      | -13.74    | -353.78     | 1        | Mandatory | 0           | 2           |
      | 2017-11-26 | 1      | 10     | 14024855   | 332      | 0               | Merchant     | 1      | 80.28     | 2067        | 1        | Mandatory | 0           | 0           |
      | 2017-11-26 | 1      | 12     | 14024855   | 332      | 0               | Merchant     | 1      | 116.67    | 3003.78     | 1        | Mandatory | 0           | 0           |
      | 2017-11-26 | 1      | 3      | 14024855   | 332      | 0               | Merchant     | 1      | 30.91     | 795.91      | 1        | Mandatory | 0           | 0           |
      | 2017-11-26 | 1      | 41     | 14024855   | 332      | 0               | Merchant     | 1      | 102.93    | 2650        | 1        | Mandatory | 0           | 0           |

  Scenario: Testcase 4.2.3, Rebooking return correct price breakdown when do filtering by masterRoomId
    Given Hotels 247952 checkin 2017-07-01 los 1
    And FilterByRoomIdentifiers CnAIspTSBhAEIAIwAkABSg0zNjVEMTAwUF8xMDBQULwvek9Tb21lKDI4MTQyNyl8Njk2NDUwNXwxfDI4MTQyNzAyXzg2NjM2MjU3XzFfM18wfDF8M3xMaXN0KDEpfDM2NUQxMDBQXzEwMFB8U29tZSgpEgIIARoCKAI=
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And ReBook with roomTypeID 99999999 masterRoomTypeID 3138660 customerPaidPrice 350.0
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 2396722f-881a-3a52-ca06-664074e0c518 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-07-01 | 0      | 9      | 6964505    | 3038     | 0               | Agency       | 0      | -161.97   | -18089      | 1        | Mandatory | 0           | 2           |
      | 2017-07-01 | 1      | 10     | 6964505    | 3038     | 0               | Agency       | 1      | 156.82    | 17514       | 1        | Mandatory | 0           | 0           |
      | 2017-07-01 | 1      | 12     | 6964505    | 3038     | 0               | Agency       | 1      | 350       | 39089       | 1        | Mandatory | 0           | 0           |
      | 2017-07-01 | 1      | 3      | 6964505    | 3038     | 0               | Agency       | 1      | 133.41    | 14900       | 1        | Mandatory | 0           | 0           |
      | 2017-07-01 | 1      | 41     | 6964505    | 3038     | 0               | Agency       | 1      | 188.03    | 21000       | 1        | Mandatory | 0           | 0           |
    
  Scenario: Testcase 4.2.4, Rebooking return correct price breakdown when do filtering by masterRoomId 0
    Given Hotels 247952 checkin 2017-07-01 los 1
    And FilterByRoomIdentifiers CnAIspTSBhAEIAIwAkABSg0zNjVEMTAwUF8xMDBQULwvek9Tb21lKDI4MTQyNyl8Njk2NDUwNXwxfDI4MTQyNzAyXzg2NjM2MjU3XzFfM18wfDF8M3xMaXN0KDEpfDM2NUQxMDBQXzEwMFB8U29tZSgpEgIIARoCKAI=
    And Platform 1
    And Origin TH
    And Currency USD
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And ReBook with roomTypeID 6964505 masterRoomTypeID 0 customerPaidPrice 350.0
    And Experiment CFF-1146-REMOVE-PRICE-MATCHING User A
    When The user search
    Then The rebooking summary with prices on room UID 2396722f-881a-3a52-ca06-664074e0c518 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-07-01 | 0      | 9      | 6964505    | 3038     | 0               | Agency       | 0      | -161.97   | -18089      | 1        | Mandatory | 0           | 2           |
      | 2017-07-01 | 1      | 10     | 6964505    | 3038     | 0               | Agency       | 1      | 156.82    | 17514       | 1        | Mandatory | 0           | 0           |
      | 2017-07-01 | 1      | 12     | 6964505    | 3038     | 0               | Agency       | 1      | 350       | 39089       | 1        | Mandatory | 0           | 0           |
      | 2017-07-01 | 1      | 3      | 6964505    | 3038     | 0               | Agency       | 1      | 133.41    | 14900       | 1        | Mandatory | 0           | 0           |
      | 2017-07-01 | 1      | 41     | 6964505    | 3038     | 0               | Agency       | 1      | 188.03    | 21000       | 1        | Mandatory | 0           | 0           |
