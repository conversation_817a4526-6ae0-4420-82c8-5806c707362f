@ReBooking
@CancelRebookV3
Feature: If request with ReBooking with originalExchangeRate , toRequest exchangeRate is updated and is also reflected in response.

  Background:
    Given All Default Parameters
    And Experiment PFE-12481 User B

  Scenario: CFF-1118=A, Testcase 1.1.1, Rebooking return correct exchangeRates
    Given Hotels 184553 checkin 2017-04-01 los 1
    And FilterByRoomIdentifiers ChsIvIvxCRACIAIo6utFMAZKBzFEMU5fMU5QmAUSAggBGgQQBCgG
    And isHotelForBooking true
    And Platform 1
    And Origin TH
    And Currency THB
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And Experiment CFPB-1057 User B
    And Experiment CFF-1118 User A
    And ReBook with roomTypeID 10363614 originalSellIn 540.0 originalUsdToRequestExchangeRate 30.0
    When The user search
    Then The rebooking summary with prices on room UID eb1136c9-a5d4-9866-f0fa-42d1e7a66605 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 462.74    | 1700        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 540       | 1983.85     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 544.4     | 2000        | 1        | Mandatory | 0           | 0           |
      | [empty]    | 0      | 40     | 10363614   | 332      | 572149          | Merchant     | 410    | -4.4      | -16.15      | 1        | Unknown   | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 81.66     | 300         | 1        | Mandatory | 0           | 0           |
    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 10363614   | AED           | 16199.98      | 540.00           | 0.00         | 30.0         | 30.0             | 30.0               | 0.03333333333333333      |

  Scenario: CFF-1118=A, Testcase 1.1.2, Rebooking return correct exchangeRates when localCurrency and Request currency are same
    Given Hotels 184553 checkin 2017-04-01 los 1
    And FilterByRoomIdentifiers ChsIvIvxCRACIAIo6utFMAZKBzFEMU5fMU5QmAUSAggBGgQQBCgG
    And isHotelForBooking true
    And Platform 1
    And Origin TH
    And Currency AED
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And Experiment CFPB-1057 User B
    And Experiment CFF-1118 User A
    And ReBook with roomTypeID 10363614 originalSellIn 540.0 originalUsdToRequestExchangeRate 4.0
    When The user search
    Then The rebooking summary with prices on room UID eb1136c9-a5d4-9866-f0fa-42d1e7a66605 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 462.74    | 1850.94     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 540       | 2160        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 544.4     | 2177.58     | 1        | Mandatory | 0           | 0           |
      | [empty]    | 0      | 40     | 10363614   | 332      | 572149          | Merchant     | 410    | -4.4      | -17.58      | 1        | Unknown   | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 81.66     | 326.64      | 1        | Mandatory | 0           | 0           |
    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 10363614   | AED           | 2160.00       | 540.00           | 0.00         | 4.0          | 4.0              | 4.0                | 0.25                     |

  Scenario: CFF-1118=B, Testcase 1.1.1, Rebooking return correct exchangeRates
    Given Hotels 184553 checkin 2017-04-01 los 1
    And FilterByRoomIdentifiers ChsIvIvxCRACIAIo6utFMAZKBzFEMU5fMU5QmAUSAggBGgQQBCgG
    And isHotelForBooking true
    And Platform 1
    And Origin TH
    And Currency THB
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And Experiment CFPB-1057 User B
    And Experiment CFF-1118 User B
    And ReBook with roomTypeID 10363614 originalSellIn 540.0 originalUsdToRequestExchangeRate 30.0
    When The user search
    Then The rebooking summary with prices on room UID eb1136c9-a5d4-9866-f0fa-42d1e7a66605 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 462.74    | 1700        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 540       | 1983.85     | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 544.4     | 2000        | 1        | Mandatory | 0           | 0           |
      | [empty]    | 0      | 40     | 10363614   | 332      | 572149          | Merchant     | 410    | -4.4      | -16.15      | 1        | Unknown   | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 81.66     | 300         | 1        | Mandatory | 0           | 0           |
    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 10363614   | AED           | 16200.00      | 540.00           | 0.00         | 25.7467      | 25.7467          | 25.7467            | 0.038839929000609785     |

  Scenario: CFF-1118=B, Testcase 1.1.2, Rebooking return correct exchangeRates when localCurrency and Request currency are same
    Given Hotels 184553 checkin 2017-04-01 los 1
    And FilterByRoomIdentifiers ChsIvIvxCRACIAIo6utFMAZKBzFEMU5fMU5QmAUSAggBGgQQBCgG
    And isHotelForBooking true
    And Platform 1
    And Origin TH
    And Currency AED
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And Experiment CFPB-1057 User B
    And Experiment CFF-1118 User B
    And ReBook with roomTypeID 10363614 originalSellIn 540.0 originalUsdToRequestExchangeRate 4.0
    When The user search
    Then The rebooking summary with prices on room UID eb1136c9-a5d4-9866-f0fa-42d1e7a66605 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 540       | 2160        | 1        | Mandatory | 0           | 0           |
      | [empty]    | 0      | 40     | 10363614   | 332      | 572149          | Merchant     | 410    | -4.4      | 160         | 1        | Unknown   | 0           | 0           |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 462.74    | 1700        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 81.66     | 300         | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 544.4     | 2000        | 1        | Mandatory | 0           | 0           |
    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 10363614   | AED           | 2160.00       | 540.00           | 0.00         | 3.6738       | 3.6738           | 3.6738             | 0.2721977244270238       |

  Scenario: CFF-1118=A, Testcase 1.1.6.1, adjust a surcharge correctly
    Given Hotels 159114 checkin 2017-10-31 los 1
    And Language 1
    And RatePlans 1,6
    And isHotelForBooking true
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Currency THB
    And Experiment CFPB-1057 User B
    And Experiment CFF-1118 User A
    And FilterByRoomIdentifiers ChsI2qnxCRACIAIozq5GMARKBzFEMU5fMU5QmAUSAggBGgYgASgEMAE=
    And ReBook with roomTypeID 10365549 originalSellIn 25.00 originalUsdToRequestExchangeRate 4.0
    When The user search
    Then The rebooking summary with prices on room UID 95e9f9cb-73f9-3bb5-9b32-8db224b75d33 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-10-31 | 0      | 10     | 10365549   | 332      | 576423          | Merchant     | 2      | 0.78      | 60.5        | 2        | Mandatory | 0           | 344         |
      | 2017-10-31 | 0      | 3      | 10365549   | 332      | 576423          | Merchant     | 2      | 0         | 0           | 2        | Mandatory | 0           | 344         |
      | 2017-10-31 | 0      | 41     | 10365549   | 332      | 576423          | Merchant     | 2      | 0.78      | 60.5        | 2        | Mandatory | 0           | 344         |
      | 2017-10-31 | 1      | 10     | 10365549   | 332      | 576423          | Merchant     | 1      | 18.45     | 1438.5      | 1        | Mandatory | 0           | 0           |
      | 2017-10-31 | 1      | 3      | 10365549   | 332      | 576423          | Merchant     | 1      | 1.93      | 150         | 1        | Mandatory | 0           | 0           |
      | 2017-10-31 | 1      | 41     | 10365549   | 332      | 576423          | Merchant     | 1      | 20.78     | 1620        | 1        | Mandatory | 0           | 0           |
      | [empty]    | 0      | 40     | 10365549   | 332      | 576423          | Merchant     | 410    | 2.66      | 208.32      | 1        | Unknown   | 0           | 0           |
      | 2017-10-31 | 0      | 12     | 10365549   | 332      | 576423          | Merchant     | 2      | 0.87      | 67.74       | 2        | Mandatory | 0           | 344         |
      | 2017-10-31 | 1      | 12     | 10365549   | 332      | 576423          | Merchant     | 1      | 23.26     | 1813.84     | 1        | Mandatory | 0           | 0           |
    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 10365549   | BDT           | 100.00        | 25.00            | 0.00         | 4.0          | 4.0              | 4.0                | 0.25                     |

  Scenario: CFF-1118=A, Testcase 1.1.6.2, adjust 2 surcharges correctly
    Given Hotels 288020 checkin 2017-04-29 los 1
    And Language 1
    And RatePlans 1,6
    And isHotelForBooking true
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Currency THB
    And Experiment CFPB-1057 User B
    And Experiment CFF-1118 User A
    And FilterByRoomIdentifiers ChcIwJmBARACIAwwBEoHMUQxTl8xTlCYBRICCAEaBiABKAQwAQ==
    And ReBook with roomTypeID 1058400 originalSellIn 28.00 originalUsdToRequestExchangeRate 4.0
    When The user search
    Then The rebooking summary with prices on room UID 46f2eb2c-f99c-9d1d-29d2-c6ddbdbaff3f should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-29 | 0      | 10     | 1058400    | 332      | 0               | Merchant     | 2      | 0.18      | 0.74        | 1        | Mandatory | 0           | 276         |
      | 2017-04-29 | 0      | 10     | 1058400    | 332      | 0               | Merchant     | 2      | 3.88      | 15.54       | 2        | Mandatory | 0           | 288         |
      | 2017-04-29 | 0      | 3      | 1058400    | 332      | 0               | Merchant     | 2      | 0         | 0           | 2        | Mandatory | 0           | 288         |
      | 2017-04-29 | 0      | 3      | 1058400    | 332      | 0               | Merchant     | 2      | 0         | 0           | 1        | Mandatory | 0           | 276         |
      | 2017-04-29 | 0      | 41     | 1058400    | 332      | 0               | Merchant     | 2      | 0.18      | 0.74        | 1        | Mandatory | 0           | 276         |
      | 2017-04-29 | 0      | 41     | 1058400    | 332      | 0               | Merchant     | 2      | 3.88      | 15.54       | 2        | Mandatory | 0           | 288         |
      | 2017-04-29 | 1      | 10     | 1058400    | 332      | 0               | Merchant     | 1      | 13.84     | 55.35       | 1        | Mandatory | 0           | 0           |
      | 2017-04-29 | 1      | 3      | 1058400    | 332      | 0               | Merchant     | 1      | 2.7       | 10.82       | 1        | Mandatory | 0           | 0           |
      | 2017-04-29 | 1      | 41     | 1058400    | 332      | 0               | Merchant     | 1      | 18.45     | 73.8        | 1        | Mandatory | 0           | 0           |
      | [empty]    | 0      | 40     | 1058400    | 332      | 0               | Merchant     | 410    | 1.61      | 6.38        | 1        | Unknown   | 0           | 0           |
      | 2017-04-29 | 0      | 12     | 1058400    | 332      | 0               | Merchant     | 2      | 4.12      | 16.48       | 2        | Mandatory | 0           | 288         |
      | 2017-04-29 | 0      | 12     | 1058400    | 332      | 0               | Merchant     | 2      | 0.19      | 0.78        | 1        | Mandatory | 0           | 276         |
      | 2017-04-29 | 1      | 12     | 1058400    | 332      | 0               | Merchant     | 1      | 19.57     | 78.26       | 1        | Mandatory | 0           | 0           |
    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 1058400    | THB           | 112.00        | 28.00            | 0.00         | 4.0          | 4.0              | 4.0                | 0.25                     |

  Scenario: CFF-1118=B, Testcase 1.1.6.1, adjust a surcharge correctly
    Given Hotels 159114 checkin 2017-10-31 los 1
    And Language 1
    And RatePlans 1,6
    And isHotelForBooking true
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Currency THB
    And Experiment CFPB-1057 User B
    And Experiment CFF-1118 User B
    And FilterByRoomIdentifiers ChsI2qnxCRACIAIozq5GMARKBzFEMU5fMU5QmAUSAggBGgYgASgEMAE=
    And ReBook with roomTypeID 10365549 originalSellIn 25.00 originalUsdToRequestExchangeRate 4.0
    When The user search
    Then The rebooking summary with prices on room UID 95e9f9cb-73f9-3bb5-9b32-8db224b75d33 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-10-31 | 0      | 10     | 10365549   | 332      | 576423          | Merchant     | 2      | 0.78      | 60.5        | 2        | Mandatory | 0           | 344         |
      | 2017-10-31 | 0      | 3      | 10365549   | 332      | 576423          | Merchant     | 2      | 0         | 0           | 2        | Mandatory | 0           | 344         |
      | 2017-10-31 | 0      | 41     | 10365549   | 332      | 576423          | Merchant     | 2      | 0.78      | 60.5        | 2        | Mandatory | 0           | 344         |
      | 2017-10-31 | 1      | 10     | 10365549   | 332      | 576423          | Merchant     | 1      | 18.45     | 1438.5      | 1        | Mandatory | 0           | 0           |
      | 2017-10-31 | 1      | 3      | 10365549   | 332      | 576423          | Merchant     | 1      | 1.93      | 150         | 1        | Mandatory | 0           | 0           |
      | 2017-10-31 | 1      | 41     | 10365549   | 332      | 576423          | Merchant     | 1      | 20.78     | 1620        | 1        | Mandatory | 0           | 0           |
      | [empty]    | 0      | 40     | 10365549   | 332      | 576423          | Merchant     | 410    | 2.66      | 208.32      | 1        | Unknown   | 0           | 0           |
      | 2017-10-31 | 0      | 12     | 10365549   | 332      | 576423          | Merchant     | 2      | 0.87      | 67.74       | 2        | Mandatory | 0           | 344         |
      | 2017-10-31 | 1      | 12     | 10365549   | 332      | 576423          | Merchant     | 1      | 23.26     | 1813.84     | 1        | Mandatory | 0           | 0           |
    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 10365549   | BDT           | 100.00        | 25.00            | 0.00         | 25.7467      | 25.7467          | 25.7467            | 0.038839929000609785     |

  Scenario: CFF-1118=B, Testcase 1.1.6.2, adjust 2 surcharges correctly
    Given Hotels 288020 checkin 2017-04-29 los 1
    And Language 1
    And RatePlans 1,6
    And isHotelForBooking true
    And Platform 1
    And Adults 2
    And Children 0
    And Rooms 1
    And Currency THB
    And Experiment CFPB-1057 User B
    And Experiment CFF-1118 User B
    And FilterByRoomIdentifiers ChcIwJmBARACIAwwBEoHMUQxTl8xTlCYBRICCAEaBiABKAQwAQ==
    And ReBook with roomTypeID 1058400 originalSellIn 28.00 originalUsdToRequestExchangeRate 4.0
    When The user search
    Then The rebooking summary with prices on room UID 46f2eb2c-f99c-9d1d-29d2-c6ddbdbaff3f should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-29 | 0      | 3      | 1058400    | 332      | 0               | Merchant     | 2      | 0         | 0           | 2        | Mandatory | 0           | 288         |
      | 2017-04-29 | 0      | 3      | 1058400    | 332      | 0               | Merchant     | 2      | 0         | 0           | 1        | Mandatory | 0           | 276         |
      | 2017-04-29 | 0      | 12     | 1058400    | 332      | 0               | Merchant     | 2      | 4.12      | 16.48       | 2        | Mandatory | 0           | 288         |
      | 2017-04-29 | 0      | 12     | 1058400    | 332      | 0               | Merchant     | 2      | 0.19      | 0.78        | 1        | Mandatory | 0           | 276         |
      | 2017-04-29 | 1      | 12     | 1058400    | 332      | 0               | Merchant     | 1      | 19.57     | 78.26       | 1        | Mandatory | 0           | 0           |
      | [empty]    | 0      | 40     | 1058400    | 332      | 0               | Merchant     | 410    | 1.61      | -567.75     | 1        | Unknown   | 0           | 0           |
      | 2017-04-29 | 0      | 10     | 1058400    | 332      | 0               | Merchant     | 2      | 0.18      | 4.75        | 1        | Mandatory | 0           | 276         |
      | 2017-04-29 | 0      | 10     | 1058400    | 332      | 0               | Merchant     | 2      | 3.88      | 100         | 2        | Mandatory | 0           | 288         |
      | 2017-04-29 | 0      | 41     | 1058400    | 332      | 0               | Merchant     | 2      | 0.18      | 4.75        | 1        | Mandatory | 0           | 276         |
      | 2017-04-29 | 0      | 41     | 1058400    | 332      | 0               | Merchant     | 2      | 3.88      | 100         | 2        | Mandatory | 0           | 288         |
      | 2017-04-29 | 1      | 10     | 1058400    | 332      | 0               | Merchant     | 1      | 13.84     | 356.25      | 1        | Mandatory | 0           | 0           |
      | 2017-04-29 | 1      | 3      | 1058400    | 332      | 0               | Merchant     | 1      | 2.7       | 69.65       | 1        | Mandatory | 0           | 0           |
      | 2017-04-29 | 1      | 41     | 1058400    | 332      | 0               | Merchant     | 1      | 18.45     | 475         | 1        | Mandatory | 0           | 0           |

    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 1058400    | THB           | 112.00        | 28.00            | 0.00         | 25.7467      | 25.7467          | 25.7467            | 0.038839929000609785     |

  Scenario: CFF-1118=A, Testcase 1.1.7, adjust extrabed correctly
    Given Hotels 6003459 checkin 2017-10-01 los 1
    And Language 1
    And RatePlans 1,6
    And isHotelForBooking true
    And Platform 1
    And Adults 3
    And Children 0
    And Rooms 1
    And Currency THB
    And Experiment CFPB-1057 User B
    And Experiment CFF-1118 User A
    And FilterByRoomIdentifiers 2ad943fc-b67e-4eb2-117f-8023e9f86569
    And ReBook with roomTypeID 10365228 originalSellIn 105.00 originalUsdToRequestExchangeRate 4.0
    When The user search
    Then The rebooking summary with prices on room UID 2ad943fc-b67e-4eb2-117f-8023e9f86569 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | [empty]    | 0      | 40     | 10365228   | 332      | 575781          | Merchant     | 410    | -34.83    | -139.29     | 1        | Unknown   | 0           | 0           |
      | 2017-10-01 | 1      | 10     | 10365228   | 332      | 575781          | Merchant     | 1      | 102.78    | 411.12      | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 12     | 10365228   | 332      | 575781          | Merchant     | 1      | 89.1      | 356.42      | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 3      | 10365228   | 332      | 575781          | Merchant     | 1      | 14.57     | 58.26       | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 41     | 10365228   | 332      | 575781          | Merchant     | 1      | 118.66    | 474.62      | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 10     | 10365228   | 332      | 575781          | Merchant     | 4      | 17.99     | 71.97       | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 12     | 10365228   | 332      | 575781          | Merchant     | 4      | 15.9      | 63.58       | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 3      | 10365228   | 332      | 575781          | Merchant     | 4      | 2.91      | 11.65       | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 41     | 10365228   | 332      | 575781          | Merchant     | 4      | 21.17     | 84.67       | 1        | Mandatory | 0           | 0           |
    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 10365228   | THB           | 420.00        | 105.00           | 0.00         | 4.0          | 4.0              | 4.0                | 0.25                     |
      | 10365226   | THB           | 420.00        | 105.00           | 0.00         | 4.0          | 4.0              | 4.0                | 0.25                     |

  Scenario: CFF-1118=B, Testcase 1.1.7, adjust extrabed correctly
    Given Hotels 6003459 checkin 2017-10-01 los 1
    And Language 1
    And RatePlans 1,6
    And isHotelForBooking true
    And Platform 1
    And Adults 3
    And Children 0
    And Rooms 1
    And Currency THB
    And Experiment CFPB-1057 User B
    And Experiment CFF-1118 User B
    And FilterByRoomIdentifiers 2ad943fc-b67e-4eb2-117f-8023e9f86569
    And ReBook with roomTypeID 10365228 originalSellIn 105.00 originalUsdToRequestExchangeRate 4.0
    When The user search
    Then The rebooking summary with prices on room UID 2ad943fc-b67e-4eb2-117f-8023e9f86569 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-10-01 | 1      | 12     | 10365228   | 332      | 575781          | Merchant     | 1      | 89.1      | 356.42      | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 12     | 10365228   | 332      | 575781          | Merchant     | 4      | 15.9      | 63.58       | 1        | Mandatory | 0           | 0           |
      | [empty]    | 0      | 40     | 10365228   | 332      | 575781          | Merchant     | 410    | -34.83    | -3180       | 1        | Unknown   | 0           | 0           |
      | 2017-10-01 | 1      | 10     | 10365228   | 332      | 575781          | Merchant     | 4      | 17.99     | 463.25      | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 10     | 10365228   | 332      | 575781          | Merchant     | 1      | 102.78    | 2646.25     | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 3      | 10365228   | 332      | 575781          | Merchant     | 4      | 2.91      | 75          | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 3      | 10365228   | 332      | 575781          | Merchant     | 1      | 14.57     | 375         | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 41     | 10365228   | 332      | 575781          | Merchant     | 4      | 21.17     | 545         | 1        | Mandatory | 0           | 0           |
      | 2017-10-01 | 1      | 41     | 10365228   | 332      | 575781          | Merchant     | 1      | 118.66    | 3055        | 1        | Mandatory | 0           | 0           |

    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 10365226   | THB           | 420.00        | 105.00           | 0.00         | 25.7467      | 25.7467          | 25.7467            | 0.038839929000609785     |
      | 10365228   | THB           | 420.00        | 105.00           | 0.00         | 25.7467      | 25.7467          | 25.7467            | 0.038839929000609785     |

  Scenario: Testcase 1.2.1, Rebooking return correct exchangeRates by skipping Freezing FX and CFF-1118=B
    Given Hotels 184553 checkin 2017-04-01 los 1
    And FilterByRoomIdentifiers ChsIvIvxCRACIAIo6utFMAZKBzFEMU5fMU5QmAUSAggBGgQQBCgG
    And isHotelForBooking true
    And Platform 1
    And Origin TH
    And Currency THB
    And CID 0
    And Language 1
    And BookingDate 2017-02-01
    And RatePlans 1
    And Adults 2
    And Children 0
    And Rooms 1
    And isHotelForBooking true
    And Experiment CFF-1118 User B
    And ReBook with roomTypeID 10363614 originalSellIn 540.0 originalUsdToRequestExchangeRate 30.0
    When The user search
    Then The rebooking summary with prices on room UID eb1136c9-a5d4-9866-f0fa-42d1e7a66605 should match the actual result
      | chargeDate | roomNo | itemId | roomTypeId | supplier | rateCategory.id | paymentModel | typeId | USDAmount | localAmount | quantity | option    | prototypeId | surchargeId |
      | 2017-04-01 | 1      | 10     | 10363614   | 332      | 572149          | Merchant     | 1      | 462.74    | 1700        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 41     | 10363614   | 332      | 572149          | Merchant     | 1      | 544.4     | 2000        | 1        | Mandatory | 0           | 0           |
      | 2017-04-01 | 1      | 3      | 10363614   | 332      | 572149          | Merchant     | 1      | 81.66     | 300         | 1        | Mandatory | 0           | 0           |
      | [empty]    | 0      | 40     | 10363614   | 332      | 572149          | Merchant     | 410    | -4.4      | -16.15      | 1        | Unknown   | 0           | 0           |
      | 2017-04-01 | 1      | 12     | 10363614   | 332      | 572149          | Merchant     | 1      | 540       | 1983.85     | 1        | Mandatory | 0           | 0           |
    Then DF should return prices with exchangeRates
      | roomtypeID | localCurrency | paymentAmount | paymentAmountUSD | upliftAmount | exchangeRate | siteExchangeRate | upliftExchangeRate | requestToUSDExchangeRate |
      | 10363614   | AED           | 16200.00      | 540.00           | 0.00         | 25.7467      | 25.7467          | 25.7467            | 0.038839929000609785     |
