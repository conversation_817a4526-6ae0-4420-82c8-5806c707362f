@StayOccupancy
Feature: Stay occupancy from room identifier in Booking form should be correct after user book room

  Background:
    Given All Default Parameters
    And Platform 1
    And StoreFront 3
    And CID 1831843
    And Hotels 264301 checkin 2019-05-01 los 1
    And BookingDate 2019-04-28

  @SOY_FIX
  Scenario: 1: User search 1 room but book 2 room that under fit
   # User search --> 3 Adults, 1 Child, Age 14, 1 Room
    Given Adults 3
    And Children 1
    And ChildAges 14
    And Rooms 1
     #property page search
    When The user search
    And landing to booking form with room identifier where occupancy 2, chanelId 1, roomType 15458498 and rateplan 661340
    And simplifiedRoomSelectionRequest with filtered room identifier, modified rooms 2
    And isHotelForBooking true
    # User select 2 rooms that fit and bigger
    # booking form page search
    When The user search
    Then stay information in BF should match result
      | stayNoOfAdults | stayNoOfChildren | stayListOfChildAge | stayNoOfRoom | roomID   | roomOccupancy |
      | 4              | 0                | [empty]            | 2            | 15458498 | 2             |

  Scenario: 2: User search 1 room but book 2 room that fit and bigger
   # User search --> 3 Adults, 1 Child, Age 14, 1 Room
    Given Adults 3
    And Children 1
    And ChildAges 14
    And Rooms 1
     #property page search
    When The user search
    And landing to booking form with room identifier where occupancy 5, chanelId 1, roomType 15458498 and rateplan 661340
    And simplifiedRoomSelectionRequest with filtered room identifier, modified rooms 2
    And isHotelForBooking true
    # User select 2 rooms that fit and bigger
    When The user search
    Then stay information in BF should match result
      | stayNoOfAdults | stayNoOfChildren | stayListOfChildAge | stayNoOfRoom | roomID   | roomOccupancy |
      | 10             | 0                | [empty]            | 2            | 15458498 | 5             |

  @SOY_FIX
  Scenario: 3: User search 1 adult 1 room that bigger
   # User search --> 1 Adults, 0 Child, 1 Room
    Given Adults 1
    And Children 0
    And Rooms 1
    # User select 1 room that fit and bigger
    When The user search
    And landing to booking form with room identifier where occupancy 5, chanelId 1, roomType 15458498 and rateplan 661340
    And simplifiedRoomSelectionRequest with filtered room identifier, modified rooms 1
    And isHotelForBooking true
    When The user search
    Then stay information in BF should match result
      | stayNoOfAdults | stayNoOfChildren | stayListOfChildAge | stayNoOfRoom | roomID   | roomOccupancy |
      | 5              | 0                | [empty]            | 1            | 15458498 | 5             |

  @SOY_FIX
  Scenario: 4: User search 1 adult 1 room that bigger & modified rooms 2
   # User search --> 1 Adults, 0 Child, 1 Room
    Given Adults 1
    And Children 0
    And Rooms 1
    # User select 1 room that fit and bigger
    When The user search
    And landing to booking form with room identifier where occupancy 5, chanelId 1, roomType 15458498 and rateplan 661340
    And simplifiedRoomSelectionRequest with filtered room identifier, modified rooms 2
    And isHotelForBooking true
    When The user search
    Then stay information in BF should match result
      | stayNoOfAdults | stayNoOfChildren | stayListOfChildAge | stayNoOfRoom | roomID   | roomOccupancy |
      | 10             | 0                | [empty]            | 2            | 15458498 | 5             |

  @SOY_FIX
  Scenario: 5: User search 3 adults but book 2 adults room that exceed capacity
   # User search --> 3 Adults, 1 Child, Age 14, 1 Room
    Given Adults 3
    And Rooms 1
    # User select 1 room that exceed capacity
    When The user search
    And landing to booking form with room identifier where occupancy 2, chanelId 1, roomType 15458498 and rateplan 661340
    And simplifiedRoomSelectionRequest with filtered room identifier, modified rooms 1
    And isHotelForBooking true
    When The user search
    Then stay information in BF should match result
      | stayNoOfAdults | stayNoOfChildren | stayListOfChildAge | stayNoOfRoom | roomID   | roomOccupancy |
      | 2              | 0                | [empty]            | 1            | 15458498 | 2             |

  @SOY_FIX
  Scenario: 6: User search 3 adults but book 2 adults room that exceed capacity
   # User search --> 3 Adults, 1 Child, Age 14, 1 Room
    Given Adults 3
    And Rooms 1
    # User select 1 room that exceed capacity
    When The user search
    And landing to booking form with room identifier where occupancy 2, chanelId 1, roomType 15458498 and rateplan 661340
    And simplifiedRoomSelectionRequest with filtered room identifier, modified rooms 1
    And isHotelForBooking true
    When The user search
    Then stay information in BF should match result
      | stayNoOfAdults | stayNoOfChildren | stayListOfChildAge | stayNoOfRoom | roomID   | roomOccupancy |
      | 2              | 0                | [empty]            | 1            | 15458498 | 2             |

  Scenario: 7: return stayOcc 5A 1C (children as adult) and require 1 extrabed
    Given Hotels 264300 checkin 2017-10-30 los 1
    And BookingDate 2017-10-29
    And Adults 5
    And Children 1
    And ChildAges 6
    And Rooms 2
    And Currency THB
    And CID 1831843
    When The user search
    And landing to booking form with room identifier where occupancy 2, chanelId 1, roomType 15413139 and rateplan 0
    And simplifiedRoomSelectionRequest with filtered room identifier, modified rooms 2
    And isHotelForBooking true
    # User select 2 rooms that fit and bigger
    # booking form page search
    When The user search
    Then occupancy for all dmc rooms should match full result in THB
      | RoomID   | SupplierID | Occupancy | Adults | Children | MandatoryExtraBed | MaxExtraBed | IsFit | SellEx | MaxAllowedFreeChildren |
      | 15413139 | 332        | 2         | 3      | 0        | 1                 | 1           | true  | 386.20 | 2                      |
    Then stay information in BF should match result
      | stayNoOfAdults | stayNoOfChildren | stayListOfChildAge | stayNoOfRoom | roomID   | roomOccupancy |
      | 5              | 1                | 6                  | 2            | 15413139 | 2             |

  Scenario: 8: return stayOcc 5A 1C (free children) and require 1 extrabed
    Given Hotels 264300 checkin 2017-10-30 los 1
    And BookingDate 2017-10-29
    And Adults 5
    And Children 1
    And ChildAges 3
    And Rooms 2
    And Currency THB
    And CID 1831843
    When The user search
    And landing to booking form with room identifier where occupancy 2, chanelId 1, roomType 15413139 and rateplan 0
    And simplifiedRoomSelectionRequest with filtered room identifier, modified rooms 2
    And isHotelForBooking true
    # User select 2 rooms that fit and bigger
    # booking form page search
    When The user search
    Then occupancy for all dmc rooms should match full result in THB
      | RoomID   | SupplierID | Occupancy | Adults | Children | MandatoryExtraBed | MaxExtraBed | IsFit | SellEx | MaxAllowedFreeChildren |
      | 15413139 | 332        | 2         | 3      | 0        | 1                 | 1           | true  | 386.20 | 2                      |
    Then stay information in BF should match result
      | stayNoOfAdults | stayNoOfChildren | stayListOfChildAge | stayNoOfRoom | roomID   | roomOccupancy |
      | 5              | 1                | 3                  | 2            | 15413139 | 2             |
