package com.agoda.papi.pricing.display.utils

import com.agoda.papi.pricing.display.models.PricingDisplayAdditionalRequest
import models.utils.Implicits.DoubleRounding

object DecimalPrecisionUtils {

  /**
    * Applies decimal precision override based on payment method and experiment configuration.
    *
    * @param amount The amount to be rounded
    * @param additionalRequest Contains experiment flags, payment method ID, and whitelabel setting
    * @param requestCurrency The request currency
    * @param defaultPrecision The default precision to use if no override applies
    * @return The rounded amount with appropriate decimal precision
    */
  def applyDecimalPrecisionOverride(
    amount: Double,
    additionalRequest: PricingDisplayAdditionalRequest,
    requestCurrency: String,
    defaultPrecision: Int,
  ): Double =
    if (!additionalRequest.experimentCarrier.isEnableLinePayTwd) {
      amount.roundAt(defaultPrecision)
    } else {
      val paymentMethodId = additionalRequest.paymentMethodId
      val whitelabelSetting = additionalRequest.whitelabelSetting

      (paymentMethodId, whitelabelSetting) match {
        case (Some(methodId), Some(wlSetting)) =>
          if (wlSetting.currencyDecimalOverrideConfig.hasDecimalOverride(requestCurrency, Some(methodId))) {
            val overriddenDecimalPlaces = wlSetting.currencyDecimalOverrideConfig
              .getDecimalPrecision(requestCurrency, Some(methodId), defaultPrecision)
            amount.roundAt(overriddenDecimalPlaces)
          } else {
            amount.roundAt(defaultPrecision)
          }
        case _ => amount.roundAt(defaultPrecision)
      }
    }
}
