package com.agoda.papi.pricing.graphql

import com.agoda.commons.traffic
import com.agoda.commons.traffic.TrafficType
import api.request.{FeatureFlag, PaymentChargeType, PaymentChargeTypes}
import com.agoda.papi.enums.campaign.{
  CampaignDiscountType,
  CampaignDiscountTypes,
  CampaignStatusType,
  CampaignStatusTypes,
  CampaignType,
  CampaignTypes,
  CampaignValidDateType,
  ConsolidatedPeekState,
  InelegiblePromotionReasons,
  IneligiblePromotionReason,
  PromotionCodeType,
  PromotionType,
  PromotionTypes,
}
import com.agoda.papi.enums.hotel.{
  FireDrillContractType,
  PayLaterOptionCode,
  PayLaterSubTypeCode,
  StayPackageType,
  StayPackageTypes,
}
import com.agoda.papi.enums.request.{ContractFilterType, FilterCriteria, StackDiscountOption}
import com.agoda.papi.enums.room.{
  AccrualPolicyStartFrom,
  BenefitTargetType,
  BenefitTargetTypes,
  CancellationChargeSettingType,
  CancellationFeeType,
  CancellationGroup,
  ChildRateType,
  ExternalLoyaltyItem,
  ExternalLoyaltyItemType,
  GeoType,
  LoyaltyPromotionBenefitCategory,
  LoyaltyPromotionBenefitType,
  PricingChildRateType,
  SubChargeType,
  ValueCalculationMethodType,
  ValueMethodType,
  ValueTagType,
  ValueTagTypes,
  WhomToPayType,
}
import models._
import models.enums.{
  AffiliateModelType,
  AffiliatePaymentType,
  CancellationByType,
  CancellationOptionType,
  CancellationReasonType,
  CancellationResultType,
  DispatchAvailabilityType,
  DispatchAvailabilityTypes,
  FinanceOfferType,
  PostBookingPaymentTransactionType,
  PostBookingPaymentType,
  ReBookingActionType,
  RefundOptionType,
  SupplierType,
}
import models.pricing._
import models.pricing.enums.{RatePlanStatus, RoomCategoryChangeType, _}
import models.starfruit._
import sangria.macros.derive._
import sangria.schema._

object EnumSchema {

  /**
    * Create implicit val of Enum's companion object here
    */
  implicit val trafficType = TrafficType
  implicit val CorTypeFlagsEnum = CorTypeFlags
  implicit val DownliftSourcesEnum = DownliftSources
  implicit val RepurposeDiscountMethodEnum = RepurposeDiscountMethod
  implicit val ChargeOptionsEnum = ChargeOptions
  implicit val BookingRateTypesEnum = BookingRateTypes
  implicit val BookingItemTypesEnum = BookingItemTypes
  implicit val ApplyTypesEnumtype = ApplyTypes
  implicit val RateApplyTypesEnum = RateApplyTypes
  implicit val PaymentModelsEnumTypes = PaymentModels
  implicit val ChargeTypesEnumTypes = ChargeTypes
  implicit val ChargeGroupsEnumTypes = ChargeGroups
  implicit val ApplyTypesEnum = ApplyTypes
  implicit val PayTypeesEnum = PayTypes
  implicit val WhomToPayTypesEnumTypes = WhomToPayType
  implicit val RatePlanStatusTypesEnum = RatePlanStatusTypes
  implicit val TaxTypesEnum = TaxTypes
  implicit val PaxTypesEnum = PaxTypes
  implicit val ValueCalculationMethodTypeEnum = ValueCalculationMethodType
  implicit val ValueMethodTypeEnum = ValueMethodType
  implicit val SwapRoomTypesEnum = SwapRoomTypes
  implicit val RoomCategoryChangeTypeEnum = RoomCategoryChangeType
  implicit val GeoTypeEnum = GeoType
  implicit val PricingMessageVariableTypesEnum = PricingMessageVariableType
  implicit val PricingMessageLocalizationTypeEnum = PricingMessageLocalizationType
  implicit val IneligiblePromotionReasonsTypesEnum = InelegiblePromotionReasons
  implicit val discountTypes = DiscountTypes
  implicit val LoyaltyReasonEnum = LoyaltyReasons
  implicit val PaymentChargeTypeEnum = PaymentChargeTypes
  implicit val CancellationChargeSettingTypes = CancellationChargeSettingType
  implicit val RequestedPriceEnum = RequestedPrice
  implicit val subChargeTypes = SubChargeType
  implicit val ChildRateTypeEnum = ChildRateType
  implicit val PricingChildRateTypeEnum = PricingChildRateType
  implicit val RoomBundleTypesEnum = RoomBundleTypes
  implicit val BookingDurationUnitTypesEnum = BookingDurationUnitTypes
  implicit val DFBundleStayTypesEnum = DFBundleStayTypes
  implicit val DFBundleTypeEnum = DFBundleTypes
  implicit val LoyaltyItemTypesEnum = LoyaltyItem
  implicit val BenefitTargetTypeEnum = BenefitTargetTypes
  implicit val StayPackageTypeEnum = StayPackageTypes
  implicit val RoomOfferNameTypeEnum = RoomOfferNameType
  implicit val DispatchAvailabilityTypeEnum = DispatchAvailabilityTypes
  implicit val RatePlanStatusEnum = RatePlanStatuses
  implicit val ExternalLoyaltyItemTypesEnum = ExternalLoyaltyItem
  implicit val CampaignDiscountTypesEnum = CampaignDiscountTypes
  implicit val CampaignStatusTypesEnum = CampaignStatusTypes
  implicit val PromotionTypesEnum = PromotionTypes
  implicit val M150TransparencyTypeEnum = M150TransparencyVersionType
  implicit val ConsolidatedPeekStateEnum = ConsolidatedPeekState
  implicit val ValueTagTypeEnum = ValueTagTypes

  /**
    * Add GraphQL enum object here
    */
  implicit val TrafficTypeType: GQLEnum[TrafficType] = deriveEnumType[TrafficType]()
  implicit val CorTypeFlagType: GQLEnum[CorTypeFlag] = deriveEnumType[CorTypeFlag]()
  implicit val DownliftSourceType: GQLEnum[DownliftSource] = deriveEnumType[DownliftSource]()
  implicit val RepurposeDiscountMethodType: GQLEnum[RepurposeDiscountMethod] = deriveEnumType[RepurposeDiscountMethod]()
  implicit val ChargeOptionType: GQLEnum[ChargeOption] = deriveEnumType[ChargeOption]()
  implicit val BookingItemTypee: GQLEnum[BookingItemType] = deriveEnumType[BookingItemType]()
  implicit val BookingRateType: GQLEnum[BookingRateType] = deriveEnumType[BookingRateType]()
  implicit val ChargeTypeType: GQLEnum[ChargeType] = deriveEnumType[ChargeType]()
  implicit val ChargeGroupType: GQLEnum[ChargeGroup] = deriveEnumType[ChargeGroup]()
  implicit val ApplyTypeType: GQLEnum[ApplyType] = deriveEnumType[ApplyType](EnumTypeName("DFApplyType"))
  implicit val PayTypeType: GQLEnum[PayType] = deriveEnumType[PayType]()
  implicit val WhomToPayTypeInputType: GQLEnum[WhomToPayType] = deriveEnumType[WhomToPayType]()
  implicit val RateApplyTypeType: GQLEnum[RateApplyType] = deriveEnumType[RateApplyType]()
  implicit val PRatePlanStatusTypesType: GQLEnum[RatePlanStatusType] = deriveEnumType[RatePlanStatusType]()
  implicit val TaxTypeType: GQLEnum[TaxType] = deriveEnumType[TaxType]()
  implicit val SupplierTypeType = EnumType(
    name = "SupplierType",
    values = List(
      EnumValue("PUSH", value = SupplierType.Push, description = Some("PUSH")),
      EnumValue("PULL", value = SupplierType.Pull, description = Some("PULL")),
      EnumValue("DISABLED", value = SupplierType.Disabled, description = Some("DISABLED")),
    ),
  )
  implicit val PaxTypeType: GQLEnum[PaxType] = deriveEnumType[PaxType]()
  implicit val ValueCalculationMethodTypeType: GQLEnum[ValueCalculationMethodType] =
    deriveEnumType[ValueCalculationMethodType]()
  implicit val ValueMethodTypeType: GQLEnum[ValueMethodType] = deriveEnumType[ValueMethodType]()
  implicit val SwapRoomTypeType: GQLEnum[SwapRoomType] = deriveEnumType[SwapRoomType]()
  implicit val GeoTypeType: GQLEnum[GeoType] = deriveEnumType[GeoType]()
  implicit val PaymentModelInputType: GQLEnum[PaymentModel] = deriveEnumType[PaymentModel]()
  implicit val PricingMessageVariableTypeEnumType: GQLEnum[PricingMessageVariableType] =
    deriveEnumType[PricingMessageVariableType]()
  implicit val IneligiblePromotionReasonType: GQLEnum[IneligiblePromotionReason] =
    deriveEnumType[IneligiblePromotionReason]()
  implicit val DiscountInputTypeType: GQLEnum[models.pricing.DiscountType] =
    deriveEnumType[models.pricing.DiscountType]()
  implicit val PricingMessageLocalizationEnumType: GQLEnum[PricingMessageLocalizationType] =
    deriveEnumType[PricingMessageLocalizationType]()
  implicit val LoyaltyReasonType: GQLEnum[LoyaltyReason] = deriveEnumType[LoyaltyReason]()
  implicit val FeatureFlagType: GQLEnum[FeatureFlag] = deriveEnumType[FeatureFlag]()
  implicit val FilterCriteriaType: GQLEnum[FilterCriteria] = deriveEnumType[FilterCriteria]()
  implicit val CampaignDiscountTypeType: GQLEnum[CampaignDiscountType] = deriveEnumType[CampaignDiscountType]()
  implicit val CampaignValidDateTypeType: GQLEnum[CampaignValidDateType] = deriveEnumType[CampaignValidDateType]()
  implicit val PaymentChargeTypeType: GQLEnum[PaymentChargeType] = deriveEnumType[PaymentChargeType]()
  implicit val LoyaltyTypeType: GQLEnum[LoyaltyType] = deriveEnumType[LoyaltyType]()
  implicit val LoyaltyItemTypeType: GQLEnum[LoyaltyItemType] = deriveEnumType[LoyaltyItemType]()
  implicit val RoomCategoryChangeTypetYPE: GQLEnum[RoomCategoryChangeType] = deriveEnumType[RoomCategoryChangeType]()
  implicit val ExternalLoyaltyItemTypeType: GQLEnum[ExternalLoyaltyItemType] = deriveEnumType[ExternalLoyaltyItemType]()
  implicit val FinanceOfferTypeType: GQLEnum[FinanceOfferType] = deriveEnumType[FinanceOfferType]()

  implicit val RoomSortingStrategyType: GQLEnum[RoomSortingStrategy] = deriveEnumType[RoomSortingStrategy]()
  implicit val StackDiscountOptionType: GQLEnum[StackDiscountOption] = deriveEnumType[StackDiscountOption]()
  implicit val DealType: GQLEnum[Deal] = deriveEnumType[Deal]()
  implicit val ChannelDiscountType: GQLEnum[ChannelDiscount] = deriveEnumType[ChannelDiscount]()
  implicit val CancellationChargeSettingEnumType: GQLEnum[CancellationChargeSettingType] =
    deriveEnumType[CancellationChargeSettingType]()
  implicit val CancellationGroupType: GQLEnum[CancellationGroup] = deriveEnumType[CancellationGroup]()
  implicit val CancellationFeeTypeType: GQLEnum[CancellationFeeType] = deriveEnumType[CancellationFeeType]()
  implicit val TaxReceiptTypeType: GQLEnum[TaxReceiptType] = deriveEnumType[TaxReceiptType]()
  implicit val RequestedPriceType: GQLEnum[RequestedPrice] =
    deriveEnumType[RequestedPrice](EnumTypeName("DFRequestedPrice"))
  implicit val DiscountTypeType: GQLEnum[DFDiscountType] = deriveEnumType[DFDiscountType]()
  implicit val SubChargeTypes: GQLEnum[SubChargeType] = deriveEnumType[SubChargeType]()
  implicit val ChildRateEnumType: GQLEnum[ChildRateType] = deriveEnumType[ChildRateType]()
  implicit val PricingChildRateEnumType: GQLEnum[PricingChildRateType] = deriveEnumType[PricingChildRateType]()
  implicit val RoomBundleType: GQLEnum[RoomBundleType] = deriveEnumType[RoomBundleType]()
  implicit val BookingDurationUnitType: GQLEnum[BookingDurationUnitType] = deriveEnumType[BookingDurationUnitType]()

  implicit val DFBundleStayType: GQLEnum[DFBundleStayType] = deriveEnumType[DFBundleStayType]()
  implicit val DFBundleType: GQLEnum[DFBundleType] = deriveEnumType[DFBundleType]()
  implicit val BenefitUnitType: GQLEnum[BenefitUnit] = deriveEnumType[BenefitUnit]()
  implicit val BenefitTargetTypeType: GQLEnum[BenefitTargetType] = deriveEnumType[BenefitTargetType]()
  implicit val RareRoomType: GQLEnum[RareRoomType] = deriveEnumType[RareRoomType]()

  implicit val packageOptionalRebateType: GQLEnum[PackageOptionalRebateType] =
    deriveEnumType[PackageOptionalRebateType]()
  implicit val PackageProductEnumTypes: GQLEnum[PackageProductType] = deriveEnumType[PackageProductType]()
  implicit val StayPackageType: GQLEnum[StayPackageType] = deriveEnumType[StayPackageType]()
  implicit val PayLaterSubTypeCodeType: GQLEnum[PayLaterSubTypeCode] = deriveEnumType[PayLaterSubTypeCode]()
  implicit val PayLaterOptionCodeType: GQLEnum[PayLaterOptionCode] = deriveEnumType[PayLaterOptionCode]()
  implicit val ContractFilterEnumType: GQLEnum[ContractFilterType] = deriveEnumType[ContractFilterType]()

  implicit val PromotionTypeEnumType: GQLEnum[PromotionType] = deriveEnumType[PromotionType]()

  implicit val PromotionCodeTypeEnumType: GQLEnum[PromotionCodeType] = deriveEnumType[PromotionCodeType]()

  implicit val RoomOfferNameTypeEnumType: GQLEnum[RoomOfferNameType] = deriveEnumType[RoomOfferNameType]()
  implicit val DispatchAvailabilityTypeEnumType: GQLEnum[DispatchAvailabilityType] =
    deriveEnumType[DispatchAvailabilityType]()
  implicit val RatePlanStatusEnumType: GQLEnum[RatePlanStatus] = deriveEnumType[RatePlanStatus]()

  implicit val RocketmilesPublishPriceLogicTypeEnumType: GQLEnum[RocketmilePublishPriceLogicType] =
    deriveEnumType[RocketmilePublishPriceLogicType]()
  implicit val RocketmilesSortByFieldEnumType: GQLEnum[RocketmileSortByField] = deriveEnumType[RocketmileSortByField]()
  implicit val RocketMilesRoomAwareMatchingLevelTypeEnumType: GQLEnum[RocketMilesRoomAwareMatchingLevelType] =
    deriveEnumType[RocketMilesRoomAwareMatchingLevelType]()
  implicit val FireDrillContractTypeEnumType: GQLEnum[FireDrillContractType] = deriveEnumType[FireDrillContractType]()

  implicit val CampaignTypeEnumType: GQLEnum[CampaignType] = deriveEnumType[CampaignType]()

  implicit val CampaignStatusTypeEnumType: GQLEnum[CampaignStatusType] = deriveEnumType[CampaignStatusType]()

  implicit val ValueTagTypeEnumType: GQLEnum[ValueTagType] = deriveEnumType[ValueTagType]()

  implicit val AffiliateModelTypeType: GQLEnum[AffiliateModelType] = deriveEnumType[AffiliateModelType]()

  implicit val AffiliatePaymentTypeType: GQLEnum[AffiliatePaymentType] = deriveEnumType[AffiliatePaymentType]()

  implicit val CancellationByTypeType: GQLEnum[CancellationByType] = deriveEnumType[CancellationByType]()

  implicit val CancellationReasonTypeType: GQLEnum[CancellationReasonType] = deriveEnumType[CancellationReasonType]()

  implicit val CancellationResultTypeType: GQLEnum[CancellationResultType] = deriveEnumType[CancellationResultType]()

//  implicit val CancellationValidationFailReasonType: GQLEnum[CancellationValidationFailReason] = deriveEnumType[CancellationValidationFailReason]()

  implicit val PostBookingPaymentTransactionTypeType: GQLEnum[PostBookingPaymentTransactionType] =
    deriveEnumType[PostBookingPaymentTransactionType]()

  implicit val PostBookingPaymentTypeType: GQLEnum[PostBookingPaymentType] = deriveEnumType[PostBookingPaymentType]()

  implicit val RefundOptionTypeType: GQLEnum[RefundOptionType] = deriveEnumType[RefundOptionType]()

  implicit val CancellationOptionTypeType: GQLEnum[CancellationOptionType] = deriveEnumType[CancellationOptionType]()

  implicit val M150TransparencyVersionTypeType: GQLEnum[M150TransparencyVersionType] =
    deriveEnumType[M150TransparencyVersionType]()

  implicit val ConsolidatedPeekStateType: GQLEnum[ConsolidatedPeekState] = EnumType[ConsolidatedPeekState](
    name = "ConsolidatedPeekState",
    values = ConsolidatedPeekState.values.toList.map { e =>
      EnumValue(name = e.entryName, description = Some(e.entryName), value = e)
    },
  )

  implicit val ReBookingActionTypeType: GQLEnum[ReBookingActionType] = deriveEnumType[ReBookingActionType]()

  implicit val AccrualPolicyStartFromType: GQLEnum[AccrualPolicyStartFrom] = EnumType[AccrualPolicyStartFrom](
    name = "AccrualPolicyStartFrom",
    values = AccrualPolicyStartFrom.values.toList.map { e =>
      EnumValue(name = e.value, description = Some(e.value), value = e)
    },
  )

  implicit val LoyaltyPromotionBenefitTypeType: GQLEnum[LoyaltyPromotionBenefitType] =
    EnumType[LoyaltyPromotionBenefitType](
      name = "LoyaltyPromotionBenefitType",
      values = LoyaltyPromotionBenefitType.values.toList.map { e =>
        EnumValue(name = e.value, description = Some(e.value), value = e)
      },
    )

  implicit val LoyaltyPromotionBenefitCategoryType: GQLEnum[LoyaltyPromotionBenefitCategory] =
    EnumType[LoyaltyPromotionBenefitCategory](
      name = "LoyaltyPromotionBenefitCategory",
      values = LoyaltyPromotionBenefitCategory.values.toList.map { e =>
        EnumValue(name = e.value, description = Some(e.value), value = e)
      },
    )
}
