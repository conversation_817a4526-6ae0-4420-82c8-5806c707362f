package com.agoda.papi.ypl.commission.apm

import com.agoda.papi.ypl.commission.ApmCommissionHolder
import com.agoda.papi.ypl.commission.apm.helper.APMHelper.isSupplierIdValid
import com.agoda.papi.ypl.commission.apm.models.ApmConst.toGMT
import com.agoda.papi.ypl.commission.apm.models.{
  ApmCommissionDiscountResult,
  ApmCommissionDiscountRoomParameters,
  ApmCommissionReductionEligibilityHolder,
  MultipleAutoPriceMatchHolder,
}
import com.agoda.papi.ypl.commission.models.YplRateFenceHolder
import org.joda.time.DateTime
import com.agoda.papi.ypl.utils.Implicits._

trait ApmCommissionDiscountService extends ApmService {

  /**
    * Main entrypoint to calculate APM commission reduction
    * @return Map[roomUID, ApmCommissionReductionResult]
    */
  def calculateApmCommissionReductionResult(
    apmRoomParams: Map[Int, ApmCommissionDiscountRoomParameters],
    bookingDate: DateTime,
    hotelGmtOffset: Int,
    dispatchedMasterChannels: Set[Int],
    hotelSupplierId: Int,
    hotelId: Long,
    hotelCountryId: Long,
    priceDates: List[DateTime],
    removeBedPaidExclusionFromApmExp: Boolean,
    skipApmPriceAdjustmentForResellExp: Boolean,
    enabledAiForBedNetwork: Boolean,
    enableApmMultipleDiscount: Boolean,
    excludeRateChannelFromApmExp: Boolean,
    isApmFixEndDate: Boolean,
    enabledApmArpPlusProgram: Boolean,
    requestFences: Map[Int, Set[YplRateFenceHolder]]): Map[Int, ApmCommissionDiscountResult]
}

trait ApmCommissionDiscountServiceImpl extends ApmCommissionDiscountService with ApmService {

  private[apm] def calculateIsApmCommissionDiscountMonthly(
    bookingDate: DateTime,
    hotelGmtOffset: Int,
    apmCommissionReductionEligibility: Seq[ApmCommissionReductionEligibilityHolder]): Boolean = {

    val bookingDateHotelTime = bookingDate.plusHours(toGMT + hotelGmtOffset)
    val monthHotelTime = bookingDateHotelTime.getMonthOfYear
    val yearHotelTime = bookingDateHotelTime.getYear

    apmCommissionReductionEligibility
      .find(eligibility => eligibility.month == monthHotelTime && eligibility.year == yearHotelTime)
      .forall(_.isCommissionReductionApplied)
  }

  private[apm] def calculateIsApmCommissionDiscountEligible(
    apmCommissionDiscountPercent: Double,
    bookingDate: DateTime,
    hotelGmtOffset: Int,
    apmCommissionReductionEligibility: Seq[ApmCommissionReductionEligibilityHolder],
    isActiveOrSuspendedApmHotel: Boolean,
    supplierId: Int): Boolean = {
    val isApmCommissionDiscountMonthlyApplied = calculateIsApmCommissionDiscountMonthly(
      bookingDate = bookingDate,
      hotelGmtOffset = hotelGmtOffset,
      apmCommissionReductionEligibility = apmCommissionReductionEligibility,
    )

    val isApmCommissionDiscountEligible = isActiveOrSuspendedApmHotel && isApmCommissionDiscountMonthlyApplied &&
      isSupplierIdValid(supplierId) && apmCommissionDiscountPercent > 0d

    isApmCommissionDiscountEligible
  }

  private[apm] def getCommissionSetting(commissionDiscountChannelSetting: Option[MultipleAutoPriceMatchHolder]) =
    commissionDiscountChannelSetting.exists(_.isActiveAndEnableCommission)

  private[apm] def getCommissionFences(
    requestFences: Map[Int, Set[YplRateFenceHolder]],
    commissionDiscountChannelSetting: Seq[MultipleAutoPriceMatchHolder]): Set[YplRateFenceHolder] = {
    val commissionDiscountChannelList = commissionDiscountChannelSetting.flatMap { setting =>
      setting.commissionDiscountChannelId
    }
    commissionDiscountChannelList.flatMap(key => requestFences.getOrElse(key, Set.empty)).toSet
  }

  override def calculateApmCommissionReductionResult(
    apmRoomParams: Map[Int, ApmCommissionDiscountRoomParameters],
    bookingDate: DateTime,
    hotelGmtOffset: Int,
    dispatchedMasterChannels: Set[Int],
    hotelSupplierId: Int,
    hotelId: Long,
    hotelCountryId: Long,
    priceDates: List[DateTime],
    removeBedPaidExclusionFromApmExp: Boolean,
    skipApmPriceAdjustmentForResellExp: Boolean,
    enabledAiForBedNetwork: Boolean,
    enableApmMultipleDiscount: Boolean,
    excludeRateChannelFromApmExp: Boolean,
    isApmFixEndDate: Boolean,
    enabledApmArpPlusProgram: Boolean,
    requestFences: Map[Int, Set[YplRateFenceHolder]]): Map[Int, ApmCommissionDiscountResult] = {
    val apmCommissionHolder =
      apmRoomParams.values.headOption.map(_.apmCommissionHolder).getOrElse(ApmCommissionHolder.default)
    // Every room containing the exact same holder, so doing head option here.
    val commissionFences = getCommissionFences(requestFences, apmCommissionHolder.multipleAutoPriceMatchHolder)

    val commissionDiscountChannelSetting = getApmCommissionDiscountChannelSetting(
      dispatchedMasterChannels,
      apmCommissionHolder.multipleAutoPriceMatchHolder,
      bookingDate,
      isApmFixEndDate)
    val apmCommissionDiscountPercent =
      if (enableApmMultipleDiscount
        && apmCommissionHolder.apmSettings.exists(_.apmMultipleDiscountHotelList.contains(hotelId))) {
        if (isApmFixEndDate) {
          commissionDiscountChannelSetting
            .flatMap(
              _.apmCommissionDiscount
                .filter(c =>
                  validateStartEndDateRange(c.startDate, Some(c.endDate), bookingDate) &&
                  dispatchedMasterChannels.contains(c.commissionChannelId))
                .flatMap(_.commissionDiscountPercent)
                .maxOption)
            // fallback to old config
            .orElse(commissionDiscountChannelSetting.map(_.commissionDiscountPercent))
            .getOrElse(0d)
        } else {
          commissionDiscountChannelSetting
            .flatMap(
              _.apmCommissionDiscount
                .filter(c =>
                  (c.startDate.isBefore(bookingDate) || c.startDate.isEqual(bookingDate)) &&
                  (c.endDate.isAfter(bookingDate) || c.endDate.isEqual(bookingDate)) &&
                  dispatchedMasterChannels.contains(c.commissionChannelId))
                .flatMap(_.commissionDiscountPercent)
                .maxOption)
            // fallback to old config
            .orElse(commissionDiscountChannelSetting.map(_.commissionDiscountPercent))
            .getOrElse(0d)
        }
      } else {
        commissionDiscountChannelSetting.map(_.commissionDiscountPercent).getOrElse(0d)
      }

    val isApmCommissionDiscountEligible = calculateIsApmCommissionDiscountEligible(
      apmCommissionDiscountPercent = apmCommissionDiscountPercent,
      hotelGmtOffset = hotelGmtOffset,
      apmCommissionReductionEligibility = apmCommissionHolder.apmCommissionReductionEligibility,
      isActiveOrSuspendedApmHotel = getCommissionSetting(commissionDiscountChannelSetting),
      supplierId = hotelSupplierId,
      bookingDate = bookingDate,
    )

    // There's always one program per hotel
    val apmProgramId: Int = commissionDiscountChannelSetting.map(_.programId).getOrElse(0)
    val apmProgramType: Int = commissionDiscountChannelSetting.flatMap(_.programType).getOrElse(0)
    val blackoutDates =
      priceDates.filter(isBlackoutDate(getBlackoutDateListByConfigLevel(apmCommissionHolder.apmConfigs), _)).toSet
    val additionalCommRedHotelLevel = getAdditionalCommissionReductionByConfigLevel(apmCommissionHolder.apmConfigs)
    val apmDiscountPercentWithCommRedHotelLevel = apmCommissionDiscountPercent + additionalCommRedHotelLevel

    apmRoomParams.map { case (index, roomParameters) =>
      val isRoomEligible = validateEligibleRoom(
        roomParameters,
        apmCommissionHolder.apmSettings,
        apmCommissionHolder.apmConfigs,
        hotelCountryId,
        // no adjustment but give commission reduction to rooms under Quarantine Rate channel
        isExcludeQuarantineChannel = false,
        apmProgramId = apmProgramId,
        apmProgramType = apmProgramType,
        skipPackageValidateCommReductionFeatureExp = true,
        removeBedPaidExclusionFromApmExp = removeBedPaidExclusionFromApmExp,
        skipApmPriceAdjustmentForResellExp = skipApmPriceAdjustmentForResellExp,
        enabledAiForBedNetwork = enabledAiForBedNetwork,
        excludeRateChannelFromApmExp = excludeRateChannelFromApmExp,
        enabledApmArpPlusProgram = enabledApmArpPlusProgram,
      )
      val isRoomMatchRateFence = roomParameters.fences.intersect(commissionFences).nonEmpty

      index -> ApmCommissionDiscountResult(
        blackoutDates = blackoutDates,
        isRoomEligible = isRoomEligible && isApmCommissionDiscountEligible && isRoomMatchRateFence,
        apmDiscountPercent = apmDiscountPercentWithCommRedHotelLevel,
        finalCommissionPercent = roomParameters.marginPercentage - apmDiscountPercentWithCommRedHotelLevel,
        commissionDiscountChannelSetting = commissionDiscountChannelSetting,
        isSupplierValid = getCommissionSetting(commissionDiscountChannelSetting)
          && isSupplierIdValid(roomParameters.supplierId),
      )
    }
  }
}
