package com.agoda.papi.ypl.commission.apm.models

import com.agoda.papi.enums.hotel.{DMC, StayPackageType}
import com.agoda.papi.enums.room.Channel._
import org.joda.time.DateTime

trait ApmRoomParameters {
  val compositeChannels: List[Int]
  val stayPackageType: Option[StayPackageType]
  val isExternalResellDataEmpty: Boolean
  val isEmptyHourlyAvailableSlots: Boolean
}

object ApmConst {
  val toGMT: Int = -7
  val DEFAULT_MAX_DELTA_PERCENT: Double = 30.0
  object Channel {
    val BedbankChannels: Set[Int] = Set(Bedbank, BedbankAffiliates).map(_.i)
    val NotEligibleRcForApmNam: Set[Int] = Set(APO, Packages, RestPackage, RestOpaque).map(_.i)
  }

  object Supplier {
    val apmDirectConnectSupplierList: Set[Int] = Set(
      <PERSON><PERSON><PERSON>,
      DMC<PERSON>,
      DMC.HiltonByD /* AKA. Channel.DHIL */,
      DMC.Hyatt,
      DMC.IHGD,
      DMC.<PERSON>,
      DMC.OmniDhisco,
      DMC.Pegasus,
      DMC.RezTrip,
      DMC.PegasusRV27994,
      DMC.SynXisCCRS,
      DMC.SynXisECRS,
      DMC.Disney,
      DMC.MGM,
      DMC.Radisson,
      DMC.SynXisUCRS,
      DMC.OYO,
    ).map(_.value)
  }
}

case class ApmSettingHolder(apmSellExProgramIds: List[Int],
                            excludeNamRcProgramIds: List[Int],
                            bedsAdvancedApmProgramIds: List[Int],
                            apmMadOBlackListCountryList: List[Long] = List.empty,
                            apmMultipleDiscountHotelList: List[Long] = List.empty,
                            apmTaiwanAsoEligibleProgramIds: List[Int] = List.empty,
                            apmTaiwanAsoEligibleProgramTypes: List[Int] = List.empty,
                            excludeRateChannelIds: List[Int] = List.empty)

case class ApmCommissionReductionEligibilityHolder(year: Int, month: Int, isCommissionReductionApplied: Boolean)

case class MultipleAutoPriceMatchHolder(programId: Int,
                                        commissionDiscountChannelId: Option[Int],
                                        commissionDiscountPercent: Double,
                                        adjustmentChannelId: Int,
                                        adjustmentDiscountPercent: Double,
                                        statusId: Int,
                                        startDate: DateTime,
                                        endDate: Option[DateTime],
                                        apmAdjustmentDiscount: Seq[ApmAdjustmentDiscount],
                                        apmCommissionDiscount: Seq[ApmCommissionDiscount],
                                        programType: Option[Int]) {
  lazy val isActive: Boolean = statusId == ApmHotelStatus.Active
  lazy val isActiveOrSuspendedApmHotel: Boolean =
    statusId == ApmHotelStatus.Active || statusId == ApmHotelStatus.Suspended

  lazy val isActiveByApmHotelStatusWithProgramType: Boolean =
    ApmHotelStatus.isActiveByApmHotelStatusWithProgramType(statusId)

  lazy val isEnableCommissionByProgramType: Boolean =
    ApmProgramType.isEnableCommissionByProgramType(programType.getOrElse(0))

  lazy val isActiveAndEnableCommission: Boolean =
    isActiveByApmHotelStatusWithProgramType && isEnableCommissionByProgramType
}

// TODO: Migrate From YplModel.HotelMeta once the experiment VEL-607 has flatten B
case class ApmConfigHolder(globalLevel: Seq[String], programLevel: Map[Int, Seq[String]], hotelLevel: Seq[String])

case class ApmAdjustmentDiscount(programId: Int,
                                 adjustmentChannelId: Int,
                                 adjustmentDiscountPercent: Option[Double],
                                 startDate: DateTime,
                                 endDate: DateTime)
case class ApmCommissionDiscount(programId: Int,
                                 commissionChannelId: Int,
                                 commissionDiscountPercent: Option[Double],
                                 startDate: DateTime,
                                 endDate: DateTime)

object ApmHotelStatus {
  val Active = 1
  val InActive = 2
  val Suspended = 3
  val Experimental = 6
  val ExperimentalV2 = 7
  val PeakSeason = 8

  def isActiveByApmHotelStatus(status: Int): Boolean = status match {
    case Active | Suspended | Experimental | ExperimentalV2 | PeakSeason => true
    case _ => false
  }

  def isActiveByApmHotelStatusWithProgramType(status: Int): Boolean = status match {
    case Active | Suspended | Experimental | ExperimentalV2 | PeakSeason => true
    case _ => false
  }
}

object ApmProgramType {
  val Ai = 1
  val AiCalendarView = 2
  val PeakSeason = 11
  val Arp = 12
  val ArpV2 = 13
  val ArpPlus = 16

  def isEnableCommissionByProgramType(apmProgramType: Int): Boolean = apmProgramType match {
    case AiCalendarView => false
    case _ => true
  }
}
