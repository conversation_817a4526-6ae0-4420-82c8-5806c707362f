package com.agoda.papi.ypl.commission.apm

import com.agoda.papi.enums.hotel.StayPackageType
import com.agoda.papi.enums.hotel.StayPackageTypes.Escapes
import com.agoda.papi.enums.room.Channel
import com.agoda.papi.ypl.commission.apm.`enum`.ApmConfigType
import com.agoda.papi.ypl.commission.apm.models.ApmConst.Channel.{BedbankChannels, NotEligibleRcForApmNam}
import com.agoda.papi.ypl.commission.apm.models.{
  ApmConfigHolder,
  ApmProgramType,
  ApmRoomParameters,
  ApmSettingHolder,
  MultipleAutoPriceMatchHolder,
}
import org.joda.time.DateTime

import scala.util.Try

/**
  * Containing methods used in both APM Discount and Normal APM and AGP
  */
trait ApmSettingService {
  def getApmRoomChannelSettings(multipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder],
                                bookingDateTime: DateTime,
                                isApmFixDate: Boolean): Seq[MultipleAutoPriceMatchHolder] =
    if (isApmFixDate) {
      multipleAutoPriceMatch.filter { item =>
        item.isActive && validateStartEndDateRange(item.startDate, item.endDate, bookingDateTime)
      }
    } else {
      multipleAutoPriceMatch.filter { item =>
        item.isActive &&
        !item.startDate.isAfter(bookingDateTime) &&
        item.endDate.forall(date => !date.isBefore(bookingDateTime))
      }
    }

  def getApmCommissionDiscountChannelSetting(dispatchChannels: Set[Int],
                                             multipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder],
                                             bookingDateTime: DateTime,
                                             isApmFixDate: Boolean): Option[MultipleAutoPriceMatchHolder] =
    if (isApmFixDate) {
      multipleAutoPriceMatch
        .filter { apmSetting =>
          apmSetting.commissionDiscountChannelId match {
            case Some(commissionChannelId) => dispatchChannels.contains(commissionChannelId) &&
              getCommissionHotelStatus(apmSetting) &&
              validateStartEndDateRange(apmSetting.startDate, apmSetting.endDate, bookingDateTime)
            case _ => false // default case, otherwise will have exception
          }
        }
        .sortBy(item => (item.commissionDiscountPercent, item.programId))
        .lastOption
    } else {
      multipleAutoPriceMatch
        .filter { apmSetting =>
          apmSetting.commissionDiscountChannelId match {
            case Some(commissionChannelId) => dispatchChannels.contains(commissionChannelId) &&
              getCommissionHotelStatus(apmSetting) &&
              !apmSetting.startDate.isAfter(bookingDateTime) &&
              apmSetting.endDate.forall(date => !date.isBefore(bookingDateTime))
            case _ => false // default case, otherwise will have exception
          }
        }
        .sortBy(item => (item.commissionDiscountPercent, item.programId))
        .lastOption
    }

  def getCommissionHotelStatus(apmSetting: MultipleAutoPriceMatchHolder): Boolean =
    apmSetting.isActiveAndEnableCommission

  def validateStartEndDateRange(startDate: DateTime, endDate: Option[DateTime], bookingDateTime: DateTime): Boolean =
    !startDate.isAfter(bookingDateTime) && endDate.forall(endDate => bookingDateTime.isBefore(endDate.plusDays(1)))

}

private[commission] trait ApmService extends ApmSettingService {
  def validateEligibleRoom[T <: ApmRoomParameters](apmCommissionRoomParameters: T,
                                                   apmSetting: Option[ApmSettingHolder],
                                                   apmConfigs: Map[Int, ApmConfigHolder],
                                                   hotelCountryId: Long,
                                                   isExcludeQuarantineChannel: Boolean,
                                                   apmProgramId: Int,
                                                   apmProgramType: Int,
                                                   skipPackageValidateCommReductionFeatureExp: Boolean,
                                                   removeBedPaidExclusionFromApmExp: Boolean,
                                                   skipApmPriceAdjustmentForResellExp: Boolean,
                                                   enabledAiForBedNetwork: Boolean,
                                                   excludeRateChannelFromApmExp: Boolean,
                                                   enabledApmArpPlusProgram: Boolean): Boolean = {
    def isRoomValidForBedsAdvancedExp(isBedsChannel: Boolean) =
      if (removeBedPaidExclusionFromApmExp) {
        val isBedsAdvancedProgramId = apmSetting.exists(_.bedsAdvancedApmProgramIds.contains(apmProgramId))
        isBedsChannel && isBedsAdvancedProgramId
      } else !isBedsChannel

    def isEnableAiForBedNetWork(isBedsChannel: Boolean): Boolean =
      // for BN if there's enableAiForBedNetwork config then apply AI else check for BedAi validation(isRoomValidForBedsAdvancedExp)
      if (isBedsChannel) {
        if (enabledAiForBedNetwork && getEnableAiForBedNetworkByConfigLevel(apmConfigs)) {
          true
        } else {
          isRoomValidForBedsAdvancedExp(isBedsChannel)
        }
      } else true

    def isTaiwanASOEligible(hotelCountryId: Long,
                            stayPackageType: Option[StayPackageType],
                            apmProgramId: Int,
                            apmProgramType: Int): Boolean = {
      val TAIWAN_COUNTRY_ID = 140
      if (hotelCountryId == TAIWAN_COUNTRY_ID && stayPackageType.contains(Escapes)) {
        if (enabledApmArpPlusProgram) {
          apmSetting.exists(_.apmTaiwanAsoEligibleProgramTypes.contains(apmProgramType))
        } else {
          apmSetting.exists(_.apmTaiwanAsoEligibleProgramIds.contains(apmProgramId))
        }
      } else true
    }

    def isExcludeRateChannelFromApm(rateChannelId: Int): Boolean =
      if (enabledApmArpPlusProgram) {
        excludeRateChannelFromApmExp && apmSetting.exists(
          _.excludeRateChannelIds.contains(rateChannelId)) && apmProgramType == ApmProgramType.ArpPlus
      } else {
        excludeRateChannelFromApmExp && apmSetting.exists(_.excludeRateChannelIds.contains(rateChannelId))
      }

    // APM do not apply to hourly rates rooms
    // APM do not apply with BedBank Rate Channel - From Feb 2022 BedBank Rate Channel is re-enabled under experiment
    // APM do not apply to `Package` Rate Plan for price adjustment (but comm reduction will apply on package)
    // APM do not apply to Quarantine Rate channel but give commission reduction to rooms under Quarantine Rate channel
    // APM do not apply to Taiwan ASO when program_id is not in (149, 150, 151) or program_type is not in (11, 16) based on experiment
    // APM do not apply to APO, Package, RestPackage, RestOpaque for NAM program
    // APM do not apply to resell offers
    // APM do not apply to a program if it's rate channel id is in excludeRateChannelIds
    val channel = apmCommissionRoomParameters.compositeChannels
    val stayPackageType = apmCommissionRoomParameters.stayPackageType
    val isNotPackage = skipPackageValidateCommReductionFeatureExp || !channel.contains(Channel.Packages.i)
    val isNotQuarantineChannel = if (isExcludeQuarantineChannel) !channel.contains(Channel.Quarantine.i) else true
    val isBedsChannel = BedbankChannels.exists(channel.contains)
    val isEnableAiForBedNetwork = isEnableAiForBedNetWork(isBedsChannel)

    val isEligibleTaiwanASO = isTaiwanASOEligible(hotelCountryId, stayPackageType, apmProgramId, apmProgramType)

    val isExcludeRateChannel = channel.exists(isExcludeRateChannelFromApm)

    val excludeNamRcProgramIds = apmSetting.map(_.excludeNamRcProgramIds).getOrElse(Nil)
    val isNotInBlackListRcForNam =
      if (excludeNamRcProgramIds.contains(apmProgramId))
        !NotEligibleRcForApmNam.exists(nonEligibleRc => channel.contains(nonEligibleRc))
      else true

    val isNotResellOffer = !skipApmPriceAdjustmentForResellExp || apmCommissionRoomParameters.isExternalResellDataEmpty

    apmCommissionRoomParameters.isEmptyHourlyAvailableSlots && isNotPackage &&
    isEnableAiForBedNetwork && isNotQuarantineChannel && isEligibleTaiwanASO && isNotInBlackListRcForNam && isNotResellOffer && !isExcludeRateChannel
  }

  private def getApmConfig[T](allConfigs: Map[Int, ApmConfigHolder],
                              configType: ApmConfigType,
                              processFunc: Seq[String] => T,
                              fallBackApmConfigHolder: ApmConfigHolder =
                                ApmConfigHolder(Seq.empty, Map.empty, Seq.empty)): T = {
    val selectedConfig = allConfigs.getOrElse(configType.value, fallBackApmConfigHolder)
    processFunc(selectedConfig.hotelLevel)
  }

  def getAdditionalCommissionReductionByConfigLevel(allConfigs: Map[Int, ApmConfigHolder]): Double = getApmConfig(
    allConfigs,
    ApmConfigType.CommissionReduction,
    commission => commission.headOption.map(c => Try(c.toDouble).getOrElse(0d)).getOrElse(0d),
  )

  def getEnableAiForBedNetworkByConfigLevel(allConfigs: Map[Int, ApmConfigHolder]): Boolean = getApmConfig(
    allConfigs,
    ApmConfigType.EnableAiForBedNetwork,
    eligibleHotel => eligibleHotel.headOption.nonEmpty,
  )

  def getBlackoutDateListByConfigLevel(allConfigs: Map[Int, ApmConfigHolder]): Seq[DateTime] = getApmConfig(
    allConfigs,
    ApmConfigType.BlackoutDays,
    dates => dates.flatMap(d => Try(DateTime.parse(d)).toOption.toList),
  )

  // Both APM Discount and Normal APM and AGP
  def isBlackoutDate(blackoutDates: Seq[DateTime], stayDate: DateTime): Boolean =
    blackoutDates.exists(_.compareTo(stayDate) == 0)

}
