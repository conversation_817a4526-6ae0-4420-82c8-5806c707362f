package com.agoda.papi.ypl.commission.apm

import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.enums.room.{Channel, ChargeType, RateType, SubChargeType}
import com.agoda.papi.ypl.commission.ApmCommissionHolder
import com.agoda.papi.ypl.commission.apm.models.ApmConst.DEFAULT_MAX_DELTA_PERCENT
import com.agoda.papi.ypl.commission.apm.models.ApmTypes.RoomIndex
import com.agoda.papi.ypl.commission.apm.models._
import com.agoda.papi.ypl.commission.builder.{
  ApmCommissionHolderBuilder,
  ApmPriceAdjustmentRoomParametersBuilder,
  MultipleAutoPriceMatchHolderBuilder,
}
import com.agoda.papi.ypl.commission.models.{RoomOccupancyHolder, YplRateFenceHolder}
import com.agoda.utils.DoubleRounding
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers
import org.mockito.Mockito.{times, verify}
import org.specs2.matcher.AnyMatchers
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope

class ApmPriceAdjustmentServiceSpec extends SpecificationWithJUnit with Mockito with AnyMatchers {
  val aValidHotelId = 1
  val aValidFences = Set(YplRateFenceHolder("TH", 1, 1)) // TOE
  val checkIn: DateTime = DateTime.parse("2023-08-30")
  val aValidPrice: ApmPriceAdjustmentPriceParameters =
    ApmPriceAdjustmentPriceParameters(checkIn, 0, 0, ChargeType.Room, SubChargeType.None)
  val aValidStayDate1: DateTime = checkIn
  val aValidStayDate2: DateTime = checkIn.plusDays(1)
  val aValidStayDate3: DateTime = checkIn.plusDays(2)
  val aValidStayDate4: DateTime = checkIn.plusDays(3)
  val aValidLos = 4
  val aValidRoomOccupancy: RoomOccupancyHolder =
    RoomOccupancyHolder(adults = 2, children = 0, extraBeds = 0, infants = 0)
  val aValidYplChannelRTL: Int = Channel.Retail.i
  val aValidYplChannelAPS: Int = Channel.APS.i
  val aValidYplChannelAPM = 1051
  val aValidMarginPercentage = 15d

  val aValidApmSellInDate1: Double = 130d
  val aValidApmSellInDate2: Double = 120d
  val aValidApmSellInDate4: Double = 135d

  val aValidRoomTypeId1: Long = 1111
  val aValidRoomTypeId2: Long = 2222
  val aValidRoomTypeId3: Long = 3333
  val aValidRoomTypeId4: Long = 4444

  val aValidRateCategoryId1: Int = 71
  val aValidRateCategoryId2: Int = 72
  val aValidRateCategoryId3: Int = 73
  val validApprovalPriceIdPool: Seq[Long] = (1L to 10L)

  val aValidCountryId1: Long = 1

  val YCSSupplierID = 332
  val aValidApmCommissionHolder =
    ApmCommissionHolderBuilder(ApmCommissionHolder(Seq.empty, Seq.empty, Map.empty, None, Map.empty, Map.empty))
  val aValidMultipleAutoPriceMatchHolder = MultipleAutoPriceMatchHolderBuilder(
    MultipleAutoPriceMatchHolder(1,
                                 None,
                                 10d,
                                 1,
                                 10d,
                                 ApmHotelStatus.Active,
                                 DateTime.parse("2022-01-01"),
                                 None,
                                 Seq.empty,
                                 Seq.empty,
                                 Some(1)))

  val aValidApmSettingHolder = Some(ApmSettingHolder(List.empty, List.empty, List.empty, List.empty, List(1)))
  val aValidRoom = ApmPriceAdjustmentRoomParametersBuilder(
    ApmPriceAdjustmentRoomParameters(
      "uid",
      1,
      RoomOccupancyHolder(1, 0, 0, 0),
      15d,
      List.empty,
      None,
      false,
      false,
      ApmCommissionHolder.default,
      1,
      1,
      false,
      PaymentModel.Unknown,
      0,
      aValidFences,
      List.empty,
    ))
  val baseRoom = aValidRoom
    .withRoomTypeId(aValidRoomTypeId1)
    .withSupplierId(332)
    .withOccupancy(aValidRoomOccupancy)
    .withRateCategoryId(aValidRateCategoryId1)
    .withCompositeChannels(List(aValidYplChannelRTL))
    .withPaymentModel(PaymentModel.Merchant)
    .withMarginPercentage(aValidMarginPercentage)
  def buildValidYPLPrice(chargeType: ChargeType,
                         date: DateTime,
                         netEx: Double,
                         multiplier: Double = 1d,
                         subChargeType: SubChargeType = SubChargeType.None): ApmPriceAdjustmentPriceParameters = {
    val netExclusive = netEx * multiplier

    val margin = netExclusive * 0.15d
    val fee = netExclusive * 0.07d
    val tax = (netExclusive * 0.1d) + (fee * 0.1d)
    val pf = margin * 0.1d

    val netInclusive = netExclusive + tax + fee
    val sellExclusive = netExclusive + margin
    val sellInclusive = netExclusive + margin + tax + fee + pf

    aValidPrice.copy(
      chargeType = chargeType,
      date = date,
      sellExclusive = sellExclusive,
      sellInclusive = sellInclusive,
      subChargeType = subChargeType,
    )
  }

  val aValidPrices = Seq(
    buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
    buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
    buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
    buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate1, 50d),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate2, 50d),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate3, 50d),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate4, 50d),
  )

  val aValidPricesWithSubChargeType = Seq(
    buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d, 1, SubChargeType.None),
    buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d, 1, SubChargeType.Adult),
    buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 50d, 1, SubChargeType.Baby),
    buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 50d, 1, SubChargeType.Toddler),
    buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 50d, 1, SubChargeType.Child),
    buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 50d, 1, SubChargeType.PreSchool),
    buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 50d, 1, SubChargeType.GradeSchool),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate1, 100d, 1, SubChargeType.None),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate2, 100d, 1, SubChargeType.Adult),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate3, 100d, 1, SubChargeType.None),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate4, 100d, 1, SubChargeType.Adult),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate1, 50d, 1, SubChargeType.Baby),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate2, 50d, 1, SubChargeType.Toddler),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate3, 50d, 1, SubChargeType.Child),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate4, 50d, 1, SubChargeType.PreSchool),
    buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate4, 50d, 1, SubChargeType.GradeSchool),
  )

  def aValidApmSellInPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo] = Map(
    aValidStayDate1 -> AutoPriceMatchPriceInfo(aValidApmSellInDate1, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
    aValidStayDate2 -> AutoPriceMatchPriceInfo(aValidApmSellInDate2, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
    aValidStayDate4 -> AutoPriceMatchPriceInfo(aValidApmSellInDate4, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
  )

  val apmDeltaPercentHotelLevelOnly =
    ApmDeltaPercentageHolder(hotelLevel = Some(10), programLevel = None, globalLevel = None)
  val apmDeltaPercentHotelProgramLevelOnly =
    ApmDeltaPercentageHolder(hotelLevel = Some(10), programLevel = Some(20), globalLevel = None)
  val apmDeltaPercentHotelGlobalLevelOnly =
    ApmDeltaPercentageHolder(hotelLevel = Some(10), programLevel = None, globalLevel = Some(20))
  val apmDeltaPercentProgramLevelOnly =
    ApmDeltaPercentageHolder(hotelLevel = None, programLevel = Some(20), globalLevel = None)
  val apmDeltaPercentProgramGlobalLevelOnly =
    ApmDeltaPercentageHolder(hotelLevel = None, programLevel = Some(20), globalLevel = Some(30))
  val apmDeltaPercentGlobalLevelOnly =
    ApmDeltaPercentageHolder(hotelLevel = None, programLevel = None, globalLevel = Some(30))
  val apmDeltaPercentHotelProgramGlobalLevelOnly =
    ApmDeltaPercentageHolder(hotelLevel = Some(10), programLevel = Some(20), globalLevel = Some(30))
  val apmDeltaPercentHotelEmpty = ApmDeltaPercentageHolder(hotelLevel = None, programLevel = None, globalLevel = None)

  val originalRooms: List[(RoomIndex, ApmPriceAdjustmentRoomParameters)] = List(
    aValidRoom
      .withRoomTypeId(aValidRoomTypeId1)
      .withSupplierId(332)
      .withOccupancy(aValidRoomOccupancy)
      .withRateCategoryId(aValidRateCategoryId1)
      .withCompositeChannels(List(aValidYplChannelRTL))
      .withPaymentModel(PaymentModel.Merchant)
      .withMarginPercentage(aValidMarginPercentage)
      .withPrices(List(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
      ).zipWithIndex.map(t => (t._2, t._1)))
      .build,
    aValidRoom
      .withRoomTypeId(aValidRoomTypeId1)
      .withSupplierId(YCSSupplierID)
      .withOccupancy(aValidRoomOccupancy)
      .withRateCategoryId(aValidRateCategoryId1)
      .withCompositeChannels(List(aValidYplChannelRTL))
      .withPaymentModel(PaymentModel.Agency)
      .withMarginPercentage(aValidMarginPercentage)
      .withPrices(List(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
      ).zipWithIndex.map(t => (t._2, t._1)))
      .build,
    aValidRoom
      .withRoomTypeId(aValidRoomTypeId1)
      .withSupplierId(YCSSupplierID)
      .withOccupancy(aValidRoomOccupancy)
      .withRateCategoryId(aValidRateCategoryId1)
      .withCompositeChannels(List(aValidYplChannelAPS))
      .withPaymentModel(PaymentModel.Merchant)
      .withMarginPercentage(aValidMarginPercentage)
      .withPrices(List(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 90d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 90d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 90d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 90d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 45d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 45d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 45d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 45d),
      ).zipWithIndex.map(t => (t._2, t._1)))
      .build,
    aValidRoom
      .withRoomTypeId(aValidRoomTypeId1)
      .withSupplierId(YCSSupplierID)
      .withOccupancy(aValidRoomOccupancy)
      .withRateCategoryId(aValidRateCategoryId2)
      .withCompositeChannels(List(aValidYplChannelAPS))
      .withPaymentModel(PaymentModel.Merchant)
      .withMarginPercentage(aValidMarginPercentage)
      .withPrices(List(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 105d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 105d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 105d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 105d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 54d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 54d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 54d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 54d),
      ).zipWithIndex.map(t => (t._2, t._1)))
      .build,
    aValidRoom
      .withRoomTypeId(aValidRoomTypeId2)
      .withSupplierId(YCSSupplierID)
      .withOccupancy(aValidRoomOccupancy)
      .withRateCategoryId(aValidRateCategoryId1)
      .withCompositeChannels(List(aValidYplChannelRTL))
      .withPaymentModel(PaymentModel.Merchant)
      .withMarginPercentage(aValidMarginPercentage)
      .withPrices(List(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
      ).zipWithIndex.map(t => (t._2, t._1)))
      .build,
    aValidRoom
      .withRoomTypeId(aValidRoomTypeId2)
      .withSupplierId(YCSSupplierID)
      .withOccupancy(aValidRoomOccupancy)
      .withRateCategoryId(aValidRateCategoryId1)
      .withCompositeChannels(List(aValidYplChannelAPS))
      .withPaymentModel(PaymentModel.Merchant)
      .withMarginPercentage(aValidMarginPercentage)
      .withPrices(List(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 90d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 90d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 90d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 90d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 45d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 45d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 45d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 45d),
      ).zipWithIndex.map(t => (t._2, t._1)))
      .build,
    aValidRoom
      .withRoomTypeId(aValidRoomTypeId2)
      .withSupplierId(YCSSupplierID)
      .withOccupancy(aValidRoomOccupancy)
      .withRateCategoryId(aValidRateCategoryId2)
      .withCompositeChannels(List(aValidYplChannelRTL))
      .withPaymentModel(PaymentModel.Merchant)
      .withMarginPercentage(aValidMarginPercentage)
      .withPrices(List(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 120d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 120d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 120d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 120d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 60d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 60d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 60d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 60d),
      ).zipWithIndex.map(t => (t._2, t._1)))
      .build,
    aValidRoom
      .withRoomTypeId(aValidRoomTypeId2)
      .withSupplierId(YCSSupplierID)
      .withOccupancy(aValidRoomOccupancy)
      .withRateCategoryId(aValidRateCategoryId2)
      .withCompositeChannels(List(aValidYplChannelAPS))
      .withPaymentModel(PaymentModel.Merchant)
      .withMarginPercentage(aValidMarginPercentage)
      .withPrices(List(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 105d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 105d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 105d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 105d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 54d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 54d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 54d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 54d),
      ).zipWithIndex.map(t => (t._2, t._1)))
      .build,
    aValidRoom
      .withRoomTypeId(aValidRoomTypeId3)
      .withSupplierId(YCSSupplierID)
      .withOccupancy(aValidRoomOccupancy)
      .withRateCategoryId(aValidRateCategoryId3)
      .withCompositeChannels(List(aValidYplChannelRTL))
      .withPaymentModel(PaymentModel.Merchant)
      .withMarginPercentage(aValidMarginPercentage)
      .withPrices(List(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 40d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 40d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 40d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 40d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 10d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 10d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 10d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 10d),
      ).zipWithIndex.map(t => (t._2, t._1)))
      .build,
    aValidRoom
      .withRoomTypeId(aValidRoomTypeId4)
      .withSupplierId(YCSSupplierID)
      .withOccupancy(aValidRoomOccupancy)
      .withRateCategoryId(aValidRateCategoryId3)
      .withCompositeChannels(List(aValidYplChannelRTL))
      .withPaymentModel(PaymentModel.Merchant)
      .withMarginPercentage(aValidMarginPercentage)
      .withPrices(List(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 40d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 40d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 40d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 40d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 10d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 10d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 10d),
        buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 10d),
      ).zipWithIndex.map(t => (t._2, t._1)))
      .build,
  ).zipWithIndex.map(t => (t._2, t._1))

  trait ApmScope extends Scope with ApmPriceAdjustmentServiceImpl

  "buildPriceDailyMap" should {
    "return buildRoomPriceDeltaMapV2 correctly when aValidApmCommissionDiscountPercent = zero, aValidMaximumDeltaPercentOpt = None" in new ApmScope {
      val aValidApmPriceDiscountPercent: Double = 0
      val aValidMaximumDeltaPercentOpt: Option[Double] = None
      val isSellExAdjustment: Boolean = false

      buildRoomPriceDeltaMapV2(aValidPrices,
                               aValidApmSellInPriceDailyMap,
                               aValidApmPriceDiscountPercent,
                               aValidMaximumDeltaPercentOpt,
                               aValidRoomTypeId1,
                               isSellExAdjustment,
                               Seq.empty) should_=== (Map(
        (aValidStayDate1, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive - aValidApmSellInDate1,
            rawDelta = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive - aValidApmSellInDate1,
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate1,
            priceDiscountPercent = 0d,
            maximumDeltaPercent = DEFAULT_MAX_DELTA_PERCENT,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(0),
          ),
        (aValidStayDate2, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive - aValidApmSellInDate2,
            rawDelta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive - aValidApmSellInDate2,
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate2,
            priceDiscountPercent = 0d,
            maximumDeltaPercent = DEFAULT_MAX_DELTA_PERCENT,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(1),
          ),
      ), List(ApmApprovalPriceNotUseReason(3, NotUseReasonConstants.ycsCheaperThanOTA)))
    }

    "return buildRoomPriceDeltaMapV2 correctly when aValidApmCommissionDiscountPercent = 100" in new ApmScope {
      val aValidApmPriceDiscountPercent: Double = 100
      val aValidMaximumDeltaPercentOpt: Option[Double] = None
      val isSellExAdjustment: Boolean = false

      buildRoomPriceDeltaMapV2(aValidPrices,
                               aValidApmSellInPriceDailyMap,
                               aValidApmPriceDiscountPercent,
                               aValidMaximumDeltaPercentOpt,
                               aValidRoomTypeId1,
                               isSellExAdjustment,
                               Seq.empty) should_=== (Map.empty, List(
        ApmApprovalPriceNotUseReason(1, NotUseReasonConstants.ycsCheaperThanOTA),
        ApmApprovalPriceNotUseReason(2, NotUseReasonConstants.ycsCheaperThanOTA),
        ApmApprovalPriceNotUseReason(3, NotUseReasonConstants.ycsCheaperThanOTA),
      ))
    }

    "return buildRoomPriceDeltaMapV2 correctly when aValidApmCommissionDiscountPercent = -30" in new ApmScope {
      val aValidApmPriceDiscountPercent: Double = -30
      val aValidMaximumDeltaPercentOpt: Option[Double] = None
      val isSellExAdjustment: Boolean = false

      buildRoomPriceDeltaMapV2(aValidPrices,
                               aValidApmSellInPriceDailyMap,
                               aValidApmPriceDiscountPercent,
                               aValidMaximumDeltaPercentOpt,
                               aValidRoomTypeId1,
                               isSellExAdjustment,
                               Seq.empty) should_=== (Map.empty, List(
        ApmApprovalPriceNotUseReason(1, NotUseReasonConstants.ycsCheaperThanOTA),
        ApmApprovalPriceNotUseReason(2, NotUseReasonConstants.ycsCheaperThanOTA),
        ApmApprovalPriceNotUseReason(3, NotUseReasonConstants.ycsCheaperThanOTA),
      ))
    }

    "return buildRoomPriceDeltaMapV2 correctly when aValidApmCommissionDiscountPercent = zero, aValidMaximumDeltaPercentOpt = None, isSellExAdjustment = true" in new ApmScope {
      val aValidApmPriceDiscountPercent: Double = 20
      val aValidMaximumDeltaPercentOpt: Option[Double] = None
      val isSellExAdjustment: Boolean = true

      buildRoomPriceDeltaMapV2(aValidPrices,
                               aValidApmSellInPriceDailyMap,
                               aValidApmPriceDiscountPercent,
                               aValidMaximumDeltaPercentOpt,
                               aValidRoomTypeId1,
                               isSellExAdjustment,
                               Seq.empty) should_=== (Map(
        (aValidStayDate1, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate1,
              100d).sellExclusive - (aValidApmSellInDate1 - (aValidApmSellInDate1 * aValidApmPriceDiscountPercent * 0.01)),
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate1,
              100d).sellExclusive - (aValidApmSellInDate1 - (aValidApmSellInDate1 * aValidApmPriceDiscountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellExclusive,
            marketPrice = aValidApmSellInDate1,
            priceDiscountPercent = 20d,
            maximumDeltaPercent = DEFAULT_MAX_DELTA_PERCENT,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(0),
          ),
        (aValidStayDate2, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate2,
              100d).sellExclusive - (aValidApmSellInDate2 - (aValidApmSellInDate2 * aValidApmPriceDiscountPercent * 0.01)),
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate2,
              100d).sellExclusive - (aValidApmSellInDate2 - (aValidApmSellInDate2 * aValidApmPriceDiscountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellExclusive,
            marketPrice = aValidApmSellInDate2,
            priceDiscountPercent = 20d,
            maximumDeltaPercent = DEFAULT_MAX_DELTA_PERCENT,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(1),
          ),
        (aValidStayDate4, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate3,
              100d).sellExclusive - (aValidApmSellInDate4 - (aValidApmSellInDate4 * aValidApmPriceDiscountPercent * 0.01)),
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate3,
              100d).sellExclusive - (aValidApmSellInDate4 - (aValidApmSellInDate4 * aValidApmPriceDiscountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d).sellExclusive,
            marketPrice = aValidApmSellInDate4,
            priceDiscountPercent = 20d,
            maximumDeltaPercent = DEFAULT_MAX_DELTA_PERCENT,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(2),
          ),
      ), List.empty)
    }

    "return buildRoomPriceDeltaMapV2 correctly and cheapestRoomTypeId is not matched" in new ApmScope {
      val aValidApmPriceDiscountPercent: Double = 0
      val aValidMaximumDeltaPercentOpt: Option[Double] = None
      val isSellExAdjustment: Boolean = false

      buildRoomPriceDeltaMapV2(aValidPrices,
                               aValidApmSellInPriceDailyMap,
                               aValidApmPriceDiscountPercent,
                               aValidMaximumDeltaPercentOpt,
                               aValidRoomTypeId2,
                               isSellExAdjustment,
                               Seq.empty) should_=== (Map.empty, List.empty)
    }

    "return buildRoomPriceDeltaMapV2 correctly and cheapestRoomTypeId is matched" in new ApmScope {
      val aValidApmPriceDiscountPercent: Double = 0
      val aValidMaximumDeltaPercentOpt: Option[Double] = None
      val isSellExAdjustment: Boolean = false

      buildRoomPriceDeltaMapV2(aValidPrices,
                               aValidApmSellInPriceDailyMap,
                               aValidApmPriceDiscountPercent,
                               aValidMaximumDeltaPercentOpt,
                               aValidRoomTypeId1,
                               isSellExAdjustment,
                               Seq.empty) should_=== (Map(
        (aValidStayDate1, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive - aValidApmSellInDate1,
            rawDelta = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive - aValidApmSellInDate1,
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate1,
            priceDiscountPercent = 0d,
            maximumDeltaPercent = DEFAULT_MAX_DELTA_PERCENT,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(0),
          ),
        (aValidStayDate2, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive - aValidApmSellInDate2,
            rawDelta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive - aValidApmSellInDate2,
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate2,
            priceDiscountPercent = 0d,
            maximumDeltaPercent = DEFAULT_MAX_DELTA_PERCENT,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(1),
          ),
      ), List(ApmApprovalPriceNotUseReason(3, NotUseReasonConstants.ycsCheaperThanOTA)))
    }

    "return buildRoomPriceDeltaMapV2 correctly when aValidApmCommissionDiscountPercent > zero, aValidMaximumDeltaPercentOpt = None" in new ApmScope {
      val discountPercent = 5.0d
      val aValidApmPriceDiscountPercent: Double = discountPercent
      val aValidMaximumDeltaPercentOpt: Option[Double] = None
      val isSellExAdjustment: Boolean = false

      buildRoomPriceDeltaMapV2(aValidPrices,
                               aValidApmSellInPriceDailyMap,
                               aValidApmPriceDiscountPercent,
                               aValidMaximumDeltaPercentOpt,
                               aValidRoomTypeId1,
                               isSellExAdjustment,
                               Seq.empty) should_=== (Map(
        (aValidStayDate1, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate1,
              100d).sellInclusive - (aValidApmSellInDate1 - (aValidApmSellInDate1 * discountPercent * 0.01)),
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate1,
              100d).sellInclusive - (aValidApmSellInDate1 - (aValidApmSellInDate1 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate1,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = DEFAULT_MAX_DELTA_PERCENT,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(0),
          ),
        (aValidStayDate2, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate2,
              100d).sellInclusive - (aValidApmSellInDate2 - (aValidApmSellInDate2 * discountPercent * 0.01)),
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate2,
              100d).sellInclusive - (aValidApmSellInDate2 - (aValidApmSellInDate2 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate2,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = DEFAULT_MAX_DELTA_PERCENT,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(1),
          ),
        (aValidStayDate4, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate4,
              100d).sellInclusive - (aValidApmSellInDate4 - (aValidApmSellInDate4 * discountPercent * 0.01)),
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate4,
              100d).sellInclusive - (aValidApmSellInDate4 - (aValidApmSellInDate4 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate4,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = DEFAULT_MAX_DELTA_PERCENT,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(2),
          ),
      ), List.empty)
    }

    "return buildRoomPriceDeltaMapV2 correctly when aValidApmCommissionDiscountPercent = zero, aValidMaximumDeltaPercentOpt != None" in new ApmScope {
      val aValidApmPriceDiscountPercent: Double = 0
      val aValidMaximumDeltaPercentOpt: Option[Double] = Some(10.0)
      val isSellExAdjustment: Boolean = false

      buildRoomPriceDeltaMapV2(aValidPrices,
                               aValidApmSellInPriceDailyMap,
                               aValidApmPriceDiscountPercent,
                               aValidMaximumDeltaPercentOpt,
                               aValidRoomTypeId1,
                               isSellExAdjustment,
                               Seq.empty) should_=== (Map(
        (aValidStayDate1, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive - aValidApmSellInDate1,
            rawDelta = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive - aValidApmSellInDate1,
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate1,
            priceDiscountPercent = aValidApmPriceDiscountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(0),
          ),
        (aValidStayDate2, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive * 0.1,
            rawDelta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive - aValidApmSellInDate2,
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate2,
            priceDiscountPercent = aValidApmPriceDiscountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = true,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(1),
          ),
      ), List(ApmApprovalPriceNotUseReason(3, NotUseReasonConstants.ycsCheaperThanOTA)))
    }

    "return buildRoomPriceDeltaMapV2 correctly when aValidApmCommissionDiscountPercent > zero, aValidMaximumDeltaPercentOpt != None" in new ApmScope {
      val discountPercent = 5.0d
      val aValidApmPriceDiscountPercent: Double = discountPercent
      val aValidMaximumDeltaPercentOpt: Option[Double] = Some(5.0)
      val isSellExAdjustment: Boolean = false

      buildRoomPriceDeltaMapV2(aValidPrices,
                               aValidApmSellInPriceDailyMap,
                               aValidApmPriceDiscountPercent,
                               aValidMaximumDeltaPercentOpt,
                               aValidRoomTypeId1,
                               isSellExAdjustment,
                               Seq.empty) should_=== (Map(
        (aValidStayDate1, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive * 0.05,
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate1,
              100d).sellInclusive - (aValidApmSellInDate1 - (aValidApmSellInDate1 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate1,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = true,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(0),
          ),
        (aValidStayDate2, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive * 0.05,
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate2,
              100d).sellInclusive - (aValidApmSellInDate2 - (aValidApmSellInDate2 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate2,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = true,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(1),
          ),
        (aValidStayDate4, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate4,
              100d).sellInclusive - (aValidApmSellInDate4 - (aValidApmSellInDate4 * discountPercent * 0.01)),
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate4,
              100d).sellInclusive - (aValidApmSellInDate4 - (aValidApmSellInDate4 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate4,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(2),
          ),
      ), List.empty)
    }

    "return buildRoomPriceDeltaMapV2 correctly when ApmBlackoutDate config is empty, aValidApmCommissionDiscountPercent > zero, aValidMaximumDeltaPercentOpt != None" in new ApmScope {
      val discountPercent = 5.0d
      val aValidApmPriceDiscountPercent: Double = discountPercent
      val aValidMaximumDeltaPercentOpt: Option[Double] = Some(5.0)
      val isSellExAdjustment: Boolean = false

      buildRoomPriceDeltaMapV2(aValidPrices,
                               aValidApmSellInPriceDailyMap,
                               aValidApmPriceDiscountPercent,
                               aValidMaximumDeltaPercentOpt,
                               aValidRoomTypeId1,
                               isSellExAdjustment,
                               Seq.empty) should_=== (Map(
        (aValidStayDate1, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive * 0.05,
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate1,
              100d).sellInclusive - (aValidApmSellInDate1 - (aValidApmSellInDate1 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate1,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = true,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(0),
          ),
        (aValidStayDate2, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive * 0.05,
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate2,
              100d).sellInclusive - (aValidApmSellInDate2 - (aValidApmSellInDate2 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate2,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = true,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(1),
          ),
        (aValidStayDate4, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate4,
              100d).sellInclusive - (aValidApmSellInDate4 - (aValidApmSellInDate4 * discountPercent * 0.01)),
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate4,
              100d).sellInclusive - (aValidApmSellInDate4 - (aValidApmSellInDate4 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate4,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(2),
          ),
      ), List.empty)
    }

    "return buildRoomPriceDeltaMapV2 correctly when  stay date is not in ApmBlackoutDate config, aValidApmCommissionDiscountPercent > zero, aValidMaximumDeltaPercentOpt != None" in new ApmScope {
      val discountPercent = 5.0d
      val aValidApmPriceDiscountPercent: Double = discountPercent
      val aValidMaximumDeltaPercentOpt: Option[Double] = Some(5.0)
      val isSellExAdjustment: Boolean = false

      val apmBlackoutDateList = Seq(DateTime.parse("2021-09-09").withTimeAtStartOfDay())

      buildRoomPriceDeltaMapV2(
        aValidPrices,
        aValidApmSellInPriceDailyMap,
        aValidApmPriceDiscountPercent,
        aValidMaximumDeltaPercentOpt,
        aValidRoomTypeId1,
        isSellExAdjustment,
        apmBlackoutDateList,
      ) should_=== (Map(
        (aValidStayDate1, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive * 0.05,
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate1,
              100d).sellInclusive - (aValidApmSellInDate1 - (aValidApmSellInDate1 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate1,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = true,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(0),
          ),
        (aValidStayDate2, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive * 0.05,
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate2,
              100d).sellInclusive - (aValidApmSellInDate2 - (aValidApmSellInDate2 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate2,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = true,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(1),
          ),
        (aValidStayDate4, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate4,
              100d).sellInclusive - (aValidApmSellInDate4 - (aValidApmSellInDate4 * discountPercent * 0.01)),
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate4,
              100d).sellInclusive - (aValidApmSellInDate4 - (aValidApmSellInDate4 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate4,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = false,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(2),
          ),
      ), List.empty)
    }

    "return buildRoomPriceDeltaMapV2 correctly when some stay dates are in ApmBlackoutDate config, aValidApmCommissionDiscountPercent > zero, aValidMaximumDeltaPercentOpt != None" in new ApmScope {
      val discountPercent = 5.0d
      val aValidApmPriceDiscountPercent: Double = discountPercent
      val aValidMaximumDeltaPercentOpt: Option[Double] = Some(5.0)
      val isSellExAdjustment: Boolean = false

      val apmBlackoutDateList = Seq(aValidStayDate1, aValidStayDate4)

      buildRoomPriceDeltaMapV2(
        aValidPrices,
        aValidApmSellInPriceDailyMap,
        aValidApmPriceDiscountPercent,
        aValidMaximumDeltaPercentOpt,
        aValidRoomTypeId1,
        isSellExAdjustment,
        apmBlackoutDateList,
      ) should_=== (Map(
        (aValidStayDate2, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive * 0.05,
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate2,
              100d).sellInclusive - (aValidApmSellInDate2 - (aValidApmSellInDate2 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate2,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = true,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(1),
          ),
      ), List.empty)
    }

    "return buildRoomPriceDeltaMapV2 correctly when all stay dates are in ApmBlackoutDate config, aValidApmCommissionDiscountPercent > zero, aValidMaximumDeltaPercentOpt != None" in new ApmScope {
      val discountPercent = 5.0d
      val aValidApmPriceDiscountPercent: Double = discountPercent
      val aValidMaximumDeltaPercentOpt: Option[Double] = Some(5.0)
      val isSellExAdjustment: Boolean = false

      val apmBlackoutDateList = Seq(aValidStayDate1, aValidStayDate2, aValidStayDate3, aValidStayDate4)

      buildRoomPriceDeltaMapV2(
        aValidPrices,
        aValidApmSellInPriceDailyMap,
        aValidApmPriceDiscountPercent,
        aValidMaximumDeltaPercentOpt,
        aValidRoomTypeId1,
        isSellExAdjustment,
        apmBlackoutDateList,
      ) should_=== (Map.empty, List.empty)
    }

    "return buildRoomPriceDeltaMapV2 correctly when some stay dates are in ApmBlackoutDate config, aValidApmCommissionDiscountPercent > zero, aValidMaximumDeltaPercentOpt != None" in new ApmScope {
      val discountPercent = 5.0d
      val aValidApmPriceDiscountPercent: Double = discountPercent
      val aValidMaximumDeltaPercentOpt: Option[Double] = Some(5.0)
      val isSellExAdjustment: Boolean = false

      val apmBlackoutDateList = Seq(aValidStayDate1, aValidStayDate4)

      buildRoomPriceDeltaMapV2(
        aValidPricesWithSubChargeType,
        aValidApmSellInPriceDailyMap,
        aValidApmPriceDiscountPercent,
        aValidMaximumDeltaPercentOpt,
        aValidRoomTypeId1,
        isSellExAdjustment,
        apmBlackoutDateList,
      ) should_=== (Map(
        (aValidStayDate2, ChargeType.Room) ->
          CalculatedDeltaInfo(
            delta = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive * 0.05,
            rawDelta = buildValidYPLPrice(
              ChargeType.Room,
              aValidStayDate2,
              100d).sellInclusive - (aValidApmSellInDate2 - (aValidApmSellInDate2 * discountPercent * 0.01)),
            cheapestPrice = buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).sellInclusive,
            marketPrice = aValidApmSellInDate2,
            priceDiscountPercent = discountPercent,
            maximumDeltaPercent = aValidMaximumDeltaPercentOpt.get,
            isPartialAdjustment = true,
            apmRoomTypeId = aValidRoomTypeId1,
            apmApprovalPriceId = validApprovalPriceIdPool(1),
          ),
      ), List.empty)
    }

  }

  "calculateMaximumDelta" should {
    "return calculateMaximumDeltaPercentageV2 correctly when oldLogicMaximumDeltaPercent=None and apmDeltaPercentHotelProgramGlobalLevelOnly" in new ApmScope {
      val hotelProgramId = 1
      val deltaLevel = apmDeltaPercentHotelProgramGlobalLevelOnly
      val apmDeltaPercentProgramMap = Map(hotelProgramId -> deltaLevel)
      calculateMaximumDeltaPercentageV2(apmDeltaPercentProgramMap, hotelProgramId) must_== (deltaLevel.hotelLevel)
    }

    "return calculateMaximumDeltaPercentageV2 correctly when oldLogicMaximumDeltaPercent!=None and apmDeltaPercentHotelProgramLevelOnly" in new ApmScope {
      val hotelProgramId = 1
      val deltaLevel = apmDeltaPercentHotelProgramLevelOnly
      val apmDeltaPercentProgramMap = Map(hotelProgramId -> deltaLevel)
      calculateMaximumDeltaPercentageV2(apmDeltaPercentProgramMap, hotelProgramId) must_== (deltaLevel.hotelLevel)
    }

    "return calculateMaximumDeltaPercentageV2 correctly when oldLogicMaximumDeltaPercent!=None and apmDeltaPercentHotelGlobalLevelOnly" in new ApmScope {
      val hotelProgramId = 1
      val deltaLevel = apmDeltaPercentHotelGlobalLevelOnly
      val apmDeltaPercentProgramMap = Map(hotelProgramId -> deltaLevel)
      calculateMaximumDeltaPercentageV2(apmDeltaPercentProgramMap, hotelProgramId) must_== (deltaLevel.hotelLevel)
    }

    "return calculateMaximumDeltaPercentageV2 correctly when oldLogicMaximumDeltaPercent!=None and apmDeltaPercentProgramLevelOnly" in new ApmScope {
      val hotelProgramId = 1
      val deltaLevel = apmDeltaPercentProgramLevelOnly
      val apmDeltaPercentProgramMap = Map(hotelProgramId -> deltaLevel)
      calculateMaximumDeltaPercentageV2(apmDeltaPercentProgramMap, hotelProgramId) must_== (deltaLevel.programLevel)
    }

    "return calculateMaximumDeltaPercentageV2 correctly when oldLogicMaximumDeltaPercent!=None and apmDeltaPercentProgramGlobalLevelOnly" in new ApmScope {
      val hotelProgramId = 1
      val deltaLevel = apmDeltaPercentProgramGlobalLevelOnly
      val apmDeltaPercentProgramMap = Map(hotelProgramId -> deltaLevel)
      calculateMaximumDeltaPercentageV2(apmDeltaPercentProgramMap, hotelProgramId) must_== (deltaLevel.programLevel)
    }

    "return calculateMaximumDeltaPercentageV2 correctly when oldLogicMaximumDeltaPercent!=None and apmDeltaPercentGlobalLevelOnly" in new ApmScope {
      val hotelProgramId = 1
      val deltaLevel = apmDeltaPercentGlobalLevelOnly
      val apmDeltaPercentProgramMap = Map(hotelProgramId -> deltaLevel)
      calculateMaximumDeltaPercentageV2(apmDeltaPercentProgramMap, hotelProgramId) must_== (deltaLevel.globalLevel)
    }

    "return calculateMaximumDeltaPercentageV2 correctly when oldLogicMaximumDeltaPercent!=None and apmDeltaPercentHotelEmpty" in new ApmScope {
      val hotelProgramId = 1
      val deltaLevel = apmDeltaPercentHotelEmpty
      val apmDeltaPercentProgramMap = Map(hotelProgramId -> deltaLevel)
      calculateMaximumDeltaPercentageV2(apmDeltaPercentProgramMap, hotelProgramId) must beSome(DEFAULT_MAX_DELTA_PERCENT)
    }

    "return calculateMaximumDeltaPercentageV2 correctly when oldLogicMaximumDeltaPercent!=None and ApmDeltaPercentage=Map.Empty" in new ApmScope {
      val hotelProgramId = 1
      calculateMaximumDeltaPercentageV2(Map.empty, hotelProgramId) must beSome(DEFAULT_MAX_DELTA_PERCENT)
    }
  }

  "getRoomsByRoomTypeAndOccV2" should {
    "getRoomsByRoomTypeAndOccV2 work correctly when no eligible rooms" in new ApmScope {
      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = List.empty,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = aValidApmSettingHolder,
      )

      res should_=== List.empty
    }

    "getRoomsByRoomTypeAndOccV2 work correctly" in new ApmScope {
      val apmSetting = ApmSettingHolder(List.empty[Int], List.empty[Int], List.empty, List(1))
      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = originalRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = Some(apmSetting),
      )

      val rooms = res.head.potentialApmRooms
      val cheapestRoom = res.head.cheapestRoom

      res.head.potentialApmRoomsOnOtherOcc.length should_=== 0
      res.head.potentialApmRooms.length should_=== 4
      originalRooms.filter(_._2.roomTypeId == aValidRoomTypeId1) should_=== rooms
      cheapestRoom._2.roomTypeId should_=== aValidRoomTypeId1
      cheapestRoom._2.sellIn.roundAt(2) should_=== 483.12d
    }

    "getRoomsByRoomTypeAndOccV2 work correctly - respect isSellExAdjustment correctly" in new ApmScope {
      val apmSetting: ApmSettingHolder = ApmSettingHolder(List.empty[Int], List.empty[Int], List.empty, List(3))
      val validRooms = List(
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 100, sellInclusive = 101))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 102, sellInclusive = 100))))
          .build,
      ).zipWithIndex.map(t => (t._2, t._1))

      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = Some(apmSetting),
      )

      val res2 = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = true,
        apmSettingHolder = Some(apmSetting),
      )

      res.head.cheapestRoom._2.sellIn should_=== 100d
      res2.head.cheapestRoom._2.sellIn should_=== 101d
    }

    "getRoomsByRoomTypeAndOccV2 work correctly - other occ > 0" in new ApmScope {
      val apmSetting: ApmSettingHolder = ApmSettingHolder(List.empty[Int], List.empty[Int], List.empty, List(3))
      val validRooms = List(
        aValidRoom.withRoomTypeId(aValidRoomTypeId1).withOccupancy(RoomOccupancyHolder(2, 0, 0, 0)).build,
        aValidRoom.withRoomTypeId(aValidRoomTypeId1).withOccupancy(RoomOccupancyHolder(3, 0, 0, 0)).build,
        aValidRoom.withRoomTypeId(aValidRoomTypeId1).withOccupancy(RoomOccupancyHolder(3, 1, 0, 0)).build,
        aValidRoom.withRoomTypeId(aValidRoomTypeId1).withOccupancy(RoomOccupancyHolder(3, 0, 1, 0)).build,
        aValidRoom.withRoomTypeId(aValidRoomTypeId1).withOccupancy(RoomOccupancyHolder(3, 0, 0, 1)).build,
      ).zipWithIndex.map(t => (t._2, t._1))

      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = Some(apmSetting),
      )

      res.head.potentialApmRoomsOnOtherOcc.length should_=== 1
      res.head.potentialApmRooms.length should_=== 1
      res.head.potentialApmRoomsOnOtherOcc.head._2.isMADO should_=== Some(true)
      res.head.potentialApmRooms.head._2.isMADO should_=== None
    }

    "getRoomsByRoomTypeAndOccV2 work correctly - Setting is empty" in new ApmScope {
      val validRooms = List(
        aValidRoom.withRoomTypeId(aValidRoomTypeId1).withOccupancy(RoomOccupancyHolder(2, 0, 0, 0)).build,
        aValidRoom.withRoomTypeId(aValidRoomTypeId1).withOccupancy(RoomOccupancyHolder(3, 0, 0, 0)).build,
      ).zipWithIndex.map(t => (t._2, t._1))

      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = None,
      )

      res.head.potentialApmRoomsOnOtherOcc.length should_=== 1
      res.head.potentialApmRooms.length should_=== 1
    }

    "getRoomsByRoomTypeAndOccV2 work correctly - with room occ=2" in new ApmScope {
      val validRooms = List(
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withRateCategoryId(1)
          .withIsLinkedRate(true)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 100, sellInclusive = 101))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withRateCategoryId(1)
          .withIsLinkedRate(true)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 102, sellInclusive = 100))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withRateCategoryId(2)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 100, sellInclusive = 101))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withRateCategoryId(2)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 102, sellInclusive = 100))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withRateCategoryId(3)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 100, sellInclusive = 101))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withRateCategoryId(3)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 102, sellInclusive = 100))))
          .build,
      ).zipWithIndex.map(t => (t._2, t._1))

      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = None,
      )

      val res2 = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = true,
        apmSettingHolder = None,
      )

      res.head.potentialApmRoomsOnOtherOcc.length should_=== 0
      res.head.potentialApmRooms.length should_=== 4
      res.head.cheapestRoom._2.sellIn should_=== 100d
      res.head.cheapestRoom._2.rateCategoryId === 2
      res2.head.cheapestRoom._2.sellIn should_=== 101d
      res2.head.cheapestRoom._2.rateCategoryId === 2
    }

    "getRoomsByRoomTypeAndOccV2 work correctly - without room occ=2" in new ApmScope {
      val validRooms = List(
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(4, 0, 0, 0))
          .withRateCategoryId(2)
          .withIsLinkedRate(true)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 90, sellInclusive = 91))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(4, 0, 0, 0))
          .withRateCategoryId(2)
          .withIsLinkedRate(true)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 92, sellInclusive = 90))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(1, 0, 0, 0))
          .withRateCategoryId(1)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 70, sellInclusive = 71))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(1, 0, 0, 0))
          .withRateCategoryId(1)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 72, sellInclusive = 70))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(4, 0, 0, 0))
          .withRateCategoryId(2)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 100, sellInclusive = 101))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(4, 0, 0, 0))
          .withRateCategoryId(2)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 102, sellInclusive = 100))))
          .build,
      ).zipWithIndex.map(t => (t._2, t._1))

      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = None,
      )

      val res2 = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = true,
        apmSettingHolder = None,
      )

      res.head.potentialApmRoomsOnOtherOcc.length should_=== 4
      res.head.potentialApmRooms.length should_=== 0
      res.head.cheapestRoom._2.sellIn should_=== 100d
      res.head.cheapestRoom._2.rateCategoryId === 2
      res2.head.cheapestRoom._2.sellIn should_=== 101d
      res2.head.cheapestRoom._2.rateCategoryId === 2
    }

    "getRoomsByRoomTypeAndOccV2 work correctly - with and without room occ=2" in new ApmScope {
      val validRooms = List(
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withRateCategoryId(1)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 100, sellInclusive = 101))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withRateCategoryId(1)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 102, sellInclusive = 100))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withRateCategoryId(3)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 99, sellInclusive = 102))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(2, 0, 0, 0))
          .withRateCategoryId(3)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 103, sellInclusive = 99))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(3, 0, 0, 0))
          .withRateCategoryId(2)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 90, sellInclusive = 91))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(3, 0, 0, 0))
          .withRateCategoryId(2)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 92, sellInclusive = 90))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(4, 0, 0, 0))
          .withRateCategoryId(2)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 110, sellInclusive = 111))))
          .build,
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withOccupancy(RoomOccupancyHolder(4, 0, 0, 0))
          .withRateCategoryId(2)
          .withPrices(List((0, aValidPrice.copy(sellExclusive = 112, sellInclusive = 110))))
          .build,
      ).zipWithIndex.map(t => (t._2, t._1))

      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = None,
      )

      val res2 = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = true,
        apmSettingHolder = None,
      )

      res.head.potentialApmRoomsOnOtherOcc.length should_=== 4
      res.head.potentialApmRooms.length should_=== 4
      res.head.cheapestRoom._2.sellIn should_=== 99d
      res.head.cheapestRoom._2.rateCategoryId === 3
      res2.head.cheapestRoom._2.sellIn should_=== 102d
      res2.head.cheapestRoom._2.rateCategoryId === 3
    }

    "getRoomsByRoomTypeAndOccV2 work correctly - without room occ=2, no eligible rooms" in new ApmScope {
      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = List.empty,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = true,
        apmSettingHolder = None,
      )

      res should_=== List.empty
    }

    "getRoomsByRoomTypeAndOccV2 work correctly - without room occ=2, only eligible rooms" in new ApmScope {
      val validRooms = List(
        aValidRoom.withRoomTypeId(aValidRoomTypeId1).withOccupancy(RoomOccupancyHolder(2, 0, 0, 0)).build,
        aValidRoom.withRoomTypeId(aValidRoomTypeId1).withOccupancy(RoomOccupancyHolder(2, 0, 0, 0)).build,
      ).zipWithIndex.map(t => (t._2, t._1))

      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = validRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = true,
        apmSettingHolder = None,
      )

      res.head.potentialApmRoomsOnOtherOcc.length should_=== 0
      res.head.potentialApmRooms.length should_=== 2
    }

    "getRoomsByRoomTypeAndOccV2 work correctly when no eligible rooms" in new ApmScope {
      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = List.empty,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = aValidApmSettingHolder,
      )

      res should_=== List.empty
    }

    "getRoomsByRoomTypeAndOccV2 work correctly with MAD-O" in new ApmScope {
      val apmSetting = ApmSettingHolder(List.empty[Int], List.empty[Int], List.empty, List(3))
      val otherOccRoom = originalRooms.head._2.copy(occ = aValidRoomOccupancy.copy(adults = 3))
      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = (99, otherOccRoom) :: originalRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = aValidCountryId1,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = Some(apmSetting),
      )

      val rooms = res.head.potentialApmRooms
      val cheapestRoom = res.head.cheapestRoom

      res.head.potentialApmRoomsOnOtherOcc.length should_=== 1
      res.head.potentialApmRooms.length should_=== 4
      originalRooms.filter(_._2.roomTypeId == aValidRoomTypeId1) should_=== rooms
      cheapestRoom._2.roomTypeId should_=== aValidRoomTypeId1
      cheapestRoom._2.sellIn.roundAt(2) should_=== 483.12d
    }

    "getRoomsByRoomTypeAndOccV2 work correctly with MAD-O but hotel is in country blacklist" in new ApmScope {
      val apmSetting = ApmSettingHolder(List.empty[Int], List.empty[Int], List.empty, List(3))
      val otherOccRoom = originalRooms.head._2.copy(occ = aValidRoomOccupancy.copy(adults = 3))
      val res = getRoomsByRoomTypeAndOccV2(
        eligibleRooms = (99, otherOccRoom) :: originalRooms,
        apmRoomTypeId = aValidRoomTypeId1,
        countryId = 3,
        apmOccupancy = aValidRoomOccupancy.adults,
        isSellExAdjustment = false,
        apmSettingHolder = Some(apmSetting),
      )

      val rooms = res.head.potentialApmRooms
      val cheapestRoom = res.head.cheapestRoom

      res.head.potentialApmRoomsOnOtherOcc.length should_=== 0
      res.head.potentialApmRooms.length should_=== 4
      originalRooms.filter(_._2.roomTypeId == aValidRoomTypeId1) should_=== rooms
      cheapestRoom._2.roomTypeId should_=== aValidRoomTypeId1
      cheapestRoom._2.sellIn.roundAt(2) should_=== 483.12d
    }
  }

  "processApmRooms" should {
    trait ApmScopeWithMock extends Scope with ApmPriceAdjustmentServiceImpl {
      val mockApmPriceAdjustmentServiceImpl: ApmPriceAdjustmentServiceImpl

      override private[apm] def buildApmPriceAdjustmentRoomResult(
        filteredCheapestRoomCandidateByRateCateAndPayment: Map[RoomIndex, ApmPriceAdjustmentRoomParameters],
        roomPriceDeltaMap: Map[(DateTime, ChargeType), CalculatedDeltaInfo],
        isSellExAdjustment: Boolean,
        apmPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo],
        apmRoomChannelSetting: MultipleAutoPriceMatchHolder,
        adjustmentChannelId: RoomIndex,
        roomIndexWithCheapestRoomFromRoomTypeAndOcc: (RoomIndex, ApmPriceAdjustmentRoomParameters)) =
        mockApmPriceAdjustmentServiceImpl.buildApmPriceAdjustmentRoomResult(
          filteredCheapestRoomCandidateByRateCateAndPayment,
          roomPriceDeltaMap,
          isSellExAdjustment,
          apmPriceDailyMap,
          apmRoomChannelSetting,
          adjustmentChannelId,
          roomIndexWithCheapestRoomFromRoomTypeAndOcc,
        )

    }

    "return correctly" in new ApmScopeWithMock {
      override val mockApmPriceAdjustmentServiceImpl = mock[ApmPriceAdjustmentServiceImpl]
      mockApmPriceAdjustmentServiceImpl.buildApmPriceAdjustmentRoomResult(any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any()) returns Map.empty

      val res = processApmRooms(
        aValidApmCommissionHolder.build,
        aValidApmSellInPriceDailyMap,
        originalRooms,
        originalRooms.head,
        aValidMultipleAutoPriceMatchHolder.build,
        false,
        1,
        false,
        DateTime.now(),
        aValidHotelId,
        false,
      )
      res should_== (Map.empty, List.empty)
      verify(mockApmPriceAdjustmentServiceImpl, times(1)).buildApmPriceAdjustmentRoomResult(any(),
                                                                                            any(),
                                                                                            any(),
                                                                                            any(),
                                                                                            any(),
                                                                                            any(),
                                                                                            any())
    }

    "return correctly - isSellExAdjustment = false" in new ApmScopeWithMock {

      val room1 = baseRoom
        .withPrices(List(buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 101d)).zipWithIndex.map { case (f, s) =>
          (s, f)
        })
        .build
      val room2 = baseRoom
        .withPrices(
          List(buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).copy(sellInclusive = 150)).zipWithIndex.map {
            case (f, s) => (s, f)
          })
        .build

      override val mockApmPriceAdjustmentServiceImpl = mock[ApmPriceAdjustmentServiceImpl]
      mockApmPriceAdjustmentServiceImpl.buildApmPriceAdjustmentRoomResult(any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any()) returns Map.empty

      val res = processApmRooms(
        aValidApmCommissionHolder.build,
        aValidApmSellInPriceDailyMap,
        List((0, room1), (1, room2)),
        originalRooms.head,
        aValidMultipleAutoPriceMatchHolder.build,
        false,
        1,
        false,
        DateTime.now(),
        aValidHotelId,
        false,
      )
      res should_== (Map.empty, List.empty)
      verify(mockApmPriceAdjustmentServiceImpl, times(1)).buildApmPriceAdjustmentRoomResult(
        ArgumentMatchers.eq(Map((0 -> room1))),
        any(),
        any(),
        any(),
        any(),
        any(),
        any())
    }

    "return correctly - isSellExAdjustment = true" in new ApmScopeWithMock {
      override def buildRoomPriceDeltaMapV2(prices: Seq[ApmPriceAdjustmentPriceParameters],
                                            apmPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo],
                                            apmPriceDiscountPercent: Double,
                                            maximumDeltaPercentOpt: Option[Double],
                                            cheapestRoomTypeId: Long,
                                            isSellExAdjustment: Boolean,
                                            apmBlackoutDateList: Seq[DateTime])
        : (Map[(DateTime, ChargeType), CalculatedDeltaInfo], List[ApmApprovalPriceNotUseReason]) =
        (Map(
           ((aValidStayDate1, ChargeType.Room) -> CalculatedDeltaInfo(30, 30, 115, 0, 0, 0, false, aValidRoomTypeId1, 1))),
         List.empty)

      val room1 = baseRoom
        .withPrices(
          List(buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).copy(sellExclusive = 150)).zipWithIndex.map {
            case (f, s) => (s, f)
          })
        .build
      val room2 = baseRoom
        .withPrices(List(buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 101d)).zipWithIndex.map { case (f, s) =>
          (s, f)
        })
        .build

      override val mockApmPriceAdjustmentServiceImpl = mock[ApmPriceAdjustmentServiceImpl]
      mockApmPriceAdjustmentServiceImpl.buildApmPriceAdjustmentRoomResult(any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any()) returns Map.empty

      val res = processApmRooms(
        aValidApmCommissionHolder.build,
        aValidApmSellInPriceDailyMap,
        List((0, room1), (1, room2)),
        (1, room2),
        aValidMultipleAutoPriceMatchHolder.build,
        true,
        1,
        false,
        DateTime.now(),
        aValidHotelId,
        false,
      )
      res should_== (Map.empty, List.empty)
      verify(mockApmPriceAdjustmentServiceImpl, times(1)).buildApmPriceAdjustmentRoomResult(
        ArgumentMatchers.eq(Map((1 -> room2))),
        any(),
        any(),
        any(),
        any(),
        any(),
        any())
    }

    "return correctly - enableCalcCapPerRoom = true" in new ApmScopeWithMock {
      override def buildRoomPriceDeltaMapV2(prices: Seq[ApmPriceAdjustmentPriceParameters],
                                            apmPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo],
                                            apmPriceDiscountPercent: Double,
                                            maximumDeltaPercentOpt: Option[Double],
                                            cheapestRoomTypeId: Long,
                                            isSellExAdjustment: Boolean,
                                            apmBlackoutDateList: Seq[DateTime])
        : (Map[(DateTime, ChargeType), CalculatedDeltaInfo], List[ApmApprovalPriceNotUseReason]) =
        (Map((aValidStayDate1, ChargeType.Room) -> CalculatedDeltaInfo(30, 50, 115, 0, 0, 0, true, aValidRoomTypeId1, 1)),
         List.empty)

      val room1 = baseRoom
        .withPrices(
          List(buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 40d).copy(sellInclusive = 45)).zipWithIndex.map {
            case (f, s) => (s, f)
          })
        .build
      val room2 = baseRoom
        .withPrices(List(buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 40d)).zipWithIndex.map { case (f, s) =>
          (s, f)
        })
        .build

      override val mockApmPriceAdjustmentServiceImpl = mock[ApmPriceAdjustmentServiceImpl]
      mockApmPriceAdjustmentServiceImpl.buildApmPriceAdjustmentRoomResult(any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any()) returns Map.empty

      val res = processApmRooms(
        aValidApmCommissionHolder.build,
        aValidApmSellInPriceDailyMap,
        List((0, room1), (1, room2)),
        (1, room2),
        aValidMultipleAutoPriceMatchHolder.build,
        false,
        1,
        false,
        DateTime.now(),
        aValidHotelId,
        false,
      )
      res should_== (Map.empty, List.empty)
      verify(mockApmPriceAdjustmentServiceImpl, times(1)).buildApmPriceAdjustmentRoomResult(
        ArgumentMatchers.eq(Map.empty),
        any(),
        any(),
        any(),
        any(),
        any(),
        any())
    }

    "call buildApmPriceAdjustmentRoomResult correctly" in new ApmScopeWithMock {
      override def buildRoomPriceDeltaMapV2(prices: Seq[ApmPriceAdjustmentPriceParameters],
                                            apmPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo],
                                            apmPriceDiscountPercent: Double,
                                            maximumDeltaPercentOpt: Option[Double],
                                            cheapestRoomTypeId: Long,
                                            isSellExAdjustment: Boolean,
                                            apmBlackoutDateList: Seq[DateTime])
        : (Map[(DateTime, ChargeType), CalculatedDeltaInfo], List[ApmApprovalPriceNotUseReason]) =
        (Map.empty, List.empty)
      //        Map((aValidStayDate1, ChargeType.Room) -> CalculatedDeltaInfo(30, 115, 0,0,0, false, aValidRoomTypeId1, 1))

      val room1 = baseRoom
        .withPrices(List(
          buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).copy(sellExclusive = 150),
          buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).copy(sellExclusive = 150),
        ).zipWithIndex.map { case (f, s) => (s, f) })
        .build

      override val mockApmPriceAdjustmentServiceImpl = mock[ApmPriceAdjustmentServiceImpl]
      mockApmPriceAdjustmentServiceImpl.buildApmPriceAdjustmentRoomResult(any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any()) returns Map.empty

      val res = processApmRooms(
        aValidApmCommissionHolder.build,
        aValidApmSellInPriceDailyMap,
        List((0, room1)),
        (1, room1),
        aValidMultipleAutoPriceMatchHolder.build,
        true,
        1,
        false,
        DateTime.now(),
        aValidHotelId,
        false,
      )
      res should_== (Map.empty, List.empty)
      verify(mockApmPriceAdjustmentServiceImpl, times(1)).buildApmPriceAdjustmentRoomResult(
        ArgumentMatchers.eq(Map.empty),
        any(),
        any(),
        any(),
        any(),
        any(),
        any())
    }

    "call buildApmPriceAdjustmentRoomResult correctly - sellExAdjustment true" in new ApmScopeWithMock {
      override def buildRoomPriceDeltaMapV2(prices: Seq[ApmPriceAdjustmentPriceParameters],
                                            apmPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo],
                                            apmPriceDiscountPercent: Double,
                                            maximumDeltaPercentOpt: Option[Double],
                                            cheapestRoomTypeId: Long,
                                            isSellExAdjustment: Boolean,
                                            apmBlackoutDateList: Seq[DateTime])
        : (Map[(DateTime, ChargeType), CalculatedDeltaInfo], List[ApmApprovalPriceNotUseReason]) =
        (Map(
           (aValidStayDate1, ChargeType.Room) -> CalculatedDeltaInfo(145, 145, 115, 0, 0, 0, false, aValidRoomTypeId1, 1)),
         List.empty)

      val room1 = baseRoom
        .withPrices(
          List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).copy(sellExclusive = 150, sellInclusive = 140),
          ).zipWithIndex.map { case (f, s) => (s, f) })
        .build

      override val mockApmPriceAdjustmentServiceImpl = mock[ApmPriceAdjustmentServiceImpl]
      mockApmPriceAdjustmentServiceImpl.buildApmPriceAdjustmentRoomResult(any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any()) returns Map.empty

      val res = processApmRooms(
        aValidApmCommissionHolder.build,
        aValidApmSellInPriceDailyMap,
        List((0, room1)),
        (1, room1),
        aValidMultipleAutoPriceMatchHolder.build,
        true,
        1,
        false,
        DateTime.now(),
        aValidHotelId,
        false,
      )
      res should_== (Map.empty, List.empty)
      verify(mockApmPriceAdjustmentServiceImpl, times(1)).buildApmPriceAdjustmentRoomResult(
        ArgumentMatchers.eq(Map(0 -> room1)),
        any(),
        any(),
        any(),
        any(),
        any(),
        any())
    }

    "call buildApmPriceAdjustmentRoomResult correctly - sellExAdjustment false" in new ApmScopeWithMock {
      override def buildRoomPriceDeltaMapV2(prices: Seq[ApmPriceAdjustmentPriceParameters],
                                            apmPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo],
                                            apmPriceDiscountPercent: Double,
                                            maximumDeltaPercentOpt: Option[Double],
                                            cheapestRoomTypeId: Long,
                                            isSellExAdjustment: Boolean,
                                            apmBlackoutDateList: Seq[DateTime])
        : (Map[(DateTime, ChargeType), CalculatedDeltaInfo], List[ApmApprovalPriceNotUseReason]) =
        (Map(
           (aValidStayDate1, ChargeType.Room) -> CalculatedDeltaInfo(145, 145, 115, 0, 0, 0, false, aValidRoomTypeId1, 1)),
         List.empty)

      val room1 = baseRoom
        .withPrices(
          List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).copy(sellExclusive = 150, sellInclusive = 140),
          ).zipWithIndex.map { case (f, s) => (s, f) })
        .build

      override val mockApmPriceAdjustmentServiceImpl = mock[ApmPriceAdjustmentServiceImpl]
      mockApmPriceAdjustmentServiceImpl.buildApmPriceAdjustmentRoomResult(any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any()) returns Map.empty

      val res = processApmRooms(
        aValidApmCommissionHolder.build,
        aValidApmSellInPriceDailyMap,
        List((0, room1)),
        (1, room1),
        aValidMultipleAutoPriceMatchHolder.build,
        false,
        1,
        false,
        DateTime.now(),
        aValidHotelId,
        false,
      )
      res should_== (Map.empty, List.empty)
      verify(mockApmPriceAdjustmentServiceImpl, times(1)).buildApmPriceAdjustmentRoomResult(
        ArgumentMatchers.eq(Map.empty),
        any(),
        any(),
        any(),
        any(),
        any(),
        any())
    }

    "call buildApmPriceAdjustmentRoomResult correctly - sellInclusive is equal to delta" in new ApmScopeWithMock {
      override def buildRoomPriceDeltaMapV2(prices: Seq[ApmPriceAdjustmentPriceParameters],
                                            apmPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo],
                                            apmPriceDiscountPercent: Double,
                                            maximumDeltaPercentOpt: Option[Double],
                                            cheapestRoomTypeId: Long,
                                            isSellExAdjustment: Boolean,
                                            apmBlackoutDateList: Seq[DateTime])
        : (Map[(DateTime, ChargeType), CalculatedDeltaInfo], List[ApmApprovalPriceNotUseReason]) =
        (Map(
           (aValidStayDate1, ChargeType.Room) -> CalculatedDeltaInfo(140, 140, 115, 0, 0, 0, false, aValidRoomTypeId1, 1)),
         List.empty)

      val room1 = baseRoom
        .withPrices(
          List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).copy(sellExclusive = 150, sellInclusive = 140),
          ).zipWithIndex.map { case (f, s) => (s, f) })
        .build

      override val mockApmPriceAdjustmentServiceImpl = mock[ApmPriceAdjustmentServiceImpl]
      mockApmPriceAdjustmentServiceImpl.buildApmPriceAdjustmentRoomResult(any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any()) returns Map.empty

      val res = processApmRooms(
        aValidApmCommissionHolder.build,
        aValidApmSellInPriceDailyMap,
        List((0, room1)),
        (1, room1),
        aValidMultipleAutoPriceMatchHolder.build,
        false,
        1,
        false,
        DateTime.now(),
        aValidHotelId,
        false,
      )
      res should_== (Map.empty, List.empty)
      verify(mockApmPriceAdjustmentServiceImpl, times(1)).buildApmPriceAdjustmentRoomResult(
        ArgumentMatchers.eq(Map.empty),
        any(),
        any(),
        any(),
        any(),
        any(),
        any())
    }

    "call buildApmPriceAdjustmentRoomResult correctly - some price not exist in deltaMap" in new ApmScopeWithMock {
      override def buildRoomPriceDeltaMapV2(prices: Seq[ApmPriceAdjustmentPriceParameters],
                                            apmPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo],
                                            apmPriceDiscountPercent: Double,
                                            maximumDeltaPercentOpt: Option[Double],
                                            cheapestRoomTypeId: Long,
                                            isSellExAdjustment: Boolean,
                                            apmBlackoutDateList: Seq[DateTime])
        : (Map[(DateTime, ChargeType), CalculatedDeltaInfo], List[ApmApprovalPriceNotUseReason]) =
        (Map((aValidStayDate1, ChargeType.Room) -> CalculatedDeltaInfo(80, 80, 115, 0, 0, 0, false, aValidRoomTypeId1, 1)),
         List.empty)

      val room1 = baseRoom
        .withPrices(List(
          buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d).copy(sellExclusive = 150, sellInclusive = 140),
          buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d).copy(sellExclusive = 150, sellInclusive = 140),
        ).zipWithIndex.map { case (f, s) => (s, f) })
        .build

      override val mockApmPriceAdjustmentServiceImpl = mock[ApmPriceAdjustmentServiceImpl]
      mockApmPriceAdjustmentServiceImpl.buildApmPriceAdjustmentRoomResult(any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any(),
                                                                          any()) returns Map.empty

      val res = processApmRooms(
        aValidApmCommissionHolder.build,
        aValidApmSellInPriceDailyMap,
        List((0, room1)),
        (1, room1),
        aValidMultipleAutoPriceMatchHolder.build,
        false,
        1,
        false,
        DateTime.now(),
        aValidHotelId,
        false,
      )
      res should_== (Map.empty, List.empty)
      verify(mockApmPriceAdjustmentServiceImpl, times(1)).buildApmPriceAdjustmentRoomResult(
        ArgumentMatchers.eq(Map(0 -> room1)),
        any(),
        any(),
        any(),
        any(),
        any(),
        any())
    }

  }

  "buildApmPriceAdjustmentRoomResult" should {
    "build result correctly - all empty " in new ApmScope {
      buildApmPriceAdjustmentRoomResult(
        Map.empty,
        Map.empty,
        false,
        Map.empty,
        aValidMultipleAutoPriceMatchHolder.build,
        0,
        (0, aValidRoom.build),
      ) should_== Map.empty
    }

    "build result correctly - pass all condition" in new ApmScope {
      val room1 = baseRoom
        .withPrices(List((0, aValidPrice.copy(date = aValidStayDate1, chargeType = ChargeType.Room, sellExclusive = 100d))))
        .build
      val deltaInfo = CalculatedDeltaInfo(50, 50, 0, 0, 0, 50, false, -1, -1)
      val priceDelta: Map[(DateTime, ChargeType), CalculatedDeltaInfo] =
        Map((aValidStayDate1, ChargeType.Room) -> deltaInfo)
      buildApmPriceAdjustmentRoomResult(
        Map(0 -> room1),
        priceDelta,
        true,
        Map.empty,
        aValidMultipleAutoPriceMatchHolder.build,
        0,
        (0, aValidRoom.build),
      ) should_== Map(
        0 -> ApmPriceAdjustmentRoomResult(
          aValidFences,
          List(0 -> ApmPriceAdjustmentPriceResult(50, 100d, deltaInfo, RateType.SellExclusive)),
          aValidMultipleAutoPriceMatchHolder.build,
          0,
          (0, "uid"),
          true,
          priceDelta,
          Map.empty,
          None,
        ),
      )
    }

    "build result correctly - price not eligible for apm" in new ApmScope {
      val room1 = baseRoom
        .withPrices(
          List(
            (0,
             aValidPrice.copy(date = aValidStayDate1,
                              chargeType = ChargeType.Room,
                              subChargeType = SubChargeType.Baby,
                              sellExclusive = 100d))))
        .build
      val deltaInfo = CalculatedDeltaInfo(50, 50, 0, 0, 0, 0, false, -1, -1)
      val priceDelta: Map[(DateTime, ChargeType), CalculatedDeltaInfo] =
        Map((aValidStayDate1, ChargeType.Room) -> deltaInfo)
      buildApmPriceAdjustmentRoomResult(
        Map(0 -> room1),
        priceDelta,
        true,
        Map.empty,
        aValidMultipleAutoPriceMatchHolder.build,
        0,
        (0, aValidRoom.build),
      ) should_== Map(
        0 -> ApmPriceAdjustmentRoomResult(aValidFences,
                                          List.empty,
                                          aValidMultipleAutoPriceMatchHolder.build,
                                          0,
                                          (0, "uid"),
                                          true,
                                          priceDelta,
                                          Map.empty,
                                          None),
      )
    }

    "build result correctly - 0 final price" in new ApmScope {
      val room1 = baseRoom
        .withPrices(List((0, aValidPrice.copy(date = aValidStayDate1, chargeType = ChargeType.Room, sellExclusive = 100d))))
        .build
      val deltaInfo = CalculatedDeltaInfo(100, 100, 0, 0, 0, 100, false, -1, -1)
      val priceDelta: Map[(DateTime, ChargeType), CalculatedDeltaInfo] =
        Map((aValidStayDate1, ChargeType.Room) -> deltaInfo)
      buildApmPriceAdjustmentRoomResult(
        Map(0 -> room1),
        priceDelta,
        true,
        Map.empty,
        aValidMultipleAutoPriceMatchHolder.build,
        0,
        (0, aValidRoom.build),
      ) should_== Map(
        0 -> ApmPriceAdjustmentRoomResult(aValidFences,
                                          List.empty,
                                          aValidMultipleAutoPriceMatchHolder.build,
                                          0,
                                          (0, "uid"),
                                          true,
                                          priceDelta,
                                          Map.empty,
                                          None),
      )
    }

    "build result correctly - more than 0 final price" in new ApmScope {
      val room1 = baseRoom
        .withPrices(
          List(
            (0,
             aValidPrice
               .copy(date = aValidStayDate1, chargeType = ChargeType.Room, sellExclusive = 101d, sellInclusive = 100d))))
        .build
      val deltaInfo = CalculatedDeltaInfo(100, 100, 0, 0, 0, 100, false, -1, -1)
      val priceDelta: Map[(DateTime, ChargeType), CalculatedDeltaInfo] =
        Map((aValidStayDate1, ChargeType.Room) -> deltaInfo)
      buildApmPriceAdjustmentRoomResult(
        Map(0 -> room1),
        priceDelta,
        true,
        Map.empty,
        aValidMultipleAutoPriceMatchHolder.build,
        0,
        (0, aValidRoom.build),
      ) should_== Map(
        0 -> ApmPriceAdjustmentRoomResult(
          aValidFences,
          List(0 -> ApmPriceAdjustmentPriceResult(1, 101d, deltaInfo, RateType.SellExclusive)),
          aValidMultipleAutoPriceMatchHolder.build,
          0,
          (0, "uid"),
          true,
          priceDelta,
          Map.empty,
          None,
        ),
      )
    }

    "build result correctly - some price date not exist in delta map" in new ApmScope {
      val room1 = baseRoom
        .withPrices(
          List(
            (0,
             aValidPrice
               .copy(date = aValidStayDate1, chargeType = ChargeType.Room, sellExclusive = 101d, sellInclusive = 100d))))
        .build
      val deltaInfo = CalculatedDeltaInfo(100, 100, 0, 0, 0, 100, false, -1, -1)
      val priceDelta: Map[(DateTime, ChargeType), CalculatedDeltaInfo] =
        Map((aValidStayDate1, ChargeType.Room) -> deltaInfo)
      buildApmPriceAdjustmentRoomResult(
        Map(0 -> room1),
        priceDelta,
        true,
        Map.empty,
        aValidMultipleAutoPriceMatchHolder.build,
        0,
        (0, aValidRoom.build),
      ) should_== Map(
        0 -> ApmPriceAdjustmentRoomResult(
          aValidFences,
          List(0 -> ApmPriceAdjustmentPriceResult(1, 101d, deltaInfo, RateType.SellExclusive)),
          aValidMultipleAutoPriceMatchHolder.build,
          0,
          (0, "uid"),
          true,
          priceDelta,
          Map.empty,
          None,
        ),
      )
    }

    "build result correctly - calculate cap per room price" in new ApmScope {
      val room1 = baseRoom
        .withPrices(
          List(
            (0,
             aValidPrice
               .copy(date = aValidStayDate1, chargeType = ChargeType.Room, sellExclusive = 250d, sellInclusive = 300d))))
        .build
      val deltaInfo = CalculatedDeltaInfo(60, 100, 200, 100, 0, 30, true, 1, 1)
      val priceDelta: Map[(DateTime, ChargeType), CalculatedDeltaInfo] =
        Map((aValidStayDate1, ChargeType.Room) -> deltaInfo)
      val roomPrice: Map[DateTime, AutoPriceMatchPriceInfo] = Map(aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1))
      buildApmPriceAdjustmentRoomResult(
        Map(0 -> room1),
        priceDelta,
        false,
        roomPrice,
        aValidMultipleAutoPriceMatchHolder.build,
        0,
        (0, aValidRoom.build),
      ) should_== Map(
        0 -> ApmPriceAdjustmentRoomResult(
          aValidFences,
          List(0 -> ApmPriceAdjustmentPriceResult(210, 300d, deltaInfo.copy(delta = 90), RateType.SellInclusive)),
          aValidMultipleAutoPriceMatchHolder.build,
          0,
          (0, "uid"),
          false,
          priceDelta,
          roomPrice,
          None,
        ),
      )
    }
  }

  "calculateApmPriceAdjustmentResult" should {
    def apmPriceAdjustmentServiceImplDefaultMock(
      validateEligibleRoom: (Boolean, List[Boolean]) = (true, List()),
      determineApmProgramSettings: (Seq[MultipleAutoPriceMatchHolder], Option[MultipleAutoPriceMatchHolder]) =
        (Seq.empty[MultipleAutoPriceMatchHolder], None),
      applyApmV2: (List[(RoomIndex, ApmPriceAdjustmentRoomResult)], List[ApmApprovalPriceNotUseReason]) =
        (List.empty, List.empty)) = {
      val apmPriceAdjustmentServiceImplDefaultMock = mock[ApmPriceAdjustmentServiceImpl]
      apmPriceAdjustmentServiceImplDefaultMock.validateEligibleRoom(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()) returns (validateEligibleRoom._1, validateEligibleRoom._2: _*)
      apmPriceAdjustmentServiceImplDefaultMock.getApmRoomChannelSettings(any(),
                                                                         any(),
                                                                         any()) returns determineApmProgramSettings._1
      apmPriceAdjustmentServiceImplDefaultMock.getApmCommissionDiscountChannelSetting(
        any(),
        any(),
        any(),
        any()) returns determineApmProgramSettings._2
      apmPriceAdjustmentServiceImplDefaultMock.applyApmV2(any(),
                                                          any(),
                                                          any(),
                                                          any(),
                                                          any(),
                                                          any(),
                                                          any(),
                                                          any(),
                                                          any(),
                                                          any(),
                                                          any(),
                                                          any()) returns applyApmV2
      apmPriceAdjustmentServiceImplDefaultMock
    }
    trait ApmScopeWithMock extends Scope with ApmPriceAdjustmentServiceImpl {
      val apmPriceAdjustmentServiceImplMock: ApmPriceAdjustmentServiceImpl

      override def applyApmV2(
        hotelId: Long,
        countryId: Long,
        apmCommissionHolder: ApmCommissionHolder,
        eligibleRooms: List[(RoomIndex, ApmPriceAdjustmentRoomParameters)],
        apmRoomChannelSetting: Seq[MultipleAutoPriceMatchHolder],
        keyEntry: AutoPriceMatchKeyEntry,
        enableApmMultipleDiscount: Boolean,
        apmRateChannelId: RoomIndex,
        apmPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo],
        apmSettingHolder: Option[ApmSettingHolder],
        bookingDateTime: DateTime,
        isApmFixEndDate: Boolean): (List[(RoomIndex, ApmPriceAdjustmentRoomResult)], List[ApmApprovalPriceNotUseReason]) =
        apmPriceAdjustmentServiceImplMock.applyApmV2(
          hotelId,
          countryId,
          apmCommissionHolder,
          eligibleRooms,
          apmRoomChannelSetting,
          keyEntry,
          enableApmMultipleDiscount,
          apmRateChannelId,
          apmPriceDailyMap,
          apmSettingHolder,
          bookingDateTime,
          isApmFixEndDate,
        )

      override def validateEligibleRoom[T <: ApmRoomParameters](apmCommissionRoomParameters: T,
                                                                apmSetting: Option[ApmSettingHolder],
                                                                apmConfigs: Map[Int, ApmConfigHolder],
                                                                hotelCountryId: Long,
                                                                isExcludeQuarantineChannel: Boolean,
                                                                apmProgramId: Int,
                                                                apmProgramType: Int,
                                                                skipPackageValidateCommReductionFeatureExp: Boolean,
                                                                removeBedPaidExclusionFromApmExp: Boolean,
                                                                skipApmPriceAdjustmentForResellExp: Boolean,
                                                                enableAiExp: Boolean,
                                                                excludeRateChannelFromApmExp: Boolean,
                                                                enabledApmArpPlusProgram: Boolean): Boolean =
        apmPriceAdjustmentServiceImplMock.validateEligibleRoom(
          apmCommissionRoomParameters,
          apmSetting,
          Map.empty,
          hotelCountryId,
          isExcludeQuarantineChannel,
          apmProgramId,
          0, // apmProgramType
          skipPackageValidateCommReductionFeatureExp,
          removeBedPaidExclusionFromApmExp,
          skipApmPriceAdjustmentForResellExp,
          enableAiExp,
          excludeRateChannelFromApmExp,
          enabledApmArpPlusProgram,
        )

      override def getApmRoomChannelSettings(multipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder],
                                             bookingDate: DateTime,
                                             isApmFixDate: Boolean): Seq[MultipleAutoPriceMatchHolder] =
        apmPriceAdjustmentServiceImplMock.getApmRoomChannelSettings(multipleAutoPriceMatch, bookingDate, isApmFixDate)

      override def getApmCommissionDiscountChannelSetting(
        dispatchChannels: Set[RoomIndex],
        multipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder],
        bookingDate: DateTime,
        isApmFixDate: Boolean): Option[MultipleAutoPriceMatchHolder] = apmPriceAdjustmentServiceImplMock
        .getApmCommissionDiscountChannelSetting(dispatchChannels, multipleAutoPriceMatch, bookingDate, isApmFixDate)
    }

    val aValidRoomResult = ApmPriceAdjustmentRoomResult(
      aValidFences,
      List(
        (0,
         ApmPriceAdjustmentPriceResult(0, 0, CalculatedDeltaInfo(0, 0, 0, 0, 0, 0, false, 0, 0), RateType.SellInclusive))),
      aValidMultipleAutoPriceMatchHolder.build,
      1,
      (1, "uid"),
      false,
      Map.empty,
      Map.empty,
      None,
    )

    "call validate eligible room correctly" in new ApmScopeWithMock {
      override val apmPriceAdjustmentServiceImplMock: ApmPriceAdjustmentServiceImpl =
        apmPriceAdjustmentServiceImplDefaultMock()

      calculateApmPriceAdjustmentResult(
        List((1, baseRoom.build)),
        1,
        1,
        1,
        Set.empty,
        DateTime.parse("2022-02-02"),
        false,
        false,
        false,
        false,
        false,
        false,
        false, // enabledApmArpPlusProgram
        Map.empty,
      )
      verify(apmPriceAdjustmentServiceImplMock, times(1)).validateEligibleRoom(any(),
                                                                               any(),
                                                                               any(),
                                                                               any(),
                                                                               ArgumentMatchers.eq(true),
                                                                               any(),
                                                                               any(),
                                                                               ArgumentMatchers.eq(false),
                                                                               any(),
                                                                               any(),
                                                                               any(),
                                                                               any(),
                                                                               any())
    }

    "return some result" in new ApmScopeWithMock {
      override val apmPriceAdjustmentServiceImplMock: ApmPriceAdjustmentServiceImpl =
        apmPriceAdjustmentServiceImplDefaultMock(
          validateEligibleRoom = (true, List(false)),
          determineApmProgramSettings = (Seq(aValidMultipleAutoPriceMatchHolder.build), None),
          applyApmV2 = (List(
                          (1,
                           aValidRoomResult.copy(apmPriceDailyMap =
                             Map(aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1, Some(1)))))),
                        List.empty),
        )

      val res = calculateApmPriceAdjustmentResult(
        List(
          (1,
           baseRoom
             .withApmCommissionHolder(
               aValidApmCommissionHolder
                 .withMultipleAutoPriceMatchHolder(List(aValidMultipleAutoPriceMatchHolder.build))
                 .withAutoPriceMatchInfo(Map(AutoPriceMatchKeyEntry(1, 1, Some(1)) -> Map(
                   aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1, Some(1)))))
                 .build,
             )
             .build),
          (2,
           baseRoom
             .withApmCommissionHolder(
               aValidApmCommissionHolder
                 .withMultipleAutoPriceMatchHolder(List(aValidMultipleAutoPriceMatchHolder.build))
                 .withAutoPriceMatchInfo(Map(AutoPriceMatchKeyEntry(1, 1, Some(1)) -> Map(
                   aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1, Some(1)))))
                 .build,
             )
             .build),
        ),
        1,
        332,
        1,
        Set(1),
        DateTime.parse("2022-02-02"),
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        Map(1 -> aValidFences),
      )
      res should_== (List(
        1 -> aValidRoomResult.copy(apmPriceDailyMap =
          Map(aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1, Some(1))))), List.empty)
    }

    "return some result and not throwing exception" in new ApmScopeWithMock {
      override val apmPriceAdjustmentServiceImplMock: ApmPriceAdjustmentServiceImpl =
        apmPriceAdjustmentServiceImplDefaultMock(
          validateEligibleRoom = (true, List(false)),
          determineApmProgramSettings = (Seq(aValidMultipleAutoPriceMatchHolder.build), None),
          applyApmV2 =
            (List((1, aValidRoomResult)), List(ApmApprovalPriceNotUseReason(1, NotUseReasonConstants.ycsCheaperThanOTA))),
        )

      val res = calculateApmPriceAdjustmentResult(
        List(
          (1,
           baseRoom
             .withApmCommissionHolder(
               aValidApmCommissionHolder
                 .withMultipleAutoPriceMatchHolder(List(aValidMultipleAutoPriceMatchHolder.build))
                 .withAutoPriceMatchInfo(Map(
                   AutoPriceMatchKeyEntry(1, 1, None) -> Map(aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1, None),
                                                             aValidStayDate2 -> AutoPriceMatchPriceInfo(100, 1, 2, None)),
                   AutoPriceMatchKeyEntry(2, 1, Some(2)) -> Map(
                     aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 2, 3, Some(2))),
                 ))
                 .build,
             )
             .build),
          (2,
           baseRoom
             .withApmCommissionHolder(
               aValidApmCommissionHolder
                 .withMultipleAutoPriceMatchHolder(List(aValidMultipleAutoPriceMatchHolder.build))
                 .withAutoPriceMatchInfo(Map(AutoPriceMatchKeyEntry(1, 1, None) -> Map(
                   aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1, None),
                   aValidStayDate2 -> AutoPriceMatchPriceInfo(100, 1, 2, Some(2)))))
                 .build,
             )
             .build),
        ),
        1,
        332,
        1,
        Set(1),
        DateTime.parse("2022-02-02"),
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        Map(1 -> aValidFences),
      )
      res should_== (List(1 -> aValidRoomResult), List(
        ApmApprovalPriceNotUseReason(3, NotUseReasonConstants.notMatchInDispatchChannels),
        ApmApprovalPriceNotUseReason(1, NotUseReasonConstants.ycsCheaperThanOTA),
        ApmApprovalPriceNotUseReason(2, NotUseReasonConstants.other),
      ))
    }

    "empty if apm settings is empty" in new ApmScopeWithMock {
      override val apmPriceAdjustmentServiceImplMock: ApmPriceAdjustmentServiceImpl =
        apmPriceAdjustmentServiceImplDefaultMock(
          determineApmProgramSettings = (Seq(), None),
          applyApmV2 = (List((1, aValidRoomResult)), List.empty),
        )

      val res = calculateApmPriceAdjustmentResult(
        List(
          (1,
           baseRoom
             .withApmCommissionHolder(
               aValidApmCommissionHolder
                 .withMultipleAutoPriceMatchHolder(List(aValidMultipleAutoPriceMatchHolder.build))
                 .withAutoPriceMatchInfo(Map(AutoPriceMatchKeyEntry(1, 1, Some(1)) -> Map(
                   aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1, Some(1)))))
                 .build,
             )
             .build)),
        1,
        332,
        1,
        Set.empty,
        DateTime.parse("2022-02-02"),
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        Map.empty,
      )
      res should_== (List(), List())
    }

    "return empty if hotel not eligible" in new ApmScopeWithMock {
      override val apmPriceAdjustmentServiceImplMock: ApmPriceAdjustmentServiceImpl =
        apmPriceAdjustmentServiceImplDefaultMock(
          determineApmProgramSettings = (Seq(), None),
          applyApmV2 = (List((1, aValidRoomResult)), List.empty),
        )

      val res = calculateApmPriceAdjustmentResult(
        List(
          (1,
           baseRoom
             .withApmCommissionHolder(
               aValidApmCommissionHolder
                 .withMultipleAutoPriceMatchHolder(List(aValidMultipleAutoPriceMatchHolder.build))
                 .withAutoPriceMatchInfo(Map(AutoPriceMatchKeyEntry(1, 1, Some(1)) -> Map(
                   aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1, Some(1)))))
                 .build,
             )
             .build)),
        1,
        332,
        1,
        Set.empty,
        DateTime.parse("2022-02-02"),
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        Map.empty,
      )
      res should_== (List(), List())
    }

    "return empty if supplier not valid" in new ApmScopeWithMock {
      override val apmPriceAdjustmentServiceImplMock: ApmPriceAdjustmentServiceImpl =
        apmPriceAdjustmentServiceImplDefaultMock(
          determineApmProgramSettings = (Seq(aValidMultipleAutoPriceMatchHolder.build), None),
          applyApmV2 = (List((1, aValidRoomResult)), List.empty),
        )

      val res = calculateApmPriceAdjustmentResult(
        List(
          (1,
           baseRoom
             .withApmCommissionHolder(
               aValidApmCommissionHolder
                 .withMultipleAutoPriceMatchHolder(List(aValidMultipleAutoPriceMatchHolder.build))
                 .withAutoPriceMatchInfo(Map(AutoPriceMatchKeyEntry(1, 1, Some(1)) -> Map(
                   aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1, Some(1)))))
                 .build,
             )
             .build)),
        1,
        999,
        1,
        Set.empty,
        DateTime.parse("2022-02-02"),
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        Map.empty,
      )
      res should_== (List(), List())
    }

    "return empty if no eligible rooms" in new ApmScopeWithMock {
      override val apmPriceAdjustmentServiceImplMock: ApmPriceAdjustmentServiceImpl =
        apmPriceAdjustmentServiceImplDefaultMock(
          validateEligibleRoom = (false, List.empty),
          determineApmProgramSettings = (Seq(aValidMultipleAutoPriceMatchHolder.build), None),
          applyApmV2 = (List((1, aValidRoomResult)), List.empty),
        )

      val res = calculateApmPriceAdjustmentResult(
        List(
          (1,
           baseRoom
             .withApmCommissionHolder(
               aValidApmCommissionHolder
                 .withMultipleAutoPriceMatchHolder(List(aValidMultipleAutoPriceMatchHolder.build))
                 .withAutoPriceMatchInfo(Map(AutoPriceMatchKeyEntry(1, 1, Some(1)) -> Map(
                   aValidStayDate1 -> AutoPriceMatchPriceInfo(100, 1, 1, Some(1)))))
                 .build,
             )
             .build)),
        1,
        332,
        1,
        Set.empty,
        DateTime.parse("2022-02-02"),
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        Map.empty,
      )
      res should_== (List(), List())
    }
  }

  "getAdjustmentDiscountPercent" should {
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=false" in new ApmScope {
      val enableApmMultipleDiscount = false
      val isApmFixEndDate = false
      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting = aValidMultipleAutoPriceMatchHolder.build,
        bookingDateTime = DateTime.now(),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 10d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = false

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting = aValidMultipleAutoPriceMatchHolder.withApmAdjustmentDiscount(15d).build,
        bookingDateTime = DateTime.now(),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 15d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true with invalid hotelid" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = false

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting = aValidMultipleAutoPriceMatchHolder.withApmAdjustmentDiscount(15d).build,
        bookingDateTime = DateTime.now(),
        adjustmentChannelId = 1,
        hotelId = 2,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 10d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true withApmAdjustmentDiscount" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = false

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting = aValidMultipleAutoPriceMatchHolder.withApmAdjustmentDiscount(20d).build,
        bookingDateTime = DateTime.now(),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 20d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=false and withApmAdjustmentDiscount and BookingDate is before start-date" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = false

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting = aValidMultipleAutoPriceMatchHolder
          .withApmAdjustmentDiscount(20d)
          .withStartDate(DateTime.parse("2024-01-01"))
          .build,
        bookingDateTime = DateTime.parse("1998-01-01"),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 10d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=false and withApmAdjustmentDiscount and BookingDate equals start-date" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = false

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting = aValidMultipleAutoPriceMatchHolder
          .withApmAdjustmentDiscount(20d)
          .withStartDate(DateTime.parse("2024-01-01"))
          .build,
        bookingDateTime = DateTime.parse("1999-01-01"),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 20d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=false and withApmAdjustmentDiscount and BookingDate equals end-date" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = false

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting = aValidMultipleAutoPriceMatchHolder.withApmAdjustmentDiscount(20d).build,
        bookingDateTime = DateTime.parse("2999-01-01"),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 20d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=false and withApmAdjustmentDiscount and BookingDate is in between end-date" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = false

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        aValidMultipleAutoPriceMatchHolder.build.copy(apmAdjustmentDiscount = Seq(ApmAdjustmentDiscount(
          programId = 1,
          adjustmentChannelId = 1,
          adjustmentDiscountPercent = Some(20),
          startDate = DateTime.parse("2024-01-01T00:00:00"),
          endDate = DateTime.parse("2024-01-19T00:00:00"),
        ))),
        bookingDateTime = DateTime.parse("2024-01-19T19:17:04"),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 10d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=true and withApmAdjustmentDiscount and BookingDate is before start-date" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = true

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting =
          aValidMultipleAutoPriceMatchHolder.build.copy(apmAdjustmentDiscount = Seq(ApmAdjustmentDiscount(
            programId = 1,
            adjustmentChannelId = 1,
            adjustmentDiscountPercent = Some(20),
            startDate = DateTime.parse("2024-01-01T00:00:00"),
            endDate = DateTime.parse("2024-01-19T00:00:00"),
          ))),
        bookingDateTime = DateTime.parse("2023-12-25T00:00:00"),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 10d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=true and withApmAdjustmentDiscount and BookingDate equals start-date" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = true

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting =
          aValidMultipleAutoPriceMatchHolder.build.copy(apmAdjustmentDiscount = Seq(ApmAdjustmentDiscount(
            programId = 1,
            adjustmentChannelId = 1,
            adjustmentDiscountPercent = Some(20),
            startDate = DateTime.parse("2024-01-01T00:00:00"),
            endDate = DateTime.parse("2024-01-19T00:00:00"),
          ))),
        bookingDateTime = DateTime.parse("2024-01-01T00:00:00"),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 20d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=true and withApmAdjustmentDiscount and BookingDate is before end-date" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = true

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting =
          aValidMultipleAutoPriceMatchHolder.build.copy(apmAdjustmentDiscount = Seq(ApmAdjustmentDiscount(
            programId = 1,
            adjustmentChannelId = 1,
            adjustmentDiscountPercent = Some(20),
            startDate = DateTime.parse("2024-01-01T00:00:00"),
            endDate = DateTime.parse("2024-01-19T00:00:00"),
          ))),
        bookingDateTime = DateTime.parse("2024-01-10T00:00:00"),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 20d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=true and withApmAdjustmentDiscount and BookingDate equals end-date" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = true
      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting =
          aValidMultipleAutoPriceMatchHolder.build.copy(apmAdjustmentDiscount = Seq(ApmAdjustmentDiscount(
            programId = 1,
            adjustmentChannelId = 1,
            adjustmentDiscountPercent = Some(20),
            startDate = DateTime.parse("2024-01-01T00:00:00"),
            endDate = DateTime.parse("2024-01-19T00:00:00"),
          ))),
        bookingDateTime = DateTime.parse("2024-01-19T00:00:00"),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 20d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=true and withApmAdjustmentDiscount and BookingDate is in between end-date" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = true
      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting =
          aValidMultipleAutoPriceMatchHolder.build.copy(apmAdjustmentDiscount = Seq(ApmAdjustmentDiscount(
            programId = 1,
            adjustmentChannelId = 1,
            adjustmentDiscountPercent = Some(20),
            startDate = DateTime.parse("2024-01-01T00:00:00"),
            endDate = DateTime.parse("2024-01-19T00:00:00"),
          ))),
        bookingDateTime = DateTime.parse("2024-01-19T23:59:59"),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 20d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=true and withApmAdjustmentDiscount and BookingDate is after end-date 1 day" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = true
      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting =
          aValidMultipleAutoPriceMatchHolder.build.copy(apmAdjustmentDiscount = Seq(ApmAdjustmentDiscount(
            programId = 1,
            adjustmentChannelId = 1,
            adjustmentDiscountPercent = Some(20),
            startDate = DateTime.parse("2024-01-01T00:00:00"),
            endDate = DateTime.parse("2024-01-19T00:00:00"),
          ))),
        bookingDateTime = DateTime.parse("2024-01-20T00:00:00"),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 10d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true and isApmFixEndDate=true and withApmAdjustmentDiscount and adjustmentChannelId = 2 and BookingDate not in start-date and end-date range" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = true
      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting =
          aValidMultipleAutoPriceMatchHolder.build.copy(apmAdjustmentDiscount = Seq(ApmAdjustmentDiscount(
            programId = 1,
            adjustmentChannelId = 1,
            adjustmentDiscountPercent = Some(20),
            startDate = DateTime.parse("2024-01-01T00:00:00"),
            endDate = DateTime.parse("2024-01-19T00:00:00"),
          ))),
        bookingDateTime = DateTime.parse("2023-01-01T00:00:00"),
        adjustmentChannelId = 2,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 10d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true withApmAdjustmentDiscount wrong channelId" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = false

      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting = aValidMultipleAutoPriceMatchHolder.withApmAdjustmentDiscount(20d).build,
        bookingDateTime = DateTime.now(),
        adjustmentChannelId = 2,
        hotelId = aValidHotelId,
        apmSettings = aValidApmSettingHolder,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 10d
    }
    "return adjustmentDiscountPercent correctly when enableApmMultipleDiscount=true withApmAdjustmentDiscount empty hotelList" in new ApmScope {
      val enableApmMultipleDiscount = true
      val isApmFixEndDate = true
      getAdjustmentDiscountPercent(
        enableApmMultipleDiscount = enableApmMultipleDiscount,
        apmRoomChannelSetting = aValidMultipleAutoPriceMatchHolder.withApmAdjustmentDiscount(20d).build,
        bookingDateTime = DateTime.now(),
        adjustmentChannelId = 1,
        hotelId = aValidHotelId,
        apmSettings = None,
        isApmFixEndDate = isApmFixEndDate,
      ) must_== 10d
    }
  }

  "getInitialFences" should {
    val requestFences: Map[Int, Set[YplRateFenceHolder]] = Map(1 -> Set(YplRateFenceHolder("TH", 1, 1)),
                                                               2 -> Set(YplRateFenceHolder("TH", 2, 1)),
                                                               3 -> Set(YplRateFenceHolder("TH", 3, 1)))
    val apmRateChannelId: Int = 2
    "return correct InitialFences when fixedFenceRate=true" in new ApmScope {
      getInitialFences(requestFences = requestFences, apmRateChannelId = apmRateChannelId) must_== Set(
        YplRateFenceHolder("TH", 2, 1))
    }
    "return empty InitialFences when fixedFenceRate=true and ChannelId not in fence" in new ApmScope {
      getInitialFences(requestFences = requestFences, apmRateChannelId = 4) must_== Set.empty
    }
  }
}
