package com.agoda.papi.ypl.commission.apm

import com.agoda.papi.enums.hotel.StayPackageTypes.Escapes
import com.agoda.papi.enums.room.Channel
import com.agoda.papi.ypl.commission.ApmCommissionHolder
import com.agoda.papi.ypl.commission.apm.`enum`.ApmConfigType
import com.agoda.papi.ypl.commission.apm.models.{
  ApmCommissionDiscountRoomParameters,
  ApmConfigHolder,
  ApmHotelStatus,
  ApmProgramType,
  ApmSettingHolder,
  MultipleAutoPriceMatchHolder,
}
import com.agoda.papi.ypl.commission.builder.MultipleAutoPriceMatchHolderBuilder
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope
import org.specs2.specification.core.Fragments

class ApmServiceSpec extends SpecificationWithJUnit {
  "ApmService" should {
    val TAIWAN_COUNTRY_ID = 140
    val TAIWAN_ASO_ELIGIBLE_PROGRAM_IDS = List(149, 150, 151)
    val BEDS_ADVANCED_PROGRAM_IDS = List(127, 155, 156)
    val bedsProgramIdsSequence: Seq[Int] = Seq(127, 155, 156)

    trait TestScope extends Scope with ApmService {
      def determineApmProgramSettings(
        dispatchChannels: Set[Int],
        multipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder],
        bookingDate: DateTime,
        isApmFixEndDate: Boolean): (Seq[MultipleAutoPriceMatchHolder], Option[MultipleAutoPriceMatchHolder]) = {
        val validRoomSetting = getApmRoomChannelSettings(multipleAutoPriceMatch, bookingDate, isApmFixEndDate)
        val validCommissionSetting =
          getApmCommissionDiscountChannelSetting(dispatchChannels, multipleAutoPriceMatch, bookingDate, isApmFixEndDate)
        (validRoomSetting, validCommissionSetting)
      }
    }
    val aValidRoomParams = ApmCommissionDiscountRoomParameters(1,
                                                               0,
                                                               List.empty,
                                                               None,
                                                               isExternalResellDataEmpty = false,
                                                               isEmptyHourlyAvailableSlots = true,
                                                               ApmCommissionHolder.default,
                                                               Set.empty)

    val allApmConfigs: Map[Int, ApmConfigHolder] = Map(
      ApmConfigType.AdjustmentCap.value -> ApmConfigHolder(
        globalLevel = Seq("30"),
        programLevel = Map.empty,
        hotelLevel = Seq.empty,
      ),
      ApmConfigType.BlackoutDays.value -> ApmConfigHolder(
        globalLevel = Seq("2023-01-22", "2023-01-23", "2023-01-24", "2023-01-25"),
        programLevel = Map(
          1 -> Seq("2023-02-01", "2023-02-02", "2023-02-03"),
          54 -> Seq("2023-03-01", "2023-03-02"),
        ),
        hotelLevel = Seq("2023-04-22", "2023-05-22", "2023-06-22"),
      ),
      ApmConfigType.CommissionReduction.value -> ApmConfigHolder(
        globalLevel = Seq("1"),
        programLevel = Map(
          1 -> Seq("1"),
          54 -> Seq("0.75"),
        ),
        hotelLevel = Seq(2.55d.toString),
      ),
      ApmConfigType.EnableAiForBedNetwork.value -> ApmConfigHolder(
        globalLevel = Seq.empty,
        programLevel = Map.empty,
        hotelLevel = Seq("-"),
      ),
    )

    "validateEligibleRoom" in {

      "return validateEligibleRoom correctly when APM-1405 is user A" in new TestScope {
        // hourlyAvailableSlots
        validateEligibleRoom(
          aValidRoomParams.copy(isEmptyHourlyAvailableSlots = true),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(isEmptyHourlyAvailableSlots = false),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false

        // Bedbank Channels
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.APS).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Bedbank, Channel.APS).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.BedbankAffiliates, Channel.APS).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.Bedbank).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.BedbankAffiliates).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.Bedbank).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
      }

      "return validateEligibleRoom correctly when FEZZIK-846 is user B with no beds programId" in new TestScope {
        // Non Beds Channels
        validateEligibleRoom(
          aValidRoomParams.copy(isEmptyHourlyAvailableSlots = true),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(isEmptyHourlyAvailableSlots = false),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.APS).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true

        // Bedbank Channels
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Bedbank, Channel.APS).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.BedbankAffiliates, Channel.APS).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.Bedbank).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.BedbankAffiliates).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.Bedbank).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
      }

      "return validateEligibleRoom correctly when FEZZIK-846 is user A with no beds programId" in new TestScope {
        // Non Beds Channels
        validateEligibleRoom(
          aValidRoomParams.copy(isEmptyHourlyAvailableSlots = true),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(isEmptyHourlyAvailableSlots = false),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.APS).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true

        // Bedbank Channels
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Bedbank, Channel.APS).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.BedbankAffiliates, Channel.APS).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.Bedbank).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.BedbankAffiliates).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.Bedbank).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
      }

      bedsProgramIdsSequence.foreach { BEDS_ADVANCED_PROGRAM_ID =>
        "return validateEligibleRoom correctly when FEZZIK-846 is user B with apm beds programId" in new TestScope {
          val apmSettings: Option[ApmSettingHolder] = Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS))
          // Non Beds Channels
          validateEligibleRoom(
            aValidRoomParams.copy(isEmptyHourlyAvailableSlots = true),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== true
          validateEligibleRoom(
            aValidRoomParams.copy(isEmptyHourlyAvailableSlots = false),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== false
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.APS).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== true

          // Bedbank Channels
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Bedbank, Channel.APS).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== true
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.BedbankAffiliates, Channel.APS).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== true
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.Bedbank).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== true
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.BedbankAffiliates).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== true
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.Bedbank).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== true
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.BedbankAffiliates).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== true
        }

        "return validateEligibleRoom correctly when FEZZIK-846 is user A with apm beds programId" in new TestScope {
          val apmSettings: Option[ApmSettingHolder] = Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS))
          // Non Beds Channels
          validateEligibleRoom(
            aValidRoomParams.copy(isEmptyHourlyAvailableSlots = true),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = false,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== true
          validateEligibleRoom(
            aValidRoomParams.copy(isEmptyHourlyAvailableSlots = false),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = false,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== false
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.APS).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = false,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== true

          // Bedbank Channels
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Bedbank, Channel.APS).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = false,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== false
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.BedbankAffiliates, Channel.APS).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = false,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== false
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.Bedbank).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = false,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== false
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.BedbankAffiliates).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = false,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== false
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(Channel.Retail, Channel.Bedbank).map(_.i)),
            apmSetting = apmSettings,
            apmConfigs = Map.empty,
            hotelCountryId = 0,
            isExcludeQuarantineChannel = false,
            apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = false,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_=== false
        }
      }

      "return validateEligibleRoom correctly for isExcludePkgRatePlanExp" in new TestScope {

        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Packages).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false

        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Packages).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.APO).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
      }

      "return validateEligibleRoom correctly for isExcludePkgRatePlanExp and skipPackageValidateCommReductionFeatureExp is true" in new TestScope {
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Packages).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true

        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Packages).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.APO).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
      }

      "return validateEligibleRoom correctly for Taiwan ASO with program_id is in (149, 150, 151)" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] = Some(
          ApmSettingHolder(Nil,
                           Nil,
                           BEDS_ADVANCED_PROGRAM_IDS,
                           apmTaiwanAsoEligibleProgramIds = TAIWAN_ASO_ELIGIBLE_PROGRAM_IDS))
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 149,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 150,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 151,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 100,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 150,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 149,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams,
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 149,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams,
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 149,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true

        // Critical test case: Non-Taiwan + Escapes + invalid program ID should return true
        // This distinguishes between && and || in the Taiwan ASO condition
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0, // Non-Taiwan
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = 0, // Invalid program ID not in TAIWAN_ASO_ELIGIBLE_PROGRAM_IDS
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true // Should be true because it's non-Taiwan, so else clause applies
      }

      "return validateEligibleRoom correctly for excludeRateChannelFromApmExp when APM_EXCLUDE_RATE_CHANNEL_IDS is A" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] =
          Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS, excludeRateChannelIds = List(1, 2, 3)))
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(2)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(2, 3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1, 2)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1, 2, 3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(4)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(4, 5)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List.empty),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1, 4)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
      }

      "return validateEligibleRoom correctly for excludeRateChannelFromApmExp when APM_EXCLUDE_RATE_CHANNEL_IDS is B and excludeRateChannelIds = [1,2,3]" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] =
          Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS, excludeRateChannelIds = List(1, 2, 3)))
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(2)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(2, 3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1, 2)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1, 2, 3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(4)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(4, 5)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List.empty),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true
      }

      "return validateEligibleRoom correctly for BlackListRcForNam" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] = Some(ApmSettingHolder(Nil, List(2), Nil))
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(8)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 1,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(8)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(8)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 1,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(8)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 1,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 1,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
      }

      "return validateEligibleRoom correctly for isExcludeQuarantineChannelExp" in new TestScope {
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Quarantine).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true

        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Retail).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = true,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.Quarantine).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = true,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(Channel.APO).map(_.i)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = true,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
      }

      "return validateEligibleRoom correctly for resell offers" in new TestScope {
        validateEligibleRoom(
          aValidRoomParams.copy(isExternalResellDataEmpty = false),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = true,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(isExternalResellDataEmpty = true),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = true,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
      }

      "Returns false when REMOVE_BEDS_PAID_EXCLUSION_FROM_APM is A and APM_ENABLE_AI_FOR_BED_NETWORK is A and room has beds bank channel" in new TestScope {
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(889)),
          apmSetting = Some(ApmSettingHolder(Nil, Nil, List.empty)),
          apmConfigs = allApmConfigs,
          hotelCountryId = 1,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_== false
      }

      "Returns false when REMOVE_BEDS_PAID_EXCLUSION_FROM_APM is B and APM_ENABLE_AI_FOR_BED_NETWORK is A and room has beds bank channel but no bedsAdvancedProgramId" in new TestScope {
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(889)),
          apmSetting = Some(ApmSettingHolder(Nil, Nil, List.empty)),
          apmConfigs = allApmConfigs,
          hotelCountryId = 1,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_== false
      }

      bedsProgramIdsSequence.foreach { bedsAdvancedProgramId =>
        "Returns true when REMOVE_BEDS_PAID_EXCLUSION_FROM_APM is B and APM_ENABLE_AI_FOR_BED_NETWORK is A and room has beds bank channel with bedsAdvancedProgramId" in new TestScope {
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(889)),
            apmSetting = Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS)),
            apmConfigs = allApmConfigs,
            hotelCountryId = 1,
            isExcludeQuarantineChannel = false,
            apmProgramId = bedsAdvancedProgramId,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = false,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_== true
        }

        "Returns true when REMOVE_BEDS_PAID_EXCLUSION_FROM_APM is A and APM_ENABLE_AI_FOR_BED_NETWORK is B and room is beds bank channel with enableAiForBedNetwork config" in new TestScope {
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(889)),
            apmSetting = Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS)),
            apmConfigs = allApmConfigs,
            hotelCountryId = 1,
            isExcludeQuarantineChannel = false,
            apmProgramId = bedsAdvancedProgramId,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = false,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = true,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_== true
        }

        "Returns false when REMOVE_BEDS_PAID_EXCLUSION_FROM_APM is A and APM_ENABLE_AI_FOR_BED_NETWORK is B and room is beds bank channel without enableAiForBedNetwork config" in new TestScope {
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(889)),
            apmSetting = Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS)),
            apmConfigs = allApmConfigs.filterNot { case (k, _) => k == ApmConfigType.EnableAiForBedNetwork.value },
            hotelCountryId = 1,
            isExcludeQuarantineChannel = false,
            apmProgramId = bedsAdvancedProgramId,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = false,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = true,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_== false
        }

        "Returns true when REMOVE_BEDS_PAID_EXCLUSION_FROM_APM is B and APM_ENABLE_AI_FOR_BED_NETWORK is B and room is beds bank channel with enableAiForBedNetwork config" in new TestScope {
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(889)),
            apmSetting = Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS)),
            apmConfigs = allApmConfigs,
            hotelCountryId = 1,
            isExcludeQuarantineChannel = false,
            apmProgramId = bedsAdvancedProgramId,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = true,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_== true
        }

        "Returns true when REMOVE_BEDS_PAID_EXCLUSION_FROM_APM is B and APM_ENABLE_AI_FOR_BED_NETWORK is B and room is beds bank channel with bedsAdvancedProgramId but without enableAiForBedNetwork config" in new TestScope {
          validateEligibleRoom(
            aValidRoomParams.copy(compositeChannels = List(889)),
            apmSetting = Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS)),
            apmConfigs = allApmConfigs.filterNot { case (k, _) => k == ApmConfigType.EnableAiForBedNetwork.value },
            hotelCountryId = 1,
            isExcludeQuarantineChannel = false,
            apmProgramId = bedsAdvancedProgramId,
            apmProgramType = 0,
            skipPackageValidateCommReductionFeatureExp = false,
            removeBedPaidExclusionFromApmExp = true,
            skipApmPriceAdjustmentForResellExp = false,
            enabledAiForBedNetwork = true,
            excludeRateChannelFromApmExp = false,
            enabledApmArpPlusProgram = false,
          ) should_== true
        }
      }

      "Returns false when REMOVE_BEDS_PAID_EXCLUSION_FROM_APM is B and APM_ENABLE_AI_FOR_BED_NETWORK is B and room is beds bank channel without bedsAdvancedProgramId and without enableAiForBedNetwork config" in new TestScope {
        val bedsAdvancedProgramId = 127
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(889)),
          apmSetting = Some(ApmSettingHolder(Nil, Nil, List.empty)),
          apmConfigs = allApmConfigs.filterNot { case (k, _) => k == ApmConfigType.EnableAiForBedNetwork.value },
          hotelCountryId = 1,
          isExcludeQuarantineChannel = false,
          apmProgramId = bedsAdvancedProgramId,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = true,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_== false
      }

      "Returns true when REMOVE_BEDS_PAID_EXCLUSION_FROM_APM is B, room has no beds bank channel" in new TestScope {
        validateEligibleRoom(
          aValidRoomParams,
          apmSetting = Some(ApmSettingHolder(Nil, Nil, List.empty)),
          apmConfigs = allApmConfigs,
          hotelCountryId = 1,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_== true
      }

      "Returns true when APM_ENABLE_AI_FOR_BED_NETWORK is B and room has no beds bank channel" in new TestScope {
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(889, 54)),
          apmSetting = Some(ApmSettingHolder(Nil, Nil, List.empty)),
          apmConfigs = allApmConfigs,
          hotelCountryId = 1,
          isExcludeQuarantineChannel = false,
          apmProgramId = 2,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = true,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_== true
      }

      "return validateEligibleRoom correctly for ARP Plus program type when enabledApmArpPlusProgram is true" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] = Some(
          ApmSettingHolder(
            Nil,
            Nil,
            BEDS_ADVANCED_PROGRAM_IDS,
            apmTaiwanAsoEligibleProgramIds = TAIWAN_ASO_ELIGIBLE_PROGRAM_IDS,
            apmTaiwanAsoEligibleProgramTypes = List(ApmProgramType.ArpPlus),
          ))

        // Test with ARP Plus program type when experiment is enabled
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.ArpPlus, // ARP Plus program type
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = true, // Enable the new experiment
        ) should_=== true

        // Test with non-ARP Plus program type when experiment is enabled
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 149,
          apmProgramType = ApmProgramType.ArpPlus, // ARP Plus program type for new logic
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = true, // Enable the new experiment
        ) should_=== true // Should be true because Taiwan ASO logic still applies

        // Test that experiment flag controls which logic is used
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 149, // Valid program ID
          apmProgramType = ApmProgramType.Ai, // Non-ARP Plus program type
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false, // Disable experiment - should use program ID logic
        ) should_=== true

        // Test non-Taiwan country (should always return true regardless of program type)
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0, // Non-Taiwan
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.Ai,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = true,
        ) should_=== true

        // Test Taiwan with non-Escapes package type (should always return true)
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = None),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.Ai,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = true,
        ) should_=== true
      }

      "return validateEligibleRoom correctly for excludeRateChannelFromApmExp when APM_EXCLUDE_RATE_CHANNEL_IDS is A" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] =
          Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS, excludeRateChannelIds = List(1, 2, 3)))
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(2)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(2, 3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1, 2)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1, 2, 3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(4)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(4, 5)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List.empty),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1, 4)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = false,
        ) should_=== true
      }

      "return validateEligibleRoom correctly for excludeRateChannelFromApmExp when APM_EXCLUDE_RATE_CHANNEL_IDS is B and excludeRateChannelIds = [1,2,3]" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] =
          Some(ApmSettingHolder(Nil, Nil, BEDS_ADVANCED_PROGRAM_IDS, excludeRateChannelIds = List(1, 2, 3)))
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(2)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(2, 3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1, 2)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1, 2, 3)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(4)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(4, 5)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List.empty),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 0,
          apmProgramType = 0,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true
      }

      "validateEligibleRoom should work correctly when experiment is disabled" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] = Some(
          ApmSettingHolder(
            Nil,
            Nil,
            BEDS_ADVANCED_PROGRAM_IDS,
            apmTaiwanAsoEligibleProgramIds = TAIWAN_ASO_ELIGIBLE_PROGRAM_IDS,
            excludeRateChannelIds = List(1, 2, 3),
          ))

        // When enabledApmArpPlusProgram=false, should use the else branch (simpler logic)
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings, // Channel 1 is in excludeRateChannelIds
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.ArpPlus, // Program type irrelevant when experiment is disabled
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false, // Experiment disabled
        ) should_=== false // Should exclude because channel is in excludeRateChannelIds

        // When apmSetting is None, should not exclude
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.ArpPlus,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== true // Should not exclude because apmSetting is None
      }
    }

    "determineApmProgramSettings" in {
      val aValidMultipleAutoPriceMatchHolder = MultipleAutoPriceMatchHolder(1,
                                                                            Some(1),
                                                                            0d,
                                                                            1,
                                                                            0d,
                                                                            1,
                                                                            DateTime.parse("2023-05-01"),
                                                                            None,
                                                                            Seq.empty,
                                                                            Seq.empty,
                                                                            Some(0))
      val aValidActiveMultipleAutoPriceMatch = MultipleAutoPriceMatchHolderBuilder(aValidMultipleAutoPriceMatchHolder)
        .withStartDate(DateTime.parse("2023-05-01"))
        .withStatusId(1)
        .withCommissionDiscountChannelId(99)
        .withAdjustmentChannelId(99)

      val aValidDispatchChannel = Set(99)

      "Returns validRoomSetting correctly - dispatch channels contain one of the channel in MAPM" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch.withEndDate(DateTime.parse("2023-09-01")).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-06-01"),
                                                      false)
        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - start date after booking date" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStartDate(DateTime.parse("2023-09-01")).withStatusId(1).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-07-01"),
                                                      false)
        res.isEmpty should_== true
        res2.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given isApmFixDate= true and start date is after booking date" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStartDate(DateTime.parse("2023-09-01")).withStatusId(1).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-07-01"),
                                                      true)
        res.isEmpty should_== true
        res2.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given isApmFixDate= true and start date is before booking date" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStartDate(DateTime.parse("2023-01-01")).withStatusId(1).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-07-01"),
                                                      true)
        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - given isApmFixDate= false and start date is before booking date" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStartDate(DateTime.parse("2023-01-01")).withStatusId(1).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-07-01"),
                                                      false)
        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - given isApmFixDate= true and start date equals booking date" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStartDate(DateTime.parse("2023-01-01")).withStatusId(1).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-01-01"),
                                                      true)
        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - given isApmFixDate= false and start date equals booking date" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStartDate(DateTime.parse("2023-01-01")).withStatusId(1).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-01-01"),
                                                      false)
        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - end date empty" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch.withStatusId(1).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-06-01"),
                                                      false)
        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - given isApmFixDate= true and end date empty" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch.withStatusId(1).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-06-01"),
                                                      true)
        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - end date before booking date" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withEndDate(DateTime.parse("2023-09-01")).withStatusId(1).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-10-01"),
                                                      false)
        res.isEmpty should_== true
        res2.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given isApmFixDate= true and end date is before BookingDate" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStatusId(1).withEndDate(DateTime.parse("2023-09-01")).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-10-01"),
                                                      true)

        res.isEmpty should_== true
        res2.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given isApmFixDate= true and end date is before BookingDate 1 day" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStatusId(1).withEndDate(DateTime.parse("2023-09-01")).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-09-02T00:00:01"),
                                                      true)

        res.isEmpty should_== true
        res2.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given isApmFixDate= true and end date is after BookingDate" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStatusId(1).withEndDate(DateTime.parse("2023-07-01")).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-06-01"),
                                                      true)

        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - given isApmFixDate= false and end date is after BookingDate" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStatusId(1).withEndDate(DateTime.parse("2023-07-01")).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-06-01"),
                                                      false)

        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - given isApmFixDate= true and end date equals BookingDate" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStatusId(1).withEndDate(DateTime.parse("2023-07-01T00:00:00")).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-07-01T00:00:00"),
                                                      true)

        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - given isApmFixDate= false and end date equals BookingDate" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStatusId(1).withEndDate(DateTime.parse("2023-07-01")).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-07-01T00:00:00"),
                                                      false)

        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - given isApmFixDate= true and BookingDate is in between end date" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStatusId(1).withEndDate(DateTime.parse("2023-07-01")).build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-07-01T23:59:59"),
                                                      true)

        res.isEmpty should_== false
        res2.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - given isApmFixDate= false and BookingDate is in between end date" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch
          .withStatusId(1)
          .withEndDate(DateTime.parse("2023-07-01"))
          .withCommissionDiscountChannelId(None)
          .build

        val (res, res2) = determineApmProgramSettings(aValidDispatchChannel,
                                                      Seq(multipleAutoPriceMatch1),
                                                      DateTime.parse("2023-07-01T23:00:00"),
                                                      false)

        res.isEmpty should_== true
        res2.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given withCommissionDiscountChannelId= None isApmFixDate= false and BookingDate is in between end date" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch
          .withStatusId(1)
          .withEndDate(DateTime.parse("2023-07-01"))
          .withCommissionDiscountChannelId(None)
          .build

        val (_, res) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-07-01T23:59:59"),
                                                   false)

        res.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given withCommissionDiscountChannelId= None isApmFixDate= true and BookingDate is in between end date" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch
          .withStatusId(1)
          .withEndDate(DateTime.parse("2023-07-01"))
          .withCommissionDiscountChannelId(None)
          .build

        val (_, res) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-07-01T23:59:59"),
                                                   true)

        res.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given withCommissionDiscountChannelId= 1 isApmFixDate= true and BookingDate is in between end date" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch
          .withStatusId(1)
          .withEndDate(DateTime.parse("2023-07-01"))
          .withCommissionDiscountChannelId(1)
          .build

        val (_, res) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-07-01T23:00:00"),
                                                   true)

        res.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given withCommissionDiscountChannelId= 99 isApmFixDate= true and BookingDate is in between end date" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch
          .withStatusId(1)
          .withEndDate(DateTime.parse("2023-07-01"))
          .withCommissionDiscountChannelId(99)
          .build

        val (_, res) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-07-01T23:00:00"),
                                                   true)

        res.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - given withCommissionDiscountChannelId= 1 isApmFixDate= true and BookingDate is not in start date and end date" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch
          .withStatusId(1)
          .withStartDate(DateTime.parse("2023-07-01"))
          .withEndDate(DateTime.parse("2023-07-19"))
          .withCommissionDiscountChannelId(1)
          .build

        val (_, res) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-05-04T23:00:00"),
                                                   true)

        res.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given withCommissionDiscountChannelId= 1 isApmFixDate= false and BookingDate is not in start date and end date" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch
          .withStatusId(1)
          .withStartDate(DateTime.parse("2023-07-01"))
          .withEndDate(DateTime.parse("2023-07-19"))
          .withCommissionDiscountChannelId(1)
          .build

        val (_, res) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-05-04T23:00:00"),
                                                   false)

        res.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - given withCommissionDiscountChannelId= 1 isApmFixDate= false and BookingDate is not in start date and end date" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch
          .withStatusId(1)
          .withStartDate(DateTime.parse("2023-07-01"))
          .withEndDate(DateTime.parse("2023-07-19"))
          .withCommissionDiscountChannelId(1)
          .build

        val (_, res) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-05-04T23:00:00"),
                                                   false)

        res.isEmpty should_== true
      }
      "Returns validRoomSetting correctly - given withCommissionDiscountChannelId= 99 isApmFixDate= false and BookingDate is in between end date" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch
          .withStatusId(1)
          .withEndDate(DateTime.parse("2023-07-01"))
          .withCommissionDiscountChannelId(99)
          .build

        val (_, res) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-07-01T23:00:00"),
                                                   false)

        res.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - not active" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch.withStatusId(0).build

        val (res, _) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-06-01"),
                                                   false)
        res.isEmpty should_== true
      }

      "Returns validRoomSetting correctly - filter correctly" in new TestScope {
        val multipleAutoPriceMatch1 =
          aValidActiveMultipleAutoPriceMatch.withStatusId(1).withEndDate(DateTime.parse("2023-07-01")).build

        val (_, res) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-06-01"),
                                                   false)
        res.isEmpty should_== false
      }

      "Returns validRoomSetting correctly - filter correctly" in new TestScope {
        val multipleAutoPriceMatch1 = aValidActiveMultipleAutoPriceMatch
          .withStatusId(1)
          .withEndDate(DateTime.parse("2023-07-01"))
          .withCommissionDiscountChannelId(None)
          .build

        val (_, res) = determineApmProgramSettings(aValidDispatchChannel,
                                                   Seq(multipleAutoPriceMatch1),
                                                   DateTime.parse("2023-08-01"),
                                                   false)
        res.isEmpty should_== true
      }
    }

    "getCommissionHotelStatus" in {
      val aValidMultipleAutoPriceMatchHolder = MultipleAutoPriceMatchHolder(1,
                                                                            Some(99),
                                                                            0d,
                                                                            99,
                                                                            0d,
                                                                            6,
                                                                            DateTime.parse("2023-05-01"),
                                                                            Some(DateTime.parse("2023-09-01")),
                                                                            Seq.empty,
                                                                            Seq.empty,
                                                                            Some(0))
      "Returns correct commission hotel status correctly for each cases" in {
        Fragments(
          List(
            (ApmProgramType.Ai, ApmHotelStatus.Active, true),
            (ApmProgramType.Ai, ApmHotelStatus.InActive, false),
            (ApmProgramType.Ai, ApmHotelStatus.Suspended, true),
            (ApmProgramType.Arp, ApmHotelStatus.Experimental, true),
            (ApmProgramType.Arp, ApmHotelStatus.InActive, false),
            (ApmProgramType.ArpV2, ApmHotelStatus.ExperimentalV2, true),
            (ApmProgramType.ArpV2, ApmHotelStatus.InActive, false),
            (ApmProgramType.PeakSeason, ApmHotelStatus.PeakSeason, true),
            (ApmProgramType.PeakSeason, ApmHotelStatus.InActive, false),
            (ApmProgramType.AiCalendarView, ApmHotelStatus.Active, false),
            (ApmProgramType.AiCalendarView, ApmHotelStatus.InActive, false),
            (ApmProgramType.AiCalendarView, ApmHotelStatus.Suspended, false),
          ).map { case (programTypeId, statusId, expected) =>
            s"when programTypeId is $programTypeId and statusId is $statusId, isApmHotelActive should be $expected" in new TestScope {
              val multipleAutoPriceMatchActive: MultipleAutoPriceMatchHolder = MultipleAutoPriceMatchHolderBuilder(
                aValidMultipleAutoPriceMatchHolder).withStatusId(statusId).withProgramType(programTypeId).build

              val resActiveNonProgramType: Boolean = getCommissionHotelStatus(multipleAutoPriceMatchActive)
              resActiveNonProgramType should_== expected
            }
          }: _*,
        )
      }
    }

    "ARP Plus program validation should" in {

      "validateEligibleRoom should return false when enabledApmArpPlusProgram is true and excludeRateChannelFromApmExp excludes the rate channel with ArpPlus program type" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] = Some(
          ApmSettingHolder(
            Nil,
            Nil,
            BEDS_ADVANCED_PROGRAM_IDS,
            apmTaiwanAsoEligibleProgramIds = TAIWAN_ASO_ELIGIBLE_PROGRAM_IDS,
            excludeRateChannelIds = List(1, 2, 3),
          ))

        // This test covers the isExcludeRateChannelFromApm method
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.ArpPlus, // ARP Plus program type
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = true,
        ) should_=== false
      }

      "validateEligibleRoom should work correctly with ARP Plus program type validation" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] = Some(
          ApmSettingHolder(
            Nil,
            Nil,
            BEDS_ADVANCED_PROGRAM_IDS,
            apmTaiwanAsoEligibleProgramIds = TAIWAN_ASO_ELIGIBLE_PROGRAM_IDS,
            apmTaiwanAsoEligibleProgramTypes = List(ApmProgramType.ArpPlus),
          ))

        // Test with ARP Plus program type - should pass all validations including Taiwan ASO
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 149, // Valid Taiwan ASO program ID
          apmProgramType = ApmProgramType.ArpPlus, // ARP Plus program type
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = true,
        ) should_=== true

        // Test with non-ARP Plus program type - should still pass Taiwan ASO validation
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 149, // Valid Taiwan ASO program ID
          apmProgramType = ApmProgramType.ArpPlus, // ARP Plus program type for new logic
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = true,
        ) should_=== true

        // Test when apmSetting is None - should pass because Taiwan ASO check will fail gracefully
        validateEligibleRoom(
          aValidRoomParams.copy(stayPackageType = Some(Escapes)),
          apmSetting = None, // No apmSetting
          apmConfigs = Map.empty,
          hotelCountryId = TAIWAN_COUNTRY_ID,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.ArpPlus,
          skipPackageValidateCommReductionFeatureExp = true,
          removeBedPaidExclusionFromApmExp = true,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = true,
        ) should_=== false // Should be false because Taiwan ASO validation fails with None apmSetting
      }

      "validateEligibleRoom should handle isExcludeRateChannelFromApm edge cases correctly" in new TestScope {
        val apmSettings: Option[ApmSettingHolder] = Some(
          ApmSettingHolder(
            Nil,
            Nil,
            BEDS_ADVANCED_PROGRAM_IDS,
            apmTaiwanAsoEligibleProgramIds = TAIWAN_ASO_ELIGIBLE_PROGRAM_IDS,
            excludeRateChannelIds = List(1, 2, 3),
          ))

        // Test case: enabledApmArpPlusProgram=true with ArpPlus program type
        // Channel is excluded, so should return false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.ArpPlus, // ARP Plus program type
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = true,
        ) should_=== false

        // Test case: enabledApmArpPlusProgram=true with non-ArpPlus program type
        // Channel is excluded but program type doesn't match, so should return true
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.Ai, // Non-ARP Plus program type
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = true,
        ) should_=== true

        // Test case: enabledApmArpPlusProgram=false (uses else branch)
        // Channel is excluded, so should return false regardless of program type
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.Ai, // Program type irrelevant when experiment is false
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = false,
        ) should_=== false

        // Test case: excludeRateChannelFromApmExp=false
        // Should return true because channel exclusion is disabled
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.ArpPlus,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = false,
          enabledApmArpPlusProgram = true,
        ) should_=== true

        // Test case: apmSetting=None
        // Should return true because apmSetting.exists returns false
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = None,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.ArpPlus,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = true,
        ) should_=== true

        // Test case: channel not in excludeRateChannelIds
        // Should return true because channel is not excluded
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(99)), // Channel not in excludeRateChannelIds
          apmSetting = apmSettings,
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = ApmProgramType.ArpPlus,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true,
          enabledApmArpPlusProgram = true,
        ) should_=== true
      }

      "validateEligibleRoom should return false when apmSetting is None and excludeRateChannelFromApmExp is enabled (Taiwan ASO disabled)" in new TestScope {
        validateEligibleRoom(
          aValidRoomParams.copy(compositeChannels = List(1)),
          apmSetting = None, // apmSetting is None
          apmConfigs = Map.empty,
          hotelCountryId = 0,
          isExcludeQuarantineChannel = false,
          apmProgramId = 999,
          apmProgramType = 11,
          skipPackageValidateCommReductionFeatureExp = false,
          removeBedPaidExclusionFromApmExp = false,
          skipApmPriceAdjustmentForResellExp = false,
          enabledAiForBedNetwork = false,
          excludeRateChannelFromApmExp = true, // This is true
          enabledApmArpPlusProgram = false, // This is false to go into the else branch
        ) should_=== true // Original: !false = true, Mutated: !true = false (catches the mutation)
      }
    }
  }
}
