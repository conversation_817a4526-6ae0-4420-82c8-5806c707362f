package com.agoda.papi.ypl.commission.apm

import com.agoda.papi.enums.hotel.DMC
import com.agoda.papi.enums.room.ChargeType
import com.agoda.papi.ypl.commission.ApmCommissionHolder
import com.agoda.papi.ypl.commission.apm.`enum`.ApmConfigType
import com.agoda.papi.ypl.commission.apm.models._
import com.agoda.papi.ypl.commission.models.YplRateFenceHolder
import org.joda.time.DateTime
import org.specs2.matcher.AnyMatchers
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope

class ApmCommissionDiscountServiceSpec extends SpecificationWithJUnit with AnyMatchers {
  trait TestScopeWithRealMethodCall extends Scope with ApmCommissionDiscountServiceImpl with ApmService {}
  trait TestScope extends Scope with ApmCommissionDiscountServiceImpl with ApmService {

    val determineApmProgramSettingsResult: (Seq[MultipleAutoPriceMatchHolder], Option[MultipleAutoPriceMatchHolder]) =
      (Seq.empty, None)
    val validateEligibleRoomResult: Boolean = false
    override def validateEligibleRoom[T <: ApmRoomParameters](apmCommissionRoomParameters: T,
                                                              apmSetting: Option[ApmSettingHolder],
                                                              apmConfigs: Map[Int, ApmConfigHolder],
                                                              hotelCountryId: Long,
                                                              isExcludeQuarantineChannel: Boolean,
                                                              apmProgramId: Int,
                                                              apmProgramType: Int,
                                                              skipPackageValidateCommReductionFeatureExp: Boolean,
                                                              removeBedPaidExclusionFromApmExp: Boolean,
                                                              skipApmPriceAdjustmentForResellExp: Boolean,
                                                              enabledAiExp: Boolean,
                                                              excludeRateChannelFromApmExp: Boolean,
                                                              enabledApmArpPlusProgram: Boolean): Boolean =
      validateEligibleRoomResult

    override def getApmRoomChannelSettings(multipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder],
                                           bookingDate: DateTime,
                                           isApmFixDate: Boolean): Seq[MultipleAutoPriceMatchHolder] =
      determineApmProgramSettingsResult._1

    override def getApmCommissionDiscountChannelSetting(dispatchChannels: Set[Int],
                                                        multipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder],
                                                        bookingDate: DateTime,
                                                        isApmFixDate: Boolean): Option[MultipleAutoPriceMatchHolder] =
      determineApmProgramSettingsResult._2
  }
  "ApmCommissionDiscountService" should {
    val aValidHotelId = 1
    val aValidRoomIndex = 1
    val aValidRoomIndex2 = 2
    val aValidSupplierId = DMC.YCS.value
    val aValidRoomCompositeChannels: List[Int] = List(1, 2, 3)
    val aValidMultipleAutoPriceMatchHolder: MultipleAutoPriceMatchHolder = MultipleAutoPriceMatchHolder(
      1,
      Some(1),
      10d,
      1,
      10d,
      ApmHotelStatus.Active,
      DateTime.parse("2022-01-01"),
      None,
      Seq.empty,
      Seq.empty,
      Some(0))

    def aValidApmRoomParams: ApmCommissionDiscountRoomParameters = ApmCommissionDiscountRoomParameters(
      aValidSupplierId,
      0d,
      aValidRoomCompositeChannels,
      None,
      isExternalResellDataEmpty = false,
      isEmptyHourlyAvailableSlots = false,
      ApmCommissionHolder.default.copy(
        apmSettings = Some(ApmSettingHolder(List.empty, List.empty, List.empty, List.empty, List(1))),
        multipleAutoPriceMatchHolder = Seq(aValidMultipleAutoPriceMatchHolder),
      ),
      Set(YplRateFenceHolder(origin = "th", cid = 1, language = 1)),
    )

    "calculateApmCommissionReductionResult correctly" in new TestScope {
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res should_== Map(aValidRoomIndex -> ApmCommissionDiscountResult(Set.empty, false, 0, 0, None, false))
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly" in new TestScope {
      override val determineApmProgramSettingsResult =
        (Seq.empty,
         Some(
           aValidMultipleAutoPriceMatchHolder.copy(apmCommissionDiscount = Seq(
             ApmCommissionDiscount(
               programId = 1,
               commissionChannelId = 1,
               commissionDiscountPercent = Some(20d),
               startDate = DateTime.parse("1900-01-01"),
               endDate = DateTime.parse("2999-01-01"),
             )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 10d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly missing (multi-discount)" in new TestScope {
      override val determineApmProgramSettingsResult =
        (Seq.empty, Some(aValidMultipleAutoPriceMatchHolder.copy(commissionDiscountPercent = 7d)))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 7d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount)" in new TestScope {
      override val determineApmProgramSettingsResult =
        (Seq.empty,
         Some(
           aValidMultipleAutoPriceMatchHolder.copy(apmCommissionDiscount = Seq(
             ApmCommissionDiscount(
               programId = 1,
               commissionChannelId = 1,
               commissionDiscountPercent = Some(20d),
               startDate = DateTime.parse("1900-01-01"),
               endDate = DateTime.parse("2999-01-01"),
             )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 20d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) when isApmFixEndDate=false and BookingDate is not in start-date and end-date range" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 12d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("1900-01-01"),
                                                                endDate = DateTime.parse("1999-01-01"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 12d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) when isApmFixEndDate=false and BookingDate equals start-date" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 15d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("2024-01-01"),
                                                                endDate = DateTime.parse("2999-01-01"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2024-01-01"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 20d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) when isApmFixEndDate=false and BookingDate is after start-date" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 15d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("2024-12-01"),
                                                                endDate = DateTime.parse("2024-12-31"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2024-12-01T10:00:00"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 20d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) when isApmFixEndDate=false and BookingDate is in between end-date" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 15d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("2024-12-01"),
                                                                endDate = DateTime.parse("2024-12-31"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2024-12-31T19:19:19"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 15d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) when isApmFixEndDate=false and BookingDate is after end-date" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 15d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("2024-12-01"),
                                                                endDate = DateTime.parse("2024-12-31"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2025-01-01T00:00:00"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 15d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) when isApmFixEndDate=true and BookingDate equals start-date" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 15d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("2024-12-01"),
                                                                endDate = DateTime.parse("2024-12-31"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2024-12-01T00:00:00"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = true,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 20d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) when isApmFixEndDate=true and BookingDate is after start-date" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 15d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("2024-12-01"),
                                                                endDate = DateTime.parse("2024-12-31"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2024-12-01T11:19:04"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = true,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 20d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) when isApmFixEndDate=true and BookingDate equals end-date" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 15d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("2024-12-01"),
                                                                endDate = DateTime.parse("2024-12-31"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2024-12-31T00:00:00"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 20d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) when isApmFixEndDate=true and BookingDate is after end-date 1 day" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 15d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("2024-12-01"),
                                                                endDate = DateTime.parse("2024-12-31"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2025-01-01T00:00:00"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = true,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 15d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) when isApmFixEndDate=true and BookingDate is in between end-date" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 15d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("2024-12-01"),
                                                                endDate = DateTime.parse("2024-12-31"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2024-12-31T23:59:59"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = true,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 20d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) commissionChannelId is not matched" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 17d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 10,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("1900-01-01"),
                                                                endDate = DateTime.parse("2999-01-01"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.values.head.apmDiscountPercent should_== 17d
    }

    "calculateApmCommissionReductionResult correctly - select commission discount correctly (multi-discount) Hotel list is empty" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty,
                                                        Some(
                                                          aValidMultipleAutoPriceMatchHolder
                                                            .copy(commissionDiscountPercent = 18d)
                                                            .copy(apmCommissionDiscount = Seq(
                                                              ApmCommissionDiscount(
                                                                programId = 1,
                                                                commissionChannelId = 1,
                                                                commissionDiscountPercent = Some(20d),
                                                                startDate = DateTime.parse("1900-01-01"),
                                                                endDate = DateTime.parse("2999-01-01"),
                                                              )))))
      val res = calculateApmCommissionReductionResult(
        Map(
          aValidRoomIndex -> aValidApmRoomParams.copy(
            apmCommissionHolder = aValidApmRoomParams.apmCommissionHolder.copy(apmSettings = None))),
        DateTime.parse("2023-02-02"),
        7,
        Set(1, 2, 3),
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = true,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map(1 -> Set(YplRateFenceHolder("TH", 1, 1))),
      )
      res.values.head.apmDiscountPercent should_== 18d
    }

    "calculateApmCommissionReductionResult correctly - multipleAutoPriceMatchHolder is empty" in new TestScope {
      override val determineApmProgramSettingsResult
        : (Seq[MultipleAutoPriceMatchHolder], Option[MultipleAutoPriceMatchHolder]) = (Seq.empty, None)
      override def calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent: Double,
        bookingDate: DateTime,
        hotelGmtOffset: Int,
        apmCommissionReductionEligibility: Seq[ApmCommissionReductionEligibilityHolder],
        isActiveOrSuspendedApmHotel: Boolean,
        supplierId: Int): Boolean = isActiveOrSuspendedApmHotel
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(false)
    }

    "calculateApmCommissionReductionResult correctly - calculating blackOutDates correctly" in new TestScope {
      override val validateEligibleRoomResult: Boolean = true
      override val determineApmProgramSettingsResult
        : (Seq[MultipleAutoPriceMatchHolder], Option[MultipleAutoPriceMatchHolder]) = (Seq.empty, None)
      override def calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent: Double,
        bookingDate: DateTime,
        hotelGmtOffset: Int,
        apmCommissionReductionEligibility: Seq[ApmCommissionReductionEligibilityHolder],
        isActiveOrSuspendedApmHotel: Boolean,
        supplierId: Int): Boolean = isActiveOrSuspendedApmHotel

      val res = calculateApmCommissionReductionResult(
        Map(
          aValidRoomIndex -> aValidApmRoomParams.copy(apmCommissionHolder = aValidApmRoomParams.apmCommissionHolder.copy(
            apmConfigs = Map(ApmConfigType.BlackoutDays.value -> ApmConfigHolder(Seq.empty, Map.empty, Seq("2023-02-03"))),
          ))),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.get(aValidRoomIndex).map(_.canApplyDiscount(DateTime.parse("2023-02-03"), ChargeType.Room)) should_== Some(
        false)
      res.get(aValidRoomIndex).map(_.canApplyDiscount(DateTime.parse("2023-02-02"), ChargeType.Room)) should_== Some(
        true)
    }

    "calculateApmCommissionReductionResult correctly - calculating isSupplierValidFlag correctly" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty, Some(aValidMultipleAutoPriceMatchHolder))
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams.copy(supplierId = aValidSupplierId),
            aValidRoomIndex2 -> aValidApmRoomParams.copy(supplierId = 9999999)),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.get(aValidRoomIndex).map(_.isSupplierValid) should_== Some(true)
      res.get(aValidRoomIndex2).map(_.isSupplierValid) should_== Some(false)
    }

    "calculateApmCommissionReductionResult correctly - calculating isRoomEligible correctly" in new TestScope {
      override val determineApmProgramSettingsResult =
        (Seq.empty, Some(aValidMultipleAutoPriceMatchHolder.copy(statusId = ApmHotelStatus.Active)))

      override def validateEligibleRoom[T <: ApmRoomParameters](apmCommissionRoomParameters: T,
                                                                apmSetting: Option[ApmSettingHolder],
                                                                apmConfigs: Map[Int, ApmConfigHolder],
                                                                hotelCountryId: Long,
                                                                isExcludeQuarantineChannel: Boolean,
                                                                apmProgramId: Int,
                                                                apmProgramType: Int,
                                                                skipPackageValidateCommReductionFeatureExp: Boolean,
                                                                removeBedPaidExclusionFromApmExp: Boolean,
                                                                skipApmPriceAdjustmentForResellExp: Boolean,
                                                                enabledAiExp: Boolean,
                                                                excludeRateChannelFromApmExp: Boolean,
                                                                enabledApmArpPlusProgram: Boolean): Boolean =
        !isExcludeQuarantineChannel && skipPackageValidateCommReductionFeatureExp

      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams, aValidRoomIndex2 -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        1,
        Set.empty,
        aValidSupplierId,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map(1 -> Set(YplRateFenceHolder(origin = "th", cid = 1, language = 1))),
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(true)
      res.get(aValidRoomIndex2).map(_.isRoomEligible) should_== Some(true)
    }

    "calculateApmCommissionReductionResult correctly - calculating isRoomEligible correctly - status is peak season" in new TestScope {
      override val determineApmProgramSettingsResult =
        (Seq.empty, Some(aValidMultipleAutoPriceMatchHolder.copy(statusId = ApmHotelStatus.PeakSeason)))

      override def validateEligibleRoom[T <: ApmRoomParameters](apmCommissionRoomParameters: T,
                                                                apmSetting: Option[ApmSettingHolder],
                                                                apmConfigs: Map[Int, ApmConfigHolder],
                                                                hotelCountryId: Long,
                                                                isExcludeQuarantineChannel: Boolean,
                                                                apmProgramId: Int,
                                                                apmProgramType: Int,
                                                                skipPackageValidateCommReductionFeatureExp: Boolean,
                                                                removeBedPaidExclusionFromApmExp: Boolean,
                                                                skipApmPriceAdjustmentForResellExp: Boolean,
                                                                enabledAiExp: Boolean,
                                                                excludeRateChannelFromApmExp: Boolean,
                                                                enabledApmArpPlusProgram: Boolean): Boolean =
        !isExcludeQuarantineChannel && skipPackageValidateCommReductionFeatureExp

      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams, aValidRoomIndex2 -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        8,
        Set.empty,
        aValidSupplierId,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map(1 -> Set(YplRateFenceHolder(origin = "th", cid = 1, language = 1))),
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(true)
      res.get(aValidRoomIndex2).map(_.isRoomEligible) should_== Some(true)
    }

    "calculateApmCommissionReductionResult correctly - calculating isRoomEligible correctly - status is invalid" in new TestScope {
      override val determineApmProgramSettingsResult =
        (Seq.empty, Some(aValidMultipleAutoPriceMatchHolder.copy(statusId = 2, programType = Some(2))))

      override def validateEligibleRoom[T <: ApmRoomParameters](apmCommissionRoomParameters: T,
                                                                apmSetting: Option[ApmSettingHolder],
                                                                apmConfigs: Map[Int, ApmConfigHolder],
                                                                hotelCountryId: Long,
                                                                isExcludeQuarantineChannel: Boolean,
                                                                apmProgramId: Int,
                                                                apmProgramType: Int,
                                                                skipPackageValidateCommReductionFeatureExp: Boolean,
                                                                removeBedPaidExclusionFromApmExp: Boolean,
                                                                skipApmPriceAdjustmentForResellExp: Boolean,
                                                                enabledAiExp: Boolean,
                                                                excludeRateChannelFromApmExp: Boolean,
                                                                enabledApmArpPlusProgram: Boolean): Boolean =
        !isExcludeQuarantineChannel && skipPackageValidateCommReductionFeatureExp

      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams, aValidRoomIndex2 -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        8,
        Set.empty,
        aValidSupplierId,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map(1 -> Set(YplRateFenceHolder(origin = "th", cid = 1, language = 1))),
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(false)
      res.get(aValidRoomIndex2).map(_.isRoomEligible) should_== Some(false)
    }

    "calculateApmCommissionReductionResult correctly - calculating isRoomEligible correctly - multipleAutoPriceMatchHolder is empty" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty, None)

      override def validateEligibleRoom[T <: ApmRoomParameters](apmCommissionRoomParameters: T,
                                                                apmSetting: Option[ApmSettingHolder],
                                                                apmConfigs: Map[Int, ApmConfigHolder],
                                                                hotelCountryId: Long,
                                                                isExcludeQuarantineChannel: Boolean,
                                                                apmProgramId: Int,
                                                                apmProgramType: Int,
                                                                skipPackageValidateCommReductionFeatureExp: Boolean,
                                                                removeBedPaidExclusionFromApmExp: Boolean,
                                                                skipApmPriceAdjustmentForResellExp: Boolean,
                                                                enabledAiExp: Boolean,
                                                                excludeRateChannelFromApmExp: Boolean,
                                                                enabledApmArpPlusProgram: Boolean): Boolean =
        !isExcludeQuarantineChannel && skipPackageValidateCommReductionFeatureExp

      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams, aValidRoomIndex2 -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        8,
        Set.empty,
        aValidSupplierId,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map(1 -> Set(YplRateFenceHolder(origin = "th", cid = 1, language = 1))),
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(false)
      res.get(aValidRoomIndex2).map(_.isRoomEligible) should_== Some(false)
    }

    "calculateApmCommissionReductionResult correctly - calculating isSupplierValidFlag correctly - multipleAutoPriceMatchHolder is empty" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty, None)
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams.copy(supplierId = aValidSupplierId),
            aValidRoomIndex2 -> aValidApmRoomParams.copy(supplierId = 9999999)),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.get(aValidRoomIndex).map(_.isSupplierValid) should_== Some(false)
      res.get(aValidRoomIndex2).map(_.isSupplierValid) should_== Some(false)
    }

    "calculateApmCommissionReductionResult correctly - calculating isSupplierValidFlag correctly - empty MAPM" in new TestScope {
      override val determineApmProgramSettingsResult = (Seq.empty, None)
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams.copy(supplierId = aValidSupplierId)),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List.empty,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.get(aValidRoomIndex).map(_.isSupplierValid) should_== Some(false)
    }

    "calculateApmCommissionReductionResult correctly - passing hard coded flags to validateEligibleRoom correctly" in new TestScope {

      override def calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent: Double,
        bookingDate: DateTime,
        hotelGmtOffset: Int,
        apmCommissionReductionEligibility: Seq[ApmCommissionReductionEligibilityHolder],
        isActiveOrSuspendedApmHotel: Boolean,
        supplierId: Int): Boolean = true
      override def validateEligibleRoom[T <: ApmRoomParameters](apmCommissionRoomParameters: T,
                                                                apmSetting: Option[ApmSettingHolder],
                                                                apmConfigs: Map[Int, ApmConfigHolder],
                                                                hotelCountryId: Long,
                                                                isExcludeQuarantineChannel: Boolean,
                                                                apmProgramId: Int,
                                                                apmProgramType: Int,
                                                                skipPackageValidateCommReductionFeatureExp: Boolean,
                                                                removeBedPaidExclusionFromApmExp: Boolean,
                                                                skipApmPriceAdjustmentForResellExp: Boolean,
                                                                enabledAiExp: Boolean,
                                                                excludeRateChannelFromApmExp: Boolean,
                                                                enabledApmArpPlusProgram: Boolean): Boolean =
        !isExcludeQuarantineChannel && skipPackageValidateCommReductionFeatureExp
      val res = calculateApmCommissionReductionResult(
        Map(aValidRoomIndex -> aValidApmRoomParams),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map(1 -> Set(YplRateFenceHolder(origin = "th", cid = 1, language = 1))),
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(true)
    }

    "calculateApmCommissionReductionResult correctly - validateEligibleRoom correctly - calculateIsApmCommissionDiscountEligibleV2 true" in new TestScope {

      override def calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent: Double,
        bookingDate: DateTime,
        hotelGmtOffset: Int,
        apmCommissionReductionEligibility: Seq[ApmCommissionReductionEligibilityHolder],
        isActiveOrSuspendedApmHotel: Boolean,
        supplierId: Int): Boolean = true

      override val validateEligibleRoomResult: Boolean = true

      val res = calculateApmCommissionReductionResult(
        Map(
          aValidRoomIndex -> aValidApmRoomParams,
        ),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map(1 -> Set(YplRateFenceHolder(origin = "th", cid = 1, language = 1))),
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(true)
    }

    "calculateApmCommissionReductionResult correctly - validateEligibleRoom correctly - calculateIsApmCommissionDiscountEligibleV2 true but EligibleRoom is false" in new TestScope {

      override def calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent: Double,
        bookingDate: DateTime,
        hotelGmtOffset: Int,
        apmCommissionReductionEligibility: Seq[ApmCommissionReductionEligibilityHolder],
        isActiveOrSuspendedApmHotel: Boolean,
        supplierId: Int): Boolean = true

      override val validateEligibleRoomResult: Boolean = false

      val res = calculateApmCommissionReductionResult(
        Map(
          aValidRoomIndex -> aValidApmRoomParams,
        ),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map(1 -> Set(YplRateFenceHolder(origin = "th", cid = 1, language = 1))),
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(false)
    }

    "calculateApmCommissionReductionResult correctly - validateEligibleRoom correctly - calculateIsApmCommissionDiscountEligibleV2 true with zero commission" in new TestScope {
      override val determineApmProgramSettingsResult =
        (Seq.empty, Some(aValidMultipleAutoPriceMatchHolder.copy(commissionDiscountPercent = 0d)))
      override val validateEligibleRoomResult: Boolean = true

      val res = calculateApmCommissionReductionResult(
        Map(
          aValidRoomIndex -> aValidApmRoomParams,
        ),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map(1 -> Set(YplRateFenceHolder(origin = "th", cid = 1, language = 1))),
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(false)
    }

    "calculateApmCommissionReductionResult correctly - validateEligibleRoom correctly - calculateIsApmCommissionDiscountEligibleV2 false" in new TestScope {

      override def calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent: Double,
        bookingDate: DateTime,
        hotelGmtOffset: Int,
        apmCommissionReductionEligibility: Seq[ApmCommissionReductionEligibilityHolder],
        isActiveOrSuspendedApmHotel: Boolean,
        supplierId: Int): Boolean = false
      override val validateEligibleRoomResult: Boolean = true

      val res = calculateApmCommissionReductionResult(
        Map(
          aValidRoomIndex -> aValidApmRoomParams,
        ),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map(1 -> Set(YplRateFenceHolder(origin = "th", cid = 1, language = 1))),
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(false)
    }

    "calculateApmCommissionReductionResult correctly - validateEligibleRoom correctly - commissionFences false" in new TestScope {

      override def calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent: Double,
        bookingDate: DateTime,
        hotelGmtOffset: Int,
        apmCommissionReductionEligibility: Seq[ApmCommissionReductionEligibilityHolder],
        isActiveOrSuspendedApmHotel: Boolean,
        supplierId: Int): Boolean = true
      override val validateEligibleRoomResult: Boolean = true

      val res = calculateApmCommissionReductionResult(
        Map(
          aValidRoomIndex -> aValidApmRoomParams,
        ),
        DateTime.parse("2023-02-02"),
        7,
        Set.empty,
        0,
        aValidHotelId,
        0,
        List("2023-02-03", "2023-02-02").map(DateTime.parse),
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = false,
        enableApmMultipleDiscount = false,
        excludeRateChannelFromApmExp = false,
        isApmFixEndDate = false,
        enabledApmArpPlusProgram = false,
        requestFences = Map.empty,
      )
      res.get(aValidRoomIndex).map(_.isRoomEligible) should_== Some(false)
    }

    "calculateIsApmCommissionDiscountMonthly correctly" in new TestScope {
      calculateIsApmCommissionDiscountMonthly(
        bookingDate = DateTime.parse("2021-01-01T05:00:00.000+07:00"),
        hotelGmtOffset = 0,
        apmCommissionReductionEligibility = Seq(
          ApmCommissionReductionEligibilityHolder(year = 2020, month = 12, isCommissionReductionApplied = false),
          ApmCommissionReductionEligibilityHolder(year = 2021, month = 1, isCommissionReductionApplied = true),
        ),
      ) should_=== false

      calculateIsApmCommissionDiscountMonthly(
        bookingDate = DateTime.parse("2021-01-01T05:00:00.000+07:00"),
        hotelGmtOffset = 6,
        apmCommissionReductionEligibility = Seq(
          ApmCommissionReductionEligibilityHolder(year = 2020, month = 12, isCommissionReductionApplied = false),
          ApmCommissionReductionEligibilityHolder(year = 2021, month = 1, isCommissionReductionApplied = true),
        ),
      ) should_=== true

      calculateIsApmCommissionDiscountMonthly(
        bookingDate = DateTime.parse("2021-01-01T08:00:00.000+07:00"),
        hotelGmtOffset = 0,
        apmCommissionReductionEligibility = Seq(
          ApmCommissionReductionEligibilityHolder(year = 2020, month = 12, isCommissionReductionApplied = true),
          ApmCommissionReductionEligibilityHolder(year = 2021, month = 1, isCommissionReductionApplied = false),
        ),
      ) should_=== false

      calculateIsApmCommissionDiscountMonthly(
        bookingDate = DateTime.parse("2021-01-01T08:00:00.000+07:00"),
        hotelGmtOffset = -10,
        apmCommissionReductionEligibility = Seq(
          ApmCommissionReductionEligibilityHolder(year = 2020, month = 12, isCommissionReductionApplied = true),
          ApmCommissionReductionEligibilityHolder(year = 2021, month = 1, isCommissionReductionApplied = false),
        ),
      ) should_=== true

      calculateIsApmCommissionDiscountMonthly(
        bookingDate = DateTime.parse("2021-01-01T08:00:00.000+07:00"),
        hotelGmtOffset = 0,
        apmCommissionReductionEligibility = Seq(
          ApmCommissionReductionEligibilityHolder(year = 2021, month = 12, isCommissionReductionApplied = true),
          ApmCommissionReductionEligibilityHolder(year = 2021, month = 1, isCommissionReductionApplied = false),
        ),
      ) should_=== false

      // No value returns true
      calculateIsApmCommissionDiscountMonthly(bookingDate = DateTime.parse("2021-01-01T00:00:00.000+07:00"),
                                              hotelGmtOffset = 0,
                                              apmCommissionReductionEligibility = Seq.empty) should_=== true

    }

    "calculateIsApmCommissionDiscountEligibleV2 correctly" in new TestScope {
      calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent = 1.0,
        bookingDate = DateTime.parse("2021-01-01T00:00:00.000+07:00"),
        hotelGmtOffset = 0,
        apmCommissionReductionEligibility = Seq.empty,
        isActiveOrSuspendedApmHotel = true,
        supplierId = DMC.YCS.value,
      ) should_=== true

      calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent = 1.0,
        bookingDate = DateTime.parse("2021-01-01T00:00:00.000+07:00"),
        hotelGmtOffset = 0,
        apmCommissionReductionEligibility = Seq.empty,
        isActiveOrSuspendedApmHotel = true,
        supplierId = DMC.YCS.value,
      ) should_=== true

      calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent = 1.0,
        bookingDate = DateTime.parse("2021-01-01T00:00:00.000+07:00"),
        hotelGmtOffset = 0,
        apmCommissionReductionEligibility = Seq.empty,
        isActiveOrSuspendedApmHotel = true,
        supplierId = DMC.MockSupply.value,
      ) should_=== false

      calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent = 1.0,
        bookingDate = DateTime.parse("2021-01-01T00:00:00.000+07:00"),
        hotelGmtOffset = 0,
        apmCommissionReductionEligibility = Seq.empty,
        isActiveOrSuspendedApmHotel = true,
        supplierId = DMC.HiltonByD.value,
      ) should_=== true

      calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent = 0.0,
        bookingDate = DateTime.parse("2021-01-01T00:00:00.000+07:00"),
        hotelGmtOffset = 0,
        apmCommissionReductionEligibility = Seq.empty,
        isActiveOrSuspendedApmHotel = true,
        supplierId = DMC.YCS.value,
      ) should_=== false

      calculateIsApmCommissionDiscountEligible(
        apmCommissionDiscountPercent = 1.0,
        bookingDate = DateTime.parse("2021-01-01T00:00:00.000+07:00"),
        hotelGmtOffset = 0,
        apmCommissionReductionEligibility = Seq.empty,
        isActiveOrSuspendedApmHotel = false,
        supplierId = DMC.YCS.value,
      ) should_=== false
    }
  }

}
