package com.agoda.papi.ypl.commission.apm.model

import com.agoda.papi.ypl.commission.apm.models.{ApmHotelStatus, ApmProgramType, MultipleAutoPriceMatchHolder}
import com.agoda.papi.ypl.commission.builder.MultipleAutoPriceMatchHolderBuilder
import org.joda.time.DateTime
import org.specs2.matcher.AnyMatchers
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.core.Fragments

class ApmModelsSpec extends SpecificationWithJUnit with AnyMatchers {
  "MultipleAutoPriceMatchHolder" should {
    val aValidMultiplePriceMatchHolder = MultipleAutoPriceMatchHolderBuilder(
      MultipleAutoPriceMatchHolder(1,
                                   None,
                                   0d,
                                   1,
                                   0d,
                                   1,
                                   DateTime.parse("2023-01-01"),
                                   None,
                                   Seq.empty,
                                   Seq.empty,
                                   Some(0)))

    "compute isActive correctly" in {
      aValidMultiplePriceMatchHolder.withStatusId(ApmHotelStatus.Active).build.isActive should_== true
      aValidMultiplePriceMatchHolder.withStatusId(ApmHotelStatus.Suspended).build.isActive should_== false
      aValidMultiplePriceMatchHolder.withStatusId(ApmHotelStatus.InActive).build.isActive should_== false
      aValidMultiplePriceMatchHolder.withStatusId(ApmHotelStatus.Experimental).build.isActive should_== false
      aValidMultiplePriceMatchHolder.withStatusId(ApmHotelStatus.ExperimentalV2).build.isActive should_== false
      aValidMultiplePriceMatchHolder.withStatusId(ApmHotelStatus.PeakSeason).build.isActive should_== false
    }

    "compute isActiveOrSuspendedApmHotel correctly" in {
      aValidMultiplePriceMatchHolder
        .withStatusId(ApmHotelStatus.Active)
        .build
        .isActiveOrSuspendedApmHotel should_== true
      aValidMultiplePriceMatchHolder
        .withStatusId(ApmHotelStatus.Suspended)
        .build
        .isActiveOrSuspendedApmHotel should_== true
      aValidMultiplePriceMatchHolder
        .withStatusId(ApmHotelStatus.InActive)
        .build
        .isActiveOrSuspendedApmHotel should_== false
      aValidMultiplePriceMatchHolder
        .withStatusId(ApmHotelStatus.Experimental)
        .build
        .isActiveOrSuspendedApmHotel should_== false
      aValidMultiplePriceMatchHolder
        .withStatusId(ApmHotelStatus.ExperimentalV2)
        .build
        .isActiveOrSuspendedApmHotel should_== false
      aValidMultiplePriceMatchHolder
        .withStatusId(ApmHotelStatus.PeakSeason)
        .build
        .isActiveOrSuspendedApmHotel should_== false
    }

    "compute isActiveAndEnableCommission correctly" in {
      Fragments(
        List(
          (ApmProgramType.Ai, ApmHotelStatus.Active, true),
          (ApmProgramType.Ai, ApmHotelStatus.InActive, false),
          (ApmProgramType.Ai, ApmHotelStatus.Suspended, true),
          (ApmProgramType.Arp, ApmHotelStatus.Experimental, true),
          (ApmProgramType.ArpV2, ApmHotelStatus.ExperimentalV2, true),
          (ApmProgramType.PeakSeason, ApmHotelStatus.PeakSeason, true),
          (ApmProgramType.AiCalendarView, ApmHotelStatus.Active, false),
          (ApmProgramType.AiCalendarView, ApmHotelStatus.Suspended, false),
          (ApmProgramType.AiCalendarView, ApmHotelStatus.InActive, false),
        ).map { case (programType, statusId, expected) =>
          s"programType is $programType and statusId is $statusId, result should be $expected" in {
            val holder = aValidMultiplePriceMatchHolder.withProgramType(programType).withStatusId(statusId).build

            holder.isActiveAndEnableCommission should_== expected
          }
        }: _*,
      )
    }
  }

}
