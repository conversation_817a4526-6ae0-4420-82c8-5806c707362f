package com.agoda.papi.ypl.models.builders.ypl

import api.routing.dsl.context.{FlowSnapshot, FlowStat}
import com.agoda.papi.ypl.models.context.{CompositeChannelContext, SurchargeDataServiceContext}
import com.agoda.papi.ypl.models.pricing.UspaCampaign
import com.agoda.papi.ypl.models.{SupplierId, YplContext, YplRequest, YplSupplierSetting, context}
import com.agoda.utils.flow.{CommonFlowContextMock, ExperimentContext}
import com.agoda.utils.monitoring.AggregateReporter
import models.flow.FlowHotelStatsContext

import scala.language.implicitConversions

case class YplContextMock(request: YplRequest,
                          override val experimentContext: ExperimentContext,
                          exchangeRateCtx: context.ExchangeRateContext,
                          compositeChannelContext: CompositeChannelContext,
                          surchargeDataServiceContext: SurchargeDataServiceContext,
                          hadoopContext: context.HadoopContext,
                          override val aggregateReporter: AggregateReporter,
                          supplierSetting: Map[SupplierId, YplSupplierSetting] = Map.empty,
                          holidayCalendarContext: context.RoomLinkageHolidayCalendarContext,
                          pulseCampaignMetaCtx: context.PulseCampaignMetaContext,
                          masterChannelCategoryContext: context.MasterChannelCategoryContext,
                          eligibleUspaCampaigns: List[UspaCampaign] = List.empty,
                          addFeesInExclusivePrice: Boolean = false)
  extends CommonFlowContextMock
    with YplContext {
  override def overrideYplRequest(request: YplRequest): YplContext = this.copy(request = request)

  override def isSnapshotEnabled: Boolean = false

  override def capture(snapshot: FlowSnapshot): Unit = {}

  override def isStatsEnabled: Boolean = false

  override def logStat(stat: FlowStat): Unit = {}

  override def getStats: List[FlowStat] = List.empty

  override val flowHotelStatsContext: FlowHotelStatsContext = new FlowHotelStatsContext()
}

object YplContextMock {
  object Implicits {
    implicit def traitToBuilder(yplContext: YplContext): YplContextBuilder = YplContextBuilder(traitToMock(yplContext))

    implicit def traitToMock(yplContext: YplContext): YplContextMock = YplContextMock(
      request = yplContext.request,
      experimentContext = yplContext.experimentContext,
      exchangeRateCtx = yplContext.exchangeRateCtx,
      compositeChannelContext = yplContext.compositeChannelContext,
      surchargeDataServiceContext = yplContext.surchargeDataServiceContext,
      hadoopContext = yplContext.hadoopContext,
      aggregateReporter = yplContext.aggregateReporter,
      supplierSetting = yplContext.supplierSetting,
      holidayCalendarContext = yplContext.holidayCalendarContext,
      pulseCampaignMetaCtx = yplContext.pulseCampaignMetaCtx,
      masterChannelCategoryContext = yplContext.masterChannelCategoryContext,
      eligibleUspaCampaigns = yplContext.eligibleUspaCampaigns,
    )
    implicit def mockToTrait(yplContextMock: YplContextMock): YplContext = yplContextMock
  }
}
