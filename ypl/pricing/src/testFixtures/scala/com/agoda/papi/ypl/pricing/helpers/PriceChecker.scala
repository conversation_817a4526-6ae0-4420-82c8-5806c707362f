package com.agoda.papi.ypl.pricing.helpers

import org.specs2.mutable.SpecificationWithJUnit

/**
  * Created by ppattanapoon on 7/23/15.
  */
object PriceChecker extends SpecificationWithJUnit {
  def compareAmountValue(value1: Double, value2: Double) =
    if (Math.round(value1 * 100) == Math.round(value2 * 100))
      Math.round(value1 * 100) must_== (Math.round(value2 * 100))
    else if (Math.ceil(value1 * 100) == Math.ceil(value2 * 100))
      Math.ceil(value1 * 100) must_== (Math.ceil(value2 * 100))
    else if (Math.floor(value1 * 100) == Math.floor(value2 * 100))
      Math.floor(value1 * 100) must_== (Math.floor(value2 * 100))
    else value1 must_== (value2)
}
