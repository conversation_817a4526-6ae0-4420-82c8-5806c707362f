package com.agoda.papi.ypl.pricing.mocks

import com.agoda.papi.ypl.services.OriginManager

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 7/5/2016 AD.
  */
trait OriginManagerMock extends OriginManager {
  def getOriginByCid(cid: Option[Int]): Option[String] = if (cid.contains(99999)) Some("AU") else None
  def getOriginByCidAndHotelCountry(cid: Option[Int], hotelCountry: Option[String]): Option[String] = None
}
