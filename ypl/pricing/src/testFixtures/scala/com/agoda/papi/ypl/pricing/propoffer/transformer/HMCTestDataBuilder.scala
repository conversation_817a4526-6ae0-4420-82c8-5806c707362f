package com.agoda.papi.ypl.pricing.propoffer.transformer

import com.agoda.papi.ypl.pricing.propoffer.transformer.tax.{HMCTaxHolder, TaxType}
import com.agoda.protobuf.hotelcontext._
import com.agoda.protobuf.masterhotelcontext.poc.{HmcTax, HmcTaxDetails, HmcTaxes}
import com.agoda.protobuf.masterhotelcontext.{MasterHotelContext, MasterHotelContextExtension, MasterHotelInfo}
import com.agoda.supply.calc.proto.ApplyType
import com.agoda.supply.calc.proto.TaxPrototypeLevelsV2.TaxPrototypeLevel

trait HMCTestDataBuilder {

  lazy val taxDetailsInput = HmcTaxDetails(
    hmcTaxesIdentiferMapping = Map(
      1 -> HmcTax(
        taxId = 4,
        startDate = java.time.LocalDate.of(2022, 10, 10),
        endDate = java.time.LocalDate.of(2022, 11, 10),
        applyTo = "PB",
        applyType = ApplyType.Mandatory,
        isFee = true,
        isAmount = true,
        value = 3.00,
        isTaxable = true,
        taxPrototypeId = 12658,
      ),
      2 -> HmcTax(
        taxId = 4,
        startDate = java.time.LocalDate.of(2010, 10, 10),
        endDate = java.time.LocalDate.of(2022, 11, 10),
        applyTo = "PB",
        applyType = ApplyType.Mandatory,
        isFee = true,
        isAmount = true,
        value = 3.00,
        isTaxable = true,
        taxPrototypeId = 12658,
        taxCurrency = Some("THB"),
      ),
    ),
    ycsTaxes = Map("2-0" -> HmcTaxes(hmcTaxV1Identifiers = Seq(1), hmcTaxV2Identifiers = Seq(2))),
  )

  lazy val aValidHMCMasterHotelContext = MasterHotelContext.defaultInstance
    .withMasterHotelInfo(MasterHotelInfo.defaultInstance.withTaxType(1))
    .withMasterHotelContextExtension(MasterHotelContextExtension.defaultInstance.withTaxDetails(taxDetailsInput))

  lazy val aValidChildHotelContext = HotelContext.defaultInstance

  lazy val aValidHotelTax1 = HmcTax(
    taxId = 1,
    startDate = java.time.LocalDate.of(2010, 10, 10),
    endDate = java.time.LocalDate.of(2022, 11, 10),
    applyTo = "PRPN",
    applyType = ApplyType.Mandatory,
    isFee = false,
    isAmount = true,
    value = 10.00,
    isTaxable = true,
    taxPrototypeId = 1,
    taxPrototypeLevels = Seq(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)),
  )

  lazy val aValidHotelTax2 = aValidHotelTax1.copy(
    taxId = 2,
    applyTo = "PB",
    applyType = ApplyType.Optional,
    isFee = false,
    isAmount = false,
    value = 15.00,
    taxPrototypeId = 2,
  )

  lazy val aValidHotelTax3 = aValidHotelTax1.copy(
    taxId = 3,
    applyTo = "PAPB",
    applyType = ApplyType.Mandatory,
    isFee = true,
    isAmount = true,
    value = 5,
    taxPrototypeId = 0,
    taxPrototypeLevels = Seq.empty,
  )

  lazy val aValidHmcTaxDetail = HmcTaxDetails(
    hmcTaxesIdentiferMapping = Map(
      1 -> aValidHotelTax1,
      2 -> aValidHotelTax2,
      3 -> aValidHotelTax1.copy(taxId = 3, taxPrototypeId = 3),
      4 -> aValidHotelTax2.copy(taxId = 4, taxPrototypeId = 4),
    ),
    ycsTaxes = Map("0-0" -> HmcTaxes(hmcTaxV1Identifiers = Seq(1, 2), hmcTaxV2Identifiers = Seq(3, 4))),
    thirdPartyPullTaxes = Map("0-0" -> HmcTaxes(hmcTaxV1Identifiers = Seq(2), hmcTaxV2Identifiers = Seq(4))),
  )

  lazy val aValidHMCTaxHolder = HMCTaxHolder.default.copy(
    hotelTaxType = TaxType.ComprehensiveTaxHotelLevel,
    taxDetails = aValidHmcTaxDetail,
  )
}
