package com.agoda.papi.ypl.pricing.mocks

import com.agoda.papi.pricing.pricecalculation.api.PriceBreakdownCalculatorInterface
import com.agoda.papi.pricing.pricecalculation.models.request._
import com.agoda.papi.pricing.pricecalculation.models.response.Calculation
import com.agoda.papi.pricing.pricecalculation.pricing.{
  CommissionCalculatorImpl,
  PriceBreakdownCalculatorImpl,
  TaxBreakdownCalculatorImpl,
}

// TODO: VEL-2070 remove, temporary to improve mutation as A and B side are return the same result
trait MockPriceBreakdownCalculator extends PriceBreakdownCalculatorInterface {
  var callCounter: Int = 0

  override def calculate(request: PriceCalculationRequest): Calculation = {
    val priceBreakdownCalculator: PriceBreakdownCalculatorInterface =
      new PriceBreakdownCalculatorImpl(new TaxBreakdownCalculatorImpl(), new CommissionCalculatorImpl())
    callCounter += 1
    priceBreakdownCalculator.calculate(request)
  }
}
