package com.agoda.papi.ypl.pricing.mocks

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.ypl.models.pricing.proto.PromotionEntry
import com.agoda.papi.ypl.models.{PromotionTypeId, YplContext}
import com.agoda.papi.ypl.pricing.promotions.PromotionEntryValidation

trait PromotionEntryValidationMock extends PromotionEntryValidation {

  override def isValidByOverridingCustomerSegment(
    promotion: PromotionEntry,
    pulseCampaignMetadataByPromotionTypeId: Map[PromotionTypeId, PulseCampaignMetadata],
    customerOriginCountryCode: String,
    customerLanguageId: Int,
  )(implicit ctx: YplContext): Boolean = false
}
