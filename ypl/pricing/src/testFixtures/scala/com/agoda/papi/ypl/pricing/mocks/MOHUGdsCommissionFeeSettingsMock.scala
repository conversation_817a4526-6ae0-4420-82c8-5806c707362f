package com.agoda.papi.ypl.pricing.mocks

import com.agoda.configuration.Settings
import com.agoda.papi.ypl.settings.MOHUGdsCommissionFeeSettings
import com.typesafe.config.Config

object MOHUGdsCommissionFeeSettingsMock extends Settings[MOHUGdsCommissionFeeSettings]("mohu-gds-commission-fee") {
  override def fromSubConfig(c: Config): MOHUGdsCommissionFeeSettings =
    MOHUGdsCommissionFeeSettings(Set(-1), Set(1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009))

  def apply(): MOHUGdsCommissionFeeSettings =
    MOHUGdsCommissionFeeSettings(Set(-1), Set(1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009))

  val setting: MOHUGdsCommissionFeeSettings = this()
}
