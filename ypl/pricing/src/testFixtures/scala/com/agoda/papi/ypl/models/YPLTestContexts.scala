package com.agoda.papi.ypl.models

import com.agoda.adp.messaging.message.Message
import com.agoda.commons.models.pricing.{ExchangeRate, PulseCampaignMetadata}
import com.agoda.papi.ypl.models.builders.ypl.{YplContextBuilderImplicits, YplContextMock, YplDataExample}
import com.agoda.papi.ypl.models.context._
import com.agoda.utils.flow.CommonExperimentContextMock
import com.agoda.utils.monitoring.AggregateReporter
import org.joda.time.DateTime

trait YPLTestContexts extends YplDataExample with YplContextBuilderImplicits {
  lazy val aValidZExperimentContext = new CommonExperimentContextMock {}

  lazy val aValidYplContext: YplContextMock = YplContextMock(
    aValidYplRequest,
    aValidZExperimentContext,
    aValidExchangeRateContext,
    aValidCompositeChannelContext,
    aValidSurchargeDataServiceContext,
    aValidHadoopContext,
    AggregateReporter.noop(),
    Map.empty,
    aValidHolidayCalendarContext,
    aValidPulseCampaignMetaContext(List.empty),
    aValidMasterChannelCategoryContext,
  )

  object YplContext {
    def apply(request: YplRequest,
              compositeChannelContext: CompositeChannelContext = noneCompositeChannelCtx,
              exchangeRateCtx: ExchangeRateContext = noneExchangeRateCtx,
              aggregateReporter: AggregateReporter = AggregateReporter.noop(),
              holidayCalendarCtx: RoomLinkageHolidayCalendarContext = noHolidayCalendarCtx,
              pulseCampaignMetaContext: PulseCampaignMetaContext = noPulseCampaignCtx,
              masterChannelCategoryContext: MasterChannelCategoryContext = noMasterChannelCategoryContext,
              surchargeDataServiceContext: SurchargeDataServiceContext = aValidSurchargeDataServiceContext,
              addFeesInExclusivePrice: Boolean = false): YplContextMock = YplContextMock(
      request = request,
      experimentContext = forcedFromRequestExperimentContext(request),
      exchangeRateCtx = exchangeRateCtx,
      compositeChannelContext = compositeChannelContext,
      surchargeDataServiceContext = surchargeDataServiceContext,
      hadoopContext = noopHadoopCtx,
      aggregateReporter = aggregateReporter,
      holidayCalendarContext = holidayCalendarCtx,
      pulseCampaignMetaCtx = pulseCampaignMetaContext,
      masterChannelCategoryContext = masterChannelCategoryContext,
      supplierSetting = builders.YplSupplierSetting.mapping,
      addFeesInExclusivePrice = addFeesInExclusivePrice,
    )
  }

  val noOpExperimentContext = new CommonExperimentContextMock {}

  def forceBExperimentContext(experiments: Experiment*) = new CommonExperimentContextMock {
    override protected val experimentsMap: Map[String, Char] = experiments.map(e => e -> 'B').toMap
  }

  def forceAExperimentContext(experiments: Experiment*) = new CommonExperimentContextMock {
    override protected val experimentsMap: Map[String, Char] = experiments.map(e => e -> 'A').toMap
  }

  def forceAllBExperimentsContext() = new CommonExperimentContextMock {
    override def getForcedExperimentVariant(e: String): Option[Char] = Some('B')
  }

  def forceAllAExperimentsContext() = new CommonExperimentContextMock {
    override def getForcedExperimentVariant(e: String): Option[Char] = Some('A')
  }

  def forcedFromRequestExperimentContext(req: YplRequest) = new CommonExperimentContextMock {
    override protected val experimentsMap: Map[String, Char] = req.experiments.map(e => e.name -> e.variant).toMap
  }

  def forcedExperimentFromList(list: List[YplExperiment]) = new CommonExperimentContextMock {
    override protected val experimentsMap: Map[String, Char] = list.map(e => e.name -> e.variant).toMap
  }

  val noneExchangeRateCtx = new ExchangeRateContext {
    override def getExchangeRate(from: Currency, to: Currency): Option[ExchangeRate] = None
  }

  val noneCompositeChannelCtx = new CompositeChannelContext {
    override def getCompositeChannelId(baseChannelId: Int, stackedChannelIds: Set[Int]): Int = baseChannelId

    override def toCompositeChannelIds(channelId: Int): Set[Int] = Set(channelId)
  }

  val noopHadoopCtx = new HadoopContext {
    override def logHadoopMessage(msg: Message): Unit = {}
  }

  val noHolidayCalendarCtx = new RoomLinkageHolidayCalendarContext {
    override def getHolidayCalendar: Map[DateTime, Boolean] = Map.empty
  }

  val noPulseCampaignCtx = new PulseCampaignMetaContext {
    override def getPulseCampaignSetting(
      promotionTypeIds: List[PromotionTypeId]): Map[PromotionTypeId, PulseCampaignMetadata] = Map.empty
  }

  val noMasterChannelCategoryContext = new MasterChannelCategoryContext {
    override def getMasterChannelCategoryMap(): Map[ChannelId, ChannelCategoryId] = Map.empty

    override def getMasterChannelCategoryMapWithEcoDealsOverride: Map[ChannelId, ChannelCategoryId] = Map.empty
  }
}

object YPLTestContexts extends YPLTestContexts
