package com.agoda.papi.ypl.pricing

import com.agoda.papi.enums.room.RateType.{SellExclusive, SellInclusive}
import com.agoda.papi.enums.room._
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes}
import com.agoda.papi.pricing.pricecalculation.models.CommonProcessingFeeBreakdown
import com.agoda.papi.ypl.commission.{
  AgxCommissionHolder,
  CommissionDailyHolder,
  CommissionHolder,
  CommissionUtils,
  ProtobufCommissionHolder,
}
import com.agoda.papi.ypl.models.api.request.{YplAGXCommission, YplChildren, YplOccInfo}
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.pricing.{RoomOccupancy, YplPrice}
import com.agoda.papi.ypl.models.{
  Adults,
  Child,
  PropOfferOccupancy,
  Rooms,
  YPLTestDataBuilders,
  YplMasterChannel,
  YplRequest,
  YplRoomEntry,
}
import org.joda.time.DateTime
import org.scalatest.matchers.should.Matchers._
import org.specs2.mutable._

/**
  * Created by psingh on 7/10/15.
  */
trait PriceTestUtil extends YPLTestDataBuilders { this: Specification =>

  implicit class MatchPrices(rooms: Option[YplRoomEntry]) {
    lazy val prices = rooms.map(r => r.dailyPrices).getOrElse(Map.empty)
    lazy val pEntries = prices.flatMap(_._2.prices)

    def matchPrices(chargeType: ChargeType, occ: Int, quantity: Int, value: Double, chargeOption: ChargeOption) = {

      val cRoomPrices = pEntries.filter(_.chargeType == chargeType)
      cRoomPrices.map(_.occupancy) must containAllOf(Seq(occ))
      cRoomPrices.map(_.quantity) must containAllOf(Seq(quantity))
      val p = cRoomPrices.map(_.value)
      p must haveSize(1)
      p.headOption.getOrElse(0d) should equal(value +- 0.0001)

      cRoomPrices.map(_.chargeOption) must containAllOf(Seq(chargeOption))
    }

    def matchSize(checkIn: DateTime, checkOut: DateTime, priceCount: Int, occ: Option[RoomOccupancy] = None) = {
      val days = dates(checkIn, checkOut)
      prices must haveSize(days.length)
      prices must haveKeys(days: _*)
      pEntries must haveSize(priceCount)

      if (occ.isDefined) {
        rooms.get.occEntry must_== occ.get
      }
    }
  }

  def matchRoomEntry(result: YplRoomEntry, from: YplRoomEntry): Unit = {
    result.currency must_== from.currency
    result.cxlCode must_== from.cxlCode

    result.roomTypeId must_== from.roomTypeId
    result.channel must_== from.channel
    result.cxlCode must_== from.cxlCode
    result.isBreakFastIncluded must_== from.isBreakFastIncluded
    result.remainingRooms must_== from.remainingRooms
    result.currency must_== from.currency
    result.rateType must_== from.rateType
    result.processingFees must_== from.processingFees
    result.isAllowCombinePromotion must_== from.isAllowCombinePromotion
  }

  def request(checkIn: DateTime, checkOut: DateTime, adult: Adults, child: Child, rooms: Rooms) = {
    val occ = YplOccInfo(Some(adult), Some(YplChildren(child, None, Map.empty)), Some(rooms))
    YplRequest(searchId = "random-string",
               checkIn = checkIn,
               checkOut = checkOut,
               occ = occ,
               supplierFeatures = aValidSupplierFeatures,
               whitelabelSetting = aValidwhitelabelSetting)
  }

  def request(checkIn: DateTime, checkOut: DateTime, adult: Adults, children: List[Option[Int]], rooms: Rooms) = {
    val occ = YplOccInfo(Some(adult), Some(YplChildren(children)), Some(rooms))
    YplRequest(searchId = "random-string",
               checkIn = checkIn,
               checkOut = checkOut,
               occ = occ,
               supplierFeatures = aValidSupplierFeatures,
               whitelabelSetting = aValidwhitelabelSetting)
  }

  def dates(checkIn: DateTime, checkOut: DateTime) =
    Iterator.iterate(checkIn)(_.plusDays(1)).takeWhile(_.isBefore(checkOut)).toList

  implicit class RoomEntryToPrice(roomEntry: YplRoomEntry) {
    def toOccPrice(chargeType: ChargeType, occ: Int = 0) = roomEntry.dailyPrices.head._2.prices
      .find(p => p.chargeType == chargeType && p.occupancy == occ)
      .map(_.value)
      .getOrElse(0.0)
  }

  def createRoomEntry(checkIn: DateTime,
                      los: Int,
                      maxOcc: Int,
                      maxExtraBed: Int,
                      maxChild: Int,
                      occPrices: Map[Int, Double],
                      extraBedPrice: Option[Double] = None,
                      extraAdultPrice: Option[Double] = None,
                      extraChildPrice: Option[Double] = None,
                      dailyPriceComm: Double = 20.0,
                      agxComm: Double = 20.0,
                      priceEntryComm: Option[Double] = None,
                      rateCategory: RateCategoryEntry = aValidRateCategoryEntry,
                      rateType: RateType = RateType.SellInclusive,
                      roomQty: Int = 0,
                      extraBedQty: Int = 0,
                      taxes: Map[TaxIDWithProTypeID, TaxValue] = Map.empty,
                      supplierContractedCommission: Option[Double] = None,
                      skipCommissionHolderCalculation: Boolean = false,
                      protobufCommission: Option[Double] = None,
                      propertyRateType: Option[RateType] = None): YplRoomEntry = {

    val checkOut = checkIn.plusDays(los)
    val dRange = dates(checkIn, checkOut)

    val dayPrices = dRange.map { d =>
      val prices = occPrices
        .map(p =>
          PriceEntry(d,
                     ChargeType.Room,
                     ChargeOption.Mandatory,
                     ApplyType.PB.entryName,
                     p._1,
                     p._2,
                     roomQty,
                     rateLoadedPrice = p._2))
        .toList
      val extraBed = extraBedPrice
        .map(p =>
          List(
            PriceEntry(
              d,
              ChargeType.ExtraBed,
              ChargeOption.Optional,
              ApplyType.PB.entryName,
              0,
              p,
              extraBedQty,
              rateLoadedPrice = p,
              rateType = propertyRateType.getOrElse(RateType.Unknown),
            )))
        .getOrElse(Nil)

      (d, DailyPrice(d, taxes, false, prices ++ extraBed))
    }.toMap

    val (agxCommToUse, protobufCommToUse) =
      if (protobufCommission.isDefined) {
        (agxComm, protobufCommission.get)
      } else if (rateType == SellExclusive || rateType == SellInclusive || skipCommissionHolderCalculation) {
        (agxComm, (Math.max(0, dailyPriceComm - agxComm)))
      } else {
        val convertToMarkUpDailyPriceComm = CommissionUtils.convertToCommission(dailyPriceComm)
        val agxCommToUse = agxComm * convertToMarkUpDailyPriceComm / dailyPriceComm
        val protobufCommToUse =
          (Math.max(0, dailyPriceComm - agxComm)) * (convertToMarkUpDailyPriceComm / dailyPriceComm)
        (agxCommToUse, protobufCommToUse)
      }
    val agxCommissionHolder = AgxCommissionHolder.default
      .withPayAsYouGoCommission(agxCommToUse)
      .withIsApplyAgx(true)
      .withIsAgxEligibleRateChannel(true)
      .build
    val protobufCommissionHolder =
      ProtobufCommissionHolder.default.withContracedCommission(Math.max(0, protobufCommToUse)).build
    val dailyCommissionHolder = CommissionDailyHolder.default
      .withProtobufCommissionHolder(protobufCommissionHolder)
      .withAGXCommissionHolder(agxCommissionHolder)
      .build
    val commissionHolder = CommissionHolder.default
      .withDaily(Map(checkIn -> dailyCommissionHolder))
      .withSupplierContractedCommission(supplierContractedCommission)
      .build

    YplRoomEntry(
      roomTypeId = 1234L,
      masterRoomTypeId = 1234L,
      channel = YplMasterChannel.RTL,
      cxlCode = "1D100P",
      isBreakFastIncluded = false,
      remainingRooms = 10,
      currency = "INR",
      rateType = rateType,
      processingFees = 3.0,
      isAllowCombinePromotion = false,
      roomType = RoomTypeEntry(1234L, maxOcc, maxExtraBed, maxChild, false, 0, 0),
      dailyPrices = dayPrices,
      occEntry = RoomOccupancy(0, 0),
      originalRateType = rateType,
      rateCategory = rateCategory,
      commissionHolder = commissionHolder,
      propOfferOccupancy = PropOfferOccupancy(0, 0),
    )
  }
  // scalastyle:off
  def createPrice(date: DateTime = DateTime.now(),
                  quantity: Int = 0,
                  chargeType: ChargeType = ChargeType.Room,
                  applyType: ApplyType = ApplyType.PRPB,
                  chargeOption: ChargeOption = ChargeOption.Mandatory,
                  refId: Int = 0,
                  netExclusive: Double = 0.0,
                  tax: Double = 0.0,
                  fee: Double = 0.0,
                  margin: Double = 0.0,
                  processingFee: Double = 0.0,
                  discount: Double = 0.0,
                  downliftAmount: Double = 0.0,
                  percent: Double = 0.0,
                  taxBreakDown: List[CommonTaxBreakdown] = Nil,
                  dailyTaxes: DailyTaxes = DailyTaxes(Nil, isCleanedUpHospitalityTax = true),
                  refMargin: Double = 0.0,
                  refProcessingFee: Double = 0.0,
                  isConfigProcessingFee: Boolean = false,
                  value: Double = 0.0,
                  upliftedSellIn: Option[Double] = None,
                  processingFeeBreakdown: Option[CommonProcessingFeeBreakdown] = None,
                  downliftExAmount: Option[Double] = None): YplPrice = YplPrice(
    roomNumber = None,
    date,
    quantity,
    chargeType,
    subChargeType = SubChargeType.None,
    applyType,
    chargeOption,
    refId,
    netExclusive = netExclusive,
    tax,
    fee,
    margin,
    processingFee,
    discount,
    downliftAmount,
    percent,
    taxBreakDown,
    dailyTaxes,
    refMargin,
    refProcessingFee,
    isConfigProcessingFee,
    value,
    upliftedSellIn,
    processingFeeBreakdown,
    Map.empty,
    downliftExAmount = None,
    referenceCommissionPercent = 0d,
    agxCommission = YplAGXCommission.noAgxCommission,
  )
  // scalastyle:on

}
