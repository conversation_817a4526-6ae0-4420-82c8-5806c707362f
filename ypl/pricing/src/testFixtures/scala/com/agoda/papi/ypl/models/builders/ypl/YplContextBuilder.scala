package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.context.HadoopContext
import com.agoda.papi.ypl.models.pricing.UspaCampaign
import com.agoda.papi.ypl.models.{SupplierId, YplContext, YplRequest, YplSupplierSetting, context}
import com.agoda.utils.flow.ExperimentContext
import com.agoda.utils.monitoring.AggregateReporter

case class YplContextBuilder(build: YplContextMock) extends Builder[YplContext] with YplContextBuilderImplicits {
  def withRequest(yplRequest: YplRequest): B = build.copy(request = yplRequest)
  def withExperimentContext(ctx: ExperimentContext): B = build.copy(experimentContext = ctx)
  def withExchangeRateContext(ctx: context.ExchangeRateContext): B = build.copy(exchangeRateCtx = ctx)
  def withCompositeChannelContext(ctx: context.CompositeChannelContext): B = build.copy(compositeChannelContext = ctx)
  def withSupplierSetting(setting: Map[SupplierId, YplSupplierSetting]): B = build.copy(supplierSetting = setting)
  def withMasterChannelCategoryContext(masterChannelCategoryContext: context.MasterChannelCategoryContext): B =
    build.copy(masterChannelCategoryContext = masterChannelCategoryContext)
  def withReporter(reporter: AggregateReporter): B = build.copy(aggregateReporter = reporter)
  def withHadoopContext(hadoopContext: HadoopContext): B = build.copy(hadoopContext = hadoopContext)
  def withEligibleUspaCampaigns(campaigns: List[UspaCampaign]): B = build.copy(eligibleUspaCampaigns = campaigns)
  type B = YplContextBuilder
}

trait YplContextBuilderImplicits {
  implicit def toContextBuilder(build: YplContextMock): YplContextBuilder = YplContextBuilder(build)
  implicit def toContext(builder: YplContextBuilder): YplContextMock = builder.build
}
