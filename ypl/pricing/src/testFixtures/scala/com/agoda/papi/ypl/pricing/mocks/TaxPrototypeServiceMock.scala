package com.agoda.papi.ypl.pricing.mocks

import com.agoda.papi.enums.room.RateType
import com.agoda.papi.pricing.pricecalculation.models.tax.DailyTaxes
import com.agoda.papi.ypl.models.{SupplierId, YPLHotel, YplContext}
import com.agoda.papi.ypl.models.pricing.proto.Commission
import com.agoda.papi.ypl.services.TaxPrototypeService

trait TaxPrototypeServiceMock extends TaxPrototypeService {
  def assumeTaxValue(rateType: RateType,
                     value: Double,
                     commission: Commission,
                     dailyTaxes: DailyTaxes,
                     assumeChannelDiscount: Double,
                     isCleanedUpHospitalityTax: Boolean): DailyTaxes = dailyTaxes
  def correctTaxPrototypeLevel(yplHotel: YPLHotel, supplierIdToContextMap: Map[SupplierId, YplContext]): YPLHotel =
    yplHotel
}
