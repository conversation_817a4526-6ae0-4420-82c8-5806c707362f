package com.agoda.papi.ypl.pricing.mocks

import org.mockito.Mockito
import org.mockito.stubbing.Stubber
import org.scalatestplus.mockito.MockitoSugar

//see https://stackoverflow.com/questions/50600203/mockito-doreturn-ambiguous-reference-to-overloaded-definition/50968089#50968089
trait MockitoHelper extends MockitoSugar {
  def doReturn(toBeReturned: Any): Stubber = Mockito.doReturn(toBeReturned, Nil: _*)
}
