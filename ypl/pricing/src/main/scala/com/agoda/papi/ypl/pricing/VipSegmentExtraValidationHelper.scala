package com.agoda.papi.ypl.pricing

import com.agoda.papi.ypl.models.enums.VipLevelType

// Temporary extra rule will remove soon
object VipSegmentExtraValidationHelper {
  def isDiamondCustomerWithPlatinumSegment(
    segmentVipLevel: VipLevelType,
    userVipLevel: VipLevelType,
  ): Boolean = userVipLevel.value == VipLevelType.DIAMOND.value &&
    segmentVipLevel.value == VipLevelType.PLATINUM.value
}
