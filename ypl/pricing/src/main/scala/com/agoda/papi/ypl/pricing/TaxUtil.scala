package com.agoda.papi.ypl.pricing

import com.agoda.finance.tax.models.TaxPrototypeLevel
import com.agoda.finance.tax.services.tax.{TaxValidator => CommonTaxValidator}
import com.agoda.finance.tax.utils.CommonUtils
import com.agoda.papi.ypl.models.settings.CommonTaxSettings
import com.agoda.papi.ypl.models.{Currency, HotelMeta, YplContext}
import com.agoda.supply.calc.proto.{Tax, TaxV2}

object TaxUtil {

  val dateFormat = "yyyy-MM-dd"

  // TODO: this function just need tax ID, why not change copy it and change to Seq[Int]
  def filterTax[T](taxes: Map[Int, T], hotelCountryId: Long, bookingOrigin: Option[String]): Map[Int, T] = {
    val isIsraeliDomesticBooking = CommonUtils.isIsraeliDomesticBooking(hotelCountryId.toInt, bookingOrigin)
    filterApplicableTax(taxes = taxes, hotelCountryId = hotelCountryId)(isEnableIsraelFilter = !isIsraeliDomesticBooking)
  }

  def filterApplicableTax[T](taxes: Map[Int, T], hotelCountryId: Long)(isEnableIsraelFilter: Boolean): Map[Int, T] =
    taxes.filter { t =>
      val taxId = t._2 match {
        case tx: Tax => tx.taxId
        case tx: TaxV2 => tx.taxId
      }
      CommonTaxValidator.isApplicable(
        taxTypeId = taxId,
        hotelCountryId = hotelCountryId,
        isEnableIsraelFilter = isEnableIsraelFilter,
      )
    }

  def filterSingleTaxApplicable(taxId: Int, hotelCountryId: Long, bookingOrigin: Option[String]): Boolean = {
    val isIsraeliDomesticBooking = CommonUtils.isIsraeliDomesticBooking(hotelCountryId.toInt, bookingOrigin)
    CommonTaxValidator.isApplicable(
      taxTypeId = taxId,
      hotelCountryId = hotelCountryId,
      isEnableIsraelFilter = !isIsraeliDomesticBooking,
    )
  }

  def is3rdPartySupplier(supplierId: Int)(implicit ctx: YplContext): Boolean =
    ctx.supplierSetting.get(supplierId).exists(_.isThirdParty)

  def calculateLevelByCurrency(hotelCurrencyCode: String,
                               countryCurrencyCode: String,
                               taxLevelInCountryCurrency: List[TaxPrototypeLevel],
                               isAmount: Boolean)(implicit ctx: YplContext): List[TaxPrototypeLevel] =
    if (hotelCurrencyCode == countryCurrencyCode) taxLevelInCountryCurrency
    else {
      // need to convert currency from countryCurrencyCode to hotelCurrencyCode
      ctx.exchangeRateCtx
        .getExchangeRate(countryCurrencyCode, hotelCurrencyCode)
        .map { e =>
          taxLevelInCountryCurrency.map { l =>
            val rateStartConverted = e.toReqRate(l.rateStart)
            val rateEndConverted = e.toReqRate(l.rateEnd)
            if (isAmount) {
              val taxValueConverted = e.toReqRate(l.taxValue)
              l.copy(
                rateStart = rateStartConverted,
                rateEnd = rateEndConverted,
                taxValue = taxValueConverted,
              )
            } else {
              l.copy(rateStart = rateStartConverted, rateEnd = rateEndConverted)
            }
          }
        }
        .getOrElse(List.empty)
    }

  def isEnableTaxV2Exp(hasTaxV2: Boolean, hotel: HotelMeta, supplierId: Int)(implicit ctx: YplContext): Boolean = {
    def isExperimentB(experimentOpt: Option[String]): Boolean = experimentOpt.exists(ctx.experimentContext.isUserB)

    val activeStateExperimentOpt = getStateLevelActiveExperimentIfExists(hotel.stateId, ctx.request.commonTaxSettingsOpt)

    // For state exp, check hasTaxV2 to fall back to V1.
    if (activeStateExperimentOpt.isDefined && isExperimentB(activeStateExperimentOpt) && hasTaxV2) {
      true
    } else {
      // For country exp, no fall back to V1. No need to check hasTaxV2.
      val activeCountryExperimentOpt =
        getCountryLevelActiveExperimentIfExists(hotel.countryId.toInt, ctx.request.commonTaxSettingsOpt)
      isExperimentB(activeCountryExperimentOpt)
    }
  }

  private def getStateLevelActiveExperimentIfExists(stateIdOpt: Option[Int],
                                                    commonTaxSettingsOpt: Option[CommonTaxSettings]): Option[String] =
    commonTaxSettingsOpt match {
      case Some(commonTaxSettings) =>
        stateIdOpt.flatMap(stateId => commonTaxSettings.usState2ExperimentMapping.get(stateId))
      case None => None
    }

  private def getCountryLevelActiveExperimentIfExists(countryId: Int,
                                                      commonTaxSettingsOpt: Option[CommonTaxSettings]): Option[String] =
    commonTaxSettingsOpt match {
      case Some(commonTaxSettings) => commonTaxSettings.country2ExperimentMapping.get(countryId)
      case None => None
    }

  def convertCurrency(amount: Double, from: Currency, to: Currency)(implicit ctx: YplContext): Double =
    if (from.toUpperCase == to.toUpperCase) amount
    else ctx.exchangeRateCtx.getExchangeRate(from, to).map(e => e.toReqRate(amount)).getOrElse(amount)
}
