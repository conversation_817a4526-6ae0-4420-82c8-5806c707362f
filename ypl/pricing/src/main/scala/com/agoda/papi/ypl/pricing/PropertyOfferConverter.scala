package com.agoda.papi.ypl.pricing

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.enums.hotel.{PaymentModel, RateModel, TaxType}
import com.agoda.papi.enums.request.StackDiscountOption
import com.agoda.papi.enums.room.{PaymentChannel, _}
import com.agoda.papi.ypl.commission.ApmCommissionUtils._
import com.agoda.papi.ypl.commission._
import com.agoda.papi.ypl.commission.apm.models.{
  AutoPriceMatchKeyEntry,
  AutoPriceMatchPriceInfo,
  MultipleAutoPriceMatchHolder,
}
import com.agoda.papi.ypl.fencing.{AgxFencing, FencedChannelRate, LanguageFencing, WholesaleFencing}
import com.agoda.papi.ypl.models.Wholesale._
import com.agoda.papi.ypl.models.api.request.{YplAGXCommissionAdjustment, YplAGXCommissionAdjustmentWrapper}
import com.agoda.papi.ypl.models.consts.{BookingType, CancellationCode, ExternalDataFields, Language, Origin}
import com.agoda.papi.ypl.models.enums.{OccupancyModel, _}
import com.agoda.papi.ypl.models.hotel.AgodaAgencyFeatures
import com.agoda.papi.ypl.models.pricing.proto.{
  CustomerSegment,
  HotelOccupancySetup,
  HotelTaxInfo,
  PerPersonPrice,
  CheckInInformation => YPLCheckInInformation,
  ChildRate => YPLChildRate,
  _,
}
import com.agoda.papi.ypl.models.pricing.{BookingPriceBreakdown, RoomOccupancy}
import com.agoda.papi.ypl.models.proto.enums.SourceTypes
import com.agoda.papi.ypl.models.proto.{PeriodIntervalInput, Restriction, RestrictionEntryModel}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{ChildAgeRange => MetaChildAgeRange, _}
import com.agoda.papi.ypl.pricing.SimpleDateUtils._
import com.agoda.papi.ypl.pricing.promotions.PromotionConstant
import com.agoda.papi.ypl.pricing.promotions.PromotionConstant.NOCC_PROMOTION_TYPES
import com.agoda.papi.ypl.pricing.propoffer.transformer.tax.HMCTaxHolder
import com.agoda.papi.ypl.pricing.propoffer.transformer.{
  ChannelDiscountTransformer,
  HotelOccupancySetupTransformer,
  TaxTransformer,
  YcsRoomCapacityTransformer,
}
import com.agoda.papi.ypl.pricing.rates.RateCategoryHelper
import com.agoda.papi.ypl.pricing.supplier.SupplierRoomFilterService
import com.agoda.papi.ypl.proto.SupplierFeaturesHelper
import com.agoda.papi.ypl.utils.UspaUtils
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.SupplierRateInfo
import com.agoda.supply.calc.proto
import com.agoda.supply.calc.proto.{
  ChannelRate,
  Promotion,
  PropertyOffer,
  RoomRateCategory,
  StayPackageType,
  Commission => ProtoCommission,
  InventoryType => _,
  Tax => _,
  _,
}
import com.agoda.utils.collection.SumImplicits._
import org.joda.time.DateTimeConstants._
import org.joda.time.{DateTime, DateTimeZone, LocalTime => JodaLocalTime}

import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.time.{LocalDate, LocalTime, ZoneId}
import scala.collection.breakOut
import scala.collection.generic.FilterMonadic
import scala.collection.mutable.ListBuffer
import com.agoda.supply.calc.proto.{Tax => ProtoTax, TaxV2 => ProtoTaxV2}
import com.agoda.utils.flow.PropertyContext
import PropertyOfferRoomRateExtensions.PropertyOfferRoomRateOps
import com.agoda.papi.pricing.pricecalculation.models.tax.Tax

import _root_.models.consts.ABTest

case class ParentRateCategoryKey(parentRcId: Long, roomTypeId: RoomTypeId, channelId: ChannelId)

case class ParentRateCategoryValue(pa: RoomRateCategory, ch: ChannelRate)

// scalastyle:off
trait PropertyOfferConverter
  extends OTAConverter
    with RequestRelatedValidation
    with RateCategoryHelper
    with HotelOccupancySetupTransformer
    with YcsRoomCapacityTransformer
    with ChannelDiscountTransformer
    with TaxTransformer {

  private val DEFAULT_MAXADV = 999
  private val PGPN = "PGPN"
  private val PRPN = "PRPN"

  private[pricing] def filterRequiredPropOfferChannels(
    ph: PropertyOffer,
    dispatchChannels: YplDispatchChannels): FilterMonadic[RoomRateCategory, Seq[RoomRateCategory]] = {
    val requiredChannels =
      ph.rateRepurpose.flatMap(rate => Set(rate.sourceChannel, rate.referenceChannel, rate.targetChannel))
    ph.roomRates.withFilter { r =>
      r.channelRates.exists { c =>
        dispatchChannels.contains(YplMasterChannel(c.channelId)) || requiredChannels.contains(c.channelId)
      }
    }
  }

  private[pricing] def validHourlyBookingCutoffTime(checkIn: DateTime,
                                                    bookingDate: DateTime,
                                                    hourlyAvailability: Option[HourlyAvailability],
                                                    hotelGMT: GmtOffset): Boolean = hourlyAvailability match {
    case Some(hr) => validateSameDayBookingCutoffTime(
        checkIn = checkIn,
        bookingDate = bookingDate,
        bookingCutOffTime = hr.hourlyBookingCutoffTime.map(_.getMillisOfDay),
        hotelGMT = hotelGMT,
      )
    case _ => true
  }

  // Pre filter to reduce number of room rates
  private[pricing] def preFilterRoomRate(ph: PropertyOffer, dispatchChannels: YplDispatchChannels, hotelInfo: HotelMeta)(
    implicit ctx: YplContext): FilterMonadic[RoomRateCategory, Seq[RoomRateCategory]] =
    filterRequiredPropOfferChannels(ph = ph, dispatchChannels = dispatchChannels).withFilter { rateCategory =>
      rateCategory.channelRates.exists { channelRate =>
        val bookingRestriction: Option[Restriction] = buildRestriction(
          buildRateCategoryBookingRestriction(
            bookRes = ph.bookingRestrictions.get(channelRate.bookingRestrictionIdentifier),
            isAllowedCustomerSegmentsRestriction = ctx.request.whitelabelSetting.isCustomerSegmentValidationEnabled,
          ),
        )
        val isValidHourlyBookingCutoffTime: Boolean = validHourlyBookingCutoffTime(
          checkIn = ctx.request.checkIn,
          bookingDate = ctx.request.bookingDate,
          hourlyAvailability = channelRate.hourlyAvailability,
          hotelGMT = hotelInfo.gmtOffset,
        )
        val baseFences = ctx.request.baseFences(YplMasterChannel(channelRate.channelId))
        val isJPRichContent = hotelInfo.countryCode == "JP" && rateCategory.stayPackageType.isRichContent
        val supplierId = ph.supplyInfo.map(_.supplierId).getOrElse(0)

        val isBabyNotAllowInRateCategory = validateBabyChildRateNotAllowed(channelRate, ph, hotelInfo)
        val isJASORatePlanNameAvailable =
          if (isJPRichContent) {
            isRateCategoryAvailableInValidLanguage(ctx, hotelInfo, rateCategory)
          } else true

        // AKA KASO
        val shouldShowKoreanCustomizedOffer: Boolean =
          if (hotelInfo.countryCode.equalsIgnoreCase(
              Origin.Korea) && rateCategory.stayPackageType.isRichContent && !rateCategory.escapesApprovalStatus.isRejected) {
            isRateCategoryAvailableInValidLanguage(ctx, hotelInfo, rateCategory) && !ctx.request.isAllMseTraffic
          } else {
            true
          }

        val fixValidationForPaymentChannels =
          ctx.experimentContext.isUserB(YplExperiments.FIX_VALIDATION_FOR_PAYMENT_CHANNELS)
        val isPaymentMethodValid =
          if (fixValidationForPaymentChannels) {
            hasValidPaymentMethod(channelRate, ctx.request.whitelabelSetting)
          } else true

        val isValidateSellablePaymentChannels =
          ctx.experimentContext.isUserB(YplExperiments.VALIDATE_SELLABLE_PAYMENT_CHANNEL)
        val isPaymentChannelSellable =
          if (isValidateSellablePaymentChannels) {
            hasSellablePaymentChannels(channelRate, ctx.request.whitelabelSetting, rateCategory.inventoryType)
          } else true

        val isSupplierFenceSatisfied =
          if (ctx.experimentContext.isUserB(YplExperiments.DMC_HARDCODING_REMOVAL)) {
            supplierId != ctx.request.whitelabelSetting.mainSupplier
          } else {
            supplierId != DMC.JTBWL
          }

        val isSellingExternalSuppliersForJtbEnabled =
          (ctx.request.whitelabelSetting.iSellingExternalSuppliersForJtbEnabled
          && isSupplierFenceSatisfied
          && ctx.experimentContext.isUserB(YplExperiments.ENABLE_SELLING_DIFFERENT_SUPPLIERS_FOR_JTB))

        val passExternalSupplierCriteria =
          !isSellingExternalSuppliersForJtbEnabled || isEligibleToBeSoldOnJtbWhiteLabels(channelRate,
                                                                                         ctx.request.whitelabelSetting,
                                                                                         supplierId,
                                                                                         hotelInfo.countryCode)

        val passRoomBasedOnPastBooking =
          !ctx.experimentContext.isPropertyB(PropertyContext(hotelInfo.hotelId, hotelInfo.cityId, hotelInfo.countryId),
                                             _root_.models.consts.ABTest.GHOST_ROOM_FILTERING) ||
          checkRoomBasedOnBooking(hotelInfo, rateCategory, ctx.request.minBookingCountForSuperAgg)

        validateBookingRestriction(
          restriction = bookingRestriction,
          hotelGMT = hotelInfo.gmtOffset,
          fences = baseFences,
          supplierId,
        ) && isValidHourlyBookingCutoffTime && !isBabyNotAllowInRateCategory &&
        isJASORatePlanNameAvailable && shouldShowKoreanCustomizedOffer && isPaymentMethodValid && isPaymentChannelSellable && passExternalSupplierCriteria && passRoomBasedOnPastBooking
      }
    }

  private def checkRoomBasedOnBooking(hotelInfo: HotelMeta,
                                      rateCategory: RoomRateCategory,
                                      minBookingCountForSuperAgg: Option[Seq[YplMinBookingCountForSuperAgg]]) = {
    val roomOption = hotelInfo.enabledRoom.get(rateCategory.roomTypeId)
    minBookingCountForSuperAgg match {
      case None => true
      case Some(minBookingCount) =>
        val bookingCount =
          roomOption.map(r => r.roomBookings.map(booking => booking.inDays -> booking.count)).getOrElse(Seq.empty).toMap

        val isBookingCountValid = minBookingCount.forall { minBooking =>
          bookingCount.get(minBooking.inDays).forall(_ >= minBooking.count)
        }

        bookingCount.nonEmpty && isBookingCountValid

    }
  }

  private def isRateCategoryAvailableInValidLanguage(
    ctx: YplContext,
    hotelInfo: HotelMeta,
    rateCategory: RoomRateCategory) = hotelInfo.jasoRateCategoryLanguage
    .getOrElse(rateCategory.rateCategoryId.toInt, Set())
    .exists(languageId => languageId.equals(ctx.request.cInfo.language) || languageId.equals(Language.English))

  private def validateBabyChildRateNotAllowed(channelRate: ChannelRate, po: PropertyOffer, hotelInfo: HotelMeta)(
    implicit ctx: YplContext): Boolean = {
    implicit val request = ctx.request

    val isYCS = po.supplyInfo.exists(_.supplierId == DMC.YCS)
    val reqOcc = hotelInfo.getOccupancy
    val childRate = channelRate.childRate.map(_.childPricing).getOrElse(Seq.empty)
    val isChildRateEnabled = channelRate.childRate.exists(_.isChildRateEnabled)
    val hasBabyChildRate = childRate.exists(_.childRateTypeId == ChildRateType.Baby.value)

    isYCS && hotelInfo.countryCode == "JP" && isChildRateEnabled && !hasBabyChildRate && (reqOcc.hasBaby || reqOcc
      .hasBabyInChildAgeRange(hotelInfo.childAgeRanges))
  }

  private[pricing] def tagRoomRateCategoryWithFence(propOffer: PropertyOffer,
                                                    hotelMeta: HotelMeta,
                                                    roomRateCategory: RoomRateCategory,
                                                    channelRate: ChannelRate,
                                                    isAboCidToOriginMappingEnabled: Boolean)(implicit
    ctx: YplContext): FencedChannelRate = {
    val baseFences = ctx.request.baseFences(YplMasterChannel(roomRateCategory.channelRates.head.channelId))

    val fences = SupplierRoomFilterService.fenceSupplierRoom(propOffer.supplyInfo.map(_.supplierId).getOrElse(0),
                                                             channelRate,
                                                             baseFences,
                                                             hotelMeta)
    val finalFences = filterRateFenceByCustomerSegment(propOffer.bookingRestrictions,
                                                       fences,
                                                       channelRate,
                                                       isAboCidToOriginMappingEnabled)
    FencedChannelRate(channelRate, finalFences)
  }

  private[pricing] def overrideRoomWithSupplierFeatures(wlFeature: Option[Feature],
                                                        yplRoom: YplRoomEntry,
                                                        paymentModel: PaymentModel,
                                                        paymentChannels: List[PaymentChannel])(implicit
    r: YplContext): YplRoomEntry = {
    val yplRequest = r.request
    wlFeature match {
      case Some(feature) => SupplierFeaturesHelper.applySupplierFeatures(
          yplRoom,
          feature,
          paymentModel,
          paymentChannels,
        )(yplRequest)
      case _ => yplRoom
    }
  }

  def convert(po: PropertyOffer,
              oriHInfo: HotelMeta,
              @deprecated("Use `dispatchChannelsPerFence` instead") dispatchChannels: YplDispatchChannels,
              dispatchChannelsPerFence: Map[YplRateFence, YplDispatchChannels])(implicit
    ctx: YplContext): Option[YplHotelEntryModel] = {
    implicit val request = ctx.request

    // Not Flat B
    val isApplyYcsRateForking = ctx.experimentContext.isUserB(YplExperiments.YCS_RATE_FORKING)
    val isMORPNoCcNegativeTest = AgencyCommissionUtils.hasMorpNegativeTestRateChannel(dispatchChannels)

    // TODO: we should return empty response when supplier info is not present. process wrong supplier id is also risky
    val supplierId = po.supplyInfo.map(_.supplierId).getOrElse(0)

    // one PropertyOffer per supplier
    val wlFeature = request.supplierFeatures.features.get(supplierId)
    val hInfo =
      if (isMORPNoCcNegativeTest && oriHInfo.agencyFeatures.nonEmpty) oriHInfo.copy(agencyFeatures =
        oriHInfo.agencyFeatures.map(_.copy(agencyBookingType = BookingType.MerchantBooking)))
      else oriHInfo

    val reqOcc = hInfo.getOccupancy
    val hotelCurrency = getHotelCurrency(po)
    val countryCurrency = hInfo.countryCurrency
    val isYCS = supplierId == DMC.YCS
    val isJTBWL = supplierId == DMC.JTBWL
    val isPromotionBlocked = isPromotionBlockEnabled(supplierId)
    val isDMCHardcodingRemovalEnabled = ctx.experimentContext.isUserB(YplExperiments.DMC_HARDCODING_REMOVAL)
    val isGetDynamicOccupancyModel =
      if (isDMCHardcodingRemovalEnabled) wlFeature.exists(_.dynamicOccupancyModel) else isJTBWL

    // TODO: we should not write correct occupancyModel for supplier, and stop hardcode or default it here.
    val occupancyModel =
      if (isYCS || isGetDynamicOccupancyModel) OccupancyModel.getFromValue(po.occupancyModel.value)
      else OccupancyModel.Full
    val isBcom = po.supplyInfo.map(_.supplierId).contains(DMC.BCOM)
    val parentRcMap: Map[ParentRateCategoryKey, ParentRateCategoryValue] = buildParentRateCategoryMap(
      po.roomRates,
    )
    val hotelPaymentModel = PaymentModel.getPaymentModel(po.paymentMode.value)

    // Please cross check with PO and Direct Connect as this logic is sharing
    // this boolean is a control if we should read child rate daily used for YCS/direct connect
    // JP migrate to use child per rate category like JTBWL already

    val hotelTimeZone = DateTimeZone.forOffsetHours(hInfo.gmtOffset)
    val fireDrillProto = FireDrillProtoConversionHelper.propertyOfferFireDrillConverter(po, hotelTimeZone)

    val apmApprovalPriceIdByKeyPair = getApprovalPriceIdByKeyPair(po.apmApprovalPriceMap)
    val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] =
      buildAutoPriceMatchInfo(
        po.apmRateInfo,
        request.checkIn,
        hInfo.hotelId,
        apmApprovalPriceIdByKeyPair,
      )
    val shouldBuildMORPCommissionHolder =
      AgencyCommissionUtils.shouldBuildMORPCommissionHolder(hInfo.agencyFeatures, isYCS)

    val hotelOccupancySetup: Option[HotelOccupancySetup] = buildHotelOccupancySetup(po)
    implicit val connectionType: ConnectionType = ctx.connectionType(supplierId)
    val isHMCTaxExpOn = enableHMCTaxExp(supplierId, connectionType)

    // TODO: build tax holder at hotel level (in DFAPI) and pass it here
    val hmcTaxHolder =
      if (isHMCTaxExpOn) {
        HMCTaxHolder(hInfo.hmcMasterHotelContext.masterHotelContext)
      } else {
        HMCTaxHolder.default
      }

    val origin = ctx.request.cInfo.origin
    val hotelCountryId = oriHInfo.countryId
    val applicableTaxes = TaxUtil.filterTax(po.taxes, hotelCountryId, origin)
    val applicableTaxesV2 = TaxUtil.filterTax(po.taxesV2, hotelCountryId, origin)

    val isAboCidToOriginMappingEnabled = ctx.experimentContext.isPropertyB(
      PropertyContext(hInfo.hotelId, hInfo.cityId, hInfo.countryId),
      ABTest.ABO_CID_TO_ORIGIN_MAPPER,
    )

    val roomEntries = preFilterRoomRate(po, dispatchChannels, hInfo).flatMap { roomRateCate =>
      val roomEntry = buildPropOfferRoomEntry(
        po = po,
        hInfo = hInfo,
        roomRateCategory = roomRateCate,
        isBcomFixTaxAmountApplyToPB = isBcom,
        hotelCurrency = hotelCurrency,
        countryCurrency = countryCurrency,
        isYCS = isYCS,
        hotelPaymentModel = hotelPaymentModel,
        parentRcMap = parentRcMap,
        isJTBWL = isJTBWL,
        wlFeature = wlFeature,
        fireDrillProto = fireDrillProto,
        dispatchChannels = dispatchChannels,
        autoPriceMatchInfo = autoPriceMatchInfo,
        shouldBuildMORPCommissionHolder = shouldBuildMORPCommissionHolder,
        hmcTaxHolder = hmcTaxHolder,
        applicableTaxes = applicableTaxes,
        applicableTaxesV2 = applicableTaxesV2,
        isAboCidToOriginMappingEnabled = isAboCidToOriginMappingEnabled,
      )(
        isApplyYcsRateForking = isApplyYcsRateForking,
      )
      roomEntry
    }.toList

    // TODO merge isBookable into preFilterRoomRate after all Hotel Price (OTA) is integrated
    val filteredRooms = roomEntries.filter(_.isBookable)
    val filteredSupplierRooms = SupplierRoomFilterService.filterSupplierRooms(supplierId, filteredRooms)
    val isOTASupplier = if (isDMCHardcodingRemovalEnabled) wlFeature.exists(_.isOTASupplier) else isJTBWL
    // TODO: HMC supplier tax integration
    Some(
      YplHotelEntryModel(
        hotelId = po.masterHotelId,
        supplierId = supplierId,
        paymentModel = PaymentModel.getPaymentModel(po.paymentMode.value),
        rooms = filteredSupplierRooms,
        occupancyModel = occupancyModel,
        taxInfo = buildSupplierTax(po,
                                   isBcom,
                                   hInfo,
                                   hotelCurrency,
                                   countryCurrency,
                                   applicableTaxes,
                                   applicableTaxesV2,
                                   supplierId),
        rateModel = RateModel.New,
        surchargeRateType = RateType.getFromValue(po.surchargeRateLoadType.value),
        metaData = hInfo,
        dispatchChannels = dispatchChannels,
        dispatchChannelsPerFence = dispatchChannelsPerFence,
        ratePlanLanguage = None,
        rateReutilizations = po.rateRepurpose.map(rr =>
          YPLRateReutilizationEntry(
            sourceChannel = YplMasterChannel(rr.sourceChannel),
            referenceChannel = YplMasterChannel(rr.referenceChannel),
            targetChannel = YplMasterChannel(rr.targetChannel),
            discountType = rr.discountType,
            flatChannelDiscount = rr.channelDiscount,
            minAdvPurchase = Some(rr.minAdvPurchase).filter(_ >= 0),
            maxAdvPurchase = Some(rr.maxAdvPurchase).filter(_ != DEFAULT_MAXADV),
          )),
        reqOcc = reqOcc,
        lengthOfStay = po.lengthOfStay,
        supplierSourceType =
          po.supplyInfo.map(s => SourceTypes.getSourceType(s.supplySource.value)).filter(_ != SourceTypes.Unknown),
        // filterNot as the same logic from SupplierTTLServiceImpl
        expiresAt = Option(po.expiresAt).filterNot(_ == -1L),
        isOTASupplier = if (isOTASupplier) None else Some(true),
        supplierContractedCommission = getSupplierContractedCommission(po),
        stackChannelDiscountInfo = buildStackedChannelInfo(po.stackedChannelInfo),
        agencyNoccSetting = None,
        autoPriceMatchInfo = autoPriceMatchInfo,
        autoPriceMatchIdsHash = Some(po.apmRateApprovalPriceIdsHash),
        // For back ward compatible, bookingCutoffTime should be Local Time of the hotel time zone, not BKK, nor UTC
        // This field is validated in DF side, which the current code choose to offset the booking date
        // from BKK timezone to hotel timezone
        bookingCutoffTime = po.bookingCutOffTime.map(t => new JodaLocalTime(t.hours, t.minutes, t.seconds)),
        retailGlobalContractedCommission = getRetailGlobalContractedCommission(po.commissions),
        fireDrillProto = fireDrillProto,
        hotelOccupancySetup = hotelOccupancySetup,
        channelLookUpByRateCategoryAndRoomTypeMap =
          ChannelLookUpBuildHelper.createChannelLookUpByRateCategoryAndRoomTypePairMap(
            po,
            isPromotionBlocked,
            ChannelLookUpBuildHelper.createOverridePromotionIdsByRateCategoryIdRoomTypeIdChannelIdPair(roomEntries)),
      ))
  }

  private[pricing] def getSupplierContractedCommission(po: PropertyOffer) = po.supplyInfo.collectFirst {
    case s if s.contractedCommission > 0 => s.contractedCommission
  }

  private[pricing] def getApprovalPriceIdByKeyPair(
    apmApprovalPriceMapOpt: Option[ApmApprovalPriceMap]): ApmApprovalPriceIdByKeyPair = apmApprovalPriceMapOpt
    .map { apmApprovalPriceMap =>
      apmApprovalPriceMap.entries.flatMap { kv =>
        kv.apmApprovalPriceKey.map { case ApmApprovalPriceKey(roomTypeId, Some(stayDate), occupancy, _) =>
          ApmApprovalPriceKeyPair(occupancy, stayDate.toDateTime, roomTypeId, kv.apmChannelId) -> kv.apmApprovalPriceId
        }
      }.toMap
    }
    .getOrElse(Map.empty)

  private[pricing] def buildAutoPriceMatchInfo(apmRateInfo: Seq[ApmRateInfo],
                                               checkIn: DateTime,
                                               hotelId: HotelId,
                                               apmApprovalPriceIdByKeyPair: ApmApprovalPriceIdByKeyPair)
    : Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = {
    val emptyChannelId = 0
    val sourceAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] =
      apmRateInfo match {

        case apmRateInfo if apmRateInfo.isEmpty =>
          Map.empty[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]]

        case _ => apmRateInfo
            .flatMap { apmInfo =>
              val roomTypeId = apmInfo.roomTypeId
              apmInfo.apmPrices.flatMap { apmPrices =>
                apmPrices.stayDates.flatMap { stayDates =>
                  (stayDates.start to stayDates.end).flatMap { day =>
                    val stayDate = checkIn.plusDays(day)
                    apmPrices.apmOccupancyPrices.flatMap { occPrice =>
                      occPrice.occupancies.flatMap { occupancyRange =>
                        (occupancyRange.start to occupancyRange.end).flatMap { occ =>
                          occPrice.apmChannelPrices match {
                            case apmChannelPrices if apmChannelPrices.isEmpty =>
                              val approvalId = apmApprovalPriceIdByKeyPair
                                .getApprovalPriceIdOpt(occ, stayDate, roomTypeId, emptyChannelId)
                                .getOrElse {
                                  logger.warn(getApmApprovalIDNotFoundMsg(hotelId, occ, stayDate, roomTypeId))
                                  0L
                                }
                              Seq(
                                AutoPriceMatchKeyEntry(roomTypeId, occ, None) -> (stayDate, AutoPriceMatchPriceInfo(
                                  occPrice.sellInclusiveAmount,
                                  roomTypeId,
                                  approvalId,
                                  None)))
                            case _ => occPrice.apmChannelPrices.map { apmChannelPrice =>
                                val channelId = apmChannelPrice.apmChannelId
                                val approvalId = apmApprovalPriceIdByKeyPair
                                  .getApprovalPriceIdOpt(occ, stayDate, roomTypeId, channelId)
                                  .getOrElse {
                                    logger.warn(getApmApprovalIDNotFoundMsg(hotelId, occ, stayDate, roomTypeId))
                                    0L
                                  }
                                if (channelId == emptyChannelId) {
                                  AutoPriceMatchKeyEntry(roomTypeId, occ, None) -> (stayDate, AutoPriceMatchPriceInfo(
                                    apmChannelPrice.sellInclusiveAmount,
                                    roomTypeId,
                                    approvalId,
                                    None))
                                } else {
                                  AutoPriceMatchKeyEntry(roomTypeId,
                                                         occ,
                                                         Some(channelId)) -> (stayDate, AutoPriceMatchPriceInfo(
                                    apmChannelPrice.sellInclusiveAmount,
                                    roomTypeId,
                                    approvalId,
                                    Some(channelId)))
                                }
                              }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
            .groupBy(_._1)
            .map { case (autoPriceMatchKeyEntry, v) => autoPriceMatchKeyEntry -> v.map(_._2).toMap }(collection.breakOut)
      }
    sourceAutoPriceMatchInfo
  }

  def buildParentRateCategoryMap(rcList: Seq[RoomRateCategory]): Map[ParentRateCategoryKey, ParentRateCategoryValue] = {
    val parentIDs = rcList.filter(_.parentRateCategoryId != -1L).map(_.parentRateCategoryId).toSet
    val filteredRoomRateCategories = rcList.filter { r =>
      val isParent = parentIDs.contains(r.rateCategoryId)
      val isResell = r.channelRates.exists(_.externalData.exists(_.key == ExternalDataFields.ResellSourceBookingId))
      isParent && !isResell
    }
    filteredRoomRateCategories.flatMap { rc =>
      rc.channelRates.map { ch =>
        // Parent RC IDs
        ParentRateCategoryKey(rc.rateCategoryId, rc.roomTypeId, ch.channelId) -> ParentRateCategoryValue(rc, ch)
      }
    }.toMap
  }

  private[pricing] def getApmApprovalIDNotFoundMsg(hotelId: HotelId,
                                                   occupancy: Int,
                                                   stayDate: StayDate,
                                                   roomTypeId: RoomTypeId): String = s"approval price ID is not found, " +
    s"hotelID = ${hotelId}, " +
    s"stayDate = $stayDate, " +
    s"roomTypeID = $roomTypeId, " +
    s"occupancy = $occupancy"

  private def fallbackParentRateCategory(isYcs: Boolean,
                                         parentRcMap: Map[ParentRateCategoryKey, ParentRateCategoryValue],
                                         pa: RoomRateCategory) = parentRcMap.collectFirst {
    case (k, v) if isYcs && k.parentRcId == pa.parentRateCategoryId && k.roomTypeId == pa.roomTypeId => v
  }

  private[pricing] def buildPropOfferRoomEntry(
    po: PropertyOffer,
    hInfo: HotelMeta,
    roomRateCategory: RoomRateCategory,
    isBcomFixTaxAmountApplyToPB: Boolean,
    applicableTaxes: Map[Int, ProtoTax],
    applicableTaxesV2: Map[Int, ProtoTaxV2],
    hotelCurrency: Option[String] = None,
    countryCurrency: Option[String] = None,
    isYCS: Boolean,
    hotelPaymentModel: PaymentModel,
    parentRcMap: Map[ParentRateCategoryKey, ParentRateCategoryValue] = Map.empty,
    isJTBWL: Boolean = false,
    wlFeature: Option[Feature] = None,
    fireDrillProto: Option[FireDrillProto] = None,
    dispatchChannels: YplDispatchChannels = YplDispatchChannels(Set.empty, Set.empty),
    autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map.empty,
    shouldBuildMORPCommissionHolder: Boolean = true,
    hmcTaxHolder: HMCTaxHolder = HMCTaxHolder.default,
    childAgeRanges: List[ChildAgeRange] = Nil,
    isAboCidToOriginMappingEnabled: Boolean = false,
  )(isApplyYcsRateForking: Boolean)(implicit ctx: YplContext): Seq[YplRoomEntry] = {
    implicit val request = ctx.request
    val reqOcc = hInfo.getOccupancy
    val supplierId = po.supplyInfo.map(_.supplierId).getOrElse(0)

    // For some reason full occ is set as RoomOccupancy(0,0) in ycs reqOcc.getRoomOccupancy()
    val occupancyModel = OccupancyModel.getFromValue(po.occupancyModel.value)

    val roomLinkage = hInfo.enabledRoom
      .get(roomRateCategory.roomTypeId)
      .flatMap(RoomLinkageHelper.getHrRoomLinkagePropOffer(roomRateCategory.roomTypeId, _, po, supplierId))
    val hrRemainingRooms = roomLinkage.map(_.remainingRooms).getOrElse(0)
    val isHrEnableOnRateCategory = !roomRateCategory.isClosedForLinkage

    val (overrideChannelRates, linkedRoomTypeCode) =
      if (isHrEnableOnRateCategory) {
        (roomRateCategory.channelRates.map { channelRate =>
           channelRate.copy(remainingRooms = Math.max(hrRemainingRooms, channelRate.remainingRooms))
         },
         roomLinkage.map(_.dmcRoomId))
      } else (roomRateCategory.channelRates, None)

    val channelsWithAllotment =
      overrideChannelRates.withFilter(hasAllotment(_, roomRateCategory, reqOcc, hInfo.ignoreRequestedNumberOfRoomsForNha))
    val fencedChannelsWithAllotment = channelsWithAllotment
      .map(channelRate =>
        tagRoomRateCategoryWithFence(po, hInfo, roomRateCategory, channelRate, isAboCidToOriginMappingEnabled))
      .withFilter(_.fences.nonEmpty)
      // group by fences by AGX
      .flatMap(fencedChannelRate =>
        AgxFencing.groupByAgx(hInfo.hotelId, fencedChannelRate, request.agxCommissionAdjustmentFences))
      // further group by fences by Wholesale based on the result above
      .flatMap(fencedChannelRate =>
        WholesaleFencing.groupByWholesale(fencedChannelRate, hInfo.wholesaleMetaDataByRateFence))
      .flatMap(fencedChannelRate => LanguageFencing.groupByLanguage(fencedChannelRate))

    val isLastMinuteBooking = hInfo.isLastMinuteBooking(supplierId, request.bookingDate, request.checkIn)

    fencedChannelsWithAllotment.flatMap { fencedChannelRate =>
      val channelRate = fencedChannelRate.channelRate
      val parentRC: Option[ParentRateCategoryValue] = parentRcMap
        .get(
          ParentRateCategoryKey(roomRateCategory.parentRateCategoryId,
                                roomRateCategory.roomTypeId,
                                channelRate.channelId))
        .orElse(fallbackParentRateCategory(isYCS, parentRcMap, roomRateCategory))
      val inventoryType: InventoryType = InventoryType.getFromValue(roomRateCategory.inventoryType.value)
      val parentChannelRate = parentRC.map(_.ch)
      val channel = YplMasterChannel(channelRate.channelId)
      val (cxlCode, _) = getCxlLoadedFromProtobuf(getPropOfferCxlCode(channelRate, po))

      // Will be removed after exp RC-2470 is taken
      val allPromotionIdentifierForRoomRateSet: Set[Int] =
        po.getAllPromotionIdentifier(roomRateCategory.rateCategoryId, roomRateCategory.roomTypeId)
      val availablePromotions = getAvailablePromotions(channelRate,
                                                       cxlCode,
                                                       po,
                                                       allPromotionIdentifierForRoomRateSet,
                                                       channel.toChannelIds.toSet)
      val hasNoCCPromotions = availablePromotions.exists(p => NOCC_PROMOTION_TYPES.contains(p.typeId))

      // Building commission holder
      val commissionHolder = getCommissionHolder(
        fencedChannelRate = fencedChannelRate,
        parentChannelRate = parentChannelRate,
        commissions = po.commissions,
        supplierId = supplierId,
        inventoryType = inventoryType,
        channel = channel,
        hInfo = hInfo,
        wlFeature = wlFeature,
        fireDrillProto = fireDrillProto,
        dispatchChannels = dispatchChannels,
        multipleAutoPriceMatch = hInfo.multipleAutoPriceMatch,
        isLastMinuteBooking = isLastMinuteBooking,
        hasNoCCPromotions = hasNoCCPromotions,
        hotelId = po.masterHotelId,
        autoPriceMatchInfo = autoPriceMatchInfo,
        supplierContractedCommission = getSupplierContractedCommission(po),
        shouldBuildMORPCommissionHolder = shouldBuildMORPCommissionHolder,
      )(ctx)

      val maxUspaDiscountPercent = UspaUtils.getMaxUspaDiscountByChannelIdAndSupplierId(
        ctx.eligibleUspaCampaigns,
        channelRate.channelId,
        supplierId,
      )
      val rateCategory = buildRateCategory(
        po = po,
        channelRate = channelRate,
        roomRateCategory = roomRateCategory,
        hInfo = hInfo,
        isBcomFixTaxAmountApplyToPB = isBcomFixTaxAmountApplyToPB,
        hotelCurrency = hotelCurrency,
        countryCurrency = countryCurrency,
        isYCS = isYCS,
        parentCh = parentChannelRate,
        parentRc = parentRC.map(parent =>
          buildRateCategory(
            po,
            parent.ch,
            parent.pa,
            hInfo,
            applicableTaxes,
            applicableTaxesV2,
            isBcomFixTaxAmountApplyToPB,
            isYCS = isYCS,
            isJTBWL = isJTBWL,
            inventoryType = inventoryType,
            agxCommission = fencedChannelRate.agxCommission,
            wholesaleMetaByYplChannel = fencedChannelRate.wholesaleMetaByYplChannel,
            commissionHolder = commissionHolder,
            hmcTaxHolder = hmcTaxHolder,
            childAgeRanges = childAgeRanges,
          )(isApplyYcsRateForking)),
        isJTBWL = isJTBWL,
        inventoryType = inventoryType,
        agxCommission = fencedChannelRate.agxCommission,
        wholesaleMetaByYplChannel = fencedChannelRate.wholesaleMetaByYplChannel,
        commissionHolder = commissionHolder,
        hmcTaxHolder = hmcTaxHolder,
        childAgeRanges = childAgeRanges,
        maxUspaDiscountPercent = maxUspaDiscountPercent,
        applicableTaxes = applicableTaxes,
        applicableTaxesV2 = applicableTaxesV2,
      )(isApplyYcsRateForking)

      /*
      follow the flow for hotelprice where RC will be none ==> Empty Seq
      https://gitlab.agodadev.io/IT-Platform/ypl/blob/f7c8395044b0da82bb836e151bb8327158822728/pricing/src/main/scala/com/agoda/papi/ypl/pricing/OTAConverter.scala#L498-L498
       */
      buildYPLRoomEntry(
        channelRate,
        fencedChannelRate,
        rateCategory,
        po,
        supplierId,
        hInfo,
        roomRateCategory,
        isYCS,
        isJTBWL,
        reqOcc,
        linkedRoomTypeCode,
        occupancyModel,
        hotelPaymentModel,
        inventoryType,
        wlFeature,
        commissionHolder,
        allPromotionIdentifierForRoomRateSet,
      )
    }
  }

  private[pricing] def buildYPLRoomEntry(
    channelRate: ChannelRate,
    fencedChannelRate: FencedChannelRate,
    rateCategory: RateCategoryEntry,
    po: PropertyOffer,
    supplierId: SupplierId,
    hInfo: HotelMeta,
    roomRateCategory: RoomRateCategory,
    isYCS: Boolean,
    isJTBWL: Boolean,
    reqOcc: YplReqOccByHotelAgePolicy,
    linkedRoomTypeCode: Option[String],
    occupancyModel: OccupancyModel,
    hotelPaymentModel: PaymentModel,
    inventoryType: InventoryType,
    wlFeature: Option[Feature],
    commissionHolder: CommissionHolder,
    allPromotionIdentifierForRoomRateSet: Set[Int], // for help RC-2470
  )(implicit ctx: YplContext, request: YplRequest): Seq[YplRoomEntry] = {

    val (cxlCode, roomDataChangeTracker) = getCxlLoadedFromProtobuf(getPropOfferCxlCode(channelRate, po))

    val roomTypeInfo = po.roomTypes.get(roomRateCategory.roomTypeId)
    val isPull = po.supplyInfo.exists(_.supplySource.isPull)
    val dmcRoomId = if (isPull) Some(roomRateCategory.supplierRoomTypeId) else roomTypeInfo.map(_.supplierRoomCode)

    val roomTypeEntry = RoomTypeEntry(
      roomTypeId = roomRateCategory.roomTypeId,
      maxOccupancy = roomTypeInfo.map(_.maxOccupancy).getOrElse(0),
      maxExtraBed = roomTypeInfo.map(_.maxExtraBed).getOrElse(0),
      maxChildrenInRoom = roomTypeInfo.map(_.maxChildren).getOrElse(0),
      isDayUse = false,
      cutOffDays = roomTypeInfo.map(_.cutOffDays).getOrElse(0),
      gaCutOffDays = roomTypeInfo.map(_.gaCutOffDays).getOrElse(0),
      dmcRoomId = dmcRoomId.filter(_.nonEmpty),
      maxAdultsOccupancy = roomTypeInfo.flatMap(_.maxAdultsOccupancy),
      maxChildrenOccupancy = roomTypeInfo.flatMap(_.maxChildrenOccupancy),
      ycsRoomCapacity = roomTypeInfo.flatMap(buildYcsRoomCapacity),
      childHotelId = roomTypeInfo.flatMap(_.childHotelId),
    )
    val rateLoadType = RateType.getFromValue(channelRate.rateLoadType.value)

    // TODO Use this in OTA converter
    val extraPersonCharge = rateCategory.dailyPrices.exists { case (_, dp) =>
      dp.rpmSurcharges.exists(_.id == extraPersonSurchargeId)
    }
    val filteredDPRateCategory =
      filterDailyPrices(haveExtraPersonCharge = extraPersonCharge, rateCategory, reqOcc, occupancyModel, roomTypeEntry)

    val cxlChargeType: Option[CancellationChargeSettingType] =
      channelRate.cancelationPolicy.map(c => CancellationChargeSettingType.getFromValue(c.cancelChargeType.value))
    val checkInStartTime = rateCategory.checkInStartTime
    val checkInEndTime = rateCategory.checkInEndTime
    val isDMCHardcodingRemovalEnabeld = ctx.experimentContext.isUserB(YplExperiments.DMC_HARDCODING_REMOVAL)
    val isBuildYplExternalDataEnabled =
      if (isDMCHardcodingRemovalEnabeld)
        request.supplierFeatures.features.get(supplierId).exists(_.buildYplExternalDataEnabled)
      else isJTBWL
    val dmcDataHolder: DmcDataHolder =
      if (isBuildYplExternalDataEnabled) {
        buildYplExternalData(
          dmcHotelId = po.supplyInfo.map(_.hotelCode).getOrElse(""),
          roomTypeCode = roomRateCategory.supplierRoomTypeId,
          rateCategoryCode = rateCategory.rateCategoryCode,
          maxOcc = roomTypeEntry.maxOccupancy,
          maxChild = roomTypeEntry.maxChildrenInRoom,
          maxExtraBed = roomTypeEntry.maxExtraBed,
          currency = roomRateCategory.currencyCode,
          srcId = ctx.request.searchId,
          srcOcc = ctx.request.occ.adults + ctx.request.occ.children,
          inventoryType = inventoryType.value,
          checkInStartTime = checkInStartTime,
          checkInEndTime = checkInEndTime,
          linkedRoomTypeCode = linkedRoomTypeCode,
        )
      } else {
        DmcDataHolder(
          supplierExternalDataStr = Some(generateExternalDataStr(channelRate.externalData)),
          roomUid = Some(channelRate.roomUid).filter(_.nonEmpty),
          supplierRateInfo = Some(toSupplierRateInfo(channelRate)),
        )
      }
    val resellExternalData = getResellExternalData(channelRate)
    val (numAdults: Int, numChild: Int, propOfferOccupancy: PropOfferOccupancy) = po.occupancy match {
      case Some(occupancy) if isPull =>
        val numOfRooms = reqOcc.rooms
        val adultsPerRoom = reqOcc.guestsPerRoomForPull(occupancy.numAdults, numOfRooms)
        val childrenPerRoom = reqOcc.guestsPerRoomForPull(occupancy.numChild, numOfRooms, isChildrenGuest = true)

        val occupancyInPropOffer = PropOfferOccupancy(numAdults = occupancy.numAdults, numChild = occupancy.numChild)

        (adultsPerRoom, childrenPerRoom, occupancyInPropOffer)
      case _ => (reqOcc.guestsPerRoom(), 0, PropOfferOccupancy(reqOcc.guests, 0))
    }

    val perDayOccPrices: Seq[PerDayPerOccPrice] =
      if (request.isBookingRequest) {
        rateCategory.dailyPrices.flatMap { case (date, price) =>
          price.roomPrices.map { rp =>
            PerDayPerOccPrice(
              rateCategoryId = rateCategory.rateCategoryIdOfRateLoaded,
              stayDate = date,
              occ = rp.occupancy,
              price = rp.value,
            )
          }(collection.breakOut)
        }(collection.breakOut)
      } else {
        Seq.empty
      }

    val displayedRackrate =
      if (isDMCHardcodingRemovalEnabeld) request.supplierFeatures.features.get(supplierId).exists(_.displayRackRate)
      else isJTBWL
    val channel: YplChannel = YplMasterChannel(channelRate.channelId)
    val availablePromotions =
      getAvailablePromotions(channelRate, cxlCode, po, allPromotionIdentifierForRoomRateSet, channel.toChannelIds.toSet)

    val yplRoomEntry = YplRoomEntry(
      roomTypeId = roomRateCategory.roomTypeId,
      masterRoomTypeId = hInfo.getMasterRoomTypeId(roomRateCategory.roomTypeId),
      channel = channel,
      cxlCode = cxlCode,
      isBreakFastIncluded = roomRateCategory.benefits.contains(1),
      remainingRooms = channelRate.remainingRooms,
      currency = roomRateCategory.currencyCode,
      rateType = rateLoadType, // From the code old, it is only used on YCS old rate model where ratecategory is None
      processingFees = channelRate.processingFee,
      isAllowCombinePromotion = po.isPromotionCombinable,
      roomType = roomTypeEntry,
      dailyPrices = filteredDPRateCategory.dailyPrices, // TODO: NEED TO FILTER rateCategoryEntryWithFilteredDailyPrice
      occEntry =
        if (po.occupancyModel.isFullPatternLengthOfStay && !reqOcc.isFreeOcc)
          RoomOccupancy(numAdults, numChild, freeChildrenAndInfants = numChild, allowedFreeChildrenAndInfants = numChild)
        else RoomOccupancy(0, 0),
      availablePromotions = availablePromotions,
      channelRateType = Some(RateType.getFromValue(channelRate.channelDiscountRateLoadType.value))
        .filter(_ != RateType.Unknown)
        .orElse(Some(RateType.NetExclusive)),
      rateCategory = filteredDPRateCategory,
      originalRateType = rateLoadType,
      paymentModel = rateCategory.paymentModel,
      dmcDataHolder = Some(dmcDataHolder),
      confirmByMins = Some(channelRate.confirmByMinute).filter(_ > 0),
      isBookOnRequest = hInfo.suppliers.find(_.supplierId == supplierId).exists(_.isBookOnRequest),
      hourlyAvailableSlots = rateCategory.hourlyAvailableSlots,
      displayedRackRate =
        if (isYCS || displayedRackrate) hInfo.getRoomDisplayedRackRate(roomRateCategory.roomTypeId) else 0d,
      linkedRoomTypeCode = linkedRoomTypeCode,
      cxlChargeSetting = cxlChargeType,
      inventoryType = inventoryType,
      checkInInformation = rateCategory.checkInInformation,
      fences = fencedChannelRate.fences,
      rateCategoryInfo = po.rateCategoryInfoMap
        .filter(o => o._1 == rateCategory.rateCategoryId)
        .values
        .headOption
        .map(h => YplRateCategoryInfo(h.minNightsStay)),
      resellExternalData = resellExternalData,
      roomDataChangeTracker = Some(roomDataChangeTracker.withPerDayOccPrice(perDayOccPrices)),
      commissionHolder = commissionHolder,
      propOfferOccupancy = propOfferOccupancy,
    )

    // Update Payment Option
    val rateCategoryPaymentModel = rateCategory.paymentModel.getOrElse(hotelPaymentModel)
    val isCreditCardRequired = channelRate.paymentGuarantee.map(_.isCreditCardRequired)

    val isPrepaymentRequired = channelRate.paymentGuarantee.map(_.isPrepaymentRequired)
    val yplRoomWithPaymentOption = {
      val paymentOptions = yplRoomEntry.fences
        .map { fence =>
          (getPaymentOptions(
             supplierId = supplierId,
             hotelMeta = hInfo,
             rateCategoryPaymentModel = rateCategoryPaymentModel,
             isCreditCardRequired = isCreditCardRequired,
             isPrepaymentRequired = isPrepaymentRequired,
             origin = Some(fence.origin),
           ),
           fence)
        }
        .groupBy(v => v._1)

      paymentOptions.map { case (paymentOption, values) =>
        yplRoomEntry.copy(
          paymentOptions = paymentOption,
          fences = values.map(_._2),
        )
      }.toSeq
    }

    // Override Payment option for JTBWL
    yplRoomWithPaymentOption.map(room =>
      overrideRoomWithSupplierFeatures(
        wlFeature,
        room,
        rateCategory.paymentModel
          .map { p =>
            com.agoda.papi.enums.hotel.PaymentModel.getPaymentModel(p.i)
          }
          .getOrElse(PaymentModel.Unknown),
        rateCategory.paymentChannels,
      ))
  }

  private[pricing] def getCommissionHolder(
    fencedChannelRate: FencedChannelRate,
    parentChannelRate: Option[ChannelRate],
    commissions: Map[Int, ProtoCommission],
    supplierId: SupplierId,
    inventoryType: InventoryType,
    channel: YplChannel,
    hInfo: HotelMeta,
    wlFeature: Option[Feature],
    fireDrillProto: Option[FireDrillProto] = None,
    dispatchChannels: YplDispatchChannels,
    multipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder],
    isLastMinuteBooking: Boolean,
    hasNoCCPromotions: Boolean,
    hotelId: HotelId,
    autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]],
    supplierContractedCommission: Option[Double],
    shouldBuildMORPCommissionHolder: Boolean,
  )(implicit ctx: YplContext) = {

    val morpGenerateRoomParams =
      if (shouldBuildMORPCommissionHolder) {
        AgencyCommissionUtils.getMORPCandidateRoomParamWithAgxAdjustments(
          fences = fencedChannelRate.fences,
          hotelCountryCode = hInfo.countryCode,
          agencyBookingTypeId = hInfo.agencyFeatures.getOrElse(AgodaAgencyFeatures.default).agencyBookingType,
          isLastMinuteBooking = isLastMinuteBooking,
          hasNoCCPromotions = hasNoCCPromotions,
          hotelId = hotelId,
          agxCommissionAdjustmentFences = ctx.request.agxCommissionAdjustmentFences,
        )
      } else {
        Seq.empty[MORPCandidateRoomParamWithAgxAdjustment]
      }

    val morpCommissionHolder =
      if (shouldBuildMORPCommissionHolder) {
        CommissionHolderBuilder.buildMORPCommissionHolder(
          agencyFeatures = hInfo.agencyFeatures.getOrElse(AgodaAgencyFeatures.default),
          languageId = ctx.request.cInfo.language,
          channel = channel,
          morpGenerateRoomParams,
        )
      } else {
        MORPCommissionHolder.default
      }

    val (commissionAdjustment, commissionOverriding, marginAdjustment, commissionBaseOnHotelContractAdjustment) =
      CommissionHolderBuilder.buildWhitelabelCommissionAdjustment(
        wlFeature,
        ctx.request.whitelabelSetting,
        inventoryType,
        ctx.request.occ,
        hInfo.whiteLabelContractTypeId.getOrElse(0),
      )
    val agxCommissionAdjustment = YplAGXCommissionAdjustmentWrapper(fencedChannelRate.agxCommission)
    val agxAllDateAdjust = agxCommissionAdjustment.commissionAdjustmentOp.flatMap(
      _.allDateAdjust.map(a =>
        AgxCommissionHolder(
          payAsYouGoCommission = a.payAsYouGoCommission,
          prepaidCommission = a.prepaidCommission,
          freeTrialCommission = a.freeTrialCommission,
          isApplyAgx = true,
          isAgxEligibleRateChannel = true,
        )))
    val growthProgramCommissionHolder = GrowthProgramCommissionHolder(
      applicableGrowthProgram = hInfo.applicableGrowthPrograms,
      growthProgramFence = hInfo.growthProgramFence,
    )

    CommissionHolder(
      daily = CommissionHolderBuilder.buildDailyCommissionHolder(
        commissionIdentifierMap = commissions,
        fencedChannelRate = fencedChannelRate,
        parentChannelRate = parentChannelRate,
        supplierId = supplierId,
        agxCommissionAdjustment = agxCommissionAdjustment,
        inventoryType = inventoryType,
        supplierCommissionMapping = hInfo.supplierCommissionMapping,
        morpCandidateRoomParamWithAgxAdjustments = morpGenerateRoomParams,
        commissionAdjustmentForLanguage = hInfo.adjustCommissions,
        applicableGrowthPrograms = hInfo.applicableGrowthPrograms,
        growthProgramFence = hInfo.growthProgramFence,
      )(ctx),
      commissionAdjustment = commissionAdjustment,
      commissionOverriding = commissionOverriding,
      commissionMultiplyAdjustment = marginAdjustment,
      commissionBaseOnHotelContractAdjustment = commissionBaseOnHotelContractAdjustment,
      agxAllDateAdjust = agxAllDateAdjust,
      agpCommissionHolder = CommissionHolderBuilder.buildAgpFireDrillCommissionHolder(
        fireDrillProtoOpt = fireDrillProto,
      )(ctx),
      apmCommissionHolder = ApmCommissionHolder(
        multipleAutoPriceMatch,
        hInfo.apmCommissionReductionEligibility,
        hInfo.apmConfigs,
        ctx.request.apmSetting,
        autoPriceMatchInfo,
        hInfo.apmDeltaPercentage.map { case (k, v) => k -> v.toCommissionModel },
      ),
      morpCommissionHolder = morpCommissionHolder,
      retailGlobalContractedCommission = getRetailGlobalContractedCommission(commissions),
      supplierContractedCommission = supplierContractedCommission,
      growthProgramCommissionHolder = growthProgramCommissionHolder,
    )
  }

  private[pricing] def isPromotionBlockEnabled(supplierId: SupplierId)(implicit ctx: YplContext): Boolean = {
    val request = ctx.request
    if (ctx.experimentContext.isUserB(YplExperiments.BLOCK_PROMOTIONS_FOR_SPECIFIC_SUPPLIERS)) {
      val promotionBlockedSuppliers = request.whitelabelSetting.promotionsBlockedSuppliers
      // empty set -> block for all supplier , non-empty set -> block for suppliers in promotionBlockedSuppliers
      request.whitelabelSetting.blockPromotionsPerSupplier && (promotionBlockedSuppliers.isEmpty || promotionBlockedSuppliers
        .contains(supplierId))
    } else {
      request.whitelabelSetting.blockYCSPromotions
    }
  }

  private[pricing] def getAvailablePromotions(
    channelRate: ChannelRate,
    cxlCode: String,
    po: PropertyOffer,
    allAvailPromotionIdentifierForRoomRateSet: Set[Int],
    possibleChannelIdsForTheChannelRate: Set[Int],
  )(implicit ctx: YplContext) = {
    val isPromotionBlocked = isPromotionBlockEnabled(po.supplyInfo.map(_.supplierId).getOrElse(0))
    if (isPromotionBlocked) List.empty
    else {
      val originalAvailablePromotions: List[PromotionEntry] = channelRate.promotionIdentifiers
        .flatMap(id => po.promotions.get(id).map(buildPromotion(_, po.bookingRestrictions, cxlCode)))
        .toList

      // if it was B2B channel it should not do extra dispatch
      val doesChannelRateContainB2BChannel =
        PromotionConstant.experimentPulseToAllRateChannelSettings.forceExcludeB2BChannelIds
          .intersect(possibleChannelIdsForTheChannelRate)
          .nonEmpty
      val shouldEnablePulseToAllRateChannelApply: Boolean = {
        val whiteListHotelIds = PromotionConstant.experimentPulseToAllRateChannelSettings.whiteListHotelIdSet
        !ctx.request.isAllMseTraffic && whiteListHotelIds.contains(
          po.hotelId) && !doesChannelRateContainB2BChannel && ctx.experimentContext.isUserB(
          YplExperiments.ENABLE_PULSE_TO_ALL_RC)
      }
      if (!shouldEnablePulseToAllRateChannelApply) {
        originalAvailablePromotions
      } else {
        // For checking if the promotion is pulse
        // make sure allPossiblePromotionsUnderRoomRate does not have duplicate promotion_id and invalid cancellation policy
        val allPossiblePromotionsUnderRoomRate: List[Promotion] = allAvailPromotionIdentifierForRoomRateSet
          .flatMap { id =>
            po.promotions.get(id) match {
              case Some(promotion) if promotion.cancellationCode.isEmpty || promotion.cancellationCode == cxlCode =>
                Some((id, promotion))
              case _ => None
            }
          }
          .toList
          .groupBy(_._2.promotionId)
          .flatMap { promotionIdWithListOfIdPromotionPair =>
            val listOfIdPromotionPair: List[(Int, Promotion)] = promotionIdWithListOfIdPromotionPair._2
            listOfIdPromotionPair.sortBy(_._1).headOption.map(_._2)
          }
          .toList
          .sortBy(_.promotionId) // <-- Ensures deterministic output order

        val pulseCampaignPromoSettings: Map[PromotionTypeId, PulseCampaignMetadata] = ctx.pulseCampaignMetaCtx
          .getPulseCampaignSetting(allPossiblePromotionsUnderRoomRate.map(promotion => promotion.typeId))

        val availablePromotionsIncludingPossiblePulsePromotion: List[PromotionEntry] = {
          val allPossiblePulsePromotionsUnderRoomRate: List[Promotion] =
            allPossiblePromotionsUnderRoomRate.filter(promotion => pulseCampaignPromoSettings.contains(promotion.typeId))
          val originalAvailablePromotionIds = originalAvailablePromotions.map(_.id)
          val additionalPulsePromotions: List[PromotionEntry] = allPossiblePulsePromotionsUnderRoomRate
            .withFilter(promotion => !originalAvailablePromotionIds.contains(promotion.promotionId))
            .map { promotion =>
              val promotionEntry = buildPromotion(promotion, po.bookingRestrictions, cxlCode)
              promotionEntry.copy(additionalDispatchReasons = Set(AdditionalDispatchReason.ForcedMapToAllRateChannel))
            }
          originalAvailablePromotions ++ additionalPulsePromotions
        }
        availablePromotionsIncludingPossiblePulsePromotion
      }

    }
  }

  private def getResellExternalData(channelRate: ChannelRate) = channelRate.externalData
    .find(_.key == ExternalDataFields.ResellSourceBookingId)
    .map(id => ResellExternalData(sourceBookingId = id.value))

  private[pricing] def buildRateCategory(po: PropertyOffer,
                                         channelRate: ChannelRate,
                                         roomRateCategory: RoomRateCategory,
                                         hInfo: HotelMeta,
                                         applicableTaxes: Map[Int, ProtoTax],
                                         applicableTaxesV2: Map[Int, ProtoTaxV2],
                                         isBcomFixTaxAmountApplyToPB: Boolean,
                                         hotelCurrency: Option[String] = None,
                                         countryCurrency: Option[String] = None,
                                         isYCS: Boolean,
                                         parentCh: Option[ChannelRate] = None,
                                         parentRc: Option[RateCategoryEntry] = None,
                                         isJTBWL: Boolean = false,
                                         inventoryType: InventoryType = InventoryType.Agoda,
                                         agxCommission: Option[YplAGXCommissionAdjustment] = None,
                                         wholesaleMetaByYplChannel: WholesaleMetaByYplChannelSupplierID = Map.empty,
                                         commissionHolder: CommissionHolder = CommissionHolder.default,
                                         extrabedPerPerson: List[PerPersonPrice] = Nil,
                                         propertyRateType: RateType = RateType.Unknown,
                                         hmcTaxHolder: HMCTaxHolder = HMCTaxHolder.default,
                                         childAgeRanges: List[ChildAgeRange] = Nil,
                                         maxUspaDiscountPercent: Double = 0.0)(isApplyYcsRateForking: Boolean)(implicit
    ctx: YplContext): RateCategoryEntry = {
    implicit val request = ctx.request
    val checkIn = ctx.request.checkIn
    val reqOcc = hInfo.getOccupancy
    val whitelabelSetting = ctx.request.whitelabelSetting
    val bookRestOpt = po.bookingRestrictions.get(channelRate.bookingRestrictionIdentifier)
    val bookingRestriction = buildRestriction(
      restrictionEntry = buildRateCategoryBookingRestriction(
        bookRes = bookRestOpt,
        isAllowedCustomerSegmentsRestriction = shouldPopulateCustomerSegmentRestriction(
          channelRate.isNationalityRate,
          po.supplyInfo.exists(_.supplierId == DMC.BCOM),
          isPropOffer = true,
        ) && request.whitelabelSetting.isCustomerSegmentValidationEnabled,
      ),
    )

    val checkInInformation = po.checkInInformations.get(channelRate.checkInInformationIdentifier).map { info =>
      YPLCheckInInformation(
        checkInFrom = info.checkInFrom.map(a => LocalTime.of(a.hours, a.minutes, a.seconds)),
        checkInUntil = info.checkInUntil.map(a => LocalTime.of(a.hours, a.minutes, a.seconds)),
        checkOutFrom = info.checkOutFrom.map(a => LocalTime.of(a.hours, a.minutes, a.seconds)),
        checkOutUntil = info.checkOutUntil.map(a => LocalTime.of(a.hours, a.minutes, a.seconds)),
      )
    }

    val paymentModel =
      Some(PaymentModel.getPaymentModel(channelRate.paymentMode.value)).filter(p => p != PaymentModel.Unknown)
    val supplierId = po.supplyInfo.map(_.supplierId).getOrElse(0)

    val benefitsWithoutPropBenefits: List[BenefitEntry] = {
      val updatedBenefits = roomRateCategory.benefitIdentifiers.flatMap(po.benefits.get)
      updatedBenefits.map(buildPropOfferBenefit)(breakOut)
    }
    val updatedASOBenefitsWithDiscounts = getUpdatedBenefitsWithDiscount(
      benefitsWithoutPropBenefits,
      roomRateCategory.benefitIdentifiers.flatMap(po.benefits.get))

    val benefitEntries = addPropertyBenefits(po.propertyBenefits, updatedASOBenefitsWithDiscounts)
    val (cxlCode, _) = getCxlLoadedFromProtobuf(getPropOfferCxlCode(channelRate, po))
    val protoRateType = Option(RateType.getFromValue(channelRate.rateLoadType.value)).filterNot(_ == RateType.Unknown)
    val rateTypeLoaded = protoRateType.getOrElse(RateType.NetExclusive)
    val taxEntry = buildDailyTaxEntry(
      checkIn,
      po.lengthOfStay,
      hInfo,
      po,
      roomRateCategory,
      channelRate,
      hotelCurrency,
      countryCurrency,
      applicableTaxes,
      applicableTaxesV2,
      mohuGdsCommissionFeeSettings,
      hmcTaxHolder,
    )
    val surchargeEntry = buildSurchargeEntry(checkIn, po.lengthOfStay, po.surcharges, channelRate).toList
    val useJpChildRateOverAgeRanges = isYCS && hInfo.countryCode == "JP" && channelRate.childRate.exists(
      _.isChildRateEnabled) && channelRate.childRate.exists(_.childPricing.nonEmpty)
    val dailyPrices = buildDailyPrices(
      checkIn,
      roomRateCategory.roomTypeId,
      roomRateCategory.rateCategoryId,
      taxEntry,
      surchargeEntry,
      channelRate,
      reqOcc,
      hInfo.childAgeRanges,
      hInfo.hmcMasterHotelContext,
      rateTypeLoaded,
      roomRateCategory.currencyCode,
      parentCh,
      useJpChildRateOverAgeRanges,
      commissionHolder,
      extrabedPerPerson = extrabedPerPerson,
      propertyRateType = propertyRateType,
      protoChildAgeRanges = childAgeRanges,
      maxUspaDiscountPercent = maxUspaDiscountPercent,
    )

    val parentRateDiscount = channelRate.parentRateDiscount
    val stayPackageType: Option[StayPackageType] = Some(roomRateCategory.stayPackageType)
    val escapesApprovalStatus: Option[EscapesApprovalStatus] = Some(roomRateCategory.escapesApprovalStatus)
    val hourlyAvailableSlots = channelRate.hourlyAvailability.map { avail =>
      avail.availability.flatMap { hourlySlot =>
        toTimeInterval(hourlySlot)
      }
    }

    // YCS_RATE_FORKING = "TRUEAG-369"
    val isAgencyEnabled = (isYCS, isApplyYcsRateForking) match {
      case (true, false) => None
      case (_) => Some(roomRateCategory.isAgencyEnabled)
    }

    val cxlCodePerOcc: List[CancellationCodePerOcc] = getCancellationPerOcc(channelRate, po.cancelPolicies)

    val propOfferChildRate = channelRate.childRate
    val isDMCHarcodingRemovalEnabled = ctx.experimentContext.isUserB(YplExperiments.DMC_HARDCODING_REMOVAL)
    val hasAValidRateCode =
      if (isDMCHarcodingRemovalEnabled)
        ctx.request.supplierFeatures.features.get(supplierId).exists(_.hasAValidRateCode)
      else isJTBWL
    val rateCode =
      if (hasAValidRateCode && channelRate.supplierRateCode.nonEmpty) Some(channelRate.supplierRateCode) else None

    RateCategoryEntry(
      rateCategoryId = roomRateCategory.rateCategoryId.toInt, // correct
      cxlCode = cxlCode, // correct
      rateTypeLoaded = rateTypeLoaded, // correct
      parent = parentRc,
      remainingRoom = channelRate.remainingRooms, // correct
      benefitList = benefitEntries, // correct
      dailyPrices = dailyPrices,
      bookFrom = bookingRestriction.flatMap(_.bookFrom),
      bookTo = bookingRestriction.flatMap(_.bookTo),
      bookTimeFrom = bookingRestriction.flatMap(_.bookTimeFrom),
      bookTimeTo = bookingRestriction.flatMap(_.bookTimeTo),
      remainingRoomGa = channelRate.remainingRoomsGA,
      remainingRoomRa = Some(channelRate.remainingRoomRA).filter(_ > 0),
      minAdvance = bookingRestriction.flatMap(_.minAdvance),
      maxAdvance = bookingRestriction.flatMap(_.maxAdvance),
      isAmount = parentRateDiscount.exists(_.isAmount),
      applyTo = parentRateDiscount.map(_.applyTo).getOrElse(""),
      value = parentRateDiscount.map(_.discountValue).getOrElse(0d),
      promotionList = Nil,
      isCanCombinePromotion = false,
      offerType = Some(roomRateCategory.offerTypeId).filter(_ > 0),
      isRoomTypeNotGuarantee = channelRate.isRoomTypeNotGuaranteed,
      customerSegment = bookingRestriction.map(_.customerSegments).getOrElse(Nil),
      bookOn = bookingRestriction.flatMap(_.bookOn),
      dmcRatePlanID = Some(channelRate.supplierRateCode).filter(_.nonEmpty),
      dmcMealPlanID = Some(channelRate.supplierMealPlan).filter(_.nonEmpty),
      promotionTypeId = Some(roomRateCategory.promotionTypeId).filter(_ > 0),
      promotionTypeCmsId = Some(roomRateCategory.promotionTypeCmsId).filter(_ > 0),
      paymentModel = paymentModel,
      isAgencyEnabled = isAgencyEnabled,
      stayPackageType = stayPackageType,
      cxlCodePerOcc = cxlCodePerOcc,
      childRate = propOfferChildRate
        .map(_.childPricing.map(childPricing =>
          YPLChildRate(
            childRateTypeId = ChildRateType.getFromValue(childPricing.childRateTypeId),
            childRatePricingTypeId = PricingChildRateType.getFromValue(childPricing.pricingTypeId),
            value = childPricing.amount,
            isCountAsRoomOcc = childPricing.isCountAsRoomOcc,
          )))
        .getOrElse(Nil),
      isChildRateEnabled = propOfferChildRate.exists(_.isChildRateEnabled),
      paymentChannels = allowedPaymentChannels(channelRate, whitelabelSetting),
      checkInStartTime = channelRate.checkInTime.flatMap(_.startTime.map(_.toTimeOfDayString)),
      checkInEndTime = channelRate.checkInTime.flatMap(_.endTime.map(_.toTimeOfDayString)),
      hourlyAvailableSlots = hourlyAvailableSlots.getOrElse(Seq.empty),
      checkInInformation = checkInInformation,
      rateCategoryCode = rateCode,
      inventoryType = inventoryType,
      isYcsOrJtbWl = isYCS || isJTBWL,
      escapesApprovalStatus = escapesApprovalStatus,
      useJapanBenefitChildRateDaily = useJpChildRateOverAgeRanges,
      childPolicy = roomRateCategory.childPolicy,
    )
  }

  private[pricing] def buildStackedChannelInfo(
    stack: Seq[StackedChannelInfo]): Map[ChannelId, Seq[StackedChannelDiscount]] = stack.map { stackedDiscountInfo =>
    val stackedChannelDiscounts = stackedDiscountInfo.stackedDiscounts.map { stackedChannelDiscount =>
      StackedChannelDiscount(
        channelId = stackedChannelDiscount.channelId,
        discountPercent = stackedChannelDiscount.discountPercent,
      )
    }
    stackedDiscountInfo.baseChannelId -> stackedChannelDiscounts
  }.toMap

  private[pricing] def filterRateFenceByCustomerSegment(restrictions: Map[Int, BookingRestriction],
                                                        fences: Set[YplRateFence],
                                                        channelRate: ChannelRate,
                                                        isAboCidToOriginMappingEnabled: Boolean)(implicit
    ctx: YplContext): Set[YplRateFence] = {
    val request = ctx.request
    val restriction = buildRateCategoryBookingRestriction(
      bookRes = restrictions.get(channelRate.bookingRestrictionIdentifier),
      isAllowedCustomerSegmentsRestriction = request.whitelabelSetting.isCustomerSegmentValidationEnabled,
    )
    val customerSegments: List[CustomerSegment] = restriction.map(_.customerSegments).getOrElse(List.empty)

    fences.filter { fence =>
      val customerOrigin =
        if (isAboCidToOriginMappingEnabled) {
          // From CidToOriginMapper trait that's implemented by AffiliateCidToOriginMappingDataServiceImpl
          getOriginByCid(fence.cid) orElse Some(fence.origin)
        } else {
          // From OriginManager trait that's implemented by FileOriginManager
          getOriginByCid(Some(fence.cid)) orElse Some(fence.origin)
        }
      validateCustomerSegment(customerSegments, customerOrigin.getOrElse(""), fence.language, request.cInfo.vipLevel)
    }
  }

  private[pricing] def getCancellationPerOcc(
    channelRate: ChannelRate,
    cancelPolicies: Map[Int, CancelCode]): List[CancellationCodePerOcc] = channelRate.cancelationPolicy
    .map(_.cancelPolicyByOccupancy)
    .getOrElse(Nil)
    .map { cxlPolicy =>
      val code =
        cancelPolicies.get(cxlPolicy.cancelCodeIdentifier).map(_.code).getOrElse(CancellationCode.NON_REFUNDABLE)
      CancellationCodePerOcc(cxlPolicy.minOcc, cxlPolicy.maxOcc, code)
    }
    .toList

  private[pricing] def hasAllotment(channelRate: ChannelRate,
                                    rc: RoomRateCategory,
                                    reqOcc: YplReqOccByHotelAgePolicy,
                                    ignoreRequestedNumberOfRoomsForNha: Boolean)(implicit ctx: YplContext): Boolean = {
    implicit val request = ctx.request

    def validRemainingRoomInChannel: Boolean =
      if (ctx.request.featureRequest.ignoreRoomsCountForNha.getOrElse(false)) {
        channelRate.remainingRooms > 0 && (channelRate.remainingRooms >= reqOcc.occ.rooms || ignoreRequestedNumberOfRoomsForNha)
      } else {
        channelRate.remainingRooms > 0 && channelRate.remainingRooms >= reqOcc.occ.rooms || ignoreRequestedNumberOfRoomsForNha
      }

    validRemainingRoomInChannel ||
    request.returnZeroAllotment && applyPreFilterForZeroAllotment(
      roomTypeIdOpt = Some(rc.roomTypeId),
      rateCategoryIdOpt = None,
      filterZeroAllotmentOpt = request.preFiltersZeroAllotment,
    )
  }

  private def toSupplierRateInfo(channelRate: ChannelRate) = SupplierRateInfo(
    Some(channelRate.supplierRateCode).filter(_.nonEmpty),
    Some(channelRate.supplierMealPlan).filter(_.nonEmpty),
    channelRate.externalData.map(ex => SupplierRateInfo.ExternalData(ex.key, ex.value, Some(ex.isExcludeForRoomUID))),
  )

  private[pricing] def generateExternalDataStr(ed: Seq[ExternalData]): String = {
    val spapiRequestIdKey = "spapi-request-id"
    val spapiTimestampKey = "response-timestamp-ms"
    val spapiReplicateKey = "replicate"

    val externalDataFieldsToRemove = Set(spapiRequestIdKey, spapiTimestampKey, spapiReplicateKey)

    ed.filterNot(item => externalDataFieldsToRemove.contains(item.key))
      .withFilter(item => !item.isExcludeForRoomUID)
      .map(_.value)
      .mkString("|")
  }

  // Hotel Currency is load from agoda_YCS.dbo.ycs4_hotel.currency_code
  // Then it is flatten into PropOffer Level
  // Unfortunately, currently we do not pass this currency_code in hotel level
  // So for now, we will pick this up from a sample in PropOffer here.
  def getHotelCurrency(po: PropertyOffer): Option[String] = po.roomRates.headOption.map(_.currencyCode)

  private[pricing] def buildSupplierTax(po: PropertyOffer,
                                        isBcom: Boolean,
                                        hInfo: HotelMeta,
                                        hotelCurrency: Option[String],
                                        countryCurrency: Option[String],
                                        applicableTaxes: Map[Int, ProtoTax],
                                        applicableTaxesV2: Map[Int, ProtoTaxV2],
                                        supplierId: Int)(implicit ctx: YplContext) = {

    implicit val request: YplRequest = ctx.request
    val isApplyMOHUGdsCommissionFee: Boolean =
      mohuGdsCommissionFeeSettings.cidSet.contains(request.cInfo.cid.getOrElse(0))
    val supplierTaxType = {
      val taxType = TaxType.getFromValue(po.taxType.value)
      if (taxType == TaxType.Unknown) {
        TaxType.SimpleTax
      } else {
        taxType
      }
    }

    val isQualifiedToAppliedNewChargeType = applicableTaxesV2.nonEmpty
    val isAllTaxRoomRateContainedTaxPerDayV2 =
      po.roomRates.flatMap(r => r.channelRates.map(cr => cr.taxPerDayV2.nonEmpty)).foldLeft(true)(_ && _)
    if (isQualifiedToAppliedNewChargeType && !isAllTaxRoomRateContainedTaxPerDayV2) {
      logger.warn("TaxV2 existed on hotel level but NOT to all rooms under the hotel. Fallback to old Tax")
    }
    val isNewChargeTypeTaxAppliedExp =
      isEnableTaxV2Exp(isQualifiedToAppliedNewChargeType && isAllTaxRoomRateContainedTaxPerDayV2, hInfo, supplierId)

    val taxes =
      if (isNewChargeTypeTaxAppliedExp) {
        applicableTaxesV2.collect {
          case (_, taxV2)
              if shouldApplyIfItIsSaudiUmrahGovTax(Some(taxV2.taxPrototypeId),
                                                   isApplyMOHUGdsCommissionFee,
                                                   mohuGdsCommissionFeeSettings) =>
            buildTaxV2(
              taxV2 = taxV2,
              taxPrototypeInfo = po.taxPrototypeLevelsV2
                .get(taxV2.taxPrototypeId)
                .map(
                  getTaxPrototypeInfo[TaxPrototypeLevelsV2](
                    _,
                    hotelCurrency = hotelCurrency,
                    countryCurrency = countryCurrency,
                    isAmount = taxV2.isAmount,
                    covertTaxPrototypeLevelV2,
                  )),
              isBcom = isBcom,
              hotelCurrency = hotelCurrency,
              mohuGdsCommissionFeeSettings = mohuGdsCommissionFeeSettings,
            )
        }
      } else {
        applicableTaxes.collect {
          case (_, tax)
              if shouldApplyIfItIsSaudiUmrahGovTax(Some(tax.taxPrototypeId),
                                                   isApplyMOHUGdsCommissionFee,
                                                   mohuGdsCommissionFeeSettings) =>
            buildTax(
              tax = tax,
              taxPrototypeInfo = po.taxPrototypeLevels
                .get(tax.taxPrototypeId)
                .map(
                  getTaxPrototypeInfo[TaxPrototypeLevels](
                    _,
                    hotelCurrency = hotelCurrency,
                    countryCurrency = countryCurrency,
                    isAmount = tax.isAmount,
                    covertTaxPrototypeLevelV1,
                  )),
              isBcom = isBcom,
              countryCurrency = countryCurrency,
              hotelCurrency = hotelCurrency,
              mohuGdsCommissionFeeSettings = mohuGdsCommissionFeeSettings,
            )
        }
      }

    val isUseConfiguredProcessingFee = hInfo.getIsUseConfiguredProcessingFee
    TaxInfo(hotelTaxInfo =
              HotelTaxInfo(taxType = supplierTaxType, isConfigProcessingFees = isUseConfiguredProcessingFee),
            taxes = taxes)
  }

  private[pricing] def getPeriodIntervals(br: BookingRestriction): List[PeriodIntervalInput] = {
    val periodIntervals = new ListBuffer[PeriodIntervalInput]()

    if (br.bookDateFrom.nonEmpty || br.bookDateTo.nonEmpty) {
      periodIntervals += PeriodIntervalInput(1,
                                             br.bookDateFrom.map(_.toDateTime.getMillis).getOrElse(0),
                                             br.bookDateTo.map(_.toDateTime.getMillis).getOrElse(0))
    }

    if (br.bookTimeFrom.nonEmpty || br.bookTimeTo.nonEmpty) {
      periodIntervals += PeriodIntervalInput(2,
                                             br.bookTimeFrom.map(_.toMillisDateToday).getOrElse(0),
                                             br.bookTimeTo.map(_.toMillisDateToday).getOrElse(0))
    }

    periodIntervals.toList
  }

  private[pricing] def buildCustomerSegment(
    cs: com.agoda.supply.calc.proto.CustomerSegment): pricing.proto.CustomerSegment = pricing.proto.CustomerSegment(
    Some(cs.languageId).filter(_ > 0),
    Some(cs.countryCode).filter(_.nonEmpty),
    Some(cs.vipLevel).filter(!_.isVipLevelUndefined).map(x => enums.VipLevelType.getFromValue(x.value)),
  )

  // different inputs with the same name and almost the same logic
  private[pricing] def buildRateCategoryBookingRestriction(
    bookRes: Option[proto.BookingRestriction],
    isAllowedCustomerSegmentsRestriction: Boolean = false): Option[RestrictionEntryModel] = bookRes.map { br =>
    val customerSegments: List[CustomerSegment] =
      if (isAllowedCustomerSegmentsRestriction) br.customerSegments.map(buildCustomerSegment)(breakOut)
      else Nil

    RestrictionEntryModel(
      getPeriodIntervals(br),
      Some(br.bookOn),
      Some(br.minAdvancePurchase).filter(_ != SAME_ALL_DAYS_INDEX),
      Some(br.maxAdvancePurchase).filter(_ != DEFAULT_MAXADV),
      customerSegments,
    )
  }

  private[pricing] def buildPromotion(promo: Promotion,
                                      restrictionLookUp: Map[Int, BookingRestriction],
                                      roomCxlCode: String)(implicit ctx: YplContext) = {
    val discountType = DiscountType.getFromValue(promo.discountTypeId)

    def buildPromotionRestrictionInput(restriction: Option[BookingRestriction]): Option[RestrictionEntryModel] =
      restriction.map { r =>
        val customerSegments: List[pricing.proto.CustomerSegment] =
          r.customerSegments.map(buildCustomerSegment)(breakOut)
        RestrictionEntryModel(
          getPeriodIntervals(r),
          Some(r.bookOn),
          Some(r.minAdvancePurchase).filter(_ != SAME_ALL_DAYS_INDEX),
          Some(r.maxAdvancePurchase).filter(_ != DEFAULT_MAXADV),
          customerSegments,
          Some(r.minRooms).filter(_ != SAME_ALL_DAYS_INDEX),
          Some(r.minNight).filter(_ != SAME_ALL_DAYS_INDEX),
        )
      }

    val promotionRestrictionInput =
      buildPromotionRestrictionInput(restrictionLookUp.get(promo.bookingRestrictionIdentifier))
    val promotionRestriction = buildRestriction(promotionRestrictionInput)

    val explodedApplyDates = promo.applyDates
      .flatMap { applyDate =>
        numericToSet(applyDate.stayDates).map { d =>
          val date = ctx.request.checkIn.plusDays(d)
          date -> applyDate.discount
        }
      }
      .sortBy(_._1.getMillis)

    // AmountDiscountPerBook is only set on the first index: 0
    val applyDates: Map[DateTime, Int] = explodedApplyDates.zipWithIndex.map { case ((d, _), index) =>
      d -> (if (discountType == DiscountType.AmountDiscountPerBook ||
              discountType == DiscountType.FreeNight) 0
            else index)
    }(collection.breakOut)

    val discounts: List[Double] = discountType match {
      case DiscountType.AmountDiscountPerBook => List(explodedApplyDates.getDSum(_._2))
      case DiscountType.FreeNight => promo.discounts.filter(_ != 0d).toList
      case _ => explodedApplyDates.map(_._2)(breakOut)
    }

    val promoCxlCode = if (promo.cancellationCode.isEmpty) roomCxlCode else promo.cancellationCode

    PromotionEntry(
      id = promo.promotionId,
      typeId = promo.typeId,
      discountType = discountType,
      discounts = discounts,
      cmsTypeId = promo.cmsTypeId,
      cmsDiscountTypeId = promo.cmsDiscountTypeId,
      minRooms = promotionRestriction.flatMap(_.minRooms).getOrElse(0),
      minNightStay = promotionRestriction.flatMap(_.minNights).getOrElse(0),
      bookOn = promotionRestriction.flatMap(_.bookOn).getOrElse(""),
      bookFrom = promotionRestriction.flatMap(_.bookFrom),
      bookTo = promotionRestriction.flatMap(_.bookTo),
      minAdvPurchase = promotionRestriction.flatMap(pr => getAdvPurchase(pr.minAdvance)),
      maxAdvPurchase = promotionRestriction.flatMap(pr => getAdvPurchase(pr.maxAdvance)),
      bookTimeFrom = promotionRestriction.flatMap(d => toYcsTime(d.bookTimeFrom)).filter(_ > 0),
      bookTimeTo = promotionRestriction.flatMap(d => toYcsTime(d.bookTimeTo)).filter(_ > 0),
      applyDates = applyDates,
      cancellationCode = promoCxlCode,
      customerSegments = promotionRestriction.map(_.customerSegments).getOrElse(List.empty),
      isStackable = promo.isStackable,
      isAllowStack = promo.isStackCombine,
      stackDiscountOption = Some(StackDiscountOption.getFromValue(promo.stackableDiscountType)).filter(s =>
        s != StackDiscountOption.Unknown),
      isAllowChannelDiscount = promo.isApplyChannelDiscount,
      isRatePlanAsPromotion = promo.isRatePlanAsPromotion,
    )
  }

  private val SAME_ALL_DAYS_INDEX = -1

  private[pricing] def buildSurchargeEntry(checkIn: DateTime,
                                           lengthOfStay: Int,
                                           surcharges: Map[Int, proto.Surcharge],
                                           channelRate: ChannelRate) = {
    val isSameForAllStayDate =
      channelRate.surchargePerDay.contains(SAME_ALL_DAYS_INDEX) && channelRate.surchargePerDay.size == 1

    def surchargeFromIdentifier(identifier: Identifiers, dates: Set[DateTime], occ: Int = 0): Seq[SurchargeEntry] =
      identifier.identifiers.flatMap(id => surcharges.get(id)).map { s =>
        val appliedDates = if (s.applyTo.contains("PB")) Set(checkIn) else dates
        SurchargeEntry(
          id = s.surchargeId,
          applyTo = s.applyTo,
          option = ChargeOption.getFromValue(s.applyType.value),
          dates = appliedDates,
          isAmount = s.isAmount,
          isCommissionable = s.isCommissionable,
          value = s.value,
          occFromProto = occ,
          isPropOffer = true,
        )
      }

    val sameForAllDay = channelRate.surchargePerDay
      .get(SAME_ALL_DAYS_INDEX)
      .map { identifier =>
        val dates = (0 until lengthOfStay).map(index => checkIn.plusDays(index)).toSet
        surchargeFromIdentifier(identifier, dates)
      }
      .getOrElse(Nil)

    val dailySurcharges: Seq[SurchargeEntry] =
      if (isSameForAllStayDate) {
        sameForAllDay
      } else {
        (0 until lengthOfStay)
          .flatMap { index =>
            val stayDate = checkIn.plusDays(index)
            channelRate.surchargePerDay
              .get(index)
              .map(identifier => identifier.identifiers.map(id => (id, stayDate)))
              .getOrElse(Nil)
          }
          .groupBy(_._1)
          .flatMap { case (id, dates) => surchargeFromIdentifier(Identifiers(Seq(id)), dates.map(_._2).toSet) }(
            collection.breakOut)
      }

    val occSurcharges = channelRate.prices.flatMap { price =>
      if (price.occupancyPrices.exists(_.surcharges.nonEmpty)) {
        val dates = numericToSet(price.stayDates).map(index => checkIn.plusDays(index))

        price.occupancyPrices.flatMap { occPrice =>
          occPrice.surcharges.map { surchargeIdentifier =>
            val occs = numericToSet(occPrice.occupancies)
            occs.flatMap { occ =>
              surchargeFromIdentifier(surchargeIdentifier, dates, occ)
            }
          }
        }.flatten
      } else {
        Seq.empty
      }
    }

    dailySurcharges ++ occSurcharges.distinct

  }

  private def numericToSet(numeric: Seq[NumericRange]): Set[Int] =
    numeric.flatMap(stay => (stay.start to stay.end).map(i => i))(breakOut)

  private[pricing] def buildDailyPrices(checkIn: DateTime,
                                        roomTypeId: RoomTypeId,
                                        rateCategoryId: PropOfferRateCategoryId,
                                        taxLookUp: TaxPerDayLookUp,
                                        surcharges: List[SurchargeEntry],
                                        channelRate: ChannelRate,
                                        reqOcc: YplReqOccByHotelAgePolicy,
                                        childAgeRanges: Seq[MetaChildAgeRange],
                                        hmcMasterHotelContext: HmcMasterHotelContext,
                                        rateTypeLoaded: RateType = RateType.NetExclusive,
                                        currencyCode: String = "USD",
                                        parentCh: Option[ChannelRate] = None,
                                        useJpChildRateOverAgeRanges: Boolean = false,
                                        commissionHolder: CommissionHolder = CommissionHolder.default,
                                        extrabedPerPerson: List[PerPersonPrice] = Nil,
                                        propertyRateType: RateType = RateType.Unknown,
                                        protoChildAgeRanges: List[ChildAgeRange] = Nil,
                                        maxUspaDiscountPercent: Double = 0.0)(implicit
    ctx: YplContext): Map[StayDate, DailyPrice] = {
    val dailyPrices = {
      val applyChannelDiscountAllDay =
        channelRate.channelDiscountPerDay.size == 1 && channelRate.channelDiscountPerDay.contains(SAME_ALL_DAYS_INDEX)
      val (childRateAgeRanges, normalAgeRanges) =
        if (useJpChildRateOverAgeRanges) {
          childAgeRanges.partition(_.childAgeRangeType == ChildAgeRangeType.ChildRate)
        } else (Seq.empty, childAgeRanges)
      val childRatePricingInfo = createChildRatePriceEntries(
        childRate = parentCh.getOrElse(channelRate).childRate,
        childAgeRanges = childRateAgeRanges,
        useJpChildRateOverAgeRanges = useJpChildRateOverAgeRanges,
      )

      val hasChildren = reqOcc.occ.isSearchHasChildren

      val childPriceEntriesMap: Map[Int, Seq[ChildPriceEntry]] = createChildPriceEntriesMap(
        childAgeRangeRatesDaily = parentCh.getOrElse(channelRate).childAgeRangeRatesDaily,
        childAgeRanges = normalAgeRanges,
      )

      parentCh
        .getOrElse(channelRate)
        .prices
        .flatMap { price =>
          price.stayDates.flatMap { stayDate =>
            (stayDate.start to stayDate.end).flatMap { index =>
              val stayDate = checkIn.plusDays(index)
              val channelDiscount = buildChannelDiscount(stayDate,
                                                         index,
                                                         roomTypeId,
                                                         rateCategoryId,
                                                         applyChannelDiscountAllDay,
                                                         channelRate,
                                                         hmcMasterHotelContext)
              val supplierFundedDiscountPercentage = channelRate.supplierFundedDiscount.map(_.discountPercentage)
              val priceEntries = price.occupancyPrices.flatMap { occPrice =>
                occPrice.occupancies.flatMap { occupancyRange =>
                  (occupancyRange.start to occupancyRange.end).map { occ =>
                    createPriceEntry(
                      reqOcc,
                      stayDate,
                      occ,
                      occPrice.amount,
                      rateTypeLoaded,
                      currencyCode,
                      ctx.request.isBookingRequest,
                      ChargeType.Room,
                      maxUspaDiscount = occPrice.amount * maxUspaDiscountPercent / 100.0,
                    )
                  }
                }
              }
              // TODO: revise logic extraBedPriceEntry should be taken when extrabedPriceEntryWithAgeRange is empty
              val extraBedPriceEntry =
                if (price.extraBedPrice > 0d) {
                  Seq(createPriceEntry(
                    reqOcc = reqOcc,
                    stayDate = stayDate,
                    occupancy = 0,
                    amountPerDay = price.extraBedPrice,
                    rateType = rateTypeLoaded,
                    currencyCode = currencyCode,
                    isEnableCalculationBreakdown = ctx.request.isBookingRequest,
                    chargeType = ChargeType.ExtraBed,
                  ))
                } else Nil
              val childPriceEntries: Seq[ChildPriceEntry] =
                if (useJpChildRateOverAgeRanges && childRatePricingInfo.nonEmpty) childRatePricingInfo
                else childPriceEntriesMap.getOrElse(
                  index,
                  // do not auto gen child rate from charge age range, not sure what we put this in the 1ts place
                  // this impact logic to apply which converter new ycs occupancy logic if we should use child free mode or child rate model
                  // the auto gen child rate from charge age range is also considered as invalid as it has no price inside but with flat amount type.
                  Seq.empty,
                )
              createDP(
                taxLookUp,
                surcharges,
                stayDate,
                priceEntries ++ extraBedPriceEntry,
                childPriceEntries,
                channelDiscount,
                commissionHolder,
                supplierFundedDiscountPercentage,
              )
            }
          }
        }
        .sortBy(_._1.getMillis)
        .toMap // we need to sort after price is exploded
    }
    dailyPrices
  }

  private def createChildPriceEntriesMap(childAgeRangeRatesDaily: Seq[ChildAgeRangeRateDaily],
                                         childAgeRanges: Seq[MetaChildAgeRange]): Map[Int, Seq[ChildPriceEntry]] =
    childAgeRangeRatesDaily.flatMap { childRatesDaily =>
      childRatesDaily.stayDates.flatMap { stayDate =>
        (stayDate.start to stayDate.end).map { index =>
          val childPriceEntries = childAgeRanges.map { childAgeRange =>
            val childAgeRangeRates =
              childRatesDaily.childAgeRangeRates.find(_.ageRangeId == childAgeRange.childAgeRangeId)
            val price = childAgeRangeRates.map(_.price)
            val pricingTypeId = PricingChildRateType.getFromValue(
              childAgeRangeRates.flatMap(_.pricingTypeId).getOrElse(PricingChildRateType.FlatPrice.value))
            ChildPriceEntry(value = price,
                            pricingTypeId = pricingTypeId,
                            childAgeRangeId = childAgeRange.childAgeRangeId,
                            ageFrom = childAgeRange.ageFrom,
                            ageTo = childAgeRange.ageTo)
          }
          index -> childPriceEntries
        }
      }
    }(collection.breakOut)

  private[pricing] def createChildRatePriceEntries(childRate: Option[ChildRate],
                                                   childAgeRanges: Seq[MetaChildAgeRange],
                                                   useJpChildRateOverAgeRanges: Boolean): Seq[ChildPriceEntry] =
    if (useJpChildRateOverAgeRanges) {
      childRate match {
        case Some(c) if c.isChildRateEnabled =>
          childAgeRanges
            .sortBy(_.childAgeRangeId)(Ordering[Int].reverse)
            .foldLeft(List.empty[ChildPriceEntry]) { case (result, childAgeRange) =>
              val id = childAgeRange.childAgeRangeId
              val childRate = c.childPricing.find(_.childRateTypeId == id)
              val ageFrom = result
                .find(_.childAgeRangeId == childAgeRange.childAgeRangeId)
                .map(_.ageFrom)
                .getOrElse(childAgeRange.ageFrom)
              val pricingTypeId = PricingChildRateType.getFromValue(
                childRate.map(_.pricingTypeId).getOrElse(PricingChildRateType.FlatPrice.value))
              val priceEntry =
                if (childRate.isEmpty) {
                  ChildPriceEntry(
                    value = None,
                    pricingTypeId = PricingChildRateType.Unknown,
                    childAgeRangeId = id - 1,
                    ageFrom = ageFrom,
                    ageTo = childAgeRange.ageTo,
                    isCountAsRoomOcc = false,
                    childAgeRangeType = ChildAgeRangeType.ChildRate,
                  )
                } else {
                  ChildPriceEntry(
                    value = childRate.map(_.amount),
                    pricingTypeId = pricingTypeId,
                    childAgeRangeId = id,
                    ageFrom = ageFrom,
                    ageTo = childAgeRange.ageTo,
                    isCountAsRoomOcc = childRate.exists(_.isCountAsRoomOcc),
                    childAgeRangeType = ChildAgeRangeType.ChildRate,
                  )
                }

              result.filterNot(_.childAgeRangeId == childAgeRange.childAgeRangeId) ++ List(priceEntry)
            }
            .filterNot(_.childAgeRangeId == 0) // remove base fallback
        case _ => Seq.empty
      }
    } else Seq.empty

  private[pricing] def createPriceEntry(reqOcc: YplReqOccByHotelAgePolicy,
                                        stayDate: DateTime,
                                        occupancy: Int,
                                        amountPerDay: Double,
                                        rateType: RateType,
                                        currencyCode: String,
                                        isEnableCalculationBreakdown: Boolean = false,
                                        chargeType: ChargeType,
                                        subChargeType: SubChargeType = SubChargeType.None,
                                        maxUspaDiscount: Double = 0.0): PriceEntry = {
    val applyTo =
      if (chargeType == ChargeType.ExtraBed) {
        PGPN
      } else {
        PRPN
      }

    PriceEntry(
      stayDate,
      chargeType,
      ChargeOption.Mandatory,
      applyTo,
      occupancy,
      amountPerDay,
      quantity = if (reqOcc.isFreeOcc) 1 else reqOcc.rooms,
      rateLoadedPrice = amountPerDay,
      latestBreakdownStep = BreakdownStep.BaseStep,
      subChargeType = subChargeType,
      priceBreakdownHistory = BookingPriceBreakdown(isEnableCalculationBreakdown).newBreakdown(chargeType,
                                                                                               rateType,
                                                                                               currencyCode,
                                                                                               amountPerDay),
      maxUspaDiscount = maxUspaDiscount,
    )
  }

  private[pricing] def toTimeInterval(
    hourlyAvailability: HourlyAvailability.HourlyAvailableSlot): Option[TimeInterval] = hourlyAvailability.from.map {
    hourlyFrom =>
      // convert from hourlyAvailability object into TimeInterval HH:mm string format
      val hoursString = hourlyFrom.hours.toString
      val minutesString = hourlyFrom.minutes.toString

      TimeInterval(
        duration = hourlyAvailability.durationInMinute / 60,
        from = hoursString + ":" + minutesString,
      )
  }

  private[pricing] def createDP(taxLookUp: TaxPerDayLookUp,
                                surcharges: List[SurchargeEntry],
                                stayDate: DateTime,
                                priceEntry: Seq[PriceEntry],
                                childPriceEntries: Seq[ChildPriceEntry],
                                channelDiscount: Option[Double],
                                commissionHolder: CommissionHolder = CommissionHolder.default,
                                supplierFundedDiscountPercentage: Option[Double] = None)(implicit ctx: YplContext) = {
    val taxes = taxLookUp.get(stayDate).map(_.map { case (key, tax) => key -> tax.value })
    val filteredSurcharge =
      surcharges.filter(s => isValidSurcharge(stayDate, s.applyTo)(ctx.request) && s.dates.contains(stayDate))

    if (commissionHolder.daily.contains(stayDate)) {
      Some(
        stayDate -> DailyPrice(
          date = stayDate,
          taxes = taxes.getOrElse(Map.empty),
          isPromotionBlackOut = false,
          prices = priceEntry.toList,
          channelDiscount = channelDiscount,
          rpmSurcharges = filteredSurcharge,
          childPrices = childPriceEntries.toList,
          supplierFundedDiscountPercentage = supplierFundedDiscountPercentage,
        ))
    } else None
  }

  private def getPropOfferCxlCode(channelRate: ChannelRate, po: PropertyOffer): Option[String] = {
    val cxlCode = channelRate.cancelationPolicy.map(_.cancelCodeIdentifier).flatMap(po.cancelPolicies.get).map(_.code)
    cxlCode
  }

  // the value will be used for AGP for the case of CommissionEquivalent
  // Potential bug
  // we assume the hotel will always load a rate with retail (channelId: 1) for all languageId (id: 0)
  // what if there is none
  // or multiple found as the schema does not enforce its uniqueness
  private[pricing] def getRetailGlobalContractedCommission(commissions: Map[Int, ProtoCommission]): Double = commissions
    .withFilter { case (_, protoCommission) => protoCommission.channelId == 1 && protoCommission.languageId == 0 }
    .map { case (_, protoCommission) => protoCommission.contractedCommission }
    .headOption
    .getOrElse(0.0d)
}

// scalastyle:on

object SimpleDateUtils {
  // TODO: Verify timezone in calc - it should be without any offset
  final val BangkokZoneId: ZoneId = ZoneId.of("Asia/Bangkok")

  implicit class SimpleDateToDateTime(val sd: SimpleDate) extends AnyVal {
    def toDateTime = new DateTime(sd.year, sd.month, sd.day, 0, 0, 0)
  }

  implicit class TimeOfDayToLocalDate(val tod: TimeOfDay) extends AnyVal {

    def toMillisDateToday: Long = {
      val timeAtDateToday = LocalTime.of(tod.hours, tod.minutes, tod.seconds)
      timeAtDateToday.atDate(LocalDate.now()).atZone(BangkokZoneId).toInstant.toEpochMilli
    }

    def toTimeOfDayString: String = {
      val timeAtDateToday = LocalTime.of(tod.hours, tod.minutes, tod.seconds)
      timeAtDateToday.truncatedTo(ChronoUnit.SECONDS).format(DateTimeFormatter.ISO_LOCAL_TIME)
    }

    def getMillisOfDay: Long =
      tod.hours * MILLIS_PER_HOUR + tod.minutes * MILLIS_PER_MINUTE + tod.seconds * MILLIS_PER_SECOND
  }
}

object PropertyOfferConverter {
  type TaxId = Int
  type TaxProtoTypeId = Int
  type TaxValue = Double
  type TaxPerDayLookUp = Map[DateTime, Map[(TaxId, TaxProtoTypeId), Tax]]
}
