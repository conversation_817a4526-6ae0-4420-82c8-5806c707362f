package com.agoda.papi.ypl.pricing

import com.agoda.finance.tax.models.PriceForAssumeTax
import com.agoda.finance.tax.services.tax._
import com.agoda.papi.enums.hotel.{ChildrenStayFreeType, PaymentModel, StayPackageTypes}
import com.agoda.papi.enums.room._
import com.agoda.papi.pricing.pricecalculation.models.tax.DailyTaxes
import com.agoda.papi.pricing.pricecalculation.utils.CommonTaxConverter.toCommonChargeType
import com.agoda.papi.ypl.commission.CommissionUtils
import com.agoda.papi.ypl.commission.service.CommissionService
import com.agoda.papi.ypl.models.CommonTaxConverter.toCommonReqOccByHotelAgePolicy
import com.agoda.papi.ypl.models.api.request.YplAGXCommission
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.hotel._
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.pricing.{BookingPriceBreakdown, YplPrice}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{
  ChainId,
  CityId,
  CountryId,
  EnabledRoom,
  GmtOffset,
  HotelId,
  RoomFeatures,
  RoomTypeId,
  SupplierId,
  YPLRoom,
  YplChannel,
  YplContext,
  YplDispatchChannels,
  YplExperiments,
  YplReqOccByHotelAgePolicy,
  YplRoomEntry,
}
import com.agoda.papi.ypl.occupancy.models.YcsChildAgeRange
import com.agoda.papi.ypl.pricing.PriceCalculator._
import com.agoda.papi.ypl.services.TaxPrototypeService
import com.agoda.utils.flow.PropertyContext
import com.typesafe.scalalogging.LazyLogging
import models.consts.ABTest
import org.joda.time.DateTime

trait PriceCalculatorImpl
  extends PriceCalculator
    with HybridPrice
    with PriceCalculation
    with CommissionService
    with CancellationPolicyServiceImpl
    with CancellationPolicyWithExperimentHelper
    with LazyLogging {
  self: TaxCalculator with TaxPrototypeService =>

  override def calculateDailyPrices(
    room: YplRoomEntry,
    paymentModel: PaymentModel,
    taxInfo: TaxInfo,
    agePolicy: AgePolicy,
    ignoreRequestedNumberOfRoomsForNha: Boolean,
    supplierId: SupplierId,
    applyDiscountsMultiplicatively: Boolean,
    isPull: Boolean = false,
    priceForAssumeTaxMap: Map[DateTime, Double] = Map.empty,
    isChannelDiscountStep: Boolean = false,
    agencyFeatures: Option[AgodaAgencyFeatures] = None,
    hotelId: HotelId,
    chainId: ChainId,
    countryId: CountryId,
    cityId: CityId,
    childAgeRange: List[YcsChildAgeRange],
    isUseChildAgeRange: Boolean = false)(implicit ctx: YplContext, propertyContext: PropertyContext): List[YplPrice] = {

    val reqOcc = YplReqOccByHotelAgePolicy(ctx.request.occ,
                                           agePolicy,
                                           ignoreRequestedNumberOfRoomsForNha,
                                           childAgeRange,
                                           isUseChildAgeRange)

    val isBcomFixTaxAmountApplyToPB = (supplierId == DMC.BCOM)
    val isCleanedUpHospitalityTax = ctx.experimentContext.isPropertyB(propertyContext, YplExperiments.CLEAN_HP_TAX)

    room.dailyPrices.values.flatMap { dailyPrice =>
      val dailyTaxes = DailyTaxes(
        taxes = createTaxesWithValuesForDailyTaxes(taxInfo, dailyPrice.taxes),
        isCleanedUpHospitalityTax,
      )

      val hasTaxPrototypeLevels = dailyTaxes.hasTaxProtoTypeLevels

      val priceForAssumeTax =
        if (hasTaxPrototypeLevels) {
          getPriceForAssumeTax(dailyPrice.prices)
        } else 0.0
      dailyPrice.prices.map { priceEntry =>
        val occupancyForCommission =
          priceEntry.roomNo.flatMap(room.roomAllocationInfo.get).map(_.totalRoomOcc).getOrElse(priceEntry.occupancy)
        val commissionFromCommissionHolder: Commission = getCommissionForPriceCalculation(
          commissionHolder = room.commissionHolder,
          stayDate = dailyPrice.date,
          occupancy = occupancyForCommission,
          isAgodaAgency = room.isAgodaAgency,
          applicableMORPCandidateRoomParameters = room.applicableMORPCandidateRoomParameters,
          originalRateType = room.originalRateType,
          targetRateType = room.rateType,
          excludeWholesaleOrAgx = false,
        )

        val agxCommissionToUse = getAgxAdjustment(room, dailyPrice.date)

        val newDailyTaxes =
          if ((priceEntry.chargeType == ChargeType.Room || priceEntry.chargeType == ChargeType.ExtraBed) && hasTaxPrototypeLevels) {
            // assume tax from priceEntry.value as room price rateloadedPrice
            val rateType = room.originalRateType
            val commission =
              if (room.rateType == room.originalRateType) commissionFromCommissionHolder
              else
                CommissionUtils.normalizeCommission(room.rateType,
                                                    rateType,
                                                    commissionFromCommissionHolder,
                ) // If rate type is change, we convert commission back to original rate type
            val channelDiscountPercent = room.assumeChannelDiscount.getOrElse(priceEntry.date, 0d)
            priceForAssumeTaxMap.get(priceEntry.date) match {
              case Some(assumeSellEx) => assumeTaxValue(RateType.SellExclusive,
                                                        assumeSellEx,
                                                        commission,
                                                        dailyTaxes,
                                                        0.0,
                                                        isCleanedUpHospitalityTax = isCleanedUpHospitalityTax)
              case _ => assumeTaxValue(rateType,
                                       priceForAssumeTax,
                                       commission,
                                       dailyTaxes,
                                       channelDiscountPercent,
                                       isCleanedUpHospitalityTax = isCleanedUpHospitalityTax)
            }
          } else {
            dailyTaxes
          }

        val priceBreakdownWithProtoPriceAndDiscount = priceEntry.getUpdatedPriceBreakdownHistory(
          room,
          applyDiscountsMultiplicatively,
          agencyFeatures,
          room.commissionHolder.daily.get(dailyPrice.date),
          commissionFromCommissionHolder)
        val currentBreakdownStep = BookingPriceBreakdown.getCurrentBreakdownStep(priceBreakdownWithProtoPriceAndDiscount,
                                                                                 isChannelDiscountStep,
                                                                                 priceEntry.latestBreakdownStep)
        val excludeWholesaleOrAgx = true
        val roomPriceInfo =
          if (priceEntry.chargeType == ChargeType.ExtraBed && (priceEntry.rateType != RateType.Unknown))
            room.toRoomPriceInfo.copy(rateType = priceEntry.rateType)
          else room.toRoomPriceInfo
        if (ctx.experimentContext.isPropertyB(propertyContext, ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION)) {
          calculatePriceForPriceCalculationRefactor(
            paymentModel = paymentModel,
            date = dailyPrice.date,
            // using occupancy commission if exist else using rateplan commission
            commissionPercent = commissionFromCommissionHolder,
            agxCommission = agxCommissionToUse,
            commissionExcludingWholesaleOrAgx = getCommissionForPriceCalculation(
              commissionHolder = room.commissionHolder,
              stayDate = dailyPrice.date,
              occupancy = occupancyForCommission,
              isAgodaAgency = room.isAgodaAgency,
              applicableMORPCandidateRoomParameters = room.applicableMORPCandidateRoomParameters,
              originalRateType = room.originalRateType,
              targetRateType = room.rateType,
              excludeWholesaleOrAgx = excludeWholesaleOrAgx,
            ),
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            dailyTaxes = newDailyTaxes,
            reqOcc = toCommonReqOccByHotelAgePolicy(reqOcc),
            chargeType = priceEntry.chargeType,
            quantity = priceEntry.quantity,
            applyType = ApplyType.getFromValue(priceEntry.applyTo),
            chargeOption = priceEntry.chargeOption,
            promoDiscount = priceEntry.promoDiscount,
            valueWithChannelDiscount = priceEntry.valueWithChannelDiscount,
            discountMessages = priceEntry.discountMessages,
            roomPriceInfo = roomPriceInfo,
            supplierId = supplierId,
            subChargeType = priceEntry.subChargeType,
            roomNo = priceEntry.roomNo,
            supplierContractedCommissionFromCommissionHolder = room.commissionHolder.supplierContractedCommission,
            channelDiscountBreakdowns = priceEntry.channelDiscountBreakdowns,
            resellRefSell = priceEntry.resellRefSell,
            currentBreakdownStep = currentBreakdownStep,
            bookingPriceBreakdown = priceBreakdownWithProtoPriceAndDiscount,
            childAgeRangeId = priceEntry.childAgeRangeId,
            hotelId = hotelId,
            chainId = chainId,
            countryId = countryId,
            cityId = cityId,
            supplierFundedDiscountAmount = priceEntry.supplierFundedDiscountAmount,
            uspaDiscountAmount = None,
            uspaProgramId = None,
            isApplyTaxOnSellEx = ctx.isApplyTaxOnSellEx(supplierId,
                                                        hotelId,
                                                        chainId,
                                                        countryId,
                                                        paymentModel.i,
                                                        roomPriceInfo.originalRateType),
            lengthOfStay = ctx.request.lengthOfStay,
            storefrontId = ctx.request.cInfo.storeFront.getOrElse(0),
            whitelabelId = ctx.request.whitelabelSetting.whitelabelID,
            isThirdPartySupplier = ctx.isThirdParty(supplierId),
            applyTaxOverHelper = ctx.request.applyTaxOverHelper,
            experimentContext = ctx.experimentContext,
          )
        } else {
          calculatePrice(
            paymentModel = paymentModel,
            date = dailyPrice.date,
            // using occupancy commission if exist else using rateplan commission
            commissionPercent = commissionFromCommissionHolder,
            agxCommission = agxCommissionToUse,
            commissionExcludingWholesaleOrAgx = getCommissionForPriceCalculation(
              commissionHolder = room.commissionHolder,
              stayDate = dailyPrice.date,
              occupancy = occupancyForCommission,
              isAgodaAgency = room.isAgodaAgency,
              applicableMORPCandidateRoomParameters = room.applicableMORPCandidateRoomParameters,
              originalRateType = room.originalRateType,
              targetRateType = room.rateType,
              excludeWholesaleOrAgx = excludeWholesaleOrAgx,
            ),
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            dailyTaxes = newDailyTaxes,
            reqOcc = reqOcc,
            chargeType = priceEntry.chargeType,
            quantity = priceEntry.quantity,
            applyType = ApplyType.getFromValue(priceEntry.applyTo),
            chargeOption = priceEntry.chargeOption,
            promoDiscount = priceEntry.promoDiscount,
            value = priceEntry.value,
            valueWithChannelDiscount = priceEntry.valueWithChannelDiscount,
            discountMessages = priceEntry.discountMessages,
            roomPriceInfo = roomPriceInfo,
            supplierId = supplierId,
            subChargeType = priceEntry.subChargeType,
            roomNo = priceEntry.roomNo,
            supplierContractedCommissionFromCommissionHolder = room.commissionHolder.supplierContractedCommission,
            channelDiscountBreakdowns = priceEntry.channelDiscountBreakdowns,
            resellRefSell = priceEntry.resellRefSell,
            currentBreakdownStep = currentBreakdownStep,
            bookingPriceBreakdown = priceBreakdownWithProtoPriceAndDiscount,
            childAgeRangeId = priceEntry.childAgeRangeId,
            hotelId = hotelId,
            chainId = chainId,
            countryId = countryId,
            supplierFundedDiscountAmount = priceEntry.supplierFundedDiscountAmount,
            uspaDiscountAmount = None,
            uspaProgramId = None,
          )(isBcomFixTaxAmountApplyToPB)
        }
      }
    }(collection.breakOut)
  }

  private[pricing] def getAgxAdjustment(room: YplRoomEntry, stayDate: DateTime): YplAGXCommission = {
    val agxCommissionHolder = getAgxCommissionHolder(room.commissionHolder,
                                                     stayDate,
                                                     room.isAgodaAgency,
                                                     room.applicableMORPCandidateRoomParameters)
    YplAGXCommission(
      payAsYouGoCommission = agxCommissionHolder.payAsYouGoCommission,
      prepaidCommission = agxCommissionHolder.prepaidCommission,
      freeTrialCommission = agxCommissionHolder.freeTrialCommission,
      isError = false,
    )
  }

  def getPriceForAssumeTax(prices: List[PriceEntry]): Double = GraduatedTaxCalculator.getPriceForAssumeTax(
    prices.map(price =>
      new PriceForAssumeTax(price.rateLoadedPrice,
                            price.promoDiscount,
                            price.quantity,
                            toCommonChargeType(price.chargeType))))

  private[pricing] def getRoomLevelPaymentModel(room: YplRoomEntry,
                                                hotelLevelPaymentModel: PaymentModel): PaymentModel =
    room.paymentModel.getOrElse(hotelLevelPaymentModel)

  private[pricing] def getHasDispatchedStackChannels(dispatchChannels: YplDispatchChannels)(implicit
    ctx: YplContext): Boolean = dispatchChannels.masterChannels.size > 1

  private[pricing] def getIsDomesticOnly(supplierId: Int, channel: YplChannel, dispatchChannels: YplDispatchChannels)(
    implicit ctx: YplContext): Boolean =
    supplierId == DMC.YCS && (channel.contains(Channel.Domestic) || getHasDispatchedStackChannels(
      dispatchChannels) && Channel.GoLocalPhrase1.contains(channel.baseChannelId))

  // will be removed after integrating NEP-25984
  private[pricing] def hpTaxExist(prices: List[YplPrice], isCleanedUpHospitalityTax: Boolean): Boolean =
    if (isCleanedUpHospitalityTax) false
    else prices.exists(p => p.dailyTaxes.taxes.exists(t => t.tax.option == ChargeOption.HospitalityPrice))

  override def doPricing(hotelId: HotelId,
                         supplierId: SupplierId,
                         chainId: ChainId,
                         countryId: CountryId,
                         cityId: CityId,
                         paymentModel: PaymentModel,
                         taxInfo: TaxInfo,
                         room: YplRoomEntry,
                         dailyPrices: List[YplPrice],
                         dispatchChannels: YplDispatchChannels,
                         reqOcc: YplReqOccByHotelAgePolicy,
                         fixMarriottSurchargeExp: Boolean,
                         surchargeRateType: RateType = RateType.Unknown,
                         isOTASupplier: Option[Boolean] = None,
                         isPull: Boolean,
                         supplierContractedCommission: Option[Double] = None,
                         gmtOffset: GmtOffset = 0,
                         gmtOffsetMinutes: GmtOffset = 0,
                         enabledRoomMap: Map[RoomTypeId, EnabledRoom] = Map.empty)(implicit
    ctx: YplContext,
    propertyContext: PropertyContext): YPLRoom = {

    val isCleanedUpHospitalityTax = ctx.experimentContext.isPropertyB(propertyContext, YplExperiments.CLEAN_HP_TAX)

    implicit val roomEntry = room
    implicit val t = taxInfo
    implicit val r = ctx.request

    //  ToDO: get rid of iterable stuff

    val prices = dailyPrices
    val surchargePrices: Iterable[YplPrice] = getRPMSurchargePrices(
      paymentModel,
      taxInfo,
      room,
      prices,
      surchargeRateType,
      reqOcc,
      isPull = isPull,
      supplierId = supplierId,
      supplierContractedCommission,
      hotelId = hotelId,
      chainId = chainId,
      countryId = countryId,
      cityId = cityId,
      fixMarriottSurchargeExp = fixMarriottSurchargeExp,
    )

    val allPrices = (if (Settings.isHybrid(supplierId)) toHybridPrices(prices) else prices) ++ surchargePrices

    val hpTaxExisted = hpTaxExist(prices, isCleanedUpHospitalityTax)

    val fallbackCommission = getMarginPercentage(room.commissionHolder, room.originalRateType)

    val finalCommission = if (hpTaxExisted) fallbackCommission else calculateCommission(prices, fallbackCommission)

    val isDomesticOnly = getIsDomesticOnly(supplierId, room.channel, dispatchChannels)

    val shouldFixPromotionsBreakdown = ctx.experimentContext.isUserB(YplExperiments.FIX_PROMOTIONS_BREAKDOWN)
    val promotion = room.promotion.map(toPromotion(_, shouldFixPromotionsBreakdown))
    val promotionsBreakdown = room.promotionsBreakdown.map { case (date, promos) =>
      date -> promos.map(toPromotion(_, shouldFixPromotionsBreakdown, Some(date.toDateTimeAtStartOfDay())))
    }

    //  Note that the real status (could be helper or dispatched) will be set later in the flow
    val roomStatus = getRoomStatus(room, dispatchChannels)

    val roomLevelPaymentModel = getRoomLevelPaymentModel(room, paymentModel)

    val cancellationFees = getPAPICancellationFees(room.cxlCode)

    val cancellationCondition =
      getCancellationCondition(room.cxlCode, cancellationFees, gmtOffset, gmtOffsetMinutes)(ctx)

    val paymentChannels = room.rateCategory.paymentChannels

    val benefitList = getBenefitList(room.rateCategory.benefitList)

    // TODO: for reading, maybe we can put this logic in a separate function or service.
    val originalMasterId = enabledRoomMap.get(room.roomTypeId).map(_.masterRoomId).getOrElse(room.roomTypeId)
    val preMappedMasterRoomId = if (originalMasterId == 0) Some(room.roomTypeId) else Some(originalMasterId)

    YPLRoom(
      hotelId = hotelId,
      roomTypeId = room.roomTypeId,
      supplierId = supplierId,
      channel = room.channel,
      language = ctx.request.cInfo.language,
      currency = room.currency,
      checkIn = ctx.request.checkIn,
      lengthOfStay = ctx.request.lengthOfStay,
      occ = room.occEntry,
      marginPercentage = finalCommission,
      cxlCode = room.cxlCode,
      allotment = room.remainingRooms,
      breakfast = room.isBreakFastIncluded,
      prices = allPrices,
      priceDiffBetweenAPOAndLeastExpensiveRoom = BD_ZERO,
      rateCategoryId = room.rateCategoryId,
      paymentModel = roomLevelPaymentModel,
      allotmentRatePlan = room.allotmentRatePlan,
      dmcRoomId = room.roomType.dmcRoomId,
      rateType = room.rateType,
      reqOcc = Some(setRespectMaxOccupancyToChildrenStayFreeType(reqOcc, room)),
      rateCategory = getRateCategory(room.rateCategory, room.currency),
      roomStatus = roomStatus,
      paymentOptions = room.paymentOptions,
      dmcRatePlanID = room.rateCategory.dmcRatePlanID,
      dmcMealPlanID = room.rateCategory.dmcMealPlanID,
      cancellationFeeList = Some(cancellationFees.toList),
      roomFeatures = RoomFeatures(
        isDomesticOnly = isDomesticOnly,
        isAgodaAgency = room.isAgodaAgency,
        isAllOccSearch = r.flagInfo.isAllOcc,
        isAveragePrice = room.isAveragePrice,
        isRoomTypeNotGuarantee = room.rateCategory.isRoomTypeNotGuarantee,
        haveMultiRoomPromotion = room.haveMultiRoomPromotion,
        isTrueAgencyRateForking = room.isAgencyEnabled,
        isMultipleRoomAssignmentPrice = room.isMultipleRoomAssignmentPrice,
        isOverrideChildTypeRequest = room.isOverrideChildTypeRequest,
        isJapanChildRateApplied = room.isJapanChildRateApplied,
        isBookOnRequest = room.isBookOnRequest,
        isKeptForFlexibleMultiRoom = room.isKeptForFlexibleMultiRoom,
      ),
      cor = COR(displayedRackRate = room.displayedRackRate),
      discountInfo = DiscountInfo(
        promotion = promotion,
        promotionsBreakdown = promotionsBreakdown,
      ),
      actingAsYcs = isOTASupplier,
      originalRateType = room.originalRateType,
      dmcDataHolder = room.dmcDataHolder,
      processingFeePercent = room.processingFees,
      cxlChargeSetting = room.cxlChargeSetting,
      cancellationCondition = cancellationCondition,
      yplRoomEntry = room,
      taxInfo = Some(taxInfo),
      paymentChannels = paymentChannels,
      hourlyAvailableSlots = room.hourlyAvailableSlots,
      confirmByMins = room.confirmByMins,
      childRateSettings = room.rateCategory.childRate,
      roomAllocationInfo = room.roomAllocationInfo,
      checkInInformation = room.checkInInformation,
      stayPackageType = room.rateCategory.stayPackageType.map(v => StayPackageTypes.getFromValue(v.value)),
      fences = room.fences,
      rateCategoryInfo = room.rateCategoryInfo,
      resellExternalData = room.resellExternalData,
      roomDataChangeTracker = room.roomDataChangeTracker,
      isCheckInTimeRequired = CheckInTimeHelper.getCheckInTimeRequiredFlag(room.rateCategory,
                                                                           benefitList,
                                                                           room.checkInInformation,
                                                                           supplierId)(ctx),
      occupancyBreakdown = room.occupancyBreakdown,
      isAgodaBrand = ctx.supplierSetting.get(supplierId).exists(_.isAgodaBrand),
      masterRoomId = Some(originalMasterId),
      preMappedMasterRoomId = preMappedMasterRoomId,
      isDirectConnect = ctx.isDirectConnect(supplierId),
    )
  }

  private[pricing] def getRoomStatus(room: YplRoomEntry, dispatchChannels: YplDispatchChannels)(implicit
    ctx: YplContext): RatePlanStatus =
    if (dispatchChannels.isHelperChannel(room.channel)) {
      RatePlanStatus.Helper
    } else if (dispatchChannels.contains(room.channel)) {
      RatePlanStatus.Requested
    } else {
      RatePlanStatus.None
    }

  private[pricing] def setRespectMaxOccupancyToChildrenStayFreeType(hotelAgePolicy: YplReqOccByHotelAgePolicy,
                                                                    room: YplRoomEntry): YplReqOccByHotelAgePolicy =
    if (room.roomType.hasMaxAdultsAndChildrenOccupancy) {
      hotelAgePolicy.copy(agePolicy =
        hotelAgePolicy.agePolicy.copy(childrenStayFreeType = ChildrenStayFreeType.RespectMaxOcc))
    } else hotelAgePolicy

  private[pricing] def getRPMSurchargePrices(paymentModel: PaymentModel,
                                             taxInfo: TaxInfo,
                                             room: YplRoomEntry,
                                             prices: List[YplPrice],
                                             surchargeRateType: RateType,
                                             reqOcc: YplReqOccByHotelAgePolicy,
                                             isPull: Boolean,
                                             supplierId: SupplierId,
                                             supplierContractedCommission: Option[Double] = None,
                                             hotelId: HotelId,
                                             chainId: ChainId,
                                             countryId: CountryId,
                                             cityId: CityId,
                                             fixMarriottSurchargeExp: Boolean)(implicit
    ctx: YplContext,
    propertyContext: PropertyContext): Iterable[YplPrice] = {

    val isBcomFixTaxAmountApplyToPB = (supplierId == DMC.BCOM)
    val isPullAndNotFreeOcc = isPull && !ctx.supplierSetting.get(supplierId).exists(_.isOccFreePullSupplier)
    val rpmSurchargePrices = room.dailyPrices.values.flatMap { d =>
      val surcharges: List[SurchargeEntry] =
        getApplicableSurcharges(room, d, isPullAndNotFreeOcc, fixMarriottSurchargeExp)
      surcharges.flatMap { surcharge =>
        if (ctx.experimentContext.isPropertyB(propertyContext, ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION)) {
          calculateSurchargeForPriceCalculationRefactor(
            paymentModel,
            d,
            surchargeRateType,
            surcharge,
            getRoomPrices(prices, surcharge),
            taxInfo,
            room,
            reqOcc,
            isPull = isPull,
            supplierId = supplierId,
            supplierContractedCommission = supplierContractedCommission,
            hotelId = hotelId,
            chainId = chainId,
            countryId = countryId,
            cityId = cityId,
            fixMarriottSurchargeExp = fixMarriottSurchargeExp,
          )
        } else {
          calculateSurcharge(
            paymentModel,
            d,
            surchargeRateType,
            surcharge,
            getRoomPrices(prices, surcharge),
            taxInfo,
            room,
            reqOcc,
            isPull = isPull,
            supplierId = supplierId,
            supplierContractedCommission = supplierContractedCommission,
            hotelId = hotelId,
            chainId = chainId,
            countryId = countryId,
            fixMarriottSurchargeExp = fixMarriottSurchargeExp,
          )(isBcomFixTaxAmountApplyToPB)
        }
      }
    }
    rpmSurchargePrices
  }

  private[pricing] def getApplicableSurcharges(room: YplRoomEntry,
                                               dailyPrice: DailyPrice,
                                               isPullAndNotFreeOcc: Boolean,
                                               fixMarriottSurchargeExp: Boolean): List[SurchargeEntry] = {
    def matchOccFromProtoFilter(surcharge: SurchargeEntry): Boolean =
      surcharge.occFromProto == room.occFromProto || (surcharge.isPropOffer && surcharge.occFromProto == 0)
    if (!isPullAndNotFreeOcc && dailyPrice.rpmSurcharges.forall(d => d.occFromProto > 0 || d.isPropOffer)) {
      room.occupancyBreakdown match {
        // skip the filter here for "PR" applyType, as we will do it later on when we calculate quantity per room to support mix occ per room
        case Some(occBd) if fixMarriottSurchargeExp && occBd.occupancyUnits.nonEmpty =>
          dailyPrice.rpmSurcharges.filter(surcharge =>
            surcharge.applyTo.substring(0, 2) == "PR" || matchOccFromProtoFilter(surcharge))
        case _ => dailyPrice.rpmSurcharges.filter(matchOccFromProtoFilter)
      }
    } else {
      dailyPrice.rpmSurcharges
    }
  }

  private[pricing] def getRoomPrices(prices: List[YplPrice], surcharge: SurchargeEntry): List[YplPrice] =
    if (surcharge.isAmount) Nil else prices.filter(_.isRoom)

  private def getRateCategory(r: RateCategoryEntry, currencyCode: String): RateCategory = RateCategory(
    id = r.rateCategoryId,
    parentId = Option(r.parentId),
    code = r.rateCategoryCode,
    isAmount = r.isAmount,
    applyTo = r.applyTo,
    value = r.value,
    benefits = getBenefitList(r.benefitList),
    currencyCode = currencyCode,
    offerType = r.offerType,
    isRoomTypeNotGuarantee = r.isRoomTypeNotGuarantee,
    promotionTypeId = r.promotionTypeId,
    promotionTypeCmsId = r.promotionTypeCmsId,
    inventoryType = Option(r.inventoryType.value),
  )

  private def getBenefitList(benefits: List[BenefitEntry]) = benefits.map(x =>
    Benefit(
      id = x.benefitId,
      value = x.value,
      benefitType = x.benefitType,
      groupId = x.benefitGroupId.getOrElse(0),
      remark = x.remark.getOrElse(""),
      unit = x.unit,
      parameters = x.parameters.map(p =>
        BenefitParameter(
          position = p.position,
          value = p.value,
          unitId = p.unitId,
        )),
      benefitValue =
        x.benefitValue.map(v => BenefitValue(v.benefitValueUSD, v.benefitValuationType, v.shouldDisplayBenefit)),
    ))

  private[pricing] def toPromotion(e: PromotionEntry,
                                   shouldFixPromotionsBreakdown: Boolean,
                                   date: Option[DateTime] = None) = {
    val time = e.bookTimeFrom.map(new DateTime(_))

    Promotion(
      id = e.id,
      typeId = e.typeId,
      discountType = e.discountType,
      cmsTypeId = e.cmsTypeId,
      cmsDiscountTypeId = e.cmsDiscountTypeId,
      minNightStay = e.minNightStay,
      value =
        if (shouldFixPromotionsBreakdown) {
          date.map(e.getDiscount).getOrElse(e.discounts.headOption.getOrElse(0))
        } else {
          e.discounts(0)
        },
      minRooms = e.minRooms,
      minAdvPurchase = e.minAdvPurchase.getOrElse(DEFAULT_PROMOTION_ADV_PURCHASE),
      maxAdvPurchase = e.maxAdvPurchase.getOrElse(DEFAULT_PROMOTION_ADV_PURCHASE),
      isStackable = e.isStackable,
      isAllowChannelDiscount = e.isAllowChannelDiscount,
      isRatePlanAsPromotion = e.isRatePlanAsPromotion,
      applicableUntil = e.bookTo.map(
        _.withHourOfDay(time.map(_.hourOfDay().get()).getOrElse(0))
          .withMinuteOfHour(time.map(_.minuteOfHour().get()).getOrElse(0))),
    )
  }
}
