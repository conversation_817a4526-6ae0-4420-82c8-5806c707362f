package com.agoda.papi.ypl.pricing

import com.agoda.finance.tax.models.ReqOccByHotelAgePolicy
import com.agoda.finance.tax.services.tax.applytaxover.ApplyTaxOverHelper
import com.agoda.finance.tax.services.tax.{GraduatedTaxCalculator, TaxValidator => CommonTaxValidator}
import com.agoda.finance.tax.{enums => TaxEnums}
import com.agoda.papi.enums.hotel.{PaymentModel, TaxType}
import com.agoda.papi.enums.request.FeatureFlag
import com.agoda.papi.enums.room._
import com.agoda.papi.pricing.pricecalculation.api.PriceBreakdownCalculatorInterface
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes, TaxWithValue}
import com.agoda.papi.pricing.pricecalculation.models._
import com.agoda.papi.pricing.pricecalculation.models.response.Calculation
import com.agoda.papi.pricing.pricecalculation.utils.CommonTaxConverter.{
  toCommonRateType,
  toCommonTaxWithValue,
  toPriceCalcTaxWithValue,
}
import com.agoda.papi.ypl.commission.CommissionUtils.{convertToCommission, convertToMarkup}
import com.agoda.papi.ypl.commission.service.CommissionService
import com.agoda.papi.ypl.models.CommonTaxConverter.{toCommonReqOccByHotelAgePolicy, toCommonTaxRoomOcc}
import com.agoda.papi.ypl.models.api.request.{YplAGXCommission, YplOccInfo}
import com.agoda.papi.ypl.models.enums.PayingAgeType.AsFree
import com.agoda.papi.ypl.models.enums.{BreakdownStep, GrowthProgramCommissionBreakdown}
import com.agoda.papi.ypl.models.pricing._
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{
  ChainId,
  CityId,
  CountryId,
  DiscountMessageType,
  HotelId,
  SupplierId,
  YplChannel,
  YplContext,
  YplExperiments,
  YplReqOccByHotelAgePolicy,
  YplRoomEntry,
}
import com.agoda.papi.ypl.pricing.PriceCalculator._
import com.agoda.papi.ypl.settings.SurchargeSettings
import com.agoda.supply.calc.proto
import com.agoda.utils.collection.SumImplicits._
import com.agoda.utils.flow.{ExperimentContext, PropertyContext}
import com.typesafe.scalalogging.LazyLogging
import models.consts.ABTest
import com.agoda.papi.pricing.pricecalculation.models.tax.DailyTaxes.TaxesWithValues
import com.agoda.papi.pricing.pricecalculation.pricing.helper.PriceCalculationHelper.{
  calculateAutoProcessingFeeWithBreakdown,
  convertNetInToNetEx,
  convertSellInToNetEx,
  reCalculateProcessingFeeBasedOnMarginTax,
}
import com.agoda.papi.pricing.pricecalculation.utils.ChargeTypesUtil
import com.agoda.papi.ypl.pricing.breakdown.mappers.PriceCalculationRequestMapper
import org.joda.time.DateTime

trait PriceCalculationImpl extends PriceCalculation with CommissionService with LazyLogging {
  self: TaxCalculator =>

  val priceBreakdownCalculator: PriceBreakdownCalculatorInterface

  private def getApplyTaxOver(
    helper: ApplyTaxOverHelper,
    supplierId: Int,
    hotelId: Long,
    chainId: Int,
    countryId: Long,
    paymentModelId: Int,
    rateTypeId: Int,
    taxProtoTypeId: Int,
    taxApplyBreakdownType: Option[proto.TaxApplyBreakdownType],
    taxApplyOnId: Option[Int],
    isEnableNegativeSegmentExp: Boolean,
  ): Option[ApplyTaxOver] = helper
    .defineApplyTaxOver(
      supplierId,
      hotelId,
      chainId,
      countryId,
      paymentModelId,
      rateTypeId,
      taxProtoTypeId,
      taxApplyBreakdownType,
      taxApplyOnId,
      isEnableNegativeSegmentExp,
    )
    .map(convertToYPLModel)

  // will clean up once MR is merged to minimize the code to review
  private def convertToYPLModel(applyTaxOver: TaxEnums.ApplyTaxOver): ApplyTaxOver =
    ApplyTaxOver.getFromValue(applyTaxOver.value)

  // scalastyle:off
  /**
    * Please update same logic in [[PriceCalculation.calculatePriceForPriceCalculationRefactor()]] as well
    */
  def calculatePrice(paymentModel: PaymentModel,
                     date: DateTime,
                     commissionPercent: Double,
                     agxCommission: YplAGXCommission,
                     commissionExcludingWholesaleOrAgx: => Double,
                     hotelTaxInfo: HotelTaxInfo,
                     dailyTaxes: DailyTaxes,
                     reqOcc: YplReqOccByHotelAgePolicy,
                     chargeType: ChargeType,
                     quantity: Int,
                     applyType: ApplyType,
                     chargeOption: ChargeOption,
                     promoDiscount: Double,
                     value: Double = 0.0,
                     valueWithChannelDiscount: Double,
                     discountMessages: Map[DiscountMessageType, DiscountMessage],
                     roomPriceInfo: RoomPriceInfo,
                     supplierId: SupplierId,
                     subChargeType: SubChargeType = SubChargeType.None,
                     roomNo: Option[Int] = None,
                     supplierContractedCommissionFromCommissionHolder: Option[Double],
                     channelDiscounts: List[YplChannelDiscountBreakdown],
                     resellRefSell: Option[Double] = None,
                     currentBreakdownStep: BreakdownStep = BreakdownStep.Unknown,
                     bookingPriceBreakdown: BookingPriceBreakdown = BookingPriceBreakdown(),
                     apmPriceAdjustmentDetail: Option[ApmPriceAdjustmentDetail] = None,
                     apmCommissionDiscountPercent: Double = 0d,
                     childAgeRangeId: Option[Long] = None,
                     hotelId: HotelId,
                     chainId: ChainId,
                     countryId: CountryId,
                     sellExForMarginAdjustment: Option[Double] = None,
                     supplierFundedDiscountAmount: Option[Double] = None,
                     uspaDiscountAmount: Option[Double] = None,
                     uspaProgramId: Option[Int] = None)(
    isBcomFixTaxAmountApplyToPB: Boolean)(implicit ctx: YplContext, propertyContext: PropertyContext): YplPrice = {

    val updatedDailyTaxes = updateDailyTaxes(
      dailyTaxes,
      ctx.request.applyTaxOverHelper,
      supplierId,
      hotelId,
      chainId,
      countryId,
      paymentModel.i,
      roomPriceInfo.originalRateType.value,
      date.toString(TaxUtil.dateFormat),
    )

    val priceTotal = valueWithChannelDiscount - promoDiscount

    val (netEx: Double,
         tax: Double,
         fee: Double,
         finalMargin: Double,
         finalProcessingFee: ProcessingFees,
         breakdowns: List[CommonTaxBreakdown],
         taxOverSellEx: Double) =
      if (priceTotal < BD_ZERO) {
        (BD_ZERO, BD_ZERO, BD_ZERO, BD_ZERO, (BD_ZERO, None), Nil, BD_ZERO)
      } else {
        val calculation: Calculation = calculate(
          paymentModel = paymentModel,
          charge = chargeType,
          value = priceTotal,
          hotelTaxInfo = hotelTaxInfo,
          commissionPercent = commissionPercent,
          commissionExcludingAgxOrWholesaleFromHolder = commissionExcludingWholesaleOrAgx,
          dailyTaxes = updatedDailyTaxes,
          reqOcc = reqOcc,
          channel = roomPriceInfo.channel,
          rateType = roomPriceInfo.rateType,
          originalRateType = roomPriceInfo.originalRateType,
          roomRateType = roomPriceInfo.rateType,
          roomOcc = roomPriceInfo.roomOcc,
          processingFeePercent = roomPriceInfo.processingFeePercent,
          supplierId = supplierId,
          hasChannelDiscount = channelDiscounts.exists(_.percentage > 0),
          hotelId = hotelId,
          chainId = chainId,
          countryId = countryId,
          sellExForMarginAdjustment = sellExForMarginAdjustment,
          subChargeType = subChargeType,
        )(isBcomFixTaxAmountApplyToPB)

        (calculation.netEx,
         calculation.tax,
         calculation.fee,
         calculation.margin,
         calculation.processingFees,
         calculation.breakdowns,
         calculation.taxOverSellEx)
      }

    val margin = finalMargin
    val processingFee = finalProcessingFee._1

    val refCommissionPercent =
      getReferenceCommission(commissionPercent, roomPriceInfo.rateType, supplierContractedCommissionFromCommissionHolder)

    val supplierHotelCalculationSettings = ctx.request.supplierHotelCalculationSettings.settings.get(supplierId)

    val isEnableNegativeSegmentExp = ctx.experimentContext.isUserB(ABTest.ENABLE_NEGATIVE_SEGMENT)
    val isApplyTaxOnSellExSupplierLevel = ctx.request.applyTaxOnSellExSettings.exists(
      _.isApplyTaxOnSellEx(
        supplierId,
        hotelId,
        chainId,
        countryId,
        paymentModel.i,
        roomPriceInfo.originalRateType.value,
        isEnableNegativeSegmentExp,
      ))

    val isApplyTaxOnSellExHotelLevel = supplierHotelCalculationSettings.exists(_.isApplyTaxOnSellEx)

    val isApplyTaxOnSellEx = isApplyTaxOnSellExSupplierLevel || isApplyTaxOnSellExHotelLevel
    val isApplyNoProcessingFee = isApplyTaxOnSellEx

    val priceBreakdownWithBaseTaxesAndConfigs = bookingPriceBreakdown
      .addTaxV2(
        currentBreakdownStep,
        breakdowns,
        isApplyNoProcessingFee,
        isApplyTaxOnSellEx,
      )
      .addTotalTaxPercent(updatedDailyTaxes)

    YplPrice(
      date = date,
      quantity = quantity,
      chargeType = chargeType,
      applyType = applyType,
      chargeOption = chargeOption,
      refId = 0,
      netExclusive = netEx,
      tax = tax,
      fee = fee,
      margin = margin,
      processingFee = processingFee,
      promotionDiscount = promoDiscount,
      downliftAmount = BD_ZERO,
      downliftPercent = BD_ZERO,
      taxBreakDown = breakdowns,
      dailyTaxes = updatedDailyTaxes, // rateLoadedTypeAmount - ChannelDiscount
      refMargin = margin,
      refProcessingFee = processingFee,
      isConfigProcessingFee = hotelTaxInfo.isConfigProcessingFees,
      value = valueWithChannelDiscount,
      upliftedSellIn = None,
      processingFeeBreakdown = finalProcessingFee._2,
      discountMessages = discountMessages,
      downliftExAmount = None,
      subChargeType = subChargeType,
      roomNumber = roomNo,
      referenceCommissionPercent = refCommissionPercent,
      agxCommission = agxCommission,
      channelDiscounts = channelDiscounts,
      resellRefSell = resellRefSell,
      currentBreakdownStep = currentBreakdownStep,
      priceBreakdownHistory = priceBreakdownWithBaseTaxesAndConfigs.copy(isAPMDiscountStep = false),
      apmPriceAdjustmentDetail = apmPriceAdjustmentDetail,
      apmCommissionDiscountPercent = apmCommissionDiscountPercent,
      taxOverSellEx = taxOverSellEx,
      childAgeRangeId = childAgeRangeId,
      isApplyRefCommissionOnRefSellEx = isApplyTaxOnSellEx,
      supplierFundedDiscountAmount = supplierFundedDiscountAmount,
      uspaDiscountAmount = uspaDiscountAmount,
      uspaProgramId = uspaProgramId,
    )
  }

  // TODO: VEL-2070 rename

  /**
    * *
    * @param paymentModel
    * @param date
    * @param commissionPercent
    * @param agxCommission only pass to YPLPrice
    * @param commissionExcludingWholesaleOrAgx
    * @param hotelTaxInfo
    * @param dailyTaxes
    * @param reqOcc
    * @param chargeType
    * @param quantity
    * @param applyType only pass to YPLPrice
    * @param chargeOption only pass to YPLPrice
    * @param promoDiscount
    * @param valueWithChannelDiscount
    * @param discountMessages only pass to YPLPrice
    * @param roomPriceInfo
    * @param supplierId
    * @param subChargeType
    * @param roomNo only pass to YPLPrice
    * @param supplierContractedCommissionFromCommissionHolder
    * @param channelDiscountBreakdowns only pass to YPLPrice
    * @param resellRefSell only pass to YPLPrice
    * @param currentBreakdownStep only pass to YPLPrice
    * @param bookingPriceBreakdown only pass to YPLPrice
    * @param apmPriceAdjustmentDetail only pass to YPLPrice
    * @param apmCommissionDiscountPercent only pass to YPLPrice
    * @param childAgeRangeId only pass to YPLPrice
    * @param hotelId
    * @param chainId
    * @param countryId
    * @param sellExForMarginAdjustment
    * @param supplierFundedDiscountAmount only pass to YPLPrice
    * @param uspaDiscountAmount only pass to YPLPrice
    * @param uspaProgramId only pass to YPLPrice
    * @param isApplyTaxOnSellEx
    * @param lengthOfStay
    * @param storefrontId
    * @param whitelabelId
    * @param isThirdPartySupplier
    * @param applyTaxOverHelper
    * @param experimentContext
    */
  def calculatePriceForPriceCalculationRefactor(paymentModel: PaymentModel,
                                                date: DateTime,
                                                commissionPercent: Double,
                                                agxCommission: YplAGXCommission,
                                                commissionExcludingWholesaleOrAgx: => Double,
                                                hotelTaxInfo: HotelTaxInfo,
                                                dailyTaxes: DailyTaxes,
                                                reqOcc: ReqOccByHotelAgePolicy,
                                                chargeType: ChargeType,
                                                quantity: Int,
                                                applyType: ApplyType,
                                                chargeOption: ChargeOption,
                                                promoDiscount: Double,
                                                valueWithChannelDiscount: Double,
                                                discountMessages: Map[DiscountMessageType, DiscountMessage],
                                                roomPriceInfo: RoomPriceInfo,
                                                supplierId: SupplierId,
                                                subChargeType: SubChargeType = SubChargeType.None,
                                                roomNo: Option[Int] = None,
                                                supplierContractedCommissionFromCommissionHolder: Option[Double],
                                                channelDiscountBreakdowns: List[YplChannelDiscountBreakdown],
                                                resellRefSell: Option[Double] = None,
                                                currentBreakdownStep: BreakdownStep = BreakdownStep.Unknown,
                                                bookingPriceBreakdown: BookingPriceBreakdown = BookingPriceBreakdown(),
                                                apmPriceAdjustmentDetail: Option[ApmPriceAdjustmentDetail] = None,
                                                apmCommissionDiscountPercent: Double = 0d,
                                                childAgeRangeId: Option[Long] = None,
                                                hotelId: HotelId,
                                                chainId: ChainId,
                                                countryId: CountryId,
                                                cityId: CityId,
                                                sellExForMarginAdjustment: Option[Double] = None,
                                                supplierFundedDiscountAmount: Option[Double] = None,
                                                uspaDiscountAmount: Option[Double] = None,
                                                uspaProgramId: Option[Int] = None,
                                                isApplyTaxOnSellEx: Boolean,
                                                lengthOfStay: Int,
                                                storefrontId: Int,
                                                whitelabelId: Int,
                                                isThirdPartySupplier: Boolean,
                                                applyTaxOverHelper: ApplyTaxOverHelper,
                                                experimentContext: ExperimentContext)(implicit
    propertyContext: PropertyContext): YplPrice = {
    val isCleanedUpHospitalityTax = experimentContext.isPropertyB(propertyContext, YplExperiments.CLEAN_HP_TAX)
    val priceTotal = valueWithChannelDiscount - promoDiscount

    val (netEx: Double,
         tax: Double,
         fee: Double,
         finalMargin: Double,
         finalProcessingFee: ProcessingFees,
         breakdowns: List[CommonTaxBreakdown],
         taxOverSellEx: Double,
         refCommissionPercent: Double,
         updatedDailyTaxes: DailyTaxes) =
      if (priceTotal < BD_ZERO) {
        (BD_ZERO,
         BD_ZERO,
         BD_ZERO,
         BD_ZERO,
         (BD_ZERO, None),
         Nil,
         BD_ZERO,
         BD_ZERO,
         DailyTaxes(List.empty, isCleanedUpHospitalityTax = isCleanedUpHospitalityTax))
      } else {
        val priceCalculationRequest = PriceCalculationRequestMapper.toPriceCalculationRequest(
          hotelId = hotelId,
          chainId = chainId,
          countryId = countryId,
          cityId = cityId,
          supplierId = supplierId,
          paymentModel = paymentModel,
          isThirdPartySupplier = isThirdPartySupplier,
          isApplyTaxOnSellEx = isApplyTaxOnSellEx,
          applyTaxOverHelper = applyTaxOverHelper,
          taxType = hotelTaxInfo.taxType,
          isConfigProcessingFees = hotelTaxInfo.isConfigProcessingFees,
          processingFeePercent = roomPriceInfo.processingFeePercent,
          dailyTaxes = dailyTaxes,
          charge = chargeType,
          subChargeType = subChargeType,
          value = priceTotal,
          rateType = roomPriceInfo.rateType,
          originalRateType = roomPriceInfo.originalRateType,
          roomRateType = roomPriceInfo.rateType,
          hasChannelDiscount = channelDiscountBreakdowns.exists(_.percentage > 0),
          sellExForMarginAdjustment = sellExForMarginAdjustment,
          applicableDate = date,
          reqOcc = reqOcc,
          roomOcc = toCommonTaxRoomOcc(roomPriceInfo.roomOcc),
          commissionPercent = commissionPercent,
          contractCommissionPercent = commissionExcludingWholesaleOrAgx,
          markup = None,
          supplierContractedCommission = supplierContractedCommissionFromCommissionHolder,
          los = lengthOfStay,
          storefrontId = storefrontId,
          whitelabelId = whitelabelId,
          experimentContext = experimentContext,
        )
        val calculation: Calculation = priceBreakdownCalculator.calculate(priceCalculationRequest)

        (calculation.netEx,
         calculation.tax,
         calculation.fee,
         calculation.margin,
         calculation.processingFees,
         calculation.breakdowns,
         calculation.taxOverSellEx,
         calculation.referenceCommissionPercent,
         calculation.dailyTaxes)
      }

    val priceBreakdownWithBaseTaxesAndConfigs = bookingPriceBreakdown
      .addTaxV2(
        currentBreakdownStep,
        breakdowns,
        isApplyNoProcessingFee = isApplyTaxOnSellEx,
        isApplyTaxOnSellEx = isApplyTaxOnSellEx,
      )
      .addTotalTaxPercent(updatedDailyTaxes)

    YplPrice(
      date = date,
      quantity = quantity,
      chargeType = chargeType,
      applyType = applyType,
      chargeOption = chargeOption,
      refId = 0,
      netExclusive = netEx,
      tax = tax,
      fee = fee,
      margin = finalMargin,
      processingFee = finalProcessingFee._1,
      promotionDiscount = promoDiscount,
      downliftAmount = BD_ZERO,
      downliftPercent = BD_ZERO,
      taxBreakDown = breakdowns,
      dailyTaxes = updatedDailyTaxes,
      refMargin = finalMargin,
      refProcessingFee = finalProcessingFee._1,
      isConfigProcessingFee = hotelTaxInfo.isConfigProcessingFees,
      value = valueWithChannelDiscount,
      upliftedSellIn = None,
      processingFeeBreakdown = finalProcessingFee._2,
      discountMessages = discountMessages,
      downliftExAmount = None,
      subChargeType = subChargeType,
      roomNumber = roomNo,
      referenceCommissionPercent = refCommissionPercent,
      agxCommission = agxCommission,
      channelDiscounts = channelDiscountBreakdowns,
      resellRefSell = resellRefSell,
      currentBreakdownStep = currentBreakdownStep,
      priceBreakdownHistory = priceBreakdownWithBaseTaxesAndConfigs.copy(isAPMDiscountStep = false),
      apmPriceAdjustmentDetail = apmPriceAdjustmentDetail,
      apmCommissionDiscountPercent = apmCommissionDiscountPercent,
      taxOverSellEx = taxOverSellEx,
      childAgeRangeId = childAgeRangeId,
      isApplyRefCommissionOnRefSellEx = isApplyTaxOnSellEx,
      supplierFundedDiscountAmount = supplierFundedDiscountAmount,
      uspaDiscountAmount = uspaDiscountAmount,
      uspaProgramId = uspaProgramId,
    )
  }

  // scalastyle:off

  /**
    * the function is used for ROOM, ExtraBed and Surcharge
    * the parameters passing in are slightly diff
    * check [[PriceCalculation.calculatePrice()]] for ROOM, ExtraBed
    * check [[PriceCalculation.calculateSurcharge()]] for Surcharge
    * check [[PriceCalculation.calculateAgpPrice()]]
    *
    * Please update the same logic in [[PriceBreakdownCalculatorInterface.calculate()]] as well
    */
  // TODO: VEL-2070 remove
  def calculate(
    hotelId: HotelId, // hotel
    chainId: ChainId, // hotel (metaCache)
    countryId: CountryId, // hotel (metaCache)
    supplierId: SupplierId, // offer

    paymentModel: PaymentModel, // offer
    channel: YplChannel, // offer

    // if HotelTaxInfo.isConfigProcessingFees == true then using roomPriceInfo.processingFeePercent
    hotelTaxInfo: HotelTaxInfo, // offer
    processingFeePercent: Double, // offer

    // for AGX logic
    hasChannelDiscount: Boolean, // offer

    // for AGP logic
    sellExForMarginAdjustment: Option[Double], // offer

    reqOcc: YplReqOccByHotelAgePolicy, // offer
    roomOcc: RoomOccupancy, // offer

    rateType: RateType, // offer
    originalRateType: RateType, // offer
    roomRateType: RateType, // offer

    charge: ChargeType, // price
    value: Double, // price
    commissionPercent: Double, // price
    commissionExcludingAgxOrWholesaleFromHolder: => Double, // price
    markup: Option[Double] = None,
    dailyTaxes: DailyTaxes, // price
    subChargeType: SubChargeType, // price
  )(isBcomFixTaxAmountApplyToPB: Boolean)(implicit ctx: YplContext, propertyContext: PropertyContext): Calculation = {
    // scalastyle:on

    val finalMarkup = (rateType, markup) match {
      case (_, Some(x)) => x
      case _ => commissionPercent
    }

    implicit val r = ctx.request
    rateType match {
      case RateType.NetInclusive => calculateNetIn(
          paymentModel,
          charge,
          value,
          finalMarkup,
          commissionPercent,
          commissionExcludingAgxOrWholesaleFromHolder,
          hotelTaxInfo,
          rateType,
          originalRateType,
          roomRateType,
          roomOcc,
          processingFeePercent,
          dailyTaxes,
          reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = hasChannelDiscount,
          hotelId,
          chainId,
          countryId,
          sellExForMarginAdjustment,
          subChargeType,
        )(isBcomFixTaxAmountApplyToPB)
      case RateType.SellExclusive => calculateSellEx(
          paymentModel,
          charge,
          value,
          finalMarkup,
          commissionPercent,
          commissionExcludingAgxOrWholesaleFromHolder,
          hotelTaxInfo,
          rateType,
          originalRateType,
          roomRateType,
          roomOcc,
          processingFeePercent,
          dailyTaxes,
          reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = hasChannelDiscount,
          hotelId,
          chainId,
          countryId,
          subChargeType,
        )
      case RateType.SellInclusive => calculateSellIn(
          paymentModel,
          charge,
          value,
          finalMarkup,
          commissionPercent,
          commissionExcludingAgxOrWholesaleFromHolder,
          hotelTaxInfo,
          rateType,
          originalRateType,
          roomRateType,
          roomOcc,
          processingFeePercent,
          dailyTaxes,
          reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = hasChannelDiscount,
          hotelId,
          chainId,
          countryId,
          sellExForMarginAdjustment,
          subChargeType,
        )(isBcomFixTaxAmountApplyToPB)
      case _ => calculateNetEx(
          paymentModel,
          charge,
          value,
          finalMarkup,
          commissionPercent,
          commissionExcludingAgxOrWholesaleFromHolder,
          hotelTaxInfo,
          rateType,
          originalRateType,
          roomRateType,
          roomOcc,
          processingFeePercent,
          None,
          dailyTaxes,
          reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = hasChannelDiscount,
          hotelId,
          chainId,
          countryId,
          subChargeType = subChargeType,
        )
    }
  }

  /**
    * @param paymentModel - passed to calculateTaxes method
    * @param charge - to check if hospitality charge is applied
    * @param netEx - the price
    * @param markupPercent - markup percent which includes contracted commission and payAsYouGoCommission
    * @param commissionPercent - This value is not used in this method
    * @param payAsYouGoCommission
    * @param hotelTaxInfo
    * @param rateType
    * @param originalRateType
    * @param roomRateType
    * @param occ
    * @param processingFeePercent
    * @param marginInOpt
    * @param dailyTaxes
    * @param reqOcc
    * @param supplierId
    * @param hasChannelDiscount
    * @param hotelId
    * @param ctx
    * @return Calculation
    */
  // scalastyle:off
  // TODO: VEL-2070 remove
  private[pricing] def calculateNetEx(
    paymentModel: PaymentModel,
    charge: ChargeType,
    netEx: Double,
    markupPercent: Double,
    commissionPercent: Double,
    commissionExcludingAgxOrWholesaleFromHolder: => Double,
    hotelTaxInfo: HotelTaxInfo,
    rateType: RateType,
    originalRateType: RateType,
    roomRateType: RateType,
    occ: RoomOccupancy,
    processingFeePercent: Double,
    marginInOpt: Option[Double],
    dailyTaxes: DailyTaxes,
    reqOcc: YplReqOccByHotelAgePolicy,
    supplierId: SupplierId,
    hasChannelDiscount: Boolean,
    hotelId: HotelId,
    chainId: ChainId,
    countryId: CountryId,
    sellTaxForMarginAdjustment: Option[Double] = None,
    subChargeType: SubChargeType)(implicit ctx: YplContext, propertyContext: PropertyContext): Calculation = {
    // scalastyle:on
    // markupPercent already includes both Contracted Commission and AGX PayAsYouGo Commission

    // TODO: Verify composedCommission should be same as commissionPercent

    val isCleanedUpHospitalityTax = ctx.experimentContext.isPropertyB(propertyContext, YplExperiments.CLEAN_HP_TAX)

    // marginOpt is NonNull when called from CalculateNetIn (indirectly also CalculateSellIn)
    // where it has an incorrect calculation wrt AGP
    // This line is covered in
    // Utilize new margin adjustment from PAPI for YCS
    // Scenario: Testcase 4, Net In + CC + AC→ Correct breakdowns, EXP B
    // cross-check AGPPAY-1, not sure why [[hasChannelDiscount]] is required
    val (newValue, newMargin, newMarginOpt) =
      if ((rateType == RateType.NetExclusive || rateType == RateType.NetInclusive) && !hasChannelDiscount && charge != ChargeType.ExtraBed) {
        val composedCommission = convertToCommission(markupPercent)

        // in the case of agx or wholesale campaign, we want to lock the sell rate instead of the net rate
        // so we use the original commission, here is contractedCommission to get the sell rate
        // then use the updated commission (composedCommission) to calculate the margin, and then get the adjusted net rate (actualNetEx)
        val contractedMarkup = commissionExcludingAgxOrWholesaleFromHolder

        val sellEx = netEx * (1 + contractedMarkup / 100.0)
        val actualComposedMargin = (composedCommission / 100.0) * sellEx
        val actualNetEx = sellEx - actualComposedMargin

        (actualNetEx, actualComposedMargin, None)
      } else {
        val margin = netEx * markupPercent * TO_PERCENT

        (netEx, margin, marginInOpt)
      }

    val supplierHotelCalculationSetting = ctx.request.supplierHotelCalculationSettings.settings.get(supplierId)

    val isEnableNegativeSegmentExp = ctx.experimentContext.isUserB(ABTest.ENABLE_NEGATIVE_SEGMENT)
    val isApplyTaxOnSellExSupplierLevel = ctx.request.applyTaxOnSellExSettings.exists(
      _.isApplyTaxOnSellEx(
        supplierId,
        hotelId,
        chainId,
        countryId,
        paymentModel.i,
        originalRateType.value,
        isEnableNegativeSegmentExp,
      ))

    val isApplyTaxOnSellExHotelLevel = supplierHotelCalculationSetting.exists(_.isApplyTaxOnSellEx)

    val isApplyTaxOnSellEx = isApplyTaxOnSellExSupplierLevel || isApplyTaxOnSellExHotelLevel
    val isApplyNoProcessingFee = isApplyTaxOnSellEx

    val taxPackage = calculateTaxes(
      charge = charge,
      value = newValue,
      margin = newMargin,
      occ = occ,
      dailyTaxes = dailyTaxes,
      reqOcc = reqOcc,
      paymentModel = paymentModel,
      supplierId = supplierId,
      hotelTaxInfo = Some(hotelTaxInfo),
      subChargeType = subChargeType,
    )

    val taxDefault = taxPackage.taxDefault // Mandatory tax
    val taxOverSellEx = taxPackage.taxOverSellEx
    val fee = taxPackage.fee
    val taxHospitalityPrice = taxPackage.hospitalityPriceTax
    val taxSurcharge = taxPackage.surchargeTax
    val originalMarginTax = taxPackage.taxOverMargin

    val taxFeeDefaultBreakdowns = taxPackage.taxFeeDefaultBreakdowns
    val taxFeeOverSellExBreakdowns = taxPackage.taxFeeOverSellExBreakdowns
    val taxFeeHospitalityPriceBreakdowns = taxPackage.hospitalityPriceTaxBreakdown
    val taxSurchargeBreakdowns = taxPackage.surchargeTaxBreakdown
    val variableTaxBreakdowns = taxPackage.variableTaxBreakdowns

    // For tax setting = mandatory tax, taxes/fees should apply to both room and surcharge
    // For tax setting = hospitality tax, taxes/fees should apply to only room charge type
    // For tax setting = surcharge tax, taxes/fees should apply to only  surcharge charge type
    val (taxOverNetEx, taxBreakdowns) = charge match {
      case ChargeType.Surcharge => (taxDefault + taxSurcharge, taxFeeDefaultBreakdowns ++ taxSurchargeBreakdowns)
      case _ if ChargeTypesUtil.isHospChargeType(charge) =>
        (taxDefault + taxHospitalityPrice,
         taxFeeDefaultBreakdowns ++ taxFeeHospitalityPriceBreakdowns,
        ) // for hospitality tax
      case _ => (taxDefault, taxFeeDefaultBreakdowns)
    }

    val (finalMargin, processingFees) = calculateProcessingFee(
      charge = charge,
      netExValue = newValue,
      tax = taxOverNetEx,
      fee = fee,
      markup = markupPercent,
      margin = newMargin,
      hotelTaxInfo = hotelTaxInfo,
      roomRateType = roomRateType,
      processingFeePercent,
      marginInOpt = None,
      dt = dailyTaxes.dailyTaxesDefault,
      breakdowns = taxBreakdowns,
      isApplyNoProcessingFee = isApplyNoProcessingFee,
      isMarginAdjusted = sellTaxForMarginAdjustment.isDefined,
    )

    // recalculating tax breakdown if margin after calculating processing fee is not the same as original margin
    val finalMarginTax =
      if (finalMargin != newMargin) {
        val recalculatedTaxPackage = calculateTaxes(
          charge,
          BD_ZERO,
          finalMargin,
          occ,
          dailyTaxes.TaxAppliedMarginDailyTaxes,
          reqOcc,
          paymentModel,
          supplierId,
          Some(hotelTaxInfo),
          subChargeType,
        )
        recalculatedTaxPackage.taxOverMargin
      } else originalMarginTax

    val breakdowns =
      taxFeeDefaultBreakdowns ++ taxFeeOverSellExBreakdowns ++ taxFeeHospitalityPriceBreakdowns ++ taxSurchargeBreakdowns ++ variableTaxBreakdowns
    Calculation(
      netEx = newValue,
      margin = finalMargin,
      tax = taxOverNetEx + taxOverSellEx,
      fee = fee,
      processingFees = processingFees,
      breakdowns = breakdowns,
      taxOverSellEx = 0.0,
      hospitalityPriceTax = taxHospitalityPrice,
      surchargeTax = taxSurcharge,
      taxOverMargin = finalMarginTax,
      dailyTaxes = DailyTaxes(List.empty[TaxWithValue], isCleanedUpHospitalityTax = isCleanedUpHospitalityTax),
    )
  }

  // TODO: VEL-2070 remove
  private def calculateProcessingFee(charge: ChargeType,
                                     netExValue: Double,
                                     tax: Double,
                                     fee: Double,
                                     markup: Double,
                                     margin: Double,
                                     hotelTaxInfo: HotelTaxInfo,
                                     roomRateType: RateType,
                                     processingFeePercent: Double,
                                     marginInOpt: Option[Double],
                                     dt: DailyTaxes,
                                     breakdowns: List[CommonTaxBreakdown],
                                     isApplyNoProcessingFee: Boolean,
                                     isMarginAdjusted: Boolean = false)(implicit
    ctx: YplContext): (Margin, ProcessingFees) = {

    def processingFeeBreakdownReverse: (Margin, ProcessingFees) = calculatePFBreakdownFromInclusive(charge,
                                                                                                    netExValue,
                                                                                                    tax,
                                                                                                    fee,
                                                                                                    markup,
                                                                                                    margin,
                                                                                                    marginInOpt,
                                                                                                    breakdowns,
                                                                                                    dt,
                                                                                                    isMarginAdjusted)

    def processingFeeBreakdown: (Margin, ProcessingFees) =
      calculateProcessingFeeWithBreakdown(charge, hotelTaxInfo, dt, netExValue, margin, processingFeePercent)

    val isSimpleTax = hotelTaxInfo.taxType == TaxType.SimpleTax
    val isSellInRoom = roomRateType == RateType.SellInclusive

    if (isApplyNoProcessingFee) {
      (margin, (BD_ZERO, None))
    } else if (!hotelTaxInfo.isConfigProcessingFees && isSimpleTax || isSellInRoom) {
      processingFeeBreakdownReverse // Exclusive <- Inclusive
    } else {
      processingFeeBreakdown // Exclusive -> Inclusive
    }
  }

  /**
    * This method is called for each room, and calculates its processing fee information
    */
  private def calculateProcessingFeeWithBreakdown(charge: ChargeType,
                                                  hotelTaxInfo: HotelTaxInfo,
                                                  dailyTaxes: DailyTaxes,
                                                  netEx: Double,
                                                  margin: Double,
                                                  processingFeePercent: Double)(implicit
    ctx: YplContext): (Margin, ProcessingFees) = {
    val isManualProcessingFee = hotelTaxInfo.isConfigProcessingFees

    if (isManualProcessingFee) {
      // Manual Processing Fees
      val taxPortion = netEx * processingFeePercent * TO_PERCENT
      val feePortion = BD_ZERO
      (margin, (taxPortion, Some(CommonProcessingFeeBreakdown(taxPortion = taxPortion, feePortion = feePortion))))
    } else {
      // Auto Processing Fees
      calculateAutoProcessingFeeWithBreakdown(charge, dailyTaxes, margin)
    }
  }

  // Sum Tax Or Fee from breakdowns
  private def sumTaxOrFee(taxBreakDown: CommonTaxBreakdown) = taxBreakDown.amount * taxBreakDown.quantity

  private[pricing] def calculateTotalTaxAndFeeAmountValue(charge: ChargeType,
                                                          breakdowns: List[CommonTaxBreakdown],
                                                          isCleanedUpHospitalityTax: Boolean): Double = charge match {
    case ChargeType.Surcharge => breakdowns.getDSum(
        sumTaxOrFee,
        p =>
          (!isCleanedUpHospitalityTax && p.isAmount && p.option == ChargeOption.Surcharge && !p.isFee) || (p.isAmount && p.option == ChargeOption.Mandatory))
    case _ if ChargeTypesUtil.isHospChargeType(charge) =>
      breakdowns.getDSum(
        sumTaxOrFee,
        p =>
          (!isCleanedUpHospitalityTax && p.isAmount && p.option == ChargeOption.HospitalityPrice && !p.isFee) || (p.isAmount && p.option == ChargeOption.Mandatory),
      )
    case _ => breakdowns.getDSum(sumTaxOrFee, p => p.isAmount && p.option == ChargeOption.Mandatory)
  }

  // Given the margin inclusive of taxes, breakdown the taxes from the difference MarginIn- MarginEx
  private[pricing] def calculatePFBreakdownFromInclusive(
    charge: ChargeType,
    netExValue: Double,
    tax: Double,
    fee: Double,
    markup: Double,
    margin: Double,
    marginInOpt: Option[Double],
    breakdowns: List[CommonTaxBreakdown],
    dt: DailyTaxes,
    isMarginAdjusted: Boolean = false): (Margin, ProcessingFees) = {

    // For surcharge charge type, total tax consists of mandatory tax and surcharge tax
    // For room charge type, total tax consists of mandatory tax and hospitality tax
    val totalTaxAndFeeAmountValue: Double =
      calculateTotalTaxAndFeeAmountValue(charge, breakdowns, dt.isCleanedUpHospitalityTax)

    def calculateMarginIn(tax: Double, fee: Double, totAmount: Double) =
      (netExValue + tax + fee - totAmount) * markup * TO_PERCENT

    val marginIn = marginInOpt.getOrElse(calculateMarginIn(tax, fee, totalTaxAndFeeAmountValue))
    // If the margin inclusive is > than margin, it means we have some processing fees that we can breakdown
    if (isMarginAdjusted || marginIn > margin) {
      if (dt.allHospitalityPriceTaxes.nonEmpty || dt.allServiceChargeTaxes.nonEmpty) {
        reCalculateProcessingFeeBasedOnMarginTax(dt, marginIn)
      } else {
        val filteredBreakdown = breakdowns.filter(b => b.isAmount && b.option == ChargeOption.Mandatory)
        val (totalFeeAmount, totalTaxAmount) = filteredBreakdown.partition(_.isFee)
        val (feeBreakdown, taxBreakdown) =
          breakdowns.filter(b => !b.isAmount && b.option == ChargeOption.Mandatory).partition(_.isFee)

        // Breakdown
        val taxPortion = calculateMarginIn(tax, BD_ZERO, totalTaxAmount.getDSum(sumTaxOrFee)) - margin
        val feePortion = calculateMarginIn(BD_ZERO, fee, totalFeeAmount.getDSum(sumTaxOrFee)) - margin

        val taxPortionBreakdown = taxBreakdown.map { txb =>
          val amount =
            calculateMarginIn(txb.amount, BD_ZERO, totalTaxAmount.getDSum(sumTaxOrFee) / taxBreakdown.size) - margin
          TaxAndFeePortionBreakdown(amount, txb.typeId, txb.taxProtoTypeId, txb.whomToPay, txb.applyTo)
        }

        val feePortionBreakdown = feeBreakdown.map { feb =>
          val amount =
            calculateMarginIn(BD_ZERO, feb.amount, totalFeeAmount.getDSum(sumTaxOrFee) / feeBreakdown.size) - margin
          TaxAndFeePortionBreakdown(amount, feb.typeId, feb.taxProtoTypeId, feb.whomToPay, feb.applyTo)
        }

        // Note that at this point taxPortion + feePortion must_== marginIn-margin
        (margin,
         (marginIn - margin,
          Some(CommonProcessingFeeBreakdown(taxPortion, feePortion, Some(taxPortionBreakdown ++ feePortionBreakdown)))))
      }
    } else (margin, (BD_ZERO, None))
  }
  // scalastyle:off
  // TODO: VEL-2070 remove
  private[pricing] def calculateNetIn(paymentModel: PaymentModel,
                                      charge: ChargeType,
                                      netIn: Double,
                                      markupPercent: Double,
                                      commissionPercent: Double,
                                      commissionExcludingAgxOrWholesaleFromHolder: => Double,
                                      hotelTaxInfo: HotelTaxInfo,
                                      rateType: RateType,
                                      originalRateType: RateType,
                                      roomRateType: RateType,
                                      occ: RoomOccupancy,
                                      processingFeePercent: Double,
                                      dailyTaxes: DailyTaxes,
                                      reqOcc: YplReqOccByHotelAgePolicy,
                                      supplierId: SupplierId,
                                      hasChannelDiscount: Boolean,
                                      hotelId: HotelId,
                                      chainId: ChainId,
                                      countryId: CountryId,
                                      sellExForMarginAdjustment: Option[Double] = None,
                                      subChargeType: SubChargeType)(
    isBcomFixTaxAmountApplyToPB: Boolean)(implicit ctx: YplContext, propertyContext: PropertyContext): Calculation = {
    // scalastyle:on
    val marginIn = netIn * markupPercent * TO_PERCENT

    val (totalTaxAndFeeAmount, totalTaxAndFeePctOnNet, totalTaxAndFeePctOnSell) = TaxCalculator.getTotalTaxAndFee(
      charge = charge,
      dailyTaxes = dailyTaxes,
      occ = occ,
      reqOcc = reqOcc,
      los = ctx.request.lengthOfStay,
      supplierId = supplierId,
      isBcomFixTaxAmountApplyToPB = isBcomFixTaxAmountApplyToPB,
      subChargeType = subChargeType,
      sellExForMarginAdjustment = sellExForMarginAdjustment,
      rateType = RateType.NetInclusive,
      commissionPercentage = commissionPercent,
    )

    val netEx = convertNetInToNetEx(
      netIn = netIn,
      taxAndFeeAmount = totalTaxAndFeeAmount,
      taxAndFeePercentOnNet = totalTaxAndFeePctOnNet,
      taxAndFeePercentOnSell = totalTaxAndFeePctOnSell,
      markupPercent = commissionPercent,
    )

    val sellTaxForMarginAdjustment = sellExForMarginAdjustment.map(sellEx => netIn + marginIn - sellEx)
    val marginAdjustedMarkupPercent = calculateAdjustedMarginMarkupPercent(sellExForMarginAdjustment, netEx)

    calculateNetEx(
      paymentModel = paymentModel,
      charge = charge,
      netEx = math.max(BD_ZERO, netEx),
      markupPercent = marginAdjustedMarkupPercent.getOrElse(markupPercent),
      commissionPercent = commissionPercent,
      commissionExcludingAgxOrWholesaleFromHolder = commissionExcludingAgxOrWholesaleFromHolder,
      hotelTaxInfo = hotelTaxInfo,
      rateType = rateType,
      originalRateType = originalRateType,
      roomRateType = roomRateType,
      occ = occ,
      processingFeePercent = processingFeePercent,
      marginInOpt = Some(marginIn),
      dailyTaxes = dailyTaxes,
      reqOcc = reqOcc,
      supplierId = supplierId,
      hasChannelDiscount = hasChannelDiscount,
      hotelId = hotelId,
      chainId = chainId,
      countryId = countryId,
      sellTaxForMarginAdjustment = sellTaxForMarginAdjustment,
      subChargeType = subChargeType,
    )
  }

  // TODO: VEL-2070 remove
  private[pricing] def calculateSellEx(
    paymentModel: PaymentModel,
    charge: ChargeType,
    value: Double,
    markupPercent: Double,
    commissionPercent: Double,
    commissionExcludingAgxOrWholesaleFromHolder: => Double,
    hotelTaxInfo: HotelTaxInfo,
    rateType: RateType,
    originalRateType: RateType,
    roomRateType: RateType,
    occ: RoomOccupancy,
    processingFeePercent: Double,
    dailyTaxes: DailyTaxes,
    reqOcc: YplReqOccByHotelAgePolicy,
    supplierId: SupplierId,
    hasChannelDiscount: Boolean,
    hotelId: HotelId,
    chainId: ChainId,
    countryId: CountryId,
    subChargeType: SubChargeType)(implicit ctx: YplContext, propertyContext: PropertyContext): Calculation = {
    val newMargin = (value * markupPercent) * TO_PERCENT
    val calculation = calculateNetEx(
      paymentModel = paymentModel,
      charge = charge,
      netEx = value - newMargin,
      markupPercent = convertToMarkup(markupPercent),
      commissionPercent = commissionPercent,
      commissionExcludingAgxOrWholesaleFromHolder = commissionExcludingAgxOrWholesaleFromHolder,
      hotelTaxInfo = hotelTaxInfo,
      rateType = rateType,
      originalRateType = originalRateType,
      roomRateType = roomRateType,
      occ = occ,
      processingFeePercent = processingFeePercent,
      None,
      dailyTaxes = dailyTaxes,
      reqOcc = reqOcc,
      supplierId = supplierId,
      hasChannelDiscount = hasChannelDiscount,
      hotelId = hotelId,
      chainId = chainId,
      countryId = countryId,
      subChargeType = subChargeType,
    )

    if (dailyTaxes.hasHospitalityPrice) {
      // Must use margin from the result of calculationNetEx() since margin might be adjusted due to tax on margin re-ratio on PF
      calculation
    } else {
      calculation.copy(margin = newMargin)
    }
  }
  // scalastyle:off
  // TODO: VEL-2070 remove
  private[pricing] def calculateSellIn(paymentModel: PaymentModel,
                                       charge: ChargeType,
                                       value: Double,
                                       markupPercent: Double,
                                       commissionPercent: Double,
                                       commissionExcludingAgxOrWholesaleFromHolder: => Double,
                                       hotelTaxInfo: HotelTaxInfo,
                                       rateType: RateType,
                                       originalRateType: RateType,
                                       roomRateType: RateType,
                                       occ: RoomOccupancy,
                                       processingFeePercent: Double,
                                       dailyTaxes: DailyTaxes,
                                       reqOcc: YplReqOccByHotelAgePolicy,
                                       supplierId: SupplierId,
                                       hasChannelDiscount: Boolean,
                                       hotelId: HotelId,
                                       chainId: ChainId,
                                       countryId: CountryId,
                                       sellExForMarginAdjustment: Option[Double] = None,
                                       subChargeType: SubChargeType)(
    isBcomFixTaxAmountApplyToPB: Boolean)(implicit ctx: YplContext, propertyContext: PropertyContext): Calculation = {
    val (totalTaxAndFeeAmount, totalTaxAndFeePctOnNet, totalTaxAndFeePctOnSell) = TaxCalculator.getTotalTaxAndFee(
      charge = charge,
      dailyTaxes = dailyTaxes,
      occ = occ,
      reqOcc = reqOcc,
      los = ctx.request.lengthOfStay,
      supplierId = supplierId,
      isBcomFixTaxAmountApplyToPB = isBcomFixTaxAmountApplyToPB,
      subChargeType = subChargeType,
      sellExForMarginAdjustment = sellExForMarginAdjustment,
      rateType = RateType.SellInclusive,
      commissionPercentage = commissionPercent,
    )

    val netEx = convertSellInToNetEx(
      sellIn = value,
      taxAndFeeAmount = totalTaxAndFeeAmount,
      taxAndFeePercentOnNet = totalTaxAndFeePctOnNet,
      taxAndFeePercentOnSell = totalTaxAndFeePctOnSell,
      markupPercent = markupPercent,
    )

    val priceWithoutTaxesAndFeesAmount = math.max(BD_ZERO, value - totalTaxAndFeeAmount)
    val marginIn = (priceWithoutTaxesAndFeesAmount * markupPercent) * TO_PERCENT

    val sellTaxForMarginAdjustment = sellExForMarginAdjustment.map(sellEx => priceWithoutTaxesAndFeesAmount - sellEx)
    val marginAdjustedMarkupPercent = calculateAdjustedMarginMarkupPercent(sellExForMarginAdjustment, netEx)

    calculateNetEx(
      paymentModel = paymentModel,
      charge = charge,
      netEx = math.max(BD_ZERO, netEx),
      markupPercent = marginAdjustedMarkupPercent.getOrElse(convertToMarkup(markupPercent)),
      commissionPercent = commissionPercent,
      commissionExcludingAgxOrWholesaleFromHolder = commissionExcludingAgxOrWholesaleFromHolder,
      hotelTaxInfo = hotelTaxInfo,
      rateType = rateType,
      originalRateType = originalRateType,
      roomRateType = roomRateType,
      occ = occ,
      processingFeePercent = processingFeePercent,
      marginInOpt = Some(marginIn),
      dailyTaxes = dailyTaxes,
      reqOcc = reqOcc,
      supplierId = supplierId,
      hasChannelDiscount = hasChannelDiscount,
      hotelId = hotelId,
      chainId = chainId,
      countryId = countryId,
      sellTaxForMarginAdjustment = sellTaxForMarginAdjustment,
      subChargeType = subChargeType,
    )
  }

  def shouldShowExclusivePriceWithFeeEnabled(ctx: YplContext) =
    if (ctx.experimentContext.isUserB(ABTest.SHOW_EXCLUSIVE_WITH_FEE_TO_US_DESTINATION)) {
      ctx.request.isXmlPartner || ctx.addFeesInExclusivePrice
    } else {
      ctx.request.isXmlPartner || ctx.request.regulationFeatureEnabledSetting.isShowExclusivePriceWithFeeEnabled
    }

  /**
    * Please update same logic in [[PriceCalculation.calculateSurchargeForPriceCalculationRefactor()]] as well
    */
  def calculateSurcharge(paymentModel: PaymentModel,
                         dailyPrice: DailyPrice,
                         surchargeRateType: RateType,
                         surchargeEntry: SurchargeEntry,
                         roomPrices: List[YplPrice],
                         taxInfo: TaxInfo,
                         room: YplRoomEntry,
                         reqOcc: YplReqOccByHotelAgePolicy,
                         isPull: Boolean,
                         supplierId: SupplierId,
                         supplierContractedCommission: Option[Double],
                         hotelId: HotelId,
                         chainId: ChainId,
                         countryId: CountryId,
                         fixMarriottSurchargeExp: Boolean)(isBcomFixTaxAmountApplyToPB: Boolean)(implicit
    ctx: YplContext,
    propertyContext: PropertyContext): Option[YplPrice] = {

    implicit val request = ctx.request
    val isShowExclusivePriceWithFeeEnabled = shouldShowExclusivePriceWithFeeEnabled(ctx)
    val isCleanedUpHospitalityTax = ctx.experimentContext.isPropertyB(propertyContext, YplExperiments.CLEAN_HP_TAX)

    val amount =
      if (surchargeEntry.isAmount) surchargeEntry.value
      else calculateSurchargeAmount(dailyPrice.date, surchargeEntry, roomPrices, request.occ)

    val useRateType =
      getUseRateType(surchargeEntry, surchargeRateType, room.rateType, isShowExclusivePriceWithFeeEnabled)

    val quantity = getQuantityForSurcharge(surchargeEntry.id,
                                           surchargeEntry.applyTo,
                                           room,
                                           reqOcc,
                                           isPull,
                                           supplierId,
                                           surchargeEntry.occFromProto,
                                           fixMarriottSurchargeExp)

    if (quantity > 0 && amount > 0.0) {
      val originalDailyTaxes = tax.DailyTaxes(createTaxesWithValuesForDailyTaxes(taxInfo, dailyPrice.taxes),
                                              isCleanedUpHospitalityTax = isCleanedUpHospitalityTax)

      val excludeWholesaleOrAgx = false

      val markup = surchargeEntry.option match {
        case ChargeOption.Excluded => BD_ZERO
        case _ =>
          if (surchargeEntry.isCommissionable) {
            getCommissionForPriceCalculation(
              commissionHolder = room.commissionHolder,
              stayDate = dailyPrice.date,
              occupancy = room.occEntry.occupancy,
              isAgodaAgency = room.isAgodaAgency,
              applicableMORPCandidateRoomParameters = room.applicableMORPCandidateRoomParameters,
              originalRateType = room.originalRateType,
              targetRateType = useRateType,
              excludeWholesaleOrAgx = excludeWholesaleOrAgx,
            )
          } else BD_ZERO
      }

      val surchargeCommission = surchargeEntry.option match {
        case ChargeOption.Excluded => BD_ZERO
        case _ => getReferenceCommission(markup, useRateType, None)
      }

      // Update Graduated Tax to match with Graduated Tax in roomPrices only for FULL and PART (isAmount)
      // otherwise if PART percentage, we can derive actual value
      implicit val dailyTaxes =
        if (originalDailyTaxes.hasTaxProtoTypeLevels) {
          updateDailyTaxForTaxPrototypeLevels(originalDailyTaxes,
                                              dailyPrice,
                                              roomPrices,
                                              amount,
                                              surchargeCommission,
                                              surchargeRateType)
        } else {
          originalDailyTaxes
        }

      val dateFormat = "yyyy-MM-dd"

      val updatedDailyTaxes = updateDailyTaxes(
        dailyTaxes,
        ctx.request.applyTaxOverHelper,
        supplierId,
        hotelId,
        chainId,
        countryId,
        paymentModel.i,
        room.originalRateType.value,
        ctx.request.bookingDate.toString(dateFormat),
      )

      val commissionPercent = surchargeEntry.option match {
        case ChargeOption.Excluded => BD_ZERO
        case _ => getCommissionForSurchargeCalculation(isPull, room.`isLT-1349B`, surchargeEntry, ctx) {
            getCommissionForPriceCalculation(
              commissionHolder = room.commissionHolder,
              stayDate = dailyPrice.date,
              occupancy = room.occEntry.occupancy,
              isAgodaAgency = room.isAgodaAgency,
              applicableMORPCandidateRoomParameters = room.applicableMORPCandidateRoomParameters,
              originalRateType = room.originalRateType,
              targetRateType = room.rateType,
              excludeWholesaleOrAgx = excludeWholesaleOrAgx,
            )
          }
      }
      val calculation = surchargeEntry.option match {
        case ChargeOption.Excluded if !isShowExclusivePriceWithFeeEnabled =>
          Calculation(
            amount,
            BD_ZERO,
            BD_ZERO,
            BD_ZERO,
            (BD_ZERO, None),
            Nil,
            BD_ZERO,
            dailyTaxes = DailyTaxes(List.empty[TaxWithValue], isCleanedUpHospitalityTax = isCleanedUpHospitalityTax),
          )
        case _ =>
          val dailyTaxesToUse: DailyTaxes = getTaxesToUseForSurcharge(surchargeEntry, updatedDailyTaxes)(ctx)

          calculate(
            hotelId = hotelId,
            chainId = chainId,
            countryId = countryId,
            supplierId = supplierId,
            paymentModel = paymentModel,
            channel = room.channel,
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            processingFeePercent = room.processingFees,
            hasChannelDiscount = false,
            sellExForMarginAdjustment = None,
            reqOcc = reqOcc,
            roomOcc = room.occEntry,
            rateType = useRateType,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            charge = ChargeType.Surcharge,
            value = amount,
            commissionPercent = commissionPercent,
            commissionExcludingAgxOrWholesaleFromHolder = markup,
            markup = Some(markup),
            dailyTaxes = dailyTaxesToUse,
            subChargeType = SubChargeType.None,
          )(isBcomFixTaxAmountApplyToPB)
      }

      val margin = calculation.margin
      val processingFee = if (surchargeEntry.option == ChargeOption.Excluded) BD_ZERO else calculation.processingFee

      Some(
        YplPrice(
          date = dailyPrice.date,
          quantity = quantity,
          chargeType = ChargeType.Surcharge,
          applyType = ApplyType.getFromValue(surchargeEntry.applyTo),
          chargeOption = surchargeEntry.option,
          refId = surchargeEntry.id,
          surchargeInfo = ctx.surchargeDataServiceContext.getSurchargeInformation(surchargeId = surchargeEntry.id),
          netExclusive = calculation.netEx,
          tax = calculation.tax,
          fee = calculation.fee,
          margin = margin,
          processingFee = processingFee,
          promotionDiscount = BD_ZERO,
          downliftAmount = BD_ZERO,
          downliftPercent = getDownliftForSurcharge(surchargeEntry),
          taxBreakDown = calculation.breakdowns,
          dailyTaxes = updatedDailyTaxes,
          refMargin = margin,
          refProcessingFee = processingFee,
          isConfigProcessingFee = taxInfo.hotelTaxInfo.isConfigProcessingFees,
          processingFeeBreakdown = calculation.processingFeeBreakdown,
          value = amount,
          discountMessages = Map.empty,
          upliftedSellIn = None,
          downliftExAmount = None,
          surchargeRateType = Some(surchargeRateType),
          surchargeEntry = Some(surchargeEntry),
          dailyPrice = Some(dailyPrice),
          referenceCommissionPercent =
            if (surchargeEntry.isCommissionable) supplierContractedCommission.getOrElse(surchargeCommission)
            else BD_ZERO,
          agxCommission = YplAGXCommission.noAgxCommission,
          currentBreakdownStep = BreakdownStep.BaseStep,
          priceBreakdownHistory = BookingPriceBreakdown(
            isEnabled = request.isBookingRequest,
            protobuf = BaseProtoPrice().update(
              room = room,
              chargeType = ChargeType.Surcharge,
              agencyFeatures = None,
              commissionDailyHolder = None,
              commission = markup,
              surchargeRateType = surchargeRateType,
            ),
          ).addSurcharge(surchargeEntry),
        ))
    } else None
  }

  private[pricing] def getTaxesToUseForSurcharge(surchargeEntry: SurchargeEntry, updatedDailyTaxes: DailyTaxes)(implicit
    ctx: YplContext) = {
    val dailyTaxesToUse =
      if (surchargeEntry.option == ChargeOption.Excluded && ctx.experimentContext.isUserB(
          ABTest.DO_NOT_CONSIDER_EXCLUDED_TAX_FEE_FOR_EXCLUDED_SURCHARGE)) {
        updatedDailyTaxes.copy(
          taxes = updatedDailyTaxes.taxes.filter(_.tax.option == ChargeOption.Mandatory),
        )
      } else {
        updatedDailyTaxes
      }
    dailyTaxesToUse
  }

  // TODO: VEL-2070 rename
  def calculateSurchargeForPriceCalculationRefactor(
    paymentModel: PaymentModel,
    dailyPrice: DailyPrice,
    surchargeRateType: RateType,
    surchargeEntry: SurchargeEntry,
    roomPrices: List[YplPrice],
    taxInfo: TaxInfo,
    room: YplRoomEntry,
    reqOcc: YplReqOccByHotelAgePolicy,
    isPull: Boolean,
    supplierId: SupplierId,
    supplierContractedCommission: Option[Double],
    hotelId: HotelId,
    chainId: ChainId,
    countryId: CountryId,
    cityId: CityId,
    fixMarriottSurchargeExp: Boolean)(implicit ctx: YplContext, propertyContext: PropertyContext): Option[YplPrice] = {
    implicit val request = ctx.request
    val isShowExclusivePriceWithFeeEnabled = shouldShowExclusivePriceWithFeeEnabled(ctx)
    val isCleanedUpHospitalityTax = ctx.experimentContext.isPropertyB(propertyContext, YplExperiments.CLEAN_HP_TAX)

    val amount =
      if (surchargeEntry.isAmount) surchargeEntry.value
      else calculateSurchargeAmount(dailyPrice.date, surchargeEntry, roomPrices, request.occ)

    val useRateType =
      getUseRateType(surchargeEntry, surchargeRateType, room.rateType, isShowExclusivePriceWithFeeEnabled)

    val quantity = getQuantityForSurcharge(surchargeEntry.id,
                                           surchargeEntry.applyTo,
                                           room,
                                           reqOcc,
                                           isPull,
                                           supplierId,
                                           surchargeEntry.occFromProto,
                                           fixMarriottSurchargeExp)

    if (quantity > 0 && amount > 0.0) {
      val excludeWholesaleOrAgx = false

      val markup = surchargeEntry.option match {
        case ChargeOption.Excluded => BD_ZERO
        case _ =>
          if (surchargeEntry.isCommissionable) {
            getCommissionForPriceCalculation(
              commissionHolder = room.commissionHolder,
              stayDate = dailyPrice.date,
              occupancy = room.occEntry.occupancy,
              isAgodaAgency = room.isAgodaAgency,
              applicableMORPCandidateRoomParameters = room.applicableMORPCandidateRoomParameters,
              originalRateType = room.originalRateType,
              targetRateType = useRateType,
              excludeWholesaleOrAgx = excludeWholesaleOrAgx,
            )
          } else BD_ZERO
      }

      val surchargeCommission = surchargeEntry.option match {
        case ChargeOption.Excluded => BD_ZERO
        case _ => getReferenceCommission(markup, useRateType, None)
      }

      val commissionPercent = surchargeEntry.option match {
        case ChargeOption.Excluded => BD_ZERO
        case _ => getCommissionForSurchargeCalculation(isPull, room.`isLT-1349B`, surchargeEntry, ctx) {
            getCommissionForPriceCalculation(
              commissionHolder = room.commissionHolder,
              stayDate = dailyPrice.date,
              occupancy = room.occEntry.occupancy,
              isAgodaAgency = room.isAgodaAgency,
              applicableMORPCandidateRoomParameters = room.applicableMORPCandidateRoomParameters,
              originalRateType = room.originalRateType,
              targetRateType = room.rateType,
              excludeWholesaleOrAgx = excludeWholesaleOrAgx,
            )
          }
      }

      val originalDailyTaxes = tax.DailyTaxes(createTaxesWithValuesForDailyTaxes(taxInfo, dailyPrice.taxes),
                                              isCleanedUpHospitalityTax = isCleanedUpHospitalityTax)
      // Update Graduated Tax to match with Graduated Tax in roomPrices only for FULL and PART (isAmount)
      // otherwise if PART percentage, we can derive actual value
      val dailyTaxes =
        if (originalDailyTaxes.hasTaxProtoTypeLevels) {
          updateDailyTaxForTaxPrototypeLevels(originalDailyTaxes,
                                              dailyPrice,
                                              roomPrices,
                                              amount,
                                              surchargeCommission,
                                              surchargeRateType)
        } else {
          originalDailyTaxes
        }

      val dailyTaxesToUse: DailyTaxes = getTaxesToUseForSurcharge(surchargeEntry, dailyTaxes)(ctx)

      val calculation = surchargeEntry.option match {
        case ChargeOption.Excluded if !isShowExclusivePriceWithFeeEnabled =>
          Calculation(
            amount,
            BD_ZERO,
            BD_ZERO,
            BD_ZERO,
            (BD_ZERO, None),
            Nil,
            BD_ZERO,
            dailyTaxes = DailyTaxes(List.empty[TaxWithValue], isCleanedUpHospitalityTax = isCleanedUpHospitalityTax),
          )
        case _ =>
          val priceCalculationRequest = PriceCalculationRequestMapper.toPriceCalculationRequest(
            hotelId = hotelId,
            chainId = chainId,
            countryId = countryId,
            cityId = cityId,
            supplierId = supplierId,
            paymentModel = paymentModel,
            isThirdPartySupplier = ctx.isThirdParty(supplierId),
            isApplyTaxOnSellEx =
              ctx.isApplyTaxOnSellEx(supplierId, hotelId, chainId, countryId, paymentModel.i, room.originalRateType),
            applyTaxOverHelper = ctx.request.applyTaxOverHelper,
            taxType = taxInfo.hotelTaxInfo.taxType,
            isConfigProcessingFees = taxInfo.hotelTaxInfo.isConfigProcessingFees,
            processingFeePercent = room.processingFees,
            dailyTaxes = dailyTaxesToUse,
            charge = ChargeType.Surcharge,
            subChargeType = SubChargeType.None,
            value = amount,
            rateType = useRateType,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            hasChannelDiscount = false,
            sellExForMarginAdjustment = None,
            applicableDate = ctx.request.bookingDate,
            reqOcc = toCommonReqOccByHotelAgePolicy(reqOcc),
            roomOcc = toCommonTaxRoomOcc(room.occEntry),
            commissionPercent = commissionPercent,
            contractCommissionPercent = markup,
            markup = Some(markup),
            supplierContractedCommission = supplierContractedCommission,
            los = ctx.request.lengthOfStay,
            storefrontId = ctx.request.cInfo.storeFront.getOrElse(0),
            whitelabelId = ctx.request.whitelabelSetting.whitelabelID,
            experimentContext = ctx.experimentContext,
          )
          priceBreakdownCalculator.calculate(priceCalculationRequest)
      }

      val processingFee = if (surchargeEntry.option == ChargeOption.Excluded) BD_ZERO else calculation.processingFee

      val refCommissionPercent =
        if (surchargeEntry.isCommissionable) {
          supplierContractedCommission.getOrElse(
            surchargeEntry.option match {
              case ChargeOption.Excluded => BD_ZERO
              case _ => calculation.referenceCommissionPercent
            },
          )
        } else BD_ZERO

      Some(
        YplPrice(
          date = dailyPrice.date,
          quantity = quantity,
          chargeType = ChargeType.Surcharge,
          applyType = ApplyType.getFromValue(surchargeEntry.applyTo),
          chargeOption = surchargeEntry.option,
          refId = surchargeEntry.id,
          surchargeInfo = ctx.surchargeDataServiceContext.getSurchargeInformation(surchargeId = surchargeEntry.id),
          netExclusive = calculation.netEx,
          tax = calculation.tax,
          fee = calculation.fee,
          margin = calculation.margin,
          processingFee = processingFee,
          promotionDiscount = BD_ZERO,
          downliftAmount = BD_ZERO,
          downliftPercent = getDownliftForSurcharge(surchargeEntry),
          taxBreakDown = calculation.breakdowns,
          dailyTaxes = calculation.dailyTaxes,
          refMargin = calculation.margin,
          refProcessingFee = processingFee,
          isConfigProcessingFee = taxInfo.hotelTaxInfo.isConfigProcessingFees,
          processingFeeBreakdown = calculation.processingFeeBreakdown,
          value = amount,
          discountMessages = Map.empty,
          upliftedSellIn = None,
          downliftExAmount = None,
          surchargeRateType = Some(surchargeRateType),
          surchargeEntry = Some(surchargeEntry),
          dailyPrice = Some(dailyPrice),
          referenceCommissionPercent = refCommissionPercent,
          agxCommission = YplAGXCommission.noAgxCommission,
          currentBreakdownStep = BreakdownStep.BaseStep,
          priceBreakdownHistory = BookingPriceBreakdown(
            isEnabled = ctx.request.isBookingRequest,
            protobuf = BaseProtoPrice().update(
              room = room,
              chargeType = ChargeType.Surcharge,
              agencyFeatures = None,
              commissionDailyHolder = None,
              commission = markup,
              surchargeRateType = surchargeRateType,
            ),
          ).addSurcharge(surchargeEntry),
        ))
    } else None
  }

  // TODO: check why do we need this logic
  private[pricing] def getDownliftForSurcharge(surchargeEntry: SurchargeEntry): Double =
    if (surchargeEntry.isAmount) BD_ZERO else surchargeEntry.value

  private[pricing] def getCommissionForSurchargeCalculation(isPull: Boolean,
                                                            `isLT1349B`: Boolean,
                                                            surchargeEntry: SurchargeEntry,
                                                            ctx: YplContext)(getCommissionFn: => Double)(implicit
    propertyContext: PropertyContext): Double =
    if (isSurchargeCommissionable(isPull, `isLT1349B`, surchargeEntry, ctx)) {
      getCommissionFn
    } else {
      BD_ZERO
    }

  private[pricing] def isSurchargeCommissionable(isPull: Boolean,
                                                 `isLT1349B`: Boolean,
                                                 surchargeEntry: SurchargeEntry,
                                                 ctx: YplContext)(implicit propertyContext: PropertyContext) =
    if (surchargeEntry.isCommissionable) {
      true
    } else {
      if (isPull) {
        !`isLT1349B`
      } else {
        !ctx.experimentContext.isPropertyB(propertyContext, ABTest.PASS_ZERO_COMMISSION_FOR_NON_COMMISSIONABLE_SURCHARGE)
      }
    }

  private[pricing] def getUseRateType(surchargeEntry: SurchargeEntry,
                                      surchargeRateType: RateType,
                                      roomRateType: RateType,
                                      isShowExclusivePriceWithFeeEnabled: Boolean) =
    (surchargeEntry.option, surchargeEntry.isAmount, surchargeRateType) match {
      case (ChargeOption.Excluded, _, _) if isShowExclusivePriceWithFeeEnabled => RateType.SellInclusive
      case (_, false, _) => roomRateType
      case (_, true, RateType.Unknown) => roomRateType
      case (_, true, _) => surchargeRateType
    }

  def updateDailyTaxForTaxPrototypeLevels(dailyTaxes: DailyTaxes,
                                          dailyPrice: DailyPrice,
                                          roomPrices: List[YplPrice],
                                          surchargeAmount: Double,
                                          surchargeCommission: Double,
                                          surchargeRateType: RateType): DailyTaxes = {
    val newTaxes: TaxesWithValues = dailyTaxes.taxes.map { taxWithValue =>
      val tax = taxWithValue.tax
      roomPrices
        .find(p => p.date == dailyPrice.date && p.isRoom)
        .flatMap { roomPrice =>
          roomPrice.dailyTaxes.taxes.find(p => p.tax.id == tax.id && p.tax.protoTypeId == tax.protoTypeId).map {
            matchTax =>
              val rateType = surchargeRateType
              val sellex = roomPrice.sellExclusive
              val commission = surchargeCommission
              toPriceCalcTaxWithValue(
                GraduatedTaxCalculator.calculateSurchargeTaxValue(toCommonRateType(rateType),
                                                                  sellex,
                                                                  surchargeAmount,
                                                                  commission,
                                                                  toCommonTaxWithValue(matchTax)))
          }
        }
        .getOrElse(taxWithValue)
    }
    DailyTaxes(newTaxes, isCleanedUpHospitalityTax = dailyTaxes.isCleanedUpHospitalityTax)
  }

  private def calculateSurchargeAmount(date: DateTime,
                                       s: SurchargeEntry,
                                       prices: List[YplPrice],
                                       occ: YplOccInfo): Double = {
    val roomPrices = if (!s.applyTo.contains("PB")) prices.filter(_.date == date) else prices
    val p = roomPrices.getDSum { x =>
      if (s.option == ChargeOption.Excluded) x.sellExclusive * x.quantity
      else (x.value - x.promotionDiscount) * x.quantity
    }
    val numberOfRoom = if (s.applyTo.startsWith("PR")) occ.getRooms.toDouble else 1.0
    (p * s.value * TO_PERCENT) / numberOfRoom
  }

  private[pricing] def getOccupancyTupleForPull(room: YplRoomEntry, reqOcc: YplReqOccByHotelAgePolicy) =
    if (room.`isLT-1349B`) {
      val propOfferOccupancy = room.propOfferOccupancy
      (propOfferOccupancy.numAdults, propOfferOccupancy.numChild, propOfferOccupancy.guests)
    } else {
      val searchOcc = reqOcc.occ
      (searchOcc.adults, searchOcc.children, searchOcc.adults + searchOcc.children)
    }

  /**
    * Return quantity of surcharge.
    * For Pull supplier we don't care about age policy here.
    * Because Pull supplier have own logic to calculate age policy. e.g ActAsAdult
    *
    * @param applyTo Surcharge applyTo
    * @param room    YplRoomEntry
    * @param reqOcc  Occupancy Policy contain occ from Search and occ with applied age policy
    * @param isPull  is Pull Supplier
    * @return quantity of surcharge
    */
  private[pricing] def getQuantityForSurcharge(surchargeID: Int,
                                               applyTo: String,
                                               room: YplRoomEntry,
                                               reqOcc: YplReqOccByHotelAgePolicy,
                                               isPull: Boolean,
                                               supplierId: SupplierId,
                                               occFromProto: Int,
                                               fixMarriottSurchargeExp: Boolean): Int = {
    val (adult, child, guests) =
      if (isPull) {
        getOccupancyTupleForPull(room, reqOcc)
      } else {
        val guest =
          if (SurchargeSettings.WHITELIST_SURCHARGE_ID_APPLY_PG_ON_INFANT.contains(surchargeID)) {
            // currently guests = adults + children (exclude infants)
            reqOcc.guestsWithInfants
          } else reqOcc.surchargeGuests
        (reqOcc.adults, reqOcc.surchargeChildren, guest)
      }

    val (adultSum, childSum, guestSum) =
      if (room.isJapanChildRateApplied) {
        val totalAdults = room.roomAllocationInfo.values.map(_.adults).sum
        val totalRoomOccSum = room.roomAllocationInfo.values.map(_.totalRoomOcc).sum
        (totalAdults, totalRoomOccSum - totalAdults, totalRoomOccSum)
      } else {
        (adult, child, guests)
      }
    applyTo.substring(0, 2) match {
      case "PR" => getPRQuantityForSurcharge(room, reqOcc, occFromProto, fixMarriottSurchargeExp)
      case "PA" => if (reqOcc.isFreeOcc) room.occEntry.adults else adultSum
      case "PC" => if (reqOcc.isFreeOcc) room.occEntry.children else childSum
      case "PG" | "PP" => if (reqOcc.isFreeOcc) room.occEntry.guests else guestSum
      case "PB" | "PN" if supplierId == DMC.BCOM => reqOcc.rooms
      case _ => 1
    }
  }

  private[pricing] def getPRQuantityForSurcharge(room: YplRoomEntry,
                                                 reqOcc: YplReqOccByHotelAgePolicy,
                                                 surchargeOccProto: Int,
                                                 fixMarriottSurchargeExp: Boolean) =
    if (fixMarriottSurchargeExp) {
      room.occupancyBreakdown match {
        case Some(occBd) if occBd.occupancyUnits.isEmpty => reqOcc.rooms
        case Some(occBd) =>
          // note: surchargeOccProto can be 0 if it is from surchargePerDay where we do not care for which occ
          if (surchargeOccProto == 0) {
            occBd.rooms
          } else {
            occBd.occupancyUnits.groupBy(_.roomNo).count { case (_, units) =>
              units.filter(_.payingAgeType != AsFree).map(_.qty).sum == surchargeOccProto
            }
          }
        case _ => reqOcc.rooms
      }
    } else {
      reqOcc.rooms
    }

  private[pricing] def calculateAdjustedMarginMarkupPercent(
    sellExForMarginAdjustment: Option[Double],
    netEx: Double): Option[Double] = sellExForMarginAdjustment.map { sellEx =>
    val fallbackAdjustedMarkup = 5.0
    val isNegativeMargin = sellEx <= netEx
    if (isNegativeMargin) {
      fallbackAdjustedMarkup
    } else {
      // from calculateNetEx
      // margin = netEx * markup
      // markup = margin / netEx
      //        = (sellEx - netEx) / netEx
      //        = sellEx / netEx - netEx / netEx
      //        = sellEx / netEx - 1
      (BD_HUNDRED * sellEx / netEx) - BD_HUNDRED
    }
  }

  /**
    * We apply AGP only on Room and ExtraBed, not Surcharge
    */
  override def calculateAgpPrice(paymentModel: PaymentModel,
                                 commissionPercent: Double,
                                 hotelTaxInfo: HotelTaxInfo,
                                 reqOcc: YplReqOccByHotelAgePolicy,
                                 roomPriceInfo: RoomPriceInfo,
                                 supplierId: SupplierId,
                                 hotelId: HotelId,
                                 chainId: ChainId,
                                 countryId: CountryId,
                                 cityId: CityId,
                                 price: YplPrice,
                                 gpCommissionBreakdown: GrowthProgramCommissionBreakdown,
                                 commissionExcludingWholesaleOrAgx: => Double)(implicit
    ctx: YplContext,
    propertyContext: PropertyContext): YplPrice =
    if (price.chargeType == ChargeType.Room || price.chargeType == ChargeType.ExtraBed) {
      val isCalculateTaxAndFeeWithMarginTaxAndFee =
        ctx.request.featureFlags.contains(FeatureFlag.CalculateTaxAndFeeWithMarginTaxAndFee)
      val bookingPriceBreakdown = price
        .getUpdatedPriceBreakdown()
        .copy(
          firedrill = commissionPercent,
          gpCommissionBreakdown = gpCommissionBreakdown,
        )
      val flatTaxAmount = calculateFlatTaxAmount(price.chargeType, hotelTaxInfo.taxType, price.taxBreakDown)
      val sellInclusive = price.sellInclusive - flatTaxAmount
      val fdPrice =
        if (ctx.experimentContext.isPropertyB(propertyContext, ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION)) {
          calculatePriceForPriceCalculationRefactor(
            paymentModel = paymentModel,
            date = price.date,
            commissionPercent = commissionPercent,
            agxCommission = price.agxCommission,
            commissionExcludingWholesaleOrAgx = commissionExcludingWholesaleOrAgx,
            hotelTaxInfo = hotelTaxInfo,
            dailyTaxes = price.dailyTaxes,
            reqOcc = toCommonReqOccByHotelAgePolicy(reqOcc),
            chargeType = price.chargeType,
            quantity = price.quantity,
            applyType = price.applyType,
            chargeOption = price.chargeOption,
            promoDiscount = 0d,
            valueWithChannelDiscount = sellInclusive,
            discountMessages = Map.empty,
            roomPriceInfo = roomPriceInfo.copy(rateType = RateType.SellInclusive),
            supplierId = supplierId,
            subChargeType = price.subChargeType,
            roomNo = price.roomNumber,
            channelDiscountBreakdowns = price.channelDiscounts,
            resellRefSell = price.resellRefSell,
            currentBreakdownStep = BreakdownStep.AGP,
            bookingPriceBreakdown = bookingPriceBreakdown,
            apmPriceAdjustmentDetail = price.apmPriceAdjustmentDetail,
            apmCommissionDiscountPercent = price.apmCommissionDiscountPercent,
            childAgeRangeId = price.childAgeRangeId,
            hotelId = hotelId,
            chainId = chainId,
            countryId = countryId,
            cityId = cityId,
            sellExForMarginAdjustment = Some(price.sellExclusive),
            supplierFundedDiscountAmount = None,
            uspaDiscountAmount = None,
            uspaProgramId = None,
            isApplyTaxOnSellEx = ctx.isApplyTaxOnSellEx(supplierId,
                                                        hotelId,
                                                        chainId,
                                                        countryId,
                                                        paymentModel.i,
                                                        roomPriceInfo.originalRateType),
            lengthOfStay = ctx.request.lengthOfStay,
            storefrontId = ctx.request.cInfo.storeFront.getOrElse(0),
            whitelabelId = ctx.request.whitelabelSetting.whitelabelID,
            isThirdPartySupplier = ctx.isThirdParty(supplierId),
            applyTaxOverHelper = ctx.request.applyTaxOverHelper,
            experimentContext = ctx.experimentContext,
          )
        } else {
          calculatePrice(
            paymentModel = paymentModel,
            date = price.date,
            commissionPercent = commissionPercent,
            agxCommission = price.agxCommission,
            commissionExcludingWholesaleOrAgx = commissionExcludingWholesaleOrAgx,
            hotelTaxInfo = hotelTaxInfo,
            dailyTaxes = price.dailyTaxes,
            reqOcc = reqOcc,
            chargeType = price.chargeType,
            quantity = price.quantity,
            applyType = price.applyType,
            chargeOption = price.chargeOption,
            promoDiscount = 0d,
            value = sellInclusive,
            valueWithChannelDiscount = sellInclusive,
            discountMessages = Map.empty,
            roomPriceInfo = roomPriceInfo.copy(rateType = RateType.SellInclusive),
            supplierId = supplierId,
            subChargeType = price.subChargeType,
            roomNo = price.roomNumber,
            channelDiscounts = price.channelDiscounts,
            resellRefSell = price.resellRefSell,
            currentBreakdownStep = BreakdownStep.AGP,
            bookingPriceBreakdown = bookingPriceBreakdown,
            apmPriceAdjustmentDetail = price.apmPriceAdjustmentDetail,
            apmCommissionDiscountPercent = price.apmCommissionDiscountPercent,
            childAgeRangeId = price.childAgeRangeId,
            hotelId = hotelId,
            chainId = chainId,
            countryId = countryId,
            sellExForMarginAdjustment = Some(price.sellExclusive),
            supplierFundedDiscountAmount = None,
            uspaDiscountAmount = None,
            uspaProgramId = None,
          )(supplierId == DMC.BCOM)
        }
      price.copy(
        netExclusive = fdPrice.netExclusive,
        margin = fdPrice.margin,
        processingFee = fdPrice.processingFee,
        refMargin = fdPrice.margin,
        refProcessingFee = fdPrice.processingFee,
        tax = fdPrice.tax,
        fee = fdPrice.fee,
        taxBreakDown = fdPrice.taxBreakDown,
        processingFeeBreakdown =
          if (isCalculateTaxAndFeeWithMarginTaxAndFee) fdPrice.processingFeeBreakdown else price.processingFeeBreakdown,
        referenceCommissionPercent = fdPrice.referenceCommissionPercent,
        currentBreakdownStep = BreakdownStep.AGP,
        priceBreakdownHistory = fdPrice.priceBreakdownHistory,
      )
    } else price

  private[pricing] def checkApplyTaxOnSellEx(supplierId: SupplierId,
                                             hotelId: HotelId,
                                             chainId: ChainId,
                                             countryId: CountryId,
                                             paymentModel: Int,
                                             originalRateType: RateType)(implicit ctx: YplContext): Boolean = {
    val isEnableNegativeSegmentExp = ctx.experimentContext.isUserB(ABTest.ENABLE_NEGATIVE_SEGMENT)
    val isApplyTaxOnSellExBaseOnConfig = ctx.request.applyTaxOnSellExSettings.exists(
      _.isApplyTaxOnSellEx(
        supplierId,
        hotelId,
        chainId,
        countryId,
        paymentModel,
        originalRateType.value,
        isEnableNegativeSegmentExp,
      ))
    val supplierHotelCalculationSetting = ctx.request.supplierHotelCalculationSettings.settings.get(supplierId)
    val isApplyTaxOnSellExHotelLevel = supplierHotelCalculationSetting.exists(_.isApplyTaxOnSellEx)
    isApplyTaxOnSellExBaseOnConfig || isApplyTaxOnSellExHotelLevel
  }

  // TODO: VEL-2070 remove
  private[pricing] def updateDailyTaxes(
    dailyTaxes: DailyTaxes,
    applyTaxOverHelper: ApplyTaxOverHelper,
    supplierId: SupplierId,
    hotelId: HotelId,
    chainId: ChainId,
    countryId: CountryId,
    paymentModelId: Int,
    rateTypeId: Int,
    applicableDate: String,
  )(implicit ctx: YplContext): DailyTaxes = {
    val filteredTaxes = dailyTaxes.taxes.filter { case TaxWithValue(tax, _) =>
      val isSuperAgg: Boolean = TaxUtil.is3rdPartySupplier(supplierId)
      val storefrontId: Int = ctx.request.cInfo.storeFront.getOrElse(0)
      val whitelabelId: Int = ctx.request.whitelabelSetting.whitelabelID

      // Check if the tax is applicable
      CommonTaxValidator.isApplicable(
        taxTypeId = tax.id,
        hotelCountryId = countryId,
        applicableDate = Some(applicableDate),
        chainId = Some(chainId),
        rateLoadTypeId = Some(rateTypeId),
        isSuperagg = Some(isSuperAgg),
        storefrontId = Some(storefrontId),
        whitelabelId = Some(whitelabelId),
      )
    }

    val isEnableNegativeSegmentExp = ctx.experimentContext.isUserB(ABTest.ENABLE_NEGATIVE_SEGMENT)

    val updatedTaxes = filteredTaxes.map { case TaxWithValue(tax, value) =>
      val applyTaxOver = getApplyTaxOver(
        applyTaxOverHelper,
        supplierId,
        hotelId,
        chainId,
        countryId,
        paymentModelId,
        rateTypeId,
        tax.protoTypeId,
        tax.applyBreakdownType,
        tax.applyOn,
        isEnableNegativeSegmentExp,
      )
      TaxWithValue(tax.copy(applyOver = applyTaxOver), value)
    }

    dailyTaxes.copy(taxes = updatedTaxes)
  }
}
