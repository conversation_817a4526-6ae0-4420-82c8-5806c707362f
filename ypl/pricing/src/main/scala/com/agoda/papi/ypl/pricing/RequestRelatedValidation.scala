package com.agoda.papi.ypl.pricing

import com.agoda.papi.ypl.models.YplRequest
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.models.enums.VipLevelType
import com.agoda.papi.ypl.models.pricing.proto.CustomerSegment
import com.agoda.papi.ypl.models.suppliers.DMC
import org.joda.time.{DateTime, Days, LocalTime}

/**
  * Created by e<PERSON><PERSON><PERSON> on 10/28/2016 AD.
  *
  * Use it to do time/request related verifications.
  */
trait RequestRelatedValidation {
  private val toGMT = -7

  /**
    * Check if booking date is between [checkOut-maxAdv, checkIn-minAdv]
    *
    * checkin - maxAdv = cInMax
    * checkin - minAdv = cInMin
    * checkout - maxAdv = cOutMax
    * checkout - minAdv = cOutMin
    *
    * =================================================
    *          min b.date                max b.date
    * ----|--------|-----------||-----------|--------|---------|--------|----
    *  cInMax   cOutMax   booking date   cInMin   cOutMin   checkin  checkout
    */
  def validateMinMaxAdvancePurchase(minAdvance: Option[Int],
                                    maxAdvance: Option[Int],
                                    hotelGMT: GmtOffset,
                                    supplierId: SupplierId)(implicit ctx: YplContext, r: YplRequest): Boolean = {
    val isDMCHardcodingEnabled = ctx.experimentContext.isUserB(YplExperiments.DMC_HARDCODING_REMOVAL)
    val validateCheckinCheckout =
      if (isDMCHardcodingEnabled)
        ctx.request.supplierFeatures.features.get(supplierId).exists(_.validateCheckinCheckout)
      else (supplierId == DMC.JTBWL)
    if (validateCheckinCheckout) {
      val isCheckinValid = validateBookDay(None, minAdvance.map(r.checkIn.minusDays), hotelGMT)
      val isCheckoutValid = validateBookDay(maxAdvance.map(maxAdv => r.checkOut.minusDays(maxAdv + 1)), None, hotelGMT)

      isCheckinValid && isCheckoutValid
    } else {
      validateBookDay(maxAdvance.map(r.checkIn.minusDays), minAdvance.map(r.checkIn.minusDays), hotelGMT)
    }
  }

  /**
    * Day-week validation
    * @param bookOn example '0110111'
    */
  def validateBookOn(bookOn: Option[String])(implicit r: YplRequest): Boolean =
    Option(bookOn).flatten.filter(str => str.length == 7).forall { allowedBookDay =>
      val bookingDate = r.bookingDate.getDayOfWeek % 7
      allowedBookDay(bookingDate) != '0'
    }

  /**
    * Verifying category book from & to periods.
    * Currently we support different combinations (from / to / from+to)
    */
  def validateBookDay(bookFrom: Option[DateTime], bookTo: Option[DateTime], hotelGMT: GmtOffset)(implicit
    ctx: YplContext,
    r: YplRequest): Boolean = {
    val bookingDateHotelTime = r.bookingDate.plusHours(toGMT + hotelGMT)

    val bookDayOnly = bookingDateHotelTime.withTime(0, 0, 0, 0)
    val isBookDayFromValid = bookDayOnly.getMillis >= bookFrom.getOrElse(DateTime.now()).getMillis
    val isBookDayToValid = bookDayOnly.getMillis <= bookTo.getOrElse(DateTime.now()).getMillis

    //  Basic checks
    val bookDayValidation = (bookFrom.isDefined, bookTo.isDefined) match {
      case (true, true) => isBookDayFromValid && isBookDayToValid
      case (true, _) => isBookDayFromValid
      case (_, true) => isBookDayToValid
      case _ => true
    }

    //  It's possible that we have booking day ahead checkin day ()
    val aheadDays = 2
    val bookDayAheadValidation = Days.daysBetween(r.checkIn, bookDayOnly).getDays != aheadDays
    bookDayValidation && bookDayAheadValidation
  }

  def validateBookTime(bookTimeFrom: Option[Long], bookTimeTo: Option[Long], hotelGMT: GmtOffset)(implicit
    r: YplRequest): Boolean = {
    val bookingDateHotelTime = r.bookingDate.plusHours(toGMT + hotelGMT)
    def convertTick(tick: Long): DateTime = new DateTime(0).withMillis(tick / 10000)

    val bookTimeFromMillis = convertTick(bookTimeFrom.getOrElse(0)).plusHours(toGMT).getMillis
    val bookTimeToMillis = convertTick(bookTimeTo.getOrElse(0)).plusHours(toGMT).getMillis
    // val bookTime = convertTick(bookingDateHotelTime.getMillisOfDay * 10000L)   //  Doesn't work but it should.
    val bookTime = new DateTime(1970,
                                1,
                                1,
                                bookingDateHotelTime.getHourOfDay,
                                bookingDateHotelTime.getMinuteOfHour,
                                bookingDateHotelTime.getSecondOfMinute)

    val isTimeFromCorrect = bookTime.getMillis >= bookTimeFromMillis
    val isTimeToCorrect = bookTime.getMillis <= bookTimeToMillis

    (bookTimeFrom.isDefined, bookTimeTo.isDefined) match {
      //  Overnight case
      case (true, true) if bookTimeFrom.get > bookTimeTo.get => isTimeFromCorrect || isTimeToCorrect
      case (true, true) => isTimeFromCorrect && isTimeToCorrect
      case (true, _) => isTimeFromCorrect
      case (_, true) => isTimeToCorrect
      case _ => true
    }
  }

  /**
    * Verifying category book from & to periods for OTA
    * Currently we support different combinations (from / to / from+to)
    */
  def validateBookTimeOTA(bookTimeFrom: Option[Long], bookTimeTo: Option[Long], hotelGMT: GmtOffset)(implicit
    r: YplRequest): Boolean = {
    val bookingDateHotelTime = r.bookingDate.plusHours(toGMT + hotelGMT)
    val bookingTimeFrom = new LocalTime(bookTimeFrom.getOrElse(0L))
    val bookingTimeTo = new LocalTime(bookTimeTo.getOrElse(0L))

    if (bookingTimeFrom != bookingTimeTo) {
      val bookingTime = bookingDateHotelTime.toLocalTime.withSecondOfMinute(0)
      val isTimeFromCorrect = !bookingTime.isBefore(bookingTimeFrom)
      val isTimeToCorrect = !bookingTime.isAfter(bookingTimeTo)

      if (bookingTimeTo.isBefore(bookingTimeFrom)) {
        isTimeFromCorrect || isTimeToCorrect
      } else {
        isTimeFromCorrect && isTimeToCorrect
      }
    } else true
  }

  def validateCustomerSegment(
    customerSegments: List[CustomerSegment],
    origin: String,
    languageId: Int,
    vipLevel: Option[VipLevelType])(implicit ctx: YplContext): Boolean = customerSegments.isEmpty || {

    val vipSegments = customerSegments.filter(x => x.vipLevel.exists(_.value != VipLevelType.UNDEFINED.value))
    val nonVipSegments =
      customerSegments.filter(x => x.vipLevel.isEmpty || x.vipLevel.exists(_.value == VipLevelType.UNDEFINED.value))

    (validateVipSegment(vipSegments, vipLevel)
    && validateOriginLanguageCustomerSegment(nonVipSegments, origin, languageId))

  }

  def validateVipSegment(vipSegments: List[CustomerSegment], vipLevel: Option[VipLevelType])(implicit
    ctx: YplContext): Boolean =
    if (vipSegments.isEmpty) {
      true
    } else {
      ctx.experimentContext.isUserB(YplExperiments.CHECK_VIP_CUSTOMER_SEGMENT) &&
      (vipLevel.isDefined &&
      vipSegments.exists { cs =>
        (cs.vipLevel, vipLevel) match {
          case (Some(segmentLevel), Some(userLevel)) => segmentLevel.value == userLevel.value ||
            VipSegmentExtraValidationHelper.isDiamondCustomerWithPlatinumSegment(segmentLevel, userLevel)
          case _ => false
        }
      })
    }

  def validateOriginLanguageCustomerSegment(customerSegments: List[CustomerSegment], origin: String, language: Int)(
    implicit ctx: YplContext): Boolean = customerSegments.isEmpty || {
    if (ctx.experimentContext.isUserB(YplExperiments.ORIGIN_CASE_INSENSITIVE_CUSTOMER_SEGMENT)) {
      customerSegments.exists(cs =>
        cs.countryCode.forall(_.equalsIgnoreCase(origin)) &&
        cs.languageId.forall(_ == language))
    } else {
      customerSegments.exists(cs =>
        cs.countryCode.forall(_ == origin) &&
        cs.languageId.forall(_ == language))
    }
  }

  def validateSameDayBookingCutoffTime(
    checkIn: DateTime,
    bookingDate: DateTime,
    bookingCutOffTime: Option[Long],
    hotelGMT: GmtOffset): Boolean = bookingCutOffTime.filter(_ > 0).forall { bCutOff =>
    val bookingDateInHotelTimeZone = bookingDate.plusHours(toGMT + hotelGMT)
    !isSameDayBooking(checkIn, bookingDateInHotelTimeZone) || isValidSameDayBookTime(bookingDateInHotelTimeZone, bCutOff)
  }

  private def isValidSameDayBookTime(bookingDateInHotelTimeZone: DateTime, bookingCutOffTime: Long): Boolean =
    bookingDateInHotelTimeZone.getMillisOfDay <= bookingCutOffTime

  private def isSameDayBooking(checkIn: DateTime, bookingDateInHotelTimeZone: DateTime): Boolean =
    Days.daysBetween(bookingDateInHotelTimeZone.toLocalDate, checkIn.toLocalDate).isLessThan(Days.ONE)

  def validateBookingTimeIsBeforePropertyCutOffTime(
    bookingCutOffTime: Option[LocalTime],
    gmtOffset: GmtOffset,
    gmtOffsetMinutes: GmtOffset,
    bookingDate: DateTime,
    checkIn: DateTime,
  ): Boolean = bookingCutOffTime match {
    case Some(cutOffTime) =>
      val hotelLocalBookingDate = bookingDate.plusHours(toGMT + gmtOffset).plusMinutes(gmtOffsetMinutes)
      val hotelLocalCutOffTime =
        checkIn.withTime(cutOffTime.getHourOfDay, cutOffTime.getMinuteOfHour, cutOffTime.getSecondOfMinute, 0)

      hotelLocalBookingDate.isBefore(hotelLocalCutOffTime)

    case _ => true
  }
}
