package com.agoda.papi.ypl.pricing

import com.agoda.finance.tax.models.ESSfinancialBreakdown
import com.agoda.papi.enums.hotel.{PaymentModel, TaxType}
import com.agoda.papi.enums.room.{ChargeType, SubChargeType}
import com.agoda.papi.ypl.models.pricing.{RoomOccupancy, YPLESSFinancialBreakdown}
import com.agoda.papi.ypl.models.{
  SupplierId,
  YPLBookingCountryIndicator,
  YplContext,
  YplExperiments,
  YplReqOccByHotelAgePolicy,
}
import com.agoda.finance.tax.services.tax.{B2CTaxCalculator, TaxCalculator => CommonTaxCalculator}
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes, TaxFeeBreakdowns}
import com.agoda.papi.pricing.pricecalculation.utils.CommonTaxConverter._
import com.agoda.papi.ypl.models.CommonTaxConverter.{
  toCommonReqOccByHotelAgePolicy,
  toCommonTaxBookingCountryIndicator,
  toCommonTaxRoomOcc,
  toYPLESSFinancialBreakdown,
}
import com.agoda.papi.ypl.models.pricing.proto.HotelTaxInfo
import com.agoda.utils.collection.SumImplicits._
import com.agoda.utils.flow.PropertyContext
import org.joda.time.DateTime

trait TaxCalculatorImpl extends TaxCalculator {
  override def calculateTaxes(charge: ChargeType,
                              value: Double,
                              margin: Double,
                              occ: RoomOccupancy,
                              dailyTaxes: DailyTaxes,
                              reqOcc: YplReqOccByHotelAgePolicy,
                              paymentModel: PaymentModel,
                              supplierId: SupplierId,
                              hotelTaxInfo: Option[HotelTaxInfo],
                              subChargeType: SubChargeType)(implicit
    ctx: YplContext,
    propertyContext: PropertyContext): TaxFeeBreakdowns = calculateTaxesCommon(charge,
                                                                               value,
                                                                               margin,
                                                                               occ,
                                                                               dailyTaxes,
                                                                               reqOcc,
                                                                               paymentModel,
                                                                               supplierId,
                                                                               hotelTaxInfo,
                                                                               subChargeType)

  def calculateTaxesCommon(
    charge: ChargeType,
    value: Double,
    margin: Double,
    occ: RoomOccupancy,
    dailyTaxes: DailyTaxes,
    reqOcc: YplReqOccByHotelAgePolicy,
    paymentModel: PaymentModel,
    supplierId: SupplierId,
    hotelTaxInfo: Option[HotelTaxInfo],
    subChargeType: SubChargeType)(implicit ctx: YplContext, propertyContext: PropertyContext): TaxFeeBreakdowns = {
    val isCleanedUpHospitalityTax = ctx.experimentContext.isPropertyB(propertyContext, YplExperiments.CLEAN_HP_TAX)
    val commonTaxFeeBreakdown = CommonTaxCalculator.calculateTaxes(
      charge = toCommonChargeType(charge),
      value = value,
      margin = margin,
      occ = toCommonTaxRoomOcc(occ),
      dailyTaxes = toCommonDailyTaxes(dailyTaxes),
      reqOcc = toCommonReqOccByHotelAgePolicy(reqOcc),
      paymentModel = toCommonPaymentModel(paymentModel),
      supplierId = supplierId,
      lengthOfStay = ctx.request.lengthOfStay,
      subChargeType = subChargeType,
      isCleanedUpHospitalityTax = isCleanedUpHospitalityTax,
    )

    TaxFeeBreakdowns(
      taxDefault = commonTaxFeeBreakdown.taxDefault,
      fee = commonTaxFeeBreakdown.fee,
      taxFeeDefaultBreakdowns = commonTaxFeeBreakdown.taxFeeDefaultBreakdowns.map(toCommonTaxBreakdown),
      taxOverSellEx = commonTaxFeeBreakdown.taxOverSellEx,
      taxFeeOverSellExBreakdowns = commonTaxFeeBreakdown.taxFeeOverSellExBreakdowns.map(toCommonTaxBreakdown),
      hospitalityPriceTax = commonTaxFeeBreakdown.hospitalityPriceTax,
      hospitalityPriceTaxBreakdown = commonTaxFeeBreakdown.hospitalityPriceTaxBreakdowns.map(toCommonTaxBreakdown),
      surchargeTax = commonTaxFeeBreakdown.surchargeTax,
      surchargeTaxBreakdown = commonTaxFeeBreakdown.surchargeTaxBreakdowns.map(toCommonTaxBreakdown),
      taxOverMargin = commonTaxFeeBreakdown.taxOverMargin,
      taxFeeOverMarginBreakdowns = commonTaxFeeBreakdown.taxOverMarginBreakdowns.map(toCommonTaxBreakdown),
      variableTaxBreakdowns = commonTaxFeeBreakdown.variableTaxBreakdowns.map(toCommonTaxBreakdown),
    )
  }

  def calculateFlatTaxAmount(chargeType: ChargeType, taxType: TaxType, taxBreakDown: List[CommonTaxBreakdown]): Double = {
    val isFlatTaxApplicable =
      chargeType == ChargeType.Room && taxType == TaxType.ComprehensiveTaxHotelLevel || taxType == TaxType.ComprehensiveTaxRoomLevel
    if (isFlatTaxApplicable) taxBreakDown.getDSum(x => x.amount * x.quantity, _.isAmount)
    else 0d
  }

  def calculateB2CTaxes(amount: Double,
                        financialBreakdownTypeId: Int,
                        bookingDate: DateTime,
                        determinedCountryCodeOpt: Option[String],
                        bookingCountryIndicatorOpt: Option[YPLBookingCountryIndicator],
                        isCleanedUpHospitalityTax: Boolean): Option[YPLESSFinancialBreakdown] = {
    val taxResult: Option[(Option[ESSfinancialBreakdown], Option[String])] =
      (bookingCountryIndicatorOpt, determinedCountryCodeOpt) match {
        case (_, Some(determinedCountryCode)) => Some(
            (
              Some(
                B2CTaxCalculator.calculateB2CTax(
                  productId = financialBreakdownTypeId,
                  amount = amount,
                  isTaxInclusive = true,
                  bookerCountryCode = determinedCountryCode,
                  calDate = bookingDate,
                  isCleanedUpHospitalityTax = isCleanedUpHospitalityTax,
                )),
              Some(determinedCountryCode),
            ),
          )
        case (Some(bookingCountryIndicator), None) =>
          val result = B2CTaxCalculator.calculateB2CTaxWithCountryDetermination(
            productId = financialBreakdownTypeId,
            amount = amount,
            isTaxInclusive = true,
            bookingCountryIndicator = toCommonTaxBookingCountryIndicator(bookingCountryIndicator),
            calDate = bookingDate,
            serviceOrigin = bookingCountryIndicator.serviceTaxCountry,
            isCleanedUpHospitalityTax = isCleanedUpHospitalityTax,
          )

          Some((result.ESSfinancialBreakdown, result.countryCode))
        case (_, _) => None
      }

    toYPLESSFinancialBreakdown(taxResult)
  }
}
