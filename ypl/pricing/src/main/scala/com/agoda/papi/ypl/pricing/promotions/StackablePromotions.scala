package com.agoda.papi.ypl.pricing.promotions

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.enums.request.StackDiscountOption
import com.agoda.papi.ypl.models.pricing.proto.PromotionEntry
import com.agoda.papi.ypl.models.{
  PromotionId,
  PromotionTypeId,
  PulsePromoDiscount,
  YplContext,
  YplExperiments,
  YplRequest,
  YplRoomEntry,
}
import com.agoda.papi.ypl.models.consts.MegaSalePromotions
import com.agoda.papi.ypl.pricing.PulsePromotionsHelper
import org.joda.time.DateTime

trait StackablePromotions extends PromoRoomGeneration with PromoValidation {

  /**
    * Apply stackable promotions to multiple rooms
    *
    * @return List of YplRoomEntry after apply all stackable promotion
    */
  private[promotions] def generateRoomsWithStackablePromos(
    promotions: List[PromotionEntry],
    rooms: List[YplRoomEntry],
    localBookingDate: DateTime,
    applyDiscountsMultiplicatively: Boolean,
    promotionTypeIdToPulseCampaignMetadata: Map[PromotionTypeId, PulseCampaignMetadata],
    useBaseCxlPolicyForPromotionalRooms: Boolean = false,
    isMegaCampaignEnabled: Boolean = false)(implicit ctx: YplContext, request: YplRequest): List[YplRoomEntry] =
    promotions.filter(_.isStackable) match {
      case Nil => List.empty
      case stackablePromos => rooms.flatMap { room =>
          val isAllowStack = room.promotion.forall(
            _.isAllowCombine &&
            room.promotionsBreakdown.foldLeft(false) { case (allowed, (_, promos)) =>
              allowed || promos.exists(_.isAllowStack)
            })
          if (isAllowStack) applyStackablePromotions(
            room,
            stackablePromos,
            localBookingDate,
            applyDiscountsMultiplicatively,
            promotionTypeIdToPulseCampaignMetadata,
            useBaseCxlPolicyForPromotionalRooms,
            isMegaCampaignEnabled,
          )
          else None
        }
    }

  /**
    * Apply all stackable promotions
    *
    * @param room       room with normal promotion
    * @param promotions all stackable promotion
    * @return room after apply stack promotions
    */
  private[promotions] def applyStackablePromotions(
    room: YplRoomEntry,
    promotions: List[PromotionEntry],
    bookingDate: DateTime,
    applyDiscountsMultiplicatively: Boolean,
    promotionTypeIdToPulseCampaignMetadata: Map[PromotionTypeId, PulseCampaignMetadata],
    useBaseCxlPolicyForPromotionalRooms: Boolean = false,
    isMegaCampaignEnabled: Boolean = false)(implicit request: YplRequest, ctx: YplContext): Option[YplRoomEntry] = {

    // Step 1: Find list of promotion for each night
    val applyDatesMap = room.dailyPrices.map { case (date, price) =>
      date -> promotions.collect {
        case promo: PromotionEntry if isApplicableDate(price, promo, bookingDate, request.checkIn) => promo
      }
    }

    // Step 2: Find StackDiscountOption for each night
    val discountOptionMap = applyDatesMap.map { case (date, promos) =>
      if (promos.isEmpty) date -> None
      else if (promos.exists(p => p.stackDiscountOption == Option(StackDiscountOption.Multiplicative)))
        date -> Option(StackDiscountOption.Multiplicative)
      else date -> Option(StackDiscountOption.Additive)
    }

    // Step 3: Find the best pulse promotion for each night
    val pulsePromotionTypeIdeCampaignMetadata =
      // Filter mega sale campaign data out when isMegaCampaignEnabled is true
      if (isMegaCampaignEnabled) {
        promotionTypeIdToPulseCampaignMetadata.filterNot { case (_, campaignMetadata) =>
          MegaSalePromotions.CampaignTypeIds.contains(campaignMetadata.campaignTypeId)
        }
      } else {
        promotionTypeIdToPulseCampaignMetadata
      }
    val (dateTimeToMaybeBestPulsePromotionId, bestPulseMetadata) =
      findPulseBestPromo(room, applyDatesMap, pulsePromotionTypeIdeCampaignMetadata)

    // Step 4: Find the best mega sale promotion for each night
    val (
      dateTimeToMaybeBestMegaPromotionId,
      bestMegaMetadata,
    ) = getBestMegaPromotionData(room, applyDatesMap, promotionTypeIdToPulseCampaignMetadata, isMegaCampaignEnabled)

    // Step 5: Apply promotions to price breakdowns for each night
    val finalDiscountRoom = promotions.foldLeft(room) { (finalDiscountRoom, promo) =>
      createPromotionalRoom(
        finalDiscountRoom,
        promo,
        bookingDate,
        discountOptionMap,
        applyDiscountsMultiplicatively,
        promotionTypeIdToPulseCampaignMetadata,
        dateTimeToMaybeBestPulsePromotionId,
        dateTimeToMaybeBestMegaPromotionId,
        useBaseCxlPolicyForPromotionalRooms,
        isMegaCampaignEnabled,
      ).getOrElse(room)
    }

    // Step 6: Find the final pulse metadata to display a badge on front-end side
    // Check if at least one stackable promotion has been applied and update pulseCampaignMetadata if necessary
    if (isMegaCampaignEnabled) {
      // For B variant with mega Sale campaigns
      PulsePromotionsHelper.findTheFinalPulseMetadata(room, finalDiscountRoom, bestMegaMetadata, bestPulseMetadata)
    } else {
      // For A variant without Mega Sale campaigns
      Option(finalDiscountRoom).withFilter(_ != room).map { updatedRoom =>
        if (updatedRoom.pulseCampaignMetadata.nonEmpty) {
          updatedRoom.copy(pulseCampaignMetadata = bestPulseMetadata)
        } else {
          updatedRoom
        }
      }
    }
  }

  private[promotions] def getBestMegaPromotionData(
    room: YplRoomEntry,
    applyDatesMap: Map[DateTime, List[PromotionEntry]],
    promotionTypeIdToPulseCampaignMetadata: Map[Int, PulseCampaignMetadata],
    isMegaCampaignEnabled: Boolean)(implicit
    ctx: YplContext): (Map[DateTime, Option[PromotionId]], Option[PulseCampaignMetadata]) =
    // Generate the best mega sale promotion metadata by date
    if (isMegaCampaignEnabled) {
      // Filter only mega sale campaign data
      val megaSalePromotionTypeIdToPulseCampaignMetadata = promotionTypeIdToPulseCampaignMetadata.filter {
        case (_, campaignMetadata) => MegaSalePromotions.CampaignTypeIds.contains(campaignMetadata.campaignTypeId)
      }
      // Find the best mega sale promotion metadata by date
      findPulseBestPromo(
        room,
        applyDatesMap,
        megaSalePromotionTypeIdToPulseCampaignMetadata,
        isMegaSaleType = true,
      )
      // Define default values when isMegaCampaignEnabled is false
    } else {
      (Map.empty[DateTime, Option[PromotionId]], None)
    }

  private[promotions] def findPulseBestPromo(
    room: YplRoomEntry,
    applyDatesMap: Map[DateTime, List[PromotionEntry]],
    promotionTypeIdToPulseCampaignMetadata: Map[PromotionTypeId, PulseCampaignMetadata],
    isMegaSaleType: Boolean = false)(implicit
    ctx: YplContext): (Map[DateTime, Option[PromotionId]], Option[PulseCampaignMetadata]) = {

    // Build pulse promotion discount by promotion id
    def buildPulsePromoDiscountByPromotionId(promotions: List[PromotionEntry],
                                             date: DateTime): Option[(Int, PulsePromoDiscount)] = promotions
      .filter(p => promotionTypeIdToPulseCampaignMetadata.contains(p.typeId))
      .sortBy(_.typeId)
      .foldRight[Option[PromotionEntry]](None) { (pulsePromo, selectedPulsePromo) =>
        val selectedDiscount = selectedPulsePromo.map(_.getDiscount(date)).getOrElse(-1.0)
        if (pulsePromo.getDiscount(date) > selectedDiscount) Some(pulsePromo)
        else selectedPulsePromo
      }
      .map { promo =>
        promo.id -> PulsePromoDiscount(promo.typeId, promo.getDiscount(date))
      }

    // Step 1: Generate dateTimeToMaybeBestPulsePromotionId to build date to best promotion id

    // Step 1.1: Build the best pulse promo discount metadata by date
    val dateToBestPromotionIdAndPulsePromoDiscount: Map[DateTime, Option[(Int, PulsePromoDiscount)]] =
      applyDatesMap.map { case (date, promos) =>
        date -> {
          buildPulsePromoDiscountByPromotionId(promos, date)
        }
      }

    // Step 1.2: Map the dates and the best promotion IDs
    val dateTimeToMaybeBestPulsePromotionId: Map[DateTime, Option[Int]] =
      dateToBestPromotionIdAndPulsePromoDiscount.map { case (date, promo) => date -> promo.map(_._1) }

    // Step 2: Generate bestPulseMeta to find the best pulse promo discount metadata

    // Step 2.1: Get the experiment flag to allow display badge for extra dispatching
    val allowShowPulseBadgeForExtraDispatchReason =
      ctx.experimentContext.isUserB(YplExperiments.ALLOW_SHOW_PULSE_BADGE_FOR_EXTRA_DISPATCH_REASON)

    // Step 2.2: Build non-stackable and displayable pulse promo discount metadata
    val nonStackDisplayableMetadataPulsePromoDiscount = room.promotion.flatMap {
      case p
          if promotionTypeIdToPulseCampaignMetadata.contains(
            p.typeId) && !p.isStackable && (p.additionalDispatchReasons.isEmpty || allowShowPulseBadgeForExtraDispatchReason) =>
        Some(PulsePromoDiscount(p.typeId, room.totalPromoDiscount))
      case _ => None
    }

    // Step 3.3: Build the best stackable and displayable pulse promo discount metadata by date
    val dateToBestDisplayablePulseMetaDataPulsePromo: Map[DateTime, Option[(Int, PulsePromoDiscount)]] =
      applyDatesMap.map { case (date, promos) =>
        date -> {
          val filteredPromos =
            promos.filter(p => p.additionalDispatchReasons.isEmpty || allowShowPulseBadgeForExtraDispatchReason)
          buildPulsePromoDiscountByPromotionId(filteredPromos, date)
        }
      }

    // Step 3.4: Build stack displayable pulse promo discount metadata
    val stackDisplayableMetaDataPulsePromoDiscount =
      dateToBestDisplayablePulseMetaDataPulsePromo.map(_._2.map(_._2)).toList

    // Step 3.5: Combine non-stackable and stackable pulse promo discount metadata
    val pulsePromoDiscount: List[Option[PulsePromoDiscount]] =
      nonStackDisplayableMetadataPulsePromoDiscount :: stackDisplayableMetaDataPulsePromoDiscount

    // Step 3.6: Find the best pulse promotion metadata
    val bestPulseMeta =
      pulsePromoDiscount.max.flatMap(promo => promotionTypeIdToPulseCampaignMetadata.get(promo.promotionTypeId))

    // Output
    (dateTimeToMaybeBestPulsePromotionId, bestPulseMeta)
  }

}
