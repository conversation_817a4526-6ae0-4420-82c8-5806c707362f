package com.agoda.papi.ypl.pricing.propoffer.transformer

import com.agoda.finance.tax.enums.TaxLevelCalculationType
import com.agoda.finance.tax.models.{TaxPrototypeLevel, TaxPrototypeInfo}
import com.agoda.papi.enums.room.{ChargeOption, WhomToPayType}
import com.agoda.papi.pricing.pricecalculation.models.tax.Tax
import com.agoda.papi.ypl.models.consts.ApplyTo
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{ConnectionType, Currency, HotelMeta, StayDate, SupplierId, YplContext, YplExperiments}
import com.agoda.papi.ypl.pricing.TaxUtil
import com.agoda.papi.ypl.pricing.propoffer.transformer.tax.HMCTaxHolder
import com.agoda.papi.ypl.settings.MOHUGdsCommissionFeeSettings
import com.agoda.protobuf.masterhotelcontext
import com.agoda.supply.calc.proto
import com.agoda.supply.calc.proto.{PropertyOffer, Tax => ProtoTax, TaxV2 => ProtoTaxV2}
import org.joda.time.DateTime

import scala.collection.generic.FilterMonadic

trait TaxTransformer {

  private val SAME_ALL_DAYS_INDEX = -1

  private type TaxId = Int
  private type TaxProtoTypeId = Int
  private type TaxIdWithProtoTypeId = (TaxId, TaxProtoTypeId)
  protected type TaxPerDayLookUp = Map[DateTime, Map[TaxIdWithProtoTypeId, Tax]]

  protected def isEnableTaxV2Exp(
    hasTaxV2: Boolean,
    hotel: HotelMeta,
    supplierId: SupplierId,
  )(implicit ctx: YplContext): Boolean

  protected def calculateLevelByCurrency(
    hotelCurrencyCode: String,
    countryCurrencyCode: String,
    taxLevelInCountryCurrency: List[TaxPrototypeLevel],
    isAmount: Boolean,
  )(implicit ctx: YplContext): List[TaxPrototypeLevel]

  protected def convertCurrency(
    amount: Double,
    from: Currency,
    to: Currency,
  )(implicit ctx: YplContext): Double

  protected def enableHMCTaxExp(
    supplierId: SupplierId,
    connectionType: ConnectionType,
  )(implicit ctx: YplContext): Boolean = {
    val isHmcEnabled = ctx.experimentContext.isUserB(YplExperiments.ENABLE_HMC_FETCH)
    val isUseTaxesFromHmc = ctx.experimentContext.isUserB(YplExperiments.USE_TAXES_FROM_HMC)
    val isUseTaxesFromHmcForSuperAgg = ctx.experimentContext.isUserB(YplExperiments.USE_TAXES_FROM_HMC_SUPERAGG)
    val isBcom = supplierId == DMC.BCOM

    isHmcEnabled &&
    isUseTaxesFromHmc &&
    !isBcom &&
    (!connectionType.isThirdParty || (connectionType.isThirdParty && isUseTaxesFromHmcForSuperAgg))
  }

  protected[pricing] def buildDailyTaxEntry(
    checkIn: DateTime,
    lengthOfStay: Int,
    hotelMeta: HotelMeta,
    po: PropertyOffer,
    roomRateCategory: proto.RoomRateCategory,
    channelRate: proto.ChannelRate,
    hotelCurrency: Option[Currency],
    countryCurrency: Option[Currency],
    applicableTaxes: Map[Int, ProtoTax],
    applicableTaxesV2: Map[Int, ProtoTaxV2],
    mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings,
    hmcTaxHolder: HMCTaxHolder = HMCTaxHolder.default,
  )(implicit ctx: YplContext): TaxPerDayLookUp = {
    val supplierId = po.supplyInfo.map(_.supplierId).getOrElse(0)
    val isBcomFixTaxAmountApplyToPB = supplierId == DMC.BCOM
    val isPull = po.supplyInfo.exists(_.supplySource.isPull)
    val connectionType = ctx.connectionType(supplierId)

    val isHMCExpOn = enableHMCTaxExp(supplierId, connectionType)

    // Piggyback on this flag V2 for HMC poc as well
    val isNewChargeTypeTaxAppliedExp =
      isEnableTaxV2Exp(applicableTaxesV2.nonEmpty && channelRate.taxPerDayV2.nonEmpty, hotelMeta, supplierId)

    if (isNewChargeTypeTaxAppliedExp) {
      val isSameTaxV2ForAllStayDate =
        channelRate.taxPerDayV2.contains(SAME_ALL_DAYS_INDEX) && channelRate.taxPerDayV2.size == 1
      val allDayTaxesV2 = buildTaxV2FromPo(
        SAME_ALL_DAYS_INDEX,
        channelRate.taxPerDayV2,
        po.taxPrototypeLevelsV2,
        applicableTaxesV2,
        isBcomFixTaxAmountApplyToPB,
        hotelCurrency,
        countryCurrency,
        mohuGdsCommissionFeeSettings,
      )
      (0 until lengthOfStay).map { index =>
        val stayDate = checkIn.plusDays(index)
        val taxEntry: Map[(TaxID, TaxProtoTypeID), Tax] =
          if (isHMCExpOn) {
            buildTaxV2FromHmc(
              checkIn,
              stayDate,
              hotelMeta,
              supplierId,
              isPull,
              roomRateCategory,
              hmcTaxHolder,
              hotelCurrency,
              countryCurrency,
              mohuGdsCommissionFeeSettings,
            )
          } else if (isSameTaxV2ForAllStayDate) {
            allDayTaxesV2
          } else {
            buildTaxV2FromPo(
              index,
              channelRate.taxPerDayV2,
              po.taxPrototypeLevelsV2,
              applicableTaxesV2,
              isBcomFixTaxAmountApplyToPB,
              hotelCurrency,
              countryCurrency,
              mohuGdsCommissionFeeSettings,
            )
          }
        stayDate -> taxEntry
      }(collection.breakOut)
    } else {
      val isSameTaxV1ForAllStayDate =
        channelRate.taxPerDay.contains(SAME_ALL_DAYS_INDEX) && channelRate.taxPerDay.size == 1
      val allDayTaxesV1 = buildTaxV1FromPo(
        SAME_ALL_DAYS_INDEX,
        channelRate.taxPerDay,
        po.taxPrototypeLevels,
        applicableTaxes,
        isBcomFixTaxAmountApplyToPB,
        hotelCurrency,
        countryCurrency,
        mohuGdsCommissionFeeSettings,
      )
      (0 until lengthOfStay).map { index =>
        val stayDate = checkIn.plusDays(index)
        val taxEntry: Map[(TaxID, TaxProtoTypeID), Tax] =
          if (isHMCExpOn) {
            buildTaxV1FromHmc(
              checkIn,
              stayDate,
              hotelMeta,
              supplierId,
              isPull,
              roomRateCategory,
              hmcTaxHolder,
              hotelCurrency,
              countryCurrency,
              mohuGdsCommissionFeeSettings,
            )
          } else if (isSameTaxV1ForAllStayDate) {
            allDayTaxesV1
          } else {
            buildTaxV1FromPo(
              index,
              channelRate.taxPerDay,
              po.taxPrototypeLevels,
              applicableTaxes,
              isBcomFixTaxAmountApplyToPB,
              hotelCurrency,
              countryCurrency,
              mohuGdsCommissionFeeSettings,
            )
          }
        stayDate -> taxEntry
      }(collection.breakOut)
    }
  }

  private def buildTaxV1FromPo(
    index: Int,
    taxPerDay: Map[Int, proto.Identifiers],
    taxPrototypeLevels: Map[TaxProtoTypeId, proto.TaxPrototypeLevels],
    applicableTaxes: Map[Int, proto.Tax],
    isBcomFixTaxAmountApplyToPB: Boolean,
    hotelCurrency: Option[String],
    countryCurrency: Option[String],
    mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings,
  )(implicit ctx: YplContext): Map[TaxIdWithProtoTypeId, Tax] = {
    val taxes = getTaxesFromPo(index, taxPerDay, applicableTaxes)
    taxes.map { tax =>
      buildTax(
        tax,
        taxPrototypeLevels
          .get(tax.taxPrototypeId)
          .map(
            getTaxPrototypeInfo[proto.TaxPrototypeLevels](
              _,
              hotelCurrency = hotelCurrency,
              countryCurrency = countryCurrency,
              tax.isAmount,
              covertTaxPrototypeLevelV1,
            )),
        isBcomFixTaxAmountApplyToPB,
        countryCurrency,
        hotelCurrency,
        mohuGdsCommissionFeeSettings,
      )
    }(collection.breakOut)
  }

  private def buildTaxV2FromPo(
    index: Int,
    taxPerDayV2: Map[Int, proto.Identifiers],
    taxPrototypeLevelsV2: Map[TaxProtoTypeId, proto.TaxPrototypeLevelsV2],
    applicableTaxesV2: Map[Int, proto.TaxV2],
    isBcomFixTaxAmountApplyToPB: Boolean,
    hotelCurrency: Option[String],
    countryCurrency: Option[String],
    mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings,
  )(implicit ctx: YplContext): Map[TaxIdWithProtoTypeId, Tax] = {
    val taxes = getTaxesFromPo(index, taxPerDayV2, applicableTaxesV2)
    taxes.map { taxV2 =>
      buildTaxV2(
        taxV2,
        taxPrototypeLevelsV2
          .get(taxV2.taxPrototypeId)
          .map(
            getTaxPrototypeInfo[proto.TaxPrototypeLevelsV2](
              _,
              hotelCurrency = hotelCurrency,
              countryCurrency = countryCurrency,
              taxV2.isAmount,
              covertTaxPrototypeLevelV2,
            )),
        isBcomFixTaxAmountApplyToPB,
        hotelCurrency,
        mohuGdsCommissionFeeSettings,
      )
    }(collection.breakOut)
  }

  private def getTaxesFromPo[T](
    index: Int,
    taxPerDay: Map[Int, proto.Identifiers],
    applicableTaxes: Map[Int, T],
  ): Seq[T] = taxPerDay
    .get(index)
    .map(identifier => identifier.identifiers.flatMap(id => applicableTaxes.get(id)))
    .getOrElse(Seq.empty)

  private def buildTaxV1FromHmc(
    checkIn: DateTime,
    stayDate: DateTime,
    hotelMeta: HotelMeta,
    supplierId: SupplierId,
    isPull: Boolean,
    roomRateCategory: proto.RoomRateCategory,
    hmcTaxHolder: HMCTaxHolder,
    hotelCurrency: Option[String],
    countryCurrency: Option[String],
    mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings,
  )(implicit ctx: YplContext): Map[TaxIdWithProtoTypeId, Tax] = {
    val hmcTaxes = getTaxesFromHmc(
      checkIn,
      ctx.request.checkOut,
      stayDate,
      hotelMeta,
      supplierId,
      isPull,
      roomRateCategory,
      hotelCurrency,
      hmcTaxHolder,
      isUsingTaxV2 = false,
    )
    hmcTaxes.map { hmcTax =>
      val taxV1 = proto.Tax(
        taxId = hmcTax.taxId,
        applyTo = hmcTax.applyTo,
        applyType = hmcTax.applyType,
        isFee = hmcTax.isFee,
        isAmount = hmcTax.isAmount,
        value = hmcTax.value,
        isTaxable = hmcTax.isTaxable,
        taxPrototypeId = hmcTax.taxPrototypeId,
        taxApplyOnId = hmcTax.taxApplyOnId,
      )
      val taxPrototypeInfo =
        if (hmcTax.taxPrototypeId != 0) {
          Some(
            getTaxPrototypeInfo[proto.TaxPrototypeLevelsV2](
              proto.TaxPrototypeLevelsV2(taxProtoTypeId = hmcTax.taxPrototypeId, levels = hmcTax.taxPrototypeLevels),
              hotelCurrency = hotelCurrency,
              countryCurrency = countryCurrency,
              hmcTax.isAmount,
              covertTaxPrototypeLevelV2,
            ))
        } else {
          None
        }
      buildTax(
        taxV1,
        taxPrototypeInfo,
        isBcom = supplierId == DMC.BCOM,
        countryCurrency,
        hotelCurrency,
        mohuGdsCommissionFeeSettings,
      )
    }(collection.breakOut)
  }

  private def buildTaxV2FromHmc(
    checkIn: DateTime,
    stayDate: DateTime,
    hotelMeta: HotelMeta,
    supplierId: SupplierId,
    isPull: Boolean,
    roomRateCategory: proto.RoomRateCategory,
    hmcTaxHolder: HMCTaxHolder,
    hotelCurrency: Option[String],
    countryCurrency: Option[String],
    mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings,
  )(implicit ctx: YplContext): Map[TaxIdWithProtoTypeId, Tax] = {
    val hmcTaxes = getTaxesFromHmc(
      checkIn,
      ctx.request.checkOut,
      stayDate,
      hotelMeta,
      supplierId,
      isPull,
      roomRateCategory,
      hotelCurrency,
      hmcTaxHolder,
      isUsingTaxV2 = true,
    )
    hmcTaxes.map { hmcTax =>
      val taxV2 = proto.TaxV2(
        taxId = hmcTax.taxId,
        applyTo = hmcTax.applyTo,
        applyType = hmcTax.applyType,
        isFee = hmcTax.isFee,
        isAmount = hmcTax.isAmount,
        value = hmcTax.value,
        isTaxable = hmcTax.isTaxable,
        taxPrototypeId = hmcTax.taxPrototypeId,
        whomToPay = hmcTax.whomToPay,
        orderNumber = hmcTax.orderNumber,
        taxLevelCalculationType = hmcTax.taxLevelCalculationType,
        taxApplyBreakdownType = hmcTax.taxApplyBreakdownType,
        valueMethod = hmcTax.valueMethod,
        valueCalculationMethodType = hmcTax.valueCalculationMethodType,
        geoId = hmcTax.geoId,
        geoType = hmcTax.geoType,
        taxCurrency = hmcTax.taxCurrency,
        taxApplyOnId = hmcTax.taxApplyOnId,
      )
      val taxPrototypeInfo =
        if (hmcTax.taxPrototypeId != 0) {
          Some(
            getTaxPrototypeInfo[proto.TaxPrototypeLevelsV2](
              proto.TaxPrototypeLevelsV2(taxProtoTypeId = hmcTax.taxPrototypeId, levels = hmcTax.taxPrototypeLevels),
              hotelCurrency = hotelCurrency,
              countryCurrency = countryCurrency,
              hmcTax.isAmount,
              covertTaxPrototypeLevelV2,
            ))
        } else {
          None
        }
      buildTaxV2(
        taxV2,
        taxPrototypeInfo,
        isBcom = supplierId == DMC.BCOM,
        hotelCurrency,
        mohuGdsCommissionFeeSettings,
      )
    }(collection.breakOut)
  }

  private def getTaxesFromHmc(
    checkIn: DateTime,
    checkOut: DateTime,
    stayDate: StayDate,
    hotelMeta: HotelMeta,
    supplierId: SupplierId,
    isPull: Boolean,
    roomRateCategory: proto.RoomRateCategory,
    hotelCurrency: Option[Currency],
    taxHolder: HMCTaxHolder,
    isUsingTaxV2: Boolean,
  )(implicit ctx: YplContext): FilterMonadic[masterhotelcontext.poc.HmcTax, Seq[masterhotelcontext.poc.HmcTax]] =
    taxHolder
      .getTaxes(
        checkIn = checkIn,
        checkOut = checkOut,
        stayDate = stayDate,
        roomTypeId = roomRateCategory.roomTypeId,
        rateCategoryId = roomRateCategory.rateCategoryId,
        supplierId = supplierId,
        isPull = isPull,
        rateCurrency = roomRateCategory.currencyCode,
        hotelCurrency = hotelCurrency,
        isUsingTaxV2 = isUsingTaxV2,
        hotelMeta = hotelMeta,
      )
      .withFilter { t =>
        TaxUtil.filterSingleTaxApplicable(
          t.taxId,
          hotelMeta.countryId,
          ctx.request.cInfo.origin,
        )
      }

  protected[pricing] def covertTaxPrototypeLevelV1(
    taxPrototypeLevels: proto.TaxPrototypeLevels): List[TaxPrototypeLevel] = taxPrototypeLevels.levels
    .map(l => TaxPrototypeLevel(l.level, l.rateStart, l.rateEnd, l.taxValue, isAmount = false))
    .toList

  protected[pricing] def covertTaxPrototypeLevelV2(
    taxPrototypeLevelsV2: proto.TaxPrototypeLevelsV2): List[TaxPrototypeLevel] = taxPrototypeLevelsV2.levels
    .map(l => TaxPrototypeLevel(l.level, l.rateStart, l.rateEnd, l.taxValue, l.isAmount.getOrElse(false)))
    .toList

  protected[pricing] def getTaxPrototypeInfo[TPL](
    taxPrototypeLevels: TPL,
    hotelCurrency: Option[String],
    countryCurrency: Option[String],
    isAmount: Boolean,
    covertTaxPrototypeLevel: TPL => List[TaxPrototypeLevel],
  )(implicit ctx: YplContext): TaxPrototypeInfo = {
    val taxLevel = (hotelCurrency, countryCurrency) match {
      case (Some(hotelCurrencyCode), Some(countryCurrencyCode)) =>
        val taxLevelInCountryCurrency = covertTaxPrototypeLevel(taxPrototypeLevels)
        calculateLevelByCurrency(hotelCurrencyCode, countryCurrencyCode, taxLevelInCountryCurrency, isAmount)
      case _ =>
        // this is considered as invalid condition. then not return this tax prototype level set
        List.empty
    }
    TaxPrototypeInfo(taxLevel)
  }

  protected[pricing] def buildTax(
    tax: proto.Tax,
    taxPrototypeInfo: Option[TaxPrototypeInfo],
    isBcom: Boolean,
    countryCurrency: Option[Currency],
    hotelCurrency: Option[Currency],
    mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings,
  )(implicit ctx: YplContext): (TaxIdWithProtoTypeId, Tax) = {
    val taxId: TaxId = tax.taxId
    val taxProtoTypeId: TaxProtoTypeId = tax.taxPrototypeId

    val applyTo =
      if (isBcom && tax.isAmount && tax.applyTo == ApplyTo.PerBook) {
        ApplyTo.PerRoomPerBook
      } else {
        tax.applyTo
      }

    val taxValue =
      if (tax.isAmount && tax.applyType.isVariableTax) {
        convertCurrency(amount = tax.value, from = countryCurrency.getOrElse(""), to = hotelCurrency.getOrElse(""))
      } else {
        tax.value
      }

    val taxData = Tax(
      id = taxId,
      applyTo = applyTo,
      isAmount = tax.isAmount,
      isFee = tax.isFee,
      isTaxable = tax.isTaxable,
      value = taxValue,
      option = ChargeOption.getFromValue(tax.applyType.value),
      protoTypeId = taxProtoTypeId,
      taxPrototypeInfo = taxPrototypeInfo,
      applyOver = None,
      applyOn = Some(tax.taxApplyOnId),
    )
    ((taxId, taxProtoTypeId), taxData)
  }

  protected[pricing] def buildTaxV2(
    taxV2: proto.TaxV2,
    taxPrototypeInfo: Option[TaxPrototypeInfo],
    isBcom: Boolean,
    hotelCurrency: Option[Currency],
    mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings,
  )(implicit ctx: YplContext): (TaxIdWithProtoTypeId, Tax) = {
    val taxId: TaxId = taxV2.taxId
    val taxProtoTypeId: TaxProtoTypeId = taxV2.taxPrototypeId

    val applyTo =
      if (isBcom && taxV2.isAmount && taxV2.applyTo == ApplyTo.PerBook) {
        ApplyTo.PerRoomPerBook
      } else {
        taxV2.applyTo
      }

    val taxValue =
      if (taxV2.isAmount && taxV2.taxCurrency.exists(_.nonEmpty) && taxV2.applyType.isVariableTax) {
        convertCurrency(amount = taxV2.value, from = taxV2.taxCurrency.getOrElse(""), to = hotelCurrency.getOrElse(""))
      } else {
        taxV2.value
      }

    val taxData = Tax(
      id = taxId,
      applyTo = applyTo,
      isAmount = taxV2.isAmount,
      isFee = taxV2.isFee,
      isTaxable = taxV2.isTaxable,
      value = taxValue,
      option = ChargeOption.getFromValue(taxV2.applyType.value),
      protoTypeId = taxProtoTypeId,
      taxPrototypeInfo = taxPrototypeInfo,
      applyOver = None,
      applyOn = Some(taxV2.taxApplyOnId),
      applyBreakdownType = Some(taxV2.taxApplyBreakdownType),
      whomToPay = taxV2.whomToPay.map(t => WhomToPayType.getWhomToPayType(t)),
      orderNumber = taxV2.orderNumber,
      taxLevelCalculationType = Some(TaxLevelCalculationType.getFromValue(taxV2.taxLevelCalculationType.value)),
      valueMethod = Some(com.agoda.papi.enums.room.ValueMethodType.getValueMethodType(taxV2.valueMethod.value)),
      valueCalculationMethodType = Some(
        com.agoda.papi.enums.room.ValueCalculationMethodType
          .getValueCalculationMethodType(taxV2.valueCalculationMethodType.value)),
      geoId = taxV2.geoId,
      geoType = Some(com.agoda.papi.enums.room.GeoType.getGeoType(taxV2.geoType.value)),
    )
    ((taxId, taxProtoTypeId), taxData)
  }

}
