package com.agoda.papi.ypl.pricing

import com.agoda.papi.enums.hotel.PaymentOption
import com.agoda.papi.enums.room.RatePlanStatus
import com.agoda.papi.ypl.models.GUIDGenerator.RoomIdentifiers
import com.agoda.papi.ypl.models.consts.Measurements
import com.agoda.papi.ypl.models.{
  RoomTypeId,
  YPLCheapestHotelAggregation,
  YPLRoom,
  YplCancellation,
  YplContext,
  YplHotelEntryModel,
  YplHotelWithEntry,
  YplRoomEntry,
}
import com.typesafe.scalalogging.LazyLogging

import scala.annotation.tailrec
import scala.collection.mutable.ListBuffer
import com.agoda.papi.ypl.logging.{DFOfferFilterTrackingMessage, MessageSink, NewOccupancyLogicMeasurement}
import com.agoda.papi.ypl.pricing.LimitRoomsHeuristicService._
import com.agoda.papi.ypl.utils.Implicits.DistinctBy
import com.agoda.papi.ypl.utils.YplImplicits._
import models.consts.ABTest

trait LimitRoomsHeuristicService {
  def enhancedHeuristic[T, K](rooms: List[T], limit: Int, groupingMethod: T => K, minBy: T => Double): (List[T], List[T])

  /**
    * Known as H1 (Heuristic 1) filter by top number per supplier for all
    */
  def limitRoomsHeuristic(hOpt: Option[YplHotelEntryModel])(implicit ctx: YplContext): Option[YplHotelEntryModel]

  /**
    * Known as H4 (Heuristic 4) filter by top number per supplier for SSR(isCheapestOnly)
    */
  def limitCheapestRoomHeuristic(d: Option[YplHotelWithEntry])(implicit ctx: YplContext): Option[YplHotelWithEntry]

  def logHeuristicRoomTracking(rooms: List[YplRoomEntry],
                               reason: String,
                               hotelId: Int,
                               supplierId: Int,
                               ctx: YplContext): Unit
}

class HeuristicOrdering(guestsPerRoom: Int) extends Ordering[YPLRoom] {
  override def compare(x: YPLRoom, y: YPLRoom): Int = {
    val capacityCompare = (x.guests < guestsPerRoom).compareTo(y.guests < guestsPerRoom)

    if (capacityCompare != 0) {
      capacityCompare
    } else {
      val sellAllInCompare = x.sellAllIn.compareTo(y.sellAllIn)
      if (sellAllInCompare != 0) {
        sellAllInCompare
      } else {
        x.sellEx.compareTo(y.sellEx)
      }
    }

  }
}

/**
  * This is not a business requirement service, but a pure performance improvement for hotels with a huge amount of rooms
  */
trait LimitRoomsHeuristicServiceImpl
  extends LimitRoomsHeuristicService
    with LazyLogging
    with NewOccupancyLogicMeasurement {

  protected val messageSink: MessageSink

  private val minAddingRoomSize = 2

  /**
    * Append new room or replace the last room,
    * ensure [[candidateRoom]] room is not in the list,
    *
    * @param rooms this rooms list MUST be sorted by price ASC
    * @param candidateRoomOpt new room will be added to the list, if it haven't existed
    * @param minRoomSize if room size it less than this number [[candidateRoom]] will be adding,
    *                    if does not set, will always append ;otherwise replacing
    */
  private[pricing] def appendOrReplaceIfDoesNotExist(
    rooms: List[YPLRoom],
    candidateRoomOpt: Option[YPLRoom],
    minRoomSize: Int = Int.MaxValue,
    isDuplicateRoomFix: Boolean = false): (List[YPLRoom], List[YPLRoom]) =
    if (candidateRoomOpt.isDefined) {
      val candidateRoom = candidateRoomOpt.get
      val uids = rooms.map(_.uid).toSet
      val roomSize = rooms.size
      if (uids.contains(candidateRoom.uid)) {
        (rooms, List.empty)
      } else {
        val validMinRoomSize = math.max(1, minRoomSize)
        // keep appending when there are only 2 rooms or less
        val (originalList, droppedRooms) =
          if (roomSize <= validMinRoomSize) {
            (rooms, List.empty)
          } else {
            // Replace last rooms
            rooms.splitAt(rooms.size - 1)
          }

        val resultList =
          if (isEscapePackageSupported(candidateRoom) && isDuplicateRoomFix) originalList
          else originalList :+ candidateRoom

        (resultList, droppedRooms)
      }
    } else {
      (rooms, List.empty)
    }

  private[pricing] def isMatch(room: YplRoomEntry, roomIdentifier: RoomIdentifiers): Boolean =
    room.roomTypeId == roomIdentifier.coreFields.roomId &&
    room.paymentModel.map(_.i).getOrElse(0) == roomIdentifier.coreFields.paymentModel &&
    room.promotion.map(_.id).getOrElse(0) == roomIdentifier.coreFields.promotionId &&
    room.channel.compositeChannelId == roomIdentifier.coreFields.channelId &&
    room.rateCategoryId == roomIdentifier.coreFields.rateCategoryId &&
    room.occEntry.maxOcc == roomIdentifier.coreFields.occupancy &&
    room.occEntry.extraBeds == roomIdentifier.coreFields.extrabed &&
    room.isBreakFastIncluded == roomIdentifier.coreFields.breakfast &&
    room.cxlCode == roomIdentifier.coreFields.cxlCode

  private[pricing] def isMatch(room1: YplRoomEntry, room2: YplRoomEntry): Boolean =
    room1.roomTypeId == room2.roomTypeId &&
    room1.paymentModel.map(_.i).getOrElse(0) == room2.paymentModel.map(_.i).getOrElse(0) &&
    room1.promotion.map(_.id).getOrElse(0) == room2.promotion.map(_.id).getOrElse(0) &&
    room1.channel.compositeChannelId == room2.channel.compositeChannelId &&
    room1.rateCategoryId == room2.rateCategoryId &&
    room1.occEntry.maxOcc == room2.occEntry.maxOcc &&
    room1.occEntry.extraBeds == room2.occEntry.extraBeds &&
    room1.isBreakFastIncluded == room2.isBreakFastIncluded &&
    room1.cxlCode == room2.cxlCode

  private[pricing] def addRoomIfDoesNotExist(
    rooms: List[YplRoomEntry],
    candidateRoomOpt: Option[YplRoomEntry],
    roomsLimit: Int): (List[YplRoomEntry], List[YplRoomEntry]) = candidateRoomOpt
    .map { candidateRoom =>
      if (rooms.exists(isMatch(_, candidateRoom))) {
        (rooms, List.empty)
      } else {
        val roomSize = rooms.size
        val (originalList, droppedRooms) =
          if (roomSize < roomsLimit) {
            (rooms, List.empty)
          } else {
            // Replace last rooms
            rooms.splitAt(rooms.size - 1)
          }
        (originalList :+ candidateRoom, droppedRooms)
      }
    }
    .getOrElse((rooms, List.empty))

  private[pricing] def buildHotelAggregation(rooms: List[YPLRoom], isFixDuplicateBenefitExp: Boolean) = {
    val creditCard = rooms.forall(!_.paymentOptions.contains(PaymentOption.NoCreditCard))
    val prePayment = rooms.forall(_.paymentOptions.contains(PaymentOption.PrepaymentRequired))
    val channelIds = rooms.map(_.channel.compositeChannelId).toSet
    val potentiallyDuplicatedBenefits = rooms.flatMap(_.rateCategory.benefits)
    val benefits =
      if (isFixDuplicateBenefitExp) potentiallyDuplicatedBenefits.distinctBy(_.id).toSet
      else potentiallyDuplicatedBenefits.toSet
    val cxlCodeByPaymentModelMap: Map[Int, List[YplCancellation]] = rooms
      .groupBy(_.paymentModel)
      .map { case (k, v) => k.i -> v.map(r => YplCancellation(r.cxlCode, r.cxlChargeSetting, r.dmcData)) }(
        collection.breakOut)
    YPLCheapestHotelAggregation(benefits, cxlCodeByPaymentModelMap, channelIds, creditCard, prePayment)
  }

  def limitCheapestRoomHeuristic(d: Option[YplHotelWithEntry])(implicit ctx: YplContext): Option[YplHotelWithEntry] = {
    val partitionByIsKeptForFlexibleMultiRoomHotelWithEntryOpt = d.map(hotelEntry =>
      hotelEntry.hotel.rooms.partition(_.roomFeatures.isKeptForFlexibleMultiRoom) match {
        case (ignoreCheapestRoomOnlyRooms, normalRooms) => (
            hotelEntry.copy(hotel = hotelEntry.hotel.copy(rooms = ignoreCheapestRoomOnlyRooms)),
            hotelEntry.copy(hotel = hotelEntry.hotel.copy(rooms = normalRooms)),
          )
      })
    val ignoreCheapestRoomOnlyRoomsHotelEntryOpt =
      processLimitCheapestRoomHeuristic(partitionByIsKeptForFlexibleMultiRoomHotelWithEntryOpt.map(_._1))
    val normalRoomsHotelEntryOpt =
      processLimitCheapestRoomHeuristic(partitionByIsKeptForFlexibleMultiRoomHotelWithEntryOpt.map(_._2))
    normalRoomsHotelEntryOpt.map(hotelEntry =>
      hotelEntry.copy(hotel = hotelEntry.hotel.copy(rooms =
        hotelEntry.hotel.rooms ++ ignoreCheapestRoomOnlyRoomsHotelEntryOpt.map(_.hotel.rooms).toList.flatten)))
  }

  private def processLimitCheapestRoomHeuristic(d: Option[YplHotelWithEntry])(implicit
    ctx: YplContext): Option[YplHotelWithEntry] = {
    val request = ctx.request
    val cid = ctx.request.cInfo.cid.toString
    d.map { hotelWithEntry =>
      val yplHotel = hotelWithEntry.hotel
      val hotelEntryModel = hotelWithEntry.entry
      val limit: Int = ctx.request.heuristicRequest.flatMap(_.limitCheapestRooms).getOrElse(Int.MaxValue)
      val heuristicOrdering = new HeuristicOrdering(yplHotel.reqOcc.guestsPerRoom(withInfants = true))
      val originalRoomsSize = yplHotel.rooms.size
      // Do not need to allocate experiment if room size is less than limit
      val isAllowHeuristic = (originalRoomsSize > limit) &&
        !request.hasSortingStrategy && !request.flagInfo.isEnableSupplierFinancialInfo &&
        ctx.request.isCheapestRoomOnly && !request.flagInfo.filterAPO
      val isDuplicateRoomFix = ctx.experimentContext.isPropertyB(yplHotel, ABTest.FIX_DUPLICATE_ROOMS_FROM_H4)
      ctx.sendMetric(
        Measurements.cheapestRoomBeforeHeuristicCount,
        originalRoomsSize,
        Map("enabled" -> isAllowHeuristic.toString,
            Measurements.cidTag -> cid,
            Measurements.cheapestTag -> ctx.request.isCheapestRoomOnly.toString),
      )

      // Potential rooms are removed regardless experiment
      val expectedRemovedRooms =
        if (isAllowHeuristic) {
          originalRoomsSize - limit
        } else {
          0
        }
      ctx.sendMetric(
        Measurements.cheapestRoomRemoveWithHeuristicCount,
        expectedRemovedRooms,
        Map("enabled" -> isAllowHeuristic.toString,
            Measurements.cidTag -> cid,
            Measurements.cheapestTag -> ctx.request.isCheapestRoomOnly.toString),
      )

      val (rooms, hotelAggregation) =
        if (isAllowHeuristic) {

          val (roomsWithHelperStatus, roomsWithoutHelperStatus) =
            yplHotel.rooms.partition(r => r.roomStatus == RatePlanStatus.Helper)

          val (escapesRoomsToBeAppend, filteredRooms) = {
            val (escapesRooms, normalRooms) = roomsWithoutHelperStatus.partition(isEscapePackageSupported)
            (escapesRooms.sorted(heuristicOrdering), normalRooms.sorted(heuristicOrdering))
          }

          /**
            * 1) take cheapest limit rooms
            * 2) group limit rooms by room typeId
            * 3) replace highest price room in the group with most expensive rooms.
            */
          val (limitedRooms, initialRemovedRooms) = filteredRooms.splitAt(limit)
          val theMostExpensiveRoom =
            if (filteredRooms.nonEmpty) filteredRooms.lastOption else escapesRoomsToBeAppend.lastOption
          val grpLimitRooms = limitedRooms.groupBy(_.roomTypeId)
          val mostExpensiveRoomMap: Map[RoomTypeId, YPLRoom] = roomsWithoutHelperStatus
            .filter(r => grpLimitRooms.keySet.contains(r.roomTypeId))
            .groupBy(_.roomTypeId)
            .map { case (k, v) => k -> v.max(heuristicOrdering) }(collection.breakOut)

          val (candidateRooms, droppedRoomsForEachGroup) =
            grpLimitRooms.foldLeft((List.empty[YPLRoom], List.empty[YPLRoom])) {
              case ((roomsToReturnSoFar, roomsToRemoveSoFar), (roomTypeId, sameRoom)) =>
                val (roomsToReturn, roomsToRemove) = appendOrReplaceIfDoesNotExist(sameRoom.sorted(heuristicOrdering),
                                                                                   mostExpensiveRoomMap.get(roomTypeId),
                                                                                   minAddingRoomSize,
                                                                                   isDuplicateRoomFix)
                (roomsToReturn ++ roomsToReturnSoFar, roomsToRemove ++ roomsToRemove)
            }

          val (finalRooms, finalDroppedRooms) =
            appendOrReplaceIfDoesNotExist(candidateRooms, theMostExpensiveRoom, isDuplicateRoomFix = isDuplicateRoomFix)
          logYplRoomsForHeuristicTracking(
            (droppedRoomsForEachGroup ++ finalDroppedRooms ++ initialRemovedRooms),
            supplierLimitTo20Rooms,
            hotelEntryModel.hotelId,
            hotelEntryModel.supplierId,
            ctx,
          )
          (finalRooms ++ escapesRoomsToBeAppend ++ roomsWithHelperStatus,
           Some(
             buildHotelAggregation(roomsWithoutHelperStatus,
                                   ctx.experimentContext.isPropertyB(yplHotel, ABTest.FIX_DUPLICATE_BENEFITS))))
        } else {
          (yplHotel.rooms, None)
        }

      ctx.sendMetric(
        Measurements.cheapestRoomRemainWithHeuristicCount,
        rooms.size,
        Map("enabled" -> isAllowHeuristic.toString,
            Measurements.cidTag -> cid,
            Measurements.cheapestTag -> ctx.request.isCheapestRoomOnly.toString),
      )

      hotelWithEntry.copy(
        hotel = yplHotel.copy(rooms = rooms, hotelAggregation = hotelAggregation),
        entry = hotelEntryModel,
      )
    }
  }

  private def logYplRoomsForHeuristicTracking(rooms: List[YPLRoom],
                                              reason: String,
                                              hotelId: Int,
                                              supplierId: Int,
                                              ctx: YplContext): Unit = if (ctx.request.enableOfferFilterLogs) {
    messageSink.log {
      rooms.map { room =>
        DFOfferFilterTrackingMessage.fromYPLRoom(
          hotelId,
          supplierId,
          room,
          reason,
        )(ctx)
      }
    }
  }

  override def logHeuristicRoomTracking(rooms: List[YplRoomEntry],
                                        reason: String,
                                        hotelId: Int,
                                        supplierId: Int,
                                        ctx: YplContext): Unit = if (ctx.request.enableOfferFilterLogs) {
    messageSink.log {
      rooms.map { room =>
        DFOfferFilterTrackingMessage.fromYplRoomEntry(
          hotelId,
          supplierId,
          room,
          reason,
        )(ctx)
      }
    }
  }

  // 1. Groups the rooms by the groupingMethod
  // 2. Sorts the groups by the orderBy function applied to the minimum element
  // 3. Iterates through the sorted groups, adding all rooms from each group to the result
  //    until adding another group would exceed the limit
  // 4. Returns the result and the remaining rooms
  override def enhancedHeuristic[T, K](rooms: List[T],
                                       limit: Int,
                                       groupingMethod: T => K,
                                       orderBy: T => Double): (List[T], List[T]) = {

    @tailrec
    def loop(sortedRoomTypes: Vector[(K, Vector[T])],
             limit: Int,
             result: ListBuffer[T] = ListBuffer.empty[T]): (List[T], List[T]) =
      if (sortedRoomTypes.isEmpty) (result.toList, List.empty[T])
      else if (result.nonEmpty && sortedRoomTypes.head._2.length + result.length > limit)
        (result.toList, sortedRoomTypes.flatMap(_._2)(collection.breakOut))
      else loop(sortedRoomTypes.tail, limit, result ++= sortedRoomTypes.head._2)

    val sortedRoomTypes = rooms.toVector.groupBy(groupingMethod).toVector.sortBy { case (_, sameRoomTypes) =>
      orderBy(sameRoomTypes.minBy(orderBy))
    }

    loop(sortedRoomTypes, limit)
  }

  // this is duplicated logic from DF
  // TODO::Remove the existing ont from DFAPI
  private def isEscapePackageSupported(room: YPLRoom): Boolean =
    room.stayPackageType.contains(com.agoda.papi.enums.hotel.StayPackageTypes.Escapes)

  /**
    * When calculating the cheapest room, we can try to predict what the final room ordering will be by using a base rate.
    *
    * @param hOpt optional hotel entry
    * @param ctx  flow context
    * @return the same hotel with a (possibly) reduced number of rooms
    */
  override def limitRoomsHeuristic(hOpt: Option[YplHotelEntryModel])(implicit
    ctx: YplContext): Option[YplHotelEntryModel] = {

    def includeBookedRoom(allRooms: List[YplRoomEntry],
                          filteredRooms: List[YplRoomEntry],
                          roomsLimit: Int): (List[YplRoomEntry], List[YplRoomEntry]) = {
      val bookRoomIdentifierOpt = ctx.request.bookRoomIdentifier

      bookRoomIdentifierOpt match {
        case Some(bookRoomIdentifier) =>
          val bookRoomOpt = allRooms.filter(isMatch(_, bookRoomIdentifier)).headOption
          addRoomIfDoesNotExist(filteredRooms, bookRoomOpt, roomsLimit)
        case _ => (filteredRooms, List.empty)
      }
    }

    val cid = ctx.request.cInfo.cid.toString
    hOpt.foreach(h =>
      ctx.sendMetric(Measurements.roomBeforeHeuristicCount,
                     h.rooms.size,
                     Map("enabled" -> ctx.request.roomsLimit.nonEmpty.toString, Measurements.cidTag -> cid)))
    (hOpt, ctx.request.roomsLimit) match {
      case (Some(h), Some(limit)) =>
        val (roomsIgnoreCheapestRoomOnly, normalRooms) = h.rooms.partition(_.isKeptForFlexibleMultiRoom)
        val groupingMethod = (r: YplRoomEntry) => r.roomTypeId
        val orderBy = (r: YplRoomEntry) => r.totalValueForHeuristics
        val (filteredIgnoreCheapestRoomsOnly, removedIgnoreCheapestRoomsOnly) =
          enhancedHeuristic(roomsIgnoreCheapestRoomOnly, limit, groupingMethod, orderBy)
        val (filteredNormalRooms, removedNormalRooms) = enhancedHeuristic(normalRooms, limit, groupingMethod, orderBy)
        val filteredRooms = filteredIgnoreCheapestRoomsOnly ++ filteredNormalRooms
        val numberOfRoomsThatAreRemoved = removedIgnoreCheapestRoomsOnly.size + removedNormalRooms.size
        if (numberOfRoomsThatAreRemoved > 0) {
          ctx.sendMetric(Measurements.roomRemoveWithHeuristicCount,
                         numberOfRoomsThatAreRemoved,
                         Map(Measurements.cidTag -> cid))

          logHeuristicRoomTracking(
            removedIgnoreCheapestRoomsOnly,
            supplierLoadRateOfferLimitForFMR,
            h.hotelId,
            h.supplierId,
            ctx,
          )

          logHeuristicRoomTracking(
            removedNormalRooms,
            supplierLoadRateOfferLimit,
            h.hotelId,
            h.supplierId,
            ctx,
          )

        }
        logger.info(
          s"Heuristic has removed $numberOfRoomsThatAreRemoved and kept ${filteredRooms.size} rooms for hotel ${h.hotelId} and supplier ${h.supplierId} and roomsIgnoreCheapestRoomOnly: ${roomsIgnoreCheapestRoomOnly.size} and normalRoom: ${normalRooms.size}")

        val finalRooms =
          if (filteredRooms.forall(!_.isEscapes)) {
            val escapesRooms =
              h.rooms.filter(_.isEscapes).sortBy(_.totalValue).take(ctx.request.asoRoomsLimit.getOrElse(0))
            val swappedRooms = filteredRooms.sortBy(_.totalValue).dropRight(escapesRooms.size) ++ escapesRooms
            if (escapesRooms.nonEmpty) ctx.sendMetric(Measurements.roomSwappedWithHeuristicCount,
                                                      escapesRooms.size,
                                                      Map(Measurements.cidTag -> cid))
            swappedRooms
          } else {
            filteredRooms
          }

        val (roomsToUse, roomsThatAreRemoved) = includeBookedRoom(h.rooms, finalRooms, limit)

        logHeuristicRoomTracking(
          roomsThatAreRemoved,
          supplierLoadRateOfferLimitForBookingRoom,
          h.hotelId,
          h.supplierId,
          ctx,
        )

        reportNewOccupancyLogicMeasurement(
          measurement = Measurements.beforeRoomHeuristic1Count,
          rooms = h.rooms,
          supplierId = h.supplierId,
          yctx = ctx,
        )
        Some(h.copy(rooms = roomsToUse))
      case _ => hOpt
    }
  }
}

object LimitRoomsHeuristicService {
  val supplierLoadRateOfferLimit = "H1 : Supplier Load Rate Offer Limit"
  val supplierLoadRateOfferLimitForFMR = "H1 : Supplier Load Rate Offer Limit For FMR flow"
  val supplierLoadRateOfferLimitForBookingRoom = "H1: Supplier Load Rate Offer Limit For Booking Room"
  val supplierLimitTo20Rooms = "H4 : Supplier Limit to 20 Master Room"
}

trait LimitRoomsHeuristicServiceMock extends LimitRoomsHeuristicService {
  override def enhancedHeuristic[T, K](rooms: List[T],
                                       limit: Int,
                                       groupingMethod: T => K,
                                       minBy: T => Double): (List[T], List[T]) = (rooms, List.empty)

  override def limitRoomsHeuristic(hOpt: Option[YplHotelEntryModel])(implicit
    ctx: YplContext): Option[YplHotelEntryModel] = hOpt

  override def limitCheapestRoomHeuristic(d: Option[YplHotelWithEntry])(implicit
    ctx: YplContext): Option[YplHotelWithEntry] = d

  override def logHeuristicRoomTracking(rooms: List[YplRoomEntry],
                                        reason: String,
                                        hotelId: Int,
                                        supplierId: Int,
                                        ctx: YplContext): Unit = {}
}
