// scalastyle:off
package com.agoda.papi.ypl.pricing

import com.agoda.papi.enums.hotel.{PaymentModel, RateModel, TaxType}
import com.agoda.papi.enums.request.StackDiscountOption
import com.agoda.papi.enums.room.{DiscountType, _}
import com.agoda.papi.pricing.pricecalculation.models.tax.Tax
import com.agoda.papi.ypl.commission.ApmCommissionUtils.ApmDeltaPercentageConverter
import com.agoda.papi.ypl.commission._
import com.agoda.papi.ypl.commission.apm.models.{AutoPriceMatchKeyEntry, AutoPriceMatchPriceInfo}
import com.agoda.papi.ypl.fencing.{AgxFencing, FencedRateCategory}
import com.agoda.papi.ypl.logging.DFAgxCommissionAdjustmentsMessage
import com.agoda.papi.ypl.models
import com.agoda.papi.ypl.models.Wholesale.{DEFAULT_CAMPAIGN_COMMISSION, ZERO_WHOLESALE_METADATA}
import com.agoda.papi.ypl.models.api.request.{
  YplAGXCommission,
  YplAGXCommissionAdjustment,
  YplAGXCommissionAdjustmentWrapper,
}
import com.agoda.papi.ypl.models.consts._
import com.agoda.papi.ypl.models.enums.OccupancyModel.FullPatternLengthOfStay
import com.agoda.papi.ypl.models.enums._
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.pricing.{BookingPriceBreakdown, RoomOccupancy}
import com.agoda.papi.ypl.models.proto._
import com.agoda.papi.ypl.models.proto.enums.SourceTypes
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.pricing.supplier.SupplierRoomFilterService
import com.agoda.papi.ypl.services.{CidToOriginMapper, OriginManager}
import com.agoda.papi.ypl.settings.{HadoopLoggingSettings, MOHUGdsCommissionFeeSettings}
import com.agoda.papi.ypl.utils.TimeTypes
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice.{
  OccupancyPrice => ProtoOccupancyPrice,
  TaxAndFee => ProtoTaxAndFee,
  TaxAndFeeV2 => ProtoTaxAndFeeV2,
}
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.{
  BookingRestriction => ProtoBookingRestriction,
  DailyPrice => ProtoDailyPrice,
}
import com.agoda.protobuf.cache.ChannelRoomRate.{RateCategory => ProtoRateCategory}
import com.agoda.protobuf.cache.Promotion.{Restriction => ProtoPromoRestriction}
import com.agoda.protobuf.cache.{
  Benefit,
  ChannelRoomRate,
  HotelPrice,
  RateRepurposingData,
  RoomType,
  StackedChannelInfo,
  StructureBenefitParameter,
  TaxPrototypeLevelMap,
  Promotion => ProtoPromotion,
}
import com.agoda.protobuf.common.OccupanyModel.{FullPatternLengthOfStay => ProtoFPLOS}
import com.agoda.protobuf.common.{
  SourceType,
  TaxApplyBreakdownType => ProtoTaxApplyBreakdownType,
  TaxType => ProtoTaxType,
}
import com.agoda.utils.collection.SumImplicits._
import com.agoda.utils.flow.PropertyContext
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.{DateTime, DateTimeZone}
import _root_.models.consts.ABTest
import com.agoda.finance.tax.enums.TaxLevelCalculationType
import com.agoda.finance.tax.models.{TaxPrototypeInfo, TaxPrototypeLevel}

import java.time.LocalTime
import scala.collection.breakOut
import scala.collection.mutable.ListBuffer

/**
  * @param original
  * @param agxCommission
  */
case class Commissions(original: Double,
                       agxCommission: YplAGXCommission,
                       wholesaleMetadata: WholesaleMetadata = ZERO_WHOLESALE_METADATA,
                       // they are used to store protobuf commission for booking logs
                       loadedCommissionValue: Double = 0.0,
                       loadedContractedCommission: Double = 0.0)

// TODO: refactor scalastyle, for File length exceeds 600 lines
// scalastyle:off
trait OTAConverter
  extends RequestRelatedValidation
    with ZeroAllotmentHelper
    with LazyLogging
    with OriginManager
    with CidToOriginMapper {

  val bcomDefaultCommission =
    16.60 // As we have 15% commission in db and full refresh is hard for bcom we are overriding in YPL

  val mohuGdsCommissionFeeSettings = MOHUGdsCommissionFeeSettings.setting

  val hadoopLoggingSettings = HadoopLoggingSettings()

  val extraPersonSurchargeId = 787

  val breakfastBenefitCode: BenefitId = 1

  val breakfastTypeBenefits: Set[BenefitId] = Set(
    1, // Breakfast
    20, // Breakfast for 1
    26, // Breakfast for 2
    78, // Complimentary breakfast for 3 guests
    89, // Local breakfast
    206, // Complimentary Breakfast
    216, // VIP Only - Free Breakfast2
    257, // ${Unit1} Breakfast for ${1}
    278, // Breakfast for ${1}
    665, // VIP Only - Free Breakfast
  )

  def buildHotelEntryModel(h: HotelPrice,
                           hInfo: HotelMeta,
                           dispatchChannels: YplDispatchChannels,
                           dispatchChannelsPerFence: Map[YplRateFence, YplDispatchChannels],
                           protoCor: Cor,
                           isBcom: Boolean)(implicit ctx: YplContext): YplHotelEntryModel = {
    implicit val request = ctx.request

    val isBcomFixTaxAmountApplyToPB: Boolean = isBcom
    val isApplyMOHUGdsCommissionFee: Boolean =
      mohuGdsCommissionFeeSettings.cidSet.contains(request.cInfo.cid.getOrElse(0))

    val reqOcc = hInfo.getOccupancy
    val supplierPaymentModel = PaymentModel.getPaymentModel(h.paymentModel.value)
    val supplierTaxType =
      if (h.taxType == ProtoTaxType.UnknownTaxType) TaxType.SimpleTax else TaxType.getFromValue(h.taxType.value)
    val supplierTaxInfo = buildTaxInfo(
      taxType = supplierTaxType,
      isConfigProcessingFees = hInfo.getIsUseConfiguredProcessingFee,
      channelRoomRates = h.channelRoomRate,
      isBcomFixTaxAmountApplyToPB = isBcomFixTaxAmountApplyToPB,
      isApplyMOHUGdsCommissionFee = isApplyMOHUGdsCommissionFee,
      mohuGdsCommissionFeeSettings = mohuGdsCommissionFeeSettings,
      taxPrototypeLevelList = h.taxPrototypeLevelList,
      countryCurrency = hInfo.countryCurrency,
      hotel = hInfo,
      supplierId = h.supplierId,
    )
    val supplierSurchargeRateType = RateType.getFromValue(h.surchargeRateLoadType.map(_.value).getOrElse(0))
    val supplierSourceType = h.sourceType.map(sc => SourceTypes.getSourceType(sc.value))
    val roomTypes: Map[OTARoomTypeKey, RoomType] = h.roomTypes.map { rt =>
      OTARoomTypeKey(rt.roomTypeId, rt.supplierRoomId, rt.supplierRatePlanId) -> rt
    }(breakOut)

    val supplierStackedChannelInfo = buildSupplierStackedChannelInfo(h.stackedChannelInfo)

    val hotelTimeZone = DateTimeZone.forOffsetHours(hInfo.gmtOffset)
    val fireDrillProto = FireDrillProtoConversionHelper.hotelPriceFireDrillConverter(h, hotelTimeZone)

    val isAboCidToOriginMappingEnabled = ctx.experimentContext.isPropertyB(
      PropertyContext(hInfo.hotelId, hInfo.cityId, hInfo.countryId),
      ABTest.ABO_CID_TO_ORIGIN_MAPPER,
    )

    val rooms: List[YplRoomEntry] = h.channelRoomRate.flatMap { roomInfo =>
      val filteredRateCategories = roomInfo.rateCategories.withFilter(filterRateCategory(_, roomInfo, reqOcc))
      val fencedRateCategories = filteredRateCategories
        .map(tagFencesToRoomRateCategory(h, roomInfo, _, isAboCidToOriginMappingEnabled))
        .withFilter(_.fences.nonEmpty)
        .flatMap { rate =>
          AgxFencing.groupByAgx(hInfo.hotelId, rate, request.agxCommissionAdjustmentFences)
        }

      fencedRateCategories.flatMap(fencedRateCategory =>
        buildRoomEntry(
          rc = fencedRateCategory.rateCategory,
          roomInfo = roomInfo,
          roomTypes = roomTypes,
          hInfo = hInfo,
          h = h,
          isPullSupplier = h.sourceType.contains(SourceType.Pull),
          isBcomFixTaxAmountApplyToPB = isBcomFixTaxAmountApplyToPB,
          fencedRateCategory.fences,
          fencedRateCategory.agxCommission,
          dispatchChannels,
          fireDrillProto,
        ))
    }(breakOut)

    val filteredRooms = rooms.filter(filterBookableOtaRooms(_, dispatchChannels))
    val filteredSupplierRooms =
      SupplierRoomFilterService.filterSupplierRooms(h.supplierId, filteredRooms) // supplier specific condition

    val supplierContractedCommission = getSupplierContractedCommission(h)

    YplHotelEntryModel(
      hotelId = h.hotelId.toInt,
      supplierId = h.supplierId,
      paymentModel = supplierPaymentModel,
      rooms = filteredSupplierRooms,
      occupancyModel = OccupancyModel.Full,
      taxInfo = supplierTaxInfo,
      rateModel = RateModel.New,
      surchargeRateType = supplierSurchargeRateType,
      metaData = hInfo,
      dispatchChannels = dispatchChannels,
      dispatchChannelsPerFence = dispatchChannelsPerFence,
      ratePlanLanguage = None,
      rateReutilizations = toRateReutilizationEntry(h.rateRepurposeList),
      reqOcc = reqOcc,
      lengthOfStay = h.lengthOfStay,
      supplierSourceType = supplierSourceType,
      expiresAt = h.expiresAt,
      isOTASupplier = Some(true),
      supplierContractedCommission = supplierContractedCommission,
      stackChannelDiscountInfo = supplierStackedChannelInfo,
      agencyNoccSetting = None,
      autoPriceMatchInfo = Map.empty,
      fireDrillProto = fireDrillProto,
    )
  }

  private def getSupplierContractedCommission(h: HotelPrice) = h.supplierContractedCommission.filterNot(_ == -1)

  private[pricing] def tagFencesToRoomRateCategory(h: HotelPrice,
                                                   roomInfo: ChannelRoomRate,
                                                   rateCategory: ChannelRoomRate.RateCategory,
                                                   isAboCidToOriginMappingEnabled: Boolean)(implicit
    ctx: YplContext): FencedRateCategory = {
    val baseFences = ctx.request.baseFences(YplMasterChannel(roomInfo.channelId))

    val isPopulateCustomerSegment = shouldPopulateCustomerSegmentRestriction(
      rateCategory.supplierRateInfo.flatMap(_.isNationalityRate).getOrElse(false),
      h.supplierId == DMC.BCOM)
    val finalFences = filterRateFenceByCustomerSegment(baseFences,
                                                       rateCategory.bookingRestriction,
                                                       isPopulateCustomerSegment,
                                                       isAboCidToOriginMappingEnabled)

    FencedRateCategory(rateCategory, finalFences)
  }

  private def toRateReutilizationEntry(rateRepurposeList: Seq[RateRepurposingData]) = rateRepurposeList.map(rr =>
    YPLRateReutilizationEntry(
      sourceChannel = YplMasterChannel(rr.sourceChannel),
      referenceChannel = YplMasterChannel(rr.referenceChannel),
      targetChannel = YplMasterChannel(rr.targetChannel),
      discountType = rr.discountType,
      flatChannelDiscount = rr.channelDiscount,
      minAdvPurchase = rr.minAdvPurchase.filter(_ >= 0),
      maxAdvPurchase = rr.maxAdvPurchase.filter(_ >= 0),
    ))
  private[pricing] def getCommissionHolderForOTA(
    rc: ProtoRateCategory,
    supplierId: SupplierId,
    inventoryType: InventoryType,
    channel: YplChannel,
    hInfo: HotelMeta,
    wlFeature: Option[Feature],
    fences: Set[YplRateFence],
    fireDrillProto: Option[FireDrillProto] = None,
    dispatchChannels: YplDispatchChannels,
    agxCommissionAdjustment: YplAGXCommissionAdjustmentWrapper =
      YplAGXCommissionAdjustmentWrapper.noAgxCommissionAdjustment,
    supplierCommissionMapping: SupplierCommissionMapping = Map.empty,
    autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]],
    supplierContractedCommission: Option[Double])(implicit ctx: YplContext) = {
    val multipleAutoPriceMatch = hInfo.multipleAutoPriceMatch
    val isAgxEligibleDmc = suppliers.DMC.agxEligibleList.contains(supplierId)
    val (commissionAdjustment, commissionOverriding, marginAdjustment, commissionBaseOnHotelContractAdjustment) =
      CommissionHolderBuilder.buildWhitelabelCommissionAdjustment(wlFeature,
                                                                  ctx.request.whitelabelSetting,
                                                                  inventoryType,
                                                                  ctx.request.occ,
                                                                  hInfo.whiteLabelContractTypeId.getOrElse(0))

    val dailyCommissions: Map[Long, Seq[ChannelRoomRate.RateCategory.DailyPrice.Commission]] = rc.dailyPrices
      .groupBy(d => d.dateMillis)
      .map { case (dateMillis, dailyPrices) => dateMillis -> dailyPrices.map(_.commissions).flatten }(
        collection.breakOut)
    val dailyCommissionHolder = CommissionHolderBuilder.buildDailyCommissionHolder(
      hotelId = hInfo.hotelId,
      dailyHotelPriceCommissions = dailyCommissions,
      paymentModel = rc.paymentModel,
      channelId = channel.compositeChannelId,
      supplierId = supplierId,
      agxCommissionAdjustment = agxCommissionAdjustment,
      isAgxEligibleDmc = isAgxEligibleDmc,
      inventoryType = inventoryType,
      supplierCommissionMapping = supplierCommissionMapping,
    )(ctx)
    val growthProgramCommissionHolder = GrowthProgramCommissionHolder(
      applicableGrowthProgram = hInfo.applicableGrowthPrograms,
      growthProgramFence = hInfo.growthProgramFence,
    )

    CommissionHolder(
      dailyCommissionHolder,
      commissionAdjustment,
      commissionOverriding,
      marginAdjustment,
      commissionBaseOnHotelContractAdjustment,
      ApmCommissionHolder(
        multipleAutoPriceMatch,
        hInfo.apmCommissionReductionEligibility,
        hInfo.apmConfigs,
        ctx.request.apmSetting,
        autoPriceMatchInfo,
        hInfo.apmDeltaPercentage.map { case (k, v) => k -> v.toCommissionModel },
      ),
      agxAllDateAdjust = agxCommissionAdjustment.commissionAdjustmentOp.flatMap(
        _.allDateAdjust.map(a =>
          AgxCommissionHolder(
            payAsYouGoCommission = a.payAsYouGoCommission,
            prepaidCommission = a.prepaidCommission,
            freeTrialCommission = a.freeTrialCommission,
            isApplyAgx = true,
            isAgxEligibleRateChannel = true,
          ))),
      CommissionHolderBuilder.buildAgpFireDrillCommissionHolder(fireDrillProto),
      MORPCommissionHolder.default, // Not applicable for OTA
      retailGlobalContractedCommission = 0d,
      supplierContractedCommission = supplierContractedCommission,
      growthProgramCommissionHolder = growthProgramCommissionHolder,
    )
  }

  private[pricing] def buildRoomEntry(rc: ProtoRateCategory,
                                      roomInfo: ChannelRoomRate,
                                      roomTypes: Map[OTARoomTypeKey, RoomType],
                                      hInfo: HotelMeta,
                                      h: HotelPrice,
                                      isPullSupplier: Boolean = false,
                                      isBcomFixTaxAmountApplyToPB: Boolean,
                                      fences: Set[YplRateFence],
                                      agxCommission: Option[YplAGXCommissionAdjustment],
                                      dispatchChannels: YplDispatchChannels,
                                      fireDrillProto: Option[FireDrillProto] = None)(implicit
    ctx: YplContext): Seq[YplRoomEntry] = {
    implicit val request: YplRequest = ctx.request
    val reqOcc = hInfo.getOccupancy
    val occupancyModel =
      if (h.occupancyModel == ProtoFPLOS) OccupancyModel.FullPatternLengthOfStay else OccupancyModel.Full
    val supplierRateCategoryCode = rc.supplierRateInfo.flatMap(_.supplierRatePlan)
    val otaRoomTypeKey = OTARoomTypeKey(roomInfo.roomTypeId, roomInfo.supplierRoomTypeId, supplierRateCategoryCode)
    // Pull supplier does not set Supplier Room Type Id in rate category
    val roomType = roomTypes
      .get(otaRoomTypeKey)
      .orElse(roomTypes.get(OTARoomTypeKey(roomInfo.roomTypeId, roomInfo.supplierRoomTypeId, None)))
    val haveExtraPersonCharge = rc.dailyPrices.exists(prices =>
      prices.prices.exists(price => price.surcharges.exists(surcharge => surcharge.surchargeId == extraPersonSurchargeId)))
    val supplierCommissionMapping = hInfo.supplierCommissionMapping
    val agxCommissionAdjustmentOp = agxCommission
    val agxCommissionAdjustment = YplAGXCommissionAdjustmentWrapper(agxCommissionAdjustmentOp)
    val hotelPaymentModel = PaymentModel.getPaymentModel(h.paymentModel.value)
    val yplRoomEntryDefaultInventoryType = InventoryType.Agoda
    val isAgxEligibleDmc = suppliers.DMC.agxEligibleList.contains(h.supplierId)
    buildRateCategoryEntry(
      rc = rc,
      roomInfo = roomInfo,
      reqOcc = reqOcc,
      filterZeroCommission = isPullSupplier,
      supplierId = h.supplierId,
      isBcomFixTaxAmountApplyToPB = isBcomFixTaxAmountApplyToPB,
      supplierCommissionMapping = supplierCommissionMapping,
      agxCommissionAdjustment = agxCommissionAdjustment,
      isAgxEligibleDmc = isAgxEligibleDmc,
      hotelPrice = h,
      hInfo = hInfo,
      fences = fences,
      inventoryType = yplRoomEntryDefaultInventoryType,
    ).map { rateCategoryEntry =>
      val roomTypeEntry = buildRoomType(roomInfo, roomType)
      val isBreakfastIncluded = rateCategoryEntry.benefitList.exists(_.benefitId == 1)
      val channelRateType = roomInfo.rateType.map(rt => RateType.getFromValue(rt.value))
      val rateCategoryEntryWithFilteredDailyPrice =
        filterDailyPrices(haveExtraPersonCharge, rateCategoryEntry, hInfo.getOccupancy, occupancyModel, roomTypeEntry)
      val promotions: List[PromotionEntry] =
        rc.promotions.map(buildPromotionEntry(_, rateCategoryEntry.cxlCode))(breakOut)
      val rateType = rateCategoryEntry.rateTypeLoaded

      val dmcDataHolder = DmcDataHolder(generateExternalDataStr(rc), roomInfo.roomUid, rc.supplierRateInfo)

      val paymentModel: PaymentModel = rc.getPaymentModel.isUnknownPaymentModel match {
        case false => PaymentModel.getPaymentModel(rc.getPaymentModel.value)
        case _ => hotelPaymentModel
      }

      val (numAdults, numChild, propOfferOccupancy) = h.occupancy match {
        case Some(occupancy) =>
          val numOfRooms = reqOcc.rooms
          val adultsPerRoom = reqOcc.guestsPerRoomForPull(occupancy.numAdults, numOfRooms)
          val childrenPerRoom = reqOcc.guestsPerRoomForPull(occupancy.numChild, numOfRooms, isChildrenGuest = true)
          val propOfferOccupancy = PropOfferOccupancy(occupancy.numAdults, occupancy.numChild)

          (adultsPerRoom, childrenPerRoom, propOfferOccupancy)
        case _ => (reqOcc.guestsPerRoom(), 0, PropOfferOccupancy(reqOcc.guests, 0))
      }

      val channel = YplMasterChannel(roomInfo.channelId)

      val commissionHolder = getCommissionHolderForOTA(
        rc,
        h.supplierId,
        inventoryType = yplRoomEntryDefaultInventoryType,
        channel = channel,
        hInfo = hInfo,
        wlFeature = ctx.request.supplierFeatures.features.get(h.supplierId),
        fences = fences,
        fireDrillProto = fireDrillProto,
        dispatchChannels = dispatchChannels,
        agxCommissionAdjustment = agxCommissionAdjustment,
        supplierCommissionMapping = supplierCommissionMapping,
        autoPriceMatchInfo = Map.empty,
        supplierContractedCommission = getSupplierContractedCommission(h),
      )

      val room = YplRoomEntry(
        roomTypeId = roomInfo.roomTypeId,
        masterRoomTypeId = hInfo.getMasterRoomTypeId(roomInfo.roomTypeId),
        channel = channel,
        cxlCode = rateCategoryEntry.cxlCode, // Dont have this in OTA. Default as 0
        isBreakFastIncluded = isBreakfastIncluded,
        remainingRooms = rateCategoryEntry.remainingRoom,
        currency = roomInfo.currencyCode.getOrElse(""),
        rateType = rateType, // need to know surcharge markup,
        processingFees = roomInfo.processingFee,
        isAllowCombinePromotion = h.isPromotionCombinable.getOrElse(false), // Dont have this in OTA. Default as 0
        roomType = roomTypeEntry,
        dailyPrices = rateCategoryEntryWithFilteredDailyPrice.dailyPrices, // Need the same daily price as rate category
        occEntry =
          if (occupancyModel == FullPatternLengthOfStay && !reqOcc.isFreeOcc)
            RoomOccupancy(numAdults,
                          numChild,
                          freeChildrenAndInfants = numChild,
                          allowedFreeChildrenAndInfants = numChild)
          else
            RoomOccupancy(0,
                          0,
            ), // For some reason full occ is set as RoomOccupancy(0,0) in ycs reqOcc.getRoomOccupancy()
        availablePromotions = promotions,
        channelRateType = channelRateType,
        rateCategory = rateCategoryEntryWithFilteredDailyPrice,
        originalRateType = rateType,
        paymentModel = rateCategoryEntry.paymentModel,
        dmcDataHolder = Some(dmcDataHolder),
        hourlyAvailableSlots = rateCategoryEntry.hourlyAvailableSlots,
        confirmByMins = rc.confirmByMins,
        checkInInformation = rateCategoryEntry.checkInInformation,
        fences = fences,
        roomDataChangeTracker = rateCategoryEntry.roomDataChangeTracker,
        commissionHolder = commissionHolder,
        propOfferOccupancy = propOfferOccupancy,
      )

      val paymentOptions = fences
        .map { fence =>
          (getPaymentOptions(
             supplierId = h.supplierId,
             hotelMeta = hInfo,
             rateCategoryPaymentModel = paymentModel,
             isCreditCardRequired = rc.isCreditCardRequried,
             isPrepaymentRequired = rc.isPrepaymentRequired,
             origin = Some(fence.origin),
           ),
           fence)
        }
        .groupBy(v => v._1)

      paymentOptions.map { case (paymentOption, values) =>
        room.copy(
          paymentOptions = paymentOption,
          fences = values.map(_._2),
        )
      }.toSeq
    }
  }.getOrElse(Seq.empty)

  private[pricing] def filterDailyPrices(haveExtraPersonCharge: Boolean,
                                         rateCategoryEntry: RateCategoryEntry,
                                         reqOcc: YplReqOccByHotelAgePolicy,
                                         occupancyModel: OccupancyModel,
                                         roomTypeEntry: RoomTypeEntry) =
    if (!haveExtraPersonCharge) {
      rateCategoryEntry
    } else {
      val dailyPricesFiltered = rateCategoryEntry.dailyPrices.map { case (date, dailyPrice) =>
        val prices = dailyPrice.prices.filter(p =>
          filterPriceEntryByOccupancyAndOccupancyModel(p, reqOcc.guestsPerRoom(), occupancyModel, reqOcc, roomTypeEntry))
        (date, dailyPrice.copy(prices = prices))
      }
      rateCategoryEntry.copy(dailyPrices = dailyPricesFiltered)
    }

  private[pricing] def addPropertyBenefits(propBenefits: Seq[com.agoda.supply.calc.proto.Benefit],
                                           benefitEntries: List[BenefitEntry])(implicit
    ctx: YplContext): List[BenefitEntry] = {
    val propertyBenefits: List[BenefitEntry] = propBenefits.map(buildPropOfferBenefit)(breakOut)
    val filteredPropertyBenefits = filterPropertyBenefits(benefitEntries, propertyBenefits)
    (benefitEntries ++: filteredPropertyBenefits).distinct
  }

  // filters out breakfast from propertyBenefits if breakfast type benefit already exists in benefits
  def filterPropertyBenefits(benefitEntries: List[BenefitEntry],
                             propertyBenefits: List[BenefitEntry]): List[BenefitEntry] = {
    val breakFastExistsInRPBenefits = benefitEntries.exists(breakfastTypeBenefits contains _.benefitId)
    breakFastExistsInRPBenefits match {
      case true => propertyBenefits.filter(_.benefitId != breakfastBenefitCode)
      case _ => propertyBenefits
    }
  }

  private[pricing] def filterRateFenceByCustomerSegment(fences: Set[YplRateFence],
                                                        restrictionProto: Option[ProtoBookingRestriction],
                                                        isPopulateCustomerSegment: Boolean,
                                                        isAboCidToOriginMappingEnabled: Boolean)(implicit
    ctx: YplContext): Set[YplRateFence] = {
    val request = ctx.request
    val restriction = buildRateCategoryBookingRestrictionInput(restrictionProto, isPopulateCustomerSegment)
    val customerSegments: List[CustomerSegment] = restriction.map(_.customerSegments).getOrElse(List.empty)

    fences.filter { fence =>
      val customerOrigin =
        if (isAboCidToOriginMappingEnabled) {
          // From CidToOriginMapper trait that's implemented by AffiliateCidToOriginMappingDataServiceImpl
          getOriginByCid(fence.cid) orElse Some(fence.origin)
        } else {
          // From OriginManager trait that's implemented by FileOriginManager
          getOriginByCid(Some(fence.cid)) orElse Some(fence.origin)
        }
      validateCustomerSegment(customerSegments, customerOrigin.getOrElse(""), fence.language, request.cInfo.vipLevel)
    }
  }

  private[pricing] def shouldPopulateCustomerSegmentRestriction(isNationalityRate: Boolean,
                                                                isBcom: Boolean,
                                                                isPropOffer: Boolean = false)(implicit
    ctx: YplContext): Boolean = isPropOffer || isNationalityRate ||
    ctx.experimentContext.isUserB(YplExperiments.KILL_SWITCH_RATECATEGORY_CUSTOMER_SEGMENT) ||
    isBcom

  def buildRateCategoryEntry(rc: ProtoRateCategory,
                             roomInfo: ChannelRoomRate,
                             reqOcc: YplReqOccByHotelAgePolicy,
                             filterZeroCommission: Boolean = false,
                             supplierId: SupplierId,
                             isBcomFixTaxAmountApplyToPB: Boolean,
                             supplierCommissionMapping: SupplierCommissionMapping = Map.empty,
                             agxCommissionAdjustment: YplAGXCommissionAdjustmentWrapper =
                               YplAGXCommissionAdjustmentWrapper.noAgxCommissionAdjustment,
                             isAgxEligibleDmc: Boolean = false,
                             hotelPrice: HotelPrice,
                             hInfo: HotelMeta,
                             fences: Set[YplRateFence] = Set.empty,
                             inventoryType: InventoryType)(implicit ctx: YplContext): Option[RateCategoryEntry] = {
    val isBcom = supplierId == DMC.BCOM
    val supplierRateCategoryCode = rc.supplierRateInfo.flatMap(_.supplierRatePlan)
    val isPopulateCustomerSegment =
      shouldPopulateCustomerSegmentRestriction(rc.supplierRateInfo.flatMap(_.isNationalityRate).getOrElse(false), isBcom)
    val bookingRestrictionInput =
      buildRateCategoryBookingRestrictionInput(rc.bookingRestriction, isPopulateCustomerSegment)
    val bookRestriction = buildRestriction(bookingRestrictionInput)

    if (!validateBookingRestriction(bookRestriction, hInfo.gmtOffset, fences, supplierId)) {
      None
    } else {
      val paymentModel = rc.paymentModel.map(paymentModel => PaymentModel.getPaymentModel(paymentModel.value))
      val protoRateType = Option(RateType.getFromValue(rc.rateTypeLoaded.value)).filterNot(_ == RateType.Unknown)
      val rateTypeLoaded = protoRateType.getOrElse(RateType.NetExclusive)
      val dailyPrices: Map[DateTime, DailyPrice] = rc.dailyPrices.flatMap(dp =>
        buildDailyPrice(
          dp = dp,
          roomInfo = roomInfo,
          reqOcc = reqOcc,
          filterZeroCommission = filterZeroCommission,
          isBcom = isBcom,
          isBcomFixTaxAmountApplyToPB = isBcomFixTaxAmountApplyToPB,
          supplierCommissionMapping = supplierCommissionMapping,
          agxCommissionAdjustment = agxCommissionAdjustment,
          isAgxEligibleDmc = isAgxEligibleDmc,
          hotelPrice = hotelPrice,
          rateTypeLoaded = rateTypeLoaded,
          allowAgxLogging = rc.remainingRoom > 0,
          paymentModel = paymentModel,
          inventoryType = inventoryType,
          hInfo = hInfo,
        ))(breakOut)
      val updatedBenefits = rc.agodaBenefits
      val benefitsWithoutPropBenefits: List[BenefitEntry] =
        getBenefitsWithParams(updatedBenefits, rc.propertyBenefits).map(buildBenefit)(breakOut)
      val equivalentPropertyBenefits: Seq[com.agoda.supply.calc.proto.Benefit] =
        hotelPrice.propertyBenefits.map(transformToProtoBenefit)
      val benefitEntries: List[BenefitEntry] =
        addPropertyBenefits(equivalentPropertyBenefits, benefitsWithoutPropBenefits)
      val hourlyAvailableSlots = rc.hourlyAvailableSlots.map { slot =>
        TimeInterval(slot.duration, slot.from)
      }
      val checkInInformation = rc.checkInInformation.map { c =>
        val checkInFrom = c.checkInFrom.map(a => LocalTime.of(a.hours, a.minutes, a.seconds))
        val checkInUntil = c.checkInUntil.map(a => LocalTime.of(a.hours, a.minutes, a.seconds))
        val checkOutFrom = c.checkOutFrom.map(a => LocalTime.of(a.hours, a.minutes, a.seconds))
        val checkOutUntil = c.checkOutUntil.map(a => LocalTime.of(a.hours, a.minutes, a.seconds))
        CheckInInformation(checkInFrom, checkInUntil, checkOutFrom, checkOutUntil)
      }

      val (cxlCode, roomDataChangeTracker) = getCxlLoadedFromProtobuf(rc.cancellationCode)

      val rateCategoryEntry = RateCategoryEntry(
        rateCategoryId = rc.rateCategoryId,
        cxlCode = cxlCode,
        rateCategoryCode = None,
        /*
      Since Pull supplier can push any data to our C*.
      Some Pull Suppliers always load rate as NetExclusive and won't set rate type.
      For Push supplier, calculator will push valid load rate instead of null
         */
        rateTypeLoaded = rateTypeLoaded,
        parent = None, // Does not support parent rate category
        remainingRoom = rc.remainingRoom,
        benefitList = benefitEntries,
        dailyPrices = dailyPrices,
        bookFrom = bookRestriction.flatMap(_.bookFrom),
        bookTo = bookRestriction.flatMap(_.bookTo),
        bookTimeFrom = bookRestriction.flatMap(_.bookTimeFrom),
        bookTimeTo = bookRestriction.flatMap(_.bookTimeTo),
        remainingRoomGa = 0, // remaining room guaranteed, currently ota does not have this
        remainingRoomRa = None, // remaining room regular, currently ota does not have this either
        minAdvance = bookRestriction.flatMap(_.minAdvance),
        maxAdvance = bookRestriction.flatMap(_.maxAdvance),
        isAmount = false, // For calculating benefit for child rate category
        applyTo = "", // For calculating benefit for child rate category
        value = 0.0, // For calculating benefit for child rate category
        promotionList = List.empty, // Not used in OTA, will use the promotion list in room entry
        isCanCombinePromotion = false, // For rate category act as promotion
        offerType = rc.offerTypeId,
        isRoomTypeNotGuarantee = rc.isRoomTypeNotGuaranteed.getOrElse(false),
        customerSegment = bookRestriction.map(_.customerSegments).getOrElse(List.empty),
        bookOn = bookRestriction.flatMap(_.bookOn),
        dmcRatePlanID = supplierRateCategoryCode,
        dmcMealPlanID = rc.supplierRateInfo.flatMap(_.supplierMealPlan),
        promotionTypeId = rc.promotionTypeId,
        promotionTypeCmsId = rc.promotionTypeCmsId,
        paymentModel = paymentModel,
        isAgencyEnabled = rc.isAgencyEnabled,
        hourlyAvailableSlots = hourlyAvailableSlots,
        checkInInformation = checkInInformation,
        roomDataChangeTracker = Some(roomDataChangeTracker),
      )
      Some(rateCategoryEntry)
    }
  }

  private def transformToProtoBenefit(pb: Benefit) = com.agoda.supply.calc.proto.Benefit(
    benefitId = pb.benefitId,
    benefitType = pb.benefitType,
    parameters = pb.parameters.map(transformToProtoStructureBenefitParameter),
  )

  private def transformToProtoStructureBenefitParameter(p: StructureBenefitParameter) =
    com.agoda.supply.calc.proto.StructureBenefitParameter(
      position = p.position,
      value = p.value,
      unitId = p.unitId,
    )

  def getBenefitsWithParams(benefitIds: Seq[Int], benefits: Seq[Benefit]): Seq[Benefit] = {
    val benefitMap = benefits.groupBy(_.benefitId)
    benefitIds.map { b =>
      if (benefitMap.contains(b)) {
        benefitMap.get(b).flatMap(_.headOption).get
      } else Benefit.defaultInstance.copy(benefitId = b)
    }
  }

  def buildBenefit(benefit: com.agoda.protobuf.cache.Benefit): BenefitEntry = BenefitEntry(
    benefitId = benefit.benefitId,
    value = 0.0d,
    benefitType = benefit.benefitType,
    benefitGroupId = None,
    parameters = benefit.parameters.map(p => BenefitParameterEntry(p.position, p.value, p.unitId)),
  )

  def buildPropOfferBenefit(benefit: com.agoda.supply.calc.proto.Benefit): BenefitEntry = BenefitEntry(
    benefitId = benefit.benefitId,
    value = 0.0d,
    benefitType = benefit.benefitType,
    benefitGroupId = None,
    parameters = benefit.parameters.map(p =>
      BenefitParameterEntry(
        position = p.position,
        value = p.value,
        unitId = p.unitId,
      )),
  )

  def getUpdatedBenefitsWithDiscount(benefitsWithoutPropBenefits: List[BenefitEntry],
                                     propBenefits: Seq[com.agoda.supply.calc.proto.Benefit]): List[BenefitEntry] =
    benefitsWithoutPropBenefits.map { benefit =>
      propBenefits
        .find(_.benefitId == benefit.benefitId)
        .map { propBenefit =>
          (propBenefit.benefitValueInUSD, propBenefit.benefitValuationType) match {
            case (Some(benefitValueInUSD), Some(benefitValuationType)) => benefit.copy(benefitValue = Some(
                BenefitValueEntry(
                  benefitValueUSD = benefitValueInUSD,
                  benefitValuationType = benefitValuationType,
                  shouldDisplayBenefit = propBenefit.shouldDisplayBenefit.getOrElse(true),
                )))
            case _ => benefit
          }
        }
        .getOrElse(benefit)
    }

  private[pricing] def shouldApplyIfItIsSaudiUmrahGovTax(
    taxPrototypeId: Option[Int],
    isApplyMOHUGdsCommissionFee: Boolean,
    mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings): Boolean =
    !mohuGdsCommissionFeeSettings.targetTaxPrototypeIdSet.contains(
      taxPrototypeId.getOrElse(0)) || isApplyMOHUGdsCommissionFee

  private def isValidCommission(commission: Commissions): Boolean = commission.original > 0d

  def buildDailyPrice(dp: ProtoDailyPrice,
                      roomInfo: ChannelRoomRate,
                      reqOcc: YplReqOccByHotelAgePolicy,
                      filterZeroCommission: Boolean = false,
                      isBcom: Boolean = false,
                      isBcomFixTaxAmountApplyToPB: Boolean,
                      supplierCommissionMapping: SupplierCommissionMapping = Map.empty,
                      agxCommissionAdjustment: YplAGXCommissionAdjustmentWrapper =
                        YplAGXCommissionAdjustmentWrapper.noAgxCommissionAdjustment,
                      isAgxEligibleDmc: Boolean = false,
                      hotelPrice: HotelPrice,
                      rateTypeLoaded: RateType,
                      allowAgxLogging: Boolean = false,
                      paymentModel: Option[PaymentModel] = None,
                      inventoryType: InventoryType,
                      hInfo: HotelMeta)(implicit ctx: YplContext): Map[DateTime, DailyPrice] = {
    implicit val request: YplRequest = ctx.request

    def buildSurcharge(s: ChannelRoomRate.Surcharge, date: DateTime, occFromProto: Int) = SurchargeEntry(
      id = s.surchargeId,
      applyTo = s.applyTo,
      option = ChargeOption.getFromValue(s.applyType.value),
      dates = Set(date),
      isAmount = s.isAmount,
      isCommissionable = s.isCommissionable,
      value = s.value,
      occFromProto = occFromProto,
    )

    val yplCom = dp.commissions.map(YplCommission(_))
    val criteria =
      YplCriteria(hotelPrice.hotelId, hotelPrice.supplierId, hotelPrice.checkInMillis, hotelPrice.lengthOfStay)

    findCommission(
      yplCom,
      roomInfo.channelId,
      inventoryType,
      request.cInfo.language,
      filterZeroCommission,
      isBcom,
      supplierCommissionMapping,
      dp.dateMillis,
      rateTypeLoaded,
      agxCommissionAdjustment,
      isAgxEligibleDmc,
      criteria,
      allowAgxLogging,
      paymentModel,
    ).map { commission =>
      val date: DateTime = new DateTime(dp.dateMillis).withTimeAtStartOfDay
      val channelDiscount = dp.channelDiscount

      val priceEntries = new ListBuffer[PriceEntry]()
      val surchargeBuffer = new ListBuffer[SurchargeEntry]()
      val taxBuffer = new ListBuffer[(TaxIDWithProTypeID, TaxValue)]()
      val countryCurrency = hInfo.countryCurrency

      dp.prices.foreach { occPrice =>
        val hotelCurrency = roomInfo.currencyCode
        // priceentries
        priceEntries += buildPriceEntry(occPrice,
                                        date,
                                        reqOcc,
                                        request.isBookingRequest,
                                        rateTypeLoaded,
                                        roomInfo.currencyCode.getOrElse("USD"))
        // tax
        if (isEnableTaxV2Exp(occPrice.taxAndFeeV2.nonEmpty, hInfo, hotelPrice.supplierId)) {
          occPrice.taxAndFeeV2.map { tV2 =>
            val (key, taxV2) =
              buildTaxLegacyV2(tV2, isBcomFixTaxAmountApplyToPB, mohuGdsCommissionFeeSettings, None, hotelCurrency)
            taxBuffer += (key -> taxV2.value)
          }
        } else {
          occPrice.taxAndFee.map { t =>
            val (key, tax) = buildTaxLegacy(t,
                                            isBcomFixTaxAmountApplyToPB,
                                            mohuGdsCommissionFeeSettings,
                                            None,
                                            countryCurrency,
                                            hotelCurrency,
            ) // It use only value data, can ignore tax prototype level part.
            taxBuffer += (key -> tax.value)
          }
        }

        // surcharge
        occPrice.surcharges.foreach { surcharge =>
          if (isValidSurcharge(date, surcharge.applyTo)) {
            surchargeBuffer += buildSurcharge(surcharge, date, occFromProto = occPrice.occupancy)
          }
        }
      }

      val distinctiveSurcharges = surchargeBuffer.distinct.toList

      Map(
        date -> DailyPrice(
          date = date,
          taxes = taxBuffer.toMap,
          isPromotionBlackOut = false,
          prices = priceEntries.toList,
          channelDiscount = channelDiscount,
          rpmSurcharges = distinctiveSurcharges, // Only support new rate model so we use this surcharge
        ))
    }.getOrElse(Map.empty)
  }

  private[pricing] def isValidSurcharge(date: DateTime, applyTo: String)(implicit request: YplRequest) =
    date == request.checkIn || !applyTo.contains("PB")

  def buildPriceEntry(p: ProtoOccupancyPrice,
                      date: DateTime,
                      reqOcc: YplReqOccByHotelAgePolicy,
                      isBookingRequest: Boolean,
                      rateTypeLoaded: RateType,
                      currencyCode: String): PriceEntry = PriceEntry(
    date = date,
    chargeType = ChargeType.Room, // currently set to room, used for calculating child rate category
    chargeOption = ChargeOption.Mandatory, // currently set to mandatory used for calculating child rate category
    applyTo = "PRPN", // Currently set to per room per night. used for calculating child rate category
    occupancy = p.occupancy,
    quantity = if (reqOcc.isFreeOcc) 1 else reqOcc.rooms,
    value = p.amount,
    rateLoadedPrice = p.amount,
    resellRefSell = p.resellInfo.flatMap(_.refSell),
    latestBreakdownStep = BreakdownStep.BaseStep,
    priceBreakdownHistory =
      BookingPriceBreakdown(isBookingRequest).newBreakdown(ChargeType.Room, rateTypeLoaded, currencyCode, p.amount),
  )

  protected def toYcsTime(time: Option[Long]): Option[Long] =
    time.map(new DateTime(_).withDate(1970, 1, 1).plusHours(7).getMillis * 10000)

  def buildPromotionEntry(promotion: ProtoPromotion, defaultCxlCode: String): PromotionEntry = {
    // This is for converting OTA Time to YCS Time for Promotion Validation

    val discountType = DiscountType.getFromValue(promotion.discountTypeId.getOrElse(-1))
    val promotionRestrictionInput = buildPromotionRestrictionInput(promotion.restriction)
    val promotionRestriction = buildRestriction(promotionRestrictionInput)

    val applyDates: Map[DateTime, Int] = promotion.discounts.zipWithIndex.map { case (d, index) =>
      val date = new DateTime(d.date)
      val indexForDiscount = if (discountType == DiscountType.AmountDiscountPerNight) index else 0
      date -> indexForDiscount
    }(breakOut)

    val discounts: List[Double] = discountType match {
      case DiscountType.AmountDiscountPerBook => List(promotion.discounts.getDSum(_.value))
      case _ => promotion.discounts.map(_.value)(breakOut)
    }

    PromotionEntry(
      id = promotion.id.getOrElse(0),
      typeId = promotion.typeId.getOrElse(0),
      discountType = discountType,
      discounts = discounts,
      cmsTypeId = promotion.cmsTypeId.getOrElse(0),
      cmsDiscountTypeId = promotion.cmsDiscountTypeId.getOrElse(0),
      minRooms = promotionRestriction.flatMap(_.minRooms).getOrElse(0),
      minNightStay = promotionRestriction.flatMap(_.minNights).getOrElse(0),
      bookOn = promotionRestriction.flatMap(_.bookOn).getOrElse(""),
      bookFrom = promotionRestriction.flatMap(_.bookFrom),
      bookTo = promotionRestriction.flatMap(_.bookTo),
      minAdvPurchase = promotionRestriction.flatMap(pr => getAdvPurchase(pr.minAdvance)),
      maxAdvPurchase = promotionRestriction.flatMap(pr => getAdvPurchase(pr.maxAdvance)),
      bookTimeFrom = promotionRestriction.flatMap(d => toYcsTime(d.bookTimeFrom)).filter(_ > 0),
      bookTimeTo = promotionRestriction.flatMap(d => toYcsTime(d.bookTimeTo)).filter(_ > 0),
      applyDates = applyDates,
      cancellationCode = promotion.cancellationCode.getOrElse(defaultCxlCode),
      customerSegments = promotionRestriction.map(_.customerSegments).getOrElse(List.empty),
      isStackable = promotion.isStackable.getOrElse(false),
      isAllowStack = promotion.isStackCombine.getOrElse(false),
      stackDiscountOption = promotion.stackableDiscountType.map(dt => StackDiscountOption.getFromValue(dt)),
      isAllowChannelDiscount = promotion.isApplyChannelDiscount.getOrElse(false),
    )
  }

  protected def getAdvPurchase(advanceOpt: Option[Int]): Option[Int] = advanceOpt.flatMap { advanceValue =>
    if (advanceValue > -1) // -1 considered as None, will ignore advance day validations
      Some(advanceValue)
    else None
  }

  // different inputs with the same name and almost the same logic
  def buildRateCategoryBookingRestrictionInput(
    restriction: Option[ProtoBookingRestriction],
    isAllowedCustomerSegmentsRestriction: Boolean = false): Option[RestrictionEntryModel] = restriction.map { br =>
    val periodIntervals: List[PeriodIntervalInput] =
      br.bookingIntervals.map(bi => PeriodIntervalInput(bi.periodType.value, bi.from, bi.to))(breakOut)

    val customerSegments: List[CustomerSegment] =
      if (isAllowedCustomerSegmentsRestriction) {
        br.customerSegments.map(cs => CustomerSegment(cs.languageId, cs.countryCode, mapVipLevel(cs.vipLevel)))(breakOut)
      } else Nil

    RestrictionEntryModel(periodIntervals, br.bookOn, br.minAdvance, br.maxAdvance, customerSegments)
  }

  def mapVipLevel(vipLevel: Option[Int]): Option[models.enums.VipLevelType] = vipLevel match {
    case None => None
    case Some(x) => Option(models.enums.VipLevelType.getFromValue(x))
  }

  def buildPromotionRestrictionInput(restriction: Option[ProtoPromoRestriction]): Option[RestrictionEntryModel] =
    restriction.map { r =>
      val periodIntervals: List[PeriodIntervalInput] =
        r.bookingIntervals.map(bi => PeriodIntervalInput(bi.periodType.value, bi.from, bi.to))(breakOut)
      val customerSegments: List[CustomerSegment] =
        r.customerSegments.map(cs => CustomerSegment(cs.languageId, cs.countryCode, mapVipLevel(cs.vipLevel)))(breakOut)
      RestrictionEntryModel(periodIntervals,
                            r.bookOn,
                            r.minAdvance,
                            r.maxAdvance,
                            customerSegments,
                            r.minRooms,
                            r.minNight)
    }

  def buildRestriction(restrictionEntry: Option[RestrictionEntryModel]): Option[Restriction] =
    restrictionEntry.map { book =>
      val bookDate = book.bookingIntervals.find(_.periodType == TimeTypes.Day)
      val bookTime = book.bookingIntervals.find(ptt => ptt.periodType == TimeTypes.Time)
      Restriction(
        bookFrom = bookDate.map(bdf => new DateTime(bdf.from)),
        bookTo = bookDate.map(bdt => new DateTime(bdt.to)),
        bookTimeFrom = bookTime.map(_.from),
        bookTimeTo = bookTime.map(_.to),
        bookOn = book.bookOn,
        minAdvance = book.minAdvance,
        maxAdvance = book.maxAdvance,
        customerSegments = book.customerSegments,
        minNights = book.minNights,
        minRooms = book.minRooms,
      )
    }

  def buildRoomType(roomInfo: ChannelRoomRate, roomType: Option[RoomType]): RoomTypeEntry = RoomTypeEntry(
    roomTypeId = roomInfo.roomTypeId,
    maxOccupancy = roomType.map(_.maxOccupancy).getOrElse(0),
    maxExtraBed =
      0, // currently OTA does not support max extra bed, once it does will use the following logic: roomType.flatMap(_.maxExtrabed).getOrElse(0)
    maxChildrenInRoom = roomType.flatMap(_.maxChildren).getOrElse(0),
    isDayUse = false, // dont have this in OTA. Default as false
    cutOffDays = 0, // dont have this in OTA. Default as 0 (Regular allotment cut off days)
    gaCutOffDays = 0, // dont have this in OTA. Default as false (Guaranteed allotment cut off days)
    dmcRoomId = roomType.flatMap(_.supplierRoomId),
  )

  def calculateLevelByCurrency(hotelCurrencyCode: String,
                               countryCurrencyCode: String,
                               taxLevelInCountryCurrency: List[TaxPrototypeLevel],
                               isAmount: Boolean)(implicit ctx: YplContext): List[TaxPrototypeLevel] =
    TaxUtil.calculateLevelByCurrency(hotelCurrencyCode, countryCurrencyCode, taxLevelInCountryCurrency, isAmount)

  // Build taxes from all the taxes rooms
  // The reason we need to go through all the room is because there are taxes at room level
  def buildTaxInfo(taxType: TaxType,
                   isConfigProcessingFees: Boolean,
                   channelRoomRates: Seq[ChannelRoomRate],
                   isBcomFixTaxAmountApplyToPB: Boolean,
                   isApplyMOHUGdsCommissionFee: Boolean,
                   mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings,
                   taxPrototypeLevelList: Option[TaxPrototypeLevelMap],
                   countryCurrency: Option[String],
                   hotel: HotelMeta,
                   supplierId: Int)(implicit ctx: YplContext): TaxInfo = {

    val isTaxAndFeeV2ExistedInAllPrices: Boolean = channelRoomRates
      .flatMap(_.rateCategories.flatMap(_.dailyPrices.flatMap(_.prices.map(_.taxAndFeeV2.nonEmpty))))
      .forall(t => t)
    val isNewChargeTypeTaxAppliedExp: Boolean = isEnableTaxV2Exp(isTaxAndFeeV2ExistedInAllPrices, hotel, supplierId)
    def getTaxPrototypeInfo(taxPrototypeIdOp: Option[Int],
                            taxPrototypeLevelMapOp: Option[TaxPrototypeLevelMap],
                            hotelCurrency: Option[String],
                            isAmount: Boolean): Option[TaxPrototypeInfo] = for {
      taxPrototypeId <- taxPrototypeIdOp
      taxPrototypeLevelMap <- taxPrototypeLevelMapOp
    } yield {
      val taxLevelInCountryCurrency: List[TaxPrototypeLevel] =
        if (isNewChargeTypeTaxAppliedExp) {
          taxPrototypeLevelMap.taxPrototypeLevelMapV2
            .get(taxPrototypeId)
            .map { taxLevelListV2 =>
              taxLevelListV2.data.map(tl =>
                TaxPrototypeLevel(tl.level, tl.rateStart, tl.rateEnd, tl.taxValue, tl.isAmount.getOrElse(false)))(
                breakOut): List[TaxPrototypeLevel]
            }
            .getOrElse(List.empty)
        } else {
          taxPrototypeLevelMap.taxPrototypeLevelMap
            .get(taxPrototypeId)
            .map { taxLevelList =>
              taxLevelList.data.map(tl => TaxPrototypeLevel(tl.level, tl.rateStart, tl.rateEnd, tl.taxValue))(
                breakOut): List[TaxPrototypeLevel]
            }
            .getOrElse(List.empty)
        }
      val taxLevel = (hotelCurrency, countryCurrency) match {
        case (Some(hotelCurrencyCode), Some(countryCurrencyCode)) =>
          calculateLevelByCurrency(hotelCurrencyCode, countryCurrencyCode, taxLevelInCountryCurrency, isAmount)
        case _ =>
          // this is considered as invalid condition. then not return this tax prototype level set
          List.empty
      }
      TaxPrototypeInfo(taxLevel)
    }
    val taxBuffer = new ListBuffer[(TaxIDWithProTypeID, Tax)]()

    channelRoomRates.foreach { chrr =>
      chrr.rateCategories.foreach { r =>
        r.dailyPrices.foreach { dp =>
          dp.prices.foreach { p =>
            val hotelCurrency = chrr.currencyCode
            if (isNewChargeTypeTaxAppliedExp) {
              p.taxAndFeeV2.foreach { taxV2: ProtoTaxAndFeeV2 =>
                if (shouldApplyIfItIsSaudiUmrahGovTax(taxV2.taxPrototypeId,
                                                      isApplyMOHUGdsCommissionFee,
                                                      mohuGdsCommissionFeeSettings)) {
                  taxBuffer += buildTaxLegacyV2(
                    taxV2,
                    isBcomFixTaxAmountApplyToPB,
                    mohuGdsCommissionFeeSettings,
                    getTaxPrototypeInfo(taxV2.taxPrototypeId, taxPrototypeLevelList, chrr.currencyCode, taxV2.isAmount),
                    hotelCurrency,
                  )
                }
              }
            } else {
              p.taxAndFee.foreach { tax: ProtoTaxAndFee =>
                if (shouldApplyIfItIsSaudiUmrahGovTax(tax.taxPrototypeId,
                                                      isApplyMOHUGdsCommissionFee,
                                                      mohuGdsCommissionFeeSettings)) {
                  taxBuffer += buildTaxLegacy(
                    tax,
                    isBcomFixTaxAmountApplyToPB,
                    mohuGdsCommissionFeeSettings,
                    getTaxPrototypeInfo(tax.taxPrototypeId, taxPrototypeLevelList, chrr.currencyCode, tax.isAmount),
                    countryCurrency,
                    hotelCurrency,
                  )
                }
              }
            }
          }
        }
      }
    }

    TaxInfo(hotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = isConfigProcessingFees),
            taxes = taxBuffer.toMap)
  }

  def buildTaxLegacy(tax: ProtoTaxAndFee,
                     isBcomFixTaxAmountApplyToPB: Boolean,
                     mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings,
                     taxPrototypeInfo: Option[TaxPrototypeInfo],
                     countryCurrency: Option[Currency],
                     hotelCurrency: Option[Currency])(implicit ctx: YplContext): (TaxIDWithProTypeID, Tax) = {
    val taxId: TaxID = tax.taxId
    val taxProtoTypeId: TaxProtoTypeID = tax.taxPrototypeId.getOrElse(0)

    val applyTo =
      if (isBcomFixTaxAmountApplyToPB && tax.isAmount && tax.applyTo == ApplyTo.PerBook) ApplyTo.PerRoomPerBook
      else tax.applyTo

    val applyTaxOver =
      if (mohuGdsCommissionFeeSettings.targetTaxPrototypeIdSet.contains(taxProtoTypeId)) Some(ApplyTaxOver.SaleEx)
      else None

    val taxValue =
      if (tax.isAmount && tax.applyType.isVariableTax)
        convertCurrency(amount = tax.value, from = countryCurrency.getOrElse(""), to = hotelCurrency.getOrElse(""))
      else tax.value

    val taxData = Tax(
      id = taxId,
      applyTo = applyTo,
      isAmount = tax.isAmount,
      isFee = tax.isFee,
      isTaxable = tax.isTaxable,
      value = taxValue,
      option = ChargeOption.getFromValue(tax.applyType.value),
      protoTypeId = taxProtoTypeId,
      taxPrototypeInfo = taxPrototypeInfo,
      applyOver = applyTaxOver,
    )
    ((taxId, taxProtoTypeId), taxData)
  }

  def buildTaxLegacyV2(taxV2: ProtoTaxAndFeeV2,
                       isBcomFixTaxAmountApplyToPB: Boolean,
                       mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings,
                       taxPrototypeInfo: Option[TaxPrototypeInfo],
                       hotelCurrency: Option[Currency])(implicit ctx: YplContext): (TaxIDWithProTypeID, Tax) = {
    val taxId: TaxID = taxV2.taxId
    val taxProtoTypeId: TaxProtoTypeID = taxV2.taxPrototypeId.getOrElse(0)

    val applyTo =
      if (isBcomFixTaxAmountApplyToPB && taxV2.isAmount && taxV2.applyTo == ApplyTo.PerBook) ApplyTo.PerRoomPerBook
      else taxV2.applyTo

    val applyTaxOver =
      if (mohuGdsCommissionFeeSettings.targetTaxPrototypeIdSet.contains(taxProtoTypeId)) Some(ApplyTaxOver.SaleEx)
      else taxV2.taxApplyBreakdownType match {
        case Some(ProtoTaxApplyBreakdownType.NetEx) => Some(ApplyTaxOver.NetEx)
        case Some(ProtoTaxApplyBreakdownType.MarginCommission) => Some(ApplyTaxOver.MarginCommission)
        case _ => None
      }

    val taxValue =
      if (taxV2.isAmount && taxV2.taxCurrency.exists(_.nonEmpty) && taxV2.applyType.isVariableTax)
        convertCurrency(amount = taxV2.value, from = taxV2.taxCurrency.getOrElse(""), to = hotelCurrency.getOrElse(""))
      else taxV2.value

    val taxData = Tax(
      id = taxId,
      applyTo = applyTo,
      isAmount = taxV2.isAmount,
      isFee = taxV2.isFee,
      isTaxable = taxV2.isTaxable,
      value = taxValue,
      option = ChargeOption.getFromValue(taxV2.applyType.value),
      protoTypeId = taxProtoTypeId,
      taxPrototypeInfo = taxPrototypeInfo,
      applyOver = applyTaxOver,
      whomToPay = taxV2.whomToPay.map(w => WhomToPayType.getWhomToPayType(w)),
      orderNumber = taxV2.orderNumber,
      taxLevelCalculationType =
        taxV2.taxLevelCalculationType.map(tax => TaxLevelCalculationType.getFromValue(tax.value)),
      valueMethod = taxV2.valueMethod.map(v => ValueMethodType.getValueMethodType(v.value)),
      valueCalculationMethodType =
        taxV2.valueCalculationMethodType.map(v => ValueCalculationMethodType.getValueCalculationMethodType(v.value)),
      geoId = taxV2.geoId,
      geoType = taxV2.geoType.map(g => GeoType.getGeoType(g.value)),
    )
    ((taxId, taxProtoTypeId), taxData)
  }

  def findCommission(commissions: Seq[YplCommission],
                     channelId: ChannelId,
                     inventoryType: InventoryType,
                     languageId: LanguageId,
                     filterZeroCommission: Boolean = false,
                     isBcom: Boolean = false,
                     supplierCommissionMapping: SupplierCommissionMapping = Map.empty,
                     dateMillis: Long = 0,
                     rateType: RateType,
                     agxCommissionAdjustment: YplAGXCommissionAdjustmentWrapper =
                       YplAGXCommissionAdjustmentWrapper.noAgxCommissionAdjustment,
                     isAgxEligibleDmc: Boolean = false,
                     hotelPrice: YplCriteria,
                     allowAgxLogging: Boolean = false,
                     paymentModel: Option[PaymentModel] = None)(implicit ctx: YplContext): Option[Commissions] = {

    val isApplyAgx: Boolean = AGXCommissionHelper.isApplyAgx(
      isAgxEligible = isAgxEligibleDmc,
      supplierId = Some(hotelPrice.supplierId),
      channelId = channelId,
      paymentModelOpt = paymentModel,
    )
    val isAgxEligibleRateChannel = AGXCommissionHelper.checkFenceRateChannel(agxCommissionAdjustment,
                                                                             Some(hotelPrice.supplierId),
                                                                             channelId,
                                                                             inventoryType,
                                                                             isAgxEligibleDmc)

    val date: DateTime = new DateTime(dateMillis).withTimeAtStartOfDay
    val agx =
      if (isApplyAgx && isAgxEligibleRateChannel)
        AGXCommissionHelper.getAgxCommissionAdjustment(date, agxCommissionAdjustment.commissionAdjustmentOp)
      else YplAGXCommission.noAgxCommission

    if (isBcom && !ctx.experimentContext.isUserB(YplExperiments.BCOM_RATE_COMMISSION)) {
      Some(Commissions(supplierCommissionMapping.getOrElse(DMC.BCOM, bcomDefaultCommission), agx))
    } else {
      val commission = commissions
        .find(comm => comm.channelID == channelId && comm.languageID.contains(languageId))
        .orElse(commissions.find(comm => comm.channelID == channelId && comm.languageID.contains(0)))
        .orElse(commissions.find(comm => comm.channelID == 0 && comm.languageID.contains(languageId)))
        .orElse(commissions.find(comm => comm.channelID == 0 && comm.languageID.contains(0)))
      val value = commission.map(_.value) match {
        case Some(commValue) => commValue
        case None if isBcom => supplierCommissionMapping.getOrElse(DMC.BCOM, bcomDefaultCommission)
        case None => 0d
      }
      val contractedCommission: Double = commission.flatMap(_.contractedCommission).getOrElse(0d)
      val wholesaleCampaignCommission: Double =
        commission.flatMap(_.wholesaleCampaignCommission).getOrElse(DEFAULT_CAMPAIGN_COMMISSION)
      val isWholesaleCampCommAvailable: Boolean = commission.exists(_.wholesaleCampaignCommission.isDefined)

      val commissionPercent: Option[Commissions] =
        if (isWholesaleCampCommAvailable) { // Wholesale
          val newCommission = wholesaleCampaignCommission + contractedCommission
          val wholesaleMetadata = WholesaleMetadata(wholesaleCampaignCommission)
          Some(
            Commissions(CommissionConverter.convertCommission(rateType, newCommission),
                        YplAGXCommission.noAgxCommission,
                        wholesaleMetadata,
                        value,
                        contractedCommission))
        } else if (isApplyAgx) { // AGX
          if (contractedCommission == 0.0 && value != 0.0) {
            if (allowAgxLogging) {
              logger.warn(
                s"Contracted commission is zero, while commission value is $value, " +
                  s"hotelID: ${hotelPrice.hotelId}, " +
                  s"supplierId: ${hotelPrice.supplierId}, " +
                  s"checkInMillis: ${hotelPrice.checkInMillis}, " +
                  s"lengthOfStay: ${hotelPrice.lengthOfStay}, " +
                  s"searchId: ${ctx.request.searchId}",
              )
              DFAgxCommissionAdjustmentsMessage(
                contractedCommission,
                value,
                Some(hotelPrice.hotelId),
                Some(hotelPrice.supplierId),
              )(ctx.request).sendAsync(hadoopLoggingSettings.apiKey)
            }
            Some(
              Commissions(value, YplAGXCommission.noAgxCommission, ZERO_WHOLESALE_METADATA, value, contractedCommission),
            ) // fallback to markup
          } else {
            val newCommission: Double = agx.payAsYouGoCommission + contractedCommission
            Some(
              Commissions(CommissionConverter.convertCommission(rateType, newCommission),
                          agx,
                          ZERO_WHOLESALE_METADATA,
                          value,
                          contractedCommission))
          }
        } else Some(
          Commissions(value, YplAGXCommission.noAgxCommission, ZERO_WHOLESALE_METADATA, value, contractedCommission),
        ) // markup

      if (filterZeroCommission) {
        commissionPercent.filter(isValidCommission)
      } else {
        commissionPercent
      }
    }

  }

  protected def filterRateCategory(rc: ProtoRateCategory,
                                   channelRoom: ChannelRoomRate,
                                   reqOcc: YplReqOccByHotelAgePolicy)(implicit ctx: YplContext): Boolean = {
    implicit val request = ctx.request
    (rc.remainingRoom > 0 &&
    rc.remainingRoom >= reqOcc.occ.rooms) ||
    request.returnZeroAllotment && applyPreFilterForZeroAllotment(
      roomTypeIdOpt = Some(channelRoom.roomTypeId),
      rateCategoryIdOpt = None,
      filterZeroAllotmentOpt = ctx.request.preFiltersZeroAllotment,
    )
  }

  protected def validateBookingRestriction(restriction: Option[Restriction],
                                           hotelGMT: GmtOffset,
                                           fences: Set[YplRateFence],
                                           supplierId: SupplierId)(implicit ctx: YplContext): Boolean = {

    implicit val request = ctx.request

    val bookFrom = restriction.flatMap(_.bookFrom)
    val bookTo = restriction.flatMap(_.bookTo)
    val bookTimeFrom = restriction.flatMap(_.bookTimeFrom)
    val bookTimeTo = restriction.flatMap(_.bookTimeTo)
    val bookOn = restriction.flatMap(_.bookOn)
    val minAdv = restriction.flatMap(_.minAdvance)
    val maxAdv = restriction.flatMap(_.maxAdvance)

    val bookDayValidation = validateBookDay(bookFrom, bookTo, hotelGMT)
    val bookTimeValidation = validateBookTimeOTA(bookTimeFrom, bookTimeTo, hotelGMT)
    val bookOnValidation = validateBookOn(bookOn)
    val minMaxAdvancePurchaseValidation = validateMinMaxAdvancePurchase(minAdv, maxAdv, hotelGMT, supplierId)

    bookDayValidation && bookTimeValidation && bookOnValidation && minMaxAdvancePurchaseValidation
  }

  def filterBookableOtaRooms(room: YplRoomEntry, dispatchChannels: YplDispatchChannels)(implicit
    ctx: YplContext): Boolean = {
    implicit val request = ctx.request
    room.isBookable && dispatchChannels.contains(room.channel)
  }

  private[pricing] def filterPriceEntryByOccupancyAndOccupancyModel(price: PriceEntry,
                                                                    occupancy: Int,
                                                                    occupancyModel: OccupancyModel,
                                                                    reqOcc: YplReqOccByHotelAgePolicy,
                                                                    roomTypeEntry: RoomTypeEntry): Boolean = {
    val isMaxOccupancyValid = price.occupancy <= roomTypeEntry.maxOccupancy
    if (occupancyModel == FullPatternLengthOfStay && isMaxOccupancyValid && !reqOcc.isFreeOcc)
      price.occupancy == occupancy
    else isMaxOccupancyValid
  }

  private[pricing] def generateExternalDataStr(rc: ProtoRateCategory): Option[String] = {
    val spapiRequestIdKey = "spapi-request-id"
    val spapiTimestampKey = "response-timestamp-ms"
    val spapiReplicateKey = "replicate"

    val externalDataFieldsToRemove = Set(spapiRequestIdKey, spapiTimestampKey, spapiReplicateKey)

    rc.supplierRateInfo.map(
      _.externalData
        .filterNot(item => externalDataFieldsToRemove.contains(item.field))
        .withFilter(item => !item.isExcludeForRoomUID.getOrElse(false))
        .map(_.value)
        .mkString("|"))
  }

  def buildSupplierStackedChannelInfo(stack: Seq[StackedChannelInfo]) = stack.map { stackedDiscountInfo =>
    val stackedChannelDiscounts = stackedDiscountInfo.stackedChannelDiscounts.map { stackedChannelDiscount =>
      StackedChannelDiscount(
        channelId = stackedChannelDiscount.channelId,
        discountPercent = stackedChannelDiscount.discountPercent,
      )
    }
    stackedDiscountInfo.baseChannelId -> stackedChannelDiscounts
  }.toMap

  def getCxlLoadedFromProtobuf(cxl: Option[String]): (String, RoomDataChangeTracker) = cxl match {
    case Some(cxl) => (cxl, RoomDataChangeTracker(List(RoomDataHistory(RoomDataStep.BaseStep.entryName, cxl))))
    case _ =>
      val defaultCxl = CancellationCode.NON_REFUNDABLE
      (defaultCxl, RoomDataChangeTracker(List(RoomDataHistory(RoomDataStep.FallBackNonRefundable.entryName, defaultCxl))))
  }

  def convertCurrency(amount: Double, from: Currency, to: Currency)(implicit ctx: YplContext): Double =
    TaxUtil.convertCurrency(amount, from, to)

  protected def isEnableTaxV2Exp(hasTaxV2: Boolean, hotel: HotelMeta, supplierId: Int)(implicit
    ctx: YplContext): Boolean = TaxUtil.isEnableTaxV2Exp(hasTaxV2, hotel, supplierId)
}

// scalastyle:on
