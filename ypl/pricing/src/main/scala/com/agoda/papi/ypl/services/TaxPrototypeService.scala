package com.agoda.papi.ypl.services

import com.agoda.finance.tax.models.TaxPrototypeLevel
import com.agoda.finance.tax.services.tax.GraduatedTaxCalculator
import com.agoda.papi.enums.room.RateType
import com.agoda.papi.pricing.pricecalculation.models.tax.DailyTaxes
import com.agoda.papi.pricing.pricecalculation.utils.CommonTaxConverter._
import com.agoda.papi.ypl.commission.service.CommissionService
import com.agoda.papi.ypl.models.CommonTaxConverter.toCommonReqOccByHotelAgePolicy
import com.agoda.papi.ypl.models.api.request.YplAGXCommission
import com.agoda.papi.ypl.models.pricing.proto.Commission
import com.agoda.papi.ypl.models.pricing.{BookingPriceBreakdown, RoomPriceInfo}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{Supp<PERSON><PERSON>d, <PERSON><PERSON><PERSON><PERSON>l, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, YplReqOccByHotelAgePolicy}
import com.agoda.papi.ypl.pricing.PriceCalculation
import com.agoda.papi.ypl.services.TaxPrototypeService.excludeWholesaleOrAgx
import com.agoda.utils.flow.{PropertyContext, PropertyContextImpl}
import models.consts.ABTest
import models.flow.Variant

trait TaxPrototypeService {
  def assumeTaxValue(rateType: RateType,
                     value: Double,
                     commission: Commission,
                     dailyTaxes: DailyTaxes,
                     assumeChannelDiscount: Double,
                     isCleanedUpHospitalityTax: Boolean): DailyTaxes

  def correctTaxPrototypeLevel(yplHotel: YPLHotel, supplierIdToContextMap: Map[SupplierId, YplContext]): YPLHotel
}

object TaxPrototypeService {
  val excludeWholesaleOrAgx = true
}

/**
  * Prototype tax calculation based on assumed sellEx proposed by Sungho Yoon
  * https://agoda.atlassian.net/browse/NEP-8976
  *
  * The assumedSellEx is calculated based on each loaded rate type
  * Please check this for more detail calculation
  * https://agoda.atlassian.net/wiki/display/FINPROD/Graduated+Tax
  */
trait TaxPrototypeServiceImpl extends TaxPrototypeService {
  self: PriceCalculation with CommissionService =>

  /**
    * End rate is adjusted by adding by percentage of tax level of
    * itself.
    * To get target level, finding the first level where the rate value
    * is less then adjusted end rate
    * check this for more details: https://agoda.atlassian.net/wiki/display/FINPROD/Graduated+Tax
    */
  private def getAdjustedLevelForInclusive(value: Double,
                                           levels: List[TaxPrototypeLevel],
                                           assumedChannelDiscount: Double): Double =
    if (value < 0 || levels.isEmpty) {
      // negative load rate value or no tax level provided (should not be possible)
      0.0
    } else {
      val sortedLevels = levels.sortBy(_.taxValue)
      val normalisedAssumedChannelDiscount = assumedChannelDiscount * 0.01

      // adjusted rateEnd by adding percentage of tax also check for double max value
      def adjusted(rateEnd: Double, tax: Double): Double = {
        val normalisedTax = tax * 0.01
        val adjustedRateEnd = rateEnd * (1.0 + normalisedTax)
        adjustedRateEnd
      }

      // find first level that load rate value less than rateEnd plus tax percentage
      sortedLevels.find { level =>
        value * (1.0 - normalisedAssumedChannelDiscount) < adjusted(level.rateEnd, level.taxValue)
      } match {
        case Some(level) => level.taxValue
        case _ =>
          sortedLevels.last.taxValue // vary large load rate value so highest tax level, very likely highest tax value too
      }
    }

  /**
    * AssumedSellEx = NetIn*(1 + markup)*(1 - assumeChannelDiscount)/(1 + tax)
    * AssumedSellEx = NetEx*(1 + markup)*(1 - assumeChannelDiscount)
    * AssumedSellEx = SellIn            *(1 - assumeChannelDiscount)/(1 + tax)
    * AssumedSellEx = SellEx            *(1 - assumeChannelDiscount)
    */
  private[services] def assumeSellEx(value: Double,
                                     markup: Commission,
                                     tax: Double,
                                     assumeChannelDiscount: Double): Double = {
    val normalisedMarkup = markup * 0.01
    val normalisedTax = tax * 0.01
    val normalisedChannelDiscount = assumeChannelDiscount * 0.01
    value * (1.0 + normalisedMarkup) * (1.0 - normalisedChannelDiscount) / (1.0 + normalisedTax)
  }

  /**
    * Calculate AssumedSellEx based on loaded rate
    *
    * @return assumed sell ex
    */
  // Todo: Cannot find any usage, should be removed ?
  private[services] def calculateAssumedSellEx(rateType: RateType,
                                               value: Double,
                                               commission: Commission,
                                               taxPrototypeLevels: List[TaxPrototypeLevel],
                                               assumeChannelDiscount: Double = 0): Double = rateType match {
    case RateType.NetInclusive =>
      // val tax = getAdjustedLevelForInclusive(value, commission, taxPrototypeLevels)
      val tax = getAdjustedLevelForInclusive(value, taxPrototypeLevels, assumeChannelDiscount)
      assumeSellEx(value, commission, tax, assumeChannelDiscount)
    case RateType.NetExclusive =>
      // Exclusive, Adjusted Tax = 0.0
      assumeSellEx(value, commission, 0.0, assumeChannelDiscount)
    case RateType.SellInclusive =>
      // Sell, Adjusted Commission/Markup = 0.0
      val tax = getAdjustedLevelForInclusive(value, taxPrototypeLevels, assumeChannelDiscount)
      assumeSellEx(value, 0.0, tax, assumeChannelDiscount)
    case RateType.SellExclusive => assumeSellEx(value, 0.0, 0.0, assumeChannelDiscount)
    case _ => 0.0 // For Unknown RateType, no assume
  }

  private[services] def getTaxPrototypeLevelFromSellEx(
    sellEx: Double,
    taxPrototypeLevels: List[TaxPrototypeLevel]): Option[TaxPrototypeLevel] =
    taxPrototypeLevels.find { taxPrototypeLevel =>
      sellEx >= taxPrototypeLevel.rateStart && sellEx < taxPrototypeLevel.rateEnd
    }

  def assumeTaxValue(rateType: RateType,
                     value: Double,
                     commission: Commission,
                     dailyTaxes: DailyTaxes,
                     assumeChannelDiscount: Double = 0.0,
                     isCleanedUpHospitalityTax: Boolean): DailyTaxes = toPriceCalcDailyTaxes(
    GraduatedTaxCalculator.assumeTaxValue(
      toCommonRateType(rateType),
      value,
      commission,
      toCommonDailyTaxes(dailyTaxes),
      assumeChannelDiscount,
      isCleanedUpHospitalityTax = isCleanedUpHospitalityTax,
    ))

  def correctTaxPrototypeLevel(hotel: YPLHotel, supplierIdToContextMap: Map[SupplierId, YplContext]): YPLHotel = {
    val newRooms = hotel.rooms.groupBy(_.supplierId).flatMap { case (supplierId, rooms) =>
      supplierIdToContextMap
        .get(supplierId)
        .map { ctx =>
          val fixMarriottSurchargeExp: Boolean = supplierId == DMC.Marriott &&
            ctx.experimentContext.determineVariant(PropertyContext(hotel.id, hotel.cityId, hotel.countryId),
                                                   ABTest.MARRIOTT_SURCHARGE_ISSUE) == Variant.B
          val hotelWithTheOneSupply = hotel.copy(rooms = rooms)
          correctTaxPrototypeLevel(hotelWithTheOneSupply, fixMarriottSurchargeExp)(ctx).rooms
        }
        .getOrElse(rooms)
    }
    hotel.copy(rooms = newRooms.toList)
  }

  private[services] def getIsPull(room: YPLRoom, hotel: YPLHotel): Boolean =
    if (room.yplRoomEntry.`isLT-1349B`) {
      hotel.suppliers.get(room.supplierId).exists(_.isPull)
    } else {
      hotel.isPull
    }

  private def correctTaxPrototypeLevel(hotel: YPLHotel, fixMarriottSurchargeExp: Boolean)(implicit
    ctx: YplContext): YPLHotel = {
    implicit val propertyContext: PropertyContext = PropertyContextImpl(hotel.id, hotel.cityId, hotel.countryId)
    val hotelHasTaxProtoypeLevel = hotel.rooms
      .exists(_.prices.exists(_.dailyTaxes.taxes.exists(_.tax.taxPrototypeInfo.exists(_.taxPrototypeLevels.nonEmpty))))
    if (hotelHasTaxProtoypeLevel) {
      val reqOcc = YplReqOccByHotelAgePolicy(
        ctx.request.occ,
        hotel.reqOcc.agePolicy,
        hotel.reqOcc.ignoreRequestedNumberOfRoomsForNha,
        hotel.reqOcc.childAgeRange,
      )
      val isBcomFixTaxAmountApplyToPB = (hotel.supplierId == DMC.BCOM)

      val newRooms = hotel.rooms.map { room =>
        val priceForCorrectTax = room.dailyAssumeSellExForCalculateGraduatedTax()
        val isApplyTaxOnSellExForRoom = determineIfTaxAppliesOnSellEx(room, hotel)

        // correct room and extra bed price
        val newRoomPrices = room.roomAndExtraBedPrices.map { price =>
          val finalRoomSellEx = price.sellExclusive

          val (hasTaxUpdate, newTaxesWithValues) = {
            val result = GraduatedTaxCalculator.correctTaxes(priceForCorrectTax(price.date),
                                                             toCommonTaxesWithValues(price.dailyTaxes.taxes))
            (result._1, toPriceCalcTaxesWithValues(result._2))
          }

          if (hasTaxUpdate) {
            val (supplierFundedDiscountAmount, uspaDiscountAmount, uspaProgramId) =
              (price.supplierFundedDiscountAmount, price.uspaDiscountAmount, price.uspaProgramId)
            // we need to lock rate load type to sell Ex so that the tax prototpe level is not adjusted after recalculation
            val newRateLoadedType = RateType.SellExclusive
            // then we need new rateLoadedType amount in sellEx
            // as input parameter take valueWithChannelDiscount, need to add promotionDiscount
            val newValueWithChannelDiscount = finalRoomSellEx + price.promotionDiscount

            // For booking breakdown logging
            val bookingPriceBreakdown = price.getUpdatedPriceBreakdown()

            val roomPriceInfo = RoomPriceInfo(
              channel = room.channel,
              rateType = newRateLoadedType,
              originalRateType = room.originalRateType,
              roomOcc = room.occ,
              processingFeePercent = room.processingFeePercent,
            )
            if (ctx.experimentContext.isPropertyB(propertyContext, ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION)) {
              calculatePriceForPriceCalculationRefactor(
                paymentModel = room.paymentModel,
                date = price.date,
                commissionPercent = room.marginPercentage,
                agxCommission = YplAGXCommission.noAgxCommission,
                commissionExcludingWholesaleOrAgx = getCommissionForPriceCalculation(
                  room.yplRoomEntry.commissionHolder,
                  price.date,
                  room.occ.occupancy,
                  room.yplRoomEntry.isAgodaAgency,
                  room.yplRoomEntry.applicableMORPCandidateRoomParameters,
                  room.originalRateType,
                  room.rateType,
                  excludeWholesaleOrAgx,
                ), // Even if we pass it will not be used as rateloadtype is sellExclusive but need to pass properly to avoid inconsistency
                hotelTaxInfo = hotel.hotelTaxInfo,
                dailyTaxes = price.dailyTaxes.copy(taxes = newTaxesWithValues),
                reqOcc = toCommonReqOccByHotelAgePolicy(reqOcc),
                chargeType = price.chargeType,
                quantity = price.quantity,
                applyType = price.applyType,
                chargeOption = price.chargeOption,
                promoDiscount = price.promotionDiscount,
                valueWithChannelDiscount = newValueWithChannelDiscount,
                discountMessages = price.discountMessages,
                roomPriceInfo = roomPriceInfo,
                supplierId = getSupplierIdForRoomOrHotel(ctx, room, hotel),
                subChargeType = price.subChargeType,
                roomNo = price.roomNumber,
                supplierContractedCommissionFromCommissionHolder =
                  room.yplRoomEntry.commissionHolder.supplierContractedCommission,
                channelDiscountBreakdowns = price.channelDiscounts,
                resellRefSell = price.resellRefSell,
                currentBreakdownStep =
                  BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, false, price.currentBreakdownStep),
                bookingPriceBreakdown = bookingPriceBreakdown,
                apmPriceAdjustmentDetail = price.apmPriceAdjustmentDetail,
                apmCommissionDiscountPercent = price.apmCommissionDiscountPercent,
                childAgeRangeId = price.childAgeRangeId,
                hotelId = hotel.id,
                chainId = hotel.chainId,
                countryId = hotel.countryId,
                cityId = hotel.cityId,
                supplierFundedDiscountAmount = supplierFundedDiscountAmount,
                uspaDiscountAmount = uspaDiscountAmount,
                uspaProgramId = uspaProgramId,
                isApplyTaxOnSellEx = isApplyTaxOnSellExForRoom,
                lengthOfStay = ctx.request.lengthOfStay,
                storefrontId = ctx.request.cInfo.storeFront.getOrElse(0),
                whitelabelId = ctx.request.whitelabelSetting.whitelabelID,
                isThirdPartySupplier = ctx.isThirdParty(hotel.supplierId),
                applyTaxOverHelper = ctx.request.applyTaxOverHelper,
                experimentContext = ctx.experimentContext,
              )
            } else {
              calculatePrice(
                paymentModel = room.paymentModel,
                date = price.date,
                commissionPercent = room.marginPercentage,
                agxCommission = YplAGXCommission.noAgxCommission,
                commissionExcludingWholesaleOrAgx = getCommissionForPriceCalculation(
                  room.yplRoomEntry.commissionHolder,
                  price.date,
                  room.occ.occupancy,
                  room.yplRoomEntry.isAgodaAgency,
                  room.yplRoomEntry.applicableMORPCandidateRoomParameters,
                  room.originalRateType,
                  room.rateType,
                  excludeWholesaleOrAgx,
                ), // Even if we pass it will not be used as rateloadtype is sellExclusive but need to pass properly to avoid inconsistency
                hotelTaxInfo = hotel.hotelTaxInfo,
                dailyTaxes = price.dailyTaxes.copy(taxes = newTaxesWithValues),
                reqOcc = reqOcc,
                chargeType = price.chargeType,
                quantity = price.quantity,
                applyType = price.applyType,
                chargeOption = price.chargeOption,
                promoDiscount = price.promotionDiscount,
                valueWithChannelDiscount = newValueWithChannelDiscount,
                discountMessages = price.discountMessages,
                roomPriceInfo = roomPriceInfo,
                supplierId = getSupplierIdForRoomOrHotel(ctx, room, hotel),
                subChargeType = price.subChargeType,
                roomNo = price.roomNumber,
                supplierContractedCommissionFromCommissionHolder =
                  room.yplRoomEntry.commissionHolder.supplierContractedCommission,
                channelDiscountBreakdowns = price.channelDiscounts,
                resellRefSell = price.resellRefSell,
                currentBreakdownStep =
                  BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, false, price.currentBreakdownStep),
                bookingPriceBreakdown = bookingPriceBreakdown,
                apmPriceAdjustmentDetail = price.apmPriceAdjustmentDetail,
                apmCommissionDiscountPercent = price.apmCommissionDiscountPercent,
                childAgeRangeId = price.childAgeRangeId,
                hotelId = hotel.id,
                chainId = hotel.chainId,
                countryId = hotel.countryId,
                supplierFundedDiscountAmount = supplierFundedDiscountAmount,
                uspaDiscountAmount = uspaDiscountAmount,
                uspaProgramId = uspaProgramId,
              )(isBcomFixTaxAmountApplyToPB)
            }
          } else {
            price
          }
        }

        // correct surcharge price
        val newSurchargePrices = room.surchargePrices.map { surcharge =>
          val (hasTaxUpdate, _) = {
            val result = GraduatedTaxCalculator.correctTaxes(priceForCorrectTax(surcharge.date),
                                                             toCommonTaxesWithValues(surcharge.dailyTaxes.taxes))
            (result._1, toPriceCalcTaxesWithValues(result._2))
          }

          val recalSurcharge =
            for {
              surchargeDailyPrice <- surcharge.dailyPrice
              surchargeRateType <- surcharge.surchargeRateType
              surchargeEntry <- surcharge.surchargeEntry
              roomYplRoomEntry = room.yplRoomEntry
              roomTaxInfo <- room.taxInfo
              if (hasTaxUpdate)
            } yield
              if (ctx.experimentContext.isPropertyB(propertyContext, ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION)) {
                calculateSurchargeForPriceCalculationRefactor(
                  paymentModel = room.paymentModel,
                  dailyPrice = surchargeDailyPrice,
                  surchargeRateType = surchargeRateType,
                  surchargeEntry = surchargeEntry,
                  roomPrices = newRoomPrices.filter(_.isRoom),
                  taxInfo = roomTaxInfo,
                  room = roomYplRoomEntry,
                  reqOcc = reqOcc,
                  isPull = getIsPull(room, hotel),
                  supplierId = hotel.supplierId,
                  supplierContractedCommission = None,
                  hotelId = hotel.id,
                  chainId = hotel.chainId,
                  countryId = hotel.countryId,
                  cityId = hotel.cityId,
                  fixMarriottSurchargeExp = fixMarriottSurchargeExp,
                ).getOrElse(surcharge)
              } else {
                calculateSurcharge(
                  paymentModel = room.paymentModel,
                  dailyPrice = surchargeDailyPrice,
                  surchargeRateType = surchargeRateType,
                  surchargeEntry = surchargeEntry,
                  roomPrices = newRoomPrices.filter(_.isRoom),
                  taxInfo = roomTaxInfo,
                  room = roomYplRoomEntry,
                  reqOcc = reqOcc,
                  isPull = getIsPull(room, hotel),
                  supplierId = hotel.supplierId,
                  hotelId = hotel.id,
                  chainId = hotel.chainId,
                  countryId = hotel.countryId,
                  fixMarriottSurchargeExp = fixMarriottSurchargeExp,
                )(isBcomFixTaxAmountApplyToPB).getOrElse(surcharge)
              }

          recalSurcharge match {
            case Some(newSurcharge) => newSurcharge
            case _ => surcharge
          }
        }

        room.copy(prices = newRoomPrices ++ newSurchargePrices ++ room.nonRoomExtraBedSurchargePrices)
      }
      hotel.copy(rooms = newRooms)
    } else {
      hotel
    }
  }
  protected def getSupplierIdForRoomOrHotel(
    ctx: YplContext,
    room: YPLRoom,
    hotel: YPLHotel,
  ): SupplierId =
    if (ctx.experimentContext.isPropertyB(hotel.toPropertyContext, ABTest.ENABLE_ROOM_SUPPLIER_ID)) room.supplierId
    else hotel.supplierId

  private def determineIfTaxAppliesOnSellEx(room: YPLRoom, hotel: YPLHotel)(implicit ctx: YplContext): Boolean =
    ctx.isApplyTaxOnSellEx(
      room.supplierId,
      hotel.id,
      hotel.chainId,
      hotel.countryId,
      room.paymentModel.i,
      room.originalRateType,
    )

}
