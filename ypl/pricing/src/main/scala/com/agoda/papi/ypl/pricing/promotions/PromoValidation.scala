package com.agoda.papi.ypl.pricing.promotions

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.enums.room.DiscountType
import com.agoda.papi.ypl.models.consts.CampaignExtensionPeriod
import com.agoda.papi.ypl.models.consts.HotelPromotionDispatchConfig.aDayInMinute
import com.agoda.papi.ypl.models.consts.PulseDispatch.CampaignExtensionPeriods
import com.agoda.papi.ypl.models.enums.VipLevelType
import com.agoda.papi.ypl.models.pricing.proto.{AdditionalDispatchReason, DailyPrice, PromotionEntry}
import com.agoda.papi.ypl.models.{
  HotelMeta,
  PromotionTypeId,
  YplContext,
  YplExperiments,
  YplRateFence,
  YplRequest,
  YplRoomEntry,
}
import com.agoda.papi.ypl.pricing.{BLTPromotionSettings, VipSegmentExtraValidationHelper}
import com.agoda.papi.ypl.pricing.promotions.PromotionConstant._
import com.agoda.papi.ypl.services.{CidToOriginMapper, OriginManager}
import com.agoda.utils.flow.PropertyContext
import models.consts.ABTest
import org.joda.time.{DateTime, Days, LocalTime}

trait PromoValidation {

  protected val originManager: OriginManager
  protected val cidToOriginMapper: CidToOriginMapper
  protected val promotionEntryValidation: PromotionEntryValidation

  def checkSupportCustomerSegment(promotion: PromotionEntry,
                                  fence: YplRateFence,
                                  hotelMeta: HotelMeta,
                                  pulseCampaignMetadataByPromotionTypeId: Map[PromotionTypeId, PulseCampaignMetadata],
                                  isAboCidToOriginMappingEnabled: Boolean)(implicit
    ctx: YplContext): (Boolean, Option[AdditionalDispatchReason.Value]) = {
    val languageId = fence.language
    val vipSegments = getVipSegments(promotion)
    val nonVipSegments = promotion.customerSegments.filter(x =>
      x.vipLevel.isEmpty || x.vipLevel.exists(_.value == VipLevelType.UNDEFINED.value))
    val isValidVipSegment = vipSegments.isEmpty ||
      (ctx.experimentContext.isUserB(YplExperiments.CHECK_PROMOTION_VIP_CUSTOMER_SEGMENT) && hasValidVipSegment(
        promotion)(ctx.request, ctx))
    val customerOrigin =
      if (ctx.experimentContext.isUserB(YplExperiments.BLOCK_INT_YCS_PROMOS_BY_HOTEL_COUNTRY)) {
        hotelMeta.countryCode
      } else if (isAboCidToOriginMappingEnabled) {
        cidToOriginMapper.getOriginByCid(fence.cid).getOrElse(fence.origin)
      } else {
        originManager.getOriginByCidAndHotelCountry(Some(fence.cid), Some(hotelMeta.countryCode)).getOrElse(fence.origin)
      }
    val isValidLanguageAndOriginCustomerSegment = nonVipSegments.isEmpty || nonVipSegments.exists { cs =>
      cs.languageId.forall(_ == languageId) && cs.countryCode.forall(_.equalsIgnoreCase(customerOrigin))
    }

    val isPulseCampaignEligibleToEnableDispatchingOverrideToBeEveryCustomerSegment = () => {

      val pulseCampaignMetadata: Option[PulseCampaignMetadata] =
        pulseCampaignMetadataByPromotionTypeId.get(promotion.typeId)
      val doesHotelDisableEveryPulseCampaign = hotelMeta.pulseCampaignIdBlacklist == Seq(0)
      val isPulseCampaignInBlackList = pulseCampaignMetadata.exists { pulseCampaign =>
        hotelMeta.pulseCampaignIdBlacklist.contains(pulseCampaign.campaignTypeId)
      }
      !hotelMeta.isNorthAmericaHotel &&
      pulseCampaignMetadata.isDefined &&
      !ctx.request.isAllMseTraffic &&
      !doesHotelDisableEveryPulseCampaign &&
      !isPulseCampaignInBlackList &&
      ctx.experimentContext.isUserB(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_TO_BE_EVERY_CUSTOMER_SEGMENT)

    }

    val isSupportedCustomerSegment = isValidVipSegment && (
      isValidLanguageAndOriginCustomerSegment || {
        promotionEntryValidation.isValidByOverridingCustomerSegment(
          promotion,
          pulseCampaignMetadataByPromotionTypeId,
          customerOrigin,
          languageId,
        )
      }
      ||
      isPulseCampaignEligibleToEnableDispatchingOverrideToBeEveryCustomerSegment()
    )
    if (isSupportedCustomerSegment && !isValidLanguageAndOriginCustomerSegment) {
      (isSupportedCustomerSegment, Some(AdditionalDispatchReason.CustomerSegmentOverriding))
    } else {
      (isSupportedCustomerSegment, None)
    }
  }
  def fencePromotions(hotelMeta: HotelMeta,
                      rooms: List[YplRoomEntry],
                      bltPromotionSettings: BLTPromotionSettings,
                      pulseCampaignMetadataByPromotionTypeId: Map[PromotionTypeId, PulseCampaignMetadata])(implicit
    ctx: YplContext): List[YplRoomEntry] = {

    // BLT promotion setting null if experiment A
    val bltPromotionSettingsNullIFHotelMvpPromotions =
      Some(bltPromotionSettings).filter(_ => ctx.experimentContext.isUserB(YplExperiments.HOTEL_MVP_PROMOTIONS))

    def isFromRequiredBltCids(fence: YplRateFence) =
      bltPromotionSettingsNullIFHotelMvpPromotions.exists(_.allowCids.contains(fence.cid))

    def isPromoFromBlt(fence: YplRateFence, promotion: PromotionEntry) = promotion.customerSegments.exists { cs =>
      isFromRequiredBltCids(fence) && ((cs.countryCode, bltPromotionSettingsNullIFHotelMvpPromotions) match {
        case (Some(countryCode), Some(setting)) => setting.requiredPromotionCountryCodes.contains(countryCode)
        case _ => false
      })
    }

    val isAboCidToOriginMappingEnabled = ctx.experimentContext.isPropertyB(
      PropertyContext(hotelMeta.hotelId, hotelMeta.cityId, hotelMeta.countryId),
      ABTest.ABO_CID_TO_ORIGIN_MAPPER,
    )

    rooms.flatMap { room =>
      val (fencelessPromotions, promotionsWithCustomerSegments) =
        room.availablePromotions.partition(_.customerSegments.isEmpty)
      if (promotionsWithCustomerSegments.isEmpty) {
        List(room)
      } else {
        val combinations: Map[PromotionEntry, Set[YplRateFence]] = promotionsWithCustomerSegments.foldLeft(
          Map.empty[PromotionEntry, Set[YplRateFence]],
        ) { (acc, promotion) =>
          val applicableSetFenceAndCheckResultPair: Set[(YplRateFence, Boolean, Option[AdditionalDispatchReason.Value])] =
            room.fences
              .map { fence =>
                val isApplicableBecauseOfBlt = isPromoFromBlt(fence, promotion)
                if (isApplicableBecauseOfBlt) {
                  (fence, true, None)
                } else {
                  val (isApplicable, extraReason) = checkSupportCustomerSegment(
                    promotion,
                    fence,
                    hotelMeta,
                    pulseCampaignMetadataByPromotionTypeId,
                    isAboCidToOriginMappingEnabled,
                  )

                  (fence, isApplicable, extraReason)
                }
              }
              .filter(_._2)
          if (applicableSetFenceAndCheckResultPair.isEmpty) {
            acc
          } else {
            val generalReasonApplicableFences: Set[(YplRateFence, Boolean, Option[AdditionalDispatchReason.Value])] =
              applicableSetFenceAndCheckResultPair.filter(_._3.isEmpty)

            if (generalReasonApplicableFences.nonEmpty) {
              acc + (promotion -> generalReasonApplicableFences.map(_._1))
            } else { // overriding fence found applicable by override

              acc + (
                promotion.copy(
                  additionalDispatchReasons =
                    promotion.additionalDispatchReasons ++ applicableSetFenceAndCheckResultPair.flatMap(_._3),
                ) -> applicableSetFenceAndCheckResultPair.map(_._1)
              )
            }
          }
        }

        // Add unfenced promotions for all fences
        val allPromotions: Map[PromotionEntry, Set[YplRateFence]] =
          fencelessPromotions.foldLeft(combinations)((acc, promotion) => acc + (promotion -> room.fences))

        // Regroup to promotion list to reduce amount of rooms generated by room expansion
        val rekeyedPromotions: Map[List[PromotionEntry], Set[YplRateFence]] =
          allPromotions.groupBy(_._2).map(x => x._2.keys.toList -> x._1)

        // Add empty promotion list for missing fences
        val fencesWithPromotions: Set[YplRateFence] = allPromotions.values.flatten.toSet
        val missingFences = room.fences.diff(fencesWithPromotions)
        val rekeyedCombinations: Map[List[PromotionEntry], Set[YplRateFence]] =
          if (missingFences.isEmpty) {
            rekeyedPromotions
          } else {
            rekeyedPromotions + (Nil -> missingFences)
          }

        // Generate rooms per unique fence set
        rekeyedCombinations.map { case (promotions, fences) =>
          room.copy(fences = fences, availablePromotions = promotions)
        }.toList
      }
    }
  }

  private[promotions] def validate(promotion: PromotionEntry,
                                   bookingDate: DateTime,
                                   hotelMeta: HotelMeta,
                                   roomOpt: Option[YplRoomEntry] = None,
                                   allowNoCCPromo: Boolean = false,
                                   pulsePromotionsToRemove: Set[PromotionTypeId] = Set.empty,
                                   pulseCampaignMetadataByPromotionTypeId: Map[PromotionTypeId, PulseCampaignMetadata] =
                                     Map.empty[PromotionTypeId, PulseCampaignMetadata])(implicit
    request: YplRequest,
    ctx: YplContext): (Boolean, Option[AdditionalDispatchReason.Value]) = {

    def isPromoActive: (Boolean, Option[AdditionalDispatchReason.Value]) = {
      def convertToLocalTime(ticksOrWhateverThisIs: Long) =
        new DateTime(ticksOrWhateverThisIs / 10000).minusHours(7).toLocalTime
      val campaignTypeId: Option[Int] =
        pulseCampaignMetadataByPromotionTypeId.get(promotion.typeId).map(_.campaignTypeId)

      val shouldUsePulsePreCache =
        ctx.experimentContext.isUserB(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_USING_PULSE_PRECACHE)
      val extensionPeriod: Option[CampaignExtensionPeriod] =
        if (shouldUsePulsePreCache) {
          pulseCampaignMetadataByPromotionTypeId.get(promotion.typeId).map { pulseCampaignMetadata =>
            CampaignExtensionPeriod(
              pulseCampaignMetadata.campaignTypeId,
              bookTimeFromExtensionMinutes = pulseCampaignMetadata.bookTimeFromExtensionMinutes.getOrElse(0),
              bookTimeToExtensionMinutes = pulseCampaignMetadata.bookTimeToExtensionMinutes.getOrElse(0),
            )
          }
        } else {
          campaignTypeId.flatMap(cTypeId => CampaignExtensionPeriods.get(cTypeId))
        }

      def isInExtensionPeriod = extensionPeriod.exists { period =>
        val localBookTimeFrom = promotion.bookTimeFrom.map(convertToLocalTime).getOrElse(LocalTime.MIDNIGHT)
        val localBookTimeTo = promotion.bookTimeTo.map(convertToLocalTime).getOrElse(LocalTime.MIDNIGHT.minusMinutes(1))
        val bookTimeFromWithExtensionToday =
          localBookTimeFrom.toDateTimeToday.minusMinutes(period.bookTimeFromExtensionMinutes)
        val bookTimeToWithExtensionToday = localBookTimeTo.toDateTimeToday.plusMinutes(period.bookTimeToExtensionMinutes)

        val isFullDayPromotion =
          bookTimeToWithExtensionToday.getMillis >= bookTimeFromWithExtensionToday.plusMinutes(aDayInMinute).getMillis

        (isFullDayPromotion, promotion.bookFrom, promotion.bookTo) match {
          case (true, Some(bookDateFrom), Some(bookDateTo)) =>
            // if promotion is full day, we need to check only the start date time and stop date time.
            val bookDateTimeFromWithExtension =
              bookDateFrom.toLocalDate.toDateTime(localBookTimeFrom).minusMinutes(period.bookTimeFromExtensionMinutes)

            val bookDateTimeToWithExtension =
              bookDateTo.toLocalDate.toDateTime(localBookTimeTo).plusMinutes(period.bookTimeToExtensionMinutes)
            val isSupport =
              bookDateTimeFromWithExtension.getMillis <= bookingDate.getMillis && bookDateTimeToWithExtension.getMillis >= bookingDate.getMillis
            isSupport
          case (false, Some(bookDateFrom), Some(bookDateTo)) =>
            // if promotion is not full day, we need to check the time period in every day.
            val localBookTimeFromWithExtension = localBookTimeFrom.minusMinutes(period.bookTimeFromExtensionMinutes)
            val localBookTimeToWithExtension = localBookTimeTo.plusMinutes(period.bookTimeToExtensionMinutes)
            val bookingAfterLocalBookTimeFromWithExtension =
              localBookTimeFromWithExtension.getMillisOfDay <= bookingDate.toLocalTime.getMillisOfDay
            val bookingBeforeLocalBookTimeToWithExtension =
              localBookTimeToWithExtension.getMillisOfDay >= bookingDate.toLocalTime.getMillisOfDay || localBookTimeToWithExtension == LocalTime.MIDNIGHT
            val bookingHourSupport =
              bookingAfterLocalBookTimeFromWithExtension && bookingBeforeLocalBookTimeToWithExtension
            // Not support extension with cross day when book time from > book time to

            val bookDateFromSupport = bookDateFrom.getMillis <= bookingDate.withTimeAtStartOfDay.getMillis
            val bookDateToSupport = bookDateTo.getMillis >= bookingDate.withTimeAtStartOfDay.getMillis
            val bookingDaySupport = bookDateFromSupport && bookDateToSupport
            bookingHourSupport && bookingDaySupport
          case _ => false
        }
      }

      def readyToDispatch: Boolean = {

        val doesHotelDisableEveryPulseCampaign = hotelMeta.pulseCampaignIdBlacklist == Seq(0)
        val isPulseCampaignInBlackList = hotelMeta.pulseCampaignIdBlacklist.contains(campaignTypeId.getOrElse(0))
        !hotelMeta.isNorthAmericaHotel &&
        campaignTypeId.isDefined &&
        (
          ctx.experimentContext.isUserB(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_TO_MSE_AND_AFFILIATE_TRAFFIC) ||
          !ctx.request.isAllMseTraffic
        ) &&
        !doesHotelDisableEveryPulseCampaign &&
        !isPulseCampaignInBlackList
      }

      val bookingAfterPromoStartDay =
        promotion.bookFrom.forall(_.getMillis <= bookingDate.withTimeAtStartOfDay.getMillis)
      val bookingBeforePromoEndDay = promotion.bookTo.forall(_.getMillis >= bookingDate.withTimeAtStartOfDay.getMillis)

      def bookingAfterPromoStartHour =
        promotion.bookTimeFrom.forall(convertToLocalTime(_).isBefore(bookingDate.toLocalTime))
      def bookingBeforePromoEndHour = promotion.bookTimeTo.forall(convertToLocalTime(_).isAfter(bookingDate.toLocalTime))

      // "Cross-day" means the end of the window is on the following day.
      // Allow booking promotion from 22:00 to 08:00 (the next day) for example.
      // In that case booking_time must > 22:00 or booking_time < 08:00, which can't be at the same time so needs special handling
      val crossDayCaseApplicable =
        (for (from <- promotion.bookTimeFrom; to <- promotion.bookTimeTo) yield from > to).getOrElse(false)

      val bookingDaySupported = bookingAfterPromoStartDay && bookingBeforePromoEndDay
      def bookingHourSupported =
        bookingAfterPromoStartHour && bookingBeforePromoEndHour || crossDayCaseApplicable && (bookingAfterPromoStartHour || bookingBeforePromoEndHour)
      val isBookingSupported = bookingDaySupported && bookingHourSupported
      val isExtensionAllowed = isInExtensionPeriod && readyToDispatch

      val isValidBookDateAndTime = isBookingSupported || isExtensionAllowed
      val isDispatchTimeFence = !isBookingSupported && isExtensionAllowed
      val additionalDispatchReason = if (isDispatchTimeFence) Some(AdditionalDispatchReason.TimeFenceExtended) else None

      (isValidBookDateAndTime, additionalDispatchReason)
    }

    def isRequireAppliedEveryNight = promotion.typeId == NoCCRequiredTypeId || promotion.typeId == NoCCLastMinuteTypeId
    def isAppliedEveryNight = promotion.applyDates.size == request.lengthOfStay
    val (isValidBookDateAndTime, dispatchReason) = isPromoActive

    val isValid = ((promotion.discountType != DiscountType.Other && promotion.discountType != DiscountType.Combined)
      && (promotion.bookFrom.isDefined && promotion.bookTo.isDefined)
      && (promotion.bookOn.charAt(bookingDate.dayOfWeek().get() % 7) == '1')
      && (request.occ.getRooms >= promotion.minRooms)
      && isValidBookDateAndTime
      && (!isRequireAppliedEveryNight || (allowNoCCPromo && roomOpt.exists(_.isAgodaAgency) && isAppliedEveryNight))
      && !pulsePromotionsToRemove.contains(promotion.typeId))
    (isValid, dispatchReason)
  }

  private[promotions] def isApplicableDate(d: DailyPrice,
                                           promo: PromotionEntry,
                                           bookingDate: DateTime,
                                           checkIn: DateTime): Boolean = !d.isPromotionBlackOut &&
    (validateLastMinute(bookingDate, checkIn, promo) || validateAdvancePurchase(bookingDate, d.date, promo)) &&
    promo.applyDates.contains(d.date)

  private[promotions] def isVipYcsPromotionEligible(
    promo: PromotionEntry)(implicit request: YplRequest, ctx: YplContext): Boolean =
    ctx.experimentContext.isUserB(YplExperiments.SHOW_VIP_BADGE_FOR_VIP_YCS_PROMOTION) && hasValidVipSegment(promo)

  private def getVipSegments(promotion: PromotionEntry) =
    promotion.customerSegments.filter(x => x.vipLevel.exists(_.value != VipLevelType.UNDEFINED.value))

  private def hasValidVipSegment(promo: PromotionEntry)(implicit request: YplRequest, ctx: YplContext): Boolean = {
    val vipSegments = getVipSegments(promo)
    vipSegments.exists { x =>
      (x.vipLevel, request.cInfo.vipLevel) match {
        case (Some(sVipLevel), Some(cVipLevel)) => sVipLevel.value == cVipLevel.value ||
          VipSegmentExtraValidationHelper.isDiamondCustomerWithPlatinumSegment(sVipLevel, cVipLevel)
        case _ => false
      }
    }
  }

  private def validateLastMinute(bookingDate: DateTime, checkInDate: DateTime, promo: PromotionEntry): Boolean =
    if (promo.typeId != PromotionConstant.LastMinuteTypeId && promo.typeId != NoCCLastMinuteTypeId) false
    else validateAdvancePurchase(bookingDate, checkInDate, promo)

  private def validateAdvancePurchase(bookingDate: DateTime, date: DateTime, promotion: PromotionEntry): Boolean = {
    //  ToDO: cache bookingDate with start of day
    lazy val datesAfterBooking = Days.daysBetween(bookingDate.withTimeAtStartOfDay, date).getDays

    (promotion.minAdvPurchase.isEmpty || datesAfterBooking >= promotion.minAdvPurchase.get) &&
    (promotion.maxAdvPurchase.isEmpty || datesAfterBooking <= promotion.maxAdvPurchase.get)
  }

}
