package com.agoda.papi.ypl.pricing.promotions

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.ypl.models.{PromotionTypeId, YplContext, YplExperiments}
import com.agoda.papi.ypl.models.enums.VipLevelType
import com.agoda.papi.ypl.models.pricing.proto.{CustomerSegment, PromotionEntry}
import com.agoda.papi.ypl.pricing.VipSegmentExtraValidationHelper
trait PromotionEntryValidation {
  def isValidByOverridingCustomerSegment(
    promotion: PromotionEntry,
    pulseCampaignMetadataByPromotionTypeId: Map[PromotionTypeId, PulseCampaignMetadata],
    customerOriginCountryCode: String,
    customerLanguageId: Int,
  )(implicit ctx: YplContext): Boolean
}
trait PromotionEntryValidationImpl extends PromotionEntryValidation {

  protected[promotions] def getOverridingCustomerSegmentRestrictions(
    promotion: PromotionEntry,
    pulseCampaignMetadataByPromotionTypeId: Map[PromotionTypeId, PulseCampaignMetadata],
  ): List[CustomerSegment] = pulseCampaignMetadataByPromotionTypeId
    .get(promotion.typeId)
    .map { meta =>
      val segments = meta.customerSegmentOverride.map { cs =>
        CustomerSegment(
          if (cs.languageId == 0) None else Some(cs.languageId),
          Option(cs.countryIso2),
          Option(VipLevelType.getFromValue(cs.vipLevel)),
        )
      }
      segments
    }
    .getOrElse(List.empty[CustomerSegment])

  protected[promotions] def shouldUseOverridingCustomerSegment(
    promotion: PromotionEntry,
    pulseCampaignMetadataByPromotionTypeId: Map[PromotionTypeId, PulseCampaignMetadata],
  )(implicit ctx: YplContext): Boolean =
    if (ctx.request.isAllMseTraffic) {
      false
    } else {
      val overrideCustomerSegmentRestrictions =
        getOverridingCustomerSegmentRestrictions(promotion, pulseCampaignMetadataByPromotionTypeId)
      overrideCustomerSegmentRestrictions.nonEmpty && ctx.experimentContext.isUserB(
        YplExperiments.ENABLE_DISPATCHING_OVERRIDE_ON_CUSTOMER_SEGMENT_USING_PULSE_PRECACHE)
    }

  protected[promotions] def getVipSegmentRestrictions(
    customerSegmentRestrictions: List[CustomerSegment]): List[CustomerSegment] =
    customerSegmentRestrictions.filter(customerSegmentRestriction =>
      customerSegmentRestriction.vipLevel.exists(_.value != VipLevelType.UNDEFINED.value))

  protected[promotions] def getNonVipSegmentRestrictions(
    customerSegmentRestrictions: List[CustomerSegment]): List[CustomerSegment] = customerSegmentRestrictions.filter(x =>
    x.vipLevel.isEmpty || x.vipLevel.exists(_.value == VipLevelType.UNDEFINED.value))

  protected[promotions] def isValidVipSegment(customerSegmentRestrictions: List[CustomerSegment])(implicit
    ctx: YplContext): Boolean = {
    val vipSegmentRestrictions = getVipSegmentRestrictions(customerSegmentRestrictions)
    vipSegmentRestrictions.isEmpty ||
    vipSegmentRestrictions.exists(cs =>
      (cs.vipLevel, ctx.request.cInfo.vipLevel) match {
        case (Some(sVipLevel), Some(cVipLevel)) => sVipLevel.value == cVipLevel.value ||
          VipSegmentExtraValidationHelper.isDiamondCustomerWithPlatinumSegment(sVipLevel, cVipLevel)
        case _ => false
      })
  }

  protected[promotions] def isValidLanguageAndOriginCustomerSegment(
    customerSegmentRestrictions: List[CustomerSegment],
    customerLanguageId: Int,
    customerOriginCountryCode: String,
  ): Boolean = {
    val nonVipSegments = getNonVipSegmentRestrictions(customerSegmentRestrictions)
    nonVipSegments.isEmpty || nonVipSegments.exists { cs =>
      cs.languageId.forall(_ == customerLanguageId) && cs.countryCode.forall(
        _.equalsIgnoreCase(customerOriginCountryCode))
    }
  }

  def isValidByOverridingCustomerSegment(
    promotion: PromotionEntry,
    pulseCampaignMetadataByPromotionTypeId: Map[PromotionTypeId, PulseCampaignMetadata],
    customerOriginCountryCode: String,
    customerLanguageId: Int,
  )(implicit ctx: YplContext): Boolean =
    if (!shouldUseOverridingCustomerSegment(promotion, pulseCampaignMetadataByPromotionTypeId)) {
      false
    } else {
      val overridingCustomerSegmentRestrictions =
        getOverridingCustomerSegmentRestrictions(promotion, pulseCampaignMetadataByPromotionTypeId)
      if (overridingCustomerSegmentRestrictions.isEmpty) {
        false
      } else {
        val isValidVipSegmentRestrictions = isValidVipSegment(overridingCustomerSegmentRestrictions)

        isValidVipSegmentRestrictions &&
        isValidLanguageAndOriginCustomerSegment(overridingCustomerSegmentRestrictions,
                                                customerLanguageId,
                                                customerOriginCountryCode)
      }
    }
}
