package com.agoda.papi.ypl.models

import com.agoda.papi.enums.room.RateType
import com.agoda.papi.ypl.models.context.{CompositeChannelContext, SurchargeDataServiceContext}
import com.agoda.papi.ypl.models.pricing.UspaCampaign
import com.agoda.utils.flow.CommonFlowContext
import models.consts.ABTest

/**
  * This context should contain only non-mutable state.
  * All mutable state should be wrapped in context and passed as a parameters
  */
trait YplContext extends CommonFlowContext with WithYplRequest {

  val request: YplRequest

  val exchangeRateCtx: context.ExchangeRateContext
  val compositeChannelContext: CompositeChannelContext
  val surchargeDataServiceContext: SurchargeDataServiceContext
  val hadoopContext: context.HadoopContext

  val supplierSetting: Map[SupplierId, YplSupplierSetting]
  val holidayCalendarContext: context.RoomLinkageHolidayCalendarContext
  val pulseCampaignMetaCtx: context.PulseCampaignMetaContext
  val masterChannelCategoryContext: context.MasterChannelCategoryContext
  val eligibleUspaCampaigns: List[UspaCampaign]
  val addFeesInExclusivePrice: Boolean

  def sendMeasurement(measurement: Measurement): Unit = sendMetric(
    metric = measurement.metric,
    value = measurement.value,
    tags = measurement.tags,
  )
  def sendMetric(metric: String, value: Long, tags: Map[String, String] = Map.empty): Unit =
    aggregateReporter.aggregate(metric, value, tags)

  def measureTime[T](metric: String, tags: Map[String, String] = Map.empty)(f: => T): T = {
    val started = System.currentTimeMillis()
    val result = f
    sendMetric(metric, System.currentTimeMillis() - started, tags)
    result
  }

  def overrideYplRequest(request: YplRequest): YplContext

  // todo: make supplierSetting as separate model then we cam move along these 2 functions
  def isThirdParty(supplierId: SupplierId) = supplierSetting.get(supplierId).exists(_.isThirdParty)

  def isDirectConnect(supplierId: SupplierId) = !isThirdParty(supplierId)

  // Occupancy Model Full pattern length of stay
  def isOccModelFPLOS(supplierId: SupplierId) = supplierSetting.get(supplierId).exists(_.isOccModelFPLOS)

  // Supply ConnectionType, ARI have different rules for this.
  def connectionType(supplierId: SupplierId): ConnectionType =
    ConnectionType.apply(supplierId, isDirectConnect(supplierId))

  def isApplyTaxOnSellEx(supplierId: SupplierId,
                         hotelId: HotelId,
                         chainId: ChainId,
                         countryId: CountryId,
                         paymentModel: Int,
                         originalRateType: RateType)(implicit ctx: YplContext): Boolean = {
    val isEnableNegativeSegmentExp = ctx.experimentContext.isUserB(ABTest.ENABLE_NEGATIVE_SEGMENT)
    val isApplyTaxOnSellExBaseOnConfig = ctx.request.applyTaxOnSellExSettings.exists(
      _.isApplyTaxOnSellEx(
        supplierId,
        hotelId,
        chainId,
        countryId,
        paymentModel,
        originalRateType.value,
        isEnableNegativeSegmentExp,
      ))
    val supplierHotelCalculationSetting = ctx.request.supplierHotelCalculationSettings.settings.get(supplierId)
    val isApplyTaxOnSellExHotelLevel = supplierHotelCalculationSetting.exists(_.isApplyTaxOnSellEx)
    isApplyTaxOnSellExBaseOnConfig || isApplyTaxOnSellExHotelLevel
  }
}
