package com.agoda.papi.ypl.pricing.promotions

import com.agoda.papi.ypl.models.pricing.proto.{DailyPrice, PriceEntry, PromotionEntry}
import com.agoda.papi.ypl.models.{PromotionId, PromotionTypeId, YplContext, YplExperiments, YplRequest, YplRoomEntry}
import com.agoda.papi.enums.request.StackDiscountOption
import com.agoda.papi.enums.request.StackDiscountOption.Multiplicative
import com.agoda.papi.enums.room.DiscountType
import com.agoda.papi.ypl.models.pricing.DiscountMessage
import org.joda.time.{DateTime, LocalDate}
import PromotionConstant._
import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.ypl.pricing.{CancellationPolicyService, PulsePromotionsHelper}

trait PromoRoomGeneration extends CancellationPolicyService with PromoValidation {

  /**
    * Apply single promotion with conditional Mega Sale stacking prevention
    *
    * This function applies a single promotion to a room. When the ENABLE_MEGA_CAMPAIGN experiment
    * is enabled (B variant), it prevents Mega Sale promotions from stacking with each other,
    * similar to how Pulse campaigns are handled. Otherwise, it uses the legacy behavior.
    *
    * @param room                                   The original room entry to apply promotion to
    * @param newPromo                               The promotion entry to be applied
    * @param bookingDate                            The booking date for promotion application
    * @param stackDiscountOptionMap                 Discount options for each night (stackable promotions only)
    * @param applyDiscountsMultiplicatively         Whether to apply discounts multiplicatively
    * @param promotionTypeIdToPulseCampaignMetadata Campaign metadata mapping for Pulse and Mega Sale promotions
    * @param bestStackPulsePromotionIdForDate       Best stackable Pulse promotion ID for each date (legacy parameter)
    * @param dateTimeToMaybeBestMegaPromotionId     Best stackable Mega Sale promotion ID for each date (experiment parameter)
    * @param useBaseCxlPolicyForPromotionalRooms    Whether to use base cancellation policy for promotional rooms
    *
    * @return Option[YplRoomEntry] - New room entry with promotion applied, or None if promotion cannot be applied
    */
  private[promotions] def createPromotionalRoom(
    room: YplRoomEntry,
    newPromo: PromotionEntry,
    bookingDate: DateTime,
    stackDiscountOptionMap: Map[DateTime, Option[StackDiscountOption]] = Map.empty,
    applyDiscountsMultiplicatively: Boolean,
    promotionTypeIdToPulseCampaignMetadata: Map[PromotionTypeId, PulseCampaignMetadata] = Map.empty,
    dateTimeToMaybePulsePromotionId: Map[DateTime, Option[PromotionId]] = Map.empty,
    dateTimeToMaybeBestMegaPromotionId: Map[DateTime, Option[PromotionId]] = Map.empty,
    useBaseCxlPolicyForPromotionalRooms: Boolean = false,
    isMegaCampaignEnabled: Boolean = false)(implicit request: YplRequest, ctx: YplContext): Option[YplRoomEntry] = {

    // Step 1: Determine apply dates based on experiment condition
    val applyDates: Set[DateTime] = {
      val promotionMetadata = promotionTypeIdToPulseCampaignMetadata.get(newPromo.typeId)

      val (toCheckBestPulsePromo, toCheckBestMegaSalePromo) =
        if (isMegaCampaignEnabled) {
          (
            PulsePromotionsHelper.isPulseCampaignType(promotionMetadata) && dateTimeToMaybePulsePromotionId.nonEmpty,
            PulsePromotionsHelper.isMegaSaleCampaignType(
              promotionMetadata) && dateTimeToMaybeBestMegaPromotionId.nonEmpty,
          )
        } else {
          (
            promotionTypeIdToPulseCampaignMetadata.contains(newPromo.typeId) && dateTimeToMaybePulsePromotionId.nonEmpty,
            false,
          )
        }

      val isBestStackPulsePromoInDate = (date: DateTime, promotionId: PromotionId) =>
        dateTimeToMaybePulsePromotionId.getOrElse(date, None).contains(promotionId)

      val isBestMegaSalePromoInDate = (date: DateTime, promotionId: PromotionId) =>
        dateTimeToMaybeBestMegaPromotionId.getOrElse(date, None).contains(promotionId)

      val dates = room.dailyPrices.collect {
        case (date, price)
            if isApplicableDate(price, newPromo, bookingDate, request.checkIn)
            && (!toCheckBestPulsePromo || isBestStackPulsePromoInDate(date, newPromo.id))
            && (!toCheckBestMegaSalePromo || isBestMegaSalePromoInDate(date, newPromo.id)) => date
      }.toSet

      if ((toCheckBestPulsePromo || toCheckBestMegaSalePromo) && dates.isEmpty) return Option(room)

      dates
    }

    // Step 2: Generate new dailyPrices and promotionBreakdown
    val datePriceAndPromo = room.dailyPrices.values.map { dailyPrice =>
      if (applyDates.contains(dailyPrice.date) && isApply(room, dailyPrice.date.toLocalDate, newPromo)) {
        val newDailyPrices = dailyPrice.prices.map { price =>
          if (price.isRoom) applyPromotion(price,
                                           room,
                                           newPromo,
                                           applyDates,
                                           request.occ.getRooms,
                                           stackDiscountOptionMap.getOrElse(dailyPrice.date, None),
                                           applyDiscountsMultiplicatively)
          else price
        }

        (dailyPrice.date, dailyPrice.copy(prices = newDailyPrices), Option(newPromo))
      } else (dailyPrice.date, dailyPrice, None)
    }

    // Step 3: Merge promotion breakdown
    val (newDailyPrices, newPromotionBreakdown) =
      datePriceAndPromo.foldLeft((Map.empty[DateTime, DailyPrice], Map.empty[LocalDate, PromotionEntry])) {
        case ((prices, promos), (date, price, promo)) =>
          (prices + (date -> price), if (promo.isDefined) promos + (date.toLocalDate -> promo.get) else promos)
      }

    // Step 4: Merge promotion
    val newRoomPromo =
      if (isNoCCPromo(newPromo)) room.promotion
      else room.promotion.map(_ => CombinedPromo).orElse(Option(newPromo))

    // Step 5: Merge cancellation code
    val promoCxlCode = (useBaseCxlPolicyForPromotionalRooms, room.promotion.isEmpty) match {
      case (false, true) => newPromo.cancellationCode
      case (false, false) if getWorseCxlCode(room.cxlCode, newPromo.cancellationCode) == newPromo.cancellationCode =>
        newPromo.cancellationCode
      case _ => room.cxlCode
    }

    // Step 6: Define new room entry
    val allowShowPulseBadgeForExtraDispatchReason =
      ctx.experimentContext.isUserB(YplExperiments.ALLOW_SHOW_PULSE_BADGE_FOR_EXTRA_DISPATCH_REASON)

    val result = room.copy(
      promotion = newRoomPromo,
      cxlCode = promoCxlCode,
      dailyPrices = newDailyPrices,
      parentPromotionSourceRoom = Some(room),
      promotionsBreakdown = mergePromotionBreakdown(newPromotionBreakdown, room.promotionsBreakdown),
      isVipYcsPromotionEligible = isVipYcsPromotionEligible(newPromo),
      pulseCampaignMetadata =
        if (newPromo.additionalDispatchReasons.isEmpty || allowShowPulseBadgeForExtraDispatchReason) {
          // if promotion applied without any extra dispatch reasons then try to add pulse campaign metadata
          promotionTypeIdToPulseCampaignMetadata.get(newPromo.typeId).orElse(room.pulseCampaignMetadata)
        } else {
          room.pulseCampaignMetadata
        },
    )

    // Step 7: Filter room with promotion
    val roomWithPromotion = Option(result).filter { r =>
      if (isNoCCPromo(newPromo) || newPromo.isRatePlanAsPromotion) r.totalValue > 0
      else if (applyDiscountsMultiplicatively) r.totalValue > 0 && r.totalPromoDiscount > 0
      else r.totalValueWithoutChannelDiscount > 0 && r.totalPromoDiscount > 0
    }

    // Step 8: Filter room with promotion breakdown
    val finalResult = roomWithPromotion.filterNot(r => r.promotion.isEmpty && r.promotionsBreakdown.isEmpty)

    // Step 9: Override mega sale pulse metadata if needed
    PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
      room,
      finalResult,
      isMegaCampaignEnabled,
    )

  }

  private def applyPromotion(price: PriceEntry,
                             room: YplRoomEntry,
                             newPromo: PromotionEntry,
                             appliedDates: Iterable[DateTime],
                             numRooms: Int,
                             stackDiscountOption: Option[StackDiscountOption],
                             applyDiscountsMultiplicatively: Boolean): PriceEntry = {
    val discount = newPromo.getDiscount(price.date)

    // stacking is relevant only to Percent Discount promos
    val currentPromo =
      if (newPromo.discountType == DiscountType.PercentDiscount) findNormalPromotion(room, price.date.toLocalDate)
      else None
    applyDiscount(price,
                  currentPromo,
                  newPromo,
                  discount,
                  appliedDates,
                  numRooms,
                  stackDiscountOption,
                  applyDiscountsMultiplicatively)
  }

  private def isApply(room: YplRoomEntry, date: LocalDate, newPromo: PromotionEntry): Boolean =
    !newPromo.isStackable || findNormalPromotion(room, date).forall(_.isAllowStack)

  private def mergePromotionBreakdown(
    newDailyPromos: Map[LocalDate, PromotionEntry],
    originalDailyPromos: Map[LocalDate, List[PromotionEntry]]): Map[LocalDate, List[PromotionEntry]] = {
    val allPromoDates = newDailyPromos.keySet ++ originalDailyPromos.keySet
    allPromoDates.foldLeft(Map.empty[LocalDate, List[PromotionEntry]]) { case (mergedDailyPromos, date) =>
      val newPromoOpt = newDailyPromos.get(date)
      val originalPromosOpt = originalDailyPromos.get(date)

      val mergedPromos = (originalPromosOpt, newPromoOpt) match {
        case (Some(originalPromos), Some(newPromo)) => newPromo :: originalPromos
        case (Some(originalPromos), None) => originalPromos
        case _ => newPromoOpt.toList
      }

      mergedDailyPromos + (date -> mergedPromos)
    }
  }

  //  ToDO: pass number of applied dates instead of whole collection
  private[pricing] def applyDiscount(priceEntry: PriceEntry,
                                     currentPromo: Option[PromotionEntry],
                                     newPromo: PromotionEntry,
                                     discount: Double,
                                     appliedDates: Iterable[DateTime],
                                     numRooms: Int,
                                     promoToPromoStackDiscountOption: Option[StackDiscountOption] = None,
                                     promoToChannelDiscountApplyMultiplicatively: Boolean = true): PriceEntry = {
    // if discounts apply multiplicatively - promo should be applied on top of channel discount, otherwise - on original price
    val rootBaseValue =
      if (promoToChannelDiscountApplyMultiplicatively) priceEntry.value - priceEntry.channelDiscount
      else priceEntry.value

    def calculatePercentDiscountMultiplicatively(promoDiscount: Double): (Double, Double) = {
      val derivedBaseValue = (rootBaseValue - promoDiscount)
      (derivedBaseValue, derivedBaseValue * discount / 100.0)
    }

    def calculatePercentDiscount(promoDiscount: Double,
                                 currentPromo: Option[PromotionEntry],
                                 discount: Double): (Double, Double) =
      (currentPromo, promoToPromoStackDiscountOption) match {
        case (Some(_), Some(Multiplicative)) => calculatePercentDiscountMultiplicatively(promoDiscount)
        case (Some(_), None) => (rootBaseValue, 0.0)
        case _ => (rootBaseValue, rootBaseValue * discount / 100.0)
      }

    val (derivedBaseValue, discountAmount) = newPromo.discountType match {
      case DiscountType.AmountDiscountPerBook =>
        (rootBaseValue, Math.min(discount / (appliedDates.size * numRooms), rootBaseValue))
      case DiscountType.AmountDiscountPerNight => (rootBaseValue, discount)
      case DiscountType.FreeNight => (rootBaseValue, rootBaseValue)
      case DiscountType.FinalPriceDiscount => (rootBaseValue, rootBaseValue - discount)
      case DiscountType.PercentDiscount => calculatePercentDiscount(priceEntry.promoDiscount, currentPromo, discount)
      case _ => (rootBaseValue, 0.0)
    }

    val newPriceBreakdownHistory = priceEntry.priceBreakdownHistory.addPromotionV2(
      current = currentPromo,
      next = newPromo,
      rawValue = discount,
      calculatedValue = discountAmount,
      baseValue = derivedBaseValue,
    )

    if (discountAmount > 0) {
      val totalDiscount = math.min(priceEntry.promoDiscount + discountAmount, rootBaseValue)
      val updatedDiscountMessages =
        priceEntry.getUpdatedDiscountMessages(DiscountMessage(newPromo.typeId, discountAmount, newPromo.cmsTypeId))

      priceEntry.copy(
        promoDiscount = totalDiscount,
        discountMessages = updatedDiscountMessages,
        priceBreakdownHistory = newPriceBreakdownHistory,
      )
    } else {
      priceEntry.copy(
        priceBreakdownHistory = newPriceBreakdownHistory,
      )
    }
  }

  private def findNormalPromotion(room: YplRoomEntry, date: LocalDate) =
    room.promotionsBreakdown.get(date).flatMap(_.find(!_.isStackable))

  private def isNoCCPromo(p: PromotionEntry) =
    p.typeId == PromotionConstant.NoCCRequiredTypeId || p.typeId == PromotionConstant.NoCCLastMinuteTypeId

}
