package com.agoda.papi.ypl.pricing.promotions

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.enums.room.DiscountType
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.enums.RoomDataStep
import com.agoda.papi.ypl.models.pricing.proto.PromotionEntry
import com.agoda.papi.ypl.models.{
  GmtOffset,
  HotelMeta,
  PromotionTypeId,
  YplContext,
  YplExperiments,
  YplRequest,
  YplRoomEntry,
}
import com.agoda.papi.ypl.pricing.{BLTSettings, CancellationPolicyWithExperimentHelper}
import com.agoda.papi.ypl.services.{CidToOriginMapper, OriginManager}
import com.agoda.papi.ypl.utils.Implicits._

/**
  * Created by ppattanapoon on 7/13/15.
  */
object PromotionConstant {
  val CombinedId = 9999999
  val CombinedTypeId = 99
  val CombinedCmsTypeId = 0
  val CombinedCmsDiscountTypeId = 44764
  val LastMinuteTypeId = 3
  val NoCCRequiredTypeId = 36
  val NoCCLastMinuteTypeId = 37
  val NoCCDomesticTypeId = 38
  val CombinedPromo = PromotionEntry(
    PromotionConstant.CombinedId,
    PromotionConstant.CombinedTypeId,
    DiscountType.Combined,
    List(0, 0, 0, 0, 0, 0, 0),
    PromotionConstant.CombinedCmsTypeId,
    PromotionConstant.CombinedCmsDiscountTypeId,
  )
  val NOCC_PROMOTION_TYPES = Set(NoCCRequiredTypeId, NoCCLastMinuteTypeId)

  val experimentPulseToAllRateChannelSettings: ExperimentPulseToAllRateChannelSettings =
    SharePromotionCalculationSettings.getPromotionCalculationPartExperimentPulseToAllRateChannelSettingsInstance
}

trait PromotionCalculation {
  def generatePromotionalRooms(hInfo: HotelMeta,
                               rooms: List[YplRoomEntry],
                               removePulsePromotions: Boolean = false,
                               removeNoCcPromotions: Boolean = false,
                               useBaseCxlPolicyForPromotionalRooms: Boolean = false)(implicit
    ctx: YplContext): List[YplRoomEntry]
}
trait PromotionCalculationImpl
  extends PromotionCalculation
    with OriginManager
    with CidToOriginMapper
    with PromoRoomGeneration
    with CombinablePromotions
    with StackablePromotions
    with PromoRoomComparison
    with CancellationPolicyWithExperimentHelper {

  /**
    * Generate all promotional rooms
    * @param hInfo Hotel information(for calculate local time)
    * @param rooms Original room list(without promotion)
    * @param removePulsePromotions Remove Pulse Promotions from rooms
    * @return All promotional rooms
    */
  def generatePromotionalRooms(hInfo: HotelMeta,
                               rooms: List[YplRoomEntry],
                               removePulsePromotions: Boolean = false,
                               removeNoCcPromotions: Boolean = false,
                               useBaseCxlPolicyForPromotionalRooms: Boolean = false)(implicit
    ctx: YplContext): List[YplRoomEntry] = {
    implicit val request: YplRequest = ctx.request
    val localBookingDate = hInfo.toHotelDateTime(request.bookingDate)

    val bltSettings = BLTSettings.getInstance()
    val isAgencyEnable = hInfo.agencyFeatures.exists(_.isAgodaAgencyEnable) && !removeNoCcPromotions
    val pulseCampaignPromoSettings: Map[PromotionTypeId, PulseCampaignMetadata] =
      ctx.pulseCampaignMetaCtx.getPulseCampaignSetting(rooms.flatMap(_.availablePromotions.map(_.typeId).distinct))

    val fencedRooms = fencePromotions(hInfo, rooms, bltSettings, pulseCampaignPromoSettings)
    val isMegaCampaignEnabled = ctx.experimentContext.isUserB(YplExperiments.ENABLE_MEGA_CAMPAIGN)

    val promotionalRooms = fencedRooms.flatMap { room =>
      val isBNTLChannel = room.channel.baseChannelId == Channel.BNTL
      val pulsePromotionsToRemove: Set[PromotionTypeId] =
        if (removePulsePromotions) pulseCampaignPromoSettings.keySet else Set.empty
      val validPromos =
        if (isBNTLChannel) {
          List.empty
        } else {
          room.availablePromotions
            .map { promo =>
              val (isValid, dispatchReason) = validate(promo,
                                                       localBookingDate,
                                                       hInfo,
                                                       Some(room),
                                                       isAgencyEnable,
                                                       pulsePromotionsToRemove,
                                                       pulseCampaignPromoSettings)
              val updatedPromo =
                promo.copy(additionalDispatchReasons = promo.additionalDispatchReasons ++ dispatchReason.toSet)

              (updatedPromo, isValid)
            }
            .collect { case (promo, isValid) if isValid => promo }
        }
      val (unstackPromos, stackPromos) = validPromos.partition(!_.isStackable)
      val normalPromotionRooms = unstackPromos.foldLeft(List.empty[YplRoomEntry]) { case (results, promo) =>
        createPromotionalRoom(
          room,
          promo,
          localBookingDate,
          applyDiscountsMultiplicatively = hInfo.applyDiscountsMultiplicatively,
          promotionTypeIdToPulseCampaignMetadata = pulseCampaignPromoSettings,
          useBaseCxlPolicyForPromotionalRooms = useBaseCxlPolicyForPromotionalRooms,
          isMegaCampaignEnabled = isMegaCampaignEnabled,
        ).map(_ :: results).getOrElse(results)
      }
      val roomResultList =
        if (normalPromotionRooms.nonEmpty) {
          val stackablePromotionalRooms = generateRoomsWithStackablePromos(
            stackPromos,
            normalPromotionRooms,
            localBookingDate,
            hInfo.applyDiscountsMultiplicatively,
            pulseCampaignPromoSettings,
            useBaseCxlPolicyForPromotionalRooms,
            isMegaCampaignEnabled,
          )

          val bestRoomsPerCancellationPolicy =
            getBestPromoRoomPerCancellationPolicy(normalPromotionRooms, hInfo.gmtOffset, hInfo.gmtOffsetMinutes)

          val cheapestPromotionalRoom = findBest(normalPromotionRooms ::: stackablePromotionalRooms)

          val fixCombinedPromotionBreakDown =
            ctx.experimentContext.isUserB(YplExperiments.FIX_COMBINED_PROMOTION_BREAKDOWN)
          val combineRoom =
            generateRoomWithCombinedPromos(room, normalPromotionRooms, fixCombinedPromotionBreakDown).map { combine =>
              generateRoomsWithStackablePromos(
                stackPromos,
                List(combine),
                localBookingDate,
                hInfo.applyDiscountsMultiplicatively,
                pulseCampaignPromoSettings,
                useBaseCxlPolicyForPromotionalRooms,
                isMegaCampaignEnabled,
              ).headOption.getOrElse(combine)
            }

          val bestRoom = combineRoom
            .map(combine => findBest(List(combine, cheapestPromotionalRoom)))
            .getOrElse(cheapestPromotionalRoom)

          val isCombineRoomBest = combineRoom.exists(_.eq(bestRoom))

          if (isCombineRoomBest) {
            // If combineRoom is cheapest return (combineRoom + cheapestRoom), else return (cheapest)
            // DF has to return only one combined promotion and both combineRoom and cheapestPromotionalRoom can have
            // combined promotions (stackable promos are also defined as combined)
            val roomList =
              if (hasCombinedPromotion(bestRoom) && hasCombinedPromotion(cheapestPromotionalRoom)) List(bestRoom)
              else List(bestRoom, cheapestPromotionalRoom)
            (roomList ::: bestRoomsPerCancellationPolicy).distinctByRef
          } else {
            (cheapestPromotionalRoom :: bestRoomsPerCancellationPolicy).distinctByRef
          }
        } else { // no normal promotional rooms
          generateRoomsWithStackablePromos(
            stackPromos,
            List(room),
            localBookingDate,
            hInfo.applyDiscountsMultiplicatively,
            pulseCampaignPromoSettings,
            useBaseCxlPolicyForPromotionalRooms,
            isMegaCampaignEnabled,
          )
        }
      roomResultList
    }

    promotionalRooms.map(r =>
      r.copy(
        roomDataChangeTracker = r.roomDataChangeTracker.map(_.track(RoomDataStep.PromotionalRoom, r.cxlCode)),
      ))

  }

  private def hasCombinedPromotion(room: YplRoomEntry): Boolean =
    room.promotion.exists(_.discountType == DiscountType.Combined)

  /**
    * Create best promotional room for each cancellation condition(non-refund, free-cancel, special condition)
    * @return Cheapest room for each cancellation condition
    */

  private def getBestPromoRoomPerCancellationPolicy(rooms: List[YplRoomEntry],
                                                    gmtOffset: GmtOffset = 0,
                                                    gmtOffsetMinutes: GmtOffset)(implicit
    ctx: YplContext): List[YplRoomEntry] = rooms
    .groupBy { r =>
      r.promotion.map { p =>
        val cancellationFees = getPAPICancellationFees(p.cancellationCode)
        getCancellationCondition(p.cancellationCode, cancellationFees, gmtOffset, gmtOffsetMinutes)(ctx)
      }
    }
    .values
    .map(findBest(_)(ctx.request))(collection.breakOut)

  private def findBest(rooms: List[YplRoomEntry])(implicit r: YplRequest): YplRoomEntry =
    rooms.reduceLeft(chooseBetterRoom)
}
