package com.agoda.papi.ypl.pricing

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.ypl.models.{PromotionTypeId, YplDispatchChannels, YplRoomEntry}
import com.agoda.papi.ypl.models.consts.{Channel, MegaSalePromotions}

object PulsePromotionsHelper {
  def hasPulseNegativeTestRateChannel(dispatchChannels: YplDispatchChannels): Boolean =
    dispatchChannels.masterChannels.exists(_.baseChannelId == Channel.PulseNegativeMock)

  private def isMegaSaleCampaignType(campaignTypeId: Int): Boolean =
    MegaSalePromotions.CampaignTypeIds.contains(campaignTypeId)

  def isMegaSaleCampaignType(pulseCampaignMetadata: Option[PulseCampaignMetadata]): Boolean =
    pulseCampaignMetadata.exists(metadata => isMegaSaleCampaignType(metadata.campaignTypeId))

  def isPulseCampaignType(pulseCampaignMetadata: Option[PulseCampaignMetadata]): Boolean =
    pulseCampaignMetadata.exists(metadata => !isMegaSaleCampaignType(metadata.campaignTypeId))

  def isMegaSaleCampaignType(
    promotionTypeId: Int,
    promotionTypeIdToPulseCampaignMetadata: Map[PromotionTypeId, PulseCampaignMetadata]): Boolean =
    promotionTypeIdToPulseCampaignMetadata.get(promotionTypeId).exists { metadata =>
      isMegaSaleCampaignType(metadata.campaignTypeId)
    }

  def isPulseCampaignType(
    promotionTypeId: Int,
    promotionTypeIdToPulseCampaignMetadata: Map[PromotionTypeId, PulseCampaignMetadata]): Boolean =
    promotionTypeIdToPulseCampaignMetadata.get(promotionTypeId).exists { metadata =>
      !isMegaSaleCampaignType(metadata.campaignTypeId)
    }

  def findTheFinalPulseMetadata(
    originalRoom: YplRoomEntry,
    updatedDiscountRoom: YplRoomEntry,
    bestMegaMetadata: Option[PulseCampaignMetadata],
    bestPulseMetadata: Option[PulseCampaignMetadata],
  ): Option[YplRoomEntry] = Option(updatedDiscountRoom).filter(_ != originalRoom).map { updatedRoom =>
    if (updatedRoom.pulseCampaignMetadata.nonEmpty) {
      // Replace pulse campaign metadata to mega sale campaign metadata as mega sale is the first priority
      val newMetadata =
        if (isMegaSaleCampaignType(updatedRoom.pulseCampaignMetadata)) {
          bestMegaMetadata
        } else {
          bestPulseMetadata
        }
      updatedRoom.copy(pulseCampaignMetadata = newMetadata)
    } else {
      updatedRoom
    }
  }

  def overrideMegaSalePulseMetadataIfNeeded(
    originalRoom: YplRoomEntry,
    finalRoom: Option[YplRoomEntry],
    isMegaCampaignEnabled: Boolean,
  ): Option[YplRoomEntry] =
    if (isMegaCampaignEnabled) {
      // Apply Mega Sale metadata restoration logic only when experiment is enabled
      finalRoom.map { promotionalRoom =>
        if (isMegaSaleCampaignType(originalRoom.pulseCampaignMetadata) &&
          isPulseCampaignType(promotionalRoom.pulseCampaignMetadata)) {
          promotionalRoom.copy(pulseCampaignMetadata = originalRoom.pulseCampaignMetadata)
        } else {
          promotionalRoom
        }
      }
    } else {
      finalRoom
    }
}
