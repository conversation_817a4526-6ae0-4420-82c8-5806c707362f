package com.agoda.papi.ypl.pricing

import com.agoda.papi.enums.hotel.{PaymentModel, TaxType}
import com.agoda.papi.enums.room.{ChargeType, RateType, SubChargeType}
import com.agoda.papi.ypl.models.pricing.proto.{HotelTaxInfo, TaxValue}
import com.agoda.papi.ypl.models.pricing.{RoomOccupancy, YPLESSFinancialBreakdown}
import com.agoda.papi.ypl.models.{
  SupplierId,
  YPLBookingCountryIndicator,
  YplContext,
  YplExperiments,
  YplReqOccByHotelAgePolicy,
}
import com.agoda.finance.tax.services.tax.{TaxCalculator => CommonTaxCalculator}
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes, TaxFeeBreakdowns}
import com.agoda.papi.pricing.pricecalculation.utils.CommonTaxConverter._
import com.agoda.papi.ypl.models.CommonTaxConverter.{toCommonReqOccByHotelAgePolicy, toCommonTaxRoomOcc}
import com.agoda.utils.flow.PropertyContext
import org.joda.time.DateTime

/**
  * Created by ppattanapoon on 7/6/15.
  */

trait TaxCalculator {

  /**
    * @return (taxes, fees, breakdowns)
    */
  def calculateTaxes(
    charge: ChargeType,
    value: Double,
    margin: Double,
    occ: RoomOccupancy,
    dailyTaxes: DailyTaxes,
    reqOcc: YplReqOccByHotelAgePolicy,
    paymentModel: PaymentModel,
    supplierId: SupplierId,
    hotelTaxInfo: Option[HotelTaxInfo],
    subChargeType: SubChargeType)(implicit ctx: YplContext, propertyContext: PropertyContext): TaxFeeBreakdowns

  def calculateFlatTaxAmount(chargeType: ChargeType, taxType: TaxType, taxBreakDown: List[CommonTaxBreakdown]): Double

  def calculateB2CTaxes(amount: Double,
                        financialBreakdownTypeId: Int,
                        bookingDate: DateTime,
                        determinedCountryCodeOpt: Option[String],
                        bookingCountryIndicatorOpt: Option[YPLBookingCountryIndicator],
                        isCleanedUpHospitalityTax: Boolean): Option[YPLESSFinancialBreakdown]
}

object TaxCalculator {

  def getTotalTaxAndFee(charge: ChargeType,
                        dailyTaxes: DailyTaxes,
                        occ: RoomOccupancy,
                        reqOcc: YplReqOccByHotelAgePolicy,
                        los: Int,
                        supplierId: SupplierId,
                        isBcomFixTaxAmountApplyToPB: Boolean,
                        subChargeType: SubChargeType,
                        sellExForMarginAdjustment: Option[Double] = None,
                        rateType: RateType,
                        commissionPercentage: Double)(implicit
    ctx: YplContext,
    propertyContext: PropertyContext): (Double, TaxValue, TaxValue) = {
    val isCleanedUpHospitalityTax = ctx.experimentContext.isPropertyB(propertyContext, YplExperiments.CLEAN_HP_TAX)
    CommonTaxCalculator.getTotalTaxAndFee(
      dt = toCommonDailyTaxes(dailyTaxes),
      occ = toCommonTaxRoomOcc(occ),
      reqOcc = toCommonReqOccByHotelAgePolicy(reqOcc),
      los = los,
      supplierId = supplierId,
      isBcomFixTaxAmountApplyToPB = isBcomFixTaxAmountApplyToPB,
      subChargeType = subChargeType,
      chargeType = toCommonChargeType(charge),
      sellExForMarginAdjustment = sellExForMarginAdjustment,
      rateType = rateType,
      commissionPercent = commissionPercentage,
      isCleanedUpHospitalityTax = isCleanedUpHospitalityTax,
    )
  }
}
