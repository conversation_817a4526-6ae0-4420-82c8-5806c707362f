package com.agoda.papi.ypl.pricing.promotions

import com.agoda.papi.ypl.models.pricing.proto.{CustomerSegment, PromotionEntry}
import com.agoda.commons.models.pricing.{PulseCampaignMetadata, CustomerSegment => PricingCustomerSegment}
import com.agoda.papi.ypl.models.api.request.YplClientInfo
import com.agoda.papi.ypl.models.enums.VipLevelType
import com.agoda.papi.ypl.models.pricing.proto.{CustomerSegment => ProtoCustomerSegment}
import com.agoda.papi.ypl.models.{PromotionTypeId, YplContext, YplExperiments, YplRequest}

import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.mock.Mockito

class PromotionEntryValidationSpec extends SpecificationWithJUnit with Mockito {
  class TestPromotionEntryValidationImpl extends PromotionEntryValidationImpl {}
  // For overriding
  val testPromotionTypeId_1 = 1
  val testWebCampaignId_11 = 11
  val testCampaignTypeId_111 = 111
  val testCampaignBadgeCmsId = 111001
  val campaignBadgeDescCmsId = 111002
  val thLanguageId = 99
  val sgLanguageId = 101
  val allLanguageId = 0
  val testCustomerSegment_TH_PLATINUM: PricingCustomerSegment =
    PricingCustomerSegment(1, 91, thLanguageId, "TH", VipLevelType.PLATINUM.value)
  val testCustomerSegment_TH_UNDEFINED: PricingCustomerSegment =
    PricingCustomerSegment(2, 91, thLanguageId, "TH", VipLevelType.UNDEFINED.value)
  val testCustomerSegment_SG_PLATINUM: PricingCustomerSegment =
    PricingCustomerSegment(3, 92, sgLanguageId, "SG", VipLevelType.PLATINUM.value)
  val testCustomerSegment_SG_UNDEFINED: PricingCustomerSegment =
    PricingCustomerSegment(4, 92, sgLanguageId, "SG", VipLevelType.UNDEFINED.value)
  val testCustomerSegment_AllLang_JP_UNDEFINED: PricingCustomerSegment =
    PricingCustomerSegment(5, 92, allLanguageId, "JP", VipLevelType.UNDEFINED.value)
  val pulseCampaignMetadata_1_withNoOverridingCs: PulseCampaignMetadata = PulseCampaignMetadata(testPromotionTypeId_1,
                                                                                                testWebCampaignId_11,
                                                                                                testCampaignTypeId_111,
                                                                                                testCampaignBadgeCmsId,
                                                                                                campaignBadgeDescCmsId)
  val pricingCustomerSegmentRestrictions: List[PricingCustomerSegment] = List(
    testCustomerSegment_TH_PLATINUM,
    testCustomerSegment_TH_UNDEFINED,
    testCustomerSegment_SG_PLATINUM,
    testCustomerSegment_SG_UNDEFINED,
    testCustomerSegment_AllLang_JP_UNDEFINED,
  )
  val protoCustomerSegmentRestrictions: List[ProtoCustomerSegment] = pricingCustomerSegmentRestrictions.map(cs =>
    ProtoCustomerSegment(if (cs.languageId == 0) {
                           None
                         } else {
                           Some(cs.languageId)
                         },
                         Option(cs.countryIso2),
                         Option(VipLevelType.getFromValue(cs.vipLevel))))
  val pulseCampaignMetadata_1_WithOverridingCs: PulseCampaignMetadata = pulseCampaignMetadata_1_withNoOverridingCs.copy(
    customerSegmentOverride = pricingCustomerSegmentRestrictions,
  )
  "PromotionEntryValidation getOverridingCustomerSegmentRestrictions" should {

    "return customer segment overrides if present" in {
      val promotion = mock[PromotionEntry]
      promotion.typeId returns testPromotionTypeId_1

      val pulseCampaignMetadataByPromotionTypeId = Map(testPromotionTypeId_1 -> pulseCampaignMetadata_1_WithOverridingCs)

      val validator = new TestPromotionEntryValidationImpl()
      val result = validator.getOverridingCustomerSegmentRestrictions(promotion, pulseCampaignMetadataByPromotionTypeId)

      result must beEqualTo(
        List(
          ProtoCustomerSegment(Some(thLanguageId), Some("TH"), Some(VipLevelType.PLATINUM)),
          ProtoCustomerSegment(Some(thLanguageId), Some("TH"), Some(VipLevelType.UNDEFINED)),
          ProtoCustomerSegment(Some(sgLanguageId), Some("SG"), Some(VipLevelType.PLATINUM)),
          ProtoCustomerSegment(Some(sgLanguageId), Some("SG"), Some(VipLevelType.UNDEFINED)),
          ProtoCustomerSegment(None, Some("JP"), Some(VipLevelType.UNDEFINED)),
        ))
    }

    "return None if no overrides are present" in {
      val promotion = mock[PromotionEntry]
      promotion.typeId returns testPromotionTypeId_1

      val pulseCampaignMetadataByPromotionTypeId =
        Map(testPromotionTypeId_1 -> pulseCampaignMetadata_1_withNoOverridingCs)

      val validator = new TestPromotionEntryValidationImpl()
      val result = validator.getOverridingCustomerSegmentRestrictions(promotion, pulseCampaignMetadataByPromotionTypeId)

      result must be(List.empty[CustomerSegment])
    }
  }
  "PromotionEntryValidationExtensions shouldUseOverridingCustomerSegment" should {

    "return true if experiment is enabled and overrides exist" in {
      implicit val ctx: YplContext = mock[YplContext]
      ctx.request returns mock[YplRequest]
      ctx.request.isAllMseTraffic returns false
      ctx.experimentContext returns mock[com.agoda.utils.flow.ExperimentContext]
      ctx.experimentContext.isUserB(
        YplExperiments.ENABLE_DISPATCHING_OVERRIDE_ON_CUSTOMER_SEGMENT_USING_PULSE_PRECACHE) returns true
      val promotion = mock[PromotionEntry]
      promotion.typeId returns testPromotionTypeId_1
      val pulseCampaignMetadataByPromotionTypeId = Map(testPromotionTypeId_1 -> pulseCampaignMetadata_1_WithOverridingCs)

      val spyValidator = spy(new TestPromotionEntryValidationImpl())

      spyValidator.getOverridingCustomerSegmentRestrictions(promotion, pulseCampaignMetadataByPromotionTypeId) returns
        pulseCampaignMetadata_1_WithOverridingCs.customerSegmentOverride.map { cs =>
          CustomerSegment(
            if (cs.languageId == 0) None else Some(cs.languageId),
            Option(cs.countryIso2),
            Option(VipLevelType.getFromValue(cs.vipLevel)),
          )
        }
      val result = spyValidator.shouldUseOverridingCustomerSegment(promotion, pulseCampaignMetadataByPromotionTypeId)

      result must beTrue
    }

    "return false if experiment is disabled" in {
      implicit val ctx: YplContext = mock[YplContext]
      ctx.request returns mock[YplRequest]
      ctx.request.isAllMseTraffic returns false
      ctx.experimentContext returns mock[com.agoda.utils.flow.ExperimentContext]
      ctx.experimentContext.isUserB(
        YplExperiments.ENABLE_DISPATCHING_OVERRIDE_ON_CUSTOMER_SEGMENT_USING_PULSE_PRECACHE) returns false

      val promotion = mock[PromotionEntry]
      promotion.typeId returns testPromotionTypeId_1

      val pulseCampaignMetadataByPromotionTypeId = Map(testPromotionTypeId_1 -> pulseCampaignMetadata_1_WithOverridingCs)

      val spyValidator = spy(new TestPromotionEntryValidationImpl())

      spyValidator.getOverridingCustomerSegmentRestrictions(promotion, pulseCampaignMetadataByPromotionTypeId) returns
        pulseCampaignMetadata_1_WithOverridingCs.customerSegmentOverride.map { cs =>
          CustomerSegment(
            if (cs.languageId == 0) None else Some(cs.languageId),
            Option(cs.countryIso2),
            Option(VipLevelType.getFromValue(cs.vipLevel)),
          )
        }
      val result = spyValidator.shouldUseOverridingCustomerSegment(promotion, pulseCampaignMetadataByPromotionTypeId)

      result must beFalse
    }
    "return false if isAllMseTraffic" in {
      implicit val ctx: YplContext = mock[YplContext]
      ctx.request returns mock[YplRequest]
      ctx.request.isAllMseTraffic returns true
      ctx.experimentContext returns mock[com.agoda.utils.flow.ExperimentContext]
      ctx.experimentContext.isUserB(
        YplExperiments.ENABLE_DISPATCHING_OVERRIDE_ON_CUSTOMER_SEGMENT_USING_PULSE_PRECACHE) returns true
      val promotion = mock[PromotionEntry]
      promotion.typeId returns testPromotionTypeId_1

      val pulseCampaignMetadataByPromotionTypeId = Map(testPromotionTypeId_1 -> pulseCampaignMetadata_1_WithOverridingCs)

      val spyValidator = spy(new TestPromotionEntryValidationImpl())

      spyValidator.getOverridingCustomerSegmentRestrictions(promotion, pulseCampaignMetadataByPromotionTypeId) returns
        pulseCampaignMetadata_1_WithOverridingCs.customerSegmentOverride.map { cs =>
          CustomerSegment(
            if (cs.languageId == 0) None else Some(cs.languageId),
            Option(cs.countryIso2),
            Option(VipLevelType.getFromValue(cs.vipLevel)),
          )
        }
      val result = spyValidator.shouldUseOverridingCustomerSegment(promotion, pulseCampaignMetadataByPromotionTypeId)

      result must beFalse
    }
  }

  "PromotionEntryValidationExtensions getVipSegmentRestrictions" should {
    "return correct VipSegmentRestrictions" in {
      val spyValidator = spy(new TestPromotionEntryValidationImpl())
      val result = spyValidator.getVipSegmentRestrictions(protoCustomerSegmentRestrictions)
      result must beEqualTo(
        List(
          ProtoCustomerSegment(Option(thLanguageId), Option("TH"), Option(VipLevelType.PLATINUM)),
          ProtoCustomerSegment(Option(sgLanguageId), Option("SG"), Option(VipLevelType.PLATINUM)),
        ))
    }
  }
  "PromotionEntryValidationExtensions isValidVipSegment" should {
    "return true if found matching on any level" in {
      implicit val ctx: YplContext = mock[YplContext]
      val mockReq = mock[YplRequest]
      mockReq.cInfo returns YplClientInfo(
        language = thLanguageId,
        vipLevel = Some(VipLevelType.PLATINUM),
      )
      ctx.request returns mockReq

      val spyValidator = spy(new TestPromotionEntryValidationImpl())
      spyValidator.getVipSegmentRestrictions(protoCustomerSegmentRestrictions) returns
        List(ProtoCustomerSegment(Option(thLanguageId), Option("TH"), Option(VipLevelType.PLATINUM)))
      spyValidator.isValidVipSegment(
        protoCustomerSegmentRestrictions,
      ) must beTrue
    }
    "return true if empty vip restrictions" in {
      implicit val ctx: YplContext = mock[YplContext]
      val mockReq = mock[YplRequest]
      mockReq.cInfo returns YplClientInfo(
        language = thLanguageId,
        vipLevel = Some(VipLevelType.PLATINUM),
      )
      ctx.request returns mockReq
      val spyValidator = spy(new TestPromotionEntryValidationImpl())
      spyValidator.getVipSegmentRestrictions(protoCustomerSegmentRestrictions) returns
        List.empty
      spyValidator.isValidVipSegment(
        protoCustomerSegmentRestrictions.filter(cs => cs.vipLevel.exists(_.value == VipLevelType.UNDEFINED.value)),
      ) must beTrue
    }
    "return false if no matching vip level" in {
      implicit val ctx: YplContext = mock[YplContext]
      val mockReq = mock[YplRequest]
      mockReq.cInfo returns YplClientInfo(
        language = thLanguageId,
        vipLevel = Some(VipLevelType.GOLD),
      )
      ctx.request returns mockReq

      val spyValidator = spy(new TestPromotionEntryValidationImpl())
      spyValidator.getVipSegmentRestrictions(protoCustomerSegmentRestrictions) returns
        List(ProtoCustomerSegment(Option(thLanguageId), Option("TH"), Option(VipLevelType.PLATINUM)))
      spyValidator.isValidVipSegment(
        protoCustomerSegmentRestrictions,
      ) must beFalse
    }

    "return true if found matching on diamond" in {
      implicit val ctx: YplContext = mock[YplContext]
      val mockReq = mock[YplRequest]
      mockReq.cInfo returns YplClientInfo(
        language = thLanguageId,
        vipLevel = Some(VipLevelType.DIAMOND),
      )
      ctx.experimentContext returns mock[com.agoda.utils.flow.ExperimentContext]
      ctx.request returns mockReq

      val spyValidator = spy(new TestPromotionEntryValidationImpl())
      spyValidator.getVipSegmentRestrictions(protoCustomerSegmentRestrictions) returns
        List(ProtoCustomerSegment(Option(thLanguageId), Option("TH"), Option(VipLevelType.PLATINUM)))
      spyValidator.isValidVipSegment(
        protoCustomerSegmentRestrictions,
      ) must beTrue
    }
  }

  "PromotionEntryValidationExtensions getNonVipSegmentRestrictions" should {

    "return correct NonVipSegmentRestrictions" in {
      val spyValidator = spy(new TestPromotionEntryValidationImpl())
      val result = spyValidator.getNonVipSegmentRestrictions(protoCustomerSegmentRestrictions)
      result must beEqualTo(
        List(
          ProtoCustomerSegment(Option(thLanguageId), Option("TH"), Option(VipLevelType.UNDEFINED)),
          ProtoCustomerSegment(Option(sgLanguageId), Option("SG"), Option(VipLevelType.UNDEFINED)),
          ProtoCustomerSegment(None, Option("JP"), Option(VipLevelType.UNDEFINED)),
        ))
    }
  }
  "PromotionEntryValidationExtensions isValidLanguageAndOriginCustomerSegment" should {
    "return true if found matching " in {

      val spyValidator = spy(new TestPromotionEntryValidationImpl())

      spyValidator.getVipSegmentRestrictions(protoCustomerSegmentRestrictions) returns
        List(ProtoCustomerSegment(Option(thLanguageId), Option("TH"), Option(VipLevelType.UNDEFINED)))
      spyValidator.isValidLanguageAndOriginCustomerSegment(
        protoCustomerSegmentRestrictions,
        thLanguageId,
        "TH",
      ) must beTrue
    }
    "return true if empty on non vip restrictions" in {

      val spyValidator = spy(new TestPromotionEntryValidationImpl())

      spyValidator.getVipSegmentRestrictions(protoCustomerSegmentRestrictions) returns
        List.empty
      spyValidator.isValidLanguageAndOriginCustomerSegment(
        protoCustomerSegmentRestrictions,
        thLanguageId,
        "TH",
      ) must beTrue
    }
    "return false if no matching " in {

      val spyValidator = spy(new TestPromotionEntryValidationImpl())

      spyValidator.getVipSegmentRestrictions(protoCustomerSegmentRestrictions) returns
        List(ProtoCustomerSegment(Option(thLanguageId), Option("TH"), Option(VipLevelType.UNDEFINED)))
      spyValidator.isValidLanguageAndOriginCustomerSegment(
        protoCustomerSegmentRestrictions,
        thLanguageId,
        "IN",
      ) must beFalse
    }
  }

  "PromotionEntryValidationExtensions isValidByOverridingCustomerSegment" should {

    "return false if shouldUseOverridingCustomerSegment is false" in {
      implicit val ctx: YplContext = mock[YplContext]
      ctx.request returns mock[YplRequest]
      ctx.request.isAllMseTraffic returns false
      ctx.experimentContext returns mock[com.agoda.utils.flow.ExperimentContext]
      ctx.experimentContext.isUserB(
        YplExperiments.ENABLE_DISPATCHING_OVERRIDE_ON_CUSTOMER_SEGMENT_USING_PULSE_PRECACHE) returns false
      val promotion = mock[PromotionEntry]
      promotion.typeId returns testPromotionTypeId_1
      val pulseCampaignMetadataByPromotionTypeId = Map(testPromotionTypeId_1 -> pulseCampaignMetadata_1_WithOverridingCs)

      val spyValidator = spy(new TestPromotionEntryValidationImpl())

      spyValidator.shouldUseOverridingCustomerSegment(promotion, pulseCampaignMetadataByPromotionTypeId) returns false

      spyValidator.isValidByOverridingCustomerSegment(
        promotion,
        pulseCampaignMetadataByPromotionTypeId,
        "TH",
        thLanguageId,
      )(ctx) must beFalse
    }

    "return false if there are no overriding customer segment restrictions" in {
      implicit val ctx: YplContext = mock[YplContext]
      ctx.request returns mock[YplRequest]
      ctx.request.isAllMseTraffic returns false
      ctx.experimentContext returns mock[com.agoda.utils.flow.ExperimentContext]
      ctx.experimentContext.isUserB(
        YplExperiments.ENABLE_DISPATCHING_OVERRIDE_ON_CUSTOMER_SEGMENT_USING_PULSE_PRECACHE) returns true
      val promotion = mock[PromotionEntry]
      promotion.typeId returns testPromotionTypeId_1
      val spyValidator = spy(new TestPromotionEntryValidationImpl())
      val pulseCampaignMetadataByPromotionTypeId = Map.empty[PromotionTypeId, PulseCampaignMetadata]
      spyValidator.shouldUseOverridingCustomerSegment(promotion, pulseCampaignMetadataByPromotionTypeId) returns true
      spyValidator.isValidByOverridingCustomerSegment(promotion,
                                                      pulseCampaignMetadataByPromotionTypeId,
                                                      "TH",
                                                      thLanguageId) must beFalse
    }

    "return true if shouldUseOverridingCustomerSegment true , " +
      "there are overriding customer segment restrictions , valid vip and valid on non-vip segments with specific lang restriction" in {
        implicit val ctx: YplContext = mock[YplContext]
        val mockReq = mock[YplRequest]
        mockReq.cInfo returns YplClientInfo(
          language = thLanguageId,
          vipLevel = Some(VipLevelType.PLATINUM),
        )
        ctx.request returns mockReq
        ctx.request.isAllMseTraffic returns false
        ctx.experimentContext returns mock[com.agoda.utils.flow.ExperimentContext]
        ctx.experimentContext.isUserB(
          YplExperiments.ENABLE_DISPATCHING_OVERRIDE_ON_CUSTOMER_SEGMENT_USING_PULSE_PRECACHE) returns true
        val promotion = mock[PromotionEntry]
        promotion.typeId returns testPromotionTypeId_1
        val pulseCampaignMetadataByPromotionTypeId =
          Map(testPromotionTypeId_1 -> pulseCampaignMetadata_1_WithOverridingCs)
        val spyValidator = spy(new TestPromotionEntryValidationImpl())
        spyValidator.shouldUseOverridingCustomerSegment(promotion, pulseCampaignMetadataByPromotionTypeId) returns true
        spyValidator.isValidByOverridingCustomerSegment(promotion,
                                                        pulseCampaignMetadataByPromotionTypeId,
                                                        "TH",
                                                        thLanguageId) must beTrue
      }
    "there are overriding customer segment restrictions , valid vip and valid on non-vip segments with any lang restriction" in {
      implicit val ctx: YplContext = mock[YplContext]
      val mockReq = mock[YplRequest]
      mockReq.cInfo returns YplClientInfo(
        language = thLanguageId,
        vipLevel = Some(VipLevelType.PLATINUM),
      )
      ctx.request returns mockReq
      ctx.request.isAllMseTraffic returns false
      ctx.experimentContext returns mock[com.agoda.utils.flow.ExperimentContext]
      ctx.experimentContext.isUserB(
        YplExperiments.ENABLE_DISPATCHING_OVERRIDE_ON_CUSTOMER_SEGMENT_USING_PULSE_PRECACHE) returns true
      val promotion = mock[PromotionEntry]
      promotion.typeId returns testPromotionTypeId_1
      val pulseCampaignMetadataByPromotionTypeId = Map(testPromotionTypeId_1 -> pulseCampaignMetadata_1_WithOverridingCs)
      val spyValidator = spy(new TestPromotionEntryValidationImpl())
      spyValidator.shouldUseOverridingCustomerSegment(promotion, pulseCampaignMetadataByPromotionTypeId) returns true
      spyValidator.isValidByOverridingCustomerSegment(promotion,
                                                      pulseCampaignMetadataByPromotionTypeId,
                                                      "JP",
                                                      thLanguageId) must beTrue
    }
  }
}
