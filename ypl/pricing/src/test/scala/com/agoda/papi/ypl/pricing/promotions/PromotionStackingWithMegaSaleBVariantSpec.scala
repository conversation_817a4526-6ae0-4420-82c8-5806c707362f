package com.agoda.papi.ypl.pricing.promotions

import com.agoda.papi.enums.room.DiscountType
import com.agoda.papi.ypl.models._

class PromotionStackingWithMegaSaleBVariantSpec extends PromotionStackingTestUtilities {

  val yplRequest: YplRequest = aValidYplRequest
    .withCheckIn(checkInDate)
    .withCheckout(checkoutDate)
    .withClientInfo(aValidClientInfo)
    .withBookingDate(checkInDate)
    .withBExperiment(YplExperiments.ENABLE_MEGA_CAMPAIGN)

  def runTestCases(testCase: PromotionStackingTestCase): Boolean = super.runTestCases(testCase, yplRequest)

  "1. Non-Stackable Promotions Only" should {
    "1.1 should apply single non-stackable promotion (5% discount)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, false)),
          List(),
          List(),
          5,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "1.2 should pick highest discount among multiple non-stackable promotions (15% > 10%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 15, false), Promotion(2, 202, 10, false), Promotion(3, 303, 10, false)),
          List(202),
          List(303),
          15,
          List(1),
          DiscountType.PercentDiscount,
        )) should_== true
    }
    "1.3 should pick highest discount when all discounts are high (70% > 60% > 55%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, false), Promotion(2, 202, 60, false), Promotion(3, 303, 70, false)),
          List(202),
          List(303),
          70,
          List(3),
          DiscountType.PercentDiscount,
          Some(303),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "1.4 should prioritize pulse promotion over mega sale when same discount (50%) - pulse wins by higher index" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 50, false), Promotion(2, 202, 50, false), Promotion(3, 103, 50, false)),
          List(202),
          List(301),
          50,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "1.5 should prioritize mega sale over pulse and normal promotion when mega sale has higher index (same 50% discount)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 50, false), Promotion(2, 302, 50, false), Promotion(3, 103, 50, false)),
          List(201),
          List(302),
          50,
          List(2),
          DiscountType.PercentDiscount,
          Some(302),
        )) should_== true
    }
    "1.6 should prioritize pulse over normal promotion when same discount (50%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 50, false), Promotion(2, 202, 50, false)),
          List(202),
          List(),
          50,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
    "1.7 should pick higher promotion ID when same discount for normal promotions (50%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 50, false), Promotion(2, 102, 50, false)),
          List(),
          List(),
          50,
          List(2),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "1.8 should pick higher promotion ID when same discount for pulse promotions (50%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 50, false), Promotion(2, 202, 50, false)),
          List(201, 202),
          List(),
          50,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
    "1.9 should pick higher promotion ID when same discount for mega sale promotions (50%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 50, false), Promotion(2, 302, 50, false)),
          List(),
          List(301, 302),
          50,
          List(2),
          DiscountType.PercentDiscount,
          Some(302),
        )) should_== true
    }
  }

  "2. Normal Stackable Promotions Only" should {
    "2.1 should apply single normal stackable promotion (5% discount)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, true)),
          List(),
          List(),
          5,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "2.2 should stack all normal promotions when total < 100% (5% + 15% + 25% = 45%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, true), Promotion(2, 102, 15, true), Promotion(3, 103, 25, true)),
          List(),
          List(),
          45,
          List(1, 2, 3),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed with business team
    "2.3 should reject all promotions when total > 100% (55% + 55% = 110%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, true), Promotion(2, 102, 55, true)),
          List(),
          List(),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed with business team
    "2.4 should pick last valid promotions when earlier combinations exceed 100% (55%+55%+55%>100%, pick 55%+15%=70%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, true),
               Promotion(2, 102, 55, true),
               Promotion(3, 103, 55, true),
               Promotion(4, 104, 15, true)),
          List(),
          List(),
          70,
          List(3, 4),
          DiscountType.Combined,
          None,
        )) should_== true
    }
  }

  "3. Pulse Stackable Promotions Only" should {
    "3.1 should apply single pulse stackable promotion (10% discount)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 10, true)),
          List(201),
          List(),
          10,
          List(1),
          DiscountType.PercentDiscount,
          Some(201),
        )) should_== true
    }
    "3.2 should pick highest discount pulse promotion (30% > 20% > 10%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 10, true), Promotion(2, 202, 20, true), Promotion(3, 203, 30, true)),
          List(201, 202, 203),
          List(),
          30,
          List(3),
          DiscountType.PercentDiscount,
          Some(203),
        )) should_== true
    }
    "3.3 should pick higher promotion ID when same discount for pulse promotions (10%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 10, true), Promotion(2, 202, 10, true)),
          List(201, 202),
          List(),
          10,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
    "3.4 should pick higher promotion ID when same discount and same promotion type (10%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 10, true), Promotion(2, 201, 10, true)),
          List(201),
          List(),
          10,
          List(2),
          DiscountType.PercentDiscount,
          Some(201),
        )) should_== true
    }
    "3.5 should pick higher promotion ID when total would exceed 100% (55% + 55% = 110%, pick higher ID)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 55, true), Promotion(2, 202, 55, true)),
          List(201, 202),
          List(),
          55,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
  }

  "4. Mega Sale Stackable Promotions Only" should {
    "4.1 should apply single mega sale stackable promotion (10% discount)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 10, true)),
          List(),
          List(301),
          10,
          List(1),
          DiscountType.PercentDiscount,
          Some(301),
        )) should_== true
    }
    "4.2 should select highest discount mega sale promotion from multiple options (30% > 20% > 10%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 10, true), Promotion(2, 302, 20, true), Promotion(3, 303, 30, true)),
          List(301, 302, 303),
          List(),
          30,
          List(3),
          DiscountType.PercentDiscount,
          Some(303),
        )) should_== true
    }
    "4.3 should pick higher promotion ID when same discount for mega sale promotions (10%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 10, true), Promotion(2, 302, 10, true)),
          List(),
          List(301, 302),
          10,
          List(2),
          DiscountType.PercentDiscount,
          Some(302),
        )) should_== true
    }
    "4.4 should pick higher promotion ID when same discount and same promotion type (10%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 10, true), Promotion(2, 301, 10, true)),
          List(),
          List(301),
          10,
          List(2),
          DiscountType.PercentDiscount,
          Some(301),
        )) should_== true
    }
    "4.5 should pick higher promotion ID when total would exceed 100% (55% + 55% = 110%, pick higher ID)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 55, true), Promotion(2, 302, 55, true)),
          List(),
          List(301, 302),
          55,
          List(2),
          DiscountType.PercentDiscount,
          Some(302),
        )) should_== true
    }
  }

  "5. Normal + Pulse Stackable Promotions" should {
    "5.1 should stack normal and pulse promotions (5% + 10% = 15%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, true), Promotion(2, 202, 10, true)),
          List(202),
          List(),
          15,
          List(1, 2),
          DiscountType.Combined,
          Some(202),
        )) should_== true
    }
    "5.2 should combine all normal promotions with single best pulse promotion (5% + 15% + 10% = 30%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, true),
               Promotion(2, 102, 15, true),
               Promotion(3, 203, 10, true),
               Promotion(4, 204, 10, true)),
          List(203, 204),
          List(),
          30,
          List(1, 2, 4),
          DiscountType.Combined,
          Some(204),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "5.3 should reject all promotions when normal + pulse total > 100% (55% + 55% = 110%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, true), Promotion(2, 202, 55, true)),
          List(202),
          List(),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed or fixed
    "5.4 should reject all when best pulse + normal > 100% (55% + 25% = 80%, but best pulse 55% + 55% > 100%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, true), Promotion(2, 202, 55, true), Promotion(3, 203, 25, true)),
          List(202, 203),
          List(),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
  }

  "6. Normal + Mega Sale Stackable Promotions" should {
    "6.1 should stack normal and mega sale promotions (5% + 10% = 15%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, true), Promotion(2, 302, 10, true)),
          List(),
          List(302),
          15,
          List(1, 2),
          DiscountType.Combined,
          Some(302),
        )) should_== true
    }
    "6.2 should stack all normal but only highest mega sale promotion (5% + 15% + 10% = 30%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, true),
               Promotion(2, 102, 15, true),
               Promotion(3, 303, 10, true),
               Promotion(4, 304, 10, true)),
          List(),
          List(303, 304),
          30,
          List(1, 2, 4),
          DiscountType.Combined,
          Some(304),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "6.3 should reject all promotions when normal + mega sale total > 100% (55% + 55% = 110%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, true), Promotion(2, 302, 55, true)),
          List(),
          List(302),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed or fixed
    "6.4 should reject all when best mega sale + normal > 100% (55% + 25% = 80%, but best mega sale 55% + 55% > 100%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, true), Promotion(2, 302, 55, true), Promotion(3, 303, 25, true)),
          List(),
          List(302, 303),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
  }

  "7. Pulse + Mega Sale Stackable Promotions" should {
    "7.1 should combine pulse and mega sale promotions with mega sale metadata priority (5% + 10% = 15%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 5, true), Promotion(2, 302, 10, true)),
          List(201),
          List(302),
          15,
          List(1, 2),
          DiscountType.Combined,
          Some(302),
        )) should_== true
    }
    "7.2 should pick highest pulse and mega sale with mega sale metadata (15% + 10% = 25%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 5, true),
               Promotion(2, 202, 15, true),
               Promotion(3, 303, 10, true),
               Promotion(4, 304, 10, true)),
          List(201, 202),
          List(303, 304),
          25,
          List(2, 4),
          DiscountType.Combined,
          Some(304),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "7.3 should reject all promotions when pulse + mega sale total > 100% (55% + 55% = 110%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 55, true), Promotion(2, 302, 55, true)),
          List(201),
          List(302),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed or fixed
    "7.4 should reject all when best pulse + mega sale > 100% (55% + 25% = 80%, but best 55% + 55% > 100%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 55, true), Promotion(2, 302, 55, true), Promotion(3, 303, 25, true)),
          List(201),
          List(302, 303),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    "7.5 should prioritize mega sale metadata over pulse when both present (15% + 10% = 25%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 15, true), Promotion(2, 302, 10, true)),
          List(201),
          List(302),
          25,
          List(1, 2),
          DiscountType.Combined,
          Some(302),
        )) should_== true
    }
  }

  "8. Normal + Pulse + Mega Sale Stackable Promotions" should {
    "8.1 should combine normal, pulse, and mega sale promotions with mega sale metadata priority (5% + 10% + 15% = 30%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, true), Promotion(2, 202, 10, true), Promotion(3, 303, 15, true)),
          List(202),
          List(303),
          30,
          List(1, 2, 3),
          DiscountType.Combined,
          Some(303),
        )) should_== true
    }
    "8.2 should pick all normal, highest pulse/mega sale with mega sale metadata (5% + 15% + 15% + 15% = 50%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, true),
               Promotion(2, 102, 15, true),
               Promotion(3, 203, 15, true),
               Promotion(4, 204, 10, true),
               Promotion(5, 305, 15, true),
               Promotion(6, 306, 10, true)),
          List(203, 204),
          List(305, 306),
          50,
          List(1, 2, 3, 5),
          DiscountType.Combined,
          Some(305),
        )) should_== true
    }
    "8.3 should pick all normal, highest ID pulse/mega sale when same discount (5% + 15% + 10% + 10% = 40%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, true),
               Promotion(2, 102, 15, true),
               Promotion(3, 203, 10, true),
               Promotion(4, 204, 10, true),
               Promotion(5, 305, 10, true),
               Promotion(6, 306, 10, true)),
          List(203, 204),
          List(305, 306),
          40,
          List(1, 2, 4, 6),
          DiscountType.Combined,
          Some(306),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "8.4 should pick last promotion when total > 100% and odd number of promotions (foldLeft impact)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, true), Promotion(2, 202, 55, true), Promotion(3, 303, 55, true)),
          List(202),
          List(303),
          55,
          List(3),
          DiscountType.PercentDiscount,
          Some(303),
        )) should_== true // assert false as no pick and result from an error
    }
    // This case should be reviewed or fixed
    "8.5 should reject all when total > 100% and even number of promotions (foldLeft impact)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, true),
               Promotion(2, 202, 55, true),
               Promotion(3, 303, 55, true),
               Promotion(4, 104, 55, true)),
          List(202),
          List(303),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed or fixed
    "8.6 should reject all even when partial stacking < 100% but total > 100% (35% + 35% + 35% = 105%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 35, true), Promotion(2, 202, 35, true), Promotion(3, 303, 35, true)),
          List(202),
          List(303),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
  }

  "9. Mixed Non-Stackable and Stackable Promotions" should {
    "9.01 should combine non-stackable and stackable normal promotions when total under 100% (5% + 15% = 20%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 5, false), Promotion(2, 102, 15, true)),
          List(),
          List(),
          20,
          List(1, 2),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    "9.02 should pick only non-stackable when combined >= 100% (55% + 55% = 110%, pick 55%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, false), Promotion(2, 102, 55, true)),
          List(),
          List(),
          55,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "9.03 should pick only non-stackable pulse when combined >= 100% (55% + 70% = 125%, pick 55%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 55, false), Promotion(2, 102, 15, true), Promotion(3, 103, 55, true)),
          List(201),
          List(),
          55,
          List(1),
          DiscountType.PercentDiscount,
          Some(201),
        )) should_== true
    }
    "9.04 should pick only non-stackable mega sale when combined >= 100% (55% + 70% = 125%, pick 55%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 55, false), Promotion(2, 102, 15, true), Promotion(3, 103, 55, true)),
          List(),
          List(301),
          55,
          List(1),
          DiscountType.PercentDiscount,
          Some(301),
        )) should_== true
    }
    "9.05 should pick non-stackable normal over pulse stackable when >= 100% (55% + 55% = 110%, pick 55%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, false), Promotion(2, 202, 55, true)),
          List(202),
          List(),
          55,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "9.06 should pick non-stackable normal over mega sale stackable when >= 100% (55% + 55% = 110%, pick 55%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, false), Promotion(2, 302, 55, true)),
          List(),
          List(302),
          55,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "9.07 should use pulse metadata when mega sale not picked due to stacking limits (15% + 20% = 35%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 15, false),
               Promotion(2, 302, 10, true),
               Promotion(3, 303, 90, true),
               Promotion(4, 204, 20, true)),
          List(204),
          List(302, 303),
          35,
          List(1, 4),
          DiscountType.Combined,
          Some(204),
        )) should_== true
    }
    "9.08 should reject mega sale when best mega sale + normal > 100% (15% only, no mega sale metadata)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 15, false),
               Promotion(2, 302, 10, true),
               Promotion(3, 303, 90, true),
               Promotion(4, 304, 20, true)),
          List(),
          List(302, 303, 304),
          15,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "9.09 should pick only non-stackable when combined >= 100% even if stackable sum < 100% (55% vs 50%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, false), Promotion(2, 101, 25, true), Promotion(3, 103, 25, true)),
          List(),
          List(),
          55,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "9.10 should pick non-stackable when combined exactly = 100% (10% + 90% = 100%, pick 10%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 10, false),
               Promotion(2, 102, 30, true),
               Promotion(3, 203, 30, true),
               Promotion(4, 304, 30, true)),
          List(203),
          List(304),
          10,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "9.11 should combine non-stackable normal with highest pulse when < 100% (55% + 30% = 85%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, false), Promotion(2, 202, 30, true), Promotion(3, 203, 25, true)),
          List(202, 203),
          List(),
          85,
          List(1, 2),
          DiscountType.Combined,
          Some(202),
        )) should_== true
    }
    "9.12 should combine non-stackable normal with highest mega sale when < 100% (55% + 30% = 85%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, false), Promotion(2, 302, 30, true), Promotion(3, 303, 25, true)),
          List(),
          List(302, 303),
          85,
          List(1, 2),
          DiscountType.Combined,
          Some(302),
        )) should_== true
    }
    "9.13 should pick highest type ID from pulse stackable when same discount (55% + 25% = 80%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, false), Promotion(2, 202, 25, true), Promotion(3, 203, 25, true)),
          List(202, 203),
          List(),
          80,
          List(1, 3),
          DiscountType.Combined,
          Some(203),
        )) should_== true
    }
    "9.14 should pick highest type ID from mega sale stackable when same discount (55% + 25% = 80%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 55, false), Promotion(2, 302, 25, true), Promotion(3, 303, 25, true)),
          List(),
          List(302, 303),
          80,
          List(1, 3),
          DiscountType.Combined,
          Some(303),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "9.15 should combine non-stackable and stackable pulse promotions (15% + 45% = 60%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 55, false), Promotion(2, 202, 15, false), Promotion(3, 203, 45, true)),
          List(201, 202, 203),
          List(),
          60,
          List(2, 3),
          DiscountType.Combined,
          Some(203),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "9.16 should combine non-stackable and stackable mega sale promotions (15% + 45% = 60%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 55, false), Promotion(2, 302, 15, false), Promotion(3, 303, 45, true)),
          List(),
          List(301, 302, 303),
          60,
          List(2, 3),
          DiscountType.Combined,
          Some(303),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "9.17 should combine non-stackable and stackable pulse even same type (15% + 45% = 60%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 55, false), Promotion(2, 201, 15, false), Promotion(3, 201, 45, true)),
          List(201),
          List(),
          60,
          List(2, 3),
          DiscountType.Combined,
          Some(201),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "9.18 should combine non-stackable and stackable mega sale even same type (15% + 45% = 60%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 55, false), Promotion(2, 301, 15, false), Promotion(3, 301, 45, true)),
          List(),
          List(301),
          60,
          List(2, 3),
          DiscountType.Combined,
          Some(301),
        )) should_== true
    }
    "9.19 should pick non-stackable pulse when all combinations equal (55% vs 55%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 201, 10, false), Promotion(2, 202, 55, false), Promotion(3, 203, 45, true)),
          List(201, 202, 203),
          List(),
          55,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
    "9.20 should pick non-stackable mega sale when all combinations equal (55% vs 55%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 301, 10, false), Promotion(2, 302, 55, false), Promotion(3, 303, 45, true)),
          List(),
          List(301, 302, 303),
          55,
          List(2),
          DiscountType.PercentDiscount,
          Some(302),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "9.21 KNOWN ISSUE: should pick optimal stackable combination but picks last due to ordering (50% + 25% = 75%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 50, false),
               Promotion(2, 102, 25, true),
               Promotion(3, 103, 25, true),
               Promotion(4, 104, 25, true)),
          List(),
          List(),
          75,
          List(1, 4),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed or fixed
    "9.22 KNOWN ISSUE: should pick 30% stackable but picks 20% due to ordering (50% + 20% = 70%)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 50, false),
               Promotion(2, 102, 30, true),
               Promotion(3, 103, 25, true),
               Promotion(4, 104, 20, true)),
          List(),
          List(),
          70,
          List(1, 4),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed or fixed
    "9.23 KNOWN ISSUE: should stack multiple < 100% but rejects due to total >= 100% (50% only)" in {
      runTestCases(
        PromotionStackingTestCase(
          List(Promotion(1, 101, 50, false),
               Promotion(2, 102, 20, true),
               Promotion(3, 203, 20, true),
               Promotion(4, 304, 20, true)),
          List(203),
          List(304),
          50,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
  }

}
