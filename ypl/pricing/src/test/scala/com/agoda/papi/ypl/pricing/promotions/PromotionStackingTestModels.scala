package com.agoda.papi.ypl.pricing.promotions

import com.agoda.papi.enums.room.DiscountType

/**
  * Shared test models for promotion stacking tests
  */

/**
  * Represents a single promotion for testing purposes
  *
  * @param promotionId Unique identifier for the promotion
  * @param promotionTypeId Type identifier used to classify promotion (Pulse, Mega Sale, etc.)
  * @param promotionDiscount Discount percentage (e.g., 15 for 15% discount)
  * @param isStackable Whether this promotion can be combined with other stackable promotions
  */
case class Promotion(
  promotionId: Int,
  promotionTypeId: Int,
  promotionDiscount: Double,
  isStackable: Boolean,
)

/**
  * Test case model for promotion stacking scenarios
  *
  * This model defines a comprehensive test case for validating promotion stacking behavior,
  * including both regular Pulse campaigns and Mega Sale promotions. It supports testing
  * various scenarios such as:
  *
  * - Non-stackable promotions (highest discount wins)
  * - Stackable promotions (multiple promotions combine)
  * - Pulse campaign metadata assignment
  * - Mega Sale promotion anti-stacking logic
  * - Mixed promotion type scenarios
  *
  * ## Test Case Structure:
  *
  * **Input Setup:**
  * @param availablePromotions List of promotions available for the room
  *                           Each promotion has: ID, type ID, discount %, stackable flag
  * @param pulsePromotionTypeIds List of promotion type IDs that should be treated as pulse campaigns
  * @param megaSalePromotionTypeIds List of promotion type IDs that should be treated as mega sale campaigns
  *
  * **Expected Results:**
  * @param finalDiscount Expected final discount percentage after all promotions are applied
  * @param finalAppliedPromotions Expected list of promotion IDs that should be applied
  * @param finalDiscountType Expected discount type (PercentDiscount, Combined, etc.)
  * @param finalPromotionTypeIdInPulseMetadata Expected promotion type ID in pulse campaign metadata
  *
  * ## Example Usage:
  * PromotionStackingTestCase(
  *   availablePromotions = List(
  *     Promotion(1, 101, 10, false), // Non-stackable 10% discount
  *     Promotion(2, 202, 15, true)   // Stackable 15% discount (Pulse)
  *   ),
  *   pulsePromotionTypeIds = List(202),
  *   finalDiscount = 15.0,
  *   finalAppliedPromotions = List(2),
  *   finalDiscountType = DiscountType.PercentDiscount,
  *   finalPromotionTypeIdInPulseMetadata = Some(202)
  * )
  */
case class PromotionStackingTestCase(
  availablePromotions: List[Promotion],
  pulsePromotionTypeIds: List[Int] = List(),
  megaSalePromotionTypeIds: List[Int] = List(),
  finalDiscount: Double,
  finalAppliedPromotions: List[Int],
  finalDiscountType: DiscountType,
  finalPromotionTypeIdInPulseMetadata: Option[Int] = None,
) {

  /**
    * Legacy constructor for backward compatibility with existing tests
    * that only use pulse promotion type IDs
    */
  def this(
    availablePromotions: List[Promotion],
    pulsePtypeIds: List[Int],
    finalDiscount: Double,
    finalAppliedPromotions: List[Int],
    finalDiscountType: DiscountType,
    finalPromotionTypeIdInPulseMetadata: Option[Int],
  ) = this(
    availablePromotions = availablePromotions,
    pulsePromotionTypeIds = pulsePtypeIds,
    megaSalePromotionTypeIds = List(),
    finalDiscount = finalDiscount,
    finalAppliedPromotions = finalAppliedPromotions,
    finalDiscountType = finalDiscountType,
    finalPromotionTypeIdInPulseMetadata = finalPromotionTypeIdInPulseMetadata,
  )
}

/**
  * Companion object with helper methods for creating test cases
  */
object PromotionStackingTestCase {

  /**
    * Create a test case with only pulse promotion type IDs (for backward compatibility)
    */
  def withoutMegaSale(
    availablePromotions: List[Promotion],
    pulsePromotionTypeIds: List[Int],
    finalDiscount: Double,
    finalAppliedPromotions: List[Int],
    finalDiscountType: DiscountType,
    finalPromotionTypeIdInPulseMetadata: Option[Int] = None,
  ): PromotionStackingTestCase = PromotionStackingTestCase(
    availablePromotions = availablePromotions,
    pulsePromotionTypeIds = pulsePromotionTypeIds,
    megaSalePromotionTypeIds = List(),
    finalDiscount = finalDiscount,
    finalAppliedPromotions = finalAppliedPromotions,
    finalDiscountType = finalDiscountType,
    finalPromotionTypeIdInPulseMetadata = finalPromotionTypeIdInPulseMetadata,
  )
}
