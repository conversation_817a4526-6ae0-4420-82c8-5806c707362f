package com.agoda.papi.ypl.pricing

import com.agoda.finance.tax.models.{
  TaxPrototypeInfo,
  TaxPrototypeLevel,
  SupplierHotelCalculationSetting,
  SupplierHotelCalculationSettings,
}
import com.agoda.finance.tax.services.tax.applytaxover.ApplyTaxOverHelper

import java.util.concurrent.TimeUnit
import com.agoda.papi.enums.hotel.{ChildrenStayFreeType, PaymentModel, TaxType}
import com.agoda.papi.enums.room.RateType.NetExclusive
import com.agoda.papi.enums.room.ChargeOption
import com.agoda.papi.enums.room._
import com.agoda.papi.ypl.commission.{
  AgxCommissionHolder,
  CommissionHolder,
  FencedAgencyCommissionHolder,
  MORPCandidateRoomParameters,
  ProtobufCommissionHolder,
}
import com.agoda.papi.ypl.commission.CommissionUtils
import com.agoda.papi.ypl.models.api.request.{YplAGXCommission, YplChildren, YplOccInfo}
import com.agoda.papi.ypl.models.builders.ypl.{YplContextMock, YplRequestBuilder}
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.enums.GrowthProgramCommissionBreakdown
import com.agoda.papi.ypl.models.hotel.AgePolicy
import com.agoda.papi.pricing.pricecalculation.models.tax.DailyTaxes.TaxesWithValues
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.pricing.{RoomOccupancy, YplPrice}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{
  EnabledRoom,
  HotelId,
  PropOfferOccupancy,
  RoomTypeId,
  SupplierId,
  WholesaleMetadata,
  YPLRoom,
  YPLTestContexts,
  YPLTestDataBuilders,
  YplChannel,
  YplContext,
  YplExperiment,
  YplExperiments,
  YplMasterChannel,
  YplRegulationFeatureEnabledSetting,
  YplReqOccByHotelAgePolicy,
  YplRequest,
  YplRoomEntry,
}
import com.agoda.papi.ypl.commission.CommissionUtils.convertToMarkup
import com.agoda.papi.ypl.commission.service.CommissionServiceImpl
import com.agoda.papi.ypl.pricing.PriceCalculator.{TO_PERCENT, calculateCommission}
import com.agoda.papi.ypl.pricing.helpers.PriceChecker
import com.agoda.papi.ypl.pricing.promotions.Util
import com.agoda.papi.ypl.services.TaxPrototypeServiceImpl
import com.agoda.papi.ypl.settings.MOHUGdsCommissionFeeSettings
import com.agoda.papi.ypl.utils.CsvHelper
import com.github.marklister.collections.io.CsvParser
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import org.specs2.execute.{AsResult, ResultExecution, Success}
import org.specs2.mutable.SpecificationWithJUnit
import com.agoda.papi.enums.room.ApplyTaxOver
import com.agoda.papi.enums.request.FeatureFlag
import com.agoda.papi.pricing.pricecalculation.api.PriceBreakdownCalculatorInterface
import com.agoda.papi.pricing.pricecalculation.models.response.Calculation
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes, Tax, TaxWithValue}
import com.agoda.papi.pricing.pricecalculation.models.{CommonProcessingFeeBreakdown, TaxAndFeePortionBreakdown}
import com.agoda.papi.pricing.pricecalculation.pricing.{
  CommissionCalculatorImpl,
  PriceBreakdownCalculatorImpl,
  TaxBreakdownCalculatorImpl,
}
import com.agoda.papi.pricing.pricecalculation.utils.TaxFiltersCacheInitialization
import com.agoda.papi.ypl.models.occupancy.{MaxAllowedFreeChildAgeRange, OccupancyBreakdown}
import com.agoda.supply.calc.proto.TaxApplyBreakdownType
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.SupplierRateInfo.ExternalData
import com.agoda.utils.flow.{PropertyContext, PropertyContextImpl}
import models.consts.ABTest
import com.agoda.papi.ypl.pricing.mocks.MockPriceBreakdownCalculator
import models.flow.Variant
import org.mockito.Mockito.when
import org.specs2.mock.Mockito.mock
import org.specs2.specification.core.Fragment

import scala.concurrent.duration.Duration

object PriceCalculationSpec extends YPLTestDataBuilders {

  private def throwExceptionAsTheCommissionValueShouldBeUsed: Double =
    throw new Exception("The Commission excluding Agx and Wholesale shouldn't be used!")

  case class InputHotel(testId: Int,
                        hotelId: Int,
                        supplierId: Int,
                        paymentModel: Int,
                        taxType: Int,
                        isUseConfigPF: Boolean)

  case class InputTax(testId: Int,
                      taxId: Int,
                      applyTo: String,
                      isFee: Boolean,
                      isAmount: Boolean,
                      isTaxable: Boolean,
                      value: Double,
                      option: Int)

  case class InputRoomEntry(testId: Int,
                            refId: Int,
                            roomTypeId: Int,
                            ratePlanId: Int,
                            rateType: Int,
                            processingFee: Double,
                            adults: Int,
                            children: Int,
                            extraBed: Int,
                            cxlCode: String,
                            promotionId: Int,
                            surchargeMarkup: Double)

  case class InputRoomHourlySlots(testId: Int, refId: Int, checkIn: String, duration: Int)

  case class InputPrice(testId: Int,
                        refId: Int,
                        date: String,
                        commission: Double,
                        taxes: String,
                        chargeType: Int,
                        chargeOption: Int,
                        applyTo: String,
                        occupancy: Int,
                        quantity: Int,
                        value: Double,
                        discount: Double)

  case class InputRequest(testId: Int, checkIn: String, los: Int, adults: Int, children: Int, rooms: Int)

  case class ExpectedRoom(testId: Int,
                          refId: Int,
                          roomTypeId: Int,
                          ratePlanId: Int,
                          supplierId: Int,
                          adults: Int,
                          children: Int,
                          extraBed: Int,
                          cxlCode: String,
                          promotionId: Int)

  case class ExpectedHourlySlots(testId: Int, refId: Int, checkIn: String, duration: Int)

  case class ExpectedPrice(testId: Int,
                           refId: Int,
                           date: String,
                           chargeType: Int,
                           option: Int,
                           netEx: Double,
                           tax: Double,
                           fee: Double,
                           margin: Double,
                           processingFee: Double,
                           discount: Double,
                           sId: Int)

  case class ExpectedTax(testId: Int,
                         refId: Int,
                         date: String,
                         chargeType: Int,
                         taxId: Int,
                         applyTo: String,
                         isFee: Boolean,
                         option: Int,
                         percentage: Double,
                         quantity: Int,
                         amount: Double,
                         include: Boolean)

  case class Case(testId: Int,
                  r: Option[InputRequest],
                  hotel: InputHotel,
                  taxes: Option[Seq[InputTax]],
                  rooms: Map[Int, Seq[InputRoomEntry]],
                  hourlySlots: Map[Int, Seq[InputRoomHourlySlots]],
                  prices: Map[Int, Seq[InputPrice]],
                  expectedRooms: Map[Int, Seq[ExpectedRoom]],
                  expectedPrices: Map[Int, Seq[ExpectedPrice]],
                  expectedTaxes: Map[Int, Seq[ExpectedTax]],
                  expectedHourlySlots: Map[Int, Seq[ExpectedHourlySlots]]) {

    lazy val request =
      if (r.isDefined) {
        val cin = Util.convertTime(r.get.checkIn)
        YplRequest(
          r.get.testId.toString,
          cin,
          cin.plusDays(r.get.los),
          occ =
            YplOccInfo(Some(r.get.adults), Some(YplChildren(r.get.children, Some(5), Map.empty)), Some(r.get.rooms)),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
        )
      } else YplRequest(
        testId.toString,
        DateTime.now,
        DateTime.now.plusDays(1),
        supplierFeatures = aValidSupplierFeatures,
        whitelabelSetting = aValidwhitelabelSetting,
        fences = aValidYplRequestFences,
        applyTaxOnSellExSettings = Some(aValidApplyTaxOnSellExSettings),
      )

    lazy val taxList: Map[TaxIDWithProTypeID, Tax] =
      if (taxes.isDefined) {
        taxes.get.map { t =>
          (t.taxId, Tax.DEFAULT_PROTOTYPE_ID) -> Tax(t.taxId,
                                                     t.applyTo,
                                                     t.isAmount,
                                                     t.isFee,
                                                     t.isTaxable,
                                                     t.value,
                                                     ChargeOption.getFromValue(t.option))
        }.toMap
      } else {
        Map(
          (1, Tax.DEFAULT_PROTOTYPE_ID) -> Tax(1, "PB", false, false, false, 7.0, ChargeOption.Mandatory),
          (2, Tax.DEFAULT_PROTOTYPE_ID) -> Tax(2, "PB", false, true, true, 10.0, ChargeOption.Mandatory),
        )
      }

    lazy val taxInfo = TaxInfo(
      hotelTaxInfo = HotelTaxInfo(
        TaxType.getFromValue(hotel.taxType),
        hotel.isUseConfigPF,
      ),
      taxes = taxList,
    )

    lazy val roomEntries = rooms.map { x =>
      val r = x._2.head
      val rateType = RateType.getFromValue(r.rateType)
      val commissionHolder: CommissionHolder = getCommissionHolder(prices, x)
      r.refId -> YplRoomEntry(
        roomTypeId = r.roomTypeId,
        masterRoomTypeId = r.roomTypeId,
        channel = YplMasterChannel(r.ratePlanId),
        cxlCode = r.cxlCode,
        isBreakFastIncluded = true,
        remainingRooms = 10,
        currency = "THB",
        rateType = rateType,
        processingFees = r.processingFee,
        isAllowCombinePromotion = true,
        roomType = RoomTypeEntry(r.roomTypeId, 3, 1, 1, false, 0, 0),
        dailyPrices = createDailyPrices(prices.get(x._1).get),
        occEntry = RoomOccupancy(r.adults, r.children, extraBeds = r.extraBed),
        originalRateType = rateType,
        inventoryType = InventoryType.Agoda,
        rateCategory = aValidRateCategoryEntry,
        commissionHolder = commissionHolder,
        propOfferOccupancy = PropOfferOccupancy(r.adults, r.children),
      )
    }

    private def priceEntryAt(d: DateTime) = { p: PriceCalculationSpec.InputPrice =>
      PriceEntry(d,
                 ChargeType.getFromValue(p.chargeType),
                 ChargeOption.getFromValue(p.chargeOption),
                 p.applyTo,
                 p.occupancy,
                 p.value,
                 p.quantity,
                 p.discount)
    }

    lazy val hourlyRoomEntries = rooms.map { x =>
      val r = x._2.head
      val rateType = RateType.getFromValue(r.rateType)
      val commissionHolder = getCommissionHolder(prices, x)
      r.refId -> YplRoomEntry(
        roomTypeId = r.roomTypeId,
        masterRoomTypeId = r.roomTypeId,
        channel = YplMasterChannel(r.ratePlanId),
        cxlCode = r.cxlCode,
        isBreakFastIncluded = true,
        remainingRooms = 10,
        currency = "THB",
        rateType = rateType,
        processingFees = r.processingFee,
        isAllowCombinePromotion = true,
        roomType = RoomTypeEntry(r.roomTypeId, 3, 1, 1, false, 0, 0),
        dailyPrices = createDailyPrices(prices.get(x._1).get),
        occEntry = RoomOccupancy(r.adults, r.children, extraBeds = r.extraBed),
        originalRateType = rateType,
        inventoryType = InventoryType.Agoda,
        hourlyAvailableSlots = hourlySlots(r.refId).map { slot =>
          createHourlySlot(slot.duration, slot.checkIn)
        },
        rateCategory = aValidRateCategoryEntry,
        commissionHolder = commissionHolder,
        propOfferOccupancy = PropOfferOccupancy(r.adults, r.children),
      )
    }

    private def createDailyPrices(prices: Seq[InputPrice]) = {
      val byDate = prices.groupBy(_.date)
      byDate.map { x =>
        val date = Util.convertTime(x._1)
        val first = x._2.head
        date -> DailyPrice(date,
                           createDailyTaxes(first.taxes),
                           isPromotionBlackOut = false,
                           x._2.map(priceEntryAt(date)).toList)
      }
    }

    private def createDailyTaxes(t: String): Map[TaxIDWithProTypeID, TaxValue] =
      if (t.nonEmpty) t
        .split('|')
        .map { x =>
          val tk = x.split('=')
          (tk(0).toInt, Tax.DEFAULT_PROTOTYPE_ID) -> tk(1).toDouble
        }
        .toMap
      else Map()
  }

  private def getCommissionHolder(prices: Map[SupplierId, Seq[InputPrice]], x: (SupplierId, Seq[InputRoomEntry])) = {
    val inputPrices = prices.get(x._1).get
    val dailyHolder = inputPrices.groupBy(_.date).map { case (dateStr, values) =>
      Util.convertTime(dateStr) -> aValidCommissionDailyHolder
        .withProtobufCommissionHolder(ProtobufCommissionHolder.default.withValue(values.head.commission))
        .build
    }
    val commissionHolder = aValidCommissionHolder.withDaily(dailyHolder).build
    commissionHolder
  }

  private def createHourlySlot(inputDuration: Int, inputCheckIn: String) = {
    val duration = Duration(inputDuration, TimeUnit.HOURS)
    val format = DateTimeFormat.forPattern("HH:mm")
    TimeInterval(duration, format.parseLocalTime(inputCheckIn))
  }
}

class PriceCalculationSpec extends SpecificationWithJUnit with PriceTestUtil with YPLTestContexts {

  import PriceCalculationSpec._

  private val COUNTRY_ID_CANADA: Long = 100
  private val TAX_TYPE_ID_VAT: Int = 4
  private val BC_ACCOM_TAX_PST: Int = 100
  private val BC_ACCOM_TAX_MRDT: Int = 101
  private val BC_ACCOM_TAX_MAJOR_MRDT: Int = 102
  private val BC_COMM_TAX_DC: Int = 103
  private val BC_COMM_TAX_SUPERAGG: Int = 104
  private val TAX_ON_COMM: Int = 110
  private val HILTON_CHAIN_ID: Int = 13
  private val DIRECT_CONNECT_DMC = 332
  private val SUPERAGG_DMC = 29071

  TaxFiltersCacheInitialization.initializeMockCache()

  private def loadCases = {
    val inputRequests = CsvParser(InputRequest)
      .parse(CsvHelper.getCsvBodyReader("/com/agoda/papi/ypl/pricing/calculation/request.txt"))
      .groupBy(_.testId)
    val inputHotels = CsvParser(InputHotel)
      .parse(CsvHelper.getCsvBodyReader("/com/agoda/papi/ypl/pricing/calculation/hotel.txt"))
      .groupBy(_.testId)
    val inputTaxes = CsvParser(InputTax)
      .parse(CsvHelper.getCsvBodyReader("/com/agoda/papi/ypl/pricing/calculation/taxes.txt"))
      .groupBy(_.testId)
    val inputRoomEntries = CsvParser(InputRoomEntry)
      .parse(CsvHelper.getCsvBodyReader("/com/agoda/papi/ypl/pricing/calculation/roomentry.txt"))
      .groupBy(_.testId)
    val inputRoomHourlySlots = CsvParser(InputRoomHourlySlots)
      .parse(CsvHelper.getCsvBodyReader("/com/agoda/papi/ypl/pricing/calculation/room_entry_hourly_slots.txt"))
      .groupBy(_.testId)
    val inputPrices = CsvParser(InputPrice)
      .parse(CsvHelper.getCsvBodyReader("/com/agoda/papi/ypl/pricing/calculation/prices.txt"))
      .groupBy(_.testId)
    val expectedRooms = CsvParser(ExpectedRoom)
      .parse(CsvHelper.getCsvBodyReader("/com/agoda/papi/ypl/pricing/calculation/expected_room.txt"))
      .groupBy(_.testId)
    val expectedPrices = CsvParser(ExpectedPrice)
      .parse(CsvHelper.getCsvBodyReader("/com/agoda/papi/ypl/pricing/calculation/expected_price.txt"))
      .groupBy(_.testId)
    val expectedHourlySlots = CsvParser(ExpectedHourlySlots)
      .parse(CsvHelper.getCsvBodyReader("/com/agoda/papi/ypl/pricing/calculation/expected_hourly_slots.txt"))
      .groupBy(_.testId)
    val expectedTaxes = CsvParser(ExpectedTax)
      .parse(CsvHelper.getCsvBodyReader("/com/agoda/papi/ypl/pricing/calculation/expected_tax.txt"))
      .groupBy(_.testId)
    inputHotels.map { case (testId, hotels) =>
      testId -> Case(
        testId,
        inputRequests.get(testId).flatMap(_.headOption),
        hotels.head,
        inputTaxes.get(testId),
        inputRoomEntries(testId).groupBy(_.refId),
        inputRoomHourlySlots(testId).groupBy(_.refId),
        inputPrices(testId).groupBy(_.refId),
        expectedRooms(testId).groupBy(_.refId),
        expectedPrices(testId).groupBy(_.refId),
        expectedTaxes.getOrElse(testId, Seq()).groupBy(_.refId),
        expectedHourlySlots.getOrElse(testId, Seq()).groupBy(_.refId),
      )
    }
  }

  val allCases: Map[SupplierId, Case] = loadCases

  val calc = new AnyRef
    with PriceCalculatorImpl
    with PriceCalculationImpl
    with TaxCalculatorImpl
    with TaxPrototypeServiceImpl
    with CommissionServiceImpl {
    override val priceBreakdownCalculator: PriceBreakdownCalculatorInterface =
      new PriceBreakdownCalculatorImpl(new TaxBreakdownCalculatorImpl(), new CommissionCalculatorImpl())
  }

  private def compareResult(result: YPLRoom,
                            hotel: InputHotel,
                            expectedRoom: ExpectedRoom,
                            expectedPrices: Seq[ExpectedPrice],
                            expectedTaxes: Seq[ExpectedTax]) = {
    result.hotelId must_== hotel.hotelId
    result.supplierId must_== expectedRoom.supplierId
    result.paymentModel.i must_== hotel.paymentModel
    result.occ.adults must_== expectedRoom.adults
    result.occ.children must_== expectedRoom.children
    result.cxlCode must_== expectedRoom.cxlCode
    result.extraBed must_== expectedRoom.extraBed
    result.channel.baseChannelId must_== expectedRoom.ratePlanId
    result.roomTypeId must_== expectedRoom.roomTypeId
    if (expectedRoom.promotionId == 0) result.discountInfo.promotion.isDefined must_== false
    else result.discountInfo.promotion.get.id must_== expectedRoom.promotionId

    expectedPrices.foreach { ep =>
      val d = Util.convertTime(ep.date)
      val r = result.prices.find(x => x.date == d && x.chargeType == ChargeType.getFromValue(ep.chargeType))
      r.isDefined must_== true
      showPrice(r.get, ep)
      r.get.chargeType must_== ChargeType.getFromValue(ep.chargeType)
      r.get.promotionDiscount must_== ep.discount
      r.get.chargeOption.value must_== ep.option
      r.get.refId must_== ep.sId
      PriceChecker.compareAmountValue(r.get.netExclusive, ep.netEx)
      PriceChecker.compareAmountValue(r.get.tax, ep.tax)
      PriceChecker.compareAmountValue(r.get.fee, ep.fee)
      PriceChecker.compareAmountValue(r.get.margin, ep.margin)
      PriceChecker.compareAmountValue(r.get.processingFee, ep.processingFee)
    }
  }

  private def compareHourlyTimeIntervals(result: YPLRoom, expectedHourlySlots: Seq[ExpectedHourlySlots]): Unit = {
    result.hourlyAvailableSlots.size must_== expectedHourlySlots.size
    expectedHourlySlots.foreach { slot =>
      result.hourlyAvailableSlots.contains(createHourlySlot(slot.duration, slot.checkIn)) must_== true
    }
  }

  private def showPrice(r: YplPrice, e: ExpectedPrice) = {
    println(s"     NetEx:           ${r.netExclusive} = ${e.netEx}")
    println(s"     Tax:             ${r.tax} = ${e.tax}")
    println(s"     Fee:             ${r.fee} = ${e.fee}")
    println(s"     Margin:          ${r.margin} = ${e.margin}")
    println(s"     ProcessingFee:   ${r.processingFee} = ${e.processingFee}")
  }

  "PriceCalculator (Unit Tests)" should {
    "CalculateDailyPrice shouldn't throw exception when price is negative" in {
      implicit val ctx = YplContext(
        YplRequest(
          "",
          new DateTime("2025-05-25"),
          new DateTime("2025-05-25"),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        ))
      val taxInfo = aValidTaxInfo
      val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
      // ROOM WITH NEGATIVE PRICE
      val negativePrice = Map(1 -> -100d)
      val roomEntry = createRoomEntry(new DateTime("2025-05-25"), 1, 0, 0, 0, negativePrice)
      val price = calc
        .calculateDailyPrices(
          roomEntry,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)
        .head

      price.netExclusive should_== 0d
      price.tax should_== 0d
      price.fee should_== 0d
      price.margin should_== 0d
      price.processingFee should_== 0d
      price.processingFeeBreakdown should beNone
      price.taxBreakDown.isEmpty should_== true
    }

    "CalculateCommission should return commission percentage correctly" in {
      val yplPrice = mock[YplPrice]
      when(yplPrice.isRoom).thenReturn(true)
      when(yplPrice.netExclusive).thenReturn(100.0)
      when(yplPrice.margin).thenReturn(25.0)
      calculateCommission(List(yplPrice), 25.0) should_== 20.0
    }

    "CalculateCommission should fall back to original commission" in {
      val yplPrice = mock[YplPrice]
      when(yplPrice.isRoom).thenReturn(true)
      when(yplPrice.netExclusive).thenReturn(-5.0)
      when(yplPrice.margin).thenReturn(0.0)
      calculateCommission(List(yplPrice), 25.0) should_== 25.0
    }

    "CalculateDailyPrice should calculate correct tax level by extra bed" in {
      def calculateDailyPrices(priceBreakdownCalc: PriceBreakdownCalculatorInterface,
                               experiment: YplExperiment): List[YplPrice] = {
        val calc = new AnyRef
          with PriceCalculatorImpl
          with PriceCalculationImpl
          with TaxCalculatorImpl
          with TaxPrototypeServiceImpl
          with CommissionServiceImpl {
          override val priceBreakdownCalculator: PriceBreakdownCalculatorInterface = priceBreakdownCalc
        }

        implicit val ctx: YplContextMock = YplContext(
          YplRequest(
            "",
            new DateTime("2025-05-25"),
            new DateTime("2025-05-25"),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ).withExperiment(experiment))
        val taxInfo = aValidTaxInfoWithTaxPrototypeLevel
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
        val roomPrice = Map(2 -> 2000d)
        val roomEntry = createRoomEntry(
          new DateTime("2025-05-25"),
          1,
          2,
          1,
          0,
          roomPrice,
          extraBedPrice = Some(1000),
          rateType = RateType.SellExclusive,
          roomQty = 1,
          extraBedQty = 1,
          taxes = Map((1, 11111) -> 0.0),
        )
        calc.calculateDailyPrices(
          roomEntry,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)
      }

      "VEL-2070=A: call PriceCalculation" in {
        val priceBreakdownCalcSpy = new MockPriceBreakdownCalculator {}
        val allPrice =
          calculateDailyPrices(priceBreakdownCalcSpy, YplExperiment(ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION, 'A'))
        val calcRoomPrice = allPrice.filter(p => p.isRoom).head
        calcRoomPrice.netExclusive should_== 1600.0
        calcRoomPrice.margin should_== 400.0
        calcRoomPrice.tax should_== 288.0
        calcRoomPrice.sellExclusive should_== 2000.0

        val calcExtraBedPrice = allPrice.filter(p => p.isExtraBed).head
        calcExtraBedPrice.netExclusive should_== 800.0
        calcExtraBedPrice.margin should_== 200.0
        calcExtraBedPrice.tax should_== 144.0
        calcExtraBedPrice.sellExclusive should_== 1000.0

        priceBreakdownCalcSpy.callCounter should_== 0
      }

      "VEL-2070=B: call PriceBreakdownCalculator" in {
        val priceBreakdownCalcSpy = new MockPriceBreakdownCalculator {}
        val allPrice =
          calculateDailyPrices(priceBreakdownCalcSpy, YplExperiment(ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION, 'B'))

        val calcRoomPrice = allPrice.filter(p => p.isRoom).head
        calcRoomPrice.netExclusive should_== 1600.0
        calcRoomPrice.margin should_== 400.0
        calcRoomPrice.tax should_== 288.0
        calcRoomPrice.sellExclusive should_== 2000.0

        val calcExtraBedPrice = allPrice.filter(p => p.isExtraBed).head
        calcExtraBedPrice.netExclusive should_== 800.0
        calcExtraBedPrice.margin should_== 200.0
        calcExtraBedPrice.tax should_== 144.0
        calcExtraBedPrice.sellExclusive should_== 1000.0

        // one call per one price
        priceBreakdownCalcSpy.callCounter should_== allPrice.size
      }
    }

    "CalculateDailyPrice should calculate correct tax level by extra bed (average by room) - isNewChargeTypeTax on DailyTaxes not given" in {
      implicit val ctx = YplContext(
        YplRequest(
          "",
          new DateTime("2025-05-25"),
          new DateTime("2025-05-25"),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        ))
      val taxInfo = aValidTaxInfoWithTaxPrototypeLevel
      val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
      val roomPrice = Map(2 -> 1000d)
      val roomEntry = createRoomEntry(
        new DateTime("2025-05-25"),
        1,
        2,
        1,
        0,
        roomPrice,
        extraBedPrice = Some(2000),
        rateType = RateType.SellExclusive,
        roomQty = 2,
        extraBedQty = 1,
        taxes = Map((1, 11111) -> 0.0),
      )
      val allPrice = calc.calculateDailyPrices(
        roomEntry,
        PaymentModel.Unknown,
        taxInfo,
        reqOcc.agePolicy,
        reqOcc.ignoreRequestedNumberOfRoomsForNha,
        DMC.YCS,
        true,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
      )(ctx, aValidPropertyContext)

      val calcRoomPrice = allPrice.filter(p => p.isRoom).head
      calcRoomPrice.netExclusive should_== 800.0
      calcRoomPrice.margin should_== 200.0
      calcRoomPrice.tax should_== 96.0
      calcRoomPrice.sellExclusive should_== 1000.0

      val calcExtraBedPrice = allPrice.filter(p => p.isExtraBed).head
      calcExtraBedPrice.netExclusive should_== 1600.0
      calcExtraBedPrice.margin should_== 400.0
      calcExtraBedPrice.tax should_== 192.0
      calcExtraBedPrice.sellExclusive should_== 2000.0
    }

    "CalculateDailyPrice should calculate correct tax level by extra bed (average by room) - isNewChargeTypeTax on DailyTaxes set as false" in {
      implicit val ctx = YplContext(
        YplRequest(
          "",
          new DateTime("2025-05-25"),
          new DateTime("2025-05-25"),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        ))
      val taxInfo = aValidTaxInfoWithTaxPrototypeLevelV2
      val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
      val roomPrice = Map(2 -> 1000d)
      val roomEntry = createRoomEntry(
        new DateTime("2025-05-25"),
        1,
        2,
        1,
        0,
        roomPrice,
        extraBedPrice = Some(2000),
        rateType = RateType.SellExclusive,
        roomQty = 2,
        extraBedQty = 1,
        taxes = Map((1, 11111) -> 0.0),
      )
      val allPrice = calc.calculateDailyPrices(
        roomEntry,
        PaymentModel.Unknown,
        taxInfo,
        reqOcc.agePolicy,
        reqOcc.ignoreRequestedNumberOfRoomsForNha,
        DMC.YCS,
        true,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
      )(ctx, aValidPropertyContext)

      val calcRoomPrice = allPrice.filter(p => p.isRoom).head
      calcRoomPrice.netExclusive should_== 800.0
      calcRoomPrice.margin should_== 200.0
      calcRoomPrice.tax should_== 96.0
      calcRoomPrice.sellExclusive should_== 1000.0

      val calcExtraBedPrice = allPrice.filter(p => p.isExtraBed).head
      calcExtraBedPrice.netExclusive should_== 1600.0
      calcExtraBedPrice.margin should_== 400.0
      calcExtraBedPrice.tax should_== 192.0
      calcExtraBedPrice.sellExclusive should_== 2000.0
    }

    "CalculateDailyPrice should calculate correct tax level by extra bed (average by room) - isNewChargeTypeTax on DailyTaxes set as true" in {
      implicit val ctx = YplContext(
        YplRequest(
          "",
          new DateTime("2025-05-25"),
          new DateTime("2025-05-25"),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        ))
      val taxInfo = aValidTaxInfoWithTaxPrototypeLevelV2
      val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
      val roomPrice = Map(2 -> 1000d)
      val roomEntry = createRoomEntry(
        new DateTime("2025-05-25"),
        1,
        2,
        1,
        0,
        roomPrice,
        extraBedPrice = Some(2000),
        rateType = RateType.SellExclusive,
        roomQty = 2,
        extraBedQty = 1,
        taxes = Map((1, 11111) -> 0.0),
      )
      val allPrice = calc.calculateDailyPrices(
        roomEntry,
        PaymentModel.Unknown,
        taxInfo,
        reqOcc.agePolicy,
        reqOcc.ignoreRequestedNumberOfRoomsForNha,
        DMC.YCS,
        true,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
      )(ctx, aValidPropertyContext)

      val calcRoomPrice = allPrice.filter(p => p.isRoom).head
      calcRoomPrice.netExclusive should_== 800.0
      calcRoomPrice.margin should_== 200.0
      calcRoomPrice.tax should_== 96.0
      calcRoomPrice.sellExclusive should_== 1000.0

      val calcExtraBedPrice = allPrice.filter(p => p.isExtraBed).head
      calcExtraBedPrice.netExclusive should_== 1600.0
      calcExtraBedPrice.margin should_== 400.0
      calcExtraBedPrice.tax should_== 192.0
      calcExtraBedPrice.sellExclusive should_== 2000.0
    }

    "CalculateDailyPrice should calculate correct tax level when has priceForAssumeTaxMap" in {
      val date = new DateTime("2025-05-25")

      implicit val ctx = YplContext(
        YplRequest("",
                   date,
                   date,
                   supplierFeatures = aValidSupplierFeatures,
                   whitelabelSetting = aValidwhitelabelSetting,
                   fences = aValidYplRequestFences))
      val taxInfo = aValidTaxInfoWithTaxPrototypeLevel
      val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
      val roomPrice = Map(2 -> 2000d)
      val roomEntry = createRoomEntry(date,
                                      1,
                                      2,
                                      1,
                                      0,
                                      roomPrice,
                                      extraBedPrice = Some(1000),
                                      rateType = RateType.SellExclusive,
                                      roomQty = 1,
                                      extraBedQty = 1,
                                      taxes = Map((1, 11111) -> 0.0))
      val allPrice = calc.calculateDailyPrices(
        roomEntry,
        PaymentModel.Unknown,
        taxInfo,
        reqOcc.agePolicy,
        reqOcc.ignoreRequestedNumberOfRoomsForNha,
        DMC.YCS,
        true,
        priceForAssumeTaxMap = Map(date -> 10000.0),
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
      )(ctx, aValidPropertyContext)

      val calcRoomPrice = allPrice.filter(p => p.isRoom).head
      calcRoomPrice.netExclusive should_== 1600.0
      calcRoomPrice.margin should_== 400.0
      calcRoomPrice.tax should_== 448.0
      calcRoomPrice.sellExclusive should_== 2000.0

      val calcExtraBedPrice = allPrice.filter(p => p.isExtraBed).head
      calcExtraBedPrice.netExclusive should_== 800.0
      calcExtraBedPrice.margin should_== 200.0
      calcExtraBedPrice.tax should_== 224.0
      calcExtraBedPrice.sellExclusive should_== 1000.0
    }

    "calculate Extrabed Charge from Price Entry rate type correctly" in {
      val date = DateTime.parse("2024-07-24")
      implicit val ctx = YplContext(
        YplRequest("",
                   date,
                   date,
                   supplierFeatures = aValidSupplierFeatures,
                   whitelabelSetting = aValidwhitelabelSetting,
                   fences = aValidYplRequestFences))
      val taxInfo = aValidTaxInfoWithTaxPrototypeLevel
      val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
      val roomPrice = Map(2 -> 2000d)
      val roomEntry = createRoomEntry(
        date,
        1,
        2,
        1,
        0,
        roomPrice,
        extraBedPrice = Some(1000),
        rateType = RateType.SellExclusive,
        roomQty = 1,
        extraBedQty = 1,
        taxes = Map((1, 11111) -> 0.0),
        propertyRateType = Some(RateType.NetExclusive),
      )
      val allPrice = calc.calculateDailyPrices(
        roomEntry,
        PaymentModel.Unknown,
        taxInfo,
        reqOcc.agePolicy,
        reqOcc.ignoreRequestedNumberOfRoomsForNha,
        DMC.YCS,
        true,
        priceForAssumeTaxMap = Map(date -> 10000.0),
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
      )(ctx, aValidPropertyContext)

      val calcRoomPrice = allPrice.filter(p => p.isRoom).head
      calcRoomPrice.netExclusive should_== 1600.0
      calcRoomPrice.margin should_== 400.0
      calcRoomPrice.tax should_== 448.0
      calcRoomPrice.sellExclusive should_== 2000.0

      val calcExtraBedPrice = allPrice.filter(p => p.isExtraBed).head
      calcExtraBedPrice.netExclusive should_== 1000.0
      calcExtraBedPrice.margin should_== 200.0
      calcExtraBedPrice.tax should_== 280.0
      calcExtraBedPrice.sellInclusive should_== 1536.0
    }

    "CalculateDailyPrice should calculate correct referenceCommissionPercent" in {

      "return supplierContractedCommission if has value" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            new DateTime("2025-05-25"),
            new DateTime("2025-05-25"),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))
        val taxInfo = aValidTaxInfo
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
        val roomPrice = Map(2 -> 2000d)
        val roomEntry = createRoomEntry(new DateTime("2025-05-25"),
                                        1,
                                        2,
                                        1,
                                        0,
                                        roomPrice,
                                        dailyPriceComm = 25.00,
                                        supplierContractedCommission = Some(30.0))
        val allPrice = calc.calculateDailyPrices(
          roomEntry,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)

        allPrice.head.referenceCommissionPercent should_== 30.00
      }
      "return correct commission for Sell rate hotel" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            new DateTime("2025-05-25"),
            new DateTime("2025-05-25"),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))
        val taxInfo = aValidTaxInfo
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
        val roomPrice = Map(2 -> 2000d)
        val roomEntry = createRoomEntry(new DateTime("2025-05-25"),
                                        1,
                                        2,
                                        1,
                                        0,
                                        roomPrice,
                                        dailyPriceComm = 25.00,
                                        rateType = RateType.SellInclusive)
        val allPrice = calc.calculateDailyPrices(
          roomEntry,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)

        allPrice.head.referenceCommissionPercent should_== 25.00
      }
      "return correct commission for Net rate hotel" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            new DateTime("2025-05-25"),
            new DateTime("2025-05-25"),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))
        val taxInfo = aValidTaxInfo
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
        val roomPrice = Map(2 -> 2000d)
        val roomEntry = createRoomEntry(new DateTime("2025-05-25"),
                                        1,
                                        2,
                                        1,
                                        0,
                                        roomPrice,
                                        dailyPriceComm = 25.00,
                                        rateType = RateType.NetInclusive)
        val allPrice = calc.calculateDailyPrices(
          roomEntry,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)

        allPrice.head.referenceCommissionPercent should_== 20.00
      }
    }

    "CalculateDailyPrice should calculate margin correctly" in {
      def calculateDailyPrices(priceBreakdownCalc: PriceBreakdownCalculatorInterface,
                               experiment: YplExperiment): List[YplPrice] = {
        val calc = new AnyRef
          with PriceCalculatorImpl
          with PriceCalculationImpl
          with TaxCalculatorImpl
          with TaxPrototypeServiceImpl
          with CommissionServiceImpl {
          override val priceBreakdownCalculator: PriceBreakdownCalculatorInterface = priceBreakdownCalc
        }

        implicit val ctx = YplContext(aValidYplRequest.withExperiment(experiment))
        val taxInfo = aValidTaxInfo
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
        val roomPrice = Map(2 -> 2000d)
        val roomEntry = createRoomEntry(new DateTime("2025-05-25"),
                                        1,
                                        2,
                                        1,
                                        0,
                                        roomPrice,
                                        dailyPriceComm = 25.00,
                                        rateType = NetExclusive,
                                        skipCommissionHolderCalculation = true)
        calc.calculateDailyPrices(
          roomEntry,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)
      }

      "VEL-2070=A: call PriceCalculation" in {
        val priceBreakdownCalcSpy = new MockPriceBreakdownCalculator {}
        val allPrice =
          calculateDailyPrices(priceBreakdownCalcSpy, YplExperiment(ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION, 'A'))

        import com.agoda.papi.ypl.utils.Implicits._

        allPrice.head.margin.roundAt(2) should_== 526.32

        priceBreakdownCalcSpy.callCounter should_== 0
      }

      "VEL-2070=B: call PriceBreakdownCalculator" in {
        val priceBreakdownCalcSpy = new MockPriceBreakdownCalculator {}
        val allPrice =
          calculateDailyPrices(priceBreakdownCalcSpy, YplExperiment(ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION, 'B'))

        import com.agoda.papi.ypl.utils.Implicits._

        allPrice.head.margin.roundAt(2) should_== 526.32

        priceBreakdownCalcSpy.callCounter should_== 1
      }
    }

    "GetRPMSurchargePrices should calculate correct referenceCommissionPercent" should {

      val dailyPriceComm = 25.0
      val dailyPriceMarkUp = convertToMarkup(dailyPriceComm)

      val taxInfo = aValidTaxInfo
      val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
      val roomPrice = Map(2 -> 2000d)
      val roomEntry =
        createRoomEntry(new DateTime("2025-05-25"), 1, 2, 1, 0, roomPrice, roomQty = 1, dailyPriceComm = 50.0)

      "With experiment, return commission for non commissionable surcharge with supplier contract commission" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            new DateTime("2025-05-25"),
            new DateTime("2025-05-25"),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))

        val surcharge = List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(), false, false, 1000))
        val dailyPricesWithSurcharges: Map[DateTime, DailyPrice] = roomEntry.dailyPrices.map { case (date, price) =>
          date -> price.copy(rpmSurcharges = surcharge)
        }
        val roomEntryWithSurcharges =
          roomEntry.copy(dailyPrices = dailyPricesWithSurcharges,
                         rateCategory = aValidRateCategoryEntry.copy(dailyPrices = dailyPricesWithSurcharges))

        val dailyPrice = calc.calculateDailyPrices(
          roomEntryWithSurcharges,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)
        implicit val propertyContext = PropertyContextImpl(1, 1, 1)
        val surchargePrices = calc.getRPMSurchargePrices(
          PaymentModel.Unknown,
          taxInfo,
          roomEntryWithSurcharges,
          dailyPrice,
          RateType.Unknown,
          reqOcc,
          false,
          DMC.YCS,
          Some(30.0),
          1,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
          fixMarriottSurchargeExp = false,
        )

        surchargePrices.head.referenceCommissionPercent should_== 0.00
      }

      "With experiment, return commission for non commissionable surcharge" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            new DateTime("2025-05-25"),
            new DateTime("2025-05-25"),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))

        val surcharge = List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(), false, false, 1000))
        val dailyPricesWithSurcharges: Map[DateTime, DailyPrice] = roomEntry.dailyPrices.map { case (date, price) =>
          date -> price.copy(rpmSurcharges = surcharge)
        }
        val roomEntryWithSurcharges = roomEntry.copy(dailyPrices = dailyPricesWithSurcharges)

        val dailyPrice = calc.calculateDailyPrices(
          roomEntryWithSurcharges,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)
        implicit val propertyContext = PropertyContextImpl(1, 1, 1)
        val surchargePrices = calc.getRPMSurchargePrices(
          PaymentModel.Unknown,
          taxInfo,
          roomEntryWithSurcharges,
          dailyPrice,
          RateType.Unknown,
          reqOcc,
          false,
          DMC.YCS,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
          fixMarriottSurchargeExp = false,
        )

        surchargePrices.head.referenceCommissionPercent should_== 0.00
      }

      "With experiment, return commission for Sell rate surcharge" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            new DateTime("2025-05-25"),
            new DateTime("2025-05-25"),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))

        val roomEntry = createRoomEntry(new DateTime("2025-05-25"),
                                        1,
                                        2,
                                        1,
                                        0,
                                        roomPrice,
                                        roomQty = 1,
                                        dailyPriceComm = dailyPriceComm,
                                        protobufCommission = Some(dailyPriceComm),
                                        agxComm = 0d)

        val surcharge = List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(), false, true, 1000))
        val dailyPricesWithSurcharges: Map[DateTime, DailyPrice] = roomEntry.dailyPrices.map { case (date, price) =>
          date -> price.copy(rpmSurcharges = surcharge)
        }
        val roomEntryWithSurcharges =
          roomEntry.copy(dailyPrices = dailyPricesWithSurcharges,
                         rateCategory = aValidRateCategoryEntry.copy(dailyPrices = dailyPricesWithSurcharges))

        val dailyPrice = calc.calculateDailyPrices(
          roomEntryWithSurcharges,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)
        implicit val propertyContext = PropertyContextImpl(1, 1, 1)
        val surchargePrices = calc.getRPMSurchargePrices(
          PaymentModel.Unknown,
          taxInfo,
          roomEntryWithSurcharges,
          dailyPrice,
          RateType.Unknown,
          reqOcc,
          false,
          DMC.YCS,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
          fixMarriottSurchargeExp = false,
        )

        surchargePrices.head.referenceCommissionPercent should_== dailyPriceComm
      }

      "With experiment, return commission for Net rate surcharge" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            new DateTime("2025-05-25"),
            new DateTime("2025-05-25"),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))

        val roomEntry = createRoomEntry(new DateTime("2025-05-25"),
                                        1,
                                        2,
                                        1,
                                        0,
                                        roomPrice,
                                        roomQty = 1,
                                        dailyPriceComm = dailyPriceMarkUp,
                                        protobufCommission = Some(dailyPriceComm),
                                        agxComm = 0d)

        val surcharge = List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(), false, true, 1000))
        val dailyPricesWithSurcharges: Map[DateTime, DailyPrice] = roomEntry.dailyPrices.map { case (date, price) =>
          date -> price.copy(rpmSurcharges = surcharge)
        }
        val roomEntryWithSurcharges =
          roomEntry.copy(dailyPrices = dailyPricesWithSurcharges,
                         rateType = RateType.NetInclusive,
                         rateCategory = aValidRateCategoryEntry.copy(dailyPrices = dailyPricesWithSurcharges))

        val dailyPrice = calc.calculateDailyPrices(
          roomEntryWithSurcharges,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)
        implicit val propertyContext = PropertyContextImpl(1, 1, 1)
        val surchargePrices = calc.getRPMSurchargePrices(
          PaymentModel.Unknown,
          taxInfo,
          roomEntryWithSurcharges,
          dailyPrice,
          RateType.Unknown,
          reqOcc,
          false,
          DMC.YCS,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
          fixMarriottSurchargeExp = false,
        )

        surchargePrices.head.referenceCommissionPercent should_== dailyPriceComm
      }

      "With agx, return commission for Net rate surcharge" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            new DateTime("2025-05-25"),
            new DateTime("2025-05-25"),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))
        val roomEntry = createRoomEntry(
          new DateTime("2025-05-25"),
          1,
          2,
          1,
          0,
          roomPrice,
          roomQty = 1,
          dailyPriceComm = convertToMarkup(dailyPriceComm),
          protobufCommission = Some(dailyPriceComm),
          agxComm = 10d,
        )

        val surcharge = List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(), false, true, 1000))
        val dailyPricesWithSurcharges: Map[DateTime, DailyPrice] = roomEntry.dailyPrices.map { case (date, price) =>
          date -> price.copy(rpmSurcharges = surcharge)
        }
        val roomEntryWithSurcharges =
          roomEntry.copy(dailyPrices = dailyPricesWithSurcharges,
                         rateType = RateType.NetInclusive,
                         rateCategory = aValidRateCategoryEntry.copy(dailyPrices = dailyPricesWithSurcharges))

        val dailyPrice = calc.calculateDailyPrices(
          roomEntryWithSurcharges,
          PaymentModel.Unknown,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)
        implicit val propertyContext = PropertyContextImpl(1, 1, 1)
        val surchargePrices = calc.getRPMSurchargePrices(
          PaymentModel.Unknown,
          taxInfo,
          roomEntryWithSurcharges,
          dailyPrice,
          RateType.Unknown,
          reqOcc,
          false,
          DMC.YCS,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
          fixMarriottSurchargeExp = false,
        )

        surchargePrices.head.referenceCommissionPercent should_== 35d
      }
    }

    "updateDailyTaxForTaxPrototypeLevels should return normal tax if not has tax prototype level - Variant A" in {
      val date = new DateTime("2025-05-25")

      implicit val ctx = YplContext(
        YplRequest("",
                   date,
                   date,
                   supplierFeatures = aValidSupplierFeatures,
                   whitelabelSetting = aValidwhitelabelSetting,
                   fences = aValidYplRequestFences))

      val dailyTaxes = aValidDailyTax
      val dailyPrice = aValidDailyPrice.copy(date = date)
      val roomPrice = List(aValidPrice.copy(date = date, dailyTaxes = aValidDailyTax))

      val result =
        calc.updateDailyTaxForTaxPrototypeLevels(dailyTaxes, dailyPrice, roomPrice, 150, 10, RateType.SellExclusive)

      result.taxes.head.taxValue should_== 10.0 // dailyTaxes.taxes.head.taxValue
    }

    "updateDailyTaxForTaxPrototypeLevels should return normal tax if not has tax prototype level - Exp VYG-323 allocated as B" in {
      val date = new DateTime("2025-05-25")

      implicit val ctx = YplContext(
        YplRequest(
          "",
          date,
          date,
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          experiments = List(YplExperiment(US_TAX_V2_EXPERIMENT, 'B')),
        ))

      val dailyTaxes = aValidDailyTaxV2
      val dailyPrice = aValidDailyPrice.copy(date = date)
      val roomPrice = List(aValidPrice.copy(date = date, dailyTaxes = aValidDailyTaxV2))

      val result =
        calc.updateDailyTaxForTaxPrototypeLevels(dailyTaxes, dailyPrice, roomPrice, 150, 10, RateType.SellExclusive)

      result.taxes.head.taxValue should_== 10.0 // dailyTaxes.taxes.head.taxValue
    }

    "updateDailyTaxForTaxPrototypeLevels should not update tax for different price date" in {
      val date1 = new DateTime("2025-05-25")
      val date2 = new DateTime("2025-05-25").plusDays(1)

      implicit val ctx = YplContext(
        YplRequest("",
                   date1,
                   date1,
                   supplierFeatures = aValidSupplierFeatures,
                   whitelabelSetting = aValidwhitelabelSetting,
                   fences = aValidYplRequestFences))

      val dailyTaxes = aValidDailyTaxWithTaxPrototypeLevel
      val dailyPrice = aValidDailyPrice.copy(date = date1)
      val roomPrice = List(aValidPrice.copy(date = date2, dailyTaxes = aValidDailyTaxWithTaxPrototypeLevelWithValue))

      val result =
        calc.updateDailyTaxForTaxPrototypeLevels(dailyTaxes, dailyPrice, roomPrice, 150, 10, RateType.SellExclusive)

      result.taxes.head.taxValue should_== 0.0 // dailyTaxes.taxes.head.taxValue
    }

    "updateDailyTaxForTaxPrototypeLevels should not update tax for different tax id / tax prototype id" in {
      val date1 = new DateTime("2025-05-25")

      implicit val ctx = YplContext(
        YplRequest("",
                   date1,
                   date1,
                   supplierFeatures = aValidSupplierFeatures,
                   whitelabelSetting = aValidwhitelabelSetting,
                   fences = aValidYplRequestFences))

      val dailyTaxes = aValidDailyTaxWithTaxPrototypeLevel
      val dailyPrice = aValidDailyPrice.copy(date = date1)
      val roomDailyTaxes =
        DailyTaxes(List(TaxWithValue(aValidTaxWithTaxPrototypeLevel.copy(id = 1, protoTypeId = 2), 12.0)),
                   isCleanedUpHospitalityTax = true)

      val roomPrice = List(aValidPrice.copy(date = date1, dailyTaxes = roomDailyTaxes))

      val result =
        calc.updateDailyTaxForTaxPrototypeLevels(dailyTaxes, dailyPrice, roomPrice, 150, 10, RateType.SellExclusive)

      result.taxes.head.taxValue should_== 0.0 // dailyTaxes.taxes.head.taxValue
    }

    "updateDailyTaxForTaxPrototypeLevels should update tax value when found in roomPrices" in {
      val date1 = new DateTime("2025-05-25")

      implicit val ctx = YplContext(
        YplRequest("",
                   date1,
                   date1,
                   supplierFeatures = aValidSupplierFeatures,
                   whitelabelSetting = aValidwhitelabelSetting,
                   fences = aValidYplRequestFences))

      val dailyTaxes = aValidDailyTaxWithTaxPrototypeLevel
      val dailyPrice = aValidDailyPrice.copy(date = date1)
      val roomPrice = List(aValidPrice.copy(date = date1, dailyTaxes = aValidDailyTaxWithTaxPrototypeLevelWithValue))

      val result =
        calc.updateDailyTaxForTaxPrototypeLevels(dailyTaxes, dailyPrice, roomPrice, 150, 10, RateType.SellExclusive)

      result.taxes.head.taxValue should_== 12.0 // aValidDailyTaxWithTaxPrototypeLevelWithValue.taxes.head.taxValue
    }

    "updateDailyTaxForTaxPrototypeLevels should update tax value when prototype level is found in roomPrices" in {
      val date1 = new DateTime("2025-05-25")

      implicit val ctx = YplContext(
        YplRequest("",
                   date1,
                   date1,
                   supplierFeatures = aValidSupplierFeatures,
                   whitelabelSetting = aValidwhitelabelSetting,
                   fences = aValidYplRequestFences))

      val dailyTaxes = aValidDailyTaxWithTaxPrototypeLevel
      val dailyPrice = aValidDailyPrice.copy(date = date1)
      val roomPrice = List(aValidPrice.copy(date = date1, dailyTaxes = aValidDailyTaxWithTaxPrototypeLevelWithValue))

      val result =
        calc.updateDailyTaxForTaxPrototypeLevels(dailyTaxes, dailyPrice, roomPrice, 150, 10, RateType.SellExclusive)
      result.taxes.head.taxValue should_== 12.0 // aValidDailyTaxWithTaxPrototypeLevelWithValue.taxes.head.taxValue
    }

    "Extract RPM Surcharge For Pull" in {
      val mockSurcharge = aValidSurchargeEntry.withId(1).withOption(ChargeOption.Excluded).withOccFromProto(2)
      val mockDailyPrice =
        aValidDailyPrice.withRpmSurcharges(List(mockSurcharge, mockSurcharge.withOccFromProto(1))).build
      val mockTaxInfo = aValidTaxInfo.build
      val mockDateTime = new DateTime("2025-05-25")
      val mockYplPrices = List(aValidPrice)
      val mockSupplierId = 332
      val mockIsPull = true // SourceType
      val roomWithSurcharges = aValidRoomEntry.withDailyPrices(Map(mockDateTime -> mockDailyPrice))

      "Extract correctly" in {
        implicit val ctx = YplContext(aValidYplRequest)
        implicit val propertyContext = PropertyContextImpl(1, 1, 1)
        val res = calc.getRPMSurchargePrices(
          PaymentModel.Merchant,
          mockTaxInfo,
          roomWithSurcharges,
          mockYplPrices,
          RateType.Unknown,
          aValidReqOcc,
          isPull = mockIsPull,
          supplierId = mockSupplierId,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
          fixMarriottSurchargeExp = false,
        )
        res.nonEmpty must_== true
      }

    }

  }

  "PriceCalculator (Flow Tests)" should {
    implicit def unitAsResult: AsResult[Unit] = new AsResult[Unit] {
      def asResult(r: => Unit) = ResultExecution.execute(r)(_ => Success())
    }

    "Base Room Calculation integrated with taxV2" should {
      def prepareTaxV2HotelTestCases(debugTestId: Int = 0,
                                     isHPTaxAndFeeApplied: Boolean = false,
                                     isMATaxAndFeeApplied: Boolean = true,
                                     isOnlyMATaxApplied: Boolean = false): Map[SupplierId, (Case, TaxInfo)] = {
        val cases = if (debugTestId > 0) allCases.filter(_._1 == debugTestId) else allCases
        cases.map { case (testId, _case) =>
          val mandatoryTaxAndFee = _case.taxInfo.taxes
          val mandatoryTax = mandatoryTaxAndFee.last._1 -> mandatoryTaxAndFee.last._2.copy(isFee = false,
                                                                                           option =
                                                                                             ChargeOption.Mandatory,
                                                                                           isTaxable = false,
                                                                                           applyOver = None)
          val hpTaxAndFee = _case.taxInfo.taxes.map(t =>
            t._1 -> t._2.copy(option = ChargeOption.HospitalityPrice, applyOver = Some(ApplyTaxOver.NetEx)))
          val taxes = (isHPTaxAndFeeApplied, isMATaxAndFeeApplied) match {
            case (false, true) => if (isOnlyMATaxApplied) Map(mandatoryTax) else mandatoryTaxAndFee
            case (true, false) => hpTaxAndFee
            case (true, true) =>
              if (isOnlyMATaxApplied) hpTaxAndFee + mandatoryTax else hpTaxAndFee ++ mandatoryTaxAndFee
            case _ => mandatoryTaxAndFee
          }

          val taxInfo = _case.taxInfo.copy(
            hotelTaxInfo = _case.taxInfo.hotelTaxInfo,
            taxes = taxes,
          )

          (testId, (_case, taxInfo))
        }
      }

      def calculateYPLPrice(yplRequest: YplRequest,
                            roomEntries: Map[Int, YplRoomEntry],
                            paymentModel: PaymentModel,
                            hotel: InputHotel,
                            taxInfo: TaxInfo) = {
        val reqOcc = YplReqOccByHotelAgePolicy(yplRequest.occ, AgePolicy(childMaxAge = 10))
        implicit val ctx: YplContext = YplContext(yplRequest)
        implicit val propertyContext = PropertyContextImpl(1, 1, 1)
        roomEntries.map { x =>
          val dailyPrices = calc.calculateDailyPrices(
            room = x._2,
            paymentModel = paymentModel,
            taxInfo = taxInfo,
            agePolicy = reqOcc.agePolicy,
            ignoreRequestedNumberOfRoomsForNha = reqOcc.ignoreRequestedNumberOfRoomsForNha,
            supplierId = hotel.supplierId,
            applyDiscountsMultiplicatively = true,
            hotelId = hotel.hotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            cityId = aValidCityId,
          )
          x._1 -> calc.doPricing(
            hotelId = hotel.hotelId,
            supplierId = hotel.supplierId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            paymentModel = PaymentModel.getPaymentModel(hotel.paymentModel),
            taxInfo = taxInfo,
            room = x._2,
            dailyPrices = dailyPrices,
            dispatchChannels = aValidYplDispatchChannels,
            surchargeRateType = RateType.Unknown,
            reqOcc = reqOcc,
            fixMarriottSurchargeExp = false,
            cityId = aValidCityId,
          )
        }
      }

      "Base Room (Price) Calculation - isNewChargeTypeTaxApplied given as false" in {
        val cases = prepareTaxV2HotelTestCases()
        cases.foreach { case (testId, (_case, taxInfo)) =>
          implicit val request: YplRequest = _case.request
          val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
          implicit val ctx: YplContext = YplContext(request)

          val hotel = _case.hotel
          val paymentModel = PaymentModel.getPaymentModel(hotel.paymentModel)
          val results = calculateYPLPrice(_case.request, _case.roomEntries, paymentModel, hotel, taxInfo)

          results.size must_== _case.expectedRooms.size
          _case.expectedRooms.foreach { case (refId, rooms) =>
            val expectedRoom = rooms.head
            compareResult(results(refId),
                          hotel,
                          expectedRoom,
                          _case.expectedPrices(refId),
                          _case.expectedTaxes.getOrElse(refId, Seq()))
          }
        }
      }

      "Base Room (Price) Calculation with Mandatory Tax - isNewChargeTypeTaxApplied given as true" in {
        val cases =
          prepareTaxV2HotelTestCases(debugTestId =
            13) // if (debugTestId > 0) allCases.filter(_._1 == debugTestId) else allCases

        cases.foreach { case (testId, (_case, taxInfo)) =>
          implicit val request: YplRequest = _case.request
          val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
          implicit val ctx: YplContext = YplContext(request)

          val hotel = _case.hotel
          val paymentModel = PaymentModel.getPaymentModel(hotel.paymentModel)
          val results = calculateYPLPrice(_case.request, _case.roomEntries, paymentModel, hotel, taxInfo)

          results.size must_== _case.expectedRooms.size
          _case.expectedRooms.foreach { case (refId, rooms) =>
            val expectedRoom = rooms.head
            compareResult(results(refId),
                          hotel,
                          expectedRoom,
                          _case.expectedPrices(refId),
                          _case.expectedTaxes.getOrElse(refId, Seq()))
          }
        }
      }

      "Base Room (Price) Calculation with Hospitality Tax- isNewChargeTypeTaxApplied given as true" in {
        val cases =
          prepareTaxV2HotelTestCases(debugTestId = 13, isHPTaxAndFeeApplied = true, isMATaxAndFeeApplied = false)

        cases.foreach { case (testId, (_case, taxInfo)) =>
          implicit val request: YplRequest = _case.request
          val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
          implicit val ctx: YplContext = YplContext(request)

          val hotel = _case.hotel
          val paymentModel = PaymentModel.getPaymentModel(hotel.paymentModel)
          val results = calculateYPLPrice(_case.request, _case.roomEntries, paymentModel, hotel, taxInfo)

          results.size must_== _case.expectedRooms.size
          results.head._2.prices.head.netExclusive must_== 1000
          results.head._2.prices.head.tax must_== 70
          results.head._2.prices.head.margin must_== 267.5
          results.head._2.prices.head.processingFee must_== 0
          results.head._2.marginPercentage must_== 20.0

        }
      }

      "[Edge Case]Base Room (Price) Calculation with Hospitality/Mandatory Tax- isNewChargeTypeTaxApplied given as false" in {
        val cases = prepareTaxV2HotelTestCases(debugTestId = 13, isHPTaxAndFeeApplied = false, isOnlyMATaxApplied = true)

        cases.foreach { case (testId, (_case, taxInfo)) =>
          implicit val request: YplRequest = _case.request
          val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
          implicit val ctx: YplContext = YplContext(request)

          val hotel = _case.hotel
          val paymentModel = PaymentModel.getPaymentModel(hotel.paymentModel)
          val results = calculateYPLPrice(_case.request, _case.roomEntries, paymentModel, hotel, taxInfo)

          results.size must_== _case.expectedRooms.size

          results.head._2.prices.head.netExclusive must_== 1000
          results.head._2.prices.head.tax must_== 100
          results.head._2.prices.head.margin must_== 250
          results.head._2.prices.head.processingFee must_== 25
          results.head._2.marginPercentage must_== 20.0

        }
      }

      "[Edge Case]Base Room (Price) Calculation with Hospitality/Mandatory Tax- isNewChargeTypeTaxApplied given as true" in {
        val cases = prepareTaxV2HotelTestCases(debugTestId = 13, isHPTaxAndFeeApplied = true, isOnlyMATaxApplied = true)

        cases.foreach { case (testId, (_case, taxInfo)) =>
          implicit val request: YplRequest = _case.request
          val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
          implicit val ctx: YplContext = YplContext(request)

          val hotel = _case.hotel
          val paymentModel = PaymentModel.getPaymentModel(hotel.paymentModel)
          val results = calculateYPLPrice(_case.request, _case.roomEntries, paymentModel, hotel, taxInfo)

          results.size must_== _case.expectedRooms.size

          results.head._2.prices.head.netExclusive must_== 1000
          results.head._2.prices.head.tax must_== 170
          results.head._2.prices.head.margin must_== 292.5
          results.head._2.prices.head.processingFee must_== 0
          results.head._2.marginPercentage must_== 20.0

        }
      }

      "[Edge Case]Multiple Base Room (Price) Calculation with Hospitality/Mandatory Tax- isNewChargeTypeTaxApplied given as true" in {
        val taxV1Case = prepareTaxV2HotelTestCases(debugTestId = 13).head._2._1
        val taxV2Case =
          prepareTaxV2HotelTestCases(debugTestId = 13, isHPTaxAndFeeApplied = true, isOnlyMATaxApplied = true)
        val combinedDailyPrices = taxV2Case.head._2._1.roomEntries.head._2.dailyPrices.head
        val dailyPriceInput = List(taxV1Case.roomEntries.head._2.dailyPrices.head,
                                   (combinedDailyPrices._1.plusDays(1), combinedDailyPrices._2)).toMap
        val roomEntryTest = taxV2Case.head._2._1.roomEntries.map(r => r._1 -> r._2.copy(dailyPrices = dailyPriceInput))

        val testCase = taxV2Case.head._2
        implicit val request: YplRequest = testCase._1.request
        val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
        implicit val ctx: YplContext = YplContext(request)
        val hotel = testCase._1.hotel
        val taxInfo = testCase._2
        val paymentModel = PaymentModel.getPaymentModel(hotel.paymentModel)
        val results = calculateYPLPrice(request, roomEntryTest, paymentModel, hotel, taxInfo)

        results.head._2.prices.head.netExclusive must_== 1000
        results.head._2.prices.head.tax must_== 170
        results.head._2.prices.head.margin must_== 292.5
        results.head._2.prices.head.processingFee must_== 0
        results.head._2.marginPercentage must_== 20.0
        results.head._2.roomStatus must_== RatePlanStatus.Requested

      }
    }

    "Hourly slots in YPL Room" in {
      val debugTestId = 0
      val cases = if (debugTestId > 0) allCases.filter(_._1 == debugTestId) else allCases

      cases.foreach { case (testId, _case) =>
        implicit val request = _case.request
        val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
        implicit val ctx = YplContext(request)
        implicit val propertyContext = PropertyContextImpl(1, 1, 1)
        val hotel = _case.hotel
        val paymentModel = PaymentModel.getPaymentModel(hotel.paymentModel)
        val taxInfo = _case.taxInfo
        val results = _case.hourlyRoomEntries.map { x =>
          val dailyPrices = calc.calculateDailyPrices(
            room = x._2,
            paymentModel = paymentModel,
            taxInfo = taxInfo,
            agePolicy = reqOcc.agePolicy,
            ignoreRequestedNumberOfRoomsForNha = reqOcc.ignoreRequestedNumberOfRoomsForNha,
            supplierId = hotel.supplierId,
            applyDiscountsMultiplicatively = true,
            hotelId = hotel.hotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            cityId = aValidCityId,
          )
          x._1 -> calc.doPricing(
            hotelId = hotel.hotelId,
            supplierId = hotel.supplierId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            cityId = aValidCityId,
            paymentModel = PaymentModel.getPaymentModel(hotel.paymentModel),
            taxInfo = taxInfo,
            room = x._2,
            dailyPrices = dailyPrices,
            dispatchChannels = aValidYplDispatchChannels,
            surchargeRateType = RateType.Unknown,
            reqOcc = reqOcc,
            isPull = false,
            fixMarriottSurchargeExp = false,
          )
        }

        results.size must_== _case.expectedRooms.size
        _case.expectedRooms.foreach { case (refId, rooms) =>
          val expectedRoom = rooms.head
          println(
            s"[$testId] => RefID: $refId => RoomTypeID: ${expectedRoom.roomTypeId}, RatePlanID: ${expectedRoom.ratePlanId}")
          compareResult(results(refId),
                        hotel,
                        expectedRoom,
                        _case.expectedPrices(refId),
                        _case.expectedTaxes.getOrElse(refId, Seq()))
          compareHourlyTimeIntervals(results(refId), _case.expectedHourlySlots(refId))
        }
      }
    }

    "Extract Surcharges" in {

      "Merchant" in {
        val currentCase = allCases.head._2

        implicit val request: YplRequest = currentCase.request.build
        val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
        implicit val ctx: YplContext = YplContext(request)

        val taxInfo = currentCase.taxInfo
        val roomEntry: YplRoomEntry = currentCase.roomEntries.head._2
        val paymentModel = PaymentModel.getPaymentModel(currentCase.hotel.paymentModel)

        val surchargeBaseAmount = 15.0
        val surchargeValues = Range(0, 4)
          .map(it =>
            SurchargeEntry(it,
                           "PAPR",
                           ChargeOption.Mandatory,
                           Set(),
                           isAmount = true,
                           isCommissionable = false,
                           surchargeBaseAmount + it))
          .toList

        val dailyPricesWithSurcharges: Map[DateTime, DailyPrice] = roomEntry.dailyPrices.map { case (date, price) =>
          date -> price.copy(rpmSurcharges = surchargeValues)
        }

        val roomEntryWithSurcharges = roomEntry.copy(dailyPrices = dailyPricesWithSurcharges)

        val calculatedDailyPrices = calc.calculateDailyPrices(
          roomEntry,
          paymentModel,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.YCS,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)
        implicit val propertyContext = PropertyContextImpl(1, 1, 1)
        val surchargePrices = calc.getRPMSurchargePrices(
          paymentModel = paymentModel,
          taxInfo = taxInfo,
          room = roomEntryWithSurcharges,
          prices = calculatedDailyPrices,
          surchargeRateType = RateType.Unknown,
          reqOcc = reqOcc,
          isPull = false,
          supplierId = currentCase.hotel.supplierId,
          hotelId = currentCase.hotel.hotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
          fixMarriottSurchargeExp = false,
        )

        surchargeValues.size must_== surchargePrices.size
        Range(0, 4).foreach { amount =>
          val price = surchargePrices.filter(_.netExclusive == amount + surchargeBaseAmount)
          price.size must_== 1
        }
      }

      "BCOM - quantity should multiply the number of room for PB and PN" in {
        val currentCase = allCases(2)

        implicit val request: YplRequest = currentCase.request.build

        val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
        implicit val ctx: YplContext = YplContext(request)

        val taxList: Map[TaxIDWithProTypeID, Tax] = Map(
          (1, Tax.DEFAULT_PROTOTYPE_ID) -> Tax(1,
                                               "PB",
                                               isAmount = true,
                                               isFee = false,
                                               isTaxable = false,
                                               7.0,
                                               ChargeOption.Mandatory),
          (2, Tax.DEFAULT_PROTOTYPE_ID) -> Tax(2,
                                               "PB",
                                               isAmount = true,
                                               isFee = true,
                                               isTaxable = true,
                                               10.0,
                                               ChargeOption.Mandatory),
        )

        val taxInfo = TaxInfo(
          hotelTaxInfo = HotelTaxInfo(
            TaxType.SimpleTax,
            isConfigProcessingFees = false,
          ),
          taxes = taxList,
        )

        val roomEntry = currentCase.roomEntries.head._2
        val paymentModel = PaymentModel.Agency

        val surchargeBaseAmount = 15.0
        val surchargePBValues = Range(0, 2)
          .map(it =>
            SurchargeEntry(it,
                           "PB",
                           ChargeOption.Mandatory,
                           Set(),
                           isAmount = true,
                           isCommissionable = false,
                           surchargeBaseAmount + it))
          .toList
        val surchargePNValues = Range(2, 4)
          .map(it =>
            SurchargeEntry(it,
                           "PN",
                           ChargeOption.Mandatory,
                           Set(),
                           isAmount = true,
                           isCommissionable = false,
                           surchargeBaseAmount + it))
          .toList
        val surchargeValues = surchargePBValues ++ surchargePNValues

        val dailyPricesWithSurcharges: Map[DateTime, DailyPrice] = roomEntry.dailyPrices.map { case (date, price) =>
          date -> price.copy(rpmSurcharges = surchargeValues)
        }

        val roomEntryWithSurcharges = roomEntry.copy(dailyPrices = dailyPricesWithSurcharges)

        val calculatedDailyPrices = calc.calculateDailyPrices(
          roomEntry,
          PaymentModel.Agency,
          taxInfo,
          reqOcc.agePolicy,
          reqOcc.ignoreRequestedNumberOfRoomsForNha,
          DMC.BCOM,
          true,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
        )(ctx, aValidPropertyContext)
        implicit val propertyContext = PropertyContextImpl(1, 1, 1)
        val surchargePrices = calc.getRPMSurchargePrices(
          paymentModel = paymentModel,
          taxInfo = taxInfo,
          room = roomEntryWithSurcharges,
          prices = calculatedDailyPrices,
          surchargeRateType = RateType.Unknown,
          reqOcc = reqOcc,
          isPull = false,
          supplierId = DMC.BCOM,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
          fixMarriottSurchargeExp = false,
        )
        surchargeValues.size must_== surchargePrices.size
        surchargePrices.foreach(s => s.quantity must_== 2)
        Range(0, 4).foreach { amount =>
          val price = surchargePrices.filter(_.netExclusive == amount + surchargeBaseAmount)
          price.size must_== 1

        }
      }
    }

    "Extract RPM Surcharges" in {
      val currentCase = allCases.head._2

      implicit val request: YplRequest = currentCase.request.build
      val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
      implicit val ctx: YplContext = YplContext(request)
      val taxInfo = currentCase.taxInfo
      val roomEntry = currentCase.roomEntries.head._2
      val paymentModel = PaymentModel.getPaymentModel(currentCase.hotel.paymentModel)

      val surchargeBaseAmount = 20.0
      val surchargeValues = Range(0, 6)
        .map(it =>
          SurchargeEntry(it,
                         "PAPR",
                         ChargeOption.Mandatory,
                         Set(),
                         isAmount = true,
                         isCommissionable = false,
                         surchargeBaseAmount + it))
        .toList

      val dailyPricesWithSurcharges: Map[DateTime, DailyPrice] = roomEntry.dailyPrices.map { case (date, price) =>
        date -> price.copy(rpmSurcharges = surchargeValues)
      }

      val roomEntryWithSurcharges = roomEntry.copy(dailyPrices = dailyPricesWithSurcharges)

      val calculatedDailyPrices = calc.calculateDailyPrices(
        roomEntry,
        paymentModel,
        taxInfo,
        reqOcc.agePolicy,
        reqOcc.ignoreRequestedNumberOfRoomsForNha,
        DMC.YCS,
        true,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
      )(ctx, aValidPropertyContext)

      val surchargePrices = calc.getRPMSurchargePrices(
        paymentModel,
        taxInfo,
        roomEntryWithSurcharges,
        calculatedDailyPrices,
        RateType.Unknown,
        reqOcc,
        isPull = false,
        supplierId = currentCase.hotel.supplierId,
        hotelId = currentCase.hotel.hotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
        fixMarriottSurchargeExp = false,
      )(ctx, aValidPropertyContext)

      "VEL-2070=A: Call PriceCalculation" in {
        val priceBreakdownCalcSpy = new MockPriceBreakdownCalculator {}
        val calc = new AnyRef
          with PriceCalculatorImpl
          with PriceCalculationImpl
          with TaxCalculatorImpl
          with TaxPrototypeServiceImpl
          with CommissionServiceImpl {
          override val priceBreakdownCalculator: PriceBreakdownCalculatorInterface = priceBreakdownCalcSpy
        }

        val surchargePrices = calc.getRPMSurchargePrices(
          paymentModel,
          taxInfo,
          roomEntryWithSurcharges,
          calculatedDailyPrices,
          RateType.Unknown,
          reqOcc,
          isPull = false,
          supplierId = currentCase.hotel.supplierId,
          hotelId = currentCase.hotel.hotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
          fixMarriottSurchargeExp = false,
        )(ctx, aValidPropertyContext)

        surchargeValues.size must_== surchargePrices.size

        Range(0, 6).foreach { amount =>
          val price = surchargePrices.filter(_.netExclusive == surchargeBaseAmount + amount)
          price.size must_== 1
        }

        priceBreakdownCalcSpy.callCounter should_== 0
      }

      "VEL-2070=B: Call PriceBreakdownCalculator" in {
        implicit val request: YplRequest = currentCase.request.build
        val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
        implicit val ctx: YplContext = YplContext(request.withBExperiment(ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION))

        val priceBreakdownCalcSpy = new MockPriceBreakdownCalculator {}
        val calc = new AnyRef
          with PriceCalculatorImpl
          with PriceCalculationImpl
          with TaxCalculatorImpl
          with TaxPrototypeServiceImpl
          with CommissionServiceImpl {
          override val priceBreakdownCalculator: PriceBreakdownCalculatorInterface = priceBreakdownCalcSpy
        }

        val surchargePrices = calc.getRPMSurchargePrices(
          paymentModel,
          taxInfo,
          roomEntryWithSurcharges,
          calculatedDailyPrices,
          RateType.Unknown,
          reqOcc,
          isPull = false,
          supplierId = currentCase.hotel.supplierId,
          hotelId = currentCase.hotel.hotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          cityId = aValidCityId,
          fixMarriottSurchargeExp = false,
        )(ctx, aValidPropertyContext)

        surchargeValues.size must_== surchargePrices.size

        Range(0, 6).foreach { amount =>
          val price = surchargePrices.filter(_.netExclusive == surchargeBaseAmount + amount)
          price.size must_== 1
        }

        // one call per one price
        priceBreakdownCalcSpy.callCounter should_== surchargePrices.size
      }
    }

    "getApplicableSurcharges for non propoffer with extraperson - isPullAndFreeOcc true should not filter anything" in {
      val guestsPerRoom = 2
      val occSurcharges: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withId(1).withOccFromProto(0).withIsPropOffer(false).build)
      val extraPersonSurcharge: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withOccFromProto(3).withIsPropOffer(false))
      val allSurcharges: List[SurchargeEntry] = occSurcharges ++ extraPersonSurcharge
      val dailyPricesWithSurcharges: DailyPrice = aValidDailyPrice.withRpmSurcharges(allSurcharges)
      val roomEntry = aValidRoomEntry
        .withDailyPrices(Map(new DateTime("2025-05-25") -> dailyPricesWithSurcharges))
        .withOccFromProto(guestsPerRoom)

      val surchargePrices =
        calc.getApplicableSurcharges(roomEntry, dailyPricesWithSurcharges, true, fixMarriottSurchargeExp = false)

      surchargePrices must containTheSameElementsAs(allSurcharges) // not filter anything
    }

    "getApplicableSurcharges for non Propoffer with extraperson - isPullAndFreeOcc false should return only matching occ surcharge if guests less than extraperson surcharge" in {
      val guestsPerRoom = 2
      val occSurcharges: List[SurchargeEntry] = List(1, 2, 3).map(occ =>
        aValidSurchargeEntry
          .withId(1)
          .withOccFromProto(occ)
          .withIsPropOffer(false)
          .build) // allocc surcharge for propoffer will have occ 0
      val extraPersonSurcharge: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withOccFromProto(3).withIsPropOffer(false))
      val allSurcharges: List[SurchargeEntry] = occSurcharges ++ extraPersonSurcharge
      val dailyPricesWithSurcharges: DailyPrice = aValidDailyPrice.withRpmSurcharges(allSurcharges)
      val roomEntry = aValidRoomEntry
        .withDailyPrices(Map(new DateTime("2025-05-25") -> dailyPricesWithSurcharges))
        .withOccFromProto(guestsPerRoom)

      val surchargePrices =
        calc.getApplicableSurcharges(roomEntry, dailyPricesWithSurcharges, false, fixMarriottSurchargeExp = false)

      surchargePrices must containTheSameElementsAs(
        List(
          aValidSurchargeEntry.withId(1).withOccFromProto(guestsPerRoom).build,
        ))
    }

    "getApplicableSurcharges for non Propoffer with extraperson - isPullAndFreeOcc false should return matching occ surcharge and extraperson if guests matches extraperson surcharge occ" in {
      val guestsPerRoom = 3
      val occSurcharges: List[SurchargeEntry] = List(1, 2, 3).map(occ =>
        aValidSurchargeEntry
          .withId(1)
          .withOccFromProto(occ)
          .withIsPropOffer(false)
          .build) // allocc surcharge for propoffer will have occ 0
      val extraPersonSurcharge: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withOccFromProto(3).withIsPropOffer(false))
      val allSurcharges: List[SurchargeEntry] = occSurcharges ++ extraPersonSurcharge
      val dailyPricesWithSurcharges: DailyPrice = aValidDailyPrice.withRpmSurcharges(allSurcharges)
      val roomEntry = aValidRoomEntry
        .withDailyPrices(Map(new DateTime("2025-05-25") -> dailyPricesWithSurcharges))
        .withOccFromProto(guestsPerRoom)

      val surchargePrices =
        calc.getApplicableSurcharges(roomEntry, dailyPricesWithSurcharges, false, fixMarriottSurchargeExp = false)

      surchargePrices must containTheSameElementsAs(
        List(
          aValidSurchargeEntry.withId(1).withOccFromProto(guestsPerRoom).build,
        ) ++ extraPersonSurcharge)
    }

    "getApplicableSurcharges for Propoffer with extraperson - isPullAndFreeOcc true should not filter anything" in {
      val guestsPerRoom = 2
      // propoffer, occfromproto 0 means applies to all occ
      val occSurcharges: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withId(1).withOccFromProto(0).withIsPropOffer(true).build)
      val extraPersonSurcharge: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withOccFromProto(3).withIsPropOffer(true))
      val allSurcharges: List[SurchargeEntry] = occSurcharges ++ extraPersonSurcharge
      val dailyPricesWithSurcharges: DailyPrice = aValidDailyPrice.withRpmSurcharges(allSurcharges)
      val roomEntry = aValidRoomEntry
        .withDailyPrices(Map(new DateTime("2025-05-25") -> dailyPricesWithSurcharges))
        .withOccFromProto(guestsPerRoom)

      val surchargePrices =
        calc.getApplicableSurcharges(roomEntry, dailyPricesWithSurcharges, true, fixMarriottSurchargeExp = false)

      surchargePrices must containTheSameElementsAs(allSurcharges) // not filter anything
    }

    "getApplicableSurcharges for Propoffer with extraperson - isPullAndFreeOcc false should return all occ surcharge if guests less than extraperson surcharge" in {
      val guestsPerRoom = 2
      val occSurcharges: List[SurchargeEntry] = List(
        aValidSurchargeEntry.withId(1).withOccFromProto(0).withIsPropOffer(true).build,
      ) // allocc surcharge for propoffer will have occ 0
      val extraPersonSurcharge: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withOccFromProto(3).withIsPropOffer(true))
      val allSurcharges: List[SurchargeEntry] = occSurcharges ++ extraPersonSurcharge
      val dailyPricesWithSurcharges: DailyPrice = aValidDailyPrice.withRpmSurcharges(allSurcharges)
      val roomEntry = aValidRoomEntry
        .withDailyPrices(Map(new DateTime("2025-05-25") -> dailyPricesWithSurcharges))
        .withOccFromProto(guestsPerRoom)

      val surchargePrices =
        calc.getApplicableSurcharges(roomEntry, dailyPricesWithSurcharges, false, fixMarriottSurchargeExp = false)

      surchargePrices must containTheSameElementsAs(occSurcharges)
    }

    "getApplicableSurcharges for Propoffer with extraperson - isPullAndFreeOcc false should return all occ surcharge and extraperson if guests matches extraperson surcharge occ" in {
      val guestsPerRoom = 3
      val occSurcharges: List[SurchargeEntry] = List(
        aValidSurchargeEntry.withId(1).withOccFromProto(0).withIsPropOffer(true).build,
      ) // allocc surcharge for propoffer will have occ 0
      val extraPersonSurcharge: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withOccFromProto(3).withIsPropOffer(true))
      val allSurcharges: List[SurchargeEntry] = occSurcharges ++ extraPersonSurcharge
      val dailyPricesWithSurcharges: DailyPrice = aValidDailyPrice.withRpmSurcharges(allSurcharges)
      val roomEntry = aValidRoomEntry
        .withDailyPrices(Map(new DateTime("2025-05-25") -> dailyPricesWithSurcharges))
        .withOccFromProto(guestsPerRoom)

      val surchargePrices =
        calc.getApplicableSurcharges(roomEntry, dailyPricesWithSurcharges, false, fixMarriottSurchargeExp = false)

      surchargePrices must containTheSameElementsAs(allSurcharges)
    }

    "getApplicableSurcharges for Propoffer, VEL-1935 = B & occupancyBreakdown is nonEmpty should not filter PR rates" in {
      val guestsPerRoom = 2
      // propoffer, occfromproto 0 means applies to all occ
      val occSurcharges: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withId(1).withOccFromProto(0).withIsPropOffer(true).build)
      val extraPersonSurcharge: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withOccFromProto(3).withIsPropOffer(true).withApplyTo("PRPB"))
      val allSurcharges: List[SurchargeEntry] = occSurcharges ++ extraPersonSurcharge
      val dailyPricesWithSurcharges: DailyPrice = aValidDailyPrice.withRpmSurcharges(allSurcharges)

      val occuUnit1 = aValidOccupancyUnit.withRoomNo(1).withQty(2)
      val occuUnit2 = aValidOccupancyUnit.withRoomNo(2).withQty(3)
      val occupancyBreakdown =
        OccupancyBreakdown(List(occuUnit1, occuUnit2), 5, MaxAllowedFreeChildAgeRange.empty, false)

      val roomEntry = aValidRoomEntry
        .withDailyPrices(Map(new DateTime("2025-05-25") -> dailyPricesWithSurcharges))
        .withOccFromProto(guestsPerRoom)
        .withOccupancyBreakdown(Some(occupancyBreakdown))

      val surchargePrices =
        calc.getApplicableSurcharges(roomEntry, dailyPricesWithSurcharges, false, fixMarriottSurchargeExp = true)

      surchargePrices must containTheSameElementsAs(allSurcharges) // not filter anything
    }

    "getApplicableSurcharges for Propoffer, VEL-1935 = B & occupancyBreakdown is empty (isAllOcc request) should filter" in {
      val guestsPerRoom = 2
      // propoffer, occfromproto 0 means applies to all occ
      val occSurcharges: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withId(1).withOccFromProto(0).withIsPropOffer(true).build)
      val extraPersonSurcharge: List[SurchargeEntry] =
        List(aValidSurchargeEntry.withOccFromProto(3).withIsPropOffer(true).withApplyTo("PRPB"))
      val allSurcharges: List[SurchargeEntry] = occSurcharges ++ extraPersonSurcharge
      val dailyPricesWithSurcharges: DailyPrice = aValidDailyPrice.withRpmSurcharges(allSurcharges)

      val occupancyBreakdown = OccupancyBreakdown(List(), 0, MaxAllowedFreeChildAgeRange.empty, false)

      val roomEntry = aValidRoomEntry
        .withDailyPrices(Map(new DateTime("2025-05-25") -> dailyPricesWithSurcharges))
        .withOccFromProto(guestsPerRoom)
        .withOccupancyBreakdown(Some(occupancyBreakdown))

      val surchargePrices =
        calc.getApplicableSurcharges(roomEntry, dailyPricesWithSurcharges, false, fixMarriottSurchargeExp = true)

      surchargePrices must containTheSameElementsAs(occSurcharges) // not filter anything
    }

    "Get Room Prices for Amount Surcharge" in {
      val currentCase = allCases.head._2
      val roomEntry = currentCase.roomEntries.head._2

      val prices = Range(0, 6).map(it => createPrice(chargeType = ChargeType.Room)).toList

      val surcharge =
        SurchargeEntry(0, "PCPR", ChargeOption.Mandatory, Set(), isAmount = true, isCommissionable = false, 10.0)

      val roomPrices = calc.getRoomPrices(prices, surcharge)

      roomPrices.isEmpty must_== true
    }

    "Get Room Prices for Percent Surcharge" in {
      val surcharge =
        SurchargeEntry(0, "PB", ChargeOption.Mandatory, Set(), isAmount = false, isCommissionable = false, 100.0)

      val numberOfPrices = 6
      val prices = Range(0, numberOfPrices).map(it => createPrice(chargeType = ChargeType.Room)).toList

      val roomPrices = calc.getRoomPrices(prices, surcharge)

      roomPrices.size must_== numberOfPrices
    }

    "Calculate correct downlift" in {
      implicit val ctx: YplContext = YplContext(aValidYplRequest)
      val tollerance = 0.1
      val taxBreakdown = List(
        CommonTaxBreakdown(typeId = 2,
                           isFee = true,
                           quantity = 1,
                           amount = 218.748,
                           applyTo = "PB",
                           percentage = 10.0,
                           noAdjust = false,
                           include = true,
                           orderNumber = 0),
        CommonTaxBreakdown(typeId = 1,
                           isFee = false,
                           quantity = 1,
                           amount = 190.332,
                           applyTo = "PB",
                           percentage = 7.91,
                           noAdjust = false,
                           include = true,
                           orderNumber = 0),
      )
      val taxApplyOnSellExBreakdown = List(
        CommonTaxBreakdown(
          typeId = 2,
          isFee = false,
          quantity = 1,
          amount = 218.748,
          applyTo = "PB",
          percentage = 10.0,
          noAdjust = false,
          include = true,
          applyOver = Some(ApplyTaxOver.SaleEx),
          orderNumber = 0,
        ),
      )
      val price = createPrice(chargeType = ChargeType.Room,
                              netExclusive = 2187.482,
                              tax = 190.333,
                              fee = 218.748,
                              margin = 312.497,
                              processingFee = 58.44,
                              taxBreakDown = taxBreakdown)

      "When isConfigProcessingFee = False" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(isConfigProcessingFee = false,
                             processingFeeBreakdown = Some(CommonProcessingFeeBreakdown(30, 22.82))),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.Merchant,
        )
        newPrice.downliftAmount must beCloseTo(35.6, tollerance)
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(30, tollerance)
        newPrice.processingFee must beCloseTo(52.83, tollerance)
        newPrice.margin must beCloseTo(282.49, tollerance)
        newPrice.processingFeeBreakdown must beNone
      }

      "When isConfigProcessingFee = False with dailyTax" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(isConfigProcessingFee = false,
                             processingFeeBreakdown = Some(CommonProcessingFeeBreakdown(30, 22.82)),
                             dailyTaxes = aValidDailyTax),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.Merchant,
        )
        newPrice.downliftAmount must beCloseTo(35.6, tollerance)
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(30, tollerance)
        newPrice.processingFee must beCloseTo(52.83, tollerance)
        newPrice.margin must beCloseTo(282.49, tollerance)
        newPrice.processingFeeBreakdown.get.taxPortion must beCloseTo(28.25056765290649, tollerance)
      }

      "When isConfigProcessingFee = False with dailyTax with tax on sell ex" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(
            isConfigProcessingFee = false,
            processingFeeBreakdown = Some(CommonProcessingFeeBreakdown(30, 22.82)),
            dailyTaxes = aValidDailyTax.copy(taxes =
              aValidDailyTax.taxes :+ TaxWithValue(aValidTaxApplyOnSellEx.copy(value = 10.0), 10.0)),
          ),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.Merchant,
        )
        newPrice.downliftAmount must beCloseTo(35.6, tollerance)
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(30, tollerance)
        newPrice.processingFee must beCloseTo(52.83, tollerance)
        newPrice.margin must beCloseTo(282.49, tollerance)
        newPrice.processingFeeBreakdown.get.taxPortion must beCloseTo(28.25056765290649, tollerance)
      }

      val mockPrices =
        price.copy(isConfigProcessingFee = false,
                   taxBreakDown =
                     taxBreakdown ++ List(aValidTaxBreakdown.withAmount(1000).withOption(ChargeOption.Excluded).build))
      "When isConfigProcessingFee = False and skipTaxExcludedAmount = False, there is Tax Exclude Amount setting" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(price = mockPrices,
                                                                downliftAmount = 35.6,
                                                                skipTaxExcludedAmount = false,
                                                                paymentModel = PaymentModel.Merchant)
        newPrice.downliftAmount must beCloseTo(35.6, tollerance)
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(29.99, tollerance)
        newPrice.processingFee must beCloseTo(52.83, tollerance)
        newPrice.margin must beCloseTo(282.50, tollerance)
        newPrice.processingFeeBreakdown must beNone
      }

      "When isConfigProcessingFee = False and skipTaxExcludedAmount = True there is Tax Exclude Amount setting" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = mockPrices,
          downliftAmount = 35.6,
          skipTaxExcludedAmount = true,
          paymentModel = PaymentModel.Merchant,
        )
        newPrice.downliftAmount must beCloseTo(35.6, tollerance)
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(30, tollerance)
        newPrice.processingFee must beCloseTo(52.83, tollerance)
        newPrice.margin must beCloseTo(282.49, tollerance)
        newPrice.processingFeeBreakdown must beNone
      }

      "When isConfigProcessingFee = True and payment model is Merchant" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(isConfigProcessingFee = true),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.Merchant,
        )
        newPrice.downliftAmount must beCloseTo(35.6, tollerance)
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(35.6, tollerance)
        newPrice.margin must beCloseTo(276.88, tollerance)
        newPrice.processingFeeBreakdown must beNone
      }

      "When isConfigProcessingFee = True and payment model is MerchantCommission" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(isConfigProcessingFee = true),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.MerchantCommission,
        )
        newPrice.downliftAmount must beCloseTo(35.6, tollerance)
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(35.6, tollerance)
        newPrice.margin must beCloseTo(276.88, tollerance)
        newPrice.processingFeeBreakdown must beNone
      }

      "When PaymentModel is Merchant with ProcessingFee = 0" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(processingFee = 0),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.Merchant,
        )
        newPrice.downliftAmount must beCloseTo(35.6, tollerance)
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(35.6, tollerance)
        newPrice.processingFee must beCloseTo(0d, tollerance)
        newPrice.margin must beCloseTo(276.897, tollerance)
        newPrice.processingFeeBreakdown must beNone
      }

      "When MerchantCommission has processing fee" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(processingFee = 58.44),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.MerchantCommission,
        )
        newPrice.downliftAmount must beCloseTo(35.6, tollerance)
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(29.99, tollerance)
        newPrice.processingFee must beCloseTo(52.83, tollerance)
        newPrice.margin must beCloseTo(282.506, tollerance)
        newPrice.processingFeeBreakdown must beNone
      }

      "When PaymentModel is MerchantCommission and ProcessingFee = 0" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(processingFee = 0),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.MerchantCommission,
        )
        newPrice.downliftAmount must beCloseTo(35.6, tollerance)
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(35.6, tollerance)
        newPrice.processingFee must beCloseTo(0d, tollerance)
        newPrice.margin must beCloseTo(276.897, tollerance)
        newPrice.processingFeeBreakdown must beNone
      }

      "When PaymentModel is MerchantCommission and tax is incorrect - old formula should downlift incorrectly" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(margin = 100, processingFee = 10),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.MerchantCommission,
        )
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(32.36, tollerance)
      }

      "When PaymentModel is MerchantCommission with HP tax for NEP-25984 (old)" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(
            margin = 100,
            processingFee = 10,
            dailyTaxes = DailyTaxes(allPercentHPTaxAndFee, isCleanedUpHospitalityTax = false),
            taxBreakDown = List(aValidTaxBreakdown.copy(option = ChargeOption.HospitalityPrice)),
          ),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.MerchantCommission,
        )
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(32.36, tollerance)
        newPrice.processingFeeBreakdown.isDefined must_== false
      }

      "When PaymentModel is MerchantCommission with HP tax for NEP-25984 (new)" in {
        val newPrice = PriceCalculator.applyPriceDownliftAmount(
          price = price.copy(
            margin = 100,
            processingFee = 10,
            dailyTaxes = DailyTaxes(allPercentHPTaxAndFee, isCleanedUpHospitalityTax = true),
            taxBreakDown = List(aValidTaxBreakdown.copy(option = ChargeOption.HospitalityPrice)),
          ),
          downliftAmount = 35.6,
          paymentModel = PaymentModel.MerchantCommission,
        )
        newPrice.downliftExAmount.getOrElse(0d) must beCloseTo(32.36, tollerance)
        newPrice.processingFeeBreakdown.isDefined must_== true
      }
    }

    "Verified haveMultiRoomPromotion and isMultipleRoomAssignmentPrice work as expected" in {
      val currentCase = allCases.head._2

      implicit val request: YplRequest = currentCase.request
      val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
      implicit val ctx: YplContext = YplContext(request)
      implicit val propertyContext = PropertyContextImpl(1, 1, 1)
      val hotel = currentCase.hotel
      val taxInfo = currentCase.taxInfo
      val roomEntryWithFlag =
        currentCase.roomEntries.head._2.copy(haveMultiRoomPromotion = true, isMultipleRoomAssignmentPrice = true)
      val roomEntryWithoutFlag = currentCase.roomEntries.head._2
      val paymentModel = PaymentModel.getPaymentModel(currentCase.hotel.paymentModel)

      val resultWithFlag = calc.doPricing(
        hotel.hotelId,
        hotel.supplierId,
        aValidChainId,
        aValidCountryId,
        aValidCityId,
        PaymentModel.getPaymentModel(hotel.paymentModel),
        taxInfo,
        roomEntryWithFlag,
        List.empty,
        aValidYplDispatchChannels,
        reqOcc = reqOcc,
        fixMarriottSurchargeExp = false,
        RateType.Unknown,
        isPull = false,
      )
      resultWithFlag.roomFeatures.haveMultiRoomPromotion must_== true
      resultWithFlag.roomFeatures.isMultipleRoomAssignmentPrice must_== true

      val resultWithoutFlag = calc.doPricing(
        hotel.hotelId,
        hotel.supplierId,
        aValidChainId,
        aValidCountryId,
        aValidCityId,
        PaymentModel.getPaymentModel(hotel.paymentModel),
        taxInfo,
        roomEntryWithoutFlag,
        List.empty,
        aValidYplDispatchChannels,
        reqOcc = reqOcc,
        fixMarriottSurchargeExp = false,
        RateType.Unknown,
        isPull = false,
      )
      resultWithoutFlag.roomFeatures.haveMultiRoomPromotion must_== false
      resultWithoutFlag.roomFeatures.isMultipleRoomAssignmentPrice must_== false
    }

    "Verified childRateSetting work as expected" in {
      val currentCase = allCases.head._2

      implicit val request: YplRequest = currentCase.request
      val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
      implicit val ctx: YplContext = YplContext(request)
      implicit val propertyContext = PropertyContextImpl(1, 1, 1)
      val hotel = currentCase.hotel
      val taxInfo = currentCase.taxInfo
      val checkIn = new DateTime("2025-05-25")
      val mockDailyPrice = DailyPrice(
        checkIn,
        Map.empty,
        false,
        List(PriceEntry(checkIn, ChargeType.Room, chargeOption = ChargeOption.Mandatory, applyTo = "PB", 2, 10d)))
      val dailyPrices = Map(checkIn.plusDays(1) -> mockDailyPrice)

      val childRate = Seq(ChildRate(ChildRateType.PreSchool, PricingChildRateType.FlatPrice, 100, true))
      val rateCategory = RateCategoryEntry(
        rateCategoryId = 123,
        rateCategoryCode = None,
        cxlCode = "",
        parent = None,
        rateTypeLoaded = RateType.Unknown,
        remainingRoom = 123,
        bookFrom = None,
        bookTo = None,
        bookTimeFrom = None,
        bookTimeTo = None,
        minAdvance = None,
        maxAdvance = None,
        isCanCombinePromotion = true,
        offerType = None,
        benefitList = List(),
        dailyPrices = dailyPrices,
        remainingRoomGa = 123,
        remainingRoomRa = Option(123),
        isAmount = true,
        applyTo = "",
        value = 1,
        promotionList = List(),
        customerSegment = List(CustomerSegment(countryCode = Option("TH"), languageId = Option(99))),
        childRate = childRate,
      )

      val roomEntryWithChildRate = currentCase.roomEntries.head._2.copy(rateCategory = rateCategory)
      val roomEntryWithoutChildRate =
        currentCase.roomEntries.head._2.copy(rateCategory = rateCategory.copy(childRate = Seq.empty))

      val resultWithChildRateSetting = calc.doPricing(
        hotel.hotelId,
        hotel.supplierId,
        aValidChainId,
        aValidCountryId,
        aValidCityId,
        PaymentModel.getPaymentModel(hotel.paymentModel),
        taxInfo,
        roomEntryWithChildRate,
        List.empty,
        aValidYplDispatchChannels,
        reqOcc = reqOcc,
        fixMarriottSurchargeExp = false,
        RateType.Unknown,
      )
      resultWithChildRateSetting.childRateSettings must beEqualTo(childRate)

      val resultWithoutChildRateSetting = calc.doPricing(
        hotel.hotelId,
        hotel.supplierId,
        aValidChainId,
        aValidCountryId,
        aValidCityId,
        PaymentModel.getPaymentModel(hotel.paymentModel),
        taxInfo,
        roomEntryWithoutChildRate,
        List.empty,
        aValidYplDispatchChannels,
        reqOcc = reqOcc,
        fixMarriottSurchargeExp = false,
        RateType.Unknown,
      )
      resultWithoutChildRateSetting.childRateSettings must beEqualTo(Seq.empty)
    }
  }

  "choose surcharges" should {
    val emptyRPMSurcharges = Iterable()

    val rpmSurchargeCount = 2
    val nonEmptyRPMSurcharges = Range(0, rpmSurchargeCount).map(_ => createPrice())

    val surchargeCount = 3
    val surcharges = Range(0, surchargeCount).map(_ => createPrice())

    "IsYCS should always set to true when OTA Supplier is true" in {
      val currentCase = allCases.head._2

      implicit val request: YplRequest = currentCase.request
      val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
      implicit val ctx: YplContext = YplContext(request)
      val hotel = currentCase.hotel
      implicit val propertyContext = PropertyContextImpl(1, 1, 1)
      val resultWithoutFlag = calc.doPricing(
        hotel.hotelId,
        hotel.supplierId,
        aValidChainId,
        aValidCountryId,
        aValidCityId,
        PaymentModel.getPaymentModel(hotel.paymentModel),
        currentCase.taxInfo,
        currentCase.roomEntries.head._2,
        List.empty,
        aValidYplDispatchChannels,
        reqOcc = reqOcc,
        fixMarriottSurchargeExp = false,
        RateType.Unknown,
        isOTASupplier = None,
        isPull = false,
      )

      val resultWithFlag = calc.doPricing(
        hotel.hotelId,
        hotel.supplierId,
        aValidChainId,
        aValidCountryId,
        aValidCityId,
        PaymentModel.getPaymentModel(hotel.paymentModel),
        currentCase.taxInfo,
        currentCase.roomEntries.head._2,
        List.empty,
        aValidYplDispatchChannels,
        reqOcc = reqOcc,
        fixMarriottSurchargeExp = false,
        RateType.Unknown,
        isOTASupplier = Some(true),
        isPull = false,
      )

      resultWithoutFlag.actingAsYcs must_== None
      resultWithFlag.actingAsYcs must_== Some(true)
    }
  }

  "cxlChargeSetting" should {
    "return type correctly" in {
      val currentCase = allCases.head._2

      implicit val request: YplRequest = currentCase.request
      val reqOcc = YplReqOccByHotelAgePolicy(request.occ, AgePolicy(childMaxAge = 10))
      implicit val ctx: YplContext = YplContext(request)
      val hotel = currentCase.hotel
      implicit val propertyContext = PropertyContextImpl(1, 1, 1)
      val resultWithCxlType = calc.doPricing(
        hotel.hotelId,
        hotel.supplierId,
        aValidChainId,
        aValidCountryId,
        aValidCityId,
        PaymentModel.getPaymentModel(hotel.paymentModel),
        currentCase.taxInfo,
        currentCase.roomEntries.head._2.copy(cxlChargeSetting = Some(CancellationChargeSettingType.FirstNight)),
        List.empty,
        aValidYplDispatchChannels,
        reqOcc = reqOcc,
        fixMarriottSurchargeExp = false,
        RateType.Unknown,
        isOTASupplier = None,
        isPull = false,
      )

      val resultWithOutCxlType = calc.doPricing(
        hotel.hotelId,
        hotel.supplierId,
        aValidChainId,
        aValidCountryId,
        aValidCityId,
        PaymentModel.getPaymentModel(hotel.paymentModel),
        currentCase.taxInfo,
        currentCase.roomEntries.head._2,
        List.empty,
        aValidYplDispatchChannels,
        reqOcc = reqOcc,
        fixMarriottSurchargeExp = false,
        RateType.Unknown,
        isOTASupplier = Some(true),
        isPull = false,
      )

      resultWithCxlType.cxlChargeSetting must_== Some(CancellationChargeSettingType.FirstNight)
      resultWithOutCxlType.cxlChargeSetting must_== None
    }
  }

  "masterRoomId, preMappedMasterRoomId and isAgodaBrand set correctly " should {
    val hotel = aValidHotelEntryModel
    val taxInfo = aValidTaxInfo
    val reqOcc = aValidReqOcc
    implicit val ctx = aValidYplContext
    implicit val propertyContext = PropertyContextImpl(1, 1, 1)
    def test(roomTypeEntry: YplRoomEntry, enabledRoomMap: Map[RoomTypeId, EnabledRoom])(
      expectedRoomTypeId: RoomTypeId,
      expectedMasterRoomTypeId: RoomTypeId,
      expectedPremapMasterRoomTypeId: RoomTypeId) = {
      val yplRoom = calc.doPricing(
        hotel.hotelId,
        hotel.supplierId,
        aValidChainId,
        aValidCountryId,
        aValidCityId,
        PaymentModel.getPaymentModel(hotel.paymentModel.i),
        taxInfo,
        roomTypeEntry,
        List.empty,
        aValidYplDispatchChannels,
        reqOcc = reqOcc,
        fixMarriottSurchargeExp = false,
        RateType.Unknown,
        isOTASupplier = Some(true),
        enabledRoomMap = enabledRoomMap,
      )
      yplRoom.roomTypeId shouldEqual (expectedRoomTypeId)
      yplRoom.masterRoomId shouldEqual (Some(expectedMasterRoomTypeId))
      yplRoom.preMappedMasterRoomId shouldEqual (Some(expectedPremapMasterRoomTypeId))
    }

    def testAgodaBrand(ctx: YplContext)(expectedAgodaBrand: Boolean) = {
      val yplRoom = calc.doPricing(
        hotel.hotelId,
        hotel.supplierId,
        aValidChainId,
        aValidCountryId,
        aValidCityId,
        PaymentModel.getPaymentModel(hotel.paymentModel.i),
        taxInfo,
        aValidRoomEntry,
        List.empty,
        aValidYplDispatchChannels,
        reqOcc = reqOcc,
        fixMarriottSurchargeExp = false,
        RateType.Unknown,
        isOTASupplier = Some(true),
      )(ctx, PropertyContextImpl(1, 1, 1))
      yplRoom.isAgodaBrand shouldEqual (expectedAgodaBrand)
    }

    "when enabledRoom non Empty and masterID != 0" in {
      val roomId = 1L
      val masterRoomId = 2L
      val enabledRoom = aValidOtaEnabledRoomWithLinkage.withMasterRoomTypeId(masterRoomId)
      val enabledRoomMap = Map(1L -> enabledRoom)
      val room = aValidRoomEntry.withRoomTypeId(1L)

      test(room, enabledRoomMap)(roomId, masterRoomId, masterRoomId)
    }

    "when enabledRoom non Empty and masterId == 0" in {
      val roomId = 1L
      val masterRoomId = 0L
      val enabledRoom = aValidOtaEnabledRoomWithLinkage.withMasterRoomTypeId(masterRoomId)
      val enabledRoomMap = Map(1L -> enabledRoom)
      val room = aValidRoomEntry.withRoomTypeId(1L)

      test(room, enabledRoomMap)(roomId, masterRoomId, roomId)
    }

    "when no enabled Room" in {
      val roomId = 1L
      val enabledRoomMap = Map.empty[RoomTypeId, EnabledRoom]
      val room = aValidRoomEntry.withRoomTypeId(1L)

      test(room, enabledRoomMap)(roomId, roomId, roomId)
    }

    "isAgodaBrand to false if no setting " in {
      testAgodaBrand(aValidYplContext.withSupplierSetting(Map.empty))(false)
    }

    "isAgodaBrand to true if setting with true " in {
      testAgodaBrand(
        aValidYplContext.withSupplierSetting(Map(hotel.supplierId -> aValidYplSupplierSetting.withIsAgodaBrand(true))))(
        true)
    }

    "isAgodaBrand to true if setting with false " in {
      testAgodaBrand(
        aValidYplContext.withSupplierSetting(Map(hotel.supplierId -> aValidYplSupplierSetting.withIsAgodaBrand(false))))(
        false)
    }
  }

  "get Room Level Payment Model correctly" should {
    calc.getRoomLevelPaymentModel(aValidRoomEntry.withPaymentModel(Some(PaymentModel.Merchant)),
                                  PaymentModel.MerchantCommission) must be(PaymentModel.Merchant)
    calc.getRoomLevelPaymentModel(aValidRoomEntry.withPaymentModel(None), PaymentModel.MerchantCommission) must be(
      PaymentModel.MerchantCommission)
    calc.getRoomLevelPaymentModel(aValidRoomEntry.withPaymentModel(Some(PaymentModel.Merchant)),
                                  PaymentModel.Merchant) must be(PaymentModel.Merchant)
  }

  "get Room Level Is Domestic Only correctly" should {
    implicit val ctx = aValidYplContext
    calc.getIsDomesticOnly(supplierId = DMC.BCOM,
                           channel = YplMasterChannel.Domestic,
                           aValidYplDispatchChannels) must beFalse
    calc.getIsDomesticOnly(supplierId = DMC.YCS, channel = YplMasterChannel.RTL, aValidYplDispatchChannels) must beFalse
    calc.getIsDomesticOnly(supplierId = DMC.YCS, channel = YplMasterChannel.RTL, aValidYplDispatchChannels) must beFalse
    calc.getIsDomesticOnly(supplierId = DMC.YCS,
                           channel = YplChannel(Channel.Mobile, Set(Channel.APS), 222),
                           aValidYplDispatchChannels) must beFalse

    calc.getIsDomesticOnly(supplierId = DMC.YCS,
                           channel = YplMasterChannel.Domestic,
                           aValidYplDispatchChannels) must beTrue
    calc.getIsDomesticOnly(supplierId = DMC.YCS,
                           channel = YplMasterChannel.GoLocalAPS,
                           aValidYplDispatchChannels) must beTrue
    calc.getIsDomesticOnly(supplierId = DMC.YCS,
                           channel = YplMasterChannel.GoLocalMobile,
                           aValidYplDispatchChannels) must beTrue
    calc.getIsDomesticOnly(supplierId = DMC.YCS,
                           channel = YplMasterChannel.GoLocalVIP,
                           aValidYplDispatchChannels) must beTrue
    calc.getIsDomesticOnly(supplierId = DMC.YCS,
                           channel = YplChannel(Channel.Domestic, Set(Channel.APS), 220),
                           aValidYplDispatchChannels) must beTrue
    calc.getIsDomesticOnly(supplierId = DMC.YCS,
                           channel = YplChannel(Channel.APS, Set(Channel.Domestic), 220),
                           aValidYplDispatchChannels) must beTrue
  }

  "getHasDispatchedStackChannels correctly" should {
    val yplEmptyDispatchChannels = aValidYplDispatchChannels.withMasterChannels(Set.empty)
    val oneChannelInYplDispatchChannel = aValidYplDispatchChannels.withMasterChannels(Set(YplMasterChannel.RTL))
    val yplDispatchChannelWithMultipleChannels =
      aValidYplDispatchChannels.withMasterChannels(Set(YplMasterChannel.RTL, YplMasterChannel.Domestic))

    calc.getHasDispatchedStackChannels(yplDispatchChannelWithMultipleChannels)(aValidYplContext) must beTrue
    calc.getHasDispatchedStackChannels(oneChannelInYplDispatchChannel)(aValidYplContext) must beFalse
    calc.getHasDispatchedStackChannels(yplEmptyDispatchChannels)(aValidYplContext) must beFalse
  }

  private def buildTaxes(taxes: List[Tax]): TaxesWithValues = taxes.map(t => TaxWithValue(t, t.value))

  case class ComparablePrice(cal: Calculation) {

    import com.agoda.papi.ypl.utils.Implicits._

    val netEx: TaxValue = cal.netEx.roundAt(2)
    val margin: TaxValue = cal.margin.roundAt(2)
    val tax: TaxValue = cal.tax.roundAt(2)
    val fee: TaxValue = cal.fee.roundAt(2)
    val pf: TaxValue = cal.processingFee.roundAt(2)
    val netIn: TaxValue = (cal.netEx + cal.tax + cal.fee).roundAt(2)
    val sellIn: TaxValue =
      (cal.netEx + cal.margin + cal.tax + cal.fee + cal.processingFee + cal.taxOverSellEx).roundAt(2)
    val sellEx: TaxValue = (cal.netEx + cal.margin).roundAt(2)
    val taxOnSellEx: TaxValue = cal.taxOverSellEx.roundAt(2)
    val hospitalityTax: TaxValue = cal.hospitalityPriceTax.roundAt(2)
    val surchargeTax: TaxValue = cal.surchargeTax.roundAt(2)
    val marginTax: TaxValue = cal.taxOverMargin.roundAt(2)
  }

  "Calculate Net Inclusive correctly [Umrah]" should {
    val protoMandatoryTax = aValidProtoTax.withChargeOption(ChargeOption.Mandatory)
    val protoTaxAmount = protoMandatoryTax.withIsAmount(true)
    val gdsFee = protoMandatoryTax
      .withTaxId(167)
      .withTaxPrototypeId(68126)
      .withApplyTo("PB")
      .withIsAmount(false)
      .withTaxValue(7.5d)
      .withApplyOver(ApplyTaxOver.SaleEx)

    val supplierId = DMC.YCS
    val paymentModel = PaymentModel.Merchant
    val chargeType = ChargeType.Room
    val currentDate = new DateTime("2025-05-25")
    val baseRoom = aValidRoomEntry.withOriginalRateType(RateType.NetInclusive)
    val hotelTaxInfo = aValidHotelTaxInfo.withTaxType(TaxType.ComprehensiveTaxHotelLevel)
    val taxInfo: TaxInfo = aValidTaxInfo.withHotelTaxInfo(hotelTaxInfo)

    implicit val request: YplRequestBuilder#B#B =
      aValidYplRequest.withCheckIn(currentDate).withCheckout(currentDate.plusDays(2))
    val ctx = YplContext(request)
    val dailyPrice: DailyPrice = aValidDailyPrice.copy(date = currentDate)
    "Test Case 1) NetIn with Umrah Tax" in {
      val reqOcc: YplReqOccByHotelAgePolicy = aValidReqOcc.withAdults(4).withRooms(2)
      val room: YplRoomEntry = baseRoom.withOccupancy(aValidRoomOccupancy.withAdults(2))
      val tax1 = protoTaxAmount.withTaxId(159).withApplyTo("PRPN").withIsAmount(true).withTaxValue(20d)
      val mohuGdsCommissionFeeSettings = new MOHUGdsCommissionFeeSettings(Set[Int] {
                                                                            1892256
                                                                          },
                                                                          Set[Int] {
                                                                            68126
                                                                          })

      "Calculate price WITH excluding GDS fees Only" in {
        val taxWithValue: TaxesWithValues = buildTaxes(List(gdsFee))
        val res = ComparablePrice(
          calc.calculateNetIn(
            paymentModel = paymentModel,
            charge = chargeType,
            netIn = 250d,
            markupPercent = 16.28d,
            commissionPercent = 14.0,
            commissionExcludingAgxOrWholesaleFromHolder = 16.28d,
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.NetInclusive,
            originalRateType = RateType.NetInclusive,
            roomRateType = RateType.NetInclusive,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 230.31
        res.netIn must_== 250.39
        res.sellEx must_== 267.8
        res.pf must_== 0
        res.tax must_== 20.09
        res.taxOnSellEx must_== 0
        res.margin must_== 37.49
        res.sellIn must_== 287.89
      }
      "Calculate price WITH excluding GDS fees and adding the other fees" in {
        val taxWithValue: TaxesWithValues = buildTaxes(List(tax1, gdsFee))
        val res = ComparablePrice(
          calc.calculateNetIn(
            paymentModel = paymentModel,
            charge = chargeType,
            netIn = 250d,
            markupPercent = 16.28d,
            commissionPercent = 14.0d,
            commissionExcludingAgxOrWholesaleFromHolder = 16.28d,
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.NetInclusive,
            originalRateType = RateType.NetInclusive,
            roomRateType = RateType.NetInclusive,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 211.88
        res.margin must_== 34.49
        res.pf must_== 0
        res.netIn must_== 250.36
        res.sellEx must_== 246.38
        res.taxOnSellEx must_== 0
        res.tax must_== 38.48
        res.fee must_== 0
        res.sellIn must_== 284.86
      }

      val taxInfoForNetex = TaxInfo(
        hotelTaxInfo = HotelTaxInfo(taxType = TaxType.SimpleTax, isConfigProcessingFees = false),
        taxes = Map(
          (1, 68126) -> Tax(1,
                            "PB",
                            false,
                            false,
                            false,
                            7.5,
                            ChargeOption.Mandatory,
                            68126,
                            None,
                            Some(ApplyTaxOver.SaleEx)),
          (2, Tax.DEFAULT_PROTOTYPE_ID) -> Tax(2, "PB", false, true, true, 20.0, ChargeOption.Mandatory),
          (3, Tax.DEFAULT_PROTOTYPE_ID) -> Tax(3, "PB", false, false, false, 20.0, ChargeOption.Mandatory),
        ),
      )

      "Calculate price with two taxes against netex" in {
        val taxWithValue: TaxesWithValues = buildTaxes(taxInfoForNetex.taxes.map(_._2).toList)
        val res = ComparablePrice(
          calc.calculateNetEx(
            paymentModel = paymentModel,
            charge = chargeType,
            netEx = 100d,
            markupPercent = 16.279,
            commissionPercent = 14,
            commissionExcludingAgxOrWholesaleFromHolder = 16.279,
            hotelTaxInfo = taxInfoForNetex.hotelTaxInfo,
            rateType = RateType.NetExclusive,
            originalRateType = RateType.NetInclusive,
            roomRateType = RateType.NetInclusive,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            marginInOpt = None,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(ctx, aValidPropertyContext))
        res.netEx must_== 100
        res.sellEx must_== 116.28
        res.tax must_== 34.22
        res.margin must_== 16.28
        res.pf must_== 7.16
        res.netIn must_== 154.22
        res.taxOnSellEx must_== 0.0
      }
    }
  }

  "Calculate Net Inclusive correctly" should {

    val protoMandatoryTax = aValidProtoTax.withChargeOption(ChargeOption.Mandatory)
    val protoMandatoryFee = protoMandatoryTax.withIsFee(true)
    val protoExcludedTax = aValidProtoTax.withChargeOption(ChargeOption.Excluded)
    val protoTaxAmount = protoMandatoryTax.withIsAmount(true)

    // Mock Taxes
    // TC#1
    val occupancyTaxPB = protoMandatoryTax.withTaxId(5).withApplyTo("PB").withIsAmount(false).withTaxValue(10d)
    val bedTaxPGPN = protoTaxAmount.withTaxId(20).withApplyTo("PGPN").withIsAmount(false).withTaxValue(12d)
    val saleTaxPRPB = protoTaxAmount.withTaxId(66).withApplyTo("PRPB").withIsAmount(false).withTaxValue(15d)
    val countryTax = protoTaxAmount.withTaxId(159).withApplyTo("PRPN").withIsAmount(true).withTaxValue(20d)
    val miscFeePGPN = protoTaxAmount.withTaxId(166).withApplyTo("PGPN").withIsAmount(true).withTaxValue(50d)

    // TC#2
    val cityTaxPB = protoExcludedTax.withTaxId(168).withApplyTo("PB").withIsAmount(false).withTaxValue(7d)
    val cityTaxPAPN = protoExcludedTax.withTaxId(153).withApplyTo("PAPN").withIsAmount(true).withTaxValue(10d)
    val touristTaxPRPN = protoExcludedTax.withTaxId(164).withApplyTo("PRPN").withIsAmount(true).withTaxValue(5d)
    // TC#3
    val tourismFeePRPN = protoMandatoryFee.withTaxId(157).withApplyTo("PRPN").withIsAmount(false).withTaxValue(5d)
    val salesTaxPB = protoMandatoryTax.withTaxId(1).withApplyTo("PB").withIsAmount(false).withTaxValue(20d)
    val resortFeePGPB = protoMandatoryTax.withTaxId(68).withApplyTo("PGPB").withIsAmount(true).withTaxValue(12d)

    val supplierId = DMC.YCS
    val paymentModel = PaymentModel.Merchant
    val chargeType = ChargeType.Room
    val currentDate = new DateTime("2025-05-25")
    val baseRoom = aValidRoomEntry.withOriginalRateType(RateType.NetInclusive)
    val hotelTaxInfo = aValidHotelTaxInfo.withTaxType(TaxType.ComprehensiveTaxHotelLevel)
    val taxInfo: TaxInfo = aValidTaxInfo.withHotelTaxInfo(hotelTaxInfo)
    val priceEntry: Option[PriceEntry] = Some(aValidPriceEntry)

    implicit val request: YplRequestBuilder#B#B =
      aValidYplRequest.withCheckIn(currentDate).withCheckout(currentDate.plusDays(2))
    val ctx = YplContext(request)
    val dailyPrice: DailyPrice = aValidDailyPrice.copy(date = currentDate)

    "Test Case 1) NetIn with Tax Mandatory (P+A)" in {
      val reqOcc: YplReqOccByHotelAgePolicy = aValidReqOcc.withAdults(4).withRooms(2)
      val room: YplRoomEntry = baseRoom.withOccupancy(aValidRoomOccupancy.withAdults(2))

      val taxWithValue: TaxesWithValues = buildTaxes(List(occupancyTaxPB, bedTaxPGPN, saleTaxPRPB, countryTax))

      "Calculate price WITH deduct tax amount" in {
        val res = ComparablePrice(
          calc.calculateNetIn(
            paymentModel = paymentModel,
            charge = chargeType,
            netIn = 250d,
            markupPercent = 17.647,
            commissionPercent = 15.0,
            commissionExcludingAgxOrWholesaleFromHolder = 17.647,
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.NetInclusive,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 154.36
        res.margin must_== 27.24
        res.pf must_== 10.08
        res.netIn must_== 250d
      }
    }

    "Test Case 2) NetIn with Tax Exclude (P+A)" in {
      val reqOcc: YplReqOccByHotelAgePolicy = aValidReqOcc.withAdults(4).withRooms(2)
      val room: YplRoomEntry = baseRoom.withOccupancy(aValidRoomOccupancy.withAdults(2))

      val taxWithValue: TaxesWithValues = buildTaxes(List(cityTaxPB, cityTaxPAPN, touristTaxPRPN))

      "Calculate price WITH deduct tax amount" in {
        val res = ComparablePrice(
          calc.calculateNetIn(
            paymentModel = paymentModel,
            charge = chargeType,
            netIn = 250d,
            markupPercent = 17.647,
            commissionPercent = 15.0d,
            commissionExcludingAgxOrWholesaleFromHolder = 17.647,
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.NetInclusive,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 250d
        res.netEx must_== 250
        res.margin must_== 44.12
        res.pf must_== 0d
        res.netIn must_== 250d
      }
    }

    "Test Case 3) NetIn with Tax Exclude (P+A)" in {
      val agePolicy = aValidAgePolicy
        .withIsChildrenStayingFree(true)
        .withMinGuestAge(1)
        .withInfantMaxAge(2)
        .withChildMaxAge(14)
        .withChildrenStayFreeType(ChildrenStayFreeType.Default)
      val reqOcc: YplReqOccByHotelAgePolicy =
        aValidReqOcc.withAdults(6).withChildrenAges(List(1, 7)).withRooms(2).withAgePolicy(agePolicy)
      val room: YplRoomEntry = baseRoom.withOccupancy(aValidRoomOccupancy.withAdults(3))
      val taxWithValue: TaxesWithValues = buildTaxes(List(tourismFeePRPN, salesTaxPB, resortFeePGPB, cityTaxPAPN))

      "Calculate price WITH deduct tax amount" in {
        val res = ComparablePrice(
          calc.calculateNetIn(
            paymentModel = paymentModel,
            charge = chargeType,
            netIn = 250d,
            markupPercent = 17.647,
            commissionPercent = 15.0d,
            commissionExcludingAgxOrWholesaleFromHolder = 17.647,
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.NetInclusive,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 183.2
        res.margin must_== 32.33
        res.pf must_== 8.08
        res.netIn must_== 250d
      }
    }

    "Test Case 4) SellIn with Mandatory Tax(P+A)" in {
      val reqOcc: YplReqOccByHotelAgePolicy = aValidReqOcc.withAdults(4).withRooms(2)
      val room: YplRoomEntry =
        baseRoom.withOriginalRateType(RateType.SellInclusive).withOccupancy(aValidRoomOccupancy.withAdults(2))

      val taxWithValue: TaxesWithValues = buildTaxes(List(bedTaxPGPN, occupancyTaxPB, protoTaxAmount, saleTaxPRPB))
      val commissionPercent = 15.0

      "Calculate price WITH deduct tax amount" in {
        val res = ComparablePrice(
          calc.calculateSellIn(
            paymentModel = paymentModel,
            charge = chargeType,
            value = 500d,
            markupPercent = commissionPercent,
            commissionPercent = commissionPercent,
            commissionExcludingAgxOrWholesaleFromHolder = CommissionUtils.convertToMarkup(commissionPercent),
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.SellInclusive,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 283.81
        res.margin must_== 50.08
        res.pf must_== 18.53
        // This is still incorrect because Tax Per Person should not be set as Percentage. Need Business side to clarify.
        res.sellIn must_== 493.99
      }
    }

    "Test Case 5) SellIn With Tax Excluded (P+A)" in {
      val reqOcc: YplReqOccByHotelAgePolicy = aValidReqOcc.withAdults(4).withRooms(2)
      val room: YplRoomEntry =
        baseRoom.withOriginalRateType(RateType.SellInclusive).withOccupancy(aValidRoomOccupancy.withAdults(2))

      val taxWithValue: TaxesWithValues = buildTaxes(List(cityTaxPB, cityTaxPAPN, touristTaxPRPN))

      "Calculate price WITH deduct tax amount" in {
        val commissionPercent = 15.0
        val res = ComparablePrice(
          calc.calculateSellIn(
            paymentModel = paymentModel,
            charge = chargeType,
            value = 500d,
            markupPercent = commissionPercent,
            commissionPercent = commissionPercent,
            commissionExcludingAgxOrWholesaleFromHolder = CommissionUtils.convertToMarkup(commissionPercent),
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.SellInclusive,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 425d
        res.margin must_== 75.0
        res.pf must_== 0d
        res.sellIn must_== 500d
      }
    }

    "Test Case 6) SellIn With Tax Mandatory + Excluded (P+A)" in {
      val agePolicy = aValidAgePolicy
        .withIsChildrenStayingFree(true)
        .withMinGuestAge(1)
        .withInfantMaxAge(2)
        .withChildMaxAge(14)
        .withChildrenStayFreeType(ChildrenStayFreeType.Default)
      val reqOcc: YplReqOccByHotelAgePolicy =
        aValidReqOcc.withAdults(6).withChildrenAges(List(1, 7)).withRooms(2).withAgePolicy(agePolicy)
      val room: YplRoomEntry =
        baseRoom.withOriginalRateType(RateType.SellInclusive).withOccupancy(aValidRoomOccupancy.withAdults(3))
      val taxWithValue: TaxesWithValues = buildTaxes(List(tourismFeePRPN, salesTaxPB, resortFeePGPB, cityTaxPAPN))
      val commissionPercent = 15.0

      "Calculate price WITH deduct tax amount" in {
        val res = ComparablePrice(
          calc.calculateSellIn(
            paymentModel = paymentModel,
            charge = chargeType,
            value = 500d,
            markupPercent = commissionPercent,
            commissionPercent = commissionPercent,
            commissionExcludingAgxOrWholesaleFromHolder = CommissionUtils.convertToMarkup(commissionPercent),
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.SellInclusive,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 325.72
        res.margin must_== 57.48
        res.pf must_== 14.37
        res.sellIn must_== 500d
      }
    }

    "Test Case 7) Tax Mandatory Over NetIn/SellIn" in {
      val agePolicy = aValidAgePolicy
        .withIsChildrenStayingFree(true)
        .withMinGuestAge(1)
        .withInfantMaxAge(2)
        .withChildMaxAge(14)
        .withChildrenStayFreeType(ChildrenStayFreeType.Default)
      val reqOcc: YplReqOccByHotelAgePolicy =
        aValidReqOcc.withAdults(6).withChildrenAges(List(1, 7)).withRooms(2).withAgePolicy(agePolicy)
      val room: YplRoomEntry =
        baseRoom.withOriginalRateType(RateType.SellInclusive).withOccupancy(aValidRoomOccupancy.withAdults(3))
      val taxWithValue: TaxesWithValues =
        buildTaxes(List(occupancyTaxPB, bedTaxPGPN, miscFeePGPN, countryTax.withTaxValue(100d)))
      val commissionPercent = 15.0

      "Calculate SellIn WITH deduct tax amount" in {
        val res = ComparablePrice(
          calc.calculateSellIn(
            paymentModel = paymentModel,
            charge = chargeType,
            value = 250d,
            markupPercent = commissionPercent,
            commissionPercent = commissionPercent,
            commissionExcludingAgxOrWholesaleFromHolder = commissionPercent,
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.SellInclusive,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 0d
        res.margin must_== 0d
        res.pf must_== 0d
        res.sellIn must_== 275d
      }

      "Calculate NetIn WITH deduct tax amount" in {
        val res = ComparablePrice(
          calc.calculateNetIn(
            paymentModel = paymentModel,
            charge = chargeType,
            netIn = 500d,
            markupPercent = commissionPercent,
            commissionPercent = commissionPercent,
            commissionExcludingAgxOrWholesaleFromHolder = commissionPercent,
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.NetInclusive,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 154.11
        res.margin must_== 23.12
        res.pf must_== 5.09
        res.sellIn must_== 528.2
      }
    }

    "Test Case 8) Tax Excluded Over NetIn/SellIn" in {
      val agePolicy = aValidAgePolicy
        .withIsChildrenStayingFree(true)
        .withMinGuestAge(1)
        .withInfantMaxAge(2)
        .withChildMaxAge(14)
        .withChildrenStayFreeType(ChildrenStayFreeType.Default)
      val reqOcc: YplReqOccByHotelAgePolicy =
        aValidReqOcc.withAdults(6).withChildrenAges(List(1, 7)).withRooms(2).withAgePolicy(agePolicy)
      val room: YplRoomEntry =
        baseRoom.withOriginalRateType(RateType.SellInclusive).withOccupancy(aValidRoomOccupancy.withAdults(3))
      val taxWithValue: TaxesWithValues =
        buildTaxes(List(cityTaxPB, cityTaxPAPN.withTaxValue(55), touristTaxPRPN.withTaxValue(150)))
      val commissionPercent = 15.0

      "Calculate SellIn WITH deduct tax amount" in {
        val res = ComparablePrice(
          calc.calculateSellIn(
            paymentModel = paymentModel,
            charge = chargeType,
            value = 300d,
            markupPercent = commissionPercent,
            commissionPercent = commissionPercent,
            commissionExcludingAgxOrWholesaleFromHolder = CommissionUtils.convertToMarkup(commissionPercent),
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.SellInclusive,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 255d
        res.margin must_== 45d
        res.pf must_== 0d
        res.sellIn must_== 300d
      }

      "Calculate NetIn WITH deduct tax amount" in {
        val res = ComparablePrice(
          calc.calculateNetIn(
            paymentModel = paymentModel,
            charge = chargeType,
            netIn = 300d,
            markupPercent = commissionPercent,
            commissionPercent = commissionPercent,
            commissionExcludingAgxOrWholesaleFromHolder = commissionPercent,
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.NetInclusive,
            originalRateType = room.originalRateType,
            roomRateType = room.rateType,
            occ = room.occEntry,
            processingFeePercent = room.processingFees,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 300d
        res.margin must_== 45d
        res.pf must_== 0d
        res.sellIn must_== 345d
      }
    }

    "Test Case 9) Do not deduct tax amount from surcharge type" in {
      val surchargeType = ChargeType.Surcharge
      val originalRateType = RateType.SellInclusive
      val agePolicy = aValidAgePolicy
        .withIsChildrenStayingFree(true)
        .withMinGuestAge(1)
        .withInfantMaxAge(2)
        .withChildMaxAge(14)
        .withChildrenStayFreeType(ChildrenStayFreeType.Default)
      val reqOcc: YplReqOccByHotelAgePolicy =
        aValidReqOcc.withAdults(6).withChildrenAges(List(1, 7)).withRooms(2).withAgePolicy(agePolicy)
      val room: YplRoomEntry =
        baseRoom.withOriginalRateType(originalRateType).withOccupancy(aValidRoomOccupancy.withAdults(3))
      val taxWithValue: TaxesWithValues = buildTaxes(List(tourismFeePRPN, salesTaxPB, resortFeePGPB, cityTaxPAPN.copy()))

      "Calculate SellIn WITHOUT deduct tax amount" in {
        val res = ComparablePrice(
          calc.calculateSellIn(
            paymentModel = paymentModel,
            charge = surchargeType,
            value = 300d,
            markupPercent = 15.0,
            commissionPercent = 15.0,
            commissionExcludingAgxOrWholesaleFromHolder = CommissionUtils.convertToMarkup(15.0),
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.SellInclusive,
            occ = room.occEntry,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            originalRateType = originalRateType,
            roomRateType = room.rateType,
            processingFeePercent = 0.0,
            supplierId = supplierId,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 204d
        res.margin must_== 36d
        res.pf must_== 9d
        res.netIn must_== 255d
        res.sellIn must_== 300d
      }
    }

    "Test Case 10) applyTaxOnSellEx and applyNoProcessingFee (SupplierOverridingConfiguration)" in {
      val surchargeType = ChargeType.Surcharge
      val originalRateType = RateType.SellInclusive
      val agePolicy = aValidAgePolicy
        .withIsChildrenStayingFree(true)
        .withMinGuestAge(1)
        .withInfantMaxAge(2)
        .withChildMaxAge(14)
        .withChildrenStayFreeType(ChildrenStayFreeType.Default)
      val reqOcc: YplReqOccByHotelAgePolicy =
        aValidReqOcc.withAdults(6).withChildrenAges(List(1, 7)).withRooms(2).withAgePolicy(agePolicy)
      val room: YplRoomEntry = baseRoom
        .withOriginalRateType(originalRateType)
        .withRateType(originalRateType)
        .withOccupancy(aValidRoomOccupancy.withAdults(3))
      val taxWithValue: TaxesWithValues = buildTaxes(List(tourismFeePRPN, salesTaxPB, resortFeePGPB, cityTaxPAPN.copy()))

      "Do not calculate processing fee and calculate tax base on sellex when supplier id = 29014" in {
        val res = ComparablePrice(
          calc.calculateSellIn(
            paymentModel = paymentModel,
            charge = surchargeType,
            value = 300d,
            markupPercent = 15.0,
            commissionPercent = 15.0,
            commissionExcludingAgxOrWholesaleFromHolder = 15.0,
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.SellInclusive,
            occ = room.occEntry,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            originalRateType = originalRateType,
            roomRateType = room.rateType,
            processingFeePercent = 0.0,
            supplierId = 29014,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 204d
        res.margin must_== 36d
        res.pf must_== 0d
        res.netIn must_== 255d
        res.sellIn must_== 291d
        res.sellEx must_== 240d
        res.tax must_== 40.8d // sellex * tax%
      }

    }

    "Test Case 11) Apply zero processing fee and apply tax on sellEx controlled by child hotel level config" should {
      val surchargeType = ChargeType.Surcharge
      val originalRateType = RateType.SellInclusive
      val agePolicy = aValidAgePolicy
        .withIsChildrenStayingFree(true)
        .withMinGuestAge(1)
        .withInfantMaxAge(2)
        .withChildMaxAge(14)
        .withChildrenStayFreeType(ChildrenStayFreeType.Default)
      val reqOcc: YplReqOccByHotelAgePolicy =
        aValidReqOcc.withAdults(6).withChildrenAges(List(1, 7)).withRooms(2).withAgePolicy(agePolicy)
      val room: YplRoomEntry =
        baseRoom.withOriginalRateType(originalRateType).withOccupancy(aValidRoomOccupancy.withAdults(3)).build
      val taxWithValue: TaxesWithValues = buildTaxes(List(tourismFeePRPN, salesTaxPB, resortFeePGPB, cityTaxPAPN.copy()))

      "Calculate price correctly" in {
        val request = aValidYplRequest
          .withCheckIn(currentDate)
          .withCheckout(currentDate.plusDays(2))
          .withSupplierHotelCalculationSettings(supplierHotelCalculationSettings =
            SupplierHotelCalculationSettings(settings = Map(
              27912 -> SupplierHotelCalculationSetting(isApplyTaxOnSellEx = true),
            )))
        val ctx = YplContext(request)

        val res = ComparablePrice(
          calc.calculateSellIn(
            paymentModel = paymentModel,
            charge = surchargeType,
            value = 300d,
            markupPercent = 15.0,
            commissionPercent = 15.0,
            commissionExcludingAgxOrWholesaleFromHolder = CommissionUtils.convertToMarkup(15.0),
            hotelTaxInfo = taxInfo.hotelTaxInfo,
            rateType = RateType.SellInclusive,
            occ = room.occEntry,
            dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
            reqOcc = reqOcc,
            originalRateType = originalRateType,
            roomRateType = room.rateType,
            processingFeePercent = 0.0,
            supplierId = 27912,
            hasChannelDiscount = false,
            hotelId = aValidHotelId,
            chainId = aValidChainId,
            countryId = aValidCountryId,
            subChargeType = SubChargeType.None,
          )(false)(ctx, aValidPropertyContext))
        res.netEx must_== 204d
        res.margin must_== 36d
        res.pf must_== 0d
        res.netIn must_== 255d
        res.sellIn must_== 291d
        res.sellEx must_== 240d
        res.tax must_== 40.8d
      }
    }

    "adjust margin should not deduct amountTax" in {
      val withoutAdjustResult = calc.calculateNetIn(
        paymentModel = PaymentModel.Merchant,
        charge = ChargeType.Room,
        netIn = 100d,
        markupPercent = 25d,
        commissionPercent = 25d,
        commissionExcludingAgxOrWholesaleFromHolder = 25d,
        hotelTaxInfo = aValidHotelTaxInfo,
        rateType = RateType.NetInclusive,
        originalRateType = RateType.NetInclusive,
        roomRateType = RateType.NetInclusive,
        occ = aValidRoomOccupancy,
        processingFeePercent = 0d,
        dailyTaxes = DailyTaxes(List(TaxWithValue(amountTax, 35.0)), isCleanedUpHospitalityTax = true),
        reqOcc = aValidReqOcc,
        supplierId = aValidSupplierId,
        hasChannelDiscount = false,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        sellExForMarginAdjustment = None,
        subChargeType = SubChargeType.None,
      )(true)(aValidYplContext, aValidPropertyContext)
      val withAdjustResult = calc.calculateNetIn(
        paymentModel = PaymentModel.Merchant,
        charge = ChargeType.Room,
        netIn = 100d,
        markupPercent = 25d,
        commissionPercent = 25d,
        commissionExcludingAgxOrWholesaleFromHolder = 25d,
        hotelTaxInfo = aValidHotelTaxInfo,
        rateType = RateType.NetInclusive,
        originalRateType = RateType.NetInclusive,
        roomRateType = RateType.NetInclusive,
        occ = aValidRoomOccupancy,
        processingFeePercent = 0d,
        dailyTaxes = DailyTaxes(List(TaxWithValue(amountTax, 35.0)), isCleanedUpHospitalityTax = true),
        reqOcc = aValidReqOcc,
        supplierId = aValidSupplierId,
        hasChannelDiscount = false,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        sellExForMarginAdjustment = Some(200d),
        subChargeType = SubChargeType.None,
      )(true)(aValidYplContext, aValidPropertyContext)
      withoutAdjustResult.netEx shouldNotEqual withAdjustResult.netEx
    }

    "adjust margin should not deduct hpTaxAmount" in {
      val withoutAdjustResult = calc.calculateNetIn(
        paymentModel = PaymentModel.Merchant,
        charge = ChargeType.Room,
        netIn = 100d,
        markupPercent = 25d,
        commissionPercent = 25d,
        commissionExcludingAgxOrWholesaleFromHolder = throwExceptionAsTheCommissionValueShouldBeUsed,
        hotelTaxInfo = aValidHotelTaxInfo,
        rateType = RateType.SellInclusive,
        originalRateType = RateType.NetInclusive,
        roomRateType = RateType.NetInclusive,
        occ = aValidRoomOccupancy,
        processingFeePercent = 0d,
        dailyTaxes = DailyTaxes(List(TaxWithValue(aValidHPAmountTaxV2, 5.0)), isCleanedUpHospitalityTax = true),
        reqOcc = aValidReqOcc,
        supplierId = aValidSupplierId,
        hasChannelDiscount = false,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        sellExForMarginAdjustment = None,
        subChargeType = SubChargeType.None,
      )(true)(aValidYplContext, aValidPropertyContext)
      val withAdjustResult = calc.calculateNetIn(
        paymentModel = PaymentModel.Merchant,
        charge = ChargeType.Room,
        netIn = 100d,
        markupPercent = 25d,
        commissionPercent = 25d,
        commissionExcludingAgxOrWholesaleFromHolder = throwExceptionAsTheCommissionValueShouldBeUsed,
        hotelTaxInfo = aValidHotelTaxInfo,
        rateType = RateType.SellInclusive,
        originalRateType = RateType.NetInclusive,
        roomRateType = RateType.NetInclusive,
        occ = aValidRoomOccupancy,
        processingFeePercent = 0d,
        dailyTaxes = DailyTaxes(List(TaxWithValue(aValidHPAmountTaxV2, 5.0)), isCleanedUpHospitalityTax = true),
        reqOcc = aValidReqOcc,
        supplierId = aValidSupplierId,
        hasChannelDiscount = false,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        sellExForMarginAdjustment = Some(200d),
        subChargeType = SubChargeType.None,
      )(true)(aValidYplContext, aValidPropertyContext)
      withoutAdjustResult.netEx shouldNotEqual withAdjustResult
    }
  }

  "calculateDailyPrices should return RoomNo and SubChargeType correctly" in {
    implicit val ctx = YplContext(
      YplRequest(
        "",
        new DateTime("2025-05-25"),
        new DateTime("2025-05-25"),
        supplierFeatures = aValidSupplierFeatures,
        whitelabelSetting = aValidwhitelabelSetting,
        fences = aValidYplRequestFences,
      ))
    val taxInfo = aValidTaxInfo
    val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
    val mockPrice = Map(1 -> 100d)
    val roomEntry = createRoomEntry(new DateTime("2025-05-25"), 1, 0, 0, 0, mockPrice)
    val updatedRoomPrice = roomEntry.dailyPrices.map { case (date, price) =>
      val mockPriceEntry = List(
        PriceEntry(
          date,
          ChargeType.Room,
          ChargeOption.Mandatory,
          ApplyType.PRPN.entryName,
          occupancy = 1,
          value = 100,
          roomNo = Some(1),
          subChargeType = SubChargeType.Adult,
          quantity = 1,
        ),
        PriceEntry(
          date,
          ChargeType.Room,
          ChargeOption.Mandatory,
          ApplyType.PRPN.entryName,
          occupancy = 1,
          value = 50,
          roomNo = Some(1),
          subChargeType = SubChargeType.GradeSchool,
          quantity = 1,
        ),
        PriceEntry(
          date,
          ChargeType.Room,
          ChargeOption.Mandatory,
          ApplyType.PRPN.entryName,
          occupancy = 2,
          value = 50,
          roomNo = Some(2),
          subChargeType = SubChargeType.PreSchool,
          quantity = 1,
        ),
        PriceEntry(
          date,
          ChargeType.Room,
          ChargeOption.Mandatory,
          ApplyType.PRPN.entryName,
          occupancy = 2,
          value = 50,
          roomNo = Some(2),
          subChargeType = SubChargeType.PreSchool,
          quantity = 1,
        ),
        PriceEntry(
          date,
          ChargeType.Room,
          ChargeOption.Mandatory,
          ApplyType.PRPN.entryName,
          occupancy = 2,
          value = 50,
          roomNo = Some(2),
          subChargeType = SubChargeType.PreSchool,
          quantity = 1,
        ),
      )
      date -> price.copy(prices = mockPriceEntry)
    }

    val actual = calc.calculateDailyPrices(
      roomEntry.copy(dailyPrices = updatedRoomPrice),
      PaymentModel.Unknown,
      taxInfo,
      reqOcc.agePolicy,
      reqOcc.ignoreRequestedNumberOfRoomsForNha,
      DMC.YCS,
      true,
      hotelId = aValidHotelId,
      chainId = aValidChainId,
      countryId = aValidCountryId,
      cityId = aValidCityId,
    )(ctx, aValidPropertyContext)

    actual.count(_.roomNumber.contains(1)) should_== 2
    actual.count(r => r.roomNumber.contains(1) && r.subChargeType == SubChargeType.Adult) should_== 1
    actual.count(r => r.roomNumber.contains(1) && r.subChargeType == SubChargeType.GradeSchool) should_== 1

    actual.count(_.roomNumber.contains(2)) should_== 3
    actual.count(r => r.roomNumber.contains(2) && r.subChargeType == SubChargeType.PreSchool) should_== 3
  }

  "Calculate Net Exclusive correctly" should {
    val currentDate = new DateTime("2025-05-25")
    val request = aValidYplRequest.withCheckIn(currentDate).withCheckout(currentDate.plusDays(2))
    val ctx = YplContext(request)

    val aValidMarginInOpt: Option[Double] = None
    val aValidPaymentModel: PaymentModel = PaymentModel.Merchant
    val aValidRoomOccupancy = RoomOccupancy(adults = 2, children = 0)
    val aValidChargeType: ChargeType = ChargeType.Room
    val aValidCommissionPercentage = 42.0
    val aValidHotelTaxInfo: HotelTaxInfo = HotelTaxInfo(taxType = TaxType.SimpleTax, isConfigProcessingFees = true)
    val aValidRateType: RateType = RateType.NetInclusive
    val aValidProcessingFeePercentage = 14.0
    val aValidDailyTaxes: DailyTaxes = DailyTaxes(List.empty, isCleanedUpHospitalityTax = true)
    val aValidSupplierId = 29014

    def netExUnderTest(value: Double,
                       markupPercent: Double,
                       rateType: RateType = RateType.NetInclusive,
                       hasChannelDiscount: Boolean = false,
                       chargeType: ChargeType = aValidChargeType,
                       dailyTaxes: DailyTaxes = aValidDailyTaxes,
                       supplierId: SupplierId = aValidSupplierId,
                       hotelId: HotelId = aValidHotelId,
                       commissionExcludingAgxOrWholesaleFromHolder: => Double)(ctx: YplContext) = calc.calculateNetEx(
      paymentModel = aValidPaymentModel,
      charge = chargeType,
      netEx = 100d,
      markupPercent = markupPercent,
      commissionPercent = aValidCommissionPercentage,
      commissionExcludingAgxOrWholesaleFromHolder = commissionExcludingAgxOrWholesaleFromHolder,
      hotelTaxInfo = aValidHotelTaxInfo,
      rateType = rateType,
      originalRateType = aValidRateType,
      roomRateType = aValidRateType,
      occ = aValidRoomOccupancy,
      processingFeePercent = aValidProcessingFeePercentage,
      marginInOpt = aValidMarginInOpt,
      dailyTaxes = dailyTaxes,
      reqOcc = aValidHotel.reqOcc,
      supplierId = supplierId,
      hasChannelDiscount = hasChannelDiscount,
      hotelId = hotelId,
      chainId = aValidChainId,
      countryId = aValidCountryId,
      subChargeType = SubChargeType.None,
    )(ctx, aValidPropertyContext)

    def commissionToMarkup(com: Double) = CommissionConverter.convertCommission(RateType.NetInclusive, com)

    "NetLoadedHotels with wholesale commission as is" should {
      "will use commissionExcludingAgxOrWholesaleFromHolder" in {
        val yplRequest = aValidYplRequest
        val ctx = YplContext(yplRequest)
        val wm = WholesaleMetadata(Some(3.0))

        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(35d),
            commissionExcludingAgxOrWholesaleFromHolder = commissionToMarkup(32d),
          )(ctx))

        // contractedCommission = 35 - 3 = 32
        // composedCommission = 32 + 3 = 35
        // val contractedMarkup = 1/(1-0.32) = 1.470
        // val sellEx = netEx * (1 + contractedMarkup / 100.0) = 100* 1.470 = 147.06
        // val actualComposedMargin = (composedCommission / 100.0) * sellEx = 0.35 * 147.06 = 51.47
        // val actualNetEx = sellEx - actualComposedMargin = 147.06 - 51.47 = 95.59
        res.sellEx must_== 147.06d
        res.netEx must_== 95.59d
        res.margin must_== 51.47d
      }
    }

    "NetLoadedHotels with same commissionExcludingAgxOrWholesaleFromHolder and markUp percent value " in {
      val res = ComparablePrice(
        netExUnderTest(
          value = 100d,
          markupPercent = commissionToMarkup(35d),
          commissionExcludingAgxOrWholesaleFromHolder = commissionToMarkup(35d),
        )(ctx))

      res.sellEx must_== 153.85d
      res.netEx must_== 100d
      res.margin must_== 53.85d
    }

    "NetLoadHotels will use commissionExcludingAgxOrWholesaleFromHolder which will decrease netEx value (1st example)" in {
      val res = ComparablePrice(
        netExUnderTest(
          value = 100d,
          markupPercent = commissionToMarkup(35d),
          commissionExcludingAgxOrWholesaleFromHolder = commissionToMarkup(20d),
        )(ctx))

      res.sellEx must_== 125d
      res.netEx must_== 81.25d
      res.margin must_== 43.75d
    }

    "NetLoadHotels with not use commissionExcludingAgxOrWholesaleFromHolder to decrease netEx value (Edge case no contracted commission)" in {
      val res = ComparablePrice(
        netExUnderTest(
          value = 100d,
          markupPercent = commissionToMarkup(35d),
          commissionExcludingAgxOrWholesaleFromHolder = 0d,
        )(ctx))

      res.sellEx must_== 100d
      res.netEx must_== 65d
      res.margin must_== 35d
    }

    "NetLoadHotels with not use commissionExcludingAgxOrWholesaleFromHolder if we decrease netEx value if there is channel discount" in {
      val res = ComparablePrice(
        netExUnderTest(
          value = 100d,
          markupPercent = commissionToMarkup(35d),
          hasChannelDiscount = true,
          commissionExcludingAgxOrWholesaleFromHolder = throwExceptionAsTheCommissionValueShouldBeUsed,
        )(ctx))

      res.sellEx must_== 153.85d
      res.netEx must_== 100d
      res.margin must_== 53.85d
    }

    "NetLoadHotels will not use commissionExcludingAgxOrWholesaleFromHolder if it is for extra bed price" in {
      val res = ComparablePrice(
        netExUnderTest(
          value = 100d,
          markupPercent = commissionToMarkup(35d),
          chargeType = ChargeType.ExtraBed,
          commissionExcludingAgxOrWholesaleFromHolder = throwExceptionAsTheCommissionValueShouldBeUsed,
        )(ctx))

      res.sellEx must_== 153.85d
      res.netEx must_== 100d
      res.margin must_== 53.85d
    }

    "isApplyTaxOnSellEx" should {
      "use supplierFeatures" should {
        val yplRequest = aValidYplRequest.withApplyTaxOnSellExSettings(None)

        "not apply tax on sellEx" in {
          val ctx = YplContext(yplRequest.build)
          val res = ComparablePrice(
            netExUnderTest(
              value = 100d,
              markupPercent = commissionToMarkup(35d),
              dailyTaxes = aValidDailyTaxWithTaxPrototypeLevelWithValue,
              supplierId = DMC.JTBWL,
              commissionExcludingAgxOrWholesaleFromHolder = commissionToMarkup(35d),
            )(ctx))
          res.sellEx must_== 153.85d
          res.sellIn must_== 179.85d
          res.netEx must_== 100d
          res.netIn must_== 112d
          res.pf must_== 14d
        }

        "apply tax on sellEx" in {
          val yplRequest = aValidYplRequest
            .withSupplierFeature(aValidSupplierFeatures)
            .withApplyTaxOnSellExSettings(Some(aValidApplyTaxOnSellExSettings))
            .build
          val ctx = YplContext(yplRequest)
          val res = ComparablePrice(
            netExUnderTest(
              value = 100d,
              markupPercent = commissionToMarkup(35d),
              dailyTaxes = aValidDailyTaxWithTaxPrototypeLevelWithValue,
              supplierId = DMC.JTBWL,
              commissionExcludingAgxOrWholesaleFromHolder = commissionToMarkup(35d),
            )(ctx))
          res.sellEx must_== 153.85d
          res.sellIn must_== 165.85d
          res.netEx must_== 100d
          res.netIn must_== 112d
          res.pf must_== 0d
        }
      }
    }
  }

  "PriceCalculator - CalculateDailyPrice" should {
    implicit val ctx = YplContext(
      YplRequest(
        "",
        new DateTime("2025-05-25"),
        new DateTime("2025-05-25"),
        supplierFeatures = aValidSupplierFeatures,
        whitelabelSetting = aValidwhitelabelSetting,
        fences = aValidYplRequestFences,
        applyTaxOnSellExSettings = Some(aValidApplyTaxOnSellExSettings),
      ))

    val loadedPrice = 100.0d
    val contractedCommission = 15.0d
    val adjustedComm = 10.0d
    val taxPercent = 10.0d

    val pflexTaxLevel = TaxPrototypeInfo(List(TaxPrototypeLevel(1, 0.0, 100000.0, taxPercent)))
    val tax = Tax(1, "PRPN", false, false, false, 0.0, ChargeOption.Mandatory, 11111, Some(pflexTaxLevel))
    val taxInfo = TaxInfo(aValidHotelTaxInfo, Map((1, 11111) -> tax))
    val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy())
    val roomPrice = Map(2 -> 1000d)

    def getExtraBedPrice(paymentModel: PaymentModel, rateType: RateType, supplierId: Int = DMC.YCS)(implicit
      ctx: YplContext): YplPrice = {
      val roomEntry = createRoomEntry(
        new DateTime("2025-05-25"),
        1,
        2,
        1,
        0,
        roomPrice,
        extraBedPrice = Some(loadedPrice),
        rateType = rateType,
        roomQty = 1,
        extraBedQty = 1,
        taxes = Map((1, 11111) -> 0.0),
        dailyPriceComm = (contractedCommission + adjustedComm),
        agxComm = adjustedComm,
      )
      val allPrice = calc.calculateDailyPrices(
        roomEntry,
        paymentModel = paymentModel,
        taxInfo,
        reqOcc.agePolicy,
        reqOcc.ignoreRequestedNumberOfRoomsForNha,
        supplierId,
        true,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
      )(ctx, aValidPropertyContext)
      allPrice.filter(p => p.isExtraBed).head
    }

    def to2Decs(x: Double): Double = BigDecimal(x).setScale(2, BigDecimal.RoundingMode.HALF_UP).toDouble

    "Calculate extra bed price: Merchant/Sell Inclusive Exp VYG-323" in {
      val calcExtraBedPrice = getExtraBedPrice(PaymentModel.Merchant, RateType.SellInclusive)(ctx)
      to2Decs(calcExtraBedPrice.netExclusive) should_== 68.18
      to2Decs(calcExtraBedPrice.margin) should_== 22.73
      to2Decs(calcExtraBedPrice.tax) should_== 6.82
      to2Decs(calcExtraBedPrice.processingFee) should_== 2.27
      to2Decs(calcExtraBedPrice.netInclusive) should_== 75.00
      to2Decs(calcExtraBedPrice.sellExclusive) should_== 90.91
      to2Decs(calcExtraBedPrice.sellInclusive) should_== 100.00
    }

    "Calculate extra bed price: Merchant/Sell Exclusive Exp VYG-323" in {
      val calcExtraBedPrice = getExtraBedPrice(PaymentModel.Merchant, RateType.SellExclusive)(ctx)
      to2Decs(calcExtraBedPrice.netExclusive) should_== 75.00
      to2Decs(calcExtraBedPrice.margin) should_== 25.00
      to2Decs(calcExtraBedPrice.tax) should_== 7.50
      to2Decs(calcExtraBedPrice.processingFee) should_== 2.50
      to2Decs(calcExtraBedPrice.netInclusive) should_== 82.50
      to2Decs(calcExtraBedPrice.sellExclusive) should_== 100.00
      to2Decs(calcExtraBedPrice.sellInclusive) should_== 110.00
    }

    "Calculate extra bed price: Merchant/Sell Exclusive Exp VYG-323" in {
      val calcExtraBedPrice = getExtraBedPrice(PaymentModel.Merchant, RateType.SellExclusive)(ctx)
      to2Decs(calcExtraBedPrice.netExclusive) should_== 75.00
      to2Decs(calcExtraBedPrice.margin) should_== 25.00
      to2Decs(calcExtraBedPrice.tax) should_== 7.50
      to2Decs(calcExtraBedPrice.processingFee) should_== 2.50
      to2Decs(calcExtraBedPrice.netInclusive) should_== 82.50
      to2Decs(calcExtraBedPrice.sellExclusive) should_== 100.00
      to2Decs(calcExtraBedPrice.sellInclusive) should_== 110.00
    }

    "Calculate extra bed price: Agency(comm = 10%)/Net Inclusive Exp VYG-323" in {
      val calcExtraBedPrice = getExtraBedPrice(PaymentModel.Merchant, RateType.NetInclusive)(ctx)
      to2Decs(calcExtraBedPrice.netExclusive) should_== 90.91
      to2Decs(calcExtraBedPrice.margin) should_== 22.73
      to2Decs(calcExtraBedPrice.tax) should_== 9.09
      to2Decs(calcExtraBedPrice.processingFee) should_== 2.27
      to2Decs(calcExtraBedPrice.netInclusive) should_== 100.00
      to2Decs(calcExtraBedPrice.sellExclusive) should_== 113.64
      to2Decs(calcExtraBedPrice.sellInclusive) should_== 125.0
    }

    "Calculate extra bed price: Merchant Comm/Net Exclusive Exp VYG-323" in {
      val calcExtraBedPrice = getExtraBedPrice(PaymentModel.MerchantCommission, RateType.NetExclusive)(ctx)
      to2Decs(calcExtraBedPrice.netExclusive) should_== 100.00
      to2Decs(calcExtraBedPrice.margin) should_== 25.0
      to2Decs(calcExtraBedPrice.tax) should_== 12.5
      to2Decs(calcExtraBedPrice.processingFee) should_== 0.0
      to2Decs(calcExtraBedPrice.netInclusive) should_== 112.5
      to2Decs(calcExtraBedPrice.sellExclusive) should_== 125.0
      to2Decs(calcExtraBedPrice.sellInclusive) should_== 137.5
    }

    "Calculate price with isApplyRefCommissionOnRefSellEx correctly [1]" in {
      val ctx = YplContext(
        YplRequest(
          searchId = "",
          checkIn = DateTime.parse("2023-04-15"),
          checkOut = DateTime.parse("2023-04-16"),
          supplierFeatures = aValidSupplierFeatures,
          supplierHotelCalculationSettings = SupplierHotelCalculationSettings(settings = Map(
            27912 -> SupplierHotelCalculationSetting(isApplyTaxOnSellEx = true),
          )),
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        ),
      )
      val yplPrice = getExtraBedPrice(PaymentModel.MerchantCommission, RateType.SellExclusive, 27912)(ctx)

      yplPrice.isApplyRefCommissionOnRefSellEx should_== true
    }

    "Calculate price with isApplyRefCommissionOnRefSellEx correctly [2]" in {
      val ctx = YplContext(
        YplRequest(
          searchId = "",
          checkIn = DateTime.parse("2023-04-15"),
          checkOut = DateTime.parse("2023-04-16"),
          supplierFeatures = aValidSupplierFeatures,
          supplierHotelCalculationSettings = SupplierHotelCalculationSettings(settings = Map(
            27912 -> SupplierHotelCalculationSetting(isApplyTaxOnSellEx = false),
          )),
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        ),
      )
      val yplPrice = getExtraBedPrice(PaymentModel.MerchantCommission, RateType.SellExclusive, 27912)(ctx)

      yplPrice.isApplyRefCommissionOnRefSellEx should_== false
    }

    "Calculate price with isApplyRefCommissionOnRefSellEx correctly [3]" in {
      val ctx = YplContext(
        YplRequest(
          searchId = "",
          checkIn = DateTime.parse("2023-04-15"),
          checkOut = DateTime.parse("2023-04-16"),
          supplierFeatures = aValidSupplierFeatures,
          supplierHotelCalculationSettings = SupplierHotelCalculationSettings(settings = Map(
            27912 -> SupplierHotelCalculationSetting(isApplyTaxOnSellEx = true),
          )),
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        ),
      )
      val yplPrice = getExtraBedPrice(PaymentModel.MerchantCommission, RateType.SellExclusive, 27913)(ctx)

      yplPrice.isApplyRefCommissionOnRefSellEx should_== false
    }
  }

  "PriceCalculator - setRespectMaxOccupancyToChildrenStayFreeType" should {
    "should have RespectMaxOcc if maxAdultsOccupancy and maxChildsOccupancy is not empty" in {
      val roomTypeEntry = aValidRoomTypeEntry.withMaxAdultsOccupancy(2).withMaxChildrenOccupancy(2).build
      val yplRoom = aValidRoomEntry.withRoomTypeEntry(roomTypeEntry).build
      val agePolicy = aValidReqOcc

      val res = calc.setRespectMaxOccupancyToChildrenStayFreeType(agePolicy, yplRoom)
      res.agePolicy.childrenStayFreeType should_== ChildrenStayFreeType.RespectMaxOcc
    }

    "should not have maxOcc if maxChildsOccupancy is empty" in {
      val roomTypeEntry = aValidRoomTypeEntry.withMaxAdultsOccupancy(2).build
      val yplRoom = aValidRoomEntry.withRoomTypeEntry(roomTypeEntry).build
      val agePolicy = aValidReqOcc

      val res = calc.setRespectMaxOccupancyToChildrenStayFreeType(agePolicy, yplRoom)
      res.agePolicy.childrenStayFreeType should_!= ChildrenStayFreeType.RespectMaxOcc
    }

    "should not have maxOcc if maxAdultsOccupancy is empty" in {
      val roomTypeEntry = aValidRoomTypeEntry.withMaxChildrenOccupancy(2).build
      val yplRoom = aValidRoomEntry.withRoomTypeEntry(roomTypeEntry).build
      val agePolicy = aValidReqOcc

      val res = calc.setRespectMaxOccupancyToChildrenStayFreeType(agePolicy, yplRoom)
      res.agePolicy.childrenStayFreeType should_!= ChildrenStayFreeType.RespectMaxOcc
    }

    "should not have maxOcc if maxAdultsOccupancy & maxChildrenOccupancy empty" in {
      val roomTypeEntry = aValidRoomTypeEntry
      val yplRoom = aValidRoomEntry.withRoomTypeEntry(roomTypeEntry).build
      val agePolicy = aValidReqOcc

      val res = calc.setRespectMaxOccupancyToChildrenStayFreeType(agePolicy, yplRoom)
      res.agePolicy.childrenStayFreeType should_!= ChildrenStayFreeType.RespectMaxOcc
    }
  }

  "Price calculator (calculate NetEx) - calculate tax based on new charge type taxes: HP, MA, SA" should {

    case class ComparablePrice(cal: Calculation) {

      import com.agoda.papi.ypl.utils.Implicits._

      val netEx: TaxValue = cal.netEx.roundAt(2)
      val margin: TaxValue = cal.margin.roundAt(2)
      val tax: TaxValue = cal.tax.roundAt(2)
      val fee: TaxValue = cal.fee.roundAt(2)
      val pf: TaxValue = cal.processingFee.roundAt(2)
      val netIn: TaxValue = (cal.netEx + cal.tax + cal.fee).roundAt(2)
      val sellIn: TaxValue =
        (cal.netEx + cal.margin + cal.tax + cal.fee + cal.processingFee + cal.taxOverSellEx).roundAt(2)
      val sellEx: TaxValue = (cal.netEx + cal.margin).roundAt(2)
      val taxOnSellIn: TaxValue = cal.taxOverSellEx.roundAt(2)
      val hospitalityTax: TaxValue = cal.hospitalityPriceTax.roundAt(2)
      val surchargeTax: TaxValue = cal.surchargeTax.roundAt(2)
      val marginTax: TaxValue = cal.taxOverMargin.roundAt(2)
    }

    "Calculate Net Exclusive correctly - Exp VYG-323" should {
      val currentDate = new DateTime("2025-05-25")
      val request = aValidYplRequest
        .withCheckIn(currentDate)
        .withCheckout(currentDate.plusDays(2))
        .withBExperiment(US_TAX_V2_EXPERIMENT)
        .withApplyTaxOnSellExSettings(None)
      val ctx = YplContext(request)

      val aValidMarginInOpt: Option[Double] = None
      val aValidPaymentModel: PaymentModel = PaymentModel.Merchant
      val aValidRoomOccupancy = RoomOccupancy(adults = 2, children = 0)
      val roomChargeType: ChargeType = ChargeType.Room
      val surchargeChargeType: ChargeType = ChargeType.Surcharge
      val aValidCommissionPercentage = 10.0
      val simpleTaxTypeHotelTaxInfo: HotelTaxInfo =
        HotelTaxInfo(taxType = TaxType.SimpleTax, isConfigProcessingFees = true)
      val comprehensiveTaxTypeHotelTaxInfo: HotelTaxInfo =
        simpleTaxTypeHotelTaxInfo.copy(taxType = TaxType.ComprehensiveTaxRoomLevel)
      val aValidRateType: RateType = RateType.NetInclusive
      val aValidProcessingFeePercentage = 14.0
      val aValidSupplierId = 29014

      def netExUnderTest(value: Double,
                         markupPercent: Double,
                         agxCommission: YplAGXCommission,
                         rateType: RateType = RateType.NetInclusive,
                         hasChannelDiscount: Boolean = false,
                         chargeType: ChargeType,
                         dt: DailyTaxes,
                         hotelTaxInfo: HotelTaxInfo)(ctx: YplContext) = {
        def commissionExcludingAgxOrWholesaleFromHolder =
          if (rateType == RateType.NetInclusive || rateType == RateType.NetExclusive) markupPercent
          else throwExceptionAsTheCommissionValueShouldBeUsed
        calc.calculateNetEx(
          paymentModel = aValidPaymentModel,
          charge = chargeType,
          netEx = 100d,
          markupPercent = markupPercent,
          commissionPercent = aValidCommissionPercentage,
          commissionExcludingAgxOrWholesaleFromHolder = commissionExcludingAgxOrWholesaleFromHolder,
          hotelTaxInfo = hotelTaxInfo.copy(isConfigProcessingFees = false),
          rateType = rateType,
          originalRateType = aValidRateType,
          roomRateType = aValidRateType,
          occ = aValidRoomOccupancy,
          processingFeePercent = aValidProcessingFeePercentage,
          marginInOpt = aValidMarginInOpt,
          dailyTaxes = dt,
          reqOcc = aValidHotel.reqOcc,
          supplierId = aValidSupplierId,
          hasChannelDiscount = hasChannelDiscount,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = SubChargeType.None,
        )(ctx, aValidPropertyContext)
      }

      def commissionToMarkup(com: Double) = CommissionConverter.convertCommission(RateType.NetInclusive, com)

      "1.1) Net Ex Loaded - with Percent HP, Percent MA tax type - is simple tax type - room charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = roomChargeType,
            dt = percentHPAndPercentMATaxes,
            hotelTaxInfo = simpleTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "1.2) Net Ex Loaded - with HP Percent/Amount tax/Fee and MA Percent Tax/Fee - is simple tax type - room charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = roomChargeType,
            dt = DailyTaxes(allPercentHPTaxAndFee ++ allAmountHPTaxAndFee ++ allPercentMarginTaxAndFee,
                            isCleanedUpHospitalityTax = true),
            hotelTaxInfo = simpleTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 0.63d
        res.fee must_== 2.5d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "1.3) Net Ex Loaded - with HP Percent/Amount tax/Fee and NO MA Percent Tax/Fee - is simple tax type - room charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = roomChargeType,
            dt = DailyTaxes(allPercentHPTaxAndFee ++ allAmountHPTaxAndFee, isCleanedUpHospitalityTax = true),
            hotelTaxInfo = simpleTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "2.1) Net Ex Loaded - with only Amount HP and positive percent MA Tax type - is NOT simple tax type - room charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = roomChargeType,
            dt = percentHPAndPercentMATaxes,
            hotelTaxInfo = comprehensiveTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 1.25d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "2.2) Net Ex Loaded - with HP Percent/Amount tax/Fee and MA Percent Tax/Fee - is NOT simple tax type - room charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = roomChargeType,
            dt = DailyTaxes(allPercentHPTaxAndFee ++ allAmountHPTaxAndFee ++ allPercentMarginTaxAndFee,
                            isCleanedUpHospitalityTax = true),
            hotelTaxInfo = comprehensiveTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 3.81d
        res.fee must_== 2.5d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "2.3) Net Ex Loaded - with HP Percent/Amount tax/Fee and NO MA Percent Tax/Fee - is NOT simple tax type - room charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = roomChargeType,
            dt = DailyTaxes(allPercentHPTaxAndFee ++ allAmountHPTaxAndFee, isCleanedUpHospitalityTax = true),
            hotelTaxInfo = comprehensiveTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "3.1) Net Ex Loaded - with Percent SA, Percent MA tax type - is simple tax type - surcharge charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = surchargeChargeType,
            dt = percentSurchargeAndPercentMATaxes,
            hotelTaxInfo = simpleTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "3.2) Net Ex Loaded - with SA Percent/Amount tax/Fee and MA Percent/Amount Tax/Fee - is simple tax type - surcharge charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = surchargeChargeType,
            dt = DailyTaxes(allPercentSurchargeTaxAndFee ++ allAmountSurchargeTaxAndFee ++ allPercentMarginTaxAndFee,
                            isCleanedUpHospitalityTax = true),
            hotelTaxInfo = simpleTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 0.63d
        res.fee must_== 2.5d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "3.3) Net Ex Loaded - with SA Percent/Amount tax/Fee and NO MA Percent/Amount Tax/Fee - is simple tax type - surcharge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = surchargeChargeType,
            dt =
              DailyTaxes(allPercentSurchargeTaxAndFee ++ allAmountSurchargeTaxAndFee, isCleanedUpHospitalityTax = true),
            hotelTaxInfo = simpleTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "4.1) Net Ex Loaded - with only Amount SA and positive percent MA Tax type - is NOT simple tax type - surcharge charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = surchargeChargeType,
            dt = percentSurchargeAndPercentMATaxes,
            hotelTaxInfo = comprehensiveTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 1.25d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "4.2) Net Ex Loaded - with HP Percent/Amount tax/Fee and MA Percent/Amount Tax/Fee - is NOT simple tax type - room charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = surchargeChargeType,
            dt = DailyTaxes(allPercentSurchargeTaxAndFee ++ allAmountSurchargeTaxAndFee ++ allPercentMarginTaxAndFee,
                            isCleanedUpHospitalityTax = true),
            hotelTaxInfo = comprehensiveTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 3.81d
        res.fee must_== 2.5d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "4.3) Net Ex Loaded - with HP Percent/Amount tax/Fee and no MA Percent/Amount Tax/Fee - is NOT simple tax type - surcharge charge type" in {
        val res = ComparablePrice(
          netExUnderTest(
            value = 100d,
            markupPercent = commissionToMarkup(20d),
            agxCommission = YplAGXCommission.noAgxCommission,
            chargeType = surchargeChargeType,
            dt =
              DailyTaxes(allPercentSurchargeTaxAndFee ++ allAmountSurchargeTaxAndFee, isCleanedUpHospitalityTax = true),
            hotelTaxInfo = comprehensiveTaxTypeHotelTaxInfo,
          )(ctx))

        res.sellEx must_== 125d
        res.netEx must_== 100d
        res.margin must_== 25d
        res.tax must_== 0d
        res.pf must_== 0d
        res.fee must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }
    }
  }

  "Calculate Net Inclusive correctly  - calculate tax based on new charge type taxes: HP, MA, SA" should {

    val supplierId = DMC.YCS
    val merchantPaymentModel = PaymentModel.Merchant
    val merchantComPaymentModel = PaymentModel.MerchantCommission
    val roomChargeType = ChargeType.Room
    val surchargeChargeType = ChargeType.Surcharge
    val currentDate = new DateTime("2025-05-25")
    val baseRoom = aValidRoomEntry.withOriginalRateType(RateType.NetInclusive)
    val simpleTaxHotelInfo = aValidHotelTaxInfo.copy(taxType = TaxType.SimpleTax)

    implicit val request: YplRequestBuilder#B#B =
      aValidYplRequest.withCheckIn(currentDate).withCheckout(currentDate.plusDays(2))
    val ctx = YplContext(request)
    val reqOcc: YplReqOccByHotelAgePolicy = aValidReqOcc.withAdults(4).withRooms(2)
    val room: YplRoomEntry = baseRoom.withOccupancy(aValidRoomOccupancy.withAdults(2))

    def comparePriceFromCalculateNetIn(charge: ChargeType,
                                       originalRateType: RateType = room.originalRateType,
                                       rateType: RateType = RateType.NetInclusive,
                                       dailyTaxes: DailyTaxes,
                                       paymentModel: PaymentModel,
                                       value: Double = 100d,
                                       hotelTaxInfo: HotelTaxInfo = simpleTaxHotelInfo)(implicit
      ctx: YplContext = ctx) = {
      def commissionExcludingAgxOrWholesaleFromHolder =
        if (rateType == RateType.NetExclusive || rateType == RateType.NetInclusive) 25
        else throwExceptionAsTheCommissionValueShouldBeUsed
      ComparablePrice(
        calc.calculateNetIn(
          paymentModel = paymentModel,
          charge = charge,
          netIn = value,
          markupPercent = 25,
          commissionPercent = 20,
          commissionExcludingAgxOrWholesaleFromHolder = commissionExcludingAgxOrWholesaleFromHolder,
          hotelTaxInfo = hotelTaxInfo,
          rateType = rateType,
          originalRateType = originalRateType,
          roomRateType = rateType,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          dailyTaxes = dailyTaxes,
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = SubChargeType.None,
        )(false)(ctx, aValidPropertyContext))
    }

    "Net Inclusive rate loaded type - experiment VYG-323 allocated as B applied" in {

      "1.1) Calculate price WITH HP amount/percent tax - original net rate is Net In - room charge type" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = room.originalRateType,
          rateType = RateType.NetInclusive,
          dailyTaxes = DailyTaxes(allPercentHPTaxAndFee ++ allAmountHPTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
        )
        res.netEx must_== 100d
        res.tax must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.pf must_== 0d
        res.marginTax must_== 0d
      }

      "1.2) Calculate price WITH HP amount/percent tax - original net rate is Net Ex - room charge type" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          dailyTaxes = DailyTaxes(allPercentHPTaxAndFee ++ allAmountHPTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
        )
        res.netEx must_== 100d
        res.tax must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.pf must_== 0d
        res.marginTax must_== 0d
      }

      "1.3) Calculate price WITH HP amount/percent tax - original net rate is Net Ex - HP charge type - merchant com payment model" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          dailyTaxes = DailyTaxes(allPercentHPTaxAndFee ++ allAmountHPTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
        )
        res.netEx must_== 110d
        res.tax must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.pf must_== 0d
        res.marginTax must_== 0d
      }

      "1.4) Calculate price WITH HP amount/percent tax - original net rate is Net Ex - HP charge type - merchant com payment model - room rate type is sell in" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.SellInclusive,
          dailyTaxes = DailyTaxes(allPercentHPTaxAndFee ++ allAmountHPTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
        )
        res.netEx must_== 110d
        res.tax must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.pf must_== 0d
        res.marginTax must_== 0d
      }

      "2.1) Calculate price WITH Surcharge amount/percent tax - original net rate is Net In - surcharge charge type" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = room.originalRateType,
          rateType = RateType.NetInclusive,
          dailyTaxes =
            DailyTaxes(allPercentSurchargeTaxAndFee ++ allAmountSurchargeTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 110d,
        )
        res.netEx must_== 110d
        res.tax must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.pf must_== 0d
        res.marginTax must_== 0d
      }

      "2.2) Calculate price WITH Surcharge amount/percent tax - original net rate is Net Ex - surcharge charge type - merchant payment model" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          dailyTaxes =
            DailyTaxes(allPercentSurchargeTaxAndFee ++ allAmountSurchargeTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 110d,
        )
        res.netEx must_== 110d
        res.tax must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.pf must_== 0d
        res.marginTax must_== 0d
      }

      "2.3 Test Calculate price WITH Surcharge amount/percent tax - original net rate is Net Ex - surcharge charge type - merchant com payment model - room ratetype is not Sell In" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          dailyTaxes =
            DailyTaxes(allPercentSurchargeTaxAndFee ++ allAmountSurchargeTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
        )
        res.netEx must_== 110d
        res.tax must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.pf must_== 0d
        res.marginTax must_== 0d
      }

      "2.4) Calculate price WITH Surcharge amount/percent tax - original net rate is Net Ex - surcharge charge type - merchant com payment model - room ratetype is Sell In" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.SellInclusive,
          dailyTaxes =
            DailyTaxes(allPercentSurchargeTaxAndFee ++ allAmountSurchargeTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
        )
        res.netEx must_== 110d
        res.tax must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.pf must_== 0d
        res.marginTax must_== 0d
      }

      "[Mandatory Tax] 1.1) Calculate price WITH HP amount/percent tax - original net rate is Net In - room charge type" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = room.originalRateType,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
        )
        res.netEx must_== 81.82
        res.tax must_== 18.18
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "[Mandatory Tax] 1.2) Calculate price WITH HP amount/percent tax - original net rate is Net Ex - room charge type" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
        )
        res.netEx must_== 81.82
        res.tax must_== 18.18
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "[Mandatory Tax] 1.3) Calculate price WITH HP amount/percent tax - original net rate is Net Ex - HP charge type - merchant com payment model" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
        )
        res.netEx must_== 90.91
        res.tax must_== 19.09
        res.fee must_== 0
        res.pf must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "[Mandatory Tax] 1.4) Calculate price WITH HP amount/percent tax - original net rate is Net Ex - HP charge type - merchant com payment model - room rate type is sell in" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
        )
        res.netEx must_== 90.91
        res.tax must_== 19.09
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "[Edge Case][Mandatory Tax] 1.2) Calculate price WITH Mandatory amount/percent tax + Tax on Margin - original net rate is Net Ex - room charge type" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allPercentMarginTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
        )
        res.netEx must_== 82.99
        res.margin must_== 20.75
        res.pf must_== 2.62
        res.tax must_== 8.4
        res.fee must_== 2.07
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
      }

      "[Mandatory Tax] 2.1) Calculate price WITH Surcharge amount/percent tax - original net rate is Net In - surcharge charge type" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = room.originalRateType,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 110d,
        )
        res.netEx must_== 100.00
        res.tax must_== 10
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "[Mandatory Tax] 2.2) Calculate price WITH Surcharge amount/percent tax - original net rate is Net Ex - surcharge charge type - merchant payment model" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 110d,
        )
        res.netEx must_== 100.00
        res.tax must_== 10
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "[Mandatory Tax] 2.3) Calculate price WITH Surcharge amount/percent tax - original net rate is Net Ex - surcharge charge type - merchant com payment model - room ratetype is not Sell In" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
        )
        res.netEx must_== 100
        res.tax must_== 10
        res.fee must_== 0
        res.pf must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "[Mandatory Tax] 2.4) Calculate price WITH Surcharge amount/percent tax - original net rate is Net Ex - surcharge charge type - merchant com payment model - room ratetype is Sell In" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
        )
        res.netEx must_== 100
        res.tax must_== 10
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }
    }

    "Net Inclusive rate loaded type - experiment VYG-323 allocated as A applied" in {
      "1.1) Calculate price WITH MA amount/percent tax - original net rate is Net In - room charge type" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = room.originalRateType,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netEx must_== 81.82
        res.tax must_== 18.18
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "1.2) Calculate price WITH MA amount/percent tax - original net rate is Net Ex - room charge type" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netEx must_== 81.82
        res.tax must_== 18.18
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "1.3) Calculate price WITH MA amount/percent tax - original net rate is Net Ex - HP charge type - merchant com payment model" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netEx must_== 90.91
        res.tax must_== 19.09
        res.fee must_== 0
        res.pf must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "1.4) Calculate price WITH MA amount/percent tax - original net rate is Net Ex - HP charge type - merchant com payment model - room rate type is sell in" in {
        val res = comparePriceFromCalculateNetIn(
          charge = roomChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netEx must_== 90.91
        res.tax must_== 19.09
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "2.1) Calculate price WITH Surcharge amount/percent tax - original net rate is Net In - surcharge charge type" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = room.originalRateType,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 110d,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netEx must_== 100.00
        res.tax must_== 10
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "2.2) Calculate price WITH Surcharge amount/percent tax - original net rate is Net Ex - surcharge charge type - merchant payment model" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 110d,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netEx must_== 100.00
        res.tax must_== 10
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "2.3) Calculate price WITH Surcharge amount/percent tax - original net rate is Net Ex - surcharge charge type - merchant com payment model - room ratetype is not Sell In" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.NetInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netEx must_== 100
        res.tax must_== 10
        res.fee must_== 0
        res.pf must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

      "2.4) Calculate price WITH Surcharge amount/percent tax - original net rate is Net Ex - surcharge charge type - merchant com payment model - room ratetype is Sell In" in {
        val res = comparePriceFromCalculateNetIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetExclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantComPaymentModel,
          value = 110d,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netEx must_== 100.00
        res.tax must_== 10
        res.fee must_== 0
        res.hospitalityTax must_== 0
        res.surchargeTax must_== 0
        res.marginTax must_== 0
      }

    }
  }

  "Calculate Sell Inclusive correctly - calculate tax based on new charge type taxes: HP, MA, SA, Mandatory Tax" should {
    val supplierId = DMC.YCS
    val merchantPaymentModel = PaymentModel.Merchant
    val roomChargeType = ChargeType.Room
    val surchargeChargeType = ChargeType.Surcharge
    val currentDate = new DateTime("2025-05-25")
    val baseRoom = aValidRoomEntry.withOriginalRateType(RateType.NetInclusive)
    val simpleTaxHotelInfo = aValidHotelTaxInfo.copy(taxType = TaxType.SimpleTax)

    implicit val request: YplRequestBuilder#B#B =
      aValidYplRequest.withCheckIn(currentDate).withCheckout(currentDate.plusDays(2))
    val ctx = YplContext(request)
    val reqOcc: YplReqOccByHotelAgePolicy = aValidReqOcc.withAdults(4).withRooms(2)
    val room: YplRoomEntry = baseRoom.withOccupancy(aValidRoomOccupancy.withAdults(2))

    def comparePriceFromCalculateSellIn(charge: ChargeType,
                                        originalRateType: RateType = room.originalRateType,
                                        rateType: RateType = RateType.SellInclusive,
                                        dailyTaxes: DailyTaxes,
                                        paymentModel: PaymentModel,
                                        value: Double = 100d,
                                        hotelTaxInfo: HotelTaxInfo = simpleTaxHotelInfo)(implicit
      yplContext: YplContext = ctx) = ComparablePrice(
      calc.calculateSellIn(
        paymentModel = paymentModel,
        charge = charge,
        value = value,
        markupPercent = 20.0d,
        commissionPercent = 20.0d,
        commissionExcludingAgxOrWholesaleFromHolder = 20.0d,
        hotelTaxInfo = hotelTaxInfo,
        rateType = rateType,
        originalRateType = originalRateType,
        roomRateType = rateType,
        occ = room.occEntry,
        processingFeePercent = room.processingFees,
        dailyTaxes = dailyTaxes,
        reqOcc = reqOcc,
        supplierId = supplierId,
        hasChannelDiscount = false,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        subChargeType = SubChargeType.None,
      )(false)(yplContext, aValidPropertyContext))

    "Sell Inclusive rate loaded type - experiment VYG-323 allocated B applied" in {

      "1.1) calculate price with room charge type - original rate type is SellIn " in {
        val res = comparePriceFromCalculateSellIn(
          charge = roomChargeType,
          originalRateType = RateType.SellInclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentHPTaxAndFee ++ allAmountHPTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 147.50d,
        )
        res.netIn must_== 118.0d
        res.netEx must_== 118.0d
        res.tax must_== 0d
        res.margin must_== 29.5d
        res.pf must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "1.2) calculate price with room charge type - original rate type is Net In" in {
        val res = comparePriceFromCalculateSellIn(
          charge = roomChargeType,
          originalRateType = RateType.NetInclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentHPTaxAndFee ++ allAmountHPTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 147.50d,
        )
        res.netEx must_== 118d
        res.tax must_== 0d
        res.margin must_== 29.5d // equal to margin opt
        res.pf must_== 0d
        res.fee must_== 0d
        res.hospitalityTax must_== 0d
        res.surchargeTax must_== 0d
        res.marginTax must_== 0d
      }

      "2.1) calculate price with surcharge charge type - original rate type is SellIn" in {
        val res = comparePriceFromCalculateSellIn(
          charge = surchargeChargeType,
          originalRateType = RateType.SellInclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentSurchargeTaxAndFee ++ allAmountSurchargeTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 137.50d,
        )
        res.netIn must_== 110d
        res.netEx must_== 110d
        res.tax must_== 0d
        res.margin must_== 27.5d
        res.pf must_== 0d
        res.fee must_== 0d
        res.surchargeTax must_== 0d
        res.hospitalityTax must_== 0d
        res.marginTax must_== 0d
      }

      "2.2) calculate price with surcharge charge type - original rate type is NOT SellIn" in {
        val res = comparePriceFromCalculateSellIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetInclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentSurchargeTaxAndFee ++ allAmountSurchargeTaxAndFee, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 147.50d,
        )
        res.netEx must_== 118d
        res.tax must_== 0d
        res.margin must_== 29.5d // equal to margin opt
        res.pf must_== 0d
        res.fee must_== 0d
        res.surchargeTax must_== 0d
        res.hospitalityTax must_== 0d
        res.marginTax must_== 0d
      }

      "3.1) [Mandatory Tax] calculate price with room charge type - original rate type is SellIn" in {
        val res = comparePriceFromCalculateSellIn(
          charge = roomChargeType,
          originalRateType = RateType.SellInclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 147.50d,
        )
        res.netIn must_== 120.0d
        res.netEx must_== 100.0d
        res.tax must_== 20.0d
        res.margin must_== 25.0d
        res.pf must_== 2.5d
        res.fee must_== 0
        res.surchargeTax must_== 0.0d
        res.hospitalityTax must_== 0.0d
        res.marginTax must_== 0.0d
      }

      "3.2) [Mandatory Tax] calculate price with surcharge charge type - original rate type is SellIn" in {
        val res = comparePriceFromCalculateSellIn(
          charge = surchargeChargeType,
          originalRateType = RateType.SellInclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 137.50d,
        )
        res.netIn must_== 110.0d
        res.netEx must_== 100.0d
        res.tax must_== 10.0d
        res.margin must_== 25.0d
        res.pf must_== 2.5d
        res.fee must_== 0
        res.surchargeTax must_== 0.0d
        res.hospitalityTax must_== 0.0d
        res.marginTax must_== 0.0d
      }
    }

    "Sell Inclusive rated loaded type - experiment VYG-323 allocated A applied" in {
      "1.1) calculate price with room charge type - original rate type is SellIn - Mandatory Tax" in {
        val res = comparePriceFromCalculateSellIn(
          charge = roomChargeType,
          originalRateType = RateType.SellInclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 147.50d,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netIn must_== 120.0d
        res.netEx must_== 100.0d
        res.tax must_== 20.0d
        res.margin must_== 25.0d
        res.pf must_== 2.5d
        res.fee must_== 0
        res.surchargeTax must_== 0.0d
        res.hospitalityTax must_== 0.0d
        res.marginTax must_== 0.0d
      }

      "1.2) calculate price with room charge type - original rate type is Net In - Mandatory Tax" in {
        val res = comparePriceFromCalculateSellIn(
          charge = roomChargeType,
          originalRateType = RateType.NetInclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 147.50d,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netEx must_== 100.0d
        res.tax must_== 20.0d
        res.margin must_== 25.0d // equal to margin opt
        res.pf must_== 2.5d
        res.fee must_== 0
        res.surchargeTax must_== 0.0d
        res.hospitalityTax must_== 0.0d
        res.marginTax must_== 0.0d
      }

      "2.1) calculate price with surcharge charge type - original rate type is SellIn - Mandatory Tax" in {
        val res = comparePriceFromCalculateSellIn(
          charge = surchargeChargeType,
          originalRateType = RateType.SellInclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 137.50d,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netIn must_== 110.0d
        res.netEx must_== 100.0d
        res.tax must_== 10.0d
        res.margin must_== 25.0d
        res.pf must_== 2.5d
        res.fee must_== 0
        res.surchargeTax must_== 0.0d
        res.hospitalityTax must_== 0.0d
        res.marginTax must_== 0.0d
      }

      "2.2) calculate price with surcharge charge type - original rate type is NOT SellIn - Mandatory Tax" in {
        val res = comparePriceFromCalculateSellIn(
          charge = surchargeChargeType,
          originalRateType = RateType.NetInclusive,
          rateType = RateType.SellInclusive,
          DailyTaxes(allPercentMandatoryTax ++ allAmountMandatoryTax, isCleanedUpHospitalityTax = true),
          paymentModel = merchantPaymentModel,
          value = 147.50d,
          hotelTaxInfo = simpleTaxHotelInfo,
        )
        res.netEx must_== 107.27d
        res.tax must_== 10.73d
        res.margin must_== 26.82
        res.pf must_== 2.68d
        res.fee must_== 0
        res.surchargeTax must_== 0.0d
        res.hospitalityTax must_== 0.0d
        res.marginTax must_== 0.0d
      }

    }
  }

  "getCommissionForSurchargeCalculation" should {
    val commissionableSurcharge = SurchargeEntry(
      id = 1,
      applyTo = "",
      option = ChargeOption.Mandatory,
      dates = Set.empty,
      isAmount = false,
      isCommissionable = true,
      value = 0L,
    )
    val nonCommissionableSurcharge = commissionableSurcharge.copy(
      isCommissionable = false,
    )

    val ctxB = aValidYplContext.withExperimentContext(
      forcedFromRequestExperimentContext(
        aValidYplRequest.withBExperiment(ABTest.PASS_ZERO_COMMISSION_FOR_NON_COMMISSIONABLE_SURCHARGE),
      ),
    )
    val ctxA = aValidYplContext
    implicit val propertyContext: PropertyContext = PropertyContextImpl(1, 1, 1)

    "return true if VEL-2083 B and surcharge is commissionable" in {
      calc.isSurchargeCommissionable(false, false, commissionableSurcharge, ctxB) should_== (true)
    }

    "return false if VEL-2083 B and surcharge is not commissionable" in {
      calc.isSurchargeCommissionable(false, false, nonCommissionableSurcharge, ctxB) should_== (false)
    }

    "return true if VEL-2083 A" in {
      calc.isSurchargeCommissionable(false, false, commissionableSurcharge, ctxA) should_== (true)
      calc.isSurchargeCommissionable(false, false, nonCommissionableSurcharge, ctxA) should_== (true)
    }

    "return true for pull supplier if room has LT-1349 B in externalData nand surcharge is commissionable" in {
      calc.isSurchargeCommissionable(true, true, commissionableSurcharge, ctxA) should_== (true)
    }

    "return false for pull supplier if room has LT-1349 B in externalData and surcharge is not commissionable" in {
      calc.isSurchargeCommissionable(true, true, nonCommissionableSurcharge, ctxA) should_== (false)
    }

    "return true for pull supplier if room has LT-1349 A in externalDataA" in {
      calc.isSurchargeCommissionable(true, false, commissionableSurcharge, ctxA) should_== (true)
      calc.isSurchargeCommissionable(true, false, nonCommissionableSurcharge, ctxA) should_== (true)
    }

    "return 0 from getCommissionForSurchargeCalculation if not commissionable" in {
      // for push supplier
      calc.getCommissionForSurchargeCalculation(false, false, nonCommissionableSurcharge, ctxB)(10.0) should_== (0)

      // for pull supplier
      calc.getCommissionForSurchargeCalculation(true, true, nonCommissionableSurcharge, ctxA)(10.0) should_== (0)
    }

    "return non 0 from getCommissionForSurchargeCalculation if commissionable" in {
      // for push supplier
      calc.getCommissionForSurchargeCalculation(false, false, commissionableSurcharge, ctxB)(10.0) should_== (10.0)

      // for pull supplier
      calc.getCommissionForSurchargeCalculation(true, true, commissionableSurcharge, ctxA)(10.0) should_== (10.0)
    }
  }

  "getUseRateType" should {
    "return roomRateType if surchargeEntry.isAmount is false" in {
      val surchargeEntry: SurchargeEntry = SurchargeEntry(
        id = 1,
        applyTo = "",
        option = ChargeOption.Mandatory,
        dates = Set.empty,
        isAmount = false,
        isCommissionable = false,
        value = 0L,
      )
      val surchargeRateType: RateType = RateType.NetExclusive
      val roomRateType: RateType = RateType.NetInclusive

      val useRateType = calc.getUseRateType(surchargeEntry, surchargeRateType, roomRateType, false)
      useRateType should_== RateType.NetInclusive
    }

    "return roomRateType if surchargeEntry.isAmount is true and surchargeRateType is Unknown" in {
      val surchargeEntry: SurchargeEntry = SurchargeEntry(
        id = 1,
        applyTo = "",
        option = ChargeOption.Mandatory,
        dates = Set.empty,
        isAmount = true,
        isCommissionable = false,
        value = 0L,
      )
      val surchargeRateType: RateType = RateType.Unknown
      val roomRateType: RateType = RateType.NetInclusive

      val useRateType = calc.getUseRateType(surchargeEntry, surchargeRateType, roomRateType, false)
      useRateType should_== RateType.NetInclusive
    }

    "return surchargeRateType if surchargeEntry.isAmount is true and surchargeRateType is not Unknown" in {
      val surchargeEntry: SurchargeEntry = SurchargeEntry(
        id = 1,
        applyTo = "",
        option = ChargeOption.Mandatory,
        dates = Set.empty,
        isAmount = true,
        isCommissionable = false,
        value = 0L,
      )
      val surchargeRateType: RateType = RateType.NetExclusive
      val roomRateType: RateType = RateType.NetInclusive

      val useRateType = calc.getUseRateType(surchargeEntry, surchargeRateType, roomRateType, false)
      useRateType should_== RateType.NetExclusive
    }

    "return surchargeRateType if surchargeEntry.isAmount is true and surchargeRateType is NetExclusive with (isShowExclusivePriceWithFeeEnabled)=B" in {
      val surchargeEntry: SurchargeEntry = SurchargeEntry(
        id = 1,
        applyTo = "",
        option = ChargeOption.Excluded,
        dates = Set.empty,
        isAmount = true,
        isCommissionable = false,
        value = 0L,
      )
      val surchargeRateType: RateType = RateType.NetExclusive
      val roomRateType: RateType = RateType.NetInclusive

      val useRateType =
        calc.getUseRateType(surchargeEntry, surchargeRateType, roomRateType, isShowExclusivePriceWithFeeEnabled = true)
      useRateType should_== RateType.SellInclusive
    }
  }

  "PriceCalculator - getRoomStatus" should {
    val ctx = aValidYplContext
    val helperChannel = YplMasterChannel.Package
    val requestedChannel = YplMasterChannel.RTL
    val notRequestedChannel = YplMasterChannel.Mobile
    val masterChannels = Set(helperChannel, requestedChannel)
    val helperChannels = Set(helperChannel)
    val yplDispatchChannels =
      aValidYplDispatchChannels.withMasterChannels(masterChannels).withHelperChannels(helperChannels).build

    val roomWithHelperChannel = aValidRoomEntry.withChannel(helperChannel).build
    val roomWithRequestedChannel = aValidRoomEntry.withChannel(requestedChannel).build
    val roomWithNotRequestedChannel = aValidRoomEntry.withChannel(notRequestedChannel).build

    "return Helper status if it is a helper channel" in {
      calc.getRoomStatus(roomWithHelperChannel, yplDispatchChannels)(ctx) should_== (RatePlanStatus.Helper)
    }

    "return Requested status if a requested channel but not a helper channel" in {
      calc.getRoomStatus(roomWithRequestedChannel, yplDispatchChannels)(ctx) should_== (RatePlanStatus.Requested)
    }

    "return None status if it is not a requested channel" in {
      calc.getRoomStatus(roomWithNotRequestedChannel, yplDispatchChannels)(ctx) should_== (RatePlanStatus.None)
    }
  }

  "getAgxAdjustment" should {
    val stayDate = new DateTime(2023, 6, 1, 0, 0, 0, 0)
    val morpCommissionHolder = aValidMORPCommissionHolder
    val morpCandidateRoomParameters = MORPCandidateRoomParameters.default
      .withRateFence(aValidRoomEntry.fences)
      .withApplyNoCCCommission(aValidRoomEntry.applyNoCCCommission)
      .build
    val fencedAGXCommissionHolder = AgxCommissionHolder.default
      .withPayAsYouGoCommission(4.0)
      .withPrepaidCommission(7.0)
      .withFreeTrialCommission(8.0)
      .withIsApplyAgx(true)
      .withIsAgxEligibleRateChannel(true)
      .build
    val fencedAgencyCommissionHolder = FencedAgencyCommissionHolder.default
      .withMORPCandidateRoomParameters(morpCandidateRoomParameters)
      .withAGXCommissionHolder(fencedAGXCommissionHolder)
    val morpCommissionDailyHolder =
      aValidMORPCommissionDailyHolder.withFencedAgxCommissionHolders(Seq(fencedAgencyCommissionHolder)).build
    val agxCommissionHolder = AgxCommissionHolder.default
      .withPayAsYouGoCommission(5.0)
      .withPrepaidCommission(7.0)
      .withFreeTrialCommission(8.0)
      .withIsApplyAgx(true)
      .withIsAgxEligibleRateChannel(true)
      .build
    val commissionHolder = aValidCommissionHolder
      .withMORPCommissionHolder(morpCommissionHolder)
      .withDaily(
        Map(
          stayDate -> aValidCommissionDailyHolder
            .withMORPCommissionDailyHolder(morpCommissionDailyHolder)
            .withAGXCommissionHolder(agxCommissionHolder)
            .withProtobufCommissionHolder(ProtobufCommissionHolder(15.0, 10.0))
            .build),
      )
      .build
    val roomEntry = aValidRoomEntry.withCommissionHolder(commissionHolder).withRateType(RateType.SellExclusive).build

    "return agx commission from commission holder" in {
      calc.getAgxAdjustment(roomEntry, stayDate) should_== (YplAGXCommission(5.0, 7.0, 8.0, false))
    }
  }

  "calculateAdjustedMarginMarkupPercent" should {
    "return fallback markup when sellEx is less than netEx" in {
      val result = calc.calculateAdjustedMarginMarkupPercent(Some(99.0), 100.0).get
      result should_== 5.0
    }

    "return fallback markup when sellEx is equal to netEx" in {
      val result = calc.calculateAdjustedMarginMarkupPercent(Some(100.0), 100.0).get
      result should_== 5.0
    }

    "calculate new markup correctly" in {
      val netEx = 200.0
      val sellEx = 250.0
      val markUp = calc.calculateAdjustedMarginMarkupPercent(Some(sellEx), netEx).get
      val margin = markUp * netEx * TO_PERCENT
      margin should_== 50.0
    }
  }

  "calculateAgpPrice" should {
    "skip calculation for surcharge" in {
      val price = aValidPrice.withChargeType(ChargeType.Surcharge)
      val result = calc.calculateAgpPrice(
        paymentModel = PaymentModel.Unknown,
        commissionPercent = 40d,
        hotelTaxInfo = aValidHotelTaxInfo,
        reqOcc = aValidReqOcc,
        roomPriceInfo = aValidRoom.toRoomPriceInfo,
        supplierId = aValidSupplierId,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
        price = price,
        GrowthProgramCommissionBreakdown.default,
        commissionExcludingWholesaleOrAgx = throwExceptionAsTheCommissionValueShouldBeUsed,
      )(aValidYplContext, aValidPropertyContext)
      result.netExclusive shouldEqual price.netExclusive
    }

    "call calculate price for charge type room" in {
      val price = aValidPrice.withChargeType(ChargeType.Room)
      val result = calc.calculateAgpPrice(
        paymentModel = PaymentModel.Unknown,
        commissionPercent = 40d,
        hotelTaxInfo = aValidHotelTaxInfo,
        reqOcc = aValidReqOcc,
        roomPriceInfo = aValidRoom.toRoomPriceInfo,
        supplierId = aValidSupplierId,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
        price = price,
        GrowthProgramCommissionBreakdown.default,
        commissionExcludingWholesaleOrAgx = throwExceptionAsTheCommissionValueShouldBeUsed,
      )(aValidYplContext, aValidPropertyContext)
      result.netExclusive should_== 750d
    }

    "call calculate price for charge type extra bed" in {
      val price = aValidPrice.withChargeType(ChargeType.Room)
      val result = calc.calculateAgpPrice(
        paymentModel = PaymentModel.Unknown,
        commissionPercent = 40d,
        hotelTaxInfo = aValidHotelTaxInfo,
        reqOcc = aValidReqOcc,
        roomPriceInfo = aValidRoom.toRoomPriceInfo,
        supplierId = aValidSupplierId,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
        price = price,
        GrowthProgramCommissionBreakdown.default,
        commissionExcludingWholesaleOrAgx = throwExceptionAsTheCommissionValueShouldBeUsed,
      )(aValidYplContext, aValidPropertyContext)
      result.netExclusive should_== 750d
    }

    "return processingFeeBreakdown from price before AGP adjustment when CalculateTaxAndFeeWithMarginTaxAndFee is not available" in {
      val commonProcessingFeeBreakdown = CommonProcessingFeeBreakdown(20.0, 0.0, None)
      val price = aValidPrice
        .withChargeType(ChargeType.Room)
        .withTaxBreakdown(aValidTaxBreakdown.withInclude(true).withOption(ChargeOption.Mandatory))
        .withProcessingFeeBreakdown(Some(commonProcessingFeeBreakdown))
      val result = calc.calculateAgpPrice(
        paymentModel = PaymentModel.Unknown,
        commissionPercent = 40d,
        hotelTaxInfo = aValidHotelTaxInfo,
        reqOcc = aValidReqOcc,
        roomPriceInfo = aValidRoom.toRoomPriceInfo,
        supplierId = aValidSupplierId,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
        price = price,
        GrowthProgramCommissionBreakdown.default,
        commissionExcludingWholesaleOrAgx = throwExceptionAsTheCommissionValueShouldBeUsed,
      )(aValidYplContext, aValidPropertyContext)
      result.netExclusive should_== 750d
      result.processingFeeBreakdown.get should_== commonProcessingFeeBreakdown
    }

    "return processingFeeBreakdown calculated based on AGP applied when CalculateTaxAndFeeWithMarginTaxAndFee is available" in {
      val commonProcessingFeeBreakdown = CommonProcessingFeeBreakdown(20.0, 0.0, None)
      val adjustedCommonProcessingFeeBreakdown = CommonProcessingFeeBreakdown(
        0d,
        105d,
        Some(List(TaxAndFeePortionBreakdown(105d, 1, 1, Some(WhomToPayType.Property), "PRPN"))))
      val dailyTaxes = aValidDailyTax
        .withTaxes(
          List(
            aValidTaxWithValue
              .withTax(
                aValidTax
                  .withTaxPrototypeId(1)
                  .withWhomToPay(WhomToPayType.Property)
                  .withValueCalculationMethodType(Some(ValueCalculationMethodType.Amount))
                  .withValueMethod(Some(ValueMethodType.Fixed))
                  .withGeoId(Some(114))
                  .withIsTaxAble(true)
                  .withIsFee(true)
                  .withGeoType(Some(GeoType.Country))
                  .build)
              .withTaxValue(20.0)
              .build))
        .build
      val price = aValidPrice
        .withChargeType(ChargeType.Room)
        .withTaxBreakdown(aValidTaxBreakdown)
        .withProcessingFeeBreakdown(Some(commonProcessingFeeBreakdown))
        .withDailyTaxes(dailyTaxes)
      val result = calc.calculateAgpPrice(
        paymentModel = PaymentModel.Unknown,
        commissionPercent = 40d,
        hotelTaxInfo = aValidHotelTaxInfo,
        reqOcc = aValidReqOcc,
        roomPriceInfo = aValidRoom.toRoomPriceInfo,
        supplierId = aValidSupplierId,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
        price = price,
        GrowthProgramCommissionBreakdown.default,
        commissionExcludingWholesaleOrAgx = throwExceptionAsTheCommissionValueShouldBeUsed,
      )(aValidYplContext.withRequest(
          aValidYplRequest.withFeatureFlags(Set(FeatureFlag.CalculateTaxAndFeeWithMarginTaxAndFee))),
        aValidPropertyContext)
      result.netExclusive should_== 625d
      result.processingFeeBreakdown.get should_== adjustedCommonProcessingFeeBreakdown
    }
  }

  "Calculate tax with child rate correctly" should {

    val protoMandatoryTax = aValidProtoTax.withChargeOption(ChargeOption.Mandatory)

    val percentTax = protoMandatoryTax.withTaxId(5).withApplyTo("PB").withIsAmount(false).withTaxValue(10d)
    val amountTax = protoMandatoryTax.withTaxId(159).withApplyTo("PRPN").withIsAmount(true).withTaxValue(20d)

    val supplierId = DMC.YCS
    val paymentModel = PaymentModel.Merchant
    val chargeType = ChargeType.Room
    val currentDate = new DateTime("2025-05-25")
    val baseRoom = aValidRoomEntry.withOriginalRateType(RateType.NetInclusive)
    val hotelTaxInfo = aValidHotelTaxInfo.withTaxType(TaxType.ComprehensiveTaxHotelLevel)
    val taxInfo: TaxInfo = aValidTaxInfo.withHotelTaxInfo(hotelTaxInfo)

    implicit val request: YplRequestBuilder#B#B =
      aValidYplRequest.withCheckIn(currentDate).withCheckout(currentDate.plusDays(2))
    val ctx = YplContext(request)

    val reqOcc: YplReqOccByHotelAgePolicy = aValidReqOcc.withAdults(4).withRooms(2)
    val room: YplRoomEntry = baseRoom.withOccupancy(aValidRoomOccupancy.withAdults(2))
    val taxWithValue: TaxesWithValues = buildTaxes(List(percentTax, amountTax))

    "Calculate NetIn correctly" in {
      def comparablePriceNetIn(subChargeType: SubChargeType): ComparablePrice = ComparablePrice(
        calc.calculateNetIn(
          paymentModel = paymentModel,
          charge = chargeType,
          netIn = 250d,
          markupPercent = 17.647,
          commissionPercent = 15.0,
          commissionExcludingAgxOrWholesaleFromHolder = 17.647,
          hotelTaxInfo = taxInfo.hotelTaxInfo,
          rateType = RateType.NetInclusive,
          originalRateType = RateType.NetInclusive,
          roomRateType = RateType.NetInclusive,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = subChargeType,
        )(false)(ctx, aValidPropertyContext))

      "Calculate NetIn with Adult rate" in {
        val res = comparablePriceNetIn(SubChargeType.Adult)
        res.netEx must_== 209.09d
        res.tax must_== 40.91d
        res.netIn must_== 250d
      }

      "Calculate NetIn with Child rate" in {
        val res = comparablePriceNetIn(SubChargeType.Child)
        res.netEx must_== 227.27d
        res.tax must_== 22.73d
        res.netIn must_== 250d
      }
    }
    "Calculate NetEx correctly" in {
      def comparablePriceNetEx(subChargeType: SubChargeType): ComparablePrice = ComparablePrice(
        calc.calculateNetEx(
          paymentModel = paymentModel,
          charge = chargeType,
          netEx = 250d,
          markupPercent = 17.647,
          commissionPercent = 15.0,
          commissionExcludingAgxOrWholesaleFromHolder = 17.647,
          hotelTaxInfo = taxInfo.hotelTaxInfo,
          rateType = RateType.NetExclusive,
          originalRateType = RateType.NetExclusive,
          roomRateType = RateType.NetExclusive,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          marginInOpt = None,
          dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = subChargeType,
        )(ctx, aValidPropertyContext))

      "Calculate NetEx with Adult rate" in {
        val res = comparablePriceNetEx(SubChargeType.Adult)
        res.netEx must_== 250d
        res.tax must_== 45d
        res.netIn must_== 295d
      }

      "Calculate NetEx with Child rate" in {
        val res = comparablePriceNetEx(SubChargeType.Child)
        res.netEx must_== 250d
        res.tax must_== 25d
        res.netIn must_== 275d
      }
    }
    "Calculate SellIn correctly" in {
      def comparablePriceSellIn(subChargeType: SubChargeType): ComparablePrice = ComparablePrice(
        calc.calculateSellIn(
          paymentModel = paymentModel,
          charge = chargeType,
          value = 250d,
          // The code in calculateSellIn will treat below variable as commissionPercent, not actual markupPercent
          markupPercent = 15.0,
          commissionPercent = 15.0,
          commissionExcludingAgxOrWholesaleFromHolder = 17.647,
          hotelTaxInfo = taxInfo.hotelTaxInfo,
          rateType = RateType.SellInclusive,
          originalRateType = RateType.SellInclusive,
          roomRateType = RateType.SellInclusive,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = subChargeType,
        )(false)(ctx, aValidPropertyContext))

      "Calculate SaleIn with Adult rate" in {
        val res = comparablePriceSellIn(SubChargeType.Adult)
        res.netEx must_== 177.73d
        res.sellEx must_== 209.09d
        res.tax must_== 37.77d
        res.pf must_== 3.14d
        res.sellIn must_== 250d
        res.taxOnSellEx must_== 0d
      }

      "Calculate SaleIn with Child rate" in {
        val res = comparablePriceSellIn(SubChargeType.Child)
        res.netEx must_== 193.18d
        res.sellEx must_== 227.27d
        res.tax must_== 19.32d
        res.pf must_== 3.41d
        res.sellIn must_== 250d
        res.taxOnSellEx must_== 0d
      }
    }
    "Calculate SellEx correctly" in {
      def comparablePriceSellEx(subChargeType: SubChargeType): ComparablePrice = ComparablePrice(
        calc.calculateSellEx(
          paymentModel = paymentModel,
          charge = chargeType,
          value = 250d,
          markupPercent = 17.647,
          commissionPercent = 15.0,
          commissionExcludingAgxOrWholesaleFromHolder = 17.647,
          hotelTaxInfo = taxInfo.hotelTaxInfo,
          rateType = RateType.SellExclusive,
          originalRateType = RateType.SellExclusive,
          roomRateType = RateType.SellExclusive,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = subChargeType,
        )(ctx, aValidPropertyContext))

      "Calculate SaleEx with Adult rate" in {
        val res = comparablePriceSellEx(SubChargeType.Adult)
        res.netEx must_== 205.88d
        res.margin must_== 44.12d
        res.tax must_== 40.59d
        res.pf must_== 4.41d
        res.sellEx must_== 250d
        res.sellIn must_== 295d
      }

      "Calculate SaleEx with Child rate" in {
        val res = comparablePriceSellEx(SubChargeType.Child)
        res.netEx must_== 205.88d
        res.margin must_== 44.12d
        res.tax must_== 20.59d
        res.pf must_== 4.41d
        res.sellEx must_== 250d
        res.sellIn must_== 275d
      }
    }
  }

  "Calculate fee with amount correctly" should {

    val protoMandatoryTax = aValidProtoTax.withChargeOption(ChargeOption.Mandatory)

    val percentTax = protoMandatoryTax.withTaxId(5).withApplyTo("PB").withIsAmount(false).withTaxValue(10d)
    val amountFee =
      protoMandatoryTax.withTaxId(159).withIsFee(true).withApplyTo("PRPN").withIsAmount(true).withTaxValue(20d)

    val supplierId = DMC.YCS
    val paymentModel = PaymentModel.Merchant
    val chargeType = ChargeType.Room
    val currentDate = new DateTime(2025, 1, 1, 0, 0, 0, 0)
    val baseRoom = aValidRoomEntry.withOriginalRateType(RateType.NetInclusive)
    val hotelTaxInfo = aValidHotelTaxInfo.withTaxType(TaxType.ComprehensiveTaxHotelLevel)
    val taxInfo: TaxInfo = aValidTaxInfo.withHotelTaxInfo(hotelTaxInfo)

    implicit val request: YplRequestBuilder#B#B =
      aValidYplRequest.withCheckIn(currentDate).withCheckout(currentDate.plusDays(2))

    val ctx = YplContext(request)

    val reqOcc: YplReqOccByHotelAgePolicy = aValidReqOcc.withAdults(4).withRooms(2)
    val room: YplRoomEntry = baseRoom.withOccupancy(aValidRoomOccupancy.withAdults(2))
    val taxWithValue: TaxesWithValues = buildTaxes(List(percentTax, amountFee))

    "Calculate NetIn correctly" in {
      def comparablePriceNetIn(subChargeType: SubChargeType): ComparablePrice = ComparablePrice(
        calc.calculateNetIn(
          paymentModel = paymentModel,
          charge = chargeType,
          netIn = 250d,
          markupPercent = 17.647,
          commissionPercent = 15.0,
          commissionExcludingAgxOrWholesaleFromHolder = 17.647,
          hotelTaxInfo = taxInfo.hotelTaxInfo,
          rateType = RateType.NetInclusive,
          originalRateType = RateType.NetInclusive,
          roomRateType = RateType.NetInclusive,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = subChargeType,
        )(false)(ctx, aValidPropertyContext))

      "Calculate NetIn" in {
        val res = comparablePriceNetIn(SubChargeType.Adult)
        res.netEx must_== 209.09d
        res.tax must_== 20.91d
        res.fee must_== 20.00d
        res.netIn must_== 250d
      }
    }
    "Calculate NetEx correctly" in {
      def comparablePriceNetEx(subChargeType: SubChargeType): ComparablePrice = ComparablePrice(
        calc.calculateNetEx(
          paymentModel = paymentModel,
          charge = chargeType,
          netEx = 250d,
          markupPercent = 17.647,
          commissionPercent = 15.0,
          commissionExcludingAgxOrWholesaleFromHolder = 17.647,
          hotelTaxInfo = taxInfo.hotelTaxInfo,
          rateType = RateType.NetExclusive,
          originalRateType = RateType.NetExclusive,
          roomRateType = RateType.NetExclusive,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          marginInOpt = None,
          dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = subChargeType,
        )(ctx, aValidPropertyContext))

      "Calculate NetEx" in {
        val res = comparablePriceNetEx(SubChargeType.Adult)
        res.netEx must_== 250d
        res.tax must_== 25d
        res.fee must_== 20d
        res.netIn must_== 295d
      }
    }
    "Calculate SellIn correctly" in {
      def comparablePriceSellIn(subChargeType: SubChargeType): ComparablePrice = ComparablePrice(
        calc.calculateSellIn(
          paymentModel = paymentModel,
          charge = chargeType,
          value = 250d,
          // The code in calculateSellIn will treat below variable as commissionPercent, not actual markupPercent
          markupPercent = 15.0,
          commissionPercent = 15.0,
          commissionExcludingAgxOrWholesaleFromHolder = 17.647,
          hotelTaxInfo = taxInfo.hotelTaxInfo,
          rateType = RateType.SellInclusive,
          originalRateType = RateType.SellInclusive,
          roomRateType = RateType.SellInclusive,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = subChargeType,
        )(false)(ctx, aValidPropertyContext))

      "Calculate SaleIn" in {
        val res = comparablePriceSellIn(SubChargeType.Adult)
        res.netEx must_== 177.73d
        res.sellEx must_== 209.09d
        res.tax must_== 17.77d
        res.fee must_== 20d
        res.pf must_== 3.14d
        res.sellIn must_== 250d
        res.taxOnSellEx must_== 0d
      }
    }
    "Calculate SellEx correctly" in {
      def comparablePriceSellEx(subChargeType: SubChargeType): ComparablePrice = ComparablePrice(
        calc.calculateSellEx(
          paymentModel = paymentModel,
          charge = chargeType,
          value = 250d,
          markupPercent = 17.647,
          commissionPercent = 15.0,
          commissionExcludingAgxOrWholesaleFromHolder = 17.647,
          hotelTaxInfo = taxInfo.hotelTaxInfo,
          rateType = RateType.SellExclusive,
          originalRateType = RateType.SellExclusive,
          roomRateType = RateType.SellExclusive,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          dailyTaxes = DailyTaxes(taxWithValue, isCleanedUpHospitalityTax = true),
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = subChargeType,
        )(ctx, aValidPropertyContext))

      "Calculate SaleEx" in {
        val res = comparablePriceSellEx(SubChargeType.Adult)
        res.netEx must_== 205.88d
        res.margin must_== 44.12d
        res.tax must_== 20.59d
        res.fee must_== 20.0d
        res.pf must_== 4.41d
        res.sellEx must_== 250d
        res.sellIn must_== 295d
      }
    }

    "Calculate SellEx correctly with hospitality tax (old)" in {
      val hospitalityTaxesWithValue =
        taxWithValue.map(tax => tax.copy(tax = tax.tax.copy(option = ChargeOption.HospitalityPrice)))
      def comparablePriceSellEx(subChargeType: SubChargeType): ComparablePrice = ComparablePrice(
        calc.calculateSellEx(
          paymentModel = paymentModel,
          charge = chargeType,
          value = 250d,
          markupPercent = 17.647,
          commissionPercent = 15.0,
          commissionExcludingAgxOrWholesaleFromHolder = 17.647,
          hotelTaxInfo = taxInfo.hotelTaxInfo,
          rateType = RateType.SellExclusive,
          originalRateType = RateType.SellExclusive,
          roomRateType = RateType.SellExclusive,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          dailyTaxes = DailyTaxes(hospitalityTaxesWithValue, isCleanedUpHospitalityTax = false),
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = subChargeType,
        )(ctx, aValidPropertyContext))

      "Calculate SaleEx" in {
        val res = comparablePriceSellEx(SubChargeType.Adult)
        res.netEx must_== 205.88d
        res.margin must_== 48.53d
        res.tax must_== 20.59d
        res.fee must_== 0d
        res.pf must_== 0d
        res.sellEx must_== 254.41d
        res.sellIn must_== 275d
      }
    }

    "Calculate SellEx correctly with hospitality tax (new)" in {
      val hospitalityTaxesWithValue =
        taxWithValue.map(tax => tax.copy(tax = tax.tax.copy(option = ChargeOption.HospitalityPrice)))
      def comparablePriceSellEx(subChargeType: SubChargeType): ComparablePrice = ComparablePrice(
        calc.calculateSellEx(
          paymentModel = paymentModel,
          charge = chargeType,
          value = 250d,
          markupPercent = 17.647,
          commissionPercent = 15.0,
          commissionExcludingAgxOrWholesaleFromHolder = 17.647,
          hotelTaxInfo = taxInfo.hotelTaxInfo,
          rateType = RateType.SellExclusive,
          originalRateType = RateType.SellExclusive,
          roomRateType = RateType.SellExclusive,
          occ = room.occEntry,
          processingFeePercent = room.processingFees,
          dailyTaxes = DailyTaxes(hospitalityTaxesWithValue, isCleanedUpHospitalityTax = true),
          reqOcc = reqOcc,
          supplierId = supplierId,
          hasChannelDiscount = false,
          hotelId = aValidHotelId,
          chainId = aValidChainId,
          countryId = aValidCountryId,
          subChargeType = subChargeType,
        )(ctx, aValidPropertyContext))

      "Calculate SaleEx" in {
        val res = comparablePriceSellEx(SubChargeType.Adult)
        res.netEx must_== 205.88d
        res.margin must_== 44.12d
        res.tax must_== 40.59d
        res.fee must_== 0d
        res.pf must_== 0d
        res.sellEx must_== 250d
        res.sellIn must_== 290.59d
      }
    }
  }

  "Calculate Surcharge correctly" should {
    def calculateSurcharge(value: Double = 20.0,
                           option: ChargeOption = ChargeOption.Mandatory,
                           wlShowExclusivePriceWithFeeEnabled: Boolean = false,
                           isXmlPartner: Boolean = false,
                           addFeesInExclusivePrice: Boolean = false): Option[YplPrice] = {
      val surchargeEntry = aValidSurchargeEntry.copy(isAmount = true, value = value, option = option)
      val context =
        if (addFeesInExclusivePrice) {
          aValidYplContext
            .withExperimentContext(
              forcedFromRequestExperimentContext(
                aValidYplRequest.withBExperiment(ABTest.SHOW_EXCLUSIVE_WITH_FEE_TO_US_DESTINATION),
              ),
            )
            .build
        } else {
          aValidYplContext.build
        }
      val contextWithRegulation = context.copy(
        request = context.request.copy(
          regulationFeatureEnabledSetting =
            YplRegulationFeatureEnabledSetting.default.copy(isShowExclusivePriceWithFeeEnabled =
              wlShowExclusivePriceWithFeeEnabled),
          isXmlPartner = isXmlPartner,
        ),
        addFeesInExclusivePrice = addFeesInExclusivePrice,
      )
      calc.calculateSurcharge(
        paymentModel = PaymentModel.Merchant,
        dailyPrice = aValidDailyPrice.withTaxes(Map((1, 1) -> 15d)),
        surchargeRateType = RateType.NetExclusive,
        surchargeEntry = surchargeEntry,
        roomPrices = List(aValidPrice),
        taxInfo = aValidTaxInfo.withTaxes(Map((1, 1) -> aValidTax)),
        room = aValidRoomEntry,
        reqOcc = aValidReqOcc,
        isPull = false,
        supplierId = aValidSupplierId,
        supplierContractedCommission = Some(10.0),
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        fixMarriottSurchargeExp = false,
      )(true)(contextWithRegulation, PropertyContextImpl(1, 1, 1))
    }

    "Calculate Surcharge with positive amount" in {
      val result = calculateSurcharge()
      result.map(_.value) shouldEqual (Some(20.0))
    }

    "Calculate Surcharge with zero amount" in {
      val result = calculateSurcharge(0.0)
      result shouldEqual (None)
    }

    "Calculate Surcharge with negative amount" in {
      val result = calculateSurcharge(-20.0)
      result shouldEqual (None)
    }

    "Calculate Surcharge - Excluded" >> {
      Fragment.foreach(
        List(
          (false, false, Some(20), Some(20), Some(0), Some(0), Some(0)),
          (true, false, Some(20), Some(17.391304347826086), Some(2.608695652173913), Some(0), Some(0)),
          (false, true, Some(20), Some(17.391304347826086), Some(2.608695652173913), Some(0), Some(0)),
        )) {
        case (wlShowExclusivePriceWithFeeEnabled,
              isXmlPartner,
              expectedSellIn,
              expectedNetEx,
              expectedTax,
              expectedFee,
              expectedMargin) =>
          s"With isXmlPartner=$isXmlPartner, wlShowExclusivePriceWithFeeEnabled=$wlShowExclusivePriceWithFeeEnabled" ! {
            val result = calculateSurcharge(20, ChargeOption.Excluded, wlShowExclusivePriceWithFeeEnabled, isXmlPartner)
            result.map(_.sellInclusive) must_== expectedSellIn
            result.map(_.netExclusive) must_== expectedNetEx
            result.map(_.tax) must_== expectedTax
            result.map(_.fee) must_== expectedFee
            result.map(_.margin) must_== expectedMargin
          }
      }
    }

    "With LT-1446 centralized logic Calculate Surcharge - Excluded" >> {
      Fragment.foreach(
        List(
          (false, false, Some(20), Some(20), Some(0), Some(0), Some(0)),
          (true, false, Some(20), Some(17.391304347826086), Some(2.608695652173913), Some(0), Some(0)),
          (false, true, Some(20), Some(17.391304347826086), Some(2.608695652173913), Some(0), Some(0)),
        )) {
        case (addFeesInExclusivePrice,
              isXmlPartner,
              expectedSellIn,
              expectedNetEx,
              expectedTax,
              expectedFee,
              expectedMargin) =>
          s"With isXmlPartner=$isXmlPartner, addFeesInExclusivePrice=$addFeesInExclusivePrice" ! {
            val result = calculateSurcharge(20, ChargeOption.Excluded, false, isXmlPartner, addFeesInExclusivePrice)
            result.map(_.sellInclusive) must_== expectedSellIn
            result.map(_.netExclusive) must_== expectedNetEx
            result.map(_.tax) must_== expectedTax
            result.map(_.fee) must_== expectedFee
            result.map(_.margin) must_== expectedMargin
          }
      }
    }
  }

  "checkApplyTaxOnSellEx" should {
    "return false if applyTaxOnSellExSettings and supplierHotelCalculationSettings are empty" in {
      implicit val ctx: YplContext = YplContext(aValidYplRequest.copy(applyTaxOnSellExSettings = None))
      val supplierId = DMC.JTBWL
      val hotelId = 0
      val chainId = 0
      val countryId = 0
      val paymentModel = 1
      val originalRateType = RateType.NetInclusive

      val result = calc.checkApplyTaxOnSellEx(supplierId, hotelId, chainId, countryId, paymentModel, originalRateType)
      result must_== false
    }

    "return true if applyTaxOnSellExSettings or supplierHotelCalculationSettings is true" in {
      implicit val ctx: YplContext = YplContext(aValidYplRequest)
      val supplierId = DMC.JTBWL
      val hotelId = 0
      val chainId = 0
      val countryId = 0
      val paymentModel = 1
      val originalRateType = RateType.NetInclusive

      val result = calc.checkApplyTaxOnSellEx(supplierId, hotelId, chainId, countryId, paymentModel, originalRateType)
      result must_== true
    }

    "return false if both applyTaxOnSellExSettings and supplierHotelCalculationSettings are false" in {
      implicit val ctx: YplContext = YplContext(aValidYplRequest)
      val supplierId = DMC.SynxisCCRS
      val hotelId = 0
      val chainId = 0
      val countryId = 0
      val paymentModel = 1
      val originalRateType = RateType.NetInclusive

      val result = calc.checkApplyTaxOnSellEx(supplierId, hotelId, chainId, countryId, paymentModel, originalRateType)
      result must_== false
    }

    "update dailyTaxes correctly" in {
      implicit val ctx: YplContext = YplContext(aValidYplRequest)
      val dailyTaxes = DailyTaxes(
        taxes = List(
          TaxWithValue(aValidTax.copy(applyBreakdownType = Some(TaxApplyBreakdownType.NetEx), applyOver = None),
                       aValidTax.value)),
        isCleanedUpHospitalityTax = true,
      )
      val applyTaxOverHelper = ApplyTaxOverHelper(
        Set(),
        None,
        com.agoda.finance.tax.models
          .SupplierHotelCalculationSettings(Map.empty[Int, com.agoda.finance.tax.models.SupplierHotelCalculationSetting]))

      val result = calc.updateDailyTaxes(
        dailyTaxes = dailyTaxes,
        applyTaxOverHelper = applyTaxOverHelper,
        supplierId = 0,
        hotelId = 0,
        chainId = 0,
        countryId = 0,
        paymentModelId = 0,
        rateTypeId = 0,
        applicableDate = "2024-10-01",
      )
      result.taxes.head.tax.applyOver must beSome(ApplyTaxOver.NetEx)
    }

    "update dailyTaxes correctly when chainId = 13 and rateloadType = NetEx and isDirectConnect" in {
      implicit val ctx: YplContext = YplContext(aValidYplRequest)
      val dailyTaxes = DailyTaxes(
        taxes = List(
          TaxWithValue(aValidTax.copy(applyBreakdownType = Some(TaxApplyBreakdownType.NetEx), applyOver = None),
                       aValidTax.value),
          TaxWithValue(aValidTax.copy(id = TAX_ON_COMM, applyOver = None), aValidTax.value),
        ),
        isCleanedUpHospitalityTax = true,
      )
      val applyTaxOverHelper = ApplyTaxOverHelper(
        Set(),
        None,
        com.agoda.finance.tax.models
          .SupplierHotelCalculationSettings(Map.empty[Int, com.agoda.finance.tax.models.SupplierHotelCalculationSetting]))

      val result = calc.updateDailyTaxes(
        dailyTaxes = dailyTaxes,
        applyTaxOverHelper = applyTaxOverHelper,
        supplierId = DIRECT_CONNECT_DMC,
        hotelId = aValidHotelId,
        chainId = HILTON_CHAIN_ID,
        countryId = COUNTRY_ID_CANADA,
        paymentModelId = 0,
        rateTypeId = RateType.NetInclusive.value,
        applicableDate = "2024-10-01",
      )
      result.taxes.length must_== 1
      result.taxes.head.tax.applyOver must beSome(ApplyTaxOver.NetEx)
    }

    "update dailyTaxes correctly when chainId = 13 and rateloadType = NetIn and isDirectConnect" in {
      implicit val ctx: YplContext = YplContext(aValidYplRequest)
      val dailyTaxes = DailyTaxes(
        taxes = List(
          TaxWithValue(aValidTax.copy(applyBreakdownType = Some(TaxApplyBreakdownType.NetEx), applyOver = None),
                       aValidTax.value),
          TaxWithValue(aValidTax.copy(id = TAX_ON_COMM, applyOver = None), aValidTax.value),
        ),
        isCleanedUpHospitalityTax = true,
      )
      val applyTaxOverHelper = ApplyTaxOverHelper(
        Set(),
        None,
        com.agoda.finance.tax.models
          .SupplierHotelCalculationSettings(Map.empty[Int, com.agoda.finance.tax.models.SupplierHotelCalculationSetting]))

      val result = calc.updateDailyTaxes(
        dailyTaxes = dailyTaxes,
        applyTaxOverHelper = applyTaxOverHelper,
        supplierId = DIRECT_CONNECT_DMC,
        hotelId = aValidHotelId,
        chainId = HILTON_CHAIN_ID,
        countryId = COUNTRY_ID_CANADA,
        paymentModelId = 0,
        rateTypeId = RateType.NetExclusive.value,
        applicableDate = "2024-10-01",
      )
      result.taxes.length must_== 1
      result.taxes.head.tax.applyOver must beSome(ApplyTaxOver.NetEx)
    }

    "update dailyTaxes correctly when chainId = 13 and rateloadType = SellEx and isDirectConnect" in {
      implicit val ctx: YplContext = YplContext(aValidYplRequest)
      val dailyTaxes = DailyTaxes(
        taxes = List(
          TaxWithValue(aValidTax.copy(applyBreakdownType = Some(TaxApplyBreakdownType.NetEx), applyOver = None),
                       aValidTax.value),
          TaxWithValue(aValidTax.copy(id = TAX_ON_COMM, applyOver = None), aValidTax.value),
        ),
        isCleanedUpHospitalityTax = true,
      )
      val applyTaxOverHelper = ApplyTaxOverHelper(
        Set(),
        None,
        com.agoda.finance.tax.models
          .SupplierHotelCalculationSettings(Map.empty[Int, com.agoda.finance.tax.models.SupplierHotelCalculationSetting]))

      val result = calc.updateDailyTaxes(
        dailyTaxes = dailyTaxes,
        applyTaxOverHelper = applyTaxOverHelper,
        supplierId = DIRECT_CONNECT_DMC,
        hotelId = aValidHotelId,
        chainId = HILTON_CHAIN_ID,
        countryId = COUNTRY_ID_CANADA,
        paymentModelId = 0,
        rateTypeId = RateType.SellExclusive.value,
        applicableDate = "2024-10-01",
      )
      result.taxes.length must_== 2
      result.taxes.head.tax.applyOver must beSome(ApplyTaxOver.NetEx)
    }

    "update dailyTaxes correctly on when storefront id = 89 and whitelabel id = 1 " in {
      implicit val ctx: YplContext = YplContext(aValidYplRequest.withStoreFrontId(89).withWhiteLabelId(1))
      val dailyTaxes = DailyTaxes(
        taxes = List(
          TaxWithValue(aValidTax.copy(applyBreakdownType = Some(TaxApplyBreakdownType.NetEx), applyOver = None),
                       aValidTax.value),
          TaxWithValue(aValidTax.copy(id = BC_ACCOM_TAX_PST, applyOver = None), aValidTax.value),
          TaxWithValue(aValidTax.copy(id = BC_ACCOM_TAX_MRDT, applyOver = None), aValidTax.value),
          TaxWithValue(aValidTax.copy(id = BC_ACCOM_TAX_MAJOR_MRDT, applyOver = None), aValidTax.value),
        ),
        isCleanedUpHospitalityTax = true,
      )
      val applyTaxOverHelper = ApplyTaxOverHelper(
        Set(),
        None,
        com.agoda.finance.tax.models
          .SupplierHotelCalculationSettings(Map.empty[Int, com.agoda.finance.tax.models.SupplierHotelCalculationSetting]))

      val result = calc.updateDailyTaxes(
        dailyTaxes = dailyTaxes,
        applyTaxOverHelper = applyTaxOverHelper,
        supplierId = 0,
        hotelId = 0,
        chainId = 0,
        countryId = 0,
        paymentModelId = 0,
        rateTypeId = 0,
        applicableDate = "2024-10-01",
      )
      result.taxes.length must_== 4
      result.taxes.head.tax.applyOver must beSome(ApplyTaxOver.NetEx)
    }
    "update dailyTaxes correctly when storefront id = 89 and whitelabel id != 1 " in {
      implicit val ctx: YplContext = YplContext(aValidYplRequest.withStoreFrontId(89).withWhiteLabelId(2))
      val dailyTaxes = DailyTaxes(
        taxes = List(
          TaxWithValue(aValidTax.copy(applyBreakdownType = Some(TaxApplyBreakdownType.NetEx), applyOver = None),
                       aValidTax.value),
          TaxWithValue(aValidTax.copy(id = BC_ACCOM_TAX_PST, applyOver = None), aValidTax.value),
          TaxWithValue(aValidTax.copy(id = BC_ACCOM_TAX_MRDT, applyOver = None), aValidTax.value),
          TaxWithValue(aValidTax.copy(id = BC_ACCOM_TAX_MAJOR_MRDT, applyOver = None), aValidTax.value),
        ),
        isCleanedUpHospitalityTax = true,
      )
      val applyTaxOverHelper = ApplyTaxOverHelper(
        Set(),
        None,
        com.agoda.finance.tax.models
          .SupplierHotelCalculationSettings(Map.empty[Int, com.agoda.finance.tax.models.SupplierHotelCalculationSetting]))

      val result = calc.updateDailyTaxes(
        dailyTaxes = dailyTaxes,
        applyTaxOverHelper = applyTaxOverHelper,
        supplierId = 0,
        hotelId = 0,
        chainId = 0,
        countryId = 0,
        paymentModelId = 0,
        rateTypeId = 0,
        applicableDate = "2024-10-01",
      )
      result.taxes.length must_== 1
      result.taxes.head.tax.applyOver must beSome(ApplyTaxOver.NetEx)
    }
  }

  "getPRQuantityForSurcharge" should {
    val reqOcc = aValidReqOcc.withRooms(2).withAdults(5)

    val occuUnit1 = aValidOccupancyUnit.withRoomNo(1).withQty(2)
    val occuUnit2 = aValidOccupancyUnit.withRoomNo(2).withQty(3)
    val occupancyBreakdown = OccupancyBreakdown(List(occuUnit1, occuUnit2), 5, MaxAllowedFreeChildAgeRange.empty, false)

    val roomEntryWithOccBreakdown = aValidRoomEntry.withOccFromProto(3).withOccupancyBreakdown(Some(occupancyBreakdown))
    val roomEntry = aValidRoomEntry.withOccFromProto(3)
    val occInfo = aValidOccInfo.withAdults(5).withRooms(2)

    "with occupancyBreakdown" should {
      "for A variant return 2 " in {
        val qty = calc.getPRQuantityForSurcharge(roomEntryWithOccBreakdown, reqOcc, 3, fixMarriottSurchargeExp = false)
        qty should_== (2)
      }

      "for B variant return 1" in {
        val qty = calc.getPRQuantityForSurcharge(roomEntryWithOccBreakdown, reqOcc, 3, fixMarriottSurchargeExp = true)
        qty should_== (1)
      }

      "for B variant return 0 if occupancy from room is not equal to surcharge occupancy from proto" in {
        val occuUnit1 = aValidOccupancyUnit.withRoomNo(1).withQty(2)
        val occuUnit2 = aValidOccupancyUnit.withRoomNo(2).withQty(1)
        val occupancyBreakdown =
          OccupancyBreakdown(List(occuUnit1, occuUnit2), 3, MaxAllowedFreeChildAgeRange.empty, false)
        val roomEntryWithOccBreakdown =
          aValidRoomEntry.withOccFromProto(3).withOccupancyBreakdown(Some(occupancyBreakdown))
        val qty = calc.getPRQuantityForSurcharge(roomEntryWithOccBreakdown, reqOcc, 3, fixMarriottSurchargeExp = true)
        qty should_== (0)
      }
    }

    "without occupancyBreakdown" should {
      "for A variant return 2 " in {
        val qty = calc.getPRQuantityForSurcharge(roomEntry, reqOcc, 3, fixMarriottSurchargeExp = false)
        qty should_== (2)
      }

      "for B variant return 2" in {
        val qty = calc.getPRQuantityForSurcharge(roomEntry, reqOcc, 3, fixMarriottSurchargeExp = true)
        qty should_== (2)
      }
    }

  }

  "getOccupancyTupleForPull" should {
    val yplOcc = aValidOccInfo.withAdults(2).withChildren(Some(YplChildren(1, Some(3), Map.empty)))
    val reqOcc = aValidReqOcc.withAdults(2).withOccupancy(yplOcc)
    val bVariantSupplierRateInfo = aValidOTASupplierRateInfo.withExternalData(
      Seq(ExternalData(YplExperiments.FOR_PULL_SPLIT_PROTO_PRICE_TO_ROOM_AND_SURCHARGE, "B")))
    val bDMCDataHolder = aValidDMCDataHolder.withSupplierRateInfo(Some(bVariantSupplierRateInfo))
    val occEntry = aValidRoomOccupancy.withAdults(3)
    val roomWithBVariant =
      aValidRoomEntry.withDmcDataHolder(Some(bDMCDataHolder)).withPropOfferOccupancy(PropOfferOccupancy(3, 0))

    "return 3 adults from occEntry for pull if exp is B" in {
      val (adults, children, guests) = calc.getOccupancyTupleForPull(roomWithBVariant, reqOcc)
      adults should_== (3)
      guests should_== (3)
      children should_== (0)
    }

    "return 2 adults from reqOcc with emptyDmcDataHolder" in {
      val roomEntry = aValidRoomEntry.withOcEntry(occEntry).withDmcDataHolder(None)
      val (adults, children, guests) = calc.getOccupancyTupleForPull(roomEntry, reqOcc)
      adults should_== (2)
      guests should_== (3)
      children should_== (1)
    }

    "return 2 adults from reqOcc with emptySupplierRateInfo" in {
      val roomEntryWithEmptySupplierRateInfo =
        aValidRoomEntry.withOcEntry(occEntry).withDmcDataHolder(Some(aValidDMCDataHolder.withSupplierRateInfo(None)))
      val (adults, children, guests) = calc.getOccupancyTupleForPull(roomEntryWithEmptySupplierRateInfo, reqOcc)
      adults should_== (2)
      guests should_== (3)
      children should_== (1)
    }

    "return 2 adults from reqOcc with emptyExternalData" in {
      val roomEntryWithEmptySupplierRateInfo = aValidRoomEntry
        .withOcEntry(occEntry)
        .withDmcDataHolder(
          Some(aValidDMCDataHolder.withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withExternalData(Seq.empty)))))
      val (adults, children, guests) = calc.getOccupancyTupleForPull(roomEntryWithEmptySupplierRateInfo, reqOcc)
      adults should_== (2)
      guests should_== (3)
      children should_== (1)
    }

    "return 2 adults from reqOcc with A variant in externalData" in {
      val roomEntryWithEmptySupplierRateInfo = aValidRoomEntry
        .withOcEntry(occEntry)
        .withDmcDataHolder(Some(aValidDMCDataHolder.withSupplierRateInfo(Some(aValidOTASupplierRateInfo
          .withExternalData(Seq(ExternalData(YplExperiments.FOR_PULL_SPLIT_PROTO_PRICE_TO_ROOM_AND_SURCHARGE, "A")))))))
      val (adults, children, guests) = calc.getOccupancyTupleForPull(roomEntryWithEmptySupplierRateInfo, reqOcc)
      adults should_== (2)
      guests should_== (3)
      children should_== (1)
    }

    // will be removed when integrating NEP-25984
    "calculate total surcharge tax and fee amount correctly for NEP-25984" in {
      val breakdowns = List(
        aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.Surcharge, isFee = false, amount = 1),
        aValidTaxBreakdown.copy(percentage = 10, option = ChargeOption.Surcharge, isFee = false, amount = 2),
        aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.Unknown, isFee = false, amount = 4),
        aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.Surcharge, isFee = true, amount = 8),
        aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.Mandatory, amount = 16),
        aValidTaxBreakdown.copy(percentage = 10, option = ChargeOption.Mandatory, amount = 32),
        aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.Unknown, amount = 64),
      )
      calc.calculateTotalTaxAndFeeAmountValue(ChargeType.Surcharge,
                                              breakdowns,
                                              isCleanedUpHospitalityTax = false) should_== 17
      calc.calculateTotalTaxAndFeeAmountValue(ChargeType.Surcharge,
                                              breakdowns,
                                              isCleanedUpHospitalityTax = true) should_== 16
    }

    // will be removed when integrating NEP-25984
    "calculate total hospitality tax and fee amount correctly for NEP-25984" in {
      val breakdowns = List(
        aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.HospitalityPrice, isFee = false, amount = 1),
        aValidTaxBreakdown.copy(percentage = 10, option = ChargeOption.HospitalityPrice, isFee = false, amount = 2),
        aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.Unknown, isFee = false, amount = 4),
        aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.HospitalityPrice, isFee = true, amount = 8),
        aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.Mandatory, amount = 16),
        aValidTaxBreakdown.copy(percentage = 10, option = ChargeOption.Mandatory, amount = 32),
        aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.Unknown, amount = 64),
      )
      calc.calculateTotalTaxAndFeeAmountValue(ChargeType.Room,
                                              breakdowns,
                                              isCleanedUpHospitalityTax = false) should_== 17
      calc.calculateTotalTaxAndFeeAmountValue(ChargeType.Room,
                                              breakdowns,
                                              isCleanedUpHospitalityTax = true) should_== 16
    }

    "check hp tax exist correctly for NEP-25984" in {
      val price1 = aValidPrice.copy(
        dailyTaxes = aValidDailyTax.copy(isCleanedUpHospitalityTax = false),
        taxBreakDown =
          List(aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.HospitalityPrice, isFee = false, amount = 1)),
      )
      val price2 =
        aValidPrice.copy(dailyTaxes = aValidDailyTax.copy(isCleanedUpHospitalityTax = false), taxBreakDown = List())
      val price3 = aValidPrice.copy(
        dailyTaxes = aValidDailyTax.copy(isCleanedUpHospitalityTax = true),
        taxBreakDown =
          List(aValidTaxBreakdown.copy(percentage = 0, option = ChargeOption.HospitalityPrice, isFee = false, amount = 1)),
      )
      val price4 =
        aValidPrice.copy(dailyTaxes = aValidDailyTax.copy(isCleanedUpHospitalityTax = true), taxBreakDown = List())

      PriceCalculator.hpTaxExist(price1) should_== true
      PriceCalculator.hpTaxExist(price2) should_== false
      PriceCalculator.hpTaxExist(price3) should_== false
      PriceCalculator.hpTaxExist(price4) should_== false
    }

    "should get downlift for surcharge correctly" in {
      calc.getDownliftForSurcharge(aValidSurchargeEntry.copy(isAmount = false, value = 5.0)) should_== 5.0
      calc.getDownliftForSurcharge(aValidSurchargeEntry.copy(isAmount = true, value = 5.0)) should_== 0.0
    }
  }

  "getTaxesToUseForSurcharge" should {
    val mandatorySurchargeEntry = aValidSurchargeEntry.withOption(ChargeOption.Mandatory).build
    val excludedSurchargeEntry = aValidSurchargeEntry.withOption(ChargeOption.Excluded).build
    val mandatoryTax = TaxWithValue(aValidTax.withTaxId(1).withChargeOption(ChargeOption.Mandatory).build, 10.0)
    val excludedTax = TaxWithValue(aValidTax.withTaxId(2).withChargeOption(ChargeOption.Excluded).build, 12.0)
    val dailyTaxes = aValidDailyTax.withTaxes(List(mandatoryTax, excludedTax)).build
    val reqA = aValidYplRequest.withExperiment(
      YplExperiment(ABTest.DO_NOT_CONSIDER_EXCLUDED_TAX_FEE_FOR_EXCLUDED_SURCHARGE, Variant.A))
    val ctxA = aValidYplContext.withRequest(reqA).withExperimentContext(forcedFromRequestExperimentContext(reqA))
    val reqB = aValidYplRequest.withExperiment(
      YplExperiment(ABTest.DO_NOT_CONSIDER_EXCLUDED_TAX_FEE_FOR_EXCLUDED_SURCHARGE, Variant.B))
    val ctxB = aValidYplContext.withRequest(reqB).withExperimentContext(forcedFromRequestExperimentContext(reqB))

    "return allTaxes with exp is A" in {
      val taxesToUseForMandatorySurcharge = calc.getTaxesToUseForSurcharge(mandatorySurchargeEntry, dailyTaxes)(ctxA)
      val taxesToUseForExcludedSurcharge = calc.getTaxesToUseForSurcharge(excludedSurchargeEntry, dailyTaxes)(ctxA)

      taxesToUseForMandatorySurcharge.taxes.size should_== (2)
      taxesToUseForExcludedSurcharge.taxes.size should_== (2)
    }

    "return allTaxes with exp is B for mandatory surcharge" in {
      val taxesToUseForMandatorySurcharge = calc.getTaxesToUseForSurcharge(mandatorySurchargeEntry, dailyTaxes)(ctxB)
      taxesToUseForMandatorySurcharge.taxes.size should_== (2)
    }

    "return mandatoryTaxes with exp is B for excluded surcharge" in {
      val taxesToUseForExcludedSurcharge = calc.getTaxesToUseForSurcharge(excludedSurchargeEntry, dailyTaxes)(ctxB)
      taxesToUseForExcludedSurcharge.taxes.size should_== (1)
      taxesToUseForExcludedSurcharge.taxes.forall(_.tax.option == ChargeOption.Mandatory) shouldEqual (true)
    }
  }
}
