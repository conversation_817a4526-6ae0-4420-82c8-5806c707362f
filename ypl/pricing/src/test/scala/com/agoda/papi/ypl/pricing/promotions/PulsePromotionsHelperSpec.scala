package com.agoda.papi.ypl.pricing.promotions

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.ypl.models.{
  PromotionTypeId,
  YPLTestDataBuilders,
  YplDispatchChannels,
  YplMasterChannel,
  YplRoomEntry,
}
import com.agoda.papi.ypl.models.consts.{Channel, MegaSalePromotions}
import com.agoda.papi.ypl.pricing.PulsePromotionsHelper
import org.specs2.mutable.SpecificationWithJUnit

class PulsePromotionsHelperSpec extends SpecificationWithJUnit with YPLTestDataBuilders {

  "hasPulseNegativeTestRateChannel" should {
    "return true if there is mock rate channel" in {
      val dispatchedChannels = YplDispatchChannels(
        Set(
          YplMasterChannel(Channel.RTL),
          YplMasterChannel(Channel.PulseNegativeMock),
        ))
      val result = PulsePromotionsHelper.hasPulseNegativeTestRateChannel(dispatchedChannels)
      result should beTrue
    }

    "return false if there is no mock rate channel" in {
      val dispatchedChannels = YplDispatchChannels(
        Set(
          YplMasterChannel(Channel.RTL),
          YplMasterChannel(Channel.APS),
        ))
      val result = PulsePromotionsHelper.hasPulseNegativeTestRateChannel(dispatchedChannels)
      result should beFalse
    }
  }

  "isMegaSaleCampaignType with Option[PulseCampaignMetadata]" should {
    "return true when pulseCampaignMetadata contains a MegaSale campaign type ID" in {
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head // Use first MegaSale ID
      val metadata = Some(
        PulseCampaignMetadata(
          promotionTypeId = 100,
          webCampaignId = 1000,
          campaignTypeId = megaSaleCampaignTypeId,
          campaignBadgeCmsId = 1,
          campaignBadgeDescCmsId = 1,
        ))

      val result = PulsePromotionsHelper.isMegaSaleCampaignType(metadata)
      result should beTrue
    }

    "return true when pulseCampaignMetadata contains another MegaSale campaign type ID" in {
      val megaSaleCampaignTypeId = 1807 // AgodaMegaSaleWorldTourismFlashSaleCampaignTypeId
      val metadata = Some(
        PulseCampaignMetadata(
          promotionTypeId = 200,
          webCampaignId = 2000,
          campaignTypeId = megaSaleCampaignTypeId,
          campaignBadgeCmsId = 2,
          campaignBadgeDescCmsId = 2,
        ))

      val result = PulsePromotionsHelper.isMegaSaleCampaignType(metadata)
      result should beTrue
    }

    "return false when pulseCampaignMetadata contains a non-MegaSale campaign type ID" in {
      val nonMegaSaleCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val metadata = Some(
        PulseCampaignMetadata(
          promotionTypeId = 300,
          webCampaignId = 3000,
          campaignTypeId = nonMegaSaleCampaignTypeId,
          campaignBadgeCmsId = 3,
          campaignBadgeDescCmsId = 3,
        ))

      val result = PulsePromotionsHelper.isMegaSaleCampaignType(metadata)
      result should beFalse
    }

    "return false when pulseCampaignMetadata is None" in {
      val result = PulsePromotionsHelper.isMegaSaleCampaignType(None)
      result should beFalse
    }

    "return false when pulseCampaignMetadata contains campaign type ID 0" in {
      val metadata = Some(
        PulseCampaignMetadata(
          promotionTypeId = 400,
          webCampaignId = 4000,
          campaignTypeId = 0,
          campaignBadgeCmsId = 4,
          campaignBadgeDescCmsId = 4,
        ))

      val result = PulsePromotionsHelper.isMegaSaleCampaignType(metadata)
      result should beFalse
    }

    "return false when pulseCampaignMetadata contains negative campaign type ID" in {
      val metadata = Some(
        PulseCampaignMetadata(
          promotionTypeId = 500,
          webCampaignId = 5000,
          campaignTypeId = -1,
          campaignBadgeCmsId = 5,
          campaignBadgeDescCmsId = 5,
        ))

      val result = PulsePromotionsHelper.isMegaSaleCampaignType(metadata)
      result should beFalse
    }
  }

  "isPulseCampaignType with Option[PulseCampaignMetadata]" should {
    "return true when pulseCampaignMetadata contains a non-MegaSale campaign type ID" in {
      val nonMegaSaleCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val metadata = Some(
        PulseCampaignMetadata(
          promotionTypeId = 100,
          webCampaignId = 1000,
          campaignTypeId = nonMegaSaleCampaignTypeId,
          campaignBadgeCmsId = 1,
          campaignBadgeDescCmsId = 1,
        ))

      val result = PulsePromotionsHelper.isPulseCampaignType(metadata)
      result should beTrue
    }

    "return true when pulseCampaignMetadata contains campaign type ID 0" in {
      val metadata = Some(
        PulseCampaignMetadata(
          promotionTypeId = 200,
          webCampaignId = 2000,
          campaignTypeId = 0,
          campaignBadgeCmsId = 2,
          campaignBadgeDescCmsId = 2,
        ))

      val result = PulsePromotionsHelper.isPulseCampaignType(metadata)
      result should beTrue
    }

    "return true when pulseCampaignMetadata contains negative campaign type ID" in {
      val metadata = Some(
        PulseCampaignMetadata(
          promotionTypeId = 300,
          webCampaignId = 3000,
          campaignTypeId = -1,
          campaignBadgeCmsId = 3,
          campaignBadgeDescCmsId = 3,
        ))

      val result = PulsePromotionsHelper.isPulseCampaignType(metadata)
      result should beTrue
    }

    "return false when pulseCampaignMetadata contains a MegaSale campaign type ID" in {
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head // Use first MegaSale ID
      val metadata = Some(
        PulseCampaignMetadata(
          promotionTypeId = 400,
          webCampaignId = 4000,
          campaignTypeId = megaSaleCampaignTypeId,
          campaignBadgeCmsId = 4,
          campaignBadgeDescCmsId = 4,
        ))

      val result = PulsePromotionsHelper.isPulseCampaignType(metadata)
      result should beFalse
    }

    "return false when pulseCampaignMetadata contains another MegaSale campaign type ID" in {
      val megaSaleCampaignTypeId = 1808 // AgodaMegaSaleWorldTourismVipCampaignTypeId
      val metadata = Some(
        PulseCampaignMetadata(
          promotionTypeId = 500,
          webCampaignId = 5000,
          campaignTypeId = megaSaleCampaignTypeId,
          campaignBadgeCmsId = 5,
          campaignBadgeDescCmsId = 5,
        ))

      val result = PulsePromotionsHelper.isPulseCampaignType(metadata)
      result should beFalse
    }

    "return false when pulseCampaignMetadata is None" in {
      val result = PulsePromotionsHelper.isPulseCampaignType(None)
      result should beFalse
    }
  }

  "isMegaSaleCampaignType with Map[PromotionTypeId, PulseCampaignMetadata]" should {
    "return true when promotion type ID exists in map with MegaSale campaign type" in {
      val promotionTypeId = 100
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val metadata = PulseCampaignMetadata(
        promotionTypeId = promotionTypeId,
        webCampaignId = 1000,
        campaignTypeId = megaSaleCampaignTypeId,
        campaignBadgeCmsId = 1,
        campaignBadgeDescCmsId = 1,
      )
      val map = Map(promotionTypeId -> metadata)

      val result = PulsePromotionsHelper.isMegaSaleCampaignType(promotionTypeId, map)
      result should beTrue
    }

    "return false when promotion type ID exists in map with non-MegaSale campaign type" in {
      val promotionTypeId = 200
      val nonMegaSaleCampaignTypeId = 1000
      val metadata = PulseCampaignMetadata(
        promotionTypeId = promotionTypeId,
        webCampaignId = 2000,
        campaignTypeId = nonMegaSaleCampaignTypeId,
        campaignBadgeCmsId = 2,
        campaignBadgeDescCmsId = 2,
      )
      val map = Map(promotionTypeId -> metadata)

      val result = PulsePromotionsHelper.isMegaSaleCampaignType(promotionTypeId, map)
      result should beFalse
    }

    "return false when promotion type ID does not exist in map" in {
      val promotionTypeId = 300
      val otherPromotionTypeId = 400
      val metadata = PulseCampaignMetadata(
        promotionTypeId = otherPromotionTypeId,
        webCampaignId = 4000,
        campaignTypeId = MegaSalePromotions.CampaignTypeIds.head,
        campaignBadgeCmsId = 4,
        campaignBadgeDescCmsId = 4,
      )
      val map = Map(otherPromotionTypeId -> metadata)

      val result = PulsePromotionsHelper.isMegaSaleCampaignType(promotionTypeId, map)
      result should beFalse
    }

    "return false when map is empty" in {
      val promotionTypeId = 500
      val emptyMap = Map.empty[PromotionTypeId, PulseCampaignMetadata]

      val result = PulsePromotionsHelper.isMegaSaleCampaignType(promotionTypeId, emptyMap)
      result should beFalse
    }

    "verify that isMegaSaleCampaignType and isPulseCampaignType are mutually exclusive for Some metadata" in {
      val testCampaignTypeIds = List(
        1000, // Non-MegaSale
        MegaSalePromotions.CampaignTypeIds.head, // MegaSale
        0, // Edge case
        -1, // Edge case
        Int.MaxValue, // Edge case
      )

      testCampaignTypeIds.foreach { campaignTypeId =>
        val metadata = Some(
          PulseCampaignMetadata(
            promotionTypeId = 100,
            webCampaignId = 1000,
            campaignTypeId = campaignTypeId,
            campaignBadgeCmsId = 1,
            campaignBadgeDescCmsId = 1,
          ))

        val isMegaSale = PulsePromotionsHelper.isMegaSaleCampaignType(metadata)
        val isPulse = PulsePromotionsHelper.isPulseCampaignType(metadata)

        // They should be mutually exclusive for Some metadata
        (isMegaSale && isPulse) should beFalse
        (isMegaSale || isPulse) should beTrue
      }
      testCampaignTypeIds.length should greaterThan(0)
    }

    "verify that both functions return false for None metadata" in {
      val isMegaSale = PulsePromotionsHelper.isMegaSaleCampaignType(None)
      val isPulse = PulsePromotionsHelper.isPulseCampaignType(None)

      isMegaSale should beFalse
      isPulse should beFalse
    }
  }

  "isPulseCampaignType with Map[PromotionTypeId, PulseCampaignMetadata]" should {
    "return true when promotion type ID exists in map with non-MegaSale campaign type" in {
      val promotionTypeId = 100
      val nonMegaSaleCampaignTypeId = 1000
      val metadata = PulseCampaignMetadata(
        promotionTypeId = promotionTypeId,
        webCampaignId = 1000,
        campaignTypeId = nonMegaSaleCampaignTypeId,
        campaignBadgeCmsId = 1,
        campaignBadgeDescCmsId = 1,
      )
      val map = Map(promotionTypeId -> metadata)

      val result = PulsePromotionsHelper.isPulseCampaignType(promotionTypeId, map)
      result should beTrue
    }

    "return false when promotion type ID exists in map with MegaSale campaign type" in {
      val promotionTypeId = 200
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val metadata = PulseCampaignMetadata(
        promotionTypeId = promotionTypeId,
        webCampaignId = 2000,
        campaignTypeId = megaSaleCampaignTypeId,
        campaignBadgeCmsId = 2,
        campaignBadgeDescCmsId = 2,
      )
      val map = Map(promotionTypeId -> metadata)

      val result = PulsePromotionsHelper.isPulseCampaignType(promotionTypeId, map)
      result should beFalse
    }

    "return false when promotion type ID does not exist in map" in {
      val promotionTypeId = 300
      val otherPromotionTypeId = 400
      val metadata = PulseCampaignMetadata(
        promotionTypeId = otherPromotionTypeId,
        webCampaignId = 4000,
        campaignTypeId = 1000, // Non-MegaSale
        campaignBadgeCmsId = 4,
        campaignBadgeDescCmsId = 4,
      )
      val map = Map(otherPromotionTypeId -> metadata)

      val result = PulsePromotionsHelper.isPulseCampaignType(promotionTypeId, map)
      result should beFalse
    }

    "return false when map is empty" in {
      val promotionTypeId = 500
      val emptyMap = Map.empty[PromotionTypeId, PulseCampaignMetadata]

      val result = PulsePromotionsHelper.isPulseCampaignType(promotionTypeId, emptyMap)
      result should beFalse
    }
  }

  "findTheFinalPulseMetadata" should {

    // Helper method to create YplRoomEntry with pulse metadata
    def createRoomWithPulseMetadata(pulseCampaignMetadata: Option[PulseCampaignMetadata]): YplRoomEntry =
      aValidRoomEntry.copy(pulseCampaignMetadata = pulseCampaignMetadata)

    // Helper method to create PulseCampaignMetadata
    def createPulseCampaignMetadata(promotionTypeId: Int, campaignTypeId: Int): PulseCampaignMetadata =
      PulseCampaignMetadata(
        promotionTypeId = promotionTypeId,
        webCampaignId = 1000,
        campaignTypeId = campaignTypeId,
        campaignBadgeCmsId = 1,
        campaignBadgeDescCmsId = 1,
      )

    "return None when originalRoom equals updatedDiscountRoom" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val updatedRoom = originalRoom // Same reference
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, 1000))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beNone
    }

    "return None when updatedDiscountRoom is null" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val updatedRoom: Null = null
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, 1000))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beNone
    }

    "return Some with unchanged room when updatedRoom has empty pulseCampaignMetadata" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val updatedRoom = createRoomWithPulseMetadata(None).copy(roomTypeId = 999L) // Different to ensure != check passes
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, 1000))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      result.get should_== updatedRoom
      result.get.pulseCampaignMetadata should beNone
    }

    "return Some with bestMegaMetadata when updatedRoom has MegaSale campaign metadata" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val existingMegaMetadata = createPulseCampaignMetadata(300, megaSaleCampaignTypeId)
      val updatedRoom = createRoomWithPulseMetadata(Some(existingMegaMetadata)).copy(roomTypeId = 999L)
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, megaSaleCampaignTypeId))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, 1000))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      result.get.pulseCampaignMetadata should_== bestMegaMetadata
      result.get.roomTypeId should_== 999L
    }

    "return Some with bestPulseMetadata when updatedRoom has non-MegaSale campaign metadata" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val nonMegaSaleCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val existingPulseMetadata = createPulseCampaignMetadata(300, nonMegaSaleCampaignTypeId)
      val updatedRoom = createRoomWithPulseMetadata(Some(existingPulseMetadata)).copy(roomTypeId = 999L)
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, nonMegaSaleCampaignTypeId))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      result.get.pulseCampaignMetadata should_== bestPulseMetadata
      result.get.roomTypeId should_== 999L
    }

    "should handle room with Some metadata correctly when metadata is isEmpty" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val pulseMetadata = createPulseCampaignMetadata(300, 1000)
      val updatedRoom = createRoomWithPulseMetadata(Some(pulseMetadata)).copy(roomTypeId = 999L)
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, 1000))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      result.get.pulseCampaignMetadata should_== bestPulseMetadata

      result.get.pulseCampaignMetadata should not be Some(pulseMetadata)
    }

    "should not replace metadata when original metadata is None" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val updatedRoom = createRoomWithPulseMetadata(None).copy(roomTypeId = 999L)
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, 1000))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      result.get.pulseCampaignMetadata should beNone

      result.get.pulseCampaignMetadata should not be bestPulseMetadata
      result.get.pulseCampaignMetadata should not be bestMegaMetadata
    }

    "should not replace metadata when original metadata is None but the best metadata is valid" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val megaSaleMetadata = createPulseCampaignMetadata(300, MegaSalePromotions.CampaignTypeIds.head)
      val updatedRoom = createRoomWithPulseMetadata(Some(megaSaleMetadata)).copy(roomTypeId = 999L)
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, 1000))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      result.get.pulseCampaignMetadata should_== bestMegaMetadata

      result.get.pulseCampaignMetadata should not be Some(megaSaleMetadata)
    }

    "handle edge case with None bestMegaMetadata for MegaSale campaign" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val existingMegaMetadata = createPulseCampaignMetadata(300, megaSaleCampaignTypeId)
      val updatedRoom = createRoomWithPulseMetadata(Some(existingMegaMetadata)).copy(roomTypeId = 999L)
      val bestMegaMetadata = None // None instead of Some
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, 1000))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      result.get.pulseCampaignMetadata should beNone
    }

    "handle edge case with None bestPulseMetadata for Pulse campaign" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val nonMegaSaleCampaignTypeId = 1000
      val existingPulseMetadata = createPulseCampaignMetadata(300, nonMegaSaleCampaignTypeId)
      val updatedRoom = createRoomWithPulseMetadata(Some(existingPulseMetadata)).copy(roomTypeId = 999L)
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = None // None instead of Some

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      result.get.pulseCampaignMetadata should beNone
    }

    "preserve all other room fields when updating metadata" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val pulseMetadata = createPulseCampaignMetadata(300, 1000)
      val updatedRoom = createRoomWithPulseMetadata(Some(pulseMetadata)).copy(
        roomTypeId = 999L,
        masterRoomTypeId = 888L,
        cxlCode = "TEST_CXL",
        isBreakFastIncluded = true,
        remainingRooms = 5,
      )
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, 1000))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      val finalRoom = result.get
      finalRoom.roomTypeId should_== 999L
      finalRoom.masterRoomTypeId should_== 888L
      finalRoom.cxlCode should_== "TEST_CXL"
      finalRoom.isBreakFastIncluded should beTrue
      finalRoom.remainingRooms should_== 5
      finalRoom.pulseCampaignMetadata should_== bestPulseMetadata
    }

    "handle boundary case with campaign type ID 0" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val zeroMetadata = createPulseCampaignMetadata(300, 0)
      val updatedRoom = createRoomWithPulseMetadata(Some(zeroMetadata)).copy(roomTypeId = 999L)
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, 0))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      // Campaign type ID 0 is not a MegaSale campaign, so should use bestPulseMetadata
      result.get.pulseCampaignMetadata should_== bestPulseMetadata
    }

    "handle boundary case with negative campaign type ID" in {
      val originalRoom = createRoomWithPulseMetadata(None)
      val negativeMetadata = createPulseCampaignMetadata(300, -1)
      val updatedRoom = createRoomWithPulseMetadata(Some(negativeMetadata)).copy(roomTypeId = 999L)
      val bestMegaMetadata = Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head))
      val bestPulseMetadata = Some(createPulseCampaignMetadata(200, -1))

      val result = PulsePromotionsHelper.findTheFinalPulseMetadata(
        originalRoom,
        updatedRoom,
        bestMegaMetadata,
        bestPulseMetadata,
      )

      result should beSome
      // Negative campaign type ID is not a MegaSale campaign, so should use bestPulseMetadata
      result.get.pulseCampaignMetadata should_== bestPulseMetadata
    }

  }

  "overrideMegaSalePulseMetadataIfNeeded" should {

    // Helper method to create YplRoomEntry with pulse metadata
    def createRoomWithPulseMetadata(pulseCampaignMetadata: Option[PulseCampaignMetadata]): YplRoomEntry =
      aValidRoomEntry.copy(pulseCampaignMetadata = pulseCampaignMetadata)

    // Helper method to create PulseCampaignMetadata
    def createPulseCampaignMetadata(promotionTypeId: Int, campaignTypeId: Int): PulseCampaignMetadata =
      PulseCampaignMetadata(
        promotionTypeId = promotionTypeId,
        webCampaignId = 1000,
        campaignTypeId = campaignTypeId,
        campaignBadgeCmsId = 1,
        campaignBadgeDescCmsId = 1,
      )

    "return finalRoom unchanged when isMegaCampaignEnabled is false" in {
      val originalRoom =
        createRoomWithPulseMetadata(Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head)))
      val finalRoom = Some(createRoomWithPulseMetadata(Some(createPulseCampaignMetadata(200, 1000))))
      val isMegaCampaignEnabled = false

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result should_== finalRoom
    }

    "return None when finalRoom is None and isMegaCampaignEnabled is true" in {
      val originalRoom =
        createRoomWithPulseMetadata(Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head)))
      val finalRoom = None
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result should beNone
    }

    "return None when finalRoom is None and isMegaCampaignEnabled is false" in {
      val originalRoom =
        createRoomWithPulseMetadata(Some(createPulseCampaignMetadata(100, MegaSalePromotions.CampaignTypeIds.head)))
      val finalRoom = None
      val isMegaCampaignEnabled = false

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      // When isMegaCampaignEnabled is false, should return finalRoom directly (None)
      result should beNone
    }

    "restore original mega sale metadata when original is mega sale and final is pulse" in {
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val originalMegaMetadata = createPulseCampaignMetadata(100, megaSaleCampaignTypeId)
      val originalRoom = createRoomWithPulseMetadata(Some(originalMegaMetadata))

      val pulseCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val finalPulseMetadata = createPulseCampaignMetadata(200, pulseCampaignTypeId)
      val finalRoom = Some(createRoomWithPulseMetadata(Some(finalPulseMetadata)).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result should beSome
      result.get.pulseCampaignMetadata should_== Some(originalMegaMetadata)
      result.get.roomTypeId should_== 999L // Verify other fields are preserved
    }

    "keep final room unchanged when original is pulse and final is mega sale" in {
      val pulseCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val originalPulseMetadata = createPulseCampaignMetadata(100, pulseCampaignTypeId)
      val originalRoom = createRoomWithPulseMetadata(Some(originalPulseMetadata))

      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val finalMegaMetadata = createPulseCampaignMetadata(200, megaSaleCampaignTypeId)
      val finalRoom = Some(createRoomWithPulseMetadata(Some(finalMegaMetadata)).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result should beSome
      result.get.pulseCampaignMetadata should_== Some(finalMegaMetadata)
      result.get.roomTypeId should_== 999L
    }

    "keep final room unchanged when both original and final are mega sale" in {
      val megaSaleCampaignTypeId1 = MegaSalePromotions.CampaignTypeIds.head
      val megaSaleCampaignTypeId2 = MegaSalePromotions.CampaignTypeIds(1)
      val originalMegaMetadata = createPulseCampaignMetadata(100, megaSaleCampaignTypeId1)
      val originalRoom = createRoomWithPulseMetadata(Some(originalMegaMetadata))

      val finalMegaMetadata = createPulseCampaignMetadata(200, megaSaleCampaignTypeId2)
      val finalRoom = Some(createRoomWithPulseMetadata(Some(finalMegaMetadata)).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result should beSome
      result.get.pulseCampaignMetadata should_== Some(finalMegaMetadata)
      result.get.roomTypeId should_== 999L
    }

    "keep final room unchanged when both original and final are pulse" in {
      val pulseCampaignTypeId1 = 1000 // Not in MegaSalePromotions list
      val pulseCampaignTypeId2 = 2000 // Not in MegaSalePromotions list
      val originalPulseMetadata = createPulseCampaignMetadata(100, pulseCampaignTypeId1)
      val originalRoom = createRoomWithPulseMetadata(Some(originalPulseMetadata))

      val finalPulseMetadata = createPulseCampaignMetadata(200, pulseCampaignTypeId2)
      val finalRoom = Some(createRoomWithPulseMetadata(Some(finalPulseMetadata)).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result should beSome
      result.get.pulseCampaignMetadata should_== Some(finalPulseMetadata)
      result.get.roomTypeId should_== 999L
    }

    "should handle isMegaCampaignEnabled correctly" in {
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val originalMegaMetadata = createPulseCampaignMetadata(100, megaSaleCampaignTypeId)
      val originalRoom = createRoomWithPulseMetadata(Some(originalMegaMetadata))

      val pulseCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val finalPulseMetadata = createPulseCampaignMetadata(200, pulseCampaignTypeId)
      val finalRoom = Some(createRoomWithPulseMetadata(Some(finalPulseMetadata)).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result.get.pulseCampaignMetadata should_== Some(originalMegaMetadata)
      result.get.pulseCampaignMetadata should not be Some(finalPulseMetadata)
    }

    "should handle when finalRoom.isEmpty correctly" in {
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val originalMegaMetadata = createPulseCampaignMetadata(100, megaSaleCampaignTypeId)
      val originalRoom = createRoomWithPulseMetadata(Some(originalMegaMetadata))

      val pulseCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val finalPulseMetadata = createPulseCampaignMetadata(200, pulseCampaignTypeId)
      val finalRoom = Some(createRoomWithPulseMetadata(Some(finalPulseMetadata)).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result should beSome
      result.get.pulseCampaignMetadata should_== Some(originalMegaMetadata)
      result.get.pulseCampaignMetadata should not be Some(finalPulseMetadata)
    }

    "handle edge case with None original metadata" in {
      val originalRoom = createRoomWithPulseMetadata(None)

      val pulseCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val finalPulseMetadata = createPulseCampaignMetadata(200, pulseCampaignTypeId)
      val finalRoom = Some(createRoomWithPulseMetadata(Some(finalPulseMetadata)).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      // When original has no metadata, final room should remain unchanged
      result should beSome
      result.get.pulseCampaignMetadata should_== Some(finalPulseMetadata)
      result.get.roomTypeId should_== 999L
    }

    "handle edge case with None final metadata" in {
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val originalMegaMetadata = createPulseCampaignMetadata(100, megaSaleCampaignTypeId)
      val originalRoom = createRoomWithPulseMetadata(Some(originalMegaMetadata))

      val finalRoom = Some(createRoomWithPulseMetadata(None).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      // When final room has no metadata, it should remain unchanged
      result should beSome
      result.get.pulseCampaignMetadata should beNone
      result.get.roomTypeId should_== 999L
    }

    "preserve all other room fields when overriding metadata" in {
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val originalMegaMetadata = createPulseCampaignMetadata(100, megaSaleCampaignTypeId)
      val originalRoom = createRoomWithPulseMetadata(Some(originalMegaMetadata))

      val pulseCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val finalPulseMetadata = createPulseCampaignMetadata(200, pulseCampaignTypeId)
      val finalRoom = Some(
        createRoomWithPulseMetadata(Some(finalPulseMetadata)).copy(
          roomTypeId = 999L,
          masterRoomTypeId = 888L,
          cxlCode = "TEST_CXL",
          isBreakFastIncluded = true,
          remainingRooms = 5,
        ))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result should beSome
      val finalRoomResult = result.get
      finalRoomResult.roomTypeId should_== 999L
      finalRoomResult.masterRoomTypeId should_== 888L
      finalRoomResult.cxlCode should_== "TEST_CXL"
      finalRoomResult.isBreakFastIncluded should beTrue
      finalRoomResult.remainingRooms should_== 5
      finalRoomResult.pulseCampaignMetadata should_== Some(originalMegaMetadata)
    }

    "handle boundary case with campaign type ID 0 in original" in {
      val originalMetadata = createPulseCampaignMetadata(100, 0) // Campaign type ID 0
      val originalRoom = createRoomWithPulseMetadata(Some(originalMetadata))

      val pulseCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val finalPulseMetadata = createPulseCampaignMetadata(200, pulseCampaignTypeId)
      val finalRoom = Some(createRoomWithPulseMetadata(Some(finalPulseMetadata)).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      // Campaign type ID 0 is not a MegaSale campaign, so no override should occur
      result should beSome
      result.get.pulseCampaignMetadata should_== Some(finalPulseMetadata)
      result.get.roomTypeId should_== 999L
    }

    "handle boundary case with negative campaign type ID in original" in {
      val originalMetadata = createPulseCampaignMetadata(100, -1) // Negative campaign type ID
      val originalRoom = createRoomWithPulseMetadata(Some(originalMetadata))

      val pulseCampaignTypeId = 1000 // Not in MegaSalePromotions list
      val finalPulseMetadata = createPulseCampaignMetadata(200, pulseCampaignTypeId)
      val finalRoom = Some(createRoomWithPulseMetadata(Some(finalPulseMetadata)).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      // Negative campaign type ID is not a MegaSale campaign, so no override should occur
      result should beSome
      result.get.pulseCampaignMetadata should_== Some(finalPulseMetadata)
      result.get.roomTypeId should_== 999L
    }

    "prevent finalRoom.nonEmpty -> true mutation - should return finalRoom when None and isMegaCampaignEnabled is false" in {
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val originalMegaMetadata = createPulseCampaignMetadata(100, megaSaleCampaignTypeId)
      val originalRoom = createRoomWithPulseMetadata(Some(originalMegaMetadata))

      val finalRoom = None // Critical: finalRoom is None
      val isMegaCampaignEnabled = false // Critical: disabled, so should return finalRoom directly

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result should beNone
      result should_== finalRoom
    }

    "prevent case None -> case Some mutation - None should return None" in {

      val originalRoom = createRoomWithPulseMetadata(None)
      val finalRoom: Option[YplRoomEntry] = None
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      // When finalRoom is None, result should be None
      result should beNone
      result should_== None
    }

    "prevent case Some -> case None mutation - Some should be processed" in {
      val megaSaleCampaignTypeId = MegaSalePromotions.CampaignTypeIds.head
      val originalMegaMetadata = createPulseCampaignMetadata(100, megaSaleCampaignTypeId)
      val originalRoom = createRoomWithPulseMetadata(Some(originalMegaMetadata))

      val pulseCampaignTypeId = 1000
      val finalPulseMetadata = createPulseCampaignMetadata(200, pulseCampaignTypeId)
      val finalRoom = Some(createRoomWithPulseMetadata(Some(finalPulseMetadata)).copy(roomTypeId = 999L))
      val isMegaCampaignEnabled = true

      val result = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        finalRoom,
        isMegaCampaignEnabled,
      )

      result should beSome
      result.get.pulseCampaignMetadata should_== Some(originalMegaMetadata)
      result.get.pulseCampaignMetadata should not be Some(finalPulseMetadata)
    }

    "comprehensive pattern matching verification" in {
      // This test ensures the pattern matching works correctly for all cases
      // and helps detect mutations in the match expression

      val originalRoom = createRoomWithPulseMetadata(None)

      // Test case 1: None with enabled flag should return None
      val noneResult = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        None,
        isMegaCampaignEnabled = true,
      )
      noneResult should beNone

      // Test case 2: None with disabled flag should return None
      val noneDisabledResult = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        None,
        isMegaCampaignEnabled = false,
      )
      noneDisabledResult should beNone

      // Test case 3: Some with enabled flag should return Some
      val someRoom = Some(createRoomWithPulseMetadata(None))
      val someResult = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        someRoom,
        isMegaCampaignEnabled = true,
      )
      someResult should beSome

      // Test case 4: Some with disabled flag should return the original Some
      val someDisabledResult = PulsePromotionsHelper.overrideMegaSalePulseMetadataIfNeeded(
        originalRoom,
        someRoom,
        isMegaCampaignEnabled = false,
      )
      someDisabledResult should_== someRoom

      // Verify the pattern matching behavior is consistent
      noneResult should_== noneDisabledResult
      someResult should beSome
      someDisabledResult should beSome
    }

  }
}
