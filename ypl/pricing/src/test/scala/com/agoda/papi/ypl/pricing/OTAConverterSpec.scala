package com.agoda.papi.ypl.pricing

import com.agoda.commons.models.pricing.ExchangeRate
import com.agoda.finance.tax.enums.TaxLevelCalculationType
import com.agoda.finance.tax.models.{TaxPrototypeInfo, TaxPrototypeLevel}
import com.agoda.papi.enums.hotel.{PaymentModel, RateModel, TaxType}
import com.agoda.papi.enums.request.{BookingDurationType, FeatureFlag, StackDiscountOption}
import com.agoda.papi.enums.room.{
  BenefitTypes,
  ChargeOption,
  ChargeType,
  DiscountType,
  InventoryType,
  RateType,
  WhomToPayType,
}
import com.agoda.papi.ypl.fencing.FencedAgxCommission
import com.agoda.papi.ypl.models.api.request.YplAGXCommission.noAgxCommission
import com.agoda.papi.ypl.models.api.request._
import com.agoda.papi.ypl.models.consts.{Benefits, CancellationCode, Channel}
import com.agoda.papi.ypl.models.context.ExchangeRateContext
import com.agoda.papi.enums.room.ApplyTaxOver.SaleEx
import com.agoda.papi.ypl.models.enums.OccupancyModel.{Full, FullPatternLengthOfStay => Fplos}
import com.agoda.papi.ypl.models.enums.{BreakdownStep, VipLevelType, OccupancyModel => YPLOccupancyModel}
import com.agoda.papi.ypl.models.hotel.AgePolicy
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.pricing.{BookingPriceBreakdown, RoomOccupancy}
import com.agoda.papi.ypl.models.proto.{OTARoomTypeKey, PeriodIntervalInput, Restriction, RestrictionEntryModel}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.pricing.mocks.{CidToOriginMapperMock, OriginManagerMock}
import com.agoda.papi.ypl.services.{CidToOriginMapper, OriginManager}
import com.agoda.papi.ypl.settings.MOHUGdsCommissionFeeSettings
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{times, verify, when}
import org.specs2.mock.Mockito.mock
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.SupplierRateInfo.ExternalData
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.{
  BookingRestriction => RateCategoryBookingRestriction,
  TimeInterval => OTATimeInterval,
}
import com.agoda.protobuf.cache.Promotion.Restriction.{
  CustomerSegment => PromotionRestrictionCustomerSegment,
  PeriodInterval => PromotionRestrictionPeriodInterval,
  PeriodType => PromotionRestrictionPeriodType,
}
import com.agoda.protobuf.cache.Promotion.{Discount, Restriction => PromotionRestriction}
import com.agoda.protobuf.cache.{
  Benefit,
  ChannelRoomRate,
  HotelPrice,
  RateRepurposingData,
  StructureBenefitParameter,
  Promotion => ProtoPromotion,
}
import com.agoda.protobuf.common.OccupanyModel.{FullPatternLengthOfStay, FullRate}
import com.agoda.protobuf.common.{
  ApplyType,
  Occupancy,
  OccupanyModel,
  TaxApplyBreakdownType,
  PaymentModel => ProtoPaymentModel,
}
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope
import com.agoda.papi.enums.room.ApplyTaxOver
import com.agoda.papi.pricing.pricecalculation.models.tax.Tax

//scalastyle:off
class OTAConverterSpec
  extends SpecificationWithJUnit
    with YPLTestDataBuilders
    with YPLTestContexts
    with YplOddCheckinCheckoutTestBuilder {

  private val fenceTH1 = YplRateFence("TH", 1, 1)
  private val fenceUS1 = YplRateFence("US", 1, 1)
  private val yplDispatchChannels =
    YplDispatchChannels(masterChannels = Set(YplMasterChannel.RTL, YplMasterChannel.APS, YplMasterChannel.NET))
  private val fencedDispatchChannels = Map(aValidRateFence -> yplDispatchChannels)
  "OTA Converter" should {
    val otaConverter = new OTAConverter with OriginManagerMock with CidToOriginMapperMock {}
    val mohuGdsCommissionFeeSettings = new MOHUGdsCommissionFeeSettings(Set[Int] {
                                                                          1
                                                                        },
                                                                        Set[Int] {
                                                                          1
                                                                        })
    val US_NEW_TAX_APPLY_TYPE = "VYG-323" // constant used to mock US Tax V2 experiment
    "Find commission" should {

      val commission =
        aValidOTACommission.withChannelID(1).withLanguageID(1).withValue(20.0).withContractedCommission(15.0)
      val commissionWithLanguageIdZero = aValidOTACommission.withChannelID(1).withLanguageID(0).withValue(30.0)
      val commissionWithChannelIdZero = aValidOTACommission.withChannelID(0).withLanguageID(1).withValue(40.0)
      val commissionWithChannelIdLanguageIdZero = aValidOTACommission.withChannelID(0).withLanguageID(0).withValue(50.0)
      val commissionChannelIdLanguageIdTwo = aValidOTACommission.withChannelID(2).withLanguageID(2).withValue(10)
      val commissionWithZeroValue = aValidOTACommission.withChannelID(1).withLanguageID(1).withValue(0d)
      val aValidYplAGXCommissionAdjustment: Option[YplAGXCommissionAdjustment] =
        Some(YplAGXCommissionAdjustment(Map.empty, Some(YplAGXCommission(1))))
      val aValidYplAGXCommissionAdjustmentWrapperA: YplAGXCommissionAdjustmentWrapper =
        YplAGXCommissionAdjustmentWrapper(aValidYplAGXCommissionAdjustment)
      val aValidSupplierMap: Map[SupplierId, YplAGXActionItem] =
        Map((aValidSupplierId, YplAGXActionItem(Nil, Seq(Channel.APO, Channel.RestOpaque))))
      val aValidYplAGXCommissionAdjustmentWithSupplier: Option[YplAGXCommissionAdjustment] =
        Some(YplAGXCommissionAdjustment(Map.empty, Some(YplAGXCommission(1)), aValidSupplierMap))
      val aValidYplAGXCommissionAdjustmentWrapperB: YplAGXCommissionAdjustmentWrapper =
        YplAGXCommissionAdjustmentWrapper(aValidYplAGXCommissionAdjustmentWithSupplier)
      val aValidExcludeBnSupplierMap: Map[SupplierId, YplAGXActionItem] =
        Map((aValidSupplierId, YplAGXActionItem(Nil, Channel.BedbankChannels.toSeq)))
      val aValidYplAGXCommissionAdjustmentWrapperC: YplAGXCommissionAdjustmentWrapper =
        YplAGXCommissionAdjustmentWrapper(
          Some(YplAGXCommissionAdjustment(Map.empty, Some(YplAGXCommission(1)), aValidExcludeBnSupplierMap)),
        )
      val commissionWithChannelAPO = aValidOTACommission.withChannelID(Channel.APO).withLanguageID(1).withValue(20.0)
      implicit val ctx = aValidYplContext

      val errorCommissions = Commissions(0, noAgxCommission)

      "return commission value when found with language id and channel id" in {
        val commissions = Seq(commission, commissionWithLanguageIdZero).map(YplCommission(_))
        val result: Option[Commissions] = otaConverter.findCommission(commissions,
                                                                      1,
                                                                      InventoryType.Payout,
                                                                      1,
                                                                      hotelPrice = criteria,
                                                                      rateType = RateType.NetExclusive)
        result.getOrElse(errorCommissions).original must_== 20d
      }

      "return commission value that is language id 0 when not found with given language id" in {
        val commissions = Seq(commissionWithLanguageIdZero).map(YplCommission(_))
        val result = otaConverter.findCommission(commissions,
                                                 1,
                                                 InventoryType.Payout,
                                                 1,
                                                 hotelPrice = criteria,
                                                 rateType = RateType.NetExclusive)
        result.getOrElse(errorCommissions).original must_== 30d
      }

      "return commission value that is channel id = 0 when not found with given channel id" in {
        val commissions = Seq(commissionWithChannelIdLanguageIdZero, commissionWithChannelIdZero).map(YplCommission(_))
        val result = otaConverter.findCommission(commissions,
                                                 1,
                                                 InventoryType.Payout,
                                                 1,
                                                 hotelPrice = criteria,
                                                 rateType = RateType.NetExclusive)
        result.getOrElse(errorCommissions).original must_== 40d
      }

      "return commission value that is channel id = 0 and language id = 0 when not found with given channel id and language id" in {
        val commissions =
          Seq(commissionChannelIdLanguageIdTwo, commissionWithChannelIdLanguageIdZero).map(YplCommission(_))
        val result = otaConverter.findCommission(commissions,
                                                 1,
                                                 InventoryType.Payout,
                                                 1,
                                                 hotelPrice = criteria,
                                                 rateType = RateType.NetExclusive)
        result.getOrElse(errorCommissions).original must_== 50d
      }

      "return Option(0) when not found commission with given language id and channel id" in {
        val commissions = Seq(commission, commissionWithLanguageIdZero).map(YplCommission(_))
        val result = otaConverter.findCommission(commissions,
                                                 2,
                                                 InventoryType.Payout,
                                                 0,
                                                 hotelPrice = criteria,
                                                 rateType = RateType.NetExclusive)
        result.getOrElse(errorCommissions).original must_== 0d
      }

      "return None when not found commission with given language id and channel id" in {
        val commissions = Seq(commission, commissionWithLanguageIdZero).map(YplCommission(_))
        val result = otaConverter.findCommission(commissions,
                                                 2,
                                                 InventoryType.Payout,
                                                 0,
                                                 true,
                                                 hotelPrice = criteria,
                                                 rateType = RateType.NetExclusive)
        result must beNone
      }

      "return None when commission is zero with given language id and channel id" in {
        val commissions = Seq(commissionWithZeroValue).map(YplCommission(_))
        val result = otaConverter.findCommission(commissions,
                                                 1,
                                                 InventoryType.Payout,
                                                 1,
                                                 true,
                                                 hotelPrice = criteria,
                                                 rateType = RateType.NetExclusive)
        result must beNone
      }

      "return default commission when supplier is BCOM and metadata don't have commission and BSUP-973 is A" in {
        val commissions = Seq(commissionWithZeroValue).map(YplCommission(_))
        val result = otaConverter.findCommission(commissions,
                                                 1,
                                                 InventoryType.Payout,
                                                 1,
                                                 true,
                                                 true,
                                                 hotelPrice = criteria,
                                                 rateType = RateType.NetExclusive)
        result.getOrElse(errorCommissions).original must_== 16.60
      }

      "return metadata commission when supplier is BCOM and metadata have commission and commissions is empty and BSUP-973 is A" in {
        val commissions = Seq()
        val supplierCommissionMapping = Map(3038 -> 12.78, 332 -> 23.34)
        val result = otaConverter.findCommission(commissions,
                                                 1,
                                                 InventoryType.Payout,
                                                 1,
                                                 true,
                                                 true,
                                                 supplierCommissionMapping,
                                                 hotelPrice = criteria,
                                                 rateType = RateType.NetExclusive)
        result.getOrElse(errorCommissions).original must_== 12.78
      }

      "return AGX commission when isAgxEligibleDmc is true, rate is sell, payasyougo 1 + contractedComm 15 = 16" in {
        val commissions = Seq(commission).map(YplCommission(_))

        val result = otaConverter.findCommission(commissions,
                                                 1,
                                                 InventoryType.Payout,
                                                 1,
                                                 true,
                                                 false,
                                                 Map.empty,
                                                 0,
                                                 RateType.SellExclusive,
                                                 aValidYplAGXCommissionAdjustmentWrapperA,
                                                 true,
                                                 hotelPrice = criteria)
        result.getOrElse(errorCommissions).original must_== 16.0
      }
      "fallback to markup, when isAgxEligibleDmc is true, rate is sell, contractedComm 0, agxFenceRateChannel is true, excludedChannel match(isAgxEligibleRateChannel is false)" in {
        val commissions = Seq(commissionWithChannelAPO).map(YplCommission(_))

        val result = otaConverter.findCommission(
          commissions,
          Channel.APO,
          InventoryType.Payout,
          1,
          true,
          false,
          Map.empty,
          0,
          RateType.SellExclusive,
          aValidYplAGXCommissionAdjustmentWrapperB,
          true,
          hotelPrice = criteria,
        )
        result.getOrElse(errorCommissions).original must_== 20.0
        result.getOrElse(errorCommissions).agxCommission.payAsYouGoCommission must_== 0.0
      }
      "return contracted commission, agx comm 0, when isAgxEligibleDmc is true, rate is sell, agxFenceRateChannel is true, excludedChannel match(isAgxEligibleRateChannel is false)" in {
        val commissions = Seq(commissionWithChannelAPO.withContractedCommission(15d)).map(YplCommission(_))

        val result = otaConverter.findCommission(
          commissions,
          Channel.APO,
          InventoryType.Payout,
          1,
          true,
          false,
          Map.empty,
          0,
          RateType.SellExclusive,
          aValidYplAGXCommissionAdjustmentWrapperB,
          true,
          hotelPrice = criteria,
        )
        result.getOrElse(errorCommissions).original must_== 15.0
        result.getOrElse(errorCommissions).agxCommission.payAsYouGoCommission must_== 0.0
      }
      "return AGX commission(marked up) when isAgxEligibleDmc is true, rate is net" in {
        val commissions = Seq(commission).map(YplCommission(_))
        val result = otaConverter.findCommission(commissions,
                                                 1,
                                                 InventoryType.Payout,
                                                 1,
                                                 true,
                                                 false,
                                                 Map.empty,
                                                 0,
                                                 RateType.NetExclusive,
                                                 aValidYplAGXCommissionAdjustmentWrapperA,
                                                 true,
                                                 hotelPrice = criteria)
        val comm =
          commission.contractedCommission.get + aValidYplAGXCommissionAdjustment.get.allDateAdjust.get.payAsYouGoCommission
        val expected = comm / (1.0 - (comm / 100.0))
        result.getOrElse(errorCommissions).original must_== expected
      }
      "fallback to markup, no AGX, when isAgxEligibleDmc is true, rate is net, agxFenceRateChannel is true, excludedChannel match" in {
        val commissions = Seq(commissionWithChannelAPO).map(YplCommission(_))
        val result = otaConverter.findCommission(
          commissions,
          Channel.APO,
          InventoryType.Payout,
          1,
          true,
          false,
          Map.empty,
          0,
          RateType.NetExclusive,
          aValidYplAGXCommissionAdjustmentWrapperB,
          true,
          hotelPrice = criteria,
        )
        result.getOrElse(errorCommissions).original must_== 20.0
        result.getOrElse(errorCommissions).agxCommission.payAsYouGoCommission must_== 0.0
      }
      "return markup contracted commission, no agx comm, when isAgxEligibleDmc is true, rate is net, agxFenceRateChannel is true, excludedChannel match" in {
        val commissions = Seq(commissionWithChannelAPO.withContractedCommission(15d)).map(YplCommission(_))
        val result = otaConverter.findCommission(
          commissions,
          Channel.APO,
          InventoryType.Payout,
          1,
          true,
          false,
          Map.empty,
          0,
          RateType.NetExclusive,
          aValidYplAGXCommissionAdjustmentWrapperB,
          true,
          hotelPrice = criteria,
        )
        result.getOrElse(errorCommissions).original must_== 17.647058823529413
        result.getOrElse(errorCommissions).agxCommission.payAsYouGoCommission must_== 0.0
      }

      "return total commission when isAgxEligibleDmc is false" in {
        val commissions = Seq(commission).map(YplCommission(_))
        val result = otaConverter.findCommission(commissions,
                                                 1,
                                                 InventoryType.Payout,
                                                 1,
                                                 true,
                                                 false,
                                                 Map.empty,
                                                 0,
                                                 RateType.SellExclusive,
                                                 aValidYplAGXCommissionAdjustmentWrapperA,
                                                 false,
                                                 hotelPrice = criteria)
        result.getOrElse(errorCommissions).original must_== 20.0
      }
      "fallback to commission value(fallback to markup) when isAgxEligibleDmc is true and contractedCommission is zero" in {
        val commissions = Seq(commission.copy(contractedCommission = Some(0))).map(YplCommission(_))
        val result = otaConverter.findCommission(commissions,
                                                 1,
                                                 InventoryType.Payout,
                                                 1,
                                                 true,
                                                 false,
                                                 Map.empty,
                                                 0,
                                                 RateType.SellExclusive,
                                                 aValidYplAGXCommissionAdjustmentWrapperA,
                                                 true,
                                                 hotelPrice = criteria)
        result.getOrElse(errorCommissions).original must_== 20.0
      }

      "return AGX commission when supplier is Marriott, channel is Retail, and payment model is Merchant Commission" in {
        val ctx = aValidYplContext
        val commissions = Seq(commission).map(YplCommission(_))
        val marriottCriteria = criteria.copy(supplierId = DMC.Marriott)
        val result = otaConverter.findCommission(
          commissions,
          Channel.RTL,
          InventoryType.Payout,
          1,
          true,
          false,
          Map.empty,
          0,
          RateType.SellExclusive,
          aValidYplAGXCommissionAdjustmentWrapperA,
          true,
          hotelPrice = marriottCriteria,
          paymentModel = Some(PaymentModel.MerchantCommission),
        )(ctx)
        result.getOrElse(errorCommissions).original must_== 16.0
      }

      "return AGX commission when supplier is Marriott, channel is Priceline Merchant, and payment model is Merchant Commission" in {
        val ctx = aValidYplContext
        val commissions = Seq(commission.withChannelID(Channel.RestMerchant)).map(YplCommission(_))
        val marriottCriteria = criteria.copy(supplierId = DMC.Marriott)
        val result = otaConverter.findCommission(
          commissions,
          Channel.RestMerchant,
          InventoryType.Payout,
          1,
          true,
          false,
          Map.empty,
          0,
          RateType.SellExclusive,
          aValidYplAGXCommissionAdjustmentWrapperA,
          true,
          hotelPrice = marriottCriteria,
          paymentModel = Some(PaymentModel.MerchantCommission),
        )(ctx)
        result.getOrElse(errorCommissions).original must_== 16.0
      }

      "fallback to commission value when supplier is Marriott, channel is not Retail, and payment model is Merchant Commission" in {
        val ctx = aValidYplContext
        val commissions = Seq(commission.withChannelID(Channel.Mobile)).map(YplCommission(_))
        val marriottCriteria = criteria.copy(supplierId = DMC.Marriott)
        val result = otaConverter.findCommission(
          commissions,
          Channel.Mobile,
          InventoryType.Payout,
          1,
          true,
          false,
          Map.empty,
          0,
          RateType.SellExclusive,
          aValidYplAGXCommissionAdjustmentWrapperA,
          true,
          hotelPrice = marriottCriteria,
          paymentModel = Some(PaymentModel.MerchantCommission),
        )(ctx)
        result.getOrElse(errorCommissions).original must_== 20.0
      }

      "fallback to commission value when supplier is Marriott, channel is Retail, and payment model is not Merchant Commission" in {
        val ctx = aValidYplContext
        val commissions = Seq(commission).map(YplCommission(_))
        val marriottCriteria = criteria.copy(supplierId = DMC.Marriott)
        val result = otaConverter.findCommission(
          commissions,
          Channel.RTL,
          InventoryType.Payout,
          1,
          true,
          false,
          Map.empty,
          0,
          RateType.SellExclusive,
          aValidYplAGXCommissionAdjustmentWrapperA,
          true,
          hotelPrice = marriottCriteria,
          paymentModel = Some(PaymentModel.Merchant),
        )(ctx)
        result.getOrElse(errorCommissions).original must_== 20.0
      }

      "fallback to markup, when isAgxEligibleDmc is true, BedNetwork + StaticRate, (fence)payasyougo 0 + contractedComm 15 = 15" in {
        val commissions = Seq(commission.withChannelID(Channel.Bedbank)).map(YplCommission(_))

        val ctx = aValidYplContext
        val result = otaConverter.findCommission(
          commissions,
          Channel.Bedbank,
          InventoryType.Payout,
          1,
          true,
          false,
          Map.empty,
          0,
          RateType.SellExclusive,
          aValidYplAGXCommissionAdjustmentWrapperC,
          true,
          hotelPrice = criteria,
        )(ctx)
        result.getOrElse(errorCommissions).original must_== 15.0
        result.getOrElse(errorCommissions).agxCommission.payAsYouGoCommission must_== 0.0
      }
      "return AGX commission, when isAgxEligibleDmc is true, BedNetwork + DynamicRate, (unfence)payasyougo 1 + contractedComm 15 = 16" in {
        val commissions = Seq(commission.withChannelID(Channel.BedbankAffiliates)).map(YplCommission(_))

        val ctx = aValidYplContext
        val result = otaConverter.findCommission(
          commissions,
          Channel.BedbankAffiliates,
          InventoryType.Agoda,
          1,
          true,
          false,
          Map.empty,
          0,
          RateType.SellExclusive,
          aValidYplAGXCommissionAdjustmentWrapperC,
          true,
          hotelPrice = criteria,
        )(ctx)
        result.getOrElse(errorCommissions).original must_== 16.0
        result.getOrElse(errorCommissions).agxCommission.payAsYouGoCommission must_== 1.0
      }
    }

    "Build Tax And TaxInfo" should {

      "return ApplyOver.SaleEx when condition matched" in {
        val ctx = aValidYplContext
        val mohuGdsCommissionFeeSettings = new MOHUGdsCommissionFeeSettings(Set[Int] {
                                                                              1
                                                                            },
                                                                            Set[Int] {
                                                                              1234
                                                                            })
        val tax = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsFee(true)
          .withIsAmount(false)
          .withValue(30.0)
          .withIsTaxable(true)
          .withTaxPrototypeId(1234)
        val expectedTax = Tax(1, "PB", false, true, true, 30.0, ChargeOption.Mandatory, 1234)
        val (taxProtoWithId, result) =
          otaConverter.buildTaxLegacy(tax, false, mohuGdsCommissionFeeSettings, None, None, None)(ctx)

        taxProtoWithId._1 must_== 1
        taxProtoWithId._2 must_== 1234

        result.id must_== expectedTax.id
        result.applyTo must_== expectedTax.applyTo
        result.isAmount must_== expectedTax.isAmount
        result.isFee must_== expectedTax.isFee
        result.isTaxable must_== expectedTax.isTaxable
        result.value must_== expectedTax.value
        result.option must_== expectedTax.option
        result.protoTypeId must_== expectedTax.protoTypeId
        result.applyOver.get must_== ApplyTaxOver.SaleEx
      }

      "return variable tax with correct converted amount" in {
        val countryCurrency = Some("MYR")
        val hotelCurrency = Some("EUR")

        val overrideExchangeRate = ExchangeRate(local = "MYR", request = "EUR", toUsd = 0.22, toRequest = 1.03)
        val overrideExchangeRateContext = new ExchangeRateContext {
          override def getExchangeRate(from: Currency, to: Currency): Option[ExchangeRate] = Some(overrideExchangeRate)
        }
        val ctx = aValidYplContext.copy(exchangeRateCtx = overrideExchangeRateContext)

        val tax = aValidOTATaxAndFee
          .withApplyTo("PRPN")
          .withApplyType(ApplyType.VariableTax)
          .withIsFee(false)
          .withIsAmount(true)
          .withValue(10.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(1234)

        val (_, resultSameCurrency) =
          otaConverter.buildTaxLegacy(tax, false, mohuGdsCommissionFeeSettings, None, countryCurrency, countryCurrency)(
            ctx)
        val (_, resultDifferentCurrency) =
          otaConverter.buildTaxLegacy(tax, false, mohuGdsCommissionFeeSettings, None, countryCurrency, hotelCurrency)(
            ctx)

        resultSameCurrency.value must_== 10.0
        resultDifferentCurrency.value must_== overrideExchangeRate.toReqRate(10.0)
      }

      "return variable taxV2 with correct converted amount" in {
        val countryCurrency = Some("MYR")
        val hotelCurrency = Some("EUR")

        val overrideExchangeRate = ExchangeRate(local = "MYR", request = "EUR", toUsd = 0.22, toRequest = 1.03)
        val overrideExchangeRateContext = new ExchangeRateContext {
          override def getExchangeRate(from: Currency, to: Currency): Option[ExchangeRate] = Some(overrideExchangeRate)
        }
        val ctx = aValidYplContext.copy(exchangeRateCtx = overrideExchangeRateContext)

        val taxV2 = aValidOTATaxAndFeeV2Base
          .withApplyTo("PRPN")
          .withApplyType(ApplyType.VariableTax)
          .withIsFee(false)
          .withIsAmount(true)
          .withValue(10.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(1234)
          .withTaxCurrency("MYR")
        val expectedTax = Tax(1, "PRPN", true, false, false, 10.0, ChargeOption.VariableTax, 1234)

        val (_, resultSameCurrency) =
          otaConverter.buildTaxLegacyV2(taxV2, false, mohuGdsCommissionFeeSettings, None, countryCurrency)(ctx)
        val (_, resultDifferentCurrency) =
          otaConverter.buildTaxLegacyV2(taxV2, false, mohuGdsCommissionFeeSettings, None, hotelCurrency)(ctx)

        resultSameCurrency.value must_== 10.0
        resultDifferentCurrency.value must_== overrideExchangeRate.toReqRate(10.0)
      }

      "return None when condition is not matched" in {
        val ctx = aValidYplContext
        val mohuGdsCommissionFeeSettings = new MOHUGdsCommissionFeeSettings(Set[Int] {
                                                                              1
                                                                            },
                                                                            Set[Int] {
                                                                              1
                                                                            })
        val tax = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsFee(true)
          .withIsAmount(false)
          .withValue(30.0)
          .withIsTaxable(true)
          .withTaxPrototypeId(0)
        val expectedTax = Tax(1, "PB", false, true, true, 30.0, ChargeOption.Mandatory, 0)
        val (taxProtoWithId, result) =
          otaConverter.buildTaxLegacy(tax, false, mohuGdsCommissionFeeSettings, None, None, None)(ctx)

        taxProtoWithId._1 must_== 1
        taxProtoWithId._2 must_== 0

        result.id must_== expectedTax.id
        result.applyTo must_== expectedTax.applyTo
        result.isAmount must_== expectedTax.isAmount
        result.isFee must_== expectedTax.isFee
        result.isTaxable must_== expectedTax.isTaxable
        result.value must_== expectedTax.value
        result.option must_== expectedTax.option
        result.protoTypeId must_== expectedTax.protoTypeId
        result.applyOver must_== None
      }

      "return correct tax with correct charge option" in {
        val ctx = aValidYplContext
        val tax = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsFee(true)
          .withIsAmount(false)
          .withValue(30.0)
          .withIsTaxable(true)
          .withTaxPrototypeId(0)
        val expectedTax = Tax(1, "PB", false, true, true, 30.0, ChargeOption.Mandatory, 0)
        val (taxProtoWithId, result) =
          otaConverter.buildTaxLegacy(tax, false, mohuGdsCommissionFeeSettings, None, None, None)(ctx)

        taxProtoWithId._1 must_== 1
        taxProtoWithId._2 must_== 0

        result.id must_== expectedTax.id
        result.applyTo must_== expectedTax.applyTo
        result.isAmount must_== expectedTax.isAmount
        result.isFee must_== expectedTax.isFee
        result.isTaxable must_== expectedTax.isTaxable
        result.value must_== expectedTax.value
        result.option must_== expectedTax.option
        result.protoTypeId must_== expectedTax.protoTypeId
      }

      "return correct tax info with single channel room rate - A Variant, TaxV2 is empty" in {
        val ctx = aValidYplContext
        val taxAndFee = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(10.0)
          .withIsTaxable(false)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFee))
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map((1, 0) -> Tax(1, "PRPB", false, false, false, 10.0, ChargeOption.Mandatory, 0))
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)
        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(taxType,
                                               false,
                                               Seq(channelRoomRate),
                                               false,
                                               false,
                                               mohuGdsCommissionFeeSettings,
                                               None,
                                               None,
                                               hotelMeta,
                                               1)(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with single channel room rate - B Variant, TaxAndFee V2 is empty (VYG-323 Exp should not be allocated)" in {
        val ctx =
          aValidYplContext.copy(request = aValidYplRequest).withExperimentContext(forceAllBExperimentsContext()).build
        val taxAndFee = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(10.0)
          .withIsTaxable(false)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFee))
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map((1, 0) -> Tax(1, "PRPB", false, false, false, 10.0, ChargeOption.Mandatory, 0))
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)
        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(taxType,
                                               false,
                                               Seq(channelRoomRate),
                                               false,
                                               false,
                                               mohuGdsCommissionFeeSettings,
                                               None,
                                               None,
                                               hotelMeta,
                                               1)(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with single channel room rate - B Variant, TaxAndFee V2 is given (VYG-323 Exp should be allocated as true)" in {
        val ctx = aValidYplContext
          .withExperimentContext(forceAllBExperimentsContext())
          .build
          .copy(request = aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings)))
        val taxAndFee = aValidOTATaxAndFeeV2
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(10.0)
          .withIsTaxable(false)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFeeV2(Seq(taxAndFee))
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 52) -> Tax(
            1,
            "PRPB",
            false,
            false,
            false,
            10.0,
            ChargeOption.Mandatory,
            52,
            None,
            Some(ApplyTaxOver.MarginCommission),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ))
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)
        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0, stateId = Some(1))
        val result = otaConverter.buildTaxInfo(taxType,
                                               false,
                                               Seq(channelRoomRate),
                                               false,
                                               false,
                                               mohuGdsCommissionFeeSettings,
                                               None,
                                               None,
                                               hotelMeta,
                                               1)(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with 2 channel room rates and different tax id" in {
        val ctx = aValidYplContext
        val taxAndFeeWithIdOne = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(10.0)
          .withIsTaxable(false)
          .build
        val taxAndFeeWithIdTwo = taxAndFeeWithIdOne.withTaxId(2).build
        val taxAndFees = Seq(taxAndFeeWithIdOne, taxAndFeeWithIdTwo)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(
            Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithIdOne))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 0) -> Tax(1, "PRPB", false, false, false, 10.0, ChargeOption.Mandatory, 0),
          (2, 0) -> Tax(2, "PRPB", false, false, false, 10.0, ChargeOption.Mandatory, 0),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(taxType,
                                               false,
                                               channelRoomRatesInput,
                                               false,
                                               false,
                                               mohuGdsCommissionFeeSettings,
                                               None,
                                               None,
                                               hotelMeta,
                                               1)(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with 2 channel room rates and different tax id - B Variant (VYG-323 Exp should be allocated as true)" in {
        val ctx = aValidYplContext
          .withExperimentContext(forceAllBExperimentsContext())
          .build
          .copy(request = aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings)))
        val taxAndFeeWithIdOne = aValidOTATaxAndFeeV2
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(10.0)
          .withIsTaxable(false)
        val taxAndFeeWithIdTwo = taxAndFeeWithIdOne.withTaxId(2)
        val taxAndFees = Seq(taxAndFeeWithIdOne, taxAndFeeWithIdTwo)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFeeV2(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFeeV2(Seq(taxAndFeeWithIdOne))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 52) -> Tax(
            1,
            "PRPB",
            false,
            false,
            false,
            10.0,
            ChargeOption.Mandatory,
            52,
            None,
            Some(ApplyTaxOver.MarginCommission),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
          (2, 52) -> Tax(
            2,
            "PRPB",
            false,
            false,
            false,
            10.0,
            ChargeOption.Mandatory,
            52,
            None,
            Some(ApplyTaxOver.MarginCommission),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0, stateId = Some(1))
        val result = otaConverter.buildTaxInfo(taxType,
                                               false,
                                               channelRoomRatesInput,
                                               false,
                                               false,
                                               mohuGdsCommissionFeeSettings,
                                               None,
                                               None,
                                               hotelMeta,
                                               1)(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax with correct charge option when hotel is bcom" in {
        val ctx = aValidYplContext
        val tax = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsFee(true)
          .withIsAmount(true)
          .withValue(30.0)
          .withIsTaxable(true)
          .withTaxPrototypeId(0)
        val expectedTax = Tax(1, "PRPB", true, true, true, 30.0, ChargeOption.Mandatory, 0)

        val (taxProtoWithId, result) =
          otaConverter.buildTaxLegacy(tax, true, mohuGdsCommissionFeeSettings, None, None, None)(ctx)

        taxProtoWithId._1 must_== 1
        taxProtoWithId._2 must_== 0

        result.id must_== expectedTax.id
        result.applyTo must_== "PRPB"
        result.isAmount must_== expectedTax.isAmount
        result.isFee must_== expectedTax.isFee
        result.isTaxable must_== expectedTax.isTaxable
        result.value must_== expectedTax.value
        result.option must_== expectedTax.option
        result.protoTypeId must_== expectedTax.protoTypeId
      }

      "return correct tax with correct tax prototype override" in {
        val ctx = aValidYplContext
        val tax = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsFee(true)
          .withIsAmount(false)
          .withValue(0.0)
          .withIsTaxable(true)
          .withTaxPrototypeId(111)
        val expectedTax =
          Tax(1, "PB", false, true, true, 0.0, ChargeOption.Mandatory, 111, Some(aValidOTATaxPrototypeInfo_111))
        val (taxProtoWithId, result) = otaConverter.buildTaxLegacy(tax,
                                                                   false,
                                                                   mohuGdsCommissionFeeSettings,
                                                                   Some(aValidOTATaxPrototypeInfo_111),
                                                                   None,
                                                                   None)(ctx)

        taxProtoWithId._1 must_== 1
        taxProtoWithId._2 must_== 111

        result.id must_== expectedTax.id
        result.applyTo must_== expectedTax.applyTo
        result.isAmount must_== expectedTax.isAmount
        result.isFee must_== expectedTax.isFee
        result.isTaxable must_== expectedTax.isTaxable
        result.value must_== expectedTax.value
        result.option must_== expectedTax.option
        result.protoTypeId must_== expectedTax.protoTypeId
        result.taxPrototypeInfo must_== expectedTax.taxPrototypeInfo
      }

      "return correct tax info with correct tax prototype level percentage - A Variant" in {
        val ctx = aValidYplContext
        val countryCurrency = Some("USD")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .build
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222).build
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(333).build
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444).build
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId.build
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(1,
                          "PRPB",
                          false,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          111,
                          Some(aValidOTATaxPrototypeInfo_111)),
          (2, 222) -> Tax(2,
                          "PRPB",
                          false,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          222,
                          Some(aValidOTATaxPrototypeInfo_222)),
          (3, 333) -> Tax(3, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 333, Some(TaxPrototypeInfo(List()))),
          (4, 444) -> Tax(4, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 444, Some(TaxPrototypeInfo(List()))),
          (5, 0) -> Tax(5, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 0, None),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(
          taxType,
          false,
          channelRoomRatesInput,
          false,
          false,
          mohuGdsCommissionFeeSettings,
          taxPrototypeLevelList = aValidOTATaxPrototypeLevelMap,
          countryCurrency,
          hotelMeta,
          1,
        )(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with correct tax prototype level amount - A Variant" in {
        val ctx = aValidYplContext
        val countryCurrency = Some("USD")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(true)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .build
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222).build
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(333).build
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444).build
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId.build
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(1,
                          "PRPB",
                          true,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          111,
                          Some(aValidOTATaxPrototypeInfo_111)),
          (2, 222) -> Tax(2,
                          "PRPB",
                          true,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          222,
                          Some(aValidOTATaxPrototypeInfo_222)),
          (3, 333) -> Tax(3, "PRPB", true, false, false, 0.0, ChargeOption.Mandatory, 333, Some(TaxPrototypeInfo(List()))),
          (4, 444) -> Tax(4, "PRPB", true, false, false, 0.0, ChargeOption.Mandatory, 444, Some(TaxPrototypeInfo(List()))),
          (5, 0) -> Tax(5, "PRPB", true, false, false, 0.0, ChargeOption.Mandatory, 0, None),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(
          taxType,
          false,
          channelRoomRatesInput,
          false,
          false,
          mohuGdsCommissionFeeSettings,
          taxPrototypeLevelList = aValidOTATaxPrototypeLevelMap,
          countryCurrency,
          hotelMeta,
          1,
        )(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with correct tax prototype level percentage - B Variant (VYG-323 Exp should be allocated as true)" in {
        val ctx = aValidYplContext
          .withExperimentContext(forceAllBExperimentsContext())
          .build
          .copy(request = aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings)))
        val countryCurrency = Some("USD")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFeeV2
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .withTaxApplyBreakdownType(TaxApplyBreakdownType.NetEx)
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222)
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(333)
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444)
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFeeV2(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFeeV2(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(
            1,
            "PRPB",
            false,
            false,
            false,
            0.0,
            ChargeOption.Mandatory,
            111,
            Some(aValidOTATaxPrototypeInfo_111_v2),
            Some(ApplyTaxOver.NetEx),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
          (2, 222) -> Tax(
            2,
            "PRPB",
            false,
            false,
            false,
            0.0,
            ChargeOption.Mandatory,
            222,
            Some(aValidOTATaxPrototypeInfo_222_v2),
            Some(ApplyTaxOver.NetEx),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
          (3, 333) -> Tax(
            3,
            "PRPB",
            false,
            false,
            false,
            0.0,
            ChargeOption.Mandatory,
            333,
            Some(TaxPrototypeInfo(List())),
            Some(ApplyTaxOver.NetEx),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
          (4, 444) -> Tax(
            4,
            "PRPB",
            false,
            false,
            false,
            0.0,
            ChargeOption.Mandatory,
            444,
            Some(TaxPrototypeInfo(List())),
            Some(ApplyTaxOver.NetEx),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
          (5, 0) -> Tax(
            5,
            "PRPB",
            false,
            false,
            false,
            0.0,
            ChargeOption.Mandatory,
            0,
            None,
            Some(ApplyTaxOver.NetEx),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0, stateId = Some(1))
        val result = otaConverter.buildTaxInfo(
          taxType,
          false,
          channelRoomRatesInput,
          false,
          false,
          mohuGdsCommissionFeeSettings,
          taxPrototypeLevelList = aValidOTATaxPrototypeLevelMapV2,
          countryCurrency,
          hotelMeta,
          1,
        )(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with correct tax prototype level amount - B Variant (VYG-323 Exp should be allocated as true)" in {
        val ctx = aValidYplContext
          .copy(request = aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings)),
                experimentContext = forceAllBExperimentsContext())
          .build
        val countryCurrency = Some("USD")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFeeV2
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(true)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .withTaxApplyBreakdownType(TaxApplyBreakdownType.NetEx)
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222)
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(333)
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444)
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFeeV2(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFeeV2(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(
            1,
            "PRPB",
            true,
            false,
            false,
            0.0,
            ChargeOption.Mandatory,
            111,
            Some(aValidOTATaxPrototypeInfo_111_v2),
            Some(ApplyTaxOver.NetEx),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
          (2, 222) -> Tax(
            2,
            "PRPB",
            true,
            false,
            false,
            0.0,
            ChargeOption.Mandatory,
            222,
            Some(aValidOTATaxPrototypeInfo_222_v2),
            Some(ApplyTaxOver.NetEx),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
          (3, 333) -> Tax(
            3,
            "PRPB",
            true,
            false,
            false,
            0.0,
            ChargeOption.Mandatory,
            333,
            Some(TaxPrototypeInfo(List())),
            Some(ApplyTaxOver.NetEx),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
          (4, 444) -> Tax(
            4,
            "PRPB",
            true,
            false,
            false,
            0.0,
            ChargeOption.Mandatory,
            444,
            Some(TaxPrototypeInfo(List())),
            Some(ApplyTaxOver.NetEx),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
          (5, 0) -> Tax(
            5,
            "PRPB",
            true,
            false,
            false,
            0.0,
            ChargeOption.Mandatory,
            0,
            None,
            Some(ApplyTaxOver.NetEx),
            Some(WhomToPayType.Government),
            Some(1),
            Some(TaxLevelCalculationType.FlatRate),
          ),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0, stateId = Some(1))
        val result = otaConverter.buildTaxInfo(
          taxType,
          false,
          channelRoomRatesInput,
          false,
          false,
          mohuGdsCommissionFeeSettings,
          taxPrototypeLevelList = aValidOTATaxPrototypeLevelMapV2,
          countryCurrency,
          hotelMeta,
          1,
        )(ctx)
        result must_== expectedTaxInfo
      }

      "MOHUGdsCommissionFee : return correct tax info if the pre condition experiment is true" in {
        val countryCurrency = Some("USD")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .build
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222).build
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(123456).build
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444).build
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId.build
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(1,
                          "PRPB",
                          false,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          111,
                          Some(aValidOTATaxPrototypeInfo_111)),
          (2, 222) -> Tax(2,
                          "PRPB",
                          false,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          222,
                          Some(aValidOTATaxPrototypeInfo_222)),
          (3, 123456) -> Tax(3,
                             "PRPB",
                             false,
                             false,
                             false,
                             0.0,
                             ChargeOption.Mandatory,
                             123456,
                             Some(TaxPrototypeInfo(List())),
                             Some(SaleEx)),
          (4, 444) -> Tax(4, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 444, Some(TaxPrototypeInfo(List()))),
          (5, 0) -> Tax(5, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 0, None),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          cInfo = YplClientInfo(cid = Option[Int] {
            1234
          }),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val mockedMOHUGdsCommissionFeeSettings = new MOHUGdsCommissionFeeSettings(Set[Int] {
                                                                                    1234
                                                                                  },
                                                                                  Set[Int] {
                                                                                    123456
                                                                                  })
        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(taxType,
                                               false,
                                               channelRoomRatesInput,
                                               false,
                                               true,
                                               mockedMOHUGdsCommissionFeeSettings,
                                               aValidOTATaxPrototypeLevelMap,
                                               countryCurrency,
                                               hotelMeta,
                                               1)
        result must_== expectedTaxInfo
      }

      "MOHUGdsCommissionFee : return correct tax info if the pre condition experiment is false" in {
        val countryCurrency = Some("USD")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .build
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222).build
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(123456).build
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444).build
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId.build
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(1,
                          "PRPB",
                          false,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          111,
                          Some(aValidOTATaxPrototypeInfo_111)),
          (2, 222) -> Tax(2,
                          "PRPB",
                          false,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          222,
                          Some(aValidOTATaxPrototypeInfo_222)),
          (4, 444) -> Tax(4, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 444, Some(TaxPrototypeInfo(List()))),
          (5, 0) -> Tax(5, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 0, None),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          cInfo = YplClientInfo(cid = Option[Int] {
            1234
          }),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val mockedMOHUGdsCommissionFeeSettings = new MOHUGdsCommissionFeeSettings(Set[Int] {
                                                                                    1
                                                                                  },
                                                                                  Set[Int] {
                                                                                    123456
                                                                                  })

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(taxType,
                                               false,
                                               channelRoomRatesInput,
                                               false,
                                               false,
                                               mockedMOHUGdsCommissionFeeSettings,
                                               aValidOTATaxPrototypeLevelMap,
                                               countryCurrency,
                                               hotelMeta,
                                               1)
        result must_== expectedTaxInfo
      }

      "return correct tax info with empty tax prototype level in case currency match (Country = None, Hotel = USD)" in {
        val ctx = aValidYplContext
        val countryCurrency = None
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .build
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222).build
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(333).build
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444).build
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId.build
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(1, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 111, Some(TaxPrototypeInfo(List()))),
          (2, 222) -> Tax(2, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 222, Some(TaxPrototypeInfo(List()))),
          (3, 333) -> Tax(3, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 333, Some(TaxPrototypeInfo(List()))),
          (4, 444) -> Tax(4, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 444, Some(TaxPrototypeInfo(List()))),
          (5, 0) -> Tax(5, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 0, None),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(
          taxType,
          false,
          channelRoomRatesInput,
          false,
          false,
          mohuGdsCommissionFeeSettings,
          taxPrototypeLevelList = aValidOTATaxPrototypeLevelMap,
          countryCurrency,
          hotelMeta,
          1,
        )(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with empty tax prototype level in case currency match (Country = USD, Hotel = None)" in {
        val ctx = aValidYplContext
        val countryCurrency = Some("USD")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .build
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222).build
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(333).build
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444).build
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId.build
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate
          .withCurrencyCode(None)
          .withRateCategories(
            Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(1, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 111, Some(TaxPrototypeInfo(List()))),
          (2, 222) -> Tax(2, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 222, Some(TaxPrototypeInfo(List()))),
          (3, 333) -> Tax(3, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 333, Some(TaxPrototypeInfo(List()))),
          (4, 444) -> Tax(4, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 444, Some(TaxPrototypeInfo(List()))),
          (5, 0) -> Tax(5, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 0, None),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(
          taxType,
          false,
          channelRoomRatesInput,
          false,
          false,
          mohuGdsCommissionFeeSettings,
          taxPrototypeLevelList = aValidOTATaxPrototypeLevelMap,
          countryCurrency,
          hotelMeta,
          1,
        )(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with correct tax prototype level in case currency match (Country = USD, Hotel = USD)" in {
        val ctx = aValidYplContext
        val countryCurrency = Some("USD")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .build
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222).build
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(333).build
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444).build
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId.build
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate.withRateCategories(
          Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(1,
                          "PRPB",
                          false,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          111,
                          Some(aValidOTATaxPrototypeInfo_111)),
          (2, 222) -> Tax(2,
                          "PRPB",
                          false,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          222,
                          Some(aValidOTATaxPrototypeInfo_222)),
          (3, 333) -> Tax(3, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 333, Some(TaxPrototypeInfo(List()))),
          (4, 444) -> Tax(4, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 444, Some(TaxPrototypeInfo(List()))),
          (5, 0) -> Tax(5, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 0, None),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(
          taxType,
          false,
          channelRoomRatesInput,
          false,
          false,
          mohuGdsCommissionFeeSettings,
          taxPrototypeLevelList = aValidOTATaxPrototypeLevelMap,
          countryCurrency,
          hotelMeta,
          1,
        )(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with correct percentage tax prototype level in case currency diff (Country = USD, Hotel = THB)" in {
        val usdToThb = 30.0
        val mockExchangeRateContext = new ExchangeRateContext {
          override def getExchangeRate(from: Currency, to: Currency): Option[ExchangeRate] =
            (from.toUpperCase, to.toUpperCase) match {
              case ("USD", "THB") => Some(
                  ExchangeRate(
                    local = "USD",
                    request = "THB",
                    toUsd = 1.0,
                    toRequest = usdToThb,
                  ))
              case _ => None
            }
        }
        val ctx = aValidYplContext.withExchangeRateContext(mockExchangeRateContext)
        val countryCurrency = Some("USD")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(false)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .build
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222).build
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(333).build
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444).build
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId.build
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate
          .withCurrencyCode(Some("THB"))
          .withRateCategories(
            Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val expectedOTATaxPrototypeInfo_111_THB_level = TaxPrototypeInfo(
          taxPrototypeLevels = List(
            TaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 30000, taxValue = 10),
            TaxPrototypeLevel(level = 2, rateStart = 30000, rateEnd = 299970, taxValue = 20),
          ),
        )
        val expectedOTATaxPrototypeInfo_222_THB_level = TaxPrototypeInfo(
          taxPrototypeLevels = List(
            TaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 299970, taxValue = 15),
          ),
        )

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(1,
                          "PRPB",
                          false,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          111,
                          Some(expectedOTATaxPrototypeInfo_111_THB_level)),
          (2, 222) -> Tax(2,
                          "PRPB",
                          false,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          222,
                          Some(expectedOTATaxPrototypeInfo_222_THB_level)),
          (3, 333) -> Tax(3, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 333, Some(TaxPrototypeInfo(List()))),
          (4, 444) -> Tax(4, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 444, Some(TaxPrototypeInfo(List()))),
          (5, 0) -> Tax(5, "PRPB", false, false, false, 0.0, ChargeOption.Mandatory, 0, None),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(
          taxType,
          false,
          channelRoomRatesInput,
          false,
          false,
          mohuGdsCommissionFeeSettings,
          taxPrototypeLevelList = aValidOTATaxPrototypeLevelMap,
          countryCurrency,
          hotelMeta,
          1,
        )(ctx)
        result must_== expectedTaxInfo
      }

      "return correct tax info with correct amount tax prototype level in case currency diff (Country = USD, Hotel = THB)" in {
        val usdToThb = 30.0
        val mockExchangeRateContext = new ExchangeRateContext {
          override def getExchangeRate(from: Currency, to: Currency): Option[ExchangeRate] =
            (from.toUpperCase, to.toUpperCase) match {
              case ("USD", "THB") => Some(
                  ExchangeRate(
                    local = "USD",
                    request = "THB",
                    toUsd = 1.0,
                    toRequest = usdToThb,
                  ))
              case _ => None
            }
        }
        val countryCurrency = Some("USD")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(true)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .build
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222).build
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(333).build
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444).build
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId.build
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate
          .withCurrencyCode(Some("THB"))
          .withRateCategories(
            Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val expectedOTATaxPrototypeInfo_111_THB_level = TaxPrototypeInfo(
          taxPrototypeLevels = List(
            TaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 30000, taxValue = 300),
            TaxPrototypeLevel(level = 2, rateStart = 30000, rateEnd = 299970, taxValue = 600),
          ),
        )
        val expectedOTATaxPrototypeInfo_222_THB_level = TaxPrototypeInfo(
          taxPrototypeLevels = List(
            TaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 299970, taxValue = 450),
          ),
        )

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(1,
                          "PRPB",
                          true,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          111,
                          Some(expectedOTATaxPrototypeInfo_111_THB_level)),
          (2, 222) -> Tax(2,
                          "PRPB",
                          true,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          222,
                          Some(expectedOTATaxPrototypeInfo_222_THB_level)),
          (3, 333) -> Tax(3, "PRPB", true, false, false, 0.0, ChargeOption.Mandatory, 333, Some(TaxPrototypeInfo(List()))),
          (4, 444) -> Tax(4, "PRPB", true, false, false, 0.0, ChargeOption.Mandatory, 444, Some(TaxPrototypeInfo(List()))),
          (5, 0) -> Tax(5, "PRPB", true, false, false, 0.0, ChargeOption.Mandatory, 0, None),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val aValidBYplContext = aValidYplContext.copy(
          exchangeRateCtx = mockExchangeRateContext,
        )
        val result = otaConverter.buildTaxInfo(
          taxType,
          false,
          channelRoomRatesInput,
          false,
          false,
          mohuGdsCommissionFeeSettings,
          taxPrototypeLevelList = aValidOTATaxPrototypeLevelMap,
          countryCurrency,
          hotelMeta,
          1,
        )(aValidBYplContext)
        result must_== expectedTaxInfo
      }
    }

    "Build RoomTypeEntry" should {
      "return default room type entry when no room type info found" in {
        val channelRoomRate = aValidOTAChannelRoomRate.withRoomTypeId(1)
        val expectedRoomTypeEntry = RoomTypeEntry(
          roomTypeId = 1,
          maxOccupancy = 0,
          maxExtraBed = 0,
          maxChildrenInRoom = 0,
          isDayUse = false,
          cutOffDays = 0,
          gaCutOffDays = 0,
          dmcRoomId = None,
        )

        val result = otaConverter.buildRoomType(channelRoomRate, None)
        result must_== expectedRoomTypeEntry
      }

      "return correct tax info with correct amount tax prototype level in case currency diff (Country = INR, Hotel = THB)" in {
        val inrToUsd = 0.013
        val usdToThb = 31.17
        val mockExchangeRateContext = new ExchangeRateContext {
          override def getExchangeRate(from: Currency, to: Currency): Option[ExchangeRate] =
            (from.toUpperCase, to.toUpperCase) match {
              case ("INR", "THB") => Some(
                  ExchangeRate(
                    local = "INR",
                    request = "THB",
                    toUsd = inrToUsd,
                    toRequest = usdToThb,
                  ))
              case _ => None
            }
        }
        val ctx = aValidYplContext
          .withExchangeRateContext(mockExchangeRateContext)
          .withExperimentContext(forceAllBExperimentsContext())
        val countryCurrency = Some("INR")
        val taxAndFeeWithProtoId_111 = aValidOTATaxAndFee
          .withTaxId(1)
          .withApplyTo("PRPB")
          .withIsFee(false)
          .withIsAmount(true)
          .withValue(0.0)
          .withIsTaxable(false)
          .withTaxPrototypeId(111)
          .build
        val taxAndFeeWithProtoId_222 = taxAndFeeWithProtoId_111.withTaxId(2).withTaxPrototypeId(222).build
        val taxAndFeeWithProtoId_333 = taxAndFeeWithProtoId_111.withTaxId(3).withTaxPrototypeId(333).build
        val taxAndFeeWithProtoId_444 = taxAndFeeWithProtoId_111.withTaxId(4).withTaxPrototypeId(444).build
        val taxAndFeeWithoutProtoId = taxAndFeeWithProtoId_111.withTaxId(5).clearTaxPrototypeId.build
        val taxAndFees = Seq(taxAndFeeWithProtoId_111,
                             taxAndFeeWithProtoId_222,
                             taxAndFeeWithProtoId_333,
                             taxAndFeeWithProtoId_444,
                             taxAndFeeWithoutProtoId)
        val occupancyPriceTest = aValidOTAOccupancyPrice.withTaxAndFee(taxAndFees)
        val channelRoomRateBase = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory.withDailyPrices(
          Seq(aValidOTADailyPrice.withPrices(Seq(aValidOTAOccupancyPrice.withTaxAndFee(Seq(taxAndFeeWithProtoId_111))))))))
        val channelRoomRateWithNewTaxAndFee = aValidOTAChannelRoomRate
          .withCurrencyCode(Some("THB"))
          .withRateCategories(
            Seq(aValidOTARateCategory.withDailyPrices(Seq(aValidOTADailyPrice.withPrices(Seq(occupancyPriceTest))))))
        val channelRoomRatesInput: Seq[ChannelRoomRate] = Seq(channelRoomRateBase, channelRoomRateWithNewTaxAndFee)

        val expectedOTATaxPrototypeInfo_111_THB_level = TaxPrototypeInfo(
          taxPrototypeLevels = List(
            TaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 405.21000000000004, taxValue = 4.0521),
            TaxPrototypeLevel(level = 2, rateStart = 405.21000000000004, rateEnd = 4051.69479, taxValue = 8.1042),
          ),
        )
        val expectedOTATaxPrototypeInfo_222_THB_level = TaxPrototypeInfo(
          taxPrototypeLevels = List(
            TaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 4051.69479, taxValue = 6.07815),
          ),
        )

        val taxType = TaxType.SimpleTax
        val expectedTaxes = Map(
          (1, 111) -> Tax(1,
                          "PRPB",
                          true,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          111,
                          Some(expectedOTATaxPrototypeInfo_111_THB_level)),
          (2, 222) -> Tax(2,
                          "PRPB",
                          true,
                          false,
                          false,
                          0.0,
                          ChargeOption.Mandatory,
                          222,
                          Some(expectedOTATaxPrototypeInfo_222_THB_level)),
          (3, 333) -> Tax(3, "PRPB", true, false, false, 0.0, ChargeOption.Mandatory, 333, Some(TaxPrototypeInfo(List()))),
          (4, 444) -> Tax(4, "PRPB", true, false, false, 0.0, ChargeOption.Mandatory, 444, Some(TaxPrototypeInfo(List()))),
          (5, 0) -> Tax(5, "PRPB", true, false, false, 0.0, ChargeOption.Mandatory, 0, None),
        )
        val expectedHotelTaxInfo = HotelTaxInfo(taxType = taxType, isConfigProcessingFees = false)
        val expectedTaxInfo = TaxInfo(hotelTaxInfo = expectedHotelTaxInfo, expectedTaxes)

        val hotelMeta = HotelMeta(hotelId = 1, cityId = 0)
        val result = otaConverter.buildTaxInfo(
          taxType,
          false,
          channelRoomRatesInput,
          false,
          false,
          mohuGdsCommissionFeeSettings,
          taxPrototypeLevelList = aValidOTATaxPrototypeLevelMap,
          countryCurrency,
          hotelMeta,
          1,
        )(ctx)
        print("test")
        result must_== expectedTaxInfo
      }
    }

    "Build RoomTypeEntry" should {
      "return default room type entry when no room type info found" in {
        val channelRoomRate = aValidOTAChannelRoomRate.withRoomTypeId(1)
        val expectedRoomTypeEntry = RoomTypeEntry(
          roomTypeId = 1,
          maxOccupancy = 0,
          maxExtraBed = 0,
          maxChildrenInRoom = 0,
          isDayUse = false,
          cutOffDays = 0,
          gaCutOffDays = 0,
          dmcRoomId = None,
        )

        val result = otaConverter.buildRoomType(channelRoomRate, None)
        result must_== expectedRoomTypeEntry
      }

      "return correct room type entry when given room type proto when only room type id and max occupancy supplied" in {
        val roomType = aValidOTARoomType.withRoomTypeId(1).withMaxOccupancy(2)
        val channelRoomRate = aValidOTAChannelRoomRate.withRoomTypeId(1)
        val expectedRoomTypeEntry = RoomTypeEntry(
          roomTypeId = 1,
          maxOccupancy = 2,
          maxExtraBed = 0,
          maxChildrenInRoom = 0,
          isDayUse = false,
          cutOffDays = 0,
          gaCutOffDays = 0,
          dmcRoomId = Some("STD"),
        )

        val result = otaConverter.buildRoomType(channelRoomRate, Option(roomType))
        result must_== expectedRoomTypeEntry
      }

      "return correct room type entry when given room type proto when all fields supplied" in {
        val roomType = aValidOTARoomType
          .withRoomTypeId(1)
          .withMaxOccupancy(2)
          .withMaxExtrabed(1)
          .withMaxChildren(1)
          .withSupplierRoomId("SUP")
          .withSupplierRatePlanId("Breakfast")
        val channelRoomRate = aValidOTAChannelRoomRate.withRoomTypeId(1)
        val expectedRoomTypeEntry = RoomTypeEntry(
          roomTypeId = 1,
          maxOccupancy = 2,
          maxExtraBed = 0,
          maxChildrenInRoom = 1,
          isDayUse = false,
          cutOffDays = 0,
          gaCutOffDays = 0,
          dmcRoomId = Option("SUP"),
        )

        val result = otaConverter.buildRoomType(channelRoomRate, Option(roomType))
        result must_== expectedRoomTypeEntry
      }
    }

    "Build RestrictionEntry" should {
      "Build RateCategory Booking Restriction Input" should {

        trait BuildRateCategoryRestrictionScope extends Scope {
          val bookingIntervals = Seq(
            aValidOTARateCategoryBookingRestrictionDayPeriodInterval
              .withFrom(1497286800000L)
              .withTo(4102333200000L)
              .build,
            aValidOTARateCategoryBookingRestrictionTimePeriodInterval
              .withFrom(1524416400000L)
              .withTo(1524502799000L)
              .build,
          )
          val customerSegments = Seq(
            aValidOTARateCategoryBookingRestrictionCustomerSegment.withLanguageId(1).withCountryCode("USA").build,
            aValidOTARateCategoryBookingRestrictionCustomerSegment
              .withLanguageId(0)
              .withCountryCode("00")
              .withVipLevel(4)
              .build,
          )
          val bookingRestriction = aValidOTARateCategoryBookingRestriction
            .withBookOn(Option("1111111"))
            .withMinAdvance(Option(1))
            .withMaxAdvance(Option(1))
            .withCustomerSegments(customerSegments)
            .withBookingIntervals(bookingIntervals)
            .build

          val expectedPeriodIntervalInput = List(
            PeriodIntervalInput(1, 1497286800000L, 4102333200000L),
            PeriodIntervalInput(2, 1524416400000L, 1524502799000L),
          )
          val expectedCustomerSegment = List(
            CustomerSegment(Option(1), Option("USA"), None),
            CustomerSegment(Some(0), Some("00"), Some(VipLevelType.PLATINUM)),
          )
          val expectedRestrictionEntryModel = RestrictionEntryModel(
            bookingIntervals = expectedPeriodIntervalInput,
            bookOn = Option("1111111"),
            minAdvance = Option(1),
            maxAdvance = Option(1),
            customerSegments = expectedCustomerSegment,
            minRooms = None,
            minNights = None,
          )
        }

        "return restriction input with all fields supplied and isAllowedCustomerSegmentsRestriction is true" in new BuildRateCategoryRestrictionScope {
          val result = otaConverter.buildRateCategoryBookingRestrictionInput(Option(bookingRestriction),
                                                                             isAllowedCustomerSegmentsRestriction = true)
          result must_== Option(expectedRestrictionEntryModel)
        }

        "return restriction input with all fields supplied" in new BuildRateCategoryRestrictionScope {
          val result = otaConverter.buildRateCategoryBookingRestrictionInput(Option(bookingRestriction))
          result must_== Option(expectedRestrictionEntryModel.copy(customerSegments = Nil))
        }
      }

      "Build Promotion Restriction Input" should {
        "return correct booking restriction" in {
          val bookingIntervals = Seq(
            PromotionRestrictionPeriodInterval(PromotionRestrictionPeriodType.Day, 1497286800000L, 4102333200000L),
            PromotionRestrictionPeriodInterval(PromotionRestrictionPeriodType.Time, 1524416400000L, 1524502799000L),
          )
          val customerSegments = Seq(PromotionRestrictionCustomerSegment(Option(1), Option("USA")))
          val bookingRestriction = PromotionRestriction(
            bookOn = Option("1111111"),
            minAdvance = Option(1),
            maxAdvance = Option(1),
            customerSegments = customerSegments,
            bookingIntervals = bookingIntervals,
          )

          val expectedPeriodIntervalInput = List(
            PeriodIntervalInput(1, 1497286800000L, 4102333200000L),
            PeriodIntervalInput(2, 1524416400000L, 1524502799000L),
          )
          val expectedCustomerSegment = List(
            CustomerSegment(Option(1), Option("USA")),
          )
          val expectedRestrictionEntryModel = RestrictionEntryModel(
            bookingIntervals = expectedPeriodIntervalInput,
            bookOn = Option("1111111"),
            minAdvance = Option(1),
            maxAdvance = Option(1),
            customerSegments = expectedCustomerSegment,
            minRooms = None,
            minNights = None,
          )

          val result = otaConverter.buildPromotionRestrictionInput(Option(bookingRestriction))
          result must_== Option(expectedRestrictionEntryModel)
        }
      }
    }

    "Build Restriction" should {

      "return restriction with same book time" in {
        val periodIntervalInput = List(PeriodIntervalInput(2, 1524416400000L, 1524416400000L))
        val restrictionEntryModel = RestrictionEntryModel(
          bookingIntervals = periodIntervalInput,
          bookOn = Option("1111111"),
          minAdvance = Option(1),
          maxAdvance = Option(1),
          customerSegments = List.empty,
        )
        val expectedRestriction = Restriction(
          bookFrom = None,
          bookTo = None,
          bookTimeFrom = Some(1524416400000L),
          bookTimeTo = Some(1524416400000L),
          bookOn = Option("1111111"),
          minAdvance = Option(1),
          maxAdvance = Option(1),
          customerSegments = List.empty,
        )

        val result = otaConverter.buildRestriction(Option(restrictionEntryModel))
        result must_== Option(expectedRestriction)
      }

      "return restriction without minRooms and minNights (RateCategory Booking Restriction)" in {
        val periodIntervalInput = List(
          PeriodIntervalInput(1, 1497286800000L, 4102333200000L),
          PeriodIntervalInput(2, 1524416400000L, 1524502799000L),
        )
        val customerSegment = List(
          CustomerSegment(Option(1), Option("USA")),
        )
        val restrictionEntryModel = RestrictionEntryModel(
          bookingIntervals = periodIntervalInput,
          bookOn = Option("1111111"),
          minAdvance = Option(1),
          maxAdvance = Option(1),
          customerSegments = customerSegment,
          minRooms = None,
          minNights = None,
        )
        val expectedRestriction = Restriction(
          bookFrom = Option(new DateTime(1497286800000L)),
          bookTo = Option(new DateTime(4102333200000L)),
          bookTimeFrom = Option(1524416400000L),
          bookTimeTo = Option(1524502799000L),
          bookOn = Option("1111111"),
          minAdvance = Option(1),
          maxAdvance = Option(1),
          customerSegments = customerSegment,
          minRooms = None,
          minNights = None,
        )

        val result = otaConverter.buildRestriction(Option(restrictionEntryModel))
        result must_== Option(expectedRestriction)
      }

      "return restriction with minRooms and minNights (Promotion Restriction)" in {
        val periodIntervalInput = List(
          PeriodIntervalInput(1, 1497286800000L, 4102333200000L),
          PeriodIntervalInput(2, 1524416400000L, 1524502799000L),
        )
        val customerSegment = List(
          CustomerSegment(Option(1), Option("USA")),
        )
        val restrictionEntryModel = RestrictionEntryModel(
          bookingIntervals = periodIntervalInput,
          bookOn = Option("1111111"),
          minAdvance = Option(1),
          maxAdvance = Option(1),
          customerSegments = customerSegment,
          minRooms = Option(1),
          minNights = Option(1),
        )
        val expectedRestriction = Restriction(
          bookFrom = Option(new DateTime(1497286800000L)),
          bookTo = Option(new DateTime(4102333200000L)),
          bookTimeFrom = Option(1524416400000L),
          bookTimeTo = Option(1524502799000L),
          bookOn = Option("1111111"),
          minAdvance = Option(1),
          maxAdvance = Option(1),
          customerSegments = customerSegment,
          minRooms = Option(1),
          minNights = Option(1),
        )

        val result = otaConverter.buildRestriction(Option(restrictionEntryModel))
        result must_== Option(expectedRestriction)
      }
    }

    "Build DailyPrice" should {
      "return correct daily price with no surcharges - A Variant" in {
        val date = DateTime.now.withTimeAtStartOfDay
        val dailyPrice = aValidOTADailyPrice
          .withDateMillis(date.getMillis)
          .withPrices(Seq(aValidOTAOccupancyPrice.withSurcharges(Seq.empty)))
          .withCommissions(Seq(aValidOTACommission.withLanguageID(0)))
          .withChannelDiscount(0.0)
        val roomInfo = aValidOTAChannelRoomRate

        val prices = List(
          PriceEntry(
            date = date,
            chargeType = ChargeType.Room,
            chargeOption = ChargeOption.Mandatory,
            applyTo = "PRPN",
            occupancy = 1,
            value = 100.0,
            quantity = 1,
            rateLoadedPrice = 100.0,
            latestBreakdownStep = BreakdownStep.BaseStep,
            priceBreakdownHistory = BookingPriceBreakdown(),
          ),
        )

        val expectedDailyPrice = DailyPrice(date = date,
                                            taxes = Map((1, 0) -> 10.0, (2, 0) -> 5.0),
                                            isPromotionBlackOut = false,
                                            prices = prices,
                                            channelDiscount = Some(0.0))
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Some(2), _rooms = Some(1)), AgePolicy())
        val result = otaConverter.buildDailyPrice(
          dailyPrice,
          roomInfo,
          reqOcc,
          false,
          false,
          false,
          hotelPrice = aValidOTAHotelPrice,
          rateTypeLoaded = RateType.NetExclusive,
          inventoryType = InventoryType.Payout,
          hInfo = aValidHotelInfo,
        )
        result must_== Map(date -> expectedDailyPrice)
      }

      "return correct daily price with no surcharges - B Variant Allocated, Empty TaxAndFeeV2" in {
        val date = DateTime.now.withTimeAtStartOfDay
        val dailyPrice = aValidOTADailyPrice
          .withDateMillis(date.getMillis)
          .withPrices(Seq(aValidOTAOccupancyPrice.withSurcharges(Seq.empty)))
          .withCommissions(Seq(aValidOTACommission.withLanguageID(0)))
          .withChannelDiscount(0.0)
        val roomInfo = aValidOTAChannelRoomRate

        val prices = List(
          PriceEntry(
            date = date,
            chargeType = ChargeType.Room,
            chargeOption = ChargeOption.Mandatory,
            applyTo = "PRPN",
            occupancy = 1,
            value = 100.0,
            quantity = 1,
            rateLoadedPrice = 100.0,
            latestBreakdownStep = BreakdownStep.BaseStep,
            priceBreakdownHistory = BookingPriceBreakdown(),
          ),
        )

        val expectedDailyPrice = DailyPrice(date = date,
                                            taxes = Map((1, 0) -> 10.0, (2, 0) -> 5.0),
                                            isPromotionBlackOut = false,
                                            prices = prices,
                                            channelDiscount = Some(0.0))
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          cInfo = aValidClientInfo.copy(cid = Some(-1)),
          whitelabelSetting = aValidwhitelabelSetting,
          experiments = List(YplExperiment(US_NEW_TAX_APPLY_TYPE, 'B')),
        )
        implicit val ctx = YplContext(request)
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Some(2), _rooms = Some(1)), AgePolicy())
        val result = otaConverter.buildDailyPrice(
          dailyPrice,
          roomInfo,
          reqOcc,
          false,
          false,
          false,
          hotelPrice = aValidOTAHotelPrice,
          rateTypeLoaded = RateType.NetExclusive,
          inventoryType = InventoryType.Payout,
          hInfo = aValidHotelInfo,
        )
        result must_== Map(date -> expectedDailyPrice)
      }

      "return correct daily price with no surcharges - B Variant Allocated, Given TaxAndFeeV2 " in {
        val date = DateTime.now.withTimeAtStartOfDay
        val dailyPrice = aValidOTADailyPrice
          .withDateMillis(date.getMillis)
          .withPrices(Seq(aValidOTAOccupancyPriceV2.withSurcharges(Seq.empty)))
          .withCommissions(Seq(aValidOTACommission.withLanguageID(0)))
          .withChannelDiscount(0.0)
        val roomInfo = aValidOTAChannelRoomRate

        val prices = List(
          PriceEntry(
            date = date,
            chargeType = ChargeType.Room,
            chargeOption = ChargeOption.Mandatory,
            applyTo = "PRPN",
            occupancy = 2,
            value = 100.0,
            quantity = 1,
            rateLoadedPrice = 100.0,
            latestBreakdownStep = BreakdownStep.BaseStep,
            priceBreakdownHistory = BookingPriceBreakdown(),
          ),
        )

        val expectedDailyPrice = DailyPrice(date = date,
                                            taxes = Map((3, 52) -> 4.0, (4, 53) -> 3.0),
                                            isPromotionBlackOut = false,
                                            prices = prices,
                                            channelDiscount = Some(0.0))
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          cInfo = aValidClientInfo.copy(cid = Some(-1)),
          whitelabelSetting = aValidwhitelabelSetting,
          experiments = List(YplExperiment(US_NEW_TAX_APPLY_TYPE, 'B')),
          commonTaxSettingsOpt = Some(commonTaxSettings),
        )
        implicit val ctx = YplContext(request)
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Some(2), _rooms = Some(1)), AgePolicy())
        val result = otaConverter.buildDailyPrice(
          dailyPrice,
          roomInfo,
          reqOcc,
          false,
          false,
          false,
          hotelPrice = aValidOTAHotelPrice,
          rateTypeLoaded = RateType.NetExclusive,
          inventoryType = InventoryType.Payout,
          hInfo = aValidHotelInfo.copy(stateId = Some(1)),
        )
        result must_== Map(date -> expectedDailyPrice)
      }

      "return correct daily price with no surcharges - B Variant Allocated, Empty TaxAndFeeV2" in {
        val date = DateTime.now.withTimeAtStartOfDay
        val dailyPrice = aValidOTADailyPrice
          .withDateMillis(date.getMillis)
          .withPrices(Seq(aValidOTAOccupancyPrice.withSurcharges(Seq.empty)))
          .withCommissions(Seq(aValidOTACommission.withLanguageID(0)))
          .withChannelDiscount(0.0)
        val roomInfo = aValidOTAChannelRoomRate

        val prices = List(
          PriceEntry(
            date = date,
            chargeType = ChargeType.Room,
            chargeOption = ChargeOption.Mandatory,
            applyTo = "PRPN",
            occupancy = 1,
            value = 100.0,
            quantity = 1,
            rateLoadedPrice = 100.0,
            latestBreakdownStep = BreakdownStep.BaseStep,
            priceBreakdownHistory = BookingPriceBreakdown(),
          ),
        )

        val expectedDailyPrice = DailyPrice(date = date,
                                            taxes = Map((1, 0) -> 10.0, (2, 0) -> 5.0),
                                            isPromotionBlackOut = false,
                                            prices = prices,
                                            channelDiscount = Some(0.0))
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          cInfo = aValidClientInfo.copy(cid = Some(-1)),
          whitelabelSetting = aValidwhitelabelSetting,
          experiments = List(YplExperiment(US_NEW_TAX_APPLY_TYPE, 'B')),
        )
        implicit val ctx = YplContext(request)
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Some(2), _rooms = Some(1)), AgePolicy())
        val result = otaConverter.buildDailyPrice(
          dailyPrice,
          roomInfo,
          reqOcc,
          false,
          false,
          false,
          hotelPrice = aValidOTAHotelPrice,
          rateTypeLoaded = RateType.NetExclusive,
          inventoryType = InventoryType.Payout,
          hInfo = aValidHotelInfo,
        )
        result must_== Map(date -> expectedDailyPrice)
      }

      "return correct daily price with no surcharges - B Variant Allocated, Given TaxAndFeeV2 " in {
        val date = DateTime.now.withTimeAtStartOfDay
        val dailyPrice = aValidOTADailyPrice
          .withDateMillis(date.getMillis)
          .withPrices(Seq(aValidOTAOccupancyPriceV2.withSurcharges(Seq.empty)))
          .withCommissions(Seq(aValidOTACommission.withLanguageID(0)))
          .withChannelDiscount(0.0)
        val roomInfo = aValidOTAChannelRoomRate

        val prices = List(
          PriceEntry(
            date = date,
            chargeType = ChargeType.Room,
            chargeOption = ChargeOption.Mandatory,
            applyTo = "PRPN",
            occupancy = 2,
            value = 100.0,
            quantity = 1,
            rateLoadedPrice = 100.0,
            latestBreakdownStep = BreakdownStep.BaseStep,
            priceBreakdownHistory = BookingPriceBreakdown(),
          ),
        )

        val expectedDailyPrice = DailyPrice(date = date,
                                            taxes = Map((3, 52) -> 4.0, (4, 53) -> 3.0),
                                            isPromotionBlackOut = false,
                                            prices = prices,
                                            channelDiscount = Some(0.0))
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          cInfo = aValidClientInfo.copy(cid = Some(-1)),
          whitelabelSetting = aValidwhitelabelSetting,
          experiments = List(YplExperiment(US_NEW_TAX_APPLY_TYPE, 'B')),
          commonTaxSettingsOpt = Some(commonTaxSettings),
        )
        implicit val ctx = YplContext(request)
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Some(2), _rooms = Some(1)), AgePolicy())
        val result = otaConverter.buildDailyPrice(
          dailyPrice,
          roomInfo,
          reqOcc,
          false,
          false,
          false,
          hotelPrice = aValidOTAHotelPrice,
          rateTypeLoaded = RateType.NetExclusive,
          inventoryType = InventoryType.Payout,
          hInfo = aValidHotelInfo.copy(stateId = Some(1)),
        )
        result must_== Map(date -> expectedDailyPrice)
      }

      "return correct daily price with surcharges" in {
        val date1 = DateTime.now.withTimeAtStartOfDay // check-in date
        val date2 = date1.plusDays(1) // check-out date
        val surchargePAPB = aValidOTASurcharge.withIsCommissionable(true).withApplyTo("PAPB")
        val surchargePRPN = aValidOTASurcharge.withIsCommissionable(true).withApplyTo("PRPN")
        val occupancyPrice = aValidOTAOccupancyPrice.withSurcharges(Seq(surchargePAPB, surchargePRPN))
        val dailyPrice1 = aValidOTADailyPrice
          .withDateMillis(date1.getMillis)
          .withPrices(Seq(occupancyPrice))
          .withCommissions(Seq(aValidOTACommission.withLanguageID(0)))
          .withChannelDiscount(0.0)
        val dailyPrice2 = dailyPrice1.withDateMillis(date2.getMillis)
        val roomInfo = aValidOTAChannelRoomRate

        // PB surcharges will be filtered out on non-check-in date
        val expectedRpmSurcharges1 = List(
          SurchargeEntry(1, "PAPB", ChargeOption.Mandatory, Set(date1), false, true, 10.0, 1),
          SurchargeEntry(1, "PRPN", ChargeOption.Mandatory, Set(date1), false, true, 10.0, 1),
        )
        val expectedRpmSurcharges2 =
          List(SurchargeEntry(1, "PRPN", ChargeOption.Mandatory, Set(date2), false, true, 10.0, 1))

        val prices1 = List(
          PriceEntry(
            date = date1,
            chargeType = ChargeType.Room,
            chargeOption = ChargeOption.Mandatory,
            applyTo = "PRPN",
            occupancy = 1,
            value = 100.0,
            quantity = 1,
            rateLoadedPrice = 100.0,
            latestBreakdownStep = BreakdownStep.BaseStep,
            priceBreakdownHistory = BookingPriceBreakdown(),
          ),
        )
        val prices2 = prices1.map(_.copy(date = date2))

        val expectedDailyPrice1 = DailyPrice(date = date1,
                                             taxes = Map((1, 0) -> 10.0, (2, 0) -> 5.0),
                                             isPromotionBlackOut = false,
                                             prices = prices1,
                                             channelDiscount = Some(0.0),
                                             rpmSurcharges = expectedRpmSurcharges1)
        val expectedDailyPrice2 =
          expectedDailyPrice1.copy(date = date2, prices = prices2, rpmSurcharges = expectedRpmSurcharges2)

        val request = YplRequest("",
                                 checkIn = date1,
                                 checkOut = date2,
                                 supplierFeatures = aValidSupplierFeatures,
                                 whitelabelSetting = aValidwhitelabelSetting,
                                 fences = aValidYplRequestFences)
        implicit val ctx = aValidYplContext.withRequest(request).build

        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Some(2), _rooms = Some(1)), AgePolicy())

        val result1 = otaConverter.buildDailyPrice(
          dailyPrice1,
          roomInfo,
          reqOcc,
          false,
          false,
          false,
          hotelPrice = aValidOTAHotelPrice,
          rateTypeLoaded = RateType.NetExclusive,
          inventoryType = InventoryType.Payout,
          hInfo = aValidHotelInfo,
        )
        result1 must_== Map(date1 -> expectedDailyPrice1)

        val result2 = otaConverter.buildDailyPrice(
          dailyPrice2,
          roomInfo,
          reqOcc,
          false,
          false,
          false,
          hotelPrice = aValidOTAHotelPrice,
          rateTypeLoaded = RateType.NetExclusive,
          inventoryType = InventoryType.Payout,
          hInfo = aValidHotelInfo,
        )
        result2 must_== Map(date2 -> expectedDailyPrice2)
      }

      "return correct daily price with tax prototype level" in {
        val date = DateTime.now.withTimeAtStartOfDay
        val dailyPrice = aValidOTADailyPrice
          .withDateMillis(date.getMillis)
          .withPrices(Seq(aValidOTAOccupancyPrice.withSurcharges(Seq.empty)))
          .withCommissions(Seq(aValidOTACommission.withLanguageID(0)))
          .withChannelDiscount(0.0)
        val roomInfo = aValidOTAChannelRoomRate

        val prices = List(
          PriceEntry(
            date = date,
            chargeType = ChargeType.Room,
            chargeOption = ChargeOption.Mandatory,
            applyTo = "PRPN",
            occupancy = 1,
            value = 100.0,
            quantity = 1,
            rateLoadedPrice = 100.0,
            latestBreakdownStep = BreakdownStep.BaseStep,
            priceBreakdownHistory = BookingPriceBreakdown(),
          ),
        )

        val expectedDailyPrice = DailyPrice(date = date,
                                            taxes = Map((1, 0) -> 10.0, (2, 0) -> 5.0),
                                            isPromotionBlackOut = false,
                                            prices = prices,
                                            channelDiscount = Some(0.0))
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Some(2), _rooms = Some(1)), AgePolicy())
        val result = otaConverter.buildDailyPrice(
          dailyPrice,
          roomInfo,
          reqOcc,
          false,
          false,
          false,
          hotelPrice = aValidOTAHotelPrice,
          rateTypeLoaded = RateType.NetExclusive,
          inventoryType = InventoryType.Payout,
          hInfo = aValidHotelInfo,
        )
        result must_== Map(date -> expectedDailyPrice)
      }

      "build correct price entry" in {
        val date = DateTime.now
        val occupancyPrice = aValidOTAOccupancyPrice.withTaxAndFee(Seq.empty).withAmount(10.0)
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(4), None, Some(2)), AgePolicy())
        val isBookingReq = false
        val expectedPriceEntry = PriceEntry(
          date = date,
          chargeType = ChargeType.Room,
          chargeOption = ChargeOption.Mandatory,
          applyTo = "PRPN",
          occupancy = 1,
          value = 10.0,
          quantity = 2,
          rateLoadedPrice = 10.0,
          resellRefSell = None,
          latestBreakdownStep = BreakdownStep.BaseStep,
          priceBreakdownHistory = BookingPriceBreakdown(),
        )
        val result = otaConverter.buildPriceEntry(occupancyPrice, date, reqOcc, isBookingReq, RateType.Unknown, "USD")
        result must_== expectedPriceEntry
      }

      "build correct price entry occfree" in {
        val date = DateTime.now
        val occupancyPrice = aValidOTAOccupancyPrice.withTaxAndFee(Seq.empty).withAmount(10.0)
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(0), None, Some(0)), AgePolicy())
        val isBookingReq = false
        val expectedPriceEntry = PriceEntry(
          date = date,
          chargeType = ChargeType.Room,
          chargeOption = ChargeOption.Mandatory,
          applyTo = "PRPN",
          occupancy = 1,
          value = 10.0,
          quantity = 1,
          rateLoadedPrice = 10.0,
          latestBreakdownStep = BreakdownStep.BaseStep,
          priceBreakdownHistory = BookingPriceBreakdown(),
        )
        val result = otaConverter.buildPriceEntry(occupancyPrice, date, reqOcc, isBookingReq, RateType.Unknown, "USD")
        result must_== expectedPriceEntry
      }

      "ignore timezone when converting to dateTime from protobuf epoch time" in {
        val date = DateTime.now.withTimeAtStartOfDay // check-in date
        val surchargePAPB = aValidOTASurcharge.withIsCommissionable(true).withApplyTo("PAPB")
        val surchargePRPN = aValidOTASurcharge.withIsCommissionable(true).withApplyTo("PRPN")
        val occupancyPrice = aValidOTAOccupancyPrice.withSurcharges(Seq(surchargePAPB, surchargePRPN))
        val dailyPrice = aValidOTADailyPrice
          .withDateMillis(date.plusHours(11).getMillis)
          .withPrices(Seq(occupancyPrice))
          .withCommissions(Seq(aValidOTACommission.withLanguageID(0)))
          .withChannelDiscount(0.0)
        val roomInfo = aValidOTAChannelRoomRate

        // PB surcharges will be filtered out on non-check-in date
        val expectedRpmSurcharges1 = List(
          SurchargeEntry(1, "PAPB", ChargeOption.Mandatory, Set(date), false, true, 10.0, 1),
          SurchargeEntry(1, "PRPN", ChargeOption.Mandatory, Set(date), false, true, 10.0, 1),
        )

        val prices = List(
          PriceEntry(
            date = date,
            chargeType = ChargeType.Room,
            chargeOption = ChargeOption.Mandatory,
            applyTo = "PRPN",
            occupancy = 1,
            value = 100.0,
            quantity = 1,
            rateLoadedPrice = 100.0,
            latestBreakdownStep = BreakdownStep.BaseStep,
            priceBreakdownHistory = BookingPriceBreakdown(),
          ),
        )

        val expectedDailyPrice = DailyPrice(date = date,
                                            taxes = Map((1, 0) -> 10.0, (2, 0) -> 5.0),
                                            isPromotionBlackOut = false,
                                            prices = prices,
                                            channelDiscount = Some(0.0),
                                            rpmSurcharges = expectedRpmSurcharges1)

        val request = YplRequest(
          "",
          checkIn = date,
          checkOut = date.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build

        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Some(2), _rooms = Some(1)), AgePolicy())

        val result = otaConverter.buildDailyPrice(
          dailyPrice,
          roomInfo,
          reqOcc,
          false,
          false,
          false,
          hotelPrice = aValidOTAHotelPrice,
          rateTypeLoaded = RateType.NetExclusive,
          inventoryType = InventoryType.Payout,
          hInfo = aValidHotelInfo,
        )
        result must_== Map(date -> expectedDailyPrice)
      }

      "build correct price entry with resellInfo" in {
        val date = DateTime.now
        val occupancyPrice =
          aValidOTAOccupancyPrice.withTaxAndFee(Seq.empty).withAmount(10.0).withResellInfo(aValidResellInfo)
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(4), None, Some(2)), AgePolicy())
        val isBookingReq = false
        val expectedPriceEntry = PriceEntry(
          date = date,
          chargeType = ChargeType.Room,
          chargeOption = ChargeOption.Mandatory,
          applyTo = "PRPN",
          occupancy = 1,
          value = 10.0,
          quantity = 2,
          rateLoadedPrice = 10.0,
          resellRefSell = Some(15.0),
          latestBreakdownStep = BreakdownStep.BaseStep,
          priceBreakdownHistory = BookingPriceBreakdown(),
        )
        val result = otaConverter.buildPriceEntry(occupancyPrice, date, reqOcc, isBookingReq, RateType.Unknown, "USD")
        result must_== expectedPriceEntry
      }
    }

    "Build PromotionEntry" should {

      trait PromotionEntryScope extends Scope {
        val date = DateTime.now
        val startTimeDay = date.withTimeAtStartOfDay().getMillis
        val endTimeDay = date.withHourOfDay(5).getMillis

        val bookingIntervals = Seq(
          PromotionRestrictionPeriodInterval(PromotionRestrictionPeriodType.Day, 1497286800000L, 4102333200000L),
          PromotionRestrictionPeriodInterval(PromotionRestrictionPeriodType.Time, startTimeDay, endTimeDay),
        )
        val customerSegments = Seq(PromotionRestrictionCustomerSegment(Option(1), Option("USA")))
        val bookingRestriction = PromotionRestriction(
          bookOn = Option("1111111"),
          minAdvance = Option(1),
          maxAdvance = Option(999),
          customerSegments = customerSegments,
          bookingIntervals = bookingIntervals,
          minRooms = Some(1),
          minNight = Some(1),
        )

        val baseProtoPromotion = ProtoPromotion(
          id = Some(1),
          typeId = Some(1),
          cmsTypeId = Some(1),
          cmsDiscountTypeId = Some(46316),
          cancellationCode = Some(CancellationCode.NON_REFUNDABLE),
          isStackable = Some(false),
          isStackCombine = Some(false),
          stackableDiscountType = None,
          isApplyChannelDiscount = Some(false),
        )
      }

      "return correct promotion entry" in new PromotionEntryScope {
        val discounts = Seq(Discount(date.getMillis, 20),
                            Discount(date.plusDays(1).getMillis, 20),
                            Discount(date.plusDays(2).getMillis, 20))
        val promotionProto = baseProtoPromotion
          .withDiscountTypeId(1)
          .withRestriction(bookingRestriction.withMaxAdvance(0))
          .withDiscounts(discounts)
          .withStackableDiscountType(1)

        val expectedPromotionEntry = PromotionEntry(
          id = 1,
          typeId = 1,
          discountType = DiscountType.PercentDiscount,
          discounts = List(20, 20, 20),
          cmsTypeId = 1,
          cmsDiscountTypeId = 46316,
          rewardPoint = 0,
          minRooms = 1,
          minNightStay = 1,
          bookOn = "1111111",
          bookFrom = Some(new DateTime(1497286800000L)),
          bookTo = Some(new DateTime(4102333200000L)),
          minAdvPurchase = Some(1),
          maxAdvPurchase = Some(0),
          bookTimeFrom = None,
          bookTimeTo = Some(new DateTime(endTimeDay).withDate(1970, 1, 1).plusHours(7).getMillis * 10000),
          applyDates = Map(date -> 0, date.plusDays(1) -> 0, date.plusDays(2) -> 0),
          cancellationCode = CancellationCode.NON_REFUNDABLE,
          customerSegments = List(
            CustomerSegment(Some(1), Some("USA")),
          ),
          isStackable = false,
          isAllowStack = false,
          stackDiscountOption = Some(StackDiscountOption.Additive),
          isAllowChannelDiscount = false,
        )

        val result = otaConverter.buildPromotionEntry(promotionProto, CancellationCode.NON_REFUNDABLE)
        result must_== expectedPromotionEntry
      }

      "return correct promotion entry for amount per book discount" in new PromotionEntryScope {
        val discounts = Seq(
          Discount(date.getMillis, 16.666666666666667),
          Discount(date.plusDays(1).getMillis, 16.666666666666667),
          Discount(date.plusDays(2).getMillis, 16.666666666666667),
        )
        val promotionProto = baseProtoPromotion.withDiscountTypeId(2).withDiscounts(discounts)

        val expectedPromotionEntry = PromotionEntry(
          id = 1,
          typeId = 1,
          discountType = DiscountType.AmountDiscountPerBook,
          discounts = List(50),
          cmsTypeId = 1,
          cmsDiscountTypeId = 46316,
          applyDates = Map(date -> 0, date.plusDays(1) -> 0, date.plusDays(2) -> 0),
          cancellationCode = CancellationCode.NON_REFUNDABLE,
          isStackable = false,
          isAllowStack = false,
          stackDiscountOption = None,
          isAllowChannelDiscount = false,
        )

        val result = otaConverter.buildPromotionEntry(promotionProto, CancellationCode.NON_REFUNDABLE)
        result must_== expectedPromotionEntry
      }

      "return correct promotion entry for amount per night discount" in new PromotionEntryScope {
        val discounts = Seq(Discount(date.getMillis, 10),
                            Discount(date.plusDays(1).getMillis, 20),
                            Discount(date.plusDays(2).getMillis, 30))
        val promotionProto = baseProtoPromotion.withDiscountTypeId(3).withDiscounts(discounts)

        val expectedPromotionEntry = PromotionEntry(
          id = 1,
          typeId = 1,
          discountType = DiscountType.AmountDiscountPerNight,
          discounts = List(10, 20, 30),
          cmsTypeId = 1,
          cmsDiscountTypeId = 46316,
          applyDates = Map(date -> 0, date.plusDays(1) -> 1, date.plusDays(2) -> 2),
          cancellationCode = CancellationCode.NON_REFUNDABLE,
          isStackable = false,
          isAllowStack = false,
          stackDiscountOption = None,
          isAllowChannelDiscount = false,
        )

        val result = otaConverter.buildPromotionEntry(promotionProto, CancellationCode.NON_REFUNDABLE)
        result must_== expectedPromotionEntry
      }

      "return correct promotion entry for free night discount" in new PromotionEntryScope {
        val discounts = Seq(Discount(date.getMillis, 1),
                            Discount(date.plusDays(1).getMillis, 1),
                            Discount(date.plusDays(2).getMillis, 1))
        val promotionProto = baseProtoPromotion.withDiscountTypeId(4).withDiscounts(discounts)

        val expectedPromotionEntry = PromotionEntry(
          id = 1,
          typeId = 1,
          discountType = DiscountType.FreeNight,
          discounts = List(1, 1, 1),
          cmsTypeId = 1,
          cmsDiscountTypeId = 46316,
          applyDates = Map(date -> 0, date.plusDays(1) -> 0, date.plusDays(2) -> 0),
          cancellationCode = CancellationCode.NON_REFUNDABLE,
          isStackable = false,
          isAllowStack = false,
          stackDiscountOption = None,
          isAllowChannelDiscount = false,
        )

        val result = otaConverter.buildPromotionEntry(promotionProto, CancellationCode.NON_REFUNDABLE)
        result must_== expectedPromotionEntry
      }

      "return correct promotion entry for express tonight discount" in new PromotionEntryScope {
        val discounts = Seq(Discount(date.getMillis, 1000))
        val promotionProto = baseProtoPromotion.withDiscountTypeId(5).withDiscounts(discounts)

        val expectedPromotionEntry = PromotionEntry(
          id = 1,
          typeId = 1,
          discountType = DiscountType.FinalPriceDiscount,
          discounts = List(1000),
          cmsTypeId = 1,
          cmsDiscountTypeId = 46316,
          applyDates = Map(date -> 0),
          cancellationCode = CancellationCode.NON_REFUNDABLE,
          isStackable = false,
          isAllowStack = false,
          stackDiscountOption = None,
          isAllowChannelDiscount = false,
        )

        val result = otaConverter.buildPromotionEntry(promotionProto, CancellationCode.NON_REFUNDABLE)
        result must_== expectedPromotionEntry
      }

      "return correct promotion entry for min and max advance = -1" in new PromotionEntryScope {
        val discountSeq = Seq(Discount(date.getMillis, 1000))
        val promotionProto = baseProtoPromotion
          .withRestriction(bookingRestriction.withMaxAdvance(Some(-1)).withMinAdvance(Some(-1)))
          .withDiscountTypeId(5)
          .withDiscounts(discountSeq)

        val expectedPromotionEntry = PromotionEntry(
          id = 1,
          typeId = 1,
          discountType = DiscountType.FinalPriceDiscount,
          discounts = List(1000),
          cmsTypeId = 1,
          cmsDiscountTypeId = 46316,
          applyDates = Map(date -> 0),
          cancellationCode = CancellationCode.NON_REFUNDABLE,
          isStackable = false,
          isAllowStack = false,
          stackDiscountOption = None,
          isAllowChannelDiscount = false,
          bookOn = "1111111",
          bookFrom = Some(new DateTime(1497286800000L)),
          bookTo = Some(new DateTime(4102333200000L)),
          bookTimeFrom = None,
          bookTimeTo = Some(new DateTime(endTimeDay).withDate(1970, 1, 1).plusHours(7).getMillis * 10000),
          minAdvPurchase = None,
          maxAdvPurchase = None,
          minRooms = 1,
          minNightStay = 1,
          customerSegments = List(CustomerSegment(Some(1), Some("USA"))),
        )

        val result = otaConverter.buildPromotionEntry(promotionProto, CancellationCode.NON_REFUNDABLE)
        result must_== expectedPromotionEntry
      }
    }

    "Build RateCategoryEntry" should {

      "return rate category entry with promotional rateplan data" in {
        val rateCategory = aValidOTARateCategory.withPromotionTypeId(1).withPromotionTypeCmsId(1)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val currentDate = DateTime.now
        val request = YplRequest(
          "",
          currentDate,
          currentDate.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build

        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
        val result = otaConverter
          .buildRateCategoryEntry(rateCategory,
                                  channelRoomRate,
                                  reqOcc,
                                  false,
                                  0,
                                  false,
                                  hotelPrice = aValidOTAHotelPrice,
                                  hInfo = aValidHotelInfo,
                                  inventoryType = InventoryType.Payout)
          .get

        result.promotionTypeId must_== Some(1)
        result.promotionTypeCmsId must_== Some(1)
      }

      "return correct rate category entry without cxl code" in {
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory))

        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build

        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
        val result = otaConverter
          .buildRateCategoryEntry(
            aValidOTARateCategory.withCancellationCode(None),
            channelRoomRate,
            reqOcc,
            false,
            0,
            false,
            hotelPrice = aValidOTAHotelPrice,
            hInfo = aValidHotelInfo,
            inventoryType = InventoryType.Payout,
          )
          .get

        result.cxlCode must_== CancellationCode.NON_REFUNDABLE
      }

      "should not filter benefit for variant b if experiment for SOMEEXPERIMENT" in {
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory))
        val rateCategory = aValidOTARateCategory.withAgodaBenefits(Seq(1, 2, 3))
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withExperimentContext(forceBExperimentContext("SOMEEXPERIMENT")).build
        val benefitList = List(BenefitEntry(1, 0.0d, 0, None),
                               BenefitEntry(2, 0.0, 0, None, None, 0),
                               BenefitEntry(3, 0.0, 0, None, None, 0))
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
        val result = otaConverter
          .buildRateCategoryEntry(rateCategory,
                                  channelRoomRate,
                                  reqOcc,
                                  false,
                                  0,
                                  false,
                                  hotelPrice = aValidOTAHotelPrice,
                                  hInfo = aValidHotelInfo,
                                  inventoryType = InventoryType.Payout)
          .get
        result.benefitList must_== benefitList
      }

      "return correct rate category entry without supplier rate info" in {
        val channelRoomRate = aValidOTAChannelRoomRate
        val rateCategory = aValidOTARateCategory.withCancellationCode("1D20P_100P").withAgodaBenefits(Seq(1))
        val benefitList = List(BenefitEntry(1, 0.0d, 0, None))
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
        val result = otaConverter
          .buildRateCategoryEntry(rateCategory,
                                  channelRoomRate,
                                  reqOcc,
                                  false,
                                  0,
                                  false,
                                  hotelPrice = aValidOTAHotelPrice,
                                  hInfo = aValidHotelInfo,
                                  inventoryType = InventoryType.Payout)
          .get

        result.rateCategoryId must_== rateCategory.rateCategoryId
        result.cxlCode must_== rateCategory.cancellationCode.get
        result.rateCategoryCode must_== None
        result.cxlCode must_== "1D20P_100P"
        result.rateTypeLoaded must_== RateType.NetExclusive
        result.parent must_== None
        result.remainingRoom must_== rateCategory.remainingRoom
        result.benefitList must_== benefitList
        result.remainingRoomGa must_== 0
        result.remainingRoomRa must_== None
        result.isAmount must_== false
        result.applyTo must_== ""
        result.value must_== 0.0
        result.promotionList must_== List.empty
        result.isCanCombinePromotion must_== false
        result.offerType must_== rateCategory.offerTypeId
        result.isRoomTypeNotGuarantee must_== false
        result.customerSegment must_== List.empty
        result.dmcRatePlanID must_== Some("BAR")
        result.dmcMealPlanID must_== Some("14")
      }

      "return correct rate category entry with supplier rate info" in {
        val channelRoomRate = aValidOTAChannelRoomRate
        val rateCategory = aValidOTARateCategory.withSupplierRateInfo(aValidOTASupplierRateInfo.build).build
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
        val result = otaConverter
          .buildRateCategoryEntry(rateCategory,
                                  channelRoomRate,
                                  reqOcc,
                                  false,
                                  0,
                                  false,
                                  hotelPrice = aValidOTAHotelPrice,
                                  hInfo = aValidHotelInfo,
                                  inventoryType = InventoryType.Payout)
          .get

        result.dmcRatePlanID must_== Option("BAR")
        result.dmcMealPlanID must_== Option("14")
      }

      "return correct rate category entry with default rate type as NetExclusive when ratetype is empty" in {
        val channelRoomRate = aValidOTAChannelRoomRate.clearRateType
        val rateCategory = aValidOTARateCategory.withSupplierRateInfo(aValidOTASupplierRateInfo.build).build
        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
        val result = otaConverter
          .buildRateCategoryEntry(rateCategory,
                                  channelRoomRate,
                                  reqOcc,
                                  false,
                                  0,
                                  false,
                                  hotelPrice = aValidOTAHotelPrice,
                                  hInfo = aValidHotelInfo,
                                  inventoryType = InventoryType.Payout)
          .get
        result.rateTypeLoaded must_== RateType.NetExclusive
      }

      "return payment model correctly" in {
        val channelRoomRate = aValidOTAChannelRoomRate
        val rateCategory = aValidOTARateCategory
          .withSupplierRateInfo(aValidOTASupplierRateInfo.build)
          .withPaymentModel(Some(ProtoPaymentModel.MerchantComission))
        implicit val ctx = YplContext(aValidYplRequest)

        "Setting Payment Model in RateCategory" in {
          val result = otaConverter
            .buildRateCategoryEntry(rateCategory,
                                    channelRoomRate,
                                    aValidReqOcc,
                                    false,
                                    0,
                                    false,
                                    hotelPrice = aValidOTAHotelPrice,
                                    hInfo = aValidHotelInfo,
                                    inventoryType = InventoryType.Payout)
            .get
          result.paymentModel must_== Some(PaymentModel.MerchantCommission)
        }

        "Experiment allocate to B without setting" in {
          val rateCategoryWithoutPaymentModel =
            aValidOTARateCategory.withSupplierRateInfo(aValidOTASupplierRateInfo.build)
          val result = otaConverter
            .buildRateCategoryEntry(
              rateCategoryWithoutPaymentModel,
              channelRoomRate,
              aValidReqOcc,
              false,
              0,
              false,
              hotelPrice = aValidOTAHotelPrice,
              hInfo = aValidHotelInfo,
              inventoryType = InventoryType.Payout,
            )
            .get
          result.paymentModel must beNone
        }
      }

      "return correct benefits considering property facilities for results from pull suppliers" should {
        "return correct benefits containing property benefits (no intersect) on rate-plan" in {
          val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory))
          val rateCategory = aValidOTARateCategory.withAgodaBenefits(Seq(2, 3))
          implicit val ctx = aValidYplContext
          val expectedBenefitList = List(BenefitEntry(2, 0.0, 0, None, None, 0),
                                         BenefitEntry(3, 0.0, 0, None, None, 0),
                                         BenefitEntry(1, 0.0d, 0, None),
                                         BenefitEntry(95, 0.0, 0, None, None, 0))
          val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
          val hotelPrice = aValidOTAHotelPrice.withPropertyBenefits(
            Seq(
              Benefit(benefitId = 1, benefitType = BenefitTypes.Normal.i),
              Benefit(benefitId = 95, benefitType = BenefitTypes.Normal.i),
            ),
          )
          val result = otaConverter
            .buildRateCategoryEntry(rateCategory,
                                    channelRoomRate,
                                    reqOcc,
                                    false,
                                    0,
                                    false,
                                    hotelPrice = hotelPrice,
                                    hInfo = aValidHotelInfo,
                                    inventoryType = InventoryType.Payout)
            .get
          result.benefitList must_== expectedBenefitList
        }
        "return correct benefits containing property benefits (intersect benefitId = 1) on rate-plan" in {
          val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory))
          val rateCategory = aValidOTARateCategory.withAgodaBenefits(Seq(1, 2, 3))
          implicit val ctx = aValidYplContext
          val expectedBenefitList = List(BenefitEntry(1, 0.0d, 0, None),
                                         BenefitEntry(2, 0.0, 0, None, None, 0),
                                         BenefitEntry(3, 0.0, 0, None, None, 0),
                                         BenefitEntry(95, 0.0, 0, None, None, 0))
          val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
          val hotelPrice = aValidOTAHotelPrice.withPropertyBenefits(
            Seq(
              Benefit(benefitId = 1, benefitType = BenefitTypes.Normal.i),
              Benefit(benefitId = 95, benefitType = BenefitTypes.Normal.i),
            ),
          )
          val result = otaConverter
            .buildRateCategoryEntry(rateCategory,
                                    channelRoomRate,
                                    reqOcc,
                                    false,
                                    0,
                                    false,
                                    hotelPrice = hotelPrice,
                                    hInfo = aValidHotelInfo,
                                    inventoryType = InventoryType.Payout)
            .get
          result.benefitList must_== expectedBenefitList
        }
        "return correct benefits containing property benefits (intersect benefitId = 95) on rate-plan" in {
          val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory))
          val rateCategory = aValidOTARateCategory.withAgodaBenefits(Seq(2, 3, 95))
          implicit val ctx = aValidYplContext
          val expectedBenefitList = List(BenefitEntry(2, 0.0, 0, None, None, 0),
                                         BenefitEntry(3, 0.0, 0, None, None, 0),
                                         BenefitEntry(95, 0.0, 0, None, None, 0),
                                         BenefitEntry(1, 0.0d, 0, None))
          val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
          val hotelPrice = aValidOTAHotelPrice.withPropertyBenefits(
            Seq(
              Benefit(benefitId = 1, benefitType = BenefitTypes.Normal.i),
              Benefit(benefitId = 95, benefitType = BenefitTypes.Normal.i),
            ),
          )
          val result = otaConverter
            .buildRateCategoryEntry(rateCategory,
                                    channelRoomRate,
                                    reqOcc,
                                    false,
                                    0,
                                    false,
                                    hotelPrice = hotelPrice,
                                    hInfo = aValidHotelInfo,
                                    inventoryType = InventoryType.Payout)
            .get
          result.benefitList must_== expectedBenefitList
        }
        "return correct benefits containing property benefits (no intersect but different breakfastId 26) on rate-plan" in {
          val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory))
          val rateCategory = aValidOTARateCategory.withAgodaBenefits(Seq(26, 2, 3))
          implicit val ctx = aValidYplContext
          val expectedBenefitList = List(BenefitEntry(26, 0.0d, 0, None),
                                         BenefitEntry(2, 0.0, 0, None, None, 0),
                                         BenefitEntry(3, 0.0, 0, None, None, 0),
                                         BenefitEntry(95, 0.0, 0, None, None, 0))
          val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
          val hotelPrice = aValidOTAHotelPrice.withPropertyBenefits(
            Seq(
              Benefit(benefitId = 1, benefitType = BenefitTypes.Normal.i),
              Benefit(benefitId = 95, benefitType = BenefitTypes.Normal.i),
            ),
          )
          val result = otaConverter
            .buildRateCategoryEntry(rateCategory,
                                    channelRoomRate,
                                    reqOcc,
                                    false,
                                    0,
                                    false,
                                    hotelPrice = hotelPrice,
                                    hInfo = aValidHotelInfo,
                                    inventoryType = InventoryType.Payout)
            .get
          result.benefitList must_== expectedBenefitList
        }
        "return correct benefits containing property benefits with parameters" in {
          val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory))
          val rateCategory = aValidOTARateCategory.withAgodaBenefits(Seq(26, 2, 3))
          implicit val ctx = aValidYplContext.build
          val expectedBenefitList = List(
            BenefitEntry(26, 0.0d, 0, None),
            BenefitEntry(2, 0.0, 0, None, None, 0),
            BenefitEntry(3, 0.0, 0, None, None, 0),
            BenefitEntry(55, 0.0, 1, None, None, 0, List(BenefitParameterEntry(Some(1), Some("test"), None))),
          )
          val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
          val hotelPrice = aValidOTAHotelPrice.withPropertyBenefits(
            Seq(
              Benefit(
                benefitId = 55,
                benefitType = BenefitTypes.Structured.i,
                parameters = Seq(StructureBenefitParameter(position = Some(1), value = Some("test"))),
              )))
          val result = otaConverter
            .buildRateCategoryEntry(rateCategory,
                                    channelRoomRate,
                                    reqOcc,
                                    false,
                                    0,
                                    false,
                                    hotelPrice = hotelPrice,
                                    hInfo = aValidHotelInfo,
                                    inventoryType = InventoryType.Payout)
            .get
          result.benefitList must_== expectedBenefitList
        }

        "return benefits with parameters" in {
          val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory))
          val rateCategory = aValidOTARateCategory
            .withAgodaBenefits(Seq(26))
            .withPropertyBenefits(
              Seq(
                Benefit(
                  benefitId = 26,
                  benefitType = BenefitTypes.Structured.i,
                  parameters = Seq(StructureBenefitParameter(position = Some(1), value = Some("test"))),
                )),
            )
          val expectedBenefitList =
            List(BenefitEntry(26, 0.0, 1, None, None, 0, List(BenefitParameterEntry(Some(1), Some("test"), None))))
          val reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(2)), AgePolicy())
          val hotelPrice = aValidOTAHotelPrice
          implicit val mockCtx = aValidYplContext
          val result = otaConverter
            .buildRateCategoryEntry(rateCategory,
                                    channelRoomRate,
                                    reqOcc,
                                    false,
                                    0,
                                    false,
                                    hotelPrice = hotelPrice,
                                    hInfo = aValidHotelInfo,
                                    inventoryType = InventoryType.Payout)
            .get
          result.benefitList must_== expectedBenefitList
        }

        "A-variant with checkInInformation && checkin checkout flag" in {
          val rateCategoryWithCheckInInformation = aValidOTARateCategory
            .withCheckInInformation(aValidCheckInInfo)
            .withAgodaBenefits(Seq(26))
            .withPropertyBenefits(
              Seq(
                Benefit(
                  benefitId = 26,
                  benefitType = BenefitTypes.Structured.i,
                  parameters = Seq(StructureBenefitParameter(position = Some(1), value = Some("test"))),
                )),
            )
          val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory))
          val request = YplRequest(
            "",
            DateTime.now,
            DateTime.now.plusDays(1),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
            featureRequest = YplFeatureRequest(enableRatePlanCheckInCheckOut = true),
          )
          implicit val ctx = aValidYplContext.withRequest(request).build
          val expectedBenefitList =
            List(BenefitEntry(26, 0.0, 1, None, None, 0, List(BenefitParameterEntry(Some(1), Some("test"), None))))
          val result = otaConverter
            .buildRateCategoryEntry(
              rateCategoryWithCheckInInformation,
              channelRoomRate,
              aValidReqOcc,
              false,
              0,
              false,
              hotelPrice = aValidOTAHotelPrice,
              hInfo = aValidHotelInfo,
              inventoryType = InventoryType.Payout,
            )
            .get
          result.benefitList must_== expectedBenefitList
        }

        "OTAConverter should not remove any odd checkin checkout rates" in {

          testCases.map { testCase =>
            val rateCategoryWithCheckInInformation = aValidOTARateCategory
              .withCheckInInformation(aValidCheckInInfo)
              .withAgodaBenefits(Seq(26))
              .withPropertyBenefits(
                Seq(
                  Benefit(
                    benefitId = 26,
                    benefitType = BenefitTypes.Structured.i,
                    parameters = Seq(StructureBenefitParameter(position = Some(1), value = Some("test"))),
                  )),
              )
            val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory))
            val countryId = if (testCase.requireKRHotel) 212 else 106
            val meta = aValidHotelInfo.copy(countryId = countryId)
            val durationType = if (testCase.isHourly) BookingDurationType.Hourly else BookingDurationType.Nightly
            val request = YplRequest(
              "",
              DateTime.parse("2023-08-01"),
              DateTime.parse("2023-08-02"),
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
              bookingDurationTypes = List(durationType),
              featureRequest = aValidYplFeatureRequest.copy(enableRatePlanCheckInCheckOut =
                testCase.enableRatePlanCheckinCheckoutFeatureFlag),
            )
            implicit val ctx =
              aValidYplContext.withRequest(request).withExperimentContext(forceAllBExperimentsContext()).build
            val result = otaConverter.buildRateCategoryEntry(
              rateCategoryWithCheckInInformation,
              channelRoomRate,
              aValidReqOcc,
              false,
              0,
              false,
              hotelPrice = aValidOTAHotelPrice,
              hInfo = meta,
              inventoryType = InventoryType.Payout,
            )
            result must not beNone
          }
          testCases.size must_== 8
        }

      }
    }

    "should populate CustomerSegmentRestriction" should {
      val mockCtx = aValidYplContext
      "return true when is NationalityRate" in {
        verifyShouldPopulateCustomerSegmentRestriction("UNKNOWN_EXPERIMENT", true, false) must_== true
      }

      "return true when allocated to customer segment experiment" in {
        verifyShouldPopulateCustomerSegmentRestriction(YplExperiments.KILL_SWITCH_RATECATEGORY_CUSTOMER_SEGMENT,
                                                       false,
                                                       false) must_== true
      }

      "return true when the supplier is bcom" in {
        verifyShouldPopulateCustomerSegmentRestriction("UNKNOWN_EXPERIMENT", false, true) must_== true
      }

      "return false when not allocated to either nationality rate or customer segment experiment and the supplier is not bcom" in {
        verifyShouldPopulateCustomerSegmentRestriction("UNKNOWN_EXPERIMENT", false, false) must_== false
      }
      "shouldPopulateCustomerSegmentRestriction correctly" in {
        otaConverter.shouldPopulateCustomerSegmentRestriction(true, false, false)(mockCtx) must_== true
        otaConverter.shouldPopulateCustomerSegmentRestriction(false, true, false)(mockCtx) must_== true
        otaConverter.shouldPopulateCustomerSegmentRestriction(false, false, true)(mockCtx) must_== true
      }
    }

    def verifyShouldPopulateCustomerSegmentRestriction(experiment: String,
                                                       isNationalityRate: Boolean,
                                                       isBcom: Boolean): Boolean = {
      val supplierRateInfo = aValidOTASupplierRateInfo.withIsNationalityRate(isNationalityRate)
      val forceBExpCtx = forceBExperimentContext(experiment)
      implicit val ctx = aValidYplContext.withExperimentContext(forceBExpCtx).build
      val rateCategory = aValidOTARateCategory.withSupplierRateInfo(Some(supplierRateInfo))
      otaConverter.shouldPopulateCustomerSegmentRestriction(
        rateCategory.supplierRateInfo.flatMap(_.isNationalityRate).getOrElse(false),
        isBcom)
    }

    "Build RoomEntry" should {
      "return correct room entry with no cxl code in rate category" in {
        val rateCategory = aValidOTARateCategory.withCancellationCode(None)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelPrice = aValidOTAHotelPrice.withOccupancyModel(FullRate)
        val hotelMeta = HotelMeta(6011203, 0)

        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val result = otaConverter
          .buildRoomEntry(rateCategory,
                          channelRoomRate,
                          Map.empty,
                          hotelMeta,
                          hotelPrice,
                          false,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)
          .head
        result.cxlCode must_== CancellationCode.NON_REFUNDABLE
      }

      "return correct room entry with no room type entry" in {
        val rateCategory = aValidOTARateCategory.withCancellationCode("1D20P_100P").withAgodaBenefits(Seq(1))
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).withCurrencyCode("EUR")
        val hotelPrice = aValidOTAHotelPrice.withOccupancyModel(FullRate)
        val hotelMeta = HotelMeta(6011203, 0)

        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val result = otaConverter
          .buildRoomEntry(rateCategory,
                          channelRoomRate,
                          Map.empty,
                          hotelMeta,
                          hotelPrice,
                          false,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)
          .head

        result.roomTypeId must_== 13193032
        result.channel must_== YplMasterChannel.RTL
        result.cxlCode must_== "1D20P_100P"
        result.isBreakFastIncluded must_== true
        result.remainingRooms must_== rateCategory.remainingRoom
        result.currency must_== "EUR"
        result.rateType must_== RateType.NetExclusive
        result.processingFees must_== 0.0
        result.isAllowCombinePromotion must_== true
        result.occEntry must_== RoomOccupancy(0, 0)
        result.promotion must_== None
        result.promotionsBreakdown must_== Map.empty
        result.availablePromotions must_== List.empty
        result.channelRateType must_== Some(RateType.NetExclusive)
        result.displayedRackRate must_== 0.0
        result.offerType must_== None
        result.productOfferId must_== None
        result.originalRateType must_== RateType.NetExclusive
      }

      "return correct room entries per occupancy price if is free occupancy search and fplos" in {
        val twoOccupancyPrice = aValidOTAOccupancyPrice.withOccupancy(2)
        val occupancyPriceSeq = Seq(aValidOTAOccupancyPrice.build, twoOccupancyPrice.build)
        val dailyPrice = aValidOTADailyPrice.withPrices(occupancyPriceSeq).build
        val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPrice))
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice.withOccupancyModel(FullPatternLengthOfStay)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRatePlanId("BAR").withSupplierRoomId("STD")
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)

        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          occ = YplOccInfo(_adults = Some(0), _rooms = Some(0)),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build

        val result = otaConverter
          .buildRoomEntry(rateCategory,
                          channelRoomRate,
                          roomType,
                          hotelMeta,
                          hotelPrice,
                          false,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)
          .head
        result.dailyPrices.head._2.prices.size == 2
      }

      "return correct room entries per occupancy price if is specific occupancy search and fplos" in {

        val twoOccupancyPrice = aValidOTAOccupancyPrice.withOccupancy(2)
        val occupancyPriceSeq = Seq(aValidOTAOccupancyPrice.build, twoOccupancyPrice.build)
        val dailyPrice = aValidOTADailyPrice.withPrices(occupancyPriceSeq).build
        val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPrice))
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice.withOccupancyModel(FullPatternLengthOfStay)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRatePlanId("BAR").withSupplierRoomId("STD")
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)

        val request: YplRequest = aValidYplRequest
          .withCheckIn(DateTime.now)
          .withCheckout(DateTime.now.plusDays(1))
          .withOccupancyInfo(YplOccInfo(_adults = Some(2), _rooms = Some(1)))
        implicit val ctx = aValidYplContext.withRequest(request).build

        val result = otaConverter
          .buildRoomEntry(rateCategory,
                          channelRoomRate,
                          roomType,
                          hotelMeta,
                          hotelPrice,
                          false,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)
          .head
        result.occEntry.adults == 2
      }

      "return correct room entries when supplierRateplanId is None" in {
        val supplierRateplanId = "BAR"
        val occupancyPriceSeq = Seq(aValidOTAOccupancyPrice.build)
        val dailyPrice = aValidOTADailyPrice.withPrices(occupancyPriceSeq).build
        val supplierRateInfo = aValidOTASupplierRateInfo.withSupplierRatePlan(supplierRateplanId)
        val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPrice)).withSupplierRateInfo(supplierRateInfo)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice.withOccupancyModel(FullPatternLengthOfStay)
        val protoRoomType =
          aValidOTARoomType.withMaxOccupancy(2).withSupplierRatePlanId(supplierRateplanId).withSupplierRoomId("STD")
        val roomType = Map(OTARoomTypeKey(protoRoomType.roomTypeId, protoRoomType.supplierRoomId, None) -> protoRoomType)

        val request: YplRequest = aValidYplRequest
          .withCheckIn(DateTime.now)
          .withCheckout(DateTime.now.plusDays(1))
          .withOccupancyInfo(YplOccInfo(_adults = Some(2), _rooms = Some(1)))
        implicit val ctx = aValidYplContext.withRequest(request).build

        "Build Key with Supplier Rateplan Id" in {
          val result = otaConverter
            .buildRoomEntry(rateCategory,
                            channelRoomRate,
                            roomType,
                            hotelMeta,
                            hotelPrice,
                            false,
                            false,
                            request.allFences,
                            None,
                            aValidYplDispatchChannels)
            .head
          result.roomType.maxOccupancy must_== 2
        }

        "Build Key with Supplier without Rateplan Id" in {
          val rateCategoryWithoutRateplanId = aValidOTARateCategory
            .withDailyPrices(Seq(dailyPrice))
            .withSupplierRateInfo(aValidOTASupplierRateInfo.clearSupplierRatePlan)
          val channelRoomRateWithourRateplanId =
            aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategoryWithoutRateplanId))

          val result = otaConverter
            .buildRoomEntry(rateCategory,
                            channelRoomRate,
                            roomType,
                            hotelMeta,
                            hotelPrice,
                            false,
                            false,
                            request.allFences,
                            None,
                            aValidYplDispatchChannels)
            .head
          result.roomType.maxOccupancy must_== 2
        }

      }

      "return dmcData holder correctly" in {
        val externalData = Seq(ExternalData("price", "1000"),
                               ExternalData("hotelCode", "6011203"),
                               ExternalData("currency", "JPY"),
                               ExternalData("price", "2"))
        val supplierRateInfo = aValidOTASupplierRateInfo.withExternalData(externalData)
        val rateCategory = aValidOTARateCategory.withSupplierRateInfo(supplierRateInfo)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice.withOccupancyModel(FullPatternLengthOfStay)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRatePlanId("BAR").withSupplierRoomId("STD")
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)

        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          occ = YplOccInfo(_adults = Some(0), _rooms = Some(0)),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build

        val result = otaConverter
          .buildRoomEntry(rateCategory,
                          channelRoomRate,
                          roomType,
                          hotelMeta,
                          hotelPrice,
                          false,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)
          .head
        result.dmcDataHolder must beSome
        result.dmcDataHolder.get.supplierRateInfo must beSome(supplierRateInfo)
        result.dmcDataHolder.get.supplierExternalDataStr must beSome("1000|6011203|JPY|2")
        result.dmcDataHolder.get.roomUid must beNone

      }

      "return hourly slots when present in the proto" in {
        val rateCategory =
          aValidOTARateCategory.withHourlyAvailableSlots(Seq(OTATimeInterval(4, "16:00"), OTATimeInterval(5, "16:00")))
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).build
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice =
          aValidOTAHotelPrice.withChannelRoomRateList(List(channelRoomRate)).withRoomTypes(Seq(aValidOTARoomType))
        val ctx = YplContext(aValidYplRequest)
        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.head.rateCategory.hourlyAvailableSlots must_== Seq(TimeInterval(4, "16:00"), TimeInterval(5, "16:00"))
      }

      "return multiple hourly slots when present in the proto" in {
        val rc1 =
          aValidOTARateCategory.withHourlyAvailableSlots(Seq(OTATimeInterval(4, "16:00"), OTATimeInterval(5, "16:00")))
        val rc2 = aValidOTARateCategory.withHourlyAvailableSlots(Seq(OTATimeInterval(3, "17:00")))
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rc1, rc2)).build
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice =
          aValidOTAHotelPrice.withChannelRoomRateList(List(channelRoomRate)).withRoomTypes(Seq(aValidOTARoomType))
        val ctx = YplContext(aValidYplRequest)
        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.head.rateCategory.hourlyAvailableSlots must_== Seq(TimeInterval(4, "16:00"), TimeInterval(5, "16:00"))
        res.rooms(1).rateCategory.hourlyAvailableSlots must_== Seq(TimeInterval(3, "17:00"))
      }

      "do no return hourly slots when not present in the proto" in {
        val rateCategory = aValidOTARateCategory.withPaymentModel(Some(ProtoPaymentModel.MerchantComission))
        val channelRoomRate =
          aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).withCurrencyCode("JPY").build
        val hotelMeta = HotelMeta(6011203, 0)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelPrice =
          aValidOTAHotelPrice.withChannelRoomRateList(List(channelRoomRate)).withRoomTypes(Seq(protoRoomType))
        val ctx = YplContext(aValidYplRequest)
        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.head.rateCategory.hourlyAvailableSlots must_== Seq.empty
      }

      "return checkInInformation correctly when featureRequest is populated" in {
        val rateCategory = aValidOTARateCategory.withCheckInInformation(aValidCheckInInfo)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).build
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice =
          aValidOTAHotelPrice.withChannelRoomRateList(List(channelRoomRate)).withRoomTypes(Seq(aValidOTARoomType))
        val ctx =
          YplContext(aValidYplRequest.copy(featureRequest = YplFeatureRequest(enableRatePlanCheckInCheckOut = true)))

        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)(ctx)
        val checkInInformation = res.rooms.head.checkInInformation.get
        checkInInformation.checkInFrom must beSome(java.time.LocalTime.of(16, 0))
        checkInInformation.checkInUntil must beNone
        checkInInformation.checkOutFrom must beSome(java.time.LocalTime.of(20, 0))
        checkInInformation.checkOutUntil must beNone
      }

      "return the rate correctly when checkin information not present irrespective of featureRequest" in {
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(aValidOTARateCategory)).build
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice =
          aValidOTAHotelPrice.withChannelRoomRateList(List(channelRoomRate)).withRoomTypes(Seq(aValidOTARoomType))
        val ctx =
          YplContext(aValidYplRequest.copy(featureRequest = YplFeatureRequest(enableRatePlanCheckInCheckOut = true)))

        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.size == 1
      }

      "return confirmByMins correctly" in {
        val rateCategory = aValidOTARateCategory.withConfirmByMins(30)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).build
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice =
          aValidOTAHotelPrice.withChannelRoomRateList(List(channelRoomRate)).withRoomTypes(Seq(aValidOTARoomType))
        val ctx = YplContext(aValidYplRequest)
        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.head.confirmByMins must beSome(30)
      }

      "return correct room entry for all occupancies" in {
        val singleOccPrice = aValidOTAOccupancyPrice.withOccupancy(1)
        val doubleOccPrice = aValidOTAOccupancyPrice.withOccupancy(2)
        val tripOccPrice = aValidOTAOccupancyPrice.withOccupancy(3)
        val quadOccPrice = aValidOTAOccupancyPrice.withOccupancy(4)
        val dailyPrice = aValidOTADailyPrice.withPrices(Seq(singleOccPrice, doubleOccPrice, tripOccPrice, quadOccPrice))
        val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPrice))
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelPrice =
          aValidOTAHotelPrice.withOccupancyModel(FullPatternLengthOfStay).withChannelRoomRate(Seq(channelRoomRate))
        val hotelMeta = HotelMeta(6011203, 0)

        val request = YplRequest(
          "",
          DateTime.now,
          DateTime.now.plusDays(1),
          occ = YplOccInfo(_adults = Some(0)),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val result = otaConverter
          .buildRoomEntry(rateCategory,
                          channelRoomRate,
                          Map.empty,
                          hotelMeta,
                          hotelPrice,
                          false,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)
          .head
        result.dailyPrices.head._2.prices.size == 4
      }

      "when a room has 0 allotment" in {
        val rateCategory = aValidOTARateCategory.withRemainingRoom(0)
        val channelRoomRate =
          aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).withCurrencyCode("JPY").build
        val hotelMeta = HotelMeta(6011203, 0)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelPrice =
          aValidOTAHotelPrice.withChannelRoomRateList(List(channelRoomRate)).withRoomTypes(Seq(protoRoomType))
        val ctx = YplContext(aValidYplRequest)

        "filter out 0 allotment, when no FeatureFlag.ReturnZeroAllotment" in {
          val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                      hotelMeta,
                                                      aValidYplDispatchChannels,
                                                      Map(aValidRateFence -> aValidYplDispatchChannels),
                                                      Map.empty,
                                                      false)(ctx)
          res.rooms should_== Nil
        }

        "not filter 0 allotment, when FeatureFlag.ReturnZeroAllotment" in {
          val ctx = YplContext(aValidYplRequest.withFeatureFlags((Set(FeatureFlag.ReturnZeroAllotment))))
          val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                      hotelMeta,
                                                      aValidYplDispatchChannels,
                                                      Map(aValidRateFence -> aValidYplDispatchChannels),
                                                      Map.empty,
                                                      false)(ctx)
          res.rooms.head.remainingRooms should_== 0
        }

        "not filter 0 allotment, when prefilter is define and no RateCategory Id. Do not care RateCategory here" in {
          val ctx = aValidYplContext.withRequest(
            aValidYplRequest
              .withFeatureFlags((Set(FeatureFlag.ReturnZeroAllotment)))
              .withPreFilterZeroAllotment(Some(PreFilterZeroAllotment(channelRoomRate.roomTypeId, 999))))
          val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                      hotelMeta,
                                                      aValidYplDispatchChannels,
                                                      Map(aValidRateFence -> aValidYplDispatchChannels),
                                                      Map.empty,
                                                      false)(ctx)
          res.rooms.head.remainingRooms should_== 0
        }
        "not filter 0 allotment, when prefilter is define and no RateCategory and room Id" in {
          val ctx = aValidYplContext.withRequest(
            aValidYplRequest
              .withFeatureFlags((Set(FeatureFlag.ReturnZeroAllotment)))
              .withPreFilterZeroAllotment(Some(PreFilterZeroAllotment(999, 999))))
          val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                      hotelMeta,
                                                      aValidYplDispatchChannels,
                                                      Map(aValidRateFence -> aValidYplDispatchChannels),
                                                      Map.empty,
                                                      false)(ctx)
          res.rooms should_== Nil
        }

      }

      "return correct adult/child occupancy correctly based on HotelPrice information" in {
        val hotelPrice = aValidOTAHotelPrice.withOccupancyModel(FullPatternLengthOfStay).withOccupancy(Occupancy(2, 1))
        val hotelMeta = HotelMeta(6011203, 0)

        val request = aValidYplRequest.withOccupancyInfo(YplOccInfo(_adults = Some(3), _rooms = Some(1)))
        implicit val ctx = aValidYplContext.withRequest(request).build
        val result = otaConverter
          .buildRoomEntry(aValidOTARateCategory,
                          aValidOTAChannelRoomRate,
                          Map.empty,
                          hotelMeta,
                          hotelPrice,
                          true,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)(ctx)
          .head
        result.occEntry.adults must_== (2)
        result.occEntry.children must_== (1)
      }

      "return correct adult/child occupancy correctly based on HotelPrice information with multi-rooms" in {
        val hotelPrice = aValidOTAHotelPrice.withOccupancyModel(FullPatternLengthOfStay).withOccupancy(Occupancy(2, 1))
        val hotelMeta = HotelMeta(6011203, 0)

        val request = aValidYplRequest.withOccupancyInfo(YplOccInfo(_adults = Some(3), _rooms = Some(2)))
        implicit val ctx = aValidYplContext.withRequest(request).build
        val result = otaConverter
          .buildRoomEntry(aValidOTARateCategory,
                          aValidOTAChannelRoomRate,
                          Map.empty,
                          hotelMeta,
                          hotelPrice,
                          true,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)(ctx)
          .head
        result.occEntry.adults must_== (1)
        result.occEntry.children must_== (1)
        result.occEntry.allowedFreeChildrenAndInfants must_== (1)
      }

      "Split room fencing according to AGX dispatch for daily prices" in {
        val rateCategory = aValidOTARateCategory
          .withPaymentModel(Some(ProtoPaymentModel.MerchantComission))
          .withDailyPrices(
            Seq(aValidOTADailyPrice.withCommissions(Seq(aValidOTACommission.withContractedCommission(1))).build))
        val channelRoomRate =
          aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).withCurrencyCode("JPY").build
        val hotelMeta = HotelMeta(6011203, 0)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelPrice =
          aValidOTAHotelPrice.withChannelRoomRateList(List(channelRoomRate)).withRoomTypes(Seq(protoRoomType))
        val hkFence = YplRateFence("HK", -1, 1)
        val thFence = YplRateFence("TH", -1, 1)
        val ctx = aValidYplContext.withRequest(
          aValidYplRequest
            .withFences(Map(YplMasterChannel.RTL -> Set(hkFence, thFence)))
            .withFencedAgxCommissions(new FencedAgxCommission(Map(
              hkFence -> Map(aValidHotelId.toLong -> YplAGXCommissionAdjustment(allDateAdjust =
                Some(YplAGXCommission(payAsYouGoCommission = 3)))),
              thFence -> Map(aValidHotelId.toLong -> YplAGXCommissionAdjustment(allDateAdjust =
                Some(YplAGXCommission(payAsYouGoCommission = 5)))),
            ))))
        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.size must_== 2
        res.rooms.exists(r =>
          r.fences == Set(hkFence) && r.commissionHolder.daily
            .exists(_._2.agxCommissionHolder.payAsYouGoCommission == 3d)) must_== true
        res.rooms.exists(r =>
          r.fences == Set(thFence) && r.commissionHolder.daily
            .exists(_._2.agxCommissionHolder.payAsYouGoCommission == 5d)) must_== true
      }

      "Add correct fencing to rooms (Dispatched Channel)" in {
        val rateCategory = aValidOTARateCategory.withPaymentModel(Some(ProtoPaymentModel.MerchantComission))
        val channelRoomRate =
          aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).withCurrencyCode("JPY").build
        val hotelMeta = HotelMeta(6011203, 0)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelPrice =
          aValidOTAHotelPrice.withChannelRoomRateList(List(channelRoomRate)).withRoomTypes(Seq(protoRoomType))
        val ctx = aValidYplContext.withRequest(
          aValidYplRequest.withFences(
            Map(
              YplMasterChannel.RTL -> Set(YplRateFence("HK", -1, 1), YplRateFence("TH", -1, 1)),
            )))
        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.head.fences must_== Set(YplRateFence("HK", -1, 1), YplRateFence("TH", -1, 1))
      }

      "Add correct fencing to rooms (Non Dispatched Channel)" in {
        val rateCategory = aValidOTARateCategory.withPaymentModel(Some(ProtoPaymentModel.MerchantComission))
        val channelRoomRate = aValidOTAChannelRoomRate
          .withChannelId(YplMasterChannel.NET.baseChannelId)
          .withRateCategories(Seq(rateCategory))
          .withCurrencyCode("JPY")
          .build
        val hotelMeta = HotelMeta(6011203, 0)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelPrice =
          aValidOTAHotelPrice.withChannelRoomRateList(List(channelRoomRate)).withRoomTypes(Seq(protoRoomType))
        val ctx = aValidYplContext.withRequest(
          aValidYplRequest.withFences(
            Map(
              YplMasterChannel.RTL -> Set(YplRateFence("HK", -1, 1)),
              YplMasterChannel.APS -> Set(YplRateFence("SG", -1, 1)),
            )))
        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.head.fences must_== Set(YplRateFence("HK", -1, 1), YplRateFence("SG", -1, 1))
      }

      "filter customer segment - if SCAL-1059 is B, return false if all fence origin is not allowed for customer segment" in {
        val fences = Map(YplMasterChannel.RTL -> Set(YplRateFence("TH", 1, 1)))
        val req = aValidYplRequest
          .withClientInfo(aValidClientInfo.withOrigin(Some("TH")).withLanguage(1).withVipLevel(Some(VipLevelType.GOLD)))
          .withFences(fences)
        implicit val ctx = aValidYplContext.withExperimentContext(forceAllBExperimentsContext()).withRequest(req).build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment = aValidOTARateCategoryBookingRestrictionCustomerSegment.withVipLevel(4)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelPrice =
          aValidOTAHotelPrice.withSupplierId(332).withRoomTypes(Seq(roomType)).withChannelRoomRate(Seq(channelRoomRate))
        val channels = YplDispatchChannels(masterChannels = Set(YplMasterChannel.RTL))

        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    aValidHotelInfo,
                                                    channels,
                                                    Map(fenceTH1 -> channels),
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.size must_== 0
      }

      "filter customer segment - return true if all fence origin is allowed for customer segment" in {
        val fences = Map(YplMasterChannel.RTL -> Set(YplRateFence("TH", 1, 1)))
        val req = aValidYplRequest
          .withClientInfo(
            aValidClientInfo.withOrigin(Some("TH")).withLanguage(1).withVipLevel(Some(VipLevelType.PLATINUM)))
          .withFences(fences)
        implicit val ctx = aValidYplContext.withExperimentContext(forceAllBExperimentsContext()).withRequest(req).build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment = aValidOTARateCategoryBookingRestrictionCustomerSegment.withVipLevel(4)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelPrice =
          aValidOTAHotelPrice.withSupplierId(332).withRoomTypes(Seq(roomType)).withChannelRoomRate(Seq(channelRoomRate))
        val channels = YplDispatchChannels(masterChannels = Set(YplMasterChannel.RTL))

        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    aValidHotelInfo,
                                                    channels,
                                                    Map(fenceTH1 -> channels),
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.size must_== 1
        res.rooms.head.fences must_== Set(YplRateFence("TH", 1, 1))
      }

      "filter customer segment - return false if all fence origin is not allowed for customer segment with default language and country" in {
        val fences = Map(YplMasterChannel.RTL -> Set(YplRateFence("TH", 1, 1)))
        val req = aValidYplRequest
          .withClientInfo(aValidClientInfo.withOrigin(Some("TH")).withLanguage(1).withVipLevel(Some(VipLevelType.GOLD)))
          .withFences(fences)
        implicit val ctx = aValidYplContext.withExperimentContext(forceAllBExperimentsContext()).withRequest(req).build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment =
          aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("00").withLanguageId(0).withVipLevel(4)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelPrice =
          aValidOTAHotelPrice.withSupplierId(332).withRoomTypes(Seq(roomType)).withChannelRoomRate(Seq(channelRoomRate))
        val channels = YplDispatchChannels(masterChannels = Set(YplMasterChannel.RTL))

        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    aValidHotelInfo,
                                                    channels,
                                                    Map(fenceTH1 -> channels),
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.size must_== 0
      }

      "filter customer segment - return true if all fence origin is allowed for customer segment with default language and country" in {
        val fences = Map(YplMasterChannel.RTL -> Set(YplRateFence("TH", 1, 1)))
        val req = aValidYplRequest
          .withClientInfo(
            aValidClientInfo.withOrigin(Some("TH")).withLanguage(1).withVipLevel(Some(VipLevelType.PLATINUM)))
          .withFences(fences)
        implicit val ctx = aValidYplContext.withExperimentContext(forceAllBExperimentsContext()).withRequest(req).build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment =
          aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("00").withLanguageId(0).withVipLevel(4)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelPrice =
          aValidOTAHotelPrice.withSupplierId(332).withRoomTypes(Seq(roomType)).withChannelRoomRate(Seq(channelRoomRate))
        val channels = YplDispatchChannels(masterChannels = Set(YplMasterChannel.RTL))

        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    aValidHotelInfo,
                                                    channels,
                                                    Map(fenceTH1 -> channels),
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.size must_== 1
        res.rooms.head.fences must_== Set(YplRateFence("TH", 1, 1))
      }

      "filter customer segment - return true if fence channel does not exist, 1 origin is allowed for customer segment" in {
        val fences = Map(YplMasterChannel.APS -> Set(YplRateFence("TH", 1, 1)))
        val req = aValidYplRequest
          .withClientInfo(
            aValidClientInfo.withOrigin(Some("TH")).withLanguage(1).withVipLevel(Some(VipLevelType.PLATINUM)))
          .withFences(fences)
        implicit val ctx = aValidYplContext.withExperimentContext(forceAllBExperimentsContext()).withRequest(req).build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment = aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("TH")
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelPrice =
          aValidOTAHotelPrice.withSupplierId(332).withRoomTypes(Seq(roomType)).withChannelRoomRate(Seq(channelRoomRate))
        val channels = YplDispatchChannels(masterChannels = Set(YplMasterChannel.RTL))

        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    aValidHotelInfo,
                                                    channels,
                                                    Map(fenceTH1 -> channels),
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.size must_== 1
        res.rooms.head.fences must_== Set(YplRateFence("TH", 1, 1))
      }

      "filter customer segment - return false if fence channel does not exist, no origin is allowed for customer segment" in {
        val fences = Map(YplMasterChannel.APS -> Set(YplRateFence("US", 1, 1)))
        val req = aValidYplRequest
          .withClientInfo(
            aValidClientInfo.withOrigin(Some("US")).withLanguage(1).withVipLevel(Some(VipLevelType.PLATINUM)))
          .withFences(fences)
        implicit val ctx = aValidYplContext.withExperimentContext(forceAllBExperimentsContext()).withRequest(req).build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment = aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("TH")
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelPrice =
          aValidOTAHotelPrice.withSupplierId(332).withRoomTypes(Seq(roomType)).withChannelRoomRate(Seq(channelRoomRate))
        val channels = YplDispatchChannels(masterChannels = Set(YplMasterChannel.RTL))

        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    aValidHotelInfo,
                                                    channels,
                                                    Map(fenceUS1 -> channels),
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.size must_== 0
      }

      "filter customer segment - return false if no origin is allowed for customer segment" in {
        val fences = Map(YplMasterChannel.RTL -> Set(YplRateFence("US", 1, 1), YplRateFence("UK", 1, 1)))
        val req = aValidYplRequest
          .withClientInfo(
            aValidClientInfo.withOrigin(Some("TH")).withLanguage(1).withVipLevel(Some(VipLevelType.PLATINUM)))
          .withFences(fences)
        implicit val ctx = aValidYplContext.withExperimentContext(forceAllBExperimentsContext()).withRequest(req).build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment = aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("TH")
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory))
        val hotelPrice =
          aValidOTAHotelPrice.withSupplierId(332).withRoomTypes(Seq(roomType)).withChannelRoomRate(Seq(channelRoomRate))
        val channels = YplDispatchChannels(masterChannels = Set(YplMasterChannel.RTL))

        val res = otaConverter.buildHotelEntryModel(hotelPrice,
                                                    aValidHotelInfo,
                                                    channels,
                                                    Map(fenceUS1 -> channels),
                                                    Map.empty,
                                                    false)(ctx)
        res.rooms.size must_== 0
      }
    }

    "Build Hotel Entry Model" should {
      "return correct hotel entry model" in {
        val hotelPrice = aValidOTAHotelPrice
        val hotelMeta = HotelMeta(6011203, 1)

        implicit val request = YplContext(
          YplRequest(
            "",
            DateTime.now,
            DateTime.now.plusDays(1),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))
        val result = otaConverter.buildHotelEntryModel(hotelPrice,
                                                       hotelMeta,
                                                       yplDispatchChannels,
                                                       fencedDispatchChannels,
                                                       Map.empty,
                                                       false)

        result.hotelId must_== 6011203
        result.supplierId must_== 27912
        result.paymentModel must_== PaymentModel.Merchant
        result.occupancyModel must_== YPLOccupancyModel.Full
        result.rateModel must_== RateModel.New
        result.surchargeRateType must_== RateType.NetExclusive
        result.metaData must_== hotelMeta
        result.retailHotel must_== None
        result.dispatchChannels must_== yplDispatchChannels
        result.dispatchChannelsPerFence must_== fencedDispatchChannels
        result.ratePlanLanguage must_== None
        result.rateReutilizations must_== List.empty
        result.reqOcc must_== YplReqOccByHotelAgePolicy(YplOccInfo(),
                                                        hotelMeta.agePolicy,
                                                        hotelMeta.ignoreRequestedNumberOfRoomsForNha)
        result.bookingCutoffTime must_== None
      }

      "return supplierContractedCommission of None if corresponding value in HotelPrice is -1" in {
        val hotelPrice = aValidOTAHotelPrice.copy(supplierContractedCommission = Some(-1))
        val hotelMeta = HotelMeta(6011203, 1)

        implicit val request = YplContext(
          YplRequest(
            "",
            DateTime.now,
            DateTime.now.plusDays(1),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))

        val result = otaConverter.buildHotelEntryModel(hotelPrice,
                                                       hotelMeta,
                                                       yplDispatchChannels,
                                                       fencedDispatchChannels,
                                                       Map.empty,
                                                       false)

        result.supplierContractedCommission must_== None
      }

      "return set correct supplierContractedCommission" in {
        val hotelPrice = aValidOTAHotelPrice.withSupplierContractedCommission(Some(5d))
        val hotelMeta = HotelMeta(6011203, 1)
        implicit val request = YplContext(
          YplRequest(
            "",
            DateTime.now,
            DateTime.now.plusDays(1),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))
        val result = otaConverter.buildHotelEntryModel(hotelPrice,
                                                       hotelMeta,
                                                       yplDispatchChannels,
                                                       fencedDispatchChannels,
                                                       Map.empty,
                                                       false)
        result.supplierContractedCommission must_== Some(5.00)
      }
      "return no supplierContractedCommission" in {
        val hotelPrice = aValidOTAHotelPrice
        val hotelMeta = HotelMeta(6011203, 1)
        implicit val request = YplContext(
          YplRequest(
            "",
            DateTime.now,
            DateTime.now.plusDays(1),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          ))
        val result = otaConverter.buildHotelEntryModel(hotelPrice,
                                                       hotelMeta,
                                                       yplDispatchChannels,
                                                       fencedDispatchChannels,
                                                       Map.empty,
                                                       false)
        result.supplierContractedCommission must_== None
      }

      trait OTAConverterScope extends Scope {
        val hotelPrice = aValidOTAHotelPrice
        val hotelMeta = HotelMeta(hotelId = 6011203, cityId = 1)

        def request: YplRequest

        implicit val yplContext = YplContext(request)

        def hotelPriceWithRepurposingData = hotelPrice.withRateRepurposeList(
          Seq(
            RateRepurposingData(targetChannel = 1,
                                channelDiscount = 56.34,
                                referenceChannel = 2,
                                discountType = 5,
                                sourceChannel = 3,
                                minAdvPurchase = Some(2),
                                maxAdvPurchase = Some(3))))

        val res = otaConverter.buildHotelEntryModel(hotelPriceWithRepurposingData,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)
      }

      "map rate utilisation to HotelEntryModel" in new OTAConverterScope {
        override def request: YplRequest = aValidYplRequest

        res.rateReutilizations shouldEqual List(
          YPLRateReutilizationEntry(
            sourceChannel = YplMasterChannel.NET,
            referenceChannel = YplMasterChannel.APS,
            targetChannel = YplMasterChannel.RTL,
            discountType = 5,
            flatChannelDiscount = 56.34,
            minAdvPurchase = Some(2),
            maxAdvPurchase = Some(3),
          ))
      }

      "convert agency Nocc Setting correctly when there is no data in protobuf" in new OTAConverterScope {
        override def hotelPriceWithRepurposingData: HotelPrice = hotelPrice
        override def request: YplRequest = aValidYplRequest

        res.agencyNoccSetting shouldEqual None
      }

      "convert agency Nocc Setting correctly when there is data in protobuf" in new OTAConverterScope {
        override def hotelPriceWithRepurposingData: HotelPrice = hotelPrice.withAgencyNocc(aValidOTAAgencyNocc)
        override def request: YplRequest = aValidYplRequest

        res.agencyNoccSetting shouldEqual None
      }

      "filter out invalid min and max advance purchase" in new OTAConverterScope {
        override def request: YplRequest = aValidYplRequest

        override def hotelPriceWithRepurposingData = hotelPrice.withRateRepurposeList(
          Seq(
            RateRepurposingData(targetChannel = 1,
                                channelDiscount = 56.34,
                                referenceChannel = 2,
                                discountType = 5,
                                sourceChannel = 3,
                                minAdvPurchase = Some(-1),
                                maxAdvPurchase = Some(-1))))

        res.rateReutilizations shouldEqual List(
          YPLRateReutilizationEntry(
            sourceChannel = YplMasterChannel.NET,
            referenceChannel = YplMasterChannel.APS,
            targetChannel = YplMasterChannel.RTL,
            discountType = 5,
            flatChannelDiscount = 56.34,
            minAdvPurchase = None,
            maxAdvPurchase = None,
          ))
      }

      "Read isUseConfiguredProcessingFee from HotelMeta" in {
        val hotelMeta = HotelMeta(hotelId = 6011203, cityId = 1, processingFeeOption = 1)
        val hp = aValidOTAHotelPrice.copy(isUseConfiguredProcessingFee = Some(true))

        val ctx: YplContext = YplContext(aValidYplRequest)
        val res = otaConverter.buildHotelEntryModel(hp,
                                                    hotelMeta,
                                                    yplDispatchChannels,
                                                    fencedDispatchChannels,
                                                    Map.empty,
                                                    false)(ctx)

        res.taxInfo.hotelTaxInfo.isConfigProcessingFees shouldEqual false
      }

    }

    "Filter bookable rooms" should {

      "Filter room if book from date is after today" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.plusDays(2).withTimeAtStartOfDay().getMillis
        val to = now.plusDays(3).withTimeAtStartOfDay().getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionDayPeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR")
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL)

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                    channelRoomRate,
                                                    roomType,
                                                    hotelMeta,
                                                    hotelPrice,
                                                    false,
                                                    false,
                                                    request.allFences,
                                                    None,
                                                    aValidYplDispatchChannels)

        roomEntry must beEmpty
      }

      "Filter room if book to date is before today" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.minusDays(3).withTimeAtStartOfDay().getMillis
        val to = now.minusDays(1).withTimeAtStartOfDay().getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionDayPeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR")
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL)

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                    channelRoomRate,
                                                    roomType,
                                                    hotelMeta,
                                                    hotelPrice,
                                                    false,
                                                    false,
                                                    request.allFences,
                                                    None,
                                                    aValidYplDispatchChannels)

        roomEntry must beEmpty
      }

      "Not filter room if book from date is before today and book to date is after today" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.minusDays(1).withTimeAtStartOfDay().getMillis
        val to = now.plusDays(1).withTimeAtStartOfDay().getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionDayPeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR")
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL)

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                    channelRoomRate,
                                                    roomType,
                                                    hotelMeta,
                                                    hotelPrice,
                                                    false,
                                                    false,
                                                    request.allFences,
                                                    None,
                                                    aValidYplDispatchChannels)

        roomEntry.length mustNotEqual 0
      }

      "Filter room when book time from is greater than now" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.plusHours(2).getMillis
        val to = now.plusHours(3).getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionTimePeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR")
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL)

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                    channelRoomRate,
                                                    roomType,
                                                    hotelMeta,
                                                    hotelPrice,
                                                    false,
                                                    false,
                                                    request.allFences,
                                                    None,
                                                    aValidYplDispatchChannels)

        roomEntry must beEmpty
      }

      "not filter room when book time from is less than now" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.minusHours(1).getMillis
        val to = now.plusHours(3).getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionTimePeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR")
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL)

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                    channelRoomRate,
                                                    roomType,
                                                    hotelMeta,
                                                    hotelPrice,
                                                    false,
                                                    false,
                                                    request.allFences,
                                                    None,
                                                    aValidYplDispatchChannels)

        roomEntry.length mustNotEqual 0
      }

      "not filter room when book time from is equal to now" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.getMillis
        val to = now.plusHours(3).getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionTimePeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR")
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL)

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                    channelRoomRate,
                                                    roomType,
                                                    hotelMeta,
                                                    hotelPrice,
                                                    false,
                                                    false,
                                                    request.allFences,
                                                    None,
                                                    aValidYplDispatchChannels)

        roomEntry.length mustNotEqual 0
      }

      "Filter room when book time to is less than now" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.minusHours(2).getMillis
        val to = now.minusHours(1).getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionTimePeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR")
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL)

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                    channelRoomRate,
                                                    roomType,
                                                    hotelMeta,
                                                    hotelPrice,
                                                    false,
                                                    false,
                                                    request.allFences,
                                                    None,
                                                    aValidYplDispatchChannels)

        roomEntry must beEmpty
      }

      "not filter room when book time to is equal than now" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.minusHours(1).getMillis
        val to = now.getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionTimePeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR")
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL)

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                    channelRoomRate,
                                                    roomType,
                                                    hotelMeta,
                                                    hotelPrice,
                                                    false,
                                                    false,
                                                    request.allFences,
                                                    None,
                                                    aValidYplDispatchChannels)

        roomEntry.length mustNotEqual 0
      }

      "Filter room when book on has no valid day" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val bookingRestriction: RateCategoryBookingRestriction =
          aValidOTARateCategoryBookingRestriction.withBookOn("0000000")
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR")
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL)

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                    channelRoomRate,
                                                    roomType,
                                                    hotelMeta,
                                                    hotelPrice,
                                                    false,
                                                    false,
                                                    request.allFences,
                                                    None,
                                                    aValidYplDispatchChannels)

        roomEntry must beEmpty
      }

      "Check for validity of bookingDate w.r.t minAdvance and maxAdvance restrictions" should {
        val now = DateTime.now.withTimeAtStartOfDay()
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR")
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL)
        val bookingDate = now.plusHours(7)

        "Check for minAdvance" should {
          val bookingRestriction: RateCategoryBookingRestriction =
            aValidOTARateCategoryBookingRestriction.withMinAdvance(3)
          val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)

          "Filter rooms when bookingDate is greater than checkin - minAdvance" in {
            val checkIn = now.plusDays(2)
            val checkOut = now.plusDays(5)
            val request = YplRequest(
              "",
              checkIn,
              checkOut,
              bookingDate = bookingDate,
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            implicit val ctx = aValidYplContext.withRequest(request).build
            val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                        channelRoomRate,
                                                        roomType,
                                                        hotelMeta,
                                                        hotelPrice,
                                                        false,
                                                        false,
                                                        request.allFences,
                                                        None,
                                                        aValidYplDispatchChannels)

            roomEntry must beEmpty
          }

          "Not Filter rooms when bookingDate is equal to checkin - minAdvance" in {
            val checkIn = now.plusDays(3)
            val checkOut = now.plusDays(5)
            val request = YplRequest(
              "",
              checkIn,
              checkOut,
              bookingDate = bookingDate,
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            implicit val ctx = aValidYplContext.withRequest(request).build
            val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                        channelRoomRate,
                                                        roomType,
                                                        hotelMeta,
                                                        hotelPrice,
                                                        false,
                                                        false,
                                                        request.allFences,
                                                        None,
                                                        aValidYplDispatchChannels)

            roomEntry.length mustNotEqual 0
          }

          "Not Filter rooms when bookingDate is less to checkin - minAdvance" in {
            val checkIn = now.plusDays(4)
            val checkOut = now.plusDays(5)
            val request = YplRequest(
              "",
              checkIn,
              checkOut,
              bookingDate = bookingDate,
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            implicit val ctx = aValidYplContext.withRequest(request).build
            val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                        channelRoomRate,
                                                        roomType,
                                                        hotelMeta,
                                                        hotelPrice,
                                                        false,
                                                        false,
                                                        request.allFences,
                                                        None,
                                                        aValidYplDispatchChannels)

            roomEntry.length mustNotEqual 0
          }
        }

        "Check for maxAdvance" should {
          val bookingRestriction: RateCategoryBookingRestriction =
            aValidOTARateCategoryBookingRestriction.withMaxAdvance(3)
          val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)

          "supplier is JTB" should {
            val hotelPriceJTB = hotelPrice.copy(supplierId = DMC.JTBWL)

            "Filter rooms when bookingDate is less than checkOut - maxAdvance" in {
              val checkIn = now.plusDays(1)
              val checkOut = now.plusDays(5)
              val request = YplRequest(
                "",
                checkIn,
                checkOut,
                bookingDate = bookingDate,
                supplierFeatures = aValidSupplierFeatures,
                whitelabelSetting = aValidwhitelabelSetting,
                fences = aValidYplRequestFences,
              )
              implicit val ctx: YplContext = aValidYplContext.withRequest(request).build
              val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                          channelRoomRate,
                                                          roomType,
                                                          hotelMeta,
                                                          hotelPriceJTB,
                                                          false,
                                                          false,
                                                          Set.empty,
                                                          None,
                                                          aValidYplDispatchChannels)

              roomEntry.length mustEqual 0
            }

            "Not Filter rooms when bookingDate is equal to checkOut - maxAdvance" in {
              val checkIn = now.plusDays(1)
              val checkOut = now.plusDays(4)
              val request = YplRequest(
                "",
                checkIn,
                checkOut,
                bookingDate = bookingDate,
                supplierFeatures = aValidSupplierFeatures,
                whitelabelSetting = aValidwhitelabelSetting,
                fences = aValidYplRequestFences,
              )
              implicit val ctx: YplContext = aValidYplContext.withRequest(request).build
              val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                          channelRoomRate,
                                                          roomType,
                                                          hotelMeta,
                                                          hotelPriceJTB,
                                                          false,
                                                          false,
                                                          request.allFences,
                                                          None,
                                                          aValidYplDispatchChannels)

              roomEntry.length mustNotEqual 0
            }

            "Not Filter rooms when bookingDate is greater than checkOut - maxAdvance" in {
              val checkIn = now.plusDays(1)
              val checkOut = now.plusDays(3)
              val request = YplRequest(
                "",
                checkIn,
                checkOut,
                bookingDate = bookingDate,
                supplierFeatures = aValidSupplierFeatures,
                whitelabelSetting = aValidwhitelabelSetting,
                fences = aValidYplRequestFences,
              )
              implicit val ctx: YplContext = aValidYplContext.withRequest(request).build
              val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                          channelRoomRate,
                                                          roomType,
                                                          hotelMeta,
                                                          hotelPriceJTB,
                                                          false,
                                                          false,
                                                          request.allFences,
                                                          None,
                                                          aValidYplDispatchChannels)

              roomEntry.length mustNotEqual 0
            }
          }

          "supplier is not JTB" should {
            "Filter rooms when bookingDate is less than checkIn - maxAdvance" in {
              val checkIn = now.plusDays(4)
              val checkOut = checkIn.plusDays(1)
              val request = YplRequest(
                "",
                checkIn,
                checkOut,
                bookingDate = bookingDate,
                supplierFeatures = aValidSupplierFeatures,
                whitelabelSetting = aValidwhitelabelSetting,
                fences = aValidYplRequestFences,
              )
              implicit val ctx: YplContext = aValidYplContext.withRequest(request).build
              val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                          channelRoomRate,
                                                          roomType,
                                                          hotelMeta,
                                                          hotelPrice,
                                                          false,
                                                          false,
                                                          Set.empty,
                                                          None,
                                                          aValidYplDispatchChannels)

              roomEntry.length mustEqual 0
            }

            "Not Filter rooms when bookingDate is equal to checkIn - maxAdvance" in {
              val checkIn = now.plusDays(3)
              val checkOut = checkIn.plusDays(1)
              val request = YplRequest(
                "",
                checkIn,
                checkOut,
                bookingDate = bookingDate,
                supplierFeatures = aValidSupplierFeatures,
                whitelabelSetting = aValidwhitelabelSetting,
                fences = aValidYplRequestFences,
              )
              implicit val ctx: YplContext = aValidYplContext.withRequest(request).build
              val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                          channelRoomRate,
                                                          roomType,
                                                          hotelMeta,
                                                          hotelPrice,
                                                          false,
                                                          false,
                                                          request.allFences,
                                                          None,
                                                          aValidYplDispatchChannels)

              roomEntry.length mustNotEqual 0
            }

            "Not Filter rooms when bookingDate is greater than checkIn - maxAdvance" in {
              val checkIn = now.plusDays(2)
              val checkOut = checkIn.plusDays(1)
              val request = YplRequest(
                "",
                checkIn,
                checkOut,
                bookingDate = bookingDate,
                supplierFeatures = aValidSupplierFeatures,
                whitelabelSetting = aValidwhitelabelSetting,
                fences = aValidYplRequestFences,
              )
              implicit val ctx: YplContext = aValidYplContext.withRequest(request).build
              val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                          channelRoomRate,
                                                          roomType,
                                                          hotelMeta,
                                                          hotelPrice,
                                                          false,
                                                          false,
                                                          request.allFences,
                                                          None,
                                                          aValidYplDispatchChannels)

              roomEntry.length mustNotEqual 0
            }
          }
        }

        "Not filter rooms when minAdvance and maxAdvance are None" in {
          val bookingRestriction: RateCategoryBookingRestriction =
            aValidOTARateCategoryBookingRestriction.withMaxAdvance(4)
          val rateCategory = aValidOTARateCategory
            .withBookingRestriction(bookingRestriction)
            .withPromotions(Seq(aValidOTAPromotion.withRestriction(aValidOTAPromotionRestrictionMinMaxNegative)))

          "not filter room when min and max advance are None" in {
            val checkIn = now.plusDays(1)
            val checkOut = now.plusDays(2)
            val request = YplRequest(
              "",
              checkIn,
              checkOut,
              bookingDate = bookingDate,
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            implicit val ctx = aValidYplContext.withRequest(request).build
            val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                        channelRoomRate,
                                                        roomType,
                                                        hotelMeta,
                                                        hotelPrice,
                                                        false,
                                                        false,
                                                        request.allFences,
                                                        None,
                                                        aValidYplDispatchChannels)

            roomEntry.length mustNotEqual 0
          }
        }
      }

      "not filter when Room Entry Channel Id is in channels Request" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.minusHours(1).getMillis
        val to = now.plusHours(3).getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionTimePeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR").withChannelId(Channel.APO)
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val channels = Set(Channel.RTL, Channel.APO)

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter.buildRoomEntry(rateCategory,
                                                    channelRoomRate,
                                                    roomType,
                                                    hotelMeta,
                                                    hotelPrice,
                                                    false,
                                                    false,
                                                    request.allFences,
                                                    None,
                                                    aValidYplDispatchChannels)

        roomEntry.length mustNotEqual 0
      }

      "filter when Room Entry Channel Id is not in channels Request" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.minusHours(1).getMillis
        val to = now.plusHours(3).getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionTimePeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR").withChannelId(Channel.APO)
        val rateCategory = aValidOTARateCategory.withBookingRestriction(bookingRestriction)
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val dispatchChannels = YplDispatchChannels(masterChannels = Set(YplMasterChannel.RTL))

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = Map(YplMasterChannel.RTL -> Set(aValidRateFence)),
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter
          .buildRoomEntry(rateCategory,
                          channelRoomRate,
                          roomType,
                          hotelMeta,
                          hotelPrice,
                          false,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)
          .head

        val result = otaConverter.filterBookableOtaRooms(roomEntry, dispatchChannels = dispatchChannels)
        result must_== false
      }

      "filter out when Room Entry has empty price" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.minusHours(1).getMillis
        val to = now.plusHours(3).getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionTimePeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR").withChannelId(Channel.APO)
        val singleOccPrice = aValidOTAOccupancyPrice.withOccupancy(1)
        val doubleOccPrice = aValidOTAOccupancyPrice.withOccupancy(2)
        val dailyPrice = aValidOTADailyPrice.withPrices(Nil)
        val rateCategory =
          aValidOTARateCategory.withBookingRestriction(bookingRestriction).withDailyPrices(Seq(dailyPrice))
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice.withOccupancyModel(OccupanyModel.FullPatternLengthOfStay)
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val dispatchChannels = YplDispatchChannels(
          masterChannels = Set(YplMasterChannel.RTL, YplMasterChannel.APO),
        )

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          occ = YplOccInfo(_adults = Some(3), _rooms = Some(1)),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build
        val roomEntry = otaConverter
          .buildRoomEntry(rateCategory,
                          channelRoomRate,
                          roomType,
                          hotelMeta,
                          hotelPrice,
                          false,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)
          .head
        otaConverter.filterBookableOtaRooms(roomEntry, dispatchChannels) must_== false
      }

      "filter occupancy" in {
        val now = DateTime.now.withTimeAtStartOfDay()
        val from = now.minusHours(1).getMillis
        val to = now.plusHours(3).getMillis
        val bookingRestriction: RateCategoryBookingRestriction = aValidOTARateCategoryBookingRestriction
          .withBookingIntervals(Seq(aValidOTARateCategoryBookingRestrictionTimePeriodInterval.withFrom(from).withTo(to)))
        val channelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR").withChannelId(Channel.APO)
        val singleOccPrice = aValidOTAOccupancyPrice.withOccupancy(1)
        val doubleOccPrice = aValidOTAOccupancyPrice.withOccupancy(2)
        val dailyPrice = aValidOTADailyPrice.withPrices(Seq(singleOccPrice, doubleOccPrice))
        val rateCategory =
          aValidOTARateCategory.withBookingRestriction(bookingRestriction).withDailyPrices(Seq(dailyPrice))
        val protoRoomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRoomId("STD").withSupplierRatePlanId("BAR")
        val hotelMeta = HotelMeta(6011203, 0)
        val hotelPrice = aValidOTAHotelPrice.withOccupancyModel(OccupanyModel.FullRate)
        val roomType = Map(
          OTARoomTypeKey(protoRoomType.roomTypeId,
                         protoRoomType.supplierRoomId,
                         protoRoomType.supplierRatePlanId) -> protoRoomType)
        val dispatchChannels = YplDispatchChannels(
          masterChannels = Set(YplMasterChannel.RTL, YplMasterChannel.APO),
        )

        val request = YplRequest(
          "",
          now,
          now.plusDays(1),
          bookingDate = now.plusHours(7),
          occ = YplOccInfo(_adults = Some(2), _children = Some(YplChildren(List(Some(5)))), _rooms = Some(1)),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build

        val roomEntry = otaConverter
          .buildRoomEntry(rateCategory,
                          channelRoomRate,
                          roomType,
                          hotelMeta,
                          hotelPrice,
                          false,
                          false,
                          request.allFences,
                          None,
                          aValidYplDispatchChannels)
          .head

        val result = otaConverter.filterBookableOtaRooms(room = roomEntry, dispatchChannels = dispatchChannels)
        result must_== true
      }

      "filter PriceEntry ByOccupancy And OccupancyModel" in {
        val priceEntry = aValidPriceEntry.withOccupancy(3)

        def roomTypeEntry = aValidRoomTypeEntry.withMaxOccupancy(3)

        otaConverter.filterPriceEntryByOccupancyAndOccupancyModel(priceEntry,
                                                                  3,
                                                                  YPLOccupancyModel.FullPatternLengthOfStay,
                                                                  aValidReqOcc.withAdults(3),
                                                                  roomTypeEntry) must_== true
        otaConverter.filterPriceEntryByOccupancyAndOccupancyModel(priceEntry,
                                                                  3,
                                                                  YPLOccupancyModel.FullPatternLengthOfStay,
                                                                  aValidReqOcc.withAdults(0),
                                                                  roomTypeEntry) must_== true
        otaConverter.filterPriceEntryByOccupancyAndOccupancyModel(priceEntry,
                                                                  4,
                                                                  YPLOccupancyModel.FullPatternLengthOfStay,
                                                                  aValidReqOcc.withAdults(3),
                                                                  roomTypeEntry) must_== false
        otaConverter.filterPriceEntryByOccupancyAndOccupancyModel(priceEntry,
                                                                  4,
                                                                  YPLOccupancyModel.Full,
                                                                  aValidReqOcc.withAdults(3),
                                                                  roomTypeEntry) must_== true
        otaConverter.filterPriceEntryByOccupancyAndOccupancyModel(priceEntry,
                                                                  3,
                                                                  YPLOccupancyModel.FullPatternLengthOfStay,
                                                                  aValidReqOcc.withAdults(3),
                                                                  roomTypeEntry.withMaxOccupancy(2)) must_== false
      }

      "filter customer segment - if bcom ratecategory customer segments exist return false if user origin/language not in allowed customer segments" in {
        implicit val ctx = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withFences(Map(YplMasterChannel.RTL -> Set(YplRateFence("TH", -1, 1))))
              .withClientInfo(aValidClientInfo.withLanguage(8)))
          .build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment =
          aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("CN").withLanguageId(8)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val rateCategoryFences = otaConverter
          .tagFencesToRoomRateCategory(aValidOTAHotelPrice.withSupplierId(3038),
                                       aValidOTAChannelRoomRate,
                                       rateCategory,
                                       false)
          .fences
        rateCategoryFences must beEmpty
      }

      "filter customer segment - if bcom ratecategory customer segments exist return true if user origin/language in allowed customer segments" in {
        implicit val ctx = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withFences(Map(YplMasterChannel.RTL -> Set(YplRateFence("CN", -1, 8))))
              .withClientInfo(aValidClientInfo.withLanguage(8)))
          .build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment =
          aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("CN").withLanguageId(8)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val fences = otaConverter
          .tagFencesToRoomRateCategory(aValidOTAHotelPrice.withSupplierId(3038),
                                       aValidOTAChannelRoomRate,
                                       rateCategory,
                                       false)
          .fences
        fences.size mustNotEqual 0
      }

      "filter customer segment - if bcom ratecategory customer segments exist return false if user language in allowed customer segments but not origin" in {
        implicit val ctx = aValidYplContext
          .withRequest(aValidYplRequest.withClientInfo(aValidClientInfo.withOrigin(Some("TH")).withLanguage(8)))
          .build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment =
          aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("CN").withLanguageId(8)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val fences = otaConverter
          .tagFencesToRoomRateCategory(aValidOTAHotelPrice.withSupplierId(3038),
                                       aValidOTAChannelRoomRate,
                                       rateCategory,
                                       false)
          .fences
        fences must beEmpty
      }

      "filter customer segment - if bcom ratecategory customer segments exist return false if user origin in allowed customer segments but not language" in {
        implicit val ctx = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withFences(Map(YplMasterChannel.RTL -> Set(YplRateFence("CN", -1, 1))))
              .withClientInfo(aValidClientInfo.withLanguage(8)))
          .build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment =
          aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("CN").withLanguageId(8)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val roomTypeMap =
          Map(OTARoomTypeKey(roomType.roomTypeId, roomType.supplierRoomId, roomType.supplierRatePlanId) -> roomType)
        val roomEntry = otaConverter.buildRoomEntry(
          rateCategory,
          aValidOTAChannelRoomRate,
          roomTypeMap,
          aValidHotelInfo,
          aValidOTAHotelPrice.withSupplierId(3038),
          false,
          false,
          Set.empty,
          None,
          aValidYplDispatchChannels,
        )
        roomEntry must beEmpty
      }

      "filter customer segment - if not bcom ratecategory customer segments exist return true as we treated customersegments as empty" in {
        implicit val ctx = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withFences(Map(YplMasterChannel.RTL -> Set(YplRateFence("CN", -1, 1))))
              .withClientInfo(aValidClientInfo.withLanguage(8)))
          .build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment =
          aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("CN").withLanguageId(8)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val roomTypeMap =
          Map(OTARoomTypeKey(roomType.roomTypeId, roomType.supplierRoomId, roomType.supplierRatePlanId) -> roomType)
        val fences = otaConverter
          .tagFencesToRoomRateCategory(aValidOTAHotelPrice.withSupplierId(27901),
                                       aValidOTAChannelRoomRate,
                                       rateCategory,
                                       false)
          .fences
        fences.size mustNotEqual 0
      }

      "filter customer segment - if ycs ratecategory customer segments exist CPL-79 is B return true if user vip level allowed customer segments" in {
        implicit val ctx = aValidYplContext
          .withExperimentContext(forceBExperimentContext(YplExperiments.CHECK_VIP_CUSTOMER_SEGMENT,
                                                         YplExperiments.KILL_SWITCH_RATECATEGORY_CUSTOMER_SEGMENT))
          .withRequest(aValidYplRequest.withClientInfo(
            aValidClientInfo.withOrigin(Some("TH")).withLanguage(1).withVipLevel(Some(VipLevelType.PLATINUM))))
          .build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment = aValidOTARateCategoryBookingRestrictionCustomerSegment.withVipLevel(4)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val roomTypeMap =
          Map(OTARoomTypeKey(roomType.roomTypeId, roomType.supplierRoomId, roomType.supplierRatePlanId) -> roomType)
        val roomEntry = otaConverter.buildRoomEntry(
          rateCategory,
          aValidOTAChannelRoomRate,
          roomTypeMap,
          aValidHotelInfo,
          aValidOTAHotelPrice.withSupplierId(332),
          false,
          false,
          ctx.request.allFences,
          None,
          aValidYplDispatchChannels,
        )
        roomEntry.length mustNotEqual 0
      }

      "filter customer segment - if ycs ratecategory customer segments exist CPL-79 is B return true if user vip level allowed customer segments with default language and country" in {
        implicit val ctx = aValidYplContext
          .withExperimentContext(forceBExperimentContext(YplExperiments.CHECK_VIP_CUSTOMER_SEGMENT,
                                                         YplExperiments.KILL_SWITCH_RATECATEGORY_CUSTOMER_SEGMENT))
          .withRequest(
            aValidYplRequest.withClientInfo(aValidClientInfo.withLanguage(1).withVipLevel(Some(VipLevelType.PLATINUM))))
          .build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment =
          aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("00").withLanguageId(0).withVipLevel(4)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val roomType = aValidOTARoomType.withSupplierRatePlanId(supplierRp)
        val roomTypeMap =
          Map(OTARoomTypeKey(roomType.roomTypeId, roomType.supplierRoomId, roomType.supplierRatePlanId) -> roomType)
        val fences = otaConverter
          .tagFencesToRoomRateCategory(aValidOTAHotelPrice.withSupplierId(332),
                                       aValidOTAChannelRoomRate,
                                       rateCategory,
                                       false)
          .fences
        val roomEntry = otaConverter.buildRoomEntry(
          rateCategory,
          aValidOTAChannelRoomRate,
          roomTypeMap,
          aValidHotelInfo,
          aValidOTAHotelPrice.withSupplierId(332),
          false,
          false,
          ctx.request.allFences,
          None,
          aValidYplDispatchChannels,
        )
        roomEntry.length mustNotEqual 0
        fences.size mustNotEqual 0
      }

      "filter customer segment - if ycs ratecategory customer segments exist CPL-79 is B return false if user vip level not allowed customer segments" in {
        implicit val ctx = aValidYplContext
          .withExperimentContext(forceAllBExperimentsContext())
          .withRequest(
            aValidYplRequest.withClientInfo(aValidClientInfo.withLanguage(1).withVipLevel(Some(VipLevelType.GOLD))))
          .build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment = aValidOTARateCategoryBookingRestrictionCustomerSegment.withVipLevel(4)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val fences = otaConverter
          .tagFencesToRoomRateCategory(aValidOTAHotelPrice.withSupplierId(332),
                                       aValidOTAChannelRoomRate,
                                       rateCategory,
                                       false)
          .fences
        fences must beEmpty
      }

      "filter customer segment - if ycs ratecategory customer segments exist CPL-79 is B return false if user vip level not allowed customer segments with default language and country" in {
        implicit val ctx = aValidYplContext
          .withExperimentContext(forceAllBExperimentsContext())
          .withRequest(
            aValidYplRequest.withClientInfo(aValidClientInfo.withLanguage(1).withVipLevel(Some(VipLevelType.GOLD))))
          .build
        val supplierRp = "27391011_105556508_0_1_0"
        val customerSegment =
          aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("00").withLanguageId(0).withVipLevel(4)
        val bookingRestriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(customerSegment))
        val rateCategory = aValidOTARateCategory
          .withBookingRestriction(Some(bookingRestriction))
          .withSupplierRateInfo(Some(aValidOTASupplierRateInfo.withSupplierRatePlan(Some(supplierRp))))
        val fences = otaConverter
          .tagFencesToRoomRateCategory(aValidOTAHotelPrice.withSupplierId(332),
                                       aValidOTAChannelRoomRate,
                                       rateCategory,
                                       false)
          .fences
        fences must beEmpty
      }

    }

    "Filter PriceEntry By Occupancy And OccupancyModel" should {

      trait FilterPriceEntryScope extends Scope {
        val now = DateTime.now.withTimeAtStartOfDay()
        val request = YplRequest(
          searchId = "",
          checkIn = now,
          checkOut = now.plusDays(1),
          bookingDate = now.plusHours(7),
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx = aValidYplContext.withRequest(request).build

        def priceEntry = aValidPriceEntry.withOccupancy(1).build

        def occupancy = 2

        def occupancyModel: YPLOccupancyModel = Full

        def reqOcc = aValidReqOcc.build

        def roomTypeEntry = aValidRoomTypeEntry.withMaxOccupancy(2).build

        val result = otaConverter.filterPriceEntryByOccupancyAndOccupancyModel(priceEntry,
                                                                               occupancy,
                                                                               occupancyModel,
                                                                               reqOcc,
                                                                               roomTypeEntry)
      }

      "not filter when occupancy model is Full Rate, and price occupancy equal to MaxOccupancy" in new FilterPriceEntryScope {
        override def priceEntry = aValidPriceEntry.withOccupancy(1).build

        override def occupancyModel = Full

        override def reqOcc = aValidReqOcc.build

        override def roomTypeEntry = aValidRoomTypeEntry.withMaxOccupancy(1).build

        result should_=== true
      }

      "not filter when occupancy model is Full Rate, and price occupancy less than MaxOccupancy" in new FilterPriceEntryScope {
        override def priceEntry = aValidPriceEntry.withOccupancy(1).build

        override def occupancyModel = Full

        override def reqOcc = aValidReqOcc.build

        override def roomTypeEntry = aValidRoomTypeEntry.withMaxOccupancy(2).build

        result should_=== true
      }

      "filter when occupancy model is Full Rate, and price occupancy more than MaxOccupancy" in new FilterPriceEntryScope {
        override def priceEntry = aValidPriceEntry.withOccupancy(3).build

        override def occupancyModel = Full

        override def reqOcc = aValidReqOcc.build

        override def roomTypeEntry = aValidRoomTypeEntry.withMaxOccupancy(2).build

        result should_=== false
      }

      "not filter when occupancy model is fplos, request is free occupancy, and price occupancy less than MaxOccupancy" in new FilterPriceEntryScope {
        override def priceEntry = aValidPriceEntry.withOccupancy(1).build

        override def occupancyModel = Fplos

        override def reqOcc = aValidReqOcc.withAdults(0).build

        override def roomTypeEntry = aValidRoomTypeEntry.withMaxOccupancy(4).build

        result should_=== true
      }

      "filter when occupancy model is fplos, request is free occupancy, and price occupancy more than MaxOccupancy" in new FilterPriceEntryScope {
        override def priceEntry = aValidPriceEntry.withOccupancy(7).build

        override def occupancyModel = Fplos

        override def reqOcc = aValidReqOcc.withAdults(0).build

        override def roomTypeEntry = aValidRoomTypeEntry.withMaxOccupancy(4).build

        result should_=== false
      }

      "filter when occupancy model is fplos, request is specific occupancy, and price occupancy more than MaxOccupancy" in new FilterPriceEntryScope {
        override def priceEntry = aValidPriceEntry.withOccupancy(3).build

        override def occupancyModel = Fplos

        override def reqOcc = aValidReqOcc.withAdults(2).withRooms(1).build

        override def roomTypeEntry = aValidRoomTypeEntry.withMaxOccupancy(2).build

        result should_=== false
      }

      "not filter when occupancy model is fplos, request is specific occupancy, price occupancy less than MaxOccupancy and equal to occupancy" in new FilterPriceEntryScope {
        override def priceEntry = aValidPriceEntry.withOccupancy(2).build

        override def occupancy = 2

        override def occupancyModel = Fplos

        override def reqOcc = aValidReqOcc.withAdults(2).withRooms(1).build

        override def roomTypeEntry = aValidRoomTypeEntry.withMaxOccupancy(5).build

        result should_=== true
      }

      "filter when occupancy model is fplos, request is specific occupancy, price occupancy less than MaxOccupancy and not equal to occupancy" in new FilterPriceEntryScope {
        override def priceEntry = aValidPriceEntry.withOccupancy(2).build

        override def occupancy = 3

        override def occupancyModel = Fplos

        override def reqOcc = aValidReqOcc.withAdults(2).withRooms(1).build

        override def roomTypeEntry = aValidRoomTypeEntry.withMaxOccupancy(5).build

        result should_=== false
      }
    }

    "filterRateFenceByCustomerSegment" should {
      val req = aValidYplRequest
      val ctx = aValidYplContext.withRequest(req).withExperimentContext(forcedFromRequestExperimentContext(req))
      val fences = Set(YplRateFence("TH", 1, 1), YplRateFence("US", 2, 1))
      val segment = aValidOTARateCategoryBookingRestrictionCustomerSegment.withCountryCode("TH")

      "not filter fences when not populate customer segment" in {
        val restriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(segment))
        val result = otaConverter.filterRateFenceByCustomerSegment(fences, Some(restriction), false, false)(ctx)
        result must_== Set(YplRateFence("TH", 1, 1), YplRateFence("US", 2, 1))
      }

      "not filter fences when customer segment is empty" in {
        val restriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List.empty)
        val result = otaConverter.filterRateFenceByCustomerSegment(fences, Some(restriction), true, false)(ctx)
        result must_== Set(YplRateFence("TH", 1, 1), YplRateFence("US", 2, 1))
      }

      "not filter fences when booking restriction is empty" in {
        val result = otaConverter.filterRateFenceByCustomerSegment(fences, None, true, false)(ctx)
        result must_== Set(YplRateFence("TH", 1, 1), YplRateFence("US", 2, 1))
      }

      "return only TH fence when customer segment is TH" in {
        val restriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(segment))
        val result = otaConverter.filterRateFenceByCustomerSegment(fences, Some(restriction), true, false)(ctx)
        result must_== Set(YplRateFence("TH", 1, 1))
      }

      "call CidToOriginMapper when isAboCidToOriginMappingEnabled is true" in {
        val mockCidToOriginMapper = mock[CidToOriginMapper]
        val mockOriginManager = mock[OriginManager]
        when(mockCidToOriginMapper.getOriginByCid(1587502)).thenReturn(Some("TH"))
        when(mockOriginManager.getOriginByCid(Some(1587502))).thenReturn(Some("TH"))
        when(mockOriginManager.getOriginByCidAndHotelCountry(any(), any())).thenReturn(Some("TH"))

        val testConverter = new {} with OTAConverter {
          override def getOriginByCid(cid: Option[Int]): Option[String] = mockOriginManager.getOriginByCid(cid)
          override def getOriginByCid(cid: Int): Option[String] = mockCidToOriginMapper.getOriginByCid(cid)
          override def getOriginByCidAndHotelCountry(cid: Option[Int], hotelCountry: Option[String]): Option[String] =
            mockOriginManager.getOriginByCidAndHotelCountry(cid, hotelCountry)
        }

        val testFences = Set(YplRateFence("TH", 1587502, 1))
        val restriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(segment))

        testConverter.filterRateFenceByCustomerSegment(testFences, Some(restriction), true, true)(ctx)

        verify(mockOriginManager, times(0)).getOriginByCid(any())
        verify(mockCidToOriginMapper, times(1)).getOriginByCid(1587502)
        ok
      }

      "call OriginManager when isAboCidToOriginMappingEnabled is false" in {
        val mockCidToOriginMapper = mock[CidToOriginMapper]
        val mockOriginManager = mock[OriginManager]
        when(mockCidToOriginMapper.getOriginByCid(1587502)).thenReturn(Some("TH"))
        when(mockOriginManager.getOriginByCid(Some(1587502))).thenReturn(Some("TH"))
        when(mockOriginManager.getOriginByCidAndHotelCountry(any(), any())).thenReturn(Some("TH"))

        val testConverter = new {} with OTAConverter {
          override def getOriginByCid(cid: Option[Int]): Option[String] = mockOriginManager.getOriginByCid(cid)
          override def getOriginByCid(cid: Int): Option[String] = mockCidToOriginMapper.getOriginByCid(cid)
          override def getOriginByCidAndHotelCountry(cid: Option[Int], hotelCountry: Option[String]): Option[String] =
            mockOriginManager.getOriginByCidAndHotelCountry(cid, hotelCountry)
        }

        val testFences = Set(YplRateFence("TH", 1587502, 1))
        val restriction = aValidOTARateCategoryBookingRestriction.withCustomerSegments(List(segment))

        testConverter.filterRateFenceByCustomerSegment(testFences, Some(restriction), true, false)(ctx)

        verify(mockOriginManager, times(1)).getOriginByCid(Some(1587502))
        verify(mockCidToOriginMapper, times(0)).getOriginByCid(any())
        ok
      }
    }

    "getUpdatedBenefitsWithDiscount" should {
      "update benefit valuation correctly" in {
        val benefitList =
          List(BenefitEntry(1, 0.0d, 0, None), BenefitEntry(Benefits.RoHDescription, 0.0, 0, None, None, 0))
        val propOfferBenefits = List(
          com.agoda.supply.calc.proto.Benefit(benefitId = 1, benefitValuationType = Some(1), benefitValueInUSD = Some(20)))
        otaConverter.getUpdatedBenefitsWithDiscount(benefitList, propOfferBenefits).map(_.benefitValue) should_==
          List(Some(BenefitValueEntry(20.0, 1, true)), None)
      }

      "not update benefit valuation when not presenet" in {
        val benefitList =
          List(BenefitEntry(1, 0.0d, 0, None), BenefitEntry(Benefits.RoHDescription, 0.0, 0, None, None, 0))
        val propOfferBenefits = List.empty
        otaConverter.getUpdatedBenefitsWithDiscount(benefitList, propOfferBenefits).map(_.benefitValue) should_==
          List(None, None)
      }
    }

    "getCommissionHolderForOTA" should {
      val rateCategory = aValidOTARateCategory
      val supplierId: SupplierId = aValidSupplierId
      val channel = aValidYplChannel
      val inventoryType = InventoryType.Agoda
      val hInfo = HotelMeta(aValidHotelId, 0)
      val wlFeature = None
      val fireDrillProto: Option[FireDrillProto] = None
      val dispatchChannels = aValidYplDispatchChannels
      val yplAGXCommissionAdjustment = YplAGXCommissionAdjustmentWrapper.noAgxCommissionAdjustment
      val supplierCommissionMapping: SupplierCommissionMapping = Map.empty[SupplierId, Double]
      val fences = Set.empty[YplRateFence]

      "return populated commission holder" in {
        implicit val ctx = aValidYplContext
        val result = otaConverter.getCommissionHolderForOTA(
          rateCategory,
          supplierId,
          inventoryType,
          channel,
          hInfo,
          wlFeature,
          fences,
          fireDrillProto,
          dispatchChannels,
          yplAGXCommissionAdjustment,
          supplierCommissionMapping,
          Map.empty,
          Some(4.0),
        )(ctx)
        result.daily.nonEmpty should_== (true)
        result.supplierContractedCommission should_== (Some(4.0))
      }
    }
  }
}

//scalastyle:on
