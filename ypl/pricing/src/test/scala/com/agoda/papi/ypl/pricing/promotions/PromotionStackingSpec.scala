package com.agoda.papi.ypl.pricing.promotions

import com.agoda.papi.enums.room.DiscountType
import com.agoda.papi.ypl.models._

/**
  * Promotion Stacking Test Suite - A Variant (Mega Sale as Pulse Promotion)
  *
  * This test suite validates the promotion stacking behavior when the ENABLE_MEGA_CAMPAIGN experiment RC-2660
  * is configured as A variant and no mega sale promotions.
  */
class PromotionStackingSpec extends PromotionStackingTestUtilities {

  val yplRequest: YplRequest = aValidYplRequest
    .withCheckIn(checkInDate)
    .withCheckout(checkoutDate)
    .withClientInfo(aValidClientInfo.withOrigin(Some("TH")))
    .withBookingDate(checkInDate)
    .withAExperiment(YplExperiments.ENABLE_MEGA_CAMPAIGN)

  def runTestCases(testCase: PromotionStackingTestCase): Boolean = runTestCases(testCase, yplRequest)

  // Helper method for backward compatibility with old test case format
  def runTestCases(
    availablePromotions: List[Promotion],
    pulsePtypeIds: List[Int],
    finalDiscount: Double,
    finalAppliedPromotions: List[Int],
    finalDiscountType: DiscountType,
    finalPromotionTypeIdInPulseMetadata: Option[Int],
  ): Boolean = {
    val testCase = PromotionStackingTestCase.withoutMegaSale(
      availablePromotions = availablePromotions,
      pulsePromotionTypeIds = pulsePtypeIds,
      finalDiscount = finalDiscount,
      finalAppliedPromotions = finalAppliedPromotions,
      finalDiscountType = finalDiscountType,
      finalPromotionTypeIdInPulseMetadata = finalPromotionTypeIdInPulseMetadata,
    )
    runTestCases(testCase)
  }

  "1. When have only non stackable promotions" should {
    "1.1 pick at least one discount" in {
      runTestCases(
        List(Promotion(1, 101, 5, false)),
        List(),
        5,
        List(1),
        DiscountType.PercentDiscount,
        None,
      ) should_== true
    }
    "1.2 pick higher discount" in {
      runTestCases(
        List(Promotion(1, 101, 15, false), Promotion(2, 202, 20, false)),
        List(202),
        20,
        List(2),
        DiscountType.PercentDiscount,
        Some(202),
      ) should_== true
    }
    "1.3 pick higher discount if potentially have total discount >= 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, false), Promotion(2, 202, 60, false)),
          List(202),
          60,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
    "1.4 pick pulse over normal if same discount" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 50, false), Promotion(2, 202, 50, false)),
          List(202),
          50,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
    "1.5 pick higher promotion ID if same discount for normal promotion" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 50, false), Promotion(2, 202, 50, false)),
          List(),
          50,
          List(2),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "1.6 pick higher promotion ID if same discount for pulse promotion" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 201, 50, false), Promotion(2, 202, 50, false)),
          List(201, 202),
          50,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
  }

  "2. When have only normal stackable promotions" should {
    "2.1 pick at least one discount" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 5, true)),
          List(),
          5,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "2.2 pick all if total < 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 5, true), Promotion(2, 102, 15, true), Promotion(3, 103, 25, true)),
          List(),
          45,
          List(1, 2, 3),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed with business team
    "2.3 not pick any if total > 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, true), Promotion(2, 102, 55, true)),
          List(),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed with business team
    "2.4 pick last promotions if previous promotions have total > 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, true),
               Promotion(2, 102, 55, true),
               Promotion(3, 103, 55, true),
               Promotion(4, 104, 15, true)),
          List(),
          70,
          List(3, 4),
          DiscountType.Combined,
          None,
        )) should_== true
    }
  }

  "3. When have only pulse stackable promotions" should {
    "3.1 pick at least one discount" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 201, 10, true)),
          List(201),
          10,
          List(1),
          DiscountType.PercentDiscount,
          Some(201),
        )) should_== true
    }
    "3.2 pick the highest discount" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 201, 10, true), Promotion(2, 202, 20, true), Promotion(3, 203, 30, true)),
          List(201, 202, 203),
          30,
          List(3),
          DiscountType.PercentDiscount,
          Some(203),
        )) should_== true
    }
    "3.3 pick higher promotion ID if same discount" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 201, 10, true), Promotion(2, 202, 10, true)),
          List(201, 202),
          10,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
    "3.4 pick higher promotion ID if same discount and same promotion type" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 201, 10, true), Promotion(2, 201, 10, true)),
          List(201),
          10,
          List(2),
          DiscountType.PercentDiscount,
          Some(201),
        )) should_== true
    }
    "3.5 pick higher promotion ID if potentially have total discount >= 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 201, 55, true), Promotion(2, 202, 55, true)),
          List(201, 202),
          55,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
  }

  "4. When have normal and pulse stackable promotions" should {
    "4.1 pick both normal and pulse" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 5, true), Promotion(2, 202, 10, true)),
          List(202),
          15,
          List(1, 2),
          DiscountType.Combined,
          Some(202),
        )) should_== true
    }
    "4.2 pick all normal but only one higher pulse type" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 5, true),
               Promotion(2, 102, 15, true),
               Promotion(3, 203, 10, true),
               Promotion(4, 204, 10, true)),
          List(203, 204),
          30,
          List(1, 2, 4),
          DiscountType.Combined,
          Some(204),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "4.3 not pick any if total > 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, true), Promotion(2, 202, 55, true)),
          List(202),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    "4.4 pick only the last promotion if total > 100% but the last promotion <100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, true), Promotion(2, 202, 55, true), Promotion(3, 103, 55, true)),
          List(202),
          55,
          List(3),
          DiscountType.PercentDiscount,
          None,
        )) should_== true // assert false as no pick and result from an error
    }
    // This case should be reviewed or fixed, it should pick 25% pulse
    "4.5 not pick any if total of the best pulse + normal promotion have total > 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, true), Promotion(2, 202, 55, true), Promotion(3, 203, 25, true)),
          List(202, 203),
          0,
          List(),
          DiscountType.Combined,
          None,
        )) should_== true
    }
  }

  "5. When have non stackable and stackable promotions" should {
    "5.01 pick both if total discount < 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 5, false), Promotion(2, 102, 15, true)),
          List(),
          20,
          List(1, 2),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    "5.02 pick non stackable if total discount >= 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, false), Promotion(2, 102, 55, true)),
          List(),
          55,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "5.03 pick non stackable and pulse metadata if total discount >= 100% and non stackable is pulse" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 201, 55, false), Promotion(2, 102, 15, true), Promotion(3, 103, 55, true)),
          List(201),
          55,
          List(1),
          DiscountType.PercentDiscount,
          Some(201),
        )) should_== true
    }
    "5.04 pick non stackable and no pulse metadata if total discount >= 100% and non stackable is not pulse" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, false), Promotion(2, 202, 55, true)),
          List(202),
          55,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "5.05 pick non stackable if total discount >= 100% and potential combined >= 100% " in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, false), Promotion(2, 202, 15, true), Promotion(3, 103, 55, true)),
          List(202),
          55,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "5.06 pick non stackable if total discount >= 100% and sum of multiple stackable discount < 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, false), Promotion(2, 101, 25, true), Promotion(3, 103, 25, true)),
          List(),
          55,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "5.07 pick non stackable if total discount = 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 10, false), Promotion(2, 102, 45, true), Promotion(3, 203, 45, true)),
          List(203),
          10,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    "5.08 pick only one highest discount from pulse stackable promotions" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, false), Promotion(2, 202, 30, true), Promotion(3, 203, 25, true)),
          List(202, 203),
          85,
          List(1, 2),
          DiscountType.Combined,
          Some(202),
        )) should_== true
    }
    "5.09 pick only one highest type ID from pulse stackable promotions if same discount" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, false), Promotion(2, 202, 25, true), Promotion(3, 203, 25, true)),
          List(202, 203),
          80,
          List(1, 3),
          DiscountType.Combined,
          Some(203),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "5.10 pick one non stackable pulse and one stackable pulse promotion" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 201, 55, false), Promotion(2, 202, 15, false), Promotion(3, 203, 45, true)),
          List(201, 202, 203),
          60,
          List(2, 3),
          DiscountType.Combined,
          Some(203),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "5.11 pick one non stackable pulse and one stackable pulse promotion even same pulse type" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 201, 55, false), Promotion(2, 201, 15, false), Promotion(3, 201, 45, true)),
          List(201),
          60,
          List(2, 3),
          DiscountType.Combined,
          Some(201),
        )) should_== true
    }
    "5.12 pick one non stackable pulse if all combination discounts are equal" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 201, 10, false), Promotion(2, 202, 55, false), Promotion(3, 203, 45, true)),
          List(201, 202, 203),
          55,
          List(2),
          DiscountType.PercentDiscount,
          Some(202),
        )) should_== true
    }
    // This case should be reviewed or fixed
    "5.13 known issue that pick last stackable promotions in order if stackable promotion can only apply some for < 100% (ordering issue)" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 50, false),
               Promotion(2, 102, 25, true),
               Promotion(3, 103, 25, true),
               Promotion(4, 104, 25, true)),
          List(),
          75,
          List(1, 4),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed or fixed
    "5.14 Known issue that 30% stackable promotoion is not chosen over 25% stackable promotion (ordering issue)" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 50, false),
               Promotion(2, 102, 30, true),
               Promotion(3, 103, 25, true),
               Promotion(4, 104, 20, true)),
          List(),
          70,
          List(1, 4),
          DiscountType.Combined,
          None,
        )) should_== true
    }
    // This case should be reviewed or fixed
    "5.15 Known issue that multiple stackable promotions can apply before >= 100% but total still >= 100% then return only non-stackable" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 50, false),
               Promotion(2, 102, 20, true),
               Promotion(3, 103, 20, true),
               Promotion(4, 204, 20, true)),
          List(204),
          50,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    // This case should be reviewed or fixed
    "5.16 cannot pick the best pulse stackable if the best pulse promotion got applied and discount > 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, false), Promotion(2, 202, 55, true), Promotion(3, 203, 15, true)),
          List(202, 203),
          55,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
    // This case should be reviewed or fixed
    "5.16 cannot pick the best pulse stackable if the best pulse promotion got applied and discount > 100%" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 55, false),
               Promotion(3, 202, 15, true),
               Promotion(2, 203, 55, true),
               Promotion(4, 204, 20, true)),
          List(202, 203, 204),
          55,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }

    "7.5 should pick pulse as pulse campaign metadata over mega sale promotion if the best mega sale is not picked" in {
      runTestCases(
        PromotionStackingTestCase.withoutMegaSale(
          List(Promotion(1, 101, 15, false), Promotion(2, 202, 10, true), Promotion(3, 203, 90, true)),
          List(202, 203),
          15,
          List(1),
          DiscountType.PercentDiscount,
          None,
        )) should_== true
    }
  }

}
