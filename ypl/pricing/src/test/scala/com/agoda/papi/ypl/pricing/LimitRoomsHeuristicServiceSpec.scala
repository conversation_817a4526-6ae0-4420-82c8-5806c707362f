package com.agoda.papi.ypl.pricing

import com.agoda.papi.enums.hotel.{PaymentModel, PaymentOption, StayPackageTypes}
import com.agoda.papi.enums.room.{ChargeType, RatePlanStatus}
import com.agoda.papi.ypl.models.api.request.YplFlagInfo
import com.agoda.papi.ypl.models.pricing.proto.DailyPrice
import com.agoda.papi.ypl.models.{
  RoomFeatures,
  YPLHotel,
  YPLRoom,
  YPLTestContexts,
  YPLTestDataBuilders,
  YplCancellation,
  YplContext,
  YplHeuristicRequest,
  YplHotelEntryModel,
  YplHotelWithEntry,
  YplRoomEntry,
}
import com.agoda.supply.calc.proto.StayPackageType
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit
import com.agoda.papi.ypl.logging.NoOpMessageSink
import com.agoda.papi.ypl.logging.MessageSink
import com.agoda.adp.messaging.scala.message.Message
import com.agoda.papi.ypl.logging.DFOfferFilterTrackingMessage

import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import com.agoda.papi.ypl.models.GUIDGenerator.{CoreFields, ExtendFields, RoomIdentifiers}
import models.consts.ABTest

import scala.reflect.ClassTag

class LimitRoomsHeuristicServiceSpec extends SpecificationWithJUnit with YPLTestDataBuilders with YPLTestContexts {

  def createServiceWithNoOpSink() = new LimitRoomsHeuristicServiceImpl {
    protected val messageSink: MessageSink = NoOpMessageSink
  }

  implicit class TupleFunctions(x: (List[YPLRoom], List[YPLRoom])) {
    implicit def size: (Int, Int) = (x._1.size, x._2.size)
  }

  {
    def toRoomIdentifiers(yplRoomEntry: YplRoomEntry): RoomIdentifiers = RoomIdentifiers(
      coreFields = CoreFields(
        roomId = yplRoomEntry.roomTypeId,
        paymentModel = yplRoomEntry.paymentModel.map(_.i).getOrElse(0),
        promotionId = yplRoomEntry.promotion.map(_.id).getOrElse(0),
        channelId = yplRoomEntry.channel.compositeChannelId,
        rateCategoryId = yplRoomEntry.rateCategoryId,
        occupancy = yplRoomEntry.occEntry.maxOcc,
        extrabed = yplRoomEntry.occEntry.extraBeds,
        breakfast = yplRoomEntry.isBreakFastIncluded,
        cxlCode = yplRoomEntry.cxlCode,
        supplierId = 0,
        isPriusOutput = false,
        isRepurposed = false,
        srcChannelId = 0,
        refChannelId = 0,
        dmcUid = "",
      ),
      extendFields = ExtendFields(None, false, false, Nil, Nil, None),
      roomSelectionFields = None,
      stayOccupancyFields = None,
      pricingFields = None,
    )

    "Room Limiter Heuristic" should {

      val req = aValidYplRequest.withCheapestRoomOnly(true).build
      val ctx: YplContext = aValidYplContext.withRequest(req)

      val stub = new LimitRoomsHeuristicServiceImpl {
        protected val messageSink: MessageSink = NoOpMessageSink
      }

      "Return None if no hotel in input " in {
        stub.limitRoomsHeuristic(None)(ctx) should beEmpty
      }

      "Return the same hotel if it has no rooms" in {
        val hotel: YplHotelEntryModel = aValidHotelEntryModel.withRooms(List.empty[YplRoomEntry])

        stub.limitRoomsHeuristic(Some(hotel))(ctx) should_== Some(hotel)
      }

      "not apply heuristic if hotel has many rooms but maxlimit is empty" in {
        val room: YplRoomEntry = aValidRoomEntry
        val hotel: YplHotelEntryModel =
          aValidHotelEntryModel.withRooms((for (i <- 1 to 100) yield room.withRoomTypeId(i).build).toList)
        val request = aValidYplRequest.withHeuristicRequest(None)
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx).map(_.rooms.length) should_== Some(100)
      }

      "return as is if rooms are less than maxroom specified" in {
        val room: YplRoomEntry = aValidRoomEntry
        val hotel: YplHotelEntryModel =
          aValidHotelEntryModel.withRooms((for (i <- 1 to 10) yield room.withRoomTypeId(i).build).toList)
        val request = aValidYplRequest.withHeuristicRequest(Some(YplHeuristicRequest(Some(20))))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx).map(_.rooms.length) should_== Some(10)
      }

      "apply heuristic and return limited room if hotel has many rooms and maxroom is sent" in {
        val room: YplRoomEntry = aValidRoomEntry
        val hotel: YplHotelEntryModel =
          aValidHotelEntryModel.withRooms((for (i <- 1 to 100) yield room.withRoomTypeId(i).build).toList)
        val request =
          aValidYplRequest.withHeuristicRequest(Some(YplHeuristicRequest(Some(20)))).withEnableOfferFilterLogs(true)
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))

        var logs = List.empty[DFOfferFilterTrackingMessage]
        val latch = new CountDownLatch(2)
        val stub = new LimitRoomsHeuristicServiceImpl {
          protected val messageSink: MessageSink = new MessageSink {
            override def log[T: ClassTag](f: => Iterable[Message[T]])(implicit tag: ClassTag[T]): Unit = {
              logs = logs ++ f.map(_.asInstanceOf[DFOfferFilterTrackingMessage]).toList
              latch.countDown()
            }
          }
        }
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        res.map(_.rooms.length) should_== Some(20)
        res.map(_.rooms.exists(_.isEscapes)) should_== Some(false)
        latch.await(5, TimeUnit.SECONDS)
        logs should haveSize(80)

        logs.exists(_.removalReason == LimitRoomsHeuristicService.supplierLoadRateOfferLimit) should_== (true)
        logs.exists(_.removalReason == LimitRoomsHeuristicService.supplierLoadRateOfferLimitForFMR) should_== (false)
      }

      "apply heuristic and return limited room if hotel has normal rooms + ignoredCheapestOnlyRooms rooms and maxroom is sent" in {
        val room: YplRoomEntry = aValidRoomEntry
        val hotel: YplHotelEntryModel = aValidHotelEntryModel.withRooms(
          (for (i <- 1 to 100)
            yield
              if (i <= 60) room.withRoomTypeId(i).build
              else room.withRoomTypeId(i).withIsKeptForFlexibleMultiRoom(true).build).toList)
        val request =
          aValidYplRequest.withHeuristicRequest(Some(YplHeuristicRequest(Some(20)))).withEnableOfferFilterLogs(true)
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))

        var logs = List.empty[DFOfferFilterTrackingMessage]
        val latch = new CountDownLatch(2)
        val stub = new LimitRoomsHeuristicServiceImpl {
          protected val messageSink: MessageSink = new MessageSink {
            override def log[T: ClassTag](f: => Iterable[Message[T]])(implicit tag: ClassTag[T]): Unit = {
              logs = logs ++ f.map(_.asInstanceOf[DFOfferFilterTrackingMessage]).toList
              latch.countDown()
            }
          }
        }
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        res.map(_.rooms.length) should_== Some(40)
        res.map(_.rooms.exists(_.isEscapes)) should_== Some(false)
        latch.await(5, TimeUnit.SECONDS)
        logs should haveSize(60)
        logs.exists(_.removalReason == LimitRoomsHeuristicService.supplierLoadRateOfferLimit) should_== (true)
        logs.exists(_.removalReason == LimitRoomsHeuristicService.supplierLoadRateOfferLimitForFMR) should_== (true)
      }

      "apply heuristic and return limited room without including booked room if hotel has many rooms and maxroom is sent with roomidentifier not defined" in {
        val room: YplRoomEntry = aValidRoomEntry
        val hotel: YplHotelEntryModel =
          aValidHotelEntryModel.withRooms((for (i <- 1 to 100) yield room.withRoomTypeId(i).build).toList)
        val bookedRoom = hotel.rooms(30)
        val bookRoomIdentifier = toRoomIdentifiers(bookedRoom)
        val request = aValidYplRequest.withHeuristicRequest(Some(YplHeuristicRequest(Some(20))))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        res.map(_.rooms.length) should_== Some(20)
        res.map(_.rooms.exists(stub.isMatch(_, bookRoomIdentifier))) should_== Some(false)
      }

      "apply heuristic and return limited room without including booked room if hotel has many rooms and maxroom is sent with invalid roomidentifier" in {
        val room: YplRoomEntry = aValidRoomEntry
        val hotel: YplHotelEntryModel =
          aValidHotelEntryModel.withRooms((for (i <- 1 to 100) yield room.withRoomTypeId(i).build).toList)
        val bookedRoom = hotel.rooms(30).copy(roomTypeId = 200)
        val bookRoomIdentifier = toRoomIdentifiers(bookedRoom)
        val request = aValidYplRequest
          .withHeuristicRequest(Some(YplHeuristicRequest(Some(20))))
          .withBookRoomIdentifier(Some(bookRoomIdentifier))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        res.map(_.rooms.length) should_== Some(20)
        res.map(_.rooms.exists(stub.isMatch(_, bookRoomIdentifier))) should_== Some(false)
      }

      "apply heuristic and return limited room with including booked room if hotel has many rooms and maxroom is sent with valid roomidentifier" in {
        var logs = List.empty[DFOfferFilterTrackingMessage]
        val latch = new CountDownLatch(3)
        val stub = new LimitRoomsHeuristicServiceImpl {
          protected val messageSink: MessageSink = new MessageSink {
            override def log[T: ClassTag](f: => Iterable[Message[T]])(implicit tag: ClassTag[T]): Unit = {
              logs = logs ++ f.map(_.asInstanceOf[DFOfferFilterTrackingMessage]).toList
              latch.countDown()
            }
          }
        }
        val room: YplRoomEntry = aValidRoomEntry
        val hotel: YplHotelEntryModel =
          aValidHotelEntryModel.withRooms((for (i <- 1 to 100) yield room.withRoomTypeId(i).build).toList)
        val bookedRoom = hotel.rooms(30)
        val bookRoomIdentifier = toRoomIdentifiers(bookedRoom)
        val request = aValidYplRequest
          .withHeuristicRequest(Some(YplHeuristicRequest(Some(20))))
          .withEnableOfferFilterLogs(true)
          .withBookRoomIdentifier(Some(bookRoomIdentifier))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        latch.await(5, TimeUnit.SECONDS)

        res.map(_.rooms.length) should_== Some(20)
        res.map(_.rooms.exists(stub.isMatch(_, bookRoomIdentifier))) should_== Some(true)

        logs.exists(_.removalReason == LimitRoomsHeuristicService.supplierLoadRateOfferLimit) should_== (true)
        logs.exists(
          _.removalReason == LimitRoomsHeuristicService.supplierLoadRateOfferLimitForBookingRoom) should_== (true)
      }

      "apply heuristic and return limited room if hotel has many rooms and maxroom is sent - ignoredCheapestOnlyRooms shoudln't effect normal rooms heuristic" in {
        val room: YplRoomEntry = aValidRoomEntry
        val hotelWithIgnoreCheapest: YplHotelEntryModel = aValidHotelEntryModel.withRooms(
          (for (i <- 1 to 100)
            yield
              if (i <= 60) room.withRoomTypeId(i).build
              else room.withRoomTypeId(i).withIsKeptForFlexibleMultiRoom(true).build).toList)

        val hotel: YplHotelEntryModel =
          aValidHotelEntryModel.withRooms((for (i <- 1 to 60) yield room.withRoomTypeId(i).build).toList)

        val request = aValidYplRequest.withHeuristicRequest(Some(YplHeuristicRequest(Some(20))))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))

        val resWithIgnoreCheapest = stub.limitRoomsHeuristic(Some(hotelWithIgnoreCheapest))(roomLimitCtx)
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        res.map(_.rooms.length) should_== Some(20)
        res.map(_.rooms.exists(_.isEscapes)) should_== Some(false)

        res.map(_.rooms.filterNot(_.isKeptForFlexibleMultiRoom)).get should containTheSameElementsAs(
          resWithIgnoreCheapest.map(_.rooms.filterNot(_.isKeptForFlexibleMultiRoom)).get)
      }

      "not applying swap rooms if hotel has even one ASO room after heuristic" in {
        val room: YplRoomEntry = aValidRoomEntry
        val rooms = (for (i <- 1 to 10) yield room.withRoomTypeId(i).build).toList
        val escapesRoom: YplRoomEntry =
          aValidRoomEntry.withRateCategoryEntry(aValidRateCategoryEntry.withStayPackageType(Some(StayPackageType.Escapes)))
        val escapesRooms = (for (i <- 11 to 11) yield escapesRoom.withRoomTypeId(i).build).toList
        val hotel: YplHotelEntryModel = aValidHotelEntryModel.withRooms(rooms ++ escapesRooms)
        val request = aValidYplRequest.withHeuristicRequest(Some(YplHeuristicRequest(Some(20), Some(5))))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        res.map(_.rooms.length) should_== Some(11)
        res.map(_.rooms.count(_.isEscapes)) should_== Some(1)
      }

      "applying swap" in {
        val room: YplRoomEntry = aValidRoomEntry
        val rooms = (for (i <- 1 to 10) yield room.withRoomTypeId(i).build).toList
        val dailyPrice: DailyPrice =
          aValidDailyPrice.withPrices(List(aValidPriceEntry.withChargeType(ChargeType.Room).withValue(1.0)))
        val dailyPrices = Map(new DateTime() -> dailyPrice)
        val escapesRoom: YplRoomEntry = aValidRoomEntry
          .withRateCategoryEntry(aValidRateCategoryEntry.withStayPackageType(Some(StayPackageType.Escapes)))
          .withDailyPrices(dailyPrices)
        val escapesRooms = (for (i <- 11 to 20) yield escapesRoom.withRoomTypeId(i).build).toList
        val hotel: YplHotelEntryModel = aValidHotelEntryModel.withRooms(rooms ++ escapesRooms)
        val request = aValidYplRequest.withHeuristicRequest(Some(YplHeuristicRequest(Some(10), Some(5))))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        res.map(_.rooms.length) should_== Some(10)
        res.map(_.rooms.exists(_.isEscapes)) should_== Some(true)
      }

      "not applying swap and return as is if aso rooms config not set" in {
        val room: YplRoomEntry = aValidRoomEntry
        val rooms = (for (i <- 1 to 10) yield room.withRoomTypeId(i).build).toList
        val dailyPrice: DailyPrice =
          aValidDailyPrice.withPrices(List(aValidPriceEntry.withChargeType(ChargeType.Room).withValue(1.0)))
        val dailyPrices = Map(new DateTime() -> dailyPrice)
        val escapesRoom: YplRoomEntry = aValidRoomEntry
          .withRateCategoryEntry(aValidRateCategoryEntry.withStayPackageType(Some(StayPackageType.Escapes)))
          .withDailyPrices(dailyPrices)
        val escapesRooms = (for (i <- 11 to 20) yield escapesRoom.withRoomTypeId(i).build).toList
        val hotel: YplHotelEntryModel = aValidHotelEntryModel.withRooms(rooms ++ escapesRooms)
        val request = aValidYplRequest.withHeuristicRequest(Some(YplHeuristicRequest(Some(10))))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        res.map(_.rooms.length) should_== Some(10)
        res.map(_.rooms.exists(_.isEscapes)) should_== Some(false)
      }

      "apply swap and return swapped rooms if hotel doesn't return any ASO rooms after heuristic - # of escapes rooms less than config" in {
        val room: YplRoomEntry = aValidRoomEntry
        val rooms = (for (i <- 1 to 10) yield room.withRoomTypeId(i).build).toList
        val dailyPrice: DailyPrice =
          aValidDailyPrice.withPrices(List(aValidPriceEntry.withChargeType(ChargeType.Room).withValue(1.0)))
        val dailyPrices = Map(new DateTime() -> dailyPrice)
        val escapesRoom: YplRoomEntry = aValidRoomEntry
          .withRateCategoryEntry(aValidRateCategoryEntry.withStayPackageType(Some(StayPackageType.Escapes)))
          .withDailyPrices(dailyPrices)
        val escapesRooms = (for (i <- 11 to 13) yield escapesRoom.withRoomTypeId(i).build).toList
        val hotel: YplHotelEntryModel = aValidHotelEntryModel.withRooms(rooms ++ escapesRooms)
        val request = aValidYplRequest.withHeuristicRequest(Some(YplHeuristicRequest(Some(10), Some(5))))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        res.map(_.rooms.length) should_== Some(10)
        res.map(_.rooms.count(_.isEscapes)) should_== Some(3)
      }

      "apply swap and return swapped rooms if hotel doesn't return any ASO rooms after heuristic - # of escapes rooms more than config" in {
        val room: YplRoomEntry = aValidRoomEntry
        val rooms = (for (i <- 1 to 10) yield room.withRoomTypeId(i).build).toList
        val dailyPrice: DailyPrice =
          aValidDailyPrice.withPrices(List(aValidPriceEntry.withChargeType(ChargeType.Room).withValue(1.0)))
        val dailyPrices = Map(new DateTime() -> dailyPrice)
        val escapesRoom: YplRoomEntry = aValidRoomEntry
          .withRateCategoryEntry(aValidRateCategoryEntry.withStayPackageType(Some(StayPackageType.Escapes)))
          .withDailyPrices(dailyPrices)
        val escapesRooms = (for (i <- 11 to 20) yield escapesRoom.withRoomTypeId(i).build).toList
        val hotel: YplHotelEntryModel = aValidHotelEntryModel.withRooms(rooms ++ escapesRooms)
        val request = aValidYplRequest.withHeuristicRequest(Some(YplHeuristicRequest(Some(10), Some(5))))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        val res = stub.limitRoomsHeuristic(Some(hotel))(roomLimitCtx)

        res.map(_.rooms.length) should_== Some(10)
        res.map(_.rooms.count(_.isEscapes)) should_== Some(5)
      }
    }

    "Enhanced Heuristic" should {
      val limitRooms = 100
      val stub = createServiceWithNoOpSink()
      val today = DateTime.now()
      val groupingMethod = (r: YplRoomEntry) => r.roomTypeId
      val orderBy = (r: YplRoomEntry) => r.totalValueForHeuristics

      def dailyPrice(price: Double, uspaDiscount: Double): DailyPrice = aValidDailyPrice.withPrices(
        List(
          aValidPriceEntry.withChargeType(ChargeType.Room).withValue(100.0).build,
          aValidPriceEntry.withChargeType(ChargeType.Room).withValue(price).withMaxUspaDiscount(uspaDiscount).build,
        ),
      )

      def room(index: Int, roomTypeId: Long, uspaDiscount: Double = 0.0): YplRoomEntry =
        aValidRoomEntry.withRoomTypeId(roomTypeId).withDailyPrices(Map(today -> dailyPrice(index.toDouble, uspaDiscount)))

      "Return empty list when empty list in input " in {
        val (res, removedRes) = stub.enhancedHeuristic(List.empty[YplRoomEntry], limitRooms, groupingMethod, orderBy)
        res should_== List.empty[YplRoomEntry]
        removedRes should_== List.empty[YplRoomEntry]
      }

      "Don't filter when rooms are less then limit" in {
        val rooms = List(aValidRoomEntry.build, aValidRoomEntry.build)

        val (res, removedRes) = stub.enhancedHeuristic(rooms, limitRooms, groupingMethod, orderBy)
        res.length should_== rooms.length
        removedRes.length should_== 0
      }

      "Keep the cheapest roomTypeId even if all roomTypeId (including the cheapest) are above limit" in {
        val numberOfRoomAboveLimit = limitRooms + 1

        // Cheapest
        val roomsType111 = (for (i <- 1 to numberOfRoomAboveLimit) yield room(i, 111)).toList
        // More expensive ( due to i + 1 )
        val roomsType222 = (for (i <- 1 to numberOfRoomAboveLimit) yield room(i + 1, 222)).toList

        val (res, removedRes) = stub.enhancedHeuristic(roomsType111 ++ roomsType222, limitRooms, groupingMethod, orderBy)

        res.length should_== roomsType111.length
        res.forall(_.roomTypeId should_== 111)

        removedRes.length should_== roomsType222.length
        removedRes.forall(_.roomTypeId should_== 222)
      }

      "Keep only the first N cheapest roomTypes, without splitting any roomType" in {
        val numberOfRoomsPerRoomType = 40

        // Most expensive ( due to i + 2 )
        val roomsType111 = (for (i <- 1 to numberOfRoomsPerRoomType) yield room(i + 2, 111)).toList
        // More expensive ( due to i + 1 )
        val roomsType222 = (for (i <- 1 to numberOfRoomsPerRoomType) yield room(i + 1, 222)).toList
        // Cheapest
        val roomsType333 = (for (i <- 1 to numberOfRoomsPerRoomType) yield room(i, 333)).toList

        // Limit is 100, so keep 80 rooms and discard 40 ( don't split the last roomtype )
        val (res, removedRes) =
          stub.enhancedHeuristic(roomsType111 ++ roomsType222 ++ roomsType333, limitRooms, groupingMethod, orderBy)

        res.length should_== roomsType333.length + roomsType222.length
        res.count(_.roomTypeId == 333) should_== numberOfRoomsPerRoomType
        res.count(_.roomTypeId == 222) should_== numberOfRoomsPerRoomType

        removedRes.length should_== roomsType111.length
      }

      "Keep only the first N cheapest(by totalValue with uspaDiscount) roomTypes, without splitting any roomType" in {
        val numberOfRoomsPerRoomType = 40

        // Cheapest
        val roomsType333 = (for (i <- 1 to numberOfRoomsPerRoomType) yield room(i, 333)).toList
        // cheaper total heuristics value i +1 ( i+4 -3 = i+1)
        val roomsType111 = (for (i <- 1 to numberOfRoomsPerRoomType) yield room(i + 4, 111, 3)).toList
        // More expensive ( due to i + 2 )
        val roomsType222 = (for (i <- 1 to numberOfRoomsPerRoomType) yield room(i + 2, 222)).toList

        // Limit is 100, so keep 80 rooms and discard 40 ( don't split the last roomtype )
        val (res, removedRes) =
          stub.enhancedHeuristic(roomsType111 ++ roomsType222 ++ roomsType333, limitRooms, groupingMethod, orderBy)

        res.length should_== roomsType333.length + roomsType111.length
        res.count(_.roomTypeId == 333) should_== numberOfRoomsPerRoomType
        res.count(_.roomTypeId == 111) should_== numberOfRoomsPerRoomType
        res.count(_.roomTypeId == 222) should_== 0

        removedRes.length should_== roomsType222.length
      }

    }

    "Cheapest Room Heuristic" should {

      val req = aValidYplRequest.withCheapestRoomOnly(true).build
      val ctx: YplContext =
        aValidYplContext.withRequest(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val stub = createServiceWithNoOpSink()
      "Return None if no hotel in input " in {
        stub.limitCheapestRoomHeuristic(None)(ctx) should beEmpty
      }

      "Return the same hotel if it has no rooms" in {
        val hotel: YPLHotel = aValidHotel.withRooms(Nil)
        val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)

        stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(ctx).map(_.hotel.rooms) should_== Some(hotel.rooms)
      }

      "not apply heuristic if hotel has many rooms but max limit is empty" in {
        val room: YPLRoom = aValidRoom
        val basePrice = 100d
        val mockPrice = aValidPrice.withNetExclusive(basePrice)
        val hotel: YPLHotel = aValidHotel.withRooms(
          (for (i <- 1 to 100)
            yield room.withRoomTypeId(i).withPrice(mockPrice.withNetExclusive(basePrice + i)).build).toList)
        val request = aValidYplRequest.withCheapestRoomOnly(true).withHeuristicRequest(None)
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))

        val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
        stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx).map(_.hotel.rooms.length) should_== Some(100)
      }

      "return as is if rooms are less than maxroom specified" in {
        val room: YPLRoom = aValidRoom
        val basePrice = 100d
        val mockPrice = aValidPrice.withNetExclusive(basePrice)
        val hotel: YPLHotel = aValidHotel.withRooms(
          (for (i <- 1 to 10)
            yield room.withRoomTypeId(i).withPrice(mockPrice.withNetExclusive(basePrice + i)).build).toList)
        val request = aValidYplRequest
          .withCheapestRoomOnly(true)
          .withHeuristicRequest(Some(YplHeuristicRequest(limitCheapestRooms = Some(20))))
          .withHasSortingStrategy(false)
          .withFlagInfo(YplFlagInfo(isEnableSupplierFinancialInfo = false))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))

        val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
        stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx).map(_.hotel.rooms.length) should_== Some(10)
      }

      "return all escapes rooms" in {
        val room: YPLRoom = aValidRoom
        val hotel: YPLHotel = aValidHotel.withRooms(
          (for (i <- 1 to 15) yield room.withRoomTypeId(i).build).toList ++
            // Escape rooms
            (for (i <- 16 to 25) yield room.withStayPackageType(Some(StayPackageTypes.Escapes)).withRoomTypeId(i).build))
        val request = aValidYplRequest
          .withCheapestRoomOnly(true)
          .withHeuristicRequest(Some(YplHeuristicRequest(limitCheapestRooms = Some(20))))
          .withHasSortingStrategy(false)
          .withFlagInfo(YplFlagInfo(isEnableSupplierFinancialInfo = false))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))

        val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
        val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx)
        res.map(_.hotel.rooms.length) should_== Some(25)
        res.map(_.hotel.rooms.count(_.stayPackageType.contains(StayPackageTypes.Escapes))) should_== Some(10)
      }

      "using mostExpensiveRooms from escapes rooms and not getting an exception" in {
        val room: YPLRoom = aValidRoom
        val hotel: YPLHotel = aValidHotel.withRooms(
          (for (i <- 1 to 10)
            yield room.withRoomTypeId(i).withStayPackageType(Some(StayPackageTypes.Escapes)).build).toList,
        )
        val request = aValidYplRequest
          .withCheapestRoomOnly(true)
          .withHeuristicRequest(Some(YplHeuristicRequest(limitCheapestRooms = Some(20))))
          .withHasSortingStrategy(false)
          .withFlagInfo(YplFlagInfo(isEnableSupplierFinancialInfo = false))
        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))
        val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
        val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx)
        res.map(_.hotel.rooms.length) should_== Some(10)
        res.map(_.hotel.rooms.count(_.stayPackageType.contains(StayPackageTypes.Escapes))) should_== Some(10)
      }

      "apply heuristic and return limited room if hotel has many rooms and maxroom is sent" in {
        val room: YPLRoom = aValidRoom
        val basePrice = 100d
        val mockPrice = aValidPrice.withNetExclusive(basePrice)
        val hotel: YPLHotel = aValidHotel.withRooms(
          (for (i <- 1 to 100)
            yield room.withRoomTypeId(i).withPrice(mockPrice.withNetExclusive(basePrice + i)).build).toList)
        val heuristicOrdering = new HeuristicOrdering(hotel.reqOcc.guestsPerRoom(withInfants = true))

        val request = aValidYplRequest
          .withCheapestRoomOnly(true)
          .withHeuristicRequest(Some(YplHeuristicRequest(limitCheapestRooms = Some(20))))
          .withHasSortingStrategy(false)
          .withFlagInfo(YplFlagInfo(isEnableSupplierFinancialInfo = false))
          .withEnableOfferFilterLogs(true)

        val roomLimitCtx =
          aValidYplContext.withRequest(request).withExperimentContext(forcedFromRequestExperimentContext(request))

        "when cheapest room and most expensive room are same" in {
          val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
          var logs = List.empty[DFOfferFilterTrackingMessage]
          val latch = new CountDownLatch(2)
          val stub = new LimitRoomsHeuristicServiceImpl {
            protected val messageSink: MessageSink = new MessageSink {
              override def log[T: ClassTag](f: => Iterable[Message[T]])(implicit tag: ClassTag[T]): Unit = {
                logs = f.map(_.asInstanceOf[DFOfferFilterTrackingMessage]).toList
                latch.countDown()
              }
            }
          }

          val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx)
          latch.await(5, TimeUnit.SECONDS)

          res.map(_.hotel.rooms.length) should_== Some(21)
          res.map(_.hotel.rooms.exists(_.stayPackageType.contains(StayPackageTypes.Escapes))) should_== Some(false)
          res.get.hotel.rooms.sorted(heuristicOrdering)(19).netEx should_== 120d
          res.get.hotel.rooms.sorted(heuristicOrdering).last.netEx should_== 200d

          logs.nonEmpty should_== (true)
          logs.forall(_.removalReason == LimitRoomsHeuristicService.supplierLimitTo20Rooms) should_== (true)
        }

        "when cheapest room and most expensive room are diff" in {
          val overrideHotel: YPLHotel = aValidHotel.withRooms(
            (for (i <- 1 to 100) yield room
              .withRateCategoryId(i)
              .withRoomTypeId(i % 4 + 1)
              .withPrice(mockPrice.withNetExclusive(basePrice + i))
              .build).toList)
          val hotelWithEntry = YplHotelWithEntry(overrideHotel, aValidHotelEntryModel)
          val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx)
          res.map(_.hotel.rooms.length) should_== Some(20)
          res.map(_.hotel.rooms.exists(_.stayPackageType.contains(StayPackageTypes.Escapes))) should_== Some(false)
          res.get.hotel.rooms.sorted(heuristicOrdering).last.netEx should_== 200d
        }

        "when has isKeptForFlexibleMultiRoom" in {
          val overrideHotel: YPLHotel = aValidHotel.withRooms(
            (for (i <- 1 to 200) yield room
              .withRateCategoryId(i)
              .withRoomFeatures(RoomFeatures(isKeptForFlexibleMultiRoom = i % 4 == 0))
              .withRoomTypeId(i % 4 + 1)
              .withPrice(mockPrice.withNetExclusive(basePrice + (i / 2.0).ceil.toInt))
              .build).toList)
          val hotelWithEntry = YplHotelWithEntry(overrideHotel, aValidHotelEntryModel)
          val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx)
          res.get.hotel.rooms should haveLength(40)
          res.get.hotel.rooms.filter(_.roomFeatures.isKeptForFlexibleMultiRoom) should haveLength(20)
          res.get.hotel.rooms.filterNot(_.roomFeatures.isKeptForFlexibleMultiRoom) should haveLength(20)
          res.get.hotel.rooms
            .filterNot(_.roomFeatures.isKeptForFlexibleMultiRoom)
            .sorted(heuristicOrdering)
            .last
            .netEx should_== 200d
        }
      }

      "apply heuristic on requested or dispatched room status only" in {
        def getStatus(i: Int) = i % 4 match {
          case 0 => RatePlanStatus.Requested
          case 1 => RatePlanStatus.Dispatched
          case 2 => RatePlanStatus.Helper
          case _ => RatePlanStatus.None
        }

        val room: YPLRoom = aValidRoom
        val basePrice = 100d
        val mockPrice = aValidPrice.withNetExclusive(basePrice)
        val hotel: YPLHotel = aValidHotel.withRooms(
          (for (i <- 1 to 20) yield room
            .withRoomTypeId(i)
            .withPrice(mockPrice.withNetExclusive(basePrice + i))
            .withStatus(getStatus(i))
            .build).toList)

        val request = aValidYplRequest
          .withCheapestRoomOnly(true)
          .withHeuristicRequest(Some(YplHeuristicRequest(limitCheapestRooms = Some(5))))
          .withHasSortingStrategy(false)
          .withFlagInfo(YplFlagInfo(isEnableSupplierFinancialInfo = false))
          .build

        val ctx = aValidYplContext.withRequest(request).build

        val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
        val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(ctx)
        res.map(_.hotel.rooms.length) should_== Some(11) // Here helper rooms are being appended
      }

      "do not apply heuristic when isEnableSupplierFinancialInfo == true or hasSortingStrategy or APO Secret Deal request" in {
        val room: YPLRoom = aValidRoom
        val hotel: YPLHotel = aValidHotel.withRooms((for (i <- 1 to 100) yield room.withRoomTypeId(i).build).toList)
        val request = aValidYplRequest
          .withCheapestRoomOnly(true)
          .withHeuristicRequest(Some(YplHeuristicRequest(limitCheapestRooms = Some(20))))
        "isEnableSupplierFinancialInfo == true" in {
          val overrideReq = request.withFlagInfo(YplFlagInfo(isEnableSupplierFinancialInfo = true))
          val roomLimitCtx = aValidYplContext
            .withRequest(overrideReq)
            .withExperimentContext(forcedFromRequestExperimentContext(overrideReq))
          val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
          val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx)

          res.map(_.hotel.rooms.length) should_== Some(100)
        }
        "hasSortingStrategy" in {
          val overrideReq = request.withHasSortingStrategy(true)
          val roomLimitCtx = aValidYplContext
            .withRequest(overrideReq)
            .withExperimentContext(forcedFromRequestExperimentContext(overrideReq))
          val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
          val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx)
          res.map(_.hotel.rooms.length) should_== Some(100)
        }
        "APO Secret Deal request" in {
          val overrideReq = request.withFlagInfo(YplFlagInfo(filterAPO = true))
          val roomLimitCtx = aValidYplContext
            .withRequest(overrideReq)
            .withExperimentContext(forcedFromRequestExperimentContext(overrideReq))
          val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
          val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx)
          res.map(_.hotel.rooms.length) should_== Some(100)
        }
      }

      "VEL-2093" should {
        val room: YPLRoom = aValidRoom
        val escapesRooms = (for (i <- 1 to 5) yield room
          .withRoomTypeId(i)
          .withPaymentModel(PaymentModel.Agency) // to ensure uid for escapes room is different from normal room
          .withStayPackageType(Some(StayPackageTypes.Escapes))
          .build).toList
        val nonEscapesRooms =
          (for (i <- 1 to 5) yield room.withRoomTypeId(i).withPaymentModel(PaymentModel.Merchant).build).toList
        val hotel: YPLHotel = aValidHotel.withRooms(escapesRooms ++ nonEscapesRooms)
        val request = aValidYplRequest
          .withCheapestRoomOnly(true)
          .withHeuristicRequest(Some(YplHeuristicRequest(limitCheapestRooms = Some(8))))
          .withHasSortingStrategy(false)

        "get duplicate room when VEL-2093=A and mostExpensiveRooms is picked from escapes rooms" in {
          val requestWithExpA = request.withAExperiment(ABTest.FIX_DUPLICATE_ROOMS_FROM_H4)
          val roomLimitCtx = aValidYplContext
            .withRequest(requestWithExpA)
            .withExperimentContext(forcedFromRequestExperimentContext(requestWithExpA))
          val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
          val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx)
          val totalRooms = res.map(_.hotel.rooms.size).get
          val uniqueRooms = res.map(_.hotel.rooms.map(_.uid).distinct.size).get
          (totalRooms - uniqueRooms) should_!= 0
        }

        "not get duplicate room when VEL-2093=B and mostExpensiveRooms is picked from escapes rooms" in {
          val requestWithExpB = request.withBExperiment(ABTest.FIX_DUPLICATE_ROOMS_FROM_H4)
          val roomLimitCtx = aValidYplContext
            .withRequest(requestWithExpB)
            .withExperimentContext(forcedFromRequestExperimentContext(requestWithExpB))
          val hotelWithEntry = YplHotelWithEntry(hotel, aValidHotelEntryModel)
          val res = stub.limitCheapestRoomHeuristic(Some(hotelWithEntry))(roomLimitCtx)
          val totalRooms = res.map(_.hotel.rooms.size).get
          val uniqueRooms = res.map(_.hotel.rooms.map(_.uid).distinct.size).get
          (totalRooms - uniqueRooms) should_== 0
        }
      }
    }

    "Heuristic Ordering" in {
      val ordering = new HeuristicOrdering(2)
      val mockBiggerRoom = aValidRoom
        .withRoomTypeId(3)
        .withOccupancy(aValidRoomOccupancy.withAdults(3).withChildren(2))
        .withPrices(List(aValidPrice.withNetExclusive(200).withTax(10)))
        .build
      val mockCheaperRoom =
        aValidRoom.withRoomTypeId(1).withPrices(List(aValidPrice.withNetExclusive(100).withTax(10))).build
      val mockSameSellAllInRoom =
        aValidRoom.withRoomTypeId(4).withPrices(List(aValidPrice.withNetExclusive(80).withTax(30))).build
      val mockRoom = aValidRoom.withRoomTypeId(2).withPrices(List(aValidPrice.withNetExclusive(100).withTax(20))).build

      List(mockBiggerRoom, mockRoom, mockCheaperRoom, mockSameSellAllInRoom).sorted(ordering).size should_== (4)
      List(mockRoom, mockCheaperRoom).sorted(ordering).head.roomTypeId should_== (1)
      List(mockRoom, mockCheaperRoom, mockBiggerRoom).sorted(ordering).head.roomTypeId should_== (3)
      List(mockRoom, mockCheaperRoom, mockSameSellAllInRoom).sorted(ordering).head.roomTypeId should_== (4)
    }

    "Heuristic appendOrReplaceIfDoesNotExist" in {
      val mockRoom1 = aValidRoom.withRoomTypeId(1).withPrices(List(aValidPrice.withNetExclusive(100).withTax(10))).build
      val mockRoom2 = aValidRoom.withRoomTypeId(2).withPrices(List(aValidPrice.withNetExclusive(200).withTax(10))).build
      val mockRoom3 = aValidRoom.withRoomTypeId(3).withPrices(List(aValidPrice.withNetExclusive(300).withTax(10))).build
      val mockEscapeRoom = aValidRoom.withStayPackageType(Some(StayPackageTypes.Escapes))
      val addingRoom =
        aValidRoom.withRoomTypeId(4).withPrices(List(aValidPrice.withNetExclusive(400).withTax(10))).build
      val addingRoomOpt = Some(addingRoom)
      val noneRoomOpt = None

      val stub = createServiceWithNoOpSink()
      // Add or append cases, consider min room size
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1), addingRoomOpt, 0).size should_== (2, 0)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1), addingRoomOpt, 1).size should_== (2, 0)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, mockRoom2), addingRoomOpt, 1).size should_== (2, 1)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, mockRoom2), addingRoomOpt, 1).size should_== (2, 1)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, mockRoom2, mockRoom3), addingRoomOpt, 1).size should_== (3, 1)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, mockRoom2, mockRoom3), addingRoomOpt, 2).size should_== (3, 1)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, mockRoom2, mockRoom3), addingRoomOpt, 3).size should_== (4, 0)

      // Always adding if does not set min room size
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1), addingRoomOpt).size should_== (2, 0)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, mockRoom2), addingRoomOpt).size should_== (3, 0)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, mockRoom2, mockRoom3), addingRoomOpt).size should_== (4, 0)

      // Do no thing if adding room is already existed
      stub.appendOrReplaceIfDoesNotExist(List(addingRoom), addingRoomOpt, 0).size should_== (1, 0)
      stub.appendOrReplaceIfDoesNotExist(List(addingRoom), addingRoomOpt, 1).size should_== (1, 0)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, addingRoom), addingRoomOpt, 1).size should_== (2, 0)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, addingRoom), addingRoomOpt, 1).size should_== (2, 0)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, mockRoom2, addingRoom), addingRoomOpt, 1).size should_== (3, 0)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, mockRoom2, addingRoom), addingRoomOpt, 2).size should_== (3, 0)
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1, mockRoom2, addingRoom), addingRoomOpt, 3).size should_== (3, 0)

      // Do nothing if adding room is None
      stub.appendOrReplaceIfDoesNotExist(List(mockRoom1), noneRoomOpt).size should_== (1, 0)

      // Do nothing if adding room is an escape room & VEL-2093 is B
      stub
        .appendOrReplaceIfDoesNotExist(List(mockRoom1), Some(mockEscapeRoom), 1, isDuplicateRoomFix = true)
        .size should_== (1, 0)
      stub
        .appendOrReplaceIfDoesNotExist(List(mockRoom1), Some(mockEscapeRoom), 1, isDuplicateRoomFix = false)
        .size should_== (2, 0)
    }

    "Heuristic addRoomIfDoesNotExist" should {
      val roomLimit = 30
      val roomsList = (for (i <- 1 to roomLimit) yield aValidRoomEntry.withRoomTypeId(i).build).toList
      val roomsListWithSpace = (for (i <- 1 to roomLimit - 5) yield aValidRoomEntry.withRoomTypeId(i).build).toList

      val candidateRoomDoesNotExistOpt = Some(aValidRoomEntry.withRoomTypeId(roomLimit + 1).build)
      val candidateRoomDoesExistOpt = Some(roomsList(roomLimit - 10))
      val noneRoomOpt = None

      val stub = createServiceWithNoOpSink()

      "Return same list" in {
        val (result, droppedRooms) = stub.addRoomIfDoesNotExist(roomsList, noneRoomOpt, roomLimit)
        result.size should_== roomLimit
        result.exists(stub.isMatch(_, candidateRoomDoesNotExistOpt.get)) should_== false
      }

      "Return same list if room exists already" in {
        val (result1, droppedRooms1) = stub.addRoomIfDoesNotExist(roomsList, candidateRoomDoesExistOpt, roomLimit)
        val (result2, droppedRooms2) =
          stub.addRoomIfDoesNotExist(roomsListWithSpace, candidateRoomDoesExistOpt, roomLimit)

        result1.size should_== roomLimit
        result1.exists(stub.isMatch(_, candidateRoomDoesExistOpt.get)) should_== true

        result2.size should_== 25
        result2.exists(stub.isMatch(_, candidateRoomDoesExistOpt.get)) should_== true
      }

      "Return list by adding new room if room does not exists and limit is not breached" in {
        val (result, droppedRooms) =
          stub.addRoomIfDoesNotExist(roomsListWithSpace, candidateRoomDoesNotExistOpt, roomLimit)
        val (result1, droppedRooms1) = stub.addRoomIfDoesNotExist(Nil, candidateRoomDoesNotExistOpt, roomLimit)

        result.size should_== roomsListWithSpace.size + 1
        result.exists(stub.isMatch(_, candidateRoomDoesNotExistOpt.get)) should_== true

        result1.size should_== 1
        result1.exists(stub.isMatch(_, candidateRoomDoesNotExistOpt.get)) should_== true

      }

      "Return list by replacing new room if room does not exists and limit is breached" in {
        val (result, droppedRooms) = stub.addRoomIfDoesNotExist(roomsList, candidateRoomDoesNotExistOpt, roomLimit)

        result.size should_== roomLimit
        result.exists(stub.isMatch(_, candidateRoomDoesNotExistOpt.get)) should_== true
      }

    }

    "build HotelAggregation for Cheapest heuristic correctly" in {
      val mockBenefits1 = List(aValidBenefit.withBenefitId(1).build, aValidBenefit.withBenefitId(2).build)
      val mockBenefits2 = List(aValidBenefit.withBenefitId(95).build)
      val mockBenefits3 =
        List(aValidBenefit.withBenefitId(1).withValue(20d).build, aValidBenefit.withBenefitId(114).build)

      val mockCxlCode1 = "365D100P"
      val mockCxlCode2 = "1D1N_1N"
      val mockCxlCode3 = "2D1N_1N"
      val mockRoom1 = aValidRoom
        .withRoomTypeId(1)
        .withRateCategory(aValidRateCategory.withBenefits(mockBenefits1))
        .withPaymentModel(PaymentModel.Merchant)
        .withCXLCode(mockCxlCode1)
        .withChannel(aValidYplChannel.copy(compositeChannelId = 1))
        .withPaymentOption(Set(PaymentOption.NoCreditCard, PaymentOption.PrepaymentRequired))
        .build
      val mockRoom2 = aValidRoom
        .withRoomTypeId(2)
        .build
        .withRateCategory(aValidRateCategory.withBenefits(mockBenefits2))
        .withPaymentModel(PaymentModel.MerchantCommission)
        .withCXLCode(mockCxlCode2)
        .withChannel(aValidYplChannel.copy(compositeChannelId = 2))
        .withPaymentOption(Set(PaymentOption.NoCreditCard, PaymentOption.PrepaymentRequired))
        .build
      val mockRoom3 = aValidRoom
        .withRoomTypeId(3)
        .build
        .withRateCategory(aValidRateCategory.withBenefits(mockBenefits3))
        .withPaymentModel(PaymentModel.Agency)
        .withCXLCode(mockCxlCode3)
        .withChannel(aValidYplChannel.copy(compositeChannelId = 7))
        .withPaymentOption(Set(PaymentOption.PrepaymentRequired))
        .build

      val stub = createServiceWithNoOpSink()

      val res = stub.buildHotelAggregation(List(mockRoom1, mockRoom2, mockRoom3), false)
      res.benefits should_== (mockBenefits1 ++ mockBenefits2 ++ mockBenefits3).toSet

      val resB = stub.buildHotelAggregation(List(mockRoom1, mockRoom2, mockRoom3), true)
      resB.benefits.map(_.id) should_== (mockBenefits1 ++ mockBenefits2 ++ mockBenefits3).map(_.id).toSet
      resB.benefits should_!= res.benefits

      resB.copy(benefits = Set.empty) should_== res.copy(benefits = Set.empty)

      res.cxlCodes should_== Map(2 -> List(YplCancellation("2D1N_1N", None, None)),
                                 4 -> List(YplCancellation("1D1N_1N", None, None)),
                                 1 -> List(YplCancellation("365D100P", None, None)))
      res.channelIds should_== Set(1, 2, 7)
      res.allowNoCreditCard should_== false
      res.prepayRequire should_== true
    }

    "isMatch" should {

      "return true when room identifier matches with room" in {
        val room: YplRoomEntry = aValidRoomEntry
        val roomIdentifier = toRoomIdentifiers(room)

        val stub = createServiceWithNoOpSink()
        val res = stub.isMatch(room, roomIdentifier)

        res should_== true
      }

      "return false when room identifier does not match with room" in {
        val room: YplRoomEntry = aValidRoomEntry
        val roomIdentifier = toRoomIdentifiers(room.copy(roomTypeId = 20))

        val stub = createServiceWithNoOpSink()
        val res = stub.isMatch(room, roomIdentifier)

        res should_== false
      }

      "return true when room1 matches with room2" in {
        val room1: YplRoomEntry = aValidRoomEntry
        val room2: YplRoomEntry = aValidRoomEntry

        val stub = createServiceWithNoOpSink()
        val res = stub.isMatch(room1, room2)

        res should_== true
      }

      "return false when room identifier does not match with room" in {
        val room1: YplRoomEntry = aValidRoomEntry
        val room2: YplRoomEntry = aValidRoomEntry

        val stub = createServiceWithNoOpSink()
        val res = stub.isMatch(room1, room2.copy(roomTypeId = 20))

        res should_== false
      }
    }
  }

  "LimitRoomsHeuristicServiceMock" should {
    val service = new LimitRoomsHeuristicServiceMock {}

    "return passthrough for enhancedHeuristic" in {
      service.enhancedHeuristic(
        List(aValidRoomEntry),
        1,
        (r: YplRoomEntry) => r.roomTypeId,
        (r: YplRoomEntry) => r.totalValueForHeuristics) should_== ((List(aValidRoomEntry), List.empty))
    }

    "do nothing for logHeuristicRoomTracking" in {
      service.logHeuristicRoomTracking(List(aValidRoomEntry), "some-reason", 123, 123, aValidYplContext) should_== ({})
    }
  }
}
