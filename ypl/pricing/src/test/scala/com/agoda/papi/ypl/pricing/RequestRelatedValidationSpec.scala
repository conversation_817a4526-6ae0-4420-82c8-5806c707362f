package com.agoda.papi.ypl.pricing

import com.agoda.papi.ypl.models.{
  GmtOffset,
  YPLTestContexts,
  YPLTestDataBuilders,
  YplContext,
  YplExperiment,
  YplExperiments,
  YplRequest,
}
import com.agoda.papi.ypl.models.enums.VipLevelType
import com.agoda.papi.ypl.models.pricing.proto.CustomerSegment
import com.agoda.papi.ypl.models.suppliers.DMC
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, LocalTime}
import org.mockito.Mockito.{times, verify}
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.mock.Mockito.{any, doReturn, spy}

/**
  * Created by ekalash<PERSON> on 10/28/2016 AD.
  */
class RequestRelatedValidationSpec extends SpecificationWithJUnit with YPLTestContexts with YPLTestDataBuilders {
  "Request Related Validation" should {
    // Create a concrete class for RequestRelatedValidation
    class TestRequestRelatedValidation extends RequestRelatedValidation
    val validator = new TestRequestRelatedValidation
    val defaultSupplierId = 0
    val jtbSupplier = DMC.JTBWL
    val jtbUatSupplier = DMC.JTBUATWL

    def getRequest(bookTime: DateTime = DateTime.now()): YplRequest = {
      val checkIn = bookTime.withMillisOfDay(0).plusDays(3)
      val los = 2
      val checkOut = checkIn.plusDays(los)

      YplRequest(
        "",
        checkIn,
        checkOut,
        bookingDate = bookTime,
        supplierFeatures = aValidSupplierFeatures,
        whitelabelSetting = aValidwhitelabelSetting,
        fences = aValidYplRequestFences,
      )
    }

    "Validate min / max advance purchase" in {
      "0. no validation" in {
        implicit val r = getRequest()
        implicit val ctx = YplContext(r)
        validator.validateMinMaxAdvancePurchase(None, None, 0, defaultSupplierId) should_== true
      }

      "1. BKK timezone: early morning" in {
        //  Note that UTC time will be -7 hours (so it's 6 pm previous day)
        val datenow = DateTime.now().withHourOfDay(1)
        implicit val r = getRequest(datenow)
        implicit val ctx = YplContext(r)

        "1. min advance validation" in {
          validator.validateMinMaxAdvancePurchase(Some(1), None, 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(3), None, 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(2), None, 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(4), None, 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(10), None, 0, defaultSupplierId) should_== false
        }

        "2. max advance validation" in {
          validator.validateMinMaxAdvancePurchase(None, Some(3), 0, defaultSupplierId) should_== false
          validator.validateMinMaxAdvancePurchase(None, Some(4), 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(None, Some(4), 0, jtbSupplier) should_== false
          validator.validateMinMaxAdvancePurchase(None, Some(5), 0, jtbSupplier) should_== true
        }

        "3. min / max advance validation" in {
          validator.validateMinMaxAdvancePurchase(Some(1), Some(1), 0, defaultSupplierId) should_== false
          validator.validateMinMaxAdvancePurchase(Some(1), Some(4), 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(5), Some(10), 0, defaultSupplierId) should_== false
        }
      }

      "2. BKK timezone: noon" in {
        //  Note that UTC time will be -7 hours (so BD is 5 am this day, checkIn = BD + 3 days at 00:00)
        val datenow = DateTime.now().withHourOfDay(12)
        implicit val r = getRequest(datenow)
        implicit val ctx = YplContext(r)

        "1. min advance validation" in {
          validator.validateMinMaxAdvancePurchase(Some(1), None, 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(2), None, 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(3), None, 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(4), None, 0, defaultSupplierId) should_== false
          validator.validateMinMaxAdvancePurchase(Some(10), None, 0, defaultSupplierId) should_== false
        }

        "2. max advance validation" in {
          validator.validateMinMaxAdvancePurchase(None, Some(2), 0, defaultSupplierId) should_== false
          validator.validateMinMaxAdvancePurchase(None, Some(3), 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(None, Some(3), 0, jtbSupplier) should_== false
          validator.validateMinMaxAdvancePurchase(None, Some(4), 0, jtbSupplier) should_== true
        }

        "3. min / max advance validation" in {
          validator.validateMinMaxAdvancePurchase(Some(1), Some(1), 0, defaultSupplierId) should_== false
          validator.validateMinMaxAdvancePurchase(Some(1), Some(4), 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(5), Some(10), 0, defaultSupplierId) should_== false
        }
      }

      "3. BKK timezone: almost midnight" in {
        //  Note that UTC time will be -7 hours (so BD is 05:00 this day, checkIn = BD + 3 days at 00:00)
        val datenow = DateTime.now().withHourOfDay(23).withMinuteOfHour(59)
        implicit val r = getRequest(datenow)
        implicit val ctx = YplContext(r)

        "1. min advance validation" in {
          validator.validateMinMaxAdvancePurchase(Some(1), None, 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(2), None, 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(3), None, 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(4), None, 0, defaultSupplierId) should_== false
          validator.validateMinMaxAdvancePurchase(Some(10), None, 0, defaultSupplierId) should_== false
        }

        "2. max advance validation" in {
          validator.validateMinMaxAdvancePurchase(None, Some(2), 0, defaultSupplierId) should_== false
          validator.validateMinMaxAdvancePurchase(None, Some(3), 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(None, Some(3), 0, jtbSupplier) should_== false
          validator.validateMinMaxAdvancePurchase(None, Some(4), 0, jtbSupplier) should_== true
        }

        "3. min / max advance validation" in {
          validator.validateMinMaxAdvancePurchase(Some(1), Some(1), 0, defaultSupplierId) should_== false
          validator.validateMinMaxAdvancePurchase(Some(1), Some(4), 0, defaultSupplierId) should_== true
          validator.validateMinMaxAdvancePurchase(Some(5), Some(10), 0, defaultSupplierId) should_== false
        }
      }

      "4. min / max validation with hotel timezone" in {
        //  Assume that we are in Bangkok timezone and now is 00:00, 3 days before checkin and 4 days before checkout
        implicit val r = getRequest(DateTime.now().withMillisOfDay(0))
        implicit val ctx = YplContext(r)

        /*
        +--------------+---------+-------------+--------------+--------------+-------------------+-----------+
        | BOOKING DATE | CHECKIN | MIN ADVANCE | MIN ADVANCED | HOTEL TIME   | BOOKING HOTEL GMT | ELIGIBLE? |
        | (BANGKOK)    |         |             | BOOKING      | (GMT OFFCET) |                   |           |
        +--------------+---------+-------------+--------------+--------------+-------------------+-----------+
        | 1/9 0:00     | 1/12    | 4           | 8-Jan        | -7           | 1/8 10:00         | yes       |
        | 1/9 0:00     | 1/12    | 4           | 8-Jan        | 0            | 1/8 17:00         | yes       |
        | 1/9 0:00     | 1/12    | 4           | 8-Jan        | 7            | 1/9 0:00          | no        |
        | 1/9 0:00     | 1/12    | 4           | 8-Jan        | 10           | 1/9 3:00          | no        |
        +--------------+---------+-------------+--------------+--------------+-------------------+-----------+
         */

        validator.validateMinMaxAdvancePurchase(Some(4), None, -7, defaultSupplierId) should_== true
        validator.validateMinMaxAdvancePurchase(Some(4), None, 0, defaultSupplierId) should_== true
        validator.validateMinMaxAdvancePurchase(Some(4), None, +7, defaultSupplierId) should_== false
        validator.validateMinMaxAdvancePurchase(Some(4), None, +10, defaultSupplierId) should_== false

        /*
        +--------------+---------+-------------+--------------+--------------+-------------------+-----------+
        | BOOKING DATE | CHECKIN | MAX ADVANCE | MAX ADVANCED | HOTEL TIME   | BOOKING HOTEL GMT | ELIGIBLE? |
        | (BANGKOK)    |         |             | BOOKING      | (GMT OFFCET) |                   |           |
        +--------------+---------+-------------+--------------+--------------+-------------------+-----------+
        | 1/9 0:00     | 1/12    | 3           | 9-Jan        | -7           | 1/8 10:00         | no        |
        | 1/9 0:00     | 1/12    | 3           | 9-Jan        | 0            | 1/8 17:00         | no        |
        | 1/9 0:00     | 1/12    | 3           | 9-Jan        | 7            | 1/9 0:00          | yes       |
        | 1/9 0:00     | 1/12    | 3           | 9-Jan        | 10           | 1/9 3:00          | yes       |
        +--------------+---------+-------------+--------------+--------------+-------------------+-----------+
         */

        validator.validateMinMaxAdvancePurchase(None, Some(3), 0, defaultSupplierId) should_== false
        validator.validateMinMaxAdvancePurchase(None, Some(3), +7, defaultSupplierId) should_== true
        validator.validateMinMaxAdvancePurchase(None, Some(4), 0, jtbSupplier) should_== false
        validator.validateMinMaxAdvancePurchase(None, Some(4), +7, jtbSupplier) should_== true
      }

      "5. Same day booking validation" in {
        val bookingDate = DateTime.parse("2023-04-01")
        val checkIn = bookingDate
        val checkOut = checkIn.plusDays(1)

        implicit val r: YplRequest = YplRequest(
          "",
          checkIn,
          checkOut,
          bookingDate = bookingDate,
          supplierFeatures = aValidSupplierFeatures,
          whitelabelSetting = aValidwhitelabelSetting,
          fences = aValidYplRequestFences,
        )
        implicit val ctx: YplContext = YplContext(r)
        validator.validateMinMaxAdvancePurchase(Some(0), Some(0), 7, jtbSupplier)(ctx, r) should_== true
      }

      "6. DMC Hardcoding Removal Experiment JTBFP-1295" in {
        val bookingDate = DateTime.now()
        val checkIn = bookingDate.plusDays(3)
        val checkOut = checkIn.plusDays(1)

        "JTBFP-1295 = A: fallback to hardcoded dmc check" in {
          implicit val r: YplRequest = YplRequest(
            "",
            checkIn,
            checkOut,
            bookingDate = bookingDate,
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          implicit val ctx: YplContext = YplContext(r)

          // Spy on the validator to mock validateBookDay responses
          val spiedValidator = spy(new TestRequestRelatedValidation)
          doReturn(true)
            .when(spiedValidator)
            .validateBookDay(any[Option[DateTime]], any[Option[DateTime]], any[GmtOffset])(any[YplContext],
                                                                                           any[YplRequest])

          spiedValidator.validateMinMaxAdvancePurchase(None, Some(4), 0, jtbSupplier)
          spiedValidator.validateMinMaxAdvancePurchase(None, Some(4), 0, jtbUatSupplier)
          verify(spiedValidator, times(3)).validateBookDay(any[Option[DateTime]],
                                                           any[Option[DateTime]],
                                                           any[GmtOffset])(any[YplContext], any[YplRequest])

          spiedValidator.validateMinMaxAdvancePurchase(None, Some(4), 0, defaultSupplierId)
          verify(spiedValidator, times(4)).validateBookDay(any[Option[DateTime]],
                                                           any[Option[DateTime]],
                                                           any[GmtOffset])(any[YplContext], any[YplRequest])

          ok
        }

        "JTBFP-1295 = B: use experiment flag + supplier feature flag to call validations correctly" in {
          val supplierFeatures = aValidSupplierFeatures.copy(
            features = Map(
              jtbSupplier -> avalidFeatureForJTB.copy(validateCheckinCheckout = false),
              defaultSupplierId -> aValidFeature,
              123 -> avalidFeatureForJTB,
            ),
          )

          implicit val r: YplRequest = YplRequest(
            "",
            checkIn,
            checkOut,
            bookingDate = bookingDate,
            supplierFeatures = supplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          implicit val ctx: YplContext =
            YplContext(r).withExperimentContext(forceBExperimentContext(YplExperiments.DMC_HARDCODING_REMOVAL)).build

          // Spy on the validator to mock validateBookDay responses
          val spiedValidator = spy(new TestRequestRelatedValidation)
          doReturn(true)
            .when(spiedValidator)
            .validateBookDay(any[Option[DateTime]], any[Option[DateTime]], any[GmtOffset])(any[YplContext],
                                                                                           any[YplRequest])

          spiedValidator.validateMinMaxAdvancePurchase(None, Some(4), 0, jtbSupplier)
          verify(spiedValidator, times(1)).validateBookDay(any[Option[DateTime]],
                                                           any[Option[DateTime]],
                                                           any[GmtOffset])(any[YplContext], any[YplRequest])

          // Other suppliers should return true
          spiedValidator.validateMinMaxAdvancePurchase(None, Some(4), 0, defaultSupplierId)
          verify(spiedValidator, times(2)).validateBookDay(any[Option[DateTime]],
                                                           any[Option[DateTime]],
                                                           any[GmtOffset])(any[YplContext], any[YplRequest])

          spiedValidator.validateMinMaxAdvancePurchase(None, Some(4), 0, 123)
          verify(spiedValidator, times(4)).validateBookDay(any[Option[DateTime]],
                                                           any[Option[DateTime]],
                                                           any[GmtOffset])(any[YplContext], any[YplRequest])

          // check for supplier with no corresponding configuration, validateCheckinCheckout should evaluate to false
          // should call validateBookDay only once
          spiedValidator.validateMinMaxAdvancePurchase(None, Some(4), 0, 124)
          verify(spiedValidator, times(5)).validateBookDay(any[Option[DateTime]],
                                                           any[Option[DateTime]],
                                                           any[GmtOffset])(any[YplContext], any[YplRequest])

          ok
        }
      }
    }

    "Validate book on" in {
      val week = new DateTime(2017, 6, 19, 0, 0)
      val mon = getRequest(week)
      val tue = getRequest(week.plusDays(1))
      val wed = getRequest(week.plusDays(2))
      val thu = getRequest(week.plusDays(3))
      val fri = getRequest(week.plusDays(4))
      val sat = getRequest(week.plusDays(5))
      val sun = getRequest(week.plusDays(6))

      "Basic checks" in {
        def validate(filter: Option[String], expected: Boolean) = {
          validator.validateBookOn(filter)(mon) should_== expected
          validator.validateBookOn(filter)(tue) should_== expected
          validator.validateBookOn(filter)(wed) should_== expected
          validator.validateBookOn(filter)(thu) should_== expected
          validator.validateBookOn(filter)(fri) should_== expected
          validator.validateBookOn(filter)(sat) should_== expected
          validator.validateBookOn(filter)(sun) should_== expected
        }

        "Always success" in {
          "without filter" in validate(None, expected = true)
          "with empty filter" in validate(Some(""), expected = true)
          "with null filter" in validate(null, expected = true)
          "with 100% filter" in validate(Some("1111111"), expected = true)
          "with incorrect filter" in validate(Some("abcdefg"), expected = true)
          "with incomplete filter" in validate(Some("000"), expected = true)
        }

        "Always fail" in {
          "with zero filter" in validate(Some("0000000"), expected = false)
        }
      }

      "Always success on particular filter" in {
        validator.validateBookOn(Some("1000000"))(sun) should_== true
        validator.validateBookOn(Some("0100000"))(mon) should_== true
        validator.validateBookOn(Some("0010000"))(tue) should_== true
        validator.validateBookOn(Some("0001000"))(wed) should_== true
        validator.validateBookOn(Some("0000100"))(thu) should_== true
        validator.validateBookOn(Some("0000010"))(fri) should_== true
        validator.validateBookOn(Some("0000001"))(sat) should_== true
      }

      "Always fail on particular filter" in {
        validator.validateBookOn(Some("0111111"))(sun) should_== false
        validator.validateBookOn(Some("1011111"))(mon) should_== false
        validator.validateBookOn(Some("1101111"))(tue) should_== false
        validator.validateBookOn(Some("1110111"))(wed) should_== false
        validator.validateBookOn(Some("1111011"))(thu) should_== false
        validator.validateBookOn(Some("1111101"))(fri) should_== false
        validator.validateBookOn(Some("1111110"))(sat) should_== false
      }
    }

    "validate book day" in {

      val checkInTime = new DateTime(2020, 1, 10, 0, 0)
      val bookingTime = new DateTime(2020, 1, 11, 0, 0, 15)
      val request =
        aValidYplRequest.withCheckIn(checkInTime).withCheckout(checkInTime.plusDays(1)).withBookingDate(bookingTime)

      "with AFFILIATE_PAST_MIDNIGHT experiment on" in {
        val ctx = YplContext(request)

        validator.validateBookDay(None, None, -7)(ctx, request) should_== true
        validator.validateBookDay(None, None, 0)(ctx, request) should_== true
        validator.validateBookDay(None, None, 7)(ctx, request) should_== true
        validator.validateBookDay(None, None, 10)(ctx, request) should_== true
      }

      "Booking date ahead checkin date more than 2 day" in {
        val req = request.withBookingDate(bookingTime.plusDays(1))
        val ctx = YplContext(req)

        validator.validateBookDay(None, None, -7)(ctx, req) should_== true
        validator.validateBookDay(None, None, 0)(ctx, req) should_== true
        validator.validateBookDay(None, None, 7)(ctx, req) should_== false
        validator.validateBookDay(None, None, 10)(ctx, req) should_== false
      }

    }

    "Validate same day booking cut off time" in {
      val hotelGMT = 7

      "1. Same day booking date time should validate correctly" in {
        val checkInTime = new DateTime(2020, 1, 10, 0, 0)
        val bookingTime = new DateTime(2020, 1, 9, 23, 59)

        validator.validateSameDayBookingCutoffTime(checkInTime, bookingTime, None, hotelGMT) should_== true
      }

      "2. Same day booking date time should validate correctly" in {
        val checkInTime = new DateTime(2020, 1, 10, 0, 0)
        val bookingTime = new DateTime(2020, 1, 10, 0, 0)

        validator.validateSameDayBookingCutoffTime(checkInTime, bookingTime, None, hotelGMT) should_== true
      }

      "3. Same day booking date time should validate correctly" in {
        val checkInTime = new DateTime(2020, 1, 10, 0, 0)
        val bookingTime = new DateTime(2020, 1, 10, 23, 59)
        val bookingCutOffTime = new DateTime(2020, 1, 10, 23, 59).getMillisOfDay

        validator.validateSameDayBookingCutoffTime(checkInTime,
                                                   bookingTime,
                                                   Some(bookingCutOffTime),
                                                   hotelGMT) should_== true
      }

      "4. Same day booking date time should validate correctly" in {
        val checkInTime = new DateTime(2020, 1, 10, 0, 0)
        val bookingTime = new DateTime(2020, 1, 10, 23, 59, 59, 0)
        val bookingCutOffTime = new DateTime(2020, 1, 10, 23, 59, 0, 0).getMillisOfDay

        validator.validateSameDayBookingCutoffTime(checkInTime,
                                                   bookingTime,
                                                   Some(bookingCutOffTime),
                                                   hotelGMT) should_== false
      }

      "5. Same day booking date time should validate correctly" in {
        val checkInTime = new DateTime(2020, 1, 10, 0, 0)
        val bookingTime = new DateTime(2020, 1, 9, 23, 59, 59)
        val bookingCutOffTime = new DateTime(2020, 1, 10, 23, 59, 59).getMillisOfDay

        validator.validateSameDayBookingCutoffTime(checkInTime,
                                                   bookingTime,
                                                   Some(bookingCutOffTime),
                                                   hotelGMT) should_== true
      }

      "6. Same day booking date time should validate correctly when booking cut off time is 0" in {
        val checkInTime = new DateTime(2020, 1, 10, 0, 0)
        val bookingTime = new DateTime(2020, 1, 9, 23, 59, 59)
        val bookingCutOffTime = new DateTime(2020, 1, 10, 0, 0, 0).getMillisOfDay

        validator.validateSameDayBookingCutoffTime(checkInTime,
                                                   bookingTime,
                                                   Some(bookingCutOffTime),
                                                   hotelGMT) should_== true
      }
    }

    "Validate customer segment" in {

      "Validate none vip segment if LOY-5731 is A" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            DateTime.now(),
            DateTime.now(),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            experiments = List(YplExperiment(YplExperiments.CHECK_VIP_CUSTOMER_SEGMENT, 'A')),
          ))
        val customerSegments =
          List[CustomerSegment](CustomerSegment(Some(1), None, None), CustomerSegment(None, Some("1"), None))

        validator.validateCustomerSegment(customerSegments, "1", 1, Some(VipLevelType.PLATINUM)) should_== true
      }

      "Validate none vip segment if LOY-5731 is B" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            DateTime.now(),
            DateTime.now(),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            experiments = List(YplExperiment(YplExperiments.CHECK_VIP_CUSTOMER_SEGMENT, 'B')),
          ))
        val customerSegments =
          List[CustomerSegment](CustomerSegment(Some(1), None, None), CustomerSegment(None, Some("1"), None))

        validator.validateCustomerSegment(customerSegments, "1", 1, Some(VipLevelType.PLATINUM)) should_== true
      }

      "Validate vip segment with origin and language if LOY-5731 is A" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            DateTime.now(),
            DateTime.now(),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            experiments = List(YplExperiment(YplExperiments.CHECK_VIP_CUSTOMER_SEGMENT, 'A')),
          ))
        val customerSegments = List(
          CustomerSegment(Some(1), None, None),
          CustomerSegment(None, Some("1"), None),
          CustomerSegment(None, None, Some(VipLevelType.GOLD)),
          CustomerSegment(None, None, Some(VipLevelType.PLATINUM)),
        )

        validator.validateCustomerSegment(customerSegments, "1", 1, Some(VipLevelType.UNDEFINED)) should_== false
        validator.validateCustomerSegment(customerSegments, "1", 1, Some(VipLevelType.SILVER)) should_== false
        validator.validateCustomerSegment(customerSegments, "1", 1, Some(VipLevelType.GOLD)) should_== false
        validator.validateCustomerSegment(customerSegments, "1", 1, Some(VipLevelType.PLATINUM)) should_== false
      }

      "Validate vip segment with origin and language if LOY-5731 is B" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            DateTime.now(),
            DateTime.now(),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            experiments = List(YplExperiment(YplExperiments.CHECK_VIP_CUSTOMER_SEGMENT, 'B')),
          ))
        val customerSegments = List(
          CustomerSegment(Some(1), None, None),
          CustomerSegment(None, Some("1"), None),
          CustomerSegment(None, None, Some(VipLevelType.GOLD)),
          CustomerSegment(None, None, Some(VipLevelType.PLATINUM)),
        )

        validator.validateCustomerSegment(customerSegments, "1", 1, Some(VipLevelType.UNDEFINED)) should_== false
        validator.validateCustomerSegment(customerSegments, "1", 1, Some(VipLevelType.SILVER)) should_== false
        validator.validateCustomerSegment(customerSegments, "1", 1, Some(VipLevelType.GOLD)) should_== true
        validator.validateCustomerSegment(customerSegments, "1", 1, Some(VipLevelType.PLATINUM)) should_== true
      }

      "Validate vip segment with Diamond user" in {
        implicit val ctx = YplContext(
          YplRequest(
            "",
            DateTime.now(),
            DateTime.now(),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            experiments = List(
              YplExperiment(YplExperiments.CHECK_VIP_CUSTOMER_SEGMENT, 'B'),
            ),
          ),
        )
        val goldCustomerSegments = List(
          CustomerSegment(None, None, Some(VipLevelType.GOLD)),
        )
        val platinumCustomerSegments = List(
          CustomerSegment(None, None, Some(VipLevelType.PLATINUM)),
        )
        validator.validateCustomerSegment(goldCustomerSegments, "1", 1, Some(VipLevelType.DIAMOND)) should_== false
        validator.validateCustomerSegment(platinumCustomerSegments, "1", 1, Some(VipLevelType.DIAMOND)) should_== true
      }

      "Validate Customer segment filter language" in {
        val request = aValidYplRequest.withClientInfo(aValidClientInfo.withLanguage(3).build).build
        implicit val ctx: YplContext = YplContext(request)
        val customerSegments =
          List[CustomerSegment](CustomerSegment(Some(1), Some("TH"), None), CustomerSegment(Some(2), Some("UK"), None))
        validator.validateCustomerSegment(customerSegments, "TH", 1, None) should_== true
      }

      "Validate Customer segment origin case insensitive" in {
        val customerSegments = List(
          CustomerSegment(languageId = None, countryCode = Some("us"), vipLevel = None),
          CustomerSegment(languageId = None, countryCode = Some("TH"), vipLevel = None),
          CustomerSegment(languageId = Some(1), countryCode = Some("sg"), vipLevel = None),
          CustomerSegment(languageId = Some(1), countryCode = Some("JP"), vipLevel = None),
          CustomerSegment(languageId = Some(2), countryCode = None, vipLevel = None),
        )
        "ExpA: DFOPS-3879" in {
          implicit val ctx = aValidYplContext
          validator.validateOriginLanguageCustomerSegment(customerSegments, "us", 0)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "US", 0)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "th", 0)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "TH", 0)(ctx) shouldEqual true

          validator.validateOriginLanguageCustomerSegment(customerSegments, "us", 1)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "US", 1)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "th", 1)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "TH", 1)(ctx) shouldEqual true

          validator.validateOriginLanguageCustomerSegment(customerSegments, "sg", 0)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "SG", 0)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "jp", 0)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "JP", 0)(ctx) shouldEqual false

          validator.validateOriginLanguageCustomerSegment(customerSegments, "sg", 1)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "SG", 1)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "jp", 1)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "JP", 1)(ctx) shouldEqual true

          validator.validateOriginLanguageCustomerSegment(customerSegments, "", 2)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "", 3)(ctx) shouldEqual false
        }

        "ExpB: DFOPS-3879" in {
          val request = aValidYplRequest.withBExperiment(YplExperiments.ORIGIN_CASE_INSENSITIVE_CUSTOMER_SEGMENT)
          implicit val ctx: YplContext = YplContext(request)
          validator.validateOriginLanguageCustomerSegment(customerSegments, "us", 0)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "US", 0)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "th", 0)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "TH", 0)(ctx) shouldEqual true

          validator.validateOriginLanguageCustomerSegment(customerSegments, "us", 1)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "US", 1)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "th", 1)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "TH", 1)(ctx) shouldEqual true

          validator.validateOriginLanguageCustomerSegment(customerSegments, "sg", 0)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "SG", 0)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "jp", 0)(ctx) shouldEqual false
          validator.validateOriginLanguageCustomerSegment(customerSegments, "JP", 0)(ctx) shouldEqual false

          validator.validateOriginLanguageCustomerSegment(customerSegments, "sg", 1)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "SG", 1)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "jp", 1)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "JP", 1)(ctx) shouldEqual true

          validator.validateOriginLanguageCustomerSegment(customerSegments, "", 2)(ctx) shouldEqual true
          validator.validateOriginLanguageCustomerSegment(customerSegments, "", 3)(ctx) shouldEqual false
        }
      }
    }
  }

  case class TestInput(cutOff: Option[LocalTime],
                       gmtOffset: Int,
                       gmtOffsetMin: Int,
                       bookingTime: String,
                       checkInDate: String,
                       expected: Boolean)

  val oneSecondBeforeMidnight = Some(new LocalTime(23, 59, 59))
  for (testInput <- Seq(
      TestInput(oneSecondBeforeMidnight, 5, 30, "2023-07-13T01:29+07:00", "2023-07-12", true),
      TestInput(None, 5, 30, "2023-07-13T01:31+07:00", "2023-07-12", true),
      TestInput(oneSecondBeforeMidnight, 5, 30, "2023-07-13T01:31+07:00", "2023-07-12", false),
    )) {
    class TestRequestRelatedValidation extends RequestRelatedValidation
    val validator = new TestRequestRelatedValidation()

    "validateBookingTimeIsBeforePropertyCutOffTime" should {
      s"validate booking time against property cut off time correctly when input $testInput" in {
        val isValid = validator.validateBookingTimeIsBeforePropertyCutOffTime(
          testInput.cutOff,
          testInput.gmtOffset,
          testInput.gmtOffsetMin,
          DateTime.parse(testInput.bookingTime),
          DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(testInput.checkInDate),
        )

        isValid should_== testInput.expected
      }
    }
  }
}
