package com.agoda.papi.ypl.pricing.promotions

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.enums.room.DiscountType
import com.agoda.papi.ypl.commission.models.Country
import com.agoda.papi.ypl.models.YplExperiments.HOTEL_MVP_PROMOTIONS
import com.agoda.papi.ypl.models.api.request.YplClientInfo
import com.agoda.papi.ypl.models.builders.ypl.YplContextMock
import com.agoda.papi.ypl.models.consts.PulseDispatch.MinutesInAnHour
import com.agoda.papi.ypl.models.consts.CampaignTypeId
import com.agoda.papi.ypl.models.enums.VipLevelType
import com.agoda.papi.ypl.models.pricing.proto.AdditionalDispatchReason.CustomerSegmentOverriding
import com.agoda.papi.ypl.models.pricing.proto.{AdditionalDispatchReason, CustomerSegment, PromotionEntry}
import com.agoda.papi.ypl.models.{
  YPLTestContexts,
  YPLTestDataBuilders,
  YplContext,
  YplExperiment,
  YplExperiments,
  YplRateFence,
  YplRequest,
  YplRoomEntry,
}
import com.agoda.papi.ypl.pricing.BLTPromotionSettings
import com.agoda.papi.ypl.pricing.mocks.{OriginManagerMock, PromotionEntryValidationMock, CidToOriginMapperMock}
import com.agoda.papi.ypl.services.{OriginManager, CidToOriginMapper}
import org.joda.time.DateTime
import org.scalatest.prop.Tables.Table
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.mock.Mockito.mock
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{times, verify, when}
import models.consts.ABTest

class PromoValidationSpec extends SpecificationWithJUnit with YPLTestDataBuilders with YPLTestContexts {
  val target = new {} with PromoValidation {
    override val originManager = new {} with OriginManagerMock {}
    override val cidToOriginMapper = new {} with CidToOriginMapperMock {}
    override val promotionEntryValidation: PromotionEntryValidation = new {} with PromotionEntryValidationMock {}
  }

  private def fencePromotions(promo: PromotionEntry, bltPromotionSettings: BLTPromotionSettings)(implicit
    ctx: YplContext) = {
    val room = aValidRoomEntry.withAvailablePromotions(List(promo)).build
    target
      .fencePromotions(aValidHotelInfo, List(room), bltPromotionSettings, Map.empty[Int, PulseCampaignMetadata])
      .head
      .availablePromotions
  }
  Table(
    ("testCaseDesc", "experimentVariant"),
    ("using hardcode", 'A'),
    ("using pulse metadata precache", 'B'),
  ).forEvery { (testCaseDesc, experimentVariant) =>
    "promo validation when pulse dispatch time fence exist" in {
      val hour = 36000000000L
      val minute = 600000000L

      val validHotel = aValidHotelInfo.copy(
        countryId = Country.TH.id,
        pulseCampaignIdBlacklist = Nil,
      )
      implicit val request: YplRequest = aValidYplRequest
        .withClientInfo(aValidClientInfo.withLanguage(22).withPlatformId(None).build)
        .build
        .copy(experiments =
          List(YplExperiment(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_USING_PULSE_PRECACHE, experimentVariant)))
      implicit val ctx: YplContextMock = YplContext(request)
      s"extension period of non time fence by $testCaseDesc" should {
        val promoEntry = PromotionEntry(
          discountType = DiscountType.PercentDiscount,
          bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
          bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
          bookTimeFrom = Some(14 * hour), // 14:00
          bookTimeTo = Some(18 * hour), // 18:00
          bookOn = "1111111",
          customerSegments = List(CustomerSegment(None, None)),
          typeId = 123,
        )
        "dispatch fail and extra badge is false with book date in early extension period" in {
          val bookingDate = new DateTime(2017, 10, 10, 5, 0, 0)

          target.validate(
            promoEntry,
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId =
              Map(123 -> PulseCampaignMetadata(123, 1, 12345, 1, 1, None, None, None, None, Some(0), Some(0), List.empty)),
          ) must_== (false, None)
        }
      }

      s"extension period of NoonDashDeals by $testCaseDesc" should {
        val promoEntry = PromotionEntry(
          discountType = DiscountType.PercentDiscount,
          bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
          bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
          bookTimeFrom = Some(14 * hour), // 14:00
          bookTimeTo = Some(17 * hour + 59 * minute), // 18:00
          bookOn = "1111111",
          customerSegments = List(CustomerSegment(None, None)),
          typeId = 123,
        )

        "dispatch pass and extra badge is true with extension book date to is midnight" in {
          val bookingDate = new DateTime(2017, 10, 10, 20, 0, 0)

          target.validate(
            promoEntry.copy(bookTimeFrom = Some(15 * hour), bookTimeTo = Some(18 * hour)),
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           CampaignTypeId.NoonDashDeals,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(14 * MinutesInAnHour),
                                           Some(6 * MinutesInAnHour),
                                           List.empty)),
          ) must_== (true, Some(AdditionalDispatchReason.TimeFenceExtended))
        }
        "dispatch pass and extra badge is true with empty book time" in {
          val bookingDate = new DateTime(2017, 10, 10, 5, 0, 0)

          target.validate(
            promoEntry.copy(bookTimeFrom = None, bookTimeTo = None),
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId =
              Map(123 -> PulseCampaignMetadata(123, 1, CampaignTypeId.NoonDashDeals, 1, 1)),
          ) must_== (true, None)
        }
        "dispatch pass and extra badge is true with book date in late extension period" in {
          val bookingDate = new DateTime(2017, 10, 10, 18, 0, 0)

          target.validate(
            promoEntry,
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           CampaignTypeId.NoonDashDeals,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(14 * MinutesInAnHour),
                                           Some(6 * MinutesInAnHour),
                                           List.empty)),
          ) must_== (true, Some(AdditionalDispatchReason.TimeFenceExtended))
        }
        "dispatch fail and extra badge is false with book date out of extension period in date" in {
          val bookingDate = new DateTime(2017, 10, 9, 15, 0, 0)

          target.validate(promoEntry,
                          bookingDate,
                          validHotel,
                          None,
                          pulseCampaignMetadataByPromotionTypeId =
                            Map(123 -> PulseCampaignMetadata(123, 1, 1056, 1, 1))) must_== (false, None)
        }
        "dispatch fail and extra badge is false with book date out of extension period in time" in {
          val bookingDate = new DateTime(2017, 10, 12, 23, 59, 0)

          target.validate(
            promoEntry.copy(bookTimeTo = Some(15 * hour)),
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           CampaignTypeId.NoonDashDeals,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(14 * MinutesInAnHour),
                                           Some(6 * MinutesInAnHour),
                                           List.empty)),
          ) must_== (false, None)
        }

        "dispatch pass and extra badge is true with book date from is equal to current book date" in {
          val bookingDate = new DateTime(2017, 10, 10, 12, 0, 0)

          target.validate(
            promoEntry,
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           CampaignTypeId.NoonDashDeals,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(14 * MinutesInAnHour),
                                           Some(6 * MinutesInAnHour),
                                           List.empty)),
          ) must_== (true, Some(AdditionalDispatchReason.TimeFenceExtended))
        }

        "dispatch pass and extra badge is true with book date to is equal to current book date" in {
          val bookingDate = new DateTime(2017, 10, 12, 12, 0, 0)

          target.validate(
            promoEntry,
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           CampaignTypeId.NoonDashDeals,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(14 * MinutesInAnHour),
                                           Some(6 * MinutesInAnHour),
                                           List.empty)),
          ) must_== (true, Some(AdditionalDispatchReason.TimeFenceExtended))
        }

        "dispatch pass and extra badge is true with book date to is equal to current book date for MSE traffic" in {
          implicit val request: YplRequest = aValidYplRequest
            .withClientInfo(aValidClientInfo.withLanguage(22).withPlatformId(Some(1008)).build)
            .build
            .copy(experiments =
              List(YplExperiment(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_TO_MSE_AND_AFFILIATE_TRAFFIC, 'B')))
          implicit val ctx: YplContextMock = YplContext(request)
          val bookingDate = new DateTime(2017, 10, 12, 12, 0, 0)

          target.validate(
            promoEntry,
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           CampaignTypeId.NoonDashDeals,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(14 * MinutesInAnHour),
                                           Some(6 * MinutesInAnHour),
                                           List.empty)),
          ) must_== (true, Some(AdditionalDispatchReason.TimeFenceExtended))
        }
      }

      s"when in extension period of PluseMinus12Hours with $testCaseDesc" should {
        val promoEntry = PromotionEntry(
          discountType = DiscountType.PercentDiscount,
          bookFrom = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
          bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
          bookTimeFrom = Some(0), // 00:00
          bookTimeTo = Some(23 * hour + 59 * minute), // 23:59 (mid night for full day)
          bookOn = "1111111",
          customerSegments = List(CustomerSegment(None, None)),
          typeId = 123,
        )
        "dispatch fail and extra badge is false with book time to is not midnight" in {
          val bookingDate = new DateTime(2017, 10, 13, 11, 59, 0)

          target.validate(
            promoEntry.copy(bookTimeTo = Some(1 * hour)),
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           999999,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(12 * MinutesInAnHour),
                                           Some(12 * MinutesInAnHour))),
          ) must_== (false, None)
        }
        "dispatch pass and extra badge is true with book date in early extension period" in {
          val bookingDate = new DateTime(2017, 10, 11, 12, 0, 0)

          target.validate(
            promoEntry,
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           999999,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(12 * MinutesInAnHour),
                                           Some(12 * MinutesInAnHour))),
          ) must_== (true, Some(AdditionalDispatchReason.TimeFenceExtended))
        }
        "dispatch pass and extra badge is true with book date in late extension period" in {
          val bookingDate = new DateTime(2017, 10, 13, 11, 59, 0)

          target.validate(
            promoEntry,
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           999999,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(12 * MinutesInAnHour),
                                           Some(12 * MinutesInAnHour))),
          ) must_== (true, Some(AdditionalDispatchReason.TimeFenceExtended))
        }

        "dispatch pass and extra badge is true with book date in late extension period for MSE traffic" in {
          val bookingDate = new DateTime(2017, 10, 13, 11, 59, 0)
          implicit val request: YplRequest = aValidYplRequest
            .withClientInfo(aValidClientInfo.withLanguage(22).withPlatformId(Some(1008)).build)
            .build
            .copy(experiments =
              List(YplExperiment(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_TO_MSE_AND_AFFILIATE_TRAFFIC, 'B')))
          implicit val ctx: YplContextMock = YplContext(request)

          target.validate(
            promoEntry,
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           999999,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(12 * MinutesInAnHour),
                                           Some(12 * MinutesInAnHour))),
          ) must_== (true, Some(AdditionalDispatchReason.TimeFenceExtended))
        }

        "dispatch fail and extra badge is false with book date out of early extension period" in {
          val bookingDate = new DateTime(2017, 10, 11, 11, 59, 0)

          target.validate(
            promoEntry,
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           999999,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(12 * MinutesInAnHour),
                                           Some(12 * MinutesInAnHour))),
          ) must_== (false, None)
        }
        "dispatch fail and extra badge is false with book date out of late extension period" in {
          val bookingDate = new DateTime(2017, 10, 13, 12, 0, 0)

          target.validate(
            promoEntry,
            bookingDate,
            validHotel,
            None,
            pulseCampaignMetadataByPromotionTypeId = Map(
              123 -> PulseCampaignMetadata(123,
                                           1,
                                           999999,
                                           1,
                                           1,
                                           None,
                                           None,
                                           None,
                                           None,
                                           Some(12 * MinutesInAnHour),
                                           Some(12 * MinutesInAnHour))),
          ) must_== (false, None)
        }
      }
    }
  }
  Table(
    ("testCaseName", "countryId", "variant", "platform", "pulseCampaignIdBlacklist"),
    ("isAllMseTraffic true without MSE experiment",
     Country.TH.id,
     'A',
     Some(1008),
     Seq.empty[Int],
    ), // 1008 is MSE platform
    ("NorthAmerica without MSE experiment", Country.US.id, 'A', None, Seq.empty[Int]),
    ("NorthAmerica with MSE experiment", Country.US.id, 'B', None, Seq.empty[Int]),
    ("Campaign In blacklist without MSE experiment", Country.TH.id, 'A', None, Seq(999999)),
    ("Campaign In blacklist with MSE experiment", Country.TH.id, 'B', None, Seq(999999)),
    ("Disable all campaign without MSE experiment", Country.TH.id, 'A', None, Seq(0)),
    ("Disable all campaign with MSE experiment", Country.TH.id, 'B', None, Seq(0)),
  ).forEvery { (testCaseName, countryId, variant, platform, pulseCampaignIdBlacklist) =>
    s"When $testCaseName should not dispatch time fence" in {
      val hour = 36000000000L
      val minute = 600000000L
      val request = aValidYplRequest
        .withClientInfo(aValidClientInfo.withLanguage(3).withPlatformId(platform).build)
        .build
        .copy(experiments =
          List(YplExperiment(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_TO_MSE_AND_AFFILIATE_TRAFFIC, variant)))
      val ctx: YplContext = YplContext(request)
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(0), // 00:00
        bookTimeTo = Some(23 * hour + 59 * minute), // 23:59 (mid night for full day)
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None)),
        typeId = 123,
      )
      val bookingDate = new DateTime(2017, 10, 11, 12, 0, 0)

      target.validate(
        promoEntry,
        bookingDate,
        aValidHotelInfo.copy(
          countryId = countryId,
          pulseCampaignIdBlacklist = pulseCampaignIdBlacklist,
        ),
        None,
        pulseCampaignMetadataByPromotionTypeId = Map(123 -> PulseCampaignMetadata(123, 1, 999999, 1, 1)),
      )(request, ctx) must_== (false, None)
    }
  }

  "promo validation when booking time window spans across midnight ('cross-day' case is applicable)" should {
    implicit val request = aValidYplRequest.withAExperiment(YplExperiments.HOTEL_MVP_PROMOTIONS).build
    implicit val ctx = YplContext(request)
    "pass when booking time is after 'bookTimeFrom'" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None)),
      )
      val bookingDate = new DateTime(2017, 10, 11, 11, 0, 0) // 11:00

      target.validate(promoEntry, bookingDate, aValidHotelInfo, None) must_== (true, None)
    }

    "pass when booking time is before 'bookTimeTo'" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None)), // 08:00
      )
      val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)

      target.validate(promoEntry, bookingDate, aValidHotelInfo, None) must_== (true, None)
    }

    "fail when booking time is neither after 'bookTimeFrom' nor before 'bookTimeTo'" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None)), // 09:00
      )
      val bookingDate = new DateTime(2017, 10, 11, 9, 0, 0)

      target.validate(promoEntry, bookingDate, aValidHotelInfo, None) must_== (false, None)
    }
  }

  "Promotion validation for Pulse negative test" should {
    implicit val request = aValidYplRequest.build
    implicit val ctx = YplContext(request)
    val promoEntry = PromotionEntry(
      discountType = DiscountType.PercentDiscount,
      bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
      bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
      bookTimeFrom = Some(360000000000L), // 10:00
      bookTimeTo = Some(300000000000L), // 08:20
      bookOn = "1111111",
      customerSegments = List(CustomerSegment(None, None)), // 08:00
      typeId = 1,
    )
    val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)

    "pass when pulsePromotionsToRemove does not contain promotion type id" in {
      target.validate(promoEntry,
                      bookingDate,
                      aValidHotelInfo,
                      None,
                      allowNoCCPromo = false,
                      Set.empty) must_== (true, None)
    }

    "fail when pulsePromotionsToRemove contains promotion type id" in {
      target.validate(promoEntry,
                      bookingDate,
                      aValidHotelInfo,
                      None,
                      allowNoCCPromo = false,
                      Set(1)) must_== (false, None)
    }
  }

  "Promotion validation for AFF-1542 B user case valid cid" should {
    implicit val request = aValidYplRequest
      .withBExperiment(YplExperiments.HOTEL_MVP_PROMOTIONS)
      .withClientInfo(YplClientInfo(cid = Some(1)))
      .build
    implicit val ctx = YplContext(request)
    val setting = BLTPromotionSettings.apply(Set[Integer](1), Set[String]("ZA"))
    "pass when promotion has required country" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("ZA"))),
      )

      fencePromotions(promoEntry, setting) must beEmpty
    }

    "pass when booking time is after 'bookTimeFrom'" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("ZA"))),
      )
      val bookingDate = new DateTime(2017, 10, 11, 11, 0, 0) // 11:00

      target.validate(promoEntry, bookingDate, aValidHotelInfo) must_== (true, None)
    }

    "pass when booking time is before 'bookTimeTo'" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("ZA"))), // 08:00
      )
      val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)

      target.validate(promoEntry, bookingDate, aValidHotelInfo) must_== (true, None)
    }

    "fail when booking time is neither after 'bookTimeFrom' nor before 'bookTimeTo'" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("ZA"))), // 09:00
      )
      val bookingDate = new DateTime(2017, 10, 11, 9, 0, 0)

      target.validate(promoEntry, bookingDate, aValidHotelInfo) must_== (false, None)
    }
  }

  "Promotion validation for AFF-1542 B user case invalid cid" should {
    implicit val request = aValidYplRequest
      .withBExperiment(YplExperiments.HOTEL_MVP_PROMOTIONS)
      .withClientInfo(YplClientInfo(cid = Some(152151251)))
      .build
    implicit val ctx = YplContext(request)
    "fail when promotion has required country" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("ZA"))),
      )
      val room = aValidRoomEntry.withAvailablePromotions(List(promoEntry)).build
      fencePromotions(promoEntry, BLTPromotionSettings()) must beEmpty
    }

    "pass when promo has valid country" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None)),
      )
      fencePromotions(promoEntry, BLTPromotionSettings()) must not(beEmpty)
    }

    "pass when promo and user are the same origin" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("TH"))),
      )
      val bookingDate = new DateTime(2017, 10, 11, 11, 0, 0) // 11:00

      target.validate(promoEntry, bookingDate, aValidHotelInfo) must_== (true, None)
    }
  }

  "Promotion validation for AFF-1542 B user case no cids in config" should {
    implicit val request = aValidYplRequest
      .withBExperiment(YplExperiments.HOTEL_MVP_PROMOTIONS)
      .withClientInfo(YplClientInfo(cid = Some(152151251)))
      .build
    implicit val ctx = YplContext(request)
    val setting = BLTPromotionSettings.apply(Set[Integer](), Set[String]("ZA"))
    "fail when promotion has required country" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("ZA"))),
      )

      fencePromotions(promoEntry, setting) must beEmpty
    }

    "pass when promo has valid country" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None)),
      )
      val bookingDate = new DateTime(2017, 10, 11, 11, 0, 0) // 11:00

      fencePromotions(promoEntry, BLTPromotionSettings()) must not(beEmpty)
    }

    "pass when promo and user are the same origin" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("TH"))),
      )
      val bookingDate = new DateTime(2017, 10, 11, 11, 0, 0) // 11:00

      target.validate(promoEntry, bookingDate, aValidHotelInfo) must_== (true, None)
    }
  }

  "Promotion validation for AFF-1542 B user case no cids and no country in config" should {
    implicit val request = aValidYplRequest
      .withBExperiment(YplExperiments.HOTEL_MVP_PROMOTIONS)
      .withClientInfo(YplClientInfo(cid = Some(152151251)))
      .build
    implicit val ctx = YplContext(request)
    val setting = BLTPromotionSettings.apply(Set[Integer](), Set[String]())
    "fail when promotion has required country" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("ZA"))),
      )

      val bookingDate = new DateTime(2017, 10, 11, 11, 0, 0) // 11:00

      fencePromotions(promoEntry, setting) must beEmpty
    }

    "pass when promo has valid country" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None)),
      )
      val bookingDate = new DateTime(2017, 10, 11, 11, 0, 0) // 11:00

      fencePromotions(promoEntry, BLTPromotionSettings()) must not(beEmpty)
    }

    "pass when promo and user are the same origin" in {
      val promoEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("TH"))),
      )
      fencePromotions(promoEntry, BLTPromotionSettings()) must not(beEmpty)
    }
  }

  "NoCC Promotion validation" should {
    "Fail if it doesn't apply every night" in {
      val bookingDate = DateTime.now()
      val checkIn = DateTime.now().plusDays(1).withTimeAtStartOfDay()
      val los = 3
      val request =
        aValidYplRequest.withBookingDate(bookingDate).withCheckIn(checkIn).withCheckout(checkIn.plusDays(los))
      implicit val ctx = YplContext(request)

      val noCCPromo = PromotionEntry(
        id = 1,
        typeId = PromotionConstant.NoCCRequiredTypeId,
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(Long.MinValue)),
        bookTo = Some(new DateTime(Long.MaxValue)),
        bookOn = "1111111",
        applyDates = (1 until request.lengthOfStay).map(i => request.checkIn.plusDays(i) -> 0).toMap,
        customerSegments = List(CustomerSegment(None, None)),
      )

      val noCCLastMinPromo = PromotionEntry(
        id = 2,
        typeId = PromotionConstant.NoCCLastMinuteTypeId,
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(Long.MinValue)),
        bookTo = Some(new DateTime(Long.MaxValue)),
        bookOn = "1111111",
        applyDates = (0 until (request.lengthOfStay - 1)).map(i => request.checkIn.plusDays(i) -> 0).toMap,
        customerSegments = List(CustomerSegment(None, None)),
      )

      val roomEntry = aValidRoomEntry.withAgodaAgency(true)

      target.validate(noCCPromo, bookingDate, aValidHotelInfo, Some(roomEntry), true)(request, ctx) must_== (false, None)
      target.validate(noCCLastMinPromo, bookingDate, aValidHotelInfo, Some(roomEntry), true)(request,
                                                                                             ctx) must_== (false, None)
    }

    "Pass if it applies every night" in {
      val bookingDate = DateTime.now()
      val checkIn = DateTime.now().plusDays(1).withTimeAtStartOfDay()
      val los = 3
      val request =
        aValidYplRequest.withBookingDate(bookingDate).withCheckIn(checkIn).withCheckout(checkIn.plusDays(los))
      implicit val ctx = YplContext(request)

      val noCCPromo = PromotionEntry(
        id = 1,
        typeId = PromotionConstant.NoCCRequiredTypeId,
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(Long.MinValue)),
        bookTo = Some(new DateTime(Long.MaxValue)),
        bookOn = "1111111",
        applyDates = (0 until request.lengthOfStay).map(i => request.checkIn.plusDays(i) -> 0).toMap,
        customerSegments = List(CustomerSegment(None, None)),
      )

      val noCCLastMinPromo = PromotionEntry(
        id = 2,
        typeId = PromotionConstant.NoCCLastMinuteTypeId,
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(Long.MinValue)),
        bookTo = Some(new DateTime(Long.MaxValue)),
        bookOn = "1111111",
        applyDates = (0 until request.lengthOfStay).map(i => request.checkIn.plusDays(i) -> 0).toMap,
        customerSegments = List(CustomerSegment(None, None)),
      )
      val roomEntry = aValidRoomEntry.withAgodaAgency(true)

      target.validate(noCCPromo, bookingDate, aValidHotelInfo, Some(roomEntry), true)(request, ctx) must_== (true, None)
      target.validate(noCCLastMinPromo, bookingDate, aValidHotelInfo, Some(roomEntry), true)(request,
                                                                                             ctx) must_== (true, None)
    }

    "Fail if allowNoCCPromo is false" in {
      val bookingDate = DateTime.now()
      val checkIn = DateTime.now().plusDays(1).withTimeAtStartOfDay()
      val los = 3
      val request =
        aValidYplRequest.withBookingDate(bookingDate).withCheckIn(checkIn).withCheckout(checkIn.plusDays(los))
      implicit val ctx = YplContext(request)

      val noCCPromo = PromotionEntry(
        id = 1,
        typeId = PromotionConstant.NoCCRequiredTypeId,
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(Long.MinValue)),
        bookTo = Some(new DateTime(Long.MaxValue)),
        bookOn = "1111111",
        applyDates = (0 until request.lengthOfStay).map(i => request.checkIn.plusDays(i) -> 0).toMap,
        customerSegments = List(CustomerSegment(None, None)),
      )

      val noCCLastMinPromo = PromotionEntry(
        id = 2,
        typeId = PromotionConstant.NoCCLastMinuteTypeId,
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(Long.MinValue)),
        bookTo = Some(new DateTime(Long.MaxValue)),
        bookOn = "1111111",
        applyDates = (0 until request.lengthOfStay).map(i => request.checkIn.plusDays(i) -> 0).toMap,
        customerSegments = List(CustomerSegment(None, None)),
      )
      val roomEntry = aValidRoomEntry.withAgodaAgency(true)

      target.validate(noCCPromo, bookingDate, aValidHotelInfo, Some(roomEntry), false)(request,
                                                                                       ctx) must_== (false, None)
      target.validate(noCCLastMinPromo, bookingDate, aValidHotelInfo, Some(roomEntry), false)(request,
                                                                                              ctx) must_== (false, None)
    }

    "Fail if not Agency room" in {
      val bookingDate = DateTime.now()
      val checkIn = DateTime.now().plusDays(1).withTimeAtStartOfDay()
      val los = 3
      val request =
        aValidYplRequest.withBookingDate(bookingDate).withCheckIn(checkIn).withCheckout(checkIn.plusDays(los))
      implicit val ctx = YplContext(request)

      val noCCPromo = PromotionEntry(
        id = 1000,
        typeId = PromotionConstant.NoCCRequiredTypeId,
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(Long.MinValue)),
        bookTo = Some(new DateTime(Long.MaxValue)),
        bookOn = "1111111",
        applyDates = (0 until request.lengthOfStay).map(i => request.checkIn.plusDays(i) -> 0).toMap,
        customerSegments = List(CustomerSegment(None, None)),
      )

      val noCCLastMinPromo = PromotionEntry(
        id = 2000,
        typeId = PromotionConstant.NoCCLastMinuteTypeId,
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(Long.MinValue)),
        bookTo = Some(new DateTime(Long.MaxValue)),
        bookOn = "1111111",
        applyDates = (0 until request.lengthOfStay).map(i => request.checkIn.plusDays(i) -> 0).toMap,
        customerSegments = List(CustomerSegment(None, None)),
      )
      val roomEntry = aValidRoomEntry.withAgodaAgency(false)

      target.validate(noCCPromo, bookingDate, aValidHotelInfo, Some(roomEntry), true)(request, ctx) must_== (false, None)
      target.validate(noCCLastMinPromo, bookingDate, aValidHotelInfo, Some(roomEntry), true)(request,
                                                                                             ctx) must_== (false, None)
    }
  }

  "Promotion with vip customer segment LOY-5840" should {
    implicit val request = aValidYplRequest
      .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.PLATINUM)))
      .withBExperiment("LOY-5840")
      .build
    implicit val ctx = YplContext(request)

    "Pass validation if there is only vip segment" in {
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.PLATINUM))),
      )

      val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)
      val customerOrigin = Some("TH")
      target.validate(promotionEntry, bookingDate, aValidHotelInfo, None) must_== (true, None)
    }

    "Pass validation if there is no vip segment" in {
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None, None)),
      )

      val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)
      val customerOrigin = Some("TH")
      target.validate(promotionEntry, bookingDate, aValidHotelInfo, None) must_== (true, None)
    }

    "Pass validation if there is customer segment" in {
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("TH"), None)),
      )

      val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)
      val customerOrigin = Some("TH")
      target.validate(promotionEntry, bookingDate, aValidHotelInfo, None) must_== (true, None)
    }

    "Pass validation with other customer segment" in {
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments =
          List(CustomerSegment(None, None, Some(VipLevelType.PLATINUM)), CustomerSegment(None, Some("TH"))),
      )

      val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)
      val customerOrigin = Some("TH")
      target.validate(promotionEntry, bookingDate, aValidHotelInfo, None) must_== (true, None)
    }

    "Pass validation if experiment is A" in {
      implicit val request = aValidYplRequest
        .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.PLATINUM)))
        .withAExperiment("LOY-5840")
        .build
      implicit val ctx = YplContext(request)
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("TH"))),
      )

      val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)
      val customerOrigin = Some("TH")
      target.validate(promotionEntry, bookingDate, aValidHotelInfo, None) must_== (true, None)
    }

    "Fail validation vip segment does not match" in {
      implicit val request = aValidYplRequest
        .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.PLATINUM)))
        .withAExperiment("LOY-5840")
        .build
      implicit val ctx = YplContext(request)
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.GOLD)), CustomerSegment(None, Some("TH"))),
      )

      val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)
      fencePromotions(promotionEntry, BLTPromotionSettings()) must beEmpty
    }

    "Fail validation vip segment does not match for non vip" in {
      implicit val request =
        aValidYplRequest.withClientInfo(YplClientInfo(vipLevel = None)).withBExperiment("LOY-5840").build
      implicit val ctx = YplContext(request)
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.GOLD)), CustomerSegment(None, Some("TH"))),
      )

      fencePromotions(promotionEntry, BLTPromotionSettings()) must beEmpty
    }

    "Fail validation if experiment is A and there is vip segment" in {
      implicit val request = aValidYplRequest
        .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.PLATINUM)))
        .withAExperiment("LOY-5840")
        .build
      implicit val ctx = YplContext(request)
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments =
          List(CustomerSegment(None, None, Some(VipLevelType.PLATINUM)), CustomerSegment(None, Some("TH"))),
      )

      val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)
      fencePromotions(promotionEntry, BLTPromotionSettings()) must beEmpty
    }

    "Pass validation if user is Diamond and segment is Platinum" in {
      implicit val request = aValidYplRequest
        .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.DIAMOND)))
        .withExperiments(
          List(
            YplExperiment("LOY-5840", 'B'),
          ))
        .build
      implicit val ctx = YplContext(request)

      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L),
        bookTimeTo = Some(300000000000L),
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.PLATINUM))),
      )

      val bookingDate = new DateTime(2017, 10, 11, 8, 0, 0)
      target.validate(promotionEntry, bookingDate, aValidHotelInfo, None) must_== (true, None)
    }

    "Fail validation if user is Diamond and segment is Gold" in {
      implicit val request = aValidYplRequest
        .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.DIAMOND)))
        .withExperiments(
          List(
            YplExperiment("LOY-5840", 'B'),
          ))
        .build
      implicit val ctx = YplContext(request)

      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
        bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
        bookTimeFrom = Some(360000000000L),
        bookTimeTo = Some(300000000000L),
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.GOLD))),
      )

      fencePromotions(promotionEntry, BLTPromotionSettings()) must beEmpty
    }
  }

  "VIPYCSPromotionEligible with vip customer segment LOY-5678" should {
    implicit val request = aValidYplRequest
      .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.PLATINUM)))
      .withBExperiment("LOY-5678")
      .build
    implicit val ctx = YplContext(request)

    "Room eligible for vip promotion if there is platinum vip segment" in {
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.PLATINUM))),
      )
      target.isVipYcsPromotionEligible(promotionEntry) must_== true
    }

    "Room not eligible for vip promotion  if there is no vip segment" in {
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        customerSegments = List(CustomerSegment(None, None, None)),
      )
      target.isVipYcsPromotionEligible(promotionEntry) must_== false
    }

    "Room not eligible for vip promotion if the user is not vip" in {
      implicit val request =
        aValidYplRequest.withClientInfo(YplClientInfo(vipLevel = None)).withBExperiment("LOY-5678").build
      implicit val ctx = YplContext(request)

      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.PLATINUM))),
      )
      target.isVipYcsPromotionEligible(promotionEntry) must_== false
    }

    "Room not eligible for vip promotion  if there is no vip segment for the user vip level" in {
      implicit val request = aValidYplRequest
        .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.SILVER)))
        .withBExperiment("LOY-5678")
        .build
      implicit val ctx = YplContext(request)
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.PLATINUM))),
      )
      target.isVipYcsPromotionEligible(promotionEntry) must_== false
    }

    "Room not eligible for vip promotion  if there is no platinum vip segment" in {
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.GOLD))),
      )
      target.isVipYcsPromotionEligible(promotionEntry) must_== false
    }

    "Room not eligible for vip promotion if experiment is A and there is vip segment" in {
      implicit val request = aValidYplRequest
        .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.PLATINUM)))
        .withAExperiment("LOY-5678")
        .build
      implicit val ctx = YplContext(request)
      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.PLATINUM))),
      )

      target.isVipYcsPromotionEligible(promotionEntry) must_== false
    }

    "Room not eligible for vip promotion if user is Diamond and segment is Gold" in {
      implicit val request = aValidYplRequest
        .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.DIAMOND)))
        .withExperiments(
          List(
            YplExperiment("LOY-5678", 'B'),
          ))
        .build
      implicit val ctx = YplContext(request)

      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.GOLD))),
      )
      target.isVipYcsPromotionEligible(promotionEntry) must_== false
    }

    "Room eligible for vip promotion if user is Diamond and segment is Platinum" in {
      implicit val request = aValidYplRequest
        .withClientInfo(YplClientInfo(vipLevel = Some(VipLevelType.DIAMOND)))
        .withExperiments(
          List(
            YplExperiment("LOY-5678", 'B'),
          ))
        .build
      implicit val ctx = YplContext(request)

      val promotionEntry = PromotionEntry(
        discountType = DiscountType.PercentDiscount,
        customerSegments = List(CustomerSegment(None, None, Some(VipLevelType.PLATINUM))),
      )
      target.isVipYcsPromotionEligible(promotionEntry) must_== true
    }
  }

  "promo validation for rate plan as promotion" should {
    implicit val request = aValidYplRequest.build
    implicit val ctx = YplContext(request)

    val promotion = PromotionEntry(
      discountType = DiscountType.PercentDiscount,
      bookFrom = Some(new DateTime(2017, 10, 10, 0, 0, 0)),
      bookTo = Some(new DateTime(2017, 10, 12, 0, 0, 0)),
      bookTimeFrom = Some(360000000000L), // 10:00
      bookTimeTo = Some(300000000000L), // 08:20
      bookOn = "1111111",
      customerSegments = List(CustomerSegment(None, None)),
    )
    val bookingDate = new DateTime(2017, 10, 11, 11, 0, 0) // 11:00
    val customerOrigin = Some("TH")

    "pass when promotion is not rate plan as promotion" in {
      val promoEntry = promotion.copy(isRatePlanAsPromotion = false)
      target.validate(promoEntry, bookingDate, aValidHotelInfo, None) must_== (true, None)
    }

    "pass when promotion is rate plan as promotion" in {
      val promoEntry = promotion.copy(isRatePlanAsPromotion = true)
      target.validate(promoEntry, bookingDate, aValidHotelInfo, None) must_== (true, None)
    }
  }

  def toPromotionIdsWithFences(room: YplRoomEntry) = (room.availablePromotions.map(_.id).sorted, room.fences)

  "promotion fencing " should {

    val request = aValidYplRequest.withClientInfo(aValidClientInfo.withLanguage(1).build).build

    implicit val ctx = YplContext(request)

    val promotionForTH =
      aValidPromotionEntry.withId(1).withCustomerSegments(List(CustomerSegment(None, Some("TH")))).build

    val promotionForHK =
      aValidPromotionEntry.withId(2).withCustomerSegments(List(CustomerSegment(None, Some("HK")))).build

    val promotionForAll = aValidPromotionEntry.withId(3).build

    val promotionForBLT =
      aValidPromotionEntry.withId(4).withCustomerSegments(List(CustomerSegment(Some(42), Some("TH")))).build

    val promotionForVipSilver = aValidPromotionEntry
      .withId(5)
      .withCustomerSegments(List(CustomerSegment(None, None, Some(VipLevelType.SILVER))))
      .build

    val promotionForVipGold = aValidPromotionEntry
      .withId(6)
      .withCustomerSegments(List(CustomerSegment(None, None, Some(VipLevelType.GOLD))))
      .build

    val promotionForBLTLangId =
      aValidPromotionEntry.withId(7).withCustomerSegments(List(CustomerSegment(Some(42), None))).build

    "fence promotions without promotions should result in no-op action" in {
      val room = aValidRoomEntry.withFences(Set(YplRateFence("TH", -1, 1), YplRateFence("HK", -1, 1))).build

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])
      result should_== List(room)
    }

    "fence promotions without fenced promotions should result in no-op action" in {
      val room = aValidRoomEntry
        .withAvailablePromotions(promotionForAll :: Nil)
        .withFences(Set(YplRateFence("TH", -1, 1), YplRateFence("HK", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])
      result.map(toPromotionIdsWithFences) should_== List(
        (List(3), Set(YplRateFence("TH", -1, 1), YplRateFence("HK", -1, 1))),
      )
    }

    "fence promotions by customer origin" in {
      val room = aValidRoomEntry
        .withAvailablePromotions(promotionForTH :: promotionForHK :: Nil)
        .withFences(Set(YplRateFence("TH", -1, 1), YplRateFence("HK", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (List(1), Set(YplRateFence("TH", -1, 1))),
          (List(2), Set(YplRateFence("HK", -1, 1))),
        ))
    }

    "fence promotions by customer origin and retain fenceless promotions" in {
      val room = aValidRoomEntry
        .withAvailablePromotions(promotionForTH :: promotionForHK :: promotionForAll :: Nil)
        .withFences(Set(YplRateFence("TH", -1, 1), YplRateFence("HK", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (List(3), Set(YplRateFence("TH", -1, 1), YplRateFence("HK", -1, 1))),
          (List(1), Set(YplRateFence("TH", -1, 1))),
          (List(2), Set(YplRateFence("HK", -1, 1))),
        ))
    }

    "fence promotions by customer origin and retain fenceless promotions (without fenced promotions for a specific fence)" in {
      val room = aValidRoomEntry
        .withAvailablePromotions(promotionForTH :: promotionForHK :: promotionForAll :: Nil)
        .withFences(Set(YplRateFence("TH", -1, 1), YplRateFence("SG", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (List(1), Set(YplRateFence("TH", -1, 1))),
          (List(3), Set(YplRateFence("TH", -1, 1), YplRateFence("SG", -1, 1))),
        ))
    }

    "fence promotions from BLT" in {
      val room = aValidRoomEntry
        .withAvailablePromotions(promotionForTH :: promotionForBLT :: Nil)
        .withFences(Set(YplRateFence("TH", -1, 1), YplRateFence("TH", 237, 1)))

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(
                                            allowCids = Set(237),
                                            requiredPromotionCountryCodes = Set("TH"),
                                          ),
                                          Map.empty[Int, PulseCampaignMetadata])(
        aValidYplContext.withExperimentContext(forceBExperimentContext(HOTEL_MVP_PROMOTIONS)))

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (List(1), Set(YplRateFence("TH", -1, 1), YplRateFence("TH", 237, 1))),
          (List(4), Set(YplRateFence("TH", 237, 1))),
        ))
    }

    "fence promotions from BLT (negative case: language only segment)" in {
      val room = aValidRoomEntry
        .withAvailablePromotions(promotionForTH :: promotionForBLTLangId :: Nil)
        .withFences(Set(YplRateFence("TH", -1, 1), YplRateFence("TH", 237, 1)))

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(
                                            allowCids = Set(237),
                                            requiredPromotionCountryCodes = Set("TH"),
                                          ),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (List(1), Set(YplRateFence("TH", -1, 1), YplRateFence("TH", 237, 1))),
        ))
    }

    "fence promotions (no promotions pass fence)" in {
      val room =
        aValidRoomEntry.withAvailablePromotions(promotionForHK :: Nil).withFences(Set(YplRateFence("TH", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (Nil, Set(YplRateFence("TH", -1, 1))),
        ))
    }

    "fence VIP level (LOY-5840=A)" in {
      val request = aValidYplRequest
        .withClientInfo(aValidClientInfo.withVipLevel(Some(VipLevelType.SILVER)))
        .withAExperiment(YplExperiments.CHECK_PROMOTION_VIP_CUSTOMER_SEGMENT)
        .build
      implicit val ctx = YplContext(request)

      val room = aValidRoomEntry
        .withAvailablePromotions(promotionForVipSilver :: promotionForVipGold :: Nil)
        .withFences(Set(YplRateFence("TH", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (Nil, Set(YplRateFence("TH", -1, 1))),
        ))
    }

    "fence VIP level (LOY-5840=B)" in {
      val request = aValidYplRequest
        .withClientInfo(aValidClientInfo.withVipLevel(Some(VipLevelType.SILVER)))
        .withBExperiment(YplExperiments.CHECK_PROMOTION_VIP_CUSTOMER_SEGMENT)
        .build
      implicit val ctx = YplContext(request)

      val room = aValidRoomEntry
        .withAvailablePromotions(promotionForVipSilver :: promotionForVipGold :: Nil)
        .withFences(Set(YplRateFence("TH", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (List(5), Set(YplRateFence("TH", -1, 1))),
        ))
    }

    "fence on hotel country basis and remove international promotion (PAPI-20392=B) " in {
      val request = aValidYplRequest
        .withClientInfo(aValidClientInfo.withVipLevel(Some(VipLevelType.UNDEFINED)))
        .withBExperiment(YplExperiments.BLOCK_INT_YCS_PROMOS_BY_HOTEL_COUNTRY)
        .build
      implicit val ctx = YplContext(request)

      val room =
        aValidRoomEntry.withAvailablePromotions(promotionForTH :: Nil).withFences(Set(YplRateFence("TH", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo.copy(countryCode = "HK"),
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (Nil, Set(YplRateFence("TH", -1, 1))),
        ))
    }

    "fence on hotel country basis and keep domestic promotion (PAPI-20392=B) " in {
      val request = aValidYplRequest
        .withClientInfo(aValidClientInfo.withVipLevel(Some(VipLevelType.UNDEFINED)))
        .withBExperiment(YplExperiments.BLOCK_INT_YCS_PROMOS_BY_HOTEL_COUNTRY)
        .build
      implicit val ctx = YplContext(request)

      val room =
        aValidRoomEntry.withAvailablePromotions(promotionForTH :: Nil).withFences(Set(YplRateFence("TH", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo.copy(countryCode = "TH"),
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (List(1), Set(YplRateFence("TH", -1, 1))),
        ))
    }

    "fence on customer origin basis (PAPI-20392=A) " in {
      val request = aValidYplRequest.withClientInfo(aValidClientInfo.withVipLevel(Some(VipLevelType.UNDEFINED))).build
      implicit val ctx = YplContext(request)

      val room =
        aValidRoomEntry.withAvailablePromotions(promotionForTH :: Nil).withFences(Set(YplRateFence("TH", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo.copy(countryCode = "HK"),
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (List(1), Set(YplRateFence("TH", -1, 1))),
        ))
    }

    val promotionForLanguageFilter = aValidPromotionEntry
      .withId(8)
      .withCustomerSegments(List(CustomerSegment(Some(1), Some("TH")), CustomerSegment(Some(2), Some("HK"))))
      .build

    "Language Fence on Customer Segment Filter" in {
      val request = aValidYplRequest.withClientInfo(aValidClientInfo.withLanguage(3).build).build

      implicit val ctx: YplContext = YplContext(request)

      val room = aValidRoomEntry
        .withAvailablePromotions(promotionForLanguageFilter :: Nil)
        .withFences(Set(YplRateFence("TH", -1, 1)))

      val result = target.fencePromotions(aValidHotelInfo,
                                          room :: Nil,
                                          BLTPromotionSettings(),
                                          Map.empty[Int, PulseCampaignMetadata])

      result.map(toPromotionIdsWithFences) must contain((List(8), Set(YplRateFence("TH", -1, 1))))
    }

    Table(
      ("testCaseName", "countryId", "variantOfOverrideCustomerSegmentToAll", "platform", "pulseCampaignIdBlacklist"),
      ("Turn off override customer segment experiment", Country.TH.id, 'A', None, Seq.empty[Int]),
      ("NorthAmerica", Country.US.id, 'B', None, Seq.empty[Int]),
      ("isAllMseTraffic true", Country.TH.id, 'B', Some(1008), Seq.empty[Int]), // 1008 is MSE platform
      ("Campaign In blacklist", Country.TH.id, 'B', None, Seq(999)),
      ("Disable all campaign", Country.TH.id, 'B', None, Seq(0)),
    ).forEvery { (testCaseName, countryId, variantOfOverrideCustomerSegmentToAll, platform, pulseCampaignIdBlacklist) =>
      s"When $testCaseName should fence promotions by customer origin without override" in {

        val pulsePromotionTypeId = 789
        val request = aValidYplRequest
          .withClientInfo(aValidClientInfo.withLanguage(3).withPlatformId(platform).build)
          .build
          .copy(experiments = List(YplExperiment(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_TO_BE_EVERY_CUSTOMER_SEGMENT,
                                                 variantOfOverrideCustomerSegmentToAll)))
        implicit val ctx: YplContext = YplContext(request)

        val rooms: List[YplRoomEntry] = aValidRoomEntry
          .withAvailablePromotions(
            promotionForTH.copy(id = 1) :: promotionForHK.copy(id = 2, typeId = pulsePromotionTypeId) :: Nil)
          .withFences(Set(YplRateFence("TH", -1, 1), YplRateFence("SG", -1, 1)))
          .build :: Nil

        val result = target.fencePromotions(
          aValidHotelInfo.copy(
            countryId = countryId,
            pulseCampaignIdBlacklist = pulseCampaignIdBlacklist,
          ),
          rooms,
          BLTPromotionSettings(),
          Set(pulsePromotionTypeId)
            .map(promotionTypeId =>
              promotionTypeId ->
                PulseCampaignMetadata(promotionTypeId, 123, 999, 111, 111))
            .toMap,
        )

        result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
          List(
            (List(1), Set(YplRateFence("TH", -1, 1))),
            (List(), Set(YplRateFence("SG", -1, 1))),
          ))
      }
    }

    "allow all customer segment when Turn on override customer segment experiment" in {

      val pulsePromotionTypeId = 789
      val request = aValidYplRequest
        .withClientInfo(aValidClientInfo.withLanguage(3).withPlatformId(None).build)
        .build
        .copy(experiments =
          List(YplExperiment(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_TO_BE_EVERY_CUSTOMER_SEGMENT, 'B')))
      implicit val ctx: YplContext = YplContext(request)

      val rooms: List[YplRoomEntry] = aValidRoomEntry
        .withAvailablePromotions(
          promotionForTH.copy(id = 1) // placebo
            :: promotionForHK.copy(id = 2, typeId = pulsePromotionTypeId)
            :: Nil)
        .withFences(
          Set(
            YplRateFence("TH", -1, 1),
            YplRateFence("SG", -1, 1),
          ))
        .build :: Nil

      val result = target.fencePromotions(
        aValidHotelInfo.copy(
          countryId = Country.TH.id,
          pulseCampaignIdBlacklist = Seq(888),
        ),
        rooms,
        BLTPromotionSettings(),
        Set(pulsePromotionTypeId)
          .map(promotionTypeId => promotionTypeId -> PulseCampaignMetadata(promotionTypeId, 123, 999, 111, 111))
          .toMap,
      )

      result.map(toPromotionIdsWithFences) should containTheSameElementsAs(
        List(
          (List(1), Set(YplRateFence("TH", -1, 1))),
          (List(2), Set(YplRateFence("TH", -1, 1), YplRateFence("SG", -1, 1))),
        ))
      result.map(_.availablePromotions.map(_.additionalDispatchReasons)) shouldEqual
        List(List(Set()), List(Set(CustomerSegmentOverriding)))
    }
  }
  "checkSupportCustomerSegment" should {
    val pulsePromotionTypeId = 789
    val pulsePromotionForHK = aValidPromotionEntry
      .withId(3)
      .withCustomerSegments(List(CustomerSegment(None, Some("HK"))))
      .build
      .copy(typeId = pulsePromotionTypeId)
    Table(
      ("testCaseName", "countryId", "variantOfOverrideCustomerSegmentToAll", "platform", "pulseCampaignIdBlacklist"),
      ("Turn off override customer segment experiment", Country.TH.id, 'A', None, Seq.empty[Int]),
      ("NorthAmerica", Country.US.id, 'B', None, Seq.empty[Int]),
      ("isAllMseTraffic true", Country.TH.id, 'B', Some(1008), Seq.empty[Int]), // 1008 is MSE platform
      ("Campaign In blacklist", Country.TH.id, 'B', None, Seq(999)),
      ("Disable all campaign", Country.TH.id, 'B', None, Seq(0)),
    ).forEvery { (testCaseName, countryId, variantOfOverrideCustomerSegmentToAll, platform, pulseCampaignIdBlacklist) =>
      s"When $testCaseName should fence promotions by customer origin without override" in {
        val request = aValidYplRequest
          .withClientInfo(aValidClientInfo.withLanguage(3).withPlatformId(platform).build)
          .build
          .copy(experiments = List(YplExperiment(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_TO_BE_EVERY_CUSTOMER_SEGMENT,
                                                 variantOfOverrideCustomerSegmentToAll)))
        implicit val ctx: YplContext = YplContext(request)

        val result = target.checkSupportCustomerSegment(
          pulsePromotionForHK,
          YplRateFence("SG", -1, 1),
          aValidHotelInfo.copy(
            countryId = countryId,
            pulseCampaignIdBlacklist = pulseCampaignIdBlacklist,
          ),
          Set(pulsePromotionTypeId)
            .map(promotionTypeId => promotionTypeId -> PulseCampaignMetadata(promotionTypeId, 123, 999, 111, 111))
            .toMap,
          false,
        )

        result shouldEqual (false, None)
      }
    }
    "allow all customer segment when Turn on override customer segment experiment" in {

      val request = aValidYplRequest
        .withClientInfo(aValidClientInfo.withLanguage(3).withPlatformId(None).build)
        .build
        .copy(experiments =
          List(YplExperiment(YplExperiments.ENABLE_DISPATCHING_OVERRIDE_TO_BE_EVERY_CUSTOMER_SEGMENT, 'B')))
      implicit val ctx: YplContext = YplContext(request)

      val result = target.checkSupportCustomerSegment(
        pulsePromotionForHK,
        YplRateFence("SG", -1, 1),
        aValidHotelInfo.copy(
          countryId = Country.TH.id,
          pulseCampaignIdBlacklist = Seq(888),
        ),
        Set(pulsePromotionTypeId)
          .map(promotionTypeId => promotionTypeId -> PulseCampaignMetadata(promotionTypeId, 123, 999, 111, 111))
          .toMap,
        false,
      )

      result shouldEqual (true, Some(AdditionalDispatchReason.CustomerSegmentOverriding))
    }

  }

  Table(
    ("testCaseDesc", "experimentVariant", "expectedResult"),
    ("should call cidToOriginMapper.getOriginByCid when experiment is enabled", 'B', (true, None)),
    ("should call originManager.getOriginByCidAndHotelCountry when experiment is disabled", 'A', (false, None)),
  ).forEvery { (testCaseDesc, experimentVariant, expectedResult) =>
    s"CID to origin mapping: $testCaseDesc" in {
      val mockOriginManager = mock[OriginManager]
      val mockCidToOriginMapper = mock[CidToOriginMapper]

      when(mockOriginManager.getOriginByCidAndHotelCountry(any(), any())).thenReturn(None)
      when(mockCidToOriginMapper.getOriginByCid(any())).thenReturn(Some("AU"))

      val testTarget = new {} with PromoValidation {
        override val originManager = mockOriginManager
        override val cidToOriginMapper = mockCidToOriginMapper
        override val promotionEntryValidation: PromotionEntryValidation = new {} with PromotionEntryValidationMock {}
      }

      val promotionForAU =
        aValidPromotionEntry.withId(4).withCustomerSegments(List(CustomerSegment(None, Some("AU")))).build

      val request = aValidYplRequest.withClientInfo(aValidClientInfo.withLanguage(3).withPlatformId(None).build).build

      implicit val ctx: YplContext = experimentVariant match {
        case 'B' =>
          YplContext(request).withExperimentContext(forceBExperimentContext(ABTest.ABO_CID_TO_ORIGIN_MAPPER)).build
        case 'A' =>
          YplContext(request).withExperimentContext(forceAExperimentContext(ABTest.ABO_CID_TO_ORIGIN_MAPPER)).build
      }

      val fence = YplRateFence("TH", 99999, 1)
      val hotelMeta = aValidHotelInfo.copy(
        countryId = Country.TH.id,
        countryCode = "TH",
        pulseCampaignIdBlacklist = Seq.empty,
      )

      val isAboCidToOriginMappingEnabled = experimentVariant == 'B'

      testTarget.checkSupportCustomerSegment(
        promotionForAU,
        fence,
        hotelMeta,
        Map.empty[Int, PulseCampaignMetadata],
        isAboCidToOriginMappingEnabled,
      )

      experimentVariant match {
        case 'B' =>
          verify(mockCidToOriginMapper, times(1)).getOriginByCid(99999)
          verify(mockOriginManager, times(0)).getOriginByCidAndHotelCountry(any(), any())
        case 'A' =>
          verify(mockOriginManager, times(1)).getOriginByCidAndHotelCountry(Some(99999), Some("TH"))
          verify(mockCidToOriginMapper, times(0)).getOriginByCid(any())
      }

      ok
    }
  }
}
