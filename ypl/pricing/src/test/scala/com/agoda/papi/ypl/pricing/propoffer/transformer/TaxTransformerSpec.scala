package com.agoda.papi.ypl.pricing.propoffer.transformer

import com.agoda.commons.models.pricing.ExchangeRate
import com.agoda.finance.tax.enums.TaxLevelCalculationType
import com.agoda.finance.tax.models.{TaxPrototypeInfo, TaxPrototypeLevel}
import com.agoda.papi.enums.room.{ChargeOption, GeoType}
import com.agoda.papi.pricing.pricecalculation.models.tax.Tax
import com.agoda.papi.pricing.pricecalculation.utils.TaxFiltersCacheInitialization
import com.agoda.papi.ypl.models.builders.TestDataExample
import com.agoda.papi.ypl.models.context.ExchangeRateContext
import com.agoda.papi.ypl.models.settings.CommonTaxSettings
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{
  Currency,
  HotelMeta,
  SupplierId,
  YPLTestContexts,
  YPLTestDataBuilders,
  YplContext,
  YplExperiments,
}
import com.agoda.papi.ypl.pricing.TaxUtil
import com.agoda.papi.ypl.pricing.propoffer.transformer.tax.{HMCTaxHolder, TaxType}
import com.agoda.papi.ypl.settings.MOHUGdsCommissionFeeSettings
import com.agoda.protobuf.masterhotelcontext.poc.{HmcTaxDetails, HmcTaxes}
import com.agoda.supply.calc.proto
import com.agoda.supply.calc.proto.{ApplyType, Identifiers, TaxApplyBreakdownType}
import com.agoda.utils.flow.ExperimentContext
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.BeforeAll

class TaxTransformerSpec
  extends SpecificationWithJUnit
    with Mockito
    with BeforeAll
    with TestDataExample
    with YPLTestDataBuilders
    with YPLTestContexts
    with HMCTestDataBuilder {

  override def beforeAll(): Unit = TaxFiltersCacheInitialization.initializeMockCache()

  val service: TaxTransformer = new TaxTransformer {
    override protected def isEnableTaxV2Exp(hasTaxV2: Boolean, hotel: HotelMeta, supplierId: SupplierId)(implicit
      ctx: YplContext): Boolean = TaxUtil.isEnableTaxV2Exp(hasTaxV2, hotel, supplierId)

    override protected def calculateLevelByCurrency(hotelCurrencyCode: Currency,
                                                    countryCurrencyCode: Currency,
                                                    taxLevelInCountryCurrency: List[TaxPrototypeLevel],
                                                    isAmount: Boolean)(implicit
      ctx: YplContext): List[TaxPrototypeLevel] =
      TaxUtil.calculateLevelByCurrency(hotelCurrencyCode, countryCurrencyCode, taxLevelInCountryCurrency, isAmount)

    override protected def convertCurrency(amount: Double, from: Currency, to: Currency)(implicit
      ctx: YplContext): Double = TaxUtil.convertCurrency(amount, from, to)
  }

  val mohuGdsCommissionFeeSettings: MOHUGdsCommissionFeeSettings = MOHUGdsCommissionFeeSettings.setting

  val US_NEW_TAX_APPLY_TYPE: String = US_TAX_V2_EXPERIMENT

  "TaxTransformer" should {

    "buildDailyTaxEntry from PropertyOffer" should {

      "build tax properly for same all days taxes with los > 1" in {
        val checkIn = aValidCheckIn
        val allTaxes = Map(
          1 -> aValidPropOfferTax
            .withTaxId(1)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(1),
          2 -> aValidPropOfferTax
            .withTaxId(2)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(2),
        )
        val taxProtoTypes = Map(
          1 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(1),
          2 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(2),
        )
        val channelRate = aValidPropOfferChannelRate.withTaxPerDay(Map(-1 -> Identifiers(Seq(1, 2))))
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0)
        val propOffer = aValidPropertyOffer
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(1))
          .withTaxes(allTaxes)
          .withTaxPrototypeLevels(taxProtoTypes)
          .clearTaxesV2
          .clearTaxPrototypeLevelsV2

        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
        )(aValidYplContext)
        result must_== Map(
          checkIn -> Map(
            (1, 1) -> Tax(
              1,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              1,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (1, 1) -> Tax(
              1,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              1,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
        )
      }

      "build tax properly for different taxes on each day" in {
        val checkIn = aValidCheckIn
        val allTaxes = Map(
          1 -> aValidPropOfferTax
            .withTaxId(1)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(1),
          2 -> aValidPropOfferTax
            .withTaxId(2)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(2),
          3 -> aValidPropOfferTax
            .withTaxId(3)
            .withValue(5)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(true)
            .withApplyTo("PAPB")
            .withTaxPrototypeId(0),
        )
        val taxProtoTypes = Map(
          1 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(1),
          2 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(2),
        )
        val channelRate =
          aValidPropOfferChannelRate.withTaxPerDay(Map(0 -> Identifiers(Seq(1, 2)), 1 -> Identifiers(Seq(3))))
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0)
        val propOffer = aValidPropertyOffer
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(1))
          .withTaxes(allTaxes)
          .withTaxPrototypeLevels(taxProtoTypes)
          .clearTaxesV2
          .clearTaxPrototypeLevelsV2

        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
        )(aValidYplContext)
        result must_== Map(
          checkIn -> Map(
            (1, 1) -> Tax(
              1,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              1,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (3, 0) -> Tax(3, "PAPB", true, true, true, 5.0, ChargeOption.Mandatory, 0, None, None, applyOn = Some(0)),
          ),
        )
      }

      "build tax properly for same all days taxesV2 with los > 1" in {
        val checkIn = aValidCheckIn
        val allTaxes = Map(
          1 -> aValidPropOfferTax
            .withTaxId(1)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(1),
          2 -> aValidPropOfferTax
            .withTaxId(2)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(2),
        )
        val allTaxesV2 = Map(
          3 -> aValidPropOfferTaxV2
            .withTaxId(3)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(3),
          4 -> aValidPropOfferTaxV2
            .withTaxId(4)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(4),
        )
        val taxProtoTypes = Map(
          1 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(1),
          2 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(2),
        )
        val taxPrototypesV2 = Map(
          3 -> aValidPropOfferTaxProtoTypeV2.withTaxProtoTypeId(3),
          4 -> aValidPropOfferTaxProtoTypeV2.withTaxProtoTypeId(4),
        )
        val channelRate = aValidPropOfferChannelRate
          .withTaxPerDay(Map(-1 -> Identifiers(Seq(1, 2))))
          .withTaxPerDayV2(Map(-1 -> Identifiers(Seq(3, 4))))
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0, stateId = Some(1))
        val propOffer = aValidPropertyOffer
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(1))
          .withTaxes(allTaxes)
          .withTaxPrototypeLevels(taxProtoTypes)
          .withTaxesV2(allTaxesV2)
          .withTaxPrototypeLevelsV2(taxPrototypesV2)
        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
        )(
          aValidYplContext
            .withExperimentContext(forceBExperimentContext(US_NEW_TAX_APPLY_TYPE))
            .withRequest(aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings))),
        )
        result must_== Map(
          checkIn -> Map(
            (3, 3) -> Tax(
              3,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              3,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
            (4, 4) -> Tax(
              4,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              4,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (3, 3) -> Tax(
              3,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              3,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
            (4, 4) -> Tax(
              4,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              4,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
        )
      }

      "build tax properly with taxV1 if taxesV2 is undefined when B user" in {
        val checkIn = aValidCheckIn
        val allTaxes = Map(
          1 -> aValidPropOfferTax
            .withTaxId(1)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(1),
          2 -> aValidPropOfferTax
            .withTaxId(2)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(2),
          3 -> aValidPropOfferTax
            .withTaxId(3)
            .withValue(5)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(true)
            .withApplyTo("PAPB")
            .withTaxPrototypeId(0),
        )
        val taxProtoTypes = Map(
          1 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(1),
          2 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(2),
        )
        val channelRate = aValidPropOfferChannelRate
          .withTaxPerDay(Map(0 -> Identifiers(Seq(1, 2)), 1 -> Identifiers(Seq(3))))
          .withTaxPerDayV2(Map(-1 -> Identifiers(Seq(4, 5))))
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 114L, cityId = 0, stateId = Some(1))
        val propOffer = aValidPropertyOffer
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(1))
          .withTaxes(allTaxes)
          .withTaxPrototypeLevels(taxProtoTypes)
          .clearTaxesV2
          .clearTaxPrototypeLevelsV2

        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
        )(
          aValidYplContext
            .copy(
              request = aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings)),
            )
            .withExperimentContext(forceBExperimentContext(US_NEW_TAX_APPLY_TYPE)),
        )
        result must_== Map(
          checkIn -> Map(
            (1, 1) -> Tax(
              1,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              1,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (3, 0) -> Tax(3, "PAPB", true, true, true, 5.0, ChargeOption.Mandatory, 0, None, None, applyOn = Some(0)),
          ),
        )
      }

      "build tax properly for different taxesV2 on each day" in {
        val checkIn = aValidCheckIn
        val allTaxes = Map(
          1 -> aValidPropOfferTax
            .withTaxId(1)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(1),
          2 -> aValidPropOfferTax
            .withTaxId(2)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(2),
          3 -> aValidPropOfferTax
            .withTaxId(3)
            .withValue(5)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(true)
            .withApplyTo("PAPB")
            .withTaxPrototypeId(0),
        )
        val allTaxesV2 = Map(
          4 -> aValidPropOfferTaxV2
            .withTaxId(4)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(4),
          5 -> aValidPropOfferTaxV2
            .withTaxId(5)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(5),
          6 -> aValidPropOfferTaxV2
            .withTaxId(6)
            .withValue(5)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(true)
            .withApplyTo("PAPB")
            .withTaxPrototypeId(0),
        )
        val taxProtoTypes = Map(
          1 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(1),
          2 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(2),
        )
        val taxPrototypesV2 = Map(
          4 -> aValidPropOfferTaxProtoTypeV2.withTaxProtoTypeId(4),
          5 -> aValidPropOfferTaxProtoTypeV2.withTaxProtoTypeId(5),
        )
        val channelRate = aValidPropOfferChannelRate
          .withTaxPerDay(Map(0 -> Identifiers(Seq(1, 2)), 1 -> Identifiers(Seq(3))))
          .withTaxPerDayV2(Map(0 -> Identifiers(Seq(4, 5)), 1 -> Identifiers(Seq(6))))
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0, stateId = Some(1))
        val propOffer = aValidPropertyOffer
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(1))
          .withTaxes(allTaxes)
          .withTaxPrototypeLevels(taxProtoTypes)
          .withTaxesV2(allTaxesV2)
          .withTaxPrototypeLevelsV2(taxPrototypesV2)

        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
        )(
          aValidYplContext
            .withExperimentContext(forceBExperimentContext(US_NEW_TAX_APPLY_TYPE))
            .withRequest(aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings))),
        )
        result must_== Map(
          checkIn -> Map(
            (4, 4) -> Tax(
              4,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              4,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
            (5, 5) -> Tax(
              5,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              5,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (6, 0) -> Tax(
              6,
              "PAPB",
              true,
              true,
              true,
              5.0,
              ChargeOption.Mandatory,
              0,
              None,
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
        )
      }

      "build tax properly for different taxesV2 on each day though -1 attached with taxPerDayV2" in {
        val checkIn = aValidCheckIn
        val allTaxes = Map(
          1 -> aValidPropOfferTax
            .withTaxId(1)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(1),
          2 -> aValidPropOfferTax
            .withTaxId(2)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(2),
          3 -> aValidPropOfferTax
            .withTaxId(3)
            .withValue(5)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(true)
            .withApplyTo("PAPB")
            .withTaxPrototypeId(0),
        )
        val allTaxesV2 = Map(
          4 -> aValidPropOfferTaxV2
            .withTaxId(4)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(4),
          5 -> aValidPropOfferTaxV2
            .withTaxId(5)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(5),
          6 -> aValidPropOfferTaxV2
            .withTaxId(6)
            .withValue(5)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(true)
            .withApplyTo("PAPB")
            .withTaxPrototypeId(0),
        )
        val taxProtoTypes = Map(
          1 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(1),
          2 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(2),
        )
        val taxPrototypesV2 = Map(
          4 -> aValidPropOfferTaxProtoTypeV2.withTaxProtoTypeId(4),
          5 -> aValidPropOfferTaxProtoTypeV2.withTaxProtoTypeId(5),
        )
        val channelRate = aValidPropOfferChannelRate
          .withTaxPerDay(Map(0 -> Identifiers(Seq(1, 2)), 1 -> Identifiers(Seq(3))))
          .withTaxPerDayV2(Map(-1 -> Identifiers(Seq(1, 2)), 0 -> Identifiers(Seq(4, 5)), 1 -> Identifiers(Seq(6))))
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0, stateId = Some(1))
        val propOffer = aValidPropertyOffer
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(1))
          .withTaxes(allTaxes)
          .withTaxPrototypeLevels(taxProtoTypes)
          .withTaxesV2(allTaxesV2)
          .withTaxPrototypeLevelsV2(taxPrototypesV2)

        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
        )(
          aValidYplContext
            .withExperimentContext(forceBExperimentContext(US_NEW_TAX_APPLY_TYPE))
            .withRequest(aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings))),
        )
        result must_== Map(
          checkIn -> Map(
            (4, 4) -> Tax(
              4,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              4,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
            (5, 5) -> Tax(
              5,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              5,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (6, 0) -> Tax(
              6,
              "PAPB",
              true,
              true,
              true,
              5.0,
              ChargeOption.Mandatory,
              0,
              None,
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
        )
      }

      "return v2 tax for state currently in migration" in {
        val checkIn = aValidCheckIn
        val allTaxes = Map(
          1 -> aValidPropOfferTax
            .withTaxId(1)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(1),
          2 -> aValidPropOfferTax
            .withTaxId(2)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(2),
        )
        val allTaxesV2 = Map(
          3 -> aValidPropOfferTaxV2
            .withTaxId(3)
            .withValue(10)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(3),
          4 -> aValidPropOfferTaxV2
            .withTaxId(4)
            .withValue(15)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(4),
        )
        val taxProtoTypes = Map(
          1 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(1),
          2 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(2),
        )
        val taxPrototypesV2 = Map(
          3 -> aValidPropOfferTaxProtoTypeV2.withTaxProtoTypeId(3),
          4 -> aValidPropOfferTaxProtoTypeV2.withTaxProtoTypeId(4),
        )
        val channelRate = aValidPropOfferChannelRate
          .withTaxPerDay(Map(-1 -> Identifiers(Seq(1, 2))))
          .withTaxPerDayV2(Map(-1 -> Identifiers(Seq(3, 4))))
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 114L, cityId = 0, stateId = Some(2))
        val propOffer = aValidPropertyOffer
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(1))
          .withTaxes(allTaxes)
          .withTaxPrototypeLevels(taxProtoTypes)
          .withTaxesV2(allTaxesV2)
          .withTaxPrototypeLevelsV2(taxPrototypesV2)

        val usTaxV2Settings = CommonTaxSettings(
          usExperiment2StateMapping = Map("VYG-323" -> Set(2)),
          usState2ExperimentMapping = Map(2 -> "VYG-323"),
        )
        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
        )(
          aValidYplContext
            .withExperimentContext(forceBExperimentContext(US_NEW_TAX_APPLY_TYPE))
            .withRequest(aValidYplRequest.copy(commonTaxSettingsOpt = Some(usTaxV2Settings))),
        )
        result must_== Map(
          checkIn -> Map(
            (3, 3) -> Tax(
              3,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              3,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
            (4, 4) -> Tax(
              4,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              4,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (3, 3) -> Tax(
              3,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              3,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
            (4, 4) -> Tax(
              4,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              4,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
        )
      }

//      "return v2 tax for state that completed migration (with experiment disabled)" in {
//        val checkIn = aValidCheckIn
//        val allTaxes = Map(
//          1 -> aValidPropOfferTax.withTaxId(1).withValue(10).withApplyType(ApplyType.Mandatory).withIsAmount(true).withIsFee(false).withApplyTo("PRPN").withTaxPrototypeId(1),
//          2 -> aValidPropOfferTax.withTaxId(2).withValue(15).withApplyType(ApplyType.Optional).withIsAmount(false).withIsFee(false).withApplyTo("PB").withTaxPrototypeId(2)
//        )
//        val allTaxesV2 = Map(
//          3 -> aValidPropOfferTaxV2.withTaxId(3).withValue(10).withApplyType(ApplyType.Mandatory).withIsAmount(true).withIsFee(false).withApplyTo("PRPN").withTaxPrototypeId(3),
//          4 -> aValidPropOfferTaxV2.withTaxId(4).withValue(15).withApplyType(ApplyType.Optional).withIsAmount(false).withIsFee(false).withApplyTo("PB").withTaxPrototypeId(4)
//        )
//        val taxProtoTypes = Map(1 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(1), 2 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(2))
//        val taxPrototypesV2 = Map(3 -> aValidPropOfferTaxProtoTypeV2.withTaxProtoTypeId(3), 4 -> aValidPropOfferTaxProtoTypeV2.withTaxProtoTypeId(4))
//        val channelRate = aValidPropOfferChannelRate.withTaxPerDay(Map(-1 -> Identifiers(Seq(1, 2)))).withTaxPerDayV2(Map(-1 -> Identifiers(Seq(3, 4))))
//        val hotelMeta = HotelMeta(hotelId = 1, countryId = 114L, cityId = 0, stateId = Some(1))
//        val propOffer = aValidPropertyOffer
//          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(1))
//          .withTaxes(allTaxes)
//          .withTaxPrototypeLevels(taxProtoTypes)
//          .withTaxesV2(allTaxesV2)
//          .withTaxPrototypeLevelsV2(taxPrototypesV2)
//        val usTaxV2Settings = USTaxV2Settings(statesInMigration = Set(2), statesCompletedMigration = Set(1))
//        val result = service.buildDailyTaxEntries(checkIn, 2, hotelMeta, propOffer, aValidPropOfferRoomRateCategory, channelRate, Some("USD"), Some("USD"), mohuGdsCommissionFeeSettings)(
//          aValidYplContext
//            .withRequest(aValidYplRequest.copy(usTaxV2Settings = Some(usTaxV2Settings)))
//        )
//        result must_== Map(
//          checkIn -> Map(
//            (3, 3) -> Tax(3, "PRPN", true, false, true, 10.0, ChargeOption.Mandatory, 3, Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
//              None, None, None, Some(TaxLevelCalculationType.Unknown), Some(ValueMethodType.Unknown), Some(ValueCalculationMethodType.Unknown), None, Some(GeoType.Unknown)),
//            (4, 4) -> Tax(4, "PB", false, false, true, 15.0, ChargeOption.Optional, 4, Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
//              None, None, None, Some(TaxLevelCalculationType.Unknown), Some(ValueMethodType.Unknown), Some(ValueCalculationMethodType.Unknown), None, Some(GeoType.Unknown)),
//          ),
//          checkIn.plusDays(1) -> Map(
//            (3, 3) -> Tax(3, "PRPN", true, false, true, 10.0, ChargeOption.Mandatory, 3, Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
//              None, None, None, Some(TaxLevelCalculationType.Unknown), Some(ValueMethodType.Unknown), Some(ValueCalculationMethodType.Unknown), None, Some(GeoType.Unknown)),
//            (4, 4) -> Tax(4, "PB", false, false, true, 15.0, ChargeOption.Optional, 4, Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
//              None, None, None, Some(TaxLevelCalculationType.Unknown), Some(ValueMethodType.Unknown), Some(ValueCalculationMethodType.Unknown), None, Some(GeoType.Unknown)),
//          )
//        )
//      }
    }

    "buildDailyTaxEntry from HMC" should {

      "build tax properly for same all days taxes with los > 1" in {
        val checkIn = aValidCheckIn
        val channelRate = aValidPropOfferChannelRate
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0)
        val propOffer = aValidPropertyOffer.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.YCS))

        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
          aValidHMCTaxHolder,
        )(
          aValidYplContext
            .withExperimentContext(
              forceBExperimentContext(YplExperiments.USE_TAXES_FROM_HMC, YplExperiments.ENABLE_HMC_FETCH))
            .withRequest(aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings)))
            .withSupplierSetting(Map(DMC.YCS -> aValidYplSupplierSetting.copy(isThirdParty = false),
                                     DMC.Meituan -> aValidYplSupplierSetting.copy(isThirdParty = true))),
        )
        result must_== Map(
          checkIn -> Map(
            (1, 1) -> Tax(
              1,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              1,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (1, 1) -> Tax(
              1,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              1,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
        )
      }

      "build tax properly for different taxes on each day" in {
        val checkIn = aValidCheckIn
        val checkInDate = java.time.LocalDate.of(checkIn.getYear, checkIn.getMonthOfYear, checkIn.getDayOfMonth)
        val los = 2
        val hmcTaxHolder = HMCTaxHolder.default.copy(
          hotelTaxType = TaxType.ComprehensiveTaxHotelLevel,
          taxDetails = HmcTaxDetails(
            hmcTaxesIdentiferMapping = Map(
              1 -> aValidHotelTax1.copy(startDate = checkInDate, endDate = checkInDate),
              2 -> aValidHotelTax2.copy(startDate = checkInDate, endDate = checkInDate),
              3 -> aValidHotelTax3.copy(startDate = checkInDate.plusDays(1), endDate = checkInDate.plusDays(2)),
            ),
            ycsTaxes = Map(
              "0-0" -> HmcTaxes(hmcTaxV1Identifiers = Seq(1, 2, 3)),
            ),
          ),
        )
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0)
        val propOffer = aValidPropertyOffer.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.YCS))

        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          aValidPropOfferChannelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
          hmcTaxHolder,
        )(
          aValidYplContext
            .withExperimentContext(
              forceBExperimentContext(YplExperiments.USE_TAXES_FROM_HMC, YplExperiments.ENABLE_HMC_FETCH))
            .withRequest(aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings)))
            .withSupplierSetting(Map(DMC.YCS -> aValidYplSupplierSetting.copy(isThirdParty = false),
                                     DMC.Meituan -> aValidYplSupplierSetting.copy(isThirdParty = true)))
            .overrideYplRequest(aValidYplRequest.copy(checkIn = checkIn, checkOut = checkIn.plusDays(los))),
        )
        result must_== Map(
          checkIn -> Map(
            (1, 1) -> Tax(
              1,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              1,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (3, 0) -> Tax(3, "PAPB", true, true, true, 5.0, ChargeOption.Mandatory, 0, None, None, applyOn = Some(0)),
          ),
        )
      }

      "build tax properly for same all days taxesV2 with los > 1" in {
        val checkIn = aValidCheckIn
        val los = 2
        val allTaxesV2 = Map(0 -> aValidPropOfferTaxV2)
        val channelRate = aValidPropOfferChannelRate.withTaxPerDayV2(Map(0 -> Identifiers(Nil)))
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0, stateId = Some(1))
        val propOffer =
          aValidPropertyOffer.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.YCS)).withTaxesV2(allTaxesV2)

        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
          aValidHMCTaxHolder,
        )(
          aValidYplContext
            .withExperimentContext(
              forceBExperimentContext(YplExperiments.USE_TAXES_FROM_HMC,
                                      YplExperiments.ENABLE_HMC_FETCH,
                                      US_TAX_V2_EXPERIMENT))
            .withSupplierSetting(Map(DMC.YCS -> aValidYplSupplierSetting.copy(isThirdParty = false),
                                     DMC.Meituan -> aValidYplSupplierSetting.copy(isThirdParty = true)))
            .overrideYplRequest(aValidYplRequest
              .copy(checkIn = checkIn, checkOut = checkIn.plusDays(los), commonTaxSettingsOpt = Some(commonTaxSettings))),
        )
        result must_== Map(
          checkIn -> Map(
            (3, 3) -> Tax(
              3,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              3,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(com.agoda.papi.enums.room.GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
            (4, 4) -> Tax(
              4,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              4,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(com.agoda.papi.enums.room.GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (3, 3) -> Tax(
              3,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              3,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(com.agoda.papi.enums.room.GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
            (4, 4) -> Tax(
              4,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              4,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(com.agoda.papi.enums.room.GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
        )
      }

      "build tax properly for different taxesV2 on each day" in {
        val checkIn = aValidCheckIn
        val checkInDate = java.time.LocalDate.of(checkIn.getYear, checkIn.getMonthOfYear, checkIn.getDayOfMonth)
        val los = 2
        val hmcTaxHolder = HMCTaxHolder.default.copy(
          hotelTaxType = TaxType.ComprehensiveTaxHotelLevel,
          taxDetails = HmcTaxDetails(
            hmcTaxesIdentiferMapping = Map(
              1 -> aValidHotelTax1.copy(taxId = 4, taxPrototypeId = 4, startDate = checkInDate, endDate = checkInDate),
              2 -> aValidHotelTax2.copy(taxId = 5, taxPrototypeId = 5, startDate = checkInDate, endDate = checkInDate),
              3 -> aValidHotelTax3.copy(taxId = 6, startDate = checkInDate.plusDays(1), endDate = checkInDate.plusDays(2)),
            ),
            ycsTaxes = Map(
              "0-0" -> HmcTaxes(hmcTaxV2Identifiers = Seq(1, 2, 3)),
            ),
          ),
        )
        val allTaxesV2 = Map(0 -> aValidPropOfferTaxV2)
        val channelRate = aValidPropOfferChannelRate.withTaxPerDayV2(Map(-1 -> Identifiers(Nil)))
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0, stateId = Some(1))
        val propOffer =
          aValidPropertyOffer.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.YCS)).withTaxesV2(allTaxesV2)

        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
          hmcTaxHolder,
        )(
          aValidYplContext
            .withExperimentContext(
              forceBExperimentContext(YplExperiments.USE_TAXES_FROM_HMC,
                                      YplExperiments.ENABLE_HMC_FETCH,
                                      US_TAX_V2_EXPERIMENT))
            .withRequest(aValidYplRequest
              .copy(commonTaxSettingsOpt = Some(commonTaxSettings), checkIn = checkIn, checkOut = checkIn.plusDays(los)))
            .withSupplierSetting(Map(DMC.YCS -> aValidYplSupplierSetting.copy(isThirdParty = false),
                                     DMC.Meituan -> aValidYplSupplierSetting.copy(isThirdParty = true))),
        )
        result must_== Map(
          checkIn -> Map(
            (4, 4) -> Tax(
              4,
              "PRPN",
              true,
              false,
              true,
              10.0,
              ChargeOption.Mandatory,
              4,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(com.agoda.papi.enums.room.GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
            (5, 5) -> Tax(
              5,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              5,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(com.agoda.papi.enums.room.GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (6, 0) -> Tax(
              6,
              "PAPB",
              true,
              true,
              true,
              5.0,
              ChargeOption.Mandatory,
              0,
              None,
              None,
              None,
              None,
              Some(TaxLevelCalculationType.Unknown),
              Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
              Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
              None,
              Some(com.agoda.papi.enums.room.GeoType.Unknown),
              applyOn = Some(0),
              applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
            ),
          ),
        )
      }

      "not use HMC tax when supplier is BCOM or when supplier is ThirdParty but USE_TAXES_FROM_HMC_SUPERAGG is A" in {
        val checkIn = aValidCheckIn
        val allTaxes = Map(
          1 -> aValidPropOfferTax
            .withTaxId(1)
            .withValue(100)
            .withApplyType(ApplyType.Mandatory)
            .withIsAmount(true)
            .withIsFee(false)
            .withApplyTo("PRPN")
            .withTaxPrototypeId(1),
          2 -> aValidPropOfferTax
            .withTaxId(2)
            .withValue(150)
            .withApplyType(ApplyType.Optional)
            .withIsAmount(false)
            .withIsFee(false)
            .withApplyTo("PB")
            .withTaxPrototypeId(2),
        )
        val taxProtoTypes = Map(
          1 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(1),
          2 -> aValidPropOfferTaxProtoType.withTaxProtoTypeId(2),
        )
        val channelRate = aValidPropOfferChannelRate.withTaxPerDay(Map(-1 -> Identifiers(Seq(1, 2))))
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0)
        val propOffer = aValidPropertyOffer
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.BCOM))
          .withTaxes(allTaxes)
          .withTaxPrototypeLevels(taxProtoTypes)
        val yplCtx = aValidYplContext
          .withExperimentContext(
            forceBExperimentContext(YplExperiments.USE_TAXES_FROM_HMC, YplExperiments.ENABLE_HMC_FETCH))
          .withRequest(aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings)))
          .withSupplierSetting(Map(DMC.YCS -> aValidYplSupplierSetting.copy(isThirdParty = false),
                                   DMC.Meituan -> aValidYplSupplierSetting.copy(isThirdParty = true)))

        val resultBCOM = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
          aValidHMCTaxHolder,
        )(yplCtx)
        resultBCOM must_== Map(
          checkIn -> Map(
            (1, 1) -> Tax(
              1,
              "PRPN",
              true,
              false,
              true,
              100.0,
              ChargeOption.Mandatory,
              1,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              150.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (1, 1) -> Tax(
              1,
              "PRPN",
              true,
              false,
              true,
              100.0,
              ChargeOption.Mandatory,
              1,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              150.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
        )

        val resultSuperAgg = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer.withSupplyInfo(
            aValidPropOfferSupplyInfo.withSupplierId(DMC.Meituan).withSupplySource(proto.SupplySourceType.Pull)),
          aValidPropOfferRoomRateCategory,
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
          aValidHMCTaxHolder,
        )(yplCtx)
        resultSuperAgg must_== resultBCOM
      }

      "build HMC tax properly for SuperAgg and USE_TAXES_FROM_HMC_SUPERAGG is B for same all days taxes with los > 1" in {
        val checkIn = aValidCheckIn
        val channelRate = aValidPropOfferChannelRate
        val hotelMeta = HotelMeta(hotelId = 1, countryId = 106L, cityId = 0)
        val propOffer = aValidPropertyOffer.withSupplyInfo(
          aValidPropOfferSupplyInfo.withSupplierId(DMC.Meituan).withSupplySource(proto.SupplySourceType.Pull))
        val yplCtx = aValidYplContext
          .withExperimentContext(
            forceBExperimentContext(YplExperiments.USE_TAXES_FROM_HMC,
                                    YplExperiments.ENABLE_HMC_FETCH,
                                    YplExperiments.USE_TAXES_FROM_HMC_SUPERAGG))
          .withRequest(aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings)))
          .withSupplierSetting(Map(DMC.YCS -> aValidYplSupplierSetting.copy(isThirdParty = false),
                                   DMC.Meituan -> aValidYplSupplierSetting.copy(isThirdParty = true)))

        val result = service.buildDailyTaxEntry(
          checkIn,
          2,
          hotelMeta,
          propOffer,
          aValidPropOfferRoomRateCategory.copy(roomTypeId = 1000L),
          channelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
          aValidHMCTaxHolder,
        )(yplCtx)
        result must_== Map(
          checkIn -> Map(
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
          checkIn.plusDays(1) -> Map(
            (2, 2) -> Tax(
              2,
              "PB",
              false,
              false,
              true,
              15.0,
              ChargeOption.Optional,
              2,
              Some(TaxPrototypeInfo(List(TaxPrototypeLevel(1, 2.4, 2.7, 32.4)))),
              None,
              applyOn = Some(0),
            ),
          ),
        )
      }

      "skip grouping for YCS/JTBWL when state = 46 and country = 181" in {
        implicit val yplCtx: YplContext = aValidYplContext
          .withExperimentContext(
            forceBExperimentContext(YplExperiments.USE_TAXES_FROM_HMC, YplExperiments.ENABLE_HMC_FETCH))
          .withSupplierSetting(Map(DMC.YCS -> aValidYplSupplierSetting.copy(isThirdParty = false)))

        val checkInDateJava =
          java.time.LocalDate.of(aValidCheckIn.getYear, aValidCheckIn.getMonthOfYear, aValidCheckIn.getDayOfMonth)

        // two taxes with the same taxId but different values
        val hmcTaxHi = aValidHotelTax1.copy(taxId = 101,
                                            value = 20.0,
                                            startDate = checkInDateJava,
                                            endDate = checkInDateJava,
                                            taxPrototypeId = 1)
        val hmcTaxLo = aValidHotelTax1.copy(taxId = 101,
                                            value = 10.0,
                                            startDate = checkInDateJava,
                                            endDate = checkInDateJava,
                                            taxPrototypeId = 2)

        val hmcTaxHolder = HMCTaxHolder(
          hotelTaxType = TaxType.ComprehensiveTaxRoomLevel,
          taxDetails = HmcTaxDetails(
            hmcTaxesIdentiferMapping = Map(1 -> hmcTaxHi, 2 -> hmcTaxLo),
            ycsTaxes = Map("2-0" -> HmcTaxes(hmcTaxV1Identifiers = Seq(1, 2))),
          ),
        )

        val hotelMetaSpecial = HotelMeta(hotelId = 1, countryId = 181L, cityId = 0, stateId = Some(46))
        val propOffer = aValidPropertyOffer.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.YCS))
        val roomRateCategory = aValidPropOfferRoomRateCategory.copy(roomTypeId = 2, rateCategoryId = 2)

        val res = service.buildDailyTaxEntry(
          aValidCheckIn,
          1,
          hotelMetaSpecial,
          propOffer,
          roomRateCategory,
          aValidPropOfferChannelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
          hmcTaxHolder,
        )

        res(aValidCheckIn).values.map(_.value).toSeq.sorted must_== Seq(10.0, 20.0) // no grouping
      }

      "group identical-id taxes for YCS/JTBWL in other states / countries" in {
        implicit val yplCtx: YplContext = aValidYplContext
          .withExperimentContext(
            forceBExperimentContext(YplExperiments.USE_TAXES_FROM_HMC, YplExperiments.ENABLE_HMC_FETCH))
          .withSupplierSetting(Map(DMC.YCS -> aValidYplSupplierSetting.copy(isThirdParty = false)))

        val checkInDateJava =
          java.time.LocalDate.of(aValidCheckIn.getYear, aValidCheckIn.getMonthOfYear, aValidCheckIn.getDayOfMonth)

        // two taxes with the same taxId but different values
        val hmcTaxHi = aValidHotelTax1.copy(taxId = 101,
                                            value = 20.0,
                                            startDate = checkInDateJava,
                                            endDate = checkInDateJava,
                                            taxPrototypeId = 1)
        val hmcTaxLo = aValidHotelTax1.copy(taxId = 101,
                                            value = 10.0,
                                            startDate = checkInDateJava,
                                            endDate = checkInDateJava,
                                            taxPrototypeId = 2)

        val hmcTaxHolder = HMCTaxHolder(
          hotelTaxType = TaxType.ComprehensiveTaxRoomLevel,
          taxDetails = HmcTaxDetails(
            hmcTaxesIdentiferMapping = Map(1 -> hmcTaxHi, 2 -> hmcTaxLo),
            ycsTaxes = Map("2-0" -> HmcTaxes(hmcTaxV1Identifiers = Seq(1, 2))),
          ),
        )

        val hotelMetaOther = HotelMeta(hotelId = 1, countryId = 999L, cityId = 0, stateId = Some(99))
        val propOffer = aValidPropertyOffer.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.YCS))
        val roomRateCategory = aValidPropOfferRoomRateCategory.copy(roomTypeId = 2, rateCategoryId = 2)

        val res = service.buildDailyTaxEntry(
          aValidCheckIn,
          1,
          hotelMetaOther,
          propOffer,
          roomRateCategory,
          aValidPropOfferChannelRate,
          Some("USD"),
          Some("USD"),
          propOffer.taxes,
          propOffer.taxesV2,
          mohuGdsCommissionFeeSettings,
          hmcTaxHolder,
        )

        // only the lower-value tax should survive after grouping
        res(aValidCheckIn).values.size must_== 1
        res(aValidCheckIn).values.head.id must_== 101
        res(aValidCheckIn).values.head.value must_== 10.0
      }
    }

    "buildTax" should {

      "return variable tax with correct converted amount" in {
        val mockVariableTax = proto.Tax(2, "PRPN", ApplyType.VariableTax, false, true, 10.00, false, 1234)

        val mockExchangeRateContext = mock[ExchangeRateContext]
        val mockExchangeRate = ExchangeRate("MYR", "EUR", 0.22, 1.03)
        val mockYplContext = aValidYplContext.copy(exchangeRateCtx = mockExchangeRateContext)
        mockExchangeRateContext.getExchangeRate("MYR", "EUR") returns Some(mockExchangeRate)

        val resultSameCurrency = service.buildTax(mockVariableTax,
                                                  None,
                                                  isBcom = false,
                                                  countryCurrency = Some("MYR"),
                                                  hotelCurrency = Some("MYR"),
                                                  mohuGdsCommissionFeeSettings)(mockYplContext)
        val resultDifferentCurrency = service.buildTax(mockVariableTax,
                                                       None,
                                                       isBcom = false,
                                                       countryCurrency = Some("MYR"),
                                                       hotelCurrency = Some("EUR"),
                                                       mohuGdsCommissionFeeSettings)(mockYplContext)

        resultSameCurrency._2.value must_== 10.00
        resultDifferentCurrency._2.value must_== mockExchangeRate.toReqRate(10.0)
      }

      "return without converting currency if not amount tax or not variable tax" in {
        val mockPercentageVariableTax =
          proto.Tax(1, "PRPN", ApplyType.VariableTax, false, isAmount = false, 5.00, false, 1234)
        val mockMandatoryTax = proto.Tax(2, "PRPN", ApplyType.Mandatory, false, isAmount = true, 10.00, false, 1234)

        val mockExchangeRateContext = mock[ExchangeRateContext]
        val mockExchangeRate = ExchangeRate("MYR", "EUR", 0.22, 1.03)
        val mockYplContext = aValidYplContext.copy(exchangeRateCtx = mockExchangeRateContext)
        mockExchangeRateContext.getExchangeRate("MYR", "EUR") returns Some(mockExchangeRate)

        val resultPercentageVariableTax = service.buildTax(mockPercentageVariableTax,
                                                           None,
                                                           isBcom = false,
                                                           countryCurrency = Some("MYR"),
                                                           hotelCurrency = Some("MYR"),
                                                           mohuGdsCommissionFeeSettings)(mockYplContext)
        val resultMandatoryTax = service.buildTax(mockMandatoryTax,
                                                  None,
                                                  isBcom = false,
                                                  countryCurrency = Some("MYR"),
                                                  hotelCurrency = Some("EUR"),
                                                  mohuGdsCommissionFeeSettings)(mockYplContext)

        resultPercentageVariableTax._2.value must_== 5.00
        resultMandatoryTax._2.value must_== 10.00
      }

      "buildTax returns empty applyOn and applyBreakdownType" in {
        val mockPercentageVariableTax =
          proto.Tax(1, "PRPN", ApplyType.VariableTax, false, isAmount = false, 5.00, false, 1234)

        implicit val mockYplContext: YplContext = mock[YplContext]
        val mockExperimentContext = mock[com.agoda.utils.flow.ExperimentContext]
        mockYplContext.experimentContext returns mockExperimentContext

        val (_, tax) = service.buildTax(
          mockPercentageVariableTax,
          None,
          isBcom = false,
          countryCurrency = Some("MYR"),
          hotelCurrency = Some("MYR"),
          MOHUGdsCommissionFeeSettings(cidSet = Set.empty[Int], targetTaxPrototypeIdSet = Set[Int](1234)),
        )

        tax.applyOn must beSome(0)
        tax.applyOver must beNone
      }
    }

    "buildTaxV2" should {

      "return variable tax with correct converted amount" in {
        val mockVariableTax =
          proto.TaxV2(2, "PRPN", ApplyType.VariableTax, false, true, 10.00, false, 1, taxCurrency = Some("MYR"))

        val mockExchangeRateContext = mock[ExchangeRateContext]
        val mockExchangeRate = ExchangeRate("MYR", "USD", 0.21, 1)
        mockExchangeRateContext.getExchangeRate("MYR", "USD") returns Some(mockExchangeRate)
        val mockYplContext = aValidYplContext.copy(exchangeRateCtx = mockExchangeRateContext)

        val resultMyrCurrency = service.buildTaxV2(mockVariableTax,
                                                   None,
                                                   isBcom = false,
                                                   hotelCurrency = Some("MYR"),
                                                   mohuGdsCommissionFeeSettings)(mockYplContext)
        val resultUsdCurrency = service.buildTaxV2(mockVariableTax,
                                                   None,
                                                   isBcom = false,
                                                   hotelCurrency = Some("USD"),
                                                   mohuGdsCommissionFeeSettings)(mockYplContext)

        resultMyrCurrency._2.value must_== 10.00
        resultUsdCurrency._2.value must_== 2.10
      }

      "return without converting currency if not amount tax or no currency setup" in {
        val mockPercentageTax = proto.TaxV2(1, "PRPN", ApplyType.VariableTax, false, isAmount = false, 5.00, false, 1)
        val mockNonCurrencyTax1 =
          proto.TaxV2(2, "PRPN", ApplyType.VariableTax, false, isAmount = true, 10.00, false, 2, taxCurrency = None)
        val mockNonCurrencyTax2 =
          proto.TaxV2(2, "PRPN", ApplyType.VariableTax, false, isAmount = true, 10.00, false, 3, taxCurrency = Some(""))

        val mockExchangeRateContext = mock[ExchangeRateContext]
        val mockExchangeRate = ExchangeRate("MYR", "USD", 0.21, 1)
        mockExchangeRateContext.getExchangeRate("MYR", "USD") returns Some(mockExchangeRate)
        val mockYplContext = aValidYplContext.copy(exchangeRateCtx = mockExchangeRateContext)

        val resultPercentageTax = service.buildTaxV2(mockPercentageTax,
                                                     None,
                                                     isBcom = false,
                                                     hotelCurrency = Some("USD"),
                                                     mohuGdsCommissionFeeSettings)(mockYplContext)
        val resultNonCurrencyTax1 = service.buildTaxV2(mockNonCurrencyTax1,
                                                       None,
                                                       isBcom = false,
                                                       hotelCurrency = Some("USD"),
                                                       mohuGdsCommissionFeeSettings)(mockYplContext)
        val resultNonCurrencyTax2 = service.buildTaxV2(mockNonCurrencyTax2,
                                                       None,
                                                       isBcom = false,
                                                       hotelCurrency = Some("USD"),
                                                       mohuGdsCommissionFeeSettings)(mockYplContext)

        resultPercentageTax._2.value must_== 5.00
        resultNonCurrencyTax1._2.value must_== 10.00
        resultNonCurrencyTax2._2.value must_== 10.00
      }

      "buildTaxV2 returns empty applyOn and applyBreakdownType" in {
        val mockPercentageTax = proto.TaxV2(1, "PRPN", ApplyType.VariableTax, false, isAmount = false, 5.00, false, 1)

        implicit val mockYplContext: YplContext = mock[YplContext]
        val mockExperimentContext = mock[com.agoda.utils.flow.ExperimentContext]
        mockYplContext.experimentContext returns mockExperimentContext

        val (_, tax) = service.buildTaxV2(mockPercentageTax,
                                          None,
                                          isBcom = false,
                                          hotelCurrency = Some("USD"),
                                          mohuGdsCommissionFeeSettings.copy(targetTaxPrototypeIdSet = Set(1)))

        tax.applyOn must beSome(0)
        tax.applyBreakdownType must beSome(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED)
        tax.applyOver must beNone
      }
    }

    "getTaxPrototypeInfoFromPo" should {
      val mockCurr1 = "USD"
      val mockCurr2 = "THB"
      val mockTaxID = 1
      val mockTaxProtoID1 = 1
      val mockTaxProtoID2 = 1

      val mockTaxLevel = proto.TaxPrototypeLevels(
        mockTaxProtoID1,
        Seq(proto.TaxPrototypeLevels.TaxPrototypeLevel(mockTaxProtoID1, 100d, 200d, 10d),
            proto.TaxPrototypeLevels.TaxPrototypeLevel(mockTaxProtoID2, 200d, 300d, 12d)),
      )

      val mockTaxLevelV2 = proto.TaxPrototypeLevelsV2(
        mockTaxProtoID1,
        Seq(proto.TaxPrototypeLevelsV2.TaxPrototypeLevel(1, 100d, 200d, 10d),
            proto.TaxPrototypeLevelsV2.TaxPrototypeLevel(2, 200d, 300d, 12d, Some(true))),
      )

      "Do NOT return TaxProtoTypeInfo when currencies are not set" in {
        val res =
          service.getTaxPrototypeInfo[proto.TaxPrototypeLevels](mockTaxLevel,
                                                                None,
                                                                None,
                                                                false,
                                                                service.covertTaxPrototypeLevelV1)(aValidYplContext)
        res.taxPrototypeLevels.isEmpty must beTrue
      }

      "return TaxProtoTypeInfo when currencies are same" in {
        val res =
          service.getTaxPrototypeInfo[proto.TaxPrototypeLevels](mockTaxLevel,
                                                                Some(mockCurr1),
                                                                Some(mockCurr1),
                                                                false,
                                                                service.covertTaxPrototypeLevelV1)(aValidYplContext)
        res.taxPrototypeLevels.size must_== 2
      }

      "return TaxProtoTypeInfo when currencies are NOT the same" in {
        val mockYplContext = mock[YplContext]
        val mockExchangeContext = mock[ExchangeRateContext]
        mockExchangeContext.getExchangeRate("THB", "USD") returns Some(
          ExchangeRate(local = "USD", request = "THB", toUsd = 1.0, toRequest = 30))
        mockYplContext.exchangeRateCtx returns mockExchangeContext
        mockYplContext.experimentContext returns forceAllBExperimentsContext()
        val res =
          service.getTaxPrototypeInfo[proto.TaxPrototypeLevels](mockTaxLevel,
                                                                Some(mockCurr1),
                                                                Some(mockCurr2),
                                                                false,
                                                                service.covertTaxPrototypeLevelV1)(mockYplContext)
        res.taxPrototypeLevels.size must_== 2
        res.taxPrototypeLevels must_== Seq(TaxPrototypeLevel(mockTaxProtoID1, 3000d, 6000d, 10d),
                                           TaxPrototypeLevel(mockTaxProtoID2, 6000d, 9000d, 12d))
      }

      "Do NOT return TaxProtoTypeInfo when currencies are not set - VYG-323 allocated as B Variant" in {
        val aValidBYplContext = aValidYplContext.withExperimentContext(forceAllBExperimentsContext()).build
        val res =
          service.getTaxPrototypeInfo[proto.TaxPrototypeLevelsV2](mockTaxLevelV2,
                                                                  None,
                                                                  None,
                                                                  false,
                                                                  service.covertTaxPrototypeLevelV2)(aValidBYplContext)
        res.taxPrototypeLevels.isEmpty must beTrue
      }

      "return TaxProtoTypeInfo when currencies are same - VYG-323 allocated as B Variant" in {
        val aValidBYplContext = aValidYplContext.withExperimentContext(forceAllBExperimentsContext()).build
        val res =
          service.getTaxPrototypeInfo[proto.TaxPrototypeLevelsV2](mockTaxLevelV2,
                                                                  Some(mockCurr1),
                                                                  Some(mockCurr1),
                                                                  false,
                                                                  service.covertTaxPrototypeLevelV2)(aValidBYplContext)
        val expectedLevel1 = TaxPrototypeLevel(1, 100d, 200d, 10d)
        val expectedLevel2 = TaxPrototypeLevel(2, 200d, 300d, 12d, true)
        res.taxPrototypeLevels.size must_== 2
        res.taxPrototypeLevels must_== List(expectedLevel1, expectedLevel2)
      }

      "return TaxProtoTypeInfo when currencies are NOT the same -  VYG-323 allocated as B Variant" in {
        val mockYplContext = mock[YplContext]
        val mockExchangeContext = mock[ExchangeRateContext]
        val mockExperimentContext = mock[ExperimentContext]
        mockExchangeContext.getExchangeRate("THB", "USD") returns Some(
          ExchangeRate(local = "USD", request = "THB", toUsd = 1.0, toRequest = 30))
        mockExperimentContext.isUserB(any()) returns true
        mockYplContext.exchangeRateCtx returns mockExchangeContext
        mockYplContext.experimentContext returns mockExperimentContext
        val res =
          service.getTaxPrototypeInfo[proto.TaxPrototypeLevelsV2](mockTaxLevelV2,
                                                                  Some(mockCurr1),
                                                                  Some(mockCurr2),
                                                                  false,
                                                                  service.covertTaxPrototypeLevelV2)(mockYplContext)
        res.taxPrototypeLevels.size must_== 2
        res.taxPrototypeLevels must_== Seq(TaxPrototypeLevel(1, 3000d, 6000d, 10d),
                                           TaxPrototypeLevel(2, 6000d, 9000d, 12d, true))
      }

      "return TaxProtoTypeInfo when currencies are NOT the same and isAmount - Tier Tax always on" in {
        val mockYplContext = mock[YplContext]
        val mockExchangeContext = mock[ExchangeRateContext]
        mockExchangeContext.getExchangeRate("THB", "USD") returns Some(
          ExchangeRate(local = "USD", request = "THB", toUsd = 1.0, toRequest = 30))
        mockYplContext.exchangeRateCtx returns mockExchangeContext

        val res =
          service.getTaxPrototypeInfo[proto.TaxPrototypeLevelsV2](mockTaxLevelV2,
                                                                  Some(mockCurr1),
                                                                  Some(mockCurr2),
                                                                  true,
                                                                  service.covertTaxPrototypeLevelV2)(mockYplContext)
        res.taxPrototypeLevels.size must_== 2
        res.taxPrototypeLevels must_== Seq(TaxPrototypeLevel(1, 3000d, 6000d, 300d),
                                           TaxPrototypeLevel(2, 6000d, 9000d, 360d, true))
      }
    }
  }
}
