package com.agoda.papi.ypl.pricing.promotions

import com.agoda.papi.enums.request.StackDiscountOption.Additive
import com.agoda.papi.enums.room.{ChargeOption, ChargeType, DiscountType}
import com.agoda.papi.pricing.pricecalculation.models.tax.Tax
import com.agoda.papi.ypl.models.{YPLTestContexts, YPLTestDataBuilders, YplContext}
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.models.helpers.PromotionTestHelper
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.pricing.CancellationPolicyServiceImpl
import com.agoda.papi.ypl.pricing.mocks.PromotionEntryValidationMock
import com.agoda.papi.ypl.services.{CidToOriginMapper, OriginManager}
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit

/**
  * Shared utilities trait for promotion stacking tests
  */
trait PromotionStackingTestUtilities extends SpecificationWithJUnit with YPLTestDataBuilders with YPLTestContexts {

  /**
    * Common converter implementation for promotion calculation tests
    */
  protected val converter: PromotionCalculationImpl with CancellationPolicyServiceImpl with PromotionEntryValidationMock =
    new {} with PromotionCalculationImpl with CancellationPolicyServiceImpl with PromotionEntryValidationMock {
      override def getOriginByCid(cid: Option[Int]): Option[String] = None
      override def getOriginByCidAndHotelCountry(cid: Option[Int], hotelCountry: Option[String]): Option[String] = None

      override val originManager: OriginManager = this
      override def getOriginByCid(cid: Int): Option[String] = None
      override val cidToOriginMapper: CidToOriginMapper = this
      override val promotionEntryValidation: PromotionEntryValidation = this
    }

  /**
    * Common test data
    */
  protected val hotelInfo: HotelMeta = HotelMeta(12345, 320)
  protected val checkInDate: DateTime = parseDateTime("2020-08-24")
  protected val checkoutDate: DateTime = parseDateTime("2020-08-26")

  /**
    * Generate a room entry with the given promotions for testing
    */
  def generateRoomEntryWithPromotions(promoEntry: List[Promotion]): YplRoomEntry = {
    val priceEntry = PriceEntry(checkInDate, ChargeType.Room, ChargeOption.Mandatory, "PRPN", 1, value = 100)
    val tax = Map((1, Tax.DEFAULT_PROTOTYPE_ID) -> 10.0d)
    val dailyPrice = DailyPrice(checkInDate, tax, isPromotionBlackOut = false, List(priceEntry))
    val promos = promoEntry.map(p =>
      PromotionEntry(
        id = p.promotionId,
        typeId = p.promotionTypeId,
        discounts = List(p.promotionDiscount),
        discountType = DiscountType.PercentDiscount,
        bookFrom = Some(checkInDate.minusDays(1)),
        bookTo = Some(checkInDate.plusDays(1)),
        bookTimeFrom = Some(360000000000L), // 10:00
        bookTimeTo = Some(300000000000L), // 08:20
        bookOn = "1111111",
        customerSegments = List(CustomerSegment(None, Some("TH"))),
        applyDates = Map(checkInDate -> 0),
        isStackable = p.isStackable,
        stackDiscountOption = Some(Additive),
      ))
    aValidRoomEntry.copy(dailyPrices = Map(checkInDate -> dailyPrice), availablePromotions = promos)
  }

  /**
    * Generate YPL context with pulse and mega sale metadata (performance optimized)
    */
  def generateYplContextWithPulseAndMegaSaleMetadata(yplRequest: YplRequest,
                                                     pulsePromotionTypeIds: List[Int] = List.empty,
                                                     megaSalePromotionTypeIds: List[Int] = List.empty): YplContext =
    if (pulsePromotionTypeIds.nonEmpty || megaSalePromotionTypeIds.nonEmpty) YplContext(
      yplRequest,
      pulseCampaignMetaContext =
        PromotionTestHelper.createPulseCampaignMetaContext(pulsePromotionTypeIds, megaSalePromotionTypeIds))
    else YplContext(yplRequest)

  /**
    * Generate YPL context with only pulse metadata (for backward compatibility)
    */
  def generateYplContextWithPulseMetadata(yplRequest: YplRequest,
                                          webCampaignPtypeIds: List[Int] = List.empty): YplContext =
    webCampaignPtypeIds match {
      case pTypes if pTypes.isEmpty => YplContext(yplRequest)
      case pTypes => YplContext(yplRequest, pulseCampaignMetaContext = aValidPulseCampaignMetaContext(pTypes))
    }

  /**
    * Run test cases and validate the results
    */
  def runTestCases(testCase: PromotionStackingTestCase, yplRequest: YplRequest): Boolean = {
    val roomEntry = generateRoomEntryWithPromotions(testCase.availablePromotions)
    val yplContext = generateYplContextWithPulseAndMegaSaleMetadata(
      yplRequest,
      testCase.pulsePromotionTypeIds,
      testCase.megaSalePromotionTypeIds,
    )
    val appliedRooms = converter.generatePromotionalRooms(hotelInfo, List(roomEntry))(yplContext)

    // Check no room
    if (testCase.finalDiscount == 0 && testCase.finalAppliedPromotions.isEmpty) {
      return appliedRooms.isEmpty
    }

    val promotionResultOption = for {
      room <- appliedRooms.headOption
      dayPrice <- room.dailyPrices.get(checkInDate)
      price <- dayPrice.prices.headOption
      promotionsBreakdown <- room.promotionsBreakdown.get(checkInDate.toLocalDate)
      roomPromotion <- room.promotion
    } yield {
      // Check total discount
      val isDiscountPassed = price.promoDiscount equals testCase.finalDiscount
      // Check applied promotions
      val isPromotionListPassed = testCase.finalAppliedPromotions.forall(
        promotionsBreakdown.map(_.id).contains) && testCase.finalAppliedPromotions.length == promotionsBreakdown.length
      // Check discount type
      val isDiscountTypePassed = roomPromotion.discountType equals testCase.finalDiscountType
      // Check Pulse metadata
      val pulseMetadata: Option[Int] = room.pulseCampaignMetadata.map(_.promotionTypeId)
      val isPulseMetadataPassed = pulseMetadata equals testCase.finalPromotionTypeIdInPulseMetadata
      isDiscountPassed && isPromotionListPassed && isDiscountTypePassed && isPulseMetadataPassed
    }

    promotionResultOption.contains(true)
  }

}
