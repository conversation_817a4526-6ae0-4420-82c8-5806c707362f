package com.agoda.papi.ypl.pricing

import com.agoda.finance.tax.enums.TaxLevelCalculationType
import com.agoda.papi.enums.hotel._
import com.agoda.papi.enums.request.{BookingDurationType, FeatureFlag, StackDiscountOption}
import com.agoda.papi.enums.room.RateType.SellExclusive
import com.agoda.papi.enums.room.{InventoryType, PaymentChannel, ApplyType => _, _}
import com.agoda.papi.pricing.metadata.RoomBookings
import com.agoda.papi.pricing.pricecalculation
import com.agoda.papi.pricing.pricecalculation.utils.TaxFiltersCacheInitialization

import com.agoda.papi.ypl.commission.apm.models.{AutoPriceMatchKeyEntry, AutoPriceMatchPriceInfo}
import com.agoda.papi.ypl.commission.{CommissionDailyHolder, MORPCommissionHolder}
import com.agoda.papi.ypl.fencing.{FencedAgxCommission, FencedChannelRate}
import com.agoda.papi.ypl.models.Wholesale._
import com.agoda.papi.ypl.models.api.request.{YplAGXCommission, YplAGXCommissionAdjustment, YplChildren, YplOccInfo}
import com.agoda.papi.ypl.models.builders.ypl.YplContextMock
import com.agoda.papi.ypl.models.consts.{Channel, ExternalDataFields}
import com.agoda.papi.ypl.models.enums.{BreakdownStep, OccupancyModel}
import com.agoda.papi.ypl.models.hotel.{AgePolicy, AgodaAgencyCommission, AgodaAgencyFeatures, SupplierCCMapping}
import com.agoda.papi.ypl.models.pricing._
import com.agoda.papi.ypl.models.pricing.proto.{
  CustomerSegment,
  HotelTaxInfo,
  CheckInInformation => YPLCheckInInformation,
  ChildRate => _,
  _,
}
import com.agoda.papi.ypl.models.proto.{PeriodIntervalInput, RestrictionEntryModel}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.suppliers.NoCCMeta.PaymentOptionSet
import com.agoda.papi.ypl.models.{SupplierFeatures, YplExperiments, ChildAgeRange => MetaChildAgeRange, _}
import com.agoda.papi.ypl.pricing.PropertyOfferConverter.TaxPerDayLookUp
import com.agoda.papi.ypl.pricing.mocks.{OriginManagerMock, CidToOriginMapperMock}
import com.agoda.papi.ypl.services.{CidToOriginMapper, OriginManager}
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.SupplierRateInfo
import com.agoda.supply.calc.proto
import com.agoda.supply.calc.proto.CancelationPolicy.CancelPolicyByOccupancy
import com.agoda.supply.calc.proto.ChildRate.ChildPricing
import com.agoda.supply.calc.proto.OccupancyModel.FullPatternLengthOfStay
import com.agoda.supply.calc.proto.{ApmApprovalPriceKeyValuePair, CheckInInformation, RoomLinkage => PropRoomLinkage, _}
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, LocalTime => JodaLocalTime}
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.BeforeAll
import org.specs2.matcher.DataTables
import org.mockito.Mockito.{times, verify, when}

import java.time.LocalTime
import scala.concurrent.duration.HOURS

class PropertyOfferConverterSpec
  extends SpecificationWithJUnit
    with YPLTestDataBuilders
    with Mockito
    with YPLTestContexts
    with YplOddCheckinCheckoutTestBuilder
    with BeforeAll
    with DataTables {

  override def beforeAll(): Unit = TaxFiltersCacheInitialization.initializeMockCache()

  val converter = new PropertyOfferConverter with OriginManagerMock with CidToOriginMapperMock {}
  val hotelPaymentModel = PaymentModel.Merchant
  val US_NEW_TAX_APPLY_TYPE = US_TAX_V2_EXPERIMENT

  implicit class SimpleDateTime(val t: DateTime) {
    def toSimpleDate(): SimpleDate = SimpleDate(t.getYear, t.getMonthOfYear, t.getDayOfMonth)
  }

  val channelRateWithoutChildRate = aValidPropOfferChannelRate
    .withPrices(
      Seq(
        aValidPropOfferPriceDaily
          .withStayDates(Seq(NumericRange(0, 0)))
          .withOccupancyPrices(Seq(
            aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(30),
            aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(2, 2))).withAmount(50),
          )),
        aValidPropOfferPriceDaily
          .withStayDates(Seq(NumericRange(1, 1)))
          .withOccupancyPrices(Seq(
            aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(20),
            aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(2, 2))).withAmount(30),
          )),
      ),
    )
    .withChannelDiscountPerDay(Map(0 -> 10, 1 -> 12))
    .clearChildAgeRangeRatesDaily

  "convert hotel entry" should {

    val mockPropOfferSurcharge = Surcharge(10, "PRPB", proto.ApplyType.Mandatory, false, 34.3, true)
    val mockPropOfferTax = proto.Tax(23, "PB", ApplyType.Mandatory, true, true, 34.20, true, 2)
    val mockPropOfferTaxV2_1 = proto.TaxV2(23, "PB", ApplyType.Mandatory, true, true, 34.20, true, 2)
    val mockPropOfferTaxV2_2 = proto.TaxV2(
      24,
      "PB",
      ApplyType.Mandatory,
      false,
      false,
      10,
      false,
      3,
      whomToPay = Some(1),
      orderNumber = Some(0),
      taxLevelCalculationType = com.agoda.supply.calc.proto.TaxLevelCalculationType.FlatRate,
      taxApplyBreakdownType = TaxApplyBreakdownType.NetEx,
      valueMethod = proto.ValueMethodType.Fixed,
      valueCalculationMethodType = proto.ValueCalculationMethodType.Percent,
      geoId = Some(114),
      geoType = proto.TaxGeoType.Country,
    )

    val aValidTimeOfDay = TimeOfDay(hours = 23, minutes = 59, seconds = 58)
    val po = aValidPropertyOffer
      .withHotelId(9999)
      .withMasterHotelId(10637)
      .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(3038))
      .withPaymentMode(PaymentMode.Agency)
      .withOccupancyModel(proto.OccupancyModel.Full)
      .withTaxes(Map(1 -> mockPropOfferTax))
      .withSurcharges(Map(1 -> mockPropOfferSurcharge))
      .withBookingCutOffTime(aValidTimeOfDay)
    val poWithTaxV2 = aValidPropertyOfferWithTaxV2
      .withHotelId(9999)
      .withMasterHotelId(10637)
      .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(3038))
      .withPaymentMode(PaymentMode.Agency)
      .withOccupancyModel(proto.OccupancyModel.Full)
      .withTaxes(Map(1 -> mockPropOfferTax))
      .withSurcharges(Map(1 -> mockPropOfferSurcharge))
      .withBookingCutOffTime(aValidTimeOfDay)
      .withTaxesV2(Map(1 -> mockPropOfferTaxV2_1))
    val poWithMultipleTaxesV2 = poWithTaxV2.withTaxesV2(Map(1 -> mockPropOfferTaxV2_1, 2 -> mockPropOfferTaxV2_2))
    val meta = aValidHotelInfo.withHotelId(10637).withProcessingFeeOption(1).withStateId(1)
    val dispatchChannels = YplDispatchChannels(Set(YplMasterChannel(1)), Set.empty)
    val fencedDispatchChannels = Map(aValidRateFence -> dispatchChannels)
    implicit val ctx = aValidYplContext

    "works properly - with TaxV2 is empty on Variant A (Experiment should not get allocated)" in {
      val result = converter.convert(po, meta, dispatchChannels, fencedDispatchChannels)
      result must not be empty
      val hotel = result.get
      hotel.hotelId must_== 10637
      hotel.supplierId must_== 3038
      hotel.paymentModel must_== PaymentModel.Agency
      result must not be empty
      val hotelentry = result.get
      hotelentry.rooms must not be empty
      hotelentry.copy(rooms = List.empty) must_== YplHotelEntryModel(
        hotelId = 10637,
        supplierId = 3038,
        paymentModel = PaymentModel.Agency,
        rooms = List(),
        occupancyModel = OccupancyModel.Full,
        taxInfo = TaxInfo(
          HotelTaxInfo(TaxType.SimpleTax, false),
          Map(
            (23, 2) -> pricecalculation.models.tax
              .Tax(23, "PRPB", true, true, true, 34.2, ChargeOption.Mandatory, 2, None, None, applyOn = Some(0))),
        ),
        rateModel = RateModel.New,
        surchargeRateType = SellExclusive,
        metaData = meta,
        retailHotel = None,
        dispatchChannels = dispatchChannels,
        dispatchChannelsPerFence = fencedDispatchChannels,
        ratePlanLanguage = None,
        rateReutilizations = List(
          YPLRateReutilizationEntry(YplChannel(3, Set(), 3),
                                    YplChannel(1, Set(), 1),
                                    YplChannel(2, Set(), 2),
                                    5,
                                    3.5,
                                    Some(1),
                                    Some(2))),
        reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(1), None, Some(1), None, List()),
                                           AgePolicy(0, 0, 0, false, ChildrenStayFreeType.Unknown),
                                           false),
        lengthOfStay = aValidLos,
        bookingCutoffTime =
          Some(new JodaLocalTime(aValidTimeOfDay.hours, aValidTimeOfDay.minutes, aValidTimeOfDay.seconds)),
        supplierSourceType = None,
        expiresAt = Option(0),
        isOTASupplier = Some(true),
        supplierContractedCommission = Some(4.5),
        stackChannelDiscountInfo = Map.empty,
        autoPriceMatchIdsHash = Some("hash"),
        channelLookUpByRateCategoryAndRoomTypeMap = Map(
          (10L, 24453L) ->
            RateCategoryChannelLookUp(Set(1), Map(1 -> Set(0)))),
      )
    }

    "works properly - with TaxV2 is not empty on Variant A (VYG-323 Experiment should get allocated as false)" in {
      val result = converter.convert(poWithTaxV2, meta, dispatchChannels, fencedDispatchChannels)
      result must not be empty
      val hotel = result.get
      hotel.hotelId must_== 10637
      hotel.supplierId must_== 3038
      hotel.paymentModel must_== PaymentModel.Agency
      result must not be empty
      val hotelentry = result.get
      hotelentry.rooms must not be empty
      hotelentry.copy(rooms = List.empty) must_== YplHotelEntryModel(
        hotelId = 10637,
        supplierId = 3038,
        paymentModel = PaymentModel.Agency,
        rooms = List(),
        occupancyModel = OccupancyModel.Full,
        taxInfo = TaxInfo(
          HotelTaxInfo(TaxType.SimpleTax, false),
          Map(
            (23, 2) -> pricecalculation.models.tax
              .Tax(23, "PRPB", true, true, true, 34.2, ChargeOption.Mandatory, 2, None, None, applyOn = Some(0))),
        ),
        rateModel = RateModel.New,
        surchargeRateType = SellExclusive,
        metaData = meta,
        retailHotel = None,
        dispatchChannels = dispatchChannels,
        dispatchChannelsPerFence = fencedDispatchChannels,
        ratePlanLanguage = None,
        rateReutilizations = List(
          YPLRateReutilizationEntry(YplChannel(3, Set(), 3),
                                    YplChannel(1, Set(), 1),
                                    YplChannel(2, Set(), 2),
                                    5,
                                    3.5,
                                    Some(1),
                                    Some(2))),
        reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(1), None, Some(1), None, List()),
                                           AgePolicy(0, 0, 0, false, ChildrenStayFreeType.Unknown),
                                           false),
        lengthOfStay = aValidLos,
        bookingCutoffTime =
          Some(new JodaLocalTime(aValidTimeOfDay.hours, aValidTimeOfDay.minutes, aValidTimeOfDay.seconds)),
        supplierSourceType = None,
        expiresAt = Option(0),
        isOTASupplier = Some(true),
        supplierContractedCommission = Some(4.5),
        stackChannelDiscountInfo = Map.empty,
        autoPriceMatchIdsHash = Some("hash"),
        channelLookUpByRateCategoryAndRoomTypeMap = Map(
          (10L, 24453L) ->
            RateCategoryChannelLookUp(Set(1), Map(1 -> Set(0)))),
      )
    }

    "works properly - with TaxV2 is not empty but TaxPerDayV2 not existed (VYG-323 Experiment should not be allocated)" in {
      val result =
        converter.convert(po.withTaxesV2(Map(1 -> mockPropOfferTaxV2_1)), meta, dispatchChannels, fencedDispatchChannels)
      result must not be empty
      val hotel = result.get
      hotel.hotelId must_== 10637
      hotel.supplierId must_== 3038
      hotel.paymentModel must_== PaymentModel.Agency
      result must not be empty
      val hotelentry = result.get
      hotelentry.rooms must not be empty
      hotelentry.copy(rooms = List.empty) must_== YplHotelEntryModel(
        hotelId = 10637,
        supplierId = 3038,
        paymentModel = PaymentModel.Agency,
        rooms = List(),
        occupancyModel = OccupancyModel.Full,
        taxInfo = TaxInfo(
          HotelTaxInfo(TaxType.SimpleTax, false),
          Map(
            (23, 2) -> pricecalculation.models.tax
              .Tax(23, "PRPB", true, true, true, 34.2, ChargeOption.Mandatory, 2, None, None, applyOn = Some(0))),
        ),
        rateModel = RateModel.New,
        surchargeRateType = SellExclusive,
        metaData = meta,
        retailHotel = None,
        dispatchChannels = dispatchChannels,
        dispatchChannelsPerFence = fencedDispatchChannels,
        ratePlanLanguage = None,
        rateReutilizations = List(
          YPLRateReutilizationEntry(YplChannel(3, Set(), 3),
                                    YplChannel(1, Set(), 1),
                                    YplChannel(2, Set(), 2),
                                    5,
                                    3.5,
                                    Some(1),
                                    Some(2))),
        reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(1), None, Some(1), None, List()),
                                           AgePolicy(0, 0, 0, false, ChildrenStayFreeType.Unknown),
                                           false),
        lengthOfStay = aValidLos,
        bookingCutoffTime =
          Some(new JodaLocalTime(aValidTimeOfDay.hours, aValidTimeOfDay.minutes, aValidTimeOfDay.seconds)),
        supplierSourceType = None,
        expiresAt = Option(0),
        isOTASupplier = Some(true),
        supplierContractedCommission = Some(4.5),
        stackChannelDiscountInfo = Map.empty,
        autoPriceMatchIdsHash = Some("hash"),
        channelLookUpByRateCategoryAndRoomTypeMap = Map(
          (10L, 24453L) ->
            RateCategoryChannelLookUp(Set(1), Map(1 -> Set(0)))),
      )
    }

    "works properly - with TaxV2 is not empty but TaxPerDayV2 partially existed (VYG-323 Experiment should not be allocated)" in {
      lazy val aValidBExperimentContext = forceBExperimentContext(US_NEW_TAX_APPLY_TYPE)
      val result = converter.convert(
        po.withRoomRates(Seq(aValidPropOfferRoomRateCategoryWithPartialTaxPerDayV2))
          .withTaxesV2(
            Map(1 -> mockPropOfferTaxV2_1,
                2 -> mockPropOfferTaxV2_1.withValue(22),
                3 -> mockPropOfferTaxV2_1.withValue(33))),
        meta,
        dispatchChannels,
        fencedDispatchChannels,
      )(aValidYplContext.withExperimentContext(aValidBExperimentContext).build)
      result must not be empty
      val hotel = result.get
      hotel.hotelId must_== 10637
      hotel.supplierId must_== 3038
      hotel.paymentModel must_== PaymentModel.Agency
      result must not be empty
      val hotelentry = result.get
      hotelentry.rooms must not be empty
      hotelentry.copy(rooms = List.empty) must_== YplHotelEntryModel(
        hotelId = 10637,
        supplierId = 3038,
        paymentModel = PaymentModel.Agency,
        rooms = List(),
        occupancyModel = OccupancyModel.Full,
        taxInfo = TaxInfo(
          HotelTaxInfo(TaxType.SimpleTax, false),
          Map(
            (23, 2) -> pricecalculation.models.tax
              .Tax(23, "PRPB", true, true, true, 34.2, ChargeOption.Mandatory, 2, None, None, applyOn = Some(0))),
        ),
        rateModel = RateModel.New,
        surchargeRateType = SellExclusive,
        metaData = meta,
        retailHotel = None,
        dispatchChannels = dispatchChannels,
        dispatchChannelsPerFence = fencedDispatchChannels,
        ratePlanLanguage = None,
        rateReutilizations = List(
          YPLRateReutilizationEntry(YplChannel(3, Set(), 3),
                                    YplChannel(1, Set(), 1),
                                    YplChannel(2, Set(), 2),
                                    5,
                                    3.5,
                                    Some(1),
                                    Some(2))),
        reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(1), None, Some(1), None, List()),
                                           AgePolicy(0, 0, 0, false, ChildrenStayFreeType.Unknown),
                                           false),
        lengthOfStay = aValidLos,
        bookingCutoffTime =
          Some(new JodaLocalTime(aValidTimeOfDay.hours, aValidTimeOfDay.minutes, aValidTimeOfDay.seconds)),
        supplierSourceType = None,
        expiresAt = Option(0),
        isOTASupplier = Some(true),
        supplierContractedCommission = Some(4.5),
        stackChannelDiscountInfo = Map.empty,
        autoPriceMatchIdsHash = Some("hash"),
        channelLookUpByRateCategoryAndRoomTypeMap = Map(
          (10L, 24453L) ->
            RateCategoryChannelLookUp(Set(1), Map(1 -> Set(0)))),
      )
    }

    "works properly - with TaxV2 is empty but TaxPerDayV2 partially existed (VYG-323 Experiment should not be allocated)" in {
      lazy val aValidBExperimentContext = forceBExperimentContext(US_NEW_TAX_APPLY_TYPE)
      val result =
        converter.convert(po.withRoomRates(Seq(aValidPropOfferRoomRateCategoryWithPartialTaxPerDayV2)),
                          meta,
                          dispatchChannels,
                          fencedDispatchChannels)(aValidYplContext.withExperimentContext(aValidBExperimentContext).build)
      result must not be empty
      val hotel = result.get
      hotel.hotelId must_== 10637
      hotel.supplierId must_== 3038
      hotel.paymentModel must_== PaymentModel.Agency
      result must not be empty
      val hotelentry = result.get
      hotelentry.rooms must not be empty
      hotelentry.copy(rooms = List.empty) must_== YplHotelEntryModel(
        hotelId = 10637,
        supplierId = 3038,
        paymentModel = PaymentModel.Agency,
        rooms = List(),
        occupancyModel = OccupancyModel.Full,
        taxInfo = TaxInfo(
          HotelTaxInfo(TaxType.SimpleTax, false),
          Map(
            (23, 2) -> pricecalculation.models.tax
              .Tax(23, "PRPB", true, true, true, 34.2, ChargeOption.Mandatory, 2, None, None, applyOn = Some(0))),
        ),
        rateModel = RateModel.New,
        surchargeRateType = SellExclusive,
        metaData = meta,
        retailHotel = None,
        dispatchChannels = dispatchChannels,
        dispatchChannelsPerFence = fencedDispatchChannels,
        ratePlanLanguage = None,
        rateReutilizations = List(
          YPLRateReutilizationEntry(YplChannel(3, Set(), 3),
                                    YplChannel(1, Set(), 1),
                                    YplChannel(2, Set(), 2),
                                    5,
                                    3.5,
                                    Some(1),
                                    Some(2))),
        reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(1), None, Some(1), None, List()),
                                           AgePolicy(0, 0, 0, false, ChildrenStayFreeType.Unknown),
                                           false),
        lengthOfStay = aValidLos,
        bookingCutoffTime =
          Some(new JodaLocalTime(aValidTimeOfDay.hours, aValidTimeOfDay.minutes, aValidTimeOfDay.seconds)),
        supplierSourceType = None,
        expiresAt = Option(0),
        isOTASupplier = Some(true),
        supplierContractedCommission = Some(4.5),
        stackChannelDiscountInfo = Map.empty,
        autoPriceMatchIdsHash = Some("hash"),
        channelLookUpByRateCategoryAndRoomTypeMap = Map(
          (10L, 24453L) ->
            RateCategoryChannelLookUp(Set(1), Map(1 -> Set(0)))),
      )
    }

    "works properly - with TaxV2 is not empty on MSE search under Variant B (VYG-323 Experiment should not get allocated)" in {
      val yplRequest = aValidYplRequest.copy(cInfo = aValidYplRequest.cInfo.copy(cid = Some(1)))
      val result = converter.convert(poWithTaxV2, meta, dispatchChannels, fencedDispatchChannels)(
        aValidYplContext.copy(request = yplRequest))
      result must not be empty
      val hotel = result.get
      hotel.hotelId must_== 10637
      hotel.supplierId must_== 3038
      hotel.paymentModel must_== PaymentModel.Agency
      result must not be empty
      val hotelentry = result.get
      hotelentry.rooms must not be empty
      hotelentry.copy(rooms = List.empty) must_== YplHotelEntryModel(
        hotelId = 10637,
        supplierId = 3038,
        paymentModel = PaymentModel.Agency,
        rooms = List(),
        occupancyModel = OccupancyModel.Full,
        taxInfo = TaxInfo(
          HotelTaxInfo(TaxType.SimpleTax, false),
          Map(
            (23, 2) -> pricecalculation.models.tax
              .Tax(23, "PRPB", true, true, true, 34.2, ChargeOption.Mandatory, 2, None, None, applyOn = Some(0))),
        ),
        rateModel = RateModel.New,
        surchargeRateType = SellExclusive,
        metaData = meta,
        retailHotel = None,
        dispatchChannels = dispatchChannels,
        dispatchChannelsPerFence = fencedDispatchChannels,
        ratePlanLanguage = None,
        rateReutilizations = List(
          YPLRateReutilizationEntry(YplChannel(3, Set(), 3),
                                    YplChannel(1, Set(), 1),
                                    YplChannel(2, Set(), 2),
                                    5,
                                    3.5,
                                    Some(1),
                                    Some(2))),
        reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(1), None, Some(1), None, List()),
                                           AgePolicy(0, 0, 0, false, ChildrenStayFreeType.Unknown),
                                           false),
        lengthOfStay = aValidLos,
        bookingCutoffTime =
          Some(new JodaLocalTime(aValidTimeOfDay.hours, aValidTimeOfDay.minutes, aValidTimeOfDay.seconds)),
        supplierSourceType = None,
        expiresAt = Option(0),
        isOTASupplier = Some(true),
        supplierContractedCommission = Some(4.5),
        stackChannelDiscountInfo = Map.empty,
        autoPriceMatchIdsHash = Some("hash"),
        channelLookUpByRateCategoryAndRoomTypeMap = Map(
          (10L, 24453L) ->
            RateCategoryChannelLookUp(Set(1), Map(1 -> Set(0)))),
      )
    }

    "works properly - with TaxV2 is empty on Variant B (VYG-323 Experiment should not get allocated)" in {
      lazy val aValidBExperimentContext = forceBExperimentContext(US_NEW_TAX_APPLY_TYPE)
      val BVariantCtx = aValidYplContext.withExperimentContext(aValidBExperimentContext).build
      val result = converter.convert(po, meta, dispatchChannels, fencedDispatchChannels)(BVariantCtx)
      result must not be empty
      val hotel = result.get
      hotel.hotelId must_== 10637
      hotel.supplierId must_== 3038
      hotel.paymentModel must_== PaymentModel.Agency
      result must not be empty
      val hotelentry = result.get
      hotelentry.rooms must not be empty
      hotelentry.copy(rooms = List.empty) must_== YplHotelEntryModel(
        hotelId = 10637,
        supplierId = 3038,
        paymentModel = PaymentModel.Agency,
        rooms = List(),
        occupancyModel = OccupancyModel.Full,
        taxInfo = TaxInfo(
          HotelTaxInfo(TaxType.SimpleTax, false),
          Map(
            (23, 2) -> pricecalculation.models.tax
              .Tax(23, "PRPB", true, true, true, 34.2, ChargeOption.Mandatory, 2, None, None, applyOn = Some(0))),
        ),
        rateModel = RateModel.New,
        surchargeRateType = SellExclusive,
        metaData = meta,
        retailHotel = None,
        dispatchChannels = dispatchChannels,
        dispatchChannelsPerFence = fencedDispatchChannels,
        ratePlanLanguage = None,
        rateReutilizations = List(
          YPLRateReutilizationEntry(YplChannel(3, Set(), 3),
                                    YplChannel(1, Set(), 1),
                                    YplChannel(2, Set(), 2),
                                    5,
                                    3.5,
                                    Some(1),
                                    Some(2))),
        reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(1), None, Some(1), None, List()),
                                           AgePolicy(0, 0, 0, false, ChildrenStayFreeType.Unknown),
                                           false),
        lengthOfStay = aValidLos,
        bookingCutoffTime =
          Some(new JodaLocalTime(aValidTimeOfDay.hours, aValidTimeOfDay.minutes, aValidTimeOfDay.seconds)),
        supplierSourceType = None,
        expiresAt = Option(0),
        isOTASupplier = Some(true),
        supplierContractedCommission = Some(4.5),
        stackChannelDiscountInfo = Map.empty,
        autoPriceMatchIdsHash = Some("hash"),
        channelLookUpByRateCategoryAndRoomTypeMap = Map(
          (10L, 24453L) ->
            RateCategoryChannelLookUp(Set(1), Map(1 -> Set(0)))),
      )
    }

    "works properly - with TaxV2 is non-empty but room rates do not contain taxV2" in {
      lazy val aValidBExperimentContext = forceBExperimentContext(US_NEW_TAX_APPLY_TYPE)
      val BVariantCtx = aValidYplContext
        .withExperimentContext(aValidBExperimentContext)
        .build
        .copy(request = aValidYplContext.request.copy(commonTaxSettingsOpt = Some(commonTaxSettings)))
      val invalidPOWithTaxV2 = po.withTaxesV2(Map(1 -> aValidPropOfferTaxV2))
      val result = converter.convert(invalidPOWithTaxV2, meta, dispatchChannels, fencedDispatchChannels)(BVariantCtx)
      result must not be empty
      val hotel = result.get
      hotel.hotelId must_== 10637
      hotel.supplierId must_== 3038
      hotel.paymentModel must_== PaymentModel.Agency
      result must not be empty
      val hotelentry = result.get
      hotelentry.rooms must not be empty
      hotelentry.copy(rooms = List.empty) must_== YplHotelEntryModel(
        hotelId = 10637,
        supplierId = 3038,
        paymentModel = PaymentModel.Agency,
        rooms = List(),
        occupancyModel = OccupancyModel.Full,
        taxInfo = TaxInfo(
          HotelTaxInfo(TaxType.SimpleTax, false),
          Map(
            (23, 2) -> pricecalculation.models.tax
              .Tax(23, "PRPB", true, true, true, 34.2, ChargeOption.Mandatory, 2, None, None, applyOn = Some(0))),
        ),
        rateModel = RateModel.New,
        surchargeRateType = SellExclusive,
        metaData = meta,
        retailHotel = None,
        dispatchChannels = dispatchChannels,
        dispatchChannelsPerFence = fencedDispatchChannels,
        ratePlanLanguage = None,
        rateReutilizations = List(
          YPLRateReutilizationEntry(YplChannel(3, Set(), 3),
                                    YplChannel(1, Set(), 1),
                                    YplChannel(2, Set(), 2),
                                    5,
                                    3.5,
                                    Some(1),
                                    Some(2))),
        reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(1), None, Some(1), None, List()),
                                           AgePolicy(0, 0, 0, false, ChildrenStayFreeType.Unknown),
                                           false),
        lengthOfStay = aValidLos,
        bookingCutoffTime =
          Some(new JodaLocalTime(aValidTimeOfDay.hours, aValidTimeOfDay.minutes, aValidTimeOfDay.seconds)),
        supplierSourceType = None,
        expiresAt = Option(0),
        isOTASupplier = Some(true),
        supplierContractedCommission = Some(4.5),
        stackChannelDiscountInfo = Map.empty,
        autoPriceMatchIdsHash = Some("hash"),
        channelLookUpByRateCategoryAndRoomTypeMap = Map(
          (10L, 24453L) ->
            RateCategoryChannelLookUp(Set(1), Map(1 -> Set(0)))),
      )
    }

    "works properly - with TaxV2/TaxLevelPrototypeV2 is not empty on Variant B (VYG-323 Experiment should get allocated as true)" in {
      lazy val aValidBExperimentContext = forceBExperimentContext(US_NEW_TAX_APPLY_TYPE)
      val expectedTaxV2_1 = pricecalculation.models.tax.Tax(
        id = 23,
        applyTo = "PRPB",
        isAmount = true,
        isFee = true,
        isTaxable = true,
        value = 34.2,
        option = ChargeOption.Mandatory,
        protoTypeId = 2,
        taxPrototypeInfo = None,
        applyOver = None,
        whomToPay = None,
        orderNumber = None,
        taxLevelCalculationType = Some(TaxLevelCalculationType.Unknown),
        valueMethod = Some(com.agoda.papi.enums.room.ValueMethodType.Unknown),
        valueCalculationMethodType = Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Unknown),
        geoId = None,
        geoType = Some(GeoType.Unknown),
        applyOn = Some(0),
        applyBreakdownType = Some(TaxApplyBreakdownType.TAX_APPLY_BREAKDOWN_TYPE_UNSPECIFIED),
      )
      val expectedTaxV2_2 = pricecalculation.models.tax.Tax(
        id = 24,
        applyTo = "PB",
        isAmount = false,
        isFee = false,
        isTaxable = false,
        value = 10.0,
        option = ChargeOption.Mandatory,
        protoTypeId = 3,
        taxPrototypeInfo = None,
        applyOver = None,
        whomToPay = Some(WhomToPayType.Property),
        orderNumber = Some(0),
        taxLevelCalculationType = Some(TaxLevelCalculationType.FlatRate),
        valueMethod = Some(com.agoda.papi.enums.room.ValueMethodType.Fixed),
        valueCalculationMethodType = Some(com.agoda.papi.enums.room.ValueCalculationMethodType.Percent),
        geoId = Some(114),
        geoType = Some(GeoType.Country),
        applyOn = Some(0),
        applyBreakdownType = Some(TaxApplyBreakdownType.NetEx),
      )
      val yplRequestWithUSSettingsEnabled = aValidYplRequest.copy(commonTaxSettingsOpt = Some(commonTaxSettings))
      val BVariantCtx =
        ctx.withExperimentContext(aValidBExperimentContext).build.copy(request = yplRequestWithUSSettingsEnabled)
      val result = converter.convert(poWithMultipleTaxesV2, meta, dispatchChannels, fencedDispatchChannels)(BVariantCtx)
      result must not be empty
      val hotel = result.get
      hotel.hotelId must_== 10637
      hotel.supplierId must_== 3038
      hotel.paymentModel must_== PaymentModel.Agency
      result must not be empty
      val hotelentry = result.get
      hotelentry.rooms must not be empty
      hotelentry.copy(rooms = List.empty) must_== YplHotelEntryModel(
        hotelId = 10637,
        supplierId = 3038,
        paymentModel = PaymentModel.Agency,
        rooms = List(),
        occupancyModel = OccupancyModel.Full,
        taxInfo =
          TaxInfo(HotelTaxInfo(TaxType.SimpleTax, false), Map((23, 2) -> expectedTaxV2_1, (24, 3) -> expectedTaxV2_2)),
        rateModel = RateModel.New,
        surchargeRateType = SellExclusive,
        metaData = meta,
        retailHotel = None,
        dispatchChannels = dispatchChannels,
        dispatchChannelsPerFence = fencedDispatchChannels,
        ratePlanLanguage = None,
        rateReutilizations = List(
          YPLRateReutilizationEntry(YplChannel(3, Set(), 3),
                                    YplChannel(1, Set(), 1),
                                    YplChannel(2, Set(), 2),
                                    5,
                                    3.5,
                                    Some(1),
                                    Some(2))),
        reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(Some(1), None, Some(1), None, List()),
                                           AgePolicy(0, 0, 0, false, ChildrenStayFreeType.Unknown),
                                           false),
        lengthOfStay = aValidLos,
        bookingCutoffTime =
          Some(new JodaLocalTime(aValidTimeOfDay.hours, aValidTimeOfDay.minutes, aValidTimeOfDay.seconds)),
        supplierSourceType = None,
        expiresAt = Option(0),
        isOTASupplier = Some(true),
        supplierContractedCommission = Some(4.5),
        stackChannelDiscountInfo = Map.empty,
        autoPriceMatchIdsHash = Some("hash"),
        channelLookUpByRateCategoryAndRoomTypeMap = Map(
          (10L, 24453L) ->
            RateCategoryChannelLookUp(Set(1), Map(1 -> Set(0)))),
      )
    }

    "do not filter out rooms if channels is in rate repurpose" in {
      val otherDispatchChannels = YplDispatchChannels(Set(YplMasterChannel(2)), Set.empty)
      val result = converter.convert(po.withRateRepurpose(Seq(aValidPropOfferRateRepurposeInfo)),
                                     meta,
                                     otherDispatchChannels,
                                     Map(aValidRateFence -> otherDispatchChannels))
      result must not be empty
      val hotel = result.get
      val rateRepurposeChannels = Set(
        aValidPropOfferRateRepurposeInfo.sourceChannel,
        aValidPropOfferRateRepurposeInfo.referenceChannel,
        aValidPropOfferRateRepurposeInfo.targetChannel,
      )
      hotel.rooms.filter(r => rateRepurposeChannels.contains(r.channel.baseChannelId)) must not be empty
    }

    "do not filter out rooms if channels is in rate repurpose in case of YCS" in {
      val otherDispatchChannels = YplDispatchChannels(Set(YplMasterChannel(2)), Set.empty)
      val ycsSupply = po.supplyInfo.map(supplyInfo => supplyInfo.withSupplierId(332)).get
      val poUpdated = po
        .withRateRepurpose(Seq(aValidPropOfferRateRepurposeInfo))
        .withSupplyInfo(ycsSupply)
        .withOccupancyModel(proto.OccupancyModel.FullPatternLengthOfStay)
      val result =
        converter.convert(poUpdated, meta, otherDispatchChannels, Map(aValidRateFence -> otherDispatchChannels))
      result must not be empty
      val hotel = result.get
      val rateRepurposeChannels = Set(
        aValidPropOfferRateRepurposeInfo.sourceChannel,
        aValidPropOfferRateRepurposeInfo.referenceChannel,
        aValidPropOfferRateRepurposeInfo.targetChannel,
      )
      hotel.rooms.filter(r => rateRepurposeChannels.contains(r.channel.baseChannelId)) must not be empty
      hotel.occupancyModel.value must_== proto.OccupancyModel.FullPatternLengthOfStay.value
    }

    "convert extrabed correctly" in {
      val ctx = aValidYplContext.withExperimentContext(
        forcedFromRequestExperimentContext(
          aValidYplRequest.withBExperiment(YplExperiments.USE_NEW_DATA_AND_SKIP_CONVERTER)))
      val otherDispatchChannels = YplDispatchChannels(Set(YplMasterChannel(2)), Set.empty)
      val poUpdated = aValidPropertyOffer.withRoomRates(
        aValidPropertyOffer.roomRates.map(_.copy(channelRates =
          Seq(aValidPropOfferChannelRate.copy(prices = Seq(aValidPropOfferPriceDaily.withExtraBedPrice(100d)))))))

      // Kill Switch Off
      val result = converter.convert(poUpdated,
                                     meta.withIsChannelManaged(false),
                                     otherDispatchChannels,
                                     Map(aValidRateFence -> otherDispatchChannels))(aValidYplContext)
      result must not be empty
      val hotel = result.get
      hotel.rooms.head.dailyPrices.exists { case (_, v) =>
        v.prices.exists(p => p.chargeType == ChargeType.ExtraBed && p.rateType == RateType.Unknown && p.value == 100d)
      } must_== true
      hotel.rooms.head.dailyPrices.exists { case (_, v) =>
        v.prices.exists(p => p.chargeType == ChargeType.Room && p.rateType == RateType.Unknown)
      } must_== true
    }

    "filter out rooms if not in requested channels" in {
      val otherDispatchChannels = YplDispatchChannels(Set(YplMasterChannel(2)), Set.empty)
      val result = converter.convert(po.clearRateRepurpose,
                                     meta,
                                     otherDispatchChannels,
                                     Map(aValidRateFence -> otherDispatchChannels))
      result must not be empty
      val hotel = result.get
      hotel.rooms must beEmpty
    }

    "check roomtypeIdRateCategoryIdChannelRateIdMap is created in case of booking request" in {
      val otherDispatchChannels = YplDispatchChannels(Set(YplMasterChannel(2)), Set.empty)
      val ctx = aValidYplContext.withRequest(
        aValidYplRequest.copy(isBookingRequest = true).withCheckout(aValidCheckIn.plusDays(2)))
      val result = converter.convert(po, meta, otherDispatchChannels, Map(aValidRateFence -> otherDispatchChannels))(ctx)
      result must not be empty
      val hotel = result.get
      hotel.rooms.head.roomDataChangeTracker must beSome
      hotel.rooms.head.roomDataChangeTracker.get.perDayPerOccPrice.size must_== 4
    }

    "check roomtypeIdRateCategoryIdChannelRateIdMap is not created in case of non booking request" in {
      val otherDispatchChannels = YplDispatchChannels(Set(YplMasterChannel(2)), Set.empty)
      val result = converter.convert(po, meta, otherDispatchChannels, Map(aValidRateFence -> otherDispatchChannels))
      result must not be empty
      val hotel = result.get
      hotel.rooms.head.roomDataChangeTracker must beSome
      hotel.rooms.head.roomDataChangeTracker.get.perDayPerOccPrice.size must_== 0
    }

    "Read isUseConfiguredProcessingFee from HotelMeta" in {
      val ctx: YplContext = aValidYplContext
      val propOffer: PropertyOffer = po.withIsUseConfiguredProcessingFee(true)
      val hMeta: HotelMeta = meta.withProcessingFeeOption(1)
      val result = converter.convert(propOffer, hMeta, dispatchChannels, fencedDispatchChannels)(ctx)
      result must not be empty
      val hotel = result.get
      hotel.taxInfo.hotelTaxInfo.isConfigProcessingFees must_== false
    }

    "should have multiple YPLRoom with same roomtypeid but different supplierroomtypeid in roomtypeentry for pull" in {
      val roomTypeId = po.roomTypes.head._1
      val updatedRates = Seq.fill(3)(po.roomRates.head).zipWithIndex.map { case (rate, index) =>
        rate.copy(roomTypeId = roomTypeId, supplierRoomTypeId = index.toString)
      }
      val updatedPo = po.copy(
        supplyInfo = po.supplyInfo.map(_.copy(supplySource = SupplySourceType.Pull, supplierId = 27964)),
        roomRates = updatedRates,
        commissions = Map(0 -> Commission(1, 1, 1.0, 1.0, 1.0),
                          1 -> Commission(1, 1, 1.0, 1.0, 1.0),
                          2 -> Commission(1, 1, 1.0, 1.0, 1.0),
                          -1 -> Commission(1, 1, 1.0, 1.0, 1.0)),
      )
      val result = converter.convert(updatedPo, meta, dispatchChannels, fencedDispatchChannels)
      result must not be empty
      val hotel = result.get
      hotel.rooms must not be empty
      hotel.rooms.map(_.roomTypeId) must_== Seq(24453, 24453, 24453)
      hotel.rooms.map(_.roomType) must_== Seq(
        RoomTypeEntry(24453, 2, 1, 1, false, 0, 2, Some("0")),
        RoomTypeEntry(24453, 2, 1, 1, false, 0, 2, Some("1")),
        RoomTypeEntry(24453, 2, 1, 1, false, 0, 2, Some("2")),
      )
    }

    "should not set isOTASupplier on dmc: JTBWL" in {
      val result = converter.convert(po.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.JTBWL)),
                                     meta,
                                     dispatchChannels,
                                     fencedDispatchChannels)
      val resultUat = converter.convert(po.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.JTBUATWL)),
                                        meta,
                                        dispatchChannels,
                                        fencedDispatchChannels)

      val hotel = result.get
      hotel.isOTASupplier must beEmpty
      resultUat.get.isOTASupplier must_== Some(true)
    }

    "should set isOTASupplier based on supplier features when JTBFP-1295=B" in {
      val supplierFeatures = SupplierFeatures(features = Map(
        DMC.JTBWL -> avalidFeatureForJTB,
        DMC.NoSupplier -> aValidFeature,
        DMC.JTBUATWL -> avalidFeatureForJTB,
      ))
      val yplRequest = aValidYplRequest.copy(supplierFeatures = supplierFeatures)
      implicit val ctxWithSupplierFeature: YplContext = aValidYplContext
        .withRequest(yplRequest)
        .withExperimentContext(forceBExperimentContext(YplExperiments.DMC_HARDCODING_REMOVAL))

      val resultJTBWL = converter.convert(po.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.JTBWL)),
                                          meta,
                                          dispatchChannels,
                                          fencedDispatchChannels)(ctxWithSupplierFeature)
      val resultJTBUATWL = converter.convert(po.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.JTBUATWL)),
                                             meta,
                                             dispatchChannels,
                                             fencedDispatchChannels)(ctxWithSupplierFeature)
      val resultYCS = converter.convert(po.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.YCS)),
                                        meta,
                                        dispatchChannels,
                                        fencedDispatchChannels)(ctxWithSupplierFeature)

      val resultRandomSupplier =
        converter.convert(po.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.NoSupplier)),
                          meta,
                          dispatchChannels,
                          fencedDispatchChannels)(ctxWithSupplierFeature)

      resultJTBWL.get.isOTASupplier should_== None
      resultJTBUATWL.get.isOTASupplier should_== None
      resultYCS.get.isOTASupplier should_== Some(true)
      resultRandomSupplier.get.isOTASupplier should_== Some(true)
    }

    "should set occupancyModel correctly based on experiment variant and supplier features" should {
      "expVariant" | "supplierFeatures" | "supplierId" | "expectedOccupancyModel" |
        'A' ! null ! DMC.BCOM ! proto.OccupancyModel.Full |
        'A' ! null ! DMC.JTBWL ! proto.OccupancyModel.FullPatternLengthOfStay |
        'A' ! null ! DMC.JTBUATWL ! proto.OccupancyModel.Full |
        'A' ! null ! DMC.BCOM ! proto.OccupancyModel.Full |
        'B' ! null ! DMC.JTBWL ! proto.OccupancyModel.Full |
        'B' ! null ! DMC.BCOM ! proto.OccupancyModel.Full |
        'B' ! aValidSupplierFeatures ! DMC.JTBWL ! proto.OccupancyModel.FullPatternLengthOfStay |
        'B' ! aValidSupplierFeatures ! DMC.JTBUATWL ! proto.OccupancyModel.FullPatternLengthOfStay |
        'B' ! SupplierFeatures(features = Map(DMC.JTBWL -> aValidFeature)) ! DMC.JTBWL ! proto.OccupancyModel.Full |> {
          (expVariant, supplierFeatures, supplierId, expectedOccupancyModel) =>
            val poWithOccupancyModel = po.withOccupancyModel(proto.OccupancyModel.FullPatternLengthOfStay)
            val yplRequest =
              if (supplierFeatures != null) aValidYplRequest.copy(supplierFeatures = supplierFeatures)
              else
                aValidYplRequest.copy(supplierFeatures = SupplierFeatures(features = Map(DMC.NoSupplier -> aValidFeature)))

            implicit val ctxWithSupplierFeature: YplContext = aValidYplContext
              .withRequest(yplRequest)
              .withExperimentContext(
                forcedExperimentFromList(List(YplExperiment(YplExperiments.DMC_HARDCODING_REMOVAL, expVariant))))

            val result = converter.convert(
              poWithOccupancyModel.withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(supplierId)),
              meta,
              dispatchChannels,
              fencedDispatchChannels)(ctxWithSupplierFeature)

            result.get.occupancyModel.value should_== expectedOccupancyModel.value
        }
    }

    "should not set retailGlobalContractedCommission if commissions are not provided" in {
      val result = converter.convert(po.withCommissions(Map.empty), meta, dispatchChannels, fencedDispatchChannels)
      val hotel = result.get
      hotel.retailGlobalContractedCommission must_== 0.0d
    }

    "should return resell offer when resell offer is present" in {
      val req = aValidYplRequest
      val channelRate = aValidPropOfferChannelRate.withExternalData(
        Seq(proto.ExternalData(ExternalDataFields.ResellSourceBookingId, "00789")))
      val rc = aValidPropOfferRoomRateCategory.withRoomTypeId(111546).withChannelRates(Seq(channelRate))
      val rti = RoomTypeInfo(maxOccupancy = 2)

      val po = aValidPropertyOffer.withRoomRates(Seq(rc)).withRoomTypes(Map(111546L -> rti))

      // should include resell offers
      val result = converter.convert(po, aValidHotelInfo, dispatchChannels, fencedDispatchChannels)
      result.get.rooms.size must_== 1
      result.get.rooms.exists(_.isResell) must beTrue
    }

    "inventory filter" should {
      val offer = po.withRoomRates(
        Seq(aValidPropOfferRoomRateCategory.withInventoryType(com.agoda.supply.calc.proto.InventoryType.OTAJapanican)))
      val agodaReq = aValidYplRequest.withWhitelabelSetting(aValidwhitelabelSetting.copy(whitelabelID = 1))
      val context = aValidYplContext.withRequest(agodaReq)

      "not execute if Variant A of JTBFUN-1390" in {
        val ctx = context.withExperimentContext(
          forcedFromRequestExperimentContext(
            aValidYplRequest.withAExperiment(YplExperiments.REMOVE_OTA_JAPANICAN_KILL_SWITCH)),
        )
        val result = converter.convert(offer, meta, dispatchChannels, fencedDispatchChannels)(ctx)

        result.map(_.rooms).get must not be empty
      }
    }

    "not remove non-hourly rates" in {
      val yplCtx = ctx
        .copy(request = aValidYplRequest.copy(bookingDurationTypes = List(BookingDurationType.Hourly)))
        .withExperimentContext(forceAllBExperimentsContext())
        .build
      val result = converter.convert(
        aValidPropertyOffer.withRoomRates(Seq(aValidPropOfferRoomRateCategory, aValidHourlyPropOfferRoomRateCategory)),
        meta,
        dispatchChannels,
        fencedDispatchChannels,
      )(yplCtx)
      result.get.rooms.size must_== 2
    }

    "Do not filter rates for odd checkIn checkout hotels " in {
      testCases.map { testCase =>
        val durationType = if (testCase.isHourly) BookingDurationType.Hourly else BookingDurationType.Nightly
        val countryId = if (testCase.requireKRHotel) 212 else 106
        val meta = aValidHotelInfo.copy(countryId = countryId)
        val req = aValidYplRequest
          .copy(bookingDurationTypes = List(durationType))
          .withFeatureRequest(aValidYplFeatureRequest.copy(enableRatePlanCheckInCheckOut =
            testCase.enableRatePlanCheckinCheckoutFeatureFlag))
        val yplCtx = ctx.copy(request = req)
        val result = converter.convert(
          aValidPropertyOffer
            .withRoomRates(Seq(aValidPropOfferRoomRateCategory))
            .withCheckInInformations(
              Map(
                0 -> CheckInInformation(
                  checkInFrom = Some(TimeOfDay(hours = 1, minutes = 1, seconds = 1)),
                  checkInUntil = Some(TimeOfDay(hours = 2, minutes = 2, seconds = 2)),
                  checkOutFrom = Some(TimeOfDay(hours = 3, minutes = 3, seconds = 3)),
                  checkOutUntil = Some(TimeOfDay(hours = 4, minutes = 4, seconds = 4)),
                ),
              )),
          meta,
          dispatchChannels,
          fencedDispatchChannels,
        )(yplCtx)
        result.get.rooms.size must_== 1

      }
      testCases.size shouldEqual 8
    }

    "maintain proper tax type" in {
      val result = converter.convert(po.withTaxType(PropertyTaxType.ComprehensiveTaxHotelLevel),
                                     meta,
                                     dispatchChannels,
                                     fencedDispatchChannels)
      result must not be empty
      val hotelentry = result.get
      hotelentry.rooms must not be empty
      hotelentry.taxInfo.hotelTaxInfo.taxType must_== TaxType.ComprehensiveTaxHotelLevel
    }
  }

  "buildCustomerSegment" should {

    "build Customer Segment Properly" in {
      val protoCustomerSegment =
        com.agoda.supply.calc.proto.CustomerSegment.defaultInstance.withLanguageId(1).withCountryCode("TH")
      val result = converter.buildCustomerSegment(protoCustomerSegment)
      result.languageId must beSome(1)
      result.countryCode must beSome("TH")
    }

    "filter out languageId properly" in {
      val protoCustomerSegment =
        com.agoda.supply.calc.proto.CustomerSegment.defaultInstance.withLanguageId(0).withCountryCode("TH")
      val result = converter.buildCustomerSegment(protoCustomerSegment)
      result.languageId must beNone
      result.countryCode must beSome("TH")
    }

    "filter out country code properly" in {
      val protoCustomerSegment =
        com.agoda.supply.calc.proto.CustomerSegment.defaultInstance.withLanguageId(1).withCountryCode("")
      val result = converter.buildCustomerSegment(protoCustomerSegment)
      result.languageId must beSome(1)
      result.countryCode must beNone
    }
  }

  "buildRateCategoryBookingRestriction" should {
    val bookFrom = com.agoda.supply.calc.proto.SimpleDate(2020, 8, 1)
    val bookTo = com.agoda.supply.calc.proto.SimpleDate(2020, 10, 1)
    val DEFAULT_MAXADV = 999
    val customerSegments = Seq(
      com.agoda.supply.calc.proto.CustomerSegment.defaultInstance.withLanguageId(1).withCountryCode("TH"),
      com.agoda.supply.calc.proto.CustomerSegment.defaultInstance.withLanguageId(4).withCountryCode("JP"),
    )
    val bookingRestriction = com.agoda.supply.calc.proto.BookingRestriction.defaultInstance
      .withCustomerSegments(customerSegments)
      .withBookDateFrom(bookFrom)
      .withBookDateTo(bookTo)
      .withBookOn("1111111")
      .withMinAdvancePurchase(10)
      .withMaxAdvancePurchase(15)

    "populate properly" in {
      val result = converter.buildRateCategoryBookingRestriction(Some(bookingRestriction), true)
      val expectedCustomerSegment = List(
        pricing.proto.CustomerSegment(Some(1), Some("TH")),
        pricing.proto.CustomerSegment(Some(4), Some("JP")),
      )
      val expectedBookingIntervals = List(
        PeriodIntervalInput(1, 1596214800000L, 1601485200000L),
      )
      result should beSome[RestrictionEntryModel]
      result.get.bookOn should beSome("1111111")
      result.get.customerSegments should containTheSameElementsAs(expectedCustomerSegment)
      result.get.bookingIntervals should containTheSameElementsAs(expectedBookingIntervals)
      result.get.minAdvance must beSome(10)
      result.get.maxAdvance must beSome(15)
    }

    "not build customersegment if not allowed" in {
      val result = converter.buildRateCategoryBookingRestriction(Some(bookingRestriction), false)
      result should beSome[RestrictionEntryModel]
      result.get.customerSegments.isEmpty must beTrue
    }

    "filter minadv if equal to -1" in {
      val result =
        converter.buildRateCategoryBookingRestriction(Some(bookingRestriction.withMinAdvancePurchase(-1)), false)
      result should beSome[RestrictionEntryModel]
      result.get.minAdvance must beNone
    }

    "filter maxadv if equal to DEFAULT_MAXADV" in {
      val result =
        converter.buildRateCategoryBookingRestriction(Some(bookingRestriction.withMaxAdvancePurchase(DEFAULT_MAXADV)),
                                                      false)
      result should beSome[RestrictionEntryModel]
      result.get.maxAdvance must beNone
    }

  }

  "buildPromotion" should {
    val lookup = Map(0 -> aValidOfferBookingRestriction)
    implicit val ctx = aValidYplContext

    "works properly" in {
      val result = converter.buildPromotion(aValidPropOfferPromotion, lookup, "")

      val expected = aValidPromotionEntry
        .withDiscounts(aValidPropOfferPromotion.discounts.toList)
        .withBookOn(aValidOfferBookingRestriction.bookOn)
        .withBookingFrom("1970-01-01")
        .withBookingTo("2099-01-01")
        .withMinAdvance(Some(10))
        .withMaxAdvance(Some(15))
        .withCancellation(aValidPropOfferPromotion.cancellationCode)
        .withCustomerSegments(List(CustomerSegment(Some(1), Some("TH"))))
        .withApplyDates(Map(DateTime.parse("2015-10-10") -> 0, DateTime.parse("2015-10-11") -> 1))
        .build

      result must_== expected
    }

    "with discount type pb sumup discount properly with multiple range combination" in {
      val result = converter.buildPromotion(
        aValidPropOfferPromotion
          .withApplyDates(
            Seq(Discount(Seq(NumericRange(0, 1), NumericRange(3, 3)), 20), Discount(Seq(NumericRange(2, 2)), 10)))
          .withDiscountTypeId(DiscountType.AmountDiscountPerBook.value),
        lookup,
        "",
      )
      result.discounts must_== List(70)
    }

    "filter out stackdiscountoption unknown to none" in {
      val result =
        converter.buildPromotion(aValidPropOfferPromotion.withStackableDiscountType(StackDiscountOption.Unknown.value),
                                 lookup,
                                 "")
      result.stackDiscountOption must beNone
    }

    "build index for AmountDiscountPerBook correctly" in {
      val result = converter.buildPromotion(
        aValidPropOfferPromotion
          .withApplyDates(
            Seq(Discount(Seq(NumericRange(0, 1), NumericRange(3, 3)), 15), Discount(Seq(NumericRange(2, 2)), 10)))
          .withDiscountTypeId(DiscountType.AmountDiscountPerBook.value),
        lookup,
        "",
      )
      result.discounts must_== List(55)
      result.applyDates must_== Map(
        DateTime.parse("2015-10-10") -> 0,
        DateTime.parse("2015-10-11") -> 0,
        DateTime.parse("2015-10-12") -> 0,
        DateTime.parse("2015-10-13") -> 0,
      )
    }

    "build index for Percent discount correctly" in {
      val result = converter.buildPromotion(
        aValidPropOfferPromotion
          .withApplyDates(
            Seq(Discount(Seq(NumericRange(0, 1), NumericRange(3, 3)), 20), Discount(Seq(NumericRange(2, 2)), 10)))
          .withDiscountTypeId(DiscountType.PercentDiscount.value),
        lookup,
        "",
      )
      result.discounts must_== List(20.0, 20.0, 10.0, 20.0)
      result.applyDates must_== Map(
        DateTime.parse("2015-10-10") -> 0,
        DateTime.parse("2015-10-11") -> 1,
        DateTime.parse("2015-10-12") -> 2,
        DateTime.parse("2015-10-13") -> 3,
      )
    }

    "build discout for Free Night correctly" in {
      val result = converter.buildPromotion(
        aValidPropOfferPromotion
          .withApplyDates(Seq(Discount(Seq(NumericRange(2, 3)), 1d)))
          .withDiscounts(Seq(2d, 0d, 0d, 0d, 0d, 0d, 0d))
          .withDiscountTypeId(DiscountType.FreeNight.value),
        lookup,
        "",
      )
      result.discounts must_== List(2)
      result.applyDates must_== Map(
        DateTime.parse("2015-10-12") -> 0,
        DateTime.parse("2015-10-13") -> 0,
      )
    }

    "use room cancellation code if promotion cancellation is empty" in {
      val result = converter.buildPromotion(
        aValidPropOfferPromotion
          .withApplyDates(Seq(Discount(Seq(NumericRange(2, 3)), 1d)))
          .withDiscounts(Seq(2d, 0d, 0d, 0d, 0d, 0d, 0d))
          .withCancellationCode("")
          .withDiscountTypeId(DiscountType.FreeNight.value),
        lookup,
        "0D100P_100P",
      )
      result.cancellationCode must_== "0D100P_100P"
    }

    "should not use room cancellation code if promotion cancellation is not empty" in {
      val result = converter.buildPromotion(
        aValidPropOfferPromotion
          .withApplyDates(Seq(Discount(Seq(NumericRange(2, 3)), 1d)))
          .withDiscounts(Seq(2d, 0d, 0d, 0d, 0d, 0d, 0d))
          .withCancellationCode("1D100P_100P")
          .withDiscountTypeId(DiscountType.FreeNight.value),
        lookup,
        "0D100P_100P",
      )
      result.cancellationCode must_== "1D100P_100P"
    }
  }

  "buildSurchargeEntry" should {

    "build surcharge properly for same all days with los > 1, with per book applied only in first night" in {
      val checkIn = aValidCheckIn
      val surcharges = Map(
        1 -> aValidPropOfferSurcharge
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(true)
          .withIsCommissionable(true)
          .withSurchargeId(1)
          .withValue(10),
        2 -> aValidPropOfferSurcharge
          .withApplyTo("PRPN")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(false)
          .withIsCommissionable(true)
          .withSurchargeId(2)
          .withValue(5),
        3 -> aValidPropOfferSurcharge
          .withApplyTo("PAPN")
          .withApplyType(ApplyType.Optional)
          .withIsAmount(true)
          .withIsCommissionable(false)
          .withSurchargeId(3)
          .withValue(5),
      )

      val channelRate = aValidPropOfferChannelRate.withSurchargePerDay(Map(-1 -> Identifiers(Seq(1, 2, 3))))

      val result = converter.buildSurchargeEntry(checkIn, 2, surcharges, channelRate)
      result must_== Seq(
        SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(checkIn), true, true, 10, 0, true),
        SurchargeEntry(2, "PRPN", ChargeOption.Mandatory, Set(checkIn, checkIn.plusDays(1)), false, true, 5, 0, true),
        SurchargeEntry(3, "PAPN", ChargeOption.Optional, Set(checkIn, checkIn.plusDays(1)), true, false, 5, 0, true),
      )
    }

    "build surcharge properly for surcharge different for each days with los > 1, with per book applied only in first night" in {
      val checkIn = aValidCheckIn
      val surcharges = Map(
        1 -> aValidPropOfferSurcharge
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(true)
          .withIsCommissionable(true)
          .withSurchargeId(1)
          .withValue(10),
        2 -> aValidPropOfferSurcharge
          .withApplyTo("PRPN")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(false)
          .withIsCommissionable(true)
          .withSurchargeId(2)
          .withValue(5),
        3 -> aValidPropOfferSurcharge
          .withApplyTo("PAPN")
          .withApplyType(ApplyType.Optional)
          .withIsAmount(true)
          .withIsCommissionable(false)
          .withSurchargeId(3)
          .withValue(5),
      )

      val channelRate =
        aValidPropOfferChannelRate.withSurchargePerDay(Map(0 -> Identifiers(Seq(1, 2, 3)), 1 -> Identifiers(Seq(1, 2))))

      val result = converter.buildSurchargeEntry(checkIn, 2, surcharges, channelRate)
      result must containTheSameElementsAs(
        Seq(
          SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(checkIn), true, true, 10, 0, true),
          SurchargeEntry(2, "PRPN", ChargeOption.Mandatory, Set(checkIn, checkIn.plusDays(1)), false, true, 5, 0, true),
          SurchargeEntry(3, "PAPN", ChargeOption.Optional, Set(checkIn), true, false, 5, 0, true),
        ))
    }

    "build surcharge properly with occ specific surcharge, combined with normal surcharge" in {
      val checkIn = aValidCheckIn
      val surcharges = Map(
        1 -> aValidPropOfferSurcharge
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(true)
          .withIsCommissionable(true)
          .withSurchargeId(5)
          .withValue(10),
        2 -> aValidPropOfferSurcharge
          .withApplyTo("PRPN")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(false)
          .withIsCommissionable(true)
          .withSurchargeId(6)
          .withValue(5),
        3 -> aValidPropOfferSurcharge
          .withApplyTo("PAPN")
          .withApplyType(ApplyType.Optional)
          .withIsAmount(true)
          .withIsCommissionable(false)
          .withSurchargeId(787)
          .withValue(5),
      )

      val channelRate = aValidPropOfferChannelRate
        .withSurchargePerDay(Map(-1 -> Identifiers(Seq(1, 2))))
        .withPrices(
          Seq(
            aValidPropOfferPriceDaily.withOccupancyPrices(
              Seq(
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 2))),
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(3, 3))).withSurcharges(Identifiers(Seq(3))),
              ),
            )),
        )

      val result = converter.buildSurchargeEntry(checkIn, 2, surcharges, channelRate)
      result must containTheSameElementsAs(
        Seq(
          SurchargeEntry(5, "PB", ChargeOption.Mandatory, Set(checkIn), true, true, 10, 0, true),
          SurchargeEntry(6, "PRPN", ChargeOption.Mandatory, Set(checkIn, checkIn.plusDays(1)), false, true, 5, 0, true),
          SurchargeEntry(787,
                         "PAPN",
                         ChargeOption.Optional,
                         Set(checkIn, checkIn.plusDays(1)),
                         true,
                         false,
                         5,
                         3,
                         true,
          ), // set occ correctly
        ))
    }

    "build occupancy surcharge for multi los correctly" in {
      val checkIn = aValidCheckIn
      val surcharges = Map(
        1 -> aValidPropOfferSurcharge
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(true)
          .withIsCommissionable(true)
          .withSurchargeId(787)
          .withValue(10),
        2 -> aValidPropOfferSurcharge
          .withApplyTo("PRPN")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(false)
          .withIsCommissionable(true)
          .withSurchargeId(6)
          .withValue(5),
        3 -> aValidPropOfferSurcharge
          .withApplyTo("PAPN")
          .withApplyType(ApplyType.Optional)
          .withIsAmount(true)
          .withIsCommissionable(false)
          .withSurchargeId(5)
          .withValue(5),
      )

      val channelRate = aValidPropOfferChannelRate
        .withSurchargePerDay(Map(-1 -> Identifiers(Seq(2, 3))))
        .withPrices(
          Seq(
            aValidPropOfferPriceDaily
              .withOccupancyPrices(
                Seq(
                  aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 2))),
                  aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(3, 3))).withSurcharges(Identifiers(Seq(1))),
                ),
              )
              .withStayDates(Seq(com.agoda.supply.calc.proto.NumericRange(0, 0))),
            aValidPropOfferPriceDaily
              .withOccupancyPrices(
                Seq(
                  aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 2))),
                  aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(3, 3))).withSurcharges(Identifiers(Seq(1))),
                ),
              )
              .withStayDates(Seq(com.agoda.supply.calc.proto.NumericRange(1, 2))),
            aValidPropOfferPriceDaily
              .withOccupancyPrices(
                Seq(
                  aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 2))),
                  aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(3, 3))).withSurcharges(Identifiers(Seq(1))),
                ),
              )
              .withStayDates(Seq(com.agoda.supply.calc.proto.NumericRange(3, 3))),
          ),
        )

      "return correct extra person charge for Marriott" in {
        val result = converter.buildSurchargeEntry(checkIn, 2, surcharges, channelRate)
        result must containTheSameElementsAs(
          Seq(
            SurchargeEntry(6, "PRPN", ChargeOption.Mandatory, Set(checkIn, checkIn.plusDays(1)), false, true, 5, 0, true),
            SurchargeEntry(5, "PAPN", ChargeOption.Optional, Set(checkIn, checkIn.plusDays(1)), true, false, 5, 0, true),
            SurchargeEntry(787,
                           "PB",
                           ChargeOption.Mandatory,
                           Set(checkIn),
                           true,
                           true,
                           10,
                           3,
                           true,
            ), // set occ correctly
          ))
      }
    }
  }

  "Override Room With SupplierFeatures correctly" in {
    val jtbwlFeature = avalidFeatureForJTB.copy(applyNoCC = ApplyNoCC(true, Set(1), Set(1)))
    val supplierFeatures = SupplierFeatures(
      features = Map((29014 -> jtbwlFeature)),
    )

    val res = converter.overrideRoomWithSupplierFeatures(
      Some(jtbwlFeature),
      aValidRoomEntry.withPaymentModel(Some(PaymentModel.Merchant)),
      PaymentModel.Merchant,
      List(PaymentChannel.Offline),
    )(
      aValidYplContext.withRequest(aValidYplRequest.withSupplierFeature(supplierFeatures)),
    )
    res.paymentOptions must_== aValidRoomEntry.paymentOptions ++ Set(PaymentOption.NoCreditCard)
  }

  "buildRoomEntry" should {
    val po = aValidPropertyOffer
      .withCancelPolicies(Map(1 -> aValidPropOfferCancelPolicy.withCode("7D1N_3D50P_100P")))
      .withIsPromotionCombinable(true)
      .withRateCategoryInfoMap(Map(
        10 -> RateCategoryInfo(7),
      ))
    val poWithCheckInfo = po.withCheckInInformations(
      Map(
        1 -> CheckInInformation(
          checkInFrom = Some(TimeOfDay(hours = 1, minutes = 1, seconds = 1)),
          checkInUntil = Some(TimeOfDay(hours = 2, minutes = 2, seconds = 2)),
          checkOutFrom = Some(TimeOfDay(hours = 3, minutes = 3, seconds = 3)),
          checkOutUntil = Some(TimeOfDay(hours = 4, minutes = 4, seconds = 4)),
        ),
      ))
    val meta = aValidHotelInfo.withEnabledRoom(
      Map(111546L -> aValidEnabledRoom.withMasterRoomTypeId(111546).withDisplayRackrate(200d)))
    val channelRate = aValidPropOfferChannelRate
      .withChannelId(1)
      .withCancelationPolicy(
        CancelationPolicy(
          cancelCodeIdentifier = 1,
          cancelPolicyByOccupancy = Seq.empty,
          cancelChargeType = CancelChargeType.PerBookCharge,
        ))
      .withRemainingRooms(11)
      .withChannelDiscountRateLoadType(PriceType.SellExc)
      .withRateLoadType(PriceType.NetInc)
      .withProcessingFee(7)
      .withPaymentMode(PaymentMode.Agency)
      .withSupplierRateCode("8132102_93766198_2_1_1")
      .withExternalData(Seq(proto.ExternalData("a", "123"),
                            proto.ExternalData("b", "456"),
                            proto.ExternalData(ExternalDataFields.ResellSourceBookingId, "00789")))
      .withRoomUid("uid")
    val rc = aValidPropOfferRoomRateCategory
      .withRoomTypeId(111546)
      .withBenefits(Seq.empty)
      .withChannelRates(
        Seq(
          channelRate
            .withCheckInInformationIdentifier(1)
            .withSupplierFundedDiscount(SupplierFundedDiscount(discountPercentage = 10.0))))
      .withCurrencyCode("VND")

    implicit val ctx = aValidYplContext
    val ctxWithRatePlanCheckIn = ctx.copy(request =
      ctx.request.copy(featureRequest = ctx.request.featureRequest.copy(enableRatePlanCheckInCheckOut = true)))

    "convert roomentry properly" in {
      val rcUpdated = rc.copy(supplierRoomTypeId = "123")
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     rcUpdated,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      result.size must_== 1
      val resultRoomType = result.head
      resultRoomType.roomTypeId must_== 111546
      resultRoomType.channel must_== YplMasterChannel(1)
      resultRoomType.cxlCode must_== "7D1N_3D50P_100P"
      resultRoomType.isBreakFastIncluded must_== false
      resultRoomType.remainingRooms must_== 11
      resultRoomType.currency must_== "VND"
      resultRoomType.rateType must_== RateType.NetInclusive
      resultRoomType.processingFees must_== 7
      resultRoomType.isAllowCombinePromotion must_== true
      resultRoomType.dailyPrices must not be empty
      resultRoomType.occEntry must_== pricing.RoomOccupancy(0, 0)
      resultRoomType.availablePromotions must beEmpty
      resultRoomType.channelRateType must beSome(RateType.SellExclusive)
      resultRoomType.paymentModel must beSome(PaymentModel.Agency)
      resultRoomType.paymentOptions must_== PaymentOptionSet.PREPAYMENT
      resultRoomType.dailyPrices.head._2.supplierFundedDiscountPercentage must beSome(10.0)
      resultRoomType.dmcDataHolder must beSome(
        DmcDataHolder(
          Some("123|456|00789"),
          Some("uid"),
          Some(
            SupplierRateInfo(
              Some("8132102_93766198_2_1_1"),
              None,
              Seq(
                SupplierRateInfo.ExternalData("a", "123", Some(false)),
                SupplierRateInfo.ExternalData("b", "456", Some(false)),
                SupplierRateInfo.ExternalData(ExternalDataFields.ResellSourceBookingId, "00789", Some(false)),
              ),
              None,
            )),
          None,
        ),
      )
      resultRoomType.hourlyAvailableSlots must beEmpty
      resultRoomType.confirmByMins must beEmpty
      resultRoomType.displayedRackRate must_== 200d
      resultRoomType.rateCategoryInfo must_== Some(YplRateCategoryInfo(7))
      resultRoomType.resellExternalData must beSome(ResellExternalData(sourceBookingId = "00789"))
      resultRoomType.roomType.dmcRoomId must beNone
      resultRoomType.roomDataChangeTracker must beSome
      resultRoomType.roomDataChangeTracker.get.perDayPerOccPrice must beEmpty
    }

    "convert roomentry properly with correct tracker information having parent RC" in {
      val ctx = aValidYplContext.withRequest(
        aValidYplRequest.copy(isBookingRequest = true).withCheckout(aValidCheckIn.plusDays(2)))
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     aValidPropOfferRoomRateCategory,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)(ctx)
      result.size must_== 1
      val resultRoomType = result.head
      resultRoomType.roomTypeId must_== 24453
      val roomTracker = resultRoomType.roomDataChangeTracker
      roomTracker should beSome
      val perDayPrices = roomTracker.get.perDayPerOccPrice
      perDayPrices.size must_== 4
      perDayPrices.head.rateCategoryId must_== 10
    }

    "convert roomentry with existing supplierCCInformation should contain bor feature" in {
      val updatedMeta = meta.withSuppliersCCMapping(
        Set(
          SupplierCCMapping(DMC.BCOM,
                            isGenericCreditCardReq = false,
                            isDomesticCreditCardRequired = false,
                            isBookOnRequest = true),
        ))
      val updatedPo = po.copy(supplyInfo = po.supplyInfo.map(_.copy(supplierId = DMC.BCOM)))
      val result = converter.buildPropOfferRoomEntry(updatedPo,
                                                     updatedMeta,
                                                     rc,
                                                     isBcomFixTaxAmountApplyToPB = true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(isApplyYcsRateForking =
        false)
      result.size must_=== 1
      result.head.isBookOnRequest must_=== true
    }

    "convert roomentry with non-existing supplierCCInformation should contain bor feature as false as default" in {
      val updatedMeta = meta.withSuppliersCCMapping(
        Set(
          SupplierCCMapping(DMC.BCOM,
                            isGenericCreditCardReq = false,
                            isDomesticCreditCardRequired = false,
                            isBookOnRequest = true),
        ))
      val updatedPo = po.copy(supplyInfo = po.supplyInfo.map(_.copy(supplierId = DMC.YCS)))
      val result = converter.buildPropOfferRoomEntry(updatedPo,
                                                     updatedMeta,
                                                     rc,
                                                     isBcomFixTaxAmountApplyToPB = true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(isApplyYcsRateForking =
        false)
      result.size must_=== 1
      result.head.isBookOnRequest must_=== false
    }

    "convert roomentry properly with correct tracker information having Child RC" in {
      val ctx = aValidYplContext.withRequest(
        aValidYplRequest.copy(isBookingRequest = true).withCheckout(aValidCheckIn.plusDays(2)))
      val parentRcId = 999L
      val rc = aValidPropOfferRoomRateCategory
        .withRoomTypeId(24453)
        .withBenefits(Seq.empty)
        .withChannelRates(Seq(channelRate.withCheckInInformationIdentifier(1)))
        .withCurrencyCode("VND")
      val childChannelRate = channelRate.withParentRateDiscount(ParentRateDiscount(true, -5d, "PRPB")).clearPrices
      val childRc = rc.withParentRateCategoryId(parentRcId).withChannelRates(Seq(childChannelRate))
      val parentRcMap = Map(
        ParentRateCategoryKey(parentRcId, rc.roomTypeId, channelRate.channelId) -> ParentRateCategoryValue(
          rc.withRateCategoryId(parentRcId),
          channelRate))
      val updatedPo = po.withRoomRates(po.roomRates ++ Seq(aValidPropOfferRoomRateCategory.withRateCategoryId(999)))
      val result = converter.buildPropOfferRoomEntry(updatedPo,
                                                     meta,
                                                     childRc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel,
                                                     parentRcMap = parentRcMap)(false)(ctx)
      val roomRc = result.head.rateCategory
      roomRc.rateCategoryId should_== rc.rateCategoryId
      roomRc.parent.get.rateCategoryId should_== parentRcId
      roomRc.dailyPrices.nonEmpty should_== true
      roomRc.isAmount should_== true
      roomRc.value should_== -5d
      roomRc.applyTo should_== "PRPB"
      result.head.roomDataChangeTracker should beSome
      val roomTracker = result.head.roomDataChangeTracker
      roomTracker should beSome
      val perDayPrices = roomTracker.get.perDayPerOccPrice
      perDayPrices.size must_== 4
      perDayPrices.head.rateCategoryId must_== 999
    }

    "convert roomentry properly when wholesale metadata is NOT empty for net rate" in {
      val wholesaleMetaDataByRateFence: Map[YplRateFence, WholesaleMetaByYplChannelSupplierID] = Map(
        YplRateFence("TH", -1, 1) -> Map((YplChannel(1, Set.empty, 0), 1212) -> WholesaleMetadata(Some(7d))),
      )
      implicit val ctx = aValidYplContext
      val metaUpdated = meta.withWholesaleMetaDataByRateFence(wholesaleMetaDataByRateFence)
      val poUpdated = po
        .withCommissions(Map(1 -> Commission(channelId = 1, contractedCommission = 3)))
        .withSupplyInfo(proto.SupplierInfo(supplierId = 1212))
      val result = converter.buildPropOfferRoomEntry(poUpdated,
                                                     metaUpdated,
                                                     rc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)(ctx)
      result.size must_== 1
      val resultRoomType = result.head
      resultRoomType.roomTypeId must_== 111546
      resultRoomType.channel must_== YplMasterChannel(1)
      resultRoomType.cxlCode must_== "7D1N_3D50P_100P"
      resultRoomType.isBreakFastIncluded must_== false
      resultRoomType.remainingRooms must_== 11
      resultRoomType.currency must_== "VND"
      resultRoomType.rateType must_== RateType.NetInclusive
      resultRoomType.processingFees must_== 7
      resultRoomType.isAllowCombinePromotion must_== true
      resultRoomType.dailyPrices must not be empty
      resultRoomType.occEntry must_== pricing.RoomOccupancy(0, 0)
      resultRoomType.availablePromotions must beEmpty
      resultRoomType.channelRateType must beSome(RateType.SellExclusive)
      resultRoomType.paymentModel must beSome(PaymentModel.Agency)
      resultRoomType.paymentOptions must_== PaymentOptionSet.PREPAYMENT
      resultRoomType.dmcDataHolder must beSome(
        DmcDataHolder(
          Some("123|456|00789"),
          Some("uid"),
          Some(
            SupplierRateInfo(
              Some("8132102_93766198_2_1_1"),
              None,
              Seq(
                SupplierRateInfo.ExternalData("a", "123", Some(false)),
                SupplierRateInfo.ExternalData("b", "456", Some(false)),
                SupplierRateInfo.ExternalData(ExternalDataFields.ResellSourceBookingId, "00789", Some(false)),
              ),
              None,
            )),
          None,
        ),
      )
      resultRoomType.hourlyAvailableSlots must beEmpty
      resultRoomType.confirmByMins must beEmpty
      resultRoomType.displayedRackRate must_== 200d
      resultRoomType.rateCategoryInfo must_== Some(YplRateCategoryInfo(7))
      resultRoomType.resellExternalData must beSome(ResellExternalData(sourceBookingId = "00789"))
    }

    "convert roomentry with checkininformation if feature flag is present" in {
      val result = converter.buildPropOfferRoomEntry(
        poWithCheckInfo,
        meta,
        rc,
        true,
        po.taxes,
        po.taxesV2,
        isYCS = true,
        hotelPaymentModel = hotelPaymentModel)(false)(ctxWithRatePlanCheckIn)
      result.size must_== 1
      val resultRoomType = result.head
      val expected = YPLCheckInInformation(
        checkInFrom = Some(LocalTime.of(1, 1, 1)),
        checkInUntil = Some(LocalTime.of(2, 2, 2)),
        checkOutFrom = Some(LocalTime.of(3, 3, 3)),
        checkOutUntil = Some(LocalTime.of(4, 4, 4)),
      )
      resultRoomType.checkInInformation must beSome(expected)
      resultRoomType.rateCategory.checkInInformation must beSome(expected)
    }

    "convert JTBWL roomentry with Dmc Data Holder properly" in {
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     rc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel,
                                                     isJTBWL = true)(false)
      result.size must_== 1
      val resultRoomType = result.head
      resultRoomType.dmcDataHolder.get.supplierExternalDataStr must_== None
      resultRoomType.dmcDataHolder.get.roomUid must_== None
      resultRoomType.dmcDataHolder.get.supplierRateInfo must_== None
      resultRoomType.dmcDataHolder.get.yplExternalData must_== Some(
        YPLExternalData("",
                        "76282_362",
                        Some("8132102_93766198_2_1_1"),
                        Some(""),
                        "VND",
                        0,
                        0,
                        0,
                        1,
                        0.0,
                        Map(),
                        0,
                        None,
                        None,
                        None))
    }

    "convert roomentry with Dmc Data Holder properly based on supplier features when JTBFP-1295=B" in {

      // exp B, supplierFeature doesn't exist for room supplier, returns default false
      val ctxExpB = ctx.withExperimentContext(forceBExperimentContext(YplExperiments.DMC_HARDCODING_REMOVAL))
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     rc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel,
                                                     isJTBWL = true)(false)(ctxExpB.build)
      result.size must_== 1
      val resultRoomType = result.head
      resultRoomType.dmcDataHolder.get.supplierExternalDataStr must_== Some("123|456|00789")
      resultRoomType.dmcDataHolder.get.roomUid must_== Some("uid")
      resultRoomType.dmcDataHolder.get.supplierRateInfo must_!= None
      resultRoomType.dmcDataHolder.get.yplExternalData must_== None

      // exp B, supplierFeature exists for room supplier, feature disabled
      val supplierFeatures = SupplierFeatures(features = Map(
        3038 -> aValidFeature,
      ))
      val ctxWithSupplierFeatureDisabled = ctxExpB.withRequest(
        aValidYplRequest.withSupplierFeature(supplierFeatures),
      )

      val resultWithSupplierFeatureDisabled = converter.buildPropOfferRoomEntry(
        po,
        meta,
        rc,
        true,
        po.taxes,
        po.taxesV2,
        isYCS = true,
        hotelPaymentModel = hotelPaymentModel,
        isJTBWL = true)(false)(ctxWithSupplierFeatureDisabled.build)
      resultWithSupplierFeatureDisabled.size must_== 1
      val resultSupplierFeatureDisabledRoomType = resultWithSupplierFeatureDisabled.head
      resultSupplierFeatureDisabledRoomType.dmcDataHolder.get.supplierExternalDataStr must_== Some("123|456|00789")
      resultSupplierFeatureDisabledRoomType.dmcDataHolder.get.roomUid must_== Some("uid")
      resultSupplierFeatureDisabledRoomType.dmcDataHolder.get.supplierRateInfo must_!= None
      resultSupplierFeatureDisabledRoomType.dmcDataHolder.get.yplExternalData must_== None

      // exp B, supplierFeature exists for room supplier, feature enabled
      val supplierFeaturesEnabled = SupplierFeatures(features = Map(
        3038 -> aValidFeature.copy(buildYplExternalDataEnabled = true),
      ))
      val ctxWithSupplierFeatureEnabled = ctxExpB.withRequest(
        aValidYplRequest.withSupplierFeature(supplierFeaturesEnabled),
      )
      val resultWithSupplierFeatureEnabled = converter.buildPropOfferRoomEntry(
        po,
        meta,
        rc,
        true,
        po.taxes,
        po.taxesV2,
        isYCS = true,
        hotelPaymentModel = hotelPaymentModel,
        isJTBWL = true)(false)(ctxWithSupplierFeatureEnabled)
      resultWithSupplierFeatureEnabled.size must_== 1
      val resultWithSupplierFeatureEnabledRoomType = resultWithSupplierFeatureEnabled.head
      resultWithSupplierFeatureEnabledRoomType.dmcDataHolder.get.supplierExternalDataStr must_== None
      resultWithSupplierFeatureEnabledRoomType.dmcDataHolder.get.roomUid must_== None
      resultWithSupplierFeatureEnabledRoomType.dmcDataHolder.get.supplierRateInfo must_== None
      resultWithSupplierFeatureEnabledRoomType.dmcDataHolder.get.yplExternalData must_!= None
    }

    "convert roomentry with fence rate properly" in {
      val fences = Map(YplMasterChannel(1) -> Set(YplRateFence("TH", 1, 1), YplRateFence("JP", 1, 1)))
      val request = aValidYplRequest.withFences(fences)
      val ctxWithFence = ctx.withRequest(request)

      val result = converter.buildPropOfferRoomEntry(
        po.copy(supplyInfo = po.supplyInfo.map(_.copy(supplierId = DMC.BCOM))),
        meta.withSuppliersCCMapping(
          Set(
            SupplierCCMapping(DMC.BCOM, isGenericCreditCardReq = false, isDomesticCreditCardRequired = false),
          )),
        rc,
        isBcomFixTaxAmountApplyToPB = true,
        po.taxes,
        po.taxesV2,
        isYCS = true,
        hotelPaymentModel = hotelPaymentModel,
      )(isApplyYcsRateForking = false)(ctxWithFence)
      result.size must_=== 1
      result.head.paymentOptions must_=== Set(PaymentOption.NoCreditCard, PaymentOption.PrepaymentRequired)
    }

    "Do not convert rackrate for non ycs" in {
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     rc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      val resultRoomType = result.head
      resultRoomType.displayedRackRate must_== 0d
    }

    "convert rackrate for based on supplier feature when JTBFP-1295=B" in {
      val ctxExpB = ctx.withExperimentContext(forceBExperimentContext(YplExperiments.DMC_HARDCODING_REMOVAL))

      // exp B, supplierFeature doesn't exist for room supplier, returns default false
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     rc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(false)(ctxExpB.build)
      val resultRoomType = result.head
      resultRoomType.displayedRackRate must_== 0d

      // exp B, supplierFeature exist for room supplier, feature is disabled
      val supplierFeatures = SupplierFeatures(features = Map(
        3038 -> aValidFeature,
      ))
      val ctxWithSupplierFeatureDisabled = ctxExpB.withRequest(
        aValidYplRequest.withSupplierFeature(supplierFeatures),
      )
      val resultFeatureDisabled = converter.buildPropOfferRoomEntry(
        po,
        meta,
        rc,
        true,
        po.taxes,
        po.taxesV2,
        isYCS = false,
        hotelPaymentModel = hotelPaymentModel)(false)(ctxWithSupplierFeatureDisabled.build)
      val resultFeatureDIsabledRoomType = resultFeatureDisabled.head
      resultFeatureDIsabledRoomType.displayedRackRate must_== 0d

      // exp B, supplierFeature exist for room supplier, feature is enabled
      val supplierFeaturesEnabled = SupplierFeatures(features = Map(
        3038 -> aValidFeature.copy(displayRackRate = true),
      ))
      val ctxWithSupplierFeatureEnabled = ctxExpB.withRequest(
        aValidYplRequest.withSupplierFeature(supplierFeaturesEnabled),
      )
      val resultFeatureEnabled = converter.buildPropOfferRoomEntry(
        po,
        meta,
        rc,
        true,
        po.taxes,
        po.taxesV2,
        isYCS = false,
        hotelPaymentModel = hotelPaymentModel)(false)(ctxWithSupplierFeatureEnabled.build)
      val resultFeatureEnabledRoomType = resultFeatureEnabled.head
      resultFeatureEnabledRoomType.displayedRackRate must_== 200d
    }

    "convert roomentry with breakfast properly" in {
      val benefitRc = rc.withBenefits(Seq(Benefits.Breakfast.i))
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     benefitRc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      result.size must_== 1
      val resultRoomType = result.head
      resultRoomType.isBreakFastIncluded must_== true
    }

    "convert roomentry with block promotions feature enabled properly" in {
      val ctx = aValidYplContext.withRequest(
        aValidYplRequest.withWhitelabelSetting(
          aValidwhitelabelSetting.copy(blockYCSPromotions = true),
        ))
      val promotionIndex = 1
      val promotionMap = Map((promotionIndex, aValidPropOfferPromotion))
      val updatedPo = po.copy(promotions = promotionMap)

      val result = converter.buildPropOfferRoomEntry(updatedPo,
                                                     meta,
                                                     rc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)(ctx)
      result.size must_== 1
      val resultRoomType = result.head
      resultRoomType.availablePromotions.nonEmpty should_== false
    }

    "Block Promotions Tests" should {
      "validate available promotions based on supplier blocking" in {
        "PromotionsBlockedSuppliers" | "OfferSupplierId" | "ExpectedResult" | "IsExperimentOnBSide" |
          Set(DMC.JTBWL) ! 332 ! false ! true |
          Set() ! 29014 ! true ! true |
          Set(DMC.JTBWL) ! 29014 ! true ! true |
          Set(DMC.JTBWL) ! 29014 ! false ! false |> {
            (blockedSuppliers, offerSupplierId, expectedResult, isExperimentOnBSide) =>
              val mockExperimentContext = mock[com.agoda.utils.flow.ExperimentContext]
              mockExperimentContext.isUserB(
                YplExperiments.BLOCK_PROMOTIONS_FOR_SPECIFIC_SUPPLIERS) returns isExperimentOnBSide
              val ctx = aValidYplContext.withRequest(
                aValidYplRequest.withWhitelabelSetting(
                  aValidwhitelabelSetting.copy(
                    blockYCSPromotions = false,
                    promotionsBlockedSuppliers = blockedSuppliers.toSet,
                    blockPromotionsPerSupplier = true,
                  ),
                ),
              )
              val promotionIndex = 1
              val promotionMap = Map((promotionIndex, aValidPropOfferPromotion))
              val updatedPo = po
                .copy(promotions = promotionMap)
                .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(offerSupplierId))

              val result = converter.buildPropOfferRoomEntry(
                updatedPo,
                meta,
                rc,
                true,
                po.taxes,
                po.taxesV2,
                isYCS = true,
                hotelPaymentModel = hotelPaymentModel,
              )(false)(ctx.withExperimentContext(mockExperimentContext))

              result.size must_== 1
              val resultRoomType = result.head
              resultRoomType.availablePromotions.isEmpty should_== expectedResult
          }
      }
    }

    "convert roomentry with block promotions feature disabled properly" in {
      val ctx = aValidYplContext.withRequest(
        aValidYplRequest.withWhitelabelSetting(
          aValidwhitelabelSetting.copy(blockYCSPromotions = false),
        ))
      val promotionIndex = 1
      val promotionMap = Map((promotionIndex, aValidPropOfferPromotion))
      val updatedPo = po.copy(promotions = promotionMap)

      val result = converter.buildPropOfferRoomEntry(updatedPo,
                                                     meta,
                                                     rc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)(ctx)
      result.size must_== 1
      val resultRoomType = result.head
      resultRoomType.availablePromotions.nonEmpty should_== true
    }

    "convert roomentry with parent RC properly" in {
      // parentRcMap: Map[ParentRateCategoryKey, ParentRateCategoryValue] = Map.empty
      val parentRcId = 999L
      val childChannelRate = channelRate.withParentRateDiscount(ParentRateDiscount(true, -5d, "PRPB")).clearPrices
      val childRc = rc.withParentRateCategoryId(parentRcId).withChannelRates(Seq(childChannelRate))
      val parentRcMap = Map(
        ParentRateCategoryKey(parentRcId, rc.roomTypeId, channelRate.channelId) -> ParentRateCategoryValue(
          rc.withRateCategoryId(parentRcId),
          channelRate))
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     childRc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel,
                                                     parentRcMap = parentRcMap)(false)
      val roomRc = result.head.rateCategory
      roomRc.rateCategoryId should_== rc.rateCategoryId
      roomRc.parent.get.rateCategoryId should_== parentRcId
      roomRc.dailyPrices.nonEmpty should_== true
      roomRc.isAmount should_== true
      roomRc.value should_== -5d
      roomRc.applyTo should_== "PRPB"
    }

    "convert YCS roomentry with fall back parent RC properly" in {
      val parentRcId = 999L
      val childChannelRate = channelRate.withParentRateDiscount(ParentRateDiscount(true, -5d, "PRPB")).clearPrices
      val childRc = rc.withParentRateCategoryId(parentRcId).withChannelRates(Seq(childChannelRate))
      val parentRcMap = Map(
        ParentRateCategoryKey(parentRcId, rc.roomTypeId, 14) -> ParentRateCategoryValue(rc.withRateCategoryId(parentRcId),
                                                                                        channelRate))
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     childRc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel,
                                                     parentRcMap = parentRcMap)(false)

      val roomRc = result.head.rateCategory
      roomRc.rateCategoryId should_== rc.rateCategoryId
      roomRc.parent.get.rateCategoryId should_== parentRcId
      roomRc.dailyPrices.nonEmpty should_== true
      roomRc.isAmount should_== true
      roomRc.value should_== -5d
      roomRc.applyTo should_== "PRPB"
    }

    "do Not fallback NON YCS roomentry parent RC properly" in {
      val parentRcId = 999L
      val childChannelRate = channelRate.withParentRateDiscount(ParentRateDiscount(true, -5d, "PRPB")).clearPrices
      val childRc = rc.withParentRateCategoryId(parentRcId).withChannelRates(Seq(childChannelRate))
      val parentRcMap = Map(
        ParentRateCategoryKey(parentRcId, rc.roomTypeId, 14) -> ParentRateCategoryValue(rc.withRateCategoryId(parentRcId),
                                                                                        channelRate))
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     childRc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel,
                                                     parentRcMap = parentRcMap)(false)
      val roomRc = result.head.rateCategory
      roomRc.rateCategoryId should_== rc.rateCategoryId
      roomRc.parent.isEmpty should_== true
      roomRc.dailyPrices.nonEmpty should_== false
      roomRc.isAmount should_== true
      roomRc.value should_== -5d
      roomRc.applyTo should_== "PRPB"
    }

    "use netexc as default channel ratetype if unknown" in {
      val channelRateOverride = channelRate.withChannelDiscountRateLoadType(PriceType.PRICE_TYPE_UNSPECIFIED)
      val rcOverride = rc.withChannelRates(Seq(channelRateOverride))
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     rcOverride,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      result.size must_== 1
      val resultRoomType = result.head
      resultRoomType.channelRateType must beSome(RateType.NetExclusive)
    }

    "filter out invalid ratecategory properly" in {
      val invalidChannelRate = channelRate.withRemainingRooms(0)
      val overriddenRc = rc.withChannelRates(Seq(invalidChannelRate))
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     overriddenRc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      result must beEmpty
    }

    "Convert fields for JTBWL" in {
      val overriddenRc = rc
        .withChannelRates(Seq(aValidPropOfferChannelRate.withCancelationPolicy(
          aValidPropOfferCancellationPolicy.withCancelChargeType(CancelChargeType.FirstNightCharge))))
        .withInventoryType(com.agoda.supply.calc.proto.InventoryType.OTAJapanican)
      val result = converter
        .buildPropOfferRoomEntry(po,
                                 meta,
                                 overriddenRc,
                                 true,
                                 po.taxes,
                                 po.taxesV2,
                                 isYCS = true,
                                 hotelPaymentModel = hotelPaymentModel)(false)
        .head
      result.inventoryType must_== InventoryType.JtbOTAJapanican
      result.rateCategory.inventoryType must_== InventoryType.JtbOTAJapanican
      result.cxlChargeSetting must_== Some(CancellationChargeSettingType.FirstNight)
    }

    "return correct adult/child occupancy for Pull suppliers based on PropertyOffer information" in {
      val modifiedPo = po
        .withOccupancy(Occupancy(2, 1))
        .withOccupancyModel(FullPatternLengthOfStay)
        .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplySource(SupplySourceType.Pull))

      implicit val ctx = aValidYplContext.withRequest(
        aValidYplRequest.withOccupancyInfo(YplOccInfo(Some(2), Some(YplChildren(List(Some(7)))), Some(1))))
      val result = converter
        .buildPropOfferRoomEntry(modifiedPo,
                                 meta,
                                 rc,
                                 true,
                                 po.taxes,
                                 po.taxesV2,
                                 isYCS = true,
                                 hotelPaymentModel = hotelPaymentModel)(false)(ctx)
        .head

      result.occEntry.adults must_== (2)
      result.occEntry.children must_== (1)
    }

    "return correct adult/child occupancy for Pull suppliers based on PropertyOffer information with multi-rooms" in {
      val modifiedPo = po
        .withOccupancy(Occupancy(2, 1))
        .withOccupancyModel(FullPatternLengthOfStay)
        .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplySource(SupplySourceType.Pull))

      implicit val ctx = aValidYplContext.withRequest(
        aValidYplRequest.withOccupancyInfo(YplOccInfo(Some(2), Some(YplChildren(List(Some(7)))), Some(2))))
      val result = converter
        .buildPropOfferRoomEntry(modifiedPo,
                                 meta,
                                 rc,
                                 true,
                                 po.taxes,
                                 po.taxesV2,
                                 isYCS = true,
                                 hotelPaymentModel = hotelPaymentModel)(false)(ctx)
        .head

      result.occEntry.adults must_== (1)
      result.occEntry.children must_== (1)
    }

    "return correct adult/child occupancy for Pull suppliers based on PropertyOffer information with multi-rooms" in {
      val modifiedPo = po
        .withOccupancy(Occupancy(2, 1))
        .withOccupancyModel(FullPatternLengthOfStay)
        .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplySource(SupplySourceType.Pull))

      implicit val ctx = aValidYplContext.withRequest(
        aValidYplRequest.withOccupancyInfo(YplOccInfo(Some(2), Some(YplChildren(List(Some(7)))), Some(2))))
      val result = converter
        .buildPropOfferRoomEntry(modifiedPo,
                                 meta,
                                 rc,
                                 true,
                                 po.taxes,
                                 po.taxesV2,
                                 isYCS = true,
                                 hotelPaymentModel = hotelPaymentModel)(false)(ctx)
        .head

      result.occEntry.adults must_== (1)
      result.occEntry.children must_== (1)
      result.occEntry.allowedFreeChildrenAndInfants must_== (1)
    }

    "return fences when request is fenced" in {
      val fences = Map(YplMasterChannel.RTL -> Set(YplRateFence("TH", 1, 1)))
      val req = aValidYplRequest.withFences(fences)
      val ctx = aValidYplContext.withRequest(req).build
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     rc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)(ctx)
      result.size must_== 1
      result.head.fences must_== Set(YplRateFence("TH", 1, 1))
    }

    "split fences rooms based on agx commission for daily prices" in {
      val thFence = YplRateFence("TH", 1, 1)
      val hkFence = YplRateFence("HK", 2, 1)
      val fences = Map(YplMasterChannel.RTL -> Set(thFence, hkFence))
      val req = aValidYplRequest
        .withFences(fences)
        .withFencedAgxCommissions(new FencedAgxCommission(Map(
          hkFence -> Map(
            6011203L -> YplAGXCommissionAdjustment(allDateAdjust = Some(YplAGXCommission(payAsYouGoCommission = 3)))),
          thFence -> Map(
            6011203L -> YplAGXCommissionAdjustment(allDateAdjust = Some(YplAGXCommission(payAsYouGoCommission = 5)))),
        )))
      val ctx = aValidYplContext.withRequest(req).build

      val poWithCommissions = po
        .withCommissions(Map(1 -> Commission(contractedCommission = 1d)))
        .withSupplyInfo(com.agoda.supply.calc.proto.SupplierInfo(DMC.YCS))

      val result = converter.buildPropOfferRoomEntry(poWithCommissions,
                                                     meta,
                                                     rc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)(ctx)
      result.size must_== 2
      result.exists(r =>
        r.fences == Set(hkFence) && r.commissionHolder.daily
          .exists(_._2.agxCommissionHolder.payAsYouGoCommission == 3d)) must_== true
      result.exists(r =>
        r.fences == Set(thFence) && r.commissionHolder.daily
          .exists(_._2.agxCommissionHolder.payAsYouGoCommission == 5d)) must_== true
    }

    "return empty room when request is fenced, customer segment condition is not passing" in {
      val fences = Map(YplMasterChannel.RTL -> Set(YplRateFence("TH", 1, 1)))
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1).withIsNationalityRate(false)
      val cInfo = aValidClientInfo.withVipLevel(Some(com.agoda.papi.ypl.models.enums.VipLevelType.GOLD))
      val segments =
        Seq(aValidOfferCustomerSegment.withCountryCode("US").withLanguageId(0).withVipLevel(VipLevelType.PLATINUM))
      val restrictions = Map(
        1 -> aValidOfferBookingRestriction
          .withCustomerSegments(segments)
          .withMinAdvancePurchase(10)
          .withMaxAdvancePurchase(15))
      val req = aValidYplRequest
        .withFences(fences)
        .withClientInfo(cInfo)
        .withCheckIn(aValidCheckIn)
        .withBookingDate(aValidCheckIn.minusDays(12))
      val ctx = aValidYplContext.withExperimentContext(forceAllBExperimentsContext()).build.withRequest(req).build
      val rc = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo
      val po = aValidPropertyOffer.withBookingRestrictions(restrictions).withRoomRates(Seq(rc))
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     rc,
                                                     true,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = true,
                                                     hotelPaymentModel = hotelPaymentModel)(false)(ctx)
      result.size must_== 0
    }

    "convert roomentry with uspaEnabled properly" in {
      val mockExperimentContext = mock[com.agoda.utils.flow.ExperimentContext]
      val checkInFrom = new DateTime(2015, 10, 5, 0, 0)
      val checkInTo = new DateTime(2015, 10, 20, 0, 0)
      val bookStart = new DateTime(2015, 10, 1, 0, 0)
      val bookEnd = new DateTime(2015, 10, 10, 0, 0)
      val eligibleCampaign = UspaCampaign(
        1,
        1,
        "Campaign 1",
        RateType.NetExclusive,
        20.0,
        5.0,
        BenchmarkingCriteria(CxlPolicyMatchStrategy.Exact, OccupancyMatchStrategy.Exact, BreakfastMatchStrategy.Exact),
        AdjustableRateCriteria(
          Some(
            RateCriteria(Some(List(DMC.BCOM)),
                         Some(List(1, 4)),
                         checkinFrom = Some(checkInFrom),
                         checkinTo = Some(checkInTo),
                         bookStart = Some(bookStart),
                         bookEnd = Some(bookEnd)))),
      )
      val ctxWithUspa =
        ctx.withExperimentContext(mockExperimentContext).withEligibleUspaCampaigns(List(eligibleCampaign))

      val result = converter.buildPropOfferRoomEntry(
        po.copy(supplyInfo = po.supplyInfo.map(_.copy(supplierId = DMC.BCOM))),
        meta.withSuppliersCCMapping(
          Set(
            SupplierCCMapping(DMC.BCOM, isGenericCreditCardReq = false, isDomesticCreditCardRequired = false),
          )),
        rc,
        isBcomFixTaxAmountApplyToPB = true,
        po.taxes,
        po.taxesV2,
        isYCS = true,
        hotelPaymentModel = hotelPaymentModel,
      )(isApplyYcsRateForking = false)(ctxWithUspa)
      result.head.rateCategory.dailyPrices.values.flatMap { dailyPrice =>
        dailyPrice.prices.map { price =>
          price.maxUspaDiscount != 0.0
        }
      }.toSet must_=== Set(true)
    }

    "convert roomentry properly with uspaData but uspaExperiment disabled" in {
      val mockExperimentContext = mock[com.agoda.utils.flow.ExperimentContext]
      val checkInFrom = new DateTime(2015, 10, 5, 0, 0)
      val checkInTo = new DateTime(2015, 10, 20, 0, 0)
      val bookStart = new DateTime(2015, 10, 1, 0, 0)
      val bookEnd = new DateTime(2015, 10, 10, 0, 0)
      val eligibleCampaign = UspaCampaign(
        1,
        1,
        "Campaign 1",
        RateType.NetExclusive,
        20.0,
        5.0,
        BenchmarkingCriteria(CxlPolicyMatchStrategy.Exact, OccupancyMatchStrategy.Exact, BreakfastMatchStrategy.Exact),
        AdjustableRateCriteria(
          Some(
            RateCriteria(Some(List(332)),
                         Some(List(1, 4)),
                         checkinFrom = Some(checkInFrom),
                         checkinTo = Some(checkInTo),
                         bookStart = Some(bookStart),
                         bookEnd = Some(bookEnd)))),
      )

      val ctxWithUspa =
        ctx.withExperimentContext(mockExperimentContext).withEligibleUspaCampaigns(List(eligibleCampaign))

      val result = converter.buildPropOfferRoomEntry(
        po.copy(supplyInfo = po.supplyInfo.map(_.copy(supplierId = DMC.BCOM))),
        meta.withSuppliersCCMapping(
          Set(
            SupplierCCMapping(DMC.BCOM, isGenericCreditCardReq = false, isDomesticCreditCardRequired = false),
          )),
        rc,
        isBcomFixTaxAmountApplyToPB = true,
        po.taxes,
        po.taxesV2,
        isYCS = true,
        hotelPaymentModel = hotelPaymentModel,
      )(isApplyYcsRateForking = false)(ctxWithUspa)
      result.head.rateCategory.dailyPrices.values.flatMap { dailyPrice =>
        dailyPrice.prices.map { price =>
          price.maxUspaDiscount != 0.0
        }
      }.toSet must_=== Set(false)
    }

    "convert roomentry with uspaEnabled enabled properly for mismatch supplier" in {
      val mockExperimentContext = mock[com.agoda.utils.flow.ExperimentContext]
      val checkInFrom = new DateTime(2015, 10, 5, 0, 0)
      val checkInTo = new DateTime(2015, 10, 20, 0, 0)
      val bookStart = new DateTime(2015, 10, 1, 0, 0)
      val bookEnd = new DateTime(2015, 10, 10, 0, 0)
      val eligibleCampaign = UspaCampaign(
        1,
        1,
        "Campaign 1",
        RateType.NetExclusive,
        20.0,
        5.0,
        BenchmarkingCriteria(CxlPolicyMatchStrategy.Exact, OccupancyMatchStrategy.Exact, BreakfastMatchStrategy.Exact),
        adjustableRateCriteria = AdjustableRateCriteria(
          Some(
            RateCriteria(Some(List(DMC.YCS)),
                         Some(List(1, 4)),
                         checkinFrom = Some(checkInFrom),
                         checkinTo = Some(checkInTo),
                         bookStart = Some(bookStart),
                         bookEnd = Some(bookEnd)))),
      )

      val ctxWithUspa =
        ctx.withExperimentContext(mockExperimentContext).withEligibleUspaCampaigns(List(eligibleCampaign))

      val result = converter.buildPropOfferRoomEntry(
        po.copy(supplyInfo = po.supplyInfo.map(_.copy(supplierId = DMC.BCOM))),
        meta.withSuppliersCCMapping(
          Set(
            SupplierCCMapping(DMC.BCOM, isGenericCreditCardReq = false, isDomesticCreditCardRequired = false),
          )),
        rc,
        isBcomFixTaxAmountApplyToPB = true,
        po.taxes,
        po.taxesV2,
        isYCS = true,
        hotelPaymentModel = hotelPaymentModel,
      )(isApplyYcsRateForking = false)(ctxWithUspa)
      result.head.rateCategory.dailyPrices.values.flatMap { dailyPrice =>
        dailyPrice.prices.map { price =>
          price.maxUspaDiscount == 0.0
        }
      }.toSet must_=== Set(true)
    }
  }

  "buildDailyPrices" should {

    "convert daily prices properly" in {
      val checkIn = aValidCheckIn
      val los = 2
      val taxLookUp: TaxPerDayLookUp = Map(checkIn -> Map((1, 0) -> aValidTax.withTaxId(1)),
                                           checkIn.plusDays(1) -> Map((1, 0) -> aValidTax.withTaxId(1)))
      val surcharges = List(aValidSurchargeEntry.withDates(Set(checkIn)).withIsPropOffer(true).build)
      val channelRate = aValidPropOfferChannelRate
        .withPrices(Seq(
          aValidPropOfferPriceDaily
            .withStayDates(Seq(NumericRange(0, 0)))
            .withOccupancyPrices(Seq(
              aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(30),
              aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(2, 2))).withAmount(50),
            )),
          aValidPropOfferPriceDaily
            .withStayDates(Seq(NumericRange(1, 1)))
            .withOccupancyPrices(Seq(
              aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(20),
              aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(2, 2))).withAmount(30),
            )),
        ))
        .withChannelDiscountPerDay(Map(0 -> 10, 1 -> 12))
      val reqOcc = aValidReqOcc
      implicit val ctx =
        aValidYplContext.withRequest(aValidYplRequest.withCheckIn(checkIn).withCheckout(checkIn.plusDays(los))).build

      val expectedSurchargeList =
        List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(checkIn), false, true, 20.0, 0, true))
      val expectedDailyPrice1st = Map(
        checkIn -> DailyPrice(
          checkIn,
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              50,
              1,
              0.0,
              None,
              Map(),
              0.0,
              50,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(10),
          expectedSurchargeList,
        ),
      )
      val expectedDailyPrice2nd = Map(
        checkIn.plusDays(1) -> DailyPrice(
          checkIn.plusDays(1),
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn.plusDays(1),
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              20,
              1,
              0.0,
              None,
              Map(),
              0.0,
              20,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn.plusDays(1),
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(12),
          List(),
        ),
      )

      val commissionHolder = aValidCommissionHolder.withDaily(
        Map(checkIn -> CommissionDailyHolder.default, checkIn.plusDays(1) -> CommissionDailyHolder.default))
      "isYCS = false" in {
        val result = converter.buildDailyPrices(
          checkIn,
          aValidPropOfferRoomRateCategory.roomTypeId,
          aValidPropOfferRoomRateCategory.rateCategoryId,
          taxLookUp,
          surcharges,
          channelRate,
          reqOcc,
          Seq.empty,
          HmcMasterHotelContext(),
          commissionHolder = commissionHolder,
        )
        result must_== expectedDailyPrice1st ++ expectedDailyPrice2nd
      }

      "isYCS = true" in {
        val result = converter.buildDailyPrices(
          checkIn,
          aValidPropOfferRoomRateCategory.roomTypeId,
          aValidPropOfferRoomRateCategory.rateCategoryId,
          taxLookUp,
          surcharges,
          channelRate,
          reqOcc,
          Seq.empty,
          HmcMasterHotelContext(),
          commissionHolder = commissionHolder,
        )
        val expectedDailyPrice = expectedDailyPrice1st.map { case (k, v) =>
          k -> v.copy(rpmSurcharges = expectedSurchargeList)
        } ++ expectedDailyPrice2nd
        result must_== expectedDailyPrice
      }
    }

    "convert daily prices with supplier commission for bcom return bcom default commission and BSUP-973 A" in {
      val checkIn = aValidCheckIn
      val los = 1
      val taxLookUp: TaxPerDayLookUp = Map(checkIn -> Map((1, 0) -> aValidTax.withTaxId(1)))
      val surcharges = List(aValidSurchargeEntry.withDates(Set(checkIn)).withIsPropOffer(true).build)
      val channelRate = aValidPropOfferChannelRate
        .withPrices(
          Seq(
            aValidPropOfferPriceDaily
              .withStayDates(Seq(NumericRange(0, 0)))
              .withOccupancyPrices(Seq(
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(30),
                aValidPropOfferOccPrice
                  .withOccupancies(Seq(NumericRange(2, 2)))
                  .withAmount(50)
                  .withSurcharges(Identifiers(Seq(2))),
              )),
          ))
        .withChannelDiscountPerDay(Map(0 -> 10))
        .withSurchargePerDay(Map(0 -> Identifiers(Seq(1))))
      val commissionHolder = aValidCommissionHolder.withDaily(Map(checkIn -> CommissionDailyHolder.default))
      val reqOcc = aValidReqOcc
      implicit val ctx =
        aValidYplContext.withRequest(aValidYplRequest.withCheckIn(checkIn).withCheckout(checkIn.plusDays(los))).build

      val result = converter.buildDailyPrices(
        checkIn,
        aValidPropOfferRoomRateCategory.roomTypeId,
        aValidPropOfferRoomRateCategory.rateCategoryId,
        taxLookUp,
        surcharges,
        channelRate,
        reqOcc,
        Seq.empty,
        HmcMasterHotelContext(),
        commissionHolder = commissionHolder,
      )

      result must_== Map(
        checkIn -> DailyPrice(
          checkIn,
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              50,
              1,
              0.0,
              None,
              Map(),
              0.0,
              50,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(10),
          List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(checkIn), false, true, 20.0, 0, true)),
        ),
      )
    }

    "convert daily prices with supplier commission for bcom fallback to commission from bcom if no commission available and BSUP-973-B " in {
      val checkIn = aValidCheckIn
      val los = 1
      val taxLookUp: TaxPerDayLookUp = Map(checkIn -> Map((1, 0) -> aValidTax.withTaxId(1)))
      val surcharges = List(aValidSurchargeEntry.withDates(Set(checkIn)).withIsPropOffer(true).build)
      val channelRate = aValidPropOfferChannelRate
        .withPrices(
          Seq(
            aValidPropOfferPriceDaily
              .withStayDates(Seq(NumericRange(0, 0)))
              .withOccupancyPrices(Seq(
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(30),
                aValidPropOfferOccPrice
                  .withOccupancies(Seq(NumericRange(2, 2)))
                  .withAmount(50)
                  .withSurcharges(Identifiers(Seq(2))),
              )),
          ))
        .withChannelDiscountPerDay(Map(0 -> 10))
        .withSurchargePerDay(Map(0 -> Identifiers(Seq(1))))
      val commissionHolder = aValidCommissionHolder.withDaily(Map(checkIn -> CommissionDailyHolder.default))
      val reqOcc = aValidReqOcc
      implicit val ctx = aValidYplContext
        .withExperimentContext(forceBExperimentContext(YplExperiments.BCOM_RATE_COMMISSION))
        .withRequest(aValidYplRequest.withCheckIn(checkIn).withCheckout(checkIn.plusDays(los)))
        .build

      val result = converter.buildDailyPrices(
        checkIn,
        aValidPropOfferRoomRateCategory.roomTypeId,
        aValidPropOfferRoomRateCategory.rateCategoryId,
        taxLookUp,
        surcharges,
        channelRate,
        reqOcc,
        Seq.empty,
        HmcMasterHotelContext(),
        commissionHolder = commissionHolder,
      )

      result must_== Map(
        checkIn -> DailyPrice(
          checkIn,
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              50,
              1,
              0.0,
              None,
              Map(),
              0.0,
              50,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(10),
          List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(checkIn), false, true, 20.0, 0, true)),
        ),
      )
    }

    "convert daily prices with supplier commission for bcom fallback to commission from bcom if no commission available and BSUP-973-B " in {
      val checkIn = aValidCheckIn
      val los = 1
      val taxLookUp: TaxPerDayLookUp = Map(checkIn -> Map((1, 0) -> aValidTax.withTaxId(1)))
      val surcharges = List(aValidSurchargeEntry.withDates(Set(checkIn)).withIsPropOffer(true).build)
      val channelRate = aValidPropOfferChannelRate
        .withPrices(
          Seq(
            aValidPropOfferPriceDaily
              .withStayDates(Seq(NumericRange(0, 0)))
              .withOccupancyPrices(Seq(
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(30),
                aValidPropOfferOccPrice
                  .withOccupancies(Seq(NumericRange(2, 2)))
                  .withAmount(50)
                  .withSurcharges(Identifiers(Seq(2))),
              )),
          ))
        .withChannelDiscountPerDay(Map(0 -> 10))
        .withSurchargePerDay(Map(0 -> Identifiers(Seq(1))))
      val commissionHolder = aValidCommissionHolder.withDaily(Map(checkIn -> CommissionDailyHolder.default))
      val reqOcc = aValidReqOcc
      implicit val ctx = aValidYplContext
        .withExperimentContext(forceBExperimentContext(YplExperiments.BCOM_RATE_COMMISSION))
        .withRequest(aValidYplRequest.withCheckIn(checkIn).withCheckout(checkIn.plusDays(los)))
        .build

      val result = converter.buildDailyPrices(
        checkIn,
        aValidPropOfferRoomRateCategory.roomTypeId,
        aValidPropOfferRoomRateCategory.rateCategoryId,
        taxLookUp,
        surcharges,
        channelRate,
        reqOcc,
        Seq.empty,
        HmcMasterHotelContext(),
        commissionHolder = commissionHolder,
      )

      result must_== Map(
        checkIn -> DailyPrice(
          checkIn,
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              50,
              1,
              0.0,
              None,
              Map(),
              0.0,
              50,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(10),
          List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(checkIn), false, true, 20.0, 0, true)),
        ),
      )
    }

    "convert daily prices when commission per occ is set" in {
      val checkIn = aValidCheckIn
      val los = 1
      val taxLookUp: TaxPerDayLookUp = Map(checkIn -> Map((1, 0) -> aValidTax.withTaxId(1)))
      val surcharges = List(aValidSurchargeEntry.withDates(Set(checkIn)).withIsPropOffer(true).build)
      val channelRate = aValidPropOfferChannelRate
        .withPrices(
          Seq(
            aValidPropOfferPriceDaily
              .withStayDates(Seq(NumericRange(0, 0)))
              .withOccupancyPrices(Seq(
                aValidPropOfferOccPrice
                  .withOccupancies(Seq(NumericRange(1, 1)))
                  .withAmount(30)
                  .withCommissions(Identifiers(Seq(1))),
                aValidPropOfferOccPrice
                  .withOccupancies(Seq(NumericRange(2, 2)))
                  .withAmount(50)
                  .withCommissions(Identifiers(Seq(2)))
                  .withSurcharges(Identifiers(Seq(2))),
              )),
          ))
        .withChannelDiscountPerDay(Map(0 -> 10))
        .withSurchargePerDay(Map(0 -> Identifiers(Seq(1))))
      val reqOcc = aValidReqOcc
      val commissionHolder = aValidCommissionHolder.withDaily(Map(checkIn -> CommissionDailyHolder.default))
      implicit val ctx =
        aValidYplContext.withRequest(aValidYplRequest.withCheckIn(checkIn).withCheckout(checkIn.plusDays(los))).build

      val result = converter.buildDailyPrices(
        checkIn,
        aValidPropOfferRoomRateCategory.roomTypeId,
        aValidPropOfferRoomRateCategory.rateCategoryId,
        taxLookUp,
        surcharges,
        channelRate,
        reqOcc,
        Seq.empty,
        HmcMasterHotelContext(),
        commissionHolder = commissionHolder,
      )

      result must_== Map(
        checkIn -> DailyPrice(
          checkIn,
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              50,
              1,
              0.0,
              None,
              Map(),
              0.0,
              50,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(10),
          List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(checkIn), false, true, 20.0, 0, true)),
        ),
      )
    }

    "convert daily prices with child rate daily" in {
      val checkIn = aValidCheckIn
      val los = 2
      val taxLookUp: TaxPerDayLookUp = Map(checkIn -> Map((1, 0) -> aValidTax.withTaxId(1)),
                                           checkIn.plusDays(1) -> Map((1, 0) -> aValidTax.withTaxId(1)))
      val surcharges = List(aValidSurchargeEntry.withDates(Set(checkIn)).withIsPropOffer(true).build)
      val channelRate = aValidPropOfferChannelRate
        .withPrices(
          Seq(
            aValidPropOfferPriceDaily
              .withStayDates(Seq(NumericRange(0, 0)))
              .withOccupancyPrices(Seq(
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(30),
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(2, 2))).withAmount(50),
              )),
            aValidPropOfferPriceDaily
              .withStayDates(Seq(NumericRange(1, 1)))
              .withOccupancyPrices(Seq(
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(20),
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(2, 2))).withAmount(30),
              )),
          ),
        )
        .withChildAgeRangeRatesDaily(
          Seq(
            aValidChildAgeRangeRateDaily
              .withStayDates(Seq(NumericRange(0, 0)))
              .withChildAgeRangeRates(Seq(
                aValidChildAgeRangeRate.withAgeRangeId(1).withPrice(5.5).withPricingTypeId(1),
                aValidChildAgeRangeRate.withAgeRangeId(2).withPrice(6.5).withPricingTypeId(1),
              )),
            aValidChildAgeRangeRateDaily
              .withStayDates(Seq(NumericRange(2, 2)))
              .withChildAgeRangeRates(Seq(
                aValidChildAgeRangeRate.withAgeRangeId(1).withPrice(7.5).withPricingTypeId(1),
                aValidChildAgeRangeRate.withAgeRangeId(2).withPrice(8.5).withPricingTypeId(1),
              )),
          ),
        )
        .withChannelDiscountPerDay(Map(0 -> 10, 1 -> 12))
      val commissionHolder = aValidCommissionHolder.withDaily(
        Map(checkIn -> CommissionDailyHolder.default, checkIn.plusDays(1) -> CommissionDailyHolder.default))
      val reqOcc = aValidReqOcc.withChildrenAges(List(5)).withAgePolicy(aValidAgePolicy.withChildMaxAge(18))
      val childAgeRanges = Seq(MetaChildAgeRange(1, 6, 12, ChildAgeRangeType.NormalRange),
                               MetaChildAgeRange(3, 13, 18, ChildAgeRangeType.NormalRange))

      implicit val ctx =
        aValidYplContext.withRequest(aValidYplRequest.withCheckIn(checkIn).withCheckout(checkIn.plusDays(los))).build

      val expectedSurchargeList =
        List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(checkIn), false, true, 20.0, 0, true))
      val expectedDailyPrice1st = Map(
        checkIn -> DailyPrice(
          checkIn,
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              50,
              1,
              0.0,
              None,
              Map(),
              0.0,
              50,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(10),
          expectedSurchargeList,
          List(ChildPriceEntry(Some(5.5), PricingChildRateType.FlatPrice, 1, 6, 12),
               ChildPriceEntry(None, PricingChildRateType.FlatPrice, 3, 13, 18)),
        ),
      )
      val expectedDailyPrice2nd = Map(
        checkIn.plusDays(1) -> DailyPrice(
          checkIn.plusDays(1),
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn.plusDays(1),
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              20,
              1,
              0.0,
              None,
              Map(),
              0.0,
              20,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn.plusDays(1),
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(12),
          List(),
        ),
      )

      val result = converter.buildDailyPrices(
        checkIn,
        aValidPropOfferRoomRateCategory.roomTypeId,
        aValidPropOfferRoomRateCategory.rateCategoryId,
        taxLookUp,
        surcharges,
        channelRate,
        reqOcc,
        childAgeRanges,
        HmcMasterHotelContext(),
        commissionHolder = commissionHolder,
      )
      val expectedDailyPrice = expectedDailyPrice1st.map { case (k, v) =>
        k -> v.copy(rpmSurcharges = expectedSurchargeList)
      } ++ expectedDailyPrice2nd
      result must_== expectedDailyPrice
    }

    "convert daily prices with jp child rate" in {
      val checkIn = aValidCheckIn
      val los = 2
      val taxLookUp: TaxPerDayLookUp = Map(checkIn -> Map((1, 0) -> aValidTax.withTaxId(1)),
                                           checkIn.plusDays(1) -> Map((1, 0) -> aValidTax.withTaxId(1)))
      val surcharges = List(aValidSurchargeEntry.withDates(Set(checkIn)).withIsPropOffer(true).build)
      val childPricing = Seq(ChildPricing(1, 1, true, 100),
                             ChildPricing(2, 1, true, 100),
                             ChildPricing(3, 1, true, 100),
                             ChildPricing(4, 1, true, 100))
      val childRates = ChildRate(isChildRateEnabled = true, childPricing)
      val channelRate = aValidPropOfferChannelRate
        .withPrices(
          Seq(
            aValidPropOfferPriceDaily
              .withStayDates(Seq(NumericRange(0, 0)))
              .withOccupancyPrices(Seq(
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(30),
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(2, 2))).withAmount(50),
              )),
            aValidPropOfferPriceDaily
              .withStayDates(Seq(NumericRange(1, 1)))
              .withOccupancyPrices(Seq(
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(20),
                aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(2, 2))).withAmount(30),
              )),
          ),
        )
        .withChildAgeRangeRatesDaily(
          Seq(
            aValidChildAgeRangeRateDaily
              .withStayDates(Seq(NumericRange(0, 0)))
              .withChildAgeRangeRates(Seq(
                aValidChildAgeRangeRate.withAgeRangeId(1).withPrice(5.5).withPricingTypeId(1),
                aValidChildAgeRangeRate.withAgeRangeId(2).withPrice(6.5).withPricingTypeId(1),
              )),
            aValidChildAgeRangeRateDaily
              .withStayDates(Seq(NumericRange(2, 2)))
              .withChildAgeRangeRates(Seq(
                aValidChildAgeRangeRate.withAgeRangeId(1).withPrice(7.5).withPricingTypeId(1),
                aValidChildAgeRangeRate.withAgeRangeId(2).withPrice(8.5).withPricingTypeId(1),
              )),
          ),
        )
        .withChannelDiscountPerDay(Map(0 -> 10, 1 -> 12))
        .withChildRate(childRates)
      val reqOcc = aValidReqOcc.withChildrenAges(List(5)).withAgePolicy(aValidAgePolicy.withChildMaxAge(18))
      val commissionHolder = aValidCommissionHolder.withDaily(
        Map(checkIn -> CommissionDailyHolder.default, checkIn.plusDays(1) -> CommissionDailyHolder.default))
      val childAgeRanges = Seq(
        MetaChildAgeRange(1, 6, 12, ChildAgeRangeType.NormalRange),
        MetaChildAgeRange(3, 13, 18, ChildAgeRangeType.NormalRange),
        MetaChildAgeRange(1, 9, 12, ChildAgeRangeType.ChildRate),
        MetaChildAgeRange(2, 6, 8, ChildAgeRangeType.ChildRate),
        MetaChildAgeRange(3, 3, 5, ChildAgeRangeType.ChildRate),
        MetaChildAgeRange(4, 0, 2, ChildAgeRangeType.ChildRate),
      )

      implicit val ctx =
        aValidYplContext.withRequest(aValidYplRequest.withCheckIn(checkIn).withCheckout(checkIn.plusDays(los))).build

      val expectedSurchargeList =
        List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(checkIn), false, true, 20.0, 0, true))
      val expectedDailyPrice1st = Map(
        checkIn -> DailyPrice(
          checkIn,
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              50,
              1,
              0.0,
              None,
              Map(),
              0.0,
              50,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(10),
          expectedSurchargeList,
          List(
            ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 4, 0, 2, true, ChildAgeRangeType.ChildRate),
            ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 3, 3, 5, true, ChildAgeRangeType.ChildRate),
            ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 2, 6, 8, true, ChildAgeRangeType.ChildRate),
            ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 1, 9, 12, true, ChildAgeRangeType.ChildRate),
          ),
        ),
      )
      val expectedDailyPrice2nd = Map(
        checkIn.plusDays(1) -> DailyPrice(
          checkIn.plusDays(1),
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn.plusDays(1),
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              20,
              1,
              0.0,
              None,
              Map(),
              0.0,
              20,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn.plusDays(1),
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(12),
          List(),
          List(
            ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 4, 0, 2, true, ChildAgeRangeType.ChildRate),
            ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 3, 3, 5, true, ChildAgeRangeType.ChildRate),
            ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 2, 6, 8, true, ChildAgeRangeType.ChildRate),
            ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 1, 9, 12, true, ChildAgeRangeType.ChildRate),
          ),
        ),
      )

      val result = converter.buildDailyPrices(
        checkIn,
        aValidPropOfferRoomRateCategory.roomTypeId,
        aValidPropOfferRoomRateCategory.rateCategoryId,
        taxLookUp,
        surcharges,
        channelRate,
        reqOcc,
        childAgeRanges,
        HmcMasterHotelContext(),
        useJpChildRateOverAgeRanges = true,
        commissionHolder = commissionHolder,
      )
      val expectedDailyPrice = expectedDailyPrice1st.map { case (k, v) =>
        k -> v.copy(rpmSurcharges = expectedSurchargeList)
      } ++ expectedDailyPrice2nd
      result must_== expectedDailyPrice
    }

    "search with invalid child, skip child rate daily when there is no child rate set, but there childageRange policy" in {
      val checkIn = aValidCheckIn
      val los = 2
      val taxLookUp: TaxPerDayLookUp = Map(checkIn -> Map((1, 0) -> aValidTax.withTaxId(1)),
                                           checkIn.plusDays(1) -> Map((1, 0) -> aValidTax.withTaxId(1)))
      val surcharges = List(aValidSurchargeEntry.withDates(Set(checkIn)).withIsPropOffer(true).build)
      val commissionHolder = aValidCommissionHolder.withDaily(
        Map(checkIn -> CommissionDailyHolder.default, checkIn.plusDays(1) -> CommissionDailyHolder.default))
      val reqOcc = aValidReqOcc
        .withChildrenAges(List(5))
        .withAgePolicy(aValidAgePolicy.withChildMaxAge(18))
        .withOccupancy(aValidOccInfo.withChildren(children = Some(YplChildren(List(None, None), Map.empty))))
      val childAgeRanges = Seq(MetaChildAgeRange(1, 6, 12, ChildAgeRangeType.NormalRange),
                               MetaChildAgeRange(3, 13, 18, ChildAgeRangeType.NormalRange))

      implicit val ctx =
        aValidYplContext.withRequest(aValidYplRequest.withCheckIn(checkIn).withCheckout(checkIn.plusDays(los))).build

      val expectedSurchargeList =
        List(SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(checkIn), false, true, 20.0, 0, true))
      val expectedDailyPrice1st = Map(
        checkIn -> DailyPrice(
          checkIn,
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              50,
              1,
              0.0,
              None,
              Map(),
              0.0,
              50,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(10),
          expectedSurchargeList,
        ),
      )
      val expectedDailyPrice2nd = Map(
        checkIn.plusDays(1) -> DailyPrice(
          checkIn.plusDays(1),
          Map((1, 0) -> 10.0),
          false,
          List(
            PriceEntry(
              checkIn.plusDays(1),
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              20,
              1,
              0.0,
              None,
              Map(),
              0.0,
              20,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn.plusDays(1),
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              30,
              1,
              0.0,
              None,
              Map(),
              0.0,
              30,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(12),
          List(),
        ),
      )

      val result = converter.buildDailyPrices(
        checkIn,
        aValidPropOfferRoomRateCategory.roomTypeId,
        aValidPropOfferRoomRateCategory.rateCategoryId,
        taxLookUp,
        surcharges,
        channelRateWithoutChildRate,
        reqOcc,
        childAgeRanges,
        HmcMasterHotelContext(),
        commissionHolder = commissionHolder,
      )
      val expectedDailyPrice = expectedDailyPrice1st.map { case (k, v) =>
        k -> v.copy(rpmSurcharges = expectedSurchargeList)
      } ++ expectedDailyPrice2nd
      result must_== expectedDailyPrice
    }
  }

  "hasAllotment" should {

    implicit val ctx = aValidYplContext
    "return false if no remaining room left" in {
      converter.hasAllotment(aValidPropOfferChannelRate.withRemainingRooms(0),
                             aValidPropOfferRoomRateCategory,
                             aValidReqOcc,
                             false) must_== false
    }

    "return false if no remaining room left with request occ with rooms 0 (to kill mutant)" in {
      converter.hasAllotment(aValidPropOfferChannelRate.withRemainingRooms(0),
                             aValidPropOfferRoomRateCategory,
                             aValidReqOcc.withRooms(0),
                             false) must_== false
    }

    "return false if rooms remaining is same as request occ (to kill mutant)" in {
      converter.hasAllotment(aValidPropOfferChannelRate.withRemainingRooms(1),
                             aValidPropOfferRoomRateCategory,
                             aValidReqOcc.withRooms(1),
                             false) must_== true
    }

    "return false if remaining rooms less than requested rooms" in {
      converter.hasAllotment(aValidPropOfferChannelRate.withRemainingRooms(1),
                             aValidPropOfferRoomRateCategory,
                             aValidReqOcc.withRooms(2),
                             false) must_== false
    }

    "return true if remaining rooms is more than requested rooms" in {
      converter.hasAllotment(aValidPropOfferChannelRate.withRemainingRooms(10),
                             aValidPropOfferRoomRateCategory,
                             aValidReqOcc.withRooms(2),
                             false) must_== true
    }

    "return true if remaining rooms is less than requested rooms but it's NHA Single Room" in {
      converter.hasAllotment(aValidPropOfferChannelRate.withRemainingRooms(1),
                             aValidPropOfferRoomRateCategory,
                             aValidReqOcc.withRooms(10),
                             true) must_== true
    }

    "return true if remaining rooms is less than requested rooms but have prefilterzeroallotment on the room and feature flag" in {
      val rc = aValidPropOfferRoomRateCategory
      val ct = ctx.withRequest(
        aValidYplRequest
          .withPreFilterZeroAllotment(Some(PreFilterZeroAllotment(rc.roomTypeId, rc.rateCategoryId.toInt)))
          .withFeatureFlags(Set(FeatureFlag.ReturnZeroAllotment)))
      converter.hasAllotment(aValidPropOfferChannelRate.withRemainingRooms(1), rc, aValidReqOcc.withRooms(2), false)(
        ct) must_== true
    }

    "return false if remaining rooms is less than requested rooms but have prefilterzeroallotment on featureflag but on different roomtype" in {
      val rc = aValidPropOfferRoomRateCategory
      val ct = ctx.withRequest(
        aValidYplRequest
          .withPreFilterZeroAllotment(Some(PreFilterZeroAllotment(1, 1)))
          .withFeatureFlags(Set(FeatureFlag.ReturnZeroAllotment)))
      converter.hasAllotment(aValidPropOfferChannelRate.withRemainingRooms(1), rc, aValidReqOcc.withRooms(2), false)(
        ct) must_== false
    }

    "return true if remaining rooms is less than requested rooms but have prefilterzeroallotment none with featureflag" in {
      val rc = aValidPropOfferRoomRateCategory
      val ct = ctx.withRequest(
        aValidYplRequest.withPreFilterZeroAllotment(None).withFeatureFlags(Set(FeatureFlag.ReturnZeroAllotment)))
      converter.hasAllotment(aValidPropOfferChannelRate.withRemainingRooms(1), rc, aValidReqOcc.withRooms(2), false)(
        ct) must_== true
    }

    "handle various scenarios with IgnoreRoomsCountForNha feature request" in {
      "remaining" | "requested" | "ignoreNha" | "frEnabled" | "expected" |>
        0 ! 0 ! true ! true ! false |
        0 ! 0 ! true ! false ! true |
        5 ! 3 ! false ! true ! true |
        2 ! 5 ! true ! true ! true |
        2 ! 5 ! false ! true ! false |
        0 ! 3 ! true ! true ! false |
        5 ! 3 ! false ! false ! true |
        2 ! 5 ! true ! false ! true |
        2 ! 5 ! false ! false ! false |
        0 ! 3 ! true ! false ! true |
        1 ! 1 ! false ! true ! true |
        1 ! 1 ! false ! false ! true |
        1 ! 2 ! false ! true ! false |
        1 ! 2 ! false ! false ! false | { (remaining, requested, ignoreNha, frEnabled, expected) =>
          val featureRequest =
            if (frEnabled) aValidYplRequest.featureRequest.copy(ignoreRoomsCountForNha = Some(true))
            else aValidYplRequest.featureRequest
          implicit val context = ctx.withRequest(aValidYplRequest.copy(featureRequest = featureRequest))
          converter.hasAllotment(
            aValidPropOfferChannelRate.withRemainingRooms(remaining),
            aValidPropOfferRoomRateCategory,
            aValidReqOcc.withRooms(requested),
            ignoreNha,
          )(context) must_== expected
        }
    }
  }

  "buildRateCategory" should {
    val dispatchChannels = YplDispatchChannels(Set(YplMasterChannel(1)), Set.empty)
    val childAgeRanges = Seq(
      MetaChildAgeRange(1, 9, 12, ChildAgeRangeType.ChildRate),
      MetaChildAgeRange(2, 6, 8, ChildAgeRangeType.ChildRate),
      MetaChildAgeRange(3, 3, 5, ChildAgeRangeType.ChildRate),
      MetaChildAgeRange(4, 0, 2, ChildAgeRangeType.ChildRate),
    )

    "return None if not passing booking restriction condition" in {
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      implicit val ctx =
        aValidYplContext.withRequest(aValidYplRequest.withCheckIn(aValidCheckIn).withBookingDate(aValidCheckIn)).build
      val meta = aValidHotelInfo
      val po = aValidPropertyOffer
        .withBookingRestrictions(Map(1 -> aValidOfferBookingRestriction.withMinAdvancePurchase(10)))
        .withRoomRates(Seq(roomRateCategory))
      converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== false
    }

    "work properly" in {
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val request = aValidYplRequest
        .withCheckIn(aValidCheckIn)
        .withClientInfo(aValidClientInfo.withCId(Some(-1)))
        .withBookingDate(aValidCheckIn)
      val meta = aValidHotelInfo
      val po = aValidPropertyOffer
        .withBookingRestrictions(
          Map(
            1 -> aValidOfferBookingRestriction
              .withMinAdvancePurchase(-1)
              .withMaxAdvancePurchase(999)
              .withCustomerSegments(Seq(aValidPropOfferCustomerSegment.withLanguageId(1).withCountryCode("AU")))))
        .withRoomRates(Seq(roomRateCategory))
      "return None if origin is not passing booking restriction condition" in {
        val ctx = aValidYplContext.withRequest(
          request.withWhitelabelSetting(
            aValidwhitelabelSetting.copy(isCustomerSegmentValidationEnabled = true),
          ))
        converter
          .preFilterRoomRate(po, dispatchChannels, meta)(ctx)
          .flatMap { roomRateCategory =>
            roomRateCategory.channelRates.map { channelRate =>
              converter.tagRoomRateCategoryWithFence(po, meta, roomRateCategory, channelRate, false)(ctx)
            }
          }
          .exists(_.fences.nonEmpty) must_== false
      }

      "ignore customer segment if white label setting isCustomerSegmentValidationEnabled is false" in {
        val ctx = aValidYplContext.withRequest(
          request.withWhitelabelSetting(
            aValidwhitelabelSetting.copy(isCustomerSegmentValidationEnabled = false),
          ))
        converter
          .preFilterRoomRate(po, dispatchChannels, meta)(ctx)
          .flatMap { roomRateCategory =>
            roomRateCategory.channelRates.map { channelRate =>
              converter.tagRoomRateCategoryWithFence(po, meta, roomRateCategory, channelRate, false)(ctx)
            }
          }
          .exists(_.fences.nonEmpty) must_== true
      }
    }

    "return Some if overridden origin by cid passing booking restriction condition" in {
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      implicit val ctx = aValidYplContext
        .withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withClientInfo(aValidClientInfo.withCId(Some(99999)))
            .withBookingDate(aValidCheckIn))
        .build
      val meta = aValidHotelInfo
      val po = aValidPropertyOffer
        .withBookingRestrictions(
          Map(
            1 -> aValidOfferBookingRestriction
              .withMinAdvancePurchase(-1)
              .withMaxAdvancePurchase(999)
              .withCustomerSegments(Seq(aValidPropOfferCustomerSegment.withLanguageId(1).withCountryCode("AU")))))
        .withRoomRates(Seq(roomRateCategory))
      converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== true
    }

    "return Some and skip payment model check if main supplier = room supplier and JTBFP-1295 = B" in {
      implicit val ctx: YplContext = aValidYplContext
        .withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(10))
            .withWhitelabelSetting(aValidwhitelabelSetting.copy(
              iSellingExternalSuppliersForJtbEnabled = true,
              externalSuppliersConfig = Map(332 -> ExternalSuppliersConfiguration(Some(332), Some(List(2)), Some(List(1)))),
              mainSupplier = 99901,
            )))
        .withExperimentContext(forceBExperimentContext(YplExperiments.ENABLE_SELLING_DIFFERENT_SUPPLIERS_FOR_JTB,
                                                       YplExperiments.DMC_HARDCODING_REMOVAL))

      val channelRate = aValidPropOfferChannelRate.withPaymentMode(PaymentMode.Merchant)

      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))

      val po = aValidPropertyOffer
        .withRoomRates(Seq(roomRateCategory))
        .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(99901))
      val result =
        converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo.withCountryCode("TH"))(ctx).map(identity)

      result.nonEmpty must beTrue
    }

    "return None when room supplier != 29014, no external supplier config, and JTBFP-1295 = A" in {
      implicit val ctx: YplContext = aValidYplContext
        .withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(10))
            .withWhitelabelSetting(aValidwhitelabelSetting.copy(
              iSellingExternalSuppliersForJtbEnabled = true,
              externalSuppliersConfig = Map(332 -> ExternalSuppliersConfiguration(Some(332), Some(List(2)), Some(List(1)))),
              mainSupplier = 332,
            )))
        .withExperimentContext(forceBExperimentContext(YplExperiments.ENABLE_SELLING_DIFFERENT_SUPPLIERS_FOR_JTB))

      val channelRate = aValidPropOfferChannelRate.withPaymentMode(PaymentMode.Agency)

      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))

      val po = aValidPropertyOffer
        .withRoomRates(Seq(roomRateCategory))
        .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(29014))
      val result =
        converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo.withCountryCode("TH"))(ctx).map(identity)

      result.nonEmpty must beTrue
    }

    "return None ratecategory when, customer segment condition is passing - customersegment is populated for all prop offer suppliers" in {
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo
      implicit val ctx = aValidYplContext
        .withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(12))
            .withFences(Map(YplMasterChannel.RTL -> Set(YplRateFence("HK", -1, 1)))),
        )
        .build
      val po = aValidPropertyOffer
        .withBookingRestrictions(
          Map(
            1 -> aValidOfferBookingRestriction
              .withCustomerSegments(Seq(aValidOfferCustomerSegment.withCountryCode("TH").withLanguageId(1)))
              .withMinAdvancePurchase(10)
              .withMaxAdvancePurchase(15)))
        .withRoomRates(Seq(roomRateCategory))
      converter
        .preFilterRoomRate(po, dispatchChannels, meta)(ctx)
        .flatMap { roomRateCategory =>
          roomRateCategory.channelRates.map { channelRate =>
            converter.tagRoomRateCategoryWithFence(po, meta, roomRateCategory, channelRate, false)(ctx)
          }
        }
        .exists(_.fences.nonEmpty) must_== false
    }

    "return None ratecategory when, customer segment condition is passing - customersegment is populated for all prop offer suppliers" in {
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo
      implicit val ctx = aValidYplContext
        .withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(12))
            .withFences(Map(YplMasterChannel.RTL -> Set(YplRateFence("HK", -1, 1)))),
        )
        .build
      val po = aValidPropertyOffer
        .withBookingRestrictions(
          Map(
            1 -> aValidOfferBookingRestriction
              .withCustomerSegments(Seq(aValidOfferCustomerSegment.withCountryCode("TH").withLanguageId(1)))
              .withMinAdvancePurchase(10)
              .withMaxAdvancePurchase(15)))
        .withRoomRates(Seq(roomRateCategory))
      converter
        .preFilterRoomRate(po, dispatchChannels, meta)(ctx)
        .flatMap { roomRateCategory =>
          roomRateCategory.channelRates.map { channelRate =>
            converter.tagRoomRateCategoryWithFence(po, meta, roomRateCategory, channelRate, false)(ctx)
          }
        }
        .exists(_.fences.nonEmpty) must_== false
    }

    "return None if not bcom and isnationality rate and customer segment condition is not passing - customersegment is populated" in {
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1).withIsNationalityRate(true)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo
      implicit val ctx = aValidYplContext
        .withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(12))
            .withFences(Map(YplMasterChannel.RTL -> Set(YplRateFence("HK", -1, 1)))))
        .build
      val po = aValidPropertyOffer
        .withBookingRestrictions(
          Map(
            1 -> aValidOfferBookingRestriction
              .withCustomerSegments(Seq(aValidOfferCustomerSegment.withCountryCode("TH").withLanguageId(1)))
              .withMinAdvancePurchase(10)
              .withMaxAdvancePurchase(15)))
        .withRoomRates(Seq(roomRateCategory))
      converter
        .preFilterRoomRate(po, dispatchChannels, meta)(ctx)
        .flatMap { roomRateCategory =>
          roomRateCategory.channelRates.map { channelRate =>
            converter.tagRoomRateCategoryWithFence(po, meta, roomRateCategory, channelRate, false)(ctx)
          }
        }
        .exists(_.fences.nonEmpty) must_== false
    }

    "return None if bcom and customer segment condition is not passing - customersegment is populated regardless of nationalityrate" in {
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1).withIsNationalityRate(false)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo
      implicit val ctx = aValidYplContext
        .withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(12))
            .withFences(Map(YplMasterChannel.RTL -> Set(YplRateFence("HK", -1, 1)))))
        .build
      val po = aValidPropertyOffer
        .withBookingRestrictions(
          Map(
            1 -> aValidOfferBookingRestriction
              .withCustomerSegments(Seq(aValidOfferCustomerSegment.withCountryCode("TH").withLanguageId(1)))
              .withMinAdvancePurchase(10)
              .withMaxAdvancePurchase(15)))
        .withRoomRates(Seq(roomRateCategory))
      converter
        .preFilterRoomRate(po, dispatchChannels, meta)(ctx)
        .flatMap { roomRateCategory =>
          roomRateCategory.channelRates.map { channelRate =>
            converter.tagRoomRateCategoryWithFence(po, meta, roomRateCategory, channelRate, false)(ctx)
          }
        }
        .exists(_.fences.nonEmpty) must_== false
    }

    "return None if not bcom, customer segment condition is not passing and CPL-79 is B - customersegment is populated but vip level is not satisfied" in {
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1).withIsNationalityRate(false)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo
      implicit val ctx = aValidYplContext
        .withExperimentContext(forceBExperimentContext(YplExperiments.CHECK_VIP_CUSTOMER_SEGMENT,
                                                       YplExperiments.KILL_SWITCH_RATECATEGORY_CUSTOMER_SEGMENT))
        .withRequest(
          aValidYplRequest
            .withClientInfo(aValidClientInfo.withVipLevel(Some(com.agoda.papi.ypl.models.enums.VipLevelType.GOLD)))
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(12))
            .withFences(Map(YplMasterChannel.RTL -> Set(YplRateFence("HK", -1, 1)))))
        .build
      val po = aValidPropertyOffer
        .withBookingRestrictions(
          Map(
            1 -> aValidOfferBookingRestriction
              .withCustomerSegments(Seq(
                aValidOfferCustomerSegment.withCountryCode("00").withLanguageId(0).withVipLevel(VipLevelType.PLATINUM)))
              .withMinAdvancePurchase(10)
              .withMaxAdvancePurchase(15)))
        .withRoomRates(Seq(roomRateCategory))
      converter
        .preFilterRoomRate(po, dispatchChannels, meta)(ctx)
        .flatMap { roomRateCategory =>
          roomRateCategory.channelRates.map { channelRate =>
            converter.tagRoomRateCategoryWithFence(po, meta, roomRateCategory, channelRate, false)(ctx)
          }
        }
        .exists(_.fences.nonEmpty) must_== false
    }

    "return Some if not bcom, customer segment condition is passing and CPL-79 is B - customersegment is populated and vip level is satisfied" in {
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1).withIsNationalityRate(false)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo
      implicit val ctx = aValidYplContext
        .withExperimentContext(forceBExperimentContext(YplExperiments.CHECK_VIP_CUSTOMER_SEGMENT,
                                                       YplExperiments.KILL_SWITCH_RATECATEGORY_CUSTOMER_SEGMENT))
        .withRequest(
          aValidYplRequest
            .withClientInfo(aValidClientInfo.withVipLevel(Some(com.agoda.papi.ypl.models.enums.VipLevelType.PLATINUM)))
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(12)))
        .build
      val po = aValidPropertyOffer
        .withBookingRestrictions(
          Map(
            1 -> aValidOfferBookingRestriction
              .withCustomerSegments(Seq(
                aValidOfferCustomerSegment.withCountryCode("00").withLanguageId(0).withVipLevel(VipLevelType.PLATINUM)))
              .withMinAdvancePurchase(10)
              .withMaxAdvancePurchase(15)))
        .withRoomRates(Seq(roomRateCategory))
      converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== true
    }

    "return Some if request is fenced and customer segment condition is not passing - customersegment is populated but vip level is not satisfied" in {
      val fences = Map(YplMasterChannel.RTL -> Set(YplRateFence("TH", 1, 1)))
      val channelRate = aValidPropOfferChannelRate.withBookingRestrictionIdentifier(1).withIsNationalityRate(false)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo
      val cInfo = aValidClientInfo.withVipLevel(Some(com.agoda.papi.ypl.models.enums.VipLevelType.GOLD))
      val req = aValidYplRequest
        .withClientInfo(cInfo)
        .withCheckIn(aValidCheckIn)
        .withBookingDate(aValidCheckIn.minusDays(12))
        .withFences(fences)
      implicit val ctx = aValidYplContext.withRequest(req).build
      val po = aValidPropertyOffer
        .withBookingRestrictions(
          Map(
            1 -> aValidOfferBookingRestriction
              .withCustomerSegments(Seq(
                aValidOfferCustomerSegment.withCountryCode("00").withLanguageId(0).withVipLevel(VipLevelType.PLATINUM)))
              .withMinAdvancePurchase(10)
              .withMaxAdvancePurchase(15)))
        .withRoomRates(Seq(roomRateCategory))
      converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== true
    }

    "return None if request is fenced, isYCS and not allow baby child rate but search with baby" in {
      val childPricing = Seq(ChildPricing(1, 1, true, 100), ChildPricing(2, 1, true, 100), ChildPricing(3, 1, true, 100))
      val childRates = ChildRate(isChildRateEnabled = true, childPricing)
      val channelRate = aValidPropOfferChannelRate.withChildRate(childRates)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo.withChildAgeRanges(childAgeRanges).withCountryCode("JP")
      val req = aValidYplRequest
        .withCheckIn(aValidCheckIn)
        .withOccupancyInfo(aValidOccInfo.withChildren(children = Some(YplChildren(List(Some(0)), Map.empty))))
      implicit val ctx = aValidYplContext.withRequest(req).build
      val po = aValidPropertyOffer
        .withRoomRates(Seq(roomRateCategory))
        .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(332))
      converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== false
    }

    "return Some if request is fenced, isYCS and isChildRate not enabled" in {
      val childPricing = Seq(ChildPricing(1, 1, true, 100), ChildPricing(2, 1, true, 100), ChildPricing(3, 1, true, 100))
      val childRates = ChildRate(isChildRateEnabled = false, childPricing)
      val channelRate = aValidPropOfferChannelRate.withChildRate(childRates)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo.withChildAgeRanges(childAgeRanges).withCountryCode("JP")
      val req = aValidYplRequest
        .withCheckIn(aValidCheckIn)
        .withOccupancyInfo(aValidOccInfo.withChildren(children = Some(YplChildren(List(Some(0)), Map.empty))))
      implicit val ctx = aValidYplContext.withRequest(req).build
      val po = aValidPropertyOffer
        .withRoomRates(Seq(roomRateCategory))
        .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(332))
      converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== true
    }

    "return Some if request is fenced, isYCS and isChildRate is enabled with baby types" in {
      val childPricing = Seq(ChildPricing(1, 1, true, 100), ChildPricing(2, 1, true, 100), ChildPricing(4, 1, true, 100))
      val childRates = ChildRate(isChildRateEnabled = true, childPricing)
      val channelRate = aValidPropOfferChannelRate.withChildRate(childRates)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo.withChildAgeRanges(childAgeRanges).withCountryCode("JP")
      val req = aValidYplRequest
        .withCheckIn(aValidCheckIn)
        .withOccupancyInfo(aValidOccInfo.withChildren(children = Some(YplChildren(List(Some(0)), Map.empty))))
      implicit val ctx = aValidYplContext.withRequest(req).build
      val po = aValidPropertyOffer
        .withRoomRates(Seq(roomRateCategory))
        .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(332))
      converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== true
    }

    "for experiment JTBFUN-1693" should {
      "for Variant B return rateCategory if paymentChannels are valid" in {
        implicit val ctx: YplContext = aValidYplContext
          .withRequest(aValidYplRequest.withCheckIn(aValidCheckIn).withBookingDate(aValidCheckIn.minusDays(10)))
          .withExperimentContext(forceBExperimentContext(YplExperiments.FIX_VALIDATION_FOR_PAYMENT_CHANNELS))
        val result =
          converter.preFilterRoomRate(aValidPropertyOffer, dispatchChannels, aValidHotelInfo)(ctx).map(identity)
        result.nonEmpty must beTrue
      }

      "for Variant B return None if paymentChannels are not valid" in {
        implicit val ctx: YplContext = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withBookingDate(aValidCheckIn.minusDays(10))
              .withWhitelabelSetting(aValidwhitelabelSetting.copy(paymentChannels = List(PaymentChannel.OnlineSBPS))),
          )
          .withExperimentContext(forceBExperimentContext(YplExperiments.FIX_VALIDATION_FOR_PAYMENT_CHANNELS))

        val channelRate = aValidPropOfferChannelRate
          .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.Corporate))
          .withPaymentMode(PaymentMode.Agency)
        val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        val result = converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo)(ctx).map(identity)

        result.isEmpty must beTrue
      }

      "for Variant A return rateCategory as is without checking for paymentChannels" in {
        implicit val ctx: YplContext = aValidYplContext.withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(10))
            .withWhitelabelSetting(aValidwhitelabelSetting.copy(paymentChannels = List(PaymentChannel.OnlineSBPS))),
        )

        val channelRate = aValidPropOfferChannelRate
          .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.Corporate))
          .withPaymentMode(PaymentMode.Agency)
        val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        val result = converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo)(ctx).map(identity)

        result.nonEmpty must beTrue
      }
    }

    "for sellable payment channels validation" should {
      "for JTBFP-564 Variant A return rateCategory if paymentChannels are allowed" in {
        implicit val ctx: YplContext = aValidYplContext.withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(10))
            .withWhitelabelSetting(aValidwhitelabelSetting.copy(
              whitelabelID = Whitelabel.Agoda,
              paymentInventoryTypeConfigurations = List(
                PaymentInventoryTypeConfiguration(com.agoda.supply.calc.proto.InventoryType.OTARurubu.value,
                                                  List(com.agoda.supply.calc.proto.PaymentChannel.OfflinePayment.value)),
              ),
            )))

        val channelRate = aValidPropOfferChannelRate
          .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.OfflinePayment))
          .withPaymentMode(PaymentMode.Agency)

        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withInventoryType(com.agoda.supply.calc.proto.InventoryType.OTARurubu)

        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        val result = converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo)(ctx).map(identity)

        result.nonEmpty must beTrue
      }

      "for JTBFP-564 Variant A return rateCategory if paymentChannels are not allowed" in {
        implicit val ctx: YplContext = aValidYplContext.withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(10))
            .withWhitelabelSetting(aValidwhitelabelSetting.copy(
              whitelabelID = Whitelabel.Agoda,
              paymentInventoryTypeConfigurations = List(
                PaymentInventoryTypeConfiguration(com.agoda.supply.calc.proto.InventoryType.OTARurubu.value,
                                                  List(com.agoda.supply.calc.proto.PaymentChannel.OfflinePayment.value)),
              ),
            )))

        val channelRate = aValidPropOfferChannelRate
          .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.OnlineSBPS))
          .withPaymentMode(PaymentMode.Agency)

        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withInventoryType(com.agoda.supply.calc.proto.InventoryType.OTARurubu)

        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        val result = converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo)(ctx).map(identity)

        result.nonEmpty must beTrue
      }

      "for JTBFP-564 Variant B return ratecategory if WL settings does not have payment-inventory-type configuration" in {
        implicit val ctx: YplContext = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withBookingDate(aValidCheckIn.minusDays(10))
              .withWhitelabelSetting(aValidwhitelabelSetting.copy(whitelabelID = Whitelabel.Rurubu)))
          .withExperimentContext(forceBExperimentContext(YplExperiments.VALIDATE_SELLABLE_PAYMENT_CHANNEL))

        val channelRate = aValidPropOfferChannelRate
          .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.OnlineSBPS))
          .withPaymentMode(PaymentMode.Agency)

        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withInventoryType(com.agoda.supply.calc.proto.InventoryType.OTARurubu)

        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        val result = converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo)(ctx).map(identity)

        result.nonEmpty must beTrue
      }

      "for JTBFP-564 Variant B return ratecategory if WL settings does not have required inventory-type in payment-inventory-type configuration" in {
        implicit val ctx: YplContext = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withBookingDate(aValidCheckIn.minusDays(10))
              .withWhitelabelSetting(aValidwhitelabelSetting.copy(
                whitelabelID = Whitelabel.Agoda,
                paymentInventoryTypeConfigurations = List(
                  PaymentInventoryTypeConfiguration(com.agoda.supply.calc.proto.InventoryType.OTAJapanican.value,
                                                    List(com.agoda.supply.calc.proto.PaymentChannel.OnlineSBPS.value)),
                ),
              )))
          .withExperimentContext(forceBExperimentContext(YplExperiments.VALIDATE_SELLABLE_PAYMENT_CHANNEL))

        val channelRate = aValidPropOfferChannelRate
          .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.OnlineSBPS))
          .withPaymentMode(PaymentMode.Agency)

        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withInventoryType(com.agoda.supply.calc.proto.InventoryType.OTARurubu)

        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        val result = converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo)(ctx).map(identity)

        result.nonEmpty must beTrue
      }

      "for JTBFP-564 Variant B return ratecategory if WL settings does not have allowed-payment-channel list in payment-inventory-type configuration" in {
        implicit val ctx: YplContext = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withBookingDate(aValidCheckIn.minusDays(10))
              .withWhitelabelSetting(aValidwhitelabelSetting.copy(
                whitelabelID = Whitelabel.Agoda,
                paymentInventoryTypeConfigurations = List(
                  PaymentInventoryTypeConfiguration(com.agoda.supply.calc.proto.InventoryType.OTAJapanican.value,
                                                    List.empty),
                ),
              )))
          .withExperimentContext(forceBExperimentContext(YplExperiments.VALIDATE_SELLABLE_PAYMENT_CHANNEL))

        val channelRate = aValidPropOfferChannelRate
          .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.OfflinePayment))
          .withPaymentMode(PaymentMode.Agency)

        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withInventoryType(com.agoda.supply.calc.proto.InventoryType.OTAJapanican)

        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        val result = converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo)(ctx).map(identity)

        result.nonEmpty must beTrue
      }

      "for JTBFP-564 Variant B return ratecategory if WL settings has payment channel allowed for inventory type" in {
        implicit val ctx: YplContext = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withBookingDate(aValidCheckIn.minusDays(10))
              .withWhitelabelSetting(aValidwhitelabelSetting.copy(
                whitelabelID = Whitelabel.Agoda,
                paymentInventoryTypeConfigurations = List(
                  PaymentInventoryTypeConfiguration(com.agoda.supply.calc.proto.InventoryType.OTARurubu.value,
                                                    List(com.agoda.supply.calc.proto.PaymentChannel.OfflinePayment.value)),
                ),
              )))
          .withExperimentContext(forceBExperimentContext(YplExperiments.VALIDATE_SELLABLE_PAYMENT_CHANNEL))

        val channelRate = aValidPropOfferChannelRate
          .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.OfflinePayment,
                                  com.agoda.supply.calc.proto.PaymentChannel.OnlineSBPS))
          .withPaymentMode(PaymentMode.Agency)

        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withInventoryType(com.agoda.supply.calc.proto.InventoryType.OTARurubu)

        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        val result = converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo)(ctx).map(identity)

        result.nonEmpty must beTrue
      }

      "for JTBFP-564 Variant B return empty result if WL settings does not allow payment channel for inventory type" in {
        implicit val ctx: YplContext = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withBookingDate(aValidCheckIn.minusDays(10))
              .withWhitelabelSetting(aValidwhitelabelSetting.copy(
                whitelabelID = Whitelabel.Agoda,
                paymentInventoryTypeConfigurations = List(
                  PaymentInventoryTypeConfiguration(com.agoda.supply.calc.proto.InventoryType.OTARurubu.value,
                                                    List(com.agoda.supply.calc.proto.PaymentChannel.OfflinePayment.value)),
                ),
              )))
          .withExperimentContext(forceBExperimentContext(YplExperiments.VALIDATE_SELLABLE_PAYMENT_CHANNEL))

        val channelRate = aValidPropOfferChannelRate
          .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.OnlineSBPS))
          .withPaymentMode(PaymentMode.Agency)

        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withInventoryType(com.agoda.supply.calc.proto.InventoryType.OTARurubu)

        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        val result = converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo)(ctx).map(identity)

        result.isEmpty must beTrue
      }
    }

    "for external supplier validation" should {
      "JTBFP-615 = A return result without doing external supplier checks" should {
        implicit val ctx: YplContext = aValidYplContext.withRequest(
          aValidYplRequest
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(10))
            .withWhitelabelSetting(aValidwhitelabelSetting.copy(whitelabelID = Whitelabel.Rurubu)))

        val channelRate = aValidPropOfferChannelRate
          .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.OnlineSBPS))
          .withPaymentMode(PaymentMode.Agency)

        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withInventoryType(com.agoda.supply.calc.proto.InventoryType.OTARurubu)

        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        val result = converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo)(ctx).map(identity)

        result.nonEmpty must beTrue
      }
      "JTBFP-615 = B return result if payment model and origin allowed" should {
        implicit val ctx: YplContext = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withBookingDate(aValidCheckIn.minusDays(10))
              .withWhitelabelSetting(aValidwhitelabelSetting.copy(
                whitelabelID = Whitelabel.Rurubu,
                iSellingExternalSuppliersForJtbEnabled = true,
                externalSuppliersConfig =
                  Map(332 -> ExternalSuppliersConfiguration(Some(332), Some(List(2)), Some(List(1)))),
              )))
          .withExperimentContext(forceBExperimentContext(YplExperiments.ENABLE_SELLING_DIFFERENT_SUPPLIERS_FOR_JTB))

        val channelRate = aValidPropOfferChannelRate.withPaymentMode(PaymentMode.Merchant)

        val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))

        val po = aValidPropertyOffer
          .withRoomRates(Seq(roomRateCategory))
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(332))
        val result =
          converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo.withCountryCode("TH"))(ctx).map(identity)

        result.nonEmpty must beTrue
      }
      "JTBFP-615 = B return empty if payment model is not allowed" should {
        implicit val ctx: YplContext = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withBookingDate(aValidCheckIn.minusDays(10))
              .withWhitelabelSetting(aValidwhitelabelSetting.copy(
                whitelabelID = Whitelabel.Rurubu,
                iSellingExternalSuppliersForJtbEnabled = true,
                externalSuppliersConfig =
                  Map(332 -> ExternalSuppliersConfiguration(Some(332), Some(List(2)), Some(List(1)))),
              )))
          .withExperimentContext(forceBExperimentContext(YplExperiments.ENABLE_SELLING_DIFFERENT_SUPPLIERS_FOR_JTB))

        val channelRate = aValidPropOfferChannelRate.withPaymentMode(PaymentMode.Agency)

        val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))

        val po = aValidPropertyOffer
          .withRoomRates(Seq(roomRateCategory))
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(332))
        val result =
          converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo.withCountryCode("TH"))(ctx).map(identity)

        result.isEmpty must beTrue
      }
      "JTBFP-615 = B return empty if supplier has no config" should {
        implicit val ctx: YplContext = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withBookingDate(aValidCheckIn.minusDays(10))
              .withWhitelabelSetting(aValidwhitelabelSetting.copy(
                whitelabelID = Whitelabel.Rurubu,
                iSellingExternalSuppliersForJtbEnabled = true,
                externalSuppliersConfig =
                  Map(332 -> ExternalSuppliersConfiguration(Some(332), Some(List(2)), Some(List(1)))),
              )))
          .withExperimentContext(forceBExperimentContext(YplExperiments.ENABLE_SELLING_DIFFERENT_SUPPLIERS_FOR_JTB))

        val channelRate = aValidPropOfferChannelRate.withPaymentMode(PaymentMode.Merchant)

        val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))

        val po = aValidPropertyOffer
          .withRoomRates(Seq(roomRateCategory))
          .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.BCOM))
        val result =
          converter.preFilterRoomRate(po, dispatchChannels, aValidHotelInfo.withCountryCode("TH"))(ctx).map(identity)

        result.isEmpty must beTrue
      }
    }

    "return proper ratecategory if pass booking restriction condition" in {
      val checkIn = aValidCheckIn
      val surcharges = Map(
        1 -> aValidPropOfferSurcharge
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(true)
          .withIsCommissionable(true)
          .withSurchargeId(5)
          .withValue(10),
        2 -> aValidPropOfferSurcharge
          .withApplyTo("PRPN")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(false)
          .withIsCommissionable(true)
          .withSurchargeId(787)
          .withValue(5),
      )
      val po = aValidPropertyOffer
        .withCancelPolicies(Map(1 -> CancelCode("7D1N_3D50P_100P")))
        .withBookingRestrictions(
          Map(
            1 -> aValidOfferBookingRestriction
              .withCustomerSegments(Seq(aValidOfferCustomerSegment.withCountryCode("TH").withLanguageId(1)))
              .withMinAdvancePurchase(10)
              .withMaxAdvancePurchase(15),
          ))
        .withCheckInInformations(Map(
          1 -> CheckInInformation(
            checkInFrom = Some(TimeOfDay(hours = 1, minutes = 1, seconds = 1)),
            checkInUntil = Some(TimeOfDay(hours = 2, minutes = 2, seconds = 2)),
            checkOutFrom = Some(TimeOfDay(hours = 3, minutes = 3, seconds = 3)),
            checkOutUntil = Some(TimeOfDay(hours = 4, minutes = 4, seconds = 4)),
          ),
        ))
        .withSurcharges(surcharges)
        .withTaxes(Map(1 -> aValidPropOfferTax.withTaxId(23).withTaxPrototypeId(2).withValue(34.2)))
        .withBenefits(Map(
          1 -> aValidPropOfferBenefit.withBenefitId(1),
          2 -> aValidPropOfferBenefit
            .withBenefitId(2)
            .withBenefitType(1)
            .withParameters(Seq(aValidStructureBenefitParameter)),
        ))

      val roomRateCategory = aValidPropOfferRoomRateCategory
        .withRateCategoryId(123)
        .withBenefitIdentifiers(Seq(1, 2))
        .withIsAgencyEnabled(true)
      val channelRate = aValidPropOfferChannelRate
        .withCancelationPolicy(
          CancelationPolicy(
            cancelCodeIdentifier = 1,
            cancelPolicyByOccupancy = Seq.empty,
            cancelChargeType = CancelChargeType.PerBookCharge,
          ))
        .withCheckInInformationIdentifier(1)
        .withSupplierMealPlan("mealplan")
        .withRemainingRooms(10)
        .withBookingRestrictionIdentifier(1)
        .withIsRoomTypeNotGuaranteed(true)
        .withPaymentMode(PaymentMode.Agency)
        .withParentRateDiscount(ParentRateDiscount(true, -5d, "PRPB"))
      val meta = aValidHotelInfo
      val commissionHolder = aValidCommissionHolder.withDaily(
        Map(checkIn -> CommissionDailyHolder.default, checkIn.plusDays(1) -> CommissionDailyHolder.default))
      implicit val ctx: YplContext = aValidYplContext.withRequest(
        aValidYplRequest
          .withCheckIn(aValidCheckIn)
          .withBookingDate(aValidCheckIn.minusDays(12))
          .withClientInfo(aValidClientInfo.withLanguage(1).withOrigin(Some("TH")))
          .withOccupancyInfo(YplOccInfo(Some(2))))
      val result = converter.buildRateCategory(po,
                                               channelRate,
                                               roomRateCategory,
                                               meta,
                                               po.taxes,
                                               po.taxesV2,
                                               true,
                                               isYCS = false,
                                               commissionHolder = commissionHolder)(isApplyYcsRateForking = false)
      result.rateCategoryId must_== 123
      result.cxlCode must_== "7D1N_3D50P_100P"
      result.rateCategoryCode must beNone
      result.remainingRoom must_== 10
      result.parent must beNone
      result.benefitList must_== List(
        BenefitEntry(1, 0, 0, None),
        BenefitEntry(2, 0, 1, None, parameters = List(BenefitParameterEntry(Some(1), Some("1"), None))))
      result.dailyPrices mustEqual Map(
        checkIn -> DailyPrice(
          checkIn,
          Map((23, 2) -> 34.2),
          false,
          List(
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              10.25,
              1,
              0.0,
              None,
              Map(),
              0.0,
              10.25,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn,
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              12.2,
              1,
              0.0,
              None,
              Map(),
              0.0,
              12.2,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(2.5),
          List(
            SurchargeEntry(5, "PB", ChargeOption.Mandatory, Set(checkIn), true, true, 10.0, 0, true),
            SurchargeEntry(787, "PRPN", ChargeOption.Mandatory, Set(checkIn), false, true, 5.0, 0, true),
          ),
        ),
        checkIn.plusDays(1) -> DailyPrice(
          checkIn.plusDays(1),
          Map(),
          false,
          List(
            PriceEntry(
              checkIn.plusDays(1),
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              1,
              10.25,
              1,
              0.0,
              None,
              Map(),
              0.0,
              10.25,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
            PriceEntry(
              checkIn.plusDays(1),
              ChargeType.Room,
              ChargeOption.Mandatory,
              "PRPN",
              2,
              12.2,
              1,
              0.0,
              None,
              Map(),
              0.0,
              12.2,
              None,
              SubChargeType.None,
              List(),
              None,
              BreakdownStep.BaseStep,
              BookingPriceBreakdown(),
            ),
          ),
          Some(3.6),
          List(),
        ),
      )
      result.bookFrom must beSome(DateTime.parse("1970-01-01"))
      result.bookTo must beSome(DateTime.parse("2099-01-01"))
      result.bookTimeFrom must beNone
      result.bookTimeTo must beNone
      result.remainingRoomGa must_== 5
      result.remainingRoomRa must beSome(5)
      result.minAdvance must beSome(10)
      result.maxAdvance must beSome(15)
      result.isAmount must_== true
      result.value must_== -5d
      result.applyTo must_== "PRPB"
      result.promotionList must beEmpty
      result.isCanCombinePromotion must_== false
      result.offerType must beSome(1)
      result.isRoomTypeNotGuarantee must_== true
      result.customerSegment must not be empty
      result.bookOn must beSome("1111111")
      result.dmcRatePlanID must beSome("8132102_93766198_2_1_1")
      result.dmcMealPlanID must beSome("mealplan")
      result.promotionTypeId must beSome(3)
      result.promotionTypeCmsId must beSome(4)
      result.paymentModel must beSome(PaymentModel.Agency)
      result.isAgencyEnabled must beSome(true)
      result.stayPackageType must beSome(StayPackageType.NormalOffer)
      result.checkInInformation must beSome(
        YPLCheckInInformation(
          checkInFrom = Some(LocalTime.of(1, 1, 1)),
          checkInUntil = Some(LocalTime.of(2, 2, 2)),
          checkOutFrom = Some(LocalTime.of(3, 3, 3)),
          checkOutUntil = Some(LocalTime.of(4, 4, 4)),
        ))

    }

    "Read data from benefit protobuf message" in {
      implicit val ctx: YplContext = aValidYplContext

      // Benefit Identifiers will override benefit setting
      val po = aValidPropertyOffer.withBenefits(
        Map(
          1 -> aValidPropOfferBenefit.withBenefitId(1),
          2 -> aValidPropOfferBenefit
            .withBenefitId(99)
            .withBenefitType(1)
            .withParameters(Seq(aValidStructureBenefitParameter)),
        ))
      val roomRateCategory = aValidPropOfferRoomRateCategory.withBenefits(Seq(1, 2, 3)).withBenefitIdentifiers(Seq(1, 2))

      val result = converter.buildRateCategory(po,
                                               aValidPropOfferChannelRate,
                                               roomRateCategory,
                                               aValidHotelInfo,
                                               po.taxes,
                                               po.taxesV2,
                                               true,
                                               isYCS = false)(isApplyYcsRateForking = false)
      result.benefitList must_== List(
        BenefitEntry(1, 0, 0, None),
        BenefitEntry(99, 0, 1, None, parameters = Seq(BenefitParameterEntry(Some(1), Some("1"), None))))
    }

    "set useJpChildRateOverAgeRanges correctly" should {
      val childPricing = Seq(ChildPricing(1, 1, true, 100), ChildPricing(2, 1, true, 100), ChildPricing(4, 1, true, 100))
      val childRates = ChildRate(isChildRateEnabled = true, childPricing)
      val channelRate = aValidPropOfferChannelRate.withChildRate(childRates)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val validHotelInfo = aValidHotelInfo.withChildAgeRanges(childAgeRanges).withCountryCode("JP")
      val req = aValidYplRequest
        .withCheckIn(aValidCheckIn)
        .withOccupancyInfo(aValidOccInfo.withChildren(children = Some(YplChildren(List(Some(0)), Map.empty))))

      val ctx = aValidYplContext.withRequest(req)

      val po = aValidPropertyOffer

      "should be true when hotel is YCS and in Japan and has childRates enabled" in {
        val result = converter.buildRateCategory(po,
                                                 channelRate,
                                                 roomRateCategory,
                                                 validHotelInfo,
                                                 po.taxes,
                                                 po.taxesV2,
                                                 true,
                                                 isYCS = true)(isApplyYcsRateForking = false)(ctx)
        result.useJapanBenefitChildRateDaily must_== true
      }

      "should be false when hotel is not YCS but in Japan and has childRates enabled" in {
        val result = converter.buildRateCategory(po,
                                                 channelRate,
                                                 roomRateCategory,
                                                 validHotelInfo,
                                                 po.taxes,
                                                 po.taxesV2,
                                                 true,
                                                 isYCS = false)(isApplyYcsRateForking = false)(ctx)
        result.useJapanBenefitChildRateDaily must_== false
      }

      "should be false when hotel not in Japan YCS but is YCS and has childRates enabled" in {
        val notJpHotelInfo = aValidHotelInfo.withChildAgeRanges(childAgeRanges).withCountryCode("TH")
        val result = converter.buildRateCategory(po,
                                                 channelRate,
                                                 roomRateCategory,
                                                 notJpHotelInfo,
                                                 po.taxes,
                                                 po.taxesV2,
                                                 true,
                                                 isYCS = true)(isApplyYcsRateForking = false)(ctx)
        result.useJapanBenefitChildRateDaily must_== false
      }

      "should be false when hotel childRates not enabled but in Japan YCS and is YCS" in {
        val result = converter.buildRateCategory(po,
                                                 aValidPropOfferChannelRate,
                                                 roomRateCategory,
                                                 validHotelInfo,
                                                 po.taxes,
                                                 po.taxesV2,
                                                 true,
                                                 isYCS = true)(isApplyYcsRateForking = false)(ctx)
        result.useJapanBenefitChildRateDaily must_== false
      }

      "should be false when hotel not have ChildPricing but in Japan YCS and is YCS and has childRates enabled" in {
        val nonPriceChildRates = ChildRate(isChildRateEnabled = true, Seq.empty)
        val nonChildPriceChannelRate = aValidPropOfferChannelRate.withChildRate(nonPriceChildRates)
        val result = converter.buildRateCategory(po,
                                                 nonChildPriceChannelRate,
                                                 roomRateCategory,
                                                 validHotelInfo,
                                                 po.taxes,
                                                 po.taxesV2,
                                                 true,
                                                 isYCS = true)(isApplyYcsRateForking = false)(ctx)
        result.useJapanBenefitChildRateDaily must_== false
      }
    }

    "return JTBWL ratecategory" in {
      val surcharges = Map(
        1 -> aValidPropOfferSurcharge
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(true)
          .withIsCommissionable(true)
          .withSurchargeId(5)
          .withValue(10),
        2 -> aValidPropOfferSurcharge
          .withApplyTo("PRPN")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(false)
          .withIsCommissionable(true)
          .withSurchargeId(787)
          .withValue(5),
      )
      val po = aValidPropertyOffer
        .withCancelPolicies(Map(1 -> CancelCode("7D1N_3D50P_100P")))
        .withSurcharges(surcharges)
        .withTaxes(Map(1 -> aValidPropOfferTax.withTaxId(23).withTaxPrototypeId(2).withValue(34.2)))
      val roomRateCategory =
        aValidPropOfferRoomRateCategory.withRateCategoryId(123).withBenefits(Seq(1, 2)).withIsAgencyEnabled(true)
      val channelRate = aValidPropOfferChannelRate
        .withCancelationPolicy(
          CancelationPolicy(
            cancelCodeIdentifier = 1,
            cancelPolicyByOccupancy = Seq(CancelPolicyByOccupancy(1, 2, 1)),
            cancelChargeType = CancelChargeType.PerBookCharge,
          ))
        .withCheckInTime(CheckInTime(Some(TimeOfDay(1, 0, 0)), Some(TimeOfDay(12, 30, 30))))
        .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.OnlineSBPS,
                                com.agoda.supply.calc.proto.PaymentChannel.Corporate))
      val meta = aValidHotelInfo
      implicit val ctx: YplContext = aValidYplContext.withRequest(
        aValidYplRequest
          .withCheckIn(aValidCheckIn)
          .withBookingDate(aValidCheckIn.minusDays(12))
          .withWhitelabelSetting(aValidwhitelabelSetting.copy(paymentChannels = List(PaymentChannel.OnlineSBPS)))
          .withClientInfo(aValidClientInfo.withLanguage(1).withOrigin(Some("TH")))
          .withOccupancyInfo(YplOccInfo(Some(2))))
      val result = converter.buildRateCategory(po,
                                               channelRate,
                                               roomRateCategory,
                                               meta,
                                               po.taxes,
                                               po.taxesV2,
                                               true,
                                               isYCS = false,
                                               isJTBWL = true)(isApplyYcsRateForking = false)(ctx)

      result.checkInStartTime must beSome("01:00:00")
      result.checkInEndTime must beSome("12:30:30")
      result.cxlCodePerOcc must_== List(CancellationCodePerOcc(1, 2, "7D1N_3D50P_100P"))
      result.paymentChannels must_== List(PaymentChannel.OnlineSBPS)
      result.rateCategoryCode must beSome("8132102_93766198_2_1_1")
    }

    "return JTBWL ratecategory correctly based on supplier features under exp: JTBFP-1295=B" in {
      val surcharges = Map(
        1 -> aValidPropOfferSurcharge
          .withApplyTo("PB")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(true)
          .withIsCommissionable(true)
          .withSurchargeId(5)
          .withValue(10),
        2 -> aValidPropOfferSurcharge
          .withApplyTo("PRPN")
          .withApplyType(ApplyType.Mandatory)
          .withIsAmount(false)
          .withIsCommissionable(true)
          .withSurchargeId(787)
          .withValue(5),
      )
      val po = aValidPropertyOffer
        .withCancelPolicies(Map(1 -> CancelCode("7D1N_3D50P_100P")))
        .withSurcharges(surcharges)
        .withTaxes(Map(1 -> aValidPropOfferTax.withTaxId(23).withTaxPrototypeId(2).withValue(34.2)))
        .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(DMC.JTBWL))
      val roomRateCategory =
        aValidPropOfferRoomRateCategory.withRateCategoryId(123).withBenefits(Seq(1, 2)).withIsAgencyEnabled(true)
      val channelRate = aValidPropOfferChannelRate
        .withCancelationPolicy(
          CancelationPolicy(
            cancelCodeIdentifier = 1,
            cancelPolicyByOccupancy = Seq(CancelPolicyByOccupancy(1, 2, 1)),
            cancelChargeType = CancelChargeType.PerBookCharge,
          ))
        .withCheckInTime(CheckInTime(Some(TimeOfDay(1, 0, 0)), Some(TimeOfDay(12, 30, 30))))
        .withPaymentChannel(Seq(com.agoda.supply.calc.proto.PaymentChannel.OnlineSBPS,
                                com.agoda.supply.calc.proto.PaymentChannel.Corporate))
      val meta = aValidHotelInfo
      val supplierFeatures = SupplierFeatures(features = Map(
        DMC.JTBWL -> aValidFeature.copy(hasAValidRateCode = false),
      ))
      val supplierFeatureRandomSupplier = SupplierFeatures(features = Map(
        DMC.NoSupplier -> aValidFeature.copy(hasAValidRateCode = true),
      ))
      val yplRequest = aValidYplRequest
        .withCheckIn(aValidCheckIn)
        .withBookingDate(aValidCheckIn.minusDays(12))
        .withWhitelabelSetting(aValidwhitelabelSetting.copy(paymentChannels = List(PaymentChannel.OnlineSBPS)))
        .withClientInfo(aValidClientInfo.withLanguage(1).withOrigin(Some("TH")))
        .withOccupancyInfo(YplOccInfo(Some(2)))
        .withSupplierFeature(aValidSupplierFeatures)

      val ctx = aValidYplContext
        .withExperimentContext(forceBExperimentContext(YplExperiments.DMC_HARDCODING_REMOVAL))
        .withRequest(yplRequest)
      val ctxWithFeatureDisabled = ctx.withRequest(yplRequest.withSupplierFeature(supplierFeatures))
      val ctxWithRandomSupplier = ctx.withRequest(yplRequest.withSupplierFeature(supplierFeatureRandomSupplier))

      val resultWitRandomSupplierFeature = converter.buildRateCategory(
        po,
        channelRate,
        roomRateCategory,
        meta,
        po.taxes,
        po.taxesV2,
        true,
        isYCS = false,
        isJTBWL = true)(isApplyYcsRateForking = false)(ctxWithRandomSupplier)
      val resultWithFeatureEnabled = converter.buildRateCategory(po,
                                                                 channelRate,
                                                                 roomRateCategory,
                                                                 meta,
                                                                 po.taxes,
                                                                 po.taxesV2,
                                                                 true,
                                                                 isYCS = false,
                                                                 isJTBWL = true)(isApplyYcsRateForking = false)(ctx)
      val result = converter.buildRateCategory(po,
                                               channelRate,
                                               roomRateCategory,
                                               meta,
                                               po.taxes,
                                               po.taxesV2,
                                               true,
                                               isYCS = false,
                                               isJTBWL = true)(isApplyYcsRateForking = false)(ctxWithFeatureDisabled)

//      exp -> B, feature disabled
      result.rateCategoryCode should_== None

//      exp -> B, feature setting doesn't exist for supplier, fallback to false
      resultWitRandomSupplierFeature.rateCategoryCode should_== None

//      exp -> b, feature enabled
      resultWithFeatureEnabled.rateCategoryCode must beSome("8132102_93766198_2_1_1")
    }

    "return correct paymentChannels" should {
      val channelRate = aValidPropOfferChannelRate.withPaymentChannel(Seq.empty)
      implicit val ctx: YplContext = aValidYplContext.withRequest(
        aValidYplRequest.withWhitelabelSetting(
          aValidwhitelabelSetting.copy(paymentChannels = List(PaymentChannel.OnlineSBPS))))

      "return intersection of paymentChannels of Whitelabel and paymentChannels of ChannelRate" in {
        val result = converter.buildRateCategory(
          aValidPropertyOffer,
          channelRate,
          aValidPropOfferRoomRateCategory,
          aValidHotelInfo,
          aValidPropertyOffer.taxes,
          aValidPropertyOffer.taxesV2,
          isBcomFixTaxAmountApplyToPB = true,
          isYCS = false,
          isJTBWL = true,
        )(isApplyYcsRateForking = false)(ctx)

        result.paymentChannels must_== List.empty
      }
    }

    "return NONE proper ratecategory if channel is NOT in dispatch request" in {
      val channelRate = aValidPropOfferChannelRate.withChannelId(2)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo
      implicit val ctx = aValidYplContext
        .withExperimentContext(forceAllBExperimentsContext())
        .withRequest(
          aValidYplRequest
            .withClientInfo(aValidClientInfo.withVipLevel(Some(com.agoda.papi.ypl.models.enums.VipLevelType.PLATINUM)))
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(12)))
        .build
      val po = aValidPropertyOffer.clearBookingRestrictions.withRoomRates(Seq(roomRateCategory)).clearRateRepurpose
      converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== false
    }

    "return proper ratecategory if channel is NOT in dispatch request but it is in rate repurpose" in {
      val channelRate = aValidPropOfferChannelRate.withChannelId(2)
      val roomRateCategory = aValidPropOfferRoomRateCategory.withChannelRates(Seq(channelRate))
      val meta = aValidHotelInfo
      implicit val ctx = aValidYplContext
        .withRequest(
          aValidYplRequest
            .withClientInfo(aValidClientInfo.withVipLevel(Some(com.agoda.papi.ypl.models.enums.VipLevelType.PLATINUM)))
            .withCheckIn(aValidCheckIn)
            .withBookingDate(aValidCheckIn.minusDays(12)))
        .build
      val po = aValidPropertyOffer.clearBookingRestrictions
        .withRoomRates(Seq(roomRateCategory))
        .withRateRepurpose(Seq(aValidPropOfferRateRepurposeInfo))
      converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== true
    }

    "return isAgency correctly for YCS True Agency" in {
      val po = aValidPropertyOffer
      val channelRate = aValidPropOfferChannelRate
      val meta = aValidHotelInfo
      val ctx =
        aValidYplContext.withRequest(aValidYplRequest.withCheckIn(aValidCheckIn).withBookingDate(aValidCheckIn)).build
      "Exp: TRUEAG-369, Variant: A" in {
        val roomRateCategory = aValidPropOfferRoomRateCategory
        val result = converter.buildRateCategory(po,
                                                 channelRate,
                                                 roomRateCategory,
                                                 meta,
                                                 po.taxes,
                                                 po.taxesV2,
                                                 true,
                                                 isYCS = true)(isApplyYcsRateForking = false)(ctx)
        result.isAgencyEnabled must beNone
      }
      "Exp: TRUEAG-369, Variant: B" in {
        "withIsAgencyEnabled(true)" in {
          val roomRateCategory = aValidPropOfferRoomRateCategory.withIsAgencyEnabled(true)
          val result = converter.buildRateCategory(po,
                                                   channelRate,
                                                   roomRateCategory,
                                                   meta,
                                                   po.taxes,
                                                   po.taxesV2,
                                                   true,
                                                   isYCS = true)(isApplyYcsRateForking = true)(ctx)
          result.isAgencyEnabled must_== Some(true)
        }
        "withIsAgencyEnabled(false)" in {
          val roomRateCategory = aValidPropOfferRoomRateCategory.withIsAgencyEnabled(false)
          val result = converter.buildRateCategory(po,
                                                   channelRate,
                                                   roomRateCategory,
                                                   meta,
                                                   po.taxes,
                                                   po.taxesV2,
                                                   true,
                                                   isYCS = true)(isApplyYcsRateForking = true)(ctx)
          result.isAgencyEnabled must_== Some(false)
        }
      }
    }

    "filter JapanAgodaSpecialOffer room correctly" in {
      implicit val ctx = aValidYplContext.withRequest(aValidYplRequest).build
      val channelRate = aValidPropOfferChannelRate
      val roomRateCategory = aValidPropOfferRoomRateCategory
        .withChannelRates(Seq(channelRate))
        .withStayPackageType(StayPackageType.RichContent)
      val meta = aValidHotelInfo
      val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
      val aValidJapanHotelInfo = aValidHotelInfo.copy(countryCode = "JP")
      val jasoRateCategoryLanguageMap = Map(roomRateCategory.rateCategoryId.toInt -> Set(1))
      val metaWithJASORateCategoryLanguage =
        aValidJapanHotelInfo.withJASORateCategoryLanguage(jasoRateCategoryLanguageMap)

      "return Some when Rich Content rateCategory for JP if platformId is supply_push" in {
        val channelRate = aValidPropOfferChannelRate
        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withStayPackageType(StayPackageType.RichContent)
        implicit val ctx = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withClientInfo(aValidClientInfo.copy(platform = Some(1008)))
              .withBookingDate(aValidCheckIn))
          .build
        val meta = aValidHotelInfo.copy(countryCode = "JP")
        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        converter
          .preFilterRoomRate(po, dispatchChannels, metaWithJASORateCategoryLanguage)
          .map(identity)
          .nonEmpty must_== true
      }

      "return Some when Rich Content rateCategory" in {
        val channelRate = aValidPropOfferChannelRate
        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withStayPackageType(StayPackageType.RichContent)
        implicit val ctx = aValidYplContext
          .withRequest(
            aValidYplRequest.withCheckIn(aValidCheckIn).withClientInfo(aValidClientInfo).withBookingDate(aValidCheckIn))
          .build
        val meta = aValidHotelInfo
        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== true
      }

      "return Some when Rich Content rateCategory for affiliate" in {
        val channelRate = aValidPropOfferChannelRate
        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withStayPackageType(StayPackageType.RichContent)
        implicit val ctx = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withClientInfo(aValidClientInfo.copy(platform = Some(1016)))
              .withBookingDate(aValidCheckIn))
          .build
        val meta = aValidHotelInfo
        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== true
      }

      "return Some for non Japan hotel" in {
        converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity).nonEmpty must_== true
      }
      "return Some for Japan hotel when rateCategoryId and langaugeId match jasoRateCategoryLanguage in hotel meta" in {
        converter
          .preFilterRoomRate(po, dispatchChannels, metaWithJASORateCategoryLanguage)(ctx)
          .map(identity)
          .nonEmpty must_== true
      }
      "return Some for Japan hotel when rateCategoryId match jasoRateCategoryLanguage and languageId match english fallback in hotel meta" in {
        implicit val ctxBVariantWithJapanLanguage =
          aValidYplContext.withRequest(aValidYplRequest.withClientInfo(aValidClientInfo.copy(language = 6))).build
        converter
          .preFilterRoomRate(po, dispatchChannels, metaWithJASORateCategoryLanguage)(ctxBVariantWithJapanLanguage)
          .map(identity)
          .nonEmpty must_== true
      }
      "return None for Japan hotel when rateCategoryId match jasoRateCategoryLanguage and languageId not match in hotel meta" in {
        val jasoRateCategoryLanguageMap = Map(roomRateCategory.rateCategoryId.toInt -> Set(6))
        val metaWithJASORateCategoryLanguage =
          aValidJapanHotelInfo.withJASORateCategoryLanguage(jasoRateCategoryLanguageMap)
        converter
          .preFilterRoomRate(po, dispatchChannels, metaWithJASORateCategoryLanguage)(ctx)
          .map(identity)
          .nonEmpty must_== false
      }
      "return None for Japan hotel when rateCategoryId and languageId not match jasoRateCategoryLanguage in hotel meta" in {
        converter
          .preFilterRoomRate(po, dispatchChannels, aValidJapanHotelInfo)(ctx)
          .map(identity)
          .nonEmpty must_== false
      }
    }

    "return distinct benefits when propertyBenefits present and experiment 4726 active for user" in {
      implicit val ctx: YplContext = aValidYplContext

      // Benefit Identifiers will override benefit setting
      val po = aValidPropertyOffer
        .withPropertyBenefits(
          Seq(
            aValidPropOfferBenefit.withBenefitId(1),
            aValidPropOfferBenefit.withBenefitId(21),
          ),
        )
        .withBenefits(
          Map(
            1 -> aValidPropOfferBenefit.withBenefitId(1),
            2 -> aValidPropOfferBenefit.withBenefitId(2),
            3 -> aValidPropOfferBenefit.withBenefitId(3),
          ))
      val roomRateCategory = aValidPropOfferRoomRateCategory.withBenefitIdentifiers(Seq(1, 2, 3))

      val result = converter.buildRateCategory(po,
                                               aValidPropOfferChannelRate,
                                               roomRateCategory,
                                               aValidHotelInfo,
                                               po.taxes,
                                               po.taxesV2,
                                               true,
                                               isYCS = false)(isApplyYcsRateForking = false)
      result.benefitList must_== List(BenefitEntry(1, 0, 0, None),
                                      BenefitEntry(2, 0, 0, None),
                                      BenefitEntry(3, 0, 0, None),
                                      BenefitEntry(21, 0, 0, None))
    }

    "return distinct updated benefits when propertyBenefits present with SUPPIO-5000 active" in {
      implicit val ctx: YplContext = aValidYplContext

      // Benefit Identifiers will override benefit setting
      val po = aValidPropertyOffer
        .withBenefits(Map(
          1 -> aValidPropOfferBenefit.withBenefitId(1),
          2 -> aValidPropOfferBenefit
            .withBenefitId(99)
            .withBenefitType(1)
            .withParameters(Seq(aValidStructureBenefitParameter)),
        ))
        .withPropertyBenefits(Seq(
          aValidPropOfferBenefit.withBenefitId(21),
        ))
      val roomRateCategory = aValidPropOfferRoomRateCategory.withBenefits(Seq(1, 2, 3)).withBenefitIdentifiers(Seq(1, 2))

      val result = converter.buildRateCategory(po,
                                               aValidPropOfferChannelRate,
                                               roomRateCategory,
                                               aValidHotelInfo,
                                               po.taxes,
                                               po.taxesV2,
                                               true,
                                               isYCS = false)(isApplyYcsRateForking = false)
      result.benefitList must_== List(
        BenefitEntry(1, 0, 0, None),
        BenefitEntry(99, 0, 1, None, parameters = Seq(BenefitParameterEntry(Some(1), Some("1"), None))),
        BenefitEntry(21, 0, 0, None))
    }

    "return correct benefits when rc benefits already have a breakfast type benefit and propertyBenefits has breakfast" in {
      implicit val ctx: YplContext = aValidYplContext

      val po = aValidPropertyOffer
        .withPropertyBenefits(
          Seq(
            aValidPropOfferBenefit.withBenefitId(1),
            aValidPropOfferBenefit.withBenefitId(11),
          ))
        .withBenefits(
          Map(
            2 -> aValidPropOfferBenefit.withBenefitId(2),
            3 -> aValidPropOfferBenefit.withBenefitId(3),
            20 -> aValidPropOfferBenefit.withBenefitId(20),
          ))
      val roomRateCategory = aValidPropOfferRoomRateCategory.withBenefitIdentifiers(Seq(20, 2, 3))

      val result = converter.buildRateCategory(po,
                                               aValidPropOfferChannelRate,
                                               roomRateCategory,
                                               aValidHotelInfo,
                                               po.taxes,
                                               po.taxesV2,
                                               true,
                                               isYCS = false)(isApplyYcsRateForking = false)
      result.benefitList must_== List(BenefitEntry(20, 0, 0, None),
                                      BenefitEntry(2, 0, 0, None),
                                      BenefitEntry(3, 0, 0, None),
                                      BenefitEntry(11, 0, 0, None))
    }

    "return child policy when child policy existed" in {
      implicit val ctx: YplContext = aValidYplContext

      val po = aValidPropertyOffer
      val roomRateCategory =
        aValidPropOfferRoomRateCategory.withChildPolicy(ChildPolicy(1, List(RateLevelChildAgeRange(3, 4))))

      val result = converter.buildRateCategory(po,
                                               aValidPropOfferChannelRate,
                                               roomRateCategory,
                                               aValidHotelInfo,
                                               po.taxes,
                                               po.taxesV2,
                                               true,
                                               isYCS = false)(isApplyYcsRateForking = false)
      result.childPolicy must beSome(ChildPolicy(1, List(RateLevelChildAgeRange(3, 4))))
    }

    "filter koreanCustomizedOffer room correctly" in {
      implicit val ctx = aValidYplContext
      val channelRate = aValidPropOfferChannelRate
      val roomRateCategory = aValidPropOfferRoomRateCategory
        .withChannelRates(Seq(channelRate))
        .withStayPackageType(StayPackageType.RichContent)
        .withEscapesApprovalStatus(EscapesApprovalStatus.Approved)
      val meta = aValidHotelInfo
      val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
      val aValidKoreanHotelInfo = aValidHotelInfo.copy(countryCode = "KR")
      val specialRateCategoryLanguageMap = Map(roomRateCategory.rateCategoryId.toInt -> Set(1))
      val metaWithSpecialRateCategoryLanguage =
        aValidKoreanHotelInfo.withJASORateCategoryLanguage(specialRateCategoryLanguageMap).build

      "don't return Some when Rich Content rateCategory for KR if platformId is supply_push" in {
        val channelRate = aValidPropOfferChannelRate
        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withStayPackageType(StayPackageType.RichContent)
          .withEscapesApprovalStatus(EscapesApprovalStatus.Approved)
        implicit val ctx = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withClientInfo(aValidClientInfo.copy(platform = Some(1008)))
              .withBookingDate(aValidCheckIn))
          .build
        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        converter
          .preFilterRoomRate(po, dispatchChannels, hotelInfo = metaWithSpecialRateCategoryLanguage)(ctx)
          .map(identity)
          .nonEmpty must_== false
      }
      "return Some when Rich Content rateCategory" in {
        val channelRate = aValidPropOfferChannelRate
        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withStayPackageType(StayPackageType.RichContent)
        implicit val ctx = aValidYplContext
          .withRequest(
            aValidYplRequest.withCheckIn(aValidCheckIn).withClientInfo(aValidClientInfo).withBookingDate(aValidCheckIn))
          .build
        val meta = aValidHotelInfo
        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        converter.preFilterRoomRate(po, dispatchChannels, meta).map(identity).nonEmpty must_== true
      }

      "don't return Some when Rich Content rateCategory for affiliate" in {
        val channelRate = aValidPropOfferChannelRate
        val roomRateCategory = aValidPropOfferRoomRateCategory
          .withChannelRates(Seq(channelRate))
          .withStayPackageType(StayPackageType.RichContent)
          .withEscapesApprovalStatus(EscapesApprovalStatus.Approved)
        implicit val ctx = aValidYplContext
          .withRequest(
            aValidYplRequest
              .withCheckIn(aValidCheckIn)
              .withClientInfo(aValidClientInfo.copy(platform = Some(1016)))
              .withBookingDate(aValidCheckIn))
          .build
        val po = aValidPropertyOffer.withRoomRates(Seq(roomRateCategory))
        converter
          .preFilterRoomRate(po, dispatchChannels, metaWithSpecialRateCategoryLanguage)
          .map(identity)
          .nonEmpty must_== false
      }

      "return Some for non KR hotel" in {
        converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity).nonEmpty must_== true
      }
      "return Some for KR hotel when rateCategoryId and langaugeId match jasoRateCategoryLanguage in hotel meta" in {
        converter
          .preFilterRoomRate(po, dispatchChannels, metaWithSpecialRateCategoryLanguage)(ctx)
          .map(identity)
          .nonEmpty must_== true
      }
      "return Some for KR hotel when rateCategoryId match specialRateCategoryLanguage and languageId match english fallback in hotel meta" in {
        implicit val ctxBVariantWithKRLanguage =
          aValidYplContext.withRequest(aValidYplRequest.withClientInfo(aValidClientInfo.copy(language = 6))).build
        converter
          .preFilterRoomRate(po, dispatchChannels, metaWithSpecialRateCategoryLanguage)(ctxBVariantWithKRLanguage)
          .map(identity)
          .nonEmpty must_== true
      }
      "return None for Kr hotel when rateCategoryId match specialRateCategoryLanguage and languageId not match in hotel meta" in {
        val specialRateCategoryLanguage = Map(roomRateCategory.rateCategoryId.toInt -> Set(6))
        val metaWithSpecialRateCategoryLanguage =
          aValidKoreanHotelInfo.withJASORateCategoryLanguage(specialRateCategoryLanguage)
        converter
          .preFilterRoomRate(po, dispatchChannels, metaWithSpecialRateCategoryLanguage)(ctx)
          .map(identity)
          .nonEmpty must_== false
      }
      "return None for KR hotel when rateCategoryId and languageId not match specialRateCategoryLanguage in hotel meta" in {
        converter
          .preFilterRoomRate(po, dispatchChannels, aValidKoreanHotelInfo)(ctx)
          .map(identity)
          .nonEmpty must_== false
      }
    }

  }

  "buildAgencyNoccSetting" should {
    "return correct None" in {
      val po = aValidPropertyOffer
        .withHotelId(9999)
        .withMasterHotelId(10637)
        .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(3038))
        .withPaymentMode(PaymentMode.Agency)
        .withOccupancyModel(proto.OccupancyModel.Full)
        .withAgencyMode(proto.AgencyNoCCMode.EnabledNoccToAllAgencyBookings)
      val meta = aValidHotelInfo.withHotelId(10637)
      val dispatchChannels = YplDispatchChannels(Set(YplMasterChannel(1)), Set.empty)
      implicit val ctx = aValidYplContext

      val result = converter.convert(po, meta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
      result must not be empty
      val hotel = result.get
      hotel.agencyNoccSetting must beNone
    }
  }

  "message for APM approval ID is not found" should {
    "be correct" in {
      val dateTimeStr = "2021-12-09"
      val formatter = DateTimeFormat.forPattern("yyyy-MM-dd")
      val dateTime: DateTime = formatter.parseDateTime(dateTimeStr)
      val msg = converter.getApmApprovalIDNotFoundMsg(aValidHotelInfo.hotelId, 2, dateTime, 1)
      msg must_== "approval price ID is not found, hotelID = 6011203, stayDate = 2021-12-09T00:00:00.000+07:00, roomTypeID = 1, occupancy = 2"
    }
  }

  "convert APM RateInfo correctly" should {

    "return correct autoPriceMatchInfo" in {
      val mockApmRoomTypeId1 = 13193032
      val mockApmRoomTypeId2 = 13193038
      val mockApmRateInfo = Seq(
        ApmRateInfo(
          roomTypeId = mockApmRoomTypeId1,
          apmPrices = Seq(
            ApmPriceDaily(
              stayDates = Seq(NumericRange(0, 0), NumericRange(3, 3)),
              apmOccupancyPrices = Seq(
                ApmOccupancyPrice(occupancies = Seq(NumericRange(1, 1)), sellInclusiveAmount = 90d),
                ApmOccupancyPrice(occupancies = Seq(NumericRange(2, 2)), sellInclusiveAmount = 100d),
              ),
            ),
            ApmPriceDaily(
              stayDates = Seq(NumericRange(1, 1)),
              apmOccupancyPrices = Seq(
                ApmOccupancyPrice(occupancies = Seq(NumericRange(2, 3), NumericRange(5, 5)), sellInclusiveAmount = 100d),
                ApmOccupancyPrice(occupancies = Seq(NumericRange(4, 4)), sellInclusiveAmount = 110d),
              ),
            ),
          ),
        ),
        ApmRateInfo(
          roomTypeId = mockApmRoomTypeId2,
          apmPrices = Seq(
            ApmPriceDaily(stayDates = Seq(NumericRange(0, 3)),
                          apmOccupancyPrices =
                            Seq(ApmOccupancyPrice(occupancies = Seq(NumericRange(1, 3)), sellInclusiveAmount = 222d)))),
        ),
      )

      val apmApprovalPriceIdPool = 1L to 20L

      // These data should align with the hotel.autoPriceMatchInfo below.
      val mockApmApprovalPriceIdMapEntries = Seq(
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.toSimpleDate), 1)),
          apmApprovalPriceIdPool(0)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(3).toSimpleDate), 1)),
          apmApprovalPriceIdPool(1)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.toSimpleDate), 2)),
          apmApprovalPriceIdPool(2)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(3).toSimpleDate), 2)),
          apmApprovalPriceIdPool(3)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 2)),
          apmApprovalPriceIdPool(4)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 3)),
          apmApprovalPriceIdPool(5)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 4)),
          apmApprovalPriceIdPool(6)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 5)),
          apmApprovalPriceIdPool(7)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.toSimpleDate), 1)),
          apmApprovalPriceIdPool(8)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(1).toSimpleDate), 1)),
          apmApprovalPriceIdPool(9)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(2).toSimpleDate), 1)),
          apmApprovalPriceIdPool(10)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(3).toSimpleDate), 1)),
          apmApprovalPriceIdPool(11)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.toSimpleDate), 2)),
          apmApprovalPriceIdPool(12)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(1).toSimpleDate), 2)),
          apmApprovalPriceIdPool(13)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(2).toSimpleDate), 2)),
          apmApprovalPriceIdPool(14)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(3).toSimpleDate), 2)),
          apmApprovalPriceIdPool(15)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.toSimpleDate), 3)),
          apmApprovalPriceIdPool(16)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(1).toSimpleDate), 3)),
          apmApprovalPriceIdPool(17)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(2).toSimpleDate), 3)),
          apmApprovalPriceIdPool(18)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(3).toSimpleDate), 3)),
          apmApprovalPriceIdPool(19)),
      )
      val mockApmApprovalPriceIdMap = ApmApprovalPriceMap(entries = mockApmApprovalPriceIdMapEntries)

      val po = aValidPropertyOffer
        .withHotelId(9999)
        .withMasterHotelId(10637)
        .withOccupancyModel(proto.OccupancyModel.Full)
        .withApmRateInfo(mockApmRateInfo)
        .withApmApprovalPriceMap(mockApmApprovalPriceIdMap)
      val meta = aValidHotelInfo.withHotelId(10637)
      val dispatchChannels = YplDispatchChannels(Set(YplMasterChannel(1)), Set.empty)
      implicit val ctx = aValidYplContext.withExperimentContext(forceAllAExperimentsContext()).build

      val result = converter.convert(po, meta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
      result must not be empty
      val hotel = result.get

      hotel.autoPriceMatchInfo must_== Map(
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 1) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(90d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(0)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(90d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(1)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 2) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(2)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(3)),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(4)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 3) ->
          Map(aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(5))),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 4) ->
          Map(aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(110d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(6))),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 5) ->
          Map(aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(7))),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId2, 1) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(8)),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(9)),
            aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(10)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(11)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId2, 2) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(12)),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(13)),
            aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(14)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(15)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId2, 3) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(16)),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(17)),
            aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(18)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(19)),
          ),
      )
    }

    "return correct autoPriceMatchInfo on remove occupancy one" in {
      val mockApmRoomTypeId1 = 13193032
      val mockApmRateInfo = Seq(
        ApmRateInfo(
          roomTypeId = mockApmRoomTypeId1,
          apmPrices = Seq(
            ApmPriceDaily(stayDates = Seq(NumericRange(0, 0)),
                          apmOccupancyPrices =
                            Seq(ApmOccupancyPrice(occupancies = Seq(NumericRange(2, 2)), sellInclusiveAmount = 100d))),
          ),
        ))

      val apmApprovalPriceIdPool = 1L to 5L

      // These data should align with the hotel.autoPriceMatchInfo below.
      val mockApmApprovalPriceIdMapEntries = Seq(
        ApmApprovalPriceKeyValuePair(Option(
                                       ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.toSimpleDate), 2)),
                                     apmApprovalPriceIdPool(0)),
      )
      val mockApmApprovalPriceIdMap = ApmApprovalPriceMap(entries = mockApmApprovalPriceIdMapEntries)

      val po = aValidPropertyOffer
        .withHotelId(9999)
        .withMasterHotelId(10637)
        .withOccupancyModel(proto.OccupancyModel.Full)
        .withApmRateInfo(mockApmRateInfo)
        .withApmApprovalPriceMap(mockApmApprovalPriceIdMap)
      val meta = aValidHotelInfo.withHotelId(10637)
      val dispatchChannels = YplDispatchChannels(Set(YplMasterChannel(1)), Set.empty)
      implicit val ctx = aValidYplContext

      val result = converter.convert(po, meta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
      result must not be empty
      val hotel = result.get

      hotel.autoPriceMatchInfo must_== Map(
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 2) ->
          Map(aValidCheckIn -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(0))),
      )
    }

    "return correct autoPriceMatchInfo with apmChannelPrices" in {
      val mockApmRoomTypeId1 = aValidApmRoomTypeId1
      val mockApmRoomTypeId2 = aValidApmRoomTypeId2
      val mockApmRateInfo = Seq(
        ApmRateInfo(
          roomTypeId = mockApmRoomTypeId1,
          apmPrices = Seq(
            ApmPriceDaily(
              stayDates = Seq(NumericRange(0, 0), NumericRange(3, 3)),
              apmOccupancyPrices = Seq(
                ApmOccupancyPrice(
                  occupancies = Seq(NumericRange(1, 1)),
                  apmChannelPrices = Seq(
                    ApmChannelPrice(
                      apmChannelId = 1051,
                      sellInclusiveAmount = 90d,
                    )),
                ),
                ApmOccupancyPrice(
                  occupancies = Seq(NumericRange(2, 2)),
                  apmChannelPrices = Seq(
                    ApmChannelPrice(
                      apmChannelId = 1051,
                      sellInclusiveAmount = 100d,
                    )),
                ),
              ),
            ),
            ApmPriceDaily(
              stayDates = Seq(NumericRange(1, 1)),
              apmOccupancyPrices = Seq(
                ApmOccupancyPrice(
                  occupancies = Seq(NumericRange(2, 3), NumericRange(5, 5)),
                  apmChannelPrices = Seq(
                    ApmChannelPrice(
                      apmChannelId = 1051,
                      sellInclusiveAmount = 100d,
                    )),
                ),
                ApmOccupancyPrice(
                  occupancies = Seq(NumericRange(4, 4)),
                  apmChannelPrices = Seq(
                    ApmChannelPrice(
                      apmChannelId = 1052,
                      sellInclusiveAmount = 110d,
                    )),
                ),
              ),
            ),
          ),
        ),
        ApmRateInfo(
          roomTypeId = mockApmRoomTypeId2,
          apmPrices = Seq(
            ApmPriceDaily(
              stayDates = Seq(NumericRange(0, 3)),
              apmOccupancyPrices = Seq(
                ApmOccupancyPrice(
                  occupancies = Seq(NumericRange(1, 3)),
                  apmChannelPrices = Seq(
                    ApmChannelPrice(
                      apmChannelId = 1051,
                      sellInclusiveAmount = 222d,
                    )),
                ),
              ),
            ),
            ApmPriceDaily(
              stayDates = Seq(NumericRange(0, 0)),
              apmOccupancyPrices = Seq(
                ApmOccupancyPrice(
                  occupancies = Seq(NumericRange(1, 1)),
                  apmChannelPrices = Seq(
                    ApmChannelPrice(
                      apmChannelId = 0,
                      sellInclusiveAmount = 333d,
                    )),
                ),
              ),
            ),
          ),
        ),
      )

      val apmApprovalPriceIdPool = 1L to 21L

      // These data should align with the hotel.autoPriceMatchInfo below.
      val mockApmApprovalPriceIdMapEntries = Seq(
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.toSimpleDate), 1)),
          apmApprovalPriceIdPool(0),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(3).toSimpleDate), 1)),
          apmApprovalPriceIdPool(1),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.toSimpleDate), 2)),
          apmApprovalPriceIdPool(2),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(3).toSimpleDate), 2)),
          apmApprovalPriceIdPool(3),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 2)),
          apmApprovalPriceIdPool(4),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 3)),
          apmApprovalPriceIdPool(5),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 4)),
          apmApprovalPriceIdPool(6),
          1052),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 5)),
          apmApprovalPriceIdPool(7),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.toSimpleDate), 1)),
          apmApprovalPriceIdPool(8),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(1).toSimpleDate), 1)),
          apmApprovalPriceIdPool(9),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(2).toSimpleDate), 1)),
          apmApprovalPriceIdPool(10),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(3).toSimpleDate), 1)),
          apmApprovalPriceIdPool(11),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.toSimpleDate), 2)),
          apmApprovalPriceIdPool(12),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(1).toSimpleDate), 2)),
          apmApprovalPriceIdPool(13),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(2).toSimpleDate), 2)),
          apmApprovalPriceIdPool(14),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(3).toSimpleDate), 2)),
          apmApprovalPriceIdPool(15),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.toSimpleDate), 3)),
          apmApprovalPriceIdPool(16),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(1).toSimpleDate), 3)),
          apmApprovalPriceIdPool(17),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(2).toSimpleDate), 3)),
          apmApprovalPriceIdPool(18),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(3).toSimpleDate), 3)),
          apmApprovalPriceIdPool(19),
          1051),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.toSimpleDate), 1)),
          apmApprovalPriceIdPool(20),
          0),
      )
      val mockApmApprovalPriceIdMap = ApmApprovalPriceMap(entries = mockApmApprovalPriceIdMapEntries)

      val po = aValidPropertyOffer
        .withHotelId(9999)
        .withMasterHotelId(10637)
        .withOccupancyModel(proto.OccupancyModel.Full)
        .withApmRateInfo(mockApmRateInfo)
        .withApmApprovalPriceMap(mockApmApprovalPriceIdMap)
      val meta = aValidHotelInfo.withHotelId(10637)
      val dispatchChannels = YplDispatchChannels(Set(YplMasterChannel(1)), Set.empty)
      implicit val ctx = aValidYplContext.withExperimentContext(forceAllAExperimentsContext()).build

      val result = converter.convert(po, meta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
      result must not be empty
      val hotel = result.get

      hotel.autoPriceMatchInfo must_== Map(
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 1, Some(1051)) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(90d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(0), Some(1051)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(90d,
                                                                 aValidApmRoomTypeId1,
                                                                 apmApprovalPriceIdPool(1),
                                                                 Some(1051)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 2, Some(1051)) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(2), Some(1051)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(100d,
                                                                 aValidApmRoomTypeId1,
                                                                 apmApprovalPriceIdPool(3),
                                                                 Some(1051)),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(100d,
                                                                 aValidApmRoomTypeId1,
                                                                 apmApprovalPriceIdPool(4),
                                                                 Some(1051)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 3, Some(1051)) ->
          Map(aValidCheckIn
            .plusDays(1) -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(5), Some(1051))),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 4, Some(1052)) ->
          Map(aValidCheckIn
            .plusDays(1) -> AutoPriceMatchPriceInfo(110d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(6), Some(1052))),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 5, Some(1051)) ->
          Map(aValidCheckIn
            .plusDays(1) -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(7), Some(1051))),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId2, 1, Some(1051)) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(8), Some(1051)),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d,
                                                                 aValidApmRoomTypeId2,
                                                                 apmApprovalPriceIdPool(9),
                                                                 Some(1051)),
            aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d,
                                                                 aValidApmRoomTypeId2,
                                                                 apmApprovalPriceIdPool(10),
                                                                 Some(1051)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(222d,
                                                                 aValidApmRoomTypeId2,
                                                                 apmApprovalPriceIdPool(11),
                                                                 Some(1051)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId2, 2, Some(1051)) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(12), Some(1051)),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d,
                                                                 aValidApmRoomTypeId2,
                                                                 apmApprovalPriceIdPool(13),
                                                                 Some(1051)),
            aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d,
                                                                 aValidApmRoomTypeId2,
                                                                 apmApprovalPriceIdPool(14),
                                                                 Some(1051)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(222d,
                                                                 aValidApmRoomTypeId2,
                                                                 apmApprovalPriceIdPool(15),
                                                                 Some(1051)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId2, 3, Some(1051)) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(16), Some(1051)),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d,
                                                                 aValidApmRoomTypeId2,
                                                                 apmApprovalPriceIdPool(17),
                                                                 Some(1051)),
            aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d,
                                                                 aValidApmRoomTypeId2,
                                                                 apmApprovalPriceIdPool(18),
                                                                 Some(1051)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(222d,
                                                                 aValidApmRoomTypeId2,
                                                                 apmApprovalPriceIdPool(19),
                                                                 Some(1051)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId2, 1, None) ->
          Map(aValidCheckIn -> AutoPriceMatchPriceInfo(333d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(20), None)),
      )
    }

    "return autoPriceMatchInfo as empty map" in {
      val po =
        aValidPropertyOffer.withHotelId(9999).withMasterHotelId(10637).withOccupancyModel(proto.OccupancyModel.Full)
      val meta = aValidHotelInfo.withHotelId(10637)
      val dispatchChannels = YplDispatchChannels(Set(YplMasterChannel(1)), Set.empty)
      implicit val ctx = aValidYplContext.withExperimentContext(forceAllAExperimentsContext()).build

      val result = converter.convert(po, meta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
      result must not be empty
      val hotel = result.get

      hotel.autoPriceMatchInfo must_== Map.empty
    }

    "return correct autoPriceMatchInfo" in {
      val mockApmRoomTypeId1 = 13193032
      val mockApmRoomTypeId2 = 13193038
      val apmApprovalPriceIdPool = 1L to 20L

      // These data should align with the hotel.autoPriceMatchInfo below.
      val mockApmApprovalPriceIdMapEntries = Seq(
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.toSimpleDate), 1)),
          apmApprovalPriceIdPool(0)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(1).toSimpleDate), 1)),
          apmApprovalPriceIdPool(1)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(2).toSimpleDate), 1)),
          apmApprovalPriceIdPool(2)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(3).toSimpleDate), 1)),
          apmApprovalPriceIdPool(3)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.toSimpleDate), 2)),
          apmApprovalPriceIdPool(4)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 2)),
          apmApprovalPriceIdPool(5)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(2).toSimpleDate), 2)),
          apmApprovalPriceIdPool(6)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(3).toSimpleDate), 2)),
          apmApprovalPriceIdPool(7)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.toSimpleDate), 3)),
          apmApprovalPriceIdPool(8)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 3)),
          apmApprovalPriceIdPool(9)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId2, Some(aValidCheckIn.plusDays(2).toSimpleDate), 3)),
          apmApprovalPriceIdPool(10)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 4)),
          apmApprovalPriceIdPool(11)),
        ApmApprovalPriceKeyValuePair(
          Option(ApmApprovalPriceKey(aValidApmRoomTypeId1, Some(aValidCheckIn.plusDays(1).toSimpleDate), 5)),
          apmApprovalPriceIdPool(12)),
      )
      val mockApmApprovalPriceIdMap = ApmApprovalPriceMap(entries = mockApmApprovalPriceIdMapEntries)

      val mockApmRateInfo = Seq(
        ApmRateInfo(
          roomTypeId = mockApmRoomTypeId1,
          apmPrices = Seq(
            ApmPriceDaily(
              stayDates = Seq(NumericRange(0, 0), NumericRange(3, 3)),
              apmOccupancyPrices = Seq(
                ApmOccupancyPrice(occupancies = Seq(NumericRange(1, 1)), sellInclusiveAmount = 90d),
                ApmOccupancyPrice(occupancies = Seq(NumericRange(2, 2)), sellInclusiveAmount = 100d),
              ),
            ),
            ApmPriceDaily(
              stayDates = Seq(NumericRange(1, 1)),
              apmOccupancyPrices = Seq(
                ApmOccupancyPrice(occupancies = Seq(NumericRange(2, 3), NumericRange(5, 5)), sellInclusiveAmount = 100d),
                ApmOccupancyPrice(occupancies = Seq(NumericRange(4, 4)), sellInclusiveAmount = 110d),
              ),
            ),
          ),
        ),
        ApmRateInfo(
          roomTypeId = mockApmRoomTypeId2,
          apmPrices = Seq(
            ApmPriceDaily(stayDates = Seq(NumericRange(0, 2)),
                          apmOccupancyPrices =
                            Seq(ApmOccupancyPrice(occupancies = Seq(NumericRange(1, 3)), sellInclusiveAmount = 222d)))),
        ),
      )

      val po = aValidPropertyOffer
        .withHotelId(9999)
        .withMasterHotelId(10637)
        .withOccupancyModel(proto.OccupancyModel.Full)
        .withApmRateInfo(mockApmRateInfo)
        .withApmApprovalPriceMap(mockApmApprovalPriceIdMap)
      val meta = aValidHotelInfo.withHotelId(10637)
      val dispatchChannels = YplDispatchChannels(Set(YplMasterChannel(1)), Set.empty)
      implicit val ctx = aValidYplContext.withExperimentContext(forceAllAExperimentsContext()).build

      val result = converter.convert(po, meta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))(ctx)
      result must not be empty
      val hotel = result.get

      hotel.autoPriceMatchInfo must_== Map(
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 1) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(90d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(0)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(90d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(3)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 3) ->
          Map(aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(9))),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId2, 1) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, 0),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(1)),
            aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(2)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId2, 3) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(8)),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, 0),
            aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(10)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 4) ->
          Map(aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(110d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(11))),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 2) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(4)),
            aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(7)),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(5)),
          ),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId1, 5) ->
          Map(aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(100d, aValidApmRoomTypeId1, apmApprovalPriceIdPool(12))),
        AutoPriceMatchKeyEntry(aValidApmRoomTypeId2, 2) ->
          Map(
            aValidCheckIn -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, 0),
            aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, 0),
            aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d, aValidApmRoomTypeId2, apmApprovalPriceIdPool(6)),
          ),
      )
    }

  }

  "toTimeInterval correctly" should {
    "return timeInterval when hour > 10" in {
      val hourlyAvailability = HourlyAvailability.HourlyAvailableSlot(
        from = Some(TimeOfDay(hours = 16, minutes = 0)),
        durationInMinute = 240,
      )

      val result = converter.toTimeInterval(hourlyAvailability)
      result must not be None

      val expected = Some(TimeInterval(duration = 4, from = "16:00"))

      result must_== expected
    }

    "return timeInterval when hour < 10" in {
      val hourlyAvailability = HourlyAvailability.HourlyAvailableSlot(
        from = Some(TimeOfDay(hours = 7, minutes = 0)),
        durationInMinute = 240,
      )

      val result = converter.toTimeInterval(hourlyAvailability)
      result must not be None

      val expected = Some(TimeInterval(duration = 4, from = "07:00"))

      result must_== expected
    }

    "return timeInterval when minute > 10" in {
      val hourlyAvailability = HourlyAvailability.HourlyAvailableSlot(
        from = Some(TimeOfDay(hours = 7, minutes = 25)),
        durationInMinute = 240,
      )

      val result = converter.toTimeInterval(hourlyAvailability)
      result must not be None

      val expected = Some(TimeInterval(duration = 4, from = "07:25"))

      result must_== expected
    }

    "return timeInterval when minute < 10" in {
      val hourlyAvailability = HourlyAvailability.HourlyAvailableSlot(
        from = Some(TimeOfDay(hours = 7, minutes = 9)),
        durationInMinute = 240,
      )

      val result = converter.toTimeInterval(hourlyAvailability)
      result must not be None

      val expected = Some(TimeInterval(duration = 4, from = "07:09"))

      result must_== expected
    }

    "return timeInterval when duration in minute not full hour" in {
      val hourlyAvailability = HourlyAvailability.HourlyAvailableSlot(
        from = Some(TimeOfDay(hours = 7, minutes = 0)),
        durationInMinute = 200,
      )

      val result = converter.toTimeInterval(hourlyAvailability)
      result must not be None

      val expected = Some(TimeInterval(duration = 3, from = "07:00"))

      result must_== expected
    }

    "convert hourlyAvailability with hours less than 10 and minutes less than 10" in {
      val hourlyAvailability = HourlyAvailability.HourlyAvailableSlot(
        from = Some(TimeOfDay(hours = 8, minutes = 5)),
        durationInMinute = 120,
      )
      val result = converter.toTimeInterval(hourlyAvailability)
      result must beSome(TimeInterval(duration = 2, from = "08:05"))
    }

    "convert hourlyAvailability with hours greater than or equal to 10 and minutes less than 10" in {
      val hourlyAvailability = HourlyAvailability.HourlyAvailableSlot(
        from = Some(TimeOfDay(hours = 11, minutes = 5)),
        durationInMinute = 120,
      )
      val result = converter.toTimeInterval(hourlyAvailability)
      result must beSome(TimeInterval(duration = 2, from = "11:05"))
    }

    "convert hourlyAvailability with hours less than 10 and minutes greater than or equal to 10" in {
      val hourlyAvailability = HourlyAvailability.HourlyAvailableSlot(
        from = Some(TimeOfDay(hours = 8, minutes = 15)),
        durationInMinute = 120,
      )
      val result = converter.toTimeInterval(hourlyAvailability)
      result must beSome(TimeInterval(duration = 2, from = "08:15"))
    }

    "convert hourlyAvailability with hours greater than or equal to 10 and minutes greater than or equal to 10" in {
      val hourlyAvailability = HourlyAvailability.HourlyAvailableSlot(
        from = Some(TimeOfDay(hours = 10, minutes = 10)),
        durationInMinute = 120,
      )
      val result = converter.toTimeInterval(hourlyAvailability)
      result must beSome(TimeInterval(duration = 2, from = "10:10"))
    }

    "return None when hourlyAvailability.from is None" in {
      val hourlyAvailability = HourlyAvailability.HourlyAvailableSlot(
        from = None,
        durationInMinute = 120,
      )
      val result = converter.toTimeInterval(hourlyAvailability)
      result must beNone
    }
  }

  "create PriceEntry correctly" should {
    val mockStayDate = DateTime.parse("2021-02-15")
    val mockReqOcc = YplReqOccByHotelAgePolicy(
      YplOccInfo(_adults = Option(2), _children = Some(YplChildren(List(Some(1)))), _rooms = Option(1)),
      AgePolicy())

    "ChargeType is Room" in {
      val res = converter.createPriceEntry(mockReqOcc,
                                           mockStayDate,
                                           2,
                                           200d,
                                           RateType.NetExclusive,
                                           "THB",
                                           false,
                                           ChargeType.Room)
      res.date should_== mockStayDate
      res.chargeType should_== ChargeType.Room
      res.applyTo should_== "PRPN"
      res.chargeOption should_== ChargeOption.Mandatory
      res.occupancy should_== 2
      res.value should_== 200d
      res.quantity should_== 1

      res.priceBreakdownHistory.breakdown.isEmpty should_== true
    }

    "ChargeType is Room (multiRoom)" in {
      val multiRoomReqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Option(2),
                                                                 _children = Some(YplChildren(List(Some(1)))),
                                                                 _rooms = Option(2)),
                                                      AgePolicy())

      val res = converter.createPriceEntry(multiRoomReqOcc,
                                           mockStayDate,
                                           2,
                                           200d,
                                           RateType.NetExclusive,
                                           "THB",
                                           false,
                                           ChargeType.Room)
      res.date should_== mockStayDate
      res.chargeType should_== ChargeType.Room
      res.applyTo should_== "PRPN"
      res.chargeOption should_== ChargeOption.Mandatory
      res.occupancy should_== 2
      res.value should_== 200d
      res.quantity should_== 2

      res.priceBreakdownHistory.breakdown.isEmpty should_== true
    }

    "ChargeType is Room (free Occ)" in {
      val mockReqOccForFreeOcc =
        YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Option(0), _children = None, _rooms = Option(2)), AgePolicy())
      val res = converter.createPriceEntry(mockReqOccForFreeOcc,
                                           mockStayDate,
                                           2,
                                           200d,
                                           RateType.NetExclusive,
                                           "THB",
                                           false,
                                           ChargeType.Room)
      res.date should_== mockStayDate
      res.chargeType should_== ChargeType.Room
      res.applyTo should_== "PRPN"
      res.chargeOption should_== ChargeOption.Mandatory
      res.occupancy should_== 2
      res.value should_== 200d
      res.quantity should_== 1

      res.priceBreakdownHistory.breakdown.isEmpty should_== true
    }

    "ChargeType is Extrabed" in {
      val res = converter.createPriceEntry(mockReqOcc,
                                           mockStayDate,
                                           2,
                                           200d,
                                           RateType.NetExclusive,
                                           "THB",
                                           true,
                                           ChargeType.ExtraBed)
      res.date should_== mockStayDate
      res.chargeType should_== ChargeType.ExtraBed
      res.applyTo should_== "PGPN"
      res.chargeOption should_== ChargeOption.Mandatory
      res.occupancy should_== 2
      res.value should_== 200d
      res.quantity should_== 1

      res.priceBreakdownHistory.breakdown.head.chargeType should_== ChargeType.ExtraBed.entryName
      res.priceBreakdownHistory.breakdown.head.currency should_== "THB"
      res.priceBreakdownHistory.breakdown.head.netEx should_== Some(200d)
      res.priceBreakdownHistory.breakdown.head.discountAmount should_== ""
    }
  }

  "buildRoomEntry along with HR Room Linkage" should {
    implicit val ctx = aValidYplContext
    val checkInDate = ctx.request.checkIn
    val meta = aValidHotelInfo.withEnabledRoom(mockEnabledRoomWithLinkage)
    val otaRoomRateCategory = aValidPropOfferRoomRateCategory
      .withRoomTypeId(3203083L)
      .withChannelRates(Seq(aValidPropOfferChannelRate.withRemainingRooms(6)))
    val hrRoomRateCategory = aValidPropOfferHrRoomRateCategory
      .withRoomTypeId(3203084L)
      .withChannelRates(Seq(aValidPropOfferChannelRate.withRemainingRooms(8)))
    val hourlyRoomRateCategory = aValidPropOfferRoomRateCategory
      .withRoomTypeId(3203083L)
      .withChannelRates(Seq(aValidPropOfferChannelRate.withHourlyAvailability(aValidHourlyAvailability)))

    val po = aValidPropertyOffer
      .withCheckInDate(SimpleDate(checkInDate.getYear, checkInDate.getMonthOfYear, checkInDate.getDayOfMonth))
      .withSupplyInfo(aValidPropOfferJTBSupplyInfo)
      .withRoomTypes(Map(3203083L -> aValidPropOfferRoomTypeInfo, 3203084L -> aValidPropOfferHrRoomTypeInfo))
      .withRoomRates(Seq(otaRoomRateCategory, hrRoomRateCategory))

    "returns linkedRoomTypeCode and remainingRooms of HR room correctly when linked" in {
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     otaRoomRateCategory,
                                                     isBcomFixTaxAmountApplyToPB = false,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 8)
      result.head.linkedRoomTypeCode should_== Some("DEF")
    }

    "returns linkedRoomTypeCode and remainingRooms under exp JTBFP-1295=B and supplier feature enabled" in {

      val req = aValidYplRequest.withBExperiment(YplExperiments.DMC_HARDCODING_REMOVAL)
      implicit val forceBctx: YplContextMock =
        aValidYplContext.withRequest(req).withExperimentContext(forcedFromRequestExperimentContext(req)).build

      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     otaRoomRateCategory,
                                                     isBcomFixTaxAmountApplyToPB = false,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(isApplyYcsRateForking =
        false)(forceBctx)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 8)
      result.head.linkedRoomTypeCode should_== Some("DEF")
    }

    "returns linkedRoomTypeCode and remainingRooms under exp JTBFP-1295=B and supplier feature disabled" in {
      val req = aValidYplRequest
        .withBExperiment(YplExperiments.DMC_HARDCODING_REMOVAL)
        .withSupplierFeature(SupplierFeatures(features = Map(DMC.JTBWL -> aValidFeature)))
      implicit val forceBctx: YplContextMock =
        aValidYplContext.withRequest(req).withExperimentContext(forcedFromRequestExperimentContext(req)).build

      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     otaRoomRateCategory,
                                                     isBcomFixTaxAmountApplyToPB = false,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(isApplyYcsRateForking =
        false)(forceBctx)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 6)
      result.head.linkedRoomTypeCode should_== None
    }

    "returns linkedRoomTypeCode and remainingRooms under exp JTBFP-1295=B and missing supplier feature" in {
      val req = aValidYplRequest
        .withBExperiment(YplExperiments.DMC_HARDCODING_REMOVAL)
        .withSupplierFeature(SupplierFeatures(features = Map(29015 -> avalidFeatureForJTB)))
      implicit val forceBctx: YplContextMock =
        aValidYplContext.withRequest(req).withExperimentContext(forcedFromRequestExperimentContext(req)).build

      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     otaRoomRateCategory,
                                                     isBcomFixTaxAmountApplyToPB = false,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(isApplyYcsRateForking =
        false)(forceBctx)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 6)
      result.head.linkedRoomTypeCode should_== None
    }

    "returns linkedRoomTypeCode and remainingRooms correctly under exp JTBFP-1295=A by hardcoded DMC id check" in {
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     otaRoomRateCategory,
                                                     isBcomFixTaxAmountApplyToPB = false,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(isApplyYcsRateForking =
        false)(aValidYplContext)

      val poUat = aValidPropertyOffer
        .withCheckInDate(SimpleDate(checkInDate.getYear, checkInDate.getMonthOfYear, checkInDate.getDayOfMonth))
        .withSupplyInfo(aValidPropOfferJTBUatSupplyInfo)
        .withRoomTypes(Map(3203083L -> aValidPropOfferRoomTypeInfo, 3203084L -> aValidPropOfferHrRoomTypeInfo))
        .withRoomRates(Seq(otaRoomRateCategory, hrRoomRateCategory))
      val resultUat = converter.buildPropOfferRoomEntry(poUat,
                                                        meta,
                                                        otaRoomRateCategory,
                                                        isBcomFixTaxAmountApplyToPB = false,
                                                        po.taxes,
                                                        po.taxesV2,
                                                        isYCS = false,
                                                        hotelPaymentModel = hotelPaymentModel)(isApplyYcsRateForking =
        false)(aValidYplContext)

      result.size must_== 1
      result.forall(_.remainingRooms should_== 8)
      result.head.linkedRoomTypeCode should_== Some("DEF")

      resultUat.size must_== 1
      resultUat.forall(_.remainingRooms should_== 6)
      resultUat.head.linkedRoomTypeCode should_== None
    }

    "returns linkedRoomTypeCode and remainingRooms of OTA room correctly when linked" in {
      val otaRoomRate = otaRoomRateCategory.copy(channelRates = Seq(aValidPropOfferChannelRate.withRemainingRooms(9)))
      val hrRoomRate = hrRoomRateCategory.copy(channelRates = Seq(aValidPropOfferChannelRate.withRemainingRooms(6)))
      val propOffer = po.copy(roomRates = Seq(otaRoomRate, hrRoomRate))

      val result = converter.buildPropOfferRoomEntry(propOffer,
                                                     meta,
                                                     otaRoomRate,
                                                     isBcomFixTaxAmountApplyToPB = false,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 9)
      result.head.linkedRoomTypeCode should_== Some("DEF")
    }

    "returns linkedRoomTypeCode and remainingRooms from roomTypes when force exp JPST-281 B" in {
      val otaRoomTypesWithRoomLinkageInfo =
        aValidPropOfferRoomTypeInfo.copy(roomLinkage = Some(PropRoomLinkage(3203084L, "HR", 10)))
      val propOfferWithRoomLinkage =
        po.copy(roomTypes = Map(3203083L -> otaRoomTypesWithRoomLinkageInfo), roomRates = Seq(otaRoomRateCategory))
      val req = aValidYplRequest.withBExperiment(YplExperiments.USE_ROOM_LINKAGE_INFO_FROM_PROPOFFER)
      val forceBctx = aValidYplContext.withRequest(req).withExperimentContext(forcedFromRequestExperimentContext(req))
      val result = converter.buildPropOfferRoomEntry(
        propOfferWithRoomLinkage,
        meta,
        otaRoomRateCategory,
        isBcomFixTaxAmountApplyToPB = false,
        po.taxes,
        po.taxesV2,
        isYCS = false,
        hotelPaymentModel = hotelPaymentModel,
      )(false)(forceBctx)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 10)
      result.head.linkedRoomTypeCode should_== Some("HR")
    }

    "returns linkedRoomTypeCode from HR and remainingRooms from OTA when OTA have more remaining room and force exp JPST-281 B" in {
      val otaRoomTypesWithRoomLinkageInfo =
        aValidPropOfferRoomTypeInfo.copy(roomLinkage = Some(PropRoomLinkage(3203084L, "HR", 5)))
      val propOfferWithRoomLinkage =
        po.copy(roomTypes = Map(3203083L -> otaRoomTypesWithRoomLinkageInfo, 3203084L -> aValidPropOfferHrRoomTypeInfo),
                roomRates = Seq(otaRoomRateCategory))
      val req = aValidYplRequest.withBExperiment(YplExperiments.USE_ROOM_LINKAGE_INFO_FROM_PROPOFFER)
      val forceBctx = aValidYplContext.withRequest(req).withExperimentContext(forcedFromRequestExperimentContext(req))
      val result = converter.buildPropOfferRoomEntry(
        propOfferWithRoomLinkage,
        meta,
        otaRoomRateCategory,
        isBcomFixTaxAmountApplyToPB = false,
        po.taxes,
        po.taxesV2,
        isYCS = false,
        hotelPaymentModel = hotelPaymentModel,
      )(false)(forceBctx)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 6)
      result.head.linkedRoomTypeCode should_== Some("HR")
    }

    "returns linkedRoomTypeCode as empty and remainingRooms from OTA when no roomlinkage in RoomTypes and force exp JPST-281 B" in {
      val otaRoomTypesWithOutRoomLinkageInfo = aValidPropOfferRoomTypeInfo.copy(roomLinkage = None)
      val propOfferWithRoomLinkage = po.copy(roomTypes = Map(3203083L -> otaRoomTypesWithOutRoomLinkageInfo,
                                                             3203084L -> aValidPropOfferHrRoomTypeInfo),
                                             roomRates = Seq(otaRoomRateCategory))
      val req = aValidYplRequest.withBExperiment(YplExperiments.USE_ROOM_LINKAGE_INFO_FROM_PROPOFFER)
      val forceBctx = aValidYplContext.withRequest(req).withExperimentContext(forcedFromRequestExperimentContext(req))
      val result = converter.buildPropOfferRoomEntry(
        propOfferWithRoomLinkage,
        meta,
        otaRoomRateCategory,
        isBcomFixTaxAmountApplyToPB = false,
        po.taxes,
        po.taxesV2,
        isYCS = false,
        hotelPaymentModel = hotelPaymentModel,
      )(false)(forceBctx)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 6)
      result.head.linkedRoomTypeCode should_== Some("")
    }

    "returns linkedRoomTypeCode as empty when stay date lies in blackout period of multiple days" in {
      val meta = aValidHotelInfoWithLinkageExclude
      val otaRoomRate = otaRoomRateCategory.copy(channelRates = Seq(aValidPropOfferChannelRate.withRemainingRooms(5)))
      val hrRoomRate = hrRoomRateCategory.copy(channelRates = Seq(aValidPropOfferChannelRate.withRemainingRooms(7)))
      val propOffer = po.copy(roomRates = Seq(otaRoomRate, hrRoomRate))

      val result = converter.buildPropOfferRoomEntry(propOffer,
                                                     meta,
                                                     otaRoomRate,
                                                     isBcomFixTaxAmountApplyToPB = false,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 5)
      result.head.linkedRoomTypeCode should_== None
    }

    "returns linkedRoomTypeCode as empty when stay date lies in blackout period of multiple days" in {
      val meta = aValidHotelInfoWithLinkageExclude
      val otaRoomRate = otaRoomRateCategory.copy(channelRates = Seq(aValidPropOfferChannelRate.withRemainingRooms(6)))
      val hrRoomRate = hrRoomRateCategory.copy(channelRates = Seq(aValidPropOfferChannelRate.withRemainingRooms(7)))
      val propOffer = po.copy(roomRates = Seq(otaRoomRate, hrRoomRate))

      val result = converter.buildPropOfferRoomEntry(propOffer,
                                                     meta,
                                                     otaRoomRate,
                                                     isBcomFixTaxAmountApplyToPB = false,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 6)
      result.head.linkedRoomTypeCode should_== None
    }

    "returns linkedRoomTypeCode as empty when supplier is not JTB" in {
      val propOffer = po.copy(supplyInfo = Some(aValidPropOfferSupplyInfo))
      val result = converter.buildPropOfferRoomEntry(propOffer,
                                                     meta,
                                                     otaRoomRateCategory,
                                                     isBcomFixTaxAmountApplyToPB = false,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      result.size must_== 1
      result.forall(_.remainingRooms should_== 6)
      result.head.linkedRoomTypeCode should_== None
    }

    "returns hourlyAvailableSlots when property offer has hourly slots" in {
      val result = converter.buildPropOfferRoomEntry(po,
                                                     meta,
                                                     hourlyRoomRateCategory,
                                                     isBcomFixTaxAmountApplyToPB = false,
                                                     po.taxes,
                                                     po.taxesV2,
                                                     isYCS = false,
                                                     hotelPaymentModel = hotelPaymentModel)(false)
      result.size must_== 1
      result.head.hourlyAvailableSlots.size should_== 1
      result.head.hourlyAvailableSlots.head.duration._1 should_== 3
      result.head.hourlyAvailableSlots.head.duration._2 should_== HOURS
      result.head.hourlyAvailableSlots.head.from.getHourOfDay should_== 10
    }

    "returns hourlyAvailableSlots correctly with HourlyBookingCutoffTime" in {
      val propOffer = po.withRoomRates(
        Seq(
          hourlyRoomRateCategory.withChannelRates(
            Seq(
              aValidPropOfferChannelRate.withHourlyAvailability(
                aValidHourlyAvailability.withHourlyBookingCutoffTime(TimeOfDay(hours = 1, minutes = 2, seconds = 3))),
            ),
          )))
      "when HourlyBookingCutoffTime > booking data time" in {
        val bookingDate = checkInDate.withTimeAtStartOfDay().plusHours(2)
        val result =
          converter.convert(propOffer, meta.withGmtOffset(7), aValidYplDispatchChannels, aValidFencedYplDispatchChannels)(
            ctx = aValidYplContext.withRequest(aValidYplRequest.withBookingDate(bookingDate)),
          )
        result.get.rooms.size must_== 0
      }
      "when HourlyBookingCutoffTime < booking data time" in {
        val bookingDate = checkInDate.withTimeAtStartOfDay().plusHours(1)
        val result =
          converter.convert(propOffer, meta.withGmtOffset(7), aValidYplDispatchChannels, aValidFencedYplDispatchChannels)(
            ctx = aValidYplContext.withRequest(aValidYplRequest.withBookingDate(bookingDate)),
          )
        result.get.rooms.size must_== 1
      }
    }
  }

  "filterRateFenceByOrigin" should {
    val converter = new PropertyOfferConverter with OriginManagerMock with CidToOriginMapperMock {}
    val customerSegments = Seq(
      com.agoda.supply.calc.proto.CustomerSegment.defaultInstance.withLanguageId(1).withCountryCode("TH"),
      com.agoda.supply.calc.proto.CustomerSegment.defaultInstance.withLanguageId(4).withCountryCode("US"),
    )
    val restriction =
      com.agoda.supply.calc.proto.BookingRestriction.defaultInstance.withCustomerSegments(customerSegments)
    val restrictions = Map(1 -> restriction)

    val yplRateFences = Set(YplRateFence("TH", 1, 1), YplRateFence("US", 2, 1), YplRateFence("UK", 3, 1))
    val channelRate = com.agoda.supply.calc.proto.ChannelRate.defaultInstance.withBookingRestrictionIdentifier(1)

    "not filter when whitelabel setting is not enable" in {
      val whitelabelSetting = aValidwhitelabelSetting.copy(isCustomerSegmentValidationEnabled = false)
      val req = aValidYplRequest.withWhitelabelSetting(whitelabelSetting)
      val ctx = aValidYplContext.withRequest(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val result = converter.filterRateFenceByCustomerSegment(restrictions, yplRateFences, channelRate, false)(ctx)
      result should_== yplRateFences
    }

    "not filter when customer segment is empty from empty restriction" in {
      val req = aValidYplRequest
      val ctx = aValidYplContext.withRequest(req)

      val restrictions: Map[Int, BookingRestriction] = Map.empty
      val result = converter.filterRateFenceByCustomerSegment(restrictions, yplRateFences, channelRate, false)(ctx)
      result should_== yplRateFences
    }

    "not filter when customer segment is empty from non exist restriction" in {
      val req = aValidYplRequest
      val ctx = aValidYplContext.withRequest(req)

      val invalidRestrictions = Map(2 -> restriction)
      val result =
        converter.filterRateFenceByCustomerSegment(invalidRestrictions, yplRateFences, channelRate, false)(ctx)
      result should_== yplRateFences
    }

    "filter by customer segment" in {
      val req = aValidYplRequest.withFences(Map(YplMasterChannel.RTL -> yplRateFences))
      val ctx = aValidYplContext.withRequest(req)

      val result = converter.filterRateFenceByCustomerSegment(restrictions, yplRateFences, channelRate, false)(ctx)
      result should_== Set(YplRateFence("TH", 1, 1))
    }

    "call CidToOriginMapper when isAboCidToOriginMappingEnabled is true" in {
      val mockCidToOriginMapper = mock[CidToOriginMapper]
      val mockOriginManager = mock[OriginManager]
      when(mockCidToOriginMapper.getOriginByCid(1)).thenReturn(Some("TH"))
      when(mockOriginManager.getOriginByCid(Some(1))).thenReturn(Some("TH"))

      val testConverter = new PropertyOfferConverter with OriginManagerMock with CidToOriginMapperMock {
        override def getOriginByCid(cid: Option[Int]): Option[String] = mockOriginManager.getOriginByCid(cid)
        override def getOriginByCid(cid: Int): Option[String] = mockCidToOriginMapper.getOriginByCid(cid)
      }

      val testFences = Set(YplRateFence("TH", 1, 1))
      val req = aValidYplRequest.withFences(Map(YplMasterChannel.RTL -> testFences))
      val ctx = aValidYplContext.withRequest(req)

      testConverter.filterRateFenceByCustomerSegment(restrictions, testFences, channelRate, true)(ctx)

      verify(mockOriginManager, times(0)).getOriginByCid(any())
      verify(mockCidToOriginMapper, times(1)).getOriginByCid(1)
      ok
    }

    "call OriginManager when isAboCidToOriginMappingEnabled is false" in {
      val mockCidToOriginMapper = mock[CidToOriginMapper]
      val mockOriginManager = mock[OriginManager]
      when(mockCidToOriginMapper.getOriginByCid(1)).thenReturn(Some("TH"))
      when(mockOriginManager.getOriginByCid(Some(1))).thenReturn(Some("TH"))

      val testConverter = new PropertyOfferConverter with OriginManagerMock with CidToOriginMapperMock {
        override def getOriginByCid(cid: Option[Int]): Option[String] = mockOriginManager.getOriginByCid(cid)
        override def getOriginByCid(cid: Int): Option[String] = mockCidToOriginMapper.getOriginByCid(cid)
      }

      val testFences = Set(YplRateFence("TH", 1, 1))
      val req = aValidYplRequest.withFences(Map(YplMasterChannel.RTL -> testFences))
      val ctx = aValidYplContext.withRequest(req)

      testConverter.filterRateFenceByCustomerSegment(restrictions, testFences, channelRate, false)(ctx)

      verify(mockOriginManager, times(1)).getOriginByCid(Some(1))
      verify(mockCidToOriginMapper, times(0)).getOriginByCid(any())
      ok
    }
  }

  "buildStackedChannelInfo" should {
    "when stacking only 1 level" in {
      val result = converter.buildStackedChannelInfo(
        Seq(
          StackedChannelInfo(1,
                             Seq(
                               StackedChannelInfo.StackedChannelDiscount(2, 10),
                             )),
          StackedChannelInfo(2,
                             Seq(
                               StackedChannelInfo.StackedChannelDiscount(1, 7),
                             )),
        ))
      result.size must_== 2
      result(1).size must_== 1
      result(2).size must_== 1
    }

    "when stacking only 2 or more level" in {
      val result = converter.buildStackedChannelInfo(
        Seq(
          StackedChannelInfo(1,
                             Seq(
                               StackedChannelInfo.StackedChannelDiscount(2, 10),
                               StackedChannelInfo.StackedChannelDiscount(3, 20),
                             )),
        ))
      result.size must_== 1
      result(1).size must_== 2
    }
  }

  "createChildRatePriceEntries" should {
    val childAgeRanges = Seq(
      MetaChildAgeRange(1, 9, 12, ChildAgeRangeType.ChildRate),
      MetaChildAgeRange(2, 6, 8, ChildAgeRangeType.ChildRate),
      MetaChildAgeRange(3, 3, 5, ChildAgeRangeType.ChildRate),
      MetaChildAgeRange(4, 0, 2, ChildAgeRangeType.ChildRate),
    )
    "not create when useJpChildRateOverAgeRanges is false" in {
      val childAgeRanges = Seq(MetaChildAgeRange(1, 6, 12, ChildAgeRangeType.NormalRange),
                               MetaChildAgeRange(3, 13, 18, ChildAgeRangeType.NormalRange))
      val result = converter.createChildRatePriceEntries(None, childAgeRanges, false)

      result.size must_== 0
    }

    "create empty when isChildRateEnabled is false" in {
      val childPricing = Seq(ChildPricing(1, 1, true, 100),
                             ChildPricing(2, 1, true, 100),
                             ChildPricing(3, 1, true, 100),
                             ChildPricing(4, 1, false, 100))
      val childRates = ChildRate(isChildRateEnabled = false, childPricing)

      val result = converter.createChildRatePriceEntries(Some(childRates), childAgeRanges, true)
      result.size must_== 0
    }

    "create correctly when useJpChildRateOverAgeRanges is true" in {
      val childPricing = Seq(ChildPricing(1, 1, true, 100),
                             ChildPricing(2, 1, true, 100),
                             ChildPricing(3, 1, true, 100),
                             ChildPricing(4, 1, false, 100))
      val childRates = ChildRate(isChildRateEnabled = true, childPricing)
      val result = converter.createChildRatePriceEntries(Some(childRates), childAgeRanges, true)

      val expected = List(
        ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 1, 9, 12, true, ChildAgeRangeType.ChildRate),
        ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 2, 6, 8, true, ChildAgeRangeType.ChildRate),
        ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 3, 3, 5, true, ChildAgeRangeType.ChildRate),
        ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 4, 0, 2, false, ChildAgeRangeType.ChildRate),
      )
      result.size must_== 4
      result should containTheSameElementsAs(expected)
    }

    "create correctly with fallback age when useJpChildRateOverAgeRanges is true" in {
      val childPricing = Seq(ChildPricing(1, 1, true, 100), ChildPricing(3, 1, true, 100))
      val childRates = ChildRate(isChildRateEnabled = true, childPricing)
      val result = converter.createChildRatePriceEntries(Some(childRates), childAgeRanges, true)

      val expected = List(
        ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 1, 6, 12, true, ChildAgeRangeType.ChildRate),
        ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 3, 0, 5, true, ChildAgeRangeType.ChildRate),
      )
      result.size must_== 2
      result should containTheSameElementsAs(expected)
    }

    "create correctly with fallback age when useJpChildRateOverAgeRanges is true" in {
      val childPricing = Seq(ChildPricing(2, 1, true, 100))
      val childRates = ChildRate(isChildRateEnabled = true, childPricing)
      val result = converter.createChildRatePriceEntries(Some(childRates), childAgeRanges, true)

      val expected = List(
        ChildPriceEntry(Some(100), PricingChildRateType.FlatPrice, 2, 0, 8, true, ChildAgeRangeType.ChildRate),
      )
      result.size must_== 1
      result should containTheSameElementsAs(expected)
    }
  }

  "buildParentRateCategoryMap" should {
    "skip resell room-ratecategory" in {
      val parentRatecategoryRoomRate = aValidPropOfferRoomRateCategory
      val childRatecategoryRoomRate = aValidPropOfferRoomRateCategory.copy(
        parentRateCategoryId = parentRatecategoryRoomRate.rateCategoryId,
        rateCategoryId = 11,
      )
      val resellChannelRates = Seq(
        aValidPropOfferChannelRate.copy(
          externalData = Seq(ExternalData(ExternalDataFields.ResellSourceBookingId, "123456")),
        ),
      )
      val resellRoomRate = parentRatecategoryRoomRate.copy(channelRates = resellChannelRates)
      val input = Seq(
        parentRatecategoryRoomRate,
        childRatecategoryRoomRate,
        resellRoomRate,
      )
      val actual = converter.buildParentRateCategoryMap(input)
      val expected = Map(
        ParentRateCategoryKey(10, 24453, 1) -> ParentRateCategoryValue(
          pa = parentRatecategoryRoomRate,
          ch = parentRatecategoryRoomRate.channelRates.head,
        ),
      )

      actual must_== expected
    }
  }

  "getCommissionHolder" should {
    val channelRate = aValidPropOfferChannelRate
    val fences = Set(aValidRateFence)
    val fencedChannelRate = FencedChannelRate(channelRate, fences)
    val parentChannelRate: Option[ChannelRate] = None
    val supplierId = 332
    val inventoryType = InventoryType.Agoda
    val channel = YplMasterChannel(channelRate.channelId)
    val commissions = Map.empty[Int, com.agoda.supply.calc.proto.Commission]
    val hotelInfo = aValidHotelInfo
    val fireDrillProto = Some(aValidFireDrillProto)
    val ctx = aValidYplContext.withExperimentContext(forcedFromRequestExperimentContext(aValidYplRequest)).build

    "should return non empty map" in {
      val commissionHolder = converter.getCommissionHolder(
        fencedChannelRate,
        parentChannelRate,
        commissions,
        supplierId,
        inventoryType,
        channel,
        hotelInfo,
        None,
        fireDrillProto,
        aValidYplDispatchChannels.build,
        Seq.empty,
        isLastMinuteBooking = false,
        hasNoCCPromotions = false,
        hotelId = aValidHotelId,
        Map.empty,
        Some(4.0),
        shouldBuildMORPCommissionHolder = true,
      )(ctx)
      commissionHolder.daily.isEmpty should_== (false)
      commissionHolder.morpCommissionHolder.morpCandidateRoomParametersSeq.nonEmpty should_== (true)
      commissionHolder.supplierContractedCommission should_== (Some(4.0))
    }

    "should return default morpCommissionHolder if shouldBuildMORPCommissionHolder is false " in {
      val commissionHolder = converter.getCommissionHolder(
        fencedChannelRate,
        parentChannelRate,
        commissions,
        supplierId,
        inventoryType,
        channel,
        hotelInfo,
        None,
        fireDrillProto,
        aValidYplDispatchChannels.build,
        Seq.empty,
        isLastMinuteBooking = false,
        hasNoCCPromotions = false,
        hotelId = aValidHotelId,
        Map.empty,
        Some(4.0),
        shouldBuildMORPCommissionHolder = false,
      )(ctx)
      commissionHolder.daily.isEmpty should_== (false)
      commissionHolder.morpCommissionHolder should_== MORPCommissionHolder.default
      commissionHolder.morpCommissionHolder.morpCandidateRoomParametersSeq.nonEmpty should_== (false)
      commissionHolder.supplierContractedCommission should_== (Some(4.0))
    }
  }

  "getRetailGlobalContractedCommission" should {
    "return correct value" in {
      val result = converter.getRetailGlobalContractedCommission(
        Map(
          1 -> aValidPropOfferCommission.withChannelId(1).withLanguageId(0).withContractedCommission(1d),
          2 -> aValidPropOfferCommission.withChannelId(2).withLanguageId(0).withContractedCommission(2d),
          3 -> aValidPropOfferCommission.withChannelId(1).withLanguageId(1).withContractedCommission(3d),
          4 -> aValidPropOfferCommission.withChannelId(2).withLanguageId(2).withContractedCommission(4d),
        ))
      result should_== 1d
    }

    "return 0 as fallback value" in {
      val result = converter.getRetailGlobalContractedCommission(Map.empty)
      result should_== 0d
    }
  }

  "getSupplierContractedCommission" should {
    "return empty if value is 0" in {
      val propOffer = aValidPropertyOffer.withSupplyInfo(aValidPropOfferSupplyInfo.withContractedCommission(0))
      val result = converter.getSupplierContractedCommission(propOffer)
      result should_== (None)
    }

    "return empty if value is less than zero" in {
      val propOffer = aValidPropertyOffer.withSupplyInfo(aValidPropOfferSupplyInfo.withContractedCommission(-1.0))
      val result = converter.getSupplierContractedCommission(propOffer)
      result should_== (None)
    }

    "return correct value if value is greater than 0" in {
      val propOffer = aValidPropertyOffer.withSupplyInfo(aValidPropOfferSupplyInfo.withContractedCommission(1.0))
      val result = converter.getSupplierContractedCommission(propOffer)
      result should_== (Some(1.0))
    }
  }

  "filterRequiredPropOfferChannels" should {
    "should return roomRate if channel is in DispatchChannel" in {
      val propOffer = aValidPropertyOffer.withRateRepurpose(Seq.empty)
      val masterChannels =
        propOffer.roomRates.flatMap(r => r.channelRates.map(_.channelId)).map(YplMasterChannel(_)).toSet
      val dispatchChannels = aValidYplDispatchChannels.withHelperChannels(Set.empty).withMasterChannels(masterChannels)

      val filterMonad = converter.filterRequiredPropOfferChannels(propOffer, dispatchChannels)
      val result = filterMonad.map(identity)

      result.size shouldEqual (1)
    }

    "should return roomRate if channel is in rateRepurpose" in {
      val propOffer = aValidPropertyOffer.withRateRepurpose(Seq(aValidPropOfferRateRepurposeInfo))
      val dispatchChannels = aValidYplDispatchChannels.withHelperChannels(Set.empty).withMasterChannels(Set.empty)

      val filterMonad = converter.filterRequiredPropOfferChannels(propOffer, dispatchChannels)
      val result = filterMonad.map(identity)

      result.size shouldEqual (1)
    }

    "should not return roomRate as channel is not in both dispatch or rateRepurpose" in {
      val roomRate1 = aValidPropOfferRoomRateCategory.withChannelRates(Seq.empty)
      val roomRate2 = aValidPropOfferRoomRateCategory.withChannelRates(Seq(aValidPropOfferChannelRate))
      val propOffer = aValidPropertyOffer.withRateRepurpose(Seq.empty).withRoomRates(Seq(roomRate1, roomRate2))
      val dispatchChannels = aValidYplDispatchChannels.withHelperChannels(Set.empty).withMasterChannels(Set.empty)

      val filterMonad = converter.filterRequiredPropOfferChannels(propOffer, dispatchChannels)
      val result = filterMonad.map(identity)

      result.size shouldEqual (0)
    }
  }

  "MORP NoCc Negative Test should return correct agencyBookingType" should {
    implicit val ctx = aValidYplContext
    val agodaAgencyFeatures = AgodaAgencyFeatures.default
      .withIsAgodaAgencyEnable(true)
      .withAgencyBookingType(1)
      .withCommissionList(List(AgodaAgencyCommission(10.0)))
      .build
    val meta = aValidHotelInfo.withAgodaAgencyFeatures(Some(agodaAgencyFeatures))

    "should return agencyBookingType = 1 if MORP NoCc Negative Rate Channel is not provided" in {
      val propOffer = aValidPropertyOffer.withRateRepurpose(Seq.empty)
      val masterChannels =
        propOffer.roomRates.flatMap(r => r.channelRates.map(_.channelId)).map(YplMasterChannel(_)).toSet
      val dispatchChannels = aValidYplDispatchChannels.withHelperChannels(Set.empty).withMasterChannels(masterChannels)

      val result =
        converter.convert(propOffer, meta.withGmtOffset(7), dispatchChannels, aValidFencedYplDispatchChannels)(
          ctx = aValidYplContext.withRequest(aValidYplRequest),
        )

      result.get.metaData.agencyFeatures.get.agencyBookingType shouldEqual (1)
    }

    "should return agencyBookingType = 0 if MORP NoCc Negative Rate Channel is provided" in {
      val propOffer = aValidPropertyOffer.withRateRepurpose(Seq.empty)
      val channels = Set(YplMasterChannel(Channel.MORPNoCcNegativeMock))
      val dispatchChannels = aValidYplDispatchChannels.withHelperChannels(Set.empty).withMasterChannels(channels)

      val result =
        converter.convert(propOffer, meta.withGmtOffset(7), dispatchChannels, aValidFencedYplDispatchChannels)(
          ctx = aValidYplContext.withRequest(aValidYplRequest),
        )

      result.get.metaData.agencyFeatures.get.agencyBookingType shouldEqual (0)
    }
  }

  "checkRoomBasedOnBooking" should {
    val experiment = forcedFromRequestExperimentContext(
      aValidYplRequest.withBExperiment(_root_.models.consts.ABTest.GHOST_ROOM_FILTERING),
    )
    val meta = aValidHotelInfo.withEnabledRoom(
      Map(
        24453L -> aValidEnabledRoom.withRoomBookings(
          Seq(
            RoomBookings(inDays = 30, count = 10),
            RoomBookings(inDays = 90, count = 20),
            RoomBookings(inDays = 180, count = 30),
          )),
      ),
    )
    val dispatchChannels = YplDispatchChannels(Set(YplMasterChannel(1)), Set.empty)
    val po = aValidPropertyOffer
    "do not filter when minBookingCountForSuperAgg is None" in {
      implicit val ctx = aValidYplContext.withExperimentContext(experiment).build
      val meta = aValidHotelInfo.withEnabledRoom(Map(24453L -> aValidEnabledRoom))
      val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

      result.size shouldEqual 1
    }
    "filter when 30 days minBookingCount is sent" should {
      "when minBookingCountForSuperAgg < withBookingLast30Days" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(
            inDays = 30,
            count = 1,
          ))
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 1
      }
      "when minBookingCountForSuperAgg > withBookingLast30Days" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(
            inDays = 30,
            count = 15,
          ))
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 0
      }
    }
    "filter when 90 days minBookingCount is sent" should {
      "when minBookingCountForSuperAgg < withBookingLast90Days" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(
            inDays = 90,
            count = 2,
          ))
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 1
      }
      "when minBookingCountForSuperAgg > withBookingLast90Days" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(
            inDays = 90,
            count = 25,
          ))
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 0
      }
    }
    "filter when 180 days minBookingCount is sent" should {
      "when minBookingCountForSuperAgg < withBookingLast90Days" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(
            inDays = 180,
            count = 3,
          ))
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 1
      }
      "when minBookingCountForSuperAgg > withBookingLast90Days" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(
            inDays = 180,
            count = 35,
          ))
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 0
      }
    }
    "filter when 35 days minBookingCount is sent" should {
      "when minBookingCountForSuperAgg 3 is not given" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(
            inDays = 35,
            count = 3,
          ))
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 1
      }
      "when minBookingCountForSuperAgg 100 not given" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(inDays = 35, count = 100),
        )
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 1
      }
    }
    "inDays 30 present and inDays 35 not present" should {
      "when minBookingCountForSuperAgg < withBookingLast90Days" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(inDays = 35, count = 100),
          YplMinBookingCountForSuperAgg(inDays = 30, count = 1),
        )
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 1
      }
      "when minBookingCountForSuperAgg > withBookingLast90Days" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(inDays = 35, count = 100),
          YplMinBookingCountForSuperAgg(inDays = 30, count = 15),
        )
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 0
      }
    }

    "inDays 30 present and inDays 90 present" should {
      "when 30 days is true and 90 days is true" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(inDays = 30, count = 5),
          YplMinBookingCountForSuperAgg(inDays = 90, count = 15),
        )
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 1
      }
      "when 30 days is true and 90 days is false" in {
        val yplMinBookingCountForSuperAgg = Seq(
          YplMinBookingCountForSuperAgg(inDays = 30, count = 5),
          YplMinBookingCountForSuperAgg(inDays = 90, count = 25),
        )
        val request = aValidYplRequest.withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg)
        implicit val ctx = aValidYplContext.withRequest(request).withExperimentContext(experiment).build
        val result = converter.preFilterRoomRate(po, dispatchChannels, meta)(ctx).map(identity)

        result.size shouldEqual 0
      }
    }

  }
}
