package com.agoda.papi.ypl.pricing.promotions

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.enums.request.StackDiscountOption
import com.agoda.papi.ypl.models.api.request.YplOccInfo
import com.agoda.papi.ypl.models.pricing.proto.{AdditionalDispatchReason, CustomerSegment, DailyPrice, PromotionEntry}
import com.agoda.papi.ypl.models.{YPLTestContexts, YPLTestDataBuilders, YplContext, YplExperiment, YplExperiments}
import com.agoda.papi.ypl.pricing.promotions.PromotionConstant._
import com.agoda.papi.enums.room.{ChargeOption, ChargeType, DiscountType}
import com.agoda.papi.ypl.models.consts.MegaSalePromotions
import com.agoda.papi.ypl.pricing.CancellationPolicyServiceImpl
import com.agoda.papi.ypl.pricing.mocks.{CidToOriginMapperMock, OriginManagerMock, PromotionEntryValidationMock}
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.core.Fragment

class StackablePromoSpec extends SpecificationWithJUnit with YPLTestDataBuilders with YPLTestContexts {

  import DiscountType._
  import StackDiscountOption._

  val converter = new {}
    with PromotionCalculationImpl
      with CancellationPolicyServiceImpl
      with PromotionEntryValidationMock
      with CidToOriginMapperMock {
    override def getOriginByCid(cid: Option[Int]): Option[String] = None
    override def getOriginByCidAndHotelCountry(cid: Option[Int], hotelCountry: Option[String]): Option[String] = None

    override val originManager = this
    override val promotionEntryValidation = this
    override val cidToOriginMapper = this
  }

  val FREENIGHT_DISC = List(100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0)
  val FIFTEEN_DISC = List(15.0, 15.0, 15.0, 15.0, 15.0, 15.0, 15.0)
  val TEN_DISC = List(10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0)
  val FIVE_DISC = List(5.0, 5.0, 5.0, 5.0, 5.0, 5.0, 5.0)
  val TWO_DISC = List(2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0)
  val ZERO_DISC = List(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
  val FIFTY_DISC = List(50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0)

  "Apply Stackable Promotions" should {
    val stub = new StackablePromotions with CancellationPolicyServiceImpl with PromotionEntryValidationMock {
      override val originManager = new {} with OriginManagerMock {}
      override val promotionEntryValidation = this
      override val cidToOriginMapper = new {} with CidToOriginMapperMock {}
    }
    val checkin = DateTime.parse("2017-02-06")
    val los = 1
    val basePromo = mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los))
    val ctx = mockYplContext(checkin, los)

    "1. Return the room if at least 1 stackable promotion is applied " in {
      val applicableStackPromo =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))

      val room = aValidRoomEntry
        .withPromotion(basePromo)
        .withAvailablePromotions(List(basePromo, applicableStackPromo))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val res = stub.applyStackablePromotions(room,
                                              List(applicableStackPromo),
                                              checkin.minusDays(1),
                                              applyDiscountsMultiplicatively = false,
                                              promotionTypeIdToPulseCampaignMetadata = Map.empty)(aValidYplRequest, ctx)
      res should not beEmpty
    }

    "2. Don't return the room if no stackable promotion can be applied " in {
      val outOfRangeDates = Map(checkin.minusDays(20) -> 0)
      val unapplicableStackPromo =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, outOfRangeDates)

      val room = aValidRoomEntry
        .withPromotion(basePromo)
        .withAvailablePromotions(List(basePromo, unapplicableStackPromo))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val res = stub.applyStackablePromotions(room,
                                              List(unapplicableStackPromo),
                                              checkin.minusDays(1),
                                              applyDiscountsMultiplicatively = false,
                                              promotionTypeIdToPulseCampaignMetadata = Map.empty)(aValidYplRequest, ctx)
      res should beEmpty
    }

    "3. Return the room if at least 1 stackable promotion is applied and ignore unapplicable promotion" in {
      val outOfRangeDates = Map(checkin.minusDays(20) -> 0)
      val unapplicableStackPromo =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, outOfRangeDates)
      val applicableStackPromo =
        mockPromo(11, PercentDiscount, Option(Additive), FIFTEEN_DISC, false, true, generateApplyDates(checkin, los))

      val room = aValidRoomEntry
        .withPromotion(basePromo)
        .withAvailablePromotions(List(basePromo, applicableStackPromo, unapplicableStackPromo))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val res = stub.applyStackablePromotions(
        room,
        List(applicableStackPromo, unapplicableStackPromo),
        checkin.minusDays(1),
        applyDiscountsMultiplicatively = false,
        promotionTypeIdToPulseCampaignMetadata = Map.empty,
      )(aValidYplRequest, ctx)
      res.map(_.roomDailyPrices.map(_.promoDiscount)) should_== Some(List(15.0))
    }

    "4. Return the room if at least 1 stackable promotion is applied and only best applicable pulse stackable promotion is applied" in {
      val outOfRangeDates = Map(checkin.minusDays(20) -> 0)
      val unapplicableStackPromo = mockPromo(10,
                                             PercentDiscount,
                                             Option(Additive),
                                             FIFTEEN_DISC,
                                             isAllowStack = false,
                                             isStackable = true,
                                             outOfRangeDates).copy(typeId = 1)
      val applicableStackPromo = mockPromo(11,
                                           PercentDiscount,
                                           Option(Additive),
                                           TEN_DISC,
                                           isAllowStack = false,
                                           isStackable = true,
                                           generateApplyDates(checkin, los)).copy(typeId = 2)
      val newerApplicableStackPromo = mockPromo(12,
                                                PercentDiscount,
                                                Option(Additive),
                                                TEN_DISC,
                                                isAllowStack = false,
                                                isStackable = true,
                                                generateApplyDates(checkin, los)).copy(typeId = 3)
      val worseApplicableStackPromo = mockPromo(13,
                                                PercentDiscount,
                                                Option(Additive),
                                                FIVE_DISC,
                                                isAllowStack = false,
                                                isStackable = true,
                                                generateApplyDates(checkin, los)).copy(typeId = 4)
      val duplicateApplicableStackPromo = mockPromo(14,
                                                    PercentDiscount,
                                                    Option(Additive),
                                                    TEN_DISC,
                                                    isAllowStack = false,
                                                    isStackable = true,
                                                    generateApplyDates(checkin, los)).copy(typeId = 3)

      val pulseCampaignMetadata = Map(
        1 -> PulseCampaignMetadata(1, 1, 1, 1, 1),
        2 -> PulseCampaignMetadata(2, 1, 1, 1, 1),
        3 -> PulseCampaignMetadata(3, 1, 1, 1, 1),
        4 -> PulseCampaignMetadata(4, 1, 1, 1, 1),
      )

      val promotions = List(unapplicableStackPromo,
                            applicableStackPromo,
                            newerApplicableStackPromo,
                            worseApplicableStackPromo,
                            duplicateApplicableStackPromo)
      val room = aValidRoomEntry
        .withPromotion(basePromo)
        .withAvailablePromotions(
          List(basePromo,
               unapplicableStackPromo,
               applicableStackPromo,
               newerApplicableStackPromo,
               worseApplicableStackPromo,
               duplicateApplicableStackPromo))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val res = stub.applyStackablePromotions(
        room,
        promotions,
        checkin.minusDays(1),
        applyDiscountsMultiplicatively = false,
        promotionTypeIdToPulseCampaignMetadata = pulseCampaignMetadata)(aValidYplRequest, mockYplContext(checkin, los))
      res.map(_.roomDailyPrices.map(_.promoDiscount)) should_== Some(List(10.0))
      res.flatMap(_.pulseCampaignMetadata) should_== Some(PulseCampaignMetadata(3, 1, 1, 1, 1))
      res.map(_.promotionsBreakdown.values.head.size) should_== Some(1)
      res.map(_.promotionsBreakdown.values.head.head) should_== Some(duplicateApplicableStackPromo)
    }

    "5. Return the room without pulse data if only best applicable normal stackable promotion is applied" in {
      val applicableStackPromo = mockPromo(11,
                                           PercentDiscount,
                                           Option(Additive),
                                           TEN_DISC,
                                           isAllowStack = false,
                                           isStackable = true,
                                           generateApplyDates(checkin, los)).copy(typeId = 1)

      val pulseCampaignMetadata = Map(
        2 -> PulseCampaignMetadata(2, 1, 1, 1, 1),
      )

      val promotions = List(applicableStackPromo)
      val room = aValidRoomEntry
        .withPromotion(basePromo)
        .withAvailablePromotions(List(basePromo, applicableStackPromo))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val res = stub.applyStackablePromotions(
        room,
        promotions,
        checkin.minusDays(1),
        applyDiscountsMultiplicatively = false,
        promotionTypeIdToPulseCampaignMetadata = pulseCampaignMetadata)(aValidYplRequest, mockYplContext(checkin, los))
      res.map(_.roomDailyPrices.map(_.promoDiscount)) should_== Some(List(10.0))
      res.flatMap(_.pulseCampaignMetadata) should_== None
      res.map(_.promotionsBreakdown.values.head.size) should_== Some(1)
      res.map(_.promotionsBreakdown.values.head.head) should_== Some(applicableStackPromo)
    }

    "6. Return none room if 2 normal and pulse stackable promotion are applicable but total discount >= 100" in {
      val applicableStackPromo1 = mockPromo(1,
                                            PercentDiscount,
                                            Option(Additive),
                                            FIFTY_DISC,
                                            isAllowStack = false,
                                            isStackable = true,
                                            generateApplyDates(checkin, los)).copy(typeId = 1)
      val applicableStackPromo2 = mockPromo(2,
                                            PercentDiscount,
                                            Option(Additive),
                                            FIFTY_DISC,
                                            isAllowStack = false,
                                            isStackable = true,
                                            generateApplyDates(checkin, los)).copy(typeId = 2)

      val pulseCampaignMetadata = Map(
        1 -> PulseCampaignMetadata(2, 1, 1, 1, 1),
      )

      val promotions = List(applicableStackPromo1, applicableStackPromo2)
      val room = aValidRoomEntry
        .withPromotion(basePromo)
        .withAvailablePromotions(List(basePromo, applicableStackPromo1, applicableStackPromo2))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val res = stub.applyStackablePromotions(
        room,
        promotions,
        checkin.minusDays(1),
        applyDiscountsMultiplicatively = false,
        promotionTypeIdToPulseCampaignMetadata = pulseCampaignMetadata)(aValidYplRequest, mockYplContext(checkin, los))
      res should beEmpty
    }
  }

  "Basic Stackable Promotion" should {

    val checkin = DateTime.parse("2017-02-06")
    val los = 1

    "1. One normal promo and one stack promo, should apply both" in {
      val ctx = mockYplContext(checkin, los)
      val basePromo1 = mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los))

      val stackPromo1 =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))

      val room = aValidRoomEntry
        .withAvailablePromotions(List(basePromo1, stackPromo1))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (2)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      val stackRoom = promoRooms.find(p => p.promotion.get.id == CombinedId).get
      preStackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (10.0)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (15.0)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (2)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 10) should_== (true)
    }

    "2. StackDiscountOption = Multiplicative, apply discount multiplicatively" in {
      val ctx = mockYplContext(checkin, los)
      val basePromo1 = mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los))

      val stackPromo1 =
        mockPromo(10, PercentDiscount, Option(Multiplicative), FIVE_DISC, false, true, generateApplyDates(checkin, los))

      val room = aValidRoomEntry
        .withAvailablePromotions(List(basePromo1, stackPromo1))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)

      promoRooms.length should_== (2)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      val stackRoom = promoRooms.find(p => p.promotion.get.id == CombinedId).get
      preStackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (10.0)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.promotion.get.id should_== (CombinedId)
      stackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (14.5)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (2)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 10) should_== (true)
    }

    "3. Multiple stack promos, should apply all" in {
      val ctx = mockYplContext(checkin, los)
      val basePromo1 =
        mockPromo(1, AmountDiscountPerNight, None, TEN_DISC, true, false, generateApplyDates(checkin, los))

      val stackPromo1 =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
      val stackPromo2 =
        mockPromo(11, PercentDiscount, Option(Additive), TWO_DISC, false, true, generateApplyDates(checkin, los))

      val room = aValidRoomEntry
        .withAvailablePromotions(List(basePromo1, stackPromo1, stackPromo2))
        .withDailyPrices(generateDailyPrices(checkin, los, price = 200))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (2)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      val stackRoom = promoRooms.find(p => p.promotion.get.id == CombinedId).get
      preStackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (10.0)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.promotion.get.id should_== (CombinedId)
      stackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (24.00)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (3)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 10) should_== (true)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 11) should_== (true)
    }

    "4. Multiple stack promos and different discount option, should pack most restrictive option" in {
      val ctx = mockYplContext(checkin, los)
      val basePromo1 = mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los))

      val stackPromo1 =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
      val stackPromo2 =
        mockPromo(11, PercentDiscount, Option(Multiplicative), TWO_DISC, false, true, generateApplyDates(checkin, los))

      val room = aValidRoomEntry
        .withAvailablePromotions(List(basePromo1, stackPromo1, stackPromo2))
        .withDailyPrices(generateDailyPrices(checkin, los, price = 200))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (2)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      val stackRoom = promoRooms.find(p => p.promotion.get.id == CombinedId).get
      preStackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (20.0)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.promotion.get.id should_== (CombinedId)
      stackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (32.42)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (3)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 10) should_== (true)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 11) should_== (true)
    }

    "5. Doesn't have normal promo, should apply stack promo" in {
      val ctx = mockYplContext(checkin, los)
      val stackPromo1 =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))

      val room =
        aValidRoomEntry.withAvailablePromotions(List(stackPromo1)).withDailyPrices(generateDailyPrices(checkin, los))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (1)
      val stackRoom = promoRooms.find(p => p.promotion.get.id == 10).get
      stackRoom.promotion.get.id should_== (10)
      stackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (5.0)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (1)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 10) should_== (true)
    }

    "6. Normal promo doesn't allow stack discount, shouldn't apply stack promo" in {
      val ctx = mockYplContext(checkin, los)
      val basePromo1 = mockPromo(1, PercentDiscount, None, TEN_DISC, false, false, generateApplyDates(checkin, los))

      val stackPromo1 =
        mockPromo(10, PercentDiscount, Option(Multiplicative), FIVE_DISC, false, true, generateApplyDates(checkin, los))
      val stackPromo2 =
        mockPromo(11, PercentDiscount, Option(Multiplicative), TWO_DISC, false, true, generateApplyDates(checkin, los))

      val room = aValidRoomEntry
        .withAvailablePromotions(List(basePromo1, stackPromo1, stackPromo2))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (1)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      preStackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (10.0)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
    }

    "7. Multiple normal promos, shouldn return cheapest room after apply stack promos" in {
      val ctx = mockYplContext(checkin, los)
      val basePromo1 = mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los))
      val basePromo2 = mockPromo(2, PercentDiscount, None, FIFTEEN_DISC, false, false, generateApplyDates(checkin, los))

      val stackPromo1 =
        mockPromo(10, PercentDiscount, Some(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
      val stackPromo2 =
        mockPromo(11, PercentDiscount, Some(Additive), TWO_DISC, false, true, generateApplyDates(checkin, los))

      val room = aValidRoomEntry
        .withAvailablePromotions(List(basePromo1, basePromo2, stackPromo1, stackPromo2))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (2)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      val stackRoom = promoRooms.find(p => p.promotion.get.id == CombinedId).get
      preStackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (15.0)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 2) should_== (true)
      stackRoom.promotion.get.id should_== (CombinedId)
      stackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (17.0)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (3)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 10) should_== (true)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 11) should_== (true)
    }

    "8. Freenight promo, shouldn't apply any stack promo" in {

      val los = 2
      val ctx = mockYplContext(checkin, los)

      val basePromo1 = mockPromo(1, FreeNight, None, FREENIGHT_DISC, true, false, generateApplyDates(checkin, 1))

      val stackPromo1 =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
      val stackPromo2 =
        mockPromo(11, PercentDiscount, Option(Additive), TWO_DISC, false, true, generateApplyDates(checkin, los))

      val room = aValidRoomEntry
        .withAvailablePromotions(List(basePromo1, stackPromo1, stackPromo2))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (1)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      preStackRoom.promotion.get.id should_== (1)
      preStackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (100.0)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
    }

    "9. Multiple stack promos, should apply all and pick most restrictive cancellation" in {
      val ctx = mockYplContext(checkin, los)
      val basePromo1 =
        mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los), "1D1N_1N")

      val stackPromo1 = mockPromo(10,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIVE_DISC,
                                  false,
                                  true,
                                  generateApplyDates(checkin, los),
                                  "7D1N_1N")
      val stackPromo2 = mockPromo(11,
                                  PercentDiscount,
                                  Option(Additive),
                                  TWO_DISC,
                                  false,
                                  true,
                                  generateApplyDates(checkin, los),
                                  "3D1N_1N")

      val room = aValidRoomEntry
        .withAvailablePromotions(List(basePromo1, stackPromo1, stackPromo2))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (2)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      val stackRoom = promoRooms.find(p => p.promotion.get.id == CombinedId).get
      preStackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (10.0)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.cxlCode should_== ("7D1N_1N")
      stackRoom.promotion.get.id should_== (CombinedId)
      stackRoom.dailyPrices(checkin).prices.head.promoDiscount should_== (17.0)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).length should_== (3)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) should_== (true)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 10) should_== (true)
      stackRoom.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 11) should_== (true)
    }
  }

  "Stackable Promotion with Combined Promotion" should {
    val checkin = DateTime.parse("2017-02-06")
    val roomWithCombinedPromo = aValidRoomEntry.withAllowedCombinePromotion(true)

    "1. Combined promotions with single stack promo, should apply stack promo on different normal promo" in {
      val los = 2
      val ctx = mockYplContext(checkin, los)

      val basePromo1 =
        mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, Map(checkin -> toDiscountIndex(checkin)), "1D1N_1N")
      val basePromo2 = mockPromo(2,
                                 PercentDiscount,
                                 None,
                                 FIFTEEN_DISC,
                                 true,
                                 false,
                                 Map(checkin.plusDays(1) -> toDiscountIndex(checkin.plusDays(1))),
                                 "1D1N_1N")

      val stackPromo1 = mockPromo(10,
                                  PercentDiscount,
                                  Option(Multiplicative),
                                  FIVE_DISC,
                                  false,
                                  true,
                                  generateApplyDates(checkin, los),
                                  "7D1N_1N")

      val room = roomWithCombinedPromo
        .withAvailablePromotions(List(basePromo1, basePromo2, stackPromo1))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (2)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      val stackRooms = promoRooms.filter(p => p.promotion.get.id == CombinedId)
      preStackRoom.promotion.get.id should_== (2)
      preStackRoom.dailyPrices(checkin.plusDays(1)).prices.head.promoDiscount should_== (15.0)
      preStackRoom.promotionsBreakdown(checkin.plusDays(1).toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.plusDays(1).toLocalDate).exists(p => p.id == 2) should_== (true)

      stackRooms.exists { r =>
        r.promotion.get.id == CombinedId &&
        r.dailyPrices(checkin).prices.head.promoDiscount == 14.5 &&
        r.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) &&
        r.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 10) &&
        r.dailyPrices(checkin.plusDays(1)).prices.head.promoDiscount == 19.25 &&
        r.promotionsBreakdown(checkin.plusDays(1).toLocalDate).exists(p => p.id == 2) &&
        r.promotionsBreakdown(checkin.plusDays(1).toLocalDate).exists(p => p.id == 10)
      } should_== true

    }

    "2. Combined promotions with multiple stack promo and different discount type, select correct discount type per night" in {
      val los = 2
      val ctx = mockYplContext(checkin, los)

      val basePromo1 =
        mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, Map(checkin -> toDiscountIndex(checkin)), "1D1N_1N")
      val basePromo2 = mockPromo(2,
                                 PercentDiscount,
                                 None,
                                 FIFTEEN_DISC,
                                 true,
                                 false,
                                 Map(checkin.plusDays(1) -> toDiscountIndex(checkin.plusDays(1))),
                                 "1D1N_1N")

      val stackPromo1 = mockPromo(10,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIVE_DISC,
                                  false,
                                  true,
                                  Map(checkin -> toDiscountIndex(checkin)),
                                  "7D1N_1N")
      val stackPromo2 = mockPromo(11,
                                  PercentDiscount,
                                  Option(Multiplicative),
                                  FIVE_DISC,
                                  false,
                                  true,
                                  Map(checkin.plusDays(1) -> toDiscountIndex(checkin.plusDays(1))),
                                  "7D1N_1N")

      val room = roomWithCombinedPromo
        .withAvailablePromotions(List(basePromo1, basePromo2, stackPromo1, stackPromo2))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (2)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      val stackRooms = promoRooms.filter(p => p.promotion.get.id == CombinedId)
      preStackRoom.promotion.get.id should_== (2)
      preStackRoom.dailyPrices(checkin.plusDays(1)).prices.head.promoDiscount should_== (15.0)
      preStackRoom.promotionsBreakdown(checkin.plusDays(1).toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.plusDays(1).toLocalDate).exists(p => p.id == 2) should_== (true)

      stackRooms.exists { r =>
        r.promotion.get.id == CombinedId &&
        r.dailyPrices(checkin).prices.head.promoDiscount == 15.0 &&
        r.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) &&
        r.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 10) &&
        r.dailyPrices(checkin.plusDays(1)).prices.head.promoDiscount == 19.25 &&
        r.promotionsBreakdown(checkin.plusDays(1).toLocalDate).exists(p => p.id == 2) &&
        r.promotionsBreakdown(checkin.plusDays(1).toLocalDate).exists(p => p.id == 11)
      } should_== true
    }

    "3. Combined promotions but some night doesn't have promo" in {
      val los = 3
      val ctx = mockYplContext(checkin, los)

      val basePromo1 =
        mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, Map(checkin -> toDiscountIndex(checkin)), "1D1N_1N")
      val basePromo2 = mockPromo(2,
                                 PercentDiscount,
                                 None,
                                 FIFTEEN_DISC,
                                 true,
                                 false,
                                 Map(checkin.plusDays(2) -> toDiscountIndex(checkin.plusDays(2))),
                                 "1D1N_1N")

      val stackPromo1 = mockPromo(10,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIVE_DISC,
                                  false,
                                  true,
                                  generateApplyDates(checkin, los),
                                  "7D1N_1N")

      val room = roomWithCombinedPromo
        .withAvailablePromotions(List(basePromo1, basePromo2, stackPromo1))
        .withDailyPrices(generateDailyPrices(checkin, los))

      val promoRooms = converter.generatePromotionalRooms(aValidHotelInfo, List(room))(ctx)
      promoRooms.length should_== (2)
      val preStackRoom = promoRooms.find(p => p.promotion.get.id != CombinedId).get
      val stackRooms = promoRooms.filter(p => p.promotion.get.id == CombinedId)
      preStackRoom.promotion.get.id should_== (2)
      preStackRoom.dailyPrices(checkin.plusDays(2)).prices.head.promoDiscount should_== (15.0)
      preStackRoom.promotionsBreakdown(checkin.plusDays(2).toLocalDate).length should_== (1)
      preStackRoom.promotionsBreakdown(checkin.plusDays(2).toLocalDate).exists(p => p.id == 2) should_== (true)

      stackRooms.exists { r =>
        r.promotion.get.id == CombinedId &&
        r.dailyPrices(checkin).prices.head.promoDiscount == 15.0 &&
        r.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 1) &&
        r.promotionsBreakdown(checkin.toLocalDate).exists(p => p.id == 10) &&
        r.dailyPrices(checkin.plusDays(2)).prices.head.promoDiscount == 20.0 &&
        r.promotionsBreakdown(checkin.plusDays(2).toLocalDate).exists(p => p.id == 2) &&
        r.promotionsBreakdown(checkin.plusDays(2).toLocalDate).exists(p => p.id == 10)
      } should_== true
    }
  }

  "NoCC Promotion" should {
    val checkin = DateTime.parse("2018-09-18")

    "Apply without normal promotions and not change promotion object" in {
      val promoId = 12121
      val los = 2
      val ctx = mockYplContext(checkin, los, None)
      val noCCPromo = mockPromo(promoId,
                                PercentDiscount,
                                Option(Multiplicative),
                                ZERO_DISC,
                                true,
                                true,
                                generateApplyDates(checkin, los),
                                "1D1N_1N").copy(typeId = PromotionConstant.NoCCRequiredTypeId)

      val room = aValidRoomEntry
        .withAvailablePromotions(List(noCCPromo))
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val hotelInfo = aValidHotelInfo.withAgodaAgency(true)

      val promoRooms = converter.generatePromotionalRooms(hotelInfo, List(room))(ctx)
      promoRooms.length should_== (1)
      promoRooms.head.promotion.isEmpty should_== true
      val promoBreakdown = promoRooms.head.promotionsBreakdown
      promoBreakdown.size should_== los
      promoBreakdown.zipWithIndex.forall { case ((date, promos), i) =>
        promos.length == 1 && promos.head.id == promoId && promos.head.getDiscount(checkin.plusDays(i)) == 0.0
      } should_== (true)
    }

    "Not apply on Merchant room" in {
      val promoId = 12121
      val los = 2
      val ctx = mockYplContext(checkin, los, None)
      val noCCPromo = mockPromo(promoId,
                                PercentDiscount,
                                Option(Multiplicative),
                                ZERO_DISC,
                                true,
                                true,
                                generateApplyDates(checkin, los),
                                "1D1N_1N").copy(typeId = PromotionConstant.NoCCRequiredTypeId)

      val room =
        aValidRoomEntry.withAvailablePromotions(List(noCCPromo)).withDailyPrices(generateDailyPrices(checkin, los))

      val hotelInfo = aValidHotelInfo.withAgodaAgency(true)

      val promoRooms = converter.generatePromotionalRooms(hotelInfo, List(room))(ctx)
      promoRooms.length should_== (0)
    }

    "Combine with normal promotion" in {
      val los = 2
      val ctx = mockYplContext(checkin, los, None)
      val normalPromo = mockPromo(1,
                                  PercentDiscount,
                                  Option(Multiplicative),
                                  TEN_DISC,
                                  true,
                                  true,
                                  generateApplyDates(checkin, los),
                                  "1D1N_1N")
      val noCCPromo = mockPromo(10,
                                PercentDiscount,
                                Option(Multiplicative),
                                ZERO_DISC,
                                true,
                                true,
                                generateApplyDates(checkin, los),
                                "1D1N_1N").copy(typeId = PromotionConstant.NoCCRequiredTypeId)

      val room = aValidRoomEntry
        .withAvailablePromotions(List(normalPromo, noCCPromo))
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val hotelInfo = aValidHotelInfo.withAgodaAgency(true)

      val promoRooms = converter.generatePromotionalRooms(hotelInfo, List(room))(ctx)
      promoRooms.length should_== (1)
      promoRooms.head.promotion.get.id should_== normalPromo.id
      promoRooms.head.totalPromoDiscount == 20.0
    }

    "Combine with other no-cc promotion" in {
      val los = 2
      val ctx = mockYplContext(checkin, los, None)
      val stackPromo1 = mockPromo(10,
                                  PercentDiscount,
                                  Option(Multiplicative),
                                  ZERO_DISC,
                                  true,
                                  true,
                                  generateApplyDates(checkin, los),
                                  "1D1N_1N").copy(typeId = PromotionConstant.NoCCRequiredTypeId)
      val stackPromo2 = mockPromo(11,
                                  PercentDiscount,
                                  Option(Multiplicative),
                                  ZERO_DISC,
                                  true,
                                  true,
                                  generateApplyDates(checkin, los),
                                  "1D1N_1N").copy(typeId = PromotionConstant.NoCCLastMinuteTypeId)

      val room = aValidRoomEntry
        .withAvailablePromotions(List(stackPromo1, stackPromo2))
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val hotelInfo = aValidHotelInfo.withAgodaAgency(true)

      val promoRooms = converter.generatePromotionalRooms(hotelInfo, List(room))(ctx)
      promoRooms.length should_== (1)
      promoRooms.head.promotion.isEmpty should_== true
      promoRooms.head.promotionsBreakdown.size should_== 2
      promoRooms.head.totalPromoDiscount == 0.0
    }

    "Last Minute Promo with invalid apply date" in {
      val los = 2
      val ctx = mockYplContext(checkin, los, None)
      val stackPromo1 = mockPromo(10,
                                  PercentDiscount,
                                  Option(Multiplicative),
                                  ZERO_DISC,
                                  true,
                                  true,
                                  generateApplyDates(checkin, los),
                                  "1D1N_1N").copy(typeId = PromotionConstant.NoCCRequiredTypeId)
      val stackPromo2 = mockPromo(
        11,
        PercentDiscount,
        Option(Multiplicative),
        ZERO_DISC,
        true,
        true,
        generateApplyDates(checkin, los),
        "1D1N_1N").copy(typeId = PromotionConstant.NoCCLastMinuteTypeId, maxAdvPurchase = Some(0))

      val room = aValidRoomEntry
        .withAvailablePromotions(List(stackPromo1))
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val promoRoomWithFiltering = converter.createPromotionalRoom(
        room,
        stackPromo2,
        checkin.minusDays(1),
        Map.empty,
        applyDiscountsMultiplicatively = true)(aValidYplRequest.withCheckIn(checkin), ctx)
      promoRoomWithFiltering.isDefined should_== false

    }
  }

  "Pulse Promotion" should {

    val checkin = DateTime.parse("2017-02-06")
    val los = 1

    "1. One normal unstack promo and one stack pulse promo, should apply both" in {
      val ctx = mockYplContext(checkin, los, None, List(100))

      val normalUnstack =
        mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los)).copy(typeId = 1)

      val pulseStack =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
          .copy(typeId = 100)

      val room = aValidRoomEntry
        .withAvailablePromotions(List(normalUnstack))
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val promoRoom = converter.createPromotionalRoom(
        room,
        pulseStack,
        checkin.minusDays(1),
        Map.empty,
        applyDiscountsMultiplicatively = true,
        promotionTypeIdToPulseCampaignMetadata = Map(100 -> PulseCampaignMetadata(100, 1, 1, 1, 1)),
        dateTimeToMaybePulsePromotionId = Map(checkin -> Some(10)),
      )(aValidYplRequest.withCheckIn(checkin), ctx)
      promoRoom.isDefined should_== true
      promoRoom.get.promotionsBreakdown.head._2.head.typeId should_== 100
      promoRoom.get.promotion.head.typeId should_== 100
      promoRoom.get.pulseCampaignMetadata.get.promotionTypeId should_== 100
    }

    "2.1 One unstack pulse promo and one stack pulse promo, should apply both" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseUnstack =
        mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los)).copy(typeId = 100)

      val pulseStack =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
          .copy(typeId = 101)

      val room = aValidRoomEntry
        .withAvailablePromotions(List(pulseUnstack))
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val promoRoom = converter.createPromotionalRoom(
        room,
        pulseStack,
        checkin.minusDays(1),
        Map.empty,
        applyDiscountsMultiplicatively = true,
        promotionTypeIdToPulseCampaignMetadata = Map(
          100 -> PulseCampaignMetadata(100, 1, 1, 1, 1),
          101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
        ),
        dateTimeToMaybePulsePromotionId = Map(checkin -> Some(10)),
      )(aValidYplRequest.withCheckIn(checkin), ctx)
      promoRoom.isDefined should_== true
      promoRoom.get.promotionsBreakdown.head._2.head.typeId should_== 101
      promoRoom.get.promotion.head.typeId should_== 101
      promoRoom.get.pulseCampaignMetadata.get.promotionTypeId should_== 101
    }
    "2.2 One unstack pulse promo and one stack pulse promo, should apply both but not add pulse metadata" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseUnstack =
        mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los)).copy(typeId = 100)

      val pulseStack =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
          .copy(typeId = 101, additionalDispatchReasons = Set(AdditionalDispatchReason.CustomerSegmentOverriding))

      val room = aValidRoomEntry
        .withAvailablePromotions(List(pulseUnstack))
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val promoRoom = converter.createPromotionalRoom(
        room,
        pulseStack,
        checkin.minusDays(1),
        Map.empty,
        applyDiscountsMultiplicatively = true,
        promotionTypeIdToPulseCampaignMetadata = Map(
          100 -> PulseCampaignMetadata(100, 1, 1, 1, 1),
          101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
        ),
        dateTimeToMaybePulsePromotionId = Map(checkin -> Some(10)),
      )(
        aValidYplRequest.withCheckIn(checkin),
        ctx,
      )
      promoRoom.isDefined should_== true
      promoRoom.get.promotionsBreakdown.head._2.head.typeId should_== 101
      promoRoom.get.promotion.head.typeId should_== 101
      promoRoom.get.pulseCampaignMetadata shouldEqual None
    }
    "2.3 One unstack pulse promo and one stack pulse promo with RC-2717 on, should apply both and add no metadata" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseUnstack = mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los))
        .copy(typeId = 100, additionalDispatchReasons = Set(AdditionalDispatchReason.CustomerSegmentOverriding))

      val pulseStack =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
          .copy(typeId = 101, additionalDispatchReasons = Set(AdditionalDispatchReason.CustomerSegmentOverriding))

      val room = aValidRoomEntry
        .withAvailablePromotions(List(pulseUnstack))
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val promoRoom = converter.createPromotionalRoom(
        room,
        pulseStack,
        checkin.minusDays(1),
        Map.empty,
        applyDiscountsMultiplicatively = true,
        promotionTypeIdToPulseCampaignMetadata = Map(
          100 -> PulseCampaignMetadata(100, 1, 1, 1, 1),
          101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
        ),
        dateTimeToMaybePulsePromotionId = Map(checkin -> Some(10)),
      )(
        aValidYplRequest.withCheckIn(checkin),
        ctx,
      )
      promoRoom.isDefined should_== true
      promoRoom.get.promotionsBreakdown.head._2.head.typeId should_== 101
      promoRoom.get.promotion.head.typeId should_== 101
      promoRoom.get.pulseCampaignMetadata shouldEqual None
    }
    "2.4 One Unstack pulse promo with RC-2717 on but not overriding, should apply both and add metadata" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseUnstack = mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los))
        .copy(typeId = 100, additionalDispatchReasons = Set())

      val room = aValidRoomEntry
        .withPromotion(pulseUnstack)
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val (_, bestPulseMeta) = converter.findPulseBestPromo(room,
                                                            Map(
                                                              checkin -> List(),
                                                            ),
                                                            Map(
                                                              100 -> PulseCampaignMetadata(100, 1, 1, 1, 1),
                                                              101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
                                                            ))(ctx)
      bestPulseMeta.get.promotionTypeId should_== 100
    }
    "2.5 One Unstack pulse promo with RC-2717 on overriding, should not apply add metadata" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseUnstack = mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los))
        .copy(typeId = 100, additionalDispatchReasons = Set(AdditionalDispatchReason.CustomerSegmentOverriding))

      val room = aValidRoomEntry
        .withPromotion(pulseUnstack)
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val (_, bestPulseMeta) = converter.findPulseBestPromo(room,
                                                            Map(
                                                              checkin -> List(),
                                                            ),
                                                            Map(
                                                              100 -> PulseCampaignMetadata(100, 1, 1, 1, 1),
                                                              101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
                                                            ))(ctx)
      bestPulseMeta shouldEqual None
    }
    "3. When pulseBestOption is sent, One unstack pulse promo and two stack pulse promo, should apply unstack and one best stack" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseUnstack =
        mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los)).copy(typeId = 100)

      val pulseStack1 =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
          .copy(typeId = 101)

      val pulseStack2 =
        mockPromo(11, PercentDiscount, Option(Additive), TWO_DISC, false, true, generateApplyDates(checkin, los))
          .copy(typeId = 102)

      val room = aValidRoomEntry
        .withAvailablePromotions(List(pulseUnstack))
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withAgodaAgency(true)

      val pulseMetadata = Map(
        100 -> PulseCampaignMetadata(100, 1, 1, 1, 1),
        101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
        102 -> PulseCampaignMetadata(102, 1, 1, 1, 1),
      )
      val (pulseBestOption, _) = converter.findPulseBestPromo(room,
                                                              Map(
                                                                checkin -> List(pulseStack1, pulseStack2),
                                                              ),
                                                              pulseMetadata)(ctx)
      val promoRoomFirst = converter.createPromotionalRoom(room,
                                                           pulseStack1,
                                                           checkin.minusDays(1),
                                                           Map.empty,
                                                           applyDiscountsMultiplicatively = true,
                                                           pulseMetadata,
                                                           pulseBestOption)(aValidYplRequest.withCheckIn(checkin), ctx)
      val promoRoom = promoRoomFirst.flatMap(
        converter.createPromotionalRoom(_,
                                        pulseStack2,
                                        checkin.minusDays(1),
                                        Map.empty,
                                        applyDiscountsMultiplicatively = true,
                                        pulseMetadata,
                                        pulseBestOption)(aValidYplRequest.withCheckIn(checkin), ctx))
      promoRoom.isDefined should_== true
      promoRoom.get.promotionsBreakdown.head._2.head.typeId should_== 101
      promoRoom.get.promotion.head.typeId should_== 101
      promoRoom.get.promotion.head.discountType should_== DiscountType.PercentDiscount
      promoRoom.get.pulseCampaignMetadata.get.promotionTypeId should_== 101
    }

    "4. When pulseBestOption is sent, Stackable Pulse promo with applicable adv purchase, should apply" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseStack1 = mockPromo(
        10,
        PercentDiscount,
        Option(Additive),
        FIVE_DISC,
        false,
        true,
        generateApplyDates(checkin.minusDays(10), los + 20)).copy(typeId = 101, minAdvPurchase = Some(3))

      val room = aValidRoomEntry.withDailyPrices(generateDailyPrices(checkin, los)).withAgodaAgency(true)

      val pulseMetadata = Map(
        101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
      )
      val pulseBestOption = Map(
        checkin -> Some(10),
      )
      val promoRoom = converter.createPromotionalRoom(room,
                                                      pulseStack1,
                                                      checkin.minusDays(5),
                                                      Map.empty,
                                                      applyDiscountsMultiplicatively = true,
                                                      pulseMetadata,
                                                      pulseBestOption)(aValidYplRequest.withCheckIn(checkin), ctx)
      promoRoom.isDefined should_== true
      promoRoom.get.promotionsBreakdown.head._2.head.typeId should_== 101
      promoRoom.get.promotion.head.typeId should_== 101
      promoRoom.get.promotion.head.discountType should_== DiscountType.PercentDiscount
      promoRoom.get.pulseCampaignMetadata.get.promotionTypeId should_== 101
    }

    "5. When pulseBestOption is not sent, Stackable Pulse promo with un-applicable adv purchase, should not apply and return None" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseStack1 =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
          .copy(typeId = 101, minAdvPurchase = Some(3))

      val room = aValidRoomEntry.withDailyPrices(generateDailyPrices(checkin, los)).withAgodaAgency(true)

      val pulseMetadata = Map(
        101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
      )
      val promoRoom = converter.createPromotionalRoom(room,
                                                      pulseStack1,
                                                      checkin.minusDays(1),
                                                      Map.empty,
                                                      applyDiscountsMultiplicatively = true,
                                                      pulseMetadata)(aValidYplRequest.withCheckIn(checkin), ctx)
      promoRoom.isDefined should_== false
    }

    "6. When pulseBestOption is sent, Stackable Pulse promo with un-applicable adv purchase, should not apply but return room" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseStack1 =
        mockPromo(10, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
          .copy(typeId = 101, minAdvPurchase = Some(3))

      val room = aValidRoomEntry.withDailyPrices(generateDailyPrices(checkin, los)).withAgodaAgency(true)

      val pulseMetadata = Map(
        101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
      )
      val pulseBestOption = Map(
        checkin -> Some(101),
      )
      val promoRoom = converter.createPromotionalRoom(room,
                                                      pulseStack1,
                                                      checkin.minusDays(1),
                                                      Map.empty,
                                                      applyDiscountsMultiplicatively = true,
                                                      pulseMetadata,
                                                      pulseBestOption)(aValidYplRequest.withCheckIn(checkin), ctx)
      promoRoom.isDefined should_== true
      promoRoom.get should_== room.build
    }

    "7. When pulseBestOption is sent, Duplicate promotion type stackable Pulse promo should only applies once" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseStack1 = mockPromo(10,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIVE_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, los)).copy(typeId = 101, minAdvPurchase = Some(3))

      val pulseStack2 = mockPromo(11,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIVE_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, los)).copy(typeId = 101, minAdvPurchase = Some(3))

      val room = aValidRoomEntry.withDailyPrices(generateDailyPrices(checkin, los)).withAgodaAgency(true)

      val pulseMetadata = Map(
        101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
      )
      val (pulseBestOption, _) = converter.findPulseBestPromo(room,
                                                              Map(
                                                                checkin -> List(pulseStack1, pulseStack2),
                                                              ),
                                                              pulseMetadata)(ctx)
      val promoRoomFirst = converter.createPromotionalRoom(room,
                                                           pulseStack1,
                                                           checkin.minusDays(1),
                                                           Map.empty,
                                                           applyDiscountsMultiplicatively = true,
                                                           pulseMetadata,
                                                           pulseBestOption)(aValidYplRequest.withCheckIn(checkin), ctx)
      promoRoomFirst.isDefined should_== true
      val promoRoom = converter.createPromotionalRoom(promoRoomFirst.get,
                                                      pulseStack2,
                                                      checkin.minusDays(1),
                                                      Map.empty,
                                                      applyDiscountsMultiplicatively = true,
                                                      pulseMetadata,
                                                      pulseBestOption)(aValidYplRequest.withCheckIn(checkin), ctx)
      promoRoom.isDefined should_== true
      promoRoom.get should_== room.build
    }

    "8. When pulseBestOption is sent, Stackable Pulse promo with un-applicable adv purchase, should not apply and return None" in {
      val ctx = mockYplContext(checkin, los, None, List(100, 101))

      val pulseStack1 = mockPromo(10,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIVE_DISC,
                                  isAllowStack = false,
                                  isStackable = true,
                                  generateApplyDates(checkin, los)).copy(typeId = 101, minAdvPurchase = Some(3))

      val room = aValidRoomEntry.withDailyPrices(generateDailyPrices(checkin, los)).withAgodaAgency(true)

      val pulseMetadata = Map(
        101 -> PulseCampaignMetadata(101, 1, 1, 1, 1),
      )
      val (bestPulseApplyDate, _) = converter.findPulseBestPromo(room, Map.empty, pulseMetadata)(ctx)
      bestPulseApplyDate.isEmpty should_== true
      val promoRoom = converter.createPromotionalRoom(room,
                                                      pulseStack1,
                                                      checkin.minusDays(1),
                                                      Map.empty,
                                                      applyDiscountsMultiplicatively = true,
                                                      pulseMetadata,
                                                      bestPulseApplyDate)(aValidYplRequest.withCheckIn(checkin), ctx)
      promoRoom.isDefined should_== false
    }

    Fragment.foreach(
      Seq(
        ('A', None, "should not show badge"),
        ('B', Some(PulseCampaignMetadata(100, 1, 1, 1, 1)), "should show badge"),
      ),
    ) { case (rc2508, expectedMetadata, description) =>
      s"9. One unstack non pulse promo and one override stack pulse promo with RC-2508=$rc2508, $description" in {
        val ctx =
          mockYplContext(checkin,
                         los,
                         Some(YplExperiment(YplExperiments.ALLOW_SHOW_PULSE_BADGE_FOR_EXTRA_DISPATCH_REASON, rc2508)),
                         List(100))

        val normalUnstack =
          mockPromo(1, PercentDiscount, None, TEN_DISC, true, false, generateApplyDates(checkin, los)).copy(typeId = 1)

        val pulseStack =
          mockPromo(10, PercentDiscount, Option(Additive), FIFTY_DISC, false, true, generateApplyDates(checkin, los))
            .copy(typeId = 100, additionalDispatchReasons = Set(AdditionalDispatchReason.CustomerSegmentOverriding))

        val room = aValidRoomEntry
          .withAvailablePromotions(List(normalUnstack))
          .withDailyPrices(generateDailyPrices(checkin, los))
          .withAgodaAgency(true)

        val promoRoom = converter.createPromotionalRoom(
          room,
          pulseStack,
          checkin.minusDays(1),
          Map.empty,
          applyDiscountsMultiplicatively = true,
          promotionTypeIdToPulseCampaignMetadata = Map(100 -> PulseCampaignMetadata(100, 1, 1, 1, 1)),
          dateTimeToMaybePulsePromotionId = Map(checkin -> Some(10)),
        )(aValidYplRequest.withCheckIn(checkin), ctx)

        if (expectedMetadata.isDefined) promoRoom.get.pulseCampaignMetadata shouldNotEqual None
        else promoRoom.get.pulseCampaignMetadata shouldEqual None
      }
    }

    "findPulseBestPromo correctly for stacking" in {
      val testingLos = 2
      val pulseUnstackPromo = mockPromo(200,
                                        PercentDiscount,
                                        None,
                                        FIVE_DISC,
                                        isAllowStack = true,
                                        isStackable = false,
                                        generateApplyDates(checkin, testingLos)).copy(typeId = 200)
      val pulsePromo1 = mockPromo(201,
                                  PercentDiscount,
                                  Option(Additive),
                                  TEN_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, testingLos)).copy(typeId = 201)
      val pulsePromo2 = mockPromo(202,
                                  PercentDiscount,
                                  Option(Additive),
                                  TEN_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, testingLos)).copy(typeId = 202)
      val pulsePromo3 = mockPromo(203,
                                  PercentDiscount,
                                  Option(Additive),
                                  TEN_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, testingLos)).copy(typeId = 203)
      val room = aValidRoomEntry
        .withAvailablePromotions(List(pulseUnstackPromo))
        .withDailyPrices(generateDailyPrices(checkin, testingLos))
        .build

      val applyDatesMap = Map(
        checkin -> List(pulsePromo1, pulsePromo2, pulsePromo3),
        checkin.plusDays(1) -> List(pulsePromo1, pulsePromo2, pulsePromo3),
      )
      val pulseMetadata = Map(
        200 -> PulseCampaignMetadata(200, 1, 1, 1, 1),
        201 -> PulseCampaignMetadata(201, 1, 1, 1, 1),
        202 -> PulseCampaignMetadata(202, 2, 2, 2, 2),
        203 -> PulseCampaignMetadata(203, 3, 3, 3, 3),
      )
      val ctx = mockYplContext(checkin, los, None, List(100, 101))
      val selectedBest = converter.findPulseBestPromo(room, applyDatesMap, pulseMetadata)(ctx)
      selectedBest._2.isDefined should_== true
      selectedBest._1.size should_== 2
      selectedBest._1.forall(_._2.contains(203)) should_== true
      selectedBest._2.exists(_.promotionTypeId == 203) should_== true
    }

    "findPulseBestPromo correctly ignore stackable metadata in room" in {
      val pulsePromo1 = mockPromo(201,
                                  PercentDiscount,
                                  Option(Additive),
                                  TEN_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, los)).copy(typeId = 201)
      val room = aValidRoomEntry
        .withAvailablePromotions(List(pulsePromo1))
        .withDailyPrices(generateDailyPrices(checkin, los))
        .withPromotion(pulsePromo1)
        .withPulseCampaignMetadata(Some(PulseCampaignMetadata(100, 1, 1, 1, 1)))
        .build

      val applyDatesMap = Map(
        checkin -> List(pulsePromo1),
      )
      val pulseMetadata = Map(
        200 -> PulseCampaignMetadata(200, 1, 1, 1, 1),
        201 -> PulseCampaignMetadata(201, 1, 1, 1, 1),
        202 -> PulseCampaignMetadata(202, 2, 2, 2, 2),
        203 -> PulseCampaignMetadata(203, 3, 3, 3, 3),
      )
      val ctx = mockYplContext(checkin, los, None, List(100, 101))
      val selectedBest = converter.findPulseBestPromo(room, Map(), pulseMetadata)(ctx)
      selectedBest._2.isDefined should_== false
    }

    "findPulseBestPromo correctly for unordered stacking" in {
      val testingLos = 5
      val pulseUnstackPromo = mockPromo(200,
                                        PercentDiscount,
                                        None,
                                        FIVE_DISC,
                                        isAllowStack = true,
                                        isStackable = false,
                                        generateApplyDates(checkin, testingLos)).copy(typeId = 200)
      val pulsePromo1 = mockPromo(201,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIFTEEN_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, testingLos)).copy(typeId = 201)
      val pulsePromo2 = mockPromo(202,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIFTEEN_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, testingLos)).copy(typeId = 202)
      val pulsePromo3 = mockPromo(203,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIFTEEN_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, testingLos)).copy(typeId = 203)
      val room = aValidRoomEntry
        .withAvailablePromotions(List(pulseUnstackPromo))
        .withDailyPrices(generateDailyPrices(checkin, testingLos))
        .build

      val applyDatesMap = Map(
        checkin -> List(pulsePromo3, pulsePromo2, pulsePromo1),
        checkin.plusDays(1) -> List(pulsePromo2, pulsePromo1, pulsePromo3),
        checkin.plusDays(2) -> List(pulsePromo1, pulsePromo3, pulsePromo2),
        checkin.plusDays(3) -> List(pulsePromo2, pulsePromo3, pulsePromo1),
        checkin.plusDays(4) -> List(pulsePromo3, pulsePromo1, pulsePromo2),
      )
      val pulseMetadata = Map(
        200 -> PulseCampaignMetadata(200, 1, 1, 1, 1),
        201 -> PulseCampaignMetadata(201, 1, 1, 1, 1),
        202 -> PulseCampaignMetadata(202, 2, 2, 2, 2),
        203 -> PulseCampaignMetadata(203, 3, 3, 3, 3),
      )
      val ctx = mockYplContext(checkin, los, None, List(100, 101))
      val selectedBest = converter.findPulseBestPromo(room, applyDatesMap, pulseMetadata)(ctx)
      selectedBest._2.isDefined should_== true
      selectedBest._1.size should_== applyDatesMap.size
      selectedBest._1.forall(_._2.contains(203)) should_== true
      selectedBest._2.exists(_.promotionTypeId == 203) should_== true
    }

    "findPulseBestPromo correctly handles empty Pulse promotions list" in {
      val room = aValidRoomEntry.withDailyPrices(generateDailyPrices(checkin, los)).build
      val ctx = mockYplContext(checkin, los, None, List(100, 101))
      val selectedBest = converter.findPulseBestPromo(room, Map.empty, Map.empty)(ctx)
      selectedBest._1.isEmpty should_== true
      selectedBest._2.isEmpty should_== true
    }

    "findPulseBestPromo correctly for case there is overriding logic applied and turn on experiment" in {
      val testingLos = 5
      val pulseUnstackPromo = mockPromo(200,
                                        PercentDiscount,
                                        None,
                                        FIVE_DISC,
                                        isAllowStack = true,
                                        isStackable = false,
                                        generateApplyDates(checkin, testingLos)).copy(typeId = 200)
      val pulsePromo1 = mockPromo(201,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIFTEEN_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, testingLos)).copy(typeId = 201)
      val pulsePromo2 = mockPromo(202,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIFTEEN_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, testingLos)).copy(typeId = 202)
      val pulsePromo3 = mockPromo(203,
                                  PercentDiscount,
                                  Option(Additive),
                                  FIFTEEN_DISC,
                                  isAllowStack = true,
                                  isStackable = true,
                                  generateApplyDates(checkin, testingLos))
        .copy(typeId = 203, additionalDispatchReasons = Set(AdditionalDispatchReason.CustomerSegmentOverriding))
      val room = aValidRoomEntry
        .withAvailablePromotions(List(pulseUnstackPromo))
        .withDailyPrices(generateDailyPrices(checkin, testingLos))
        .build

      val applyDatesMap = Map(
        checkin -> List(pulsePromo3, pulsePromo2, pulsePromo1),
        checkin.plusDays(1) -> List(pulsePromo2, pulsePromo1, pulsePromo3),
        checkin.plusDays(2) -> List(pulsePromo1, pulsePromo3, pulsePromo2),
        checkin.plusDays(3) -> List(pulsePromo2, pulsePromo3, pulsePromo1),
        checkin.plusDays(4) -> List(pulsePromo3, pulsePromo1, pulsePromo2),
      )
      val pulseMetadata = Map(
        200 -> PulseCampaignMetadata(200, 1, 1, 1, 1),
        201 -> PulseCampaignMetadata(201, 1, 1, 1, 1),
        202 -> PulseCampaignMetadata(202, 2, 2, 2, 2),
        203 -> PulseCampaignMetadata(203, 3, 3, 3, 3),
      )

      val (dateTimeToMaybeBestPulsePromotionId, bestPulseMeta) =
        converter.findPulseBestPromo(room, applyDatesMap, pulseMetadata)(
          aValidYplContext.build,
        )
      bestPulseMeta.isDefined should_== true
      dateTimeToMaybeBestPulsePromotionId.size should_== applyDatesMap.size
      dateTimeToMaybeBestPulsePromotionId.forall(_._2.contains(203)) should_== true
      bestPulseMeta.exists(_.promotionTypeId == 202) should_== true
    }

    Fragment.foreach(
      Seq(
        ("A", false, "should not return badge data"),
        ("B", true, "should return badge data"),
      ),
    ) { case (expValue, expectedBadge, description) =>
      s"findPulseBestPromo with ENABLE_PULSE_DISPATCH_BADGE=$expValue $description" in {
        val testingLos = 1
        val pulseUnstackPromo = mockPromo(200,
                                          PercentDiscount,
                                          None,
                                          FIVE_DISC,
                                          isAllowStack = true,
                                          isStackable = false,
                                          generateApplyDates(checkin, testingLos))
          .copy(typeId = 200, additionalDispatchReasons = Set(AdditionalDispatchReason.TimeFenceExtended))
        val pulsePromo1 = mockPromo(203,
                                    PercentDiscount,
                                    Option(Additive),
                                    FIFTEEN_DISC,
                                    isAllowStack = true,
                                    isStackable = true,
                                    generateApplyDates(checkin, testingLos))
          .copy(typeId = 203, additionalDispatchReasons = Set(AdditionalDispatchReason.TimeFenceExtended))
        val room = aValidRoomEntry
          .withAvailablePromotions(List(pulseUnstackPromo))
          .withDailyPrices(generateDailyPrices(checkin, testingLos))
          .build

        val applyDatesMap = Map(
          checkin -> List(pulsePromo1),
        )
        val pulseMetadata = Map(
          200 -> PulseCampaignMetadata(200, 1, 1, 1, 1),
          201 -> PulseCampaignMetadata(201, 1, 1, 1, 1),
          202 -> PulseCampaignMetadata(202, 2, 2, 2, 2),
          203 -> PulseCampaignMetadata(203, 3, 3, 3, 3),
        )

        val experiment =
          if (expValue == "B") forceBExperimentContext(YplExperiments.ALLOW_SHOW_PULSE_BADGE_FOR_EXTRA_DISPATCH_REASON)
          else forceAExperimentContext(YplExperiments.ALLOW_SHOW_PULSE_BADGE_FOR_EXTRA_DISPATCH_REASON)

        val (_, bestPulseMeta) = converter.findPulseBestPromo(room, applyDatesMap, pulseMetadata)(
          aValidYplContext.withExperimentContext(experiment),
        )
        bestPulseMeta.isDefined should_== expectedBadge
      }
    }
  }

  "Edge cases and boundary conditions" should {

    val checkin = DateTime.parse("2025-09-24")
    val los = 1

    "can handle when no available date" in {
      val ctx = mockYplContext(checkin, los, None)

      // Create a promotion with typeId that will be recognized as pulse promotion
      val pulsePromoTypeId = 12345
      val stackPromo2 = mockPromo(
        11,
        PercentDiscount,
        Option(Multiplicative),
        ZERO_DISC,
        false,
        true,
        generateApplyDates(checkin, los), // Same dates as room, but will be filtered out by best promo check
      ).copy(typeId = pulsePromoTypeId) // Regular promotion (not NoCC)

      val room = aValidRoomEntry.withDailyPrices(generateDailyPrices(checkin, los)).withAgodaAgency(true)

      // Create pulse campaign metadata for the promotion (non-mega sale campaign type)
      val pulseCampaignMetadata = Map(
        pulsePromoTypeId -> PulseCampaignMetadata(
          webCampaignId = 1000,
          promotionTypeId = pulsePromoTypeId,
          campaignTypeId = 1000, // Not in MegaSalePromotions list
          campaignBadgeCmsId = 1000,
          campaignBadgeDescCmsId = 1000,
        ),
      )

      val bestPulsePromotionIds = Map(checkin -> Some(99999)) // Different from stackPromo2.id

      val promoRoomWithFiltering = converter.createPromotionalRoom(
        room,
        stackPromo2,
        checkin.minusDays(1),
        Map.empty,
        applyDiscountsMultiplicatively = true,
        pulseCampaignMetadata,
        bestPulsePromotionIds,
        Map.empty,
        useBaseCxlPolicyForPromotionalRooms = false,
        isMegaCampaignEnabled = true,
      )(aValidYplRequest.withCheckIn(checkin), ctx)

      promoRoomWithFiltering.isDefined should_== true

      // The key test: correct logic returns a room, mutant returns None
      promoRoomWithFiltering should not be None

    }

  }

  "getBestMegaPromotionData" in {

    val checkin = DateTime.parse("2025-09-24")
    val los = 1
    val ctx = mockYplContext(checkin, los, None, List(301, 302))
    val pulseStack1 =
      mockPromo(301, PercentDiscount, Option(Additive), FIVE_DISC, false, true, generateApplyDates(checkin, los))
        .copy(typeId = 301)
    val pulseStack2 =
      mockPromo(302, PercentDiscount, Option(Additive), TWO_DISC, false, true, generateApplyDates(checkin, los))
        .copy(typeId = 302)
    val pulseMetadata = Map(
      301 -> PulseCampaignMetadata(301, 1, MegaSalePromotions.CampaignTypeIds(1), 1, 1),
      302 -> PulseCampaignMetadata(302, 1, MegaSalePromotions.CampaignTypeIds(2), 1, 1),
    )

    val room = aValidRoomEntry.withDailyPrices(generateDailyPrices(checkin, los)).withAgodaAgency(true)

    "should generate the best mega sale promotion metadata correctly when isMegaCampaignEnabled is true" in {

      val selectedBest = converter.getBestMegaPromotionData(room,
                                                            Map(checkin -> List(pulseStack1, pulseStack2)),
                                                            pulseMetadata,
                                                            isMegaCampaignEnabled = true)(ctx)
      selectedBest._1.size should_== 1
      selectedBest._1.forall(_._2.contains(301)) should_== true
      selectedBest._2.isDefined should_== true
      selectedBest._2.forall(_.promotionTypeId == 301) should_== true
    }

    "should define default values when isMegaCampaignEnabled is false" in {
      val selectedBest = converter.getBestMegaPromotionData(room,
                                                            Map(checkin -> List(pulseStack1, pulseStack2)),
                                                            pulseMetadata,
                                                            isMegaCampaignEnabled = false)(ctx)
      selectedBest._1.isEmpty should_== true
      selectedBest._2.isDefined should_== false
    }

  }

  private def generateApplyDates(checkin: DateTime, los: Int): Map[DateTime, Int] =
    (0 until los).map(n => checkin.plusDays(n) -> toDiscountIndex(checkin.plusDays(n))).toMap

  private def generateDailyPrices(checkin: DateTime, los: Int, price: Double = 100.0): Map[DateTime, DailyPrice] =
    (0 until los).map { n =>
      checkin.plusDays(n) -> aValidDailyPrice.build.copy(
        date = checkin.plusDays(n),
        prices = List(
          aValidPriceEntry.build.copy(date = checkin.plusDays(n),
                                      value = price,
                                      chargeOption = ChargeOption.Mandatory,
                                      chargeType = ChargeType.Room,
                                      quantity = 1)),
      )
    }.toMap

  private def mockYplContext(checkin: DateTime,
                             los: Int,
                             expOpt: Option[YplExperiment] = None,
                             pulsePromotionTypeIds: List[Int] = List.empty): YplContext = {
    val req = expOpt match {
      case Some(exp) => aValidYplRequest
          .withCheckIn(checkin)
          .withCheckout(checkin.plusDays(los))
          .withBookingDate(checkin.minusDays(1))
          .withOccupancyInfo(YplOccInfo(Option(2), None, Option(1)))
          .withExperiment(exp)
          .build
      case None => aValidYplRequest
          .withCheckIn(checkin)
          .withCheckout(checkin.plusDays(los))
          .withBookingDate(checkin.minusDays(1))
          .withOccupancyInfo(YplOccInfo(Option(2), None, Option(1)))
          .build
    }

    val ctx: YplContext =
      YplContext(req, pulseCampaignMetaContext = aValidPulseCampaignMetaContext(pulsePromotionTypeIds))
    ctx
  }

  private def mockPromo(id: Int,
                        discType: DiscountType,
                        stackDiscountType: Option[StackDiscountOption],
                        discounts: List[Double],
                        isAllowStack: Boolean,
                        isStackable: Boolean,
                        applyDates: Map[DateTime, Int],
                        cancellation: String = "365D100P_100P") = PromotionEntry(
    id = id,
    discountType = discType,
    discounts = discounts,
    bookOn = "1111111",
    bookFrom = Option(new DateTime(Long.MinValue)),
    bookTo = Option(new DateTime(Long.MaxValue)),
    minAdvPurchase = None,
    maxAdvPurchase = None,
    bookTimeFrom = None,
    bookTimeTo = None,
    applyDates = applyDates,
    cancellationCode = cancellation,
    customerSegments = List(CustomerSegment(None, None)),
    isAllowStack = isAllowStack,
    stackDiscountOption = Option(stackDiscountType.getOrElse(StackDiscountOption.Unknown)),
    isStackable = isStackable,
  )

  private def toDiscountIndex(date: DateTime): Int = date.dayOfWeek().get() - 1

}
