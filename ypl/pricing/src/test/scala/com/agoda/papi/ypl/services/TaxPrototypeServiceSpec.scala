package com.agoda.papi.ypl.services

import com.agoda.finance.tax.enums.TaxLevelCalculationType
import com.agoda.finance.tax.models.{AppliedTaxPrototypeLevel, TaxPrototypeInfo, TaxPrototypeLevel}
import com.agoda.papi.enums.hotel.{DMC, PaymentModel, SupplierType}
import com.agoda.papi.enums.room.RateType.{NetExclusive, NetInclusive, SellExclusive, SellInclusive}
import com.agoda.papi.enums.room._
import com.agoda.papi.pricing.pricecalculation.api.PriceBreakdownCalculatorInterface
import com.agoda.papi.pricing.pricecalculation.models.tax.{DailyTaxes, TaxWithValue}
import com.agoda.papi.pricing.pricecalculation.pricing.{
  CommissionCalculatorImpl,
  PriceBreakdownCalculatorImpl,
  TaxBreakdownCalculatorImpl,
}
import com.agoda.papi.pricing.pricecalculation.utils.TaxFiltersCacheInitialization
import com.agoda.papi.ypl.commission.service.CommissionServiceImpl
import com.agoda.papi.ypl.commission.{CommissionHolder, MORPCandidateRoomParameters}
import com.agoda.papi.ypl.models.pricing.YplPrice
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.{
  ChainId,
  CountryId,
  HotelId,
  SupplierId,
  SupplierInfo,
  YPLHotel,
  YPLRoom,
  YPLTestContexts,
  YPLTestDataBuilders,
  YplContext,
  YplExperiments,
  YplReqOccByHotelAgePolicy,
  YplRoomEntry,
}
import com.agoda.papi.ypl.pricing.mocks.MockPriceBreakdownCalculator
import com.agoda.papi.ypl.pricing.{PriceCalculationImpl, TaxCalculatorImpl}
import com.agoda.papi.ypl.utils.Implicits._
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.SupplierRateInfo.ExternalData
import com.agoda.utils.flow.{ExperimentContext, PropertyContext}
import models.consts.ABTest
import models.flow.Variant
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.specs2.mock.Mockito.mock
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.BeforeAll

import scala.concurrent.ExecutionContext

class TaxPrototypeServiceSpec
  extends SpecificationWithJUnit
    with YPLTestContexts
    with YPLTestDataBuilders
    with BeforeAll {

  implicit val compositeChannelContext = aValidCompositeChannelContext

  override def beforeAll(): Unit = TaxFiltersCacheInitialization.initializeMockCache()

  class TaxPrototypeServiceTest(
    priceBreakdownCalc: PriceBreakdownCalculatorInterface =
      new PriceBreakdownCalculatorImpl(new TaxBreakdownCalculatorImpl(), new CommissionCalculatorImpl()))
    extends TaxPrototypeServiceImpl
      with PriceCalculationImpl
      with TaxCalculatorImpl
      with CommissionServiceImpl {

    override val priceBreakdownCalculator: PriceBreakdownCalculatorInterface = priceBreakdownCalc

    implicit def ec: ExecutionContext = scala.concurrent.ExecutionContext.Implicits.global

    var lastExcludeWholesaleOrAgxValue: Option[Boolean] = None

    override def getCommissionForPriceCalculation(commissionHolder: CommissionHolder,
                                                  stayDate: DateTime,
                                                  occupancy: TaxProtoTypeID,
                                                  isAgodaAgency: Boolean,
                                                  applicableMORPCandidateRoomParameters: MORPCandidateRoomParameters,
                                                  originalRateType: RateType,
                                                  targetRateType: RateType,
                                                  excludeWholesaleOrAgx: Boolean): Double = {
      lastExcludeWholesaleOrAgxValue = Some(excludeWholesaleOrAgx)

      if (excludeWholesaleOrAgx) {
        // This shouldn't be used from tax flow as the rateType is hardcoded to SellInclusive
        // Return a distinctive value that would be obvious if used in calculation
        -999999
      } else {
        super.getCommissionForPriceCalculation(commissionHolder,
                                               stayDate,
                                               occupancy,
                                               isAgodaAgency,
                                               applicableMORPCandidateRoomParameters,
                                               originalRateType,
                                               targetRateType,
                                               excludeWholesaleOrAgx)
      }
    }
    def testGetSupplierIdForRoomOrHotel(
      ctx: YplContext,
      room: YPLRoom,
      hotel: YPLHotel,
    ): SupplierId = getSupplierIdForRoomOrHotel(ctx, room, hotel)
  }

  "TaxPrototypeLevel" should {

    val taxPrototypeService = new TaxPrototypeServiceTest

    val taxTypeId = 1
    val taxPrototypeId = 11111
    val taxAndPrototypeId = (taxTypeId, taxPrototypeId)

    // real case for indian graduated tax
    val taxPrototypeLevels = List(
      TaxPrototypeLevel(1, 0.0, 1000.0, 0.0),
      TaxPrototypeLevel(2, 1000.0, 2500.0, 12.0),
      TaxPrototypeLevel(3, 2500.0, 7500.0, 18.0),
      TaxPrototypeLevel(4, 7500.0, Double.MaxValue, 28.0),
    )

    val taxItem = aValidProtoTax
      .withTaxId(1)
      .withApplyTo("PRPN")
      .withIsAmount(false)
      .withIsFee(false)
      .withTaxValue(0.0)
      .withChargeOption(ChargeOption.Mandatory)
      .withTaxPrototypeId(taxPrototypeId)
      .withTaxPrototypeInfo(Some(TaxPrototypeInfo(taxPrototypeLevels)))

    val taxItemV2 = taxItem
      .withWhomToPay(WhomToPayType.Property)
      .withOrderNumber(Some(1))
      .withTaxLevelCalculationType(Some(TaxLevelCalculationType.FlatRate))
      .withValueMethod(Some(ValueMethodType.Fixed))
      .withValueCalculationMethodType(Some(ValueCalculationMethodType.Percent))
      .withGeoId(Some(114))
      .withGeoType(Some(GeoType.State))

    val taxPrototypeInfo = TaxPrototypeInfo(taxPrototypeLevels)

    val taxPrototypeLevel = TaxPrototypeLevel(1, 0.0, 1000.0, 0.0)

    val appliedTaxPrototypeLevel = AppliedTaxPrototypeLevel(taxPrototypeLevel = taxPrototypeLevel, actualTaxAmount = 0)

    val tierPicker = (value: Double) => taxPrototypeLevels.filter(tier => tier.taxValue == value)
    val topAmountPicker = tierPicker(_: Double).takeRight(1)
    val mockAppliedTaxPrototypeLevel = (value: Double) =>
      topAmountPicker(value).map(level => appliedTaxPrototypeLevel.withAppliedTaxPrototypeLevels(level).build)
    val mockTaxPrototypeInfo =
      (value: Double) => Some(taxPrototypeInfo.withAppliedTaxPrototypeLevels(mockAppliedTaxPrototypeLevel(value)).build)

    def genDailyTaxes(taxValue: Double, taxPrototypeInfo: Option[TaxPrototypeInfo] = None) = DailyTaxes(
      taxes = List(
        TaxWithValue(
          taxItem.withTaxValue(taxValue).withTaxPrototypeInfo(taxPrototypeInfo.orElse(taxItem.taxPrototypeInfo)),
          taxValue),
      ),
      isCleanedUpHospitalityTax = true,
    )

    val dailyTaxesMock = genDailyTaxes(0.0)

    // Use common tax library
    "no assume when no tax prototype level by common tax library - isNewChargeTypeTaxApplied is none" in {
      val dailyTaxesMockNoLevel = DailyTaxes(
        taxes = List(
          TaxWithValue(taxItem.withTaxPrototypeInfo(None).withTaxValue(10.0), 10.0),
        ),
        isCleanedUpHospitalityTax = true,
      )

      taxPrototypeService.assumeTaxValue(SellExclusive,
                                         0.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(SellExclusive,
                                         1000.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(SellExclusive,
                                         2500.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(SellExclusive,
                                         7500.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel

      taxPrototypeService.assumeTaxValue(NetExclusive,
                                         0.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(NetExclusive,
                                         1000.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(NetExclusive,
                                         2500.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(NetExclusive,
                                         7500.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel

      taxPrototypeService.assumeTaxValue(NetInclusive,
                                         0.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(NetInclusive,
                                         1000.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(NetInclusive,
                                         2500.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(NetInclusive,
                                         7500.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel

      taxPrototypeService.assumeTaxValue(SellInclusive,
                                         0.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(SellInclusive,
                                         1000.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(SellInclusive,
                                         2500.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel
      taxPrototypeService.assumeTaxValue(SellInclusive,
                                         7500.0,
                                         15.00,
                                         dailyTaxesMockNoLevel,
                                         isCleanedUpHospitalityTax = true) should_== dailyTaxesMockNoLevel

    }

    "assume % tax correctly for level 1 by common tax library - isNewChargeTypeTaxApplied is none" in {
      // SellExclusive
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        999.99,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        1000.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        2499.99,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        2500.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        7499.99,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        7500.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        10000.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        10000000.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      // NetExclusive
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        869.56,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        869.57,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        2173.91,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        2173.92,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        6521.73,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        6521.74,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        100000.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        *********.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      // NetInclusive
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        869.56,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        869.57,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        2434.78,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        2434.79,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        7695.65,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        7695.66,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        10000.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        10000000.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      // SellInclusive
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        1119.99,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        1200.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        2949.99,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        2950.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        8849.99,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        9599.99,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        9600.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        10000.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        10000000.00,
        15.00,
        dailyTaxesMock,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
    }

    "assume % tax correctly with channel discount by common tax library - isNewChargeTypeTaxApplied is none" in {
      // SellExclusive
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        1111.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        1112.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        2777.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        2778.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        8333.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        8334.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        10000.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        10000000.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      // NetExclusive
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        966.18,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        966.19,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        2415.45,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        2415.46,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        7246.37,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        7246.38,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        100000.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        *********.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      // NetInclusive1
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        966.18,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        966.19,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        2705.31,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        2705.32,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        8550.72,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        8550.73,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        10000.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        10000000.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      // SellInclusive
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        1244.44,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        1244.45,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        3277.77,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(12.0, mockTaxPrototypeInfo(12))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        3277.78,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        10666.66,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(18.0, mockTaxPrototypeInfo(18))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        10666.67,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        100000.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        10000000.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(28.0, mockTaxPrototypeInfo(28))
    }

    "assume % tax correctly with channel discount by common tax library - isNewChargeTypeTaxApplied is false" in {
      // SellExclusive
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      // NetExclusive
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      // NetInclusive1
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      // SellInclusive
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
    }

    "assume % tax correctly with channel discount by common tax library - isNewChargeTypeTaxApplied is true" in {
      // SellExclusive
      taxPrototypeService.assumeTaxValue(
        SellExclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      // NetExclusive
      taxPrototypeService.assumeTaxValue(
        NetExclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      // NetInclusive1
      taxPrototypeService.assumeTaxValue(
        NetInclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
      // SellInclusive
      taxPrototypeService.assumeTaxValue(
        SellInclusive,
        0.00,
        15.00,
        dailyTaxesMock,
        10.0,
        isCleanedUpHospitalityTax = true) should_== genDailyTaxes(0.0, mockTaxPrototypeInfo(0))
    }

    def genMockYplHotel(netExclusive: Double,
                        commissionPercent: Double,
                        taxPercent: Double,
                        rateType: RateType,
                        rateLoadedTypeAmountWithChannelDiscount: Double,
                        isTaxV2Mocked: Boolean = false,
                        supplierFundedDiscount: Option[Double] = None,
                        uspaDiscountAmount: Option[Double] = None,
                        uspaProgramId: Option[Int] = None) = {

      val dailyTaxes: DailyTaxes =
        DailyTaxes(List(TaxWithValue(taxItem, taxItem.value)), isCleanedUpHospitalityTax = true)
      val dailyTaxesV2: DailyTaxes =
        DailyTaxes(List(TaxWithValue(taxItemV2, taxItemV2.value)), isCleanedUpHospitalityTax = true)

      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmountWithChannelDiscount, taxPercent, commissionPercent)
      val taxAmount = netExclusive * taxPercent * 0.01
      val processingFeeAmount = taxPercent * 0.01 * marginAmount

      val price = aValidPrice
        .withDate(DateTime.now())
        .withQuantity(1)
        .withChargeType(ChargeType.Room)
        .withApplyType(ApplyType.PRPN)
        .withChargeOption(ChargeOption.Mandatory)
        .withNetExclusive(netExclusive)
        .withTax(taxAmount)
        .withFee(0)
        .withMargin(marginAmount)
        .withProcessingFee(processingFeeAmount)
        .withValue(rateLoadedTypeAmountWithChannelDiscount)
        .withDailyTaxes(if (isTaxV2Mocked) dailyTaxesV2 else dailyTaxes)
        .withSupplierFundedDiscountAmount(supplierFundedDiscount)
        .withUspaDiscount(uspaDiscountAmount, uspaProgramId)

      val room = aValidRoom
        .withPrice(price)
        .withRateType(rateType)
        .withOriginalRateType(rateType)
        .withMarginPercentage(marginPercentage)
      val hotelTaxInfo = aValidHotelTaxInfo.withIsConfigProcessingFees(isConfigProcessingFees = false)
      aValidHotel.withRoom(room).withHotelTaxInfo(hotelTaxInfo)
    }

    def genMockYplHotelWithExtraBed(
      commissionPercent: Double,
      taxPercent: Double,
      rateType: RateType,
      rateLoadedTypeAmountWithChannelDiscount: Double,
      rateLoadedTypeAmountExtraBed: Double,
      roomQty: Int = 1,
      extraBedQty: Int = 1,
    ) = {

      val dailyTaxes: DailyTaxes =
        DailyTaxes(List(TaxWithValue(taxItem, taxItem.value)), isCleanedUpHospitalityTax = true)

      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmountWithChannelDiscount, taxPercent, commissionPercent)
      val taxAmount = netExclusive * taxPercent * 0.01
      val processingFeeAmount = taxPercent * 0.01 * marginAmount

      val (netExclusiveExtraBed, marginAmountExtraBed, marginPercentageExtraBed) =
        calNetExMargin(rateType, rateLoadedTypeAmountExtraBed, taxPercent, commissionPercent)
      val taxAmountExtraBed = netExclusiveExtraBed * taxPercent * 0.01
      val processingFeeAmountExtraBed = taxPercent * 0.01 * marginAmountExtraBed

      val today = DateTime.now()

      val roomPrice = aValidPrice
        .withDate(new DateTime(today.getYear, today.getMonthOfYear, today.getDayOfMonth, 0, 0))
        .withQuantity(roomQty)
        .withChargeType(ChargeType.Room)
        .withApplyType(ApplyType.PRPN)
        .withChargeOption(ChargeOption.Mandatory)
        .withNetExclusive(netExclusive)
        .withTax(taxAmount)
        .withFee(0)
        .withMargin(marginAmount)
        .withProcessingFee(processingFeeAmount)
        .withValue(rateLoadedTypeAmountWithChannelDiscount)
        .withDailyTaxes(dailyTaxes)

      val extraBedPrice = aValidPrice
        .withDate(new DateTime(today.getYear, today.getMonthOfYear, today.getDayOfMonth, 0, 0))
        .withQuantity(extraBedQty)
        .withChargeType(ChargeType.ExtraBed)
        .withApplyType(ApplyType.PRPN)
        .withChargeOption(ChargeOption.Mandatory)
        .withNetExclusive(netExclusiveExtraBed)
        .withTax(taxAmountExtraBed)
        .withFee(0)
        .withMargin(marginAmountExtraBed)
        .withProcessingFee(processingFeeAmountExtraBed)
        .withValue(rateLoadedTypeAmountExtraBed)
        .withDailyTaxes(dailyTaxes)

      val room = aValidRoom
        .withPrices(List(roomPrice, extraBedPrice))
        .withRateType(rateType)
        .withOriginalRateType(rateType)
        .withMarginPercentage(marginPercentage)
      val hotelTaxInfo = aValidHotelTaxInfo.withIsConfigProcessingFees(isConfigProcessingFees = false)
      aValidHotel.withRoom(room).withHotelTaxInfo(hotelTaxInfo)
    }

    def genMockYplHotelWithSurcharge(
      commissionPercent: Double,
      taxPercent: Double,
      rateType: RateType,
      rateLoadedTypeAmountWithChannelDiscount: Double,
      rateLoadedTypeAmountSurcharge: Double,
      isTaxV2Available: Boolean = false,
    ) = {

      val dailyTaxes: DailyTaxes =
        DailyTaxes(List(TaxWithValue(taxItem, taxItem.value)), isCleanedUpHospitalityTax = true)
      val dailyTaxesV2: DailyTaxes =
        DailyTaxes(List(TaxWithValue(taxItemV2, taxItemV2.value)), isCleanedUpHospitalityTax = true)

      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmountWithChannelDiscount, taxPercent, commissionPercent)
      val taxAmount = netExclusive * taxPercent * 0.01
      val processingFeeAmount = taxPercent * 0.01 * marginAmount

      val (netExclusiveSurcharge, marginAmountSurcharge, marginPercentageSurcharge) =
        calNetExMargin(rateType, rateLoadedTypeAmountSurcharge, taxPercent, commissionPercent)
      val taxAmountSurcharge = netExclusiveSurcharge * taxPercent * 0.01
      val processingFeeAmountExtraBed = taxPercent * 0.01 * marginAmountSurcharge

      val today = DateTime.now()
      val todayDate = new DateTime(today.getYear, today.getMonthOfYear, today.getDayOfMonth, 0, 0)

      val roomPrice = aValidPrice
        .withDate(todayDate)
        .withQuantity(1)
        .withChargeType(ChargeType.Room)
        .withApplyType(ApplyType.PRPN)
        .withChargeOption(ChargeOption.Mandatory)
        .withNetExclusive(netExclusive)
        .withTax(taxAmount)
        .withFee(0)
        .withMargin(marginAmount)
        .withProcessingFee(processingFeeAmount)
        .withValue(rateLoadedTypeAmountWithChannelDiscount)
        .withDailyTaxes(if (isTaxV2Available) dailyTaxesV2 else dailyTaxes)

      val surchargePrice = aValidPrice
        .withDate(todayDate)
        .withQuantity(1)
        .withChargeType(ChargeType.Surcharge)
        .withApplyType(ApplyType.PRPN)
        .withChargeOption(ChargeOption.Mandatory)
        .withNetExclusive(netExclusiveSurcharge)
        .withTax(taxAmountSurcharge)
        .withFee(0)
        .withMargin(marginAmountSurcharge)
        .withProcessingFee(processingFeeAmountExtraBed)
        .withValue(rateLoadedTypeAmountSurcharge)
        .withDailyTaxes(if (isTaxV2Available) dailyTaxesV2 else dailyTaxes)
        .withSurchargeEntry(Some(aValidSurchargeEntry))
        .withSurchargeRateType(Some(RateType.Unknown))
        .withDailyPrice(Some(aValidDailyPriceWithTaxPrototypeLevel.copy(date = todayDate)))

      val roomEntry = aValidRoomEntry.withCommissionHolder(
        aValidCommissionHolder.withDaily(
          Map(
            todayDate -> aValidCommissionDailyHolder.withProtobufCommissionHolder(
              aValidProtobufCommissionHolder.withValue(commissionPercent),
            ),
          ),
        ),
      )

      val room = aValidRoom
        .withPrices(List(roomPrice, surchargePrice))
        .withRateType(rateType)
        .withOriginalRateType(rateType)
        .withMarginPercentage(marginPercentage)
        .withYplRoomEntry(roomEntry)
        .withTaxInfo(if (isTaxV2Available) Some(aValidTaxInfoWithTaxPrototypeLevelV2)
        else Some(aValidTaxInfoWithTaxPrototypeLevel))
      val hotelTaxInfo = aValidHotelTaxInfo.withIsConfigProcessingFees(isConfigProcessingFees = false)
      aValidHotel.withRoom(room).withHotelTaxInfo(hotelTaxInfo)
    }

    def genMockYplHotelWithFee(commissionPercent: Double,
                               taxPercent: Double,
                               rateType: RateType,
                               rateLoadedTypeAmountWithChannelDiscount: Double) = {

      val dailyTaxes: DailyTaxes = aValidDailyTaxWithTaxPrototypeLevelAndFee

      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmountWithChannelDiscount, taxPercent + 20, commissionPercent)
      val taxAmount = netExclusive * taxPercent * 0.01
      val feeAmount = netExclusive * 20 * 0.01
      val processingFeeAmount = taxPercent * 0.01 * marginAmount

      val today = DateTime.now()
      val todayDate = new DateTime(today.getYear, today.getMonthOfYear, today.getDayOfMonth, 0, 0)

      val roomPrice = aValidPrice
        .withDate(todayDate)
        .withQuantity(1)
        .withChargeType(ChargeType.Room)
        .withApplyType(ApplyType.PRPN)
        .withChargeOption(ChargeOption.Mandatory)
        .withNetExclusive(netExclusive)
        .withTax(taxAmount)
        .withFee(feeAmount)
        .withMargin(marginAmount)
        .withProcessingFee(processingFeeAmount)
        .withValue(rateLoadedTypeAmountWithChannelDiscount)
        .withDailyTaxes(dailyTaxes)
        .withSubChargeType(SubChargeType.Adult)
        .withRoomNumber(Some(1))

      val room = aValidRoom
        .withPrices(List(roomPrice))
        .withRateType(rateType)
        .withOriginalRateType(rateType)
        .withMarginPercentage(marginPercentage)
        .withYplRoomEntry(aValidRoomEntry.copy())
        .withTaxInfo(Some(aValidTaxInfoWithTaxPrototypeLevelAndFee))
      val hotelTaxInfo = aValidHotelTaxInfo.withIsConfigProcessingFees(isConfigProcessingFees = false)
      aValidHotel.withRoom(room).withHotelTaxInfo(hotelTaxInfo)
    }

    def genMockYplHotelWithFeeAndChild(commissionPercent: Double,
                                       taxPercent: Double,
                                       rateType: RateType,
                                       rateLoadedTypeAmountWithChannelDiscount: Double,
                                       adultQuantity: Int = 1,
                                       childQuanity: Int = 0) = {

      val dailyTaxes: DailyTaxes = aValidDailyTaxWithTaxPrototypeLevelAndFee

      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmountWithChannelDiscount, taxPercent + 20, commissionPercent)
      val taxAmount = netExclusive * taxPercent * 0.01
      val feeAmount = netExclusive * 20 * 0.01
      val processingFeeAmount = taxPercent * 0.01 * marginAmount

      val todayDate = new DateTime(2023, 1, 1, 0, 0)

      val roomPriceAdult = aValidPrice
        .withDate(todayDate)
        .withQuantity(adultQuantity)
        .withChargeType(ChargeType.Room)
        .withApplyType(ApplyType.PRPN)
        .withChargeOption(ChargeOption.Mandatory)
        .withNetExclusive(netExclusive)
        .withTax(taxAmount)
        .withFee(feeAmount)
        .withMargin(marginAmount)
        .withProcessingFee(processingFeeAmount)
        .withValue(rateLoadedTypeAmountWithChannelDiscount)
        .withDailyTaxes(dailyTaxes)
        .withSubChargeType(SubChargeType.Adult)
        .withRoomNumber(Some(1))

      val roomPriceChild = aValidPrice
        .withDate(todayDate)
        .withQuantity(childQuanity)
        .withChargeType(ChargeType.Room)
        .withNetExclusive(600)
        .withTax(taxAmount)
        .withFee(feeAmount)
        .withMargin(marginAmount)
        .withProcessingFee(processingFeeAmount)
        .withChargeOption(ChargeOption.Mandatory)
        .withDailyTaxes(dailyTaxes)
        .withSubChargeType(SubChargeType.Child)
        .withRoomNumber(Some(1))

      val room = aValidRoom
        .withPrices(List(roomPriceAdult, roomPriceChild))
        .withRateType(rateType)
        .withOriginalRateType(rateType)
        .withMarginPercentage(marginPercentage)
        .withYplRoomEntry(aValidRoomEntry.copy())
        .withTaxInfo(Some(aValidTaxInfoWithTaxPrototypeLevelAndFee))
      val hotelTaxInfo = aValidHotelTaxInfo.withIsConfigProcessingFees(isConfigProcessingFees = false)
      aValidHotel.withRoom(room).withHotelTaxInfo(hotelTaxInfo)
    }

    def genMockYplHotelWithFeeChildAndNone(commissionPercent: Double,
                                           taxPercent: Double,
                                           rateType: RateType,
                                           rateLoadedTypeAmountWithChannelDiscount: Double,
                                           adultQuantity: Int = 1,
                                           childQuanity: Int = 0,
                                           noneQuantity: Int = 0) = {

      val dailyTaxes: DailyTaxes = aValidDailyTaxWithTaxPrototypeLevelAndFee

      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmountWithChannelDiscount, taxPercent + 20, commissionPercent)
      val taxAmount = netExclusive * taxPercent * 0.01
      val feeAmount = netExclusive * 20 * 0.01
      val processingFeeAmount = taxPercent * 0.01 * marginAmount

      val todayDate = new DateTime(2023, 1, 1, 0, 0)

      val roomPriceAdult = aValidPrice
        .withDate(todayDate)
        .withQuantity(adultQuantity)
        .withChargeType(ChargeType.Room)
        .withApplyType(ApplyType.PRPN)
        .withChargeOption(ChargeOption.Mandatory)
        .withNetExclusive(netExclusive)
        .withTax(taxAmount)
        .withFee(feeAmount)
        .withMargin(marginAmount)
        .withProcessingFee(processingFeeAmount)
        .withValue(rateLoadedTypeAmountWithChannelDiscount)
        .withDailyTaxes(dailyTaxes)
        .withSubChargeType(SubChargeType.Adult)
        .withRoomNumber(Some(1))

      val roomPriceChild = aValidPrice
        .withDate(todayDate)
        .withQuantity(childQuanity)
        .withChargeType(ChargeType.Room)
        .withNetExclusive(600)
        .withTax(taxAmount)
        .withFee(feeAmount)
        .withMargin(marginAmount)
        .withProcessingFee(processingFeeAmount)
        .withChargeOption(ChargeOption.Mandatory)
        .withDailyTaxes(dailyTaxes)
        .withSubChargeType(SubChargeType.Child)
        .withRoomNumber(Some(1))

      val roomPriceNone = aValidPrice
        .withDate(todayDate)
        .withQuantity(noneQuantity)
        .withChargeType(ChargeType.Room)
        .withNetExclusive(1000)
        .withTax(taxAmount)
        .withFee(feeAmount)
        .withMargin(marginAmount)
        .withProcessingFee(processingFeeAmount)
        .withChargeOption(ChargeOption.Mandatory)
        .withDailyTaxes(dailyTaxes)
        .withSubChargeType(SubChargeType.None)
        .withRoomNumber(Some(1))

      val room = aValidRoom
        .withPrices(List(roomPriceAdult, roomPriceChild, roomPriceNone))
        .withRateType(rateType)
        .withOriginalRateType(rateType)
        .withMarginPercentage(marginPercentage)
        .withYplRoomEntry(aValidRoomEntry.copy())
        .withTaxInfo(Some(aValidTaxInfoWithTaxPrototypeLevelAndFee))
      val hotelTaxInfo = aValidHotelTaxInfo.withIsConfigProcessingFees(isConfigProcessingFees = false)
      aValidHotel.withRoom(room).withHotelTaxInfo(hotelTaxInfo)
    }

    def calNetExMargin(rateType: RateType,
                       rateLoadedTypeAmount: Double,
                       taxPercent: Double,
                       commissionPercent: Double) = {

      lazy val toMarkupPercent = (commissionPercent / (100.0 - commissionPercent)) * 100.0

      val (taxMultiplier, markupMultiplier, markupPercent) = rateType match {
        case RateType.NetExclusive => (0.0, 0.0, commissionPercent)
        case RateType.NetInclusive => (taxPercent, 0.0, commissionPercent)
        case RateType.SellExclusive => (0.0, toMarkupPercent, toMarkupPercent)
        case RateType.SellInclusive => (taxPercent, toMarkupPercent, toMarkupPercent)
      }
      val netEx = (rateLoadedTypeAmount / (1.0 + (taxMultiplier * 0.01))) / (1.0 + markupMultiplier * 0.01)
      val margin = markupPercent * 0.01 * netEx
      val sellEx = netEx + margin
      (netEx, margin, margin / sellEx * 100.0)
    }

    // Use common tax library
    "correct tax prototype level and % tax for rateLoadedType: NetEx by common tax library - isNewChargeTypeTaxApplied is none" in {
      "VEL-2070=A: call PriceCalculation" in {
        val priceBreakdownCalc = new MockPriceBreakdownCalculator {}
        val taxPrototypeService = new TaxPrototypeServiceTest(priceBreakdownCalc)
        // assume tax level[1] with 12 %, correct level[2] with 18 %
        implicit val ctx = aValidYplContext

        val rateType = RateType.NetExclusive
        val rateLoadedTypeAmount = 2450.0 // WithChannelDiscount
        val commissionPercent = 10.0
        val assumedSellEx = taxPrototypeService.calculateAssumedSellEx(rateType,
                                                                       rateLoadedTypeAmount,
                                                                       commissionPercent,
                                                                       taxPrototypeLevels)
        val taxPercent =
          taxPrototypeService.getTaxPrototypeLevelFromSellEx(assumedSellEx, taxPrototypeLevels).get.taxValue
        val (netExclusive, marginAmount, marginPercentage) =
          calNetExMargin(rateType, rateLoadedTypeAmount, taxPercent, commissionPercent)

        val input = genMockYplHotel(
          netExclusive = netExclusive,
          commissionPercent = commissionPercent,
          taxPercent = taxPercent,
          rateType = rateType,
          rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
        )
        val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive
        val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
        val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
        val outputPrice = output.rooms.head.prices.head

        inputPriceSellEx should_== outputPrice.sellExclusive

        outputPrice.netExclusive.roundAt(2) should_== 2450.00
        outputPrice.tax.roundAt(2) should_== 441.00
        outputPrice.fee.roundAt(2) should_== 0.0
        outputPrice.margin.roundAt(2) should_== 245.00
        outputPrice.processingFee.roundAt(2) should_== 44.10
        outputPrice.netInclusive.roundAt(2) should_== 2891.00
        outputPrice.sellExclusive.roundAt(2) should_== 2695.00
        outputPrice.sellInclusive.roundAt(2) should_== 3180.10

        outputPrice.dailyTaxes.taxes.head.taxValue should_== 18.0
        outputPrice.dailyTaxes.taxes.head.tax.value should_== 18.0
        outputPrice.taxBreakDown.head.amount.roundAt(2) should_== 441.00

        priceBreakdownCalc.callCounter should_== 0
      }

      "VEL-2070=B: call PriceBreakdownCalculator" in {
        val priceBreakdownCalc = new MockPriceBreakdownCalculator {}
        val taxPrototypeService = new TaxPrototypeServiceTest(priceBreakdownCalc)

        // assume tax level[1] with 12 %, correct level[2] with 18 %
        implicit val ctx = YplContext(aValidYplRequest.withBExperiment(ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION))

        val rateType = RateType.NetExclusive
        val rateLoadedTypeAmount = 2450.0 // WithChannelDiscount
        val commissionPercent = 10.0
        val assumedSellEx = taxPrototypeService.calculateAssumedSellEx(rateType,
                                                                       rateLoadedTypeAmount,
                                                                       commissionPercent,
                                                                       taxPrototypeLevels)
        val taxPercent =
          taxPrototypeService.getTaxPrototypeLevelFromSellEx(assumedSellEx, taxPrototypeLevels).get.taxValue
        val (netExclusive, marginAmount, marginPercentage) =
          calNetExMargin(rateType, rateLoadedTypeAmount, taxPercent, commissionPercent)

        val input = genMockYplHotel(
          netExclusive = netExclusive,
          commissionPercent = commissionPercent,
          taxPercent = taxPercent,
          rateType = rateType,
          rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
        )
        val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive
        val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
        val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
        val outputPrice = output.rooms.head.prices.head

        inputPriceSellEx should_== outputPrice.sellExclusive

        outputPrice.netExclusive.roundAt(2) should_== 2450.00
        outputPrice.tax.roundAt(2) should_== 441.00
        outputPrice.fee.roundAt(2) should_== 0.0
        outputPrice.margin.roundAt(2) should_== 245.00
        outputPrice.processingFee.roundAt(2) should_== 44.10
        outputPrice.netInclusive.roundAt(2) should_== 2891.00
        outputPrice.sellExclusive.roundAt(2) should_== 2695.00
        outputPrice.sellInclusive.roundAt(2) should_== 3180.10

        outputPrice.dailyTaxes.taxes.head.taxValue should_== 18.0
        outputPrice.dailyTaxes.taxes.head.tax.value should_== 18.0
        outputPrice.taxBreakDown.head.amount.roundAt(2) should_== 441.00

        priceBreakdownCalc.callCounter should_== 1

        // verify that commissionExcludingWholesaleOrAgx is not used when excludeWholesaleOrAgx=true
        taxPrototypeService.lastExcludeWholesaleOrAgxValue should_== Some(true)
        outputPrice.margin should_!= -999999
      }
    }

    "correct tax prototype level and % tax for rateLoadedType: NetEx by common tax library - isNewChargeTypeTaxApplied is false" in {
      // assume tax level[1] with 12 %, correct level[2] with 18 %
      implicit val ctx = aValidYplContext

      val rateType = RateType.NetExclusive
      val rateLoadedTypeAmount = 2450.0 // WithChannelDiscount
      val commissionPercent = 10.0
      val assumedSellEx =
        taxPrototypeService.calculateAssumedSellEx(rateType, rateLoadedTypeAmount, commissionPercent, taxPrototypeLevels)
      val taxPercent =
        taxPrototypeService.getTaxPrototypeLevelFromSellEx(assumedSellEx, taxPrototypeLevels).get.taxValue
      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmount, taxPercent, commissionPercent)

      val input = genMockYplHotel(
        netExclusive = netExclusive,
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
      )
      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
      val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
      val outputPrice = output.rooms.head.prices.head

      inputPriceSellEx should_== outputPrice.sellExclusive

      outputPrice.netExclusive.roundAt(2) should_== 2450.00
      outputPrice.tax.roundAt(2) should_== 441.00
      outputPrice.fee.roundAt(2) should_== 0.0
      outputPrice.margin.roundAt(2) should_== 245.00
      outputPrice.processingFee.roundAt(2) should_== 44.10
      outputPrice.netInclusive.roundAt(2) should_== 2891.00
      outputPrice.sellExclusive.roundAt(2) should_== 2695.00
      outputPrice.sellInclusive.roundAt(2) should_== 3180.10

      outputPrice.dailyTaxes.taxes.head.taxValue should_== 18.0
      outputPrice.dailyTaxes.taxes.head.tax.value should_== 18.0
      outputPrice.taxBreakDown.head.amount.roundAt(2) should_== 441.00

    }

    "correct tax prototype level and % tax for rateLoadedType: NetEx by common tax library - isNewChargeTypeTaxApplied is true" in {
      // assume tax level[1] with 12 %, correct level[2] with 18 %
      implicit val ctx = aValidYplContext

      val rateType = RateType.NetExclusive
      val rateLoadedTypeAmount = 2450.0 // WithChannelDiscount
      val commissionPercent = 10.0
      val assumedSellEx =
        taxPrototypeService.calculateAssumedSellEx(rateType, rateLoadedTypeAmount, commissionPercent, taxPrototypeLevels)
      val taxPercent =
        taxPrototypeService.getTaxPrototypeLevelFromSellEx(assumedSellEx, taxPrototypeLevels).get.taxValue
      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmount, taxPercent, commissionPercent)

      val input = genMockYplHotel(
        netExclusive = netExclusive,
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
      )
      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
      val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
      val outputPrice = output.rooms.head.prices.head

      inputPriceSellEx should_== outputPrice.sellExclusive

      outputPrice.netExclusive.roundAt(2) should_== 2450.00
      outputPrice.tax.roundAt(2) should_== 441.00
      outputPrice.fee.roundAt(2) should_== 0.0
      outputPrice.margin.roundAt(2) should_== 245.00
      outputPrice.processingFee.roundAt(2) should_== 44.10
      outputPrice.netInclusive.roundAt(2) should_== 2891.00
      outputPrice.sellExclusive.roundAt(2) should_== 2695.00
      outputPrice.sellInclusive.roundAt(2) should_== 3180.10

      outputPrice.dailyTaxes.taxes.head.taxValue should_== 18.0
      outputPrice.dailyTaxes.taxes.head.tax.value should_== 18.0
      outputPrice.taxBreakDown.head.amount.roundAt(2) should_== 441.00
    }

    "correct tax prototype level and % tax for rateLoadedType: NetIn by common tax library" in {
      // assume tax level[2] with 18 %, correct level[1] with 12 %
      implicit val ctx = aValidYplContext

      val rateType = RateType.NetInclusive
      val rateLoadedTypeAmount = 2500.0 // WithChannelDiscount
      val commissionPercent = 10.0
      val assumedSellEx =
        taxPrototypeService.calculateAssumedSellEx(rateType, rateLoadedTypeAmount, commissionPercent, taxPrototypeLevels)
      val taxPercent =
        taxPrototypeService.getTaxPrototypeLevelFromSellEx(assumedSellEx, taxPrototypeLevels).get.taxValue
      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmount, taxPercent, commissionPercent)

      val input = genMockYplHotel(
        netExclusive = netExclusive,
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
      )
      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
      val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
      val outputPrice = output.rooms.head.prices.head

      inputPriceSellEx should_== outputPrice.sellExclusive

      outputPrice.netExclusive.roundAt(2) should_== 2232.14
      outputPrice.tax.roundAt(2) should_== 267.86
      outputPrice.fee.roundAt(2) should_== 0.0
      outputPrice.margin.roundAt(2) should_== 223.21
      outputPrice.processingFee.roundAt(2) should_== 26.79
      outputPrice.netInclusive.roundAt(2) should_== 2500.00
      outputPrice.sellExclusive.roundAt(2) should_== 2455.36
      outputPrice.sellInclusive.roundAt(2) should_== 2750.00

      outputPrice.dailyTaxes.taxes.head.taxValue should_== 12.0
      outputPrice.dailyTaxes.taxes.head.tax.value should_== 12.0
      outputPrice.taxBreakDown.head.amount.roundAt(2) should_== 267.86
    }

    "correct tax prototype level and % tax for rateLoadedType: SellEx by common tax library" in {
      // assume tax level[1] with 12 %, correct level[1] with 12 %
      implicit val ctx = aValidYplContext

      val rateType = RateType.SellExclusive
      val rateLoadedTypeAmount = 2450.0 // WithChannelDiscount
      val commissionPercent = 10.0
      val assumedSellEx =
        taxPrototypeService.calculateAssumedSellEx(rateType, rateLoadedTypeAmount, commissionPercent, taxPrototypeLevels)
      val taxPercent =
        taxPrototypeService.getTaxPrototypeLevelFromSellEx(assumedSellEx, taxPrototypeLevels).get.taxValue
      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmount, taxPercent, commissionPercent)

      val input = genMockYplHotel(
        netExclusive = netExclusive,
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
      )
      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
      val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
      val outputPrice = output.rooms.head.prices.head

      inputPriceSellEx should_== outputPrice.sellExclusive

      outputPrice.netExclusive.roundAt(2) should_== 2205.00
      outputPrice.tax.roundAt(2) should_== 264.60
      outputPrice.fee.roundAt(2) should_== 0.0
      outputPrice.margin.roundAt(2) should_== 245.00
      outputPrice.processingFee.roundAt(2) should_== 29.40
      outputPrice.netInclusive.roundAt(2) should_== 2469.60
      outputPrice.sellExclusive.roundAt(2) should_== 2450.00
      outputPrice.sellInclusive.roundAt(2) should_== 2744.00

      outputPrice.dailyTaxes.taxes.head.taxValue should_== 12.0
      outputPrice.dailyTaxes.taxes.head.tax.value should_== 12.0
      outputPrice.taxBreakDown.head.amount.roundAt(2) should_== 264.60
    }

    "correct tax prototype level and % tax for rateLoadedType: SellIn by common tax library" in {
      // assume tax level[2] with 18 %, correct level[1] with 12 %
      implicit val ctx = aValidYplContext

      val rateType = RateType.SellInclusive
      val rateLoadedTypeAmount = 2500.0 // WithChannelDiscount
      val commissionPercent = 10.0
      val assumedSellEx =
        taxPrototypeService.calculateAssumedSellEx(rateType, rateLoadedTypeAmount, commissionPercent, taxPrototypeLevels)
      val taxPercent =
        taxPrototypeService.getTaxPrototypeLevelFromSellEx(assumedSellEx, taxPrototypeLevels).get.taxValue
      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmount, taxPercent, commissionPercent)

      val input = genMockYplHotel(
        netExclusive = netExclusive,
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
      )

      val inputPirce = input.rooms.head.prices.head
      val inputPirceTax = inputPirce.tax
      val inputPircePf = inputPirce.processingFee
      val inputPirceMargin = inputPirce.margin

      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
      val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
      val outputPrice = output.rooms.head.prices.head

      inputPriceSellEx should_== outputPrice.sellExclusive

      outputPrice.netExclusive.roundAt(2) should_== 2008.93
      outputPrice.tax.roundAt(2) should_== 241.07
      outputPrice.fee.roundAt(2) should_== 0.0
      outputPrice.margin.roundAt(2) should_== 223.21
      outputPrice.processingFee.roundAt(2) should_== 26.79
      outputPrice.netInclusive.roundAt(2) should_== 2250.00
      outputPrice.sellExclusive.roundAt(2) should_== 2232.14
      outputPrice.sellInclusive.roundAt(2) should_== 2500.00

      outputPrice.dailyTaxes.taxes.head.taxValue should_== 12.0
      outputPrice.dailyTaxes.taxes.head.tax.value should_== 12.0
      outputPrice.taxBreakDown.head.amount.roundAt(2) should_== 241.07
    }

    "correct tax prototype level and % tax with ExtraBed by common tax library" in {
      implicit val ctx = aValidYplContext

      val rateType = RateType.NetExclusive
      val rateLoadedTypeAmount = 2000.0
      val rateLoadedTypeAmountExtraBed = 450.0
      val commissionPercent = 10.0
      val assumedSellEx = taxPrototypeService.calculateAssumedSellEx(rateType,
                                                                     rateLoadedTypeAmount + rateLoadedTypeAmountExtraBed,
                                                                     commissionPercent,
                                                                     taxPrototypeLevels)
      val taxPercent =
        taxPrototypeService.getTaxPrototypeLevelFromSellEx(assumedSellEx, taxPrototypeLevels).get.taxValue

      val input = genMockYplHotelWithExtraBed(
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
        rateLoadedTypeAmountExtraBed = rateLoadedTypeAmountExtraBed,
      )
      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
      val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
      val outputPrice = output.rooms.head.prices.head

      inputPriceSellEx should_== outputPrice.sellExclusive

      outputPrice.netExclusive.roundAt(2) should_== 2000.00
      outputPrice.tax.roundAt(2) should_== 360.00
      outputPrice.fee.roundAt(2) should_== 0.0
      outputPrice.margin.roundAt(2) should_== 200.00
      outputPrice.processingFee.roundAt(2) should_== 36.00
      outputPrice.netInclusive.roundAt(2) should_== 2360.00
      outputPrice.sellExclusive.roundAt(2) should_== 2200.00
      outputPrice.sellInclusive.roundAt(2) should_== 2596.0

      output.rooms.head.netExExtraBed should_== 450.0
      output.rooms.head.taxExtraBed should_== 81.0
      output.rooms.head.feeExtraBed should_== 0.0
      output.rooms.head.marginExtraBed should_== 45.0
      output.rooms.head.pfExtraBed.roundAt(2) should_== 8.1
      output.rooms.head.netInExtraBed should_== 531.0
      output.rooms.head.sellExExtraBed should_== 495.0
      output.rooms.head.sellInExtraBed should_== 584.1

      outputPrice.dailyTaxes.taxes.head.taxValue should_== 18.0
      outputPrice.dailyTaxes.taxes.head.tax.value should_== 18.0
      outputPrice.taxBreakDown.head.amount.roundAt(2) should_== 360.00
    }

    "correct retain tax prototype level and % tax with Surcharge by common tax library - isNewChargeTypeTaxApplied is none" in {
      "VEL-2070=A: Call PriceCalculation" in {
        // Retain tax as 18%
        val priceBreakdownCalc = new MockPriceBreakdownCalculator {}
        val taxPrototypeService = new TaxPrototypeServiceTest(priceBreakdownCalc)
        implicit val ctx = aValidYplContext.build

        val rateType = RateType.SellExclusive
        val rateLoadedTypeAmount = 2400.0
        val rateLoadedTypeAmountSurcharge = 240.0
        val commissionPercent = 10.0
        val taxPercent = 18.0

        val input = genMockYplHotelWithSurcharge(
          commissionPercent = commissionPercent,
          taxPercent = taxPercent,
          rateType = rateType,
          rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
          rateLoadedTypeAmountSurcharge = rateLoadedTypeAmountSurcharge,
        )
        val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

        val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
        val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
        val outputPrice = output.rooms.head.prices.filter(p => p.isRoom).head
        val outputSurcharge = output.rooms.head.prices.filter(p => p.isMandatorySurcharge).head

        inputPriceSellEx should_== outputPrice.sellExclusive

        outputPrice.netExclusive.roundAt(2) should_== 2160.00
        outputPrice.tax.roundAt(2) should_== 388.80
        outputPrice.fee.roundAt(2) should_== 0.00
        outputPrice.margin.roundAt(2) should_== 240.00
        outputPrice.processingFee.roundAt(2) should_== 43.20
        outputPrice.netInclusive.roundAt(2) should_== 2548.80
        outputPrice.sellExclusive.roundAt(2) should_== 2400.00
        outputPrice.sellInclusive.roundAt(2) should_== 2832.00

        output.rooms.head.netExSurcharge.roundAt(2) should_== 480.00
        output.rooms.head.taxSurcharge.roundAt(2) should_== 86.40

        outputSurcharge.netExclusive.roundAt(2) should_== 480.00
        outputSurcharge.tax.roundAt(2) should_== 86.40
        outputSurcharge.fee.roundAt(2) should_== 0.00
        outputSurcharge.margin.roundAt(2) should_== 48.00
        outputSurcharge.processingFee.roundAt(2) should_== 8.64
        outputSurcharge.netInclusive.roundAt(2) should_== 566.40
        outputSurcharge.sellExclusive.roundAt(2) should_== 528.00
        outputSurcharge.sellInclusive.roundAt(2) should_== 623.04

        priceBreakdownCalc.callCounter should_== 0
      }

      "VEL-2070=B: Call PriceBreakdownCalculator" in {
        // Retain tax as 18%
        val priceBreakdownCalc = new MockPriceBreakdownCalculator {}
        val taxPrototypeService = new TaxPrototypeServiceTest(priceBreakdownCalc)

        implicit val ctx = YplContext(aValidYplRequest.withBExperiment(ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION))

        val rateType = RateType.SellExclusive
        val rateLoadedTypeAmount = 2400.0
        val rateLoadedTypeAmountSurcharge = 240.0
        val commissionPercent = 10.0
        val taxPercent = 18.0

        val input = genMockYplHotelWithSurcharge(
          commissionPercent = commissionPercent,
          taxPercent = taxPercent,
          rateType = rateType,
          rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
          rateLoadedTypeAmountSurcharge = rateLoadedTypeAmountSurcharge,
        )
        val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

        val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
        val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
        val outputPrice = output.rooms.head.prices.filter(p => p.isRoom).head
        val outputSurcharge = output.rooms.head.prices.filter(p => p.isMandatorySurcharge).head

        inputPriceSellEx should_== outputPrice.sellExclusive

        outputPrice.netExclusive.roundAt(2) should_== 2160.00
        outputPrice.tax.roundAt(2) should_== 388.80
        outputPrice.fee.roundAt(2) should_== 0.00
        outputPrice.margin.roundAt(2) should_== 240.00
        outputPrice.processingFee.roundAt(2) should_== 43.20
        outputPrice.netInclusive.roundAt(2) should_== 2548.80
        outputPrice.sellExclusive.roundAt(2) should_== 2400.00
        outputPrice.sellInclusive.roundAt(2) should_== 2832.00

        output.rooms.head.netExSurcharge.roundAt(2) should_== 480.00
        output.rooms.head.taxSurcharge.roundAt(2) should_== 86.40

        outputSurcharge.netExclusive.roundAt(2) should_== 480.00
        outputSurcharge.tax.roundAt(2) should_== 86.40
        outputSurcharge.fee.roundAt(2) should_== 0.00
        outputSurcharge.margin.roundAt(2) should_== 48.00
        outputSurcharge.processingFee.roundAt(2) should_== 8.64
        outputSurcharge.netInclusive.roundAt(2) should_== 566.40
        outputSurcharge.sellExclusive.roundAt(2) should_== 528.00
        outputSurcharge.sellInclusive.roundAt(2) should_== 623.04

        // called for calculateRoom and calculateSurcharge
        priceBreakdownCalc.callCounter should_== 2
      }
    }

    "correct retain taxV2 prototype level and % taxV2 with Surcharge by common tax library - isNewChargeTypeTaxApplied is none" in {
      // Retain tax as 18%
      implicit val ctx = aValidYplContext.build

      val rateType = RateType.SellExclusive
      val rateLoadedTypeAmount = 2400.0
      val rateLoadedTypeAmountSurcharge = 240.0
      val commissionPercent = 10.0
      val taxPercent = 18.0

      val input = genMockYplHotelWithSurcharge(
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
        rateLoadedTypeAmountSurcharge = rateLoadedTypeAmountSurcharge,
        isTaxV2Available = true,
      )
      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
      val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
      val outputPrice = output.rooms.head.prices.filter(p => p.isRoom).head
      val outputSurcharge = output.rooms.head.prices.filter(p => p.isMandatorySurcharge).head

      inputPriceSellEx should_== outputPrice.sellExclusive

      outputPrice.netExclusive.roundAt(2) should_== 2160.00
      outputPrice.tax.roundAt(2) should_== 388.80
      outputPrice.fee.roundAt(2) should_== 0.00
      outputPrice.margin.roundAt(2) should_== 240.00
      outputPrice.processingFee.roundAt(2) should_== 43.20
      outputPrice.netInclusive.roundAt(2) should_== 2548.80
      outputPrice.sellExclusive.roundAt(2) should_== 2400.00
      outputPrice.sellInclusive.roundAt(2) should_== 2832.00

      output.rooms.head.netExSurcharge.roundAt(2) should_== 480.00
      output.rooms.head.taxSurcharge.roundAt(2) should_== 86.40

      outputSurcharge.netExclusive.roundAt(2) should_== 480.00
      outputSurcharge.tax.roundAt(2) should_== 86.40
      outputSurcharge.fee.roundAt(2) should_== 0.00
      outputSurcharge.margin.roundAt(2) should_== 48.00
      outputSurcharge.processingFee.roundAt(2) should_== 8.64
      outputSurcharge.netInclusive.roundAt(2) should_== 566.40
      outputSurcharge.sellExclusive.roundAt(2) should_== 528.00
      outputSurcharge.sellInclusive.roundAt(2) should_== 623.04
    }

    "correct recalculate tax prototype level and % tax with Surcharge by common tax library" in {
      // Recalculate from 12% to 18%
      implicit val ctx = aValidYplContext.build

      val rateType = RateType.SellExclusive
      val rateLoadedTypeAmount = 2400.0
      val rateLoadedTypeAmountSurcharge = 240.0
      val commissionPercent = 10.0
      val taxPercent = 12.0

      val input = genMockYplHotelWithSurcharge(
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
        rateLoadedTypeAmountSurcharge = rateLoadedTypeAmountSurcharge,
      )
      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
      val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
      val outputPrice = output.rooms.head.prices.filter(p => p.isRoom).head
      val outputSurcharge = output.rooms.head.prices.filter(p => p.isMandatorySurcharge).head

      inputPriceSellEx should_== outputPrice.sellExclusive

      outputPrice.netExclusive.roundAt(2) should_== 2160.00
      outputPrice.tax.roundAt(2) should_== 388.80
      outputPrice.fee.roundAt(2) should_== 0.00
      outputPrice.margin.roundAt(2) should_== 240.00
      outputPrice.processingFee.roundAt(2) should_== 43.20
      outputPrice.netInclusive.roundAt(2) should_== 2548.80
      outputPrice.sellExclusive.roundAt(2) should_== 2400.00
      outputPrice.sellInclusive.roundAt(2) should_== 2832.00

      output.rooms.head.netExSurcharge.roundAt(2) should_== 480.00
      output.rooms.head.taxSurcharge.roundAt(2) should_== 86.40

      outputSurcharge.netExclusive.roundAt(2) should_== 480.00
      outputSurcharge.tax.roundAt(2) should_== 86.40
      outputSurcharge.fee.roundAt(2) should_== 0.00
      outputSurcharge.margin.roundAt(2) should_== 48.00
      outputSurcharge.processingFee.roundAt(2) should_== 8.64
      outputSurcharge.netInclusive.roundAt(2) should_== 566.40
      outputSurcharge.sellExclusive.roundAt(2) should_== 528.00
      outputSurcharge.sellInclusive.roundAt(2) should_== 623.04
    }

    "correct retain tax prototype level and % tax with Fee by common tax library" in {
      // Retain tax at 18%
      implicit val ctx = aValidYplContext

      val rateType = RateType.SellExclusive
      val rateLoadedTypeAmount = 2400.0
      val commissionPercent = 10.0
      val taxPercent = 18.0

      val input = genMockYplHotelWithFee(
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
      )

      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
      val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
      val outputPrice = output.rooms.head.prices.filter(p => p.isRoom).head

      inputPriceSellEx should_== outputPrice.sellExclusive

      outputPrice.netExclusive.roundAt(2) should_== 2160.00
      outputPrice.tax.roundAt(2) should_== 388.80
      outputPrice.fee.roundAt(2) should_== 432.00
      outputPrice.margin.roundAt(2) should_== 240.00
      outputPrice.processingFee.roundAt(2) should_== 91.20
      outputPrice.netInclusive.roundAt(2) should_== 2980.80
      outputPrice.sellExclusive.roundAt(2) should_== 2400.00
      outputPrice.sellInclusive.roundAt(2) should_== 3312.00
    }

    "correct recalculate tax prototype level and % tax with Fee by common tax library" in {
      implicit val ctx = aValidYplContext
      def validateResult(inputPriceSellEx: Double, outputYplPrice: YplPrice) = {
        inputPriceSellEx should_== outputYplPrice.sellExclusive
        outputYplPrice.netExclusive.roundAt(2) should_== 2160.00
        outputYplPrice.tax.roundAt(2) should_== 388.80
        outputYplPrice.fee.roundAt(2) should_== 432.00
        outputYplPrice.margin.roundAt(2) should_== 240.00
        outputYplPrice.processingFee.roundAt(2) should_== 91.20
        outputYplPrice.netInclusive.roundAt(2) should_== 2980.80
        outputYplPrice.sellExclusive.roundAt(2) should_== 2400.00
        outputYplPrice.sellInclusive.roundAt(2) should_== 3312.00
      }

      // Recalculate from 12% to 18%
      val rateType = RateType.SellExclusive
      val rateLoadedTypeAmount = 2400.0
      val commissionPercent = 10.0
      val taxPercent = 12.0

      val input = genMockYplHotelWithFee(
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
      )

      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      "return subChargeType.Adult and Some(roomNo)" in {
        val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
        val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
        val outputPrice = output.rooms.head.prices.filter(p => p.isRoom).head
        val totalSellExwithExtraBed = output.rooms.head.totalSellExWithExtraBed
        validateResult(inputPriceSellEx, outputPrice)
        outputPrice.subChargeType should_== SubChargeType.Adult
        outputPrice.roomNumber should_== Some(1)
        totalSellExwithExtraBed should_== 2400.00
      }

    }
    "correct recalculate tax prototype level and % tax with Fee by common tax library with 1 child" in {
      implicit val ctx = aValidYplContext
      def validateResult(inputPriceSellEx: Double, outputYplPrice: YplPrice) = {
        inputPriceSellEx should_== outputYplPrice.sellExclusive
        outputYplPrice.netExclusive.roundAt(2) should_== 2160.00
        outputYplPrice.tax.roundAt(2) should_== 388.80
        outputYplPrice.fee.roundAt(2) should_== 432.00
        outputYplPrice.margin.roundAt(2) should_== 240.00
        outputYplPrice.processingFee.roundAt(2) should_== 91.20
        outputYplPrice.netInclusive.roundAt(2) should_== 2980.80
        outputYplPrice.sellExclusive.roundAt(2) should_== 2400.00
        outputYplPrice.sellInclusive.roundAt(2) should_== 3312.00
      }

      // Recalculate from 12% to 18%
      val rateType = RateType.SellExclusive
      val rateLoadedTypeAmount = 2400.0
      val commissionPercent = 10.0
      val taxPercent = 12.0

      val input = genMockYplHotelWithFeeAndChild(
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
        childQuanity = 1,
      )

      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      "return subChargeType.Adult and Some(roomNo)" in {
        val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
        val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
        val outputPrice = output.rooms.head.prices.filter(p => p.isRoom).head
        val totalSellExwithExtraBed = output.rooms.head.totalSellExWithExtraBed
        validateResult(inputPriceSellEx, outputPrice)
        outputPrice.subChargeType should_== SubChargeType.Adult
        outputPrice.roomNumber should_== Some(1)
        totalSellExwithExtraBed should_== 3240.00
      }
    }
    "correct recalculate tax prototype level and % tax with Fee by common tax library with 1 child and 1 None" in {
      implicit val ctx = aValidYplContext
      def validateResult(inputPriceSellEx: Double, outputYplPrice: YplPrice) = {
        inputPriceSellEx should_== outputYplPrice.sellExclusive
        outputYplPrice.netExclusive.roundAt(2) should_== 2160.00
        outputYplPrice.tax.roundAt(2) should_== 388.80
        outputYplPrice.fee.roundAt(2) should_== 432.00
        outputYplPrice.margin.roundAt(2) should_== 240.00
        outputYplPrice.processingFee.roundAt(2) should_== 91.20
        outputYplPrice.netInclusive.roundAt(2) should_== 2980.80
        outputYplPrice.sellExclusive.roundAt(2) should_== 2400.00
        outputYplPrice.sellInclusive.roundAt(2) should_== 3312.00
      }

      // Recalculate from 12% to 18%
      val rateType = RateType.SellExclusive
      val rateLoadedTypeAmount = 2400.0
      val commissionPercent = 10.0
      val taxPercent = 12.0

      val input = genMockYplHotelWithFeeChildAndNone(
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
        childQuanity = 1,
        noneQuantity = 1,
      )

      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive

      "return subChargeType.Adult and Some(roomNo)" in {
        val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
        val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
        val outputPrice = output.rooms.head.prices.filter(p => p.isRoom).head
        val totalSellExwithExtraBed = output.rooms.head.totalSellExWithExtraBed
        validateResult(inputPriceSellEx, outputPrice)
        outputPrice.subChargeType should_== SubChargeType.Adult
        outputPrice.roomNumber should_== Some(1)
        totalSellExwithExtraBed should_== 4480.0
      }
    }

    "pass correct value for fixMarriottSurchargeExp" should {
      val price = aValidPrice
      val taxPrototypeService = new TaxPrototypeServiceTest {
        override def calculateSurcharge(paymentModel: PaymentModel,
                                        dailyPrice: DailyPrice,
                                        surchargeRateType: RateType,
                                        surchargeEntry: SurchargeEntry,
                                        roomPrices: List[YplPrice],
                                        taxInfo: TaxInfo,
                                        room: YplRoomEntry,
                                        reqOcc: YplReqOccByHotelAgePolicy,
                                        isPull: Boolean,
                                        supplierId: SupplierId,
                                        supplierContractedCommission: Option[Double] = None,
                                        hotelId: HotelId,
                                        chainId: ChainId,
                                        countryId: CountryId,
                                        fixMarriottSurchargeExp: Boolean)(isBcomFixTaxAmountApplyToPB: Boolean)(implicit
          ctx: YplContext,
          propertyContext: PropertyContext): Option[YplPrice] =
          if (fixMarriottSurchargeExp) {
            Some(price.withQuantity(2))
          } else Some(price.withQuantity(1))
      }

      val marriott = DMC.Marriott.value

      "return true when VEL-1935 is B and supplier is Marriott" in {
        val yplHotel = aValidHotel
          .withRoom(
            aValidRoom
              .withSupplierId(marriott)
              .withYplRoomEntry(aValidRoomEntry)
              .withTaxInfo(Some(aValidTaxInfo))
              .withPrices(
                List(
                  price
                    .withChargeType(ChargeType.Surcharge)
                    .withSurchargeRateType(Some(NetExclusive))
                    .withDailyPrice(Some(aValidDailyPrice))
                    .withSurchargeEntry(Some(aValidSurchargeEntry))
                    .withDailyTaxes(aValidDailyTaxWithTaxPrototypeLevel)
                    .build)),
          )
          .build
        val yplContext = mock[YplContext]
        val expContext = mock[ExperimentContext]

        when(expContext.determineVariant(any(), ArgumentMatchers.eq(ABTest.MARRIOTT_SURCHARGE_ISSUE)))
          .thenReturn(Variant.B)
        when(yplContext.experimentContext).thenReturn(expContext)
        when(yplContext.request).thenReturn(aValidYplRequest)

        val result = taxPrototypeService.correctTaxPrototypeLevel(yplHotel, Map(marriott -> yplContext))
        result.rooms.head.prices.head.quantity shouldEqual 2
      }

      "return false when VEL-1935 is A and supplier is Marriott" in {
        val yplHotel = aValidHotel
          .withRoom(
            aValidRoom
              .withSupplierId(marriott)
              .withYplRoomEntry(aValidRoomEntry)
              .withTaxInfo(Some(aValidTaxInfo))
              .withPrices(
                List(
                  price
                    .withChargeType(ChargeType.Surcharge)
                    .withSurchargeRateType(Some(NetExclusive))
                    .withDailyPrice(Some(aValidDailyPrice))
                    .withSurchargeEntry(Some(aValidSurchargeEntry))
                    .withDailyTaxes(aValidDailyTaxWithTaxPrototypeLevel)
                    .build)),
          )
          .build
        val yplContext = mock[YplContext]
        val expContext = mock[ExperimentContext]

        when(expContext.determineVariant(any(), ArgumentMatchers.eq(ABTest.MARRIOTT_SURCHARGE_ISSUE)))
          .thenReturn(Variant.A)
        when(yplContext.experimentContext).thenReturn(expContext)
        when(yplContext.request).thenReturn(aValidYplRequest)

        val result = taxPrototypeService.correctTaxPrototypeLevel(yplHotel, Map(marriott -> yplContext))
        result.rooms.head.prices.head.quantity shouldEqual 1
      }
    }

    "pass supplierFundedDiscount, uspaDiscountAmount, uspaProgramId to calculatePrice" in {
      implicit val ctx = aValidYplContext.build

      val rateType = RateType.NetExclusive
      val rateLoadedTypeAmount = 2450.0 // WithChannelDiscount
      val commissionPercent = 10.0
      val assumedSellEx =
        taxPrototypeService.calculateAssumedSellEx(rateType, rateLoadedTypeAmount, commissionPercent, taxPrototypeLevels)
      val taxPercent =
        taxPrototypeService.getTaxPrototypeLevelFromSellEx(assumedSellEx, taxPrototypeLevels).get.taxValue
      val (netExclusive, marginAmount, marginPercentage) =
        calNetExMargin(rateType, rateLoadedTypeAmount, taxPercent, commissionPercent)

      val input = genMockYplHotel(
        netExclusive = netExclusive,
        commissionPercent = commissionPercent,
        taxPercent = taxPercent,
        rateType = rateType,
        rateLoadedTypeAmountWithChannelDiscount = rateLoadedTypeAmount,
        supplierFundedDiscount = Some(17),
        uspaDiscountAmount = Some(5.0),
        uspaProgramId = Some(2),
      )
      val inputPriceSellEx = input.rooms.head.prices.head.sellExclusive
      val supplierIdToContextMap = Map(input.rooms.head.supplierId -> ctx)
      val output = taxPrototypeService.correctTaxPrototypeLevel(input, supplierIdToContextMap)
      val outputPrice = output.rooms.head.prices.head

      outputPrice.supplierFundedDiscountAmount shouldEqual Some(17.0)
      outputPrice.uspaDiscountAmount shouldEqual Some(5)
      outputPrice.uspaProgramId shouldEqual Some(2)
    }

    "getIsPull" should {
      val bVariantSupplierRateInfo = aValidOTASupplierRateInfo.withExternalData(
        Seq(ExternalData(YplExperiments.FOR_PULL_SPLIT_PROTO_PRICE_TO_ROOM_AND_SURCHARGE, "B")))
      val bDMCDataHolder = aValidDMCDataHolder.withSupplierRateInfo(Some(bVariantSupplierRateInfo))
      val roomEntryWithBVariant = aValidRoomEntry.withDmcDataHolder(Some(bDMCDataHolder))
      val pullSupplierId = 12345
      val pushSupplierId = 332
      val supplierMap = Map(
        pullSupplierId -> SupplierInfo(pullSupplierId, SupplierType.Pull, true, true),
        pushSupplierId -> SupplierInfo(pushSupplierId, SupplierType.Push, true, true),
      )

      val pullRoomWithBVariant = aValidRoom.withYplRoomEntry(roomEntryWithBVariant).withSupplierId(pullSupplierId)
      val pullRoom = aValidRoom.withSupplierId(pullSupplierId)
      val pushRoom = aValidRoom.withSupplierId(pushSupplierId)

      def test(room: YPLRoom, hotelIsPull: Boolean)(expectedResult: Boolean) = {
        val hotel = aValidHotel
          .withRoom(room)
          .withSuppliers(supplierMap)
          .withSourceType(if (hotelIsPull) Some(_root_.com.agoda.papi.ypl.models.proto.enums.SourceTypes.Pull) else None)

        taxPrototypeService.getIsPull(
          room,
          hotel,
        ) shouldEqual (expectedResult)
      }

      "return true for pull Room with LT-1349 is B and hotel.isPull is false" in {
        test(pullRoomWithBVariant, false)(true)
      }

      "return false for pull Room with LT-1349 is not B and hotel.isPull is false" in {
        test(pullRoom, false)(false)
      }

      "return true with LT-1349 is not B and hotel.isPull is true" in {
        test(pullRoom, true)(true)
      }

      "return true for pushRoom with hotel.isPull is true" in {
        test(pushRoom, true)(true)
      }

      "return false for pushRoom with hotel.isPull is false" in {
        test(pushRoom, false)(false)
      }
    }
    "getSupplierIdForRoomOrHotel should select correct supplierId based on experiment" in {
      val taxPrototypeService = new TaxPrototypeServiceTest

      val roomSupplierId = 123
      val hotelSupplierId = 456
      val propertyId = 789L
      val cityId = 1
      val countryId = 2

      val room = aValidRoom.withSupplierId(roomSupplierId)
      val hotel = aValidHotel
        .withId(propertyId)
        .withCityId(cityId)
        .withCountryId(countryId)
        .withSupplierId(hotelSupplierId)
        .withRoom(room)

      val expContext = mock[ExperimentContext]
      val ctx = mock[YplContext]
      when(ctx.experimentContext).thenReturn(expContext)
      when(ctx.request).thenReturn(aValidYplRequest)
      val propertyContext = PropertyContext(propertyId, cityId, countryId)

      // B variant: should use room.supplierId
      when(expContext.isPropertyB(propertyContext, ABTest.ENABLE_ROOM_SUPPLIER_ID)).thenReturn(true)
      taxPrototypeService.testGetSupplierIdForRoomOrHotel(ctx, room, hotel) mustEqual roomSupplierId

      // A variant: should use hotel.supplierId
      when(expContext.isPropertyB(propertyContext, ABTest.ENABLE_ROOM_SUPPLIER_ID)).thenReturn(false)
      taxPrototypeService.testGetSupplierIdForRoomOrHotel(ctx, room, hotel) mustEqual hotelSupplierId
    }
  }

  "TaxProtoTypeService.excludeWholesaleOrAgx should return true" in {
    TaxPrototypeService.excludeWholesaleOrAgx shouldEqual true
  }
}
