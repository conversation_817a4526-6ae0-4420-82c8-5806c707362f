package com.agoda.papi.ypl.pricing

import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.enums.room.{ChargeOption, ChargeType, RateType}
import com.agoda.papi.pricing.pricecalculation.api.PriceBreakdownCalculatorInterface
import com.agoda.papi.pricing.pricecalculation.models.request.PriceCalculationRequest
import com.agoda.papi.pricing.pricecalculation.models.response.Calculation
import com.agoda.papi.pricing.pricecalculation.pricing.{
  CommissionCalculatorImpl,
  PriceBreakdownCalculatorImpl,
  TaxBreakdownCalculatorImpl,
}
import com.agoda.papi.pricing.pricecalculation.utils.TaxFiltersCacheInitialization
import com.agoda.papi.ypl.commission.service.CommissionServiceImpl
import com.agoda.papi.ypl.commission.{CommissionHolder, MORPCandidateRoomParameters, ProtobufCommissionHolder}
import com.agoda.papi.ypl.models.YPLTestDataBuilders._
import com.agoda.papi.ypl.models.pricing.{RoomOccupancy, YplPrice}
import com.agoda.papi.ypl.models.{YPLTestContexts, YplRegulationFeatureEnabledSetting}
import com.agoda.papi.ypl.services.TaxPrototypeServiceImpl
import com.agoda.utils.flow.PropertyContextImpl
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.core.Fragment

class PriceCalculationForSurchargeSpec extends SpecificationWithJUnit with YPLTestContexts {

  trait MockPriceBreakdownCalculator extends PriceBreakdownCalculatorInterface {
    override def calculate(request: PriceCalculationRequest): Calculation = Calculation(
      netEx = 900.01,
      margin = 99.99,
      tax = 90.0,
      fee = 0.0,
      processingFees = (10.0, None),
      breakdowns = List.empty,
      taxOverSellEx = 0.0,
      surchargeTax = 10.0,
      dailyTaxes = request.priceInfo.taxAndFee.dailyTaxes,
    )
  }

  val calc = new PriceCalculatorImpl
    with PriceCalculationImpl
    with TaxCalculatorImpl
    with TaxPrototypeServiceImpl
    with CommissionServiceImpl {
    override val priceBreakdownCalculator: PriceBreakdownCalculatorInterface =
      new PriceBreakdownCalculatorImpl(new TaxBreakdownCalculatorImpl(), new CommissionCalculatorImpl())

    override def getCommissionForPriceCalculation(commissionHolder: CommissionHolder,
                                                  stayDate: DateTime,
                                                  occupancy: Int,
                                                  isAgodaAgency: Boolean,
                                                  applicableMORPCandidateRoomParameters: MORPCandidateRoomParameters,
                                                  originalRateType: RateType,
                                                  targetRateType: RateType,
                                                  excludeWholesaleOrAgx: Boolean): Double = 10.0
  }

  val calcWithPriceBreakdownCalculatorMock = new PriceCalculatorImpl
    with PriceCalculationImpl
    with TaxCalculatorImpl
    with TaxPrototypeServiceImpl
    with CommissionServiceImpl {
    override val priceBreakdownCalculator: PriceBreakdownCalculatorInterface = new MockPriceBreakdownCalculator {}

    override def getCommissionForPriceCalculation(commissionHolder: CommissionHolder,
                                                  stayDate: DateTime,
                                                  occupancy: Int,
                                                  isAgodaAgency: Boolean,
                                                  applicableMORPCandidateRoomParameters: MORPCandidateRoomParameters,
                                                  originalRateType: RateType,
                                                  targetRateType: RateType,
                                                  excludeWholesaleOrAgx: Boolean): Double = 10.0
  }

  TaxFiltersCacheInitialization.initializeMockCache()

  // Calculate processingFee correctly when surcharge option is not Excluded (tier tax always on)
  "calculateSurchargeForPriceCalculationRefactor" should {
    def calculateSurcharge(value: Double = 20.0,
                           option: ChargeOption = ChargeOption.Mandatory,
                           wlShowExclusivePriceWithFeeEnabled: Boolean = false,
                           isXmlPartner: Boolean = false,
                           isCommissionable: Boolean = true,
                           useMockCalculation: Boolean = false,
                           isAmount: Boolean = true,
                           supplierContractedCommission: Option[Double] = Some(10.0)): Option[YplPrice] = {
      val surchargeEntry = aValidSurchargeEntry.copy(isAmount = isAmount,
                                                     value = value,
                                                     option = option,
                                                     isCommissionable = isCommissionable)
      val context = aValidYplContext.build
      val contextWithRegulation = context.copy(request = context.request.copy(
        regulationFeatureEnabledSetting = YplRegulationFeatureEnabledSetting.default
          .copy(isShowExclusivePriceWithFeeEnabled = wlShowExclusivePriceWithFeeEnabled),
        isXmlPartner = isXmlPartner,
      ))
      val price = aValidPrice.withValue(value)
      val roomPrice = aValidPrice
        .withValue(value)
        .withChargeType(ChargeType.Room)
        .withDailyTaxes(aValidDailyTaxWithTaxPrototypeLevelWithValue)
      val aValidCommissionHolderWithProtobuf = aValidCommissionHolder.copy(
        daily = Map(
          price.date -> aValidCommissionDailyHolder.copy(
            protobufCommissionHolder = ProtobufCommissionHolder(10.0, 10.0),
          )),
      )

      val calculator = if (useMockCalculation) calcWithPriceBreakdownCalculatorMock else calc
      calculator.calculateSurchargeForPriceCalculationRefactor(
        paymentModel = PaymentModel.Merchant,
        dailyPrice = aValidDailyPrice.withTaxes(Map((1, 11111) -> 15d)),
        surchargeRateType = RateType.NetExclusive,
        surchargeEntry = surchargeEntry,
        roomPrices = List(roomPrice),
        taxInfo = aValidTaxInfo.withTaxes(Map((1, 11111) -> aValidTaxWithTaxPrototypeLevel.withTaxValue(15.0))),
        room = aValidRoomEntry.withCommissionHolder(aValidCommissionHolderWithProtobuf),
        reqOcc = aValidReqOcc,
        isPull = false,
        supplierId = aValidSupplierId,
        supplierContractedCommission = supplierContractedCommission,
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
        fixMarriottSurchargeExp = false,
      )(contextWithRegulation, PropertyContextImpl(1, 1, 1))
    }

    // TODO: check why do we need this logic
    "Calculate Surcharge with positive amount" in {
      val result = calculateSurcharge()
      result.map(_.value) shouldEqual (Some(20.0))
      result.map(_.downliftPercent) shouldEqual (Some(0.0))
    }

    // TODO: check why do we need this logic
    "Calculate Surcharge with positive amount and isAmount = false" in {
      val result = calculateSurcharge(15.0, isAmount = false)
      result.map(_.value) shouldEqual (Some(0.75))
      result.map(_.downliftPercent) shouldEqual (Some(15.0))
    }

    "Calculate Surcharge with zero amount" in {
      val result = calculateSurcharge(0.0)
      result shouldEqual (None)
    }

    "Calculate Surcharge with negative amount" in {
      val result = calculateSurcharge(-20.0)
      result shouldEqual (None)
    }

    "Calculate Surcharge - Excluded" >> {
      Fragment.foreach(
        List(
          (false, false, None, Some(20), Some(20), Some(0), Some(0), Some(0), Some(0.0)),
          (true, false, None, Some(20), Some(17.391304347826086), Some(2.608695652173913), Some(0), Some(0), Some(0.0)),
          (false,
           true,
           Some(12.0),
           Some(20),
           Some(17.391304347826086),
           Some(2.608695652173913),
           Some(0),
           Some(0),
           Some(12.0)),
        )) {
        case (wlShowExclusivePriceWithFeeEnabled,
              isXmlPartner,
              supplierContractedCommission,
              expectedSellIn,
              expectedNetEx,
              expectedTax,
              expectedFee,
              expectedMargin,
              expectedRefCom) =>
          s"With isXmlPartner=$isXmlPartner, wlShowExclusivePriceWithFeeEnabled=$wlShowExclusivePriceWithFeeEnabled" ! {
            val result = calculateSurcharge(20,
                                            ChargeOption.Excluded,
                                            wlShowExclusivePriceWithFeeEnabled,
                                            isXmlPartner,
                                            supplierContractedCommission = supplierContractedCommission)
            result.map(_.sellInclusive) must_== expectedSellIn
            result.map(_.netExclusive) must_== expectedNetEx
            result.map(_.tax) must_== expectedTax
            result.map(_.fee) must_== expectedFee
            result.map(_.margin) must_== expectedMargin
            result.map(_.referenceCommissionPercent) shouldEqual expectedRefCom
            result.map(_.processingFee) shouldEqual Some(0.0)
          }
      }
    }

    "Calculate referenceCommissionPercent correctly when commissionable for Surcharge Mandatory" in {
      val result = calculateSurcharge(100.0, isCommissionable = true)
      result.map(_.referenceCommissionPercent) shouldEqual Some(10.0)
    }

    "Calculate referenceCommissionPercent correctly when non commissionable for Surcharge Mandatory" in {
      val result = calculateSurcharge(100.0, isCommissionable = false)
      result.map(_.referenceCommissionPercent) shouldEqual Some(0.0)
    }

    "Calculate processingFee correctly when surcharge option is not Excluded" in {
      val result = calculateSurcharge(100.0, ChargeOption.Mandatory, useMockCalculation = true)
      result.map(_.processingFee) shouldEqual Some(10.0)
    }

    "Calculate processingFee correctly when surcharge option is Excluded" in {
      val result = calculateSurcharge(100.0,
                                      ChargeOption.Excluded,
                                      wlShowExclusivePriceWithFeeEnabled = true,
                                      useMockCalculation = true)
      result.map(_.processingFee) shouldEqual Some(0.0)
    }

    "Return None when quantity is zero" in {
      val surchargeEntry = aValidSurchargeEntry.copy(
        isAmount = true,
        value = 20.0,
        option = ChargeOption.Mandatory,
        isCommissionable = true,
        applyTo = "PA",
      )

      val context = aValidYplContext.build
      val contextWithRegulation = context.copy(request = context.request.copy(
        regulationFeatureEnabledSetting =
          YplRegulationFeatureEnabledSetting.default.copy(isShowExclusivePriceWithFeeEnabled = false),
        isXmlPartner = false,
      ))

      val price = aValidPrice.withValue(20.0)
      val roomPrice = aValidPrice
        .withValue(20.0)
        .withChargeType(ChargeType.Room)
        .withDailyTaxes(aValidDailyTaxWithTaxPrototypeLevelWithValue)

      val aValidCommissionHolderWithProtobuf = aValidCommissionHolder.copy(
        daily = Map(
          price.date -> aValidCommissionDailyHolder.copy(
            protobufCommissionHolder = ProtobufCommissionHolder(10.0, 10.0),
          )),
      )

      val roomWithZeroAdults = aValidRoomEntry
        .withCommissionHolder(aValidCommissionHolderWithProtobuf)
        .build
        .copy(occEntry = RoomOccupancy(0, 0)) // 0 adults, 0 children

      val reqOccWithFreeOcc = aValidReqOcc.withAdults(0)

      val result = calcWithPriceBreakdownCalculatorMock.calculateSurchargeForPriceCalculationRefactor(
        paymentModel = PaymentModel.Merchant,
        dailyPrice = aValidDailyPrice.withTaxes(Map((1, 11111) -> 15d)),
        surchargeRateType = RateType.NetExclusive,
        surchargeEntry = surchargeEntry,
        roomPrices = List(roomPrice),
        taxInfo = aValidTaxInfo.withTaxes(Map((1, 11111) -> aValidTaxWithTaxPrototypeLevel.withTaxValue(15.0))),
        room = roomWithZeroAdults,
        reqOcc = reqOccWithFreeOcc,
        isPull = false,
        supplierId = aValidSupplierId,
        supplierContractedCommission = Some(10.0),
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
        fixMarriottSurchargeExp = false,
      )(contextWithRegulation, PropertyContextImpl(1, 1, 1))

      result shouldEqual None
    }

    "Return None when quantity is negative" in {
      val surchargeEntry = aValidSurchargeEntry.copy(
        isAmount = true,
        value = 20.0,
        option = ChargeOption.Mandatory,
        isCommissionable = true,
        applyTo = "PC",
      )

      val context = aValidYplContext.build
      val contextWithRegulation = context.copy(request = context.request.copy(
        regulationFeatureEnabledSetting =
          YplRegulationFeatureEnabledSetting.default.copy(isShowExclusivePriceWithFeeEnabled = false),
        isXmlPartner = false,
      ))

      val price = aValidPrice.withValue(20.0)
      val roomPrice = aValidPrice
        .withValue(20.0)
        .withChargeType(ChargeType.Room)
        .withDailyTaxes(aValidDailyTaxWithTaxPrototypeLevelWithValue)

      val aValidCommissionHolderWithProtobuf = aValidCommissionHolder.copy(
        daily = Map(
          price.date -> aValidCommissionDailyHolder.copy(
            protobufCommissionHolder = ProtobufCommissionHolder(10.0, 10.0),
          )),
      )

      val roomWithZeroChildren = aValidRoomEntry
        .withCommissionHolder(aValidCommissionHolderWithProtobuf)
        .build
        .copy(occEntry = RoomOccupancy(2, 0)) // 2 adults, 0 children

      val reqOccWithFreeOcc = aValidReqOcc.withAdults(0)

      val result = calcWithPriceBreakdownCalculatorMock.calculateSurchargeForPriceCalculationRefactor(
        paymentModel = PaymentModel.Merchant,
        dailyPrice = aValidDailyPrice.withTaxes(Map((1, 11111) -> 15d)),
        surchargeRateType = RateType.NetExclusive,
        surchargeEntry = surchargeEntry,
        roomPrices = List(roomPrice),
        taxInfo = aValidTaxInfo.withTaxes(Map((1, 11111) -> aValidTaxWithTaxPrototypeLevel.withTaxValue(15.0))),
        room = roomWithZeroChildren,
        reqOcc = reqOccWithFreeOcc,
        isPull = false,
        supplierId = aValidSupplierId,
        supplierContractedCommission = Some(10.0),
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
        fixMarriottSurchargeExp = false,
      )(contextWithRegulation, PropertyContextImpl(1, 1, 1))

      // Should return None when quantity is 0
      result shouldEqual None
    }

    "Return processingFee zero for surcharge excluded when addFeesInExclusivePrice = true" in {
      val surchargeEntry = aValidSurchargeEntry.copy(
        isAmount = true,
        value = 20.0,
        option = ChargeOption.Excluded,
        isCommissionable = true,
        applyTo = "PB",
      )

      val context = aValidYplContext.build
      val contextWithRegulation = context.copy(request = context.request.copy(
        regulationFeatureEnabledSetting =
          YplRegulationFeatureEnabledSetting.default.copy(isShowExclusivePriceWithFeeEnabled = true),
        isXmlPartner = true,
      ))

      val price = aValidPrice.withValue(20.0)
      val roomPrice = aValidPrice
        .withValue(20.0)
        .withChargeType(ChargeType.Room)
        .withDailyTaxes(aValidDailyTaxWithTaxPrototypeLevelWithValue)

      val aValidCommissionHolderWithProtobuf = aValidCommissionHolder.copy(
        daily = Map(
          price.date -> aValidCommissionDailyHolder.copy(
            protobufCommissionHolder = ProtobufCommissionHolder(10.0, 10.0),
          )),
      )

      val roomWithZeroAdults = aValidRoomEntry
        .withCommissionHolder(aValidCommissionHolderWithProtobuf)
        .build
        .copy(occEntry = RoomOccupancy(1, 0)) // 1 adult, 0 children

      val reqOccWithFreeOcc = aValidReqOcc.withAdults(1)

      val result = calcWithPriceBreakdownCalculatorMock.calculateSurchargeForPriceCalculationRefactor(
        paymentModel = PaymentModel.Merchant,
        dailyPrice = aValidDailyPrice.withTaxes(Map((1, 11111) -> 15d)),
        surchargeRateType = RateType.NetExclusive,
        surchargeEntry = surchargeEntry,
        roomPrices = List(roomPrice),
        taxInfo = aValidTaxInfo.withTaxes(Map((1, 11111) -> aValidTaxWithTaxPrototypeLevel.withTaxValue(15.0))),
        room = roomWithZeroAdults,
        reqOcc = reqOccWithFreeOcc,
        isPull = false,
        supplierId = aValidSupplierId,
        supplierContractedCommission = Some(10.0),
        hotelId = aValidHotelId,
        chainId = aValidChainId,
        countryId = aValidCountryId,
        cityId = aValidCityId,
        fixMarriottSurchargeExp = false,
      )(contextWithRegulation, PropertyContextImpl(1, 1, 1))

      result.map(_.processingFee).getOrElse(0.0) shouldEqual 0.0
      result.map(_.refProcessingFee).getOrElse(0.0) shouldEqual 0.0
    }
  }
}
