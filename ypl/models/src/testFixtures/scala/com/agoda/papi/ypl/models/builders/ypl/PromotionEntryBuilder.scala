package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.room.DiscountType
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.{CustomerSegment, PromotionEntry}
import org.joda.time.DateTime

case class PromotionEntryBuilder(build: PromotionEntry) extends Builder[PromotionEntry] {
  def withId(id: Int): B = build.copy(id = id)
  def withTypeId(typeId: Int): B = build.copy(typeId = typeId)
  def withDiscountType(discountType: DiscountType): B = build.copy(discountType = discountType)
  def withMinRooms(mr: Int): B = build.copy(minRooms = mr)
  def withChannelDiscount(allowCD: Boolean): B = build.copy(isAllowChannelDiscount = allowCD)
  def withBookingFrom(bookingFrom: String): B = build.copy(bookFrom = Some(parseDateTime(bookingFrom)))
  def withBookingTo(bookingTo: String): B = build.copy(bookTo = Some(parseDateTime(bookingTo)))
  def withDiscounts(discounts: List[Double]): B = build.copy(discounts = discounts)
  def withBookOn(bookOn: String): B = build.copy(bookOn = bookOn)
  def withMinAdvance(minAdv: Option[Int]): B = build.copy(minAdvPurchase = minAdv)
  def withMaxAdvance(maxAdv: Option[Int]): B = build.copy(maxAdvPurchase = maxAdv)
  def withCustomerSegments(cus: List[CustomerSegment]): B = build.copy(customerSegments = cus)
  def withApplyDates(applyDate: Map[DateTime, Int]): B = build.copy(applyDates = applyDate)
  def withCancellation(cxl: String): B = build.copy(cancellationCode = cxl)

  type B = PromotionEntryBuilder
}
