package com.agoda.papi.ypl.models.builders.propoffer

import com.agoda.papi.constants.RatePlan
import com.agoda.papi.enums.room.PricingChildRateType
import com.agoda.papi.ypl.models.Wholesale.WholesaleMetaByYplChannelSupplierID
import com.agoda.papi.ypl.models.{WholesaleMetadata, YplChannel}
import com.agoda.papi.ypl.models.builders.BaseDataExample
import com.agoda.supply.calc.proto
import com.agoda.supply.calc.proto.ChildAgeRangeRateDaily.ChildAgeRangeRate
import com.agoda.supply.calc.proto.HourlyAvailability.HourlyAvailableSlot
import com.agoda.supply.calc.proto.StackedChannelInfo.StackedChannelDiscount
import com.agoda.supply.calc.proto.TaxPrototypeLevels.TaxPrototypeLevel
import com.agoda.supply.calc.proto.{HotelOccupancySetup, _}

private[builders] trait PropOfferDataExample extends BaseDataExample {

  lazy val aValidOfferCustomerSegment = CustomerSegment.defaultInstance.withLanguageId(1).withCountryCode("TH")

  lazy val aValidOfferBookingRestriction = BookingRestriction.defaultInstance
    .withCustomerSegments(Seq(aValidOfferCustomerSegment))
    .withBookDateFrom(SimpleDate(1970, 1, 1))
    .withBookDateTo(SimpleDate(2099, 1, 1))
    .withBookOn("1111111")
    .withMinAdvancePurchase(10)
    .withMaxAdvancePurchase(15)

  lazy val aValidPropOfferDiscount = Discount(Seq(NumericRange(0, 1)), 20)
  lazy val aValidPropOfferPromotion = Promotion.defaultInstance
    .withCancellationCode("24D100P_100P")
    .withDiscounts(Seq(20, 20))
    .withApplyDates(Seq(aValidPropOfferDiscount))
    .withIsStackCombine(true)
    .withIsStackable(false)
    .withIsStackCombine(true)
    .withIsApplyChannelDiscount(true)

  lazy val aValidStructureBenefitParameter = StructureBenefitParameter.defaultInstance.withPosition(1).withValue("1")
  lazy val aValidPropOfferBenefit = Benefit.defaultInstance.withBenefitId(1).withBenefitType(0)
  lazy val aValidPropOfferStructureBenefit =
    Benefit.defaultInstance.withBenefitId(99).withBenefitType(1).withParameters(Seq(aValidStructureBenefitParameter))

  lazy val aValidPropOfferSurcharge = Surcharge(surchargeId = 1,
                                                applyTo = "PRPB",
                                                applyType = proto.ApplyType.Mandatory,
                                                isAmount = false,
                                                value = 10.0,
                                                isCommissionable = true)

  lazy val aValidPropOfferTax = Tax(taxId = 1,
                                    applyTo = "PB",
                                    applyType = ApplyType.Mandatory,
                                    isFee = true,
                                    isAmount = true,
                                    value = 10.0,
                                    isTaxable = true,
                                    taxPrototypeId = 1)

  lazy val aValidPropOfferTaxV2 = TaxV2(taxId = 1,
                                        applyTo = "PB",
                                        applyType = ApplyType.Mandatory,
                                        isFee = true,
                                        isAmount = true,
                                        value = 10.0,
                                        isTaxable = true,
                                        taxPrototypeId = 1)

  lazy val aValidPropOfferCancelPolicy = CancelCode(aValidCxlCode)

  lazy val aValidPropOfferCustomerSegment = CustomerSegment(1, "TH")

  lazy val aValidPropOfferStackedChannelInfo =
    StackedChannelInfo(baseChannelId = 1, stackedDiscounts = Seq(StackedChannelDiscount(1, 10d)))

  lazy val aValidPropOfferBookingRestriction =
    BookingRestriction("2020-09-09", 0, 1, None, None, None, None, 1, 1, Seq(aValidPropOfferCustomerSegment))

  lazy val aValidPropOfferTaxPrototypeLevel = TaxPrototypeLevel(1, 2.4, 2.7, 32.40)

  lazy val aValidPropOfferTaxPrototypeLevelV2 = TaxPrototypeLevelsV2.TaxPrototypeLevel(1, 2.4, 2.7, 32.40)

  lazy val aValidPropOfferTaxPrototypeLevels = Seq(aValidPropOfferTaxPrototypeLevel)

  lazy val aValidPropOfferTaxPrototypeLevelsV2 = Seq(aValidPropOfferTaxPrototypeLevelV2)

  lazy val aValidPropOfferTaxProtoType = TaxPrototypeLevels(2, aValidPropOfferTaxPrototypeLevels)

  lazy val aValidPropOfferTaxProtoTypeV2 = TaxPrototypeLevelsV2(4, aValidPropOfferTaxPrototypeLevelsV2)

  lazy val aValidPropOfferRoomTypeInfo = RoomTypeInfo(maxOccupancy = 2,
                                                      maxChildren = 1,
                                                      maxExtraBed = 1,
                                                      isDayUse = false,
                                                      cutOffDays = 0,
                                                      gaCutOffDays = 2,
                                                      supplierRoomCode = "ABC")

  lazy val aValidPropOfferHrRoomTypeInfo = RoomTypeInfo(maxOccupancy = 2,
                                                        maxChildren = 1,
                                                        maxExtraBed = 1,
                                                        isDayUse = false,
                                                        cutOffDays = 0,
                                                        gaCutOffDays = 2,
                                                        supplierRoomCode = "DEF")

  lazy val aValidPropOfferApmOccupancyPrice =
    ApmOccupancyPrice(occupancies = Seq(NumericRange(1, 1)), sellInclusiveAmount = 90d)

  lazy val aValidPropOfferApmChannelPrice = ApmChannelPrice(apmChannelId = 1051, sellInclusiveAmount = 90d)

  lazy val aValidPropOfferApmPriceDaily = ApmPriceDaily(
    stayDates = Seq(NumericRange(0, 0)),
    apmOccupancyPrices = Seq(aValidPropOfferApmOccupancyPrice),
  )

  lazy val aValidPropOfferApmRateInfo = ApmRateInfo(
    roomTypeId = aValidRoomTypeId,
    apmPrices = Seq(aValidPropOfferApmPriceDaily),
  )

  lazy val aValidApmApprovalPriceKey: ApmApprovalPriceKey =
    ApmApprovalPriceKey(aValidRoomTypeId, Some(SimpleDate(2021, 11, 29)), 1)

  lazy val aValidApmApprovalPriceKeyValuePair: ApmApprovalPriceKeyValuePair = ApmApprovalPriceKeyValuePair(
    Some(aValidApmApprovalPriceKey),
    1,
  )

  lazy val aValidApmApprovalPriceMapEntries = Seq(aValidApmApprovalPriceKeyValuePair)

  lazy val aValidApmApprovalPriceMap: ApmApprovalPriceMap = ApmApprovalPriceMap(
    entries = aValidApmApprovalPriceMapEntries,
  )

  lazy val aValidPropOfferRateRepurposeInfo = RateRepurposingInfo(2, 3.5, 1, 5, 3, 1, 2, 0, 0)

  lazy val aValidPropOfferAgeRange = AgeRange()
  lazy val aValidPropOfferPerPersonPrice = PerPersonPrice()
  lazy val aValidPropOfferHotelOccupancy = HotelOccupancy()
  lazy val aValidPropOfferHotelOccupancySetup = HotelOccupancySetup()

  lazy val aValidPropertyOffer = PropertyOffer.defaultInstance
    .withHotelId(aValidHotelId)
    .withMasterHotelId(aValidHotelId) // HotelId is deprecated
    .withOccupancyModel(proto.OccupancyModel.Full)
    .withSupplyInfo(aValidPropOfferSupplyInfo)
    .withPaymentMode(PaymentMode.Agency)
    .withCheckInDate(SimpleDate(2020, 9, 12))
    .withLengthOfStay(1)
    .withSurchargeRateLoadType(PriceType.SellExc)
    .withRateRepurpose(Seq(aValidPropOfferRateRepurposeInfo))
    .withTaxes(Map(1 -> aValidPropOfferTax))
    .withCancelPolicies(Map(1 -> aValidPropOfferCancelPolicy))
    .withRoomRates(Seq(aValidPropOfferRoomRateCategory))
    .withRoomTypes(Map(24453L -> aValidPropOfferRoomTypeInfo))
    .withApmRateApprovalPriceIdsHash("hash")

  lazy val aValidPropertyOfferWithTaxV2 = aValidPropertyOffer
    .withRoomRates(Seq(aValidPropOfferRoomRateCategoryWithTaxV2))
    .withTaxesV2(Map(1 -> aValidPropOfferTaxV2))

  lazy val aValidPropOfferSupplyInfo = SupplierInfo(3038, "76282_362", 4.5)

  lazy val aValidPropOfferJTBSupplyInfo = SupplierInfo(29014, "76282_362", 4.5)
  lazy val aValidPropOfferJTBUatSupplyInfo = SupplierInfo(99901, "76282_362", 4.5)

  lazy val aValidPropOfferOccPrice = OccupancyPrice(
    occupancies = Seq(NumericRange(1, 1)),
    amount = 10.25,
    commissions = None,
    surcharges = None,
  )

  lazy val aValidPropOfferPriceDaily = PriceDaily(
    stayDates = Seq(NumericRange(0, 1)),
    occupancyPrices = Seq(
      aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(10.25),
      aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(2, 2))).withAmount(12.2),
    ),
  )

  lazy val aValidChildAgeRangeRate =
    ChildAgeRangeRate(ageRangeId = 1, price = 5.0, pricingTypeId = Some(PricingChildRateType.FlatPrice.value))

  lazy val aValidChildAgeRangeRateDaily = ChildAgeRangeRateDaily(
    stayDates = Seq(NumericRange(0, 1)),
    childAgeRangeRates = Seq(aValidChildAgeRangeRate),
  )

  lazy val aValidPropOferFireDrillInfo =
    FireDrillInfo(contractId = 1, commission = 10d, activateDate = Some(SimpleDate(1970, 1, 1)))
  lazy val aValidPropOfferCancellationPolicy = CancelationPolicy(
    cancelCodeIdentifier = 2,
    cancelPolicyByOccupancy = Seq.empty,
    cancelChargeType = CancelChargeType.PerBookCharge,
  )
  lazy val aValidPropOfferChannelRate = ChannelRate(
    channelId = 1,
    roomUid = "",
    supplierRateCode = "8132102_93766198_2_1_1",
    remainingRooms = 10,
    remainingRoomsGA = 5,
    remainingRoomRA = 5,
    taxPerDay = Map(0 -> Identifiers(Seq(1, 2))),
    surchargePerDay = Map(0 -> Identifiers(Seq(1, 2))),
    surchargeMarkup = 23.34,
    commissionPerDay = Map(0 -> Identifiers(Seq(1, 2))),
    channelDiscountPerDay = Map(0 -> 2.5, 1 -> 3.6),
    promotionIdentifiers = Seq(1, 2),
    isNationalityRate = true,
    confirmByMinute = -1,
    cancelationPolicy = Some(aValidPropOfferCancellationPolicy),
    paymentGuarantee = None,
    bookingRestrictionIdentifier = 0,
    externalData = Seq.empty,
    rateLoadType = PriceType.SellExc,
    processingFee = 0,
    prices = Seq(aValidPropOfferPriceDaily),
  )

  lazy val aValidYplWholesaleMetadata: WholesaleMetadata = WholesaleMetadata(Some(6.0))
  lazy val aValidWholesaleMetaByYplChannel: WholesaleMetaByYplChannelSupplierID =
    Map((new YplChannel(1, Set.empty, 0), 332) -> aValidYplWholesaleMetadata)

  lazy val aValidPropOfferChannelRateWithRateLoadTypeNetIn =
    aValidPropOfferChannelRate.copy(rateLoadType = PriceType.NetInc)

  lazy val aValidHourlyPropOfferChannelRate = aValidPropOfferChannelRate.copy(
    hourlyAvailability =
      Some(HourlyAvailability(availability = Seq(HourlyAvailableSlot(Some(TimeOfDay(10, 0, 0)), 3)))),
    supplierRateCode = "8132103_93766198_2_1_1",
  )

  lazy val aValidPropOfferChannelRateWithTaxV2 = aValidPropOfferChannelRate.copy(
    taxPerDayV2 = Map(-1 -> Identifiers(Seq(1, 2))),
    surchargePerDay = Map(0 -> Identifiers(Seq(1, 2))),
  )

  lazy val aValidPropOfferRoomRateCategory = RoomRateCategory(
    roomTypeId = 24453,
    rateCategoryId = 10,
    parentRateCategoryId = -1,
    benefits = Seq(1, 2),
    offerTypeId = 1,
    promotionTypeId = 3,
    promotionTypeCmsId = 4,
    isAgencyEnabled = true,
    currencyCode = "JPY",
    channelRates = Seq(aValidPropOfferChannelRate),
  )

  lazy val aValidHourlyPropOfferRoomRateCategory =
    aValidPropOfferRoomRateCategory.copy(channelRates = Seq(aValidHourlyPropOfferChannelRate), rateCategoryId = 11)
  lazy val aValidHourlyPropOfferRoomRateCategoryWithMultipleChannel = aValidPropOfferRoomRateCategory.copy(
    channelRates = Seq(aValidHourlyPropOfferChannelRate,
                       aValidHourlyPropOfferChannelRate.copy(channelId = 2,
                                                             hourlyAvailability = None,
                                                             supplierRateCode = "8132105_93766198_2_1_1")),
    rateCategoryId = 11,
  )

  lazy val aValidPropOfferRoomRateCategoryWithPartialTaxPerDayV2 = aValidPropOfferRoomRateCategory.withChannelRates(
    Seq(aValidPropOfferChannelRateWithTaxV2.copy(taxPerDayV2 = Map(-1 -> Identifiers(Seq(1, 2, 3)))),
        aValidPropOfferChannelRate))
  lazy val aValidPropOfferRoomRateCategoryWithTaxV2 =
    aValidPropOfferRoomRateCategory.withChannelRates(Seq(aValidPropOfferChannelRateWithTaxV2))

  lazy val aValidPropOfferHrRoomRateCategory = RoomRateCategory(
    roomTypeId = 24454,
    rateCategoryId = 10,
    parentRateCategoryId = -1,
    benefits = Seq(1, 2),
    offerTypeId = 1,
    promotionTypeId = 3,
    promotionTypeCmsId = 4,
    isAgencyEnabled = true,
    currencyCode = "JPY",
    channelRates = Seq(aValidPropOfferChannelRate),
  )

  lazy val aValidPropOfferCommission = Commission(0, 0, 10, 34.3, 2.5)

  lazy val aValidPropOfferPaymentGuarantee = PaymentGuarantee(false, false, false)

  lazy val aValidHourlyAvailability =
    HourlyAvailability(None, Seq(HourlyAvailableSlot(from = Some(TimeOfDay(hours = 10)), durationInMinute = 180)))

  def createMockPropOfferHotels(roomNum: Int, losNum: Int = 1, rcNum: Int = 1, surcharegNum: Int, promotionNum: Int) = {
    val baseRcLen = 8
    val baseRcPow = 4
    val baseRcId = (math.pow(10, baseRcLen) + math.pow(10, baseRcPow)).toInt
    val rcIds = (1 to rcNum).map(_ + baseRcId)

    val mockNonRetailChannels = Set(
      RatePlan.NET,
      RatePlan.Domestic,
      RatePlan.Mobile,
      RatePlan.APO,
      RatePlan.China,
      RatePlan.Prius,
      RatePlan.VIP,
      RatePlan.RestRetail,
      RatePlan.RestMerchant,
      RatePlan.RestMerchant,
      RatePlan.RestMerchant,
      RatePlan.Package,
      RatePlan.PrefOpaque,
      RatePlan.RestPrivate,
      RatePlan.RestOpaque,
      RatePlan.RestMobile,
      RatePlan.RestPackage,
    )
    val channelIds = Set(RatePlan.RTL, RatePlan.APS) // ++ shuffle(mockNonRetailChannels).take(channelNum)

    val commissions = channelIds
      .map(channelId =>
        channelId -> aValidPropOfferCommission.withChannelId(channelId).withValue(math.max(channelId * 5d, 99d)))
      .toMap

    val roomTypeIds = 1 to roomNum
    val surchargeIds = 1 to surcharegNum
    val promotionIds = 1 to promotionNum
    val losIds = 1 to losNum

    val surcharges: Map[Int, Surcharge] = surchargeIds.map { surchargeId =>
      surchargeId -> aValidPropOfferSurcharge
        .withSurchargeId(surchargeId)
        .withValue(math.max(surchargeId * 10d, 99d))
        .withIsAmount(surchargeId % 3 == 0)
    }.toMap

    val mockRestriction = aValidPropOfferBookingRestriction
      .withBookDateFrom(SimpleDate(1900, 6, 8))
      .withBookDateTo(SimpleDate(9999, 6, 18))
      .withBookOn("1111111")
      .clearCustomerSegments
      .withMinAdvancePurchase(-1)
      .withMaxAdvancePurchase(999)

    val promotions = promotionIds
      .map(promotionId =>
        promotionId -> aValidPropOfferPromotion
          .withTypeId(promotionId)
          .withDiscounts(Seq(math.max(promotionId * 5d, 99d), 0d, 0d, 0d, 0d, 0d, 0d))
          .withBookingRestrictionIdentifier(1)
          .withDiscountTypeId(promotionId % 2 + 1)
          .withApplyDates(Seq(Discount(Seq(NumericRange(0, 0)), math.max(promotionId * 5d, 99d)))))
      .toMap
    val mockRoomRates: Seq[RoomRateCategory] = roomTypeIds.flatMap { roomId =>
      channelIds.flatMap { chId =>
        rcIds.map { rcId =>
          val mockChannelRates = aValidPropOfferChannelRate
            .withChannelId(chId)
            .withSurchargePerDay(Map(-1 -> Identifiers(surcharges.keySet.toSeq)))
            .withCommissionPerDay(Map(-1 -> Identifiers(commissions.keySet.toSeq)))
            .withPromotionIdentifiers(promotions.keySet.toSeq)
            .withPrices(
              Seq(
                aValidPropOfferPriceDaily
                  .withStayDates(Seq(NumericRange(0, losNum - 1)))
                  .withOccupancyPrices(Seq(
                    aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(1, 1))).withAmount(3000),
                    aValidPropOfferOccPrice.withOccupancies(Seq(NumericRange(2, 2))).withAmount(5000),
                  ))),
            )

          aValidPropOfferRoomRateCategory
            .withRateCategoryId(rcId)
            .withChannelRates(Seq(mockChannelRates))
            .withRoomTypeId(roomId)
        }
      }
    }

    val mockRoomTypes = roomTypeIds.map(roomId => roomId.toLong -> aValidPropOfferHrRoomTypeInfo).toMap
    aValidPropertyOffer
      .withSupplyInfo(aValidPropOfferSupplyInfo.withSupplierId(332))
      .withRoomRates(mockRoomRates)
      .withRoomTypes(mockRoomTypes)
      .withSurcharges(surcharges)
      .withCommissions(commissions)
      .withPromotions(promotions)
      .withBookingRestrictions(Map(1 -> mockRestriction))
  }
}
