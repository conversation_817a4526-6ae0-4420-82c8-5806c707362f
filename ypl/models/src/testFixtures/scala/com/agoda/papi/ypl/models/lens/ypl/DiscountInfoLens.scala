package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.ypl.models.hotel.{DiscountInfo, Promotion}
import monocle.{Lens, Optional}
import org.joda.time.LocalDate

object DiscountInfoLens {
  type A = DiscountInfo

  val promotionLens = Optional[A, Promotion](_.promotion)(promo => discount => discount.copy(promotion = Some(promo)))
  val promoBreakdownLens = Lens[A, Map[LocalDate, List[Promotion]]](_.promotionsBreakdown)(breakdown =>
    discount => discount.copy(promotionsBreakdown = breakdown))
  def promoBreakdownLens(date: LocalDate) = Optional[A, List[Promotion]](_.promotionsBreakdown.get(date))(promos =>
    discount => discount.copy(promotionsBreakdown = discount.promotionsBreakdown + (date -> promos)))

}
