package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.Promotion.Restriction.CustomerSegment

case class OTAPromotionCustomerSegmentBuilder(build: CustomerSegment) extends Builder[CustomerSegment] {
  def withLanguageId(languageId: Option[Int]): B = build.copy(languageId = languageId)
  def withCountryCode(countryCode: Option[String]): B = build.copy(countryCode = countryCode)

  type B = OTAPromotionCustomerSegmentBuilder
}
