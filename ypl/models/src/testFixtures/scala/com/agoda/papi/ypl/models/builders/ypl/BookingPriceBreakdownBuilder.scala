package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.PromotionEntry
import com.agoda.papi.ypl.models.pricing.{BookingPriceBreakdown, PriceBreakdown}

case class BookingPriceBreakdownBuilder(build: BookingPriceBreakdown) extends Builder[BookingPriceBreakdown] {
  def withEnabled(): B = build.copy(isEnabled = true)

  def withAppendStep(breakdown: PriceBreakdown): B = build.copy(breakdown = build.breakdown ++ Seq(breakdown))

  def withAppendPromotion(current: Option[PromotionEntry],
                          newPromo: PromotionEntry,
                          rawValue: Double,
                          calculatedValue: Double,
                          baseValue: Double): B =
    build.addPromotionV2(current, newPromo, rawValue, calculatedValue, baseValue)

  type B = BookingPriceBreakdownBuilder
}
