package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.hotel.{Benefit, RateCategory}

case class RateCategoryBuilder(build: RateCategory) extends Builder[RateCategory] {
  def withId(id: Int): B = build.copy(id = id)
  def withIsAmount(isAmount: Boolean): B = build.copy(isAmount = isAmount)
  def withParentID(parentID: Int): B = build.copy(parentId = if (parentID == -1) None else Some(parentID))
  def withValue(value: Double): B = build.copy(value = value)
  def withPromotionTypeId(id: Int): B = build.copy(promotionTypeId = Option(id))
  def withPromotionTypeCmsId(cmsId: Int): B = build.copy(promotionTypeCmsId = Option(cmsId))
  def withBenefits(benefits: List[Benefit]): B = build.copy(benefits = benefits)

  type B = RateCategoryBuilder
}
