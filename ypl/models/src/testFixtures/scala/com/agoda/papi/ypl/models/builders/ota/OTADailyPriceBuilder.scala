package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice.{
  AdditionalOccupancyCharge,
  OccupancyPrice,
  Commission,
}

case class OTADailyPriceBuilder(build: DailyPrice) extends Builder[DailyPrice] {
  def withDateMilli(millis: Long): B = build.copy(dateMillis = millis)
  def withPrices(prices: Seq[OccupancyPrice]): B = build.copy(prices = prices)
  def withCommissions(commisions: Seq[Commission]): B = build.copy(commissions = commisions)
  def withAdditionalCharges(additionalCharges: Seq[AdditionalOccupancyCharge]): B =
    build.copy(additionalCharges = additionalCharges)
  def withChannelDiscount(channelDiscount: Option[Double]): B = build.copy(channelDiscount = channelDiscount)

  type B = OTADailyPriceBuilder
}
