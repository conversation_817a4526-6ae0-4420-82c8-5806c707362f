package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.YplReqOccByHotelAgePolicy
import com.agoda.papi.ypl.models.api.request.{YplChildren, YplOccInfo, YplRoomAssignment}
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.hotel.AgePolicy
import com.agoda.papi.ypl.occupancy.models.YcsChildAgeRange

case class ReqOccBuilder(build: YplReqOccByHotelAgePolicy) extends Builder[YplReqOccByHotelAgePolicy] {
  def withAgePolicy(agePolicy: AgePolicy): B = build.copy(agePolicy = agePolicy)
  def withOccupancy(occ: YplOccInfo): B = build.copy(occ = occ)

  // OccInfo attributes, shortcut on reqOcc
  def withAdults(adults: Int): B = build.copy(occ = build.occ.copy(_adults = Option(adults)))
  def withChildrenAges(ages: List[Int]): B =
    build.copy(occ = build.occ.copy(_children = Option(YplChildren(ages.map(Option(_))))))
  def withRooms(rooms: Int): B = build.copy(occ = build.occ.copy(_rooms = Option(rooms)))
  def withRoomAssignments(roomAssignments: List[YplRoomAssignment]): B =
    build.copy(occ = build.occ.copy(roomAssignment = roomAssignments))

  // Hotel Child Age Range
  def withChildAgeRange(childAgeRange: List[YcsChildAgeRange]): B = build.copy(childAgeRange = childAgeRange)
  def withIsUseChildAgeRange(isUseChildAgeRange: Boolean): B = build.copy(isUseChildAgeRange = isUseChildAgeRange)

  type B = ReqOccBuilder
}
