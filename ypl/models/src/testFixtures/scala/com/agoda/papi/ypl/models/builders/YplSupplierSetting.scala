package com.agoda.papi.ypl.models.builders

import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{SupplierId, YplSupplierSetting}

// fetch from PROD
object YplSupplierSetting {
  private val defaultYplSupplierSetting = new YplSupplierSetting(
    isOccFreePullSupplier = false,
    isChildStayFree = false,
    isThirdParty = false,
    isOccModelFPLOS = false,
    isAgodaBrand = true,
  )

  val mapping: Map[SupplierId, YplSupplierSetting] = Map(
    DMC.YCS -> defaultYplSupplierSetting.copy(isThirdParty = false),
    DMC.JTBWL -> defaultYplSupplierSetting.copy(isThirdParty = true),
    DMC.BCOM -> defaultYplSupplierSetting.copy(isThirdParty = true),
    DMC.Huizhi -> defaultYplSupplierSetting.copy(isThirdParty = true),
  )
}
