package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.room.ChildRateType
import com.agoda.papi.ypl.models.Quantity
import com.agoda.papi.ypl.models.api.request.YplChildren
import com.agoda.papi.ypl.models.builders.Builder

case class ChildrenBuilder(build: YplChildren) extends Builder[YplChildren] {
  def withChildren(children: List[Int]): B = build.copy(ages = children.map(age => age.toOption(true)))
  def withChildRate(childRate: Map[ChildRateType, Quantity]): B = build.copy(childrenTypes = childRate)
  type B = ChildrenBuilder
}
