package com.agoda.papi.ypl.models.builders.implicits

import com.agoda.finance.tax.models.{AppliedTaxPrototypeLevel, TaxPrototypeInfo, TaxPrototypeLevel}
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes, Tax, TaxWithValue}
import com.agoda.papi.ypl.commission.builder.implicits.CommissionImplicitTestDataBuilder
import com.agoda.papi.ypl.models.api.request._
import com.agoda.papi.ypl.models.builders.ypl._
import com.agoda.papi.ypl.models.hotel._
import com.agoda.papi.ypl.models.occupancy.{MaxAllowedFreeChildAgeRange, OccupancyUnit}
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.pricing.{BookingPriceBreakdown, RoomOccupancy, YplPrice}
import com.agoda.papi.ypl.models.{
  DmcDataHolder,
  EasyCancel,
  FireDrillProto,
  HotelMeta,
  RateSilo,
  SupplierInfo,
  YPLHotel,
  YPLMetaRateSilo,
  YPLRateReutilizationEntry,
  YPLRoom,
  YplChannel,
  YplDispatchChannels,
  YplHotelEntryModel,
  YplReqOccByHotelAgePolicy,
  YplRequest,
  YplRoomEntry,
  YplSupplierSetting,
}
import com.agoda.papi.ypl.occupancy.builder.implicites.OccupancyImplicitTestDataBuilder

//scalastyle:off
private[implicits] trait YPLImplicitTestDataBuilder
  extends CommissionImplicitTestDataBuilder
    with OccupancyImplicitTestDataBuilder {
  implicit def toHotelBuilder(build: YPLHotel): HotelBuilder = HotelBuilder(build)

  implicit def toHotelMetaBuilder(build: HotelMeta): HotelMetaBuilder = HotelMetaBuilder(build)

  implicit def toSupplierHotelBuilder(build: SupplierHotel): SupplierHotelBuilder = SupplierHotelBuilder(build)

  implicit def toRoomBuilder(build: YPLRoom): RoomBuilder = RoomBuilder(build)

  implicit def toPriceBuilder(build: YplPrice): PriceBuilder = PriceBuilder(build)

  implicit def toTaxBreakdownBuilder(build: CommonTaxBreakdown): TaxBreakdownBuilder = TaxBreakdownBuilder(build)

  implicit def toTaxPrototypeInfoBuilder(build: TaxPrototypeInfo): TaxPrototypeInfoBuilder =
    TaxPrototypeInfoBuilder(build)

  implicit def toAppliedTaxPrototypeLevelBuilder(build: AppliedTaxPrototypeLevel): AppliedTaxPrototypeLevelBuilder =
    AppliedTaxPrototypeLevelBuilder(build)

  implicit def toTaxPrototypeLevelBuilder(build: TaxPrototypeLevel): TaxPrototypeLevelBuilder =
    TaxPrototypeLevelBuilder(build)

  implicit def toYplRequestBuilder(build: YplRequest): YplRequestBuilder = YplRequestBuilder(build)

  implicit def toRoomEntryBuilder(build: YplRoomEntry): RoomEntryBuilder = RoomEntryBuilder(build)

  implicit def toReqOccBuilder(build: YplReqOccByHotelAgePolicy): ReqOccBuilder = ReqOccBuilder(build)

  implicit def toPromotionBuilder(build: Promotion): PromotionBuilder = PromotionBuilder(build)

  implicit def toPromotionEntryBuilder(build: PromotionEntry): PromotionEntryBuilder = PromotionEntryBuilder(build)

  implicit def toRateCategoryBuilder(build: RateCategory): RateCategoryBuilder = RateCategoryBuilder(build)

  implicit def toRoomEntryTypeBuilder(build: RoomTypeEntry): RoomTypeEntryBuilder = RoomTypeEntryBuilder(build)

  implicit def toYcsRoomCapacityBuilder(build: YcsRoomCapacity): YcsRoomCapacityBuilder = YcsRoomCapacityBuilder(build)

  implicit def toRoomOccupancyBuilder(build: RoomOccupancy): RoomOccupancyBuilder = RoomOccupancyBuilder(build)

  implicit def toPriceEntryBuilder(build: PriceEntry): PriceEntryBuilder = PriceEntryBuilder(build)

  implicit def toDailyPriceBuilder(build: DailyPrice): DailyPriceBuilder = DailyPriceBuilder(build)

  implicit def toYplHotelEntryModelBuilder(build: YplHotelEntryModel): YplHotelEntryModelBuilder =
    YplHotelEntryModelBuilder(build)

  implicit def toYplRateReutilizationEntryBuilder(build: YPLRateReutilizationEntry): YPLRateReutilizationEntryBuilder =
    YPLRateReutilizationEntryBuilder(build)

  implicit def toAgePolicyBuilder(build: AgePolicy): AgePolicyBuilder = AgePolicyBuilder(build)

  implicit def toOccInfoBuilder(build: YplOccInfo): OccInfoBuilder = OccInfoBuilder(build)

  implicit def toChildPriceEntryBuilder(build: ChildPriceEntry): ChildPriceEntryBuilder = ChildPriceEntryBuilder(build)

  implicit def toChildrenBuilder(build: YplChildren): ChildrenBuilder = ChildrenBuilder(build)

  implicit def toDiscountInfoBuilder(build: DiscountInfo): DiscountInfoBuilder = DiscountInfoBuilder(build)

  implicit def torateSiloBuilder(build: RateSilo): RateSiloBuilder = RateSiloBuilder(build)

  implicit def toYplFlagInfoBuilder(build: YplFlagInfo): YplFlagInfoBuilder = YplFlagInfoBuilder(build)

  implicit def toYplFeatureRequestBuilder(build: YplFeatureRequest): YplFeatureRequestBuilder =
    YplFeatureRequestBuilder(build)

  implicit def toMetaSiloBuilder(build: YPLMetaRateSilo): MetaSiloBuilder = MetaSiloBuilder(build)

  implicit def toYplDmcDataHolderBuilder(build: DmcDataHolder): YplDmcDataHolderBuilder = YplDmcDataHolderBuilder(build)

  implicit def toYplSupplierInfoBuilder(build: SupplierInfo): YplSupplierInfoBuilder = YplSupplierInfoBuilder(build)

  implicit def toBenefitBuilder(build: Benefit): BenefitBuilder = BenefitBuilder(build)

  implicit def toProtoTaxBuilder(build: Tax): TaxBuilder = TaxBuilder(build)

  implicit def toTaxInfoBuilder(build: TaxInfo): TaxInfoBuilder = TaxInfoBuilder(build)

  implicit def toHotelTaxInfoBuilder(build: HotelTaxInfo): HotelTaxInfoBuilder = HotelTaxInfoBuilder(build)

  implicit def toRateCategoryEntryBuilder(build: RateCategoryEntry): RateCategoryEntryBuilder =
    RateCategoryEntryBuilder(build)

  implicit def toYplSupplierSettingBuilder(build: YplSupplierSetting): YplSupplierSettingBuilder =
    YplSupplierSettingBuilder(build)

  implicit def toYPLClientInfoBuilder(build: YplClientInfo) = YPLClientInfoBuilder(build)

  implicit def toSurchargeEntryBuilder(build: SurchargeEntry): SurchargeEntryBuilder = SurchargeEntryBuilder(build)

  implicit def toAgodaAgencyFeaturesBuilder(build: AgodaAgencyFeatures): AgodaAgencyFeaturesBuilder =
    AgodaAgencyFeaturesBuilder(build)

  implicit def toEasyCancelBuilder(build: EasyCancel): EasyCancelBuilder = EasyCancelBuilder(build)

  implicit def toYplDispatchChannelsBuilder(build: YplDispatchChannels): YplDispatchChannelsBuilder =
    YplDispatchChannelsBuilder(build)

  implicit def toCancellationCodePerOccBuilder(build: CancellationCodePerOcc): CancellationCodePerOccBuilder =
    CancellationCodePerOccBuilder(build)

  implicit def toBookingPriceBreakdown(build: BookingPriceBreakdown): BookingPriceBreakdownBuilder =
    BookingPriceBreakdownBuilder(build)

  implicit def toEnabledRoom(build: YplEnabledRoom): EnabledRoomBuilder = EnabledRoomBuilder(build)

  implicit def toRoomLinkage(build: YplRoomLinkage): RoomLinkageBuilder = RoomLinkageBuilder(build)

  implicit def toYplChannelBuilder(build: YplChannel): YplChannelBuilder = YplChannelBuilder(build)

  implicit def toStackedChannelDiscountBuilder(build: StackedChannelDiscount): StackedChannelDiscountBuilder =
    StackedChannelDiscountBuilder(build)

  implicit def toFireDrillProtoBuilder(build: FireDrillProto): FireDrillProtoBuilder = FireDrillProtoBuilder(build)

  implicit def toOccupancyUnitBuilder(build: OccupancyUnit): OccupancyUnitBuilder = OccupancyUnitBuilder(build)

  implicit def toDailyTaxesBuilder(build: DailyTaxes): DailyTaxesBuilder = DailyTaxesBuilder(build)

  implicit def toDailyTaxes(builder: DailyTaxesBuilder): DailyTaxes = builder.build

  implicit def toTaxWithValueBuilder(build: TaxWithValue): TaxWithValueBuilder = TaxWithValueBuilder(build)

  implicit def toTaxWithValue(builder: TaxWithValueBuilder): TaxWithValue = builder.build

  implicit def toMaxAllowedFreeChildAgeRangeBuilder(
    build: MaxAllowedFreeChildAgeRange): MaxAllowedFreeChildAgeRangeBuilder = MaxAllowedFreeChildAgeRangeBuilder(build)
}
