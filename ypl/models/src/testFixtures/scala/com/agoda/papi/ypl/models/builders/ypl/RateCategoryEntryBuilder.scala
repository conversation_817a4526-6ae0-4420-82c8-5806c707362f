package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.{BenefitEntry, CancellationCodePerOcc, ChildRate, RateCategoryEntry}
import com.agoda.supply.calc.proto.{EscapesApprovalStatus, StayPackageType}

case class RateCategoryEntryBuilder(build: RateCategoryEntry) extends Builder[RateCategoryEntry] {
  def withId(id: Int): B = build.copy(rateCategoryId = id)
  def withValue(value: Double): B = build.copy(value = value)
  def withApplyTo(value: String): B = build.copy(applyTo = value)
  def withChildRate(childRate: Seq[ChildRate]): B = build.copy(childRate = childRate)
  def withDmcRatePlanId(id: Option[String]): B = build.copy(dmcRatePlanID = id)
  def withCxlCodePerOcc(cxlCodePerOcc: List[CancellationCodePerOcc]): B = build.copy(cxlCodePerOcc = cxlCodePerOcc)
  def withIsChildRateEnabled(isChildRateEnabled: Boolean): B = build.copy(isChildRateEnabled = isChildRateEnabled)
  def withStayPackageType(stayPackageType: Option[StayPackageType]): B = build.copy(stayPackageType = stayPackageType)
  def withEscapesApprovalStatus(escapesApprovalStatus: Option[EscapesApprovalStatus]): B =
    build.copy(escapesApprovalStatus = escapesApprovalStatus)
  def withIsYcsOrJtbWl(value: Boolean): B = build.copy(isYcsOrJtbWl = value)
  def withBenefits(benefits: List[BenefitEntry]): B = build.copy(benefitList = benefits)
  def withUseJapanBenefitChildRateDaily(value: Boolean): B = build.copy(useJapanBenefitChildRateDaily = value)

  type B = RateCategoryEntryBuilder
}
