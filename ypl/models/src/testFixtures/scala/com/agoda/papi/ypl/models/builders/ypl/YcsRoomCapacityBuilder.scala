package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.YcsRoomCapacity

case class YcsRoomCapacityBuilder(build: YcsRoomCapacity) extends Builder[YcsRoomCapacity] {
  def withMaxGuestOnExistingBed(max: Int): B = build.copy(maxGuestOnExistingBed = max)
  def withMaxAdditionalGuest(max: Int): B = build.copy(maxAdditionalGuest = max)
  def withMaxAdults(max: Int): B = build.copy(maxAdults = max)
  def withMaxChildren(max: Int): B = build.copy(maxChildren = max)
  type B = YcsRoomCapacityBuilder
}
