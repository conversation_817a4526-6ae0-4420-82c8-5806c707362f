package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.RoomType

case class OTARoomTypeBuilder(build: RoomType) extends Builder[RoomType] {
  def withRoomTypeId(id: Long): B = build.copy(roomTypeId = id)
  def withMaxOccupancy(maxOcc: Int): B = build.copy(maxOccupancy = maxOcc)
  def withMaxExtrabed(maxExtrabed: Option[Int]): B = build.copy(maxExtrabed = maxExtrabed)
  def withMaxChildren(maxChildren: Option[Int]): B = build.copy(maxChildren = maxChildren)
  def withSupplierRoomId(supplierRoomId: Option[String]): B = build.copy(supplierRoomId = supplierRoomId)
  def withSupplierRatePlanId(supplierRateplanId: Option[String]): B = build.copy(supplierRatePlanId = supplierRateplanId)

  type B = OTARoomTypeBuilder
}
