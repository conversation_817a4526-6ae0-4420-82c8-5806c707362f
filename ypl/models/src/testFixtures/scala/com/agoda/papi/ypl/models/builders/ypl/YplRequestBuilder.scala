package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.finance.tax.models.{ApplyTaxOnSellExSettings, SupplierHotelCalculationSettings}
import com.agoda.papi.enums.request.{FeatureFlag, FilterCriteria}
import com.agoda.papi.ypl.commission.apm.models.ApmSettingHolder
import com.agoda.papi.ypl.fencing.AgxCommissionFencing
import com.agoda.papi.ypl.models.api.request._
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.{
  BComBMPSetting,
  Currency,
  GUIDGenerator,
  PreFilterZeroAllotment,
  SupplierFeatures,
  WhitelabelSetting,
  YplChannel,
  YplExperiment,
  YplHeuristicRequest,
  YplMinBookingCountForSuperAgg,
  YplRateFence,
  YplRegulationFeatureEnabledSetting,
  YplRequest,
}
import org.joda.time.DateTime

// scalastyle:off
case class YplRequestBuilder(build: YplRequest) extends Builder[YplRequest] {
  def withChannels(chs: Set[YplChannel]): B = build.copy(channels = chs)
  def withClientInfo(info: YplClientInfo): B = build.copy(cInfo = info)
  def withBookingDate(date: String): B = build.copy(bookingDate = parseDateTime(date))
  def withBookingDate(date: DateTime): B = build.copy(bookingDate = date)
  def withBookRoomIdentifier(bookRoomIdentifier: Option[GUIDGenerator.RoomIdentifiers]): B =
    build.copy(bookRoomIdentifier = bookRoomIdentifier)
  def withCurrency(currency: Currency): B = build.copy(currency = currency)
  def withOccupancyInfo(info: YplOccInfo): B = build.copy(occ = info)
  def withCheckout(dateTime: String): B = build.copy(checkOut = parseDateTime(dateTime))
  def withCheckout(dateTime: DateTime): B = build.copy(checkOut = dateTime)
  def withCheckIn(dateTime: String): B = build.copy(checkIn = parseDateTime(dateTime))
  def withCheckIn(dateTime: DateTime): B = build.copy(checkIn = dateTime)
  def withExperiment(experiment: YplExperiment): B = build.copy(experiments = List(experiment))
  def withExperiments(experiments: List[YplExperiment]): B = build.copy(experiments = experiments)
  def withAExperiment(experiment: String): B = build.copy(experiments = List(YplExperiment(experiment, 'A')))
  def withAExperimentInSeq(experiment: String): B =
    build.copy(experiments = build.experiments ++ List(YplExperiment(experiment, 'A')))
  def withBExperiment(experiment: String): B = build.copy(experiments = List(YplExperiment(experiment, 'B')))
  def withBExperimentInSeq(experiment: String): B =
    build.copy(experiments = build.experiments ++ List(YplExperiment(experiment, 'B')))
  def withBExperiments(experiment_1: String, experiment_2: String): B =
    build.copy(experiments = List(YplExperiment(experiment_1, 'B'), YplExperiment(experiment_2, 'B')))
  def withMaxRoomSuggestions(maxSuggestions: Int): B =
    build.copy(featureRequest = build.featureRequest.copy(maxSuggestions = maxSuggestions))
  def withFlagInfo(info: YplFlagInfo): B = build.copy(flagInfo = info)
  def withCheapestRoomOnly(cheapest: Boolean): B = build.copy(isCheapestRoomOnly = cheapest)
  def withFeatureRequest(featureRequest: YplFeatureRequest): B = build.copy(featureRequest = featureRequest)
  def withFeatureFlags(featureFlags: Set[FeatureFlag]): B = build.copy(featureFlags = featureFlags)
  def withWhitelabelSetting(wl: WhitelabelSetting): B = build.copy(whitelabelSetting = wl)
  def withPreFilterZeroAllotment(pf: Option[PreFilterZeroAllotment]): B = build.copy(preFiltersZeroAllotment = pf)
  def withHeuristicRequest(heuristic: Option[YplHeuristicRequest]): B = build.copy(heuristicRequest = heuristic)
  def withSupplierFeature(feature: SupplierFeatures): B = build.copy(supplierFeatures = feature)
  def withHasSortingStrategy(hasSortingStrategy: Boolean): B = build.copy(hasSortingStrategy = hasSortingStrategy)
  def withFences(fences: Map[YplChannel, Set[YplRateFence]]): B = build.copy(fences = fences)
  def withCheapestRoomFilters(filters: List[Int]): B = build.copy(cheapestRoomFilters = filters)
  def withFencedAgxCommissions(fencedAgxCommissions: AgxCommissionFencing): B =
    build.copy(agxCommissionAdjustmentFences = fencedAgxCommissions)
  def withBcomBMPSetting(bmpSetting: Option[BComBMPSetting]): B = build.copy(bcomBMPSetting = bmpSetting)
  def withIsSRR(isSSR: Boolean): B = build.copy(isSSR = isSSR)
  def withFilterCriteria(filterCriteria: Option[List[FilterCriteria]]): B = build.copy(filterCriteria = filterCriteria)
  def withApplyTaxOnSellExSettings(applyTaxOnSellExSettings: Option[ApplyTaxOnSellExSettings]): B =
    build.copy(applyTaxOnSellExSettings = applyTaxOnSellExSettings)
  def withSupplierHotelCalculationSettings(supplierHotelCalculationSettings: SupplierHotelCalculationSettings): B =
    build.copy(supplierHotelCalculationSettings = supplierHotelCalculationSettings)
  def withYplApmSetting(yplApmSetting: Option[ApmSettingHolder]): B = build.copy(apmSetting = yplApmSetting)
  def withEnableOfferFilterLogs(enable: Boolean): B = build.copy(enableOfferFilterLogs = enable)
  def withXMLPartner(isXmlPartner: Boolean): B = build.copy(isXmlPartner = isXmlPartner)
  def withFilterPaymentModelAgencyByModifyingRoomStatus(filterPaymentModelAgencyByModifyingRoomStatus: Boolean): B =
    build.copy(filterPaymentModelAgencyByModifyingRoomStatus = filterPaymentModelAgencyByModifyingRoomStatus)
  def withStoreFrontId(storeFront: Int): B = build.copy(cInfo = build.cInfo.copy(storeFront = Some(storeFront)))
  def withWhiteLabelId(whiteLabelId: Int): B =
    build.copy(whitelabelSetting = build.whitelabelSetting.copy(whitelabelID = whiteLabelId))
  def withYplMinBookingCountForSuperAgg(yplMinBookingCountForSuperAgg: Seq[YplMinBookingCountForSuperAgg]): B =
    build.copy(minBookingCountForSuperAgg = Option(yplMinBookingCountForSuperAgg))
  def withShowExclusivePriceWithFeeEnabled(enabled: Boolean): B = build.copy(regulationFeatureEnabledSetting =
    YplRegulationFeatureEnabledSetting.default.copy(isShowExclusivePriceWithFeeEnabled = enabled))

  type B = YplRequestBuilder
}
