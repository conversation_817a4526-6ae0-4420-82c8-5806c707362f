package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.Surcharge
import com.agoda.protobuf.cache.ChannelRoomRate.Surcharge.SurchargeTax
import com.agoda.protobuf.common.ApplyType

case class OTASurchargeBuilder(build: Surcharge) extends Builder[Surcharge] {
  def withTaxId(surchargeId: Int): B = build.copy(surchargeId = surchargeId)
  def withApplyTo(applyTo: String): B = build.copy(applyTo = applyTo)
  def withApplyType(applyType: ApplyType): B = build.copy(applyType = applyType)
  def withIsAmount(isAmount: Boolean): B = build.copy(isAmount = isAmount)
  def withValue(value: Double): B = build.copy(value = value)
  def withIsCommission(isCommission: Boolean): B = build.copy(isCommissionable = isCommission)
  def withTaxes(taxes: Seq[SurchargeTax]): B = build.copy(taxes = taxes)
  type B = OTASurchargeBuilder
}
