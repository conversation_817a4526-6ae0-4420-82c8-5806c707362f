package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.StackedChannelDiscount

case class StackedChannelDiscountBuilder(build: StackedChannelDiscount) extends Builder[StackedChannelDiscount] {
  def withChannelId(channelId: Int): B = build.copy(channelId = channelId)
  def withDiscountPercent(discountPercent: Double): B = build.copy(discountPercent = discountPercent)

  type B = StackedChannelDiscountBuilder
}
