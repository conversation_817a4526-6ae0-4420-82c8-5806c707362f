package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.room.{ApplyType, ChargeOption, ChargeType, RateType, SubChargeType}
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes}
import com.agoda.papi.pricing.pricecalculation.models.CommonProcessingFeeBreakdown
import com.agoda.papi.ypl.models.api.request.YplAGXCommission
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.{DailyPrice, SurchargeEntry}
import com.agoda.papi.ypl.models.pricing.{DiscountMessage, YplChannelDiscountBreakdown, YplPrice}
import org.joda.time.DateTime

case class PriceBuilder(build: YplPrice) extends Builder[YplPrice] {
  def withDateString(dateString: String): B = build.copy(date = DateTime.parse(dateString))
  def withDate(date: DateTime): B = build.copy(date = date)
  def withDailyTaxes(taxes: DailyTaxes): B = build.copy(dailyTaxes = taxes)
  def withNetExclusive(value: Double): B = build.copy(netExclusive = value)
  def withPromoDiscount(value: Double): B = build.copy(promotionDiscount = value)
  def withMargin(value: Double): B = build.copy(margin = value)
  def withProcessingFee(value: Double): B = build.copy(processingFee = value)
  def withFee(value: Double): B = build.copy(fee = value)
  def withDownliftAmount(value: Double): B = build.copy(downliftAmount = value)
  def withTax(value: Double): B = build.copy(tax = value)
  def withChargeType(chargeType: ChargeType): B = build.copy(chargeType = chargeType)
  def withApplyType(applyType: ApplyType): B = build.copy(applyType = applyType)
  def withChargeOption(chargeOption: ChargeOption): B = build.copy(chargeOption = chargeOption)
  def withValue(value: Double): B = build.copy(value = value)
  def withQuantity(quantity: Int): B = build.copy(quantity = quantity)
  def withTaxBreakdown(tb: CommonTaxBreakdown): B = build.copy(taxBreakDown = List(tb))
  def withTaxBreakdown(tbs: List[CommonTaxBreakdown]): B = build.copy(taxBreakDown = tbs)
  def withRefId(refId: Int): B = build.copy(refId = refId)
  def withDiscountMessages(dMsgs: List[DiscountMessage]): B =
    build.copy(discountMessages = dMsgs.groupBy(_.discountType).mapValues(_.head))
  def withSurchargeRateType(value: Option[RateType]): B = build.copy(surchargeRateType = value)
  def withSurchargeEntry(value: Option[SurchargeEntry]): B = build.copy(surchargeEntry = value)
  def withDailyPrice(value: Option[DailyPrice]): B = build.copy(dailyPrice = value)
  def withIsConfigProcessingFee(value: Boolean): B = build.copy(isConfigProcessingFee = value)
  def withChannelDiscountBreakdown(channelDiscounts: List[YplChannelDiscountBreakdown]): B =
    build.copy(channelDiscounts = channelDiscounts)
  def withSubChargeType(subChargeType: SubChargeType): B = build.copy(subChargeType = subChargeType)
  def withRoomNumber(roomNumber: Option[Int]): B = build.copy(roomNumber = roomNumber)
  def withChildAgeRangeId(childAgeRangeId: Option[Long]): B = build.copy(childAgeRangeId = childAgeRangeId)
  def withYplAgxCommission(yplAgxCommission: YplAGXCommission): B = build.copy(agxCommission = yplAgxCommission)
  def withAgpMargin(agpMargin: Double): B = build.copy(agpMargin = agpMargin)
  def withAgpInvoiceMargin(agpInvoiceMargin: Double): B = build.copy(agpInvoiceMargin = agpInvoiceMargin)
  def withProcessingFeeBreakdown(processingFeeBreakdown: Option[CommonProcessingFeeBreakdown]): B =
    build.copy(processingFeeBreakdown = processingFeeBreakdown)
  def withSupplierFundedDiscountAmount(supplierFundedDiscountAmount: Option[Double]): B =
    build.copy(supplierFundedDiscountAmount = supplierFundedDiscountAmount)
  def withUspaDiscount(uspaDiscountAmount: Option[Double], uspaProgramId: Option[Int]): B =
    build.copy(uspaDiscountAmount = uspaDiscountAmount, uspaProgramId = uspaProgramId)

  type B = PriceBuilder
}
