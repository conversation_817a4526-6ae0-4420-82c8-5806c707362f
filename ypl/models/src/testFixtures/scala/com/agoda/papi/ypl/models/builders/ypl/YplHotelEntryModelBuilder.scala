package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.ypl.commission.apm.models.{AutoPriceMatchKeyEntry, AutoPriceMatchPriceInfo}
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.enums.OccupancyModel
import com.agoda.papi.ypl.models.pricing.proto.{
  HotelOccupancySetup,
  RateCategoryChannelLookUp,
  StackedChannelDiscount,
  TaxInfo,
}
import com.agoda.papi.ypl.models.proto.enums.SourceType
import com.agoda.papi.ypl.models.{
  ChannelId,
  EnabledRoom,
  HotelMeta,
  RoomTypeId,
  StayDate,
  SupplierId,
  YPLRateReutilizationEntry,
  YplDispatchChannels,
  YplHotelEntryModel,
  YplRateFence,
  YplReqOccByHotelAgePolicy,
  YplRoomEntry,
}

case class YplHotelEntryModelBuilder(build: YplHotelEntryModel) extends Builder[YplHotelEntryModel] {
  def withRooms(rooms: List[YplRoomEntry]): B = build.copy(rooms = rooms)
  def withOccupancyModel(om: OccupancyModel): B = build.copy(occupancyModel = om)
  def withMetaData(hotelInfo: HotelMeta): B = build.copy(metaData = hotelInfo)
  def withTaxInfo(taxInfo: TaxInfo): B = build.copy(taxInfo = taxInfo)
  def withSourceType(st: Option[SourceType]): B = build.copy(supplierSourceType = st)
  def withSupplierId(supplierId: SupplierId): B = build.copy(supplierId = supplierId)
  def withReqOccByHotelAgePolicy(reqOcc: YplReqOccByHotelAgePolicy): B = build.copy(reqOcc = reqOcc)
  def withPaymentModel(paymentModel: PaymentModel): B = build.copy(paymentModel = paymentModel)
  def withHotelId(hotelId: Int): B = build.copy(hotelId = hotelId)
  def withEnabledRooms(enabledRooms: Map[RoomTypeId, EnabledRoom]): B =
    build.copy(metaData = build.metaData.copy(enabledRoom = enabledRooms))
  def withStackedChannelInfo(stack: Map[ChannelId, Seq[StackedChannelDiscount]]): B =
    build.copy(stackChannelDiscountInfo = stack)
  def withDispatchChannels(dispatchChannels: YplDispatchChannels): B = build.copy(dispatchChannels = dispatchChannels)
  def withAutoPriceMatchInfo(apmInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]]): B =
    build.copy(autoPriceMatchInfo = apmInfo)
  def withRateReutilizations(rateReutilizationEntries: Seq[YPLRateReutilizationEntry]): B =
    build.copy(rateReutilizations = rateReutilizationEntries)
  def withChannelLookUpByRateCategoryAndRoomTypeMap(
    channelLookUpByRateCategoryAndRoomTypeMap: Map[(Long, Long), RateCategoryChannelLookUp]): B =
    build.copy(channelLookUpByRateCategoryAndRoomTypeMap = channelLookUpByRateCategoryAndRoomTypeMap)
  def withDispatchChannelsPerFence(dispatchChannelsPerFence: Map[YplRateFence, YplDispatchChannels]): B =
    build.copy(dispatchChannelsPerFence = dispatchChannelsPerFence)
  def withLos(los: Int): B = build.copy(lengthOfStay = los)
  def withHotelOccupancySetup(hotelOccupancySetup: Option[HotelOccupancySetup]): B =
    build.copy(hotelOccupancySetup = hotelOccupancySetup)

  type B = YplHotelEntryModelBuilder
}
