package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.{YPLRateReutilizationEntry, YplChannel}

case class YPLRateReutilizationEntryBuilder(build: YPLRateReutilizationEntry) extends Builder[YPLRateReutilizationEntry] {
  def withSourceChannel(sourceChannel: YplChannel): B = build.copy(sourceChannel = sourceChannel)
  def withReferenceChannel(referenceChannel: YplChannel): B = build.copy(referenceChannel = referenceChannel)
  def withTargetChannel(targetChannel: YplChannel): B = build.copy(targetChannel = targetChannel)
  def withDiscountType(discountType: Int): B = build.copy(discountType = discountType)
  def withFlatChannelDiscount(flatChannelDiscount: Double): B = build.copy(flatChannelDiscount = flatChannelDiscount)
  def withMinAdvPurchase(minAdv: Option[Int]): B = build.copy(minAdvPurchase = minAdv)
  def withMaxAdvPurchase(maxAdv: Option[Int]): B = build.copy(maxAdvPurchase = maxAdv)

  type B = YPLRateReutilizationEntryBuilder
}
