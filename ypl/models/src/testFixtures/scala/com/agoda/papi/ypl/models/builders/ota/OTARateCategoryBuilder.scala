package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.{
  BookingRestriction,
  DailyPrice,
  SupplierRateInfo,
  TimeInterval,
}
import com.agoda.protobuf.cache.Promotion
import com.agoda.protobuf.common.{PaymentModel, RateType}

case class OTARateCategoryBuilder(build: RateCategory) extends Builder[RateCategory] {
  def withRateCategoryId(id: Int): B = build.copy(rateCategoryId = id)
  def withRateTypeLoaded(rateType: RateType): B = build.copy(rateTypeLoaded = rateType)
  def withCancellationCode(code: Option[String]): B = build.copy(cancellationCode = code)
  def withRemainingRooms(remainingRoom: Int): B = build.copy(remainingRoom = remainingRoom)
  def withBookingRestriction(bookingRestriction: Option[BookingRestriction]): B =
    build.copy(bookingRestriction = bookingRestriction)
  def withDailyPrices(dailyPrices: Seq[DailyPrice]): B = build.copy(dailyPrices = dailyPrices)
  def withSupplierRateInfo(supplierRateInfo: Option[SupplierRateInfo]): B =
    build.copy(supplierRateInfo = supplierRateInfo)
  def withIsCreditCardRequired(creditCardRequired: Option[Boolean]): B =
    build.copy(isCreditCardRequried = creditCardRequired)
  def withIsDomesticCreditCardRequired(domesticCCRequired: Option[Boolean]): B =
    build.copy(isDomesticCreditCardRequried = domesticCCRequired)
  def withSurchargeMarkup(surchargeMarkup: Option[Double]): B = build.copy(surchargeMarkup = surchargeMarkup)
  def withAgodaBenefits(benefits: Seq[Int]): B = build.copy(agodaBenefits = benefits)
  def withIsPrepaymentRequired(prepaymentRequired: Option[Boolean]): B =
    build.copy(isPrepaymentRequired = prepaymentRequired)
  def withPromotions(promotions: Seq[Promotion]): B = build.copy(promotions = promotions)
  def withConfirmByMins(confirmByMins: Option[Int]): B = build.copy(confirmByMins = confirmByMins)
  def withIsRoomTypeNotGuaranteed(roomTypeNotGuaranteed: Option[Boolean]): B =
    build.copy(isRoomTypeNotGuaranteed = roomTypeNotGuaranteed)
  def withPaymentModel(paymentModel: Option[PaymentModel]): B = build.copy(paymentModel = paymentModel)
  def withOfferTypeId(offerTypeId: Option[Int]): B = build.copy(offerTypeId = offerTypeId)
  def withPromotionTypeId(promotionTypeId: Int): B = build.copy(promotionTypeId = Option(promotionTypeId))
  def withPromotionTypeCmsId(promotionTypeCmsId: Int): B = build.copy(promotionTypeCmsId = Option(promotionTypeCmsId))
  def withHourlyAvailableSlots(timeInterval: Seq[TimeInterval]): B = build.copy(hourlyAvailableSlots = timeInterval)

  type B = OTARateCategoryBuilder
}
