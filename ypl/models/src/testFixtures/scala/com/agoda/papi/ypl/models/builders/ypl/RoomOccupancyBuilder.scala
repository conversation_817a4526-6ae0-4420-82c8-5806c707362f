package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.RoomOccupancy

case class RoomOccupancyBuilder(build: RoomOccupancy) extends Builder[RoomOccupancy] {
  def withAdults(adults: Int): B = build.copy(adults = adults)
  def withChildren(children: Int): B = build.copy(children = children)
  def withInfants(infants: Int): B = build.copy(infants = infants)
  def withFreeChildren(freeChildren: Int): B = build.copy(freeChildrenAndInfants = freeChildren)
  def withExtraBeds(extraBeds: Int): B = build.copy(extraBeds = extraBeds)
  def withMaxExtraBed(maxExtraBeds: Int): B = build.copy(maxExtraBeds = maxExtraBeds)
  def withMaxFreeChildren(allowedFreeChildrenAndInfants: Int): B =
    build.copy(allowedFreeChildrenAndInfants = allowedFreeChildrenAndInfants)
  def withPerOfferMaxFreeChildren(perOfferMaxFreeChildren: Int): B =
    build.copy(perOfferMaxFreeChildren = perOfferMaxFreeChildren)
  type B = RoomOccupancyBuilder
}
