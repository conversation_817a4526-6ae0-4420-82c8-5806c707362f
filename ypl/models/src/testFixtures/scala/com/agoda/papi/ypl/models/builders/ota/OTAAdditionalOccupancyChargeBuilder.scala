package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice.{AdditionalOccupancyCharge, TaxAndFee}

case class OTAAdditionalOccupancyChargeBuilder(build: AdditionalOccupancyCharge)
  extends Builder[AdditionalOccupancyCharge] {
  def withAgeQualifyingCode(ageQualifyingCode: String): B = build.copy(ageQualifyingCode = ageQualifyingCode)
  def withAmount(amount: Double): B = build.copy(amount = amount)
  def withTaxAndFee(taxAndFee: Seq[TaxAndFee]): B = build.copy(taxAndFee = taxAndFee)
  def withMaxAdditionalGuests(maxAdditionalGuests: Int): B = build.copy(maxAdditionalGuests = maxAdditionalGuests)
  def withPercent(percent: Boolean): B = build.copy(percent = percent)
  def withPayAtHotel(payAtHotel: Boolean): B = build.copy(payAtHotel = payAtHotel)

  type B = OTAAdditionalOccupancyChargeBuilder
}
