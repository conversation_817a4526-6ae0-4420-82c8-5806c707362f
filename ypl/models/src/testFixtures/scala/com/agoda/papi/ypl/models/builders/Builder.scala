package com.agoda.papi.ypl.models.builders

import com.agoda.papi.ypl.models.builders.implicits.ImplicitTestDataBuilder
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat

abstract class Builder[A] extends ImplicitTestDataBuilder {
  def build: A

  protected def parseDateTime(date: String, format: String = "yyyy-MM-dd"): DateTime = {
    val jodaFormat = DateTimeFormat.forPattern(format)
    jodaFormat.parseDateTime(date)
  }

  implicit class ObjectHelper[T](obj: T) {
    def toOption(condition: => Boolean): Option[T] = if (condition) Some(obj) else None
  }
}
