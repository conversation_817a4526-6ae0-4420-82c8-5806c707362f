package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.ypl.models.pricing.YplChannelDiscountBreakdown
import monocle.Lens

object YplChannelDiscountBreakdownLens {
  type A = YplChannelDiscountBreakdown
  val idLens = Lens[A, Int](_.channelId)(id => cd => cd.copy(channelId = id))
  val isBaseLens = Lens[A, <PERSON>olean](_.isBase)(isBase => cd => cd.copy(isBase = isBase))
  val percentageLens = Lens[A, Double](_.percentage)(percentage => cd => cd.copy(percentage = percentage))
  val amountLens = Lens[A, Double](_.amount)(amount => cd => cd.copy(amount = amount))
}
