package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.lens.ypl.YPLMetaRateSiloLens
import com.agoda.papi.ypl.models.{ChannelsPerSupplierMap, HotelId, HotelMeta, RateSilo, YPLMetaRateSilo}

case class MetaSiloBuilder(build: YPLMetaRateSilo) extends Builder[YPLMetaRateSilo] {
  def withMeta(meta: HotelMeta): B = YPLMetaRateSiloLens.hotelMetaLens.set(meta)(build)
  def withRateSilo(ratesilo: RateSilo): B = YPLMetaRateSiloLens.rateSiloLens.set(ratesilo)(build)
  def withHotelId(hotelId: HotelId): B = YPLMetaRateSiloLens.hotelIdSetter.set(hotelId.toInt)(build)
  def withSupplierChannelMap(supplierMap: ChannelsPerSupplierMap): B =
    YPLMetaRateSiloLens.supplierChannelsMapLens.set(supplierMap)(build)
  type B = MetaSiloBuilder
}
