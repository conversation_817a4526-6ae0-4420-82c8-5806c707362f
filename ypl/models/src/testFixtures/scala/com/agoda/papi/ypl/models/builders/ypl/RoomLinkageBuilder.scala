package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.{RoomLinkage, RoomLinkageExclusionDate}
import org.joda.time.DateTime

case class YplRoomLinkage(linkedRoomType: Long,
                          daysBeforeWeekdays: Int,
                          daysBeforeWeekend: Long,
                          exclusionDates: Seq[RoomLinkageExclusionDate] = Seq.empty)
  extends RoomLinkage

case class YplRoomLinkageExclusionDate(exclusionDateFrom: Option[DateTime], exclusionDateTo: Option[DateTime])
  extends RoomLinkageExclusionDate

case class RoomLinkageBuilder(build: YplRoomLinkage) extends Builder[YplRoomLinkage] {
  def withLinkedRoomType(id: Long): B = build.copy(linkedRoomType = id)

  def withDaysBeforeWeekdays(id: Int): B = build.copy(daysBeforeWeekdays = id)

  def withDaysBeforeWeekend(id: Int): B = build.copy(daysBeforeWeekend = id)

  def withExclusionDates(value: Seq[RoomLinkageExclusionDate]): B = build.copy(exclusionDates = value)

  type B = YplRoomLinkage
}
