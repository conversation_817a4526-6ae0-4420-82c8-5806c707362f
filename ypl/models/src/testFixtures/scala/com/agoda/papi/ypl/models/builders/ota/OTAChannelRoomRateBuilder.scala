package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory
import com.agoda.protobuf.common.RateType

case class OTAChannelRoomRateBuilder(build: ChannelRoomRate) extends Builder[ChannelRoomRate] {
  def withRoomTypeId(roomTypeId: Long): B = build.copy(roomTypeId = roomTypeId)
  def withChannelId(channelId: Int): B = build.copy(channelId = channelId)
  def withCurrencyCode(currencyCode: Option[String]): B = build.copy(currencyCode = currencyCode)
  def withProccessingFee(processingFee: Double): B = build.copy(processingFee = processingFee)
  def withRateCategories(rateCat: Seq[RateCategory]): B = build.copy(rateCategories = rateCat)
  def withSupplierRoomTypeId(supplierRoomTypeId: Option[String]): B = build.copy(supplierRoomTypeId = supplierRoomTypeId)
  def withTimeStampt(timestanp: Option[String]): B = build.copy(timeStamp = timestanp)
  def withRoomUID(uid: Option[String]): B = build.copy(roomUid = uid)
  def withRateType(rateType: Option[RateType]): B = build.copy(rateType = rateType)

  type B = OTAChannelRoomRateBuilder
}
