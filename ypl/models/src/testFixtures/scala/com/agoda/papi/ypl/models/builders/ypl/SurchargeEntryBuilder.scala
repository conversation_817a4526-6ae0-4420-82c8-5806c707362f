package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.room.ChargeOption
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.SurchargeEntry
import org.joda.time.DateTime

case class SurchargeEntryBuilder(build: SurchargeEntry) extends Builder[SurchargeEntry] {
  def withId(id: Int): B = build.copy(id = id)
  def withOption(option: ChargeOption): B = build.copy(option = option)
  def withOccFromProto(occ: Int): B = build.copy(occFromProto = occ)
  def withDates(dates: Set[DateTime]): B = build.copy(dates = dates)
  def withIsPropOffer(isPropOffer: Boolean): B = build.copy(isPropOffer = isPropOffer)
  def withValue(value: Double) = build.copy(value = value)
  def withApplyTo(applyTo: String): B = build.copy(applyTo = applyTo)

  type B = SurchargeEntryBuilder
}
