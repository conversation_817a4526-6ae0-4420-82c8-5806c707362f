package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.lens.ota.RateRepurposingDataLens
import com.agoda.protobuf.cache.RateRepurposingData

case class OTARateRepurposingDataBuilder(build: RateRepurposingData) extends Builder[RateRepurposingData] {
  def withTargetChannel(channel: Int): B = RateRepurposingDataLens.targetChannelLens.set(channel)(build)
  def withChannelDiscount(discount: Double): B = RateRepurposingDataLens.channelDiscountLens.set(discount)(build)
  type B = RateRepurposingData
}
