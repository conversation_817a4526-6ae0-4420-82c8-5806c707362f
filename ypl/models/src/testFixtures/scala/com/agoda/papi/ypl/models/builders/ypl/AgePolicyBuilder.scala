package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.hotel.ChildrenStayFreeType
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.hotel.AgePolicy

case class AgePolicyBuilder(build: AgePolicy) extends Builder[AgePolicy] {
  def withIsChildrenStayingFree(stayFree: Boolean): B = build.copy(isChildrenStayFree = stayFree)
  def withInfantMaxAge(infantMaxAge: Int): B = build.copy(infantMaxAge = infantMaxAge)
  def withChildMaxAge(childMaxAge: Int): B = build.copy(childMaxAge = childMaxAge)
  def withChildrenStayFreeType(stayFreeType: ChildrenStayFreeType): B = build.copy(childrenStayFreeType = stayFreeType)
  def withMinGuestAge(minGuestAge: Int): B = build.copy(minGuestAge = minGuestAge)
  type B = AgePolicyBuilder
}
