package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.enums.room.{InventoryType, RateType}
import com.agoda.papi.ypl.commission.CommissionHolder
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.occupancy.OccupancyBreakdown
import com.agoda.papi.ypl.models.pricing.RoomOccupancy
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.{
  Currency,
  DmcDataHolder,
  PropOfferOccupancy,
  ResellExternalData,
  RoomNumber,
  RoomTypeId,
  YPLRoomAllocation,
  YplChannel,
  YplRateFence,
  YplRoomEntry,
}
import org.joda.time.{DateTime, LocalDate}

//scalastyle:off
case class RoomEntryBuilder(build: YplRoomEntry) extends Builder[YplRoomEntry] {
  def withAvailablePromotions(availablePromotions: List[PromotionEntry]): B =
    build.copy(availablePromotions = availablePromotions)
  def withPromotion(promotion: PromotionEntry): B = build.copy(promotion = Option(promotion))
  def withAllowedCombinePromotion(allowed: Boolean): B = build.copy(isAllowCombinePromotion = allowed)
  def withOccupancy(occ: RoomOccupancy): B = build.copy(occEntry = occ)
  def withRoomTypeEntry(roomTypeEntry: RoomTypeEntry): B = build.copy(roomType = roomTypeEntry)
  def withDailyPrices(dailyPrices: Map[DateTime, DailyPrice]): B = build.copy(dailyPrices = dailyPrices)
  def withRoomTypeId(id: RoomTypeId): B = build.copy(roomTypeId = id)
  def withChannel(ch: YplChannel): B = build.copy(channel = ch)
  def withAgodaAgency(isAgodaAgency: Boolean): B = build.copy(isAgodaAgency = isAgodaAgency)
  def withPaymentModel(paymentModel: Option[PaymentModel]): B = build.copy(paymentModel = paymentModel)
  def withRateType(rateType: RateType): B = build.copy(rateType = rateType)
  def withOriginalRateType(rateType: RateType): B = build.copy(originalRateType = rateType)
  def withRateCategoryEntry(rateCategory: RateCategoryEntry): B = build.copy(rateCategory = rateCategory)
  def withPromotionsBreakdown(breakdown: Map[LocalDate, List[PromotionEntry]]): B =
    build.copy(promotionsBreakdown = breakdown)
  def withProcessingFees(pf: Double): B = build.copy(processingFees = pf)
  def withRoomType(roomType: RoomTypeEntry): B = build.copy(roomType = roomType)
  def withAveragePrice(avg: Boolean): B = build.copy(isAveragePrice = avg)
  def withIsMultipleRoomAssignmentPrice(isMultipleRoomAssignmentPrice: Boolean): B =
    build.copy(isMultipleRoomAssignmentPrice = isMultipleRoomAssignmentPrice)
  def withIsKeptForFlexibleMultiRoom(isKeptForFlexibleMultiRoom: Boolean): B =
    build.copy(isKeptForFlexibleMultiRoom = isKeptForFlexibleMultiRoom)
  def withCxlCode(cxl: String): B = build.copy(cxlCode = cxl)
  def withOccFromProto(occFromProto: Int): B = build.copy(occFromProto = occFromProto)
  def withCurrency(currency: Currency): B = build.copy(currency = currency)
  def withInventoryType(inv: InventoryType): B = build.copy(inventoryType = inv)
  def withRemainingRooms(remainingRooms: Int): B = build.copy(remainingRooms = remainingRooms)
  def withDmcDataHolder(dmcDataHolder: Option[DmcDataHolder]): B = build.copy(dmcDataHolder = dmcDataHolder)
  def withHourlyAvailableSlots(hourlyAvailableSlots: Seq[TimeInterval]): B =
    build.copy(hourlyAvailableSlots = hourlyAvailableSlots)
  def withFences(fences: Set[YplRateFence]): B = build.copy(fences = fences)
  def withPulseCampaignMetadata(pulseCampaignMetadataOpt: Option[PulseCampaignMetadata]) =
    build.copy(pulseCampaignMetadata = pulseCampaignMetadataOpt)
  def withResellExternalData(resellExternalDataOpt: Option[ResellExternalData]): B =
    build.copy(resellExternalData = resellExternalDataOpt)
  def withCommissionHolder(commissionHolder: CommissionHolder): B = build.copy(commissionHolder = commissionHolder)
  def withApplyNoCCCommissionn(applyNoCCCommission: Boolean): B = build.copy(applyNoCCCommission = applyNoCCCommission)
  def withOccupancyBreakdown(occupancyBreakdown: Option[OccupancyBreakdown]): B =
    build.copy(occupancyBreakdown = occupancyBreakdown)
  def withRoomAllocationInfo(roomAllocationInfo: Map[RoomNumber, YPLRoomAllocation]): B =
    build.copy(roomAllocationInfo = roomAllocationInfo)
  def withOcEntry(occEntry: RoomOccupancy): B = build.copy(occEntry = occEntry)
  def withPropOfferOccupancy(propOfferOccupancy: PropOfferOccupancy): B =
    build.copy(propOfferOccupancy = propOfferOccupancy)

  type B = RoomEntryBuilder
}
