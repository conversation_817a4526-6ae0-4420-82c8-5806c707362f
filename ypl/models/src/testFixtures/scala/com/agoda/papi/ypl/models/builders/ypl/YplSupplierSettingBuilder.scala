package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.YplSupplierSetting
import com.agoda.papi.ypl.models.builders.Builder

case class YplSupplierSettingBuilder(build: YplSupplierSetting) extends Builder[YplSupplierSetting] {
  def withIsChildStayFree(childStayFree: Boolean): B = build.copy(isChildStayFree = childStayFree)
  def withIsOccFree(occFree: Boolean): B = build.copy(isOccFreePullSupplier = occFree)
  def withIsThirdParty(isThirdParty: Boolean): B = build.copy(isThirdParty = isThirdParty)
  def withIsAgodaBrand(isAgodaBrand: Boolean): B = build.copy(isAgodaBrand = isAgodaBrand)

  type B = YplSupplierSettingBuilder
}
