package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.room.{ApplyTaxOver, ApplyType, ChargeOption, WhomToPayType}
import com.agoda.papi.pricing.pricecalculation.models.tax.CommonTaxBreakdown
import com.agoda.papi.ypl.models.builders.Builder

case class TaxBreakdownBuilder(build: CommonTaxBreakdown) extends Builder[CommonTaxBreakdown] {
  def withTypeId(id: Int): B = build.copy(typeId = id)
  def withIsFee(isFee: Boolean): B = build.copy(isFee = isFee)
  def withQuantity(q: Int): B = build.copy(quantity = q)
  def withAmount(amount: Double): B = build.copy(amount = amount)
  def withApplyType(applyType: ApplyType): B = build.copy(applyTo = applyType.entryName)
  def withPercentage(perc: Double): B = build.copy(percentage = perc)
  def withTaxProtoTypeId(taxProtoTypeId: Int): B = build.copy(taxProtoTypeId = taxProtoTypeId)
  def withInclude(include: Boolean): B = build.copy(include = include)
  def withTaxable(value: Boolean): B = build.copy(taxable = Some(value))
  def withOption(option: ChargeOption): B = build.copy(option = option)
  def withApplyOver(applyOver: ApplyTaxOver): B = build.copy(applyOver = Some(applyOver))
  def withWhomToPay(whomToPay: Option[WhomToPayType]): B = build.copy(whomToPay = whomToPay)

  type B = TaxBreakdownBuilder
}
