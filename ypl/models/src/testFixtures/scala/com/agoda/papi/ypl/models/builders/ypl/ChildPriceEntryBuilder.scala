package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.room.PricingChildRateType
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.ChildPriceEntry

case class ChildPriceEntryBuilder(build: ChildPriceEntry) extends Builder[ChildPriceEntry] {
  def withValue(value: Option[Double]): B = build.copy(value = value)
  def withChildAgeRangeId(childAgeRangeId: Int): B = build.copy(childAgeRangeId = childAgeRangeId)
  def withPricingTypeId(pricingTypeId: PricingChildRateType): B = build.copy(pricingTypeId = pricingTypeId)
  def withAgeFromTo(ageFrom: Int, ageTo: Int): B = build.copy(ageFrom = ageFrom, ageTo = ageTo)

  type B = ChildPriceEntryBuilder
}
