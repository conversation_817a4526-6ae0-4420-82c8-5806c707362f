package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.LanguageId
import com.agoda.papi.ypl.models.api.request.YplClientInfo
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.enums.VipLevelType
import com.agoda.papi.ypl.models.lens.ypl.YPLClientInfoLens

case class YPLClientInfoBuilder(build: YplClientInfo) extends Builder[YplClientInfo] {
  type B = YPLClientInfoBuilder

  def withLanguage(language: LanguageId): B = YPLClientInfoLens.languageLens.set(language)(build)

  @deprecated("Use YplRequest.fences instead")
  def withOrigin(origin: Option[String]): B = YPLClientInfoLens.originLens.set(origin)(build)

  def withCid(cid: Option[Int]): B = YPLClientInfoLens.cidLens.set(cid)(build)

  def withMemberId(memberId: Option[Long]): B = YPLClientInfoLens.memberIdLens.set(memberId)(build)

  def withUserLogIn(userLogIn: Boolean): B = YPLClientInfoLens.loginLens.set(userLogIn)(build)

  @deprecated("Use YplRequest.fences instead")
  def withCId(cid: Option[Int]): B = YPLClientInfoLens.cidLens.set(cid)(build)

  def withVipLevel(vipLevel: Option[VipLevelType]): B = YPLClientInfoLens.vipLevelLens.set(vipLevel)(build)

  def withPlatformId(platformId: Option[Int]): B = YPLClientInfoLens.platformLens.set(platformId)(build)
}
