package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.ypl.models.api.request.YplFlagInfo
import monocle.Lens

object FlagInfoLens {
  type A = YplFlagInfo
  val apsEnableLens = Lens[A, <PERSON><PERSON><PERSON>](_.isApsEnabled)(aps => flag => flag.copy(isApsEnabled = aps))
  val filterApoLens = Lens[A, <PERSON>olean](_.filterAPO)(filterapo => flag => flag.copy(filterAPO = filterapo))
  val rpm2IncludeLens =
    Lens[A, Boolean](_.isRPM2Included)(rpm2Include => flag => flag.copy(isRPM2Included = rpm2Include))
  val isAllOccLens = Lens[A, Boolean](_.isAllOcc)(allOcc => flag => flag.copy(isAllOcc = allOcc))
  val isMseLens = Lens[A, <PERSON><PERSON><PERSON>](_.isMSE)(isMSE => flag => flag.copy(isMSE = isMSE))

}
