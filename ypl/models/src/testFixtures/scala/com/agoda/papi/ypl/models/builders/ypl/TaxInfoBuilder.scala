package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.pricing.pricecalculation.models.tax.Tax
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto._

case class TaxInfoBuilder(build: TaxInfo) extends Builder[TaxInfo] {
  def withTaxes(taxes: Map[TaxIDWithProTypeID, Tax]): B = build.copy(taxes = taxes)
  def withHotelTaxInfo(hotelTaxInfo: HotelTaxInfo): B = build.copy(hotelTaxInfo = hotelTaxInfo)

  type B = TaxInfoBuilder
}
