package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.cancellation.models.CancellationFee
import com.agoda.papi.cancellation.models.waiver.Waiver
import com.agoda.papi.enums.hotel.{PaymentModel, PaymentOption, StayPackageType}
import com.agoda.papi.enums.room.{RatePlanStatus, RateType}
import com.agoda.papi.ypl.commission.apm.models.MultipleAutoPriceMatchHolder
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.hotel._
import com.agoda.papi.ypl.models.pricing.proto.{TaxInfo, TimeInterval}
import com.agoda.papi.ypl.models.pricing.{RoomOccupancy, YplPrice}
import com.agoda.papi.ypl.models.{
  ApmExternalData,
  DmcDataHolder,
  HotelId,
  ResellExternalData,
  RoomFeatures,
  SupplierId,
  YPLRoom,
  YplCancellationCondition,
  YplChannel,
  YplPartialRefundInfo,
  YplPriusOutput,
  YplRateFence,
  YplReqOccByHotelAgePolicy,
  YplRoomEntry,
}

//scalastyle:off
case class RoomBuilder(build: YPLRoom) extends Builder[YPLRoom] {
  def withRateCategoryId(id: Int): B = build.copy(rateCategoryId = id)
  def withoutActingYcs(): B = build.copy(actingAsYcs = None)
  def withActingYCs(bool: Boolean): B = build.copy(actingAsYcs = Some(bool))
  def withHotelId(hotelId: HotelId): B = build.copy(hotelId = hotelId)
  def withBreakfast(breakfast: Boolean): B = build.copy(breakfast = breakfast)
  def withBenefit(benefit: Benefit): B = build.copy(benefits = List(benefit))
  def withBenefits(benefits: List[Benefit]): B = build.copy(benefits = benefits)
  def withOccupancy(occupancy: RoomOccupancy): B = build.copy(occ = occupancy)
  def withCXLCode(code: String): B = build.copy(cxlCode = code)
  def withRoomTypeId(roomTypeId: Long): B = build.copy(roomTypeId = roomTypeId)
  def withPaymentModel(paymentModel: PaymentModel): B = build.copy(paymentModel = paymentModel)
  def withPrice(price: YplPrice): B = build.copy(prices = List(price))
  def withPrices(prices: List[YplPrice]): B = build.copy(prices = prices)
  def withSupplierId(supplierId: SupplierId): B = build.copy(supplierId = supplierId)
  def withAllotment(allotment: Int): B = build.copy(allotment = allotment)
  def withPromotion(promotion: Promotion): B = build.copy(discountInfo = DiscountInfo(promotion = Some(promotion)))
  def withRateCategory(rateCategory: RateCategory): B =
    build.copy(rateCategoryId = rateCategory.id, rateCategory = rateCategory)
  def withReqOcc(recOcc: Option[YplReqOccByHotelAgePolicy]): B = build.copy(reqOcc = recOcc)
  def withMasterRoomId(masterRoomId: Long): B = build.copy(masterRoomId = Some(masterRoomId))
  def withMasterRoomId(optMasterRoomId: Option[Long]): B = build.copy(masterRoomId = optMasterRoomId)
  def withCurrency(currency: String): B = build.copy(currency = currency)
  def withRoomFeatures(features: RoomFeatures): B = build.copy(roomFeatures = features)
  def withChannel(channel: YplChannel): B = build.copy(channel = channel)
  def withStatus(roomStatus: RatePlanStatus): B = build.copy(roomStatus = roomStatus)
  def withDiscountInfo(discountInfo: DiscountInfo): B = build.copy(discountInfo = discountInfo)
  def withFireDrill(isFireDrill: Boolean): B = build.copy(isFireDrill = isFireDrill)
  def withPriusOutput(prius: YplPriusOutput): B = build.copy(priusOutput = Some(prius))
  def withNoCreditCard(noCreditCard: Boolean): B = build.copy(noCreditCard = noCreditCard)
  def withPrepaymentRequired(prepaymentRequired: Boolean): B = build.copy(prepaymentRequired = prepaymentRequired)
  def withDmcData(dmcData: DmcData): B = build.copy(dmcData = Some(dmcData))
  def withPaymentOption(paymentOptions: Set[PaymentOption]): B = build.copy(paymentOptions = paymentOptions)
  def withActAsYCS(actYCS: Boolean): B = build.copy(actingAsYcs = Some(actYCS))
  def withDmcRoomId(dmcRoomId: Option[String]): B = build.copy(dmcRoomId = dmcRoomId)
  def withDmcRatePlanId(dmcRatePlanId: Option[String]): B = build.copy(dmcRatePlanID = dmcRatePlanId)
  def withDmcMealPlanId(dmcMealPlanId: Option[String]): B = build.copy(dmcMealPlanID = dmcMealPlanId)
  def withRateType(rateType: RateType): B = build.copy(rateType = rateType)
  def withOriginalRateType(rateType: RateType): B = build.copy(originalRateType = rateType)
  def withAgodaAgency(isAgodaAgency: Boolean): B =
    build.copy(roomFeatures = build.roomFeatures.copy(isAgodaAgency = isAgodaAgency))
  def withDmcDataHolder(dmcDataHolder: Option[DmcDataHolder]): B = build.copy(dmcDataHolder = dmcDataHolder)
  def withSellerBrand(brand: String): B = build.copy(sellerBrand = brand)
  def withIsFireDrill(isFireDrill: Boolean): B = build.copy(isFireDrill = isFireDrill)
  def withProcessingFees(pf: Double): B = build.copy(processingFeePercent = pf)
  def withMarginPercentage(mp: Double): B = build.copy(marginPercentage = mp)
  def withYplRoomEntry(roomEntry: YplRoomEntry): B = build.copy(yplRoomEntry = roomEntry)
  def withTaxInfo(info: Option[TaxInfo]): B = build.copy(taxInfo = info)
  def withCancellationCondition(condition: YplCancellationCondition): B = build.copy(cancellationCondition = condition)
  def withCancellationFeeList(cancellationFeeList: Option[List[CancellationFee]]): B =
    build.copy(cancellationFeeList = cancellationFeeList)
  def withPartialRefundInfo(info: Option[YplPartialRefundInfo]): B = build.copy(partialRefundInfo = info)
  def withIsAgodaRefund(isAgodaRefund: Boolean): B = build.copy(isAgodaRefund = isAgodaRefund)
  def withLengthOfStay(los: Int): B = build.copy(lengthOfStay = los)
  def withStayPackageType(stay: Option[StayPackageType] = None): B = build.copy(stayPackageType = stay)
  def withApmExternalData(apmExtData: ApmExternalData): B = build.copy(apmExternalData = Some(apmExtData))
  def withApmCommissionDiscountSetting(setting: MultipleAutoPriceMatchHolder): B =
    build.copy(apmCommissionDiscountSetting = Some(setting))
  def withApmPriceAdjustmentSetting(setting: MultipleAutoPriceMatchHolder): B =
    build.copy(apmPriceAdjustmentSetting = Some(setting))
  def withFences(fences: Set[YplRateFence]): B = build.copy(fences = fences)
  def withResellExternalData(resellExternalData: ResellExternalData): B =
    build.copy(resellExternalData = Some(resellExternalData))
  def withFireDrillContract(fireDrillContract: YplFireDrillContract): B =
    build.copy(fireDrillContract = Some(fireDrillContract))
  def withHourlyAvailableSlots(hourlyAvailableSlots: Seq[TimeInterval] = Seq.empty): B =
    build.copy(hourlyAvailableSlots = hourlyAvailableSlots)
  def withFeeWaiver(feeWaivers: Seq[Waiver]): B = build.copy(feeWaivers = feeWaivers)
  def withConfirmByMins(confirmByMins: Option[Int]): B = build.copy(confirmByMins = confirmByMins)
  def withRateFence(fences: Set[YplRateFence]): B = build.copy(fences = fences)
  def withRoomStatus(status: RatePlanStatus): B = build.copy(roomStatus = status)
  type B = RoomBuilder
}
//scalastyle:on
