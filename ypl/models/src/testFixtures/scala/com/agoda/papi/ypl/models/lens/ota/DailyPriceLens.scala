package com.agoda.papi.ypl.models.lens.ota

import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice.OccupancyPrice
import monocle.Lens

object DailyPriceLens {
  type A = DailyPrice

  val dateInMilliLens = Lens[A, Long](_.dateMillis)(millis => daily => daily.copy(dateMillis = millis))
  val occPriceListLens = Lens[A, Seq[OccupancyPrice]](_.prices)(prices => daily => daily.copy(prices = prices))

}
