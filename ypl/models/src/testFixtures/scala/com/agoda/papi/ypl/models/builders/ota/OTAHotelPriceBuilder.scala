package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.lens.ota.HotelPriceLens
import com.agoda.protobuf.cache._
import com.agoda.protobuf.common._

case class OTAHotelPriceBuilder(build: HotelPrice) extends Builder[HotelPrice] {
  def withHotelId(id: Int): B = HotelPriceLens.hotelIdLens.set(id)(build)
  def withCheckIn(millis: Long): B = HotelPriceLens.checkInLens.set(millis)(build)
  def withLos(los: Int): B = HotelPriceLens.losLens.set(los)(build)
  def withSupplierId(supplierId: Int): B = HotelPriceLens.supplierIdLens.set(supplierId)(build)
  def withOccupancyModel(occModel: OccupanyModel): B = HotelPriceLens.occupancyModelLens.set(occModel)(build)
  def withTaxType(taxType: TaxType): B = HotelPriceLens.taxTypeLens.set(taxType)(build)
  def withPaymentModel(payment: PaymentModel): B = HotelPriceLens.paymentModelLens.set(payment)(build)
  def withSurchargeRateLoadType(rateType: Option[RateType]): B =
    HotelPriceLens.surchargeRateLoadTypeLens.set(rateType)(build)
  def withChannelRoomRateList(channelRoomRates: List[ChannelRoomRate]): B =
    HotelPriceLens.channelRoomRateListLens.set(channelRoomRates)(build)
  def withRoomTypes(roomTypes: Seq[RoomType]): B = HotelPriceLens.roomTypesLens.set(roomTypes)(build)
  def withVersion(version: Option[Float]): B = HotelPriceLens.versionLens.set(version)(build)
  def withIsUseConfiguredProcessingFee(isUse: Option[Boolean]): B =
    HotelPriceLens.isUseConfigureProcessingFeeLens.set(isUse)(build)
  def withSourceType(sourceType: Option[SourceType]): B = HotelPriceLens.sourceTypeLens.set(sourceType)(build)
  def withExpiresAt(expiresAt: Option[Long]): B = HotelPriceLens.expireAtLens.set(expiresAt)(build)
  def withIsPromotionCombinable(promotionCombinable: Option[Boolean]): B =
    HotelPriceLens.isPromotionCombinableLens.set(promotionCombinable)(build)
  def withFireDrill(firedrill: Option[FireDrill]): B = HotelPriceLens.firedrillLens.set(firedrill)(build)
  def withRateRepurposeList(rateRepurpose: Seq[RateRepurposingData]): B =
    HotelPriceLens.rateRepurposedListLens.set(rateRepurpose)(build)
  def withSupplierContractedCommission(supplierContractedCommission: Option[Double]): B =
    HotelPriceLens.supplierContractedCommissionLens.set(supplierContractedCommission)(build)
  def withPropertyBenefits(propertyBenefits: Seq[Benefit]): B =
    HotelPriceLens.propertyBenefitsLens.set(propertyBenefits)(build)

  type B = OTAHotelPriceBuilder
}
