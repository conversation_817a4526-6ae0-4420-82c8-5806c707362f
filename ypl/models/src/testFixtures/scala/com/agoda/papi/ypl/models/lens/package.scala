package com.agoda.papi.ypl.models

import monocle.{Lens, Optional, Prism, Setter}

package object lens {

  object SetterChain {
    def apply[A, B](setters: Setter[A, B]*): Setter[A, B] = {
      val chains = { input: (B => B) => Function.chain(setters.map(_.modify(input))) }
      Setter(f => chains(f))
    }
  }

  implicit def lensAsSetter[A, B](lens: Lens[A, B]): Setter[A, B] = lens.asSetter
  implicit def optionsAsSetter[A, B](options: Optional[A, B]): Setter[A, B] = options.asSetter
  implicit def prismAsSetter[A, B](prism: Prism[A, B]): Setter[A, B] = prism.asSetter

}
