package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.Promotion
import com.agoda.protobuf.cache.Promotion.{Discount, Restriction}

case class OTAPromotionBuilder(build: Promotion) extends Builder[Promotion] {
  def withPromotionId(id: Option[Int]): B = build.copy(id = id)
  def withTypeId(typeId: Option[Int]): B = build.copy(typeId = typeId)
  def withCmsTypeId(cmsTypeId: Option[Int]): B = build.copy(cmsTypeId = cmsTypeId)
  def withDiscountTypeId(discountTypeId: Option[Int]): B = build.copy(discountTypeId = discountTypeId)
  def withCmsDiscountTypeId(cmsDiscountTypeId: Option[Int]): B = build.copy(cmsDiscountTypeId = cmsDiscountTypeId)
  def withRestriction(restriction: Option[Restriction]): B = build.copy(restriction = restriction)
  def withCancellationCode(code: Option[String]): B = build.copy(cancellationCode = code)
  def withDiscounts(discounts: Seq[Discount]): B = build.copy(discounts = discounts)
  def withIsStackable(stackable: Option[Boolean]): B = build.copy(isStackable = stackable)
  def withIsStackCombine(stackCombine: Option[Boolean]): B = build.copy(isStackCombine = stackCombine)
  def withStackableDiscountType(stackableDiscountType: Option[Int]): B =
    build.copy(stackableDiscountType = stackableDiscountType)
  def withIsApplyChannelDiscount(applyChannelDiscount: Option[Boolean]): B =
    build.copy(isApplyChannelDiscount = applyChannelDiscount)

  type B = OTAPromotionBuilder
}
