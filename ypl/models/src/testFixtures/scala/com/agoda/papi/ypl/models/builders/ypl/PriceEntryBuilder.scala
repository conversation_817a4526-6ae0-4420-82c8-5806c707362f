package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.room.{ChargeOption, ChargeType, RateType, SubChargeType}
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.PriceEntry
import com.agoda.papi.ypl.models.pricing.{BookingPriceBreakdown, DiscountMessage}
import org.joda.time.DateTime

case class PriceEntryBuilder(build: PriceEntry) extends Builder[PriceEntry] {
  def withChargeType(chargeType: ChargeType): B = build.copy(chargeType = chargeType)
  def withChargeOption(chargeOption: ChargeOption): B = build.copy(chargeOption = chargeOption)
  def withQuantity(value: Int): B = build.copy(quantity = value)
  def withValue(value: Double): B = build.copy(value = value)
  def withRateLoadedPrice(value: Double): B = build.copy(rateLoadedPrice = value)
  def withChannelDiscount(value: Double): B = build.copy(channelDiscount = value)
  def withPromoDiscount(value: Double): B = build.copy(promoDiscount = value)
  def withDiscountMessage(value: DiscountMessage): B =
    build.copy(discountMessages = build.getUpdatedDiscountMessages(value))
  def withOccupancy(value: Int): B = build.copy(occupancy = value)
  def withDate(value: DateTime): B = build.copy(date = value)
  def withPriceBreakdownHistory(history: BookingPriceBreakdown): B = build.copy(priceBreakdownHistory = history)
  def withSubChargeType(subChargeType: SubChargeType): B = build.copy(subChargeType = subChargeType)
  def withRoomNo(roomNo: Int): B = build.copy(roomNo = Some(roomNo))
  def withRoomNoOpt(roomNo: Option[Int]): B = build.copy(roomNo = roomNo)
  def withChildAgeRangeId(id: Long): B = build.copy(childAgeRangeId = Some(id))
  def withRateType(rateType: RateType): B = build.copy(rateType = rateType)
  def withMaxUspaDiscount(value: Double): B = build.copy(maxUspaDiscount = value)
  def withApplyTo(applyTo: String): B = build.copy(applyTo = applyTo)

  type B = PriceEntryBuilder
}
