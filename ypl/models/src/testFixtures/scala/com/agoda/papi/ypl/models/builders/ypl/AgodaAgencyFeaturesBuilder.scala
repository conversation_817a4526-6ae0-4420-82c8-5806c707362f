package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.hotel.{AgodaAgencyCommission, AgodaAgencyFeatures}

case class AgodaAgencyFeaturesBuilder(build: AgodaAgencyFeatures) extends Builder[AgodaAgencyFeatures] {

  def withIsAgodaAgencyEnable(isAgodaAgencyEnable: Boolean): B = build.copy(isAgodaAgencyEnable = isAgodaAgencyEnable)

  def withAgencyBookingType(agencyBookingType: Int): B = build.copy(agencyBookingType = agencyBookingType)

  def withCommissionList(commissionList: List[AgodaAgencyCommission]): B = build.copy(commissionList = commissionList)

  def withCommissionNoCcOffer(commissionNoCcOffer: Int): B = build.copy(commissionNoCcOffer = commissionNoCcOffer)

  type B = AgodaAgencyFeatures
}
