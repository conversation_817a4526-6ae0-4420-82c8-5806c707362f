package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.FireDrill

case class OTAFireDrillBuilder(build: FireDrill) extends Builder[FireDrill] {
  def withContractId(id: Int): B = build.copy(contractId = id)
  def withCommission(commission: Double): B = build.copy(commission = commission)
  def withActivateDate(activateDate: Long): B = build.copy(activateDate = activateDate)

  type B = OTAFireDrillBuilder
}
