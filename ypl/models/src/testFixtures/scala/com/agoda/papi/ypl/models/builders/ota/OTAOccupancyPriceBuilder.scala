package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice.{OccupancyPrice, TaxAndFee}
import com.agoda.protobuf.cache.ChannelRoomRate.Surcharge

case class OTAOccupancyPriceBuilder(build: OccupancyPrice) extends Builder[OccupancyPrice] {
  def withOccupancy(occupancy: Int): B = build.copy(occupancy = occupancy)
  def withAmount(amount: Double): B = build.copy(amount = amount)
  def withSurcharges(surcharges: Seq[Surcharge]): B = build.copy(surcharges = surcharges)
  def withTaxAndFee(taxAndFee: Seq[TaxAndFee]): B = build.copy(taxAndFee = taxAndFee)

  type B = OTAOccupancyPriceBuilder
}
