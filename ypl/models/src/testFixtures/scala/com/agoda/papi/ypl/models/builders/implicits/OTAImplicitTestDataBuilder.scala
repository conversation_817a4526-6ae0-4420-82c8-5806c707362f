package com.agoda.papi.ypl.models.builders.implicits

import com.agoda.papi.ypl.models.builders.ota._
import com.agoda.protobuf.cache.ChannelRoomRate.{RateCategory, Surcharge}
import com.agoda.protobuf.cache.ChannelRoomRate.Surcharge.SurchargeTax
import com.agoda.protobuf.cache._
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.BookingRestriction.{
  CustomerSegment => BookingCustomerSegment,
  PeriodInterval => RateCategoryBookingRestrictionPeriodInterval,
}
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice.{
  AdditionalOccupancyCharge,
  OccupancyPrice,
  TaxAndFee,
  Commission => DailyPriceCommision,
}
import com.agoda.protobuf.cache.Promotion.Restriction.{
  CustomerSegment => PromotionCustomerSegment,
  PeriodInterval => PromotionPeriodInterval,
}
import com.agoda.protobuf.cache.Promotion.{Discount, Restriction => PromotionRestriction}
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.{
  DailyPrice,
  SupplierRateInfo,
  BookingRestriction => RateCategoryBookingRestriction,
}

private[implicits] trait OTAImplicitTestDataBuilder {

  implicit def toOTAHotelPriceBuilder(build: HotelPrice): OTAHotelPriceBuilder = OTAHotelPriceBuilder(build)
  implicit def toOTAFiredrillBuilder(build: FireDrill): OTAFireDrillBuilder = OTAFireDrillBuilder(build)
  implicit def toOTARoomTypeBuilder(build: RoomType): OTARoomTypeBuilder = OTARoomTypeBuilder(build)
  implicit def toOTAPromotionCustomerSegmentBuilder(
    build: PromotionCustomerSegment): OTAPromotionCustomerSegmentBuilder = OTAPromotionCustomerSegmentBuilder(build)
  implicit def toOTADailyPriceBuilder(build: DailyPrice): OTADailyPriceBuilder = OTADailyPriceBuilder(build)
  implicit def toOTAChannelRoomRateBuilder(build: ChannelRoomRate): OTAChannelRoomRateBuilder =
    OTAChannelRoomRateBuilder(build)
  implicit def toOTARateCategoryBuilder(build: RateCategory): OTARateCategoryBuilder = OTARateCategoryBuilder(build)
  implicit def toOTADiscountBuilder(build: Discount): OTADiscountBuilder = OTADiscountBuilder(build)
  implicit def toOTAPromotionBuilder(build: Promotion): OTAPromotionBuilder = OTAPromotionBuilder(build)
  implicit def toOTAPromotionRestriction(build: PromotionRestriction): OTAPromotionRestrictionBuilder =
    OTAPromotionRestrictionBuilder(build)
  implicit def toOTAPromotionPeriodIntervalBuilder(build: PromotionPeriodInterval): OTAPromotionPeriodIntervalBuilder =
    OTAPromotionPeriodIntervalBuilder(build)
  implicit def toOTARateCategoryBookingRestrictionBuilder(
    build: RateCategoryBookingRestriction): OTARateCategoryBookingRestrictionBuilder =
    OTARateCategoryBookingRestrictionBuilder(build)
  implicit def toOTACommissionBuilder(build: DailyPriceCommision): OTACommissionBuilder = OTACommissionBuilder(build)
  implicit def toOTATaxAndFeeBuilder(build: TaxAndFee): OTATaxAndFeeBuilder = OTATaxAndFeeBuilder(build)
  implicit def toOTASurchargeTaxBuilder(build: SurchargeTax): OTASurchargeTaxBuilder = OTASurchargeTaxBuilder(build)
  implicit def toOTASurchargeBuilder(build: Surcharge): OTASurchargeBuilder = OTASurchargeBuilder(build)
  implicit def toOTAOccupancyPriceBuilder(build: OccupancyPrice): OTAOccupancyPriceBuilder =
    OTAOccupancyPriceBuilder(build)
  implicit def toOTABookingRestrictionCustomerSegmentBuilder(
    build: BookingCustomerSegment): OTABookingRestrictionCustomerSegmentBuilder =
    OTABookingRestrictionCustomerSegmentBuilder(build)
  implicit def toOTABookingRestrictionPeriodIntervalBuilder(
    build: RateCategoryBookingRestrictionPeriodInterval): OTABookingRestrictionPeriodIntervalBuilder =
    OTABookingRestrictionPeriodIntervalBuilder(build)
  implicit def toOTASupplierRateInfoBuilder(build: SupplierRateInfo): OTASupplierRateInfoBuilder =
    OTASupplierRateInfoBuilder(build)
  implicit def toOTAAdditionalOccupancyChargeBuilder(
    build: AdditionalOccupancyCharge): OTAAdditionalOccupancyChargeBuilder = OTAAdditionalOccupancyChargeBuilder(build)
  implicit def toOTARateRrepurposingDataBuilder(build: RateRepurposingData): OTARateRepurposingDataBuilder =
    OTARateRepurposingDataBuilder(build)
}
