package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.room.DiscountType
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.hotel.Promotion

case class PromotionBuilder(build: Promotion) extends Builder[Promotion] {
  def withId(id: Int): B = build.copy(id = id)
  def withType(typeId: Int): B = build.copy(typeId = typeId)
  def withDiscountType(dt: DiscountType = DiscountType.Other): B = build.copy(discountType = dt)
  def withMinRooms(mr: Int): B = build.copy(minRooms = mr)
  def withValue(value: Double): B = build.copy(value = value)
  def withStackable(stackable: Boolean): B = build.copy(isStackable = stackable)
  type B = PromotionBuilder
}
