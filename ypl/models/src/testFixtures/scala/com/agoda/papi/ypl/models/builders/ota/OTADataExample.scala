package com.agoda.papi.ypl.models.builders.ota

import com.agoda.finance.tax.models.{TaxPrototypeInfo, TaxPrototypeLevel}
import com.agoda.papi.enums.room.{BenefitTypes, ChargeOption}
import com.agoda.papi.pricing.pricecalculation.models.tax.Tax
import com.agoda.papi.ypl.models.YplCriteria
import com.agoda.papi.ypl.models.builders.BaseDataExample
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.BookingRestriction.{
  CustomerSegment => BookingCustomerSegment,
  PeriodInterval => RateCategoryBookingRestrictionPeriodInterval,
  PeriodType => RateCategoryBookingRestrictionPeriodType,
}
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice.{
  AdditionalOccupancyCharge,
  OccupancyPrice,
  ResellInfo,
  TaxAndFee,
  TaxAndFeeV2,
  Commission => DailyPriceCommision,
}
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.{
  CheckInInformation,
  DailyPrice,
  SupplierRateInfo,
  BookingRestriction => RateCategoryBookingRestriction,
}
import com.agoda.protobuf.cache.ChannelRoomRate.{RateCategory, Surcharge}
import com.agoda.protobuf.cache.Promotion.Restriction.{
  CustomerSegment => PromotionCustomerSegment,
  PeriodInterval => PromotionPeriodInterval,
  PeriodType => PromotionPeriodType,
}
import com.agoda.protobuf.cache.Promotion.{Discount, Restriction => PromotionRestriction}
import com.agoda.protobuf.cache._
import com.agoda.protobuf.common.{ApplyType => OTAApplyType, _}
import com.agoda.protobuf.cache.{
  TaxPrototypeLevel => ProtoTaxPrototypeLevel,
  TaxPrototypeLevelList => ProtoTaxPrototypeLevelList,
  TaxPrototypeLevelListV2 => ProtoTaxPrototypeLevelListV2,
  TaxPrototypeLevelMap => ProtoTaxPrototypeLevelMap,
  TaxPrototypeLevelV2 => ProtoTaxPrototypeLevelV2,
}
import com.agoda.protobuf.cache.TaxPrototypeLevelMap.{TaxPrototypeLevelMapEntry => ProtoTaxPrototypeLevelMapEntry}

private[builders] trait OTADataExample extends BaseDataExample {

  /* =================================
   *        PRIVATE CONSTRUCTORS
   * ================================= */

  private val occupancyModel = OccupanyModel.FullRate
  private val paymentModel = PaymentModel.Merchant
  private val taxType = TaxType.ComprehensiveTaxHotelLevel
  private val surchargeRateLoadType = RateType.NetExc
  private val supplierRoomId = Option("STD")
  private val supplierRatePlanId = Option("BAR")

  lazy val aValidOTAHotelPrice = HotelPrice(
    aValidHotelId,
    aValidCheckInMillis,
    aValidLos,
    aValidSupplierId,
    Option(aValidSupplierHotelId),
    occupancyModel,
    taxType,
    paymentModel,
    Option(surchargeRateLoadType),
    Seq(aValidOTAChannelRoomRate),
    Seq(aValidOTARoomType),
    isPromotionCombinable = Option(true),
  )
  val criteria = YplCriteria(aValidOTAHotelPrice.hotelId,
                             aValidOTAHotelPrice.supplierId,
                             aValidOTAHotelPrice.checkInMillis,
                             aValidOTAHotelPrice.lengthOfStay)
  lazy val aValidOTARoomType =
    RoomType(aValidRoomTypeId, 10, supplierRoomId = supplierRoomId, supplierRatePlanId = supplierRatePlanId)
  lazy val aValidOTAChannelRoomRate = ChannelRoomRate(aValidRoomTypeId,
                                                      1,
                                                      Some("USD"),
                                                      0.0,
                                                      Seq(aValidOTARateCategory),
                                                      supplierRoomId,
                                                      rateType = Option(RateType.NetExc),
  ) // this is channel discount rate type)
  lazy val aValidOTAStackChannelInfo = StackedChannelInfo(1, Seq(StackedChannelInfo.StackedChannelDiscount(1, 10.0)))
  lazy val aValidOTARateCategory = RateCategory(
    1,
    RateType.NetExc,
    Option("24D100P_100P"),
    8,
    None,
    Seq(aValidOTADailyPrice),
    Option(aValidOTASupplierRateInfo),
    Option(true),
    Option(true),
    None,
    Seq.empty,
    Option(false),
    Seq.empty,
    isRoomTypeNotGuaranteed = Option(false),
  )
  lazy val aValidCheckInInfo = CheckInInformation(Some(TimeOfDay(16, 0, 0)), None, Some(TimeOfDay(20, 0, 0)), None)
  lazy val aValidOTAPromotion = Promotion(Some(164554024),
                                          Some(1),
                                          Some(46310),
                                          Some(1),
                                          None,
                                          Some(aValidOTAPromotionRestriction),
                                          None,
                                          Seq(aValidOTADiscount),
                                          None,
                                          None,
                                          None,
                                          None)
  lazy val aValidOTAPromotionRestriction = PromotionRestriction(
    Some("1111111"),
    minNight = Some(1),
    minRooms = Some(1),
    customerSegments = Seq(aValidOTAPromotionCustomerSegment),
    bookingIntervals = Seq(aValidOTAPromotionRestrictionDayPeriodInterval),
  )
  lazy val aValidOTAPromotionRestrictionMinMaxNegative = PromotionRestriction(
    Some("1111111"),
    minAdvance = Some(-1),
    maxAdvance = Some(-1),
    minNight = Some(1),
    minRooms = Some(1),
    customerSegments = Seq(aValidOTAPromotionCustomerSegment),
    bookingIntervals = Seq(aValidOTAPromotionRestrictionDayPeriodInterval),
  )
  lazy val aValidOTAPromotionRestrictionDayPeriodInterval =
    PromotionPeriodInterval(PromotionPeriodType.Day, 1498214499000L, 253385750499000L)
  lazy val aValidOTAPromotionRestrictionTimePeriodInterval =
    PromotionPeriodInterval(PromotionPeriodType.Time, 1498214499000L, 1498214499000L)
  lazy val aValidOTAPromotionCustomerSegment = PromotionCustomerSegment(None, None)
  lazy val aValidOTARateCategoryBookingRestriction = RateCategoryBookingRestriction(Option("1111111"))
  lazy val aValidOTARateCategoryBookingRestrictionDayPeriodInterval = RateCategoryBookingRestrictionPeriodInterval(
    RateCategoryBookingRestrictionPeriodType.Day,
    1498214499000L,
    253385750499000L)
  lazy val aValidOTARateCategoryBookingRestrictionTimePeriodInterval = RateCategoryBookingRestrictionPeriodInterval(
    RateCategoryBookingRestrictionPeriodType.Time,
    1498214499000L,
    1498214499000L)
  lazy val aValidOTARateCategoryBookingRestrictionCustomerSegment = BookingCustomerSegment(None, None)
  lazy val aValidOTASupplierRateInfo = SupplierRateInfo(supplierRatePlanId, Option("14"))
  lazy val aValidOTADailyPrice = DailyPrice(aValidCheckInMillis, Seq(aValidOTAOccupancyPrice), Seq(aValidOTACommission))
  lazy val aValidOTACommission = DailyPriceCommision(channelID = 1, languageID = Some(0), 20.0, Some(0.0), Some(5.0))
  lazy val aValidOTAAdditionalOccupancyCharge =
    AdditionalOccupancyCharge("test", 10.0, Seq(aValidOTATaxAndFee), 1, AdditionalGuestType.ExtraBedCharge, true, false)
  lazy val aValidOTAOccupancyPrice =
    OccupancyPrice(1, 100.0, Seq(aValidOTASurcharge), Seq(aValidOTATaxAndFee, aValidOTAFeeTaxable))
  lazy val aValidOTAOccupancyPriceV2 = OccupancyPrice(2,
                                                      100.0,
                                                      Seq(aValidOTASurcharge),
                                                      Seq(aValidOTATaxAndFee, aValidOTAFeeTaxable),
                                                      None,
                                                      Seq(aValidOTATaxAndFeeV2, aValidOTAFeeTaxableV2))
  lazy val aValidResellInfo = ResellInfo(Some(15.0))
  lazy val aValidOTATaxAndFee = TaxAndFee(1, "PB", OTAApplyType.Mandatory, false, false, 10.0, false, None)
  lazy val aValidOTAFeeTaxable = TaxAndFee(2, "PB", OTAApplyType.Mandatory, true, false, 5.0, true, None)
  lazy val aValidOTATaxAndFeeV2Base = TaxAndFeeV2(1, "PB", OTAApplyType.Mandatory, false, false, 10.0, false, None)
  lazy val aValidOTATaxAndFeeV2 = TaxAndFeeV2(
    3,
    "PB",
    OTAApplyType.Mandatory,
    false,
    false,
    4.0,
    false,
    Some(52),
    Some(2),
    Some(1),
    Some(TaxLevelCalculationType.FlatRate),
    Some(TaxApplyBreakdownType.MarginCommission),
  )
  lazy val aValidOTAFeeTaxableV2 = TaxAndFeeV2(
    4,
    "PB",
    OTAApplyType.Mandatory,
    true,
    false,
    3.0,
    true,
    Some(53),
    Some(3),
    Some(1),
    Some(TaxLevelCalculationType.FlatRate),
    Some(TaxApplyBreakdownType.MarginCommission),
  )
  lazy val aValidOTASurcharge = Surcharge(1, "PB", OTAApplyType.Mandatory, false, 10.0, false)
  lazy val aValidOTADiscount = Discount(1545325200000L, 20.0)
  lazy val aValidOTAFireDrill = FireDrill(contractId = 1,
                                          commission = 30.0,
                                          activateDate = 1525280400000L, // Thursday, May 3, 2018 12:00:00 AM GMT+07:00
                                          isNewCommissionLogic = false)

  lazy val aValidProtoTax = Tax(id = 1,
                                applyTo = "PB",
                                isAmount = false,
                                isFee = false,
                                isTaxable = false,
                                value = 10d,
                                option = ChargeOption.Mandatory)
  lazy val aValidOTARateRepurposingData = RateRepurposingData(targetChannel = 1,
                                                              channelDiscount = 56.34,
                                                              referenceChannel = 2,
                                                              discountType = 5,
                                                              sourceChannel = 3,
                                                              minAdvPurchase = Some(-1),
                                                              maxAdvPurchase = Some(-1))
  lazy val aValidOTAAgencyNocc = AgencyNocc(agencyNoccMode = AgencyNoccMode.EnabledNoccToAllAgencyBookings)

  lazy val aValidOTAHotelPriceWithTaxLevel = HotelPrice(
    aValidHotelId,
    aValidCheckInMillis,
    aValidLos,
    aValidSupplierId,
    Option(aValidSupplierHotelId),
    occupancyModel,
    taxType,
    paymentModel,
    Option(surchargeRateLoadType),
    Seq(aValidOTAChannelRoomRateWithTaxLevel),
    Seq(aValidOTARoomType),
    isPromotionCombinable = Option(true),
    taxPrototypeLevelList = aValidOTATaxPrototypeLevelMap,
  )
  lazy val aValidOTAChannelRoomRateWithTaxLevel = ChannelRoomRate(aValidRoomTypeId,
                                                                  1,
                                                                  Some("USD"),
                                                                  0.0,
                                                                  Seq(aValidOTARateCategoryWithTaxLevel),
                                                                  supplierRoomId,
                                                                  rateType = Option(RateType.NetExc),
  ) // this is channel discount rate type)
  lazy val aValidOTARateCategoryWithTaxLevel = RateCategory(
    1,
    RateType.NetExc,
    Option("24D100P_100P"),
    8,
    None,
    Seq(aValidOTADailyPriceWithTaxLevel),
    Option(aValidOTASupplierRateInfo),
    Option(true),
    Option(true),
    None,
    Seq.empty,
    Option(false),
    Seq.empty,
    isRoomTypeNotGuaranteed = Option(false),
  )
  lazy val aValidOTADailyPriceWithTaxLevel =
    DailyPrice(aValidCheckInMillis, Seq(aValidOTAOccupancyPriceWithTaxLevel), Seq(aValidOTACommission))
  lazy val aValidOTAOccupancyPriceWithTaxLevel = OccupancyPrice(
    1,
    100.0,
    Seq(aValidOTASurcharge),
    Seq(aValidOTATaxAndFeeWithTaxLevel_111, aValidOTATaxAndFeeWithTaxLevel_222, aValidOTATaxAndFeeWithTaxLevel_333))
  lazy val aValidOTATaxAndFeeWithTaxLevel_111 =
    TaxAndFee(1, "PB", OTAApplyType.Mandatory, false, false, 0.0, false, Some(111))
  lazy val aValidOTATaxAndFeeWithTaxLevel_222 =
    TaxAndFee(2, "PB", OTAApplyType.Mandatory, false, false, 0.0, false, Some(222))
  lazy val aValidOTATaxAndFeeWithTaxLevel_333 =
    TaxAndFee(3, "PB", OTAApplyType.Mandatory, false, false, 0.0, false, Some(333))

  lazy val aValidOTATaxPrototypeLevel = ProtoTaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 1000, taxValue = 0)
  lazy val aValidOTATaxPrototypeLevelMapEntry = ProtoTaxPrototypeLevelMapEntry()

  lazy val aValidOTATaxPrototypeInfo_111 = TaxPrototypeInfo(
    taxPrototypeLevels = List(
      TaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 1000, taxValue = 10),
      TaxPrototypeLevel(level = 2, rateStart = 1000, rateEnd = 9999, taxValue = 20),
    ),
  )
  lazy val aValidOTATaxPrototypeInfo_111_v2 = TaxPrototypeInfo(
    taxPrototypeLevels = List(
      TaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 1000, taxValue = 10, isAmount = true),
      TaxPrototypeLevel(level = 2, rateStart = 1000, rateEnd = 9999, taxValue = 20, isAmount = false),
    ),
  )
  lazy val aValidOTATaxPrototypeInfo_222 = TaxPrototypeInfo(
    taxPrototypeLevels = List(
      TaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 9999, taxValue = 15),
    ),
  )
  lazy val aValidOTATaxPrototypeInfo_222_v2 = TaxPrototypeInfo(
    taxPrototypeLevels = List(
      TaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 9999, taxValue = 15, isAmount = true),
    ),
  )
  lazy val aValidOTATaxPrototypeLevelMap = Some(
    ProtoTaxPrototypeLevelMap(
      Map(
        111 -> ProtoTaxPrototypeLevelList(
          Seq[ProtoTaxPrototypeLevel](ProtoTaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 1000, taxValue = 10),
                                      ProtoTaxPrototypeLevel(level = 2, rateStart = 1000, rateEnd = 9999, taxValue = 20))),
        222 -> ProtoTaxPrototypeLevelList(
          Seq[ProtoTaxPrototypeLevel](ProtoTaxPrototypeLevel(level = 1, rateStart = 0, rateEnd = 9999, taxValue = 15))),
        333 -> ProtoTaxPrototypeLevelList(),
      ),
    ),
  )
  lazy val aValidOTATaxPrototypeLevelMapV2 = Some(
    ProtoTaxPrototypeLevelMap(
      taxPrototypeLevelMapV2 = Map(
        111 -> ProtoTaxPrototypeLevelListV2(
          Seq[ProtoTaxPrototypeLevelV2](
            ProtoTaxPrototypeLevelV2(level = 1, rateStart = 0, rateEnd = 1000, taxValue = 10, isAmount = Some(true)),
            ProtoTaxPrototypeLevelV2(level = 2, rateStart = 1000, rateEnd = 9999, taxValue = 20, isAmount = Some(false)),
          )),
        222 -> ProtoTaxPrototypeLevelListV2(
          Seq[ProtoTaxPrototypeLevelV2](
            ProtoTaxPrototypeLevelV2(level = 1, rateStart = 0, rateEnd = 9999, taxValue = 15, isAmount = Some(true)))),
        333 -> ProtoTaxPrototypeLevelListV2(),
      ),
    ),
  )
  lazy val aValidPropertyBenefitsMap = Map(
    1 -> Benefit(benefitId = 1, benefitType = BenefitTypes.Normal.i),
    95 -> Benefit(benefitId = 95, benefitType = BenefitTypes.Normal.i),
  )
}
