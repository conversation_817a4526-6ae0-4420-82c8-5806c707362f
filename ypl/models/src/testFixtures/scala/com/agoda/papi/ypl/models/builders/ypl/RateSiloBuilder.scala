package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.{Cor, HotelId, RateSilo, SupplierId}
import com.agoda.protobuf.cache.HotelPrice
import com.agoda.supply.calc.proto.PropertyOffer
import org.joda.time.DateTime

case class RateSiloBuilder(build: RateSilo) extends Builder[RateSilo] {
  def withPropOffer(po: PropertyOffer): B = build.copy(offers = Map(po.supplyInfo.get.supplierId -> po))
  def withHotelId(hotelId: HotelId): B = build.copy(hotelId = hotelId)
  def withCheckIn(checkIn: DateTime): B = build.copy(checkIn = checkIn)
  def withLengthOfStay(los: Int): B = build.copy(lengthOfStay = los)
  def withPrices(prices: Map[SupplierId, HotelPrice]): B = build.copy(prices = prices)
  def withCor(cor: Cor): B = build.copy(protoCor = cor)

  type B = RateSiloBuilder
}
