package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.ypl.commission.apm.models.{
  ApmCommissionReductionEligibilityHolder,
  ApmConfigHolder,
  MultipleAutoPriceMatchHolder,
}
import com.agoda.papi.ypl.commission.growth.program.GrowthProgramCommissionDetail
import com.agoda.papi.ypl.models.Wholesale.WholesaleMetaDataByRateFence
import com.agoda.papi.ypl.models.hotel.{AgePolicy, AgodaAgencyFeatures, SupplierCCMapping}
import com.agoda.papi.ypl.models.{
  AgencyNoCreditCard,
  ApmConfigTypeId,
  ChildAgeRange,
  EasyCancel,
  EnabledRoom,
  HmcMasterHotelContext,
  HmcSupplierHotelContext,
  HotelMeta,
  LanguageId,
  RateCategoryId,
  RoomTypeId,
  SupplierHotelMapping,
  WhitelabelID,
  YplRateFence,
}
import com.agoda.protobuf.hotelcontext.HotelContext
import com.agoda.protobuf.masterhotelcontext.MasterHotelContext
import monocle.Lens

object HotelMetaLens {
  type A = HotelMeta
  val hotelIdLens = Lens[A, Int](_.hotelId.toInt)(id => meta => meta.copy(hotelId = id))
  val augmentationLens = Lens[A, Boolean](_.augmentation)(aug => meta => meta.copy(augmentation = aug))
  val processingFeeOptionLens = Lens[A, Int](_.processingFeeOption)(pf => meta => meta.copy(processingFeeOption = pf))
  val agencyFeatureLens =
    Lens[A, Option[AgodaAgencyFeatures]](_.agencyFeatures)(feature => meta => meta.copy(agencyFeatures = feature))
  val gmtOffSetLens = Lens[A, Int](_.gmtOffset)(offset => meta => meta.copy(gmtOffset = offset))
  val supplierMappingLens =
    Lens[A, SupplierHotelMapping](_.supplierMappings)(mapping => meta => meta.copy(supplierMappings = mapping))
  val countryCodeLens = Lens[A, String](_.countryCode)(code => meta => meta.copy(countryCode = code))
  val countryIdLens = Lens[A, Long](_.countryId)(countryId => meta => meta.copy(countryId = countryId))
  val supplierCCMappingLens =
    Lens[A, Set[SupplierCCMapping]](_.suppliers)(suppliers => meta => meta.copy(suppliers = suppliers))
  val applyDiscountsMultiplicativelyLens = Lens[A, Boolean](_.applyDiscountsMultiplicatively)(apply =>
    meta => meta.copy(applyDiscountsMultiplicatively = apply))
  val agePolicyLens = Lens[A, AgePolicy](_.agePolicy)(agePolicy => meta => meta.copy(agePolicy = agePolicy))
  val countryCurrencyLens =
    Lens[A, Option[String]](_.countryCurrency)(currency => meta => meta.copy(countryCurrency = currency))
  val enabledRoomLens =
    Lens[A, Map[RoomTypeId, EnabledRoom]](_.enabledRoom)(mapping => meta => meta.copy(enabledRoom = mapping))
  val easyCencelLens =
    Lens[A, Option[EasyCancel]](_.easyCancel)(easyCancel => meta => meta.copy(easyCancel = easyCancel))
  val gmtOffSetMinutesLens = Lens[A, Int](_.gmtOffsetMinutes)(offset => meta => meta.copy(gmtOffsetMinutes = offset))
  val chainIdLens = Lens[A, Int](_.chainId)(chainId => meta => meta.copy(chainId = chainId))
  val multiAutoPriceMatch = Lens[A, Seq[MultipleAutoPriceMatchHolder]](_.multipleAutoPriceMatch)(multiApm =>
    meta => meta.copy(multipleAutoPriceMatch = multiApm))
  val ratecategoryWlMappingLens = Lens[A, Map[RateCategoryId, Set[WhitelabelID]]](_.ratecategoryWlMapping)(rcWl =>
    meta => meta.copy(ratecategoryWlMapping = rcWl))
  val childAgeRangesLens =
    Lens[A, Seq[ChildAgeRange]](_.childAgeRanges)(childAgeRanges => meta => meta.copy(childAgeRanges = childAgeRanges))
  val agencyNoCreditCardSettingLens =
    Lens[A, Seq[AgencyNoCreditCard]](_.agencyNoCreditCardSetting)(agencyNoCreditCardSetting =>
      meta => meta.copy(agencyNoCreditCardSetting = agencyNoCreditCardSetting))
  val apmLeadingRoomAdjustmentIdLens = Lens[A, Seq[Int]](_.apmLeadingRoomAdjustmentIds)(leadingIds =>
    meta => meta.copy(apmLeadingRoomAdjustmentIds = leadingIds))
  val isStackableV2EnabledLens = Lens[A, Boolean](_.isStackableV2Enabled)(isStackableV2Enabled =>
    meta => meta.copy(isStackableV2Enabled = isStackableV2Enabled))
  val jasoRateCategoryLanguageLens =
    Lens[A, Map[RateCategoryId, Set[LanguageId]]](_.jasoRateCategoryLanguage)(jasoRCL =>
      meta => meta.copy(jasoRateCategoryLanguage = jasoRCL))
  val apmConfigLens =
    Lens[A, Map[ApmConfigTypeId, ApmConfigHolder]](_.apmConfigs)(configs => meta => meta.copy(apmConfigs = configs))
  val isSingleRoomLens = Lens[A, Boolean](_.isSingleRoomNHA)(isSNha => meta => meta.copy(isSingleRoomNHA = isSNha))
  val wholesaleMetaDataByRateFenceLens: Lens[A, WholesaleMetaDataByRateFence] =
    Lens[A, WholesaleMetaDataByRateFence](_.wholesaleMetaDataByRateFence)(configs =>
      meta => meta.copy(wholesaleMetaDataByRateFence = configs))
  val restrictedRatecategoryIds = Lens[A, Set[Int]](_.restrictedRatecategoryIds)(restrictedRatecategoryIds =>
    meta => meta.copy(restrictedRatecategoryIds = restrictedRatecategoryIds))
  val stateIdLens = Lens[A, Option[Int]](_.stateId)(stateId => meta => meta.copy(stateId = stateId))
  val growthProgramCommissionLens =
    Lens[A, Map[YplRateFence, Seq[GrowthProgramCommissionDetail]]](_.applicableGrowthPrograms)(growthPrograms =>
      meta => meta.copy(applicableGrowthPrograms = growthPrograms))
  val apmCommissionReductionEligibilityLens =
    Lens[A, Seq[ApmCommissionReductionEligibilityHolder]](_.apmCommissionReductionEligibility)(commReducEligible =>
      meta => meta.copy(apmCommissionReductionEligibility = commReducEligible))
  val isChannelManagedLens =
    Lens[A, Boolean](_.isChannelManaged)(isChannelManaged => meta => meta.copy(isChannelManaged = isChannelManaged))
  val hmcSupplierHotelContextLens =
    Lens[A, HotelContext](meta => meta.hmcSupplierHotelContext.supplierHotelContext)(hmcSupplierHotelContext =>
      meta => meta.copy(hmcSupplierHotelContext = HmcSupplierHotelContext(hmcSupplierHotelContext)))
  val hmcMasterHotelContextLens =
    Lens[A, MasterHotelContext](meta => meta.hmcMasterHotelContext.masterHotelContext)(hmcMasterHotelContext =>
      meta => meta.copy(hmcMasterHotelContext = HmcMasterHotelContext(hmcMasterHotelContext)))
}
