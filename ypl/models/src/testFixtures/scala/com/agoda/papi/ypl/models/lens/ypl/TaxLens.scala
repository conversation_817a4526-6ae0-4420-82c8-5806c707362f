package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.pricing.pricecalculation.models.tax.Tax
import monocle.Lens

object TaxLens {
  type A = Tax
  val idLens = Lens[A, Int](_.id)(id => tax => tax.copy(id = id))
  val valueLens = Lens[A, Double](_.value)(value => tax => tax.copy(value = value))
  val isAmountLens = Lens[A, Boolean](_.isAmount)(isAmount => tax => tax.copy(isAmount = isAmount))
  val applyToLens = Lens[A, String](_.applyTo)(applyTo => tax => tax.copy(applyTo = applyTo))
  val isFeeLens = Lens[A, Boolean](_.isFee)(isFee => tax => tax.copy(isFee = isFee))
}
