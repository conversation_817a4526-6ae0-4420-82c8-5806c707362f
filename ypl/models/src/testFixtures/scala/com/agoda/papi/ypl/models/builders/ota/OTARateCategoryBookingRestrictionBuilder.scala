package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.BookingRestriction
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.BookingRestriction.{CustomerSegment, PeriodInterval}

case class OTARateCategoryBookingRestrictionBuilder(build: BookingRestriction) extends Builder[BookingRestriction] {
  def withBookOn(bookon: Option[String]): B = build.copy(bookOn = bookon)
  def withMinAdvance(minAdv: Option[Int]): B = build.copy(minAdvance = minAdv)
  def withMaxAdvance(maxAdv: Option[Int]): B = build.copy(maxAdvance = maxAdv)
  def withCustomerSegments(customerSegment: Seq[CustomerSegment]): B = build.copy(customerSegments = customerSegment)
  def withBookingIntervals(bookingIntervals: Seq[PeriodInterval]): B = build.copy(bookingIntervals = bookingIntervals)

  type B = OTARateCategoryBookingRestrictionBuilder
}
