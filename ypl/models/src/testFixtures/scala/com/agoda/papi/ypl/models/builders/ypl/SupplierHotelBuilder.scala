package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.SupplierId
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.hotel.SupplierHotel

case class SupplierHotelBuilder(build: SupplierHotel) extends Builder[SupplierHotel] {
  def withSupplierId(id: SupplierId): B = build.copy(dmcId = id)
  def withSupplierHotelId(id: String): B = build.copy(dmcHotelId = id)
  def withSupplierDmcHotelId(dmcHotelId: String): B = build.copy(dmcHotelId = dmcHotelId)

  type B = SupplierHotel
}
