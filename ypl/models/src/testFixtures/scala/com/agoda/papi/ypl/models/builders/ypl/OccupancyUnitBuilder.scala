package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.room.{ChargeType, SubChargeType}
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.enums.PayingAgeType
import com.agoda.papi.ypl.models.occupancy.OccupancyUnit

case class OccupancyUnitBuilder(build: OccupancyUnit) extends Builder[OccupancyUnit] {
  def withRoomNo(roomNo: Int): B = build.copy(roomNo = roomNo)
  def withChargeType(chargeType: ChargeType): B = build.copy(chargeType = chargeType)
  def withSubChargeType(subChargeType: SubChargeType): B = build.copy(subChargeType = subChargeType)
  def withChildAge(childAge: Option[Int]): B = build.copy(childAge = childAge)
  def withChildAgeRangeId(childAgeRangeId: Option[Int]): B = build.copy(childAgeRangeId = childAgeRangeId)
  def withPayingAgeType(payingAgeType: PayingAgeType): B = build.copy(payingAgeType = payingAgeType)
  def withQty(qty: Int): B = build.copy(qty = qty)
  type B = OccupancyUnitBuilder
}
