package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.hotel.Benefit
import com.agoda.papi.ypl.models.builders.Builder

case class BenefitBuilder(build: Benefit) extends Builder[Benefit] {
  def withBenefitId(id: Int): B = build.copy(id = id)
  def withGroupId(id: Int): B = build.copy(groupId = id)
  def withValue(value: Double): B = build.copy(value = value)

  type B = BenefitBuilder
}
