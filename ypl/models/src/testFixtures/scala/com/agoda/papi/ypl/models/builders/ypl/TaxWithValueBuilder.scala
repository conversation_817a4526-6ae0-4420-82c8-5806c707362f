package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.pricing.pricecalculation.models.tax.{Tax, TaxWithValue}
import com.agoda.papi.ypl.models.builders.Builder

case class TaxWithValueBuilder(build: TaxWithValue) extends Builder[TaxWithValue] {
  def withTax(value: Tax): B = build.copy(tax = value)
  def withTaxValue(value: Double): B = build.copy(taxValue = value)
  type B = TaxWithValueBuilder
}
