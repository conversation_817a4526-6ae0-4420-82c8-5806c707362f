package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.commission.apm.models.{
  ApmCommissionReductionEligibilityHolder,
  ApmConfigHolder,
  MultipleAutoPriceMatchHolder,
}
import com.agoda.papi.ypl.models.Wholesale.WholesaleMetaDataByRateFence
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.hotel.{AgePolicy, AgodaAgencyFeatures, SupplierCCMapping}
import com.agoda.papi.ypl.models.lens.ypl.HotelMetaLens
import com.agoda.papi.ypl.models.{
  AgencyNoCreditCard,
  ApmConfigTypeId,
  ChildAgeRange,
  EasyCancel,
  EnabledRoom,
  HotelId,
  HotelMeta,
  RateCategoryId,
  RoomTypeId,
  SupplierHotelMapping,
  SupplierId,
  WhitelabelID,
}
import com.agoda.protobuf.hotelcontext.HotelContext
import com.agoda.protobuf.masterhotelcontext.MasterHotelContext

case class HotelMetaBuilder(build: HotelMeta) extends Builder[HotelMeta] {
  def withAugmentation(aug: Boolean = true): B = HotelMetaLens.augmentationLens.set(aug)(build)
  def withProcessingFeeOption(pf: Int = 10): B = HotelMetaLens.processingFeeOptionLens.set(pf)(build)
  def withHotelId(id: HotelId): B = HotelMetaLens.hotelIdLens.set(id.toInt)(build)
  def withSuppliers(suppliers: SupplierId*): B = build.withSuppliersCCMapping(suppliers.map(SupplierCCMapping(_)).toSet)
  def withSuppliersCCMapping(suppliers: Set[SupplierCCMapping]): B =
    HotelMetaLens.supplierCCMappingLens.set(suppliers)(build)
  def withApplyDiscountsMultiplicatively(applyDiscountsMultiplicatively: Boolean): B =
    HotelMetaLens.applyDiscountsMultiplicativelyLens.set(applyDiscountsMultiplicatively)(build)
  def withAgodaAgency(isAgodaAgencyEnable: Boolean): B =
    build.withAgodaAgencyFeatures(Some(AgodaAgencyFeatures(isAgodaAgencyEnable = isAgodaAgencyEnable, 1, Nil, 0)))
  def withAgodaAgencyFeatures(features: Option[AgodaAgencyFeatures]): B =
    HotelMetaLens.agencyFeatureLens.set(features)(build)
  def withGmtOffset(offset: Int): B = HotelMetaLens.gmtOffSetLens.set(offset)(build)
  def withCountryCode(code: String): B = HotelMetaLens.countryCodeLens.set(code)(build)
  def withCountryId(countryId: Long): B = HotelMetaLens.countryIdLens.set(countryId)(build)
  def withSupplierMapping(mapping: SupplierHotelMapping): B = HotelMetaLens.supplierMappingLens.set(mapping)(build)
  def withAgePolicy(agePolicy: AgePolicy): B = HotelMetaLens.agePolicyLens.set(agePolicy)(build)
  def withCountryCurrency(currency: Option[String]): B = HotelMetaLens.countryCurrencyLens.set(currency)(build)
  def withEnabledRoom(mapping: Map[RoomTypeId, EnabledRoom]): B = HotelMetaLens.enabledRoomLens.set(mapping)(build)
  def withEasyCancel(easyCancel: Option[EasyCancel]): B = HotelMetaLens.easyCencelLens.set(easyCancel)(build)
  def withRatecategoryWlMapping(rcWl: Map[RateCategoryId, Set[WhitelabelID]]): B =
    HotelMetaLens.ratecategoryWlMappingLens.set(rcWl)(build)
  def withChildAgeRanges(childAgeRanges: Seq[ChildAgeRange]): B =
    HotelMetaLens.childAgeRangesLens.set(childAgeRanges)(build)
  def withAgencyNoCreditCardSetting(agencyNoCreditCardSetting: Seq[AgencyNoCreditCard]): B =
    HotelMetaLens.agencyNoCreditCardSettingLens.set(agencyNoCreditCardSetting)(build)
  def clearSupplierMapping: B = HotelMetaLens.supplierMappingLens.set(Map.empty)(build)
  def clearEnabledRoomMapping: B = HotelMetaLens.enabledRoomLens.set(Map.empty)(build)
  def clearAgodaAgencyFeatures: B = HotelMetaLens.agencyFeatureLens.set(None)(build)
  def clearEasyCancel: B = HotelMetaLens.easyCencelLens.set(None)(build)
  def clearWholesaleMetaDataByRateFence: B = HotelMetaLens.wholesaleMetaDataByRateFenceLens.set(Map.empty)(build)
  def withGmtOffsetMinutes(offset: Int): B = HotelMetaLens.gmtOffSetMinutesLens.set(offset)(build)
  def withApmLeadingRoomAdjustmentIds(apmLeadingRoomAdjustmentIds: Seq[Int]): B =
    HotelMetaLens.apmLeadingRoomAdjustmentIdLens.set(apmLeadingRoomAdjustmentIds)(build)
  def withIsStackableV2Enabled(isStackableV2Enabled: Boolean): B =
    HotelMetaLens.isStackableV2EnabledLens.set(isStackableV2Enabled)(build)
  def withMultipleAutoPriceMatch(multiAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder]): B =
    HotelMetaLens.multiAutoPriceMatch.set(multiAutoPriceMatch)(build)
  def withJASORateCategoryLanguage(jasoRCL: Map[RateCategoryId, Set[WhitelabelID]]): B =
    HotelMetaLens.jasoRateCategoryLanguageLens.set(jasoRCL)(build)
  def withApmConfigs(apmConfigs: Map[ApmConfigTypeId, ApmConfigHolder]): B =
    HotelMetaLens.apmConfigLens.set(apmConfigs)(build)
  def withWholesaleMetaDataByRateFence(wholesaleMetaDataByRateFence: WholesaleMetaDataByRateFence): B =
    HotelMetaLens.wholesaleMetaDataByRateFenceLens.set(wholesaleMetaDataByRateFence)(build)
  def withIsSingleRoomNHA(isSingleRoomNHA: Boolean): B = HotelMetaLens.isSingleRoomLens.set(isSingleRoomNHA)(build)
  def withRestrictedRatecategoryIds(restrictedRatecategoryIds: Set[Int]): B =
    HotelMetaLens.restrictedRatecategoryIds.set(restrictedRatecategoryIds)(build)
  def withStateId(stateId: Int): B = HotelMetaLens.stateIdLens.set(Some(stateId))(build)
  def withCommissionReductionEligibility(commReducEligible: Seq[ApmCommissionReductionEligibilityHolder]): B =
    HotelMetaLens.apmCommissionReductionEligibilityLens.set(commReducEligible)(build)
  def withIsChannelManaged(isChannelManaged: Boolean): B =
    HotelMetaLens.isChannelManagedLens.set(isChannelManaged)(build)
  def withHMCChildHotelContext(ctx: HotelContext): B = HotelMetaLens.hmcSupplierHotelContextLens.set(ctx)(build)
  def withHMCMasterHotelContext(ctx: MasterHotelContext): B = HotelMetaLens.hmcMasterHotelContextLens.set(ctx)(build)
  def withChainId(chainId: Int): B = HotelMetaLens.chainIdLens.set(chainId)(build)
  type B = HotelMetaBuilder
}
