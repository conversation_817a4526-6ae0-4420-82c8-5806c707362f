package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.Surcharge.SurchargeTax
import com.agoda.protobuf.common.ApplyType

case class OTASurchargeTaxBuilder(build: SurchargeTax) extends Builder[SurchargeTax] {
  def withTaxId(taxId: Int): B = build.copy(taxId = taxId)
  def withApplyTo(applyTo: String): B = build.copy(applyTo = applyTo)
  def withApplyType(applyType: ApplyType): B = build.copy(applyType = applyType)
  def withIsAmount(isAmount: Boolean): B = build.copy(isAmount = isAmount)
  def withValue(value: Double): B = build.copy(value = value)
  type B = OTASurchargeTaxBuilder
}
