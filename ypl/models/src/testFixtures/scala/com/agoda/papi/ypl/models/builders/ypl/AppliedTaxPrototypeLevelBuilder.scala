package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.finance.tax.models.{AppliedTaxPrototypeLevel, TaxPrototypeLevel}
import com.agoda.papi.ypl.models.builders.Builder

case class AppliedTaxPrototypeLevelBuilder(build: AppliedTaxPrototypeLevel) extends Builder[AppliedTaxPrototypeLevel] {
  def withAppliedTaxPrototypeLevels(level: TaxPrototypeLevel): B = build.copy(taxPrototypeLevel = level)
  def withActualTaxAmount(amount: Double): B = build.copy(actualTaxAmount = amount)

  type B = AppliedTaxPrototypeLevel
}
