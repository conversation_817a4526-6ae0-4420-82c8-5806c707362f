package com.agoda.papi.ypl.models.lens.ota

import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.{BookingRestriction, DailyPrice}
import com.agoda.protobuf.common.RateType
import monocle.Lens

object RateCategoryLens {
  type A = RateCategory

  val rateCategoryIdLens = Lens[A, Int](_.rateCategoryId)(id => rateCategory => rateCategory.copy(rateCategoryId = id))
  val rateTypeLoadLens =
    Lens[A, RateType](_.rateTypeLoaded)(rateTypeLoad => rateCategory => rateCategory.copy(rateTypeLoaded = rateTypeLoad))
  val cancellationCodeLens =
    Lens[A, Option[String]](_.cancellationCode)(code => rateCategory => rateCategory.copy(cancellationCode = code))
  val remaningRoomLens = Lens[A, Int](_.remainingRoom)(rooms => rateCategory => rateCategory.copy(remainingRoom = rooms))
  val bookingRestrictionLens = Lens[A, Option[BookingRestriction]](_.bookingRestriction)(restriction =>
    rateCategory => rateCategory.copy(bookingRestriction = restriction))
  val dailyPricesListLens =
    Lens[A, Seq[DailyPrice]](_.dailyPrices)(daily => rateCategory => rateCategory.copy(dailyPrices = daily))

}
