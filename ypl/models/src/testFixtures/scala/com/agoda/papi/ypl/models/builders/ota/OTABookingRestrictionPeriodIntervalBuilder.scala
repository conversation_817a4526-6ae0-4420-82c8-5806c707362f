package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.BookingRestriction.{PeriodInterval, PeriodType}

case class OTABookingRestrictionPeriodIntervalBuilder(build: PeriodInterval) extends Builder[PeriodInterval] {
  def withPeriodType(periodType: PeriodType): B = build.copy(periodType = periodType)
  def withFrom(from: Long): B = build.copy(from = from)
  def withTo(to: Long): B = build.copy(to = to)

  type B = OTABookingRestrictionPeriodIntervalBuilder
}
