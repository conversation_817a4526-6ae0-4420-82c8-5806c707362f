package com.agoda.papi.ypl.models.lens.ota

import com.agoda.protobuf.cache.ChannelRoomRate
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory
import monocle.Lens

object ChannelRoomRateLens {
  type A = ChannelRoomRate
  val roomTypeIdLens = Lens[A, Long](_.roomTypeId)(id => room => room.copy(roomTypeId = id))
  val channelIdLens = Lens[A, Int](_.channelId)(channel => room => room.copy(channelId = channel))
  val currencyCodeLens = Lens[A, Option[String]](_.currencyCode)(currency => room => room.copy(currencyCode = currency))
  val processingFeeLens = Lens[A, Double](_.processingFee)(pf => room => room.copy(processingFee = pf))
  val rateCategoriesListLens =
    Lens[A, Seq[RateCategory]](_.rateCategories)(ratecats => room => room.copy(rateCategories = ratecats))

}
