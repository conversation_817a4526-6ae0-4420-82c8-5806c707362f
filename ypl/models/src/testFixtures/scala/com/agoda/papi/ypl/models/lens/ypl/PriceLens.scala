package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.pricing.pricecalculation.models.tax.{DailyTaxes, TaxWithValue}
import com.agoda.papi.ypl.models.pricing.YplPrice
import monocle.Lens

object PriceLens {
  type A = YplPrice
  val dailyTaxLens = Lens[A, DailyTaxes](_.dailyTaxes)(dailyTaxes => prices => prices.copy(dailyTaxes = dailyTaxes))
  val taxWithValuesLens = Lens[A, List[TaxWithValue]](_.dailyTaxes.taxes)(taxWithValues =>
    prices => prices.copy(dailyTaxes = DailyTaxes(taxWithValues, isCleanedUpHospitalityTax = true)))
}
