package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.ypl.models.hotel.{Benefit, RateCategory}
import monocle.{Lens, Optional}

object RateCategoryLens {
  type A = RateCategory

  val rateCategoryIdLens = Lens[A, Int](_.id)(id => ratecategory => ratecategory.copy(id = id))
  val benefitLens =
    Lens[A, List[Benefit]](_.benefits)(benefits => ratecategory => ratecategory.copy(benefits = benefits))
  val isRoomTypeNotGuaranteeLens = Lens[A, Boolean](_.isRoomTypeNotGuarantee)(isroomtypenotguarantee =>
    ratecategory => ratecategory.copy(isRoomTypeNotGuarantee = isroomtypenotguarantee))
  val promotionTypeIdLens = Optional[A, Int](_.promotionTypeId)(promotiontypeid =>
    ratecategory => ratecategory.copy(promotionTypeId = Some(promotiontypeid)))
  val promotionTypeCmsIdLens = Optional[A, Int](_.promotionTypeCmsId)(promotiontypecmsid =>
    ratecategory => ratecategory.copy(promotionTypeCmsId = Some(promotiontypecmsid)))

}
