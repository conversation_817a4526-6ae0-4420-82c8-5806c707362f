package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.ypl.models.lens._
import com.agoda.papi.ypl.models.{RateSilo, SupplierId}
import com.agoda.protobuf.cache.HotelPrice
import monocle.{Lens, Setter}
import org.joda.time.DateTime

object RateSiloLens {
  type A = RateSilo
  val checkInLens = Lens[A, DateTime](_.checkIn) { checkIn => silo =>
    silo.copy(checkIn = checkIn, prices = silo.prices.mapValues(hp => hp.withCheckInMillis(checkIn.getMillis)))
  }
  val losLens = Lens[A, Int](_.lengthOfStay) { los => silo =>
    silo.copy(
      lengthOfStay = los,
      prices = silo.prices.mapValues(hp => hp.withLengthOfStay(los)),
    )
  }
  val hotelPriceLens =
    Lens[A, Map[SupplierId, HotelPrice]](_.prices)(hotelPrices => silo => silo.copy(prices = hotelPrices))

  val hotelIdSetter: Setter[A, Int] = {
    val outerIdLens = Lens[A, Int](_.hotelId.toInt)(id => silo => silo.copy(hotelId = id))
    SetterChain(outerIdLens)
  }
}
