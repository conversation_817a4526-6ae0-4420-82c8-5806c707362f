package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.enums.request.FeatureFlag
import com.agoda.papi.ypl.commission.apm.models.ApmSettingHolder
import com.agoda.papi.ypl.fencing.AgxCommissionFencing
import com.agoda.papi.ypl.models.api.request._
import com.agoda.papi.ypl.models.settings.CommonTaxSettings
import com.agoda.papi.ypl.models.{
  SupplierFeatures,
  SupplierFundedDiscountSetting,
  WhitelabelSetting,
  YplChannel,
  YplExperiments,
  YplRateFence,
  YplRequest,
}
import monocle.Lens
import org.joda.time.DateTime

object YPLRequestLens {
  type A = YplRequest
  val occInfoLens = Lens[A, YplOccInfo](_.occ)(occ => request => request.copy(occ = occ))
  val channelLens = Lens[A, Set[YplChannel]](_.channels)(chs => request => request.copy(channels = chs))
  val checkInLens = Lens[A, DateTime](_.checkIn)(checkin => request => request.copy(checkIn = checkin))
  val supplierFeaturesLens = Lens[A, SupplierFeatures](_.supplierFeatures)(supplierFeatures =>
    request => request.copy(supplierFeatures = supplierFeatures))
  val featuresLens = supplierFeaturesLens.composeLens(FeaturesLens.featureslens)
  val checkOutLens = Lens[A, DateTime](_.checkOut)(checkout => request => request.copy(checkOut = checkout))
  val losLens = Lens[A, Int](_.lengthOfStay)(los => request => request.copy(checkOut = request.checkIn.plusDays(los)))
  val bookingDateLens =
    Lens[A, DateTime](_.bookingDate)(bookingDate => request => request.copy(bookingDate = bookingDate))
  val experimentLens = Lens[A, YplExperiments](_.experiments)(exps => request => request.copy(experiments = exps))
  val agxCommissionAdjustment = Lens[A, AgxCommissionFencing](_.agxCommissionAdjustmentFences)(agxFencing =>
    request => request.copy(agxCommissionAdjustmentFences = agxFencing))
  val flagInfoLens = Lens[A, YplFlagInfo](_.flagInfo)(flag => request => request.copy(flagInfo = flag))
  val isSSRLens = Lens[A, Boolean](_.isSSR)(isSSR => request => request.copy(isSSR = isSSR))
  val isCheapestRoomOnlyLens =
    Lens[A, Boolean](_.isCheapestRoomOnly)(cheapestOnly => request => request.copy(isCheapestRoomOnly = cheapestOnly))
  val clientInfoLens = Lens[A, YplClientInfo](_.cInfo)(cInfo => request => request.copy(cInfo = cInfo))
  val featureFlagLens =
    Lens[A, Set[FeatureFlag]](_.featureFlags)(featureFlags => request => request.copy(featureFlags = featureFlags))
  val whileLabelIdsLens = Lens[A, Int](_.whitelabelSetting.whitelabelID)(wlId =>
    request =>
      request.copy(whitelabelSetting = WhitelabelSetting(
        whitelabelID = wlId,
        defaultDMCSellability = Map.empty,
        isAdjustCommission = false,
        isOverrideCommission = false,
        isAdjustMargin = false,
        paymentChannels = Nil,
        logInInventoryTypeList = Nil,
        isBreakfastAndDinnerIncludeEnable = false,
        isCustomerSegmentValidationEnabled = false,
        externalVipDisplayConfigs = List.empty,
        isAdjustCommissionFromHotelContract = false,
        blockYCSPromotions = false,
        paymentInventoryTypeConfigurations = List.empty,
      )))
  val adultLens = occInfoLens.composeLens(OccInfoLens.occInfoAdultLens)
  val childrenLens = occInfoLens.composeLens(OccInfoLens.occInfoChildrenLens)
  val roomAssignmentLens = occInfoLens.composeLens(OccInfoLens.occInfoRoomAssignmentLens)
  val roomLens = occInfoLens.composeLens(OccInfoLens.occInfoRoomLens)
  val isAllOccLens = flagInfoLens.composeLens(FlagInfoLens.isAllOccLens)
  val languageLens = clientInfoLens.composeLens(YPLClientInfoLens.languageLens)
  val filterAPOLens = flagInfoLens.composeLens(FlagInfoLens.filterApoLens)
  val fencesLens =
    Lens[A, Map[YplChannel, Set[YplRateFence]]](_.fences)(fences => request => request.copy(fences = fences))
  val apmSettingLens =
    Lens[A, Option[ApmSettingHolder]](_.apmSetting)(apmSetting => request => request.copy(apmSetting = apmSetting))
  val blockPromotionsLens = Lens[A, Boolean](_.whitelabelSetting.blockYCSPromotions)(blockYCSPromotions =>
    request =>
      request.copy(whitelabelSetting = WhitelabelSetting(
        whitelabelID = 0,
        defaultDMCSellability = Map.empty,
        isAdjustCommission = false,
        isOverrideCommission = false,
        isAdjustMargin = false,
        paymentChannels = Nil,
        logInInventoryTypeList = Nil,
        isBreakfastAndDinnerIncludeEnable = false,
        isCustomerSegmentValidationEnabled = false,
        externalVipDisplayConfigs = List.empty,
        isAdjustCommissionFromHotelContract = false,
        blockYCSPromotions = blockYCSPromotions,
        paymentInventoryTypeConfigurations = List.empty,
        iSellingExternalSuppliersForJtbEnabled = false,
        externalSuppliersConfig = Map.empty,
      )))
  val isBookingRequestLens = Lens[A, Boolean](_.isBookingRequest)(isBookingRequest =>
    request => request.copy(isBookingRequest = isBookingRequest))
  val usTaxV2SettingsLens = Lens[A, Option[CommonTaxSettings]](_.commonTaxSettingsOpt)(usTaxV2Settings =>
    request => request.copy(commonTaxSettingsOpt = usTaxV2Settings))

  val storeFrontIdsLens = Lens[A, Int](_.cInfo.storeFront.getOrElse(0))(sfId =>
    request => request.copy(cInfo = request.cInfo.copy(storeFront = Some(sfId))))

  val supplierFundedDiscountEnabledSuppliersLens =
    Lens[A, SupplierFundedDiscountSetting](_.supplierFundedDiscountSetting)(supplierFundedDiscountSetting =>
      request => request.copy(supplierFundedDiscountSetting = supplierFundedDiscountSetting))

  val isApplyNewOccupancyLogicExpLens = Lens[A, Boolean](_.isApplyNewOccupancyLogic)(isApplyNewOccupancyLogic =>
    request => request.copy(isApplyNewOccupancyLogic = isApplyNewOccupancyLogic))
}
