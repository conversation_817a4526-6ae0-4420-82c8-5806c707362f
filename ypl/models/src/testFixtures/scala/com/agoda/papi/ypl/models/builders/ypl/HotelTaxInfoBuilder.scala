package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.hotel.TaxType
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto._

case class HotelTaxInfoBuilder(build: HotelTaxInfo) extends Builder[HotelTaxInfo] {
  def withTaxType(taxType: TaxType): B = build.copy(taxType = taxType)
  def withIsConfigProcessingFees(isConfigProcessingFees: Boolean): B =
    build.copy(isConfigProcessingFees = isConfigProcessingFees)
  type B = HotelTaxInfoBuilder
}
