package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.{YplChannel, YplDispatchChannels}

case class YplDispatchChannelsBuilder(build: YplDispatchChannels) extends Builder[YplDispatchChannels] {
  def withMasterChannels(masterChannels: Set[YplChannel]): B = build.copy(masterChannels = masterChannels)
  def withHelperChannels(helperChannels: Set[YplChannel]): B = build.copy(helperChannels = helperChannels)

  type B = YplDispatchChannelsBuilder
}
