package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.hotel.{DiscountInfo, Promotion}
import org.joda.time.LocalDate

case class DiscountInfoBuilder(build: DiscountInfo) extends Builder[DiscountInfo] {
  def withPromotion(p: Promotion): B = build.copy(promotion = Some(p))
  def withPromotionsBreakdown(pb: Map[LocalDate, List[Promotion]]): B = build.copy(promotionsBreakdown = pb)

  type B = DiscountInfoBuilder
}
