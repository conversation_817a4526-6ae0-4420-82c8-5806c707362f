package com.agoda.papi.ypl.models.builders
import com.agoda.papi.enums.hotel.WaiverType
import com.agoda.papi.cancellation.models.waiver.Waiver
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.YplMasterChannel
import com.agoda.utils.flow.{PropertyContext, PropertyContextImpl}
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat

//sth that shared betweeon ypl/ota models; ex. hotelId
private[builders] trait BaseDataExample {
  val aValidCheckIn = parseDateTime("2015-10-10")
  val aValidLos = 1
  val aValidHotelId = 6011203
  val aValidChainId = 12
  val aValidCountryId = 119
  val aValidCityId = 9395
  val aValidSupplierId = 27912
  val aValidSupplierHotelId = "29247"
  val aValidCheckInMillis = aValidCheckIn.getMillis //  1525798800000L //Wednesday, May 9, 2018 12:00:00 AM GMT+07:00
  val aValidRoomTypeId = 13193032
  val aValidYplChannel = YplMasterChannel(1)
  val aValidAPMYplChannel = YplMasterChannel(Channel.APM)
  val aValidCurrency = "THB"
  val aValidLanguageId = 1
  val aValidCxlCode = "365D100P_100P"
  val aValidFeeWaiver: Seq[Waiver] =
    Seq(Waiver(expiryDays = "24H", leadTime = "72H", feeValue = "0P", cmsId = 101, None, WaiverType.CountryGracePeriod))
  val aValidHotelGracePeriodFeeWaiver: Seq[Waiver] = Seq(
    Waiver(expiryDays = "24H", leadTime = "72H", feeValue = "0P", cmsId = 101, None, WaiverType.HotelSettingsGracePeriod))
  val aValidPropertyContext: PropertyContextImpl = PropertyContext(aValidHotelId, aValidCityId, aValidCountryId)

  protected def parseDateTime(date: String, format: String = "yyyy-MM-dd"): DateTime = {
    val jodaFormat = DateTimeFormat.forPattern(format)
    jodaFormat.parseDateTime(date)
  }

  implicit class ObjectHelper[T](obj: T) {
    def toOption(condition: => Boolean): Option[T] = if (condition) Some(obj) else None
  }
}
