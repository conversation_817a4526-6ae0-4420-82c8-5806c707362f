package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.LanguageId
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice.Commission

case class OTACommissionBuilder(build: Commission) extends Builder[Commission] {
  def withChannelId(channelId: Int): B = build.copy(channelID = channelId)
  def withLanguageId(languageId: Option[LanguageId]): B = build.copy(languageID = languageId)
  def withValue(value: Double): B = build.copy(value = value)

  type B = OTACommissionBuilder
}
