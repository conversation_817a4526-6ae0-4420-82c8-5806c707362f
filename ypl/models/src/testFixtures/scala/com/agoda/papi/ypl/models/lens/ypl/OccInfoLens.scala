package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.ypl.models.api.request.{YplChildren, YplOccFilter, YplOccInfo, YplRoomAssignment}
import com.agoda.papi.ypl.models.{Adults, Rooms}
import monocle.Lens

object OccInfoLens {
  type A = YplOccInfo
  val occInfoAdultLens = Lens[A, Adults](_.adults)(adult => occ => occ.copy(_adults = Some(adult)))
  val occInfoChildrenLens = Lens[A, Option[YplChildren]](_._children)(children => occ => occ.copy(_children = children))
  val occInfoRoomLens = Lens[A, Rooms](_.rooms)(room => occ => occ.copy(_rooms = Some(room)))
  val occInfoRoomAssignmentLens = Lens[A, List[YplRoomAssignment]](_.roomAssignment)(roomAssignment =>
    occ => occ.copy(roomAssignment = roomAssignment))
  val occInfoOccFilterLens =
    Lens[A, Option[YplOccFilter]](_.occFilter)(occFilter => occ => occ.copy(occFilter = occFilter))
}
