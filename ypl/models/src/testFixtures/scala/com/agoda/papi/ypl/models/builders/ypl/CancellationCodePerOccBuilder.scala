package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.CancellationCodePerOcc

case class CancellationCodePerOccBuilder(build: CancellationCodePerOcc) extends Builder[CancellationCodePerOcc] {
  def withMinOcc(minOcc: Int): B = build.copy(minOcc = minOcc)
  def withMaxOcc(maxOcc: Int): B = build.copy(maxOcc = maxOcc)
  def withCxlCode(cxlCode: String): B = build.copy(cxlCode = cxlCode)
  type B = CancellationCodePerOccBuilder
}
