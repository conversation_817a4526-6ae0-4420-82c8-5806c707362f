package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.FireDrillProto
import com.agoda.papi.ypl.models.builders.Builder
import org.joda.time.DateTime

case class FireDrillProtoBuilder(build: FireDrillProto) extends Builder[FireDrillProto] {
  def withContractId(contractId: Int): B = build.copy(contractId = contractId)

  def withCommission(commission: Double): B = build.copy(commission = commission)

  def withIsAgpLite(isAgpLite: Boolean): B = build.copy(isAgpLite = isAgpLite)

  def withActivateDate(activateDate: Option[DateTime]): B = build.copy(activateDate = activateDate)

  type B = FireDrillProtoBuilder
}
