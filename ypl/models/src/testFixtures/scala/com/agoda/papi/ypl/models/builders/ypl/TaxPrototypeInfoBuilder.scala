package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.finance.tax.models.{AppliedTaxPrototypeLevel, TaxPrototypeInfo, TaxPrototypeLevel}
import com.agoda.papi.ypl.models.builders.Builder

case class TaxPrototypeInfoBuilder(build: TaxPrototypeInfo) extends Builder[TaxPrototypeInfo] {
  def withTaxPrototypeLevels(levels: List[TaxPrototypeLevel]): B = build.copy(taxPrototypeLevels = levels)
  def withAppliedTaxPrototypeLevels(levels: List[AppliedTaxPrototypeLevel]): B =
    build.copy(appliedTaxPrototypeLevels = levels)

  type B = TaxPrototypeInfoBuilder
}
