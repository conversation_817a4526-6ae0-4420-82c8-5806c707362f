package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.enums.hotel.{PaymentModel, RateModel}
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.lens.ypl.HotelLens
import com.agoda.papi.ypl.models.pricing.YplSupplierStats
import com.agoda.papi.ypl.models.pricing.proto.HotelTaxInfo
import com.agoda.papi.ypl.models.proto.enums.SourceType
import com.agoda.papi.ypl.models.{
  FireDrillProto,
  HotelFeatures,
  SupplierId,
  SupplierInfo,
  TaxMetaData,
  YPLHotel,
  YPLRoom,
  YplReqOccByHotelAgePolicy,
}
import org.joda.time.LocalTime

case class HotelBuilder(build: YPLHotel) extends Builder[YPLHotel] {
  def withHotelPolicy(policy: String): B = HotelLens.hotelPolicyLens.set(policy)(build)
  def withSuppliers(suppliersMap: Map[SupplierId, SupplierInfo]): B = HotelLens.supplierMapLens.set(suppliersMap)(build)
  def withSupplierId(id: SupplierId): B = HotelLens.supplierIdLens.set(id)(build)
  def addRoom(room: YPLRoom): B = build.withRooms(build.rooms :+ room)
  def withId(id: Long): B = HotelLens.hotelIdLens.set(id)(build)
  def withPaymentModel(paymentModel: PaymentModel): B = HotelLens.paymentModelLens.set(paymentModel)(build)
  def withRoom(room: YPLRoom): B = build.withRooms(List(room))
  def withRooms(rooms: List[YPLRoom]): B = HotelLens.roomsLens.set(rooms)(build)
  def withSupplierSummary(suppliers: List[SupplierInfo]): B = build.withSuppliers(suppliers.map(x => x.id -> x).toMap)
  def withBookingCutoffTime(cutoffTime: Option[LocalTime]): B = HotelLens.bookingCutoffLens.set(cutoffTime)(build)
  def withGmtOffset(gmtOffset: Int): B = HotelLens.gmtOffsetLens.set(gmtOffset)(build)
  def withGmtOffsetMinutes(gmtOffset: Int): B = HotelLens.gmtOffsetMinutesLens.set(gmtOffset)(build)
  def withCountryId(countryId: Long): B = HotelLens.countryIdLens.set(countryId)(build)
  def withCityId(cityId: Long): B = build.copy(cityId = cityId)
  def withFeatures(feature: HotelFeatures): B = HotelLens.featureLens.set(feature)(build)
  def withReqOcc(reqOcc: YplReqOccByHotelAgePolicy): B = HotelLens.reqOccLens.set(reqOcc)(build)
  def withRateModelType(rateModel: RateModel): B = build.copy(rateModelType = rateModel)
  def withHotelTaxInfo(hotelTaxInfo: HotelTaxInfo): B = build.copy(hotelTaxInfo = hotelTaxInfo)
  def withSourceType(st: Option[SourceType]): B = build.copy(supplierSourceType = st)
  def withFireDrillProto(fireDrillProto: Option[FireDrillProto]): B = build.copy(fireDrillProto = fireDrillProto)
  def withStats(stats: Map[SupplierId, YplSupplierStats]): B = build.copy(stats = stats)
  def withTaxMetaData(taxMetaData: List[TaxMetaData]): B = build.copy(taxMetaData = taxMetaData)
  def withEnableBnplSuppliers(enableBnplSuppliers: Set[SupplierId]): B =
    build.copy(enableBnplSuppliers = enableBnplSuppliers)

  type B = HotelBuilder
}
