package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.ypl.models.LanguageId
import com.agoda.papi.ypl.models.api.request.YplClientInfo
import com.agoda.papi.ypl.models.enums.VipLevelType
import monocle.Lens

object YPLClientInfoLens {
  type A = YplClientInfo
  val languageLens = Lens[A, LanguageId](_.language)(lang => cInfo => cInfo.copy(language = lang))
  val platformLens = Lens[A, Option[Int]](_.platform)(platform => cInfo => cInfo.copy(platform = platform))
  val cidLens = Lens[A, Option[Int]](_.cid)(cid => cInfo => cInfo.copy(cid = cid))
  val originLens = Lens[A, Option[String]](_.origin)(origin => cInfo => cInfo.copy(origin = origin))
  val loginLens = Lens[A, <PERSON>olean](_.isLogIn)(login => cInfo => cInfo.copy(isLogIn = login))
  val vipLevelLens = Lens[A, Option[VipLevelType]](_.vipLevel)(vipLevel => cInfo => cInfo.copy(vipLevel = vipLevel))
  val memberIdLens = Lens[A, Option[Long]](_.memberId)(memberId => cInfo => cInfo.copy(memberId = memberId))
  val storeFrontLens = Lens[A, Option[Int]](_.storeFront)(storeFront => cInfo => cInfo.copy(storeFront = storeFront))
  val userIdLens = Lens[A, Option[String]](_.userId)(userId => cInfo => cInfo.copy(userId = userId))
  val affiliateIdLens =
    Lens[A, Option[String]](_.affiliateId)(affiliateId => cInfo => cInfo.copy(affiliateId = affiliateId))
  val deviceTypeIdLens =
    Lens[A, Option[Int]](_.deviceTypeId)(deviceTypeId => cInfo => cInfo.copy(deviceTypeId = deviceTypeId))
  val localeLens = Lens[A, Option[String]](_.locale)(locale => cInfo => cInfo.copy(locale = locale))
  val ipAddressLens = Lens[A, Option[String]](_.ipAddress)(ipAddress => cInfo => cInfo.copy(ipAddress = ipAddress))
  val externalPartnerIdLens = Lens[A, Option[String]](_.externalPartnerId)(externalPartnerId =>
    cInfo => cInfo.copy(externalPartnerId = externalPartnerId))
}
