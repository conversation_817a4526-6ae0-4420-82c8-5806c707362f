package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.{DisplayedRackrate, EnabledRoom, MasterRoomId, RoomBookings, RoomLinkage}

case class YplEnabledRoom(masterRoomId: MasterRoomId,
                          displayedRackRate: DisplayedRackrate,
                          maxAllowedFreeChildren: Option[Int],
                          roomLinkage: Option[RoomLinkage] = None,
                          roomBookings: Seq[RoomBookings] = Seq.empty,
                          rohFlag: Boolean = false)
  extends EnabledRoom

case class EnabledRoomBuilder(build: YplEnabledRoom) extends Builder[YplEnabledRoom] {
  def withMasterRoomTypeId(id: Long): B = build.copy(masterRoomId = id)

  def withMaxAllowedFreeChildren(optMax: Option[Int]): B = build.copy(maxAllowedFreeChildren = optMax)

  def withDisplayRackrate(value: Double): B = build.copy(displayedRackRate = value)

  def withRoomLinkage(value: Option[RoomLinkage]): B = build.copy(roomLinkage = value)

  def withRoomBookings(value: Seq[RoomBookings]): B = build.copy(roomBookings = value)

  def withRohFlag(value: Boolean): B = build.copy(rohFlag = value)

  type B = YplEnabledRoom
}
