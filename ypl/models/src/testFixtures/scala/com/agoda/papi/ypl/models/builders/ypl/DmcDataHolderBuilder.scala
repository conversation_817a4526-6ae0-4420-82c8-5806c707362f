package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.DmcDataHolder
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.SupplierRateInfo

case class YplDmcDataHolderBuilder(build: DmcDataHolder) extends Builder[DmcDataHolder] {
  def withSupplierExternalDataStr(externalData: Option[String]): B = build.copy(supplierExternalDataStr = externalData)
  def withRoomUid(uid: Option[String]): B = build.copy(roomUid = uid)
  def withSupplierRateInfo(supplierRateInfo: Option[SupplierRateInfo]): B =
    build.copy(supplierRateInfo = supplierRateInfo)

  type B = YplDmcDataHolderBuilder
}
