package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.EasyCancel
import com.agoda.papi.ypl.models.builders.Builder
import org.joda.time.DateTime

case class EasyCancelBuilder(build: EasyCancel) extends Builder[EasyCancel] {
  def withIsEnable(isEnabled: Boolean): B = build.copy(isEnabled = isEnabled)
  def withStartDate(startDate: Option[DateTime]): B = build.copy(startDate = startDate)
  def withEndDate(endDate: Option[DateTime]): B = build.copy(endDate = endDate)
  def withCxlCode(cxlCode: Option[String]): B = build.copy(cxlCode = cxlCode)

  type B = EasyCancel
}
