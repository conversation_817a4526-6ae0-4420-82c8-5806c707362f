package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.adp.messaging.message.Message
import com.agoda.commons.models.pricing.{ExchangeRate, PulseCampaignMetadata}
import com.agoda.finance.tax.enums.TaxLevelCalculationType
import com.agoda.finance.tax.models.{
  ApplyTaxOnSellExSetting,
  ApplyTaxOnSellExSettings,
  TaxPrototypeInfo,
  TaxPrototypeLevel,
}
import com.agoda.papi.enums.hotel._
import com.agoda.papi.enums.room.GeoType.Country
import com.agoda.papi.enums.room._
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes, Tax, TaxWithValue}
import com.agoda.papi.pricing.pricecalculation.models.{TaxProtoTypeID, _}
import com.agoda.papi.ypl.commission._
import com.agoda.papi.ypl.commission.apm.models._
import com.agoda.papi.ypl.commission.growth.program.{GrowthProgramCommissionDetail, GrowthProgramFence}
import com.agoda.papi.ypl.models.Wholesale._
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.models.api.request._
import com.agoda.papi.ypl.models.builders.ota.OTADataExample
import com.agoda.papi.ypl.models.consts.Channel.{EcoDeals, LoyaltyCategoryId}
import com.agoda.papi.ypl.models.consts.{Channel, ChannelCategory}
import com.agoda.papi.ypl.models.helpers.PromotionTestHelper
import com.agoda.papi.ypl.models.context._
import com.agoda.papi.ypl.models.enums._
import com.agoda.papi.ypl.models.hotel._
import com.agoda.papi.ypl.models.occupancy.{MaxAllowedFreeChildAgeRange, OccupancyUnit}
import com.agoda.papi.ypl.models.pricing._
import com.agoda.papi.ypl.models.pricing.proto.{ChildAgeRange, HotelOccupancySetup => YplHotelOccupancySetup, _}
import com.agoda.papi.ypl.models.settings.CommonTaxSettings
import com.agoda.papi.ypl.models.suppliers.{DMC => SupplierDMC}
import com.agoda.supply.calc.proto.{ChildPolicy, RateLevelChildAgeRange}
import org.joda.time.DateTime

trait YplDataExample extends OTADataExample {
  lazy val aValidDateTime = DateTime.parse("2020-01-01")
  lazy val aValidPricingData = YplPricingData(Right(aValidOTAHotelPrice),
                                              aValidHotelInfo,
                                              aValidYplDispatchChannels,
                                              aValidFencedYplDispatchChannels)
  lazy val aValidRateFence: YplRateFence = YplRateFence("TH", -1, 1)
  lazy val aValidYplDispatchChannels = YplDispatchChannels(Set(YplMasterChannel.RTL, YplMasterChannel.APS), Set.empty)
  lazy val aValidNegativeYplDispatchChannels = YplDispatchChannels(
    Set(YplMasterChannel.RTL, YplMasterChannel.APS, YplMasterChannel(Channel.MORPNegativeMock)),
    Set.empty)
  lazy val aValidFencedYplDispatchChannels = Map(aValidRateFence -> aValidYplDispatchChannels)
  lazy val aValidYplRequestFences: Map[YplChannel, Set[YplRateFence]] =
    Map(YplMasterChannel.RTL -> Set(aValidRateFence), YplMasterChannel.APS -> Set(aValidRateFence))
  lazy val aValidRoomOccupancy = RoomOccupancy(1, 0)
  lazy val aValidPaying = PayingChildAges(Nil, Nil, Nil)
  lazy val aValidOfferOccupancy = OfferOccupancy(1,
                                                 OfferOccupancy.noPayingChildAges,
                                                 1,
                                                 0,
                                                 0,
                                                 MaxAllowedFreeChildAgeRange.empty,
                                                 isApplyNewOccupancyLogic = false)
  lazy val aValidRateSilo = RateSilo(hotelId = aValidHotelId,
                                     checkIn = aValidCheckIn,
                                     lengthOfStay = aValidLos,
                                     prices = Map(aValidOTAHotelPrice.supplierId -> aValidOTAHotelPrice))
  lazy val aValidEmptyRateSilo = RateSilo(hotelId = aValidHotelId, checkIn = aValidCheckIn, lengthOfStay = aValidLos)
  lazy val aValidHotelInfo = HotelMeta(aValidHotelId, aValidCityId)
  lazy val aValidSupplierHotel = SupplierHotel(aValidHotelId, -1, "dmc hotel")
  // a room is the master when its masterRoomId = 0
  lazy val aValidEnabledRoom = YplEnabledRoom(
    masterRoomId = 0,
    displayedRackRate = 0.0,
    maxAllowedFreeChildren = None,
  )
  lazy val unknownProgramId = -1
  lazy val aValidRetailChannelId = 1
  lazy val aValidPackageChannelId = 2
  lazy val aValidChannelDiscounts = List(
    YplChannelDiscountBreakdown(2, true, 10, 10),
    YplChannelDiscountBreakdown(6, false, 5, 5),
  )

  lazy val aValidPrice = {
    val margin = 150
    val pf = 20

    YplPrice(
      date = DateTime.parse("2023-04-03").withTimeAtStartOfDay(),
      quantity = 1,
      chargeType = ChargeType.Room,
      applyType = ApplyType.PB,
      chargeOption = ChargeOption.Mandatory,
      refId = 1,
      netExclusive = 1000,
      tax = 50,
      fee = 30,
      margin = margin,
      processingFee = pf,
      promotionDiscount = 10,
      downliftAmount = 100,
      downliftPercent = 10,
      taxBreakDown = List.empty,
      value = 0.0,
      refMargin = margin,
      refProcessingFee = pf,
      upliftedSellIn = None,
      processingFeeBreakdown = None,
      discountMessages = Map.empty,
      downliftExAmount = None,
      isConfigProcessingFee = false,
      dailyTaxes = DailyTaxes(List.empty, isCleanedUpHospitalityTax = true),
      referenceCommissionPercent = 1d,
      agxCommission = YplAGXCommission.noAgxCommission,
      channelDiscounts = Nil,
    )
  }
  lazy val aValidPriceBreakdown = PriceBreakdown.create(BreakdownStep.BaseStep,
                                                        aValidCurrency,
                                                        aValidPrice.chargeType,
                                                        RateType.SellInclusive,
                                                        aValidPrice.sellInclusive)
  lazy val aValidBookingPriceBreakdown = BookingPriceBreakdown(true).newBreakdown(aValidPrice.chargeType,
                                                                                  RateType.SellInclusive,
                                                                                  aValidCurrency,
                                                                                  aValidPrice.sellInclusive)
  lazy val aValidPriceWithPriceBreakdown = aValidPrice.copy(
    currentBreakdownStep = BreakdownStep.PriceBreakdown,
    priceBreakdownHistory = aValidBookingPriceBreakdown.copy(baseProtoPrice =
      aValidBookingPriceBreakdown.baseProtoPrice.copy(currency = aValidCurrency)),
  )
  lazy val aValidRateCategory = RateCategory(111, None, None, false, "", 0.0, List(), "moola", None, false)
  lazy val aValidTaxBreakdown = CommonTaxBreakdown(1,
                                                   isFee = false,
                                                   quantity = 1,
                                                   amount = 10,
                                                   applyTo = "PRPN",
                                                   percentage = 0d,
                                                   noAdjust = true,
                                                   include = false,
                                                   orderNumber = 0)
  lazy val aValidTaxBreakdownV2 = CommonTaxBreakdown(
    1,
    isFee = false,
    quantity = 1,
    amount = 10,
    applyTo = "PRPN",
    percentage = 0d,
    noAdjust = true,
    include = false,
    option = ChargeOption.Mandatory,
    taxable = Some(false),
    taxProtoTypeId = 1,
    whomToPay = Some(WhomToPayType.Property),
    applyOver = Some(ApplyTaxOver.NetEx),
    orderNumber = 0,
  )
  lazy val aValidDiscountInfo = DiscountInfo()
  lazy val aValidRoom = YPLRoom(
    hotelId = aValidHotelId,
    roomTypeId = aValidRoomTypeId,
    supplierId = aValidSupplierId,
    channel = aValidYplChannel,
    language = aValidLanguageId,
    currency = aValidCurrency,
    checkIn = aValidCheckIn,
    lengthOfStay = aValidLos,
    occ = aValidRoomOccupancy,
    marginPercentage = 5.0d,
    cxlCode = "cxlCode",
    allotment = 0,
    breakfast = false,
    prices = List(aValidPrice),
    paymentModel = PaymentModel.Agency,
    rateType = RateType.NetExclusive,
    discountInfo = aValidDiscountInfo,
    originalRateType = RateType.NetExclusive,
    processingFeePercent = 0.0,
    rateCategory = aValidRateCategory,
    fences = Set(aValidRateFence),
    yplRoomEntry = aValidRoomEntry,
  )
  lazy val aValidRoomWithAPMChannel: YPLRoom = YPLRoom(
    hotelId = aValidHotelId,
    roomTypeId = aValidRoomTypeId,
    supplierId = aValidSupplierId,
    channel = aValidAPMYplChannel,
    language = aValidLanguageId,
    currency = aValidCurrency,
    checkIn = aValidCheckIn,
    lengthOfStay = aValidLos,
    occ = aValidRoomOccupancy,
    marginPercentage = 5.0d,
    cxlCode = "cxlCode",
    allotment = 0,
    breakfast = false,
    prices = List(aValidPrice),
    paymentModel = PaymentModel.Agency,
    rateType = RateType.NetExclusive,
    discountInfo = aValidDiscountInfo,
    originalRateType = RateType.NetExclusive,
    processingFeePercent = 0.0,
    rateCategory = aValidRateCategory,
    fences = Set(aValidRateFence),
    yplRoomEntry = aValidRoomEntry,
    apmExternalData = Some(
      ApmExternalData(
        isApmHotelActive = true,
        adjustmentIdsHash = "",
        priceAdjustmentDetails = Seq(ApmPriceAdjustmentExternalData(
          DateTime.parse("2015-10-10"),
          chargeType = ChargeType.Room,
          currency = "THB",
          cheapestSellIn = 120d,
          marketSellIn = 100d,
          delta = 20d,
          originalSellIn = 120d,
          marketPriceDiscountPercent = 0d,
          maximumDeltaPercent = 50d,
          isPartialAdjustment = None,
          approvalPriceId = 1,
          cheapestPriceLocal = 120d,
          marketPriceLocal = 100d,
          originalPriceLocal = 120d,
          adjustmentRateType = RateType.SellInclusive,
        )),
        priceCommission = Seq.empty,
        logicVersion = "",
        priceAdjustmentProgram = MultipleAutoPriceMatchHolder(
          programId = 0,
          commissionDiscountChannelId = Some(0),
          adjustmentChannelId = 0,
          adjustmentDiscountPercent = 0,
          commissionDiscountPercent = 0,
          statusId = 0,
          startDate = DateTime.parse("2020-01-01"),
          endDate = Some(DateTime.parse("2999-12-31")),
          apmAdjustmentDiscount =
            Seq(ApmAdjustmentDiscount(1, 1, Some(5.0d), DateTime.parse("1999-01-01"), DateTime.parse("2999-01-01"))),
          apmCommissionDiscount =
            Seq(ApmCommissionDiscount(1, 1, Some(5.0d), DateTime.parse("1999-01-01"), DateTime.parse("2999-01-01"))),
          programType = Some(1),
        ),
        commissionDiscountProgram = MultipleAutoPriceMatchHolder(
          programId = 0,
          commissionDiscountChannelId = Some(0),
          adjustmentChannelId = 0,
          adjustmentDiscountPercent = 0,
          commissionDiscountPercent = 0,
          statusId = 0,
          startDate = DateTime.parse("2020-01-01"),
          endDate = Some(DateTime.parse("2999-12-31")),
          apmAdjustmentDiscount =
            Seq(ApmAdjustmentDiscount(1, 1, Some(5.0d), DateTime.parse("1999-01-01"), DateTime.parse("2999-01-01"))),
          apmCommissionDiscount =
            Seq(ApmCommissionDiscount(1, 1, Some(5.0d), DateTime.parse("1999-01-01"), DateTime.parse("2999-01-01"))),
          programType = Some(1),
        ),
        rateFence = Set(aValidRateFence),
        apmLeadingRoomAdjustmentIds = Seq.empty,
      )),
  )

  lazy val aValidwhitelabelSetting = WhitelabelSetting(
    whitelabelID = 1,
    defaultDMCSellability = Map.empty,
    isAdjustCommission = false,
    isOverrideCommission = false,
    isAdjustMargin = false,
    paymentChannels = Nil,
    logInInventoryTypeList = Nil,
    isCustomerSegmentValidationEnabled = true,
    isBreakfastAndDinnerIncludeEnable = false,
    externalVipDisplayConfigs = Nil,
    isAdjustCommissionFromHotelContract = false,
    blockYCSPromotions = false,
    paymentInventoryTypeConfigurations = Nil,
  )

  lazy val usaCountryId = 181
  lazy val californiaStateId = 18
  lazy val aValidFeeWaiverFiltrationSettings = FeeWaiverFiltrationSettings(
    description = "",
    hotelCountryId = usaCountryId,
    hotelStateId = californiaStateId,
    expiryDuration = "24H",
    checkInTime = "16:00:00",
    feeValue = "0P",
    leadTimeDuration = "72H",
    filterChannelId = List(aValidPackageChannelId),
  )

  lazy val avalidFeatureForJTB = Feature(
    applyNoCC = ApplyNoCC(true, Set(6), Set(2)),
    applyNoPrepaymentRequired = ApplyNoPrepaymentRequired(true, Set(2)),
    isApplyTaxOnSellEx = true,
    isApplyNoProcessingFee = true,
    commissionAdjustment =
      CommissionAdjustment(inventoryType = Set(InventoryType.JtbOTAJapanican), adjustmentValue = 1),
    commissionOverriding =
      CommissionOverriding(inventoryType = Set(InventoryType.JtbHR, InventoryType.JtbHR2), commission = 5.5),
    marginAdjustment = MarginAdjustment(inventoryType = Set(InventoryType.JtbOTAJapanican, InventoryType.JtbOTARurubu),
                                        occFactor = Map(1 -> 75),
                                        defaultOccFactor = 65),
    isApplyMultipleRoomAssignment = true,
    commissionBaseOnHotelContractAdjustment =
      CommissionBaseOnHotelContractAdjustment(Set(InventoryType.JtbOTAJapanican), Map(1 -> 3, 2 -> 4)),
    false,
    applyPriceDate = true,
    isStrictExactMatchDMC = true,
    isDefaultSellable = false,
    isCheckinTimeRequired = true,
    filterDuplicateTaxesByMinValue = true,
    isRoomLinkageEnabled = true,
    validateCheckinCheckout = true,
    addDMCData = true,
    getTaxBreakdownStringById = true,
    dynamicOccupancyModel = true,
    isOTASupplier = true,
    hasAValidRateCode = true,
    buildYplExternalDataEnabled = true,
    displayRackRate = true,
    isValidOccupancyPricingSupplier = false,
    useBaseCxlPolicyForPromotionalRooms = false,
    skipComposePostProcessMessage = true,
  )
  lazy val aValidSupplierFeatures = SupplierFeatures(features =
    Map(29014 -> avalidFeatureForJTB.copy(isDefaultSellable = true, validateCheckinCheckout = false),
        99901 -> avalidFeatureForJTB))

  lazy val aValidFeature = Feature(
    applyNoCC = ApplyNoCC(isEnable = false, Set.empty, Set.empty),
    applyNoPrepaymentRequired = ApplyNoPrepaymentRequired(isEnable = false, Set.empty),
    isApplyTaxOnSellEx = false,
    isApplyNoProcessingFee = false,
    commissionAdjustment = CommissionAdjustment(Set.empty, 0),
    commissionOverriding = CommissionOverriding(Set.empty, 0),
    marginAdjustment = MarginAdjustment(Set.empty, Map.empty, 0),
    isApplyMultipleRoomAssignment = false,
    commissionBaseOnHotelContractAdjustment = CommissionBaseOnHotelContractAdjustment(Set.empty, Map.empty),
    useCmsIdWhichExcludeTaxAndFee = false,
    applyPriceDate = false,
    isStrictExactMatchDMC = false,
    isDefaultSellable = true,
    isCheckinTimeRequired = false,
    filterDuplicateTaxesByMinValue = false,
    isRoomLinkageEnabled = false,
    validateCheckinCheckout = false,
    addDMCData = false,
    getTaxBreakdownStringById = false,
    dynamicOccupancyModel = false,
    isOTASupplier = false,
    hasAValidRateCode = false,
    buildYplExternalDataEnabled = false,
    displayRackRate = false,
    isValidOccupancyPricingSupplier = true,
    useBaseCxlPolicyForPromotionalRooms = false,
    skipComposePostProcessMessage = false,
  )

  lazy val aValidsuperAggOccupancySamplingRate = SuperAggOccupancyFlowSetting(samplingRate = 0.01, enableNewFlow = true)

  lazy val aValidApplyTaxOnSellExSettings = ApplyTaxOnSellExSettings(
    List(
      ApplyTaxOnSellExSetting(Some(Set(SupplierDMC.JTBWL))),
      ApplyTaxOnSellExSetting(paymentModelId = Some(PaymentModel.MerchantCommission.i)),
      ApplyTaxOnSellExSetting(supplierIds = Some(Set(SupplierDMC.SynxisCCRS)), hotelIds = Some(Set(25797230))),
      ApplyTaxOnSellExSetting(supplierIds = Some(Set(SupplierDMC.DHIL)), excludedPaymentModelIds = Some(Set(1))),
    ))

  lazy val aValidFireDrillContract =
    YplFireDrillContract(contractId = 1, contractType = FireDrillContractType.AGP, advancePay = true)

  lazy val aValidHotel = YPLHotel(
    id = aValidHotelId,
    aValidCheckIn,
    aValidLos,
    PaymentModel.Merchant,
    "hotelPolicy",
    534345L,
    0,
    RateModel.Old,
    rooms = List(aValidRoom),
    reqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(), AgePolicy()),
    hotelTaxInfo = HotelTaxInfo(TaxType.SimpleTax, true),
    yplCheapestStayPackageRatePlans = Seq.empty,
  )
  lazy val aValidClientInfo =
    YplClientInfo(1, Some(1), cid = Some(aValidRateFence.cid), origin = Some(aValidRateFence.origin))
  lazy val aValidApmRoomTypeId1 = 13193032
  lazy val aValidApmRoomTypeId2 = 13193038
  lazy val aValidApmRoomTypeId3 = 13193033
  lazy val aValidApmSetting =
    ApmSettingHolder(List.empty[Int], List.empty[Int], List.empty[Int], List.empty[Long], List(6011203))
  lazy val US_TAX_V2_EXPERIMENT = "VYG-323"
  lazy val commonTaxSettings = CommonTaxSettings(usExperiment2StateMapping = Map(US_TAX_V2_EXPERIMENT -> Set(1)),
                                                 usState2ExperimentMapping = Map(1 -> US_TAX_V2_EXPERIMENT))
  lazy val aValidYplRequest = YplRequest(
    "",
    aValidCheckIn,
    aValidCheckIn.plusDays(aValidLos),
    channels = Set(YplMasterChannel.RTL, YplMasterChannel.APS),
    flagInfo = YplFlagInfo(true),
    occ = YplOccInfo(Some(1), None, Some(1)),
    bookingDate = aValidCheckIn.minusDays(4),
    isSSR = false,
    isBookingRequest = false,
    cInfo = aValidClientInfo,
    whitelabelSetting = aValidwhitelabelSetting,
    supplierFeatures = aValidSupplierFeatures,
    apmSetting = Some(aValidApmSetting),
    fences = Map(YplMasterChannel.RTL -> Set(aValidRateFence), YplMasterChannel.APS -> Set(aValidRateFence)),
    applyTaxOnSellExSettings = Some(aValidApplyTaxOnSellExSettings),
    superAggOccupancySamplingRate = aValidsuperAggOccupancySamplingRate,
    uspaStepSettings = UspaStepSetting(1000),
  )

  lazy val aValidRateType = RateType.getFromValue(1)

  lazy val aValidAgePolicy = AgePolicy()
  lazy val aValidOccInfo = YplOccInfo(Some(0), None, Some(1))
  lazy val aValidChildren = YplChildren()
  lazy val aValidReqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Option(1), _rooms = Option(1)), aValidAgePolicy)
  lazy val aValidPromotion = Promotion()
  lazy val aValidPromotionEntry = PromotionEntry(discountType = DiscountType.Other)
  lazy val aValidRoomTypeEntry = RoomTypeEntry(0, 0, 0, 0, false, 0, 0)
  lazy val aValidPriceEntry = PriceEntry(aValidCheckIn, ChargeType.Unknown, ChargeOption.Unknown, "PB", 0, 0.0)
  lazy val aValidSurchargeEntry = SurchargeEntry(1, "PB", ChargeOption.Mandatory, Set(aValidCheckIn), false, true, 20d)
  lazy val aValidChildPriceEntry = ChildPriceEntry(
    value = None,
    pricingTypeId = PricingChildRateType.Free,
    childAgeRangeId = 1,
    ageFrom = 0,
    ageTo = 1,
    isCountAsRoomOcc = true,
    childAgeRangeType = ChildAgeRangeType.ChildRate,
  )
  lazy val aValidYcsRoomCapacity = YcsRoomCapacity(1, 2, 3, 4)
  lazy val aValidDailyPrice = DailyPrice(aValidCheckIn, Map(), false, List())
  lazy val aValidRateCategoryEntry = RateCategoryEntry(
    rateCategoryId = 123,
    rateCategoryCode = None,
    cxlCode = "",
    parent = None,
    rateTypeLoaded = RateType.Unknown,
    bookFrom = Option(DateTime.now.minusDays(10)),
    bookTo = Option(DateTime.now.plusDays(10)),
    bookTimeFrom = None,
    bookTimeTo = None,
    minAdvance = None,
    maxAdvance = None,
    isCanCombinePromotion = true,
    offerType = None,
    benefitList = List(),
    dailyPrices = Map(aValidDailyPrice.date -> aValidDailyPrice),
    remainingRoom = 123,
    remainingRoomGa = 123,
    remainingRoomRa = Option(123),
    isAmount = true,
    applyTo = "",
    value = 1,
    promotionList = List(),
    customerSegment = List(),
  )
  lazy val aValidPropOfferOccupancy = PropOfferOccupancy(numAdults = 2, numChild = 0)

  lazy val aValidRoomEntry = YplRoomEntry(
    roomTypeId = 0L,
    masterRoomTypeId = 0L,
    channel = YplMasterChannel(0),
    cxlCode = "",
    isBreakFastIncluded = false,
    remainingRooms = 0,
    currency = "THB",
    rateType = aValidRateType,
    processingFees = 0d,
    isAllowCombinePromotion = false,
    roomType = RoomTypeEntry(0, 0, 0, 0, false, 0, 0),
    dailyPrices = Map.empty[DateTime, DailyPrice],
    occEntry = RoomOccupancy(0, 0),
    originalRateType = aValidRateType,
    inventoryType = InventoryType.Agoda,
    rateCategory = aValidRateCategoryEntry,
    fences = Set(aValidRateFence),
    propOfferOccupancy = PropOfferOccupancy(0, 0),
  )

  lazy val aValidHotelTaxInfo = HotelTaxInfo(TaxType.SimpleTax, false)
  lazy val aValidTaxInfo = TaxInfo(aValidHotelTaxInfo, Map())

  lazy val aValidTax = Tax(1, "PRPN", false, false, false, 10.0, ChargeOption.Mandatory, 1, None)
  lazy val amountTax = Tax(1, "PRPN", true, false, false, 35, ChargeOption.Mandatory, 1, None)
  lazy val aValidTaxV2 = Tax(
    1,
    "PRPN",
    false,
    false,
    false,
    10.0,
    ChargeOption.Mandatory,
    1,
    None,
    Some(ApplyTaxOver.MarginCommission),
    Some(WhomToPayType.Government),
    Some(1),
    Some(TaxLevelCalculationType.FlatRate),
  )
  lazy val aValidFee = Tax(2, "PRPN", false, true, false, 20.0, ChargeOption.Mandatory)
  lazy val aValidTaxApplyOnSellEx =
    Tax(3, "PRPN", false, false, false, 30.0, ChargeOption.Mandatory, 1, None, Some(ApplyTaxOver.SaleEx))
  lazy val aValidTaxWithValue = TaxWithValue(aValidTax, 0.0)
  lazy val aValidDailyTax = DailyTaxes(List(TaxWithValue(aValidTax, 10.0)), isCleanedUpHospitalityTax = true)
  lazy val aValidDailyTaxV2 = DailyTaxes(List(TaxWithValue(aValidTaxV2, 10.0)), isCleanedUpHospitalityTax = true)
  lazy val aValidHPPercentTaxV2 = Tax(
    id = 1,
    applyTo = "PRPN",
    isAmount = false,
    isFee = false,
    isTaxable = false,
    value = 10.0,
    option = ChargeOption.HospitalityPrice,
    protoTypeId = 2,
    taxPrototypeInfo = None,
    applyOver = Some(com.agoda.papi.enums.room.ApplyTaxOver.NetEx),
    whomToPay = Some(WhomToPayType.Property),
    orderNumber = Some(0),
    taxLevelCalculationType = Some(TaxLevelCalculationType.FlatRate),
  )
  lazy val aValidMAPercentTaxV2 = Tax(
    id = 1,
    applyTo = "PRPN",
    isAmount = false,
    isFee = false,
    isTaxable = false,
    value = 5.0,
    option = ChargeOption.Mandatory,
    protoTypeId = 2,
    taxPrototypeInfo = None,
    applyOver = Some(ApplyTaxOver.MarginCommission),
    whomToPay = Some(WhomToPayType.Property),
    orderNumber = Some(0),
    taxLevelCalculationType = Some(TaxLevelCalculationType.FlatRate),
  )
  lazy val aValidSurchargePercentTaxV2 = Tax(
    id = 1,
    applyTo = "PRPN",
    isAmount = false,
    isFee = false,
    isTaxable = false,
    value = 10.0,
    option = ChargeOption.Surcharge,
    protoTypeId = 2,
    taxPrototypeInfo = None,
    applyOver = Some(ApplyTaxOver.NetEx),
    whomToPay = Some(WhomToPayType.Property),
    orderNumber = Some(0),
    taxLevelCalculationType = Some(TaxLevelCalculationType.FlatRate),
  )

  lazy val aValidVariableTax = Tax(
    id = 1,
    applyTo = "PRPN",
    isAmount = true,
    isFee = false,
    isTaxable = false,
    value = 10.0,
    option = ChargeOption.VariableTax,
    protoTypeId = 2,
    whomToPay = Some(WhomToPayType.Government),
  )

  // Mandatory tax
  lazy val mandatoryTaxBase = aValidHPPercentTaxV2.copy(option = ChargeOption.Mandatory, applyOver = None)
  lazy val aValidMandatoryAmountTaxV2 = mandatoryTaxBase.copy(isAmount = true, geoType = Some(Country))
  lazy val aValidMandatoryTaxablePercentFee = mandatoryTaxBase.copy(isTaxable = true, isFee = true)
  lazy val aValidMandatoryNonTaxablePercentFee = mandatoryTaxBase.copy(isTaxable = false, isFee = true)
  lazy val aValidMandatoryNonTaxableAmountFee = mandatoryTaxBase.copy(isTaxable = false, isFee = true, isAmount = true)

  lazy val allPercentMandatoryTax = List(TaxWithValue(mandatoryTaxBase, 10.0))
  lazy val allAmountMandatoryTax = List(TaxWithValue(aValidMandatoryAmountTaxV2, 10.0))

  // hospitality tax
  lazy val aValidHPAmountTaxV2 = aValidHPPercentTaxV2.copy(isAmount = true, geoType = Some(Country))
  lazy val aValidHPTaxablePercentFee = aValidHPPercentTaxV2.copy(isTaxable = true, isFee = true)
  lazy val aValidHPNonTaxablePercentFee = aValidHPPercentTaxV2.copy(isTaxable = false, isFee = true)
  lazy val aValidHPNonTaxableAmountFee =
    aValidHPPercentTaxV2.copy(isTaxable = false, isFee = true, isAmount = true, geoType = Some(Country))

  // Margin Tax
  lazy val validMAAmountTaxV2 = aValidMAPercentTaxV2.copy(isAmount = true, isFee = false, isTaxable = false)
  lazy val validMATaxablePercentFeeV2 = aValidMAPercentTaxV2.copy(isAmount = false, isFee = true, isTaxable = true)
  lazy val validMANonTaxablePercentFeeV2 = aValidMAPercentTaxV2.copy(isAmount = false, isFee = true, isTaxable = false)
  lazy val validMANonTaxableAmountFeeV2 = aValidMAPercentTaxV2.copy(isAmount = true, isFee = true, isTaxable = false)

  // Surcharge Tax
  lazy val aValidSurchargeAmountTaxV2 =
    aValidSurchargePercentTaxV2.copy(isAmount = true, isFee = false, isTaxable = false)
  lazy val aValidSurchargeTaxablePercentFee =
    aValidSurchargePercentTaxV2.copy(isAmount = false, isFee = true, isTaxable = true)
  lazy val aValidSurchargeNonTaxablePercentFee =
    aValidSurchargePercentTaxV2.copy(isAmount = false, isFee = true, isTaxable = false)
  lazy val aValidSurchargeNonTaxableAmountFee =
    aValidSurchargePercentTaxV2.copy(isAmount = true, isFee = true, isTaxable = false)

  lazy val allPercentHPTaxAndFee = List(TaxWithValue(aValidHPPercentTaxV2, 10.0),
                                        TaxWithValue(aValidHPTaxablePercentFee, 10.0),
                                        TaxWithValue(aValidHPNonTaxablePercentFee, 10))
  lazy val allAmountHPTaxAndFee =
    List(TaxWithValue(aValidHPAmountTaxV2, 10.0), TaxWithValue(aValidHPNonTaxableAmountFee, 10.0))
  lazy val allPercentMarginTaxAndFee = List(TaxWithValue(aValidMAPercentTaxV2, 5.0),
                                            TaxWithValue(validMATaxablePercentFeeV2, 5.0),
                                            TaxWithValue(validMANonTaxablePercentFeeV2, 5.0))
  lazy val allAmountMarginTaxAndFee =
    List(TaxWithValue(validMAAmountTaxV2, 5.0), TaxWithValue(validMANonTaxableAmountFeeV2, 5.0))
  lazy val allPercentSurchargeTaxAndFee = List(
    TaxWithValue(aValidSurchargePercentTaxV2, 10.0),
    TaxWithValue(aValidSurchargeTaxablePercentFee, 10.0),
    TaxWithValue(aValidSurchargeNonTaxablePercentFee, 10),
  )
  lazy val allAmountSurchargeTaxAndFee =
    List(TaxWithValue(aValidSurchargeAmountTaxV2, 10.0), TaxWithValue(aValidSurchargeNonTaxableAmountFee, 10.0))
  lazy val percentHPAndPercentMATaxes: DailyTaxes = DailyTaxes(
    List(TaxWithValue(aValidHPPercentTaxV2, 10.0), TaxWithValue(aValidMAPercentTaxV2, 5.0)),
    isCleanedUpHospitalityTax = true)
  lazy val percentSurchargeAndPercentMATaxes: DailyTaxes = DailyTaxes(
    List(TaxWithValue(aValidSurchargePercentTaxV2, 10.0), TaxWithValue(aValidMAPercentTaxV2, 5.0)),
    isCleanedUpHospitalityTax = true)
  lazy val variableTaxes: DailyTaxes =
    DailyTaxes(List(TaxWithValue(aValidVariableTax, 10.0)), isCleanedUpHospitalityTax = true)

  lazy val aValidDailyTaxWithApplyOnSellEx =
    DailyTaxes(List(TaxWithValue(aValidTaxApplyOnSellEx, 30.0)), isCleanedUpHospitalityTax = true)
  lazy val aValidDailyHpPercentTaxV2 =
    DailyTaxes(List(TaxWithValue(aValidHPPercentTaxV2, 10.0)), isCleanedUpHospitalityTax = true)

  lazy val aValidTaxPrototypeInfoWithTaxPrototypeLevel = TaxPrototypeInfo(
    List(
      TaxPrototypeLevel(1, 0.0, 1000.0, 0),
      TaxPrototypeLevel(2, 1000.0, 2500.0, 12.0),
      TaxPrototypeLevel(3, 2500.0, 7500.0, 18.0),
      TaxPrototypeLevel(4, 7500.0, 99999999.99, 28.0),
    ))
  lazy val aValidTaxWithTaxPrototypeLevel = Tax(1,
                                                "PRPN",
                                                false,
                                                false,
                                                false,
                                                0.0,
                                                ChargeOption.Mandatory,
                                                11111,
                                                Some(aValidTaxPrototypeInfoWithTaxPrototypeLevel))
  lazy val aValidTaxWithTaxPrototypeLevelV2 = Tax(
    1,
    "PRPN",
    false,
    false,
    false,
    0.0,
    ChargeOption.Mandatory,
    11111,
    Some(aValidTaxPrototypeInfoWithTaxPrototypeLevel),
    whomToPay = Some(WhomToPayType.Property),
    orderNumber = Some(1),
    taxLevelCalculationType = Some(TaxLevelCalculationType.FlatRate),
    valueMethod = Some(ValueMethodType.Fixed),
    valueCalculationMethodType = Some(ValueCalculationMethodType.Amount),
    geoId = Some(114),
    geoType = Some(GeoType.State),
  )
  lazy val aValidTaxInfoWithTaxPrototypeLevel =
    TaxInfo(aValidHotelTaxInfo, Map((1, 11111) -> aValidTaxWithTaxPrototypeLevel))
  lazy val aValidTaxInfoWithTaxPrototypeLevelV2 =
    TaxInfo(aValidHotelTaxInfo, Map((1, 11111) -> aValidTaxWithTaxPrototypeLevelV2))
  lazy val aValidDailyTaxWithTaxPrototypeLevel =
    DailyTaxes(List(TaxWithValue(aValidTaxWithTaxPrototypeLevel, 0.0)), isCleanedUpHospitalityTax = true)
  lazy val aValidDailyTaxWithTaxPrototypeLevelV2 =
    DailyTaxes(List(TaxWithValue(aValidTaxWithTaxPrototypeLevelV2, 0.0)), isCleanedUpHospitalityTax = true)
  lazy val aValidDailyTaxWithTaxPrototypeLevelWithValue =
    DailyTaxes(List(TaxWithValue(aValidTaxWithTaxPrototypeLevel, 12.0)), isCleanedUpHospitalityTax = true)
  lazy val aValidDailyPriceWithTaxPrototypeLevel = DailyPrice(aValidCheckIn, Map((1, 11111) -> 0.0), false, List())

  lazy val aValidDailyTaxWithTaxPrototypeLevelAndFee = DailyTaxes(
    List(TaxWithValue(aValidTaxWithTaxPrototypeLevel, 0.0), TaxWithValue(aValidFee, 20.0)),
    isCleanedUpHospitalityTax = true)
  lazy val aValidDailyTaxWithTaxPrototypeLevelAndFeeWithValue = DailyTaxes(
    List(TaxWithValue(aValidTaxWithTaxPrototypeLevel, 12.0), TaxWithValue(aValidFee, 20.0)),
    isCleanedUpHospitalityTax = true)
  lazy val aValidTaxInfoWithTaxPrototypeLevelAndFee =
    TaxInfo(aValidHotelTaxInfo, Map((1, 11111) -> aValidTaxWithTaxPrototypeLevel, (2, 0) -> aValidFee))

  lazy val aValidHotelEntryModelWithTaxPrototypeLevel = YplHotelEntryModel(
    hotelId = 0,
    supplierId = 0,
    paymentModel = PaymentModel.Merchant,
    rooms = List(),
    occupancyModel = OccupancyModel.Unknown,
    taxInfo = aValidTaxInfoWithTaxPrototypeLevel,
    rateModel = RateModel.Unknown,
    surchargeRateType = RateType.Unknown,
    metaData = aValidHotelInfo,
    ratePlanLanguage = None,
    rateReutilizations = Seq(),
    reqOcc = aValidReqOcc,
    lengthOfStay = aValidLos,
    stackChannelDiscountInfo = Map.empty,
    dispatchChannels = aValidYplDispatchChannels,
    dispatchChannelsPerFence = Map(aValidRateFence -> aValidYplDispatchChannels),
  )

  lazy val aValidTaxPrototypeLevel = TaxPrototypeLevel(1, 0.0, 1000.0, 0.0)
  lazy val aValidHotelEntryModel = YplHotelEntryModel(
    hotelId = 0,
    supplierId = 0,
    paymentModel = PaymentModel.Merchant,
    rooms = List(),
    occupancyModel = OccupancyModel.Unknown,
    taxInfo = aValidTaxInfo,
    rateModel = RateModel.Unknown,
    surchargeRateType = RateType.Unknown,
    metaData = aValidHotelInfo,
    ratePlanLanguage = None,
    rateReutilizations = Seq(),
    reqOcc = aValidReqOcc,
    lengthOfStay = aValidLos,
    stackChannelDiscountInfo = Map.empty,
    dispatchChannels = aValidYplDispatchChannels,
    dispatchChannelsPerFence = Map(aValidRateFence -> aValidYplDispatchChannels),
  )

  lazy val aValidHotelOccupancy = HotelOccupancy(false, false, HotelChildRateMode.UniversalPerProperty)

  lazy val aValidHotelOccupancySetup = YplHotelOccupancySetup(
    occupancy = aValidHotelOccupancy,
    childAgeRanges = Nil,
    perPersonPrices = Nil,
    currency = "USD",
    rateLoadType = RateType.NetExclusive,
  )

  lazy val aValidHotelOccupancySetupWithExtrabed = YplHotelOccupancySetup(
    occupancy = HotelOccupancy(false, true, HotelChildRateMode.UniversalPerProperty),
    childAgeRanges = List(ChildAgeRange(111, 1, 3, true)),
    perPersonPrices = List(
      PerPersonPrice(1, None, 100d, PricingChildRateType.FlatPrice, PerPersonChargeType.ExtraBed, None, true),
      PerPersonPrice(2, Some(111), 50d, PricingChildRateType.FlatPrice, PerPersonChargeType.ExtraBed, None, false),
    ),
    currency = "USD",
    rateLoadType = RateType.NetExclusive,
  )

  lazy val aValidYPLRateReutilizationEntry =
    YPLRateReutilizationEntry(aValidYplChannel, aValidYplChannel, aValidYplChannel, 2, 10d, None, None)

  lazy val aValidYplAgencyNoccSetting = YplAgencyNoccSetting(agencyNoccMode = AgencyNoccMode.ApplyNoccToAllBookings)

  lazy val aValidDMCDataHolder = DmcDataHolder(supplierExternalDataStr = Some("a|b|c"),
                                               roomUid = Some("some uid"),
                                               supplierRateInfo = Some(aValidOTASupplierRateInfo))

  lazy val aValidCor = COR(displayedRackRate = 11.12345)

  val aValidYplFlagInfo = YplFlagInfo()
  val aValidYplFeatureRequest = YplFeatureRequest()
  val aValidBenefit = Benefit(id = 1, value = 10, groupId = 99)
  val aValidYplSupplierSetting = YplSupplierSetting(isOccFreePullSupplier = false,
                                                    isChildStayFree = false,
                                                    isThirdParty = false,
                                                    isOccModelFPLOS = false,
                                                    isAgodaBrand = false)

  lazy val aValidFireDrillProto = FireDrillProto(contractId = 1,
                                                 commission = 0.2,
                                                 activateDate = Some(aValidCheckIn),
                                                 isAgpLite = false,
                                                 contractType = FireDrillContractType.AGP)

  // todo: this create map of all suppler defined in enum.hotel.DMC, try do just one YCS and Channel id: 1
  private val supplierList: Set[Int] = DMC.values.map(_.value).toSet
  lazy val aValidYplDmcDataHolder = DmcDataHolder(supplierExternalDataStr = Some("a|b|c"), roomUid = Some("some uid"))
  lazy val aValidMetaRateSilo = YPLWithMeta(
    d = aValidRateSilo,
    meta = aValidHotelInfo,
    supplierChannelsMap = supplierList
      .map(supplier =>
        supplier -> aValidYplDispatchChannels.copy(
          masterChannels = Range(0, 100).map(YplMasterChannel(_)).toSet,
        ))
      .toMap,
  )

  lazy val aValidExchangeRateContext = new ExchangeRateContext {
    override def getExchangeRate(from: Currency, to: Currency): Option[ExchangeRate] = None
  }

  lazy val aValidSurchargeDataServiceContext = new SurchargeDataServiceContext {
    override def getSurchargeInformation(surchargeId: TaxProtoTypeID): Option[SurchargeInfo] = None
  }

  lazy val aValidCompositeChannelContext = new CompositeChannelContext {
    val compositeChannelApsDomestic = 100
    val compositeChannelApsMobile = 101
    val compositeChannelDomesticMobile = 102
    val compositeChannelMobileAps = 103
    val compositeChannelDomesticAps = 104
    val compositeChannelApsVipGold = 105
    val compositeChannelMobileVipGold = 106
    val compositeChannelVipGoldMobileApp = 107

    case class CompositeChannel(baseChannelId: Int, stackedChannelIds: Set[Int], compositeChannelId: Int)

    val compositeChannelData = List(
      CompositeChannel(2, Set(6), compositeChannelApsDomestic),
      CompositeChannel(2, Set(7), compositeChannelApsMobile),
      CompositeChannel(6, Set(7), compositeChannelDomesticMobile),
      CompositeChannel(7, Set(2), compositeChannelMobileAps),
      CompositeChannel(6, Set(2), compositeChannelDomesticAps),
      CompositeChannel(2, Set(27), compositeChannelApsVipGold),
      CompositeChannel(7, Set(27), compositeChannelMobileVipGold),
      CompositeChannel(27, Set(1098), compositeChannelVipGoldMobileApp),
    )

    override def getCompositeChannelId(baseChannelId: Int, stackedChannelIds: Set[Int]): Int = compositeChannelData
      .find(s => s.baseChannelId == baseChannelId && s.stackedChannelIds == stackedChannelIds)
      .map(_.compositeChannelId)
      .getOrElse(baseChannelId)

    override def toCompositeChannelIds(channelId: Int): Set[Int] = Set(channelId)
  }

  lazy val aValidHadoopContext = new HadoopContext {
    override def logHadoopMessage(msg: Message): Unit = {}
  }

  lazy val aValidHolidayCalendarContext = new RoomLinkageHolidayCalendarContext {
    override def getHolidayCalendar: Map[DateTime, Boolean] = Map.empty
  }

  lazy val aValidHolidayCalendarContextWithHoliday = new RoomLinkageHolidayCalendarContext {
    override def getHolidayCalendar: Map[DateTime, Boolean] = mockHolidayCalendar
  }

  /*
    |Category Id | Category Name        | Channel Ids         |
    |     1	     | Product discounts    | 2,8,14              |
    |     2      | Loyalty discounts    | 27,111              |
    |     3      | Geography discounts  | 6,9,21,24,25,26     |
    |     4      | Platform discounts   | 7,1098              |
   */
  lazy val aValidMasterChannelCategoryContext = new MasterChannelCategoryContext {
    val categoryToChannelMap = Map(
      ChannelCategory.Product -> Set(Channel.APS, Channel.APO, Channel.Package, Channel.EcoDeals),
      ChannelCategory.Loyalty -> Set(Channel.VipGold, Channel.VipPlatinum),
      ChannelCategory.Geography -> Set(Channel.Domestic,
                                       Channel.China,
                                       Channel.International,
                                       Channel.AsiaIPOnly,
                                       Channel.IndonesiaIPOnly,
                                       Channel.ChinaIPOnly),
      ChannelCategory.Platform -> Set(Channel.Mobile, Channel.MobileApp),
      ChannelCategory.Bedbank -> Set(Channel.Bedbank, Channel.BedbankAffiliates),
    )
    val channelToCategoryMap = categoryToChannelMap.flatMap { case (key, values) => values.toList.map(v => v -> key) }

    override def getMasterChannelCategoryMap(): Map[ChannelId, ChannelCategoryId] = channelToCategoryMap

    override def getMasterChannelCategoryMapWithEcoDealsOverride: Map[ChannelId, ChannelCategoryId] =
      channelToCategoryMap ++ Map(EcoDeals -> LoyaltyCategoryId)
  }

  def aValidPulseCampaignMetaContext(webCampaignPtypeIds: List[Int],
                                     overrideCampaignTypeId: Option[Int] = None): PulseCampaignMetaContext =
    new PulseCampaignMetaContext {
      override def getPulseCampaignSetting(
        promotionTypeIds: List[PromotionTypeId]): Map[PromotionTypeId, PulseCampaignMetadata] =
        webCampaignPtypeIds.zipWithIndex
          .map { case (ptypeId, index) =>
            (ptypeId,
             PulseCampaignMetadata(
               webCampaignId = index,
               promotionTypeId = ptypeId,
               campaignTypeId = overrideCampaignTypeId.getOrElse(index),
               campaignBadgeCmsId = index,
               campaignBadgeDescCmsId = index,
             ))
          }
          .toMap
          .filter(x => promotionTypeIds.contains(x._1))
    }

  def aValidPulseAndMegaSaleCampaignMetaContext(pulsePromotionTypeIds: List[Int] = Nil,
                                                megaSalePromotionTypeIds: List[Int] = Nil): PulseCampaignMetaContext =
    PromotionTestHelper.createPulseCampaignMetaContext(pulsePromotionTypeIds, megaSalePromotionTypeIds)

  lazy val aValidAgodaAgencyFeatures = AgodaAgencyFeatures(true, 1, Nil, 0)

  lazy val aValidEasyCancel = EasyCancel(isEnabled = false, None, None, None)

  lazy val aValidPartialRefundInfo = YplPartialRefundInfo("", "", "", true)

  lazy val aValidOtaEnabledRoomWithLinkage = YplEnabledRoom(
    masterRoomId = 0,
    displayedRackRate = 0.0,
    maxAllowedFreeChildren = None,
    roomLinkage = Some(YplRoomLinkage(3203084, 5, 7, Seq.empty)),
    roomBookings = Seq(
      YplRoomBookings(inDays = 30, count = 0),
      YplRoomBookings(inDays = 90, count = 0),
      YplRoomBookings(inDays = 180, count = 0),
    ),
  )

  lazy val aValidHrEnabledRoom = YplEnabledRoom(
    masterRoomId = 0,
    displayedRackRate = 0.0,
    maxAllowedFreeChildren = None,
    roomBookings = Seq.empty,
  )

  /*
    exclude from: 2015-10-08
    exclude till: 2015-10-11
   */
  lazy val aValidOtaRoomWithLinkageExcludeDate =
    YplRoomLinkageExclusionDate(Some(new DateTime(1444262400000L)), Some(new DateTime(1444521600000L)))

  /*
    exclude from: 2015-10-09 00:00
    exclude till: 2015-10-09 00:00
   */
  lazy val aValidOtaRoomWithLinkageExcludeOneDay =
    YplRoomLinkageExclusionDate(Some(new DateTime(1444323600000L)), Some(new DateTime(1444323600000L)))

  lazy val aValidOtaEnabledRoomWithLinkageExclude = YplEnabledRoom(
    masterRoomId = 0,
    displayedRackRate = 0.0,
    maxAllowedFreeChildren = None,
    roomLinkage =
      Some(YplRoomLinkage(3203084, 5, 7, Seq(aValidOtaRoomWithLinkageExcludeDate, aValidOtaRoomWithLinkageExcludeOneDay))),
  )

  lazy val aValidOtaEnabledRoomWithLinkageExcludeOneDay = YplEnabledRoom(
    masterRoomId = 0,
    displayedRackRate = 0.0,
    maxAllowedFreeChildren = None,
    roomLinkage = Some(YplRoomLinkage(3203084, 5, 7, Seq(aValidOtaRoomWithLinkageExcludeOneDay))),
  )

  lazy val mockEnabledRoomWithLinkage: Map[RoomTypeId, EnabledRoom] = Map(
    3203083L -> aValidOtaEnabledRoomWithLinkage,
    3203084L -> aValidHrEnabledRoom,
  )

  lazy val mockEnabledRoomWithLinkageExclude: Map[RoomTypeId, EnabledRoom] = Map(
    3203083L -> aValidOtaEnabledRoomWithLinkageExclude,
    3203084L -> aValidHrEnabledRoom,
  )

  lazy val mockEnabledRoomWithLinkageExcludeOneDay: Map[RoomTypeId, EnabledRoom] = Map(
    3203083L -> aValidOtaEnabledRoomWithLinkageExcludeOneDay,
    3203084L -> aValidHrEnabledRoom,
  )

  lazy val aValidHotelInfoWithLinkage = HotelMeta(aValidHotelId, aValidCityId, enabledRoom = mockEnabledRoomWithLinkage)

  lazy val aValidHotelInfoWithLinkageExclude =
    HotelMeta(aValidHotelId, aValidCityId, enabledRoom = mockEnabledRoomWithLinkageExclude)

  lazy val aValidHotelInfoWithLinkageExcludeOneDay =
    HotelMeta(aValidHotelId, aValidCityId, enabledRoom = mockEnabledRoomWithLinkageExcludeOneDay)

  lazy val mockHolidayCalendar = Map(
    aValidCheckIn -> true,
    aValidCheckIn.plusDays(1) -> false,
  )

  lazy val aValidCancellationCodePerOcc = CancellationCodePerOcc(1, 2, "1D1N_100P")

  lazy val aValidStackedChannelDiscount = StackedChannelDiscount(2, 0.0)
  lazy val aValidMultipleAutoPriceMatch =
    MultipleAutoPriceMatchHolder(0, None, 0d, 0, 0d, 0, DateTime.parse("2023-05-23"), None, Seq.empty, Seq.empty, Some(1))
  // Wholesale
  lazy val aValidWholesaleMetaDataByRateFence: WholesaleMetaDataByRateFence = Map.empty

  lazy val aValidCommissionHolder = CommissionHolder(
    daily = Map.empty,
    commissionAdjustment = CommissionAdjustmentHolder.default,
    commissionOverriding = CommissionOverridingHolder.default,
    commissionMultiplyAdjustment = CommissionMultiplyAdjustmentHolder.default,
    commissionBaseOnHotelContractAdjustment = CommissionBaseOnHotelContractAdjustmentHolder.default,
    apmCommissionHolder = ApmCommissionHolder.default,
    agxAllDateAdjust = None,
    agpCommissionHolder = AgpCommissionHolder.default,
    morpCommissionHolder = MORPCommissionHolder.default,
    retailGlobalContractedCommission = 0d,
    supplierContractedCommission = None,
    growthProgramCommissionHolder = GrowthProgramCommissionHolder.default,
  )

  lazy val aValidCommissionDailyHolder = CommissionDailyHolder(
    protobufCommissionHolder = ProtobufCommissionHolder.default,
    agxCommissionHolder = AgxCommissionHolder.default,
    wholesaleCommissionHolder = WholesaleCommissionHolder.default,
    morpCommissionDailyHolder = MORPCommissionDailyHolder.default,
    occupancyCommissionHolder = OccupancyCommissionHolder.default,
    growthProgramCommissionHolder = GrowthProgramCommissionHolder.default,
  )

  lazy val aValidProtobufCommissionHolder = ProtobufCommissionHolder.default

  lazy val aValidAgpCommissionHolder = AgpCommissionHolder.default
  lazy val aValidMORPCandidateRoomParameters = MORPCandidateRoomParameters(
    rateFence = Set.empty,
    applyNoCCCommission = false,
  )

  lazy val aValidApmCommissionDiscountResult = ApmCommissionDiscountResult(
    blackoutDates = Set.empty,
    isRoomEligible = false,
    apmDiscountPercent = 0d,
    finalCommissionPercent = 0d,
    commissionDiscountChannelSetting = None,
    isSupplierValid = false,
  )

  lazy val aValidMORPCommissionDailyHolder = MORPCommissionDailyHolder(
    fencedAgxCommissionHolders = Seq.empty,
    commissionAdjustmentForLanguageHolder = CommissionAdjustmentForLanguageHolder.default,
  )

  lazy val aValidAgpLiteGrowthProgramCommissionHolder = GrowthProgramCommissionHolder(
    Map(
      aValidRateFence -> Seq(
        GrowthProgramCommissionDetail(
          commission = 0.2d,
          paymentTypeId = 1,
          programTypeId = 1,
          programSubTypeId = 2,
          freeTrial = false,
          rankingBoost = 0.2d,
          date = None,
        ))),
    growthProgramFence = GrowthProgramFence.defaultInstance,
  )

  lazy val aValidAgpLiteInvoiceGrowthProgramCommissionHolder = GrowthProgramCommissionHolder(
    Map(
      aValidRateFence -> Seq(
        GrowthProgramCommissionDetail(
          commission = 0.2d,
          paymentTypeId = 2,
          programTypeId = 1,
          programSubTypeId = 2,
          freeTrial = false,
          rankingBoost = 0.2d,
          date = None,
        ))),
    growthProgramFence = GrowthProgramFence.defaultInstance,
  )

  lazy val aValidAgpPaidGrowthProgramCommissionHolder = GrowthProgramCommissionHolder(
    Map(
      aValidRateFence -> Seq(
        GrowthProgramCommissionDetail(
          commission = 0.2d,
          paymentTypeId = 2,
          programTypeId = 1,
          programSubTypeId = 1,
          freeTrial = false,
          rankingBoost = 0.2d,
          date = None,
        ))),
    growthProgramFence = GrowthProgramFence.defaultInstance,
  )

  lazy val aValidAgpPaidNonInvoiceGrowthProgramCommissionHolder = GrowthProgramCommissionHolder(
    Map(
      aValidRateFence -> Seq(
        GrowthProgramCommissionDetail(
          commission = 0.2d,
          paymentTypeId = 1,
          programTypeId = 1,
          programSubTypeId = 1,
          freeTrial = false,
          rankingBoost = 0.2d,
          date = None,
        ))),
    growthProgramFence = GrowthProgramFence.defaultInstance,
  )

  lazy val aValidBedsPaidGrowthProgramCommissionHolder = GrowthProgramCommissionHolder(
    Map(
      aValidRateFence -> Seq(
        GrowthProgramCommissionDetail(
          commission = 0.0d,
          paymentTypeId = 1,
          programTypeId = 4,
          programSubTypeId = 1,
          freeTrial = false,
          rankingBoost = 0.0d,
          date = None,
        ))),
    growthProgramFence = GrowthProgramFence.defaultInstance,
  )

  lazy val aValidAgxPaidGrowthProgramCommissionHolder = GrowthProgramCommissionHolder(
    Map(
      aValidRateFence -> Seq(
        GrowthProgramCommissionDetail(
          commission = 0.0d,
          paymentTypeId = 1,
          programTypeId = 2,
          programSubTypeId = 2,
          freeTrial = false,
          rankingBoost = 0.0d,
          date = None,
        ))),
    growthProgramFence = GrowthProgramFence.defaultInstance,
  )

  // child policy testing
  lazy val roomWith1ChildPolicy = aValidRoomEntry.copy(
    rateCategory = aValidRateCategoryEntry.copy(
      childPolicy = Some(ChildPolicy(1, List(RateLevelChildAgeRange(4, 7)))),
    ),
    roomType = aValidRoomTypeEntry.copy(
      0,
      4,
      0,
      1,
    ),
  )
  lazy val roomWith2ChildPolicy = aValidRoomEntry.copy(
    rateCategory = aValidRateCategoryEntry.copy(
      childPolicy = Some(ChildPolicy(2, List(RateLevelChildAgeRange(4, 7)))),
    ),
    roomType = aValidRoomTypeEntry.copy(
      0,
      4,
      0,
      1,
    ),
  )
  lazy val invalidChildPolicy = aValidRoomEntry.copy(
    rateCategory = aValidRateCategoryEntry.copy(
      childPolicy = Some(ChildPolicy(1, List(RateLevelChildAgeRange(0, 7)))),
    ),
    roomType = aValidRoomTypeEntry.copy(
      0,
      4,
      0,
      1,
    ),
  )

  lazy val aValidEnabledRoomWith0MAFC = aValidEnabledRoom.copy(
    maxAllowedFreeChildren = Some(0),
  )
  lazy val hotelEntryWithChildPolicyWithoutFreeChildren = aValidHotelEntryModel.copy(
    rooms = List(roomWith1ChildPolicy, aValidRoomEntry),
    metaData = aValidHotelInfo.copy(
      enabledRoom = Map(0L -> aValidEnabledRoomWith0MAFC),
    ),
    reqOcc = aValidHotelEntryModel.reqOcc.copy(agePolicy = AgePolicy(0, 3, 0, true)),
  )
  lazy val hotelEntryWithChildPolicyWithFreeChildren = aValidHotelEntryModel.copy(
    rooms = List(roomWith1ChildPolicy, aValidRoomEntry),
    metaData = aValidHotelInfo.copy(
      enabledRoom = Map(
        0L -> aValidEnabledRoom.copy(
          maxAllowedFreeChildren = Some(1),
        )),
    ),
    reqOcc = aValidHotelEntryModel.reqOcc.copy(agePolicy = AgePolicy(0, 3, 0, true)),
  )
  lazy val hotelEntryWith2ChildPolicy = aValidHotelEntryModel.copy(
    rooms = List(roomWith2ChildPolicy),
    metaData = aValidHotelInfo.copy(
      enabledRoom = Map(0L -> aValidEnabledRoomWith0MAFC),
    ),
    reqOcc = aValidHotelEntryModel.reqOcc.copy(agePolicy = AgePolicy(0, 3, 0, true)),
  )
  lazy val hotelEntryWithInvalidChildPolicy = aValidHotelEntryModel.copy(
    rooms = List(invalidChildPolicy),
    metaData = aValidHotelInfo.copy(
      enabledRoom = Map(0L -> aValidEnabledRoomWith0MAFC),
    ),
    reqOcc = aValidHotelEntryModel.reqOcc.copy(agePolicy = AgePolicy(0, 3, 0, true)),
  )

  lazy val aValidOccupancyUnit = OccupancyUnit(
    roomNo = 1,
    chargeType = ChargeType.Room,
    subChargeType = SubChargeType.Adult,
    childAge = None,
    childAgeRangeId = None,
    payingAgeType = PayingAgeType.AsAdultRate,
    qty = 1,
  )
}

object YplDataExample extends YplDataExample
