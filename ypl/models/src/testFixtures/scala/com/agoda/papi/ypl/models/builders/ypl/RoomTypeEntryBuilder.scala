package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.{RoomTypeEntry, YcsRoomCapacity}

case class RoomTypeEntryBuilder(build: RoomTypeEntry) extends Builder[RoomTypeEntry] {
  def withMaxOccupancy(maxOcc: Int): B = build.copy(maxOccupancy = maxOcc)
  def withMaxExtraBed(maxExtraBed: Int): B = build.copy(maxExtraBed = maxExtraBed)
  def withMaxChildrenInRoom(maxChildrenInRoom: Int): B = build.copy(maxChildrenInRoom = maxChildrenInRoom)
  def withMaxAdultsOccupancy(maxAdultsOccupancy: Int): B = build.copy(maxAdultsOccupancy = Some(maxAdultsOccupancy))
  def withMaxChildrenOccupancy(maxChildrenOccupancy: Int): B =
    build.copy(maxChildrenOccupancy = Some(maxChildrenOccupancy))
  def withYcsRoomCapacity(ycsRoomCapacity: Option[YcsRoomCapacity]): B = build.copy(ycsRoomCapacity = ycsRoomCapacity)
  type B = RoomTypeEntryBuilder
}
