package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.Promotion.Restriction
import com.agoda.protobuf.cache.Promotion.Restriction.{CustomerSegment, PeriodInterval}

case class OTAPromotionRestrictionBuilder(build: Restriction) extends Builder[Restriction] {
  def withBookOn(bookon: Option[String]): B = build.copy(bookOn = bookon)
  def withMinAdvance(minAdv: Option[Int]): B = build.copy(minAdvance = minAdv)
  def withMaxAdvance(maxAdv: Option[Int]): B = build.copy(maxAdvance = maxAdv)
  def withCustomerSegment(customerSegments: Seq[CustomerSegment]): B = build.copy(customerSegments = customerSegments)
  def withBookingInterval(bookingInterval: Seq[PeriodInterval]): B = build.copy(bookingIntervals = bookingInterval)
  def withMinRooms(minRooms: Option[Int]): B = build.copy(minRooms = minRooms)
  def withMinNights(minNight: Option[Int]): B = build.copy(minNight = minNight)

  type B = OTAPromotionRestrictionBuilder
}
