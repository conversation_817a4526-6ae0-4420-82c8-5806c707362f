package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.ypl.models.{
  CountryId,
  HotelFeatures,
  SupplierId,
  SupplierInfo,
  YPLHotel,
  YPLRoom,
  YplReqOccByHotelAgePolicy,
}
import monocle.Lens
import org.joda.time.LocalTime

object HotelLens {
  type A = YPLHotel
  val hotelIdLens = Lens[A, Long](_.id)(id => hotel => hotel.copy(id = id))
  val supplierIdLens = Lens[A, Int](_.supplierId)(id => hotel => hotel.copy(supplierId = id))
  val roomsLens = Lens[A, List[YPLRoom]](_.rooms)(rooms => hotel => hotel.copy(rooms = rooms))
  val hotelPolicyLens = Lens[A, String](_.hotelPolicy)(policy => hotel => hotel.copy(hotelPolicy = policy))
  val paymentModelLens = Lens[A, PaymentModel](_.paymentModel)(pm => hotel => hotel.copy(paymentModel = pm))
  val bookingCutoffLens =
    Lens[A, Option[LocalTime]](_.bookingCutoffTime)(cutoff => hotel => hotel.copy(bookingCutoffTime = cutoff))
  val gmtOffsetLens = Lens[A, Int](_.gmtOffset)(offset => hotel => hotel.copy(gmtOffset = offset))
  val gmtOffsetMinutesLens = Lens[A, Int](_.gmtOffsetMinutes)(offset => hotel => hotel.copy(gmtOffsetMinutes = offset))
  val featureLens = Lens[A, HotelFeatures](_.features)(features => hotel => hotel.copy(features = features))
  val reqOccLens = Lens[A, YplReqOccByHotelAgePolicy](_.reqOcc)(occ => hotel => hotel.copy(reqOcc = occ))
  val supplierMapLens =
    Lens[A, Map[SupplierId, SupplierInfo]](_.suppliers)(suppliers => hotel => hotel.copy(suppliers = suppliers))
  val countryIdLens = Lens[A, CountryId](_.countryId)(country => hotel => hotel.copy(countryId = country))

}
