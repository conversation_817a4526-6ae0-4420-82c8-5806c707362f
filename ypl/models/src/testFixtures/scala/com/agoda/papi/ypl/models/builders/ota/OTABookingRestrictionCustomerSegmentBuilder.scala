package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.LanguageId
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.BookingRestriction.CustomerSegment

case class OTABookingRestrictionCustomerSegmentBuilder(build: CustomerSegment) extends Builder[CustomerSegment] {
  def withLanguageId(languageId: Option[LanguageId]): B = build.copy(languageId = languageId)
  def withCountryCode(countryCode: Option[String]): B = build.copy(countryCode = countryCode)

  type B = OTABookingRestrictionCustomerSegmentBuilder
}
