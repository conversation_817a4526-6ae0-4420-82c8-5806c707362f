package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.finance.tax.enums.TaxLevelCalculationType
import com.agoda.finance.tax.models.TaxPrototypeInfo
import com.agoda.papi.enums.room.{
  ApplyTaxOver,
  ChargeOption,
  GeoType,
  ValueCalculationMethodType,
  ValueMethodType,
  WhomToPayType,
}
import com.agoda.papi.pricing.pricecalculation.models.tax.Tax
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto._

case class TaxBuilder(build: Tax) extends Builder[Tax] {
  def withTaxId(id: TaxID): B = build.copy(id = id)
  def withApplyTo(applyTo: String): B = build.copy(applyTo = applyTo)
  def withIsAmount(isAmount: Boolean): B = build.copy(isAmount = isAmount)
  def withIsFee(isFee: Boolean): B = build.copy(isFee = isFee)
  def withIsTaxAble(isTaxable: Boolean): B = build.copy(isTaxable = isTaxable)
  def withTaxValue(value: TaxValue): B = build.copy(value = value)
  def withChargeOption(option: ChargeOption): B = build.copy(option = option)
  def withTaxPrototypeId(id: TaxProtoTypeID): B = build.copy(protoTypeId = id)
  def withTaxPrototypeInfo(info: Option[TaxPrototypeInfo]): B = build.copy(taxPrototypeInfo = info)
  def withApplyOver(applyOver: ApplyTaxOver): B = build.copy(applyOver = Some(applyOver))
  def withWhomToPay(whomToPay: WhomToPayType): B = build.copy(whomToPay = Some(whomToPay))
  def withOrderNumber(orderNumber: Option[Int]): B = build.copy(orderNumber = orderNumber)
  def withTaxLevelCalculationType(calculationType: Option[TaxLevelCalculationType]): B =
    build.copy(taxLevelCalculationType = calculationType)
  def withValueMethod(valueMethod: Option[ValueMethodType]): B = build.copy(valueMethod = valueMethod)
  def withValueCalculationMethodType(methodType: Option[ValueCalculationMethodType]): B =
    build.copy(valueCalculationMethodType = methodType)
  def withGeoId(id: Option[Int]): B = build.copy(geoId = id)
  def withGeoType(geo: Option[GeoType]): B = build.copy(geoType = geo)

  type B = TaxBuilder
}
