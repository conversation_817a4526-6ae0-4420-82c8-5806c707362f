package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.finance.tax.models.TaxPrototypeLevel
import com.agoda.papi.ypl.models.builders.Builder

case class TaxPrototypeLevelBuilder(build: TaxPrototypeLevel) extends Builder[TaxPrototypeLevel] {
  def withTaxAmount(amount: Double): B = build.copy(taxValue = amount)
  def withRateStart(rateStart: Double): B = build.copy(rateStart = rateStart)
  def withRateEnd(rateEnd: Double): B = build.copy(rateEnd = rateEnd)
  def withIsAmount(isAmount: Boolean): B = build.copy(isAmount = isAmount)
  def withLevel(level: Int): B = build.copy(level = level)

  type B = TaxPrototypeLevel
}
