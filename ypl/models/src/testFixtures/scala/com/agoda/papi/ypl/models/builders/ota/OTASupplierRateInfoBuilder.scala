package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.SupplierRateInfo
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.SupplierRateInfo.ExternalData

case class OTASupplierRateInfoBuilder(build: SupplierRateInfo) extends Builder[SupplierRateInfo] {
  def withSupplierRatePlan(supplierRateplan: Option[String]): B = build.copy(supplierRatePlan = supplierRateplan)
  def withSupplierMealPlan(supplierMealPlan: Option[String]): B = build.copy(supplierMealPlan = supplierMealPlan)
  def withExternalData(externalData: Seq[ExternalData]): B = build.copy(externalData = externalData)

  type B = OTASupplierRateInfoBuilder
}
