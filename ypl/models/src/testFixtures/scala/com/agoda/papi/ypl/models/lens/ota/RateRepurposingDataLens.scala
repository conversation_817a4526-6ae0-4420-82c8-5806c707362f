package com.agoda.papi.ypl.models.lens.ota

import com.agoda.protobuf.cache.RateRepurposingData
import monocle.Lens

object RateRepurposingDataLens {
  type A = RateRepurposingData
  val targetChannelLens =
    Lens[A, Int](_.targetChannel)(channel => raterepurpose => raterepurpose.copy(targetChannel = channel))
  val channelDiscountLens =
    Lens[A, Double](_.channelDiscount)(discount => raterepurpose => raterepurpose.copy(channelDiscount = discount))
}
