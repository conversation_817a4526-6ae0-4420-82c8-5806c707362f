package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.YplChannel

case class YplChannelBuilder(build: YplChannel) extends Builder[YplChannel] {
  def withBaseChannel(baseChannelId: Int): YplChannel = build.copy(baseChannelId = baseChannelId)
  def withStackChannels(stackedChannelIds: Set[Int]): YplChannel = build.copy(stackedChannelIds = stackedChannelIds)

  type B = YplChannelBuilder
}
