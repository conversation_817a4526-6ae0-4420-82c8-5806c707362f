package com.agoda.papi.ypl.models.lens.ota

import com.agoda.protobuf.cache._
import com.agoda.protobuf.common._
import monocle.Lens

object HotelPriceLens {
  type A = HotelPrice
  val hotelIdLens = Lens[A, Int](_.hotelId.toInt)(hotelId => hotelPrice => hotelPrice.copy(hotelId = hotelId))
  val checkInLens = Lens[A, Long](_.checkInMillis)(checkIn => hotelPrice => hotelPrice.copy(checkInMillis = checkIn))
  val losLens = Lens[A, Int](_.lengthOfStay)(los => hotelPrice => hotelPrice.copy(lengthOfStay = los))
  val supplierIdLens = Lens[A, Int](_.supplierId)(id => hotelPrice => hotelPrice.copy(supplierId = id))
  val occupancyModelLens =
    Lens[A, OccupanyModel](_.occupancyModel)(occ => hotelPrice => hotelPrice.copy(occupancyModel = occ))
  val taxTypeLens = Lens[A, TaxType](_.taxType)(tax => hotelPrice => hotelPrice.copy(taxType = tax))
  val paymentModelLens = Lens[A, PaymentModel](_.paymentModel)(pm => hotelPrice => hotelPrice.copy(paymentModel = pm))
  val surchargeRateLoadTypeLens = Lens[A, Option[RateType]](_.surchargeRateLoadType)(loadType =>
    hotelPrice => hotelPrice.copy(surchargeRateLoadType = loadType))
  val channelRoomRateListLens =
    Lens[A, Seq[ChannelRoomRate]](_.channelRoomRate)(rooms => hotelPrice => hotelPrice.copy(channelRoomRate = rooms))
  val roomTypesLens =
    Lens[A, Seq[RoomType]](_.roomTypes)(roomTypes => hotelPrice => hotelPrice.copy(roomTypes = roomTypes))
  val versionLens = Lens[A, Option[Float]](_.version)(version => hotelPrice => hotelPrice.copy(version = version))
  val isUseConfigureProcessingFeeLens = Lens[A, Option[Boolean]](_.isUseConfiguredProcessingFee)(isUse =>
    hotelPrice => hotelPrice.copy(isUseConfiguredProcessingFee = isUse))
  val sourceTypeLens =
    Lens[A, Option[SourceType]](_.sourceType)(srcType => hotelPrice => hotelPrice.copy(sourceType = srcType))
  val expireAtLens = Lens[A, Option[Long]](_.expiresAt)(expire => hotelPrice => hotelPrice.copy(expiresAt = expire))
  val isPromotionCombinableLens = Lens[A, Option[Boolean]](_.isPromotionCombinable)(combinable =>
    hotelPrice => hotelPrice.copy(isPromotionCombinable = combinable))
  val firedrillLens = Lens[A, Option[FireDrill]](_.fireDrill)(fd => hotelPrice => hotelPrice.copy(fireDrill = fd))
  val rateRepurposedListLens = Lens[A, Seq[RateRepurposingData]](_.rateRepurposeList)(repurposeList =>
    hotelPrice => hotelPrice.copy(rateRepurposeList = repurposeList))
  val supplierContractedCommissionLens =
    Lens[A, Option[Double]](_.supplierContractedCommission)(supplierContractedCommission =>
      hotelPrice => hotelPrice.copy(supplierContractedCommission = supplierContractedCommission))
  val propertyBenefitsLens = Lens[A, Seq[Benefit]](_.propertyBenefits)(propertyBenefits =>
    hotelPrice => hotelPrice.copy(propertyBenefits = propertyBenefits))
}
