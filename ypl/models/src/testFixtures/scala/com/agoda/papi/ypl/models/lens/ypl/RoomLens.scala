package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.enums.hotel.{PaymentModel, PaymentOption}
import com.agoda.papi.enums.room.CancellationChargeSettingType
import com.agoda.papi.ypl.models.context.CompositeChannelContext
import com.agoda.papi.ypl.models.hotel.{DiscountInfo, RateCategory, YplFireDrillContract}
import com.agoda.papi.ypl.models.pricing.{RoomOccupancy, YplPrice}
import com.agoda.papi.ypl.models.{
  MarginPercent,
  RateCategoryId,
  RoomFeatures,
  RoomTypeId,
  RoomUID,
  SupplierId,
  YPLRoom,
  YplReqOccByHotelAgePolicy,
  YplRoomEntry,
}
import monocle.{Getter, Lens}

object RoomLens {

  // default, used for testing
  implicit val compositeChannelContext = new CompositeChannelContext {
    def getCompositeChannelId(baseChannelId: Int, stackedChannelIds: Set[Int] = Set.empty): Int = baseChannelId
    def toCompositeChannelIds(channelId: Int): Set[Int] = Set(channelId)
  };

  type A = YPLRoom
  // RoomID   | Supplier | PaymentModel | ChannelID | RatePlanID | Occ | Promotion | Cancellation | display.perBook.allInclusive | display.perRoomPerNight.allInclusive | CmsDiscountTypeId | DiscountValue | CmsTypeid |
  val roomTypeIdLens = Lens[A, RoomTypeId](_.roomTypeId)(id => room => room.copy(roomTypeId = id))
  val supplierIdLens = Lens[A, SupplierId](_.supplierId)(id => room => room.copy(supplierId = id))
  val paymentModelLens = Lens[A, PaymentModel](_.paymentModel)(pm => room => room.copy(paymentModel = pm))
  val occLens = Lens[A, RoomOccupancy](_.occ)(occ => room => room.copy(occ = occ))
  val discountInfoLens = Lens[A, DiscountInfo](_.discountInfo)(discount => room => room.copy(discountInfo = discount))
  val cxlLens = Lens[A, String](_.cxlCode)(code => room => room.copy(cxlCode = code))
  val priceLens = Lens[A, List[YplPrice]](_.prices)(prices => room => room.copy(prices = prices))
  val rateCategoryIdLens = Lens[A, RateCategoryId](_.rateCategoryId)(id => room => room.copy(rateCategoryId = id))
  val rateCategoryLens = Lens[A, RateCategory](_.rateCategory)(rc => room => room.copy(rateCategory = rc))
  val paymentOptionLens =
    Lens[A, Set[PaymentOption]](_.paymentOptions)(options => room => room.copy(paymentOptions = options))
  val marginPercentLens =
    Lens[A, MarginPercent](_.marginPercentage)(marginPercent => room => room.copy(marginPercentage = marginPercent))
  val isFireDrillLens = Lens[A, Boolean](_.isFireDrill)(isFireDrill => room => room.copy(isFireDrill = isFireDrill))
  val fireDrillContractLens =
    Lens[A, Option[YplFireDrillContract]](_.fireDrillContract)(fdc => room => room.copy(fireDrillContract = fdc))
  val commissionEquivalentLens =
    Lens[A, Option[Double]](_.commissionEquivalent)(commission => room => room.copy(commissionEquivalent = commission))
  val roomUidLens = Getter[A, RoomUID](_.uid)
  val cxlChargeSettingLens = Lens[A, Option[CancellationChargeSettingType]](_.cxlChargeSetting)(code =>
    room => room.copy(cxlChargeSetting = code))
  val noCreditCardLens = Lens[A, Boolean](_.noCreditCard)(isNoCC => room => room.copy(noCreditCard = isNoCC))
  val prepayamentRequiredLens = Lens[A, Boolean](_.prepaymentRequired)(isPrepaymentRequired =>
    room => room.copy(prepaymentRequired = isPrepaymentRequired))
  val allotmentLens = Lens[A, Int](_.allotment)(allotment => room => room.copy(allotment = allotment))
  val reqOccLens = Lens[A, Option[YplReqOccByHotelAgePolicy]](_.reqOcc)(reqOcc => room => room.copy(reqOcc = reqOcc))
  val roomFeaturesLens =
    Lens[A, RoomFeatures](_.roomFeatures)(roomFeatures => room => room.copy(roomFeatures = roomFeatures))
  // val channelLens = Lens[A, YplChannel](_.channel)(ch => room => room.copy(channel = ch))
  // val channelIdsLens = Lens[A, YplChannel](_.channel)(ch => room => room.copy(channel = ch))
  val channelIdsLens = Getter[A, String] { a =>
    (List(a.channel.baseChannelId) ++ a.channel.stackedChannelIds.toList.sorted).mkString(",")
  }
  val promotionIdsLens = Getter[A, String] { a =>
    a.allPromotions.map(_.id).toList.sorted.mkString(",")
  }
  val hasPromotionLens = Getter[A, Boolean](_.hasPromotion)
  val yplRoomEntryLens = Lens[A, YplRoomEntry](_.yplRoomEntry)(yplEntry => room => room.copy(yplRoomEntry = yplEntry))
  val fencesLens = Getter[A, String] { room =>
    if (room.fences.isEmpty) "All blocked" else room.fences.map(_.asString).toList.sorted.mkString(", ")
  }
}
