package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.RoomBookings
import com.agoda.papi.ypl.models.builders.Builder

case class YplRoomBookings(
  inDays: Int,
  count: Int,
) extends RoomBookings

case class RoomBookingBuilder(build: YplRoomBookings) extends Builder[YplRoomBookings] {
  def withInDays(value: Int): B = build.copy(inDays = value)

  def withCount(value: Int): B = build.copy(count = value)

  type B = YplRoomBookings
}
