package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.occupancy.MaxAllowedFreeChildAgeRange

case class MaxAllowedFreeChildAgeRangeBuilder(build: MaxAllowedFreeChildAgeRange)
  extends Builder[MaxAllowedFreeChildAgeRange] {
  def withAgeFrom(ageFrom: Int): B = build.copy(ageFrom = ageFrom)
  def withAgeTo(ageTo: Int): B = build.copy(ageTo = ageTo)
  def withQuantity(quantity: Int): B = build.copy(quantity = quantity)
  type B = MaxAllowedFreeChildAgeRangeBuilder
}
