package com.agoda.papi.ypl.models.builders.implicits

import com.agoda.papi.ypl.commission.builder.implicits.CommissionImplicitTestDataBuilder
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.occupancy.builder.implicites.OccupancyImplicitTestDataBuilder

trait ImplicitTestDataBuilder
  extends YPLImplicitTestDataBuilder
    with OTAImplicitTestDataBuilder
    with CommissionImplicitTestDataBuilder
    with OccupancyImplicitTestDataBuilder {
  implicit def fromBuilder[A](builder: Builder[A]): A = builder.build
}

object ImplicitTestDataBuilder extends ImplicitTestDataBuilder
