package com.agoda.papi.ypl.models.builders.ota

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto.TaxProtoTypeID
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice.TaxAndFee
import com.agoda.protobuf.common.ApplyType

case class OTATaxAndFeeBuilder(build: TaxAndFee) extends Builder[TaxAndFee] {
  def withTaxId(taxId: Int): B = build.copy(taxId = taxId)
  def withApplyTo(applyTo: String): B = build.copy(applyTo = applyTo)
  def withApplyType(applyType: ApplyType): B = build.copy(applyType = applyType)
  def withIsFee(isFee: Boolean): B = build.copy(isFee = isFee)
  def withIsAmount(isAmount: Boolean): B = build.copy(isAmount = isAmount)
  def withValue(value: Double): B = build.copy(value = value)
  def withIsTaxable(isTaxable: Boolean): B = build.copy(isTaxable = isTaxable)
  def withTaxProtoTypeId(taxProtoTypeId: Option[TaxProtoTypeID]): B = build.copy(taxPrototypeId = taxProtoTypeId)
  type B = OTATaxAndFeeBuilder
}
