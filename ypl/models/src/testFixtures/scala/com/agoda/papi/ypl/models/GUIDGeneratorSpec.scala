package com.agoda.papi.ypl.models

import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.enums.room.{RateType, RepurposeDiscountMethod}
import com.agoda.papi.ypl.models.GUIDGenerator.{CoreFields, ExtendFields, RoomIdentifiers, RoomSelectionFields}
import com.agoda.papi.ypl.models.builders.ypl.YplDataExample
import com.agoda.papi.ypl.models.hotel.{DiscountInfo, DmcData, Promotion, RateCategory}
import com.agoda.papi.ypl.models.pricing.{RoomOccupancy, YplRateRepurposeInfo}
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit

object GUIDGeneratorSpec extends YplDataExample {
  def createYPLRoom(roomTypeId: RoomTypeId,
                    channel: YplChannel,
                    occupancy: Int,
                    breakfastInclude: Boolean,
                    cancellationCode: String,
                    supplierId: Int,
                    promotionId: Int,
                    rateCategoryId: Option[Int] = None,
                    prius: Option[YplPriusOutput] = None,
                    rateRepurposeInfo: Option[YplRateRepurposeInfo] = None,
                    dmcUID: Option[String] = None,
                    subSupplierId: Option[Int] = None,
                    hotelId: Int = -1,
                    isCxlDuplicated: Boolean = false,
                    isAgodaRefund: Boolean = false): YPLRoom = {
    val partialRefundInfo = createPartialRefundInfo(isCxlDuplicated)

    val rateCategory =
      RateCategory(rateCategoryId.getOrElse(-1), None, None, false, "", 0.0, List(), "moola", None, false)

    YPLRoom(
      hotelId = hotelId,
      roomTypeId = roomTypeId,
      supplierId = supplierId,
      channel = channel,
      language = 0,
      currency = "",
      checkIn = DateTime.now(),
      lengthOfStay = 1,
      occ = RoomOccupancy(occupancy, 0),
      marginPercentage = 0,
      cxlCode = cancellationCode,
      allotment = 1,
      breakfast = breakfastInclude,
      prices = List.empty,
      rateCategoryId = rateCategoryId.getOrElse(0),
      paymentModel = PaymentModel.Agency,
      dmcData = dmcUID.map(x => DmcData(dmcUID = x)),
      rateType = RateType.Unknown,
      priusOutput = prius,
      rateCategory = rateCategory,
      rateRepurposeInfos = rateRepurposeInfo.toSeq,
      discountInfo = DiscountInfo(promotion = Some(Promotion(id = promotionId))),
      subSupplierId = subSupplierId,
      originalRateType = RateType.Unknown,
      processingFeePercent = 0.0,
      partialRefundInfo = partialRefundInfo,
      isAgodaRefund = isAgodaRefund,
      yplRoomEntry = aValidRoomEntry,
    )
  }

  def createPartialRefundInfo(isCxlDuplicated: Boolean) =
    if (isCxlDuplicated) {
      Some(YplPartialRefundInfo("", "", "", true))
    } else {
      None
    }

}

/**
  * Created by vpathomsuriy on 7/8/2016 AD.
  */
class GUIDGeneratorSpec extends SpecificationWithJUnit with YPLTestDataBuilders {
  import GUIDGeneratorSpec._

  "GUIDGenerator" should {

    "ORM - normal room" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        occupancy = 2,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "0f398530-7db0-f8c7-77c1-29dbc48e3749"
    }

    "ORM - promotion room, non refund" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 1234,
        channel = YplMasterChannel.RTL,
        occupancy = 2,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "365D100P_100P",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "7a0c6f14-e9e2-292e-be14-d886a04e726d"
    }

    "ORM - APS room with breakfast" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.APS,
        occupancy = 3,
        breakfastInclude = true,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "12761b82-b4d5-8fff-5058-b201ad7b034e"
    }

    "NRM - normal room" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        rateCategoryId = Some(1234),
        occupancy = 3,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "b8269c07-56aa-4d83-aa6b-3ccc47ac03b8"
    }

    "NRM - promotion room, non refund" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 1234,
        channel = YplMasterChannel.RTL,
        rateCategoryId = Some(1234),
        occupancy = 3,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "365D100P_100P",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "41844eb8-4cd2-4864-7087-d4981aaaa839"
    }

    "NRM - APS room with breakfast" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.APS,
        rateCategoryId = Some(1234),
        occupancy = 3,
        breakfastInclude = true,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "5f47ba93-8eb1-6f01-155f-2c6ee84c68b7"
    }

    "NRM - reutilized" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        rateCategoryId = Some(1234),
        occupancy = 3,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
        rateRepurposeInfo = Some(
          YplRateRepurposeInfo(
            isReutilized = true,
            reUtilChannel = YplMasterChannel.APO,
            reUtilRoomUID = "",
            refChannel = YplMasterChannel.RTL,
            refRoomUID = "",
            srcChannel = YplMasterChannel.APS,
            srcRoomUID = "",
            repurposeDiscountMethod = RepurposeDiscountMethod.FlatDiscount,
            discountPercent = 0,
          )),
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "0810bc44-558a-900e-34ec-e53f9109bb20"
    }

    "NRM - normal room with reutilized hint" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        rateCategoryId = Some(1234),
        occupancy = 3,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
        rateRepurposeInfo = Some(
          YplRateRepurposeInfo(
            isReutilized = false,
            reUtilChannel = YplMasterChannel.APO,
            reUtilRoomUID = "",
            refChannel = YplMasterChannel.RTL,
            refRoomUID = "",
            srcChannel = YplMasterChannel.APS,
            srcRoomUID = "",
            repurposeDiscountMethod = RepurposeDiscountMethod.FlatDiscount,
            discountPercent = 0,
          )),
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "b8269c07-56aa-4d83-aa6b-3ccc47ac03b8"
    }

    "DMC - normal promotion room" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 1234,
        channel = YplMasterChannel.RTL,
        occupancy = 2,
        breakfastInclude = false,
        supplierId = 27800,
        cancellationCode = "365D100P_100P",
        prius = None,
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "89fc64b3-2bde-9c0b-ff72-7d882a550cfa"
    }

    "DMC - room id 0" in {
      val mockRoom = createYPLRoom(roomTypeId = 0,
                                   promotionId = 0,
                                   channel = YplMasterChannel.RTL,
                                   occupancy = 2,
                                   breakfastInclude = false,
                                   supplierId = 3038,
                                   cancellationCode = "1D1N_1N",
                                   prius = None)
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "12de61a2-ecaf-7f5a-b73e-d361e8a4d2eb"
    }

    "DMC - no cancellation code" in {
      val mockRoom = createYPLRoom(roomTypeId = 10001,
                                   promotionId = 0,
                                   channel = YplMasterChannel.RTL,
                                   occupancy = 2,
                                   breakfastInclude = true,
                                   supplierId = 27802,
                                   cancellationCode = "",
                                   prius = None)
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "1219f3af-a1a3-10f5-6bcc-a3d2d9634461"
    }

    "DMC - room with dmcUID" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        occupancy = 2,
        breakfastInclude = true,
        supplierId = 27802,
        cancellationCode = "",
        prius = None,
        dmcUID = Some("1|2|3|4|5"),
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "9b016863-e22d-0b9b-81b9-c5bfd9023434"
    }

    "DMC - room with dmcUID, subSupplier" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        occupancy = 2,
        breakfastInclude = true,
        supplierId = 27802,
        cancellationCode = "",
        prius = None,
        dmcUID = Some("1|2|3|4|5"),
        subSupplierId = Some(28999),
      )
      GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "daee5163-bf02-2d2e-e177-36cd7a527d06"
    }

    "NRM - room with cxl duplicated flag is TRUE and Agoda Refund is TRUE" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        rateCategoryId = Some(1234),
        occupancy = 3,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
        isCxlDuplicated = true,
        isAgodaRefund = true,
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "008cf01e-6216-7886-b016-f53dadfdd3dd"
    }

    "NRM - room with cxl duplicated flag is TRUE and Agoda Refund is FALSE" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        rateCategoryId = Some(1234),
        occupancy = 3,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
        isCxlDuplicated = true,
        isAgodaRefund = false,
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "875ea8a3-4588-decb-c45f-b39ecac0dd49"
    }

    "NRM - room with cxl duplicated flag is FALSE and Agoda Refund is TRUE" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        rateCategoryId = Some(1234),
        occupancy = 3,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
        isCxlDuplicated = false,
        isAgodaRefund = true,
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "7f631289-5df7-c506-35a6-1ca5a7d81ae5"
    }

    "NRM - room with cxl duplicated flag is FALSE and Agoda Refund is FALSE" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        rateCategoryId = Some(1234),
        occupancy = 3,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
        isCxlDuplicated = false,
        isAgodaRefund = false,
      )
      com.agoda.papi.ypl.models
        .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "b8269c07-56aa-4d83-aa6b-3ccc47ac03b8"
    }

    "NRM - room with cxl duplicated flag is FALSE and Agoda Refund is FALSE different Channels:" in {

      def genRoom(isCxlDuplicated: Boolean, isAgodaRefund: Boolean, yplChannel: YplChannel): YPLRoom = {
        val partialRefundInfo = if (isCxlDuplicated) Some(aValidPartialRefundInfo) else None
        aValidRoom.withPartialRefundInfo(partialRefundInfo).withAgodaAgency(false).withChannel(yplChannel).build
      }
      val masterChannel2 = YplMasterChannel(2)
      val compositeChannel26 = YplChannel(2, Set(6), 100)
      val compositeChannel62 = YplChannel(6, Set(2), 100)

      "on Master Channel" in {
        val mockRoom = genRoom(false, false, masterChannel2)
        com.agoda.papi.ypl.models
          .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "a614488b-5d11-f2b4-e865-c2a0d3ff2a67"
      }
      "on Composite Channel 2, 6" in {
        val mockRoom = genRoom(false, false, compositeChannel26)
        com.agoda.papi.ypl.models
          .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "a29e2c73-c081-69b2-8203-a96072b03310"
      }
      "on Composite Channel 6, 2" in {
        val mockRoom = genRoom(false, false, compositeChannel62)
        com.agoda.papi.ypl.models
          .GUIDGenerator(mockRoom.toRoomIdentifiers()) shouldEqual "1322c09b-89c7-0ec2-d254-64bdf6f3c5b7"
      }
    }

    "sanitizeCompositeChannelIds correcty and backward compatible where single master channel will not have composite channel IDs" in {
      GUIDGenerator.sanitizeCompositeChannelIds(Nil) shouldEqual Nil
      GUIDGenerator.sanitizeCompositeChannelIds(List(1)) shouldEqual Nil
      GUIDGenerator.sanitizeCompositeChannelIds(List(1, 2)) shouldEqual List(1, 2)
    }

    "equals operator overridden should compare only specific fields not entire object " in {
      val coreFields = CoreFields(
        roomId = 1,
        paymentModel = 1,
        promotionId = 1,
        channelId = 1,
        rateCategoryId = 1,
        occupancy = 1,
        extrabed = 1,
        breakfast = true,
        cxlCode = "cxlCode",
        supplierId = 332,
        isPriusOutput = true,
        isRepurposed = true,
        srcChannelId = 1,
        refChannelId = 1,
        dmcUid = "dmcUid",
      )

      val extendFields = ExtendFields(subSupplierId = Some(1),
                                      isCxlDuplicated = true,
                                      isAgodaRefund = true,
                                      compositeChannelIds = Nil,
                                      paidChildAges = Nil,
                                      resellSourceBookingId = None)

      val roomSelectionFields = Some(
        RoomSelectionFields(
          children = 1,
          maxExtraBed = 1,
          isRespectMaxOcc = true,
          needOccupancySearch = true,
          masterRoomOccupancy = 1,
          isFit = true,
        ))
      "equals return true when every field in roomiden field is matched" in {
        val roomIden1 = RoomIdentifiers(coreFields, extendFields, roomSelectionFields)
        val roomIden2 = roomIden1
        roomIden1.equals(roomIden2) should_== true
        roomIden1.hashCode() should_== roomIden2.hashCode()
      }
      "equals return true when core and extend field in roomiden field is matched" in {
        val roomIden1 = RoomIdentifiers(coreFields, extendFields, roomSelectionFields)
        val roomIden2 = RoomIdentifiers(coreFields, extendFields, None)
        roomIden1.equals(roomIden2) should_== true
        roomIden1.hashCode() shouldEqual roomIden2.hashCode()
      }
      "equals return false when core and extend field in roomiden field is mismatched" in {
        val roomIden1 = RoomIdentifiers(coreFields, extendFields, roomSelectionFields)
        val roomIden2 = RoomIdentifiers(coreFields.copy(occupancy = 2), extendFields, None)
        roomIden1.equals(roomIden2) should_== false
        roomIden1.hashCode() shouldNotEqual roomIden2.hashCode()
      }
      "equals return false when compare different object" in {
        val roomIden = RoomIdentifiers(coreFields, extendFields, roomSelectionFields)
        roomIden.equals(1) should_== false
        roomIden.hashCode() shouldNotEqual 1.hashCode()
      }
    }

    "Room with paidChildAges information" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        occupancy = 2,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
      )
      val roomIdentifier = mockRoom.toRoomIdentifiers()
      val roomIdentifierWithPaidChildAges =
        roomIdentifier.copy(extendFields = roomIdentifier.extendFields.copy(paidChildAges = List(2, 3, 4)))

      val roomUID = com.agoda.papi.ypl.models.GUIDGenerator(roomIdentifier)
      val roomWithPaidChildAgesUID = com.agoda.papi.ypl.models.GUIDGenerator(roomIdentifierWithPaidChildAges)
      roomUID shouldEqual "0f398530-7db0-f8c7-77c1-29dbc48e3749"
      roomWithPaidChildAgesUID shouldEqual "e17c3f8d-b570-9032-77b4-be6c1fbfe3f2"
    }

    "Resell - room with resell information" in {
      val mockRoom = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        occupancy = 2,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
      )
      val roomIdentifier = mockRoom.toRoomIdentifiers()
      val roomIdentifierWithResellInfo =
        roomIdentifier.copy(extendFields = roomIdentifier.extendFields.copy(resellSourceBookingId = Some("1234")))

      val roomUID = com.agoda.papi.ypl.models.GUIDGenerator(roomIdentifier)
      val roomWithResellInfoUID = com.agoda.papi.ypl.models.GUIDGenerator(roomIdentifierWithResellInfo)
      roomUID shouldEqual "0f398530-7db0-f8c7-77c1-29dbc48e3749"
      roomWithResellInfoUID shouldEqual "31933fb0-b277-489f-13e4-9fca38f1875c"
    }
    "hashing DMC UID for roomidentifier, able to match both hashed/non-hashed" in {
      val mockDmcUid = "A|B|C|D|E|F|G"
      val mockRoomWithDmcUid = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        occupancy = 2,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
        dmcUID = Some(mockDmcUid),
      )

      val mockRoomWithoutDmcUid = createYPLRoom(
        roomTypeId = 10001,
        promotionId = 0,
        channel = YplMasterChannel.RTL,
        occupancy = 2,
        breakfastInclude = false,
        supplierId = 332,
        cancellationCode = "1D1N_1N",
        prius = Some(YplPriusOutput(0, 0, 0, 0, 0)),
        dmcUID = None,
      )
      val nonHashedDmcRoomIdentifier = mockRoomWithDmcUid.toRoomIdentifiers()
      val nonDmcUidRoomIdentifier = mockRoomWithoutDmcUid.toRoomIdentifiers()
      val hashedDmcRoomIdentifier = mockRoomWithDmcUid
        .copy(dmcData = mockRoomWithDmcUid.dmcData.map(_.copy(dmcUID = GUIDGenerator.md5(mockDmcUid))))
        .toRoomIdentifiers()
      // backward compatibility match
      hashedDmcRoomIdentifier.equals(nonHashedDmcRoomIdentifier) should_== true
      nonHashedDmcRoomIdentifier.equals(hashedDmcRoomIdentifier) should_== true
      // String match
      hashedDmcRoomIdentifier.equals(hashedDmcRoomIdentifier) should_== true
      // empty DMC UID match
      nonDmcUidRoomIdentifier.equals(nonHashedDmcRoomIdentifier) should_== false

      // explicit assert this case due to there is validation before calling this
      hashedDmcRoomIdentifier.matchDMCUid(mockDmcUid, mockDmcUid) should_== true
      hashedDmcRoomIdentifier.matchDMCUid("", "") should_== true
      hashedDmcRoomIdentifier.matchDMCUid(mockDmcUid, "") should_== false
      hashedDmcRoomIdentifier.matchDMCUid("ABC", "DEF") should_== false

    }
  }
}
