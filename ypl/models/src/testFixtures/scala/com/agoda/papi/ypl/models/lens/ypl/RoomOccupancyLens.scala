package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.ypl.models.pricing.RoomOccupancy
import monocle.Lens

object RoomOccupancyLens {
  type A = RoomOccupancy
  val adultLens = Lens[A, Int](_.adults)(adult => occ => occ.copy(adults = adult))
  val childrenLens = Lens[A, Int](_.children)(child => occ => occ.copy(children = child))
  val infantsLens = Lens[A, Int](_.infants)(infant => occ => occ.copy(infants = infant))
  val extraBedLens = Lens[A, Int](_.extraBeds)(eb => occ => occ.copy(extraBeds = eb))
  val maxExtraBedLens = Lens[A, Int](_.maxExtraBeds)(eb => occ => occ.copy(maxExtraBeds = eb))
  val freeKidLens = Lens[A, Int](_.freeChildrenAndInfants)(eb => occ => occ.copy(freeChildrenAndInfants = eb))
  val maxFreeKidLens =
    Lens[A, Int](_.allowedFreeChildrenAndInfants)(eb => occ => occ.copy(allowedFreeChildrenAndInfants = eb))
  val paidChildAgesLens =
    Lens[A, List[Int]](_.paidChildAges)(paidChildAges => occ => occ.copy(paidChildAges = paidChildAges))
}
