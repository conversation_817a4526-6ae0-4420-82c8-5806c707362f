package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.enums.room.DiscountType
import com.agoda.papi.ypl.models.hotel.Promotion
import monocle.Lens

object PromotionLens {
  type A = Promotion
  val promotionIdLens = Lens[A, Int](_.id)(id => promotion => promotion.copy(id = id))
  val discountTypeLens =
    Lens[A, DiscountType](_.discountType)(discountType => promotion => promotion.copy(discountType = discountType))
  val cmsTypeIdLens = Lens[A, Int](_.cmsTypeId)(cmsTypeId => promotion => promotion.copy(cmsTypeId = cmsTypeId))
  val cmsDiscountTypeIdLens = Lens[A, Int](_.cmsDiscountTypeId)(cmsDiscountTypeId =>
    promotion => promotion.copy(cmsDiscountTypeId = cmsDiscountTypeId))
  val minNightStayLens = Lens[A, Int](_.minNightStay)(minNight => promotion => promotion.copy(minNightStay = minNight))
  val valueLens = Lens[A, Double](_.value)(value => promotion => promotion.copy(value = value))
  val isRatePlanAsPromotionLens = Lens[A, Boolean](_.isRatePlanAsPromotion)(isRatePlanAsPromotion =>
    promotion => promotion.copy(isRatePlanAsPromotion = isRatePlanAsPromotion))

}
