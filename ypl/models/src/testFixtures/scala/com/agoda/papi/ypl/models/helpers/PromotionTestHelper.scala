package com.agoda.papi.ypl.models.helpers

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.ypl.models.consts.MegaSalePromotions
import com.agoda.papi.ypl.models.context.PulseCampaignMetaContext
import com.agoda.papi.ypl.models.PromotionTypeId

/**
  * Helper class for managing pulse campaign metadata in tests.
  * Consolidates common functionality for creating and updating pulse campaign metadata contexts.
  */
object PromotionTestHelper {

  def createPulseCampaignMetaContext(
    pulsePromotionTypeIds: List[Int] = Nil,
    megaSalePromotionTypeIds: List[Int] = Nil,
  ): PulseCampaignMetaContext = new PulseCampaignMetaContext {
    override def getPulseCampaignSetting(
      promotionTypeIds: List[PromotionTypeId],
    ): Map[PromotionTypeId, PulseCampaignMetadata] = {

      // Generate pulse campaign metadata (regular campaigns)
      val pulseMetadata = generatePulseMetadata(pulsePromotionTypeIds)

      // Generate mega sale campaign metadata (using MegaSale campaign type IDs)
      val megaSaleMetadata = generateMegaSaleMetadata(megaSalePromotionTypeIds)

      // Combine both maps and filter by requested promotion type IDs
      (pulseMetadata ++ megaSaleMetadata).filter { case (ptypeId, _) => promotionTypeIds.contains(ptypeId) }
    }
  }

  def generatePulseMetadata(pulsePromotionTypeIds: List[Int]): Map[PromotionTypeId, PulseCampaignMetadata] =
    pulsePromotionTypeIds.zipWithIndex.map { case (ptypeId, index) =>
      (ptypeId,
       PulseCampaignMetadata(
         webCampaignId = ptypeId,
         promotionTypeId = ptypeId,
         campaignTypeId = ptypeId, // Regular campaign type ID
         campaignBadgeCmsId = ptypeId,
         campaignBadgeDescCmsId = ptypeId,
       ))
    }.toMap

  def generateMegaSaleMetadata(megaSalePromotionTypeIds: List[Int]): Map[PromotionTypeId, PulseCampaignMetadata] = {
    val megaSaleCampaignTypeIds = MegaSalePromotions.CampaignTypeIds

    megaSalePromotionTypeIds.zipWithIndex.map { case (ptypeId, index) =>
      val campaignTypeId = megaSaleCampaignTypeIds.lift(index).getOrElse(megaSaleCampaignTypeIds.head)

      (ptypeId,
       PulseCampaignMetadata(
         webCampaignId = ptypeId,
         promotionTypeId = ptypeId,
         campaignTypeId = campaignTypeId, // Use actual MegaSale campaign type ID
         campaignBadgeCmsId = ptypeId,
         campaignBadgeDescCmsId = ptypeId,
       ))
    }.toMap
  }

}
