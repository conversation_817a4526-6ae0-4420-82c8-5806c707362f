package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.pricing.proto._
import org.joda.time.DateTime

case class DailyPriceBuilder(build: DailyPrice) extends Builder[DailyPrice] {
  def withPrices(prices: List[PriceEntry]): B = build.copy(prices = prices)
  def withChildPrices(childPrices: List[ChildPriceEntry]): B = build.copy(childPrices = childPrices)
  def withTaxes(taxes: Map[TaxIDWithProTypeID, TaxValue]): B = build.copy(taxes = taxes)
  def withRpmSurcharges(surcharges: List[SurchargeEntry]): B = build.copy(rpmSurcharges = surcharges)
  def withChannelDiscount(discount: Option[Double]): B = build.copy(channelDiscount = discount)
  def withDate(date: DateTime): B = build.copy(date = date)
  def withExtrabedPriceEntries(extrabedPrices: List[ExtrabedPriceEntry] = Nil): B =
    build.copy(extrabedPrices = extrabedPrices)
  type B = DailyPriceBuilder
}
