package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.api.request.{YplChildren, YplOccFilter, YplOccInfo, YplRoomAssignment}
import com.agoda.papi.ypl.models.builders.Builder
import com.agoda.papi.ypl.models.lens.ypl.OccInfoLens

case class OccInfoBuilder(build: YplOccInfo) extends Builder[YplOccInfo] {
  def withAdults(adults: Int): B = OccInfoLens.occInfoAdultLens.set(adults)(build)
  def withChildren(children: Option[YplChildren]): B = OccInfoLens.occInfoChildrenLens.set(children)(build)
  def withChildAges(childAges: Int*): B =
    OccInfoLens.occInfoChildrenLens.set(Some(YplChildren(ages = childAges.map(Some(_)).toList)))(build)
  def withRooms(rooms: Int): B = OccInfoLens.occInfoRoomLens.set(rooms)(build)
  def withRoomAssignment(roomAssignment: List[YplRoomAssignment]): B =
    OccInfoLens.occInfoRoomAssignmentLens.set(roomAssignment)(build)
  def withOccFilter(occFilter: Option[YplOccFilter]): B = OccInfoLens.occInfoOccFilterLens.set(occFilter)(build)

  type B = OccInfoBuilder
}
