package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.api.request.YplFeatureRequest
import com.agoda.papi.ypl.models.builders.Builder

case class YplFeatureRequestBuilder(build: YplFeatureRequest) extends Builder[YplFeatureRequest] {
  def withMaxSuggestion(maxSuggestion: Int): B = build.copy(maxSuggestions = maxSuggestion)
  def withPopulateExternalData(populateExternalData: Boolean): B =
    build.copy(populateExternalData = populateExternalData)

  type B = YplFeatureRequestBuilder
}
