package com.agoda.papi.ypl.models.builders.ypl

import com.agoda.papi.ypl.models.SupplierInfo
import com.agoda.papi.ypl.models.builders.Builder

case class YplSupplierInfoBuilder(build: SupplierInfo) extends Builder[SupplierInfo] {
  def withSupplierId(id: Int): B = build.copy(id = id)
  def withSupplierHotelId(id: Option[String]): B = build.copy(supplierHotelId = id)

  type B = YplSupplierInfoBuilder
}
