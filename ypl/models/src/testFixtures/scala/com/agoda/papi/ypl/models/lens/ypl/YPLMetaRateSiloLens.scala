package com.agoda.papi.ypl.models.lens.ypl

import com.agoda.papi.ypl.models.lens._
import com.agoda.papi.ypl.models.{ChannelsPerSupplierMap, HotelMeta, RateSilo, YPLMetaRateSilo, YplRateFence}
import monocle.{Lens, Setter}

object YPLMetaRateSiloLens {
  type A = YPLMetaRateSilo
  val hotelMetaLens = Lens[A, HotelMeta](_.meta)(hotelMeta => metaSilo => metaSilo.copy(meta = hotelMeta))
  val rateSiloLens = Lens[A, RateSilo](_.d)(silo => metaSilo => metaSilo.copy(d = silo))
  val supplierChannelsMapLens =
    Lens[A, ChannelsPerSupplierMap](_.supplierChannelsMap)(map => metaSolo => metaSolo.copy(supplierChannelsMap = map))
  val supplierChannelsMapAppendLens = Lens[A, ChannelsPerSupplierMap](_.supplierChannelsMap) { map => metaSolo =>
    val newChannelsMap = metaSolo.supplierChannelsMap.foldLeft(map) { case (map, (supplierId, channels)) =>
      val outputChannel = map.getOrElse(supplierId, channels)
      map.+((supplierId, outputChannel))
    }
    metaSolo.copy(supplierChannelsMap = newChannelsMap)
  }
  val fencedSupplierChannelMapLens =
    Lens[A, Map[YplRateFence, ChannelsPerSupplierMap]](_.fencedSupplierChannelsMap)(map =>
      metaSilo => metaSilo.copy(fencedSupplierChannelsMap = map))

  val hotelIdSetter: Setter[A, Int] = {
    val metaLens = hotelMetaLens.composeLens(HotelMetaLens.hotelIdLens)
    val siloLens = rateSiloLens.composeSetter(RateSiloLens.hotelIdSetter)
    SetterChain[A, Int](metaLens, siloLens)
  }

  val checkInLens = rateSiloLens.composeLens(RateSiloLens.checkInLens)
  val losLens = rateSiloLens.composeLens(RateSiloLens.losLens)
  val hotelPriceLens = rateSiloLens.composeLens(RateSiloLens.hotelPriceLens)
}
