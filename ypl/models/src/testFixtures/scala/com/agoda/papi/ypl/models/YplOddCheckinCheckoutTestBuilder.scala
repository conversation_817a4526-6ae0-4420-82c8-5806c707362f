package com.agoda.papi.ypl.models

case class OddCheckinCheckoutTestParams(enableRatePlanCheckinCheckoutFeatureFlag: Boolean,
                                        requireKRHotel: Boolean,
                                        isHourly: Boolean)

trait YplOddCheckinCheckoutTestBuilder {
  val testCases = List(
    OddCheckinCheckoutTestParams(enableRatePlanCheckinCheckoutFeatureFlag = true, requireKRHotel = true, isHourly = true),
    OddCheckinCheckoutTestParams(enableRatePlanCheckinCheckoutFeatureFlag = true,
                                 requireKRHotel = true,
                                 isHourly = false),
    OddCheckinCheckoutTestParams(enableRatePlanCheckinCheckoutFeatureFlag = true,
                                 requireKRHotel = false,
                                 isHourly = true),
    OddCheckinCheckoutTestParams(enableRatePlanCheckinCheckoutFeatureFlag = true,
                                 requireKRHotel = false,
                                 isHourly = false),
    OddCheckinCheckoutTestParams(enableRatePlanCheckinCheckoutFeatureFlag = false,
                                 requireKRHotel = true,
                                 isHourly = true),
    OddCheckinCheckoutTestParams(enableRatePlanCheckinCheckoutFeatureFlag = false,
                                 requireKRHotel = true,
                                 isHourly = false),
    OddCheckinCheckoutTestParams(enableRatePlanCheckinCheckoutFeatureFlag = false,
                                 requireKRHotel = false,
                                 isHourly = true),
    OddCheckinCheckoutTestParams(enableRatePlanCheckinCheckoutFeatureFlag = false,
                                 requireKRHotel = false,
                                 isHourly = false),
  )
}
