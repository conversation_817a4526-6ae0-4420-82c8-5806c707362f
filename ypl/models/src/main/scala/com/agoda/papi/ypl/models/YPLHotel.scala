package com.agoda.papi.ypl.models

import com.agoda.finance.tax.models.PriceForAssumeSellEx
import com.agoda.finance.tax.models.tax.TaxRegistrationStatus
import com.agoda.finance.tax.services.tax.GraduatedTaxCalculator
import com.agoda.papi.cancellation.models.CancellationFee
import com.agoda.papi.cancellation.models.waiver.Waiver
import com.agoda.papi.enums.hotel._
import com.agoda.papi.enums.room._
import com.agoda.papi.pricing.pricecalculation.utils.CommonTaxConverter._
import com.agoda.papi.ypl.commission.apm.models.{
  AutoPriceMatchKeyEntry,
  AutoPriceMatchPriceInfo,
  MultipleAutoPriceMatchHolder,
}
import com.agoda.papi.ypl.fencing.RoomFences
import com.agoda.papi.ypl.models.GUIDGenerator._
import com.agoda.papi.ypl.models.enums.RoomDataStep
import com.agoda.papi.ypl.models.hotel.{Benefit, _}
import com.agoda.papi.ypl.models.occupancy.OccupancyBreakdown
import com.agoda.papi.ypl.models.pricing._
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.proto.enums.{SourceType, SourceTypes}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.suppliers.externalData.YPLExternalDataDailyRate
import com.agoda.utils.collection.SumImplicits._
import com.agoda.utils.flow.PropertyContext
import org.joda.time.{DateTime, LocalTime}

case class YPLHotel(
  id: HotelId,
  checkIn: DateTime,
  lengthOfStay: Int,
  @deprecated("This should be room level property. In future it would be removed.", "After remove old DF endpoint")
  paymentModel: PaymentModel,
  @deprecated("In future it would be removed.", "After remove old DF endpoint")
  hotelPolicy: String,
  @deprecated("ypl doesnt need this")
  cityId: CityId,
  countryId: CountryId = 0,
  @deprecated("This should be room level property. In future it would be removed.", "After remove old DF endpoint")
  rateModelType: RateModel = RateModel.Old,
  rooms: List[YPLRoom],

  /**
    * This field should be used only for pure-supplier hotels objects.
    * For mixin hotels it shouldn't be a case.
    * Eventually I want to make it Optional, and then remove it.
    */
  @deprecated("This should be room level property. In future it would be removed.",
              "After remove old DF endpoint & PAPI response")
  supplierId: SupplierId = DMC.YCS,
  suppliers: Map[SupplierId, SupplierInfo] = Map.empty,
  enableBnplSuppliers: Set[SupplierId] = Set.empty,
  @deprecated("This should be room level property. In future it would be removed.", "After remove old DF endpoint")
  processingFeeOption: Int = 0,
  @deprecated("ypl doesnt need this")
  isMigrated: Boolean = false,
  @deprecated("ypl doesnt need this")
  var rankingScore: RankingScore = 0,
  @deprecated("ypl doesnt need this")
  distance: Double = 0.0,
  @deprecated("PAPI doesn't use this calculated field anyway", "After remove old DF endpoint")
  maxPriusPoints: Int = 0,
  supplierProtoCor: Cor = Map.empty,
  @deprecated("This should be room level property.")
  fireDrillCommission: Double = 0.0,
  //  set of bitwise flags
  paymentOptions: Set[PaymentOption] = Set.empty,
  gmtOffset: Int = 0,
  isDomesticOnly: Boolean = false,
  @deprecated("ypl doesnt need this")
  augmentation: Boolean = false,
  paxSubmit: Option[PaxType] = None,
  extraBedChildAge: Int = 0,

  /** I encourage you to use this object to have feature-related stuff in 1 place */
  features: HotelFeatures =
    HotelFeatures(), // todo: check usage if no usage after migration then we can take it out and let df read from meta
  reqOcc: YplReqOccByHotelAgePolicy,
  @deprecated("ypl doesnt need this")
  cheapestRoomId: RoomUID = "",
  //  Stats information about hotel, just for logging
  stats: Map[SupplierId, YplSupplierStats] = Map.empty,
  bookingCutoffTime: Option[LocalTime] = None,
  isAgodaBrand: Boolean = false,
  supplierSourceType: Option[SourceType] = None,
  hotelTaxInfo: HotelTaxInfo,
  supplierContractedCommission: Option[Double] = None,
  gmtOffsetMinutes: Int = 0,
  @deprecated("This field is construct from obsoleted table")
  isApmHotelActiveStatus: Boolean = false,
  autoPriceMatchIdsHash: Option[String] = None,
  chainId: ChainId = 0,
  autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map.empty,
  hotelAggregation: Option[YPLCheapestHotelAggregation] = None,
  yplCheapestStayPackageRatePlans: Seq[YplCheapestStayPackageRatePlans],
  fireDrillProto: Option[FireDrillProto] = None,
  dispatchChannels: YplDispatchChannels = YplDispatchChannels(Set.empty),
  taxMetaData: List[TaxMetaData] = List.empty,
  taxRegistrationStatus: Option[TaxRegistrationStatus] = None,
) {

  def toPropertyContext: PropertyContext = PropertyContext(id, cityId, countryId)

  /** There is at least 1 room available, either a normal room or a suggestion */
  lazy val isAvailable: Boolean = rooms.nonEmpty

  /**
    * Note this is more correct flag for determining "true" not ready hotel
    * Use this field instead of !hotel.isReady
    */
  lazy val isNotReady: Boolean = (notReadySuppliers.nonEmpty && !isAvailable)
  lazy val isReady: Boolean = !isNotReady

  /** Hotel-level property that's gonna be removed after eliminating old DF endpoint */
  lazy val isPush: Boolean = suppliers.values.exists(_.isPush) || !suppliers.values.exists(_.isPull)

  /** Set of all room suppliers that this hotel has */
  lazy val availableSuppliers: Set[SupplierId] = rooms.foldLeft(Set[SupplierId]()) { (set, room) =>
    set + room.supplierId
  }

  case class SupplyReadyStatus(readySuppliers: Set[SupplierId], notReadySuppliers: Set[SupplierId])

  lazy val supplyReadyStatus: SupplyReadyStatus =
    suppliers.foldLeft(SupplyReadyStatus(Set.empty, Set.empty)) { (status, supply) =>
      if (supply._2.isReady) SupplyReadyStatus(status.readySuppliers + supply._1, status.notReadySuppliers)
      else SupplyReadyStatus(status.readySuppliers, status.notReadySuppliers + supply._1)
    }

  def readySuppliers = supplyReadyStatus.readySuppliers
  def notReadySuppliers = supplyReadyStatus.notReadySuppliers
  def hasReadySupplier: Boolean = readySuppliers.nonEmpty

  /**
    * Find room by UID
    *
    * @param uid room uid
    * @return [Optional] [[YPLRoom]] with specified UID
    */
  def findRoomByUid(uid: RoomUID): Option[YPLRoom] = {
    val uidHash = uid.##
    rooms.find(r => r.uidHash == uidHash && r.uid == uid)
  }

  @deprecated("Use suppliers outside YPL as in supply module we have aggregated YPLHotel from push and pull")
  lazy val isPull = supplierSourceType.contains(SourceTypes.Pull)
}

/**
  * Partially Refundable contains information of base/duplicated
  *
  * @param relationUID  : UID of room that is base/target of another rooms
  * @param srcCxl       : original cxl code (Free/Partial)
  * @param targetCxl    : Override cxl code should be worse than srcCxl (Partial/NonRefund)
  * @param isDuplicated : represent this room is duplicated room or not
  */
case class YplPartialRefundInfo(relationUID: String, srcCxl: String, targetCxl: String, isDuplicated: Boolean = false)

case class YplCancellation(cxlCode: String,
                           cxlChargeSetting: Option[CancellationChargeSettingType] = None,
                           dmcData: Option[DmcData])

case class YPLCheapestHotelAggregation(benefits: Set[Benefit],
                                       cxlCodes: Map[Int, List[YplCancellation]],
                                       channelIds: Set[Int],
                                       allowNoCreditCard: Boolean,
                                       prepayRequire: Boolean)
// scalastyle:off

/**
  * @param channel : is YplChannel that carry both base channel id and the stack channel ids
  */
case class YPLRoom(
  hotelId: HotelId,
  roomTypeId: RoomTypeId,
  supplierId: SupplierId,
  channel: YplChannel,
  language: LanguageId,
  currency: Currency,
  checkIn: DateTime,
  lengthOfStay: FacilityId,
  occ: RoomOccupancy,
  marginPercentage: MarginPercent,
  cxlCode: String,
  allotment: FacilityId,
  breakfast: Boolean,
  prices: List[YplPrice],
  priceDiffBetweenAPOAndLeastExpensiveRoom: Lon = 0.0,
  rateCategoryId: FacilityId = 0,
  paymentModel: PaymentModel,
  allotmentRatePlan: FacilityId = 1,
  isDayUse: Boolean = false,
  @deprecated("we wont be populating this in the new flow") dmcData: Option[DmcData] = None,
  dmcRoomId: Option[String] = None,
  sellerBrand: String = "",
  rateType: RateType = RateType.Unknown,
  priusOutput: Option[YplPriusOutput] = None,
  reqOcc: Option[YplReqOccByHotelAgePolicy] = None,
  rateCategory: RateCategory,
  @deprecated("In future we gonna remove it, use payment options instead", "2.11.4") noCreditCard: Boolean = false,
  @deprecated("In future we gonna remove it, use payment options instead", "2.11.4") prepaymentRequired: Boolean = true,
  isFireDrill: Boolean = false,
  fireDrillContract: Option[YplFireDrillContract] = None,
  roomStatus: RatePlanStatus = RatePlanStatus.Requested,
  paymentOptions: Set[PaymentOption] = Set(PaymentOption.PrepaymentRequired),
  benefits: List[Benefit] = Nil,
  dmcRatePlanID: Option[String] = None,
  dmcMealPlanID: Option[String] = None,
  npclnChannel: Option[FacilityId] = None,
  cancellationFeeList: Option[List[CancellationFee]] = None,
  masterRoomId: Option[RoomTypeId] = None,
  preMappedMasterRoomId: Option[RoomTypeId] = None,
  rateRepurposeInfos: Seq[YplRateRepurposeInfo] = Nil,
  roomFeatures: RoomFeatures = RoomFeatures(),
  cor: COR = COR(),
  discountInfo: DiscountInfo = DiscountInfo(),
  subSupplierId: Option[SupplierId] = None,
  hotelPolicyText: Option[String] = None,
  isCached: Boolean = false,
  actingAsYcs: Option[Boolean] = None,
  morErpAdminId: Option[FacilityId] = None,
  isAgodaBrand: Boolean = false,
  originalRateType: RateType,
  dmcDataHolder: Option[DmcDataHolder] = None,
  processingFeePercent: Lon,
  commissionEquivalent: Option[Lon] = None,
  partialRefundInfo: Option[YplPartialRefundInfo] = None,
  cxlChargeSetting: Option[CancellationChargeSettingType] = None,
  cancellationCondition: YplCancellationCondition = YplCancellationCondition(CancellationGroup.Unknown),
  yplRoomEntry: YplRoomEntry,
  taxInfo: Option[TaxInfo] = None,
  isAgodaRefund: Boolean = false,
  paymentChannels: List[PaymentChannel] = List.empty,
  hourlyAvailableSlots: Seq[TimeInterval] = Seq.empty,
  isEasyCancel: Boolean = false,
  confirmByMins: Option[FacilityId] = None,
  childRateSettings: Seq[ChildRate] = Seq.empty,
  roomAllocationInfo: Map[RoomNumber, YPLRoomAllocation] = Map.empty,
  checkInInformation: Option[CheckInInformation] = None,
  stayPackageType: Option[StayPackageType] = None,
  apmExternalData: Option[ApmExternalData] = None,
  resellExternalData: Option[ResellExternalData] = None,
  override val fences: Set[YplRateFence] = Set.empty,
  apmPriceAdjustmentSetting: Option[MultipleAutoPriceMatchHolder] = None,
  apmCommissionDiscountSetting: Option[MultipleAutoPriceMatchHolder] = None,
  rateCategoryInfo: Option[YplRateCategoryInfo] = None,
  roomDataChangeTracker: Option[RoomDataChangeTracker] = None,
  isCheckInTimeRequired: Boolean = false,
  originalChannelId: Option[Int] = None,
  occupancyBreakdown: Option[OccupancyBreakdown] = None,
  isMADO: Option[Boolean] = None,
  feeWaivers: Seq[Waiver] = Seq.empty,
  isSecretlyDispatchedRate: Option[Boolean] = None,
  rateFenceToRoomStatusMap: Map[YplRateFence, RatePlanStatus] = Map.empty,
  roomExtraInfo: RoomExtraInfo = RoomExtraInfo.empty,
  payLater: Option[PayLater] = None,
  dynamicRoomMapping: Option[YplDynamicRoomMapping] = None,
  isDirectConnect: Boolean = false,
  uspaInfo: Option[UspaInfo] = None,
) extends RoomFences[YPLRoom] {

  lazy val benefitIds: Set[Int] = (benefits.map(_.id) ++ rateCategory.benefits.map(_.id)).toSet

  private val isCxlDuplicated: Boolean = partialRefundInfo.exists(_.isDuplicated)

  def toRoomIdentifiers(): RoomIdentifiers = RoomIdentifiers(
    coreFields = CoreFields(
      roomId = roomTypeId,
      paymentModel = paymentModel.i,
      promotionId = discountInfo.promotion.map(_.id).getOrElse(0),
      channelId = channel.compositeChannelId,
      rateCategoryId = rateCategoryId,
      occupancy = occupancy,
      extrabed = extraBed,
      breakfast = breakfast,
      cxlCode = cxlCode,
      supplierId = supplierId,
      isPriusOutput = priusOutput.isDefined,
      isRepurposed = isRepurposed,
      srcChannelId = repurposedInfo.map(_.srcChannel.compositeChannelId).getOrElse(0),
      refChannelId = repurposedInfo.map(_.refChannel.compositeChannelId).getOrElse(0),
      dmcUid = dmcData.map(_.dmcUID).getOrElse(""),
    ),
    extendFields = ExtendFields(subSupplierId,
                                isCxlDuplicated,
                                isAgodaRefund,
                                channel.toCompositeChannelIds,
                                occ.paidChildAges,
                                resellExternalData.map(_.sourceBookingId)),
    roomSelectionFields = None,
    stayOccupancyFields = None,
    pricingFields = None,
  )

  def isFit: Option[Boolean] =
    if (roomFeatures.isAllOccSearch) Some(true)
    else {
      // Counts the infants only for the New Occ Search, the old logic doesn't support them
      def exactGuestsMatch(r: YplReqOccByHotelAgePolicy): Boolean =
        occ.guests + occ.infants == r.guestsPerRoom(withInfants = true)

      reqOcc.map { r =>
        // still not sure how to put this under exp,
        // quick work around to bypass this by check paidChildAges, we only generate paidChildAges for fit offer.
        // todo: fix this and in DF side as well there is similar logic there too.
        // occ.paidChildAges.nonEmpty ||
        !r.isFreeOcc && // Only for non-free occ
        exactGuestsMatch(r) && // Guests must be exact match
        yplRoomEntry.roomType
          .isValidBasedOnOccupancy(r, Some(occ.maxOcc)) && // Check Valid Max Adults & Children Occupancy
        // TODO: find a way to make this logic simpler
        (!r.hasChildren || occ.hasChildren ||
        (r.hasChildren && r.adults % r.rooms > 0 &&
        occ.adults == r.guestsPerRoom() &&
        occ.differentOccPerRoom))
      }
    }

  def pricesToExternalDailyRates = roomPrices.map { p =>
    val rate = originalRateType match {
      case RateType.NetExclusive => p.netExclusive
      case RateType.NetInclusive => p.netInclusive
      case RateType.SellExclusive => p.sellExclusive
      case RateType.SellInclusive => p.sellInclusive
      case _ => p.value
    }

    YPLExternalDataDailyRate(
      incoming = rate,
      netEx = p.netExclusive,
      netIn = p.netInclusive,
      sellEx = p.sellExclusive,
      sellIn = p.sellInclusive,
      date = Option(p.date),
    )
  }

  lazy val roomPrices: List[YplPrice] = prices.filter(_.isRoom)
  lazy val nonRoomPrices: List[YplPrice] = prices.filterNot(_.isRoom)

  lazy val eligibleRoomPricesForApm: List[YplPrice] = prices.filter(_.isEligiblePriceForApm)

  lazy val extraBedPrices: List[YplPrice] = prices.filter(_.isExtraBed)
  lazy val surchargePrices: List[YplPrice] = prices.filter(p => p.isSurcharge)

  lazy val roomAndExtraBedPrices: List[YplPrice] = prices.filter(p => p.isRoom || p.isExtraBed)
  lazy val nonRoomExtraBedSurchargePrices: List[YplPrice] =
    prices.filterNot(p => p.isRoom || p.isExtraBed || p.isSurcharge)

  lazy val hasTaxPrototypeLevelWithSurchargeOrFee: Boolean =
    (prices.exists(_.dailyTaxes.taxes.exists(_.tax.taxPrototypeInfo.exists(_.taxPrototypeLevels.nonEmpty)))
    && (prices.getDSum(p => p.sellExclusive, p => p.isMandatorySurcharge) > 0 || prices.getDSum(p => p.fee) > 0))

  //  ToDO: make it lazy val since used in many places
  def numRooms: Int = math.max(1, roomPrices.headOption.map(rp => rp.quantity).getOrElse(0))

  private def mandatoryExtraBed(sel: YplPrice => Double): Double = prices.getDSum(
    p => sel(p) * p.quantity / numRooms,
    p => p.isExtraBed && p.chargeOption == ChargeOption.Mandatory,
  )

  def mandatorySurcharge = prices.getDSum(
    p => p.sellInclusive * p.quantity / numRooms,
    p => p.chargeType == ChargeType.Surcharge && p.chargeOption == ChargeOption.Mandatory,
  )

  private def getTaxes(p: YplPrice, numRoom: Int): Double = p.taxBreakDown.getDSum(
    t => (t.amount * t.quantity * p.quantity) / numRoom,
    t => (!t.include && t.option == ChargeOption.Mandatory) || (t.option == ChargeOption.Excluded),
  )

  //  ToDO: This code is duplicated in RoomCalculator, leave only 1 copy
  def payAtHotel = {
    val ycsTotal = prices.getDSum(
      p => {
        val taxBreakdowns = getTaxes(p, numRooms)
        (p.sellInclusive * p.quantity) / numRooms + taxBreakdowns
      },
      p => p.chargeType == ChargeType.Surcharge && p.chargeOption == ChargeOption.Excluded,
    )

    val dmcTotal = prices.getDSum(
      p => getTaxes(p, numRooms),
      p => p.isRoom && p.isMandatory,
    )

    ycsTotal + dmcTotal
  }

  def mandatorySurchargeNetInclusive: Double = prices.getDSum(
    p => p.netInclusive * p.quantity / numRooms,
    p => p.chargeType == ChargeType.Surcharge && p.chargeOption == ChargeOption.Mandatory,
  )

  def netEx: Double = roomPrices.getDSum(_.netExclusive)

  def netIn: Double = roomPrices.getDSum(_.netInclusive)

  def tax: Double = roomPrices.getDSum(_.tax)

  def fee: Double = roomPrices.getDSum(_.fee)

  def margin: Double = roomPrices.getDSum(_.margin)

  def downliftAmount: Double = roomPrices.getDSum(_.downliftAmount)

  def pf: Double = roomPrices.getDSum(_.processingFee)

  def roomSellEx: Double = roomPrices.getDSum(_.sellExclusive)

  def roomSellIn: Double = roomPrices.getDSum(_.sellInclusive)

  def eligibleRoomForApmSellEx: Double = eligibleRoomPricesForApm.getDSum(_.sellExclusive)

  def eligibleRoomForApmSellIn: Double = eligibleRoomPricesForApm.getDSum(_.sellInclusive)

  def roomRefSellEx: Double = roomPrices.getDSum(_.refSellExclusive)

  def roomRefSellIn: Double = roomPrices.getDSum(_.refSellInclusive)

  def payAsYouGoAgxCommission: Double = roomPrices.getDSum(_.payAsYouGoCommission)

  def prepaidAgxCommission: Double = roomPrices.getDSum(_.prepaidCommission)

  def freeTrialAgxCommission: Double = roomPrices.getDSum(_.freeTrialCommission)

  def agpReferenceCommission: Double = roomPrices.getDSum(_.agpReferenceCommission)

  def agpInvoiceReferenceCommission: Double = roomPrices.getDSum(_.agpInvoiceReferenceCommission)

  def priusValue: Double = priusOutput.map(_.value).getOrElse(0d)

  def sellEx: Double = roomSellEx + priusValue

  def sellIn: Double = roomSellIn + priusValue

  def eligibleForApmSellEx: Double = eligibleRoomForApmSellEx + priusValue

  def eligibleForApmSellIn: Double = eligibleRoomForApmSellIn + priusValue

  def netExExtraBed: Double = extraBedPrices.getDSum(_.netExclusive)

  def netInExtraBed: Double = extraBedPrices.getDSum(_.netInclusive)

  def taxExtraBed: Double = extraBedPrices.getDSum(_.tax)

  def feeExtraBed: Double = extraBedPrices.getDSum(_.fee)

  def marginExtraBed: Double = extraBedPrices.getDSum(_.margin)

  def downliftAmountExtraBed: Double = extraBedPrices.getDSum(_.downliftAmount)

  def pfExtraBed: Double = extraBedPrices.getDSum(_.processingFee)

  def sellExExtraBed: Double = extraBedPrices.getDSum(_.sellExclusive)

  def sellInExtraBed: Double = extraBedPrices.getDSum(_.sellInclusive)

  def netExSurcharge: Double = surchargePrices.getDSum(_.netExclusive)

  def taxSurcharge: Double = surchargePrices.getDSum(_.tax)

  def totalNetExWithExtraBed: Double = netEx + mandatoryExtraBed((p: YplPrice) => p.netExclusive)

  def totalNetInWithExtraBed: Double = netIn + mandatoryExtraBed((p: YplPrice) => p.netInclusive)

  def totalSellExWithExtraBed: Double = sellEx + mandatoryExtraBed((p: YplPrice) => p.sellExclusive)

  def totalSellInWithExtraBed: Double = sellIn + mandatoryExtraBed((p: YplPrice) => p.sellInclusive)

  def sellInWithExtraBed: Double = ifFits(totalSellInWithExtraBed, sellIn)

  def payOnBook: Double = sellInWithExtraBed + mandatorySurcharge

  def sellAllIn: Double = payOnBook + payAtHotel

  def offerType: Option[Int] = rateCategory.offerType

  def excludedTaxAmount: Double = prices.getDSum(
    _.netExclusive,
    x => x.chargeType == ChargeType.Surcharge && x.chargeOption == ChargeOption.Excluded,
  )

  def extraBed: Int = occ.extraBeds

  def extraBedSellIn: Double = prices.getDSum(_.sellInclusive, _.isExtraBed)

  def occupancy: Int = occ.maxOcc

  def freeChildrenAndInfantsOccupancy: Int = occ.freeChildrenAndInfants

  def guests: Int = occ.guests

  private def ifFits(ifFits: => Double, ifNot: => Double) = if (isFit.exists(b => b)) ifFits else ifNot

  def repurposedInfo: Option[YplRateRepurposeInfo] = rateRepurposeInfos.find(_.isReutilized)

  def isRepurposed: Boolean = repurposedInfo.nonEmpty

  private lazy val roomIdentity: RoomIdentity = RoomIdentity(
    roomId = roomTypeId,
    paymentModel = paymentModel.i,
    promotionId = discountInfo.promotion.map(_.id).getOrElse(0),
    channelId = channel.compositeChannelId,
    rateCategoryId = rateCategoryId,
    occupancy = occupancy,
    extrabed = extraBed,
    breakfast = breakfast,
    cxlCode = cxlCode,
    supplierId = supplierId,
    isPriusOutput = priusOutput.isDefined,
    isRepurposed = isRepurposed,
    srcChannelId = repurposedInfo.map(_.srcChannel.compositeChannelId).getOrElse(0),
    refChannelId = repurposedInfo.map(_.refChannel.compositeChannelId).getOrElse(0),
    dmcUid = dmcDataHolder.flatMap(_.roomUid).getOrElse(""),
    subSupplierId = subSupplierId,
    isCxlDuplicated = isCxlDuplicated,
    isAgodaRefund = isAgodaRefund,
    compositeChannelIds = channel.toCompositeChannelIds,
    paidChildAges = occ.paidChildAges,
    resellSourceBookingId = resellExternalData.map(_.sourceBookingId),
  )

  private lazy val roomIdentityForDF = roomIdentity.copy(dmcUid = dmcData.map(_.dmcUID).getOrElse(""))

  // roomIdentity that would be same as corresponding DF room
  def dfRoomUid: RoomUID = roomIdentityForDF.uid

  def uid: RoomUID = roomIdentity.uid

  def uidHash: RoomUIdHash = roomIdentity.uidHash

  def sellExWithExtraBed: Double = ifFits(totalSellExWithExtraBed, sellEx)

  def hotelPolicy: String = hotelPolicyText.getOrElse("")

  def isTrueAgencyRateForkingFeatureEnabled: Boolean = roomFeatures.isTrueAgencyRateForking

  lazy val hasPromotion: Boolean = discountInfo.promotionsBreakdown.nonEmpty
  lazy val allPromotions: Set[Promotion] = discountInfo.promotionsBreakdown.values.flatten.toSet
  lazy val allStackPromotions: Set[Promotion] =
    discountInfo.promotionsBreakdown.values.flatten.filter(_.isStackable).toSet
  lazy val allNormalPromotions: Set[Promotion] =
    discountInfo.promotionsBreakdown.values.flatten.filterNot(_.isStackable).toSet

  def toRoomPriceInfo = RoomPriceInfo(
    channel = channel,
    rateType = rateType,
    originalRateType = originalRateType,
    roomOcc = occ,
    processingFeePercent = processingFeePercent,
  )

  // Get assume sell ex [(room sell ex + extra bed sell ex + mandatory surcharge + fee) / room count] by date
  // If have channelDiscount, we deduct channel discount at room and surcharge price (not extra bed)
  def dailyAssumeSellExForCalculateGraduatedTax(
    channelDiscountMap: Map[DateTime, Double] = Map.empty): Map[DateTime, Double] =
    GraduatedTaxCalculator.dailyAssumeSellExForCalculateGraduatedTax(
      prices.map(price =>
        new PriceForAssumeSellEx(
          price.sellExclusive,
          price.quantity,
          price.fee,
          toCommonChargeType(price.chargeType),
          price.chargeOption,
          price.date,
          price.subChargeType,
        )),
      channelDiscountMap,
    )

  override def copyFences(newFences: Set[YplRateFence]): YPLRoom = copy(fences = newFences)

  def isActiveAndEnableCommission: Boolean = apmPriceAdjustmentSetting.exists(
    _.isActiveOrSuspendedApmHotel) || apmCommissionDiscountSetting.exists(_.isActiveAndEnableCommission)

  lazy val marginPercentageFromMarginPlusPfOverSellIn = (margin + pf) / sellIn * 100d

  def hasPositiveConfirmByMins: Boolean = this.confirmByMins.exists(_ > 0)

  def getMasterRoomIdOrElseRoomTypeId: RoomTypeId = masterRoomId.filter(_ > 0L).getOrElse(roomTypeId)

  // TODO: Remove getMasterRoomIdOrElseRoomTypeId once optimize EXP(CWSD-1717) is integrated
  lazy val masterRoomIdOrElseRoomTypeId: RoomTypeId = masterRoomId.filter(_ > 0L).getOrElse(roomTypeId)

  def isStandaloneRoom: Boolean = masterRoomId.isEmpty || masterRoomId.contains(0) || masterRoomId.contains(roomTypeId)
}
// scalastyle:on

case class RoomFeatures(isDomesticOnly: Boolean = false,
                        isAllOccSearch: Boolean = false,
                        /* Marks a room with price that is average between 2 different rates (used in multiple room search when number of guests doesn't divide equally into rooms */
                        isAveragePrice: Boolean = false,
                        isAgodaAgency: Boolean = false,
                        isRoomTypeNotGuarantee: Boolean = false,
                        isSuggestion: Boolean = false,
                        haveMultiRoomPromotion: Boolean = false,
                        isTrueAgencyRateForking: Boolean = false,
                        /* Marks a room with price that contain more than one room price according to room assignment request */
                        isMultipleRoomAssignmentPrice: Boolean = false,
                        isOverrideChildTypeRequest: Boolean = false,
                        isBcomHybridMC: Boolean = false,
                        isJapanChildRateApplied: Boolean = false,
                        isBookOnRequest: Boolean = false,
                        isKeptForFlexibleMultiRoom: Boolean = false)

case class HotelFeatures(isMyStaySupported: Boolean = false,
                         // TODO: stop using hotel level bookOnRequest,
                         //   bookOnRequest is per room level as we supplier mix suppliers
                         bookOnRequest: Boolean = false,
                         isContainChinaBOR: Boolean = false,
                         bookNowPayAtCheckIn: Boolean = false)

case class RoomDataChangeTracker(history: List[RoomDataHistory],
                                 allocations: Map[RoomNumber, GuestsAllocation] = Map.empty,
                                 perDayPerOccPrice: Seq[PerDayPerOccPrice] = List.empty) {

  def track(step: RoomDataStep, cxlCode: String): RoomDataChangeTracker = RoomDataChangeTracker(
    this.history ++ List(RoomDataHistory(step.entryName, cxlCode)),
    this.allocations,
    this.perDayPerOccPrice)

  def track(allocations: Map[RoomNumber, GuestsAllocation]): RoomDataChangeTracker =
    RoomDataChangeTracker(this.history, allocations = allocations, this.perDayPerOccPrice)

  def withPerDayOccPrice(perDayPerOccPrice: Seq[PerDayPerOccPrice]): RoomDataChangeTracker =
    RoomDataChangeTracker(this.history, this.allocations, perDayPerOccPrice = perDayPerOccPrice)

}

case class PerDayPerOccPrice(rateCategoryId: Int, stayDate: DateTime, occ: Int, price: Double)

case class RoomDataHistory(step: String, cxlCode: String)
