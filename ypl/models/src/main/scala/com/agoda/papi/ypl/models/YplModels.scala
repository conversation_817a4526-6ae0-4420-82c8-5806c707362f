package com.agoda.papi.ypl.models

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.enums.hotel.{PaymentModel, PaymentOption, RateModel}
import com.agoda.papi.enums.room._
import com.agoda.papi.pricing.pricecalculation.models.tax.Tax
import com.agoda.papi.ypl.commission.apm.models.{
  AutoPriceMatchKeyEntry,
  AutoPriceMatchPriceInfo,
  MultipleAutoPriceMatchHolder,
}
import com.agoda.papi.ypl.commission.{CommissionHolder, MORPCandidateRoomParameters}
import com.agoda.papi.ypl.fencing.RoomFences
import com.agoda.papi.ypl.models.Wholesale.WholesaleMetaDataByRateFence
import com.agoda.papi.ypl.models.YplRoomEntry.childChargeApplyTypes
import com.agoda.papi.ypl.models.enums.{MaxAllowedFree<PERSON>hild<PERSON>tatus, OccupancyModel}
import com.agoda.papi.ypl.models.hotel.YplAgencyNoccSetting
import com.agoda.papi.ypl.models.occupancy.OccupancyBreakdown
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.pricing.{RoomOccupancy, RoomPriceInfo}
import com.agoda.papi.ypl.models.proto.enums.{SourceType, SourceTypes}
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.SupplierRateInfo
import com.agoda.protobuf.cache.HotelPrice
import com.agoda.supply.calc.proto.PropertyOffer
import com.agoda.utils.collection.SumImplicits._
import org.joda.time.{DateTime, LocalDate, LocalTime}

import scala.collection.mutable.ListBuffer

/**
  * dispatch channels from heisenberg
  *
  * @param masterChannels
  * @param helperChannels
  */

case class YplDispatchChannels(masterChannels: Set[YplChannel], helperChannels: Set[YplChannel] = Set.empty) {
  private val helperBaseChannels = helperChannels.map(_.baseChannelId)
  private val masterBaseChannels = masterChannels.map(_.baseChannelId)

  def contains(yplChannel: YplChannel): Boolean = yplChannel.toCompositeChannelIds.toSet.subsetOf(masterBaseChannels)

  def isHelperChannel(yplChannel: YplChannel): Boolean =
    yplChannel.toCompositeChannelIds.toSet.subsetOf(helperBaseChannels)

}

/**
  * This is clone of DF (models.pricing.PricingData)
  *
  * @param proto
  * @param info
  * @param dispatchChannels
  * @param protoCor
  */
case class YplPricingData(proto: Either[PropertyOffer, HotelPrice],
                          info: HotelMeta,
                          @deprecated("Use 'dispatchChannelsPerFence' instead")
                          dispatchChannels: YplDispatchChannels,
                          dispatchChannelsPerFence: Map[YplRateFence, YplDispatchChannels],
                          protoCor: Cor = Map.empty)

/**
  * Hotel Model that is transformed from Property Offer Protobuf (almost unitl we kill OTA HotelPrice protobuf)
  *
  * @param hotelId             hotel id
  * @param supplierId          supplier id
  * @param paymentModel        payment model for current hotel, it is some what deprecated as we are not support diff paymentModel per rate category. this one is for fallback
  * @param lengthOfStay        the los from property offer protobuf, it does not have to match with requested los from diff use case
  * @param hotelOccupancySetup new ycs setting for hotel level about occupancy
  */
case class YplHotelEntryModel(
  hotelId: Int,
  supplierId: SupplierId,
  paymentModel: PaymentModel,
  rooms: List[YplRoomEntry],
  occupancyModel: OccupancyModel,
  taxInfo: TaxInfo,
  rateModel: RateModel,
  surchargeRateType: RateType, // RPM
  metaData: HotelMeta,
  retailHotel: Option[YPLHotel] = None,
  dispatchChannels: YplDispatchChannels,
  dispatchChannelsPerFence: Map[YplRateFence, YplDispatchChannels],
  ratePlanLanguage: Option[Map[LanguageId, Set[RatePlanId]]],
  rateReutilizations: Seq[YPLRateReutilizationEntry],
  reqOcc: YplReqOccByHotelAgePolicy,
  lengthOfStay: Int,
  bookingCutoffTime: Option[LocalTime] = None,
  supplierSourceType: Option[SourceType] = None,
  expiresAt: Option[Long] = None,
  isOTASupplier: Option[Boolean] = None,
  supplierContractedCommission: Option[Double] = None,
  stackChannelDiscountInfo: Map[ChannelId, Seq[StackedChannelDiscount]],
  agencyNoccSetting: Option[YplAgencyNoccSetting] = None,
  autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map.empty,
  autoPriceMatchIdsHash: Option[String] = None,
  retailGlobalContractedCommission: Double = 0.0d,
  wholesaleMetadataMap: WholesaleMetaDataByRateFence = Map.empty,
  channelLookUpByRateCategoryAndRoomTypeMap: Map[(Long, Long), RateCategoryChannelLookUp] = Map.empty,
  fireDrillProto: Option[FireDrillProto] = None,
  hotelOccupancySetup: Option[HotelOccupancySetup] = None,
  shouldSuggest: Boolean = false) {

  lazy val (retailRooms, nonRetailRooms) = rooms.partition(_.channel.isRTL)
  lazy val hasPerGuestTax = taxInfo.taxes.exists { case (_, tax: Tax) => tax.isPerPersonCharge }

  lazy val hasTaxPrototypeLevel = taxInfo.taxes.exists { case (_, tax: Tax) =>
    tax.taxPrototypeInfo.map(_.taxPrototypeLevels.nonEmpty).getOrElse(false)
  }

  lazy val isPull = supplierSourceType.contains(SourceTypes.Pull)

  // use for new Occupancy Logic Converter for Exp,
  // the final goal is the new data will come from property offer protobuf, then we no longer need this converter

  // check if the hotel has any room with max allowed free children
  lazy val maxAllowedFreeChildStatus: MaxAllowedFreeChildStatus = {
    val setOfRoomTypeIds = rooms.map(_.roomTypeId).toSet
    val setOfFreeChild: Set[Boolean] = setOfRoomTypeIds.map { id =>
      metaData.enabledRoom.get(id).flatMap(_.maxAllowedFreeChildren).getOrElse(0) > 0
    }
    setOfFreeChild.toList.sorted match {
      case List(false, true) => MaxAllowedFreeChildStatus.Mix
      case List(true) => MaxAllowedFreeChildStatus.AllOneOrMore
      case _ => MaxAllowedFreeChildStatus.AllZero
    }
  }

  // check if any room offer has child rate setup
  lazy val hasDailyChildRate = rooms.exists(_.dailyPrices.exists(_._2.childPrices.nonEmpty))

  // collect child age rate with free or amount: 0
  lazy val freeChildAgeRangeIds: Set[ChildAgeRangeIndex] = rooms.flatMap { room =>
    room.childRateDaily
      .flatMap { cRate =>
        cRate._2.filter(cPrice => cPrice.pricingTypeId == PricingChildRateType.Free || cPrice.value.getOrElse(0) == 0)
      }
      .map(_.childAgeRangeId)
  }(collection.breakOut)
}

case class AutoPriceMatchData(autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] =
                                Map.empty,
                              autoPriceMatchIdsHash: Option[String] = None)

case class YPLRateReutilizationEntry(sourceChannel: YplChannel,
                                     referenceChannel: YplChannel,
                                     targetChannel: YplChannel,
                                     discountType: Int,
                                     flatChannelDiscount: Double,
                                     minAdvPurchase: Option[Int],
                                     maxAdvPurchase: Option[Int])

/**
  * Protobuf entry for room.
  *
  * @param remainingRooms            allotment
  * @param occEntry                  will be setup later
  * @param isBreakFastIncluded       for YCS we get this flag from IsBreakfastIncluded in PriceInfo.
  *                                  for FPLOS hotels we grab it from HasBreakfast rate matrix field
  * @param parentPromotionSourceRoom This room is a source for room that generated with promotion
  */
case class YplRoomEntry(
  roomTypeId: RoomTypeId,
  masterRoomTypeId: MasterRoomId,
  channel: YplChannel,
  cxlCode: String,
  isBreakFastIncluded: Boolean,
  remainingRooms: Int,
  currency: Currency,
  rateType: RateType,
  processingFees: Double, // percent
  isAllowCombinePromotion: Boolean,
  roomType: RoomTypeEntry,
  dailyPrices: Map[DateTime, DailyPrice],
  occEntry: RoomOccupancy,
  occupancyBreakdown: Option[OccupancyBreakdown] = None,
  promotion: Option[PromotionEntry] = None,
  promotionsBreakdown: Map[LocalDate, List[PromotionEntry]] = Map.empty,
  availablePromotions: List[PromotionEntry] = List(),
  parentPromotionSourceRoom: Option[YplRoomEntry] = None,
  allotmentRatePlan: Int = 1,
  channelRateType: Option[RateType] = None, // RPM
  rateCategory: RateCategoryEntry,
  displayedRackRate: DisplayedRackrate = 0.0d,
  offerType: Option[Int] = None,
  productOfferId: ProductOfferId = None,
  isAgodaAgency: Boolean = false,
  /* Marks a room with price that is average between 2 different rates (used in multiple room search when number of guests doesn't divide equally into rooms */
  isAveragePrice: Boolean = false,
  originalRateType: RateType,
  haveMultiRoomPromotion: Boolean = false,
  paymentModel: Option[PaymentModel] = None,
  dmcDataHolder: Option[DmcDataHolder] = None,
  paymentOptions: Set[PaymentOption] = Set(PaymentOption.PrepaymentRequired),
  occFromProto: Int = 0,
  cxlChargeSetting: Option[CancellationChargeSettingType] = None,
  inventoryType: InventoryType = InventoryType.Agoda,
  /* Marks a room with price that contain more than one room price according to room assignment request */
  isMultipleRoomAssignmentPrice: Boolean = false,
  isKeptForFlexibleMultiRoom: Boolean = false,
  isOverrideChildTypeRequest: Boolean = false,
  hourlyAvailableSlots: Seq[TimeInterval] = Seq.empty,
  confirmByMins: Option[Int] = None,
  isBookOnRequest: Boolean = false,
  roomAllocationInfo: Map[RoomNumber, YPLRoomAllocation] = Map.empty,
  checkInInformation: Option[CheckInInformation] = None,
  linkedRoomTypeCode: Option[String] = None,
  isVipYcsPromotionEligible: Boolean = false,
  pulseCampaignMetadata: Option[PulseCampaignMetadata] = None,
  rateCategoryInfo: Option[YplRateCategoryInfo] = None,
  resellExternalData: Option[ResellExternalData] = None,
  override val fences: Set[YplRateFence] = Set.empty,
  roomDataChangeTracker: Option[RoomDataChangeTracker] = None,
  isJapanChildRateApplied: Boolean = false,
  commissionHolder: CommissionHolder = CommissionHolder.default,
  applyNoCCCommission: Boolean = false,
  priceAdjustmentId: Option[Long] = None,
  propOfferOccupancy: PropOfferOccupancy, // This is only used for surcharge quantity for pull suppliers
) extends RoomFences[YplRoomEntry] {
  lazy val applicableMORPCandidateRoomParameters = MORPCandidateRoomParameters(fences, applyNoCCCommission)
  lazy val rateCategoryId: RateCategoryId = rateCategory.rateCategoryId
  lazy val containChannelDiscount: Boolean = dailyPrices.exists { case (date, price) =>
    price.channelDiscount.exists(_ > 0d)
  }

  lazy val assumeChannelDiscount: Map[DateTime, Double] = dailyPrices.map { case (key, value) =>
    (key, value.channelDiscount.getOrElse(0d))
  }

  // backward compatible
  lazy val hasPerGuestCharge = hasPerPersonSurCharge || hasParentRateCategoryPerGuestCharge

  lazy val hasPerPersonSurCharge = dailyPrices.flatMap(_._2.rpmSurcharges).exists(_.isPerPersonCharge)

  lazy val hasParentRateCategoryPerGuestCharge =
    rateCategory.isChild && rateCategory.applyTo == ApplyType.PGPN.entryName

  // this is just an approximation if we have the special surcharge from occupancy price,
  // the current impl do a full check in function checkIsSurchargeImpactOccPrice
  lazy val hasPerOccSurCharge =
    dailyPrices.flatMap(_._2.rpmSurcharges).exists(_.occFromProto != SurchargeEntry.defaultOccFromProto)

  lazy val hasAnyPerGuestCharge = hasPerOccSurCharge || hasPerGuestCharge

  lazy val hasChildCharge = dailyPrices.values.exists(_.rpmSurcharges.exists(rpmSurcharge =>
    childChargeApplyTypes.exists(rpmSurcharge.applyTo.contains(_))))

  //  Optimized version for getting room daily prices
  lazy val roomDailyPrices: List[PriceEntry] = {
    val listBuilder = new ListBuffer[PriceEntry]
    for {
      (_, price) <- dailyPrices
      dp <- price.prices if dp.isRoom
    } listBuilder += dp

    listBuilder.result()
  }

  lazy val roomDailyPricesWithSurcharge: List[PriceEntry] = {
    val listBuilder = new ListBuffer[PriceEntry]
    for {
      (_, price) <- dailyPrices
      dp <- price.prices if dp.isRoom
    } listBuilder += {
      dp.copy(value = dp.value + price.rpmSurcharges
        .filter(s => s.occFromProto == occFromProto && s.isMandatorySurcharge)
        .getDSum(_.value))
    }

    listBuilder.result()
  }

  lazy val roomRate = roomDailyPrices.getDSum(_.valueWithChannelDiscount)
  lazy val roomRateWithSurcharge = roomDailyPricesWithSurcharge.getDSum(_.valueWithChannelDiscount)

  lazy val totalBasePrice: Double = dailyPrices.getDSum(x => x._2.prices.getDSum(_.value))
  lazy val totalSurchargeOfProtoOccupancy: Double =
    dailyPrices.getDSum(x => x._2.rpmSurcharges.getDSum(_.value, _.occFromProto == occEntry.occupancy))

  lazy val hasExtraBed = occupancyBreakdown.exists(_.extraBeds > 0)

  lazy val childRateDaily = dailyPrices.map { case (date, dailyPrice) => (date, dailyPrice.childPrices) }

  def totalValue: Double = roomDailyPrices.getDSum(_.total)

  def totalValueForHeuristics = roomDailyPrices.getDSum(_.totalForHeuristics)

  private def channelDiscountValue: Double = roomDailyPrices.getDSum(_.channelDiscount)

  def totalValueWithoutChannelDiscount: Double = totalValue + channelDiscountValue

  def totalPromoDiscount: Double = roomDailyPrices.getDSum(_.promoDiscount)

  lazy val notAllowedChannelDiscount: Boolean = promotionsBreakdown.values.exists(_.exists(!_.isAllowChannelDiscount))

  def toRoomPriceInfo = RoomPriceInfo(
    channel = channel,
    rateType = rateType,
    originalRateType = originalRateType,
    roomOcc = occEntry,
    processingFeePercent = processingFees,
  )

  def isNoExternalCharges(taxInfo: TaxInfo): Boolean = {
    val taxIds = dailyPrices.values.flatMap(_.taxes.keySet)
    val taxes = taxIds.flatMap(taxInfo.taxes.get(_))
    val surcharges = dailyPrices.values.flatMap(_.rpmSurcharges)
    taxes.forall(t => t.option == ChargeOption.Mandatory || (t.option == ChargeOption.Excluded && t.value == 0)) &&
    surcharges.forall(s => s.option == ChargeOption.Mandatory || (s.option == ChargeOption.Excluded && s.value == 0))

  }

  def isPriceMode3: Boolean = dmcDataHolder.exists { dataHolder =>
    dataHolder.supplierRateInfo.exists(rateInfo =>
      rateInfo.externalData.filter(_.field == "use_price_mode_3").exists(_.value.toBoolean))
  }

  def isAgencyEnabled: Boolean = rateCategory.isAgencyEnabled.getOrElse(false)

  def isEscapes: Boolean = rateCategory.stayPackageType.exists(_.isEscapes)

  def isNotEscapes: Boolean = !isEscapes

  def isApprovedEscapes: Boolean = isEscapes &&
    rateCategory.escapesApprovalStatus.exists(_.isApproved)

  def isResell: Boolean = resellExternalData.nonEmpty

  override def copyFences(newFences: Set[YplRateFence]): YplRoomEntry = copy(fences = newFences)

  lazy val `isLT-1349B` = dmcDataHolder.exists(
    _.supplierRateInfo.exists(
      _.externalData.exists(e =>
        e.field == YplExperiments.FOR_PULL_SPLIT_PROTO_PRICE_TO_ROOM_AND_SURCHARGE && e.value == "B"),
    ),
  )

  def getMasterRoomIdOrElseRoomTypeId: RoomTypeId = if (masterRoomTypeId > 0L) masterRoomTypeId else roomTypeId
}

case class YplRoomEntryCommission(room: YplRoomEntry, commission: Double)

case class YplChannelDiscount(discount: Double, startDate: Option[DateTime], endDate: Option[DateTime])

case class YplRoomsWithEntry(rooms: List[YPLRoom], entry: YplHotelEntryModel)

case class YplHotelWithEntry(hotel: YPLHotel, entry: YplHotelEntryModel)

case class DmcDataHolder(supplierExternalDataStr: Option[String] = None,
                         roomUid: Option[String] = None,
                         supplierRateInfo: Option[SupplierRateInfo] = None,
                         yplExternalData: Option[YPLExternalData] = None)

case class YPLExternalData(searchId: String,
                           hotelCode: String,
                           ratePlanCode: Option[String] = None,
                           roomTypeCode: Option[String] = None,
                           currencyCode: String,
                           maxOcc: Int,
                           maxChild: Int,
                           maxExtraBed: Int,
                           srcOcc: Int,
                           totalPrice: Double = 0d,
                           roomAllocationInfo: Map[RoomNumber, YPLRoomAllocation] = Map.empty,
                           inventoryType: Int,
                           checkInStartTime: Option[String] = None,
                           checkInEndTime: Option[String] = None,
                           linkedRoomTypeCode: Option[String] = None)

case class ApmExternalData(isApmHotelActive: Boolean,
                           adjustmentIdsHash: String,
                           priceAdjustmentDetails: Seq[ApmPriceAdjustmentExternalData] = Seq.empty,
                           priceCommission: Seq[ApmPriceCommissionExternalData] = Seq.empty,
                           logicVersion: String = "",
                           priceAdjustmentProgram: MultipleAutoPriceMatchHolder,
                           commissionDiscountProgram: MultipleAutoPriceMatchHolder,
                           rateFence: Set[YplRateFence],
                           apmLeadingRoomAdjustmentIds: Seq[Int] = Seq.empty,
                           additionalCommRedHotelLevel: Double = 0,
                           originalChannelId: Option[Int] = None,
                           isMADO: Option[Boolean] = None)

case class ResellExternalData(sourceBookingId: String)

case class ApmPriceAdjustmentExternalData(date: DateTime,
                                          chargeType: ChargeType,
                                          currency: Currency,
                                          cheapestSellIn: Double,
                                          marketSellIn: Double,
                                          delta: Double,
                                          originalSellIn: Double,
                                          marketPriceDiscountPercent: Double,
                                          maximumDeltaPercent: Double,
                                          isPartialAdjustment: Option[Boolean],
                                          approvalPriceId: ApmApprovalPriceId,
                                          cheapestPriceLocal: Double,
                                          marketPriceLocal: Double,
                                          originalPriceLocal: Double,
                                          adjustmentRateType: RateType)

case class ApmPriceCommissionExternalData(date: DateTime,
                                          chargeType: ChargeType,
                                          chargeOption: ChargeOption,
                                          referenceCommissionPercent: Double,
                                          commissionDiscountPercent: Double)

case class YplRoomEntryWithRoomAllocation(room: YplRoomEntry, roomAllocation: YPLRoomAllocation)

case class YplCommission(channelID: Int,
                         languageID: Option[Int],
                         value: Double,
                         contractedCommission: Option[scala.Double],
                         wholesaleCampaignCommission: Option[Double] = None)

object YplCommission {
  def apply(c: RateCategory.DailyPrice.Commission): YplCommission =
    YplCommission(c.channelID, c.languageID, c.value, c.contractedCommission)

  // plus the additional commission percentage for wholesale rate
  def updateWholesaleCommission(yplCommission: YplCommission, wholesaleCommissionOpt: Option[Double]): YplCommission =
    yplCommission.copy(wholesaleCampaignCommission = wholesaleCommissionOpt)
}

object YplRoomEntry {
  private val childChargeApplyTypes = Set("PC", "PG")
}

case class PropOfferOccupancy(numAdults: Int, numChild: Int) {
  lazy val guests: Int = numAdults + numChild
}
