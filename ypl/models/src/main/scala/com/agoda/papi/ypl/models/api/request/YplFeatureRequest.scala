package com.agoda.papi.ypl.models.api.request

case class YplFeatureRequest(maxSuggestions: Int = 0,
                             populateExternalData: Boolean = true,
                             enableRatePlanCheckInCheckOut: Boolean = false,
                             showCouponAmountInUserCurrency: Boolean = false,
                             disableEscapesPackage: Boolean = false,
                             enableRichContentOffer: Boolean = false,
                             filterCheapestRoomEscapePackage: Boolean = false,
                             enableReturnNonApprovedEscapes: Boolean = false,
                             showPastMidnightSlots: Boolean = false,
                             enableHourlySlotsForDayuseInOvernight: Boolean = false,
                             enableThirtyMinsSlots: Boolean = false,
                             enableSecretDealImprovement: Boolean = false,
                             ppLandingHotelIds: List[Int] = List.empty,
                             searchedHotelIds: List[Int] = List.empty,
                             mseHotelIds: List[Int] = List.empty,
                             enableCxlAversion: Option[Boolean] = None,
                             enablePushDayUseRates: Option[<PERSON>ole<PERSON>] = None,
                             ignoreRoomsCountForNha: Option[<PERSON><PERSON><PERSON>] = None)
