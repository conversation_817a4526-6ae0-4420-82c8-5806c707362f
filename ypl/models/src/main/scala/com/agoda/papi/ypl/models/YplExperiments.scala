package com.agoda.papi.ypl.models

object YplExperiments {

  val HOTEL_MVP_PROMOTIONS = "AFF-1542"
  val KILL_SWITCH_RATECATEGORY_CUSTOMER_SEGMENT = "CPL-79"

  // Umrah
  val UMRAH_B2B = "UMRAH-B2B"
  val UMRAH_B2C_REGIONAL = "UMRAH-B2C-REGIONAL"
  val UMRAH_B2C = "UMRAH-B2C"

  // Whitelabel

  // JTB
  val USE_ROOM_LINKAGE_INFO_FROM_PROPOFFER = "JPST-281"
  val IS_ROUND_PRICE_IN_EXTERNAL_DATA = "JPST-323"
  val KILL_SWITCH_HOKKAIDO_CAMPAIGN = "OWL-1139"
  val BLOCK_PROMOTIONS_FOR_SPECIFIC_SUPPLIERS = "JTBFP-1572"
  // Process rooms with ratePlans that have valid PaymentChannels
  val FIX_ROOM_FILTERING_ON_PAYMENT_CHANNEL = "JTBFUN-1000"
  // Remove rooms with InventoryType PayAtHotel for Japanican
  val REMOVE_OTA_JAPANICAN_KILL_SWITCH = "JTBFUN-1390"
  // Allow selling rate plans like OTA Japanican with empty payment channels
  val FIX_VALIDATION_FOR_PAYMENT_CHANNELS = "JTBFUN-1693"
  val FIX_PROMOTIONS_BREAKDOWN = "JTBFP-922"

  // True Agency
  val YCS_PAYMENT_OPTIONS = "TRUEAG-323"
  val YCS_RATE_FORKING = "TRUEAG-369"

  // Loyalty
  val CHECK_VIP_CUSTOMER_SEGMENT = "LOY-5731"
  val CHECK_PROMOTION_VIP_CUSTOMER_SEGMENT = "LOY-5840" // is taken since 2021 need to be clean up  (RC-2529)
  val SHOW_VIP_BADGE_FOR_VIP_YCS_PROMOTION = "LOY-5678"

  val GETAROOM_CHANNELS = "PAI-1117"

  val GOLOCAL_IGNORE_REQUESTED_CHANNEL_FOR_DISCOUNT_MSG = "MIN-18606"

  // BSUP
  val BCOM_NOCC_KILL_SWITCH = "BSUP-363"
  val BCOM_SKIP_LAST_MIN_NO_CC = "BSUP-612"
  val BCOM_USE_PAYMENTS_FACILITY_NO_CC = "BSUP-2246"
  val BCOM_RATE_COMMISSION = "BSUP-973"
  val BCOM_GRACE_PERIOD_ON_PROPERTY_PAGE = "BSUP-1102"
  val BCOM_V29_CXL_POLICY = "BSUP-1343"
  val BCOM_OVERRIDE_PAYMENT_OPTIONS_HYBRID = "BSUP-1716"
  val BCOM_RATE_LEVEL_CHILD_RATES = "BSUP-CHILD1"
  val BCOM_NO_CC_FROM_AVROOMS = "BSUP-3086"

  // APM
  val APM_RE_ENABLE_BEDBANK_RATE_CHANNEL = "APM-1405"
  val APM_ENABLE_AI_FOR_BED_NETWORK = "AIAR-916"
  val APM_MULTIPLE_DISCOUNT = "APM-2829"
  val APM_EXCLUDE_RATE_CHANNEL_IDS = "APM-4531"
  val APM_FIX_END_DATE = "APM-4438"
  val APM_COMMISSION_DISCOUNT_LOG = "APM-4670"
  val APM_ARP_PLUS_PROGRAM = "APM-4910"

  // BedsPaid
  val ADD_ORIGINAL_CHANNEL_ID_FOR_AI_BOOKING = "FEZZIK-845"
  val REMOVE_BEDS_PAID_EXCLUSION_FROM_APM = "FEZZIK-846"

  // Scalability
  val FIX_EXACT_OCC_ON_ALL_OC = "SCAL-1238"
  val OVERRIDE_CXL_CODE_FOR_EXT_LOYALTY = "SCAL-1418"

  // RESELL
  val SKIP_APM_PRICE_ADJUSTMENT_FOR_RESELL = "RSLCXL-488"

  // Rate Plan Promotion Text
  val FIX_OFFER_WITH_RATEPLAN_PROMOTION = "SUPPIO-5928"

  // Direct Connect PULL supplier bounceback
  val PULL_DIRECT_CONNECT_BOUNCEBACK = "SUPPIO-8337"

  // Tax
  val CLEAN_HP_TAX = "NEP-25984"

  // DFOPS
  val RATE_REUTIL_FENCING = "DFOPS-2011"
  val ORIGIN_CASE_INSENSITIVE_CUSTOMER_SEGMENT = "DFOPS-3879"

  // just for reading the fix happen in multiple place, so we just want to make it work as a whole under the same EXP
  val FIX_CHILD_RATE = "CHILD-33"
  // currently we remove it, so we would like to undo this for this exp
  val DO_NOT_REMOVE_CHILD_RATE_FREE = FIX_CHILD_RATE
  // MAFC: Max Allowed Free Children, fetch from hotel meta cache, room level
  val USE_MAX_OCC_CHILD_BEFORE_MAFC = FIX_CHILD_RATE
  val USE_MAX_OCC_CHILD_LIMIT_CHILD_RATE = FIX_CHILD_RATE

  // this will be run after the above has been finished as the fix share some common code change.
  val APPLY_PER_ROOM_LIMIT_CHILD_AGE = "CHILD-34"

  // the new Occ Logic
  val USE_NEW_DATA_AND_SKIP_CONVERTER = "CHILD-393"
  // CHILD-105: A, we derive from (YCS) child age policy, coming up in later MR
  // CHILD-105: B, generate from within new Occupancy Logic

  val USE_NEW_CHILD_AGE_RANGE_INSTEAD_OF_CHILD_POLICY = "CHILD-424"

  // Pay at Hotel
  val MORP_NOCC_SUPPORT_FULL_REFUNDABLE_CXL_ONLY = "PAH-157"

  val VALIDATE_SELLABLE_PAYMENT_CHANNEL = "JTBFP-564"

  val ENABLE_SELLING_DIFFERENT_SUPPLIERS_FOR_JTB = "JTBFP-615"

  val INCLUDE_BATHING_TAX = "JTBFP-1456"

  // Supply Growth
  val USE_BEDS_PAID_CONTRACT_TO_APPLY_BEDS_PAID = "SGPP-281"
  val ONBOARDING_AGX_PAID_PROGRAM = "SGPP-517"

  // Block International YCS Promotions
  val BLOCK_INT_YCS_PROMOS_BY_HOTEL_COUNTRY =
    "PAPI-20392" // Never run and created in calculon , need to be removed later (RC-2529)

  // Legal Tech
  val SET_CHECK_IN_TIME_FOR_LEAD_DAYS_VALIDATION = "LT-335"
  val FILTER_ROOMS_BY_CA_REGULATORY_REQUIREMENTS = "LT-410"
  val FOR_PULL_SPLIT_PROTO_PRICE_TO_ROOM_AND_SURCHARGE = "LT-1349"
  val FIX_CHEAPEST_ROOM_ORDERING_AFTER_ADDING_FEE_TO_US_ORIGIN = "LT-1415"

  // Third party/UnconventionalCICO
  val UNCONVENTIONAL_CHECKIN_CHECKOUT_REMOVE_COUNTRY_CHECK = "MNM-170"

  // it will never run on Prod
  val ENABLE_HMC_FETCH = "ST-3699"
  val USE_CHANNEL_DISCOUNT_FROM_HMC = "ST-3725"
  val USE_TAXES_FROM_HMC = "ST-3749"
  val USE_TAXES_FROM_HMC_SUPERAGG = "ST-3780"

  // Uspa ADR
  val FIX_SET_NET_RATE = "APM-4643"
  val FIX_SET_NET_RATE_BY_USER = "APM-4643-2"
  val APM_INCLUDE_ADR_RATE_CHANNEL = "APM-4618"
  val FIX_ISALLOCC_TRUE = "APM-4731"
  val ENABLE_ADJUST_CHEAPEST_OFFER_ONLY_AND_FALLBACK = "APM-4795"
  val ENABLE_PRE_BEAT_COMPARISON = "APM-4973"
  val ENABLE_ENHANCE_BOOKING_BREAKDOWN = "APM-4996"
  val UNMAPPED_ADJUSTMENT_MAX_CAP_CONFIG = "APM-4992"
  val MULTIROOM_PRICE_ADJUSTMENT_FIX = "APM-5037"

  // Uspa Boost Hero migration
  val ENABLE_USPA_BH = "OG-440"

  // USPA WAI
  val ENABLE_USPA_FLOW_OPTIMIZE = "CWSD-1717"
  val ENABLE_USPA_CVCO = "CWSD-1596"

  /// USPA
  val USE_SEQ_USPA_CAMPAIGN = "OG-544"
  val USPA_PERF_OPT = "OG-613"
  val USPA_HOTEL_TIMEOUT = "OG-591"
  val USPA_INFO = "APM-4934"

  // Supply Discount
  val ENABLE_DISPATCHING_OVERRIDE_TO_BE_EVERY_CUSTOMER_SEGMENT = "RC-2271"
  val ENABLE_DISPATCHING_OVERRIDE_TO_MSE_AND_AFFILIATE_TRAFFIC = "RC-2380"
  val ENABLE_DISPATCHING_OVERRIDE_USING_PULSE_PRECACHE = "RC-2331"
  val ENABLE_DISPATCHING_OVERRIDE_ON_CUSTOMER_SEGMENT_USING_PULSE_PRECACHE = "RC-2331-2"
  val ENABLE_PULSE_TO_ALL_RC = "RC-2470"
  val ALLOW_SHOW_PULSE_BADGE_FOR_EXTRA_DISPATCH_REASON = "RC-2508"
  val OVERRIDE_ECO_DEALS_TO_LOYALTY_CATEGORY = "RC-2416"
  val ENABLE_MEGA_CAMPAIGN = "RC-2660"

  // Consumer Fintech
  val PCFW_MESSAGING = "CFF-1059-PCFW"

  val FIX_COMBINED_PROMOTION_BREAKDOWN = "JTBFP-1461"

  val DMC_HARDCODING_REMOVAL = "JTBFP-1295"

  // Affiliate Sell Discount
  val AFFILIATE_GENERATE_SELL_DISCOUNT_OFFERS = "PAPI-22933"

  var DISABLE_ROOM_ON_ARRIVAL = "BWHF-5057"

  val USE_BASE_CXL_POLICY_FOR_PROMOTIONAL_ROOMS = "JTBFP-1601"
}
