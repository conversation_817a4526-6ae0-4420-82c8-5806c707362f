package com.agoda.papi.ypl.models.pricing

import com.agoda.papi.enums.room.RateType
import com.agoda.papi.ypl.models.pricing.BreakfastMatchStrategy.BreakfastMatchStrategy
import com.agoda.papi.ypl.models.pricing.CxlPolicyMatchStrategy.CxlPolicyMatchStrategy
import com.agoda.papi.ypl.models.pricing.BucketMatchStrategy.BucketMatchStrategy
import com.agoda.papi.ypl.models.pricing.OccupancyMatchStrategy.OccupancyMatchStrategy
import org.joda.time.DateTime

case class UspaCampaign(
  campaignId: Int,
  programId: Int,
  campaignName: String,
  rateType: RateType,
  maxDiscountPercent: Double,
  beatRatePercent: Double,
  benchmarkingCriteria: BenchmarkingCriteria,
  adjustableRateCriteria: AdjustableRateCriteria,
  benchmarkingRateCriteria: Option[BenchmarkRateCriteria] = None,
  adjustmentCondition: Option[AdjustmentCondition] = None,
  experimentId: Option[String] = None,
)
case class UspaCampaignDB(campaignId: Int, programId: Int, campaignData: String)

case class UspaHotelData(
  benchmarkingRateCriteria: Option[BenchmarkRateCriteria] = None,
)

object CxlPolicyMatchStrategy extends Enumeration {
  type CxlPolicyMatchStrategy = Value
  val Exact, SameGroup, SameOrLowerEpv, Ignore = Value
}

object OccupancyMatchStrategy extends Enumeration {
  type OccupancyMatchStrategy = Value
  val Exact, SameOrHigher, Ignore = Value
}

object BreakfastMatchStrategy extends Enumeration {
  type BreakfastMatchStrategy = Value
  val Exact, SameGroup, Ignore = Value
}

object BucketMatchStrategy extends Enumeration {
  type BucketMatchStrategy = Value
  val Exact, Count, Ignore = Value
}

case class BenchmarkingCriteria(
  cancellationPolicy: CxlPolicyMatchStrategy,
  occupancy: OccupancyMatchStrategy,
  breakfast: BreakfastMatchStrategy,
  preBeatComparison: Option[Boolean] = None,
  notApplyMaxDiscountForMultiCampaign: Option[Boolean] = None,
  dinnerBucket: Option[BucketMatchStrategy] = None,
  transportationBucket: Option[BucketMatchStrategy] = None,
  lunchBucket: Option[BucketMatchStrategy] = None,
  checkoutBucket: Option[BucketMatchStrategy] = None,
  activitiesBucket: Option[BucketMatchStrategy] = None,
  vipBucket: Option[BucketMatchStrategy] = None,
  essentialBucket: Option[BucketMatchStrategy] = None,
  miniBarBucket: Option[BucketMatchStrategy] = None,
  nonEssentialBucket: Option[BucketMatchStrategy] = None,
)

case class BenchmarkRateCriteria(
  include: Option[RateCriteria] = None,
  exclude: Option[RateCriteria] = None,
)

case class RateCriteria(
  dmcIds: Option[List[Int]] = None,
  rateChannels: Option[List[Int]] = None,
  ratePlans: Option[List[Int]] = None,
  chainIds: Option[List[Int]] = None,
  checkinFrom: Option[DateTime] = None,
  checkinTo: Option[DateTime] = None,
  bookStart: Option[DateTime] = None,
  bookEnd: Option[DateTime] = None,
  checkIns: Option[List[DateTime]] = None,
  minLos: Int = 1,
  rateCategoryInventoryType: Option[List[Int]] = None,
) {
  require(
    bookStart.forall(start => bookEnd.forall(end => start.isBefore(end))),
    "bookStart must be before bookEnd",
  )
  require(
    checkinFrom.forall(start => checkinTo.forall(end => start.isBefore(end))),
    "checkinFrom must be before checkinTo",
  )
}

case class AdjustableRateCriteria(
  include: Option[RateCriteria] = None,
  exclude: Option[RateCriteria] = None,
)

case class AdjustmentCondition(
  outputRateChannelId: Option[Int] = None,
  onlyCheapest: Option[Boolean] = None,
  rateMatchingFallback: Option[Boolean] = None,
  unmappedGroupAdjustment: Option[UnmappedGroupAdjustmentInfo] = None,
  cheapestUnmappedRoomMultiOffers: Option[CheapestUnmappedRoomMultiOffersInfo] = None,
  cheapestVsCheapestOffer: Option[Boolean] = None,
)

case class CheapestUnmappedRoomMultiOffersInfo(
  adjustIfMaxDiscountReached: Option[Boolean] = None,
  maxDiscountPercent: Option[Double] = None,
)

case class UnmappedGroupAdjustmentInfo(
  adjustIfMaxDiscountReached: Option[Boolean] = None,
  maxDiscountPercent: Option[Double] = None,
)
