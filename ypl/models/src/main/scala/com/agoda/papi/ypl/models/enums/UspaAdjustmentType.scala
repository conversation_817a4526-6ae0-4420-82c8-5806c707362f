package com.agoda.papi.ypl.models.enums

import enumeratum.{Enum, EnumEntry}

sealed abstract class UspaAdjustmentType(val value: Int) extends EnumEntry

object UspaAdjustmentType extends Enum[UspaAdjustmentType] {
  val values = findValues

  private val fields = values.map(x => (x.value, x)).toMap

  def getFromValue(value: Int): UspaAdjustmentType = fields.getOrElse(value, SameGroup)

  case object SameGroup extends UspaAdjustmentType(0)
  case object UnmappedGroup extends UspaAdjustmentType(1)
  case object CVCRoomSameGroup extends UspaAdjustmentType(2)
  case object CVCRoomUnmappedGroup extends UspaAdjustmentType(3)
  case object CVCOffer extends UspaAdjustmentType(4)
}
