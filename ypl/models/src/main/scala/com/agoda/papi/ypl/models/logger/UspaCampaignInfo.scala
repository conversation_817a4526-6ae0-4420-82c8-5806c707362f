package com.agoda.papi.ypl.models.logger

import com.agoda.papi.enums.room.RateType

case class UspaCampaignInfo(uspaRateType: String,
                            uspaProgramId: Int,
                            maxUspaDiscountPercent: Double,
                            beatRatePercent: Double,
                            uspaCampaignId: Int)
object UspaCampaignInfo {
  val default: UspaCampaignInfo = UspaCampaignInfo()

  def apply(uspaRateType: RateType = RateType.Unknown,
            uspaProgramId: Int = 0,
            maxUspaDiscountPercent: Double = 0d,
            beatRatePercent: Double = 0d,
            uspaCampaignId: Int = 0): UspaCampaignInfo =
    UspaCampaignInfo(uspaRateType.entryName, uspaProgramId, maxUspaDiscountPercent, beatRatePercent, uspaCampaignId)

}
