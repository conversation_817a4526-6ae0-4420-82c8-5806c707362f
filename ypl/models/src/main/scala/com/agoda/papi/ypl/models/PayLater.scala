package com.agoda.papi.ypl.models

import com.agoda.papi.enums.hotel.{PayLaterOptionCode, PayLaterSubTypeCode}
import org.joda.time.DateTime

case class PayLater(
  payLater: Boolean,
  authDate: DateTime = DateTime.now(),
  chargeData: DateTime = DateTime.now(),
  @deprecated("To be removed in favor of PayLaterOptions", "2024-06-30") eligibleSubTypes: Option[List[PayLaterSubType]] =
    None,
  payLaterOptions: Option[List[PayLaterOption]] = None)

case class PayLaterOption(
  code: PayLaterOptionCode,
)

@deprecated("to be removed in favor of PayLaterOption", "2024-06-30")
case class PayLaterSubType(
  code: PayLaterSubTypeCode,
)
