package com.agoda.papi.ypl.models.pricing

import com.agoda.papi.common.pricing.models.CommonPrice
import com.agoda.papi.enums.room.{ApplyType, ChargeOption, ChargeType, RateType, SubChargeType}
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes}
import com.agoda.papi.pricing.pricecalculation.models.CommonProcessingFeeBreakdown
import com.agoda.papi.ypl.models.{DiscountMessageType, SurchargeInfo}
import com.agoda.papi.ypl.models.api.request.YplAGXCommission
import com.agoda.papi.ypl.models.consts.Firedrill.agpInvoiceCollectionRate
import com.agoda.papi.ypl.models.enums.BreakdownStep
import com.agoda.papi.ypl.models.pricing.proto.{DailyPrice, SurchargeEntry}
import org.joda.time.DateTime

/**
  * Daily price information
  *
  * @param roomNumber            to display room number when multiple rooms assignment request
  * @param date                  Date of price
  * @param quantity              depending on [[applyType]]; for [[chargeType]] == [[ChargeType.Surcharge]] usually => number of adults, for [[chargeType]] == [[ChargeType.Room]] => number of rooms
  * @param chargeType            price's [[ChargeType]]
  * @param subChargeType         price's [[SubChargeType]] Ex Chartype = Room, SubChargeType = Baby
  * @param applyType             price's [[ApplyType]]
  * @param chargeOption          price's [[ChargeOption]]
  * @param netExclusive          netEx amount
  * @param tax                   calculated tax amount
  * @param fee                   calculated fee amount
  * @param margin                calculated margin amount
  * @param processingFee         calculated processing fee amount
  * @param taxBreakDown          tax breakdown
  * @param refMargin             expected this as a copy of margin, this is used as ref when margin is Downlifted
  * @param refProcessingFee      expected this as a copy of processingFee, this is used as ref when margin is Downlifted
  * @param isConfigProcessingFee usually it's false; true if is not auto calculation (not allowed by country/state level - usually for US)
  * @param value                 originally it stores rateLoadedTypeAmount, later it is overridden by rateLoadedTypeAmount - ChannelDiscount
  * @param channelDiscounts      List of applied channel discounts
  * @param agxCommission         AGX commission details
  */
case class YplPrice(
  roomNumber: Option[Int] = None,
  date: DateTime,
  quantity: Int,
  chargeType: ChargeType,
  subChargeType: SubChargeType = SubChargeType.None,
  applyType: ApplyType,
  chargeOption: ChargeOption,
  refId: Int,
  netExclusive: Double,

  /**
    * if applyOver is not defined the tax below would either
    * contain summ of taxes applied on NetEx for "Non Merchant Comm" flow
    * or contain summ of taxes applied on SellEx for "Merchant Comm" flow
    */
  tax: Double,
  fee: Double,
  margin: Double,
  processingFee: Double,
  promotionDiscount: Double,
  downliftAmount: Double,
  downliftPercent: Double,
  taxBreakDown: List[CommonTaxBreakdown],
  dailyTaxes: DailyTaxes,
  refMargin: Double,
  refProcessingFee: Double,
  isConfigProcessingFee: Boolean,
  value: Double, // rateLoadedTypeAmount - ChannelDiscount
  upliftedSellIn: Option[Double],
  processingFeeBreakdown: Option[CommonProcessingFeeBreakdown],
  // discount amounts for promotions (grouped by promo type), downlift and channel discount
  discountMessages: Map[Int, DiscountMessage],
  downliftExAmount: Option[Double],
  surchargeRateType: Option[RateType] = None,
  surchargeInfo: Option[SurchargeInfo] = None,
  surchargeEntry: Option[SurchargeEntry] = None,
  dailyPrice: Option[DailyPrice] = None,
  referenceCommissionPercent: Double = 0d,
  agxCommission: YplAGXCommission,
  channelDiscounts: List[YplChannelDiscountBreakdown] = Nil,
  resellRefSell: Option[Double] = None,
  currentBreakdownStep: BreakdownStep = BreakdownStep.Unknown,
  priceBreakdownHistory: BookingPriceBreakdown = BookingPriceBreakdown(),
  apmPriceAdjustmentDetail: Option[ApmPriceAdjustmentDetail] = None,
  apmCommissionDiscountPercent: Double = 0d,
  taxOverSellEx: Double = 0d,
  childAgeRangeId: Option[Long] = None,
  cashbackPercent: Option[Double] = None,
  cashbackAmount: Option[Double] = None,
  agpMargin: Double = 0.0d,
  agpInvoiceMargin: Double = 0.0d,
  isApplyRefCommissionOnRefSellEx: Boolean = false,
  variableTax: Option[Double] = None,
  supplierFundedDiscountAmount: Option[Double] = None,
  uspaDiscountAmount: Option[Double] = None,
  uspaProgramId: Option[Int] = None,
  appliedServiceTaxCountry: Option[String] = None,
) extends CommonPrice {
  // Agx Calculation
  lazy val payAsYouGoCommission = refSellInclusive * (agxCommission.payAsYouGoCommission / 100d)
  lazy val prepaidCommission = refSellInclusive * (agxCommission.prepaidCommission / 100d)
  lazy val freeTrialCommission = refSellInclusive * (agxCommission.freeTrialCommission / 100d)
  lazy val agpReferenceCommission = refSellInclusive * (agpMargin / 100d)
  lazy val agpInvoiceReferenceCommission = refSellInclusive * (agpInvoiceMargin / 100d) * agpInvoiceCollectionRate

  def getUpdatedDiscountMessages(discountMessage: DiscountMessage): Map[DiscountMessageType, DiscountMessage] = {
    val updatedAmount =
      this.discountMessages.get(discountMessage.discountType).map(_.value).getOrElse(0d) + discountMessage.value
    this.discountMessages + (discountMessage.discountType -> discountMessage.copy(value = updatedAmount))
  }

  def getUpdatedPriceBreakdown(isEnableEnhanceBookingBreakdown: Boolean = false): BookingPriceBreakdown =
    this.priceBreakdownHistory.addBreakdown(this, isEnableEnhanceBookingBreakdown)
}
