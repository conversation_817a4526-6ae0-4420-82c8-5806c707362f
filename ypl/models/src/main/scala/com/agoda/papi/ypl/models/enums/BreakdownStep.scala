package com.agoda.papi.ypl.models.enums

import enumeratum.{Enum, EnumEntry}

sealed abstract class BreakdownStep(val value: Int) extends EnumEntry

object BreakdownStep extends Enum[BreakdownStep] {
  val values = findValues

  private val fields = values.map(x => (x.value, x)).toMap

  def getFromValue(value: Int): BreakdownStep = fields.getOrElse(value, BaseStep)

  case object Unknown extends BreakdownStep(0)
  case object BaseStep extends BreakdownStep(1)
  case object Promotions extends BreakdownStep(2)
  case object ChannelDiscount extends BreakdownStep(3)
  case object PriceBreakdown extends BreakdownStep(4)
  case object AGP extends BreakdownStep(5)
  case object Soybean extends BreakdownStep(6)
  case object ConvertedPrice extends BreakdownStep(7)
  @deprecated("ThaiGov campaign is over")
  case object ThaiGovernmentCampaign extends BreakdownStep(8)
  case object FinalPrice extends BreakdownStep(9)
  case object BasePriceInChannelRateType extends BreakdownStep(10)
  case object ChannelAndPromotionDiscount extends BreakdownStep(11)
  case object TaxProtypeLevelCorrection extends BreakdownStep(12)
  case object APMDiscount extends BreakdownStep(13)
  case object AveragePrice extends BreakdownStep(14)
  case object RateCategoryDiscount extends BreakdownStep(15)
  case object APMCommissionDiscount extends BreakdownStep(16)
  case object ChildRate extends BreakdownStep(17)
  case object SupplierFundedDiscount extends BreakdownStep(18)
  case object USPADiscount extends BreakdownStep(19)
  case object CEGAmend extends BreakdownStep(20)
}
