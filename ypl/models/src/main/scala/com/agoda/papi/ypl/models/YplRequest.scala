package com.agoda.papi.ypl.models

import com.agoda.finance.tax.models.{ApplyTaxOnSellExSettings, SupplierHotelCalculationSettings}
import com.agoda.finance.tax.services.tax.applytaxover.ApplyTaxOverHelper
import com.agoda.papi.constants.PlatformID
import com.agoda.papi.ypl.models.api.request._
import org.joda.time.{DateTime, Days}
import com.agoda.papi.enums.request.{BookingDurationType, FeatureFlag, FilterCriteria}
import com.agoda.papi.ypl.commission.apm.models.ApmSettingHolder
import com.agoda.papi.ypl.fencing.{AgxCommissionFencing, SameAgxCommissionForAllFences}
import com.agoda.papi.ypl.models.settings.CommonTaxSettings

trait WithYplRequest {
  def request: YplRequest
}

case class PreFilterZeroAllotment(roomTypeId: RoomTypeId, rateCategoryId: RateCategoryId)
case class SimulationRequest(channels: Set[ChannelId])

/**
  * YplRequest
  */
case class YplRequest(
  searchId: String,
  checkIn: DateTime,
  checkOut: DateTime,
  currency: Currency = "",
  channels: Set[YplChannel] = Set(YplMasterChannel.RTL),
  // ToDO: move it to forced experiments in ExperimentContext
  experiments: YplExperiments = Nil,
  occ: YplOccInfo = YplOccInfo(),
  cInfo: YplClientInfo = YplClientInfo(),
  flagInfo: YplFlagInfo = YplFlagInfo(),
  bookingDate: DateTime = DateTime.now,
  featureFlags: Set[FeatureFlag] = Set.empty,
  /* Since with PAPI we still need to have HotelSearch type instead of City search, we just pass flag isSSR from papi here */
  isSSR: Boolean = false,
  isBookingRequest: Boolean = false,
  simulateRequestData: Option[YplSimulateRequestData] = None,
  /** One Property at a time so not making it as a list. * */
  isCheapestRoomOnly: Boolean = false,
  featureRequest: YplFeatureRequest = YplFeatureRequest(),
  whitelabelSetting: WhitelabelSetting,
  regulationFeatureEnabledSetting: YplRegulationFeatureEnabledSetting = YplRegulationFeatureEnabledSetting.default,
  supplierFeatures: SupplierFeatures,
  preFiltersZeroAllotment: Option[PreFilterZeroAllotment] = None,
  agxCommissionAdjustmentFences: AgxCommissionFencing = new SameAgxCommissionForAllFences(Map.empty),
  bComCCSetting: Option[BComCCSetting] = None,
  bcomBMPSetting: Option[BComBMPSetting] = None,
  heuristicRequest: Option[YplHeuristicRequest] = None,
  apmSetting: Option[ApmSettingHolder] = None,
  bookingDurationTypes: List[BookingDurationType] = Nil,
  hasSortingStrategy: Boolean = false,
  fences: Map[YplChannel, Set[YplRateFence]] = Map.empty,
  cheapestRoomFilters: List[Int] = Nil,
  filterCriteria: Option[List[FilterCriteria]] = None,
  jtbSetting: Option[YplJtbSetting] = None,
  applyTaxOnSellExSettings: Option[ApplyTaxOnSellExSettings] = None,
  supplierHotelCalculationSettings: SupplierHotelCalculationSettings =
    SupplierHotelCalculationSettings(settings = Map.empty),
  selectedCheckInTime: Option[String] = None,
  hourlyDurationFilter: Set[Int] = Set.empty,
  /** Added this parameter for rate channel Simulation */
  simulationRequest: Option[SimulationRequest] = None,
  commonTaxSettingsOpt: Option[CommonTaxSettings] = None,
  bookRoomIdentifier: Option[GUIDGenerator.RoomIdentifiers] = None,
  enableOfferFilterLogs: Boolean = false,
  superAggOccupancySamplingRate: SuperAggOccupancyFlowSetting =
    SuperAggOccupancyFlowSetting(samplingRate = 0.05, enableNewFlow = false),
  isXmlPartner: Boolean = false,
  filterPaymentModelAgencyByModifyingRoomStatus: Boolean = false,
  supplierFundedDiscountSetting: SupplierFundedDiscountSetting = SupplierFundedDiscountSetting(),
  priceAdjustmentId: Option[Long] = None,
  isApplyNewOccupancyLogic: Boolean = false,
  minBookingCountForSuperAgg: Option[Seq[YplMinBookingCountForSuperAgg]] = None,
  isAvailableCapacityIncludeChildren: Boolean = false,
  uspaStepSettings: UspaStepSetting = UspaStepSetting(50))
  extends WithYplRequest {

  override lazy val request: YplRequest = this

  // WARN: use this wisely.
  // Note: we have feature that property offer protobuf data los != requested los
  // For pricing, we suggest to use the lengthOfStay from YplHotelEntryModel instead
  lazy val lengthOfStay: Int = Days.daysBetween(checkIn, checkOut).getDays

  /**
    * Days before checkIn
    */
  lazy val leadDays: Int = Math.max(0, Days.daysBetween(bookingDateStartOfDay, requestDateStartOfDay).getDays)

  lazy val requestDateStartOfDay = checkIn.withTimeAtStartOfDay()

  lazy val bookingDateStartOfDay = bookingDate.withTimeAtStartOfDay()

  lazy val returnZeroAllotment = featureFlags.contains(FeatureFlag.ReturnZeroAllotment)

  lazy val roomsLimit: Option[Int] = heuristicRequest.flatMap(_.roomsLimit).filter(_ > 0)

  lazy val asoRoomsLimit: Option[Int] = heuristicRequest.flatMap(_.asoRoomsLimit).filter(_ > 0)

  lazy val allFences: Set[YplRateFence] = fences.flatMap(_._2.toList).toSet

  def baseFences(channel: YplChannel): Set[YplRateFence] = fences.getOrElse(channel, allFences)

  lazy val channelIdToFences: Map[ChannelId, Set[YplRateFence]] = fences.map { case (channel, fenceSet) =>
    channel.compositeChannelId -> fenceSet
  }

  def isHourlyRequest: Boolean = bookingDurationTypes.contains(BookingDurationType.Hourly)

  def isAllMseTraffic: Boolean = isMseLanding ||
    request.cInfo.platform.exists(PlatformID.isAffiliateOrPricePush)

  def isMseLanding: Boolean = request.featureRequest.mseHotelIds.nonEmpty

  final def isRateChannelSimulationRequest: Boolean = this.simulationRequest.exists(_.channels.nonEmpty)

  lazy val checkInStr = checkIn.toString("yyyy-MM-dd")
  lazy val checkOutStr = checkOut.toString("yyyy-MM-dd")

  val applyTaxOverHelper = ApplyTaxOverHelper(
    Set.empty[Int],
    applyTaxOnSellExSettings,
    supplierHotelCalculationSettings,
  )
}

case class YplCriteria(hotelId: Long, supplierId: Int, checkInMillis: Long, lengthOfStay: Int)

case class YplHeuristicRequest(roomsLimit: Option[Int] = None,
                               asoRoomsLimit: Option[Int] = None,
                               limitCheapestRooms: Option[Int] = None)
case class YplJtbSetting(otaJapanicanMaxBookingValue: Int)
case class YplMinBookingCountForSuperAgg(inDays: Int, count: Int)
