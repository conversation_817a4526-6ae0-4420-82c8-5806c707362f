package com.agoda.papi.ypl.models.pricing

import com.agoda.papi.enums.room.{ChargeType, RateType}
import com.agoda.papi.pricing.pricecalculation.models.response.Calculation
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, DailyTaxes}
import com.agoda.papi.ypl.models.enums.{BreakdownStep, GrowthProgramCommissionBreakdown, UspaPriceBreakdown}
import com.agoda.papi.ypl.models.logger.UspaCampaignInfo
import com.agoda.papi.ypl.models.pricing.BookingPriceBreakdown.roundNumber
import com.agoda.papi.ypl.models.pricing.proto.{
  BaseProtoOccupancy,
  BaseProtoPrice,
  BaseProtoTaxBreakdownV2,
  PromotionEntry,
  SurchargeEntry,
}
import com.agoda.utils.collection.SumImplicits._
import com.typesafe.scalalogging.LazyLogging

import java.util.Date

case class BookingPriceBreakdown(isEnabled: Boolean,
                                 breakdown: Seq[PriceBreakdown],
                                 baseProtoPrice: BaseProtoPrice,
                                 promotionDiscount: Double,
                                 channelDiscount: Double,
                                 downliftPercent: Double,
                                 isAPMDiscountStep: Boolean,
                                 firedrill: Double,
                                 taxBreakdownMap: Map[(Int, Int), BaseProtoTaxBreakdownV2],
                                 apmCommissionDiscountPercent: Double,
                                 isAPMCommissionDiscountStep: Boolean,
                                 apmDiscountAmount: Double,
                                 gpCommissionBreakdown: GrowthProgramCommissionBreakdown,
                                 supplierFundedDiscountPercent: Double,
                                 uspaDiscountBreakdown: UspaPriceBreakdown,
                                 uspaCampaignInfo: UspaCampaignInfo)
  extends LazyLogging {
  def newBreakdown(chargeType: ChargeType, rateType: RateType, currency: String, value: Double): BookingPriceBreakdown =
    addBreakdown(BreakdownStep.BaseStep, chargeType, rateType, currency, value)

  def addBreakdown(step: BreakdownStep,
                   chargeType: ChargeType,
                   rateType: RateType,
                   currency: String,
                   value: Double): BookingPriceBreakdown = whenEnabled(this) {
    addBreakdown(PriceBreakdown.create(step, currency, chargeType, rateType, value))
  }

  def addBreakdown(step: BreakdownStep = BreakdownStep.Unknown,
                   chargeType: String = ChargeType.Unknown.entryName,
                   discountAmount: String = "",
                   currency: String,
                   netIn: Double,
                   netEx: Double,
                   sellIn: Double,
                   sellEx: Double,
                   tax: Double,
                   fee: Double,
                   margin: Double,
                   processingFee: Double,
                   taxAndFeeAmount: Option[Double] = None): BookingPriceBreakdown = whenEnabled(this) {
    addBreakdown(
      PriceBreakdown(
        stepDesc = step.entryName,
        chargeType = chargeType,
        currency = currency,
        discountAmount = discountAmount,
        sellIn = Option(sellIn),
        sellEx = Option(sellEx),
        netIn = Option(netIn),
        netEx = Option(netEx),
        tax = Option(tax),
        fee = Option(fee),
        margin = Option(margin),
        processingFee = Option(processingFee),
        taxAndFeeAmount = taxAndFeeAmount,
      ),
    )
  }

  def addBreakdown(yplPrice: YplPrice, isEnableEnhanceBookingBreakdown: Boolean): BookingPriceBreakdown =
    if (isEnabled && yplPrice.currentBreakdownStep != BreakdownStep.Unknown) {
      val discountAmount =
        try {
          def agxCommission = roundNumber(baseProtoPrice.agxCommission.payAsYouGoCommission)
          yplPrice.currentBreakdownStep match {
            case BreakdownStep.PriceBreakdown => s"AGX: $agxCommission% (NA)"
            case BreakdownStep.Promotions => s"PRO: NA (${roundNumber(promotionDiscount)}), AGX: $agxCommission% (NA)"
            case BreakdownStep.ChannelDiscount => s"CHL: NA (${roundNumber(channelDiscount)})"
            case BreakdownStep.ChannelAndPromotionDiscount =>
              s"CHL: NA (${roundNumber(channelDiscount)}), PRO: NA (${roundNumber(promotionDiscount)})"
            case BreakdownStep.Soybean =>
              s"DL: ${roundNumber(yplPrice.downliftPercent)}% (${roundNumber(yplPrice.downliftAmount)})"
            case BreakdownStep.AGP => s"Total AGP Overwrite: ${roundNumber(firedrill)}% (NA), " +
                s"RetailCC: ${roundNumber(gpCommissionBreakdown.originalRetailCC)}%, " +
                s"AGP INM: ${roundNumber(gpCommissionBreakdown.agpInm)}%, " +
                s"Stacked AGX INM: ${roundNumber(gpCommissionBreakdown.stackedAgxInm)}%, " +
                s"APM DISC: ${roundNumber(gpCommissionBreakdown.stackedApmDiscount)}%"
            case BreakdownStep.APMDiscount => s"APM Adjustment: ${roundNumber(apmDiscountAmount)} (NA)"
            case BreakdownStep.APMCommissionDiscount =>
              s"APM Comm Discount ${roundNumber(apmCommissionDiscountPercent)} (NA)"
            case BreakdownStep.SupplierFundedDiscount =>
              s"Supplier Funded Discount ${roundNumber(supplierFundedDiscountPercent)}"
            case BreakdownStep.USPADiscount if isEnableEnhanceBookingBreakdown =>
              s"USPA: ${roundNumber(uspaDiscountBreakdown.uspaDiscountPct)}% (${roundNumber(uspaDiscountBreakdown.uspaDiscountAmount)}) " +
                s"[${uspaCampaignInfo.uspaRateType}, campaignId: ${uspaCampaignInfo.uspaCampaignId}]"
            case BreakdownStep.USPADiscount if !isEnableEnhanceBookingBreakdown =>
              s"USPA Discount ${roundNumber(uspaDiscountBreakdown.uspaDiscountAmount)}"
            case _ => "NA"
          }
        } catch {
          case ex: Throwable =>
            logger.error(ex.getMessage, ex.getCause)
            s"Error: ${ex.toString}"
        }

      addBreakdown(
        step = yplPrice.currentBreakdownStep,
        currency = baseProtoPrice.currency,
        chargeType = yplPrice.chargeType.entryName,
        discountAmount = discountAmount,
        sellIn = yplPrice.sellInclusive,
        sellEx = yplPrice.sellExclusive,
        netIn = yplPrice.netInclusive,
        netEx = yplPrice.netExclusive,
        tax = yplPrice.tax,
        fee = yplPrice.fee,
        margin = yplPrice.margin,
        processingFee = yplPrice.processingFee,
        taxAndFeeAmount = Option(yplPrice.taxAndFeeAmount),
      )

    } else {
      this
    }

  def addBreakdown(calculation: Calculation,
                   chargeType: String,
                   currentBreakdownStep: BreakdownStep): BookingPriceBreakdown =
    if (isEnabled && currentBreakdownStep != BreakdownStep.Unknown) {
      addBreakdown(
        step = currentBreakdownStep,
        currency = baseProtoPrice.currency,
        chargeType = chargeType,
        sellIn = calculation.netEx + calculation.tax + calculation.fee + calculation.margin + calculation.processingFee,
        sellEx = calculation.netEx + calculation.margin,
        netIn = calculation.netEx + calculation.tax + calculation.fee,
        netEx = calculation.netEx,
        tax = calculation.tax,
        fee = calculation.fee,
        margin = calculation.margin,
        processingFee = calculation.processingFee,
        taxAndFeeAmount = Option(calculation.breakdowns.getDSum(t => t.amount * t.quantity, f => f.isAmount)),
      )
    } else {
      this
    }

  def addBreakdown(priceBreakdown: => PriceBreakdown): BookingPriceBreakdown = whenEnabled(this) {
    copy(breakdown = breakdown :+ priceBreakdown)
  }

  def addPromotionV2(current: Option[PromotionEntry],
                     next: PromotionEntry,
                     rawValue: Double,
                     calculatedValue: Double,
                     baseValue: Double): BookingPriceBreakdown = whenEnabled(this) {
    copy(baseProtoPrice = baseProtoPrice.appendPromotionV2(current, next, rawValue, calculatedValue, baseValue))
  }
  def addSurcharge(surcharge: SurchargeEntry): BookingPriceBreakdown = whenEnabled(this) {
    copy(baseProtoPrice = baseProtoPrice.addSurcharge(surcharge))
  }

  def addTaxV2(breakdownStep: BreakdownStep,
               taxBreakDown: List[CommonTaxBreakdown],
               isApplyNoProcessingFee: Boolean = false,
               isApplyTaxOnSellEx: Boolean = false): BookingPriceBreakdown = whenEnabled(this) {
    val newTaxMap = taxBreakDown.foldLeft(taxBreakdownMap) { (currentTaxes, newTax) =>
      currentTaxes.get((newTax.typeId, newTax.taxProtoTypeId)) match {
        case Some(tax) => currentTaxes + ((newTax.typeId, newTax.taxProtoTypeId) -> tax.addAmount(breakdownStep, newTax))
        case _ =>
          currentTaxes + ((newTax.typeId, newTax.taxProtoTypeId) -> BaseProtoTaxBreakdownV2(breakdownStep, newTax))
      }
    }

    copy(
      baseProtoPrice = baseProtoPrice.copy(
        isApplyNoProcessingFee = isApplyNoProcessingFee,
        isApplyTaxOnSellEx = isApplyTaxOnSellEx,
      ),
      taxBreakdownMap = newTaxMap,
    )
  }

  def addTotalTaxPercent(dailyTaxes: DailyTaxes): BookingPriceBreakdown = {
    val totalTaxPercent =
      if (dailyTaxes.hasHospitalityPrice) {
        dailyTaxes.totalTaxAndFeePercent + dailyTaxes.totalHospitalityTaxPercent
      } else {
        dailyTaxes.totalTaxAndFeePercent
      }
    copy(baseProtoPrice = baseProtoPrice.copy(
      totalTaxPercent = totalTaxPercent,
    ))
  }

  def updateOccupancyAndRoomQuantities(stayOccupancy: BaseProtoOccupancy,
                                       simplifiedRoomQuantity: Option[Int],
                                       roomQuantity: Int): BookingPriceBreakdown = whenEnabled(this) {
    copy(baseProtoPrice = baseProtoPrice.update(stayOccupancy, simplifiedRoomQuantity, roomQuantity))
  }

  def updateTaxBreakdown(): BookingPriceBreakdown = whenEnabled(this) {
    copy(baseProtoPrice = baseProtoPrice.copy(taxBreakdown = taxBreakdownMap.map(_._2)(collection.breakOut)))
  }

  def updateTimestamp(timestamp: Option[Date]): BookingPriceBreakdown = whenEnabled(this) {
    copy(baseProtoPrice = baseProtoPrice.copy(protobufChangeTimestamp = timestamp))
  }

  def updateDiscount(promoDiscount: Double = 0.0, channelDiscount: Double = 0.0): BookingPriceBreakdown =
    whenEnabled(this) {
      copy(promotionDiscount = promoDiscount, channelDiscount = channelDiscount)
    }

  def updateBaseProto(updatedBaseProtoPrice: => BaseProtoPrice): BookingPriceBreakdown = whenEnabled(this) {
    copy(baseProtoPrice = updatedBaseProtoPrice)
  }

  def updateHash(hash: Option[String]): BookingPriceBreakdown = whenEnabled(this) {
    copy(baseProtoPrice = baseProtoPrice.copy(protobufHash = hash))
  }

  def updateUspaCampaignInfo(uspaCampaignInfo: UspaCampaignInfo): BookingPriceBreakdown = whenEnabled(this) {
    copy(uspaCampaignInfo = uspaCampaignInfo)
  }
  private def whenEnabled[T](default: T)(fn: => T): T =
    if (isEnabled) {
      fn
    } else {
      default
    }
}

object BookingPriceBreakdown {
  def apply(isEnabled: Boolean = false,
            protobuf: BaseProtoPrice = BaseProtoPrice(),
            uspaCampaignInfo: UspaCampaignInfo = UspaCampaignInfo.default): BookingPriceBreakdown =
    BookingPriceBreakdown(
      isEnabled = isEnabled,
      breakdown = Seq.empty[PriceBreakdown],
      baseProtoPrice = protobuf,
      promotionDiscount = 0.0,
      channelDiscount = 0.0,
      downliftPercent = 0.0,
      isAPMDiscountStep = false,
      firedrill = 0.0,
      taxBreakdownMap = Map.empty,
      apmCommissionDiscountPercent = 0.0,
      isAPMCommissionDiscountStep = false,
      apmDiscountAmount = 0.0,
      gpCommissionBreakdown = GrowthProgramCommissionBreakdown.default,
      supplierFundedDiscountPercent = 0.0,
      uspaDiscountBreakdown = UspaPriceBreakdown.default,
      uspaCampaignInfo = uspaCampaignInfo,
    )

  /**
    *    returns the current breakdown step based on the inputs due to the complexed logic which help
    *    to avoid logging the same step or unused steps.
    */
  def getCurrentBreakdownStep(bookingPriceBreakdown: BookingPriceBreakdown,
                              isChannelDiscountStep: Boolean,
                              latestBreakdownStep: BreakdownStep): BreakdownStep =
    /* If it is channel discount step
        1. If latest step is Price Breakdown (regular promo)
          a) if it has both channel and promotion discount, the current step will be ChannelAndPromotionsDiscount
          b) if it has only promo, Promotions
          c) if it has only channel, ChannelDiscount
          d) else it will be Unknown
        2. if it is not price breakdown, it means the previous step is Promotions
          a) if it has channel discount, ChannelDiscount as the breakdown is always updated when calculating channel discount
          b) else if there is no channel discount, it means the breakdown was never updated and it will check if it
             has promotions. If it has promotions, then Promotions.
          c) else, if it doesn't have both channel and promotion discount, PriceBreakdown
       If haven't applied channel discount yet
        1. If the latest step is not the base step:
          a) If it is not APM discount step, it means that the current step is Tax Prototype Level Correction step
          b) else it would be the APM discount step.
        2. If the latest step is the Base Step,
          a) If discount is 0, the current step will be a price breakdown since there is no promotions
          b) else the current step will be the Promotions step.
     */
    if (isChannelDiscountStep) {
      if (latestBreakdownStep == BreakdownStep.PriceBreakdown) {
        if (bookingPriceBreakdown.promotionDiscount > 0 && bookingPriceBreakdown.channelDiscount > 0)
          BreakdownStep.ChannelAndPromotionDiscount
        else if (bookingPriceBreakdown.promotionDiscount > 0) BreakdownStep.Promotions
        else if (bookingPriceBreakdown.channelDiscount > 0) BreakdownStep.ChannelDiscount
        else BreakdownStep.Unknown
      } else {
        if (bookingPriceBreakdown.channelDiscount > 0) BreakdownStep.ChannelDiscount
        else if (bookingPriceBreakdown.promotionDiscount > 0) BreakdownStep.Promotions
        else BreakdownStep.PriceBreakdown
      }
    } else {
      if (latestBreakdownStep != BreakdownStep.BaseStep) {
        if (bookingPriceBreakdown.isAPMDiscountStep) BreakdownStep.APMDiscount
        else if (bookingPriceBreakdown.isAPMCommissionDiscountStep) BreakdownStep.APMCommissionDiscount
        else BreakdownStep.TaxProtypeLevelCorrection
      } else {
        if (bookingPriceBreakdown.promotionDiscount > 0) BreakdownStep.Promotions
        else BreakdownStep.PriceBreakdown
      }
    }

  def roundNumber(value: Double): Double = BigDecimal(value).setScale(2, BigDecimal.RoundingMode.HALF_UP).doubleValue()

  def updateTaxAndProtobufTimestamp(bookingPriceBreakdown: BookingPriceBreakdown,
                                    protobufChangeTimestamp: Option[Date]): BookingPriceBreakdown =
    bookingPriceBreakdown.updateTimestamp(protobufChangeTimestamp).updateTaxBreakdown()
}
