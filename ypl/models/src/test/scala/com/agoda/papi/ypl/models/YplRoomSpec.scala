package com.agoda.papi.ypl.models

import com.agoda.papi.ypl.models.api.request.YplAGXCommission
import org.specs2.mutable.SpecificationWithJUnit

class YplRoomSpec extends SpecificationWithJUnit with YPLTestDataBuilders {

  "YPLRoom" should {
    "update fences" in {
      aValidRoom.copyFences(Set(YplRateFence("TH", -1, 1))).fences must_== Set(YplRateFence("TH", -1, 1))
    }

    "isFit" in {
      "isAllOccSearch: true" in {
        val mockRoom = aValidRoom.withRoomFeatures(RoomFeatures(isAllOccSearch = true))
        mockRoom.isFit shouldEqual Some(true)
      }
      "isAllOccSearch: false" in {
        "ReqOcc: None" in {
          val mockRoom = aValidRoom.withRoomFeatures(RoomFeatures(isAllOccSearch = false)).withReqOcc(None)
          mockRoom.isFit shouldEqual None
        }
        "ReqOcc: Some" in {
          "occ free" in {
            aValidRoom
              .withRoomFeatures(RoomFeatures(isAllOccSearch = false))
              .withReqOcc(Some(aValidReqOcc.withAdults(0)))
              .build
              .isFit shouldEqual Some(false)
          }

          "exact match occ: true" in {
            "no children" in {
              val mockRoom = aValidRoom
                .withRoomFeatures(RoomFeatures(isAllOccSearch = false))
                .withReqOcc(
                  Some(aValidReqOcc.withAdults(1).withRooms(1).withOccupancy(aValidOccInfo.withAdults(1).withRooms(1))))
                .withOccupancy(aValidRoomOccupancy.withAdults(1).withChildren(0).withInfants(0))
                .withYplRoomEntry(aValidRoomEntry.withRoomType(aValidRoomTypeEntry.withMaxOccupancy(1)))
              mockRoom.isFit shouldEqual Some(true)
            }
          }

          "has children" in {
            val mockRoom = aValidRoom
              .withRoomFeatures(RoomFeatures(isAllOccSearch = false))
              .withReqOcc(
                Some(
                  aValidReqOcc
                    .withAdults(1)
                    .withRooms(1)
                    .withAgePolicy(aValidAgePolicy
                      .withMinGuestAge(0)
                      .withInfantMaxAge(1)
                      .withChildMaxAge(2)
                      .withIsChildrenStayingFree(true))
                    .withChildrenAges(List(1))
                    .withOccupancy(aValidOccInfo.withAdults(1).withRooms(1).withChildAges(1))))
              .withOccupancy(aValidRoomOccupancy.withAdults(1).withChildren(1).withInfants(0))
              .withYplRoomEntry(aValidRoomEntry.withRoomType(aValidRoomTypeEntry.withMaxOccupancy(2)))
            mockRoom.isFit shouldEqual Some(true)
          }
        }
      }
    }

    "prices" in {
      val mockYplRoom = aValidRoom
        .withPrice(
          aValidPrice
            .withNetExclusive(1)
            .withTax(2)
            .withFee(3)
            .withMargin(4)
            .withProcessingFee(5)
            .withDownliftAmount(6)
            .withYplAgxCommission(
              YplAGXCommission(
                payAsYouGoCommission = 10,
                prepaidCommission = 20,
                freeTrialCommission = 30,
                isError = false,
              ))
            .withAgpMargin(14)
            .withAgpInvoiceMargin(15))
        .withPriusOutput(
          YplPriusOutput(
            value = 100,
            points = 200,
            margin = 50,
            tax = 51,
            channel = 1,
          ))
      "room prices" in {
        mockYplRoom.netEx shouldEqual 1.0
        mockYplRoom.netIn shouldEqual 6.0
        mockYplRoom.tax shouldEqual 2.0
        mockYplRoom.fee shouldEqual 3.0
        mockYplRoom.margin shouldEqual 4.0
        mockYplRoom.pf shouldEqual 5.0
        mockYplRoom.downliftAmount shouldEqual 6.0
        mockYplRoom.roomSellEx shouldEqual 5.0
        mockYplRoom.roomSellIn shouldEqual 15.0

        mockYplRoom.payAsYouGoAgxCommission shouldEqual 17.6
        mockYplRoom.prepaidAgxCommission shouldEqual 35.2

        mockYplRoom.freeTrialAgxCommission shouldEqual 52.8
        mockYplRoom.agpReferenceCommission shouldEqual 24.64
        mockYplRoom.agpInvoiceReferenceCommission shouldEqual 21.12
        mockYplRoom.priusValue shouldEqual 100.0
        mockYplRoom.sellEx shouldEqual 105.0
        mockYplRoom.sellIn shouldEqual 115.0
      }

      "eligible room price" in {
        val mockEligibleYplRoom = mockYplRoom
        mockEligibleYplRoom.eligibleRoomForApmSellEx shouldEqual 5.0
        mockEligibleYplRoom.eligibleRoomForApmSellIn shouldEqual 15.0
      }
    }

    "isStandaloneRoom" in {

      val room = aValidRoom.withRoomTypeId(1).build

      "return true when master room id is empty" in {
        room.isStandaloneRoom must_== true
      }

      "return true when master room id is 0" in {
        room.withMasterRoomId(0).isStandaloneRoom must_== true
      }

      "return true when master room id is the same as room type id" in {
        room.withMasterRoomId(1).isStandaloneRoom must_== true
      }

      "return false when master room id is different than room type id" in {
        room.withMasterRoomId(2).isStandaloneRoom must_== false
      }
    }
  }
}
