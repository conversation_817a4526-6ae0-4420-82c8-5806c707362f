package com.agoda.papi.ypl.models

import com.agoda.finance.tax.models.{TaxPrototypeInfo, TaxPrototypeLevel}
import org.specs2.mutable.SpecificationWithJUnit

class TaxInfoSpec extends SpecificationWithJUnit with YPLTestDataBuilders {

  "TaxInfo" should {

    "return correct result from toTaxPrototypeLevelsMap" in {

      // real case for indian graduated tax
      val taxPrototypeLevels = List(
        TaxPrototypeLevel(1, 0.0, 1000.0, 0.0),
        TaxPrototypeLevel(2, 1000.0, 2500.0, 12.0),
        TaxPrototypeLevel(3, 2500.0, 7500.0, 18.0),
        TaxPrototypeLevel(4, 7500.0, Double.MaxValue, 28.0),
      )
      val mockTax = aValidProtoTax.withTaxPrototypeInfo(Some(TaxPrototypeInfo(taxPrototypeLevels)))

      val TaxId = 1
      val TaxPrototypeId = 111

      val mockTaxInfo = aValidTaxInfo.withTaxes(Map((TaxId, TaxPrototypeId) -> mockTax))

      val taxPrototypeLevelsMap = mockTaxInfo.taxPrototypeLevelsMap

      taxPrototypeLevelsMap.size should_== (1)
      taxPrototypeLevelsMap.get(TaxPrototypeId).get should_== taxPrototypeLevels
    }

  }

}
