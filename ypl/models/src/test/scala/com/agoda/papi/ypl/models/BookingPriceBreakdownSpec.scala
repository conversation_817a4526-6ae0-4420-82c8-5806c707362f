package com.agoda.papi.ypl.models

import com.agoda.papi.enums.room.{ChargeType, RateType}
import com.agoda.papi.ypl.commission.{
  AgxCommissionHolder,
  CommissionDailyHolder,
  GrowthProgramCommissionHolder,
  MORPCommissionDailyHolder,
  OccupancyCommissionHolder,
  ProtobufCommissionHolder,
  WholesaleCommissionHolder,
}
import com.agoda.papi.ypl.models.enums.{BreakdownStep, GrowthProgramCommissionBreakdown, UspaPriceBreakdown}
import com.agoda.papi.ypl.models.pricing.proto.{
  AdditionalDispatchReason,
  BaseProtoOccupancy,
  BaseProtoPrice,
  BaseProtoTaxBreakdownV2,
  TaxAmount,
}
import com.agoda.papi.ypl.models.pricing.{BookingPriceBreakdown, PriceBreakdown}
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope
import com.agoda.papi.ypl.models.logger.UspaCampaignInfo

import java.util.Date

class BookingPriceBreakdownSpec extends SpecificationWithJUnit with YPLTestDataBuilders {
  "createBaseBreakdown" should {
    val emptyBookingBreakdown = BookingPriceBreakdown()

    "return empty BookingPriceBreakdown when it is not a booking request" in {
      val isBookingRequest = false
      val result =
        BookingPriceBreakdown(isBookingRequest).newBreakdown(ChargeType.Room, RateType.SellInclusive, "USD", 100.0)

      result should_== emptyBookingBreakdown
    }

    "return BookingPriceBreakdown with correct PriceBreakdown if it is booking request" in {
      val isBookingRequest = true
      val result =
        BookingPriceBreakdown(isBookingRequest).newBreakdown(ChargeType.Room, RateType.SellInclusive, "USD", 100.0)

      val priceBreakdown = List(PriceBreakdown("BaseStep", "USD", "Room", sellIn = Some(100.0)))
      result should_== BookingPriceBreakdown(
        isEnabled = true,
        breakdown = priceBreakdown,
        baseProtoPrice = BaseProtoPrice(),
        promotionDiscount = 0,
        channelDiscount = 0,
        downliftPercent = 0,
        isAPMDiscountStep = false,
        firedrill = 0,
        taxBreakdownMap = Map.empty,
        apmCommissionDiscountPercent = 0.0,
        isAPMCommissionDiscountStep = false,
        apmDiscountAmount = 0.0,
        gpCommissionBreakdown = GrowthProgramCommissionBreakdown.default,
        supplierFundedDiscountPercent = 0.0,
        uspaDiscountBreakdown = UspaPriceBreakdown.default,
        uspaCampaignInfo = UspaCampaignInfo.default,
      )
    }
  }

  "add new PriceBreakdown" should {
    "add price breakdown when booking is enabled and current breakdown step is defined" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBookingPriceBreakdown =
        bookingPriceBreakdown.addBreakdown(aValidPrice.copy(currentBreakdownStep = BreakdownStep.ChannelDiscount), false)

      bookingPriceBreakdown shouldNotEqual newBookingPriceBreakdown
    }

    "add price breakdown for supplier funded when booking is enabled and current breakdown step is defined" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBookingPriceBreakdown = bookingPriceBreakdown
        .addBreakdown(aValidPrice.copy(currentBreakdownStep = BreakdownStep.SupplierFundedDiscount), false)

      bookingPriceBreakdown shouldNotEqual newBookingPriceBreakdown
      newBookingPriceBreakdown.breakdown.last.discountAmount shouldEqual "Supplier Funded Discount 0.0"
    }

    "add price breakdown for uspa discount when booking is enabled and current breakdown step is defined when isEnableEnhanceBookingBreakdown = B" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBookingPriceBreakdown =
        bookingPriceBreakdown.addBreakdown(aValidPrice.copy(currentBreakdownStep = BreakdownStep.USPADiscount), true)

      bookingPriceBreakdown shouldNotEqual newBookingPriceBreakdown
      newBookingPriceBreakdown.breakdown.last.discountAmount shouldEqual "USPA: 0.0% (0.0) [Unknown, campaignId: 0]"

      val bookingPriceBreakdownWithUspaAdjustment = bookingPriceBreakdown.copy(
        uspaCampaignInfo = UspaCampaignInfo(
          uspaRateType = RateType.NetInclusive,
          uspaCampaignId = 1,
        ),
        uspaDiscountBreakdown = UspaPriceBreakdown(55.0, 10.0),
      )
      val newBookingPriceBreakdownWithUspaAdjustment = bookingPriceBreakdownWithUspaAdjustment
        .addBreakdown(aValidPrice.copy(currentBreakdownStep = BreakdownStep.USPADiscount), true)
      newBookingPriceBreakdownWithUspaAdjustment shouldNotEqual bookingPriceBreakdownWithUspaAdjustment
      newBookingPriceBreakdownWithUspaAdjustment.breakdown.last.discountAmount shouldEqual "USPA: 10.0% (55.0) [NetInclusive, campaignId: 1]"
    }

    "add price breakdown for uspa discount when booking is enabled and current breakdown step is defined when isEnableEnhanceBookingBreakdown = A" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBookingPriceBreakdown =
        bookingPriceBreakdown.addBreakdown(aValidPrice.copy(currentBreakdownStep = BreakdownStep.USPADiscount), false)

      bookingPriceBreakdown shouldNotEqual newBookingPriceBreakdown
      newBookingPriceBreakdown.breakdown.last.discountAmount shouldEqual "USPA Discount 0.0"

      val bookingPriceBreakdownWithUspaAdjustment = bookingPriceBreakdown.copy(
        uspaCampaignInfo = UspaCampaignInfo(
          uspaRateType = RateType.NetInclusive,
          uspaCampaignId = 1,
        ),
        uspaDiscountBreakdown = UspaPriceBreakdown(55.0, 10.0),
      )
      val newBookingPriceBreakdownWithUspaAdjustment = bookingPriceBreakdownWithUspaAdjustment
        .addBreakdown(aValidPrice.copy(currentBreakdownStep = BreakdownStep.USPADiscount), false)
      newBookingPriceBreakdownWithUspaAdjustment shouldNotEqual bookingPriceBreakdownWithUspaAdjustment
      newBookingPriceBreakdownWithUspaAdjustment.breakdown.last.discountAmount shouldEqual "USPA Discount 55.0"
    }

    "return empty price breakdown if is not booking request (when add with YPLPrice)" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(isEnabled = false, breakdown = Seq.empty)
      val newBookingPriceBreakdown = bookingPriceBreakdown.addBreakdown(aValidPrice, false)

      bookingPriceBreakdown.breakdown should_== newBookingPriceBreakdown.breakdown
    }

    "return empty price breakdown if current step is unknown (when add with YPLPrice)" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(isEnabled = true, breakdown = Seq.empty)
      val newBookingPriceBreakdown =
        bookingPriceBreakdown.addBreakdown(aValidPrice.copy(currentBreakdownStep = BreakdownStep.Unknown), false)

      bookingPriceBreakdown.breakdown should_== newBookingPriceBreakdown.breakdown
    }

    "return empty price breakdown if is not booking request (when add PriceBreakdown)" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(isEnabled = false, breakdown = Seq.empty)
      val newBookingPriceBreakdown = bookingPriceBreakdown.addBreakdown(aValidPriceBreakdown)

      bookingPriceBreakdown.breakdown should_== newBookingPriceBreakdown.breakdown
    }

    "add price breakdown with price breakdown object" in {
      val priceBreakdown = aValidPriceBreakdown.copy(
        stepDesc = BreakdownStep.ChannelDiscount.entryName,
        currency = "THB",
        chargeType = ChargeType.ExtraBed.entryName,
        sellEx = Some(100.55),
        sellIn = Some(120.75),
      )

      val breakdown = aValidBookingPriceBreakdown.copy(true, breakdown = Seq.empty)
      val result = breakdown.addBreakdown(priceBreakdown)

      result.breakdown.head should_== priceBreakdown
    }

    "add price breakdown when booking is enabled and AGP breakdown step is defined" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(
        isEnabled = true,
        firedrill = 29d,
        gpCommissionBreakdown = GrowthProgramCommissionBreakdown(
          originalRetailCC = 12d,
          agpInm = 7d,
          stackedAgxInm = 11d,
          stackedApmDiscount = 1d,
        ),
      )
      val newBookingPriceBreakdown =
        bookingPriceBreakdown.addBreakdown(aValidPrice.copy(currentBreakdownStep = BreakdownStep.AGP), false)

      val result = newBookingPriceBreakdown.breakdown
        .withFilter(_.stepDesc == BreakdownStep.AGP.entryName)
        .map(_.discountAmount)
        .headOption

      result should_== Some(
        "Total AGP Overwrite: 29.0% (NA), RetailCC: 12.0%, AGP INM: 7.0%, Stacked AGX INM: 11.0%, APM DISC: 1.0%")
    }
  }

  "add new promotion v2" should {
    "return empty if booking is disabled" in {
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = false)
      val newBreakdown = breakdown.addPromotionV2(Some(aValidPromotionEntry), aValidPromotionEntry, 100, 200, 200)

      breakdown.baseProtoPrice.promotionsV2 should_== newBreakdown.baseProtoPrice.promotionsV2
    }

    "return promotions if booking is enabled" in {
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBreakdown = breakdown.addPromotionV2(Some(aValidPromotionEntry), aValidPromotionEntry, 100, 200, 200)

      breakdown.baseProtoPrice.promotionsV2 should_!= newBreakdown.baseProtoPrice.promotionsV2
    }

    "return promotions with extra badge reason fields" in {
      val breakdown = aValidBookingPriceBreakdown
      val newPromotionEntry = aValidPromotionEntry.copy(id = 12345,
                                                        additionalDispatchReasons =
                                                          Set(AdditionalDispatchReason.TimeFenceExtended,
                                                              AdditionalDispatchReason.CustomerSegmentOverriding))
      val newBreakdown = breakdown.addPromotionV2(Some(aValidPromotionEntry), newPromotionEntry, 100, 200, 200)

      newBreakdown.baseProtoPrice.promotionsV2.last.newPromo.additionalDispatchReasons should_== Set(
        "TimeFenceExtended",
        "CustomerSegmentOverriding")
    }
  }
  "add surcharge" should {
    "return empty if booking is disabled" in {
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = false)
      val newBreakdown = breakdown.addSurcharge(aValidSurchargeEntry)

      breakdown.baseProtoPrice.surcharge should_== newBreakdown.baseProtoPrice.surcharge
    }

    "return surcharge if booking is enabled" in {
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBreakdown = breakdown.addSurcharge(aValidSurchargeEntry)

      breakdown.baseProtoPrice.surcharge should_!= newBreakdown.baseProtoPrice.surcharge
    }
  }

  "add TaxBreakdown" should {
    "update tax and fee correctly" in {
      val taxBreakdown = BaseProtoTaxBreakdownV2(BreakdownStep.PriceBreakdown, aValidTaxBreakdown)
      val newBreakdown =
        aValidBookingPriceBreakdown.addTaxV2(BreakdownStep.PriceBreakdown, List(aValidTaxBreakdown), true, true)

      newBreakdown.taxBreakdownMap.get((aValidTaxBreakdown.typeId, aValidTaxBreakdown.taxProtoTypeId)) should_== Some(
        taxBreakdown)
      newBreakdown.baseProtoPrice.isApplyNoProcessingFee should_== true
      newBreakdown.baseProtoPrice.isApplyTaxOnSellEx should_== true
    }

    "when GST Tax" in {
      val taxBreakdown = BaseProtoTaxBreakdownV2(BreakdownStep.PriceBreakdown, aValidTaxBreakdown)

      val gstTax = aValidTaxBreakdown.copy(amount = 5, percentage = 5)

      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(
        isEnabled = true,
        taxBreakdownMap = Map((aValidTaxBreakdown.typeId, aValidTaxBreakdown.taxProtoTypeId) -> taxBreakdown),
      )

      val actualTaxes = bookingPriceBreakdown.addTaxV2(BreakdownStep.ChannelDiscount, List(gstTax)).taxBreakdownMap
      val expectedTaxes = Map(
        (aValidTaxBreakdown.typeId, aValidTaxBreakdown.taxProtoTypeId) ->
          taxBreakdown.copy(taxAmount =
            taxBreakdown.taxAmount ++ List(TaxAmount(BreakdownStep.ChannelDiscount.entryName, 5, 5))))

      actualTaxes should_== expectedTaxes
    }

    "return empty taxes if not booking request" in {
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = false)
      val newBreakdown = breakdown.addTaxV2(BreakdownStep.BaseStep, List(aValidTaxBreakdown))

      breakdown should_== newBreakdown
    }

    "return empty taxes if input taxBreakdown is empty" in {
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBreakdown = breakdown.addTaxV2(BreakdownStep.BaseStep, List.empty, true, true)

      breakdown.taxBreakdownMap should_== newBreakdown.taxBreakdownMap
      newBreakdown.baseProtoPrice.isApplyNoProcessingFee should_== true
      newBreakdown.baseProtoPrice.isApplyTaxOnSellEx should_== true
    }
  }

  "add TotalTaxPercent" should {
    "add totalTax and hpTax when has hpPrice (NEP-25984-A)" in {
      val result =
        aValidBookingPriceBreakdown.addTotalTaxPercent(aValidDailyHpPercentTaxV2.copy(isCleanedUpHospitalityTax = false))
      result.baseProtoPrice.totalTaxPercent should_== 10d
    }
    "add totalTax and hpTax when has hpPrice (NEP-25984-B)" in {
      val result = aValidBookingPriceBreakdown.addTotalTaxPercent(aValidDailyHpPercentTaxV2)
      result.baseProtoPrice.totalTaxPercent should_== 0d
    }

    "add totalTax when does not have hpPrice (NEP-25984-A)" in {
      val result =
        aValidBookingPriceBreakdown.addTotalTaxPercent(aValidDailyTaxV2.copy(isCleanedUpHospitalityTax = false))
      result.baseProtoPrice.totalTaxPercent should_== 0d
    }
    "add totalTax when does not have hpPrice (NEP-25984-B)" in {
      val result = aValidBookingPriceBreakdown.addTotalTaxPercent(aValidDailyTaxV2)
      result.baseProtoPrice.totalTaxPercent should_== 10d
    }
  }

  "getCurrentBreakdownStep" should {
    "return ChannelAndPromotionsDiscount when the latest step is PriceBreakdown, isChannelDiscountStep is true, and contians both channel and promotions discount" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(promotionDiscount = 10, channelDiscount = 10)
      val result =
        BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, true, BreakdownStep.PriceBreakdown)

      result should_== BreakdownStep.ChannelAndPromotionDiscount
    }

    "return ChannelDiscount when the latest step is PriceBreakdown, isChannelDiscountStep is true, and contians only channel discount" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(channelDiscount = 10)
      val result =
        BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, true, BreakdownStep.PriceBreakdown)

      result should_== BreakdownStep.ChannelDiscount
    }

    "return Promotions when the latest step is PriceBreakdown, isChannelDiscountStep is true, and contians only promo discount" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(promotionDiscount = 10)
      val result =
        BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, true, BreakdownStep.PriceBreakdown)

      result should_== BreakdownStep.Promotions
    }

    "return Unknown when the latest step is PriceBreakdown, isChannelDiscountStep is true, and no promo and channel discount" in {
      val result =
        BookingPriceBreakdown.getCurrentBreakdownStep(aValidBookingPriceBreakdown, true, BreakdownStep.PriceBreakdown)

      result should_== BreakdownStep.Unknown
    }

    "return ChannelDiscount when isChannelDiscountStep is true, latest step is not PriceBreakdown and has only channel discount" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(channelDiscount = 10)
      val result = BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, true, BreakdownStep.Promotions)

      result should_== BreakdownStep.ChannelDiscount
    }

    "return Promotions when isChannelDiscountStep is true, latest step is not PriceBreakdown and has only promo discount" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(promotionDiscount = 10)
      val result = BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, true, BreakdownStep.Promotions)

      result should_== BreakdownStep.Promotions
    }

    "return PriceBreakdown when isChannelDiscountStep is true, latest step is not PriceBreakdown and has no discount" in {
      val result =
        BookingPriceBreakdown.getCurrentBreakdownStep(aValidBookingPriceBreakdown, true, BreakdownStep.Promotions)

      result should_== BreakdownStep.PriceBreakdown
    }

    "return APMDiscount when isChannelDiscountStep is false, latest step is not BaseStep and isAPMStep is true" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(isAPMDiscountStep = true)
      val result = BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, false, BreakdownStep.Promotions)

      result should_== BreakdownStep.APMDiscount
    }

    "return APMCommissionDiscount when isChannelDiscountStep is false, latest step is not BaseStep and APMCommissionDiscount is true" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(isAPMCommissionDiscountStep = true)
      val result = BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, false, BreakdownStep.Promotions)

      result should_== BreakdownStep.APMCommissionDiscount
    }

    "return TaxProtypeLevelCorrection when isChannelDiscountStep is false, latest step is not BaseStep and isAPMStep is false" in {
      val result =
        BookingPriceBreakdown.getCurrentBreakdownStep(aValidBookingPriceBreakdown, false, BreakdownStep.Promotions)

      result should_== BreakdownStep.TaxProtypeLevelCorrection
    }

    "return Promotions when it isChannelDiscountStep is false, latest step is BaseStep, and has promo discount" in {
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(promotionDiscount = 10)
      val result = BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, false, BreakdownStep.BaseStep)

      result should_== BreakdownStep.Promotions
    }

    "return Promotions when it isChannelDiscountStep is false, latest step is BaseStep, and has no discount" in {
      val result =
        BookingPriceBreakdown.getCurrentBreakdownStep(aValidBookingPriceBreakdown, false, BreakdownStep.BaseStep)

      result should_== BreakdownStep.PriceBreakdown
    }
  }
  "update occupancy and room quantity" should {
    "update correctly when is not booking request" in {
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = false)
      val baseProtoOccupancy =
        BaseProtoOccupancy(aValidOccInfo.adults, aValidOccInfo.children, 0, aValidOccInfo.childrenAges.map(Some(_)))
      val newBreakdown = breakdown.updateOccupancyAndRoomQuantities(baseProtoOccupancy, Some(0), 0)

      breakdown.baseProtoPrice.stayOccupancy should_== newBreakdown.baseProtoPrice.stayOccupancy
    }

    "update correctly when is booking request" in {
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val baseProtoOccupancy =
        BaseProtoOccupancy(aValidOccInfo.adults, aValidOccInfo.children, 0, aValidOccInfo.childrenAges.map(Some(_)))
      val newBreakdown = breakdown.updateOccupancyAndRoomQuantities(baseProtoOccupancy, Some(0), 0)

      breakdown.baseProtoPrice.stayOccupancy should_== newBreakdown.baseProtoPrice.stayOccupancy
    }
  }
  "update timestamp" should {
    "update correctly when booking request" in {
      val changeTimestamp = new Date()
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBreakdown = breakdown.updateTimestamp(Some(changeTimestamp))

      newBreakdown.baseProtoPrice.protobufChangeTimestamp should_== Some(changeTimestamp)
    }

    "do not update correctly when is not booking request" in {
      val changeTimestamp = new Date()
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = false)
      val newBreakdown = breakdown.updateTimestamp(Some(changeTimestamp))

      newBreakdown.baseProtoPrice.protobufChangeTimestamp should_== None
    }
  }
  "updateBaseProto" should {
    trait UpdateBaseProtoTestScope extends Scope {
      var executionCounts = 0
      def updateBaseProto(): BaseProtoPrice = {
        executionCounts += 1
        BaseProtoPrice()
      }
    }
    "not evaluate baseProto if not enabled" in new UpdateBaseProtoTestScope {
      aValidBookingPriceBreakdown.copy(isEnabled = false).updateBaseProto(updateBaseProto())

      executionCounts should_== 0
    }

    "evaluate baseProto if enabled" in new UpdateBaseProtoTestScope {
      aValidBookingPriceBreakdown.copy(isEnabled = true).updateBaseProto(updateBaseProto())

      executionCounts should_== 1
    }

  }

  "update discount" should {
    "return correct discount from input" in {
      val priceEntry = aValidPriceEntry.copy(promoDiscount = 15.05, channelDiscount = 20.99)

      val breakdownWithDiscount = aValidBookingPriceBreakdown
        .copy(isEnabled = true)
        .updateDiscount(priceEntry.promoDiscount, priceEntry.channelDiscount)
      breakdownWithDiscount.promotionDiscount should_== priceEntry.promoDiscount
      breakdownWithDiscount.channelDiscount should_== priceEntry.channelDiscount
    }

    "return default discount (0.0) when not booking request" in {
      val priceEntry = aValidPriceEntry.copy(promoDiscount = 15.05, channelDiscount = 20.99)

      val breakdownWithDiscount = aValidBookingPriceBreakdown
        .copy(isEnabled = false)
        .updateDiscount(priceEntry.promoDiscount, priceEntry.channelDiscount)
      breakdownWithDiscount.promotionDiscount should_== 0
      breakdownWithDiscount.channelDiscount should_== 0
    }
  }

  "update baseProtoPrice base on PriceEntry" should {
    "return correct value" in {
      val priceEntry = aValidPriceEntry
      val protoPrice = aValidBookingPriceBreakdown.baseProtoPrice
      val newProtoPrice = protoPrice.update(priceEntry,
                                            aValidRoomEntry,
                                            true,
                                            Some(aValidAgodaAgencyFeatures),
                                            Some(aValidCommissionDailyHolder),
                                            0.0)

      protoPrice should_!= newProtoPrice
    }
  }

  "update supplierFundedDiscount from price Entry" should {
    "return correct value" in {
      val priceEntry = aValidPriceEntry.copy(supplierFundedDiscountAmount = Some(20.0))
      val protoPrice = aValidBookingPriceBreakdown.baseProtoPrice
      val newProtoPrice = protoPrice.update(priceEntry,
                                            aValidRoomEntry,
                                            true,
                                            Some(aValidAgodaAgencyFeatures),
                                            Some(aValidCommissionDailyHolder),
                                            0.0)

      protoPrice should_!= newProtoPrice
    }
  }

  "update maxUspaPercent from price Entry" should {
    "return correct value" in {
      val priceEntry = aValidPriceEntry.copy(maxUspaDiscount = 10.0)
      val protoPrice = aValidBookingPriceBreakdown.baseProtoPrice
      val newProtoPrice = protoPrice.update(priceEntry,
                                            aValidRoomEntry,
                                            true,
                                            Some(aValidAgodaAgencyFeatures),
                                            Some(aValidCommissionDailyHolder),
                                            0.0)

      protoPrice should_!= newProtoPrice
    }
  }

  "update baseProtoPrice base on commissionHolder" should {
    "return correct value" in {
      val commissionDailyHolder = CommissionDailyHolder(
        protobufCommissionHolder = ProtobufCommissionHolder(1, 2),
        agxCommissionHolder = AgxCommissionHolder(3, 4, 5, true, true),
        wholesaleCommissionHolder = WholesaleCommissionHolder(true, 6),
        morpCommissionDailyHolder = MORPCommissionDailyHolder.default,
        occupancyCommissionHolder = OccupancyCommissionHolder.default,
        growthProgramCommissionHolder = GrowthProgramCommissionHolder.default,
      )

      val priceEntry = aValidPriceEntry
      val protoPrice = aValidBookingPriceBreakdown.baseProtoPrice
      val newProtoPrice = protoPrice.update(priceEntry,
                                            aValidRoomEntry,
                                            true,
                                            Some(aValidAgodaAgencyFeatures),
                                            Some(commissionDailyHolder),
                                            0.0)

      protoPrice should_!= newProtoPrice

      // check commission
      newProtoPrice.loadedCommission.commissionValue should_== commissionDailyHolder.protobufCommissionHolder.value
      newProtoPrice.loadedCommission.contractedCommission should_== commissionDailyHolder.protobufCommissionHolder.contractedCommission
      newProtoPrice.wholesaleMetadata.campaignCommissionOpt should beSome(
        commissionDailyHolder.wholesaleCommissionHolder.campaignCommissionOpt)
      newProtoPrice.agxCommission.payAsYouGoCommission should_== commissionDailyHolder.agxCommissionHolder.payAsYouGoCommission
      newProtoPrice.agxCommission.prepaidCommission should_== commissionDailyHolder.agxCommissionHolder.prepaidCommission
      newProtoPrice.agxCommission.freeTrialCommission should_== commissionDailyHolder.agxCommissionHolder.freeTrialCommission
    }
  }

  "addBreakdown" should {
    "not throw exception" in {
      val Nan = Double.NaN
      val step = BreakdownStep.Soybean
      val bookingPriceBreakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBookingPriceBreakdown = bookingPriceBreakdown.addBreakdown(
        aValidPrice.copy(
          currentBreakdownStep = step,
          downliftPercent = Nan,
        ),
        false,
      )
      val breakdown = newBookingPriceBreakdown.breakdown.filter(_.stepDesc == step.entryName).head
      // jre8: java.lang.NumberFormatException
      // jre17: java.lang.NumberFormatException: Character N is neither a decimal digit number, decimal point, nor "e" notation exponential mark.
      breakdown.discountAmount must startWith("Error: java.lang.NumberFormatException")
    }
  }

  "update hash" should {
    "update correctly when enable" in {
      val protobufHash = "hash"
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBreakdown = breakdown.updateHash(Some(protobufHash))

      newBreakdown.baseProtoPrice.protobufHash should_== Some(protobufHash)
    }

    "update hash correctly when not enable" in {
      val protobufHash = "hash"
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = false)
      val newBreakdown = breakdown.updateHash(Some(protobufHash))

      newBreakdown.baseProtoPrice.protobufHash should_== None
    }
  }

  "update Uspa campaignInfo" should {
    "update Uspa campaignInfo correctly when enable" in {
      val uspaCampaignInfo = UspaCampaignInfo(RateType.NetInclusive, 2, 15, 10)
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = true)
      val newBreakdown = breakdown.updateUspaCampaignInfo(uspaCampaignInfo)

      newBreakdown.uspaCampaignInfo should_== uspaCampaignInfo
    }

    "update Uspa campaignInfo correctly when not enable" in {
      val uspaCampaignInfo = UspaCampaignInfo(RateType.NetInclusive, 2, 15, 10)
      val breakdown = aValidBookingPriceBreakdown.copy(isEnabled = false)
      val newBreakdown = breakdown.updateUspaCampaignInfo(uspaCampaignInfo)

      newBreakdown.uspaCampaignInfo.maxUspaDiscountPercent should_!= uspaCampaignInfo.maxUspaDiscountPercent
      newBreakdown.uspaCampaignInfo.uspaRateType should_!= uspaCampaignInfo.uspaRateType
      newBreakdown.uspaCampaignInfo.uspaProgramId should_!= uspaCampaignInfo.uspaProgramId
      newBreakdown.uspaCampaignInfo.beatRatePercent should_!= uspaCampaignInfo.beatRatePercent
    }
  }
}
