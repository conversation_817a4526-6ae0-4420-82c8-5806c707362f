package com.agoda.papi.ypl.occupancy.builder

import com.agoda.papi.ypl.occupancy.models.OccTypeDef.RoomNumber
import com.agoda.papi.ypl.occupancy.models.{PerPersonAllocation, RoomAllocation}

case class RoomAllocationBuilder(build: RoomAllocation) extends Builder[RoomAllocation] {
  def withPerRoomAllocations(allocations: Map[PerPersonAllocation, Set[RoomNumber]]): B =
    build.copy(allocations = allocations)

  type B = RoomAllocation
}
