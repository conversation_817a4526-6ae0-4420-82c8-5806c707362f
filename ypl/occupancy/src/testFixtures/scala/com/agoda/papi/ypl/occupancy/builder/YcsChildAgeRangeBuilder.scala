package com.agoda.papi.ypl.occupancy.builder

import com.agoda.papi.ypl.occupancy.models.YcsChildAgeRange

case class YcsChildAgeRangeBuilder(build: YcsChildAgeRange) extends Builder[YcsChildAgeRange] {

  def withChildAgeRangeId(childAgeRangeId: Int): B = build.copy(childAgeRangeId = childAgeRangeId)
  def withAgeFrom(ageFrom: Int): B = build.copy(ageFrom = ageFrom)
  def withAgeTo(ageTo: Int): B = build.copy(ageTo = ageTo)
  def withIsRequiredExtraBed(isRequiredExtraBed: Boolean): B = build.copy(isRequiredExtraBed = isRequiredExtraBed)

  type B = YcsChildAgeRange
}
