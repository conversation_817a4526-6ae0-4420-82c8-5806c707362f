package com.agoda.papi.ypl.occupancy.builder

import com.agoda.papi.enums.hotel.HotelChildRateMode
import com.agoda.papi.ypl.occupancy.models.{ConvertedOccupancyForPropertySetting, ExtraPersonCharge, YcsChildAgeRange}

case class ConvertedOccupancyForPropertySettingBuilder(build: ConvertedOccupancyForPropertySetting)
  extends Builder[ConvertedOccupancyForPropertySetting] {

  def withIsChildRateEnabled(isChildRateEnabled: Boolean): B = build.copy(isChildRateEnabled = isChildRateEnabled)
  def withIsExtraBedEnabled(isExtraBedEnabled: Boolean): B = build.copy(isExtraBedEnabled = isExtraBedEnabled)
  def withChildRateMode(childRateMode: HotelChildRateMode): B = build.copy(childRateMode = childRateMode)
  def withChildAgeRanges(childAgeRanges: List[YcsChildAgeRange]): B = build.copy(childAgeRanges = childAgeRanges)
  def withExtraPersonCharges(extraPersonCharges: List[ExtraPersonCharge]): B =
    build.copy(extraPersonCharges = extraPersonCharges)

  type B = ConvertedOccupancyForPropertySetting
}
