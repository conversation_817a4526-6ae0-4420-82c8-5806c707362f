package com.agoda.papi.ypl.occupancy.builder

import com.agoda.papi.ypl.occupancy.models.{OccFilter, OccRequest, OccSearch}

case class OccRequestBuilder(build: OccRequest) extends Builder[OccRequest] {
  def withIsAllOcc(isAllOcc: Boolean): B = build.copy(isAllOcc = isAllOcc)
  def withOccFilter(occFilter: Option[OccFilter]): B = build.copy(occFilter = occFilter)
  def withOccSearch(occSearch: Option[OccSearch]): B = build.copy(occSearch = occSearch)

  type B = OccRequest
}
