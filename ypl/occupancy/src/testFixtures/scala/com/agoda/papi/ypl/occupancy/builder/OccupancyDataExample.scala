package com.agoda.papi.ypl.occupancy.builder

import com.agoda.papi.enums.hotel.{HotelChildRateMode, PerPersonChargeType}
import com.agoda.papi.enums.room.{PricingChildRateType, RateType}
import com.agoda.papi.ypl.occupancy.models._

trait OccupancyDataExample {

  val aValidYcsChildAgeRange = YcsChildAgeRange(
    childAgeRangeId = 1,
    ageFrom = 0,
    ageTo = 2,
    isRequiredExtraBed = true,
  )

  val aValidExtraPersonCharge = ExtraPersonCharge(
    isAdult = false,
    childAgeRangeId = None,
    perPersonChargeType = PerPersonChargeType.ExistingBed,
    pricingChildRateType = PricingChildRateType.Free,
    price = 0.0,
    benefitGroup = None,
  )

  val aValidConvertedOccupancyForPropertySetting = ConvertedOccupancyForPropertySetting(
    isChildRateEnabled = true,
    isExtraBedEnabled = true,
    childRateMode = HotelChildRateMode.UniversalPerProperty,
    childAgeRanges = List(aValidYcsChildAgeRange),
    extraPersonCharges = List(aValidExtraPersonCharge),
    currency = "USD",
    rateLoadType = RateType.NetExclusive,
  )

  val aValidConvertedOccupancyForRoomSetting = ConvertedOccupancyForRoomSetting(
    maxGuestOnExistingBed = 1,
    maxAdditionalGuest = 1,
    maxExtraBed = 1,
    maxAdults = 1,
    maxChildren = 1,
  )

  val aValidOccFilter = OccFilter()

  val aValidOccSearch = new OccSearch(1, Nil, 1)

  val aValidOccRequest = OccRequest(
    isAllOcc = false,
    occFilter = None,
    occSearch = Some(aValidOccSearch),
  )

  val aValidOccStateInput = OccStateInput(
    occRequest = aValidOccRequest,
    propertyOccupancySetting = aValidConvertedOccupancyForPropertySetting,
    roomOccupancySetting = aValidConvertedOccupancyForRoomSetting,
    hasExtraBedPriceGreaterThanZero = false,
    shouldBeKeptForFlexibleMultiRoom = false,
  )

  val aValidPerRoomAllocation = PerRoomAllocation(adultOnExistingBed = 1, specialPersons = Seq.empty)

  val aValidPerson = PersonWithAge(None)

  val aValidPerPersonAllocation = PerPersonAllocation(Seq(aValidPerson))

  val aValidOccStateOutput = OccStateOutput(
    perRoomAllocations = List(aValidPerRoomAllocation),
    searchRoomAllocation = RoomAllocation(Map(aValidPerPersonAllocation -> Set(1))),
  )

  val aValidOccState = OccState(
    input = aValidOccStateInput,
    output = aValidOccStateOutput,
  )

  val aValidRoomAllocation = new RoomAllocation(Map(PerPersonAllocation(Seq(PersonWithAge.Adult)) -> Set(1)))

}
