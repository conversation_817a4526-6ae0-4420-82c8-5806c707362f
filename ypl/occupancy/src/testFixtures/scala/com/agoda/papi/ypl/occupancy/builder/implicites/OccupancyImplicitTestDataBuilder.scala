package com.agoda.papi.ypl.occupancy.builder.implicites

import com.agoda.papi.ypl.occupancy.builder._
import com.agoda.papi.ypl.occupancy.models._

trait OccupancyImplicitTestDataBuilder {
  implicit def toConvertedOccupancyForPropertySettingBuilder(
    build: ConvertedOccupancyForPropertySetting): ConvertedOccupancyForPropertySettingBuilder =
    ConvertedOccupancyForPropertySettingBuilder(build)
  implicit def toConvertedOccupancyForRoomSettingBuilder(
    build: ConvertedOccupancyForRoomSetting): ConvertedOccupancyForRoomSettingBuilder =
    ConvertedOccupancyForRoomSettingBuilder(build)
  implicit def toExtraPersonChargeBuilder(build: ExtraPersonCharge): ExtraPersonChargeBuilder =
    ExtraPersonChargeBuilder(build)
  implicit def toOccFilterBuilder(build: OccFilter): OccFilterBuilder = OccFilterBuilder(build)
  implicit def toOccRequestBuilder(build: OccRequest): OccRequestBuilder = OccRequestBuilder(build)
  implicit def toOccSearchBuilder(build: OccSearch): OccSearchBuilder = OccSearchBuilder(build)
  implicit def toOccStateBuilder(build: OccState): OccStateBuilder = OccStateBuilder(build)
  implicit def toOccStateInputBuilder(build: OccStateInput): OccStateInputBuilder = OccStateInputBuilder(build)
  implicit def toOccStateOutputBuilder(build: OccStateOutput): OccStateOutputBuilder = OccStateOutputBuilder(build)
  implicit def toPerRoomAllocationBuilder(build: PerRoomAllocation): PerRoomAllocationBuilder =
    PerRoomAllocationBuilder(build)
  implicit def toRoomAllocationBuilder(build: RoomAllocation): RoomAllocationBuilder = RoomAllocationBuilder(build)
  implicit def toYcsChildAgeRangeBuilder(build: YcsChildAgeRange): YcsChildAgeRangeBuilder =
    YcsChildAgeRangeBuilder(build)

}
