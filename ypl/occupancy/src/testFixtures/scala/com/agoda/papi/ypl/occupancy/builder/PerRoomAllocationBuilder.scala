package com.agoda.papi.ypl.occupancy.builder

import com.agoda.papi.ypl.occupancy.models.{PerRoomAllocation, SpecialPerson}

// todo: add child with age and type
case class PerRoomAllocationBuilder(build: PerRoomAllocation) extends Builder[PerRoomAllocation] {
  def withAdultOnExistingBed(adult: Int): B = build.copy(adultOnExistingBed = adult)
  def withSpecialPersons(specialPersons: Seq[SpecialPerson]): B = build.copy(specialPersons = specialPersons)

  type B = PerRoomAllocation
}
