package com.agoda.papi.ypl.occupancy.builder

import com.agoda.papi.enums.hotel.{BenefitGroup, PerPersonChargeType}
import com.agoda.papi.enums.room.PricingChildRateType
import com.agoda.papi.ypl.occupancy.models.ExtraPersonCharge

case class ExtraPersonChargeBuilder(build: ExtraPersonCharge) extends Builder[ExtraPersonCharge] {

  def withIsAdult(isAdult: Boolean): B = build.copy(isAdult = isAdult)
  def withChildAgeRangeId(childAgeRangeId: Option[Int]): B = build.copy(childAgeRangeId = childAgeRangeId)
  def withPerPersonChargeType(perPersonChargeType: PerPersonChargeType): B =
    build.copy(perPersonChargeType = perPersonChargeType)
  def withPricingChildRateType(pricingChildRateType: PricingChildRateType): B =
    build.copy(pricingChildRateType = pricingChildRateType)
  def withPrice(price: Double): B = build.copy(price = price)
  def withBenefitGroupId(benefitGroup: Option[BenefitGroup]): B = build.copy(benefitGroup = benefitGroup)

  type B = ExtraPersonCharge
}
