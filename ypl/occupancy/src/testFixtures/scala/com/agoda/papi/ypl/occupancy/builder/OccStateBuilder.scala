package com.agoda.papi.ypl.occupancy.builder

import com.agoda.papi.ypl.occupancy.models._

case class OccStateBuilder(build: OccState) extends Builder[OccState] {
  def withInput(input: OccStateInput): B = build.copy(input = input)
  def withOutput(output: OccStateOutput): B = build.copy(output = output)

  type B = OccState
}
case class OccStateInputBuilder(build: OccStateInput) extends Builder[OccStateInput] {
  def withRequest(occRequest: OccRequest): B = build.copy(occRequest = occRequest)
  def withPropertyOccupancySetting(propertyOccupancySetting: ConvertedOccupancyForPropertySetting): B =
    build.copy(propertyOccupancySetting = propertyOccupancySetting)
  def withRoomOccupancySetting(roomOccupancySetting: ConvertedOccupancyForRoomSetting): B =
    build.copy(roomOccupancySetting = roomOccupancySetting)
  def withHasExtraBedPriceGreaterThanZero(hasExtraBedPriceGreaterThanZero: Boolean): B =
    build.copy(hasExtraBedPriceGreaterThanZero = hasExtraBedPriceGreaterThanZero)

  type B = OccStateInput
}

case class OccStateOutputBuilder(build: OccStateOutput) extends Builder[OccStateOutput] {
  def withPerRoomAllocations(perRoomAllocations: List[PerRoomAllocation]): B =
    build.copy(perRoomAllocations = perRoomAllocations)
  def withSearchRoomAllocations(searchRoomAllocations: Option[RoomAllocation]): B =
    build.copy(searchRoomAllocation = searchRoomAllocations)

  type B = OccStateOutput
}
