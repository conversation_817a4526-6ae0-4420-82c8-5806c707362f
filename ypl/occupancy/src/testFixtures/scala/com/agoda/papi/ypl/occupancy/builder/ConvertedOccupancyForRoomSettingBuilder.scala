package com.agoda.papi.ypl.occupancy.builder

import com.agoda.papi.ypl.occupancy.models.ConvertedOccupancyForRoomSetting

case class ConvertedOccupancyForRoomSettingBuilder(build: ConvertedOccupancyForRoomSetting)
  extends Builder[ConvertedOccupancyForRoomSetting] {
  def withMaxGuestOnExistingBed(maxGuestOnExistingBed: Int): B =
    build.copy(maxGuestOnExistingBed = maxGuestOnExistingBed)
  def withMaxAdditionalGuest(maxAdditionalGuest: Int): B = build.copy(maxAdditionalGuest = maxAdditionalGuest)
  def withMaxExtraBed(maxExtraBed: Int): B = build.copy(maxExtraBed = maxExtraBed)
  def withMaxAdults(maxAdults: Int): B = build.copy(maxAdults = maxAdults)
  def withMaxChildren(maxChildren: Int): B = build.copy(maxChildren = maxChildren)

  type B = ConvertedOccupancyForRoomSetting
}
