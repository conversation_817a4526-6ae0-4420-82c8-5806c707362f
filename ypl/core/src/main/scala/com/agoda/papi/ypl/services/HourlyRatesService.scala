package com.agoda.papi.ypl.services

import api.routing.dsl.ExpirableRoutingDSL
import com.agoda.papi.ypl.models.{YplContext, YplHotelEntryModel, YplRoomEntry}
import com.agoda.papi.ypl.utils.DayUseUtils
import org.joda.time.DateTimeZone

trait HourlyRatesService[T <: YplContext] extends ExpirableRoutingDSL[T] {
  def filterPastHourlyAvailableSlots(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel]

  def calculateSingleTimeSlot(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel]

  def filterHourlyDurations(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel]

  def filter30MinsHourlySlots(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel]
}

trait HourlyRatesServiceImpl[T <: YplContext] extends HourlyRatesService[T] {

  val checkOutTimeBufferHours = 2
  override def filterPastHourlyAvailableSlots(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] = {
    val request = d.ctx.request
    val isHourlyRequest = request.isHourlyRequest
    val isDayUseHackyRoomInOvernightRequest = request.featureRequest.enableHourlySlotsForDayuseInOvernight

    d.data.map { hotel =>
      val updatedRooms = (isHourlyRequest, isDayUseHackyRoomInOvernightRequest) match {
        case (true, _) => processRooms(hotel, d).filter(_.hourlyAvailableSlots.nonEmpty)
        case (_, true) => processRooms(hotel, d)
        case (_, _) => hotel.rooms.map { room =>
            room.copy(hourlyAvailableSlots = Seq.empty)
          }
      }
      hotel.copy(rooms = updatedRooms)
    }
  }

  override def calculateSingleTimeSlot(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] = {
    val request = d.ctx.request
    if (request.isHourlyRequest && request.selectedCheckInTime.nonEmpty) {
      d.data.map { hotel =>
        val updatedRooms = hotel.rooms.map { room =>
          val selectedTimeSlot =
            DayUseUtils.selectSingleTimeSlot(room.hourlyAvailableSlots, request.selectedCheckInTime.get)
          selectedTimeSlot match {
            case Some(singleSlot) => room.copy(hourlyAvailableSlots = Seq(singleSlot))

            case _ => room
          }
        }
        hotel.copy(rooms = updatedRooms)
      }
    } else {
      d.data
    }
  }

  override def filterHourlyDurations(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] = {
    val request = d.ctx.request
    if (request.isHourlyRequest && request.hourlyDurationFilter.nonEmpty) {
      d.data.map { hotel =>
        val filteredRooms = hotel.rooms.filter { room =>
          val durations = room.hourlyAvailableSlots.map(_.duration.toHours.toInt).toSet
          durations.intersect(request.hourlyDurationFilter).nonEmpty
        }
        hotel.copy(rooms = filteredRooms)
      }
    } else {
      d.data
    }
  }

  override def filter30MinsHourlySlots(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] =
    d.data.map { hotel =>
      hotel.copy(
        rooms = hotel.rooms.map { room =>
          val slots = room.hourlyAvailableSlots
          if (slots.isEmpty || shouldInclude30MinsSlot(d.ctx)) {
            room
          } else {
            val filteredSlots = slots.filter(_.from.getMinuteOfHour == 0)
            room.copy(hourlyAvailableSlots = filteredSlots)
          }
        },
      )
    }

  private def shouldInclude30MinsSlot(ctx: T): Boolean = {
    val enable30MinsSlot = ctx.request.regulationFeatureEnabledSetting.isEnable30MinsHourlySlots &&
      ctx.request.featureRequest.enableThirtyMinsSlots
    enable30MinsSlot
  }

  private def processRooms(hotel: YplHotelEntryModel, d: Data[Option[YplHotelEntryModel]]): List[YplRoomEntry] = {
    val hotelTimeZone = DateTimeZone.forOffsetHoursMinutes(hotel.metaData.gmtOffset, hotel.metaData.gmtOffsetMinutes)
    // converts booking time to hotel time zone
    val bookingDateTime = d.ctx.request.bookingDate.withZone(hotelTimeZone)

    hotel.rooms.map { room =>
      val filteredHourlyAvailableSlots = DayUseUtils.filterSlotsOnCheckOut(
        room.hourlyAvailableSlots,
        d.ctx.request.checkIn.toLocalDateTime.toDateTime(hotelTimeZone),
        bookingDateTime)
      room.copy(hourlyAvailableSlots =
        DayUseUtils.filterPastMidnightSlots(filteredHourlyAvailableSlots,
                                            d.ctx.request.featureRequest.showPastMidnightSlots))
    }
  }
}
