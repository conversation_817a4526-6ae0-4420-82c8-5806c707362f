package com.agoda.papi.ypl.services.firedrill

import com.agoda.papi.enums.hotel.FireDrillContractType.{AGP, AGX, BedsPaid}
import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.ypl.commission.ApmCommissionUtils.YplRateFenceConverter
import com.agoda.papi.ypl.commission.apm.ApmCommissionDiscountServiceImpl
import com.agoda.papi.ypl.commission.apm.models.{ApmCommissionDiscountResult, ApmCommissionDiscountRoomParameters}
import com.agoda.papi.ypl.commission.growth.program._
import com.agoda.papi.ypl.measurement.FireDrillMeasurement.{AgxPaidMeasurement, BedsPaidAgpLiteMeasurement}
import com.agoda.papi.ypl.models.YplExperiments._
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.hotel.YplFireDrillContract
import com.agoda.papi.ypl.models.hotel.YplFireDrillContract.DEFAULT_LEGACY_CONTRACT_ID
import com.agoda.papi.ypl.pricing.{FireDrillCalculator, RequestRelatedValidation, YPLRoomWithGrowthProgramState}
import com.typesafe.scalalogging.LazyLogging
import models.consts.ABTest

trait GrowthProgramService {
  def applyGrowthProgram(hotel: YPLHotel,
                         hotelGmtOffset: GmtOffset,
                         dispatchChannels: YplDispatchChannels,
                         apmProgramId: Int,
                         isAgpLiteNegativeTest: Boolean)(implicit ctx: YplContext): YPLHotel
}

trait GrowthProgramServiceImpl
  extends GrowthProgramService
    with LazyLogging
    with RequestRelatedValidation
    with ApmCommissionDiscountServiceImpl {
  self: FireDrillCalculator =>

  override def applyGrowthProgram(hotel: YPLHotel,
                                  hotelGmtOffset: GmtOffset,
                                  dispatchChannels: YplDispatchChannels = YplDispatchChannels(Set.empty),
                                  apmProgramId: Int,
                                  isAgpLiteNegativeTest: Boolean = false)(implicit ctx: YplContext): YPLHotel = {
    val fencedAgx: Map[YplRateFence, FireDrillAgx] = ctx.request.agxCommissionAdjustmentFences
      .getCommissionsFor(ctx.request.allFences, hotel.id)
      .flatMap { case (fences, agx) => fences.map(fence => fence -> FireDrillAgx.apply(agx)) }(collection.breakOut)
    val roomsWithGrowthProgramCommissions = getRoomsWithGrowthProgramCommissions(hotel, hotelGmtOffset, dispatchChannels)
    val gpProgramDetails = roomsWithGrowthProgramCommissions
      .collectFirst { case room if room.gpCommission.isDefined => room }
      .flatMap(_.gpCommissionDetail)

    val isBedsPaidProgram = isBedsPaidProgramEligible(hotel)
    val isAgxPaidProgram = isAgxPaidProgramEligible(hotel)

    val agpHotel = gpProgramDetails
      .map { program =>
        if (isMerchantPaymentModel(hotel.paymentModel)) {
          if (program.isInvoice) applyAgpInvoice(hotel, roomsWithGrowthProgramCommissions)
          else {
            if (isAgpLiteNegativeTest && program.isAgpLite) hotel
            else {
              val hotelWithAgpPrice =
                calculateGpPricesForEligibleRooms(hotel, fencedAgx, roomsWithGrowthProgramCommissions)
              removeMORPRoomsIfNonAgpLite(hotelWithAgpPrice)
            }
          }
        } else {
          hotel
        }
      }
      .getOrElse(hotel)
    if (ctx.experimentContext.isUserB(ONBOARDING_AGX_PAID_PROGRAM) && isAgxPaidProgram) {
      applyAgxPaid(agpHotel)(ctx)
    } else {
      applyBedsPaid(agpHotel, apmProgramId, isBedsPaidProgram)(ctx)
    }
  }

  private[services] def isAgxPaidProgramEligible(hotel: YPLHotel): Boolean =
    hotel.rooms.exists(_.yplRoomEntry.commissionHolder.growthProgramCommissionHolder.getAgxPaidProgramDetails.isDefined)
  private[services] def isBedsPaidProgramEligible(hotel: YPLHotel): Boolean =
    hotel.rooms.exists(_.yplRoomEntry.commissionHolder.growthProgramCommissionHolder.getBedsProgramDetails.isDefined)

  private def removeMORPRoomsIfNonAgpLite(hotel: YPLHotel): YPLHotel = hotel.copy(rooms = hotel.rooms.filter(room =>
    room.yplRoomEntry.commissionHolder.agpCommissionHolder.isAgpLite || !room.roomFeatures.isAgodaAgency))

  private[services] def calculateGpPricesForEligibleRooms(
    hotel: YPLHotel,
    fencedAgx: Map[YplRateFence, FireDrillAgx],
    yplRoomWithGrowthProgramState: List[YPLRoomWithGrowthProgramState] = Nil,
  )(implicit ctx: YplContext): YPLHotel = {

    // Set post booking item breakdown fields
    val appliedAgpMarginRooms = applyMargin(yplRoomWithGrowthProgramState)
    // Update prices, marginPercentage
    val calculatedRooms = calculateGpPrices(appliedAgpMarginRooms, fencedAgx, hotel)
    hotel.copy(rooms = calculatedRooms)
  }

  private[services] def getRoomsWithGrowthProgramCommissions(
    hotel: YPLHotel,
    hotelGmtOffset: GmtOffset,
    dispatchChannels: YplDispatchChannels,
  )(implicit ctx: YplContext): List[YPLRoomWithGrowthProgramState] = {
    val roomsWithIndex: List[(YPLRoom, Int)] = hotel.rooms.zipWithIndex
    val apmCommissionDiscountResult = apmCommissionWrapper(roomsWithIndex, dispatchChannels, ctx, hotel, hotelGmtOffset)
    roomsWithIndex.map { case (room, index) =>
      val parameter = GrowthProgramCommissionParameter(
        supplierId = hotel.supplierId,
        countryId = hotel.countryId.toInt,
        baseChannelId = room.channel.baseChannelId,
        stackedChannelIds = room.channel.stackedChannelIds,
        roomInventoryType = room.yplRoomEntry.inventoryType,
        commissionHolder = Some(room.yplRoomEntry.commissionHolder),
        priceDates = room.prices.map(_.date),
        roomUid = room.uid,
        apmCommissionDiscountResult = apmCommissionDiscountResult.get(index),
        growthProgramFence = room.yplRoomEntry.commissionHolder.growthProgramCommissionHolder.growthProgramFence,
        isOnBoardGrowthProgramFencingRuleExp = ctx.experimentContext.isUserB(ABTest.ON_BOARD_GROWTH_PROGRAM_FENCING_RULE),
      )
      YPLRoomWithGrowthProgramState(room, parameter.toGrowthProgramCommission)
    }(collection.breakOut)
  }

  private[services] def applyAgpInvoice(
    hotel: YPLHotel,
    yplRoomWithGrowthProgramState: List[YPLRoomWithGrowthProgramState]): YPLHotel = {
    val withAgpMarginRooms = applyMargin(yplRoomWithGrowthProgramState)
    val withAgpPostBookingDataRooms =
      withAgpMarginRooms.map { case YPLRoomWithGrowthProgramState(yplRoom, gpCommissionDetails) =>
        if (gpCommissionDetails.exists(_.isEligibleGrowthProgram)) {
          yplRoom.copy(
            isFireDrill = true,
            commissionEquivalent = gpCommissionDetails.flatMap(_.getCommissionEquivalent),
            fireDrillContract = Some(
              YplFireDrillContract(DEFAULT_LEGACY_CONTRACT_ID, AGP, gpCommissionDetails.exists(_.isAgpPaid)),
            ), // hide here, can be removed if we do not need to populate field
          )
        } else yplRoom
      }
    hotel.copy(rooms = withAgpPostBookingDataRooms)
  }

  private[services] def applyMargin(
    roomsWithGrowthProgramState: List[YPLRoomWithGrowthProgramState],
  ): List[YPLRoomWithGrowthProgramState] = roomsWithGrowthProgramState.map { yplRoomWithProgram =>
    if (yplRoomWithProgram.gpCommission.exists(_.isEligibleGrowthProgram)) {
      val agpMargin = getAgpMargin(
        yplRoomWithProgram.gpCommission.flatMap(_.growthProgramCommissionDetail),
      )
      val isInvoicePaymentType = yplRoomWithProgram.gpCommission.exists(_.isGrowthProgramInvoice)
      if (isInvoicePaymentType) {
        // agpInvoiceMargin & agpMargin is INM, not CC+INM
        yplRoomWithProgram.copy(
          yplRoom = yplRoomWithProgram.yplRoom.copy(prices =
            yplRoomWithProgram.yplRoom.prices.map(price => price.copy(agpInvoiceMargin = agpMargin))),
        )
      } else {
        yplRoomWithProgram.copy(
          yplRoomWithProgram.yplRoom.copy(prices =
            yplRoomWithProgram.yplRoom.prices.map(price => price.copy(agpMargin = agpMargin))),
        )
      }
    } else yplRoomWithProgram
  }

  private[services] def getAgpMargin(commissionDetailOpt: Option[GrowthProgramCommissionDetail]): Double =
    commissionDetailOpt match {
      case Some(commissionDetail) =>
        if (commissionDetail.freeTrial) commissionDetail.rankingBoost
        else commissionDetail.commission
      case None => 0d
    }

  private[services] def apmCommissionWrapper(roomsWithIndex: List[(YPLRoom, Int)],
                                             dispatchChannels: YplDispatchChannels,
                                             ctx: YplContext,
                                             hotel: YPLHotel,
                                             hotelGmtOffset: GmtOffset): Map[Int, ApmCommissionDiscountResult] =
    calculateApmCommissionReductionResult(
      apmRoomParams = roomsWithIndex.map { case (room, index) =>
        index -> ApmCommissionDiscountRoomParameters(
          room.supplierId,
          room.marginPercentage,
          room.channel.toCompositeChannelIds,
          room.stayPackageType,
          room.resellExternalData.isEmpty,
          room.hourlyAvailableSlots.isEmpty,
          room.yplRoomEntry.commissionHolder.apmCommissionHolder,
          room.fences.map(_.toCommissionModel),
        )
      }.toMap,
      bookingDate = ctx.request.bookingDate,
      hotelGmtOffset = hotelGmtOffset,
      dispatchedMasterChannels = dispatchChannels.masterChannels.map(_.baseChannelId),
      hotelSupplierId = hotel.supplierId,
      hotelId = hotel.id,
      hotelCountryId = hotel.countryId,
      priceDates = hotel.rooms.flatMap(_.prices.map(_.date)),
      removeBedPaidExclusionFromApmExp = ctx.experimentContext.isUserB(REMOVE_BEDS_PAID_EXCLUSION_FROM_APM),
      skipApmPriceAdjustmentForResellExp = ctx.experimentContext.isUserB(SKIP_APM_PRICE_ADJUSTMENT_FOR_RESELL),
      enabledAiForBedNetwork = ctx.experimentContext.isUserB(APM_ENABLE_AI_FOR_BED_NETWORK),
      enableApmMultipleDiscount = ctx.experimentContext.isUserB(APM_MULTIPLE_DISCOUNT),
      excludeRateChannelFromApmExp = ctx.experimentContext.isUserB(APM_EXCLUDE_RATE_CHANNEL_IDS),
      isApmFixEndDate = ctx.experimentContext.isUserB(APM_FIX_END_DATE),
      enabledApmArpPlusProgram = ctx.experimentContext.isUserB(APM_ARP_PLUS_PROGRAM),
      requestFences = ctx.request.fences.map { case (channel, rateFences) =>
        channel.baseChannelId -> rateFences.map(_.toCommissionModel)
      },
    )

  private[services] def isBedsNetworkChannel(room: YPLRoom)(implicit ctx: YplContext): Boolean =
    if (ctx.experimentContext.isUserB(APM_INCLUDE_ADR_RATE_CHANNEL)) {
      Channel.BedbankChannelsWithAdr.exists(room.channel.contains)
    } else Channel.BedbankChannels.exists(room.channel.contains)

  private def modifyToBedsPaid(room: YPLRoom, isBedsPaidProgram: Boolean): YPLRoom = room.copy(fireDrillContract =
    Some(YplFireDrillContract(DEFAULT_LEGACY_CONTRACT_ID, BedsPaid, advancePay = isBedsPaidProgram)))

  private[services] def setRoomContractIfApplicableAGX(room: YPLRoom)(implicit ctx: YplContext): YPLRoom = {
    val resultRoom =
      room.copy(fireDrillContract = Some(YplFireDrillContract(DEFAULT_LEGACY_CONTRACT_ID, AGX, advancePay = true)))
    ctx.sendMeasurement(
      AgxPaidMeasurement(resultRoom.fireDrillContract),
    )
    resultRoom
  }

  private[services] def setRoomContractIfApplicable(room: YPLRoom,
                                                    apmProgramId: ApmProgramId,
                                                    isBedsPaidProgram: Boolean)(implicit ctx: YplContext): YPLRoom = {
    val isBedsAdvanceProgramId = ctx.request.apmSetting.exists(_.bedsAdvancedApmProgramIds.contains(apmProgramId))
    val isUseBedsPaidContractToApplyBedsPaid: Boolean =
      ctx.experimentContext.isUserB(USE_BEDS_PAID_CONTRACT_TO_APPLY_BEDS_PAID)
    val isAPMRoom = room.apmPriceAdjustmentSetting.nonEmpty

    val resultRoom =
      if (isUseBedsPaidContractToApplyBedsPaid) {
        if ((isBedsNetworkChannel(room) && isBedsPaidProgram) || isAPMRoom && isBedsAdvanceProgramId) {
          modifyToBedsPaid(room, isBedsPaidProgram)
        } else {
          room
        }
      } else {
        if (isBedsNetworkChannel(room) || isAPMRoom && isBedsAdvanceProgramId) {
          modifyToBedsPaid(room, isBedsPaidProgram)
        } else {
          room
        }
      }

    ctx.sendMeasurement(
      BedsPaidAgpLiteMeasurement(
        room.channel,
        resultRoom.fireDrillContract,
      ))

    resultRoom
  }

  /**
    * Apply BedsPaid logic to the hotel, including fencing logic
    *
    * Set fireDrillContract in YPLRoom to BedsPaid contract type if the room is in Beds Network channel
    * and the hotel has merchant payment model
    *
    * @param hotel        YPL hotel
    * @param paymentModel Payment model for current supplier
    * @param ctx          YPL context which set correct booking time zone
    * @return YPL hotel with modified YPL rooms
    */
  private[services] def applyBedsPaid(hotel: YPLHotel, apmProgramId: Int, isBedsPaidProgram: Boolean)(implicit
    ctx: YplContext): YPLHotel =
    if (isMerchantPaymentModel(hotel.paymentModel)) {
      hotel.copy(rooms = hotel.rooms.map(room => setRoomContractIfApplicable(room, apmProgramId, isBedsPaidProgram)(ctx)))
    } else {
      hotel
    }

  private[services] def applyAgxPaid(hotel: YPLHotel)(implicit ctx: YplContext): YPLHotel =
    hotel.copy(rooms = hotel.rooms.map(room => setRoomContractIfApplicableAGX(room)(ctx)))

  private def isMerchantPaymentModel(paymentModel: PaymentModel): Boolean = paymentModel == PaymentModel.Merchant
}
