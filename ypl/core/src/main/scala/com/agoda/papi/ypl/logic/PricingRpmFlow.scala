package com.agoda.papi.ypl.logic

import api.routing.dsl.ExpirableRoutingDSL
import com.agoda.papi.enums.request.FilterCriteria
import com.agoda.papi.enums.room._
import com.agoda.papi.ypl.commission.apm.{ApmCommissionDiscountServiceImpl, ApmPriceAdjustmentServiceImpl}
import com.agoda.papi.ypl.helper.OccupancyExpHelper
import com.agoda.papi.ypl.logic.ApmPricingFlow.isSupplierIdValid
import com.agoda.papi.ypl.models.YplExperiments.UNCONVENTIONAL_CHECKIN_CHECKOUT_REMOVE_COUNTRY_CHECK
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.models.consts.{ExternalDataFields, ExternalDataValues, Measurements}
import com.agoda.papi.ypl.models.enums.BreakdownStep
import com.agoda.papi.ypl.models.pricing.{YplChannelDiscountBreakdown, YplPrice, YplSupplierStats}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.occupancy.service.{
  OccupancyAllocationService,
  OccupancyConverterForChildFreeModelService,
  OccupancyConverterForChildRateModelService,
}
import com.agoda.papi.ypl.pricing.RateCategoryCalculator.RateCategoryCalculation
import com.agoda.papi.ypl.pricing.inventory.InventoryRoomFilterService
import com.agoda.papi.ypl.pricing.occupancy.{
  OccupancyAveragePriceService,
  OccupancyCalculationByChildPolicyLogic,
  OccupancySummaryService,
}
import com.agoda.papi.ypl.utils.YplImplicits._
import com.agoda.papi.ypl.pricing.supplier.{DMCUidGenerator, SupplierFundedDiscountLogic}
import com.agoda.papi.ypl.pricing.{ChannelDiscountLogic, LimitRoomsHeuristicService, PriceCalculator}
import com.agoda.papi.ypl.services._
import com.agoda.papi.ypl.services.occupancy._
import com.agoda.papi.ypl.settings.RoomStatsTrackingSettings
import com.agoda.papi.ypl.utils.Implicits._
import com.agoda.papi.ypl.utils.ModelLens
import com.agoda.utils.collection.SumImplicits._
import com.agoda.utils.flow.{ExperimentContext, PropertyContext}
import com.agoda.utils.lens.Implicits.LensImplicit
import com.typesafe.scalalogging.LazyLogging
import models.consts.ABTest
import models.flow.Variant
import org.joda.time.DateTime
import system.FlowSupport

import scala.collection.mutable.ListBuffer
import scala.concurrent.Future

object PricingRpmFlow {
  val MAX_ROOMTYPES_PER_GROUP = 4 // chosen via loadtests, don't attempt fine tuning, differences are negligible
  val TO_PERCENT: Double = 0.01
  val VALID_ROOM_STATUSES = Set[RatePlanStatus](RatePlanStatus.Requested,
                                                RatePlanStatus.Dispatched,
                                                RatePlanStatus.Helper,
  ) // Room Statuses To Be Passed Next
  val KR_COUNTRYID = 212
}

// scalastyle:off
/**
  * Created by ppattanapoon on 10/29/15.
  */
trait PricingRpmFlow[T <: YplContext]
  extends ExpirableRoutingDSL[T]
    with PricingCommon[T]
    with OccupancyCommon[T]
    with AgodaAgencyFlow[T]
    with AgencyPaymentFlow[T]
    with RateRepurposeService
    with ChannelDiscountLogic
    with SupplierFundedDiscountLogic
    with OriginManager
    with CidToOriginMapper
    with LimitRoomsHeuristicService
    with HybridAgencyFlow[T]
    with OverrideCxlCodeFlow[T]
    with EasyCancelService[T]
    with ApmPricingFlow[T]
    with ApmCommissionDiscountServiceImpl
    with ApmPriceAdjustmentServiceImpl
    with UmrahRoomService[T]
    with HourlyRatesService[T]
    with SellableRateCategoryByWhitelabelFlow[T]
    with CheapestFilterFlow[T]
    with GracePeriodFlow[T]
    with ExternalLoyaltyService[T]
    with AgpFireDrillFlow[T]
    with OccupancyAllocationService
    with OccupancySummaryService
    with OccupancyForAllOccService
    with OccupancyForSearchOccService
    with OccupancyCalculationByChildPolicyLogic
    with OccupancyAveragePriceService
    with ChildRateService
    with ExtraBedRateService
    with OccupancyConverterForChildFreeModelService
    with OccupancyConverterForChildRateModelService
    with OccupancySettingValidationService
    with OfferOccupancySummaryService
    with FeeWaiverFlow[T]
    with FilterRoomsByRegulatoryRequirementsFlow[T]
    with FilterOutByRateChannelAndPaymentModelService[T]
    with CancellationPolicyValidationService
    with FlowSupport[T]
    with FilterPaymentModelAgencyRoomsService[T]
    with IntercoRoomDuplicationService[T]
    with LazyLogging {

  import PricingRpmFlow._

  private val roomsExpansionFlow = Sync.simple("doFilterSellableRateCategoryByWhitelabel",
                                               doFilterSellableRateCategoryByWhitelabel) |>
    Sync.simple("filterRestrictedRatecategory", filterRestrictedRatecategory) |>
    // todo: if we can move checkAddAgodaAgency after calculateOccupancy so that we do not need to repeat
    Sync.simple("filterRatePlanCheckInCheckOut", filterRatePlanCheckInCheckOut) |>
    // Filter RoH rooms early to avoid expensive calculations
    Sync.simple("filterRoHRooms", filterRoHRooms) |>
    // to check if checkAddAgodaAgency is only duplicate room and adjust % commission, then occ will be the same
    Sync.simple("checkAddAgodaAgency", checkAddAgodaAgency) |>
    // calculateOccupancy do change the price from room allocation and child rate.
    Sync.simple("occupancyPricing", occupancyPricing) |>
    // heuristics1
    Sync.simple("heuristicRoomLimiter", heuristicRoomLimiter) |>
    // todo: if filter is not related to occ, we should move them before calculateOccupancy
    Sync.simple(name = "filterEscapesPackageIfDisabled", filterEscapesPackageIfDisabled) |>
    Sync.simple(name = "filterEscapesPackageIfNotApproved", filterEscapesPackageIfNotApproved) |>
    Sync.simple(name = "filterRichContentOfferIfDisabled", filterRichContentOfferIfDisabled) |>
    Sync.simple("filterPastHourlyAvailableSlots", filterPastHourlyAvailableSlots) |>
    Sync.simple("calculateSingleTimeSlot", calculateSingleTimeSlot) |>
    Sync.simple("filterHourlyDurations", filterHourlyDurations) |>
    Sync.simple("filter30MinsHourlySlots", filter30MinsHourlySlots) |>
    Sync.simple("filterCheapestPriceRooms", doFilterCheapestPriceRooms)

  private val rpmPricingFlow =
    // duplicate rooms using ccas response and assign price adjustment id
    Sync.simple("createIntercoRooms", createIntercoRooms) |>
      // This is only done for Pull Suppliers and discount is applied on NetIn and SellIn rateLoadedPrice
      Sync.simple("calculateSupplierFundedDiscounts", calculateSupplierFundedDiscount) |>
      Sync.simple("calculateChildRateCategory", calculateChildRateCategory) |>
      Sync.simple("addSimulatePromotionForRoom", addSimulatePromotionForRoom) |>
      //  add new rooms with promotions applied on protobuf entry
      //  normally promotion apply directly to room/rateCategory rate loaded type amount
      //  this is why price break down calculation can happen after this.
      Sync.simple("calculatePromotions", calculatePromotions) |>
      //  calculate retail prices (sell prices actually) to be used next => construct pricing.Room objects
      Sync.simple("calculatePricesRpm", calculatePricesRpm) |>
      //  apply channel discount inside protobuf entry
      //  channel discount apply in channel rate loaded type,
      //  this is why we need price break down calculation before for converting price amount in different rate loaded type
      Sync.simple("applyChannelDiscountAndPromotionToEntry", channelDiscount) |>
      //  1. calculate APS / APO rooms using retail pricing.Room
      //  2. add repurposed rooms on top of all calculated rooms
      Sync.simple("calculatePriceAfterChannelDiscountAndPromotionAndRateRepurpose", reCalculatePrices) |>
      // begin paymentModel Base Logic
      Sync.simple("postProcessAgodaAgency", postProcessAgodaAgencyRpm) |>
      Sync.simple("postProcessTrueAgency", postProcessTrueAgency) |>
      Sync.simple("applyAgencyNocc", applyAgencyNocc) |>
      Sync.simple("postProcessHybrid", postProcessHybrid) |>
      // end paymentModel Base Logic
      Sync.simple("postProcessOverrideCxlCode", postProcessOverrideCxlCode) |>
      Sync.simple("filterFinalRooms", filterFinalRooms) |>
      Sync.simple("processEasyCancel", processEasyCancel) |>
      Sync.simple("doUmrahLogic", doUmrahLogic) |>
      Sync.simple("processApmCommissionDiscount", processApmCommissionDiscount) |>
      Sync.simple("adjustCancellationPolicy", adjustCancellationPolicy) |>
      Sync.simple("calculateFeeWaiver", calculateFeeWaiver) |>
      Sync.simple("filterRoomsByRegulatoryRequirements", filterRoomsByRegulatoryRequirements) |>
      Sync.simple("doOverrideLoyaltyCxlCode", doOverrideLoyaltyCxlCode) |>
      Sync.simple("filterByFilterCriteria", filterByFilterCriteria)

  private val postRpmPricingFlow =
    // heuristics4
    Sync.simple("HeuristicCheapestRoomFilter", heuristicCheapestRoomFilter) |>
      Sync.simple("executeApm", executeApm) |>
      Sync.simple("executeAgp", executeAgp) |>
      Sync.simple("processApmExternalData", processApmExternalData) |>
      Sync.simple("addDmcData", d => addDmcData(d.data)(d.ctx)) |>
      // TODO: if we can move this filter earlier to right after [[postProcessHybrid]], under exp
      Sync.simple("filterOutBedsNetworkAgency", filterOutBedsNetworkAgency) |>
      Sync.simple(
        "setDefaultCxlCode",
        setDefaultCxlCode) |> // we need to set cxl code to No Refundable for empty cxl code before sending to Soybean
      Sync.simple("filterPaymentModelAgencyRooms", filterPaymentModelAgencyRooms)

  private val modelConverterFlow = Sync.simple("beforeModelConverter", logProtobufDataBeforeModelConverter) |>
    Sync.simple("afterModelConverter", doModelConverter)

  override private[logic] val roomStatsTrackingSettings = RoomStatsTrackingSettings()

  // This will log data to room visualization via rflow
  private def logProtobufDataBeforeModelConverter(d: Data[YplPricingData]): YplPricingData = d.data

  private def setDefaultCxlCode(d: Data[Option[YPLHotel]]): Option[YPLHotel] = d withMeta { implicit mx =>
    val result: Option[YPLHotel] = d.updateSeqSeq { _: YPLHotel => room: YPLRoom =>
      fixCancellationCode(room)
    }(flowLens |> ModelLens.yplHotelOptLens, ModelLens.yplHotelYPLRoomsLens)

    result
  }

  private[logic] def occupancyPricing(r: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] = r withMeta {
    implicit mx =>
      r.data.map { d =>
        implicit val ctx = r.ctx

        val isCountryJapan: Boolean = d.metaData.countryCode == "JP"
        val isSingleRoomNHA: Boolean = d.metaData.isSingleRoomNHA
        val isValidSupplier =
          OccupancyExpHelper.isValidSupplier(d.supplierId, r.ctx.isDirectConnect, r.ctx.isOccModelFPLOS)
        val validPreCondition =
          OccupancyExpHelper.isValidPreConditionForOccupancyExp(isValidSupplier, isCountryJapan, isSingleRoomNHA)
        val isApplyNewOccupancyLogic = validPreCondition && ctx.request.isApplyNewOccupancyLogic

        ctx.sendMetric(
          Measurements.offerBeforeNewOccupancyLogicCount,
          d.rooms.size,
          Map(
            Measurements.isYcs -> (d.supplierId == DMC.YCS).toString,
            Measurements.isAllocatedBTag -> ctx.request.isApplyNewOccupancyLogic.toString,
          ),
        )
        if (isApplyNewOccupancyLogic) calculateOccupancyNewLogic(d, isApplyNewOccupancyLogic)
        else {
          val hotelEntry = calculateOccupancy(d)
          val isFixJpB2bOccDisplay = ctx.experimentContext.determineVariant(PropertyContext(d.metaData.hotelId,
                                                                                            d.metaData.cityId,
                                                                                            d.metaData.countryId),
                                                                            ABTest.FIX_JP_B2B_OCC_DISPLAY) == Variant.B
          generateOfferOccupancyFromChildAgePolicy(
            hotelEntry,
            ctx.request.checkIn,
            ctx.request.flagInfo.isAllOcc,
            isApplyNewOccupancyLogic,
            isFixJpB2bOccDisplay,
            ctx.request.isAvailableCapacityIncludeChildren,
          )
        }
      }
  }

  private def executeApm(d: Data[Option[YplHotelWithEntry]]): Option[YplHotelWithEntry] = d.data.map { data =>
    val hotel = executeApm(data.hotel, data.entry.metaData, data.entry.dispatchChannels)(d.ctx)
    YplHotelWithEntry(hotel = hotel, entry = data.entry)
  }

  private def executeAgp(d: Data[Option[YplHotelWithEntry]]): Option[YplHotelWithEntry] = d.data.map { data =>
    val hotel = executeAgp(data.hotel, data.entry.metaData)(d.ctx)
    YplHotelWithEntry(hotel = hotel, entry = data.entry)
  }

  private def processApmExternalData(d: Data[Option[YplHotelWithEntry]]): Option[YPLHotel] = d.data.map { data =>
    val meta = data.entry.metaData
    processApmExternalData(data.hotel, meta.apmLeadingRoomAdjustmentIds, meta.apmConfigs)(d.ctx)
  }

  private def doModelConverter(d: Data[YplPricingData]): Option[YplHotelEntryModel] = modelsConverter(d.data, d.ctx)

  private[logic] def heuristicCheapestRoomFilter(d: Data[Option[YplHotelWithEntry]]): Option[YplHotelWithEntry] =
    d withMeta { implicit mx =>
      limitCheapestRoomHeuristic(d.data)(d.ctx)
    }

  private[logic] def addDmcData(yplHotelOpt: Option[YPLHotel])(ctx: T): Option[YPLHotel] = {
    val isDMCHardcodingRemovalEnabled = ctx.experimentContext.isUserB(YplExperiments.DMC_HARDCODING_REMOVAL)

    val isAddDMCDataEnabled = (supplierId: Int) =>
      if (isDMCHardcodingRemovalEnabled) ctx.request.supplierFeatures.features.get(supplierId).exists(_.addDMCData)
      else DMC.JTBWL == supplierId

    yplHotelOpt.map { yplHotel =>
      if (isAddDMCDataEnabled(yplHotel.supplierId)) addDmcData(yplHotel)
      else if (DMC.YCS.equals(yplHotel.supplierId)) addDmcDataForYcsHotel(yplHotel)
      else if (isSupplierIdValid(yplHotel.supplierId)) addDmcDataForApmHotel(yplHotel)
      else yplHotel
    }
  }

  def executeRoomExpansionFlow(data: YplPricingData, ctx: T): Option[YplHotelEntryModel] = {
    val yplHotelEntriesOpt = modelConverterFlow.process(Data(data, ctx, stepMeta(modelConverterFlow)))
    val yplHotelEntriesExpandedOpt: Option[YplHotelEntryModel] = yplHotelEntriesOpt.flatMap { yplEntry =>
      roomsExpansionFlow.process(Data(Some(yplEntry), ctx, stepMeta(roomsExpansionFlow)))
    }
    yplHotelEntriesExpandedOpt
  }

  def executeRpmPricingFlowAsync(pricingData: YplPricingData,
                                 yplHotelEntryModelOpt: Option[YplHotelEntryModel],
                                 ctx: T): Future[Option[YPLHotel]] =
    // Batched
    splitAndMergeRPMFlow(yplHotelEntryModelOpt, ctx) { yplEntry =>
      rpmPricingFlow.process(Data(Some(yplEntry), ctx, stepMeta(rpmPricingFlow)))
    }.map { yplHotelOpt =>
      postRpmPricingFlow.process(Data(yplHotelOpt, ctx, stepMeta(postRpmPricingFlow)))
    }

  private[logic] def splitAndMergeRPMFlow(yplHotelEntryModelOpt: Option[YplHotelEntryModel], ctx: T)(
    process: YplHotelEntryModel => Option[YplRoomsWithEntry]): Future[Option[YplHotelWithEntry]] = yplHotelEntryModelOpt
    .map { yplEntry =>
      /* This grouping is good enough for performance, splitting by equally sized groups doesn't show visible benefits
       * The resulting iterator, when grouping max 4 roomTypes together, will look like:
       * Iterator{ List(1,1,1,7,7,3,2) , List(9,6,6,4,4,8), List(5,5,10) }
       */
      val roomTypeGroups: Iterator[List[YplRoomEntry]] =
        yplEntry.rooms.groupBy(_.roomTypeId).values.grouped(PricingRpmFlow.MAX_ROOMTYPES_PER_GROUP).map(_.flatten.toList)

      val roomsFut = for (groupN <- roomTypeGroups) yield Future(process(yplEntry.copy(rooms = groupN)))

      // I want to fail all if at least one groups fail
      Future.sequence(roomsFut).map { groupedRoomTypes: Iterator[Option[YplRoomsWithEntry]] =>
        val roomEntries = groupedRoomTypes.flatten.toVector
        roomEntries.headOption.map { rwe =>
          val yplRooms = roomEntries.foldLeft(ListBuffer.empty[YPLRoom])((yplRooms, rwe) => yplRooms ++= rwe.rooms)
          YplHotelWithEntry(hotel = roomsWithEntryToHotel(YplRoomsWithEntry(yplRooms.toList, rwe.entry), ctx),
                            entry = rwe.entry)
        }
      }
    }
    .toFuture
    .map(_.flatten)

  private def sendModelsConverterBeforeAfterMetric(metric: String, size: Int, tags: Map[String, String])(implicit
    ctx: T): Unit = if (roomStatsTrackingSettings.enabled) {
    ctx.sendMetric(metric, size.toLong, tags)
  }

  protected[logic] def modelsConverter: (YplPricingData, T) => Option[YplHotelEntryModel] =
    (data: YplPricingData, ctx: T) => {
      val converted = data.proto match {
        case Left(offer) =>
          val supplierId = offer.supplyInfo.map(_.supplierId.toString).getOrElse("")
          ctx.measureTime(Measurements.modelsConverterTime, Map("supplierId" -> supplierId)) {
            sendModelsConverterBeforeAfterMetric(
              Measurements.modelsConverterBeforeCount,
              offer.roomRates.size,
              Map(
                "supplierId" -> supplierId,
                "siteId" -> ctx.request.cInfo.cid.map(_.toString).getOrElse(""),
              ),
            )(ctx)
            convertFromPropOffer(offer, data.info, data.dispatchChannels, data.dispatchChannelsPerFence)(ctx)
          }
        case Right(otaProto) =>
          sendModelsConverterBeforeAfterMetric(
            Measurements.modelsConverterBeforeCount,
            otaProto.channelRoomRate.size,
            Map(
              "supplierId" -> otaProto.supplierId.toString,
              "siteId" -> ctx.request.cInfo.cid.map(_.toString).getOrElse(""),
            ),
          )(ctx)
          convertFromHP(otaProto, data.info, data.dispatchChannels, data.dispatchChannelsPerFence, data.protoCor)(ctx)
      }
      sendModelsConverterBeforeAfterMetric(
        Measurements.modelsConverterAfterCount,
        converted.map(_.rooms.size).getOrElse(0),
        Map(
          "supplierId" -> converted.map(_.supplierId.toString).getOrElse(""),
          "siteId" -> ctx.request.cInfo.cid.map(_.toString).getOrElse(""),
        ),
      )(ctx)
      converted
    }

  private def heuristicRoomLimiter(r: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] = r withMeta {
    implicit mx =>
      limitRoomsHeuristic(r.data)(r.ctx)
  }

  implicit class RoomEntryRpmBenefit(room: YplRoomEntry) {
    def reviseBenefit(reqOcc: YplReqOccByHotelAgePolicy, experimentContext: ExperimentContext): YplRoomEntry = {
      val (guests, rooms) =
        if (reqOcc.isFreeOcc) {
          (room.occEntry.occupancy, 1)
        } else {
          (reqOcc.guests, reqOcc.rooms)
        }
      val shouldUseRateCategoryChildRates =
        room.rateCategory.isChildRateEnabled && room.roomAllocationInfo.values.exists(_.childrenTypes.nonEmpty)

      def isEquivalentSubCharge(subChargeType_1: SubChargeType, subChargeType_2: SubChargeType): Boolean = {
        if (subChargeType_1 == subChargeType_2) return true
        (subChargeType_1, subChargeType_2) match {
          case (SubChargeType.Adult | SubChargeType.None, SubChargeType.Adult | SubChargeType.None) => true
          case _ => false
        }
      }

      val rc = room.rateCategory
      val isApplyFixForNewOccupancyLogic =
        room.occupancyBreakdown.exists(_.isApplyNewOccupancyLogic) && room.isMultipleRoomAssignmentPrice
      room.copy(dailyPrices = room.dailyPrices.map { x =>
        val prices =
          if (rc.value != 0.0) {
            x._2.prices
              .map { p =>
                val isCalculatePGPNExtraBed =
                  isApplyFixForNewOccupancyLogic && p.chargeType == ChargeType.ExtraBed && rc.applyTo == ApplyType.PGPN.entryName
                if (p.chargeType == ChargeType.Room || isCalculatePGPNExtraBed) {
                  val actualGuests =
                    if (shouldUseRateCategoryChildRates) {
                      x._2.prices
                        .filter(price => isEquivalentSubCharge(price.subChargeType, p.subChargeType))
                        .map(_.occupancy)
                        .sum
                    } else guests

                  val numOfPricesForSameOccupantTypePerRoom: Double = x._2.prices
                    .filter(_.isRoom)
                    .count(price =>
                      price.roomNo == p.roomNo && isEquivalentSubCharge(price.subChargeType, p.subChargeType))

                  val adjustedGuests: Double = actualGuests / numOfPricesForSameOccupantTypePerRoom
                  val numberOfGuestsPerRoom =
                    if (room.isJapanChildRateApplied && room.isMultipleRoomAssignmentPrice) {
                      p.occupancy
                    } else if (isApplyFixForNewOccupancyLogic) {
                      room.occupancyBreakdown match {
                        case Some(occBreakdown) if p.chargeType == ChargeType.ExtraBed =>
                          occBreakdown.occupancyUnits
                            .filter { occ =>
                              occ.roomNo == p.roomNo.getOrElse(0) &&
                              isEquivalentSubCharge(occ.subChargeType, p.subChargeType) &&
                              occ.chargeType == p.chargeType
                            }
                            .getISum(_.qty)
                        case _ => p.occupancy
                      }
                    } else {
                      adjustedGuests / rooms
                    }
                  val valueAfterCalculateBenefit = rc.calculateBenefit(
                    p.value,
                    numberOfGuestsPerRoom,
                    room.dailyPrices.size,
                    rooms,
                    p.subChargeType,
                  )
                  p.copy(
                    value = valueAfterCalculateBenefit,
                    priceBreakdownHistory = p.priceBreakdownHistory.addBreakdown(BreakdownStep.RateCategoryDiscount,
                                                                                 p.chargeType,
                                                                                 room.rateType,
                                                                                 room.currency,
                                                                                 valueAfterCalculateBenefit),
                  )
                } else {
                  p
                }
              }
              .filter(_.value > 0)
          } else x._2.prices
        x._1 -> x._2.copy(prices = prices)
      })
    }
  }

  private[logic] def calculateChildRateCategory(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] =
    d withMeta { implicit mx =>
      d.data.map(h =>
        h.copy(rooms = h.rooms.map { r =>
          if (r.rateCategory.isChild) {
            r.reviseBenefit(h.reqOcc, d.ctx.experimentContext)
          } else {
            r
          }
        }))
    }

  /**
    * Calculate base prices (retail)
    */
  private[logic] def calculatePricesRpm(d: Data[Option[YplHotelEntryModel]]): Option[YplRoomsWithEntry] = d withMeta {
    implicit mx =>
      implicit val r: YplRequest = d.ctx.request
      val result =
        d.data.map(roomsWithEntry => YplRoomsWithEntry(calculatePrices(roomsWithEntry)(d.ctx).rooms, roomsWithEntry))
      result
  }

  private def channelDiscount(d: Data[Option[YplRoomsWithEntry]]): Option[YplRoomsWithEntry] = d withMeta {
    implicit mx =>
      implicit val r = d.ctx.request
      implicit val ctx = d.ctx
      val result = d.data.map { d =>
        val channelDiscountEntry = applyChannelDiscount(d.entry, d.rooms)
        d.copy(entry = channelDiscountEntry)
      }
      result
  }

  private def calculateSupplierFundedDiscount(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] =
    d withMeta { implicit mx =>
      implicit val c: YplContext = d.ctx
      d.data.map { hotelEntry: YplHotelEntryModel =>
        val newRooms = calculateSupplierFundedDiscount(hotelEntry)
        hotelEntry.copy(rooms = newRooms)
      }
    }

  /**
    * Recalculate prices is used for several reasons:
    * 1. Calculate APS / APO prices based on suitable retail room
    * 2. Apply channel discounts / recalculate rates+commissions
    * 3. Recalculate prices / surcharges for rate-reutilized rooms (we have correct net / sell prices already)
    */
  @SuppressWarnings(Array("stryker4s.mutation.LogicalOperator", "stryker4s.mutation.ConditionalExpression"))
  private[logic] def reCalculatePrices(d: Data[Option[YplRoomsWithEntry]]): Option[YplRoomsWithEntry] = d withMeta {
    implicit mx =>
      implicit val request: YplRequest = d.ctx.request
      implicit val ctx: C = d.ctx

      val fixMarriottSurchargeExp: Boolean = d
        .map { yplRoomsWithEntry =>
          val hotelInfo = yplRoomsWithEntry.entry
          hotelInfo.supplierId == DMC.Marriott &&
          ctx.experimentContext.determineVariant(PropertyContext(hotelInfo.hotelId,
                                                                 hotelInfo.metaData.cityId,
                                                                 hotelInfo.metaData.countryId),
                                                 ABTest.MARRIOTT_SURCHARGE_ISSUE) == Variant.B
        }
        .getOrElse(false)

      val result: Option[YplRoomsWithEntry] = d.data.map { roomsWithEntry =>
        val yplHotelEntryModel = roomsWithEntry.entry
        implicit val propertyContext: PropertyContext = yplHotelEntryModel

        def calculateRoom(re: YplRoomEntry, supplierId: SupplierId, hotelId: HotelId): YPLRoom = {
          def calculatePricing(priceForAssumeTaxMap: Map[DateTime, Double] = Map.empty): YPLRoom = {
            val dailyPrices = calculateDailyPrices(
              room = re,
              paymentModel = re.paymentModel.getOrElse(yplHotelEntryModel.paymentModel),
              taxInfo = yplHotelEntryModel.taxInfo,
              agePolicy = yplHotelEntryModel.reqOcc.agePolicy,
              ignoreRequestedNumberOfRoomsForNha = yplHotelEntryModel.reqOcc.ignoreRequestedNumberOfRoomsForNha,
              supplierId = supplierId,
              applyDiscountsMultiplicatively = yplHotelEntryModel.metaData.applyDiscountsMultiplicatively,
              isPull = yplHotelEntryModel.isPull,
              priceForAssumeTaxMap = priceForAssumeTaxMap,
              isChannelDiscountStep = true,
              agencyFeatures = yplHotelEntryModel.metaData.agencyFeatures,
              hotelId = hotelId,
              chainId = yplHotelEntryModel.metaData.chainId,
              countryId = yplHotelEntryModel.metaData.countryId,
              cityId = yplHotelEntryModel.metaData.cityId,
              childAgeRange = yplHotelEntryModel.reqOcc.childAgeRange,
              isUseChildAgeRange = yplHotelEntryModel.reqOcc.isUseChildAgeRange,
            )

            val roomPrice = doPricing(
              hotelId = yplHotelEntryModel.hotelId,
              supplierId = yplHotelEntryModel.supplierId,
              paymentModel = re.paymentModel.getOrElse(yplHotelEntryModel.paymentModel),
              taxInfo = yplHotelEntryModel.taxInfo,
              room = re,
              dailyPrices = dailyPrices,
              dispatchChannels = yplHotelEntryModel.dispatchChannels,
              reqOcc = yplHotelEntryModel.reqOcc,
              surchargeRateType = yplHotelEntryModel.surchargeRateType,
              isOTASupplier = yplHotelEntryModel.isOTASupplier,
              isPull = yplHotelEntryModel.isPull,
              supplierContractedCommission = yplHotelEntryModel.supplierContractedCommission,
              gmtOffset = yplHotelEntryModel.metaData.gmtOffset,
              gmtOffsetMinutes = yplHotelEntryModel.metaData.gmtOffsetMinutes,
              chainId = yplHotelEntryModel.metaData.chainId,
              countryId = yplHotelEntryModel.metaData.countryId,
              enabledRoom = yplHotelEntryModel.metaData.enabledRoom,
              fixMarriottSurchargeExp = fixMarriottSurchargeExp,
              cityId = yplHotelEntryModel.metaData.cityId,
            )

            if (priceForAssumeTaxMap.isEmpty && roomPrice.hasTaxPrototypeLevelWithSurchargeOrFee) {
              calculatePricing(roomPrice.dailyAssumeSellExForCalculateGraduatedTax())
            } else {
              roomPrice
            }
          }

          val yplRoomOption = roomsWithEntry.rooms.find(room => room.yplRoomEntry == re)
          val yplRoom = yplRoomOption.getOrElse(calculatePricing())
          yplRoom
        }

        /**
          * Compose 2 list of rooms: reutilized rooms + enriched original rooms
          *
          * @param originalRooms src / ref rooms
          * @return reutilized + enriched
          */
        def composeReutilizedRooms(yplHotelEntryModel: YplHotelEntryModel,
                                   originalRooms: List[YPLRoom]): (Seq[YPLRoom], List[YPLRoom]) =
          if (yplHotelEntryModel.rateReutilizations.nonEmpty) {
            val reutilizedRoomDatas =
              getReutilizedRooms(yplHotelEntryModel, originalRooms, roomsWithEntry.entry.metaData.gmtOffset)
            val reutilizedRooms = reutilizedRoomDatas.map { rrd =>
              val targetBaseChannel = rrd.rateRepurposeEntry.targetChannel.baseChannelId

              // Update Channel ID on Room and the Channel Discount Breakdown in YplPrice
              val roomEntry =
                if (ctx.experimentContext.isUserB(YplExperiments.RATE_REUTIL_FENCING)) {
                  rrd.srcRoomEntry.copy(
                    channel = rrd.rateRepurposeEntry.targetChannel,
                    fences = ctx.request.fences.getOrElse(rrd.rateRepurposeEntry.targetChannel, Set.empty),
                  )
                } else {
                  rrd.srcRoomEntry.copy(channel = rrd.rateRepurposeEntry.targetChannel)
                }
              val newDailyPrices: List[YplPrice] =
                if (rrd.dailyPrices.exists(_.channelDiscounts.nonEmpty)) {
                  rrd.dailyPrices.map { price =>
                    val newChannelDiscounts: List[YplChannelDiscountBreakdown] =
                      price.channelDiscounts.map { breakdown =>
                        breakdown.copy(channelId = targetBaseChannel, isBase = true)
                      }(collection.breakOut)
                    price.copy(channelDiscounts = newChannelDiscounts)
                  }(collection.breakOut)
                } else rrd.dailyPrices

              val room = doPricing(
                hotelId = rrd.srcRoom.hotelId,
                supplierId = rrd.srcRoom.supplierId,
                paymentModel = rrd.srcRoom.paymentModel,
                taxInfo = yplHotelEntryModel.taxInfo,
                room = roomEntry,
                dailyPrices = newDailyPrices,
                dispatchChannels = yplHotelEntryModel.dispatchChannels,
                reqOcc = yplHotelEntryModel.reqOcc,
                surchargeRateType = yplHotelEntryModel.surchargeRateType,
                isOTASupplier = yplHotelEntryModel.isOTASupplier,
                isPull = yplHotelEntryModel.isPull,
                supplierContractedCommission = yplHotelEntryModel.supplierContractedCommission,
                gmtOffset = yplHotelEntryModel.metaData.gmtOffset,
                gmtOffsetMinutes = yplHotelEntryModel.metaData.gmtOffsetMinutes,
                chainId = yplHotelEntryModel.metaData.chainId,
                countryId = yplHotelEntryModel.metaData.countryId,
                enabledRoom = yplHotelEntryModel.metaData.enabledRoom,
                fixMarriottSurchargeExp = fixMarriottSurchargeExp,
                cityId = yplHotelEntryModel.metaData.cityId,
              )
              val reutilizedRoom = room.copy(rateRepurposeInfos = Seq(rrd.rateRepurposeInfo))
              val reutilizedRoomWithDmcUid = DMCUidGenerator.addDMCUid(yplHotelEntryModel, reutilizedRoom)
              val reutilizedRoomWithUid =
                reutilizedRoomWithDmcUid.copy(rateRepurposeInfos = reutilizedRoomWithDmcUid.rateRepurposeInfos.map(rr =>
                  rr.copy(reUtilRoomUID = reutilizedRoomWithDmcUid.uid)))
              reutilizedRoomWithUid
            }

            val allReutilizedInfos = reutilizedRooms.flatMap(_.rateRepurposeInfos)

            val enrichedOriginalRooms = originalRooms.map { originalRoom =>
              val repurposeInfosForOriginalRoom = allReutilizedInfos.collect {
                case rr if rr.srcRoomUID == originalRoom.uid => rr.copy(isReutilized = false)
              }
              if (repurposeInfosForOriginalRoom.nonEmpty && !originalRoom.roomFeatures.isAgodaAgency)
                originalRoom.copy(rateRepurposeInfos = repurposeInfosForOriginalRoom)
              else originalRoom
            }

            (reutilizedRooms, enrichedOriginalRooms)
          } else (Nil, originalRooms)

        val realRooms =
          yplHotelEntryModel.rooms.map(calculateRoom(_, yplHotelEntryModel.supplierId, yplHotelEntryModel.hotelId))
        val roomsWithDMCUid = realRooms.map(room => DMCUidGenerator.addDMCUid(yplHotelEntryModel, room))
        val (reutilizedRooms, enrichedRealRooms) = composeReutilizedRooms(yplHotelEntryModel, roomsWithDMCUid)
        roomsWithEntry.copy(rooms = enrichedRealRooms ++ reutilizedRooms)
      }

      result
  }

  // Only statistics after calculations are completed
  private[logic] def setSupplierStats(yplHotel: YPLHotel): YPLHotel = {
    val roomsPerSupplier = yplHotel.rooms.groupBy(_.supplierId)

    val supplierStats = roomsPerSupplier.map { case (supplierId, yplRooms) =>
      val roomTypes = yplRooms.foldLeft(Set.empty[Long]) { case (set, r) => set + r.roomTypeId }
      val roomCategories = yplRooms.foldLeft(Set.empty[Int]) { case (set, r) => set + r.rateCategoryId }
      val ratePlans = yplRooms.foldLeft(Set.empty[Int]) { case (set, r) => set + r.channel.compositeChannelId }

      val supplierStats = YplSupplierStats(
        roomsCalculated = yplRooms.getISum(r => Math.max(1, r.fences.size), _ => true),
        roomTypes = roomTypes.size,
        rateCategories = roomCategories.size,
        ratePlans = ratePlans.size,
      )
      supplierId -> supplierStats
    }
    yplHotel.copy(stats = supplierStats)
  }

  private def roomsWithEntryToHotel(roomsWithEntry: YplRoomsWithEntry, ctx: T): YPLHotel = {
    val rooms = roomsWithEntry.rooms
    val request = ctx.request
    val pricingHotel: YPLHotel = PricingCommon.convertToHotel(roomsWithEntry.entry, request, rooms)
    val yplHotel =
      if (request.featureRequest.populateExternalData)
        addExternalData(pricingHotel, roomsWithEntry.entry.supplierId, request.isSSR)
      else pricingHotel
    val yplHotelWithStats = setSupplierStats(yplHotel)
    yplHotelWithStats
  }

  private[logic] def filterFinalRooms(d: Data[Option[YplRoomsWithEntry]]): Option[YplRoomsWithEntry] = d withMeta {
    implicit mx =>
      val ctx = d.ctx
      val los = ctx.request.lengthOfStay
      d.data.map { hotelWithEntry =>
        val (rooms, numberOfRoomsRemoved) = hotelWithEntry.rooms.foldLeft((List.empty[YPLRoom], 0)) {
          case ((roomsSoFar, removedRoomsWithStackDiscount), room) =>
            val isValidRoomStatus = VALID_ROOM_STATUSES.contains(room.roomStatus)
            val isValidMultipleRoomPriceAssignmentCheck =
              (room.roomPrices.size == los || room.roomFeatures.isMultipleRoomAssignmentPrice) // to let multiple room assignment price skip this logic
            val isPriceValid = PriceCalculator.isRoomPriceValid(room.netEx, room.margin)
            (isValidRoomStatus, isValidMultipleRoomPriceAssignmentCheck, isPriceValid) match {
              case (true, true, false) if room.channel.stackedChannelIds.nonEmpty =>
                (roomsSoFar, removedRoomsWithStackDiscount + 1)
              case (true, true, true) => (room :: roomsSoFar, removedRoomsWithStackDiscount)
              case _ => (roomsSoFar, removedRoomsWithStackDiscount)
            }
        }

        if (numberOfRoomsRemoved > 0) {
          ctx.sendMetric(Measurements.roomsWithStackDiscountRemovedDueToInvalidPrice, numberOfRoomsRemoved)
        }
        // Wrapping the Inventory filtering in a kill switch experiment since in JTBB-333 we've made modifications in both A and B variants
        val filteredRooms =
          if (ctx.experimentContext.isUserB(YplExperiments.REMOVE_OTA_JAPANICAN_KILL_SWITCH))
            InventoryRoomFilterService.filterRooms(rooms)(ctx)
          else rooms
        hotelWithEntry.copy(rooms =
          filteredRooms.reverse,
        ) // TODO : If reverse is removed then tests are failing due to price mismatch. Need to check on this
      }
  }

  private[logic] def filterEscapesPackageIfDisabled(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] = {
    val isEscapesDisabled = d.ctx.request.featureRequest.disableEscapesPackage
    if (isEscapesDisabled) {
      d.data.map { hotel =>
        val nonEscapeRooms = hotel.rooms.filterNot(isEscapesSupported)
        hotel.copy(rooms = nonEscapeRooms)
      }
    } else d.data
  }

  private[logic] def filterEscapesPackageIfNotApproved(
    d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] = d.data.map { hotel =>
    if (!d.ctx.request.featureRequest.enableReturnNonApprovedEscapes) {
      val approvedRooms = hotel.rooms.filter(room => room.isNotEscapes || room.isApprovedEscapes)
      hotel.copy(rooms = approvedRooms)
    } else hotel
  }

  private[logic] def filterRatePlanCheckInCheckOut(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] =
    d.data.map { hotel =>
      val hotelCountryId = hotel.metaData.countryId

      val isUserB = d.ctx.experimentContext.isUserB(UNCONVENTIONAL_CHECKIN_CHECKOUT_REMOVE_COUNTRY_CHECK)
      val shouldReturnCheckInInfoRooms =
        (hotelCountryId == KR_COUNTRYID || isUserB) && !d.ctx.request.isHourlyRequest && d.ctx.request.featureRequest.enableRatePlanCheckInCheckOut
      if (!shouldReturnCheckInInfoRooms) {
        val nonCheckInInfoRooms = hotel.rooms.filter(room => room.checkInInformation.isEmpty)
        hotel.copy(rooms = nonCheckInInfoRooms)
      } else hotel
    }

  private[logic] def filterRichContentOfferIfDisabled(
    d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] = {
    val isRichContentOfferEnabled = d.ctx.request.featureRequest.enableRichContentOffer
    if (isRichContentOfferEnabled) d.data
    else {
      d.data.map { hotel =>
        val nonRichContentRooms = hotel.rooms.filterNot(isRichContentSupported)
        hotel.copy(rooms = nonRichContentRooms)
      }
    }
  }

  private[logic] def isEscapesSupported(room: YplRoomEntry): Boolean =
    room.rateCategory.stayPackageType.exists(_.isEscapes)

  private[logic] def isRichContentSupported(room: YplRoomEntry): Boolean =
    room.rateCategory.stayPackageType.exists(_.isRichContent)

  private[logic] def doFilterSellableRateCategoryByWhitelabel(
    d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] =
    filterSellableRateCategoryWithWhitelabelID(d, d.ctx.request.whitelabelSetting, d.ctx.request.cInfo.isLogIn)

  private[logic] def doFilterCheapestPriceRooms(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] =
    if (!d.ctx.request.flagInfo.filterAPO) filterCheapestPriceRooms(d)
    else {
      d
    }

  private[logic] def filterByFilterCriteria(d: Data[Option[YplRoomsWithEntry]]): Option[YplRoomsWithEntry] =
    d.ctx.request.filterCriteria match {
      case Some(list) =>
        if (list.contains(FilterCriteria.FreeCancellable)) d.data.map { hotel =>
          val filteredRooms = hotel.rooms.filter(r =>
            isFreeCancellation(r.cxlCode,
                               d.ctx.request.checkIn,
                               d.ctx.request.bookingDate,
                               hotel.entry.metaData.gmtOffset,
                               hotel.entry.metaData.gmtOffsetMinutes))
          hotel.copy(rooms = filteredRooms)
        }
        else d.data
      case None => d.data
    }

  private[logic] def filterRestrictedRatecategory(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] =
    d.data.map { hotel =>
      hotel.metaData.restrictedRatecategoryIds match {
        case ids if ids.nonEmpty => hotel.copy(rooms = hotel.rooms.filter(r => !ids.contains(r.rateCategoryId)))
        case _ => hotel
      }
    }

  /**
    * Filter out Run of House (RoH) rooms
    */
  private[logic] def filterRoHRooms(d: Data[Option[YplHotelEntryModel]]): Option[YplHotelEntryModel] = {
    val request = d.ctx.request
    val isRoHFilteringEnabled = d.ctx.experimentContext
      .isUserB(YplExperiments.DISABLE_ROOM_ON_ARRIVAL) && request.whitelabelSetting.disableRoomOnArrival

    if (isRoHFilteringEnabled) {
      d.data.map { hotel =>
        val filteredRooms = hotel.rooms.filterNot { room =>
          hotel.metaData.enabledRoom.get(room.masterRoomTypeId).exists(_.rohFlag) ||
          room.dmcDataHolder.exists { dataHolder =>
            dataHolder.supplierRateInfo.exists { rateInfo =>
              rateInfo.externalData.exists(e =>
                e.field == ExternalDataFields.IsRoH && e.value.equalsIgnoreCase(ExternalDataValues.BooleanTrue))
            }
          }
        }
        hotel.copy(rooms = filteredRooms)
      }
    } else {
      d.data
    }
  }
}
