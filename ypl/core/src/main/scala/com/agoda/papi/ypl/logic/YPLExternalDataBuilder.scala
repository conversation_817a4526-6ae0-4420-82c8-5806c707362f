package com.agoda.papi.ypl.logic

import com.agoda.papi.enums.room.{ChargeType, ChildRateType, RateType}
import com.agoda.papi.ypl.commission.apm.models.{
  ApmAdjustmentDiscount,
  ApmCommissionDiscount,
  MultipleAutoPriceMatchHolder,
}
import com.agoda.papi.ypl.models.hotel.DmcData
import com.agoda.papi.ypl.models.suppliers.externalData.YPLExternalDataDailyRate
import com.agoda.papi.ypl.models.{
  ApmExternalData,
  ApmPriceAdjustmentExternalData,
  ApmPriceCommissionExternalData,
  DmcDataHolder,
  HotelId,
  Quantity,
  ResellExternalData,
  RoomNumber,
  YPLExternalData,
  YPLHotel,
  YPLRoom,
  YPLRoomAllocation,
  YplRateFence,
}
import com.agoda.papi.ypl.utils.Implicits.DoubleRounding
import com.agoda.utils.collection.SumImplicits._
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.format.DateTimeFormat
import spray.json._

trait YPLExternalDataBuilder extends LazyLogging {

  private[logic] def addDmcData(hotel: YPLHotel): YPLHotel = {
    val rooms = hotel.rooms.map(r => r.copy(dmcData = buildDmcData(r.dmcDataHolder, r.pricesToExternalDailyRates)))
    hotel.copy(rooms = rooms)
  }

  private def buildDmcData(dmcDataHolder: Option[DmcDataHolder],
                           prices: List[YPLExternalDataDailyRate]): Option[DmcData] = dmcDataHolder.map { d =>
    // return PRPN with incoming rate type
    val totalPrice = prices.getDSum(_.incoming)
    val data = d.yplExternalData.map(_.copy(totalPrice = totalPrice))
    val extStr = data.flatMap { extData =>
      try Some(extData.toJson(YPLPostProcess.externalDataWriter).toString)
      catch {
        case ex: Throwable =>
          logger.error(s"Error during converting external data hotelDMCId:${extData.hotelCode}", ex)
          None
      }
    }
    DmcData(
      externalData = extStr.getOrElse(""),
    )
  }

  private[logic] def addDmcDataForApmHotel(hotel: YPLHotel): YPLHotel = {
    val rooms = hotel.rooms.map { room =>
      val roomStatus = getRoomStatus(room)
      if (roomStatus) room.copy(dmcData = buildApmDmcData(hotel.id, room.apmExternalData))
      else room
    }
    hotel.copy(rooms = rooms)
  }

  private def buildApmDmcData(hotelId: HotelId, apmExternalDataOpt: Option[ApmExternalData]): Option[DmcData] = {
    val dmcData = apmExternalDataOpt.flatMap { apmExternalData =>
      val externalDataJsonStrOpt =
        try Some(apmExternalData.toJson(YPLPostProcess.apmExternalDataWriter).toString)
        catch {
          case exception: Throwable =>
            logger.error(
              s"Error while building APM external data for hotel:${hotelId},adjustmentIdsHash:${apmExternalData.adjustmentIdsHash}",
              exception)
            None
        }
      externalDataJsonStrOpt.map { externalDataJsonStr =>
        DmcData(externalData = externalDataJsonStr)
      }
    }
    dmcData
  }

  private[logic] def getRoomStatus(room: YPLRoom): Boolean = room.isActiveAndEnableCommission

  private[logic] def addDmcDataForYcsHotel(hotel: YPLHotel): YPLHotel = {
    val rooms = hotel.rooms.map { room =>
      val roomStatus = getRoomStatus(room)
      val apmExternalDataJsonFields =
        if (roomStatus) buildApmExternalDataJsonFields(hotel.id, room.apmExternalData) else Map.empty[String, JsValue]
      val resellExternalDataJsonFields = buildResellExternalDataJsonFields(hotel.id, room.resellExternalData)
      room.copy(dmcData = mergeAndBuildDmcDataForApmAndResell(apmExternalDataJsonFields, resellExternalDataJsonFields))
    }
    hotel.copy(rooms = rooms)
  }

  private def mergeAndBuildDmcDataForApmAndResell(
    apmExternalDatJsonFields: Map[String, JsValue],
    resellExternalDatJsonFields: Map[String, JsValue]): Option[DmcData] = {
    val jsFields = apmExternalDatJsonFields ++ resellExternalDatJsonFields
    if (jsFields.nonEmpty) {
      val externalDataStr = JsObject(jsFields).toString
      Some(DmcData(externalData = externalDataStr))
    } else None
  }

  private def buildResellExternalDataJsonFields(hotelId: HotelId, resellExternalDataOpt: Option[ResellExternalData]) = {
    val jsFieldsOpt = resellExternalDataOpt.flatMap { resellExternalData =>
      try Some(resellExternalData.toJson(YPLPostProcess.resellExternalDataWriter).asJsObject.fields)
      catch {
        case exception: Throwable =>
          logger.error(s"Error while building Resell external data for hotel:${hotelId}", exception)
          None
      }
    }
    jsFieldsOpt.getOrElse(Map.empty[String, JsValue])
  }

  private def buildApmExternalDataJsonFields(hotelId: HotelId, apmExternalDataOpt: Option[ApmExternalData]) = {
    val jsFieldsOpt = apmExternalDataOpt.flatMap { apmExternalData =>
      try Some(apmExternalData.toJson(YPLPostProcess.apmExternalDataWriter).asJsObject.fields)
      catch {
        case exception: Throwable =>
          logger.error(
            s"Error while building APM external data for hotel:${hotelId},adjustmentIdsHash:${apmExternalData.adjustmentIdsHash}",
            exception)
          None
      }
    }
    jsFieldsOpt.getOrElse(Map.empty[String, JsValue])
  }
}

object YPLPostProcess extends DefaultJsonProtocol {

  implicit object ChildRateTypeJsonFormat extends RootJsonFormat[ChildRateType] {
    override def read(json: JsValue): ChildRateType =
      ChildRateType.namesToValuesMap.getOrElse(json.asInstanceOf[JsString].value, ChildRateType.Unknown)
    override def write(obj: ChildRateType): JsValue = JsNumber(obj.value)
  }

  implicit object ChargeTypeJsonFormat extends RootJsonFormat[ChargeType] {
    override def read(json: JsValue): ChargeType =
      ChargeType.namesToValuesMap.getOrElse(json.asInstanceOf[JsString].value, ChargeType.Unknown)
    override def write(obj: ChargeType): JsValue = JsNumber(obj.value)
  }

  implicit object RateTypeJsonFormat extends RootJsonFormat[RateType] {
    override def read(json: JsValue): RateType =
      RateType.namesToValuesMap.getOrElse(json.asInstanceOf[JsString].value, RateType.Unknown)
    override def write(obj: RateType): JsValue = JsNumber(obj.value)
  }

  implicit val RoomAllocationInfoFormat = new JsonWriter[(RoomNumber, YPLRoomAllocation)] {
    override def write(obj: (RoomNumber, YPLRoomAllocation)): JsValue = JsObject(
      "Adults" -> JsNumber(obj._2.adults),
      "ChildrenCountAsRoomOcc" -> JsNumber(obj._2.childrenCountAsRoomOcc),
      "ChildrenTypes" -> obj._2.childrenTypes.toList.toJson(YPLPostProcess.ChildrenTypesFormat),
      "RoomNumber" -> JsNumber(obj._1),
    )
  }
  implicit val ChildrenTypesFormat = new JsonWriter[List[(ChildRateType, Quantity)]] {
    override def write(obj: List[(ChildRateType, Quantity)]): JsValue = obj.map { o =>
      JsObject(
        "ChildRateType" -> o._1.toJson,
        "Quantity" -> JsNumber(o._2),
      )
    }.toJson
  }
  implicit val externalDataWriter = new JsonWriter[YPLExternalData] {
    def write(data: YPLExternalData): JsValue = JsObject(
      "SearchID" -> JsString(data.searchId),
      "HotelCode" -> JsString(data.hotelCode),
      "RoomTypeCode" -> JsString(data.roomTypeCode.getOrElse("")),
      "RateplanCode" -> JsString(data.ratePlanCode.getOrElse("")),
      "TotalPrice" -> JsNumber(data.totalPrice),
      "CurrencyCode" -> JsString(data.currencyCode),
      "MaxOccupancy" -> JsNumber(data.maxOcc),
      "MaxChildren" -> JsNumber(data.maxChild),
      "MaxExtraBed" -> JsNumber(data.maxExtraBed),
      "SearchOccupancy" -> JsNumber(data.srcOcc),
      "RoomAllocationInfo" -> data.roomAllocationInfo.map(_.toJson).toJson,
      "InventoryType" -> JsNumber(data.inventoryType),
      "CheckInStartTime" -> JsString(data.checkInStartTime.getOrElse("")),
      "CheckInEndTime" -> JsString(data.checkInEndTime.getOrElse("")),
      "LinkedRoomTypeCode" -> JsString(data.linkedRoomTypeCode.getOrElse("")),
    )
  }

  implicit val apmPriceAdjustmentExternalData = new JsonWriter[ApmPriceAdjustmentExternalData] {
    private val dateFormat = DateTimeFormat.forPattern("yyyy-MM-dd")
    override def write(data: ApmPriceAdjustmentExternalData): JsValue = {
      val fields = Seq[JsField](
        ("Date", JsString(data.date.toString(dateFormat))),
        ("ChargeType", data.chargeType.toJson),
        ("Currency", JsString(data.currency)),
        ("CheapestSellIn", JsNumber(data.cheapestSellIn.roundAt(6))),
        ("MarketSellIn", JsNumber(data.marketSellIn.roundAt(6))),
        ("Delta", JsNumber(data.delta.roundAt(6))),
        ("OriginalSellIn", JsNumber(data.originalSellIn.roundAt(6))),
        ("MarketPriceDiscountPercent", JsNumber(data.marketPriceDiscountPercent.roundAt(6))),
        ("MaximumDeltaPercent", JsNumber(data.maximumDeltaPercent.roundAt(6))),
        ("ApprovalPriceId", JsNumber(data.approvalPriceId)),
        ("CheapestPriceLocal", JsNumber(data.cheapestPriceLocal.roundAt(6))),
        ("MarketPriceLocal", JsNumber(data.marketPriceLocal.roundAt(6))),
        ("OriginalPriceLocal", JsNumber(data.originalPriceLocal.roundAt(6))),
        ("AdjustmentRateType", data.adjustmentRateType.toJson),
      )
      val withAdjustmentData = data.isPartialAdjustment match {
        case Some(_) => fields ++ Seq[JsField](("IsPartialAdjustment", JsBoolean(data.isPartialAdjustment.get)))
        case _ => fields
      }
      JsObject(withAdjustmentData: _*)
    }
  }

  implicit val apmPriceCommissionExternalData = new JsonWriter[ApmPriceCommissionExternalData] {
    private val dateFormat = DateTimeFormat.forPattern("yyyy-MM-dd")
    override def write(data: ApmPriceCommissionExternalData): JsValue = JsObject(
      "Date" -> JsString(data.date.toString(dateFormat)),
      "ChargeType" -> JsString(data.chargeType.toString),
      "ChargeOption" -> JsString(data.chargeOption.toString),
      "ReferenceCommissionPercent" -> JsNumber(data.referenceCommissionPercent.roundAt(6)),
      "CommissionDiscountPercent" -> JsNumber(data.commissionDiscountPercent.roundAt(6)),
    )
  }

  implicit val apmAdjustmentDiscount = new JsonWriter[ApmAdjustmentDiscount] {
    private val dateFormat = DateTimeFormat.forPattern("yyyy-MM-dd")

    override def write(data: ApmAdjustmentDiscount): JsValue = JsObject(
      "ProgramId" -> JsNumber(data.programId),
      "AdjustmentChannelId" -> JsNumber(data.adjustmentChannelId),
      "AdjustmentDiscountPercent" -> JsNumber(data.adjustmentDiscountPercent.getOrElse(0.0).roundAt(6)),
      "StartDate" -> JsString(data.startDate.toString(dateFormat)),
      "EndDate" -> JsString(data.endDate.toString(dateFormat)),
    )
  }

  implicit val apmCommissionDiscount = new JsonWriter[ApmCommissionDiscount] {
    private val dateFormat = DateTimeFormat.forPattern("yyyy-MM-dd")

    override def write(data: ApmCommissionDiscount): JsValue = JsObject(
      "ProgramId" -> JsNumber(data.programId),
      "CommissionChannelId" -> JsNumber(data.commissionChannelId),
      "CommissionDiscountPercent" -> JsNumber(data.commissionDiscountPercent.getOrElse(0.0).roundAt(6)),
      "StartDate" -> JsString(data.startDate.toString(dateFormat)),
      "EndDate" -> JsString(data.endDate.toString(dateFormat)),
    )
  }

  implicit val apmProgram = new JsonWriter[MultipleAutoPriceMatchHolder] {
    private val dateFormat = DateTimeFormat.forPattern("yyyy-MM-dd")
    override def write(data: MultipleAutoPriceMatchHolder): JsValue = JsObject(
      "ProgramId" -> JsNumber(data.programId),
      "CommissionDiscountChannelId" -> JsNumber(data.commissionDiscountChannelId.getOrElse(0)),
      "CommissionDiscountPercent" -> JsNumber(data.commissionDiscountPercent.roundAt(6)),
      "AdjustmentChannelId" -> JsNumber(data.adjustmentChannelId),
      "AdjustmentDiscountPercent" -> JsNumber(data.adjustmentDiscountPercent.roundAt(6)),
      "StatusId" -> JsNumber(data.statusId),
      "StartDate" -> JsString(data.startDate.toString(dateFormat)),
      "EndDate" -> JsString(data.endDate.map(_.toString(dateFormat)).getOrElse("2999-12-31")),
      "ApmAdjustmentDiscount" -> data.apmAdjustmentDiscount.map(_.toJson).toJson,
      "ApmCommissionDiscount" -> data.apmCommissionDiscount.map(_.toJson).toJson,
      "ProgramType" -> JsNumber(data.programType.getOrElse(0)),
    )
  }

  implicit val yplRateFenceDataWriter = new JsonWriter[YplRateFence] {
    def write(data: YplRateFence): JsValue = JsObject(
      "Origin" -> JsString(data.origin),
      "Cid" -> JsNumber(data.cid),
      "Language" -> JsNumber(data.language),
    )
  }

  /* In the final output the json fields in externalData will be merged between APM and Resell info.
     To maintain uniqueness of the field names its better to prefix APM fields with `APM` */
  implicit val apmExternalDataWriter = new JsonWriter[ApmExternalData] {
    def write(data: ApmExternalData): JsValue = {
      val fields = Seq[JsField](
        ("ApmHotelActiveStatus", JsString(data.isApmHotelActive.toString)),
        ("ApmAdjustmentIdsHash", JsString(data.adjustmentIdsHash)),
        ("ApmPriceAdjustmentDetails", data.priceAdjustmentDetails.map(_.toJson).toJson),
        ("ApmPriceCommission", data.priceCommission.map(_.toJson).toJson),
        ("LogicVersion", JsString(data.logicVersion)),
        ("PriceAdjustmentProgram", data.priceAdjustmentProgram.toJson),
        ("CommissionDiscountProgram", data.commissionDiscountProgram.toJson),
        ("ApmLeadingRoomAdjustmentIds", data.apmLeadingRoomAdjustmentIds.toJson),
        ("AdditionalCommRedHotelLevel", JsNumber(data.additionalCommRedHotelLevel)),
        ("RateFence", data.rateFence.toList.map(x => x.toJson).toJson),
      )
      val withMADOata = data.isMADO match {
        case Some(_) => fields ++ Seq[JsField](("IsMADO", JsString(data.isMADO.get.toString)))
        case _ => fields
      }
      val withOriginalChannelIdData = data.originalChannelId match {
        case Some(_) => withMADOata ++ Seq[JsField](("OriginalChannelId", JsNumber(data.originalChannelId.get)))
        case _ => withMADOata
      }
      JsObject(withOriginalChannelIdData: _*)
    }
  }

  /* In the final output the json fields in externalData will be merged between APM and Resell info.
     To maintain uniqueness of the field names its better to prefix Resell Info related fields with `Resell` */
  implicit val resellExternalDataWriter = new JsonWriter[ResellExternalData] {
    def write(data: ResellExternalData): JsValue = JsObject(
      "ResellSourceBookingId" -> JsString(data.sourceBookingId),
    )
  }

}
