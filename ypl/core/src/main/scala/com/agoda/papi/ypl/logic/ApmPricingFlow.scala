package com.agoda.papi.ypl.logic

import api.routing.dsl.ExpirableRoutingDSL
import com.agoda.papi.enums.room.{Channel, ChargeType, RateType}
import com.agoda.papi.ypl.commission.ApmCommissionUtils.{RoomOccupancyConverter, YplRateFenceConverter}
import com.agoda.papi.ypl.commission.Types.RoomIndexedValue
import com.agoda.papi.ypl.commission.apm.`enum`.ApmConfigLevel
import com.agoda.papi.ypl.commission.apm.models.ApmTypes.{RoomIndex, RoomPriceIndex}
import com.agoda.papi.ypl.commission.apm.models._
import com.agoda.papi.ypl.commission.apm.{ApmCommissionDiscountService, ApmPriceAdjustmentService}
import com.agoda.papi.ypl.commission.models.YplRateFenceHolder
import com.agoda.papi.ypl.commission.service.CommissionService
import com.agoda.papi.ypl.logging._
import com.agoda.papi.ypl.logic.ApmPricingFlow._
import com.agoda.papi.ypl.models.CommonTaxConverter.toCommonReqOccByHotelAgePolicy
import com.agoda.papi.ypl.models.YplExperiments._
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.models.enums.ApmNoPriceAdjustmentReason
import com.agoda.papi.ypl.models.hotel.DiscountInfo
import com.agoda.papi.ypl.models.pricing._
import com.agoda.papi.ypl.models.pricing.proto.HotelTaxInfo
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.suppliers.DMC.apmDirectConnectSupplierList
import com.agoda.papi.ypl.pricing.PriceCalculator._
import com.agoda.papi.ypl.pricing.{APMHelper, PriceCalculation}
import com.agoda.papi.ypl.utils.DayUseUtils
import com.agoda.papi.ypl.utils.Implicits._
import com.agoda.utils.collection.SumImplicits._
import com.agoda.utils.flow.PropertyContext
import models.consts.ABTest
import org.joda.time.DateTime

object ApmPricingFlow {
  val INT_ZERO: Int = 0
  val BD_ZERO: Double = 0.0
  val BD_HUNDRED: Double = 100.0
  val TO_PERCENT: Double = 0.01
  val DEFAULT_MAX_DELTA_PERCENT: Double = 30.0
  val UNKNOWN_PROGRAM_ID: Int = -1
  val DEFAULT_START_DATE: DateTime = DateTime.parse("2020-01-01")
  val DEFAULT_END_DATE: DateTime = DateTime.parse("2999-12-31")
  val INVALID_APPROVAL_PRICE_ID: Long = 0
  val NO_PROGRAM: MultipleAutoPriceMatchHolder = MultipleAutoPriceMatchHolder(
    programId = 0,
    commissionDiscountChannelId = Some(0),
    adjustmentChannelId = 0,
    adjustmentDiscountPercent = 0,
    commissionDiscountPercent = 0,
    statusId = 0,
    startDate = DEFAULT_START_DATE,
    endDate = Some(DEFAULT_START_DATE),
    apmAdjustmentDiscount = Seq.empty,
    apmCommissionDiscount = Seq.empty,
    programType = Some(0),
  )
  val TAIWAN_COUNTRY_ID = 140

  private[logic] def isEligibleForAPM(apmRoomChannelSetting: Seq[MultipleAutoPriceMatchHolder],
                                      hotel: YPLHotel,
                                      enableApmPriceAdjustment: Boolean): Boolean = apmRoomChannelSetting.nonEmpty &&
    isSupplierIdValid(hotel.supplierId) &&
    enableApmPriceAdjustment

  private[logic] def isSupplierIdValid(supplierId: SupplierId): Boolean = {
    val isApmDirectConnect = apmDirectConnectSupplierList.contains(supplierId)
    DMC.YCS == supplierId || isApmDirectConnect
  }

  /**
    * @param fxRate exchange rate which convert the source rate to USD
    *
    *               This function calculate the commission reduction on Ypl prices level instead of the room level.
    */
  private[logic] def getCommissionReductionAmt(yplRoom: YPLRoom, fxRate: Double): Double =
    yplRoom.prices.map(yplPrice => yplPrice.sellInclusive * yplPrice.apmCommissionDiscountPercent * 0.01 * fxRate).sum

  /**
    * GIN-153511.
    * This function logs count and amount for commission reduction only for the request of booking
    */
  private[logic] def sendCommissionReductionMetrics(ctx: YplContext,
                                                    hotelWithEntry: YplRoomsWithEntry,
                                                    yplRooms: List[YPLRoom]): Unit = if (ctx.request.isBookingRequest) {
    val tags: Map[HotelName, String] = Map("hotelOrigin" -> hotelWithEntry.entry.metaData.countryCode)
    val commissionReductionAmount: Int = yplRooms
      .map { room =>
        val fxRateOpt = ctx.exchangeRateCtx.getExchangeRate(room.currency, "USD")
        fxRateOpt match {
          case Some(fxRate) => getCommissionReductionAmt(room, fxRate.toUsd.toDouble)
          case _ => 0
        }
      }
      .sum
      .toInt

    ctx.sendMetric("apm_commission_reduction_amount", commissionReductionAmount, tags)
  }
}

// scalastyle:off file.size.limit
trait ApmPricingFlow[T <: YplContext]
  extends ExpirableRoutingDSL[T]
    with PriceCalculation
    with YPLExternalDataBuilder
    with ApmCommissionDiscountService
    with ApmPriceAdjustmentService
    with CommissionService {
  private val toGMT = -7

  /**
    * ApmPricingFlowLogElement collects all the required information for the log of each search request
    */
  case class ApmPricingFlowLogElement(hotelWithApmRooms: YPLHotel,
                                      metaData: HotelMeta,
                                      yplDispatchChannels: YplDispatchChannels,
                                      apmRoomChannelSetting: Seq[MultipleAutoPriceMatchHolder],
                                      enableApmPriceAdjustment: Boolean,
                                      apmRooms: List[YPLRoom] = List.empty,
                                      nonApmRooms: List[YPLRoom] = List.empty)

  private[logic] def executeApm(hotel: YPLHotel, meta: HotelMeta, dispatchChannels: YplDispatchChannels)(
    ctx: T): YPLHotel = {
    val isMultiRoomApplied = hotel.rooms.exists(_.roomFeatures.isMultipleRoomAssignmentPrice)
    if (!isMultiRoomApplied) {
      val (hotelWithApmRooms, _) = processApmPriceAdjustment(hotel, meta, dispatchChannels)(ctx)
      hotelWithApmRooms
    } else hotel
  }

  // TODO:: check if we can use roomOccupancy.adults - roomOccupancy.extraBeds == apmOccupancy
  private def compareRoomOccToApmOcc(roomOccupancy: RoomOccupancy, apmOccupancy: Int): Boolean =
    roomOccupancy.adults == apmOccupancy &&
    roomOccupancy.children == 0 &&
    roomOccupancy.extraBeds == 0 &&
    roomOccupancy.infants == 0

  private[logic] def buildReasonMessage(apmApprovalPriceId: ApmApprovalPriceId,
                                        apmDate: DateTime,
                                        apmRoomId: Long,
                                        apmOccupancy: Int,
                                        apmPrice: Double,
                                        apmLeadingRoomId: Seq[Int],
                                        nonApmRooms: List[YPLRoom],
                                        apmRoomChannelSetting: Seq[MultipleAutoPriceMatchHolder],
                                        isExcludeQuarantineChannel: Boolean,
                                        hotelMeta: HotelMeta)(implicit
    ctx: YplContext): ApmNoPriceAdjustmentReasonLog = {

    def checkIsAgodaCheaperReason(leftRooms: List[YPLRoom]): ApmNoPriceAdjustmentReason = {
      val agodaCheapestRoomsForAdjustment = leftRooms.minByOption(_.sellIn)

      val adjustmentDiscountPercentOpt = apmRoomChannelSetting.map(_.adjustmentDiscountPercent).maxOption
      val discountedApmPriceOpt =
        adjustmentDiscountPercentOpt.map(discountPct => apmPrice - (apmPrice * discountPct * ApmPricingFlow.TO_PERCENT))

      val roomPriceOpt = agodaCheapestRoomsForAdjustment.flatMap(cp =>
        cp.roomPrices.collectFirst { case roomPrice if roomPrice.date == apmDate => roomPrice }.map(_.sellInclusive))

      (roomPriceOpt, discountedApmPriceOpt) match {
        case (Some(roomPrice), Some(discountedApmPrice)) if roomPrice <= discountedApmPrice =>
          ApmNoPriceAdjustmentReason.AgodaAlreadyCheaper
        case _ => ApmNoPriceAdjustmentReason.OtherReason
      }
    }

    // There's always one program per hotel
    val apmProgramId = apmRoomChannelSetting.map(_.programId).headOption.getOrElse(ApmPricingFlow.INT_ZERO)

    val agodaCheapestRooms = {
      val eligibleForCheapestRoomCandidate = nonApmRooms.filter(room =>
        APMHelper.validateEligibleRoom(room,
                                       hotelMeta.countryId,
                                       hotelMeta.apmConfigs,
                                       isExcludeQuarantineChannel,
                                       apmProgramId) && compareRoomOccToApmOcc(room.occ, apmOccupancy))
      if (eligibleForCheapestRoomCandidate.nonEmpty) {
        val cheapestSellIn = eligibleForCheapestRoomCandidate.minBy(_.sellIn).sellIn
        eligibleForCheapestRoomCandidate.filter(_.sellIn == cheapestSellIn)
      } else Seq.empty
    }

    val isBlackoutDate = APMHelper.isBlackoutDate(
      APMHelper.getBlackoutDateListByConfigLevel(hotelMeta.apmConfigs, ApmConfigLevel.Hotel.value),
      apmDate)

    val reason =
      if (isBlackoutDate) {
        ApmNoPriceAdjustmentReason.ApmBlackoutDateOnStayDate
      } else {
        val nonApmRoomsMatchedApmRoomId = nonApmRooms.filter(_.roomTypeId == apmRoomId)
        val nonApmRoomsEligibleRateChannel = nonApmRoomsMatchedApmRoomId.filter(
          APMHelper.validateEligibleRoom(_,
                                         hotelMeta.countryId,
                                         hotelMeta.apmConfigs,
                                         isExcludeQuarantineChannel,
                                         apmProgramId = apmProgramId))
        val nonApmRoomsEligibleOcc =
          nonApmRoomsEligibleRateChannel.filter(room => compareRoomOccToApmOcc(room.occ, apmOccupancy))
        val nonApmRoomsMatchedAgodaCheapest =
          nonApmRoomsEligibleOcc.filter(room => agodaCheapestRooms.exists(_.roomTypeId == room.roomTypeId))
        val nonApmRoomsMatchedApmLeadingRoom =
          nonApmRoomsMatchedAgodaCheapest.filter(room => apmLeadingRoomId.contains(room.roomTypeId))
        if (nonApmRoomsMatchedApmRoomId.isEmpty) ApmNoPriceAdjustmentReason.NoRoomMatchWithApmRoomId
        else if (nonApmRoomsEligibleRateChannel.isEmpty) ApmNoPriceAdjustmentReason.NoRoomForEligibleRc
        else if (nonApmRoomsEligibleOcc.isEmpty) ApmNoPriceAdjustmentReason.NoRoomForEligibleOcc
        else if (nonApmRoomsMatchedAgodaCheapest.isEmpty) ApmNoPriceAdjustmentReason.ApmRoomIdIsNotAgodaCheapest
        else if (nonApmRoomsMatchedApmLeadingRoom.isEmpty) ApmNoPriceAdjustmentReason.AgodaCheapestIsNotLeadRoom
        else checkIsAgodaCheaperReason(nonApmRoomsMatchedApmLeadingRoom)
      }

    ApmNoPriceAdjustmentReasonLog(ctx.request.searchId, apmApprovalPriceId, reason.value)
  }

  private[logic] def buildApmNoPriceAdjustmentReasonMessage(input: ApmPricingFlowLogElement)(implicit
    ctx: YplContext): Set[ApmNoPriceAdjustmentReasonLog] = {

    case class ApmInfoInput(apmApprovalPriceId: ApmApprovalPriceId,
                            apmDate: DateTime,
                            apmRoomId: Long,
                            apmOccupancy: Int,
                            apmPrice: Double)

    if (isEligibleForAPM(input.apmRoomChannelSetting, input.hotelWithApmRooms, input.enableApmPriceAdjustment)) {

      val allInputApprovalPrices: Set[ApmInfoInput] =
        input.hotelWithApmRooms.autoPriceMatchInfo.flatMap { case (apmKey, apmInfo) =>
          val occupancy = apmKey.occupancy
          apmInfo.map { info =>
            ApmInfoInput(apmApprovalPriceId = info._2.apmApprovalPriceId,
                         apmDate = info._1,
                         apmRoomId = info._2.apmRoomTypeId,
                         apmOccupancy = occupancy,
                         apmPrice = info._2.marketPrice)
          }
        }.toSet

      val usedApprovalPriceIds =
        input.apmRooms.flatMap(_.apmExternalData).flatMap(_.priceAdjustmentDetails).map(_.approvalPriceId).toSet

      val nonUsedApprovalPriceIds =
        allInputApprovalPrices.filterNot(ap => usedApprovalPriceIds.contains(ap.apmApprovalPriceId))

      val isExcludeQuarantineChannel = true

      nonUsedApprovalPriceIds.map { aid =>
        buildReasonMessage(
          apmApprovalPriceId = aid.apmApprovalPriceId,
          apmDate = aid.apmDate,
          apmRoomId = aid.apmRoomId,
          apmOccupancy = aid.apmOccupancy,
          apmPrice = aid.apmPrice,
          apmLeadingRoomId = input.metaData.apmLeadingRoomAdjustmentIds,
          nonApmRooms = input.nonApmRooms,
          apmRoomChannelSetting = input.apmRoomChannelSetting,
          isExcludeQuarantineChannel = isExcludeQuarantineChannel,
          hotelMeta = input.metaData,
        )
      }
    } else Set.empty
  }

  /**
    * @param input a case class containing all the required information for log
    * @param ctx   YPL context
    * @return a well formatted message for Agoda adp log
    */
  private[logic] def buildApmPricingFlowMessage(input: ApmPricingFlowLogElement)(implicit
    ctx: YplContext): Option[ApmPricingFlowMessage] = {
    val allApprovalPriceIds =
      input.hotelWithApmRooms.autoPriceMatchInfo.values.flatMap(_.map(_._2.apmApprovalPriceId)).toList

    val usedApprovalPriceIds =
      input.apmRooms.flatMap(_.apmExternalData).flatMap(_.priceAdjustmentDetails).map(_.approvalPriceId)

    if (isEligibleForAPM(input.apmRoomChannelSetting, input.hotelWithApmRooms, input.enableApmPriceAdjustment)) {
      val apmPricingFlowMessage = ApmPricingFlowMessage(
        searchId = ctx.request.searchId,
        hotelId = input.metaData.hotelId,
        programIds = input.metaData.multipleAutoPriceMatch.map(_.programId).distinct,
        approvalIdCandidates = allApprovalPriceIds,
        isThisSearchGotAdjusted = input.apmRooms.nonEmpty,
        usedApprovalPriceIds = usedApprovalPriceIds,
        dispatchChannels = input.yplDispatchChannels,
        rooms = input.hotelWithApmRooms.rooms.map { r =>
          RoomForApmPricingFlowMessage(
            r.roomTypeId,
            r.rateCategoryId,
            r.channel.baseChannelId,
            r.sellIn,
            r.occupancy,
            r.supplierId,
          )
        },
        apmLeadingRoomAdjustmentIds = input.metaData.apmLeadingRoomAdjustmentIds,
      )
      Some(apmPricingFlowMessage)
    } else {
      None
    }
  }

  /**
    * Add Index to each YPLRoom and YplPrice, so that we can map the result from CommissionModule back to YPL Models
    */
  private def buildRoomAndPriceIndex(
    hotel: YPLHotel): List[(RoomIndex, (ApmPriceAdjustmentRoomParameters, List[(RoomPriceIndex, YplPrice)], YPLRoom))] =
    hotel.rooms.zipWithIndex.map { case (r, idx) =>
      val pricesWithIndex: List[(RoomPriceIndex, (ApmPriceAdjustmentPriceParameters, YplPrice))] =
        r.prices.zipWithIndex.map { case (p, idx) =>
          idx -> (ApmPriceAdjustmentPriceParameters(
            p.date,
            p.sellExclusive,
            p.sellInclusive,
            p.chargeType,
            p.subChargeType,
          ), p)
        }

      val roomParams = ApmPriceAdjustmentRoomParameters(
        r.uid,
        r.supplierId,
        r.occ.toCommissionModel,
        r.marginPercentage,
        r.channel.toCompositeChannelIds,
        r.stayPackageType,
        r.resellExternalData.isEmpty,
        r.hourlyAvailableSlots.isEmpty,
        r.yplRoomEntry.commissionHolder.apmCommissionHolder,
        r.roomTypeId,
        r.rateCategoryId,
        r.rateCategory.hasParentRateCategory,
        r.paymentModel,
        r.priusValue,
        r.fences.map(_.toCommissionModel),
        pricesWithIndex.map { case (id, (params, _)) => (id, params) },
      )

      idx -> (roomParams, pricesWithIndex.map { case (index, (_, yplPrice)) => index -> yplPrice }, r)
    }

  /**
    * @param yplContext a YPL Context from search
    * @param room a YPL room detail
    * @return a well formatted APM commission discount message for Agoda ADP Log
    */
  private[logic] def buildApmCommissionDiscountLog(yplContext: YplContext,
                                                   room: YPLRoom,
                                                   discountResult: ApmCommissionDiscountResult,
                                                   dispatchChannels: Set[Int]): Option[ApmCommissionDiscountLog] = {
    if (!yplContext.request.isBookingRequest) return None

    val defaultLogValue = ApmCommissionDiscountLog(
      searchId = yplContext.request.searchId,
      hotelId = room.hotelId,
      statusId = None,
      programId = None,
      programType = None,
      commissionDiscountChannelId = None,
      isSupplierValid = discountResult.isSupplierValid,
      isRoomEligible = discountResult.isRoomEligible,
      apmPriceCommissionDetails = room.prices.map { price =>
        ApmPriceCommissionDetail(
          date = DayUseUtils.toJavaLocalDate(price.date).toString,
          roomNumber = price.roomNumber,
          chargeType = price.chargeType,
          chargeOption = price.chargeOption,
          referenceCommissionPercent = price.referenceCommissionPercent,
          commissionDiscountPercent = price.apmCommissionDiscountPercent,
        )
      },
      roomTypeId = room.roomTypeId,
      rateCategoryId = room.rateCategoryId,
      baseChannelId = room.channel.baseChannelId,
      sellIn = room.sellIn,
      occupancy = room.occupancy,
      dmcId = room.supplierId,
      channel = room.channel,
      stayPackageType = room.stayPackageType,
      hourlyAvailableSlots = room.hourlyAvailableSlots.map(_.toString),
      resellExternalData = room.resellExternalData,
      roomFences = room.fences.map(_.toCommissionModel),
      apmSettingHolder = room.yplRoomEntry.commissionHolder.apmCommissionHolder.apmSettings,
      isCommissionDistributedFromHeisenberg = discountResult.commissionDiscountChannelSetting
        .flatMap(_.commissionDiscountChannelId)
        .exists(dispatchChannels.contains),
      isNotPackage = !room.channel.toCompositeChannelIds.contains(Channel.Packages.i),
      isNotQuarantineChannel = true,
      isExternalResellDataEmpty = room.resellExternalData.isEmpty,
      isEmptyHourlyAvailableSlots = room.hourlyAvailableSlots.isEmpty,
      skipPackageValidateCommReductionFeatureExp = true,
      removeBedPaidExclusionFromApmExp = yplContext.experimentContext.isUserB(REMOVE_BEDS_PAID_EXCLUSION_FROM_APM),
      skipApmPriceAdjustmentForResellExp = yplContext.experimentContext.isUserB(SKIP_APM_PRICE_ADJUSTMENT_FOR_RESELL),
      enabledAiForBedNetwork = yplContext.experimentContext.isUserB(APM_ENABLE_AI_FOR_BED_NETWORK),
      excludeRateChannelFromApmExp = yplContext.experimentContext.isUserB(APM_EXCLUDE_RATE_CHANNEL_IDS),
    )

    discountResult.commissionDiscountChannelSetting match {
      case Some(commissionSetting) => Some(
          defaultLogValue.copy(
            statusId = Some(commissionSetting.statusId),
            programId = Some(commissionSetting.programId),
            programType = commissionSetting.programType,
            commissionDiscountChannelId = commissionSetting.commissionDiscountChannelId,
          ))
      case None => Some(defaultLogValue)
    }

  }

  private[logic] def getCommissionExcludingAgxAndWholesale(room: YPLRoom, date: DateTime) =
    getCommissionForPriceCalculation(
      room.yplRoomEntry.commissionHolder,
      date,
      room.occ.occupancy,
      room.yplRoomEntry.isAgodaAgency,
      room.yplRoomEntry.applicableMORPCandidateRoomParameters,
      room.originalRateType,
      room.rateType,
      true,
    )

  private def buildApmRoomV2(
    room: YPLRoom,
    chainId: ChainId,
    countryId: CountryId,
    cityId: CityId,
    indexedYplPriceForRoom: Map[Int, YplPrice],
    indexedResultPriceForRoom: Map[Int, ApmPriceAdjustmentPriceResult],
    hotelTaxInfo: HotelTaxInfo,
    reqOccByHotelAgePolicy: YplReqOccByHotelAgePolicy,
    apmPriceAdjustmentSetting: MultipleAutoPriceMatchHolder,
    adjustmentChannelId: YplChannel,
    fences: Set[YplRateFenceHolder],
    isMADO: Option[Boolean])(implicit ctx: YplContext, propertyContext: PropertyContext): YPLRoom = {
    val originalChannelId = getOriginalChannelId(ctx, room.channel.compositeChannelId)
    val newPrices = indexedYplPriceForRoom.map { case (idx, p) =>
      val priceResultOpt = indexedResultPriceForRoom.get(idx)
      priceResultOpt match {
        case Some(priceResult) =>
          val rateType = priceResult.rateType
          val priceAfterDeltaReduction = priceResult.finalPrice
          val deltaInfo = priceResult.deltaInfo
          val bookingPriceBreakdown =
            p.getUpdatedPriceBreakdown().copy(isAPMDiscountStep = true, apmDiscountAmount = deltaInfo.delta)
          val adjustedPrice =
            if (ctx.experimentContext.isPropertyB(propertyContext, ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION)) {
              calculatePriceForPriceCalculationRefactor(
                paymentModel = room.paymentModel,
                date = p.date,
                commissionPercent = room.marginPercentage,
                agxCommission =
                  p.agxCommission, // Here we are passing from YPLPrice which is first set in initial pricecalculator flow so no need to pass value from commissionholder
                commissionExcludingWholesaleOrAgx = getCommissionExcludingAgxAndWholesale(room, p.date),
                hotelTaxInfo = hotelTaxInfo,
                dailyTaxes = p.dailyTaxes,
                reqOcc = toCommonReqOccByHotelAgePolicy(
                  YplReqOccByHotelAgePolicy(ctx.request.occ,
                                            reqOccByHotelAgePolicy.agePolicy,
                                            reqOccByHotelAgePolicy.ignoreRequestedNumberOfRoomsForNha)),
                chargeType = p.chargeType,
                quantity = p.quantity,
                applyType = p.applyType,
                chargeOption = p.chargeOption,
                promoDiscount = ApmPricingFlow.BD_ZERO,
                valueWithChannelDiscount = priceAfterDeltaReduction,
                discountMessages = Map.empty, // making discountMessages=Map.empty will remove promo badge on Front-end
                roomPriceInfo = RoomPriceInfo(
                  channel = adjustmentChannelId,
                  rateType = rateType,
                  originalRateType = room.originalRateType,
                  roomOcc = room.occ,
                  processingFeePercent = room.processingFeePercent,
                ),
                supplierId = room.supplierId,
                subChargeType = p.subChargeType,
                roomNo = p.roomNumber,
                supplierContractedCommissionFromCommissionHolder =
                  room.yplRoomEntry.commissionHolder.supplierContractedCommission,
                channelDiscountBreakdowns = Nil,
                resellRefSell = p.resellRefSell,
                currentBreakdownStep =
                  BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, false, p.currentBreakdownStep),
                bookingPriceBreakdown = bookingPriceBreakdown,
                apmPriceAdjustmentDetail = p.apmPriceAdjustmentDetail,
                apmCommissionDiscountPercent = p.apmCommissionDiscountPercent,
                childAgeRangeId = p.childAgeRangeId,
                hotelId = room.hotelId,
                chainId = chainId,
                countryId = countryId,
                cityId = cityId,
                supplierFundedDiscountAmount = None,
                uspaDiscountAmount = None,
                uspaProgramId = None,
                isApplyTaxOnSellEx = ctx.isApplyTaxOnSellEx(room.supplierId,
                                                            room.hotelId,
                                                            chainId,
                                                            countryId,
                                                            room.paymentModel.i,
                                                            room.originalRateType),
                lengthOfStay = ctx.request.lengthOfStay,
                storefrontId = ctx.request.cInfo.storeFront.getOrElse(0),
                whitelabelId = ctx.request.whitelabelSetting.whitelabelID,
                isThirdPartySupplier = ctx.isThirdParty(room.supplierId),
                applyTaxOverHelper = ctx.request.applyTaxOverHelper,
                experimentContext = ctx.experimentContext,
              )
            } else {
              calculatePrice(
                paymentModel = room.paymentModel,
                date = p.date,
                commissionPercent = room.marginPercentage,
                agxCommission =
                  p.agxCommission, // Here we are passing from YPLPrice which is first set in initial pricecalculator flow so no need to pass value from commissionholder
                commissionExcludingWholesaleOrAgx = getCommissionExcludingAgxAndWholesale(room, p.date),
                hotelTaxInfo = hotelTaxInfo,
                dailyTaxes = p.dailyTaxes,
                reqOcc = YplReqOccByHotelAgePolicy(ctx.request.occ,
                                                   reqOccByHotelAgePolicy.agePolicy,
                                                   reqOccByHotelAgePolicy.ignoreRequestedNumberOfRoomsForNha),
                chargeType = p.chargeType,
                quantity = p.quantity,
                applyType = p.applyType,
                chargeOption = p.chargeOption,
                promoDiscount = ApmPricingFlow.BD_ZERO,
                valueWithChannelDiscount = priceAfterDeltaReduction,
                discountMessages = Map.empty, // making discountMessages=Map.empty will remove promo badge on Front-end
                roomPriceInfo = RoomPriceInfo(
                  channel = adjustmentChannelId,
                  rateType = rateType,
                  originalRateType = room.originalRateType,
                  roomOcc = room.occ,
                  processingFeePercent = room.processingFeePercent,
                ),
                supplierId = room.supplierId,
                subChargeType = p.subChargeType,
                roomNo = p.roomNumber,
                supplierContractedCommissionFromCommissionHolder =
                  room.yplRoomEntry.commissionHolder.supplierContractedCommission,
                channelDiscountBreakdowns = Nil,
                resellRefSell = p.resellRefSell,
                currentBreakdownStep =
                  BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown, false, p.currentBreakdownStep),
                bookingPriceBreakdown = bookingPriceBreakdown,
                apmPriceAdjustmentDetail = p.apmPriceAdjustmentDetail,
                apmCommissionDiscountPercent = p.apmCommissionDiscountPercent,
                childAgeRangeId = p.childAgeRangeId,
                hotelId = room.hotelId,
                chainId = chainId,
                countryId = countryId,
                supplierFundedDiscountAmount = None,
                uspaDiscountAmount = None,
                uspaProgramId = None,
              )(room.supplierId == DMC.BCOM)
            }

          val apmPriceAdjustmentDetail = ApmPriceAdjustmentDetail(
            cheapestPrice = deltaInfo.cheapestPrice,
            marketPrice = deltaInfo.marketPrice,
            delta = deltaInfo.delta,
            originalPrice = priceResult.originalPrice,
            marketPriceDiscountPercent = deltaInfo.priceDiscountPercent,
            maximumDeltaPercent = deltaInfo.maximumDeltaPercent,
            isPartialAdjustment = Some(deltaInfo.isPartialAdjustment),
            approvalPriceId = deltaInfo.apmApprovalPriceId,
            adjustmentRateType = rateType,
          )
          adjustedPrice.copy(apmPriceAdjustmentDetail = Some(apmPriceAdjustmentDetail))
        case _ => p
      }
    }.toList
    room.copy(
      prices = newPrices,
      channel = adjustmentChannelId,
      apmPriceAdjustmentSetting = Some(apmPriceAdjustmentSetting),
      originalChannelId = originalChannelId,
      fences = fences.map(f => YplRateFence(f.origin, f.cid, f.language)),
      isMADO = isMADO,
      discountInfo = DiscountInfo(),
    )
  }
  case class ApmRoomsWithLogInfo(cheapestRoom: Option[YPLRoom],
                                 isSellExAdjustment: Boolean,
                                 apmPriceDailyMap: Map[DateTime, AutoPriceMatchPriceInfo],
                                 roomPriceDeltaMap: Map[(DateTime, ChargeType), CalculatedDeltaInfo])

  def logApmRoomResultPerCheapestRoom(newApmRoomsWithLogInfo: List[(ApmRoomsWithLogInfo, YPLRoom)], metaData: HotelMeta)(
    ctx: YplContext) = {
    val apmRoomsByCheapest = newApmRoomsWithLogInfo.groupBy { case (cheapestRoom, _) => cheapestRoom }
    apmRoomsByCheapest.foreach {
      case (ApmRoomsWithLogInfo(Some(cheapestRoomFromRoomTypeAndOcc),
                                isSellExAdjustment,
                                apmPriceDailyMap,
                                roomPriceDeltaMap),
            apmRooms) =>
        val apmCheapestRoomLog = RoomInfo(
          roomTypeId = cheapestRoomFromRoomTypeAndOcc.roomTypeId,
          ratePlanId = cheapestRoomFromRoomTypeAndOcc.rateCategoryId,
          channelId = cheapestRoomFromRoomTypeAndOcc.channel.baseChannelId,
          paymentModelId = cheapestRoomFromRoomTypeAndOcc.paymentModel.i,
          dateRoomPrices = cheapestRoomFromRoomTypeAndOcc.roomPrices.map(roomPrice =>
            DateRoomPrice(
              date = roomPrice.date.toString,
              sellExclusive = roomPrice.sellExclusive,
              sellInclusive = roomPrice.sellInclusive,
              apmApprovalPriceId = roomPrice.apmPriceAdjustmentDetail.map(_.approvalPriceId),
            )),
          totalSellInclusive = cheapestRoomFromRoomTypeAndOcc.sellIn,
          appliedDelta = Seq.empty,
          totalAppliedDelta = ApmPricingFlow.BD_ZERO,
          totalPrice =
            if (isSellExAdjustment) cheapestRoomFromRoomTypeAndOcc.sellEx else cheapestRoomFromRoomTypeAndOcc.sellIn,
        )
        val apmRoomsLog = apmRooms.map { case (_, apmRoom) =>
          val appliedDelta = apmRoom.prices.flatMap(price =>
            price.apmPriceAdjustmentDetail.map(apmDetail =>
              DateDelta(date = price.date.toString,
                        deltaPrice = apmDetail.delta,
                        apmApprovalPriceId = apmDetail.approvalPriceId,
                        apmRoomTypeId = apmRoom.roomTypeId)))
          RoomInfo(
            roomTypeId = apmRoom.roomTypeId,
            ratePlanId = apmRoom.rateCategoryId,
            channelId = apmRoom.channel.baseChannelId,
            paymentModelId = apmRoom.paymentModel.i,
            dateRoomPrices = apmRoom.roomPrices.map(roomPrice =>
              DateRoomPrice(
                date = roomPrice.date.toString,
                sellExclusive = roomPrice.sellExclusive,
                sellInclusive = roomPrice.sellInclusive,
                apmApprovalPriceId = roomPrice.apmPriceAdjustmentDetail.map(_.approvalPriceId),
              )),
            totalSellInclusive = apmRoom.sellIn,
            appliedDelta = appliedDelta,
            totalAppliedDelta = appliedDelta.getDSum(_.deltaPrice),
            totalPrice = if (isSellExAdjustment) apmRoom.sellEx else apmRoom.sellIn,
          )
        }
        val apmLog = ApmAdjustmentsMessage(
          searchId = ctx.request.searchId,
          hotelId = metaData.hotelId,
          checkIn = ctx.request.checkIn.toString,
          checkOut = ctx.request.checkOut.toString,
          occupancy = ctx.request.occ.adults,
          currency = metaData.ycsHotelCurrency.getOrElse("NONE"),
          competitorDateRoomPrices = apmPriceDailyMap
            .map(apmPrice =>
              DateRoomPrice(
                date = apmPrice._1.toString,
                sellExclusive = ApmPricingFlow.BD_ZERO,
                sellInclusive = apmPrice._2.marketPrice,
                apmApprovalPriceId = Some(apmPrice._2.apmApprovalPriceId),
              ))
            .toSeq,
          cheapestRoom = apmCheapestRoomLog,
          allDelta = roomPriceDeltaMap
            .map(roomPriceDelta =>
              DateDelta(
                date = roomPriceDelta._1._1.toString,
                deltaPrice = roomPriceDelta._2.delta,
                apmApprovalPriceId = roomPriceDelta._2.apmApprovalPriceId,
                apmRoomTypeId = roomPriceDelta._2.apmRoomTypeId,
              ))
            .toSeq,
          adjustedRooms = apmRoomsLog,
          isSellExAdjustment = isSellExAdjustment,
        )
        ctx.hadoopContext.logHadoopMessage(apmLog)
    }
  }

  def processApmPriceAdjustment(hotel: YPLHotel, metaData: HotelMeta, dispatchChannels: YplDispatchChannels)(implicit
    ctx: YplContext): (YPLHotel, Int) = {
    implicit val propertyContext: PropertyContext = metaData.toPropertyContext
    val mapping = buildRoomAndPriceIndex(hotel)
    val apmPricingRoomParametersMap = mapping.map { case (roomIndex, (param, _, _)) => roomIndex -> param }
    val roomIndexToPricesAndRoom: RoomIndexedValue[(YPLRoom, List[(RoomPriceIndex, YplPrice)])] = mapping.map {
      case (roomIndex, (_, indexedPrices, room)) => roomIndex -> (room, indexedPrices)
    }(collection.breakOut)
    val (apmPriceAdjustmentResult, notUseReasonLogList) = calculateApmPriceAdjustmentResult(
      apmRoomParams = apmPricingRoomParametersMap,
      hotelId = hotel.id,
      supplierId = hotel.supplierId,
      countryId = hotel.countryId,
      dispatchChannels = dispatchChannels.masterChannels.map(_.baseChannelId),
      bookingDateTime = ctx.request.bookingDate,
      removeBedPaidExclusionFromApmExp = ctx.experimentContext.isUserB(REMOVE_BEDS_PAID_EXCLUSION_FROM_APM),
      skipApmPriceAdjustmentForResellExp = ctx.experimentContext.isUserB(SKIP_APM_PRICE_ADJUSTMENT_FOR_RESELL),
      enabledAiForBedNetwork = ctx.experimentContext.isUserB(APM_ENABLE_AI_FOR_BED_NETWORK),
      enableApmMultipleDiscount = ctx.experimentContext.isUserB(APM_MULTIPLE_DISCOUNT),
      excludeRateChannelFromApmExp = ctx.experimentContext.isUserB(APM_EXCLUDE_RATE_CHANNEL_IDS),
      isApmFixEndDate = ctx.experimentContext.isUserB(APM_FIX_END_DATE),
      enabledApmArpPlusProgram = ctx.experimentContext.isUserB(APM_ARP_PLUS_PROGRAM),
      requestFences = ctx.request.fences.map { case (channel, rateFences) =>
        channel.baseChannelId -> rateFences.map(_.toCommissionModel)
      },
    )

    val newApmRooms = for {
      (roomIndex, result) <- apmPriceAdjustmentResult
      (room, prices) <- roomIndexToPricesAndRoom.get(roomIndex)
    } yield {
      val apmRoom = buildApmRoomV2(
        room = room,
        chainId = hotel.chainId,
        countryId = hotel.countryId,
        cityId = hotel.cityId,
        indexedYplPriceForRoom = prices.toMap,
        indexedResultPriceForRoom = result.prices.toMap,
        hotelTaxInfo = hotel.hotelTaxInfo,
        reqOccByHotelAgePolicy = hotel.reqOcc,
        apmPriceAdjustmentSetting = result.apmRoomChannelSetting,
        adjustmentChannelId = YplMasterChannel(result.adjustmentChannelId),
        fences = result.fences,
        isMADO = result.isMADO,
      )

      val logInfo = ApmRoomsWithLogInfo(
        roomIndexToPricesAndRoom.get(result.cheapestRoomIndexAndUid._1).map(_._1),
        result.isSellExAdjustment,
        result.apmPriceDailyMap,
        result.roomPriceDeltaMap,
      )

      (logInfo, apmRoom)
    }
    notUseReasonLogList.foreach(log =>
      ctx.hadoopContext.logHadoopMessage(
        ApmApprovalPriceNotUseReasonLog(
          searchId = ctx.request.searchId,
          apmApprovalPriceId = log.apmApprovalPriceId,
          reason = log.reason,
        )))

    val grouped = newApmRooms.groupBy { case (_, room) =>
      (room.fences,
       room.roomTypeId,
       room.paymentModel,
       room.allPromotions,
       room.channel,
       room.rateCategoryId,
       room.occupancy)
    }
    val dedupApmRoom = grouped
      .map { case (key, roomWithInfos) =>
        key -> roomWithInfos.minBy { case (_, room) => room.prices.getDSum(_.value, _.isEligiblePriceForApm) }
      }
      .values
      .toList
    logApmRoomResultPerCheapestRoom(dedupApmRoom, metaData)(ctx)
    (hotel.copy(rooms = hotel.rooms ++ dedupApmRoom.map { case (_, room) => room }), hotel.rooms.length)
  }

  /**
    * @param hotel YPLHotel, in this function, we would like to add external data into the YPLHotel.rooms
    * @return A YPLHotel case class with a revised list of YPLHotel.rooms
    */
  def processApmExternalData(hotel: YPLHotel,
                             apmLeadingRoomAdjustmentIds: Seq[Int] = Seq.empty,
                             apmConfigs: Map[ApmConfigTypeId, ApmConfigHolder] = Map.empty)(implicit
    ctx: YplContext): YPLHotel = {
    val roomsWithExternalData = hotel.rooms.map { room =>
      val isApmHotelProgramActive = room.isActiveAndEnableCommission
      val isApmHotelActive = isApmHotelProgramActive && isSupplierIdValid(hotel.supplierId)
      val externalData = buildApmExternalData(
        room.currency,
        hotel.autoPriceMatchIdsHash,
        room.prices,
        isApmHotelActive,
        room.apmPriceAdjustmentSetting,
        room.apmCommissionDiscountSetting,
        apmLeadingRoomAdjustmentIds,
        apmConfigs,
        room.originalChannelId,
        room.isMADO,
        room.fences,
      )

      room.copy(apmExternalData = Some(externalData))
    }
    hotel.copy(rooms = roomsWithExternalData)
  }

  private def getOriginalChannelId(ctx: YplContext, compositeChannelId: Int): Option[Int] =
    if (ctx.experimentContext.isUserB(ADD_ORIGINAL_CHANNEL_ID_FOR_AI_BOOKING)) Some(compositeChannelId)
    else None

  private def buildApmExternalData(currency: Currency,
                                   autoPriceMatchIdsHash: Option[String],
                                   prices: List[YplPrice],
                                   isApmHotelActive: Boolean,
                                   apmPriceAdjustmentSetting: Option[MultipleAutoPriceMatchHolder],
                                   apmCommissionDiscountSetting: Option[MultipleAutoPriceMatchHolder],
                                   apmLeadingRoomAdjustmentIds: Seq[Int],
                                   allConfigs: Map[ApmConfigTypeId, ApmConfigHolder],
                                   originalChannelId: Option[Int],
                                   isMADO: Option[Boolean],
                                   rateFence: Set[YplRateFence])(implicit ctx: YplContext): ApmExternalData = {
    val yplApmPriceAdjustmentDetail = prices.flatMap { price =>
      price.apmPriceAdjustmentDetail.map { adjDetail =>
        adjDetail.adjustmentRateType match {
          case RateType.SellExclusive => ApmPriceAdjustmentExternalData(
              date = price.date,
              chargeType = price.chargeType,
              currency = currency,
              cheapestSellIn = 0,
              marketSellIn = 0,
              delta = adjDetail.delta,
              originalSellIn = 0,
              marketPriceDiscountPercent = adjDetail.marketPriceDiscountPercent,
              maximumDeltaPercent = adjDetail.maximumDeltaPercent,
              approvalPriceId = adjDetail.approvalPriceId,
              isPartialAdjustment = adjDetail.isPartialAdjustment,
              cheapestPriceLocal = adjDetail.cheapestPrice,
              marketPriceLocal = adjDetail.marketPrice,
              originalPriceLocal = adjDetail.originalPrice,
              adjustmentRateType = adjDetail.adjustmentRateType,
            )
          case _ =>
            ApmPriceAdjustmentExternalData( // when rate type is don't match with the exclusive will map with inclusive mapping
              date = price.date,
              chargeType = price.chargeType,
              currency = currency,
              cheapestSellIn = adjDetail.cheapestPrice, // backward compatible
              marketSellIn = adjDetail.marketPrice, // backward compatible
              delta = adjDetail.delta,
              originalSellIn = adjDetail.originalPrice, // backward compatible
              marketPriceDiscountPercent = adjDetail.marketPriceDiscountPercent,
              maximumDeltaPercent = adjDetail.maximumDeltaPercent,
              approvalPriceId = adjDetail.approvalPriceId,
              isPartialAdjustment = adjDetail.isPartialAdjustment,
              cheapestPriceLocal = adjDetail.cheapestPrice,
              marketPriceLocal = adjDetail.marketPrice,
              originalPriceLocal = adjDetail.originalPrice,
              adjustmentRateType = adjDetail.adjustmentRateType,
            )
        }
      }
    }
    val yplPriceCommission = prices.map { price =>
      ApmPriceCommissionExternalData(
        date = price.date,
        chargeType = price.chargeType,
        chargeOption = price.chargeOption,
        referenceCommissionPercent = price.referenceCommissionPercent,
        commissionDiscountPercent = price.apmCommissionDiscountPercent,
      )
    }
    ApmExternalData(
      isApmHotelActive = isApmHotelActive,
      isMADO = isMADO,
      adjustmentIdsHash = autoPriceMatchIdsHash.getOrElse(""),
      priceAdjustmentDetails = yplApmPriceAdjustmentDetail,
      priceCommission = yplPriceCommission,
      priceAdjustmentProgram = apmPriceAdjustmentSetting.getOrElse(ApmPricingFlow.NO_PROGRAM),
      commissionDiscountProgram = apmCommissionDiscountSetting.getOrElse(ApmPricingFlow.NO_PROGRAM),
      apmLeadingRoomAdjustmentIds = apmLeadingRoomAdjustmentIds,
      additionalCommRedHotelLevel =
        APMHelper.getAdditionalCommissionReductionByConfigLevel(allConfigs, ApmConfigLevel.Hotel.value),
      originalChannelId = originalChannelId,
      rateFence = rateFence,
    )
  }

  private[logic] def processApmCommissionDiscount(r: Data[Option[YplRoomsWithEntry]]): Option[YplRoomsWithEntry] =
    r.data.map { roomsWithEntry =>
      val entry = roomsWithEntry.entry
      val roomsWithIndex = roomsWithEntry.rooms.zipWithIndex
      val roomUIDToApmCommissionRoomParameters = roomsWithIndex.map { case (room, index) =>
        index -> ApmCommissionDiscountRoomParameters(
          room.supplierId,
          room.marginPercentage,
          room.channel.toCompositeChannelIds,
          room.stayPackageType,
          room.resellExternalData.isEmpty,
          room.hourlyAvailableSlots.isEmpty,
          room.yplRoomEntry.commissionHolder.apmCommissionHolder,
          room.fences.map(_.toCommissionModel),
        )
      }.toMap

      val apmCommissionMap = calculateApmCommissionReductionResult(
        apmRoomParams = roomUIDToApmCommissionRoomParameters,
        bookingDate = r.ctx.request.bookingDate,
        hotelGmtOffset = entry.metaData.gmtOffset,
        dispatchedMasterChannels = entry.dispatchChannels.masterChannels.map(_.baseChannelId),
        hotelSupplierId = entry.supplierId,
        hotelId = entry.metaData.hotelId,
        hotelCountryId = entry.metaData.countryId,
        priceDates = roomsWithEntry.rooms.flatMap(_.prices.map(_.date)),
        removeBedPaidExclusionFromApmExp = r.ctx.experimentContext.isUserB(REMOVE_BEDS_PAID_EXCLUSION_FROM_APM),
        skipApmPriceAdjustmentForResellExp = r.ctx.experimentContext.isUserB(SKIP_APM_PRICE_ADJUSTMENT_FOR_RESELL),
        enabledAiForBedNetwork = r.ctx.experimentContext.isUserB(APM_ENABLE_AI_FOR_BED_NETWORK),
        enableApmMultipleDiscount = r.ctx.experimentContext.isUserB(APM_MULTIPLE_DISCOUNT),
        excludeRateChannelFromApmExp = r.ctx.experimentContext.isUserB(APM_EXCLUDE_RATE_CHANNEL_IDS),
        isApmFixEndDate = r.ctx.experimentContext.isUserB(APM_FIX_END_DATE),
        enabledApmArpPlusProgram = r.ctx.experimentContext.isUserB(APM_ARP_PLUS_PROGRAM),
        requestFences = r.ctx.request.fences.map { case (channel, rateFences) =>
          channel.baseChannelId -> rateFences.map(_.toCommissionModel)
        },
      )

      val apmAppliedRooms = roomsWithIndex.map { case (room, index) =>
        apmCommissionMap.get(index) match {
          case Some(result) =>
            val roomWithApmSettingsAssigned =
              if (result.isSupplierValid) {
                room.copy(apmCommissionDiscountSetting = result.commissionDiscountChannelSetting)
              } else room

            if (r.ctx.experimentContext.isUserB(APM_COMMISSION_DISCOUNT_LOG)) {
              buildApmCommissionDiscountLog(r.ctx,
                                            room,
                                            result,
                                            entry.dispatchChannels.masterChannels.map(_.baseChannelId))
                .foreach(r.ctx.hadoopContext.logHadoopMessage)
            }

            if (result.isRoomEligible) applyApmCommissionDiscount(roomWithApmSettingsAssigned, entry, result)(r.ctx)
            else roomWithApmSettingsAssigned
          case _ =>
            logger.warn("APM Commission result should contain every original rooms")
            room
        }
      }

      sendCommissionReductionMetrics(r.ctx, roomsWithEntry, apmAppliedRooms)
      roomsWithEntry.copy(rooms = apmAppliedRooms)
    }

  private[logic] def applyApmCommissionDiscount(room: YPLRoom,
                                                hotelEntry: YplHotelEntryModel,
                                                apmCommissionResult: ApmCommissionDiscountResult)(implicit
    ctx: YplContext): YPLRoom = {
    implicit val propertyContext: PropertyContext = hotelEntry.metaData.toPropertyContext
    val apmCommissionDiscountPercent = apmCommissionResult.apmDiscountPercent
    val finalCommissionPercent = apmCommissionResult.finalCommissionPercent
    val commissionDiscountChannelSetting = apmCommissionResult.commissionDiscountChannelSetting

    val pricesWithApmCommissionDiscounted = room.prices.map { price =>
      val canApplyApmCommissionDiscount = apmCommissionResult.canApplyDiscount(price.date, price.chargeType)
      // extra commission reduction at hotel level
      if (canApplyApmCommissionDiscount) {
        val rateType = RateType.SellInclusive
        val bookingPriceBreakdown = price
          .getUpdatedPriceBreakdown()
          .copy(isAPMCommissionDiscountStep = true, apmCommissionDiscountPercent = apmCommissionDiscountPercent)
        val commDiscountedPrice =
          if (ctx.experimentContext.isPropertyB(propertyContext, ABTest.REFACTOR_PRICE_CALCULATION_FUNCTION)) {
            val roomPriceInfo = room.toRoomPriceInfo.copy(rateType = rateType)
            calculatePriceForPriceCalculationRefactor(
              paymentModel = room.paymentModel,
              date = price.date,
              commissionPercent = finalCommissionPercent,
              agxCommission =
                price.agxCommission, // Here we are passing from YPLPrice which is first set in initial pricecalculator flow so no need to pass value from commissionholder
              commissionExcludingWholesaleOrAgx = getCommissionExcludingAgxAndWholesale(room, price.date),
              hotelTaxInfo = hotelEntry.taxInfo.hotelTaxInfo,
              dailyTaxes = price.dailyTaxes,
              reqOcc = toCommonReqOccByHotelAgePolicy(
                YplReqOccByHotelAgePolicy(ctx.request.occ,
                                          hotelEntry.reqOcc.agePolicy,
                                          hotelEntry.reqOcc.ignoreRequestedNumberOfRoomsForNha)),
              chargeType = price.chargeType,
              quantity = price.quantity,
              applyType = price.applyType,
              chargeOption = price.chargeOption,
              promoDiscount = ApmPricingFlow.BD_ZERO,
              valueWithChannelDiscount = price.sellInclusive, // We will fix Sell Inclusive amount
              discountMessages = price.discountMessages,
              roomPriceInfo = roomPriceInfo,
              supplierId = room.supplierId,
              subChargeType = price.subChargeType,
              roomNo = price.roomNumber,
              supplierContractedCommissionFromCommissionHolder =
                room.yplRoomEntry.commissionHolder.supplierContractedCommission,
              channelDiscountBreakdowns = price.channelDiscounts,
              resellRefSell = price.resellRefSell,
              currentBreakdownStep = BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown,
                                                                                   isChannelDiscountStep = false,
                                                                                   price.currentBreakdownStep),
              bookingPriceBreakdown = bookingPriceBreakdown,
              apmPriceAdjustmentDetail = price.apmPriceAdjustmentDetail,
              apmCommissionDiscountPercent = price.apmCommissionDiscountPercent,
              childAgeRangeId = price.childAgeRangeId,
              hotelId = room.hotelId,
              chainId = hotelEntry.metaData.chainId,
              countryId = hotelEntry.metaData.countryId,
              cityId = hotelEntry.metaData.cityId,
              supplierFundedDiscountAmount = None,
              uspaDiscountAmount = None,
              uspaProgramId = None,
              isApplyTaxOnSellEx = ctx.isApplyTaxOnSellEx(room.supplierId,
                                                          room.hotelId,
                                                          hotelEntry.metaData.chainId,
                                                          hotelEntry.metaData.countryId,
                                                          room.paymentModel.i,
                                                          roomPriceInfo.originalRateType),
              lengthOfStay = ctx.request.lengthOfStay,
              storefrontId = ctx.request.cInfo.storeFront.getOrElse(0),
              whitelabelId = ctx.request.whitelabelSetting.whitelabelID,
              isThirdPartySupplier = ctx.isThirdParty(room.supplierId),
              applyTaxOverHelper = ctx.request.applyTaxOverHelper,
              experimentContext = ctx.experimentContext,
            )
          } else {
            calculatePrice(
              paymentModel = room.paymentModel,
              date = price.date,
              commissionPercent = finalCommissionPercent,
              agxCommission =
                price.agxCommission, // Here we are passing from YPLPrice which is first set in initial pricecalculator flow so no need to pass value from commissionholder
              commissionExcludingWholesaleOrAgx = getCommissionExcludingAgxAndWholesale(room, price.date),
              hotelTaxInfo = hotelEntry.taxInfo.hotelTaxInfo,
              dailyTaxes = price.dailyTaxes,
              reqOcc = YplReqOccByHotelAgePolicy(ctx.request.occ,
                                                 hotelEntry.reqOcc.agePolicy,
                                                 hotelEntry.reqOcc.ignoreRequestedNumberOfRoomsForNha),
              chargeType = price.chargeType,
              quantity = price.quantity,
              applyType = price.applyType,
              chargeOption = price.chargeOption,
              promoDiscount = ApmPricingFlow.BD_ZERO,
              valueWithChannelDiscount = price.sellInclusive, // We will fix Sell Inclusive amount
              discountMessages = price.discountMessages,
              roomPriceInfo = room.toRoomPriceInfo.copy(rateType = rateType),
              supplierId = room.supplierId,
              subChargeType = price.subChargeType,
              roomNo = price.roomNumber,
              supplierContractedCommissionFromCommissionHolder =
                room.yplRoomEntry.commissionHolder.supplierContractedCommission,
              channelDiscountBreakdowns = price.channelDiscounts,
              resellRefSell = price.resellRefSell,
              currentBreakdownStep = BookingPriceBreakdown.getCurrentBreakdownStep(bookingPriceBreakdown,
                                                                                   isChannelDiscountStep = false,
                                                                                   price.currentBreakdownStep),
              bookingPriceBreakdown = bookingPriceBreakdown,
              apmPriceAdjustmentDetail = price.apmPriceAdjustmentDetail,
              apmCommissionDiscountPercent = price.apmCommissionDiscountPercent,
              childAgeRangeId = price.childAgeRangeId,
              hotelId = room.hotelId,
              chainId = hotelEntry.metaData.chainId,
              countryId = hotelEntry.metaData.countryId,
              supplierFundedDiscountAmount = None,
              uspaDiscountAmount = None,
              uspaProgramId = None,
            )(room.supplierId == DMC.BCOM)
          }
        commDiscountedPrice.copy(apmCommissionDiscountPercent = apmCommissionDiscountPercent)
      } else {
        price
      }
    }

    val marginPercentage = calculateCommission(pricesWithApmCommissionDiscounted, room.marginPercentage)
    room.copy(marginPercentage = marginPercentage,
              prices = pricesWithApmCommissionDiscounted,
              apmCommissionDiscountSetting = commissionDiscountChannelSetting)
  }
}
// scalastyle:on
