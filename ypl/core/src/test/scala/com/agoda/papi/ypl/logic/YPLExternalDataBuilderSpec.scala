package com.agoda.papi.ypl.logic

import com.agoda.papi.enums.room.RateType.SellInclusive
import com.agoda.papi.enums.room.{ChargeOption, ChargeType, ChildRateType, RateType}
import com.agoda.papi.ypl.commission.apm.models.{
  ApmAdjustmentDiscount,
  ApmCommissionDiscount,
  ApmHotelStatus,
  ApmProgramType,
  MultipleAutoPriceMatchHolder,
}
import com.agoda.papi.ypl.models.{
  ApmExternalData,
  ApmPriceAdjustmentExternalData,
  ApmPriceCommissionExternalData,
  DmcDataHolder,
  ResellExternalData,
  YPLExternalData,
  YPLRoomAllocation,
  YPLTestDataBuilders,
  YplMasterChannel,
}
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.core.Fragments
import spray.json._

class YPLExternalDataBuilderSpec extends SpecificationWithJUnit with YPLTestDataBuilders {

  implicit val compositeChannelContext = aValidCompositeChannelContext

  val apmApprovalPriceIdPool = 1L to 6L

  "getRoomStatus" should {
    "returns correct rooms status" in {
      Fragments(
        List(
          (ApmProgramType.Ai, ApmHotelStatus.Active, true),
          (ApmProgramType.Ai, ApmHotelStatus.InActive, false),
          (ApmProgramType.Ai, ApmHotelStatus.Suspended, true),
          (ApmProgramType.Arp, ApmHotelStatus.Experimental, true),
          (ApmProgramType.Arp, ApmHotelStatus.InActive, false),
          (ApmProgramType.ArpV2, ApmHotelStatus.ExperimentalV2, true),
          (ApmProgramType.ArpV2, ApmHotelStatus.InActive, false),
          (ApmProgramType.PeakSeason, ApmHotelStatus.PeakSeason, true),
          (ApmProgramType.PeakSeason, ApmHotelStatus.InActive, false),
        ).map { case (programTypeId, statusId, expected) =>
          s"programTypeId is $programTypeId and statusId is $statusId, result should be $expected" in {
            val mockApmSetting = aValidMultipleAutoPriceMatch.withProgramType(programTypeId).withStatusId(statusId)

            val mockYPLRoom =
              aValidRoom.withApmPriceAdjustmentSetting(mockApmSetting).withApmCommissionDiscountSetting(mockApmSetting)

            val service = new YPLExternalDataBuilder {}
            val result = service.getRoomStatus(mockYPLRoom)
            result should_== expected
          }
        }: _*,
      )
    }
  }

  "YPLPostProcess" should {
    val service = new YPLExternalDataBuilder {}
    val yplExternalData = YPLExternalData(
      searchId = "id",
      hotelCode = "hotelcode",
      ratePlanCode = Some("rateplancode"),
      roomTypeCode = Some("roomtypecode"),
      currencyCode = "currencycode",
      maxOcc = 1,
      maxChild = 1,
      maxExtraBed = 1,
      srcOcc = 1,
      roomAllocationInfo = Map(1 -> YPLRoomAllocation(1, 0, Map(ChildRateType.PreSchool -> 1)),
                               2 -> YPLRoomAllocation(2, 1, Map(ChildRateType.GradeSchool -> 2))),
      inventoryType = 1,
      linkedRoomTypeCode = Some("A4"),
    )

    val yplExternalDataWithEmpty = YPLExternalData(
      searchId = "id",
      hotelCode = "",
      ratePlanCode = None,
      roomTypeCode = None,
      currencyCode = "",
      maxOcc = 1,
      maxChild = 1,
      maxExtraBed = 1,
      srcOcc = 1,
      roomAllocationInfo = Map.empty,
      inventoryType = 0,
      checkInStartTime = Some("08:00:00"),
      checkInEndTime = Some("23:00:00"),
    )

    val dmcDataHolder = DmcDataHolder(yplExternalData = Some(yplExternalData))
    val price = aValidPrice.withQuantity(2).withNetExclusive(1000.5)
    val room = aValidRoom.withDmcDataHolder(Some(dmcDataHolder)).withPrice(price)

    val dmcDataHolderE = DmcDataHolder(yplExternalData = Some(yplExternalDataWithEmpty))
    val priceE = aValidPrice
    val roomE = aValidRoom.withOriginalRateType(SellInclusive).withDmcDataHolder(Some(dmcDataHolderE)).withPrice(priceE)

    val apmExternalDataWithPartialAdjustment = ApmExternalData(
      isApmHotelActive = true,
      adjustmentIdsHash = "hashed-adjustmentIdsHash-hashed",
      priceAdjustmentDetails = Seq(
        ApmPriceAdjustmentExternalData(
          DateTime.parse("2021-01-22T00:00:00.000+07:00"),
          ChargeType.Room,
          "THB",
          cheapestSellIn = 10.567d,
          marketSellIn = 9d,
          delta = 2.0170000393d,
          originalSellIn = 10.567d,
          marketPriceDiscountPercent = 5d,
          maximumDeltaPercent = 20d,
          isPartialAdjustment = Some(false),
          approvalPriceId = apmApprovalPriceIdPool(0),
          cheapestPriceLocal = 10.567d,
          marketPriceLocal = 9d,
          originalPriceLocal = 10.567d,
          adjustmentRateType = RateType.SellInclusive,
        ),
        ApmPriceAdjustmentExternalData(
          DateTime.parse("2021-01-23T00:00:00.000+07:00"),
          ChargeType.Room,
          "THB",
          cheapestSellIn = 8d,
          marketSellIn = 8d,
          delta = 1d,
          originalSellIn = 8d,
          marketPriceDiscountPercent = 5d,
          maximumDeltaPercent = 20d,
          isPartialAdjustment = Some(true),
          approvalPriceId = apmApprovalPriceIdPool(1),
          cheapestPriceLocal = 8d,
          marketPriceLocal = 8d,
          originalPriceLocal = 5d,
          adjustmentRateType = RateType.SellInclusive,
        ),
      ),
      priceCommission = Seq(
        ApmPriceCommissionExternalData(DateTime.parse("2021-01-22T00:00:00.000+07:00"),
                                       ChargeType.Room,
                                       ChargeOption.Mandatory,
                                       referenceCommissionPercent = 9d,
                                       commissionDiscountPercent = 1d),
        ApmPriceCommissionExternalData(DateTime.parse("2021-01-23T00:00:00.000+07:00"),
                                       ChargeType.Room,
                                       ChargeOption.Mandatory,
                                       referenceCommissionPercent = 9d,
                                       commissionDiscountPercent = 1d),
      ),
      logicVersion = "",
      priceAdjustmentProgram = ApmPricingFlow.NO_PROGRAM,
      commissionDiscountProgram = ApmPricingFlow.NO_PROGRAM,
      apmLeadingRoomAdjustmentIds = Seq(aValidRoom.roomTypeId.toInt),
      additionalCommRedHotelLevel = 0.78,
      rateFence = Set(aValidRateFence),
    )
    val multipleAutoPriceMatchInfo = MultipleAutoPriceMatchHolder(
      programId = 1,
      commissionDiscountChannelId = Some(1051),
      commissionDiscountPercent = 1d,
      adjustmentChannelId = 1051,
      adjustmentDiscountPercent = 0d,
      statusId = 1,
      startDate = new DateTime("2021-01-01"),
      endDate = Some(new DateTime("2999-12-31")),
      apmAdjustmentDiscount =
        Seq(ApmAdjustmentDiscount(1, 1, Some(5.0d), DateTime.parse("1999-01-01"), DateTime.parse("2999-01-01"))),
      apmCommissionDiscount =
        Seq(ApmCommissionDiscount(1, 1, Some(5.0d), DateTime.parse("1999-01-01"), DateTime.parse("2999-01-01"))),
      programType = Some(1),
    )

    val apmExternalDataWithoutPartialAdjustment = ApmExternalData(
      isApmHotelActive = true,
      adjustmentIdsHash = "hashed-adjustmentIdsHash-hashed",
      priceAdjustmentDetails = Seq(
        ApmPriceAdjustmentExternalData(
          DateTime.parse("2021-01-22T00:00:00.000+07:00"),
          ChargeType.Room,
          "THB",
          cheapestSellIn = 10.567d,
          marketSellIn = 9d,
          delta = 2.0170000393d,
          originalSellIn = 10.567d,
          marketPriceDiscountPercent = 5d,
          maximumDeltaPercent = 20d,
          isPartialAdjustment = None,
          approvalPriceId = apmApprovalPriceIdPool(2),
          cheapestPriceLocal = 10.567d,
          marketPriceLocal = 9d,
          originalPriceLocal = 10.567d,
          adjustmentRateType = RateType.SellInclusive,
        ),
        ApmPriceAdjustmentExternalData(
          DateTime.parse("2021-01-23T00:00:00.000+07:00"),
          ChargeType.Room,
          "THB",
          cheapestSellIn = 8d,
          marketSellIn = 8d,
          delta = 1d,
          originalSellIn = 8d,
          marketPriceDiscountPercent = 5d,
          maximumDeltaPercent = 20d,
          isPartialAdjustment = None,
          approvalPriceId = apmApprovalPriceIdPool(3),
          cheapestPriceLocal = 8d,
          marketPriceLocal = 8d,
          originalPriceLocal = 8d,
          adjustmentRateType = RateType.SellInclusive,
        ),
      ),
      priceCommission = Seq(
        ApmPriceCommissionExternalData(DateTime.parse("2021-01-22T00:00:00.000+07:00"),
                                       ChargeType.Room,
                                       ChargeOption.Mandatory,
                                       referenceCommissionPercent = 9d,
                                       commissionDiscountPercent = 1d),
        ApmPriceCommissionExternalData(DateTime.parse("2021-01-23T00:00:00.000+07:00"),
                                       ChargeType.Room,
                                       ChargeOption.Mandatory,
                                       referenceCommissionPercent = 9d,
                                       commissionDiscountPercent = 1d),
      ),
      logicVersion = "",
      priceAdjustmentProgram = ApmPricingFlow.NO_PROGRAM,
      commissionDiscountProgram = ApmPricingFlow.NO_PROGRAM,
      apmLeadingRoomAdjustmentIds = Seq(aValidRoom.roomTypeId.toInt),
      additionalCommRedHotelLevel = 1,
      rateFence = Set(aValidRateFence),
    )
    val apmExternalDataWithMultipleProgram = ApmExternalData(
      isApmHotelActive = true,
      adjustmentIdsHash = "hashed-adjustmentIdsHash-hashed",
      priceAdjustmentDetails = Seq(
        ApmPriceAdjustmentExternalData(
          DateTime.parse("2021-01-22T00:00:00.000+07:00"),
          ChargeType.Room,
          "THB",
          cheapestSellIn = 10.567d,
          marketSellIn = 9d,
          delta = 2.0170000393d,
          originalSellIn = 10.567d,
          marketPriceDiscountPercent = 5d,
          maximumDeltaPercent = 20d,
          isPartialAdjustment = Some(false),
          approvalPriceId = apmApprovalPriceIdPool(4),
          cheapestPriceLocal = 10.567d,
          marketPriceLocal = 9d,
          originalPriceLocal = 10.567d,
          adjustmentRateType = RateType.SellInclusive,
        ),
        ApmPriceAdjustmentExternalData(
          DateTime.parse("2021-01-23T00:00:00.000+07:00"),
          ChargeType.Room,
          "THB",
          cheapestSellIn = 8d,
          marketSellIn = 8d,
          delta = 1d,
          originalSellIn = 8d,
          marketPriceDiscountPercent = 5d,
          maximumDeltaPercent = 20d,
          isPartialAdjustment = Some(true),
          approvalPriceId = apmApprovalPriceIdPool(5),
          cheapestPriceLocal = 8d,
          marketPriceLocal = 8d,
          originalPriceLocal = 8d,
          adjustmentRateType = RateType.SellInclusive,
        ),
      ),
      priceCommission = Seq(
        ApmPriceCommissionExternalData(DateTime.parse("2021-01-22T00:00:00.000+07:00"),
                                       ChargeType.Room,
                                       ChargeOption.Mandatory,
                                       referenceCommissionPercent = 9d,
                                       commissionDiscountPercent = 1d),
        ApmPriceCommissionExternalData(DateTime.parse("2021-01-23T00:00:00.000+07:00"),
                                       ChargeType.Room,
                                       ChargeOption.Mandatory,
                                       referenceCommissionPercent = 9d,
                                       commissionDiscountPercent = 1d),
      ),
      logicVersion = "",
      priceAdjustmentProgram = MultipleAutoPriceMatchHolder(
        programId = 1,
        commissionDiscountChannelId = Some(1051),
        commissionDiscountPercent = 1d,
        adjustmentChannelId = 1051,
        adjustmentDiscountPercent = 0d,
        statusId = 1,
        startDate = new DateTime("2021-01-01"),
        endDate = Some(new DateTime("2999-12-31")),
        apmAdjustmentDiscount =
          Seq(ApmAdjustmentDiscount(1, 1, Some(5.0d), DateTime.parse("1999-01-01"), DateTime.parse("2999-01-01"))),
        apmCommissionDiscount =
          Seq(ApmCommissionDiscount(1, 1, Some(5.0d), DateTime.parse("1999-01-01"), DateTime.parse("2999-01-01"))),
        programType = Some(1),
      ),
      commissionDiscountProgram = MultipleAutoPriceMatchHolder(
        programId = 1,
        commissionDiscountChannelId = Some(1051),
        commissionDiscountPercent = 1d,
        adjustmentChannelId = 1051,
        adjustmentDiscountPercent = 0d,
        statusId = 1,
        startDate = new DateTime("2021-01-01"),
        endDate = Some(new DateTime("2999-12-31")),
        apmAdjustmentDiscount =
          Seq(ApmAdjustmentDiscount(1, 1, Some(5.0d), DateTime.parse("1999-01-01"), DateTime.parse("2999-01-01"))),
        apmCommissionDiscount =
          Seq(ApmCommissionDiscount(1, 1, Some(5.0d), DateTime.parse("1999-01-01"), DateTime.parse("2999-01-01"))),
        programType = Some(1),
      ),
      apmLeadingRoomAdjustmentIds = Seq(aValidRoom.roomTypeId.toInt),
      rateFence = Set(aValidRateFence),
    )

    val resellExternalData = ResellExternalData(sourceBookingId = "1234")

    val roomApm = aValidRoom
      .withChannel(YplMasterChannel.APM)
      .withApmExternalData(apmExternalDataWithPartialAdjustment)
      .withApmCommissionDiscountSetting(multipleAutoPriceMatchInfo)
    val roomApmWithoutPartialAdjustment = aValidRoom
      .withChannel(YplMasterChannel.APM)
      .withApmExternalData(apmExternalDataWithoutPartialAdjustment)
      .withApmCommissionDiscountSetting(multipleAutoPriceMatchInfo)
    val roomNonApm = aValidRoom.withChannel(YplMasterChannel.APS)
    val roomApmWithMultipleProgram = aValidRoom
      .withChannel(YplMasterChannel.APM)
      .withApmExternalData(apmExternalDataWithMultipleProgram)
      .withApmCommissionDiscountSetting(multipleAutoPriceMatchInfo)
    val roomApmWithMultipleProgramPeakSeason = aValidRoom
      .withChannel(YplMasterChannel.APM)
      .withApmExternalData(apmExternalDataWithMultipleProgram)
      .withApmCommissionDiscountSetting(multipleAutoPriceMatchInfo.copy(statusId = 8))
    val roomApmWithMultipleProgramProgramTypeCalendarView = aValidRoom
      .withChannel(YplMasterChannel.APM)
      .withApmExternalData(apmExternalDataWithMultipleProgram)
      .withApmCommissionDiscountSetting(multipleAutoPriceMatchInfo.copy(programType = Some(2)))
    val roomApmWithMultipleProgramInactive = aValidRoom
      .withChannel(YplMasterChannel.APM)
      .withApmExternalData(apmExternalDataWithMultipleProgram)
      .withApmCommissionDiscountSetting(multipleAutoPriceMatchInfo.copy(statusId = 2))
    val roomApmWithResell = roomApm.withResellExternalData(resellExternalData)
    val roomApmWithoutPartialAdjustmentAndResell =
      roomApmWithoutPartialAdjustment.withResellExternalData(resellExternalData)
    val roomNonApmWithResell = roomNonApm.withResellExternalData(resellExternalData)
    val roomApmWithMultipleProgramAndResell = roomApmWithMultipleProgram.withResellExternalData(resellExternalData)

    val hotelWL = aValidHotel.withSupplierId(29014).withRoom(room)

    val hotelWLE = aValidHotel.withSupplierId(29014).withRoom(roomE)

    val hotelYcsApm = aValidHotel.withSupplierId(332).withRoom(roomApm)

    val hotelYcsApmWithoutPartialAdjustment = aValidHotel.withSupplierId(332).withRoom(roomApmWithoutPartialAdjustment)

    val hotelYcsApmWithMultipleProgram = aValidHotel.withSupplierId(332).withRoom(roomApmWithMultipleProgram)

    val hotelYcsApmWithMultipleProgramPeakSeason =
      aValidHotel.withSupplierId(332).withRoom(roomApmWithMultipleProgramPeakSeason)

    val hotelYcsApmWithMultipleProgramAndProgramTypeCalendarView =
      aValidHotel.withSupplierId(332).withRoom(roomApmWithMultipleProgramProgramTypeCalendarView)

    val hotelYcsApmWithMultipleProgramAndInactive =
      aValidHotel.withSupplierId(332).withRoom(roomApmWithMultipleProgramInactive)

    val hotelYcsNonApm = aValidHotel.withSupplierId(332).withRoom(roomNonApm)

    val hotelYcsApmWithResell = aValidHotel.withSupplierId(332).withRoom(roomApmWithResell)

    val hotelYcsNonApmWithResell = aValidHotel.withSupplierId(332).withRoom(roomNonApmWithResell)

    val hotelYcsApmWithMultipleProgramAndResell =
      aValidHotel.withSupplierId(332).withRoom(roomApmWithMultipleProgramAndResell)

    val hotelYcsApmWithoutPartialAdjustmentAndResell =
      aValidHotel.withSupplierId(332).withRoom(roomApmWithoutPartialAdjustmentAndResell)

    "build correct external data" in {
      val data = service.addDmcData(hotelWL)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"TotalPrice":1000.5,"CurrencyCode":"currencycode","MaxChildren":1,"RateplanCode":"rateplancode","HotelCode":"hotelcode","LinkedRoomTypeCode":"A4","InventoryType":1,"CheckInStartTime":"","RoomTypeCode":"roomtypecode","MaxExtraBed":1,"CheckInEndTime":"","RoomAllocationInfo":[{"Adults":1,"ChildrenCountAsRoomOcc":0,"ChildrenTypes":[{"ChildRateType":2,"Quantity":1}],"RoomNumber":1},{"Adults":2,"ChildrenCountAsRoomOcc":1,"ChildrenTypes":[{"ChildRateType":1,"Quantity":2}],"RoomNumber":2}],"SearchOccupancy":1,"SearchID":"id","MaxOccupancy":1}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build correct external data with empty data" in {
      val data = service.addDmcData(hotelWLE)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"TotalPrice":1250.0,"CurrencyCode":"","MaxChildren":1,"RateplanCode":"","HotelCode":"","LinkedRoomTypeCode":"","InventoryType":0,"CheckInStartTime":"08:00:00","RoomTypeCode":"","MaxExtraBed":1,"CheckInEndTime":"23:00:00","RoomAllocationInfo":[],"SearchOccupancy":1,"SearchID":"id","MaxOccupancy":1}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build correct apm external data when apmExternalData exists and isPartialAdjustment is not None" in {
      val data = service.addDmcDataForApmHotel(hotelYcsApm)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":1,"CheapestSellIn":10.567,"IsPartialAdjustment":false,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":2,"CheapestSellIn":8.0,"IsPartialAdjustment":true,"OriginalPriceLocal":5.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":0.78,"CommissionDiscountProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"}}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build correct apm external data when apmExternalData exists and isPartialAdjustment is None" in {
      val data = service.addDmcDataForApmHotel(hotelYcsApmWithoutPartialAdjustment)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":3,"CheapestSellIn":10.567,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":4,"CheapestSellIn":8.0,"OriginalPriceLocal":8.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":1.0,"CommissionDiscountProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"}}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build correct apm external data when with Multiple APM Program" in {
      val data = service.addDmcDataForApmHotel(hotelYcsApmWithMultipleProgram)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":5,"CheapestSellIn":10.567,"IsPartialAdjustment":false,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":6,"CheapestSellIn":8.0,"IsPartialAdjustment":true,"OriginalPriceLocal":8.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":0.0,"CommissionDiscountProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"}}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build nothing for apm external data when apmExternalData is None" in {
      val data = service.addDmcDataForApmHotel(hotelYcsNonApm)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData))

      result should_== None
    }

    "build correct external data when apmExternalData exists and isPartialAdjustment is not None and resellExternalData is present" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApmWithResell)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":1,"CheapestSellIn":10.567,"IsPartialAdjustment":false,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":2,"CheapestSellIn":8.0,"IsPartialAdjustment":true,"OriginalPriceLocal":5.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":0.78,"ResellSourceBookingId":"1234","CommissionDiscountProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"}}
        """.stripMargin.trim

      result.parseJson should_== expected.parseJson
    }

    "build correct external data when apmExternalData exists and isPartialAdjustment is None and resellExternalData is present" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApmWithoutPartialAdjustmentAndResell)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":3,"CheapestSellIn":10.567,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":4,"CheapestSellIn":8.0,"OriginalPriceLocal":8.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":1.0,"ResellSourceBookingId":"1234","CommissionDiscountProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"}}
        """.stripMargin.trim

      result.parseJson should_== expected.parseJson
    }

    "build correct external data when apm with Multiple APM Program is present and resellExternalData is present" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApmWithMultipleProgramAndResell)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":5,"CheapestSellIn":10.567,"IsPartialAdjustment":false,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":6,"CheapestSellIn":8.0,"IsPartialAdjustment":true,"OriginalPriceLocal":8.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":0.0,"ResellSourceBookingId":"1234","CommissionDiscountProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"}}
        """.stripMargin.trim

      result.parseJson should_== expected.parseJson
    }

    "build correct resell external data when only resellExternalData is Present" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsNonApmWithResell)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected = """
                       |{"ResellSourceBookingId":"1234"}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build correct apm external data when only apmExternalData is present" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApm)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":1,"CheapestSellIn":10.567,"IsPartialAdjustment":false,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":2,"CheapestSellIn":8.0,"IsPartialAdjustment":true,"OriginalPriceLocal":5.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":0.78,"CommissionDiscountProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"}}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build correct apm external data only apmExternalData is present and isPartialAdjustment is None" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApmWithoutPartialAdjustment)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":3,"CheapestSellIn":10.567,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":4,"CheapestSellIn":8.0,"OriginalPriceLocal":8.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":1.0,"CommissionDiscountProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":0.0,"CommissionDiscountChannelId":0,"ApmAdjustmentDiscount":[],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":0,"ApmCommissionDiscount":[],"StartDate":"2020-01-01","ProgramType":0,"ProgramId":0,"StatusId":0,"EndDate":"2020-01-01"}}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build correct apm external data when only apmExternalData with Multiple APM Program is present" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApmWithMultipleProgram)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":5,"CheapestSellIn":10.567,"IsPartialAdjustment":false,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":6,"CheapestSellIn":8.0,"IsPartialAdjustment":true,"OriginalPriceLocal":8.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":0.0,"CommissionDiscountProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"}}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build correct apm external data when only apmExternalData with Multiple APM Program is present and is program type" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApmWithMultipleProgram)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":5,"CheapestSellIn":10.567,"IsPartialAdjustment":false,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":6,"CheapestSellIn":8.0,"IsPartialAdjustment":true,"OriginalPriceLocal":8.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":0.0,"CommissionDiscountProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"}}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build correct apm external data when only apmExternalData with Multiple APM Program is present and is valid program type and status" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApmWithMultipleProgram)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":5,"CheapestSellIn":10.567,"IsPartialAdjustment":false,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":6,"CheapestSellIn":8.0,"IsPartialAdjustment":true,"OriginalPriceLocal":8.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":0.0,"CommissionDiscountProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"}}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build correct apm external data when only apmExternalData with Multiple APM Program is present and is invalid program type and status" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApmWithMultipleProgramAndProgramTypeCalendarView)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      result should_== ""
    }

    "build correct apm external data when only apmExternalData with Multiple APM Program is present and is valid program type but invalid status" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApmWithMultipleProgramAndInactive)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      result should_== ""
    }

    "build correct apm external data when only apmExternalData with Multiple APM Program is present and is status peak season" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsApmWithMultipleProgramPeakSeason)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData)).getOrElse("")
      val expected =
        """
          |{"ApmHotelActiveStatus":"true","ApmLeadingRoomAdjustmentIds":[13193032],"ApmPriceAdjustmentDetails":[{"MaximumDeltaPercent":20.0,"Delta":2.017,"MarketPriceLocal":9.0,"ApprovalPriceId":5,"CheapestSellIn":10.567,"IsPartialAdjustment":false,"OriginalPriceLocal":10.567,"Currency":"THB","OriginalSellIn":10.567,"Date":"2021-01-22","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":9.0,"CheapestPriceLocal":10.567},{"MaximumDeltaPercent":20.0,"Delta":1.0,"MarketPriceLocal":8.0,"ApprovalPriceId":6,"CheapestSellIn":8.0,"IsPartialAdjustment":true,"OriginalPriceLocal":8.0,"Currency":"THB","OriginalSellIn":8.0,"Date":"2021-01-23","ChargeType":4,"AdjustmentRateType":4,"MarketPriceDiscountPercent":5.0,"MarketSellIn":8.0,"CheapestPriceLocal":8.0}],"AdditionalCommRedHotelLevel":0.0,"CommissionDiscountProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"},"LogicVersion":"","ApmAdjustmentIdsHash":"hashed-adjustmentIdsHash-hashed","RateFence":[{"Origin":"TH","Cid":-1,"Language":1}],"ApmPriceCommission":[{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-22","ChargeOption":"Mandatory","ChargeType":"Room"},{"CommissionDiscountPercent":1.0,"ReferenceCommissionPercent":9.0,"Date":"2021-01-23","ChargeOption":"Mandatory","ChargeType":"Room"}],"PriceAdjustmentProgram":{"CommissionDiscountPercent":1.0,"CommissionDiscountChannelId":1051,"ApmAdjustmentDiscount":[{"AdjustmentDiscountPercent":5.0,"AdjustmentChannelId":1,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01"}],"AdjustmentDiscountPercent":0.0,"AdjustmentChannelId":1051,"ApmCommissionDiscount":[{"CommissionDiscountPercent":5.0,"StartDate":"1999-01-01","ProgramId":1,"EndDate":"2999-01-01","CommissionChannelId":1}],"StartDate":"2021-01-01","ProgramType":1,"ProgramId":1,"StatusId":1,"EndDate":"2999-12-31"}}
        """.stripMargin.trim

      result.sorted should_== expected.sorted
    }

    "build nothing for external data when apmExternalData is not present and resellExternalData is not present" in {
      val data = service.addDmcDataForYcsHotel(hotelYcsNonApm)
      val room = data.rooms.headOption
      val result = room.flatMap(d => d.dmcData.map(_.externalData))

      result should_== None
    }

  }
}
