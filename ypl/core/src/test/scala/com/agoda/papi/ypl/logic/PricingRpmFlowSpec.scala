package com.agoda.papi.ypl.logic

import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.enums.request.FilterCriteria
import com.agoda.papi.enums.room.ChargeType.{ExtraBed, Room}
import com.agoda.papi.enums.room.SubChargeType.{Adult, Child}
import com.agoda.papi.enums.room._
import com.agoda.papi.ypl.commission.{CommissionHolder, WholesaleCommissionHolder}
import com.agoda.papi.ypl.mocks.{PricingRpmFlowImplMock, PricingRpmFlowMock}
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.models.api.request.{YplChildren, YplFlagInfo, YplOccInfo}
import com.agoda.papi.ypl.models.consts.Measurements
import com.agoda.papi.ypl.models.hotel.{AgePolicy, AgodaAgencyFeatures, DmcData}
import com.agoda.papi.ypl.models.occupancy.{MaxAllowedFreeChildAgeRange, OccupancyBreakdown}
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.pricing.{RoomOccupancy, YplChannelDiscountBreakdown, YplPrice, YplSupplierStats}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.suppliers.DMC.apmDirectConnectSupplierList
import com.agoda.papi.ypl.occupancy.models.YcsChildAgeRange
import com.agoda.papi.ypl.pricing.mocks.TaxPrototypeServiceMock
import com.agoda.papi.ypl.settings.RoomStatsTrackingSettings
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.SupplierRateInfo.ExternalData
import com.agoda.protobuf.cache.HotelPrice
import com.agoda.supply.calc.proto.{PropertyOffer, StayPackageType}
import com.agoda.utils.flow.PropertyContext
import com.agoda.utils.monitoring.AggregateReporter
import org.joda.time.{DateTime, LocalDate}
import org.mockito.Mockito.{times, verify}
import org.specs2.concurrent.ExecutionEnv
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext}
//scalastyle:off
class PricingRpmFlowSpec(implicit ec: ExecutionEnv)
  extends SpecificationWithJUnit
    with Mockito
    with YPLTestDataBuilders
    with YPLTestContexts {

  implicit val compositeChannelContext = aValidCompositeChannelContext

  class PricingRpmFlowMockImpl extends PricingRpmFlowMock with TaxPrototypeServiceMock {
    lazy val hotelIdWithTaxLevel = 1001
    lazy val hotelIdWithoutTaxLevel = 1002
    lazy val hotelIdWithRateRepurepose = 1003

    override val roomStatsTrackingSettings = RoomStatsTrackingSettings(enabled = true)

    override implicit def ec: ExecutionContext = scala.concurrent.ExecutionContext.Implicits.global

    override def shouldSuggestRooms(request: YplRequest, isSingleRoomNHA: Boolean)(implicit ctx: YplContext): Boolean =
      false

    override def addSimulatedPromotion(YplHotelEntryModel: Option[YplHotelEntryModel])(implicit
      ctx: YplContext): Option[YplHotelEntryModel] = None

    override def calculateDailyPrices(
      room: YplRoomEntry,
      paymentModel: PaymentModel,
      taxInfo: TaxInfo,
      agePolicy: AgePolicy,
      ignoreRequestedNumberOfRoomsForNha: Boolean,
      supplierId: SupplierId,
      applyDiscountsMultiplicatively: Boolean,
      isPull: Boolean = false,
      priceForAssumeTaxMap: Map[DateTime, Double] = Map.empty,
      isChannelDiscountStep: Boolean = false,
      agencyFeatures: Option[AgodaAgencyFeatures] = None,
      hotelId: HotelId,
      chainId: ChainId,
      countryId: CountryId,
      cityId: CityId,
      childAgeRange: List[YcsChildAgeRange],
      isUseChildAgeRange: Boolean = false)(implicit ctx: YplContext, propertyContext: PropertyContext): List[YplPrice] =
      if (priceForAssumeTaxMap.nonEmpty) List(aValidPrice)
      else List()

    override def doPricing(hotelId: HotelId,
                           supplierId: SupplierId,
                           chainId: ChainId,
                           countryId: CountryId,
                           cityId: CityId,
                           paymentModel: PaymentModel,
                           taxInfo: TaxInfo,
                           room: YplRoomEntry,
                           dailyPrices: List[YplPrice],
                           dispatchChannels: YplDispatchChannels,
                           reqOcc: YplReqOccByHotelAgePolicy,
                           fixMarriottSurchargeExp: Boolean,
                           surchargeRateType: RateType = RateType.Unknown,
                           isOTASupplier: Option[Boolean] = None,
                           isPull: Boolean = false,
                           supplierContractedCommission: Option[Double] = None,
                           gmtOffset: GmtOffset = 0,
                           gmtOffsetMinutes: GmtOffset = 0,
                           enabledRoom: Map[RoomTypeId, EnabledRoom] = Map.empty)(implicit
      ctx: YplContext,
      propertyContext: PropertyContext): YPLRoom = hotelId match {
      case `hotelIdWithTaxLevel` =>
        val yplRoom = aValidRoom
          .withRoomTypeId(111)
          .withChannel(YplMasterChannel.RTL)
          .withSupplierId(0)
          .withSellerBrand("calculated")
          .withOccupancy(aValidRoomOccupancy.withAdults(0))
          .withPrice(aValidPrice.copy(dailyTaxes = aValidDailyTaxWithTaxPrototypeLevel))
          .withTaxInfo(Some(aValidTaxInfoWithTaxPrototypeLevel))
        if (dailyPrices.length == 0) {
          yplRoom.withSellerBrand("calculated").build
        } else {
          yplRoom.withSellerBrand("reCalculated").build
        }
      case `hotelIdWithoutTaxLevel` =>
        val yplRoom = aValidRoom
          .withRoomTypeId(111)
          .withChannel(YplMasterChannel.RTL)
          .withSupplierId(0)
          .withSellerBrand("calculated")
          .withOccupancy(aValidRoomOccupancy.withAdults(0))
          .withPrice(aValidPrice.copy(dailyTaxes = aValidDailyTax))
          .withTaxInfo(Some(aValidTaxInfo))
        if (dailyPrices.length == 0) {
          yplRoom.withSellerBrand("calculated").build
        } else {
          yplRoom.withSellerBrand("reCalculated").build
        }
      case `hotelIdWithRateRepurepose` => aValidRoom
          .withRateCategory(aValidRateCategory)
          .withAgodaAgency(false)
          .withChannel(room.channel)
          .withFences(room.fences)
          .withHotelId(hotelIdWithRateRepurepose)
      case _ =>
        val yplRoom = aValidRoom
          .withRoomTypeId(111)
          .withChannel(YplMasterChannel.RTL)
          .withSupplierId(0)
          .withSellerBrand("calculated")
          .withOccupancy(aValidRoomOccupancy.withAdults(0))
        yplRoom.withSellerBrand("reCalculated").build
    }
  }

  trait ConverterScope extends Scope {
    val meta: HotelMeta = mock[HotelMeta]
    val ratePlans: Channels = Set.empty

    val aggregateReporter = new AggregateReporter {

      val metricList = scala.collection.mutable.ListBuffer.empty[(Currency, CountryId, Map[Currency, Currency])]

      def count(metricKey: String, count: Long = 1L, tags: Map[String, String] = Map.empty[String, String]): Unit = {}

      def aggregate(metricKey: String, value: Long, tags: Map[String, String] = Map.empty[String, String]): Unit =
        metricList += Tuple3(metricKey, value, tags)

      override def run(): Unit = {}
    }

    implicit val ctx: YplContext = aValidYplContext.withReporter(aggregateReporter)

    protected def protobuf: Either[PropertyOffer, HotelPrice]

    val pricingData = YplPricingData(protobuf, meta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
    val protocor = mock[com.agoda.papi.ypl.models.Cor]

    def pf: PricingRpmFlowMockImpl

    val modelSpy = spy(pf)
    modelSpy.modelsConverter(pricingData, ctx)
  }

  trait ConverterScopeWithStatEnabled extends ConverterScope {
    override def pf = new PricingRpmFlowMockImpl
  }

  trait ConverterScopeWithStatDisabled extends ConverterScope {

    class PricingRpmFlowMockImplWithStatDisabled extends PricingRpmFlowMockImpl {
      override val roomStatsTrackingSettings = RoomStatsTrackingSettings(enabled = false)
    }
    override def pf = new PricingRpmFlowMockImplWithStatDisabled
  }

  "Pricing rpm flow " should {
    val promotionWithDiscount = Map(LocalDate.now() -> List(aValidPromotionEntry.withChannelDiscount(false).build))
    val promotionWithoutDiscount = Map(LocalDate.now() -> List(aValidPromotionEntry.withChannelDiscount(true).build))

    val dailyPrice: DailyPrice = aValidDailyPrice
      .withPrices(List(aValidPriceEntry.withChargeType(ChargeType.Room).withValue(1.0)))
      .withChannelDiscount(None)
    val dailyPrices = Map(new DateTime() -> dailyPrice)

    val roomEntry = aValidRoomEntry
      .withRoomTypeId(111)
      .withChannel(YplMasterChannel.RTL)
      .withPromotionsBreakdown(promotionWithDiscount)
      .withDailyPrices(dailyPrices)
    val roomEntries =
      List(roomEntry.build,
           roomEntry.withPromotionsBreakdown(promotionWithoutDiscount).withChannel(YplMasterChannel.APS).build)
    val hotelEntry = aValidHotelEntryModel.withRooms(roomEntries).build
    val yplRoom = aValidRoom
      .withPrices(Nil)
      .withRoomTypeId(111)
      .withChannel(YplMasterChannel.RTL)
      .withSupplierId(0)
      .withSellerBrand("calculated")
      .withOccupancy(aValidRoomOccupancy.withAdults(0))
      .withRateCategoryId(123)
      .withRateCategory(aValidRateCategory.withId(123))
      .build

    val rooms = List(yplRoom, yplRoom.withChannel(YplMasterChannel.APS).build)

    val ctx = aValidYplContext.withRequest(aValidYplRequest)

    "call ycs property offer converter when ycs property offer protobuf is passed" in {
      "StatEnabled" in new ConverterScopeWithStatEnabled {
        override def protobuf = Left(PropertyOffer())

        there was atLeastOne(modelSpy).convertFromPropOffer(any(), any(), any(), any())(any())
        aggregateReporter.metricList.size should_== 3
      }
      "StatDisabled" in new ConverterScopeWithStatDisabled {
        override def protobuf = Left(PropertyOffer())

        there was atLeastOne(modelSpy).convertFromPropOffer(any(), any(), any(), any())(any())
        aggregateReporter.metricList.size should_== 1
      }
    }
    "call ota converter when ota probuf is passed" in {
      "StatEnabled" in new ConverterScopeWithStatEnabled {
        override def protobuf = Right(HotelPrice.defaultInstance)

        there was atLeastOne(modelSpy).convertFromHP(any(), any(), any(), any(), any())(any())
        aggregateReporter.metricList.size should_== 2
      }
      "StatDisabled" in new ConverterScopeWithStatDisabled {
        override def protobuf = Right(HotelPrice.defaultInstance)

        there was atLeastOne(modelSpy).convertFromHP(any(), any(), any(), any(), any())(any())
        aggregateReporter.metricList.size should_== 0
      }
    }

    "calculate prices" should {
      val pricingRpmFlowMock = new PricingRpmFlowMockImpl
      "should not recalculate pricing when price not contain tax level and surcharge / fee" should {
        val data =
          pricingRpmFlowMock.Data(Some(hotelEntry.copy(hotelId = pricingRpmFlowMock.hotelIdWithoutTaxLevel)), ctx, null)
        val result = pricingRpmFlowMock.calculatePricesRpm(data)
        result.get.rooms.size should_== 2
        result.get.rooms(0).sellerBrand should_== "calculated"
      }
      "should recalculate pricing when price contain tax level and surcharge / fee" should {
        val data =
          pricingRpmFlowMock.Data(Some(hotelEntry.copy(hotelId = pricingRpmFlowMock.hotelIdWithTaxLevel)), ctx, null)
        val result = pricingRpmFlowMock.calculatePricesRpm(data)
        result.get.rooms.size should_== 2
        result.get.rooms(0).sellerBrand should_== "reCalculated"
      }
    }

    "recalculate prices" should {
      val pricingRpmFlowMock = new PricingRpmFlowMockImpl

      "recalculate only if YPLRoom's yplRoomEntry not equal to YplHotelEntryModel's yplRoomEntry" in {
        val yplRooms = List(rooms.head.copy(yplRoomEntry = hotelEntry.rooms.head)) ++ rooms.drop(1)
        val roomsWithEntry = YplRoomsWithEntry(yplRooms, hotelEntry)
        val data = pricingRpmFlowMock.Data(Some(roomsWithEntry), ctx, null)

        val result: Option[YplRoomsWithEntry] = pricingRpmFlowMock.reCalculatePrices(data)
        result.get.rooms.size should_== 2
        result.get.rooms(0).sellerBrand should_== "calculated"
        result.get.rooms(1).sellerBrand should_== "reCalculated"
      }
      "in recalculate process, only calculated once when price not contain tax level and surcharge / fee" in {
        val roomswithEntry =
          YplRoomsWithEntry(rooms, hotelEntry.copy(hotelId = pricingRpmFlowMock.hotelIdWithoutTaxLevel))
        val data = pricingRpmFlowMock.Data(Some(roomswithEntry), ctx, null)

        val result: Option[YplRoomsWithEntry] = pricingRpmFlowMock.reCalculatePrices(data)
        result.get.rooms.size should_== 2
        result.get.rooms(1).sellerBrand should_== "calculated"
      }
      "in recalculate process, it has recalculate when price contain tax level and surcharge / fee" in {
        val roomswithEntry = YplRoomsWithEntry(rooms, hotelEntry.copy(hotelId = pricingRpmFlowMock.hotelIdWithTaxLevel))
        val data = pricingRpmFlowMock.Data(Some(roomswithEntry), ctx, null)

        val result: Option[YplRoomsWithEntry] = pricingRpmFlowMock.reCalculatePrices(data)
        result.get.rooms.size should_== 2
        result.get.rooms(1).sellerBrand should_== "reCalculated"
      }
      "rateutilization rooms" in {
        val mockRateUtils: Seq[YPLRateReutilizationEntry] = Seq(
          aValidYPLRateReutilizationEntry
            .withTargetChannel(YplChannel(6))
            .withFlatChannelDiscount(5d)
            .withReferenceChannel(YplChannel(1))
            .withSourceChannel(YplChannel(2)),
          aValidYPLRateReutilizationEntry
            .withTargetChannel(YplChannel(9))
            .withFlatChannelDiscount(10d)
            .withReferenceChannel(YplChannel(1))
            .withSourceChannel(YplChannel(2)),
          // Over margin
          aValidYPLRateReutilizationEntry
            .withTargetChannel(YplChannel(12))
            .withFlatChannelDiscount(20d)
            .withReferenceChannel(YplChannel(1))
            .withSourceChannel(YplChannel(2)),
        )
        val mockRoom = aValidRoom.withRateCategory(aValidRateCategory)
        val mockRoomEntry = aValidRoomEntry
          .withAgodaAgency(false)
          .withRoomTypeId(mockRoom.roomTypeId)
          .withRateCategoryEntry(aValidRateCategoryEntry.withId(mockRoom.rateCategoryId))
          .withOccupancy(RoomOccupancy(mockRoom.occupancy, 0, extraBeds = mockRoom.extraBed))

        val mockHotelEntry = aValidHotelEntryModel
          .withHotelId(1003)
          .withSupplierId(mockRoom.supplierId)
          .withRooms(List(
            mockRoomEntry.withAgodaAgency(false).withChannel(YplChannel(1)),
            mockRoomEntry.withAgodaAgency(false).withChannel(YplChannel(2)),
            mockRoomEntry.withAgodaAgency(false).withChannel(YplChannel(7)),
          ))
          .withRateReutilizations(mockRateUtils)
        val roomswithEntry = YplRoomsWithEntry(rooms, mockHotelEntry)
        val data = pricingRpmFlowMock.Data(Some(roomswithEntry), ctx, null)

        val result: Option[YplRoomsWithEntry] = pricingRpmFlowMock.reCalculatePrices(data)
        result.get.rooms.size should_== 5
        result.get.rooms.exists(_.isRepurposed) should_== true
      }

      "rateutilization rooms (DFOPS-2011=B)" in {
        val fenceOne = YplRateFence("TH", 1, 1)
        val fenceTwo = YplRateFence("TH", 2, 1)
        val mockRateUtils: Seq[YPLRateReutilizationEntry] = Seq(
          aValidYPLRateReutilizationEntry
            .withTargetChannel(YplChannel(6))
            .withFlatChannelDiscount(5d)
            .withReferenceChannel(YplChannel(1))
            .withSourceChannel(YplChannel(2)),
          aValidYPLRateReutilizationEntry
            .withTargetChannel(YplChannel(9))
            .withFlatChannelDiscount(10d)
            .withReferenceChannel(YplChannel(1))
            .withSourceChannel(YplChannel(2)),
          // Over margin
          aValidYPLRateReutilizationEntry
            .withTargetChannel(YplChannel(12))
            .withFlatChannelDiscount(20d)
            .withReferenceChannel(YplChannel(1))
            .withSourceChannel(YplChannel(2)),
        )
        val mockRoom = aValidRoom.withRateCategory(aValidRateCategory).withHotelId(1003)
        val mockRoomEntry = aValidRoomEntry
          .withAgodaAgency(false)
          .withRoomTypeId(mockRoom.roomTypeId)
          .withRateCategoryEntry(aValidRateCategoryEntry.withId(mockRoom.rateCategoryId))
          .withOccupancy(RoomOccupancy(mockRoom.occupancy, 0, extraBeds = mockRoom.extraBed))

        val mockHotelEntry = aValidHotelEntryModel
          .withHotelId(1003)
          .withSupplierId(mockRoom.supplierId)
          .withRooms(List(
            mockRoomEntry.withFences(Set(fenceOne, fenceTwo)).withAgodaAgency(false).withChannel(YplChannel(1)),
            mockRoomEntry.withFences(Set(fenceOne, fenceTwo)).withAgodaAgency(false).withChannel(YplChannel(2)),
            mockRoomEntry.withFences(Set(fenceOne)).withAgodaAgency(false).withChannel(YplChannel(7)),
          ))
          .withRateReutilizations(mockRateUtils)
        val rooms = List(
          yplRoom.withHotelId(1003).withFences(Set(fenceOne, fenceTwo)).build,
          yplRoom.withHotelId(1003).withFences(Set(fenceOne, fenceTwo)).withChannel(YplMasterChannel.APS).build,
        )
        val roomswithEntry = YplRoomsWithEntry(rooms, mockHotelEntry)
        val req = aValidYplRequest
          .withBExperiment("DFOPS-2011")
          .withFences(
            Map(
              YplMasterChannel.RTL -> Set(fenceOne, fenceTwo),
              YplMasterChannel.APS -> Set(fenceOne, fenceTwo),
              YplMasterChannel.China -> Set(fenceOne),
            ))
          .build
        val ctx = aValidYplContext.withExperimentContext(forcedFromRequestExperimentContext(req)).withRequest(req)
        val data = pricingRpmFlowMock.Data(Some(roomswithEntry), ctx, null)

        val result = pricingRpmFlowMock.reCalculatePrices(data).get.rooms
        result.size should_== 5
        result.exists(_.isRepurposed) should_== true
        result.find(r => r.isRepurposed && r.channel == YplMasterChannel.China).map(_.fences) should_== Some(
          Set(fenceOne))
      }
    }
    "Interco feature" should {
      "duplicate rooms based on price adjustment programs" in {

        val currentDate = DateTime.parse("2023-01-01")
        def createWholesaleCommissionHolder(campaignCommissionOpt: Double): CommissionHolder = {
          val cws = WholesaleCommissionHolder.default.copy(campaignCommissionOpt = campaignCommissionOpt)
          aValidCommissionHolder
            .withDaily(Map(currentDate -> aValidCommissionDailyHolder.copy(wholesaleCommissionHolder = cws)))
            .build
        }
        val room1 = aValidRoomEntry
          .withChannel(YplMasterChannel(1))
          .withCommissionHolder(aValidCommissionHolder.withDaily(Map(currentDate -> aValidCommissionDailyHolder)))
          .build
        val room2 = aValidRoomEntry
          .withChannel(YplMasterChannel(3458))
          .withCommissionHolder(createWholesaleCommissionHolder(3.0))
          .build
        val room3 = aValidRoomEntry
          .withChannel(YplMasterChannel(4843))
          .withCommissionHolder(createWholesaleCommissionHolder(4.0))
          .build
        val rooms = List(room1, room2, room3)

        val aValidProgramName = "CWS Program"
        val p1 = AdjustmentProgram(
          channelId = 3458,
          programs = List(
            Program(aValidProgramName, Adjustment(3.0), duplicateRoomAndDiscount = false, priceAdjustmentId = Some(0L)),
            Program(aValidProgramName,
                    Adjustment(12.0),
                    duplicateRoomAndDiscount = true,
                    priceAdjustmentId = Some(100000012L)),
            Program(aValidProgramName,
                    Adjustment(15.0),
                    duplicateRoomAndDiscount = true,
                    priceAdjustmentId = Some(100000015L)),
          ),
        )
        val p2 = AdjustmentProgram(
          channelId = 4843,
          programs = List(
            Program(aValidProgramName, Adjustment(3.0), duplicateRoomAndDiscount = false, priceAdjustmentId = Some(0L)),
            Program(aValidProgramName,
                    Adjustment(11.0),
                    duplicateRoomAndDiscount = true,
                    priceAdjustmentId = Some(100000011L)),
            Program(aValidProgramName,
                    Adjustment(14.0),
                    duplicateRoomAndDiscount = true,
                    priceAdjustmentId = Some(100000014L)),
          ),
        )
        val program = CommissionAdjustmentPrograms(
          hotelId = aValidHotelId,
          adjustmentPrograms = List(p1, p2),
        )

        val hotelEntry = aValidHotelEntryModel
          .withHotelId(aValidHotelId)
          .withRooms(rooms)
          .withMetaData(aValidHotelInfo.copy(ccasAdjustmentPrograms = Some(program)))
          .build
        val ctx = aValidYplContext.withRequest(aValidYplRequest)

        val pricingRpmFlowMock = new PricingRpmFlowMockImpl
        val data = pricingRpmFlowMock.Data(Some(hotelEntry), ctx, null)
        val result = pricingRpmFlowMock.createIntercoRooms(data)
        result.get.rooms.size shouldEqual 7

        def roomByChannel(channelId: Int): List[YplRoomEntry] =
          result.get.rooms.filter(_.channel.baseChannelId == channelId)

        val r1 = roomByChannel(1)
        r1.size shouldEqual 1
        r1.head.channel.baseChannelId shouldEqual 1
        r1.head.priceAdjustmentId shouldEqual None
        r1.head.commissionHolder.daily(currentDate).wholesaleCommissionHolder.isEnabled shouldEqual false
        r1.head.commissionHolder.daily(currentDate).wholesaleCommissionHolder.campaignCommissionOpt shouldEqual 0.0

        val r2 = roomByChannel(3458)
        r2.size shouldEqual 3
        r2.head.channel.baseChannelId shouldEqual 3458
        r2.head.priceAdjustmentId shouldEqual Some(0L)
        r2.head.commissionHolder.daily(currentDate).wholesaleCommissionHolder.isEnabled shouldEqual false
        r2.head.commissionHolder.daily(currentDate).wholesaleCommissionHolder.campaignCommissionOpt shouldEqual 3.0
        r2(1).channel.baseChannelId shouldEqual 3458
        r2(1).priceAdjustmentId shouldEqual Some(100000012L)
        r2(1).commissionHolder.daily(currentDate).wholesaleCommissionHolder.isEnabled shouldEqual true
        r2(1).commissionHolder.daily(currentDate).wholesaleCommissionHolder.campaignCommissionOpt shouldEqual 12.0
        r2(2).channel.baseChannelId shouldEqual 3458
        r2(2).priceAdjustmentId shouldEqual Some(100000015L)
        r2(2).commissionHolder.daily(currentDate).wholesaleCommissionHolder.isEnabled shouldEqual true
        r2(2).commissionHolder.daily(currentDate).wholesaleCommissionHolder.campaignCommissionOpt shouldEqual 15.0

        val r3 = roomByChannel(4843)
        r3.size shouldEqual 3
        r3.head.channel.baseChannelId shouldEqual 4843
        r3.head.priceAdjustmentId shouldEqual Some(0L)
        r3.head.commissionHolder.daily(currentDate).wholesaleCommissionHolder.isEnabled shouldEqual false
        r3.head.commissionHolder.daily(currentDate).wholesaleCommissionHolder.campaignCommissionOpt shouldEqual 4.0
        r3(1).channel.baseChannelId shouldEqual 4843
        r3(1).priceAdjustmentId shouldEqual Some(100000011L)
        r3(1).commissionHolder.daily(currentDate).wholesaleCommissionHolder.isEnabled shouldEqual true
        r3(1).commissionHolder.daily(currentDate).wholesaleCommissionHolder.campaignCommissionOpt shouldEqual 11.0
        r3(2).channel.baseChannelId shouldEqual 4843
        r3(2).commissionHolder.daily(currentDate).wholesaleCommissionHolder.isEnabled shouldEqual true
        r3(2).priceAdjustmentId shouldEqual Some(100000014L)
        r3(2).commissionHolder.daily(currentDate).wholesaleCommissionHolder.campaignCommissionOpt shouldEqual 14.0
      }
    }

    "calculate child rate category" should {
      val pricingRpmFlowMock = new PricingRpmFlowMockImpl

      val parentRateCategory = aValidRateCategoryEntry.copy(
        parent = None,
      )
      val baseChildRateCategory = aValidRateCategoryEntry.copy(
        rateCategoryId = 124,
        parent = Some(parentRateCategory),
      )

      "do nothing on parent rate category" in {
        val priceEntry = aValidPriceEntry.withValue(100)

        val dailyPriceEntry = aValidDailyPrice.copy(
          prices = List(
            priceEntry,
          ),
        )

        val originalDailyPrices: Map[DateTime, DailyPrice] = Map(
          dailyPriceEntry.date -> dailyPriceEntry,
        )

        val roomEntry = aValidRoomEntry.copy(
          rateCategory = parentRateCategory,
          dailyPrices = originalDailyPrices,
        )

        val hotelEntry1 = hotelEntry.copy(
          rooms = List(
            roomEntry,
          ),
        )

        val data = pricingRpmFlowMock.Data(Some(hotelEntry1), ctx, null)
        val result = pricingRpmFlowMock.calculateChildRateCategory(data)

        result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices.head.value should_== 100
      }

      "apply PGPN reduction correctly with normal charge" in {
        val priceEntry = aValidPriceEntry.copy(
          value = 200,
          chargeType = ChargeType.Room,
          subChargeType = SubChargeType.None,
        )

        val dailyPriceEntry = aValidDailyPrice.copy(
          prices = List(
            priceEntry,
          ),
        )

        val originalDailyPrices: Map[DateTime, DailyPrice] = Map(
          dailyPriceEntry.date -> dailyPriceEntry,
        )

        val childRateCategory = baseChildRateCategory.copy(
          value = -50,
          applyTo = "PGPN",
        )

        val roomEntry = aValidRoomEntry.copy(
          rateCategory = childRateCategory,
          dailyPrices = originalDailyPrices,
        )

        val hotelEntry1 = hotelEntry.copy(
          rooms = List(
            roomEntry,
          ),
          reqOcc = aValidReqOcc.copy(
            occ = YplOccInfo(
              _adults = Some(2),
              _rooms = Some(1),
            ),
          ),
        )

        val data = pricingRpmFlowMock.Data(Some(hotelEntry1), ctx, null)
        val result = pricingRpmFlowMock.calculateChildRateCategory(data)
        result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices.head.value should_== 100
      }

      "do nothing if parent and try apply adjusted PGPN reduction correctly with child rate price type = percentage discount" in {
        val priceEntry = aValidPriceEntry.copy(
          value = 200,
          chargeType = ChargeType.Room,
          subChargeType = SubChargeType.GradeSchool,
        )

        val dailyPriceEntry = aValidDailyPrice.copy(
          prices = List(
            priceEntry,
          ),
        )

        val originalDailyPrices: Map[DateTime, DailyPrice] = Map(
          dailyPriceEntry.date -> dailyPriceEntry,
        )
        val parentRateCategory = aValidRateCategoryEntry.copy(
          rateCategoryId = -1,
          parent = None,
        )

        val baseChildRateCategoryButNoChild = aValidRateCategoryEntry.copy(
          rateCategoryId = -1,
          parent = Some(parentRateCategory),
        )
        val childRateCategory = baseChildRateCategoryButNoChild.copy(
          value = -100,
          applyTo = "PGPN",
          isChildRateEnabled = true,
          childRate = List(
            ChildRate(
              childRateTypeId = ChildRateType.GradeSchool,
              childRatePricingTypeId = PricingChildRateType.PercentageAdultPrice,
              value = 50,
              isCountAsRoomOcc = false,
            ),
          ),
        )

        val childrenTypesMap: Map[ChildRateType, Quantity] = Map(
          ChildRateType.GradeSchool -> 1,
        )
        val roomEntry = aValidRoomEntry.copy(
          rateCategory = childRateCategory,
          dailyPrices = originalDailyPrices,
          roomAllocationInfo = Map(
            1 -> YPLRoomAllocation(
              adults = 1,
              childrenCountAsRoomOcc = 0,
              childrenTypes = childrenTypesMap,
            ),
          ),
        )

        val hotelEntry1 = hotelEntry.copy(
          rooms = List(
            roomEntry,
          ),
          reqOcc = aValidReqOcc.copy(
            occ = YplOccInfo(
              _adults = Some(1),
              _rooms = Some(1),
              _children = Some(
                YplChildren(
                  ages = List(Some(12)),
                  childrenTypes = childrenTypesMap,
                )),
            ),
          ),
        )

        val data = pricingRpmFlowMock.Data(Some(hotelEntry1), ctx, null)

        val result = pricingRpmFlowMock.calculateChildRateCategory(data)
        // 200 + ( -100 (value) * 50% (from child rates) )
        result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices.head.value should_== 200
      }

      "apply adjusted PGPN reduction correctly with child rate price type = percentage discount" in {
        val priceEntry = aValidPriceEntry.copy(
          value = 200,
          chargeType = ChargeType.Room,
          subChargeType = SubChargeType.GradeSchool,
          occupancy = 1,
        )

        val dailyPriceEntry = aValidDailyPrice.copy(
          prices = List(
            priceEntry,
          ),
        )

        val originalDailyPrices: Map[DateTime, DailyPrice] = Map(
          dailyPriceEntry.date -> dailyPriceEntry,
        )

        val childRateCategory = baseChildRateCategory.copy(
          value = -100,
          applyTo = "PGPN",
          isChildRateEnabled = true,
          childRate = List(
            ChildRate(
              childRateTypeId = ChildRateType.GradeSchool,
              childRatePricingTypeId = PricingChildRateType.PercentageAdultPrice,
              value = 50,
              isCountAsRoomOcc = false,
            ),
          ),
        )

        val childrenTypesMap: Map[ChildRateType, Quantity] = Map(
          ChildRateType.GradeSchool -> 1,
        )
        val roomEntry = aValidRoomEntry.copy(
          rateCategory = childRateCategory,
          dailyPrices = originalDailyPrices,
          roomAllocationInfo = Map(
            1 -> YPLRoomAllocation(
              adults = 1,
              childrenCountAsRoomOcc = 0,
              childrenTypes = childrenTypesMap,
            ),
          ),
        )

        val hotelEntry1 = hotelEntry.copy(
          rooms = List(
            roomEntry,
          ),
          reqOcc = aValidReqOcc.copy(
            occ = YplOccInfo(
              _adults = Some(1),
              _rooms = Some(1),
              _children = Some(
                YplChildren(
                  ages = List(Some(12)),
                  childrenTypes = childrenTypesMap,
                )),
            ),
          ),
        )

        val data = pricingRpmFlowMock.Data(Some(hotelEntry1), ctx, null)

        val result = pricingRpmFlowMock.calculateChildRateCategory(data)
        // 200 + ( -100 (value) * 50% (from child rates) )
        result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices.head.value should_== 150
      }

      "apply no PGPN reduction correctly with child rate price type = flat price" in {
        val priceEntry = aValidPriceEntry.copy(
          value = 200,
          chargeType = ChargeType.Room,
          subChargeType = SubChargeType.GradeSchool,
        )

        val dailyPriceEntry = aValidDailyPrice.copy(
          prices = List(
            priceEntry,
          ),
        )

        val originalDailyPrices: Map[DateTime, DailyPrice] = Map(
          dailyPriceEntry.date -> dailyPriceEntry,
        )

        val childRateCategory = baseChildRateCategory.copy(
          value = -100,
          applyTo = "PGPN",
          isChildRateEnabled = true,
          childRate = List(
            ChildRate(
              childRateTypeId = ChildRateType.GradeSchool,
              childRatePricingTypeId = PricingChildRateType.FlatPrice,
              value = 50,
              isCountAsRoomOcc = false,
            ),
          ),
        )

        val childrenTypesMap: Map[ChildRateType, Quantity] = Map(
          ChildRateType.GradeSchool -> 1,
        )
        val roomEntry = aValidRoomEntry.copy(
          rateCategory = childRateCategory,
          dailyPrices = originalDailyPrices,
          roomAllocationInfo = Map(
            1 -> YPLRoomAllocation(
              adults = 1,
              childrenCountAsRoomOcc = 0,
              childrenTypes = childrenTypesMap,
            ),
          ),
        )

        val hotelEntry1 = hotelEntry.copy(
          rooms = List(
            roomEntry,
          ),
          reqOcc = aValidReqOcc.copy(
            occ = YplOccInfo(
              _adults = Some(1),
              _rooms = Some(1),
              _children = Some(
                YplChildren(
                  ages = List(Some(12)),
                  childrenTypes = childrenTypesMap,
                )),
            ),
          ),
        )

        val data = pricingRpmFlowMock.Data(Some(hotelEntry1), ctx, null)

        val result = pricingRpmFlowMock.calculateChildRateCategory(data)
        result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices.head.value should_== 200
      }

      "apply PGPN reduction correctly with child rate price type = flat discount" in {
        val priceEntry = aValidPriceEntry.copy(
          value = 200,
          chargeType = ChargeType.Room,
          subChargeType = SubChargeType.GradeSchool,
          occupancy = 1,
        )

        val dailyPriceEntry = aValidDailyPrice.copy(
          prices = List(
            priceEntry,
          ),
        )

        val originalDailyPrices: Map[DateTime, DailyPrice] = Map(
          dailyPriceEntry.date -> dailyPriceEntry,
        )

        val childRateCategory = baseChildRateCategory.copy(
          value = -100,
          applyTo = "PGPN",
          isChildRateEnabled = true,
          childRate = List(
            ChildRate(
              childRateTypeId = ChildRateType.GradeSchool,
              childRatePricingTypeId = PricingChildRateType.DiscountAdultPrice,
              value = 100,
              isCountAsRoomOcc = false,
            ),
          ),
        )

        val childrenTypesMap: Map[ChildRateType, Quantity] = Map(
          ChildRateType.GradeSchool -> 1,
        )
        val roomEntry = aValidRoomEntry.copy(
          rateCategory = childRateCategory,
          dailyPrices = originalDailyPrices,
          roomAllocationInfo = Map(
            1 -> YPLRoomAllocation(
              adults = 1,
              childrenCountAsRoomOcc = 0,
              childrenTypes = childrenTypesMap,
            ),
          ),
        )

        val hotelEntry1 = hotelEntry.copy(
          rooms = List(
            roomEntry,
          ),
          reqOcc = aValidReqOcc.copy(
            occ = YplOccInfo(
              _adults = Some(1),
              _rooms = Some(1),
              _children = Some(
                YplChildren(
                  ages = List(Some(12)),
                  childrenTypes = childrenTypesMap,
                )),
            ),
          ),
        )

        val data = pricingRpmFlowMock.Data(Some(hotelEntry1), ctx, null)

        val result = pricingRpmFlowMock.calculateChildRateCategory(data)
        // 200 + (-100 (rate category value))
        result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices.head.value should_== 100
      }

      "apply PGPN reduction correctly with multiple charges and child rates" in {
        val basePriceEntry = aValidPriceEntry.copy(
          value = 1000,
          chargeType = ChargeType.Room,
        )

        val dailyPriceEntry: DailyPrice = aValidDailyPrice.copy(
          prices = List(
            basePriceEntry.copy(
              subChargeType = SubChargeType.Adult,
              occupancy = 1,
            ),
            basePriceEntry.copy(
              subChargeType = SubChargeType.GradeSchool,
              occupancy = 1,
            ),
            basePriceEntry.copy(
              subChargeType = SubChargeType.Baby,
              occupancy = 2,
            ),
            basePriceEntry.copy(
              subChargeType = SubChargeType.Toddler,
              occupancy = 3,
            ),
            basePriceEntry.copy(
              subChargeType = SubChargeType.PreSchool,
              occupancy = 4,
            ),
          ),
        )

        val originalDailyPrices: Map[DateTime, DailyPrice] = Map(
          dailyPriceEntry.date -> dailyPriceEntry,
        )

        val childRateCategory = baseChildRateCategory.copy(
          value = -100,
          applyTo = "PGPN",
          isChildRateEnabled = true,
          childRate = List(
            ChildRate(
              childRateTypeId = ChildRateType.GradeSchool,
              childRatePricingTypeId = PricingChildRateType.PercentageAdultPrice,
              value = 50,
              isCountAsRoomOcc = false,
            ),
            ChildRate(
              childRateTypeId = ChildRateType.Baby,
              childRatePricingTypeId = PricingChildRateType.PercentageAdultPrice,
              value = 20,
              isCountAsRoomOcc = false,
            ),
            ChildRate(
              childRateTypeId = ChildRateType.Toddler,
              childRatePricingTypeId = PricingChildRateType.DiscountAdultPrice,
              value = 100,
              isCountAsRoomOcc = false,
            ),
            ChildRate(
              childRateTypeId = ChildRateType.PreSchool,
              childRatePricingTypeId = PricingChildRateType.FlatPrice,
              value = 1000,
              isCountAsRoomOcc = false,
            ),
          ),
        )

        val childrenTypesMap: Map[ChildRateType, Quantity] = Map(
          ChildRateType.GradeSchool -> 1,
          ChildRateType.Baby -> 2,
          ChildRateType.Toddler -> 3,
          ChildRateType.PreSchool -> 4,
        )
        val roomEntry = aValidRoomEntry.copy(
          rateCategory = childRateCategory,
          dailyPrices = originalDailyPrices,
          roomAllocationInfo = Map(
            1 -> YPLRoomAllocation(adults = 1, childrenCountAsRoomOcc = 0, childrenTypes = childrenTypesMap),
          ),
        )

        val hotelEntry1 = hotelEntry.copy(
          rooms = List(
            roomEntry,
          ),
          reqOcc = aValidReqOcc.copy(
            occ = YplOccInfo(
              _adults = Some(1),
              _rooms = Some(1),
              _children = Some(
                YplChildren(
                  ages = List(Some(12)),
                  childrenTypes = childrenTypesMap,
                )),
            ),
          ),
        )

        val data = pricingRpmFlowMock.Data(Some(hotelEntry1), ctx, null)

        val result = pricingRpmFlowMock.calculateChildRateCategory(data)

        val prices = result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices

        // 1000 + (-100 (ratecategory value) * 1 adult)
        prices.find(_.subChargeType == SubChargeType.Adult).get.value should_== 900

        // 1000 + (-100 (ratecategory value) * 1 grade school * 50% (from child rate))
        prices.find(_.subChargeType == SubChargeType.GradeSchool).get.value should_== 950

        // 1000 + (-100 (ratecategory value) * 2 baby * 20% (from child rate))
        prices.find(_.subChargeType == SubChargeType.Baby).get.value should_== 960

        // 1000 + (-100 (ratecategory value) * 3 toddler)
        prices.find(_.subChargeType == SubChargeType.Toddler).get.value should_== 700

        prices.find(_.subChargeType == SubChargeType.PreSchool).get.value should_== 1000
      }

      "apply PGPN discount for multiple Adults and Children across multiple rooms" in {
        val req = aValidYplRequest
        val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

        val basePriceEntry = aValidPriceEntry.copy(
          value = 1000,
          chargeType = ChargeType.Room,
        )
        val childPricePercentage = 50

        val roomAllocationInfo = Map(
          1 -> YPLRoomAllocation(adults = 2,
                                 childrenCountAsRoomOcc = 0,
                                 childrenTypes = Map(ChildRateType.GradeSchool -> 2)),
          2 -> YPLRoomAllocation(adults = 1,
                                 childrenCountAsRoomOcc = 0,
                                 childrenTypes = Map(ChildRateType.GradeSchool -> 1)),
        )

        val pricesList = new scala.collection.mutable.ListBuffer[PriceEntry]()

        roomAllocationInfo.map { roomAllocation =>
          val roomNo = roomAllocation._1
          val allocation = roomAllocation._2
          pricesList += basePriceEntry.copy(
            occupancy = allocation.adults,
            roomNo = Some(roomNo),
            value = basePriceEntry.value * allocation.adults,
            subChargeType = SubChargeType.Adult,
          )
          allocation.childrenTypes.map { childMap =>
            (0 until childMap._2).map { _ =>
              pricesList += basePriceEntry.copy(
                occupancy = 1,
                roomNo = Some(roomNo),
                subChargeType = ChildRateType.toSubChargeType(childMap._1),
                value = basePriceEntry.value * childPricePercentage / 100,
              )
            }
          }
        }

        val adultSubCharges = List(SubChargeType.Adult, SubChargeType.None)

        val dailyPriceEntry: DailyPrice = aValidDailyPrice.copy(prices = pricesList.toList)

        val originalDailyPrices: Map[DateTime, DailyPrice] = Map(
          dailyPriceEntry.date -> dailyPriceEntry,
        )

        val childRateCategory = baseChildRateCategory.copy(
          value = -100,
          applyTo = "PGPN",
          isChildRateEnabled = true,
          childRate = List(
            ChildRate(
              childRateTypeId = ChildRateType.GradeSchool,
              childRatePricingTypeId = PricingChildRateType.PercentageAdultPrice,
              value = childPricePercentage,
              isCountAsRoomOcc = false,
            ),
          ),
        )

        val roomEntry = aValidRoomEntry.copy(
          rateCategory = childRateCategory,
          dailyPrices = originalDailyPrices,
          roomAllocationInfo = roomAllocationInfo,
          isMultipleRoomAssignmentPrice = true,
          isJapanChildRateApplied = true,
        )

        val hotelEntry1 = hotelEntry.copy(
          rooms = List(
            roomEntry,
          ),
          reqOcc = aValidReqOcc.copy(
            occ = YplOccInfo(
              _adults = Some(3),
              _rooms = Some(2),
              _children = Some(
                YplChildren(
                  ages = List(Some(12), Some(12), Some(12)),
                  childrenTypes = Map(ChildRateType.GradeSchool -> 3),
                )),
            ),
          ),
        )

        "should compute correctly" in {
          val data = pricingRpmFlowMock.Data(Some(hotelEntry1), ctx, null)

          val result = pricingRpmFlowMock.calculateChildRateCategory(data)

          val prices = result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices

          // 2000 + (-100 (ratecategory value) * 2 adults)
          prices.find(p => p.roomNo.contains(1) && adultSubCharges.contains(p.subChargeType)).get.value should_== 1800.0
          // 1000 + (-100 (ratecategory value) * 1 adult)
          prices.find(p => p.roomNo.contains(2) && adultSubCharges.contains(p.subChargeType)).get.value should_== 900.0

          // 1000 + (-100 (ratecategory value) * 50% (from child rate) * 2 grade schools)
          prices
            .filter(p => p.roomNo.contains(1) && p.subChargeType == SubChargeType.GradeSchool)
            .map(p => p.value)
            .sum should_== 900.0
          // 1000 + (-100 (ratecategory value) * 50% (from child rate) * 1 grade school)
          prices
            .filter(p => p.roomNo.contains(2) && p.subChargeType == SubChargeType.GradeSchool)
            .map(p => p.value)
            .sum should_== 450.0
        }

        "with extrabed" in {
          pricesList += basePriceEntry.copy(
            occupancy = 1,
            roomNo = Some(roomAllocationInfo.head._1),
            chargeType = ChargeType.ExtraBed,
          )
          val dailyPriceEntry: DailyPrice = aValidDailyPrice.copy(prices = pricesList.toList)
          val updatedDailyPrices: Map[DateTime, DailyPrice] = Map(
            dailyPriceEntry.date -> dailyPriceEntry,
          )
          val updatedRoomEntry = roomEntry.copy(
            dailyPrices = updatedDailyPrices,
          )
          val updatedHotelEntry = hotelEntry1.copy(
            rooms = List(updatedRoomEntry),
            reqOcc = aValidReqOcc.copy(
              occ = YplOccInfo(
                _adults = Some(4),
                _rooms = Some(2),
                _children = Some(
                  YplChildren(
                    ages = List(Some(12), Some(12), Some(12)),
                    childrenTypes = Map(ChildRateType.GradeSchool -> 3),
                  )),
              ),
            ),
          )

          "should compute correctly" in {
            val data = pricingRpmFlowMock.Data(Some(updatedHotelEntry), ctx, null)

            val result = pricingRpmFlowMock.calculateChildRateCategory(data)

            val prices = result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices

            // 2000 + (-100 (ratecategory value) * 2 adults)
            prices
              .find(p => p.roomNo.contains(1) && adultSubCharges.contains(p.subChargeType))
              .get
              .value should_== 1800.0
            // 1000 + (-100 (ratecategory value) * 1 adult)
            prices
              .find(p => p.roomNo.contains(2) && adultSubCharges.contains(p.subChargeType))
              .get
              .value should_== 900.0

            // 1000 + (-100 (ratecategory value) * 50% (from child rate) * 2 grade schools
            prices
              .filter(p => p.roomNo.contains(1) && p.subChargeType == SubChargeType.GradeSchool)
              .map(p => p.value)
              .sum should_== 900.0
            // 1000 + (-100 (ratecategory value) * 50% (from child rate) * 1 grade school
            prices
              .filter(p => p.roomNo.contains(2) && p.subChargeType == SubChargeType.GradeSchool)
              .map(p => p.value)
              .sum should_== 450.0
          }
        }
      }

      "apply PGPN reduction correctly with multiple charges and child rates CHILD-105=B" in {
        val ctx = aValidYplContext.withRequest(aValidYplRequest)

        val basePriceEntry = aValidPriceEntry.copy(
          value = 1000,
          chargeType = ChargeType.Room,
        )
        val adultPrice = basePriceEntry.copy(
          roomNo = Some(1),
          subChargeType = Adult,
          occupancy = 2,
          value = basePriceEntry.value * 2,
          quantity = 1,
        )
        val childPrice = basePriceEntry.copy(
          roomNo = Some(1),
          subChargeType = SubChargeType.Child,
          occupancy = 1,
          value = basePriceEntry.value * 0.5,
          quantity = 1,
        )
        val priceEntry = List(adultPrice, childPrice)
        val dailyPriceEntry: DailyPrice = aValidDailyPrice.copy(prices = priceEntry)
        val originalDailyPrices: Map[DateTime, DailyPrice] = Map(
          dailyPriceEntry.date -> dailyPriceEntry,
        )
        val childRateCategory = baseChildRateCategory.copy(
          value = 100,
          applyTo = "PGPN",
        )
        val roomEntry = aValidRoomEntry.copy(
          rateCategory = childRateCategory,
          dailyPrices = originalDailyPrices,
          isMultipleRoomAssignmentPrice = true,
          occupancyBreakdown = Some(
            OccupancyBreakdown(
              List(
                aValidOccupancyUnit.withChargeType(Room).withSubChargeType(Adult).withQty(2),
                aValidOccupancyUnit.withChargeType(Room).withSubChargeType(Child).withQty(1),
              ),
              3,
              MaxAllowedFreeChildAgeRange.empty,
              isApplyNewOccupancyLogic = true,
            ),
          ),
        )
        val hotelEntry1 = hotelEntry.copy(
          rooms = List(
            roomEntry,
          ),
          reqOcc = aValidReqOcc.copy(
            occ = YplOccInfo(
              _adults = Some(2),
              _rooms = Some(1),
              _children = Some(
                YplChildren(
                  ages = List(Some(5)),
                )),
            ),
          ),
        )
        val adultSubCharges = List(SubChargeType.Adult, SubChargeType.None)

        "should compute correctly when CHILD-105=B" in {
          val data = pricingRpmFlowMock.Data(Some(hotelEntry1), ctx, null)

          val result = pricingRpmFlowMock.calculateChildRateCategory(data)

          val prices = result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices

          // 2000 + (+100 (ratecategory value) * 2 adults)
          prices.find(p => p.roomNo.contains(1) && adultSubCharges.contains(p.subChargeType)).get.value should_== 2200.0

          // 500 + (+100 (ratecategory value) * 1 child)
          prices
            .filter(p => p.roomNo.contains(1) && p.subChargeType == SubChargeType.Child)
            .map(p => p.value)
            .sum should_== 600.0
        }

        "with extra bed" in {
          val priceEntryWithExtraBed = priceEntry :+ basePriceEntry.copy(
            roomNo = Some(1),
            subChargeType = Adult,
            occupancy = 1,
            value = basePriceEntry.value,
            quantity = 1,
            chargeType = ChargeType.ExtraBed,
          )
          val dailyPriceEntry: DailyPrice = aValidDailyPrice.copy(prices = priceEntryWithExtraBed)
          val updatedDailyPrices: Map[DateTime, DailyPrice] = Map(
            dailyPriceEntry.date -> dailyPriceEntry,
          )
          val updatedRoomEntry = roomEntry.copy(
            dailyPrices = updatedDailyPrices,
            occupancyBreakdown = Some(
              OccupancyBreakdown(
                List(
                  aValidOccupancyUnit.withChargeType(Room).withSubChargeType(Adult).withQty(2),
                  aValidOccupancyUnit.withChargeType(ExtraBed).withSubChargeType(Adult).withQty(1),
                  aValidOccupancyUnit.withChargeType(Room).withSubChargeType(Child).withQty(1),
                ),
                4,
                MaxAllowedFreeChildAgeRange.empty,
                isApplyNewOccupancyLogic = true,
              ),
            ),
          )
          val updatedHotelEntry = hotelEntry1.copy(
            rooms = List(updatedRoomEntry),
            reqOcc = aValidReqOcc.copy(
              occ = YplOccInfo(
                _adults = Some(3),
                _rooms = Some(1),
                _children = Some(
                  YplChildren(
                    ages = List(Some(5)),
                  )),
              ),
            ),
          )

          "should compute correctly" in {
            val data = pricingRpmFlowMock.Data(Some(updatedHotelEntry), ctx, null)

            val result = pricingRpmFlowMock.calculateChildRateCategory(data)

            val prices = result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices

            // 2000 + (+100 (ratecategory value) * 2 adults)
            prices
              .find(p => p.roomNo.contains(1) && adultSubCharges.contains(p.subChargeType))
              .get
              .value should_== 2200.0

            // 500 + (+100 (ratecategory value) * 1 child)
            prices
              .filter(p => p.roomNo.contains(1) && p.subChargeType == SubChargeType.Child)
              .map(p => p.value)
              .sum should_== 600.0

            // 1000 + (+100 (ratecategory value) * 1 adult)
            prices
              .filter(p => p.roomNo.contains(1) && p.chargeType == ChargeType.ExtraBed)
              .map(p => p.value)
              .sum should_== 1100.0
          }
        }
      }

      "treat SubChargeType Adult and None as equivalents" in {
        val basePriceEntry = aValidPriceEntry.copy(
          value = 1000,
          chargeType = ChargeType.Room,
        )

        val dailyPriceEntry: DailyPrice = aValidDailyPrice.copy(
          prices = List(
            basePriceEntry.copy(
              subChargeType = SubChargeType.Adult,
              occupancy = 1,
            ),
            basePriceEntry.copy(
              subChargeType = SubChargeType.None,
              occupancy = 1,
            ),
          ),
        )

        val originalDailyPrices: Map[DateTime, DailyPrice] = Map(
          dailyPriceEntry.date -> dailyPriceEntry,
        )

        val childRateCategory = baseChildRateCategory.copy(
          value = -100,
          applyTo = "PGPN",
          isChildRateEnabled = false,
        )

        val roomEntry = aValidRoomEntry.copy(
          rateCategory = childRateCategory,
          dailyPrices = originalDailyPrices,
          roomAllocationInfo = Map(
            1 -> YPLRoomAllocation(adults = 1, childrenCountAsRoomOcc = 0, childrenTypes = Map()),
          ),
        )

        val hotelEntry1 = hotelEntry.copy(
          rooms = List(
            roomEntry,
          ),
          reqOcc = aValidReqOcc.copy(
            occ = YplOccInfo(
              _adults = Some(2),
              _rooms = Some(1),
            ),
          ),
        )

        val data = pricingRpmFlowMock.Data(Some(hotelEntry1), ctx, null)

        val result = pricingRpmFlowMock.calculateChildRateCategory(data)

        val prices = result.get.rooms.head.dailyPrices(dailyPriceEntry.date).prices

        // 1000 + (-100 (ratecategory value) * 2 adults / (1 room * 2 objects))
        prices.find(_.subChargeType == SubChargeType.Adult).get.value should_== 900
        prices.find(_.subChargeType == SubChargeType.None).get.value should_== 900
      }
    }
  }

  // scalastyle:off
  "Split and Merge" should {

    "Return empty data when input is empty" in {
      val stub = new PricingRpmFlowMockImpl

      val resF = stub.splitAndMergeRPMFlow(None, aValidYplContext)(_ => None)

      resF.map {
        _ must be(None)
      }.await
    }

    "Return the same number of rooms when input is not empty" in {
      val stub = new PricingRpmFlowMockImpl

      val numRooms = 100
      val rooms = (for (_ <- 1 to numRooms) yield aValidRoomEntry.build).toList
      val hotelEntry = aValidHotelEntryModel.withRooms(rooms).build
      val roomsWithEntry = YplRoomsWithEntry((for (_ <- 1 to numRooms) yield aValidRoom).toList, hotelEntry)

      val res = Await.result(stub.splitAndMergeRPMFlow(Some(hotelEntry), aValidYplContext)(_ => Some(roomsWithEntry)),
                             10.seconds)

      res.get.hotel.rooms.size must_== numRooms
    }

    "Fail the entire batch when a subset fails" in {
      val stub = new PricingRpmFlowMockImpl

      def roomType3Fails(yplHEM: YplHotelEntryModel): Option[YplRoomsWithEntry] =
        if (yplHEM.rooms.map(_.roomTypeId).contains(3L)) throw new RuntimeException("") else None

      val numRooms = 100
      val rooms = (for (i <- 1 to numRooms) yield aValidRoomEntry.withRoomTypeId(i).build).toList
      val hotelEntry = aValidHotelEntryModel.withRooms(rooms).build

      val res = stub.splitAndMergeRPMFlow(Some(hotelEntry), aValidYplContext)(roomType3Fails)

      res must throwAn[Exception].await
    }
  }

  "doFilterCheapestPriceRooms" should {
    val pricingRpmFlowMock = new PricingRpmFlowMockImpl

    val mockBenefitEntry = BenefitEntry(1, 10, 9, None)
    val breakfastRoomEntry = aValidRoomEntry
      .withRoomTypeId(1)
      .withRateCategoryEntry(aValidRateCategoryEntry.withBenefits(List(mockBenefitEntry)))
      .build
    val breakfastForOneRoomEntry = aValidRoomEntry
      .withRoomTypeId(2)
      .withRateCategoryEntry(aValidRateCategoryEntry.withBenefits(List(mockBenefitEntry.copy(benefitId = 20))))
      .build
    val breakfastForTwoRoomEntry = aValidRoomEntry
      .withRoomTypeId(3)
      .withRateCategoryEntry(aValidRateCategoryEntry.withBenefits(List(mockBenefitEntry.copy(benefitId = 26))))
      .build
    val breakfastForVipRoomEntry = aValidRoomEntry
      .withRoomTypeId(4)
      .withRateCategoryEntry(aValidRateCategoryEntry.withBenefits(List(mockBenefitEntry.copy(benefitId = 216))))
      .build
    val breakfastGroupRoomEntry = aValidRoomEntry
      .withRoomTypeId(4)
      .withRateCategoryEntry(
        aValidRateCategoryEntry.withBenefits(List(mockBenefitEntry.copy(benefitId = 99, benefitGroupId = Some(1)))))
      .build

    val nonBreakfastRoomEntry = aValidRoomEntry
      .withRoomTypeId(10)
      .withRateCategoryEntry(aValidRateCategoryEntry.withBenefits(List(mockBenefitEntry.copy(benefitId = 99))))
      .build

    val ctx = aValidYplContext.withRequest(aValidYplRequest.withCheapestRoomFilters(List(0)))

    val ctxAPORequestWithExpB = aValidYplContext.withRequest(
      aValidYplRequest.withCheapestRoomFilters(List(0)).withFlagInfo(YplFlagInfo(filterAPO = true)))

    val roomEntries = List(breakfastRoomEntry,
                           breakfastForOneRoomEntry,
                           breakfastForTwoRoomEntry,
                           breakfastForVipRoomEntry,
                           breakfastGroupRoomEntry,
                           nonBreakfastRoomEntry)

    "doFilterCheapestPriceRooms" in {
      val data = pricingRpmFlowMock.Data(Some(aValidHotelEntryModel.withRooms(roomEntries).build), ctx, null)
      val res = pricingRpmFlowMock.doFilterCheapestPriceRooms(data)
      res.get.rooms.size should_== 5
    }

    "doFilterCheapestPriceRooms but there is no breakfast rooms" in {
      val data =
        pricingRpmFlowMock.Data(Some(aValidHotelEntryModel.withRooms(List(nonBreakfastRoomEntry)).build), ctx, null)
      val res = pricingRpmFlowMock.doFilterCheapestPriceRooms(data)
      res.get.rooms.isEmpty should_== true
    }

    "doFilterCheapestPriceRooms but request is APO request" in {
      val data =
        pricingRpmFlowMock.Data(Some(aValidHotelEntryModel.withRooms(roomEntries).build), ctxAPORequestWithExpB, null)
      val res = pricingRpmFlowMock.doFilterCheapestPriceRooms(data)
      res.get.rooms.size should_== 6 // return all the rooms and skip heuristic in case of APO Secret Deal request
    }

  }

  "doFilterByFilterCriteria" should {

    val pricingRpmFlowMock = new PricingRpmFlowMockImpl

    val freeCancellableRoom1 = aValidRoom.withRoomTypeId(1).withCXLCode("1D1N_1N").build
    val freeCancellableRoom2 = aValidRoom.withRoomTypeId(1).withCXLCode("1D100P").build
    val freeCancellableRoom3 = aValidRoom.withRoomTypeId(1).withCXLCode("0D0N_1N").build
    val nrfRoom1 = aValidRoom.withRoomTypeId(1).withCXLCode("5D1N_1N").build
    val nrfRoom2 = aValidRoom.withRoomTypeId(1).withCXLCode("365D25P_10D50P_100P").build
    val nrfRoom3 = aValidRoom.withRoomTypeId(1).withCXLCode("NA").build
    val nrfRoom4 = aValidRoom.withRoomTypeId(1).withCXLCode("").build

    val bookingDate = new DateTime()
    val checkInDate = bookingDate.withTimeAtStartOfDay().plusDays(3)
    val gmtOffsetOpt = 7
    val gmtOffsetMinutes = 30

    val mockListYplRoomsWithEntry =
      List(freeCancellableRoom1, freeCancellableRoom2, freeCancellableRoom3, nrfRoom1, nrfRoom2, nrfRoom3, nrfRoom4)
    val hotelEntryWithMeta: YplHotelEntryModel = aValidHotelEntryModel.withMetaData(
      aValidHotelInfo.withGmtOffset(gmtOffsetOpt).withGmtOffsetMinutes(gmtOffsetMinutes))

    "1) filterCriteria is not present" in {
      val ctx = aValidYplContext.withRequest(
        aValidYplRequest.withCheapestRoomFilters(List(0)).withCheckIn(checkInDate).withBookingDate(bookingDate))
      val data =
        pricingRpmFlowMock.Data(Some(YplRoomsWithEntry(mockListYplRoomsWithEntry, hotelEntryWithMeta)), ctx, null)
      val res = pricingRpmFlowMock.filterByFilterCriteria(data)
      res.get.rooms.size should_== 7
    }

    "2) filterCriteria = freeCancellable is present" in {
      val ctx = aValidYplContext.withRequest(
        aValidYplRequest
          .withCheapestRoomFilters(List(0))
          .withFilterCriteria(Some(List(FilterCriteria.FreeCancellable)))
          .withCheckIn(checkInDate)
          .withBookingDate(bookingDate))
      val data =
        pricingRpmFlowMock.Data(Some(YplRoomsWithEntry(mockListYplRoomsWithEntry, hotelEntryWithMeta)), ctx, null)
      val res = pricingRpmFlowMock.filterByFilterCriteria(data)
      res.get.rooms.size should_== 3
    }

    "3) filterCriteria = freeCancellable is present and all are Non Free Cancellable" in {
      val ctx = aValidYplContext.withRequest(
        aValidYplRequest
          .withCheapestRoomFilters(List(0))
          .withFilterCriteria(Some(List(FilterCriteria.FreeCancellable)))
          .withCheckIn(checkInDate)
          .withBookingDate(bookingDate))
      val data = pricingRpmFlowMock.Data(Some(YplRoomsWithEntry(List(nrfRoom1, nrfRoom2, nrfRoom3), hotelEntryWithMeta)),
                                         ctx,
                                         null)
      val res = pricingRpmFlowMock.filterByFilterCriteria(data)
      res.get.rooms.isEmpty should_== true
    }

    "4) filterCriteria = freeCancellable is present and all are Free Cancellable" in {
      val ctx = aValidYplContext.withRequest(
        aValidYplRequest
          .withCheapestRoomFilters(List(0))
          .withFilterCriteria(Some(List(FilterCriteria.FreeCancellable)))
          .withCheckIn(checkInDate)
          .withBookingDate(bookingDate))
      val data = pricingRpmFlowMock.Data(
        Some(
          YplRoomsWithEntry(List(freeCancellableRoom1, freeCancellableRoom2, freeCancellableRoom3), hotelEntryWithMeta)),
        ctx,
        null)
      val res = pricingRpmFlowMock.filterByFilterCriteria(data)
      res.get.rooms.size should_== 3
    }

    "5) filterCriteria = nonRefundable is present and all are Free Cancellable" in {
      val ctx = aValidYplContext.withRequest(
        aValidYplRequest
          .withCheapestRoomFilters(List(0))
          .withFilterCriteria(Some(List()))
          .withCheckIn(checkInDate)
          .withBookingDate(bookingDate))
      val data =
        pricingRpmFlowMock.Data(Some(YplRoomsWithEntry(mockListYplRoomsWithEntry, hotelEntryWithMeta)), ctx, null)
      val res = pricingRpmFlowMock.filterByFilterCriteria(data)
      res.get.rooms.size should_== 7
    }

  }

  "filterFinalRooms" should {
    val pricingRpmFlowMock = new PricingRpmFlowMockImpl
    val channelDiscounts1 = List(
      YplChannelDiscountBreakdown(2, true, 10.0, 8.5),
      YplChannelDiscountBreakdown(27, false, 30.1, 25.585),
      YplChannelDiscountBreakdown(7, false, 30.0, 25.5),
      YplChannelDiscountBreakdown(6, false, 30.0, 25.5),
    )
    val channelDiscounts2 = List(
      YplChannelDiscountBreakdown(2, true, 10.0, 8.5),
      YplChannelDiscountBreakdown(27, false, 10.1, 25.585),
      YplChannelDiscountBreakdown(7, false, 10.0, 25.5),
      YplChannelDiscountBreakdown(6, false, 10.0, 25.5),
    )
    val yplChannel1 = aValidYplChannel.withBaseChannel(2).withStackChannels(Set(27, 7, 6))
    val yplChannel2 = aValidYplChannel.withBaseChannel(2).withStackChannels(Set.empty)
    val zeroPrice = aValidPrice.withNetExclusive(0).withMargin(0).withChannelDiscountBreakdown(channelDiscounts1)
    val normalPrice = aValidPrice.withChannelDiscountBreakdown(channelDiscounts2)
    val yplRoom1 = aValidRoom.withRoomTypeId(1).withPrice(zeroPrice).withChannel(yplChannel1).build
    val yplRoom2 = aValidRoom.withRoomTypeId(2).withPrice(normalPrice).withChannel(yplChannel1).build
    val yplRoom3 = aValidRoom.withRoomTypeId(3).build
    val yplRoom4 = aValidRoom.withRoomTypeId(4).build
    val yplRoom5 = aValidRoom.withRoomTypeId(5).withStatus(RatePlanStatus.ReturnToClient).build
    val yplRoom6 = aValidRoom.withRoomTypeId(6).withPrice(zeroPrice).withChannel(yplChannel2).build
    val yplRoom7 = aValidRoom.withRoomTypeId(7).withPrices(List.empty).withRoomFeatures(RoomFeatures()).build

    def test(listOfYplRooms: List[YPLRoom], expectedFilteredRoomsCount: Int, expectedMeasurementsCount: Int) = {
      val aggregateReporter = new AggregateReporter {

        val metricList = scala.collection.mutable.ListBuffer.empty[(Currency, CountryId, Map[Currency, Currency])]

        def count(metricKey: String, count: Long = 1L, tags: Map[String, String] = Map.empty[String, String]): Unit = {}

        def aggregate(metricKey: String, value: Long, tags: Map[String, String] = Map.empty[String, String]): Unit =
          metricList += Tuple3(metricKey, value, tags)

        override def run(): Unit = {}
      }
      val ctx = YplContext(aValidYplRequest, aggregateReporter = aggregateReporter)
      val yplHotelEntryModel = aValidHotelEntryModel
      val yplRoomsWithEntry = YplRoomsWithEntry(listOfYplRooms, yplHotelEntryModel)
      val yplRoomEntryData = pricingRpmFlowMock.Data(Some(yplRoomsWithEntry), ctx, null)
      val filteredRooms = pricingRpmFlowMock.filterFinalRooms(yplRoomEntryData)
      filteredRooms.map(_.rooms.size).getOrElse(0) should_== expectedFilteredRoomsCount
      if (expectedMeasurementsCount > 0) {
        aggregateReporter.metricList.filter { case (metric, value, tags) =>
          metric == Measurements.roomsWithStackDiscountRemovedDueToInvalidPrice && value == expectedMeasurementsCount && tags.isEmpty
        }.size should_== 1
      } else {
        aggregateReporter.metricList.size should_== 0
      }
    }

    "should not send metric if all rooms are valid" in {
      val listOfYplRooms = List(yplRoom2, yplRoom3, yplRoom4)
      test(listOfYplRooms, 3, 0)
    }

    "should not send metric if some rooms are invalid with status but not due to stackchannels" in {
      val listOfYplRooms = List(yplRoom2, yplRoom3, yplRoom4, yplRoom5, yplRoom6, yplRoom7)
      test(listOfYplRooms, 3, 0)
    }

    "should send metric if found rooms are invalid due to invalid price and have stackchannels" in {
      val listOfYplRooms = List(yplRoom1, yplRoom2, yplRoom3, yplRoom4)
      test(listOfYplRooms, 3, 1)
    }

    "for experiment JTBFUN-1390" should {
      val yplNonOTAJapanicanRoom = yplRoom2
        .withYplRoomEntry(
          aValidRoomEntry.withInventoryType(InventoryType.Agoda).build,
        )
        .build
      val yplOTAJapanicanRoom = yplRoom2
        .withYplRoomEntry(
          aValidRoomEntry.withInventoryType(InventoryType.JtbOTAJapanican).build,
        )
        .build

      val yplHotelEntryModel = aValidHotelEntryModel
      val listOfYplRooms = List(yplNonOTAJapanicanRoom, yplOTAJapanicanRoom)
      val yplRoomsWithEntry = YplRoomsWithEntry(listOfYplRooms, yplHotelEntryModel)

      "not filter OTAJapanican rooms if Variant A" in {
        val req = aValidYplRequest
        val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))
        val yplRoomEntryData = pricingRpmFlowMock.Data(Some(yplRoomsWithEntry), ctx, null)
        val filteredRooms = pricingRpmFlowMock.filterFinalRooms(yplRoomEntryData)
        filteredRooms.map(_.rooms.size).getOrElse(0) should_== listOfYplRooms.size
      }

      "filter OTAJapanican rooms if Variant B" in {
        val req = aValidYplRequest.withBExperiment(YplExperiments.REMOVE_OTA_JAPANICAN_KILL_SWITCH)
        val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))
        val yplRoomEntryData = pricingRpmFlowMock.Data(Some(yplRoomsWithEntry), ctx, null)
        val filteredRooms = pricingRpmFlowMock.filterFinalRooms(yplRoomEntryData)
        filteredRooms.map(_.rooms.size).getOrElse(0) should_== listOfYplRooms.size - 1
      }
    }
  }

  "isEscapesSupported" should {
    val pricingRpmFlowMock = new PricingRpmFlowMockImpl

    "return correctly on RateCategory with Some(StayPackageType.Escapes)" in {
      val mockRoom =
        aValidRoomEntry.withRateCategoryEntry(aValidRateCategoryEntry.withStayPackageType(Some(StayPackageType.Escapes)))
      pricingRpmFlowMock.isEscapesSupported(mockRoom) should_== true
    }

    "return correctly on RateCategory with Some(StayPackageType.NormalOffer)" in {
      val mockRoom = aValidRoomEntry.withRateCategoryEntry(
        aValidRateCategoryEntry.withStayPackageType(Some(StayPackageType.NormalOffer)))
      pricingRpmFlowMock.isEscapesSupported(mockRoom) should_== false
    }

    "return correctly on RateCategory with None" in {
      val mockRoom = aValidRoomEntry.withRateCategoryEntry(
        aValidRateCategoryEntry.withStayPackageType(Some(StayPackageType.NormalOffer)))
      pricingRpmFlowMock.isEscapesSupported(mockRoom) should_== false
    }
  }

  "addDmcData" should {
    trait TestFixture extends PricingRpmFlowMockImpl with Scope {
      override def addDmcData(yplHotel: YPLHotel): YPLHotel =
        yplHotel.copy(rooms = yplHotel.rooms.map(_.copy(dmcData = Some(DmcData(dmcUID = "JTB")))))
      override def addDmcDataForYcsHotel(yplHotel: YPLHotel): YPLHotel =
        yplHotel.copy(rooms = yplHotel.rooms.map(_.copy(dmcData = Some(DmcData(dmcUID = "YCS")))))
      override def addDmcDataForApmHotel(yplHotel: YPLHotel): YPLHotel =
        yplHotel.copy(rooms = yplHotel.rooms.map(_.copy(dmcData = Some(DmcData(dmcUID = "APM")))))
    }

    "Add correctly for JTB hotel" in new TestFixture {
      val res = addDmcData(Some(aValidHotel.withSupplierId(DMC.JTBWL).build))(aValidYplContext.build).toList
        .flatMap(_.rooms.flatMap(_.dmcData.map(_.dmcUID).toList))
      res.isEmpty should beFalse
      res.forall(_ == "JTB") should beTrue
    }

    "Add correctly under exp JTBFP-1295" in new TestFixture {
      // JTBFP-1295 = A
      val res = addDmcData(Some(aValidHotel.withSupplierId(DMC.JTBWL).build))(aValidYplContext.build).toList
        .flatMap(_.rooms.flatMap(_.dmcData.map(_.dmcUID).toList))
      res.isEmpty should beFalse
      res.forall(_ == "JTB") should beTrue

      val ctxB = aValidYplContext
        .withExperimentContext(forceBExperimentContext(YplExperiments.DMC_HARDCODING_REMOVAL))
        .withRequest(
          aValidYplRequest.copy(supplierFeatures = SupplierFeatures(features = Map(DMC.NoSupplier -> aValidFeature))))
        .build

      // JTBFP-1295 = B, SupplierFeatures = None
      val res2 = addDmcData(Some(aValidHotel.withSupplierId(DMC.JTBWL).build))(ctxB).toList
        .flatMap(_.rooms.flatMap(_.dmcData.map(_.dmcUID).toList))
      res2.isEmpty should beTrue

      val ctxBValidSupplier = aValidYplContext
        .withExperimentContext(forceBExperimentContext(YplExperiments.DMC_HARDCODING_REMOVAL))
        .withRequest(aValidYplRequest.copy(supplierFeatures = SupplierFeatures(features = Map(DMC.YCS -> aValidFeature))))
        .build

      // JTBFP-1295 = B, Supplier features don't exist for given supplier
      val res3 = addDmcData(Some(aValidHotel.withSupplierId(DMC.JTBWL).build))(ctxBValidSupplier).toList
        .flatMap(_.rooms.flatMap(_.dmcData.map(_.dmcUID).toList))
      res3.isEmpty should beTrue

      val ctxBValidFalsySupplier = aValidYplContext
        .withExperimentContext(forceBExperimentContext(YplExperiments.DMC_HARDCODING_REMOVAL))
        .withRequest(aValidYplRequest.copy(supplierFeatures = SupplierFeatures(features = Map(DMC.JTBWL -> aValidFeature))))
        .build

      // JTBFP-1295 = B, Supplier features is falsy
      val res4 = addDmcData(Some(aValidHotel.withSupplierId(DMC.JTBWL).build))(ctxBValidFalsySupplier).toList
        .flatMap(_.rooms.flatMap(_.dmcData.map(_.dmcUID).toList))
      res4.isEmpty should beTrue

      val ctxBValidTruthySupplier = aValidYplContext
        .withExperimentContext(forceBExperimentContext(YplExperiments.DMC_HARDCODING_REMOVAL))
        .withRequest(aValidYplRequest.copy(supplierFeatures = aValidSupplierFeatures))
        .build

      // JTBFP-1295 = B, Supplier features is truthy
      val res5 = addDmcData(Some(aValidHotel.withSupplierId(DMC.JTBWL).build))(ctxBValidTruthySupplier).toList
        .flatMap(_.rooms.flatMap(_.dmcData.map(_.dmcUID).toList))
      res5.isEmpty should beFalse
      res5.forall(_ == "JTB") should beTrue

    }

    "Add correctly for YCS hotel" in new TestFixture {
      val res = addDmcData(Some(aValidHotel.withSupplierId(DMC.YCS).build))(aValidYplContext.build).toList
        .flatMap(_.rooms.flatMap(_.dmcData.map(_.dmcUID).toList))
      res.isEmpty should beFalse
      res.forall(_ == "YCS") should beTrue
    }

    "Add correctly for APM hotel" in new TestFixture {
      val res = addDmcData(Some(aValidHotel.withSupplierId(apmDirectConnectSupplierList.head).build))(
        aValidYplContext.build).toList.flatMap(_.rooms.flatMap(_.dmcData.map(_.dmcUID).toList))
      res.isEmpty should beFalse
      res.forall(_ == "APM") should beTrue
    }

    "Do nothing for in-eligible hotel" in new TestFixture {
      val res = addDmcData(Some(aValidHotel.withSupplierId(DMC.BCOM).build))(aValidYplContext.build).toList
        .flatMap(_.rooms.flatMap(_.dmcData.map(_.dmcUID).toList))
      res.exists(List("JTB", "YCS", "APM").contains(_)) should beFalse
    }
  }

  "filter restricted ratecategory" should {
    val ctx = aValidYplContext.withRequest(aValidYplRequest)

    val pricingRpmFlowMock = new PricingRpmFlowMockImpl
    val rateCategoryId = 12345
    val roomEntry = aValidRoomEntry.withRateCategoryEntry(aValidRateCategoryEntry.withId(rateCategoryId))

    "filter out when ratecategory matched with restricted list" in {
      val hotelMeta = aValidHotelInfo.withRestrictedRatecategoryIds(Set(rateCategoryId))
      val yplHotelEntry = aValidHotelEntryModel.withRooms(List(roomEntry)).withMetaData(hotelMeta).build
      val yplHotelEntryData = pricingRpmFlowMock.Data(Some(yplHotelEntry), ctx, null)

      val result = pricingRpmFlowMock.filterRestrictedRatecategory(yplHotelEntryData)
      result.map(_.rooms.size).getOrElse(-1) should_== 0
    }

    "not filter out when ratecategory not matched with restricted list" in {
      val hotelMeta = aValidHotelInfo.withRestrictedRatecategoryIds(Set(999))
      val yplHotelEntry = aValidHotelEntryModel.withRooms(List(roomEntry)).withMetaData(hotelMeta).build
      val yplHotelEntryData = pricingRpmFlowMock.Data(Some(yplHotelEntry), ctx, null)
      val result = pricingRpmFlowMock.filterRestrictedRatecategory(yplHotelEntryData)
      result.map(_.rooms.size).getOrElse(-1) should_== 1
    }

    "not filter out when restricted list is empty" in {
      val hotelMeta = aValidHotelInfo.withRestrictedRatecategoryIds(Set.empty)
      val yplHotelEntry = aValidHotelEntryModel.withRooms(List(roomEntry)).withMetaData(hotelMeta).build
      val yplHotelEntryData = pricingRpmFlowMock.Data(Some(yplHotelEntry), ctx, null)
      val result = pricingRpmFlowMock.filterRestrictedRatecategory(yplHotelEntryData)
      result.map(_.rooms.size).getOrElse(-1) should_== 1
    }
  }

  "calculate offer statistics" in {
    val room110 = aValidRoom
      .withRoomTypeId(1)
      .withChannel(YplMasterChannel.RTL)
      .withRateCategoryId(1)
      .withSupplierId(DMC.YCS)
      .withFences(Set.empty)
    val room120 = aValidRoom
      .withRoomTypeId(2)
      .withChannel(YplMasterChannel.RTL)
      .withRateCategoryId(1)
      .withSupplierId(DMC.YCS)
      .withFences(Set(aValidRateFence, YplRateFence("SG", 1, 1)))
    val room111 =
      aValidRoom.withRoomTypeId(3).withChannel(YplMasterChannel.APS).withRateCategoryId(1).withSupplierId(DMC.YCS)
    val room211 =
      aValidRoom.withRoomTypeId(1).withChannel(YplMasterChannel.RTL).withRateCategoryId(1).withSupplierId(DMC.BCOM)
    val room212 =
      aValidRoom.withRoomTypeId(1).withChannel(YplMasterChannel.RTL).withRateCategoryId(2).withSupplierId(DMC.BCOM)
    val yplRooms: List[YPLRoom] = List(room110, room111, room120, room211, room212)
    val yplHotel = aValidHotel withRooms yplRooms
    val ypl = new PricingRpmFlowMockImpl
    val hotelWithStats: YPLHotel = ypl.setSupplierStats(yplHotel)
    val stats = hotelWithStats.stats

    stats should_!= None
    stats should_== Map(
      DMC.YCS -> YplSupplierStats(roomsCalculated = 4, roomTypes = 3, rateCategories = 1, ratePlans = 2),
      DMC.BCOM -> YplSupplierStats(roomsCalculated = 2, roomTypes = 1, rateCategories = 2, ratePlans = 1),
    )
  }

  "occupancyPricing" should {
    class PricingRpmFlowImplMockClass extends PricingRpmFlowImplMock[YplContext]

    def mockCallOccupancyPricing(yplHotelEntry: YplHotelEntryModel, ctx: YplContext): PricingRpmFlowImplMockClass = {
      val mockPricingRpmFlowMock: PricingRpmFlowImplMockClass =
        org.mockito.Mockito.mock(classOf[PricingRpmFlowImplMockClass])
      val yplHotelEntryData = mockPricingRpmFlowMock.Data(Some(yplHotelEntry), ctx, null)
      org.mockito.Mockito
        .when(mockPricingRpmFlowMock.filterRoomForRateLevelChildRates(any())(any(), any()))
        .thenCallRealMethod()
      org.mockito.Mockito.when(mockPricingRpmFlowMock.occupancyPricing(any())).thenCallRealMethod()
      org.mockito.Mockito.when(mockPricingRpmFlowMock.calculateOccupancy(any())(any())).thenCallRealMethod()
      org.mockito.Mockito
        .when(mockPricingRpmFlowMock.calculateOccupancyNewLogic(any(), any())(any()))
        .thenCallRealMethod()
      org.mockito.Mockito
        .when(mockPricingRpmFlowMock.generateOfferOccupancyFromChildAgePolicy(any(), any(), any(), any(), any(), any()))
        .thenCallRealMethod()
      mockPricingRpmFlowMock.occupancyPricing(yplHotelEntryData)
      mockPricingRpmFlowMock
    }

    "calculateOccupancy correctly" in {
      "DMC: 332" in new Scope {
        val ctx = aValidYplContext.withRequest(aValidYplRequest)

        val yplHotelEntry = aValidHotelEntryModel.withSupplierId(DMC.YCS)
        val mockPricingRpmFlowMock = mockCallOccupancyPricing(yplHotelEntry, ctx)
        verify(mockPricingRpmFlowMock, times(0)).calculateOccupancyNewLogic(any(), any())(any())
        verify(mockPricingRpmFlowMock, times(1)).calculateOccupancy(any())(any())
        verify(mockPricingRpmFlowMock, times(1)).generateOfferOccupancyFromChildAgePolicy(any(),
                                                                                          any(),
                                                                                          any(),
                                                                                          any(),
                                                                                          any(),
                                                                                          any())
      }
    }

    "calculateOccupancyNewLogic correctly" in {
      val yplRequest = YplRequest(
        "",
        aValidCheckIn,
        aValidCheckIn.plusDays(aValidLos),
        channels = Set(YplMasterChannel.RTL, YplMasterChannel.APS),
        flagInfo = YplFlagInfo(true),
        occ = YplOccInfo(Some(1), None, Some(1)),
        bookingDate = aValidCheckIn.minusDays(4),
        cInfo = aValidClientInfo,
        whitelabelSetting = aValidwhitelabelSetting,
        supplierFeatures = aValidSupplierFeatures,
        apmSetting = Some(aValidApmSetting),
        fences = Map(YplMasterChannel.RTL -> Set(aValidRateFence), YplMasterChannel.APS -> Set(aValidRateFence)),
        applyTaxOnSellExSettings = Some(aValidApplyTaxOnSellExSettings),
        superAggOccupancySamplingRate = aValidsuperAggOccupancySamplingRate,
        isApplyNewOccupancyLogic = true,
      )
      val ctx = aValidYplContext
        .withRequest(yplRequest)
        .withSupplierSetting(Map(DMC.JTBWL -> aValidYplSupplierSetting.withIsThirdParty(true)))
      val dispatchedChannels = aValidYplDispatchChannels.withMasterChannels(Set(YplChannel(-899, Set.empty, -899)))

      "Pre Condition, isYCS: true & isAllOcc: false(occ search)" in new Scope {
        val yplHotelEntry = aValidHotelEntryModel.withSupplierId(DMC.YCS)
        val ctxWithIsAllOccFalse = ctx.withRequest(
          yplRequest
            .withFlagInfo(aValidYplFlagInfo.withIsAllOcc(false))
            .withOccupancyInfo(aValidOccInfo.withAdults(1).withChildAges().withRooms(1)))
        val mockPricingRpmFlowMock = mockCallOccupancyPricing(yplHotelEntry, ctxWithIsAllOccFalse)
        verify(mockPricingRpmFlowMock, times(1)).calculateOccupancyNewLogic(any(), any())(any())
        verify(mockPricingRpmFlowMock, times(0)).calculateOccupancy(any())(any())
      }

      "Pre Condition, isYCS: true & isAllOcc: ture" in new Scope {
        val yplHotelEntry = aValidHotelEntryModel.withSupplierId(DMC.YCS)
        val ctxWithIsAllOccTrue = ctx.withRequest(yplRequest.withFlagInfo(aValidYplFlagInfo.withIsAllOcc(true)))
        val mockPricingRpmFlowMock = mockCallOccupancyPricing(yplHotelEntry, ctxWithIsAllOccTrue)
        verify(mockPricingRpmFlowMock, times(1)).calculateOccupancyNewLogic(any(), any())(any())
        verify(mockPricingRpmFlowMock, times(0)).calculateOccupancy(any())(any())
      }

      "Pre Condition, isYCS: false & isDirectConnect: false" in new Scope {
        val yplHotelEntry = aValidHotelEntryModel.withSupplierId(DMC.JTBWL)
        val mockPricingRpmFlowMock = mockCallOccupancyPricing(yplHotelEntry, ctx)
        verify(mockPricingRpmFlowMock, times(0)).calculateOccupancyNewLogic(any(), any())(any())
        verify(mockPricingRpmFlowMock, times(1)).calculateOccupancy(any())(any())
      }

      "Pre Condition, isYCS: false & isDirectConnect: true & isAllOcc: false (occ search adult only)" in new Scope {
        val yplHotelEntry = aValidHotelEntryModel.withSupplierId(DMC.Marriott)
        val ctxWithIsAllOccFalse = ctx.withRequest(yplRequest.withFlagInfo(aValidYplFlagInfo.withIsAllOcc(false)))
        val mockPricingRpmFlowMock = mockCallOccupancyPricing(yplHotelEntry, ctxWithIsAllOccFalse)
        verify(mockPricingRpmFlowMock, times(0)).calculateOccupancyNewLogic(any(), any())(any())
        verify(mockPricingRpmFlowMock, times(1)).calculateOccupancy(any())(any())
      }

      "Pre Condition, isYCS: false & isDirectConnect: true & isAllOcc: true" in new Scope {
        val yplHotelEntry = aValidHotelEntryModel.withSupplierId(DMC.Marriott)
        val ctxWithIsAllOccTrue = ctx.withRequest(yplRequest.withFlagInfo(aValidYplFlagInfo.withIsAllOcc(true)))
        val mockPricingRpmFlowMock = mockCallOccupancyPricing(yplHotelEntry, ctxWithIsAllOccTrue)
        verify(mockPricingRpmFlowMock, times(0)).calculateOccupancyNewLogic(any(), any())(any())
        verify(mockPricingRpmFlowMock, times(1)).calculateOccupancy(any())(any())
      }

//      for direct connect, we will include it later once we have the logic

//      "Pre Condition, isYCS: false & isDirectConnect: true & isAllOcc: false (occ search adult only)" in new Scope {
//        val yplHotelEntry = aValidHotelEntryModel.withSupplierId(DMC.Marriott)
//        val ctxWithIsAllOccFalse = ctx.withRequest(aValidYplRequest.withFlagInfo(aValidYplFlagInfo.withIsAllOcc(false)))
//        val mockPricingRpmFlowMock = mockCallOccupancyPricing(yplHotelEntry, ctxWithIsAllOccFalse)
//        verify(mockPricingRpmFlowMock, times(1)).calculateOccupancyNewLogic(any())(any())
//        verify(mockPricingRpmFlowMock, times(0)).calculateOccupancy(any())(any())
//      }
//
//      "Pre Condition, isYCS: false & isDirectConnect: true & isAllOcc: true" in new Scope {
//        val yplHotelEntry = aValidHotelEntryModel.withSupplierId(DMC.Marriott)
//        val ctxWithIsAllOccTrue = ctx.withRequest(aValidYplRequest.withFlagInfo(aValidYplFlagInfo.withIsAllOcc(true)))
//        val mockPricingRpmFlowMock = mockCallOccupancyPricing(yplHotelEntry, ctxWithIsAllOccTrue)
//        verify(mockPricingRpmFlowMock, times(1)).calculateOccupancyNewLogic(any())(any())
//        verify(mockPricingRpmFlowMock, times(0)).calculateOccupancy(any())(any())
//      }
    }
  }

  "filterRoHRooms" should {
    val pricingRpmFlowMock = new PricingRpmFlowMockImpl()
    val hotelId = 12345

    val masterRoomTypeId1 = 1L
    val masterRoomTypeId2 = 2L
    val masterRoomTypeId3 = 3L
    val masterRoomTypeId4 = 4L

    val room1 = aValidRoomEntry.build.copy(masterRoomTypeId = masterRoomTypeId1).build
    val room2 = aValidRoomEntry.build.copy(masterRoomTypeId = masterRoomTypeId2).build
    val room3 = aValidRoomEntry.build.copy(masterRoomTypeId = masterRoomTypeId3).build

    val hotel = aValidHotelEntryModel
      .withHotelId(hotelId)
      .withRooms(List(room1, room2, room3))
      .withMetaData(
        aValidHotelInfo.withEnabledRoom(
          Map(
            masterRoomTypeId1 -> aValidEnabledRoom.withRohFlag(true),
            masterRoomTypeId2 -> aValidEnabledRoom.withRohFlag(false),
            masterRoomTypeId3 -> aValidEnabledRoom.withRohFlag(true),
          ),
        ))
      .build

    val hotelWithoutEnabledRooms = hotel.copy(metaData = hotel.metaData.copy(enabledRoom = Map.empty))

    val rohExternalData = ExternalData("isRoH", "true")
    val nonRohExternalData = ExternalData("isRoH", "false")
    val otherExternalData = ExternalData("otherField", "otherValue")

    val supplierRateInfoWithRoH = aValidOTASupplierRateInfo.withExternalData(Seq(rohExternalData))
    val supplierRateInfoWithoutRoH = aValidOTASupplierRateInfo.withExternalData(Seq(nonRohExternalData))
    val supplierRateInfoWithOther = aValidOTASupplierRateInfo.withExternalData(Seq(otherExternalData))

    val roomWithRoHExternalData = aValidRoomEntry.build
      .copy(masterRoomTypeId = masterRoomTypeId1)
      .copy(dmcDataHolder = Some(aValidDMCDataHolder.withSupplierRateInfo(Some(supplierRateInfoWithRoH))))
      .build

    val roomWithoutRoHExternalData = aValidRoomEntry.build
      .copy(masterRoomTypeId = masterRoomTypeId2)
      .copy(dmcDataHolder = Some(aValidDMCDataHolder.withSupplierRateInfo(Some(supplierRateInfoWithoutRoH))))
      .build

    val roomWithOtherExternalData = aValidRoomEntry.build
      .copy(masterRoomTypeId = masterRoomTypeId3)
      .copy(dmcDataHolder = Some(aValidDMCDataHolder.withSupplierRateInfo(Some(supplierRateInfoWithOther))))
      .build

    "filter out rooms with rohFlag=true when RoH filtering is enabled and disableRoomOnArrival is true" in {
      val req = aValidYplRequest
        .copy(whitelabelSetting = aValidYplRequest.whitelabelSetting.copy(disableRoomOnArrival = true))
        .withBExperiment(YplExperiments.DISABLE_ROOM_ON_ARRIVAL)
      val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val data = pricingRpmFlowMock.Data(Some(hotel), ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beSome
      result.get.rooms.size should_== 1
      result.get.rooms.head.masterRoomTypeId must_== masterRoomTypeId2 // Only room2 should remain as it has rohFlag=false
    }

    "not filter any rooms when RoH filtering is disabled (experiment disabled, feature enabled)" in {
      val req = aValidYplRequest
        .copy(whitelabelSetting = aValidYplRequest.whitelabelSetting.copy(disableRoomOnArrival = true))
        .withAExperiment(YplExperiments.DISABLE_ROOM_ON_ARRIVAL)
      val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val data = pricingRpmFlowMock.Data(Some(hotel), ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beSome
      result.get.rooms.size should_== 3
      result.get.rooms.map(_.masterRoomTypeId).toSet must_== Set(masterRoomTypeId1, masterRoomTypeId2, masterRoomTypeId3)
    }

    "not filter any rooms when disableRoomOnArrival is false and experiment is enabled" in {
      val req = aValidYplRequest
        .copy(whitelabelSetting = aValidYplRequest.whitelabelSetting.copy(disableRoomOnArrival = false))
        .withBExperiment(YplExperiments.DISABLE_ROOM_ON_ARRIVAL)
      val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val data = pricingRpmFlowMock.Data(Some(hotel), ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beSome
      result.get.rooms.size should_== 3
      result.get.rooms.map(_.masterRoomTypeId).toSet must_== Set(masterRoomTypeId1, masterRoomTypeId2, masterRoomTypeId3)
    }

    "not filter any rooms when both experiment and disableRoomOnArrival are false" in {
      val ctx = aValidYplContext.withRequest(aValidYplRequest)
      val data = pricingRpmFlowMock.Data(Some(hotel), ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beSome
      result.get.rooms.size should_== 3
      result.get.rooms.map(_.masterRoomTypeId).toSet must_== Set(masterRoomTypeId1, masterRoomTypeId2, masterRoomTypeId3)
    }

    "handle hotel with no rooms" in {
      val req = aValidYplRequest
        .copy(whitelabelSetting = aValidYplRequest.whitelabelSetting.copy(disableRoomOnArrival = true))
        .withBExperiment(YplExperiments.DISABLE_ROOM_ON_ARRIVAL)
      val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val emptyHotel = hotel.copy(rooms = List.empty)
      val data = pricingRpmFlowMock.Data(Some(emptyHotel), ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beSome
      result.get.rooms.size should_== 0
    }

    "handle hotel with no enabled rooms metadata" in {
      val req = aValidYplRequest
        .copy(whitelabelSetting = aValidYplRequest.whitelabelSetting.copy(disableRoomOnArrival = true))
        .withBExperiment(YplExperiments.DISABLE_ROOM_ON_ARRIVAL)
      val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val data = pricingRpmFlowMock.Data(Some(hotelWithoutEnabledRooms), ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beSome
      result.get.rooms.size should_== 3
    }

    "handle None hotel data" in {
      val req = aValidYplRequest
        .copy(whitelabelSetting = aValidYplRequest.whitelabelSetting.copy(disableRoomOnArrival = true))
        .withBExperiment(YplExperiments.DISABLE_ROOM_ON_ARRIVAL)
      val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val data = pricingRpmFlowMock.Data(None, ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beNone
    }

    "filter out rooms with isRoH=true in external data when RoH filtering is enabled" in {
      val hotelWithExternalData = aValidHotelEntryModel
        .withHotelId(hotelId)
        .withRooms(List(roomWithRoHExternalData, roomWithoutRoHExternalData, roomWithOtherExternalData))
        .withMetaData(
          aValidHotelInfo.withEnabledRoom(
            Map(
              masterRoomTypeId1 -> aValidEnabledRoom
                .withRohFlag(false), // MetaData says not RoH, but external data says RoH
              masterRoomTypeId2 -> aValidEnabledRoom.withRohFlag(false), // Both metadata and external data say not RoH
              masterRoomTypeId3 -> aValidEnabledRoom
                .withRohFlag(false), // Metadata says not RoH, external data has other field
            ),
          ))
        .build

      val req = aValidYplRequest
        .copy(whitelabelSetting = aValidYplRequest.whitelabelSetting.copy(disableRoomOnArrival = true))
        .withBExperiment(YplExperiments.DISABLE_ROOM_ON_ARRIVAL)
      val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val data = pricingRpmFlowMock.Data(Some(hotelWithExternalData), ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beSome
      result.get.rooms.size should_== 2
      result.get.rooms.map(_.masterRoomTypeId).toSet must_== Set(masterRoomTypeId2, masterRoomTypeId3)
    }

    "filter out rooms with both metadata rohFlag=true AND external data isRoH=true when RoH filtering is enabled" in {
      val roomWithExternalRoHOnly = aValidRoomEntry.build
        .copy(masterRoomTypeId = masterRoomTypeId3)
        .copy(dmcDataHolder = Some(aValidDMCDataHolder.withSupplierRateInfo(Some(supplierRateInfoWithRoH))))
        .build

      val roomWithNeitherRoH = aValidRoomEntry.build
        .copy(masterRoomTypeId = masterRoomTypeId4)
        .copy(dmcDataHolder = Some(aValidDMCDataHolder.withSupplierRateInfo(Some(supplierRateInfoWithoutRoH))))
        .build

      val hotelWithMixedRoH = aValidHotelEntryModel
        .withHotelId(hotelId)
        .withRooms(List(roomWithRoHExternalData, roomWithoutRoHExternalData, roomWithExternalRoHOnly, roomWithNeitherRoH))
        .withMetaData(
          aValidHotelInfo.withEnabledRoom(
            Map(
              masterRoomTypeId1 -> aValidEnabledRoom.withRohFlag(true), // Both metadata and external data say RoH
              masterRoomTypeId2 -> aValidEnabledRoom.withRohFlag(true), // Metadata says RoH, external data says not RoH
              masterRoomTypeId3 -> aValidEnabledRoom
                .withRohFlag(false), // Metadata says not RoH, external data says RoH
              masterRoomTypeId4 -> aValidEnabledRoom.withRohFlag(false), // Both metadata and external data say not RoH
            ),
          ))
        .build

      val req = aValidYplRequest
        .copy(whitelabelSetting = aValidYplRequest.whitelabelSetting.copy(disableRoomOnArrival = true))
        .withBExperiment(YplExperiments.DISABLE_ROOM_ON_ARRIVAL)
      val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val data = pricingRpmFlowMock.Data(Some(hotelWithMixedRoH), ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beSome
      result.get.rooms.size should_== 1 // Only roomWithNeitherRoH should remain
      result.get.rooms.head.masterRoomTypeId must_== 4L
    }

    "not filter rooms when external data isRoH=false and metadata rohFlag=false" in {
      val hotelWithoutRoH = aValidHotelEntryModel
        .withHotelId(hotelId)
        .withRooms(List(roomWithoutRoHExternalData))
        .withMetaData(
          aValidHotelInfo.withEnabledRoom(
            Map(masterRoomTypeId2 -> aValidEnabledRoom.withRohFlag(false)),
          ))
        .build

      val req = aValidYplRequest
        .copy(whitelabelSetting = aValidYplRequest.whitelabelSetting.copy(disableRoomOnArrival = true))
        .withBExperiment(YplExperiments.DISABLE_ROOM_ON_ARRIVAL)
      val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val data = pricingRpmFlowMock.Data(Some(hotelWithoutRoH), ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beSome
      result.get.rooms.size should_== 1
      result.get.rooms.head.masterRoomTypeId must_== masterRoomTypeId2
    }

    "not filter rooms when no external data is present (dmcDataHolder is None)" in {
      val roomWithoutDmcData =
        aValidRoomEntry.build.copy(masterRoomTypeId = masterRoomTypeId1).copy(dmcDataHolder = None).build

      val hotelWithoutExternalData = aValidHotelEntryModel
        .withHotelId(hotelId)
        .withRooms(List(roomWithoutDmcData))
        .withMetaData(
          aValidHotelInfo.withEnabledRoom(
            Map(masterRoomTypeId1 -> aValidEnabledRoom.withRohFlag(false)),
          ))
        .build

      val req = aValidYplRequest
        .copy(whitelabelSetting = aValidYplRequest.whitelabelSetting.copy(disableRoomOnArrival = true))
        .withBExperiment(YplExperiments.DISABLE_ROOM_ON_ARRIVAL)
      val ctx = YplContext(req).withExperimentContext(forcedFromRequestExperimentContext(req))

      val data = pricingRpmFlowMock.Data(Some(hotelWithoutExternalData), ctx, null)
      val result = pricingRpmFlowMock.filterRoHRooms(data)

      result must beSome
      result.get.rooms.size should_== 1
      result.get.rooms.head.masterRoomTypeId must_== masterRoomTypeId1
    }
  }
}
