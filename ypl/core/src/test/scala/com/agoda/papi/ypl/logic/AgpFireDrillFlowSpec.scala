package com.agoda.papi.ypl.logic

import com.agoda.finance.tax.models.ReqOccByHotelAgePolicy
import com.agoda.finance.tax.services.tax.applytaxover.ApplyTaxOverHelper
import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.enums.room._
import com.agoda.papi.pricing.pricecalculation.models.tax.DailyTaxes
import com.agoda.papi.ypl.commission.apm.models.ApmTypes.RoomIndex
import com.agoda.papi.ypl.commission.apm.models._
import com.agoda.papi.ypl.commission.builder.MultipleAutoPriceMatchHolderBuilder
import com.agoda.papi.ypl.commission.models.YplRateFenceHolder
import com.agoda.papi.ypl.commission.service.CommissionServiceImpl
import com.agoda.papi.ypl.mocks.GrowthProgramServiceMock
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.models.api.request.YplAGXCommission
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.enums.{BreakdownStep, GrowthProgramCommissionBreakdown}
import com.agoda.papi.ypl.models.pricing._
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.services.TaxPrototypeServiceImpl
import com.agoda.utils.flow.PropertyContext
import com.agoda.utils.flow.ExperimentContext
import org.joda.time.DateTime
import org.mockito.Mockito.{times, verify, when}
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope

import scala.concurrent.ExecutionContext

// scalastyle:off
class AgpFireDrillFlowSpec
  extends SpecificationWithJUnit
    with YPLTestContexts
    with GrowthProgramServiceMock
    with AgpFireDrillFlow[YplContext]
    with CommissionServiceImpl
    with TaxPrototypeServiceImpl
    with YPLTestDataBuilders
    with Mockito {

  override implicit def ec: ExecutionContext = scala.concurrent.ExecutionContext.Implicits.global

  implicit val compositeChannelContext = aValidCompositeChannelContext

  "Agp FireDrill Flow" >> {

    trait AgpScope extends Scope with AgpTestTool {
      val EXPERIMENT_A: AbUser = 'A'
      val EXPERIMENT_B: AbUser = 'B'

      val checkIn = aValidDateTime
      val aValidStayDate1: StayDate = checkIn
      val aValidStayDate2: StayDate = checkIn.plusDays(1)
      val aValidStayDate3: StayDate = checkIn.plusDays(2)
      val aValidStayDate4: StayDate = checkIn.plusDays(3)
      val aValidLos: Int = 4

      val aValidRoomTypeId1: Long = 1111
      val aValidRoomTypeId2: Long = 2222
      val aValidRoomTypeId3: Long = 3333
      val aValidRoomTypeId4: Long = 4444

      val aValidRateCategoryId1: Int = 71
      val aValidRateCategoryId2: Int = 72
      val aValidRateCategoryId3: Int = 73

      val aValidYplChannelRTL = YplMasterChannel.RTL
      val aValidYplChannelAPS = YplMasterChannel.APS

      val YCSSupplierID: Int = DMC.YCS

      val aValidMarginPercentage = 15d

      def aValidRoomOccupancy = RoomOccupancy(adults = 2, children = 0, extraBeds = 0, maxExtraBeds = 0, infants = 0)

      def buildValidYPLPrice(chargeType: ChargeType,
                             date: StayDate,
                             netEx: Double,
                             multiplier: Double = 1d,
                             apmPriceAdjustmentDetail: Option[ApmPriceAdjustmentDetail] = None,
                             subChargeType: SubChargeType = SubChargeType.None): YplPrice = {
        val netExclusive = netEx * multiplier
        val margin = netExclusive * 0.15d
        val fee = netExclusive * 0.07d
        val tax = (netExclusive * 0.1d) + (fee * 0.1d)
        val pf = margin * 0.1d

        aValidPrice.copy(
          chargeType = chargeType,
          date = date,
          netExclusive = netExclusive,
          margin = margin,
          refMargin = margin,
          tax = tax,
          fee = fee,
          processingFee = pf,
          refProcessingFee = pf,
          quantity = 1,
          applyType = ApplyType.PB,
          chargeOption = ChargeOption.Mandatory,
          dailyTaxes = aValidDailyTax,
          apmPriceAdjustmentDetail = apmPriceAdjustmentDetail,
          subChargeType = subChargeType,
        )
      }

      def aValidYplDispatchChannels =
        YplDispatchChannels(Set(YplMasterChannel.RTL, YplMasterChannel.APS, YplMasterChannel.APM), Set.empty)

      def aHotelEntryModel = aValidHotelEntryModel
        .withHotelId(aValidHotelId)
        .withSupplierId(YCSSupplierID)
        .withDispatchChannels(aValidYplDispatchChannels)

      val originalRooms: List[YPLRoom] = List(
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Agency)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 90d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 45d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId2)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 105d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 54d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 90d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 45d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId2)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 120d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 120d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 120d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 120d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 60d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 60d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 60d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 60d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId2)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 105d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 54d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId3)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId3)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 40d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 10d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId4)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId3)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 40d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 10d),
          )),
      )

      val yplRoomsWithEntry: YplRoomsWithEntry = YplRoomsWithEntry(rooms = originalRooms, entry = aHotelEntryModel)

      def mockInputYplHotel(yplExperiments: YplExperiments): YPLHotel =
        mockInputYplHotelWithContext(mockYplContext(yplExperiments), Set(aValidRateFence))

      def mockInputYplHotelWithContext(context: YplContext, fences: Set[YplRateFence]): YPLHotel =
        PricingCommon.convertToHotel(yplRoomsWithEntry.entry,
                                     context.request,
                                     yplRoomsWithEntry.rooms.map(r => r.copy(fences = fences)))

      def mockYplContext(yplExperiments: YplExperiments): YplContext = YplContext(
        aValidYplRequest
          .withFences(aValidYplRequestFences + (YplMasterChannel.APM -> Set(aValidRateFence)))
          .build
          .copy(experiments = yplExperiments))

      val originalRoomsWithAPMRooms = originalRooms :+ aValidRoomWithAPMChannel

      def aHotelEntryModelWithYcsSupplierID = aHotelEntryModel.withSupplierId(DMC.YCS)

      val yplRoomsWithAPMRoomEntry: YplRoomsWithEntry =
        YplRoomsWithEntry(rooms = originalRoomsWithAPMRooms, entry = aHotelEntryModelWithYcsSupplierID)

      def mockInputYplHotelWithAPMRoomsAndContext(context: YplContext, fences: Set[YplRateFence]): YPLHotel =
        PricingCommon.convertToHotel(yplRoomsWithAPMRoomEntry.entry,
                                     context.request,
                                     yplRoomsWithAPMRoomEntry.rooms.map(r => r.copy(fences = fences)))

      def mockInputYplHotelWithAPMRooms(yplExperiments: YplExperiments): YPLHotel = {
        val hotel = mockInputYplHotelWithAPMRoomsAndContext(mockYplContext(yplExperiments), Set(aValidRateFence))
        hotel.withFireDrillProto(Some(aValidFireDrillProto))
      }

      def mockMetaData: HotelMeta = yplRoomsWithEntry.entry.metaData
    }

    trait AgpTestTool {}

    "FD-7360: AGP Lite Negative Test" >> {

      "isAgpLiteNegativeTest return false when mock channel from PLECS not found" >> new AgpScope {
        val exp = List.empty
        val mockHotel = mockInputYplHotel(exp)
        val result = AgpFireDrillFlow.isAgpLiteNegativeTest(mockHotel.dispatchChannels)

        result must_== false
      }

      "isAgpLiteNegativeTest return true when mock channel from PLECS found" >> new AgpScope {
        override def aValidYplDispatchChannels =
          YplDispatchChannels(Set(YplMasterChannel(Channel.AgpLiteNegativeMock), YplMasterChannel(Channel.RTL)),
                              Set.empty)

        val exp = List.empty
        val mockHotel = mockInputYplHotel(exp)
        val result = AgpFireDrillFlow.isAgpLiteNegativeTest(mockHotel.dispatchChannels)
        result must_== true
      }
    }

    "executeAgp" >> {
      val aValidMultipleAutoPriceMatchHolder = MultipleAutoPriceMatchHolderBuilder(
        MultipleAutoPriceMatchHolder(1,
                                     None,
                                     10d,
                                     1,
                                     10d,
                                     ApmHotelStatus.Active,
                                     DateTime.parse("2022-01-01"),
                                     None,
                                     Seq.empty,
                                     Seq.empty,
                                     Some(1)))

      "should call applyGrowthProgram" >> new AgpScope {
        val exp: List[YplExperiment] = List.empty
        val hotels = mockInputYplHotelWithAPMRooms(exp)
        val ctx = mockYplContext(exp)
        val flowMock = mock[AgpFireDrillFlowSpec]

        when(flowMock.executeAgp(hotels, mockMetaData)(ctx)).thenCallRealMethod()
        when(flowMock.getApmRoomChannelSettings(any(), any(), any()))
          .thenReturn(Seq(aValidMultipleAutoPriceMatchHolder.build))

        try {
          val result = flowMock.executeAgp(hotels, mockMetaData)(ctx)
        } catch {
          case e: Exception => None
        }

        verify(flowMock, times(1)).applyGrowthProgram(any[YPLHotel],
                                                      any[GmtOffset],
                                                      any[YplDispatchChannels],
                                                      any[Int],
                                                      anyBoolean)(any[YplContext])
      }
    }
  }

  override def calculateApmCommissionReductionResult(
    apmRoomParams: Map[Int, ApmCommissionDiscountRoomParameters],
    bookingDate: DateTime,
    hotelGmtOffset: Int,
    dispatchedMasterChannels: Set[Int],
    hotelSupplierId: Int,
    hotelId: Long,
    hotelCountryId: Long,
    priceDates: List[DateTime],
    removeBedPaidExclusionFromApmExp: Boolean,
    skipApmPriceAdjustmentForResellExp: Boolean,
    enableAiBedExp: Boolean,
    enableApmMultipleDiscount: Boolean = false,
    excludeRateChannelFromApmExp: Boolean,
    isApmFixEndDate: Boolean = false,
    enabledApmArpPlusProgram: Boolean,
    requestFences: Map[Int, Set[YplRateFenceHolder]] = Map.empty): Map[Int, ApmCommissionDiscountResult] = ???

  override def getAdditionalCommissionReductionByConfigLevel(allConfigs: Map[Int, ApmConfigHolder]): Double = ???

  override def getBlackoutDateListByConfigLevel(allConfigs: Map[Int, ApmConfigHolder]): Seq[DateTime] = ???

  override def isBlackoutDate(blackoutDates: Seq[DateTime], stayDate: DateTime): Boolean = ???

  override def calculatePrice(paymentModel: PaymentModel,
                              date: DateTime,
                              commissionPercent: Double,
                              agxCommission: YplAGXCommission,
                              commissionExcludingWholesaleOrAgx: => Double,
                              hotelTaxInfo: HotelTaxInfo,
                              dailyTaxes: DailyTaxes,
                              reqOcc: YplReqOccByHotelAgePolicy,
                              chargeType: ChargeType,
                              quantity: Int,
                              applyType: ApplyType,
                              chargeOption: ChargeOption,
                              promoDiscount: Double,
                              value: Double,
                              valueWithChannelDiscount: Double,
                              discountMessages: Map[DiscountMessageType, DiscountMessage],
                              roomPriceInfo: RoomPriceInfo,
                              supplierId: SupplierId,
                              subChargeType: SubChargeType,
                              roomNo: Option[Int],
                              supplierContractedCommissionFromCommissionHolder: Option[Double],
                              channelDiscountBreakdowns: List[YplChannelDiscountBreakdown],
                              resellRefSell: Option[Double],
                              currentBreakdownStep: BreakdownStep,
                              bookingPriceBreakdown: BookingPriceBreakdown,
                              apmPriceAdjustmentDetail: Option[ApmPriceAdjustmentDetail],
                              apmCommissionDiscountPercent: Double,
                              childAgeRangeId: Option[Long],
                              hotelId: HotelId,
                              chainId: ChainId,
                              countryId: CountryId,
                              sellExForMarginAdjustment: Option[Double] = None,
                              supplierFundedDiscount: Option[Double] = None,
                              uspaDiscountAmount: Option[Double] = None,
                              uspaProgramId: Option[Int] = None)(
    isBcomFixTaxAmountApplyToPB: Boolean)(implicit ctx: YplContext, propertyContext: PropertyContext): YplPrice = ???

  override def calculatePriceForPriceCalculationRefactor(
    paymentModel: PaymentModel,
    date: StayDate,
    commissionPercent: AGXCommission,
    agxCommission: YplAGXCommission,
    commissionExcludingWholesaleOrAgx: => AGXCommission,
    hotelTaxInfo: HotelTaxInfo,
    dailyTaxes: DailyTaxes,
    reqOcc: ReqOccByHotelAgePolicy,
    chargeType: ChargeType,
    quantity: TaxProtoTypeID,
    applyType: ApplyType,
    chargeOption: ChargeOption,
    promoDiscount: AGXCommission,
    valueWithChannelDiscount: AGXCommission,
    discountMessages: Map[DiscountMessageType, DiscountMessage],
    roomPriceInfo: RoomPriceInfo,
    supplierId: SupplierId,
    subChargeType: SubChargeType,
    roomNo: Option[TaxProtoTypeID],
    supplierContractedCommissionFromCommissionHolder: Option[AGXCommission],
    channelDiscountBreakdowns: List[YplChannelDiscountBreakdown],
    resellRefSell: Option[AGXCommission],
    currentBreakdownStep: BreakdownStep,
    bookingPriceBreakdown: BookingPriceBreakdown,
    apmPriceAdjustmentDetail: Option[ApmPriceAdjustmentDetail],
    apmCommissionDiscountPercent: AGXCommission,
    childAgeRangeId: Option[CountryId],
    hotelId: HotelId,
    chainId: ChainId,
    countryId: CountryId,
    cityId: CityId,
    sellExForMarginAdjustment: Option[AGXCommission],
    supplierFundedDiscountAmount: Option[AGXCommission],
    uspaDiscountAmount: Option[AGXCommission],
    uspaProgramId: Option[TaxProtoTypeID],
    isApplyTaxOnSellEx: Boolean,
    lengthOfStay: TaxProtoTypeID,
    storefrontId: TaxProtoTypeID,
    whitelabelId: TaxProtoTypeID,
    isThirdPartySupplier: Boolean,
    applyTaxOverHelper: ApplyTaxOverHelper,
    experimentContext: ExperimentContext)(implicit propertyContext: PropertyContext): YplPrice = ???

  override def calculateSurcharge(paymentModel: PaymentModel,
                                  dailyPrice: DailyPrice,
                                  surchargeRateType: RateType,
                                  surchargeEntry: SurchargeEntry,
                                  roomPrices: List[YplPrice],
                                  taxInfo: TaxInfo,
                                  room: YplRoomEntry,
                                  reqOcc: YplReqOccByHotelAgePolicy,
                                  isPull: Boolean,
                                  supplierId: SupplierId,
                                  supplierContractedCommission: Option[Double],
                                  hotelId: HotelId,
                                  chainId: ChainId,
                                  countryId: CountryId,
                                  fixMarriottSurchargeExp: Boolean)(isBcomFixTaxAmountApplyToPB: Boolean)(implicit
    ctx: YplContext,
    propertyContext: PropertyContext): Option[YplPrice] = ???

  def calculateSurchargeForPriceCalculationRefactor(
    paymentModel: PaymentModel,
    dailyPrice: DailyPrice,
    surchargeRateType: RateType,
    surchargeEntry: SurchargeEntry,
    roomPrices: List[YplPrice],
    taxInfo: TaxInfo,
    room: YplRoomEntry,
    reqOcc: YplReqOccByHotelAgePolicy,
    isPull: Boolean,
    supplierId: SupplierId,
    supplierContractedCommission: Option[AGXCommission],
    hotelId: HotelId,
    chainId: ChainId,
    countryId: CountryId,
    cityId: CityId,
    fixMarriottSurchargeExp: Boolean)(implicit ctx: YplContext, propertyContext: PropertyContext): Option[YplPrice] =
    ???

  def calculateAgpPrice(paymentModel: PaymentModel,
                        commissionPercent: Double,
                        hotelTaxInfo: HotelTaxInfo,
                        reqOcc: YplReqOccByHotelAgePolicy,
                        roomPriceInfo: RoomPriceInfo,
                        supplierId: SupplierId,
                        hotelId: HotelId,
                        chainId: ChainId,
                        countryId: CountryId,
                        cityId: CityId,
                        price: YplPrice,
                        gpCommissionBreakdown: GrowthProgramCommissionBreakdown,
                        commissionExcludingWholesaleOrAgx: => Double)(implicit
    ctx: YplContext,
    propertyContext: PropertyContext): YplPrice = ???

  override def calculateApmPriceAdjustmentResult(apmRoomParams: List[(RoomIndex, ApmPriceAdjustmentRoomParameters)],
                                                 hotelId: BrandId,
                                                 supplierId: TravelerType,
                                                 countryId: BrandId,
                                                 dispatchChannels: Set[TravelerType],
                                                 bookingDateTime: StayDate,
                                                 removeBedPaidExclusionFromApmExp: Boolean,
                                                 skipApmPriceAdjustmentForResellExp: Boolean,
                                                 enabledAiBedsExp: Boolean,
                                                 enableApmMultipleDiscount: Boolean,
                                                 excludeRateChannelFromApmExp: Boolean,
                                                 isApmFixEndDate: Boolean,
                                                 enabledApmArpPlusProgram: Boolean,
                                                 requestFences: Map[TravelerType, Set[YplRateFenceHolder]])
    : (List[(RoomIndex, ApmPriceAdjustmentRoomResult)], List[ApmApprovalPriceNotUseReason]) = ???
}
// scalastyle:on
