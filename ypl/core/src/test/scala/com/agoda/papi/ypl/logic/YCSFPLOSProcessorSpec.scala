package com.agoda.papi.ypl.logic

import com.agoda.finance.tax.services.cache.{TaxFilter<PERSON>ache, TaxFilterCacheAccessor}
import com.agoda.papi.ypl.mocks.YCSFPLOSProcessorMock
import com.agoda.papi.ypl.models.api.request.YplOccInfo
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.hotel.DmcData
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{YplHotelEntryModel, _}
import com.agoda.protobuf.cache.ChannelRoomRate.RateCategory.DailyPrice
import com.agoda.protobuf.cache.Promotion.Restriction.PeriodInterval
import com.agoda.protobuf.cache.RateRepurposingData
import com.agoda.protobuf.common._
import org.joda.time.DateTime
import org.mockito.Mockito.when
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.specs2.concurrent.ExecutionEnv
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope

import scala.concurrent.duration._
import scala.concurrent.{Await, Future}

//scalastyle:off
class YCSFPLOSProcessorSpec(implicit ec: ExecutionEnv)
  extends SpecificationWithJUnit
    with YPLTestContexts
    with YPLTestDataBuilders
    with Mockito {

  implicit val compositeChannelContext = aValidCompositeChannelContext

  private def round(value: Double, precision: Int = 1): Double =
    Math.round(value * Math.pow(10, precision)) / Math.pow(10, precision)

  private def getHotel(data: YplPricingData, ctx: YplContext): Future[Option[YPLHotel]] = {
    val roomExpansionResult = flow.processRoomExpansionFlow(data, ctx)
    val resultF = flow.processRoomRpmFlow(data, roomExpansionResult, ctx)
    resultF
  }

  val flow: YCSFPLOSProcessor[YplContext] = new YCSFPLOSProcessorMock[YplContext] {}

  "YCSFPLOSProcessor" should {
    "return none if future fails" in {
      val flow = new YCSFPLOSProcessorMock[YplContext] {
        override def executeRpmPricingFlowAsync(pricingData: YplPricingData,
                                                yplHotelEntryModelOpt: Option[YplHotelEntryModel],
                                                ctx: YplContext): Future[Option[YPLHotel]] =
          Future.failed(new RuntimeException("Error"))
      }
      val res = flow.processRoomRpmFlow(aValidPricingData, None, aValidYplContext)
      res must beEqualTo(None).await
    }
  }

  "Process room expansion and rpm flow" should {

    "OTA calculation" should {

      "Basic calculation" should {
        "calculate correct prices in room for FPLOS with no promotion or channel discount or rate repurposing" in {
          // mock tax filter
          val mockCache: TaxFilterCache = mock[TaxFilterCache]
          when(mockCache.get(any[String])).thenReturn(Some(List.empty))
          TaxFilterCacheAccessor.initialize(mockCache)

          val commission = aValidOTACommission.withLanguageID(0).withValue(17.647058823529413)
          val commissions = Seq(commission, commission.withLanguageID(1))
          val taxes = Seq(
            aValidOTATaxAndFee.withValue(1.38).build,
            aValidOTATaxAndFee.withTaxId(2).withValue(1.0).build,
            aValidOTATaxAndFee.withTaxId(3).withValue(12.5).build,
            aValidOTATaxAndFee.withTaxId(4).withApplyTo("PRPN").withIsAmount(true).withValue(4.47).build,
          )
          val dailyPrice = aValidOTADailyPrice
            .withPrices(Seq(aValidOTAOccupancyPrice.withOccupancy(2).withAmount(29.08).withTaxAndFee(taxes).build))
            .withCommissions(commissions)
            .build
          val rateCategory = aValidOTARateCategory
            .withRateTypeLoaded(RateType.NetInc)
            .withRemainingRoom(1)
            .withBookingRestriction(aValidOTARateCategoryBookingRestriction)
            .withDailyPrices(Seq(dailyPrice))
          val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).withCurrencyCode("USD")
          val roomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRatePlanId("BAR").withSupplierRoomId("STD")
          val hotelPrice = aValidOTAHotelPrice
            .withChannelRoomRate(Seq(channelRoomRate))
            .withTaxType(TaxType.SimpleTax)
            .withSurchargeRateLoadType(RateType.NetInc)
            .withPaymentModel(PaymentModel.Merchant)
            .withOccupancyModel(OccupanyModel.FullPatternLengthOfStay)
            .withRoomTypes(Seq(roomType))
          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          val request = YplRequest(
            "",
            checkIn,
            checkOut,
            occ = YplOccInfo(Some(2), None, Some(1), None),
            bookingDate = checkIn,
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          val ctx = YplContext(request)
          val pricingDataHP =
            YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
          val result = getHotel(pricingDataHP, ctx)
          val hotel = Await.result(result, 10.minutes)

          hotel mustNotEqual None
          hotel.get.rooms.size must_== 1
        }

        "calculate hotel FPLOS with free occ search" in {
          // mock tax filter
          val mockCache: TaxFilterCache = mock[TaxFilterCache]
          when(mockCache.get(any[String])).thenReturn(Some(List.empty))
          TaxFilterCacheAccessor.initialize(mockCache)

          val commission = aValidOTACommission.withLanguageID(0).withValue(17.647058823529413)
          val commissions = Seq(commission, commission.withLanguageID(1))
          val taxes = Seq(
            aValidOTATaxAndFee.withValue(1.38).build,
            aValidOTATaxAndFee.withTaxId(2).withValue(1).build,
            aValidOTATaxAndFee.withTaxId(3).withValue(12.5).build,
            aValidOTATaxAndFee.withTaxId(4).withApplyTo("PRPN").withIsAmount(true).withValue(4.47).build,
          )
          val occupancyPriceOccOne =
            aValidOTAOccupancyPrice.withOccupancy(1).withAmount(32.47).withTaxAndFee(taxes).build
          val occupancyPriceOccTwo =
            aValidOTAOccupancyPrice.withOccupancy(2).withAmount(32.47).withTaxAndFee(taxes).build
          val dailyPrice = aValidOTADailyPrice
            .withPrices(Seq(occupancyPriceOccOne, occupancyPriceOccTwo))
            .withCommissions(commissions)
            .build
          val roomType =
            aValidOTARoomType.withMaxOccupancy(2).withSupplierRatePlanId("BAR").withSupplierRoomId("STD").build
          val rateCategory = aValidOTARateCategory
            .withRateTypeLoaded(RateType.NetInc)
            .withRemainingRoom(1)
            .withBookingRestriction(aValidOTARateCategoryBookingRestriction)
            .withDailyPrices(Seq(dailyPrice))
            .build
          val channelRoomRate =
            aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).withCurrencyCode("USD").build
          val hotelPrice = aValidOTAHotelPrice
            .withChannelRoomRate(Seq(channelRoomRate))
            .withTaxType(TaxType.SimpleTax)
            .withSurchargeRateLoadType(RateType.NetInc)
            .withPaymentModel(PaymentModel.Merchant)
            .withOccupancyModel(OccupanyModel.FullPatternLengthOfStay)
            .withRoomTypes(Seq(roomType))
            .build
          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          val request = YplRequest(
            "",
            checkIn,
            checkOut,
            occ = YplOccInfo(Some(0), None, Some(0), None),
            bookingDate = checkIn,
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          val ctx = YplContext(request)
          val pricingDataHP =
            YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
          val result = getHotel(pricingDataHP, ctx)
          val hotel = Await.result(result, 10.minutes)

          hotel mustNotEqual None
          hotel.get.rooms mustNotEqual Nil
          hotel.get.rooms.size must_== 1
        }

        "calculate hotel FPLOS with specific occ search" in {
          // mock tax filter
          val mockCache: TaxFilterCache = mock[TaxFilterCache]
          when(mockCache.get(any[String])).thenReturn(Some(List.empty))
          TaxFilterCacheAccessor.initialize(mockCache)

          val commission = aValidOTACommission.withLanguageID(0).withValue(17.647058823529413)
          val commissions = Seq(commission.build, commission.withLanguageID(1).build)
          val taxes = Seq(
            aValidOTATaxAndFee.withValue(1.38).build,
            aValidOTATaxAndFee.withTaxId(2).withValue(1).build,
            aValidOTATaxAndFee.withTaxId(3).withValue(12.5).build,
            aValidOTATaxAndFee.withTaxId(4).withApplyTo("PRPN").withIsAmount(true).withValue(4.47).build,
          )
          val occupancyPriceOccOne =
            aValidOTAOccupancyPrice.withOccupancy(1).withAmount(32.47).withTaxAndFee(taxes).build
          val occupancyPriceOccTwo =
            aValidOTAOccupancyPrice.withOccupancy(2).withAmount(52.00).withTaxAndFee(taxes).build
          val dailyPrice = aValidOTADailyPrice
            .withPrices(Seq(occupancyPriceOccOne, occupancyPriceOccTwo))
            .withCommissions(commissions)
            .build
          val roomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRatePlanId("BAR").withSupplierRoomId("STD")
          val rateCategory = aValidOTARateCategory
            .withRateTypeLoaded(RateType.NetExc)
            .withRemainingRoom(1)
            .withBookingRestriction(aValidOTARateCategoryBookingRestriction)
            .withDailyPrices(Seq(dailyPrice))
          val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).withCurrencyCode("USD")
          val hotelPrice = aValidOTAHotelPrice
            .withChannelRoomRate(Seq(channelRoomRate))
            .withTaxType(TaxType.SimpleTax)
            .withSurchargeRateLoadType(RateType.NetInc)
            .withPaymentModel(PaymentModel.Merchant)
            .withOccupancyModel(OccupanyModel.FullPatternLengthOfStay)
            .withRoomTypes(Seq(roomType))

          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          val request = YplRequest(
            "",
            checkIn,
            checkOut,
            occ = YplOccInfo(Some(2), None, Some(1), None),
            bookingDate = checkIn,
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          val ctx = YplContext(request)
          val pricingDataHP =
            YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
          val result = getHotel(pricingDataHP, ctx)
          val hotel = Await.result(result, 10.minutes)

          hotel mustNotEqual None
          hotel.get.rooms mustNotEqual Nil
          hotel.get.rooms.size must_== 2

          val sortedRoom = hotel.get.rooms.sortBy(_.occ.adults)

          val roomResultNonFitRoom = sortedRoom(0)
          roomResultNonFitRoom.occ.adults must_=== 1
          roomResultNonFitRoom.prices mustNotEqual Nil
          val pricesResultNonFitRoom = roomResultNonFitRoom.prices.head
          pricesResultNonFitRoom.netExclusive must_=== 32.47

          val roomResultFitRoom = sortedRoom(1)
          roomResultFitRoom.occ.adults must_=== 2
          roomResultFitRoom.prices mustNotEqual Nil
          val pricesResultFitRoom = roomResultFitRoom.prices.head
          pricesResultFitRoom.netExclusive must_=== 52.00
        }

        "calculate no hotel for protobuf with no rooms" in {
          val hotelPrice = aValidOTAHotelPrice.withChannelRoomRate(Seq.empty)
          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          val request = YplRequest(
            "",
            checkIn,
            checkOut,
            occ = YplOccInfo(Some(2), None, Some(1), None),
            bookingDate = checkIn,
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          val ctx = YplContext(request)
          val pricingDataHP =
            YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
          val result = getHotel(pricingDataHP, ctx)
          val hotel = Await.result(result, 10.minutes)

          hotel.isEmpty
        }

        "calculate correct price for FullOccupancy with no promotion or channel discount or rate repurposing" in {
          // mock tax filter
          val mockCache: TaxFilterCache = mock[TaxFilterCache]
          when(mockCache.get(any[String])).thenReturn(Some(List.empty))
          TaxFilterCacheAccessor.initialize(mockCache)

          val commission = aValidOTACommission.withLanguageID(0).withValue(17.647058823529413)
          val commissions = Seq(commission, commission.withLanguageID(1))
          val tax = aValidOTATaxAndFee.withValue(13.0)
          val occupancyPriceOccOne = aValidOTAOccupancyPrice.withOccupancy(1).withAmount(118.98).withTaxAndFee(Seq(tax))
          val occupancyPriceOccTwo = aValidOTAOccupancyPrice.withOccupancy(2).withAmount(118.98).withTaxAndFee(Seq(tax))
          val dailyPrice =
            aValidOTADailyPrice.withPrices(Seq(occupancyPriceOccOne, occupancyPriceOccTwo)).withCommissions(commissions)
          val roomType = aValidOTARoomType.withMaxOccupancy(3).withSupplierRatePlanId("BAR").withSupplierRoomId("STD")
          val rateCategory = aValidOTARateCategory
            .withRateTypeLoaded(RateType.NetExc)
            .withRemainingRoom(1)
            .withBookingRestriction(aValidOTARateCategoryBookingRestriction)
            .withDailyPrices(Seq(dailyPrice))
          val channelRoomRate = aValidOTAChannelRoomRate.withRateCategories(Seq(rateCategory)).withCurrencyCode("USD")
          val hotelPrice = aValidOTAHotelPrice
            .withChannelRoomRate(Seq(channelRoomRate))
            .withTaxType(TaxType.ComprehensiveTaxHotelLevel)
            .withSurchargeRateLoadType(RateType.NetExc)
            .withPaymentModel(PaymentModel.Merchant)
            .withOccupancyModel(OccupanyModel.FullPatternLengthOfStay)
            .withRoomTypes(Seq(roomType))
          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          val request = YplRequest(
            "",
            checkIn,
            checkOut,
            occ = YplOccInfo(Some(2), None, Some(1), None),
            bookingDate = checkIn,
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          val ctx = YplContext(request)
          val pricingDataHP =
            YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
          val result = getHotel(pricingDataHP, ctx)
          val hotel = Await.result(result, 10.minutes)

          hotel must_!= None
          hotel.get.rooms must_!= Nil
          hotel.get.rooms.size must_== 1

          val room = hotel.get.rooms.head

          room.netEx must_== 118.98000000000002
          room.netIn must_== 134.44740000000002
          room.tax must_== 15.467400000000003
          room.fee must_== 0.0
          room.margin must_== 20.996470588235297
          room.downliftAmount must_== 0.0
          room.pf must_== 2.7295411764705886
          room.roomSellEx must_== 139.97647058823532
          room.roomSellIn must_== 158.1734117647059
          room.roomRefSellEx must_== 139.97647058823532
          room.roomRefSellIn must_== 158.1734117647059
          room.sellEx must_== 139.97647058823532
          room.sellIn must_== 158.1734117647059
          room.excludedTaxAmount must_== 0.0
          room.extraBedSellIn must_== 0.0
          room.mandatorySurcharge must_== 13.44474
          room.mandatorySurchargeNetInclusive must_== 13.44474
          room.payAtHotel must_== 0.0
          room.marginPercentage must_== 15.0000000000000001

        }

        "Full Rate LOS 3" should {
          val tax = aValidOTATaxAndFee.withValue(14.0)
          val surcharge = aValidOTASurcharge
            .withApplyTo("PRPN")
            .withApplyType(ApplyType.Excluded)
            .withIsAmount(true)
            .withValue(22.8)
            .build
          val occupancyPriceOccOne = aValidOTAOccupancyPrice
            .withOccupancy(1)
            .withAmount(149.0)
            .withTaxAndFee(Seq(tax))
            .withSurcharges(Seq(surcharge))
            .build
          val occupancyPriceOccTwo = aValidOTAOccupancyPrice
            .withOccupancy(2)
            .withAmount(149.0)
            .withTaxAndFee(Seq(tax))
            .withSurcharges(Seq(surcharge))
            .build
          val occupancyPrices = Seq(occupancyPriceOccOne, occupancyPriceOccTwo)
          val commission = aValidOTACommission.withLanguageID(0).withValue(20.0)
          val dailyPrice = aValidOTADailyPrice.withPrices(occupancyPrices).withCommissions(Seq(commission)).build
          val dailyPriceSecondDay = aValidOTADailyPrice
            .withPrices(occupancyPrices)
            .withCommissions(Seq(commission))
            .withDateMillis(new DateTime(1525798800000L).plusDays(1).getMillis)
            .build
          val dailyPriceThirdDay = aValidOTADailyPrice
            .withPrices(occupancyPrices)
            .withCommissions(Seq(commission))
            .withDateMillis(new DateTime(1525798800000L).plusDays(2).getMillis)
            .build
          val dailyPrices: Seq[DailyPrice] = Seq(dailyPrice, dailyPriceSecondDay, dailyPriceThirdDay)
          val roomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRatePlanId("BAR").withSupplierRoomId("STD")
          val rateCategory =
            aValidOTARateCategory.withRateTypeLoaded(RateType.SellExc).withRemainingRoom(1).withDailyPrices(dailyPrices)
          val channelRoomRate = aValidOTAChannelRoomRate
            .withRateCategories(Seq(rateCategory))
            .withCurrencyCode("USD")
            .withProcessingFee(1.0)
            .withRateType(RateType.NetInc)
          val hotelPrice = aValidOTAHotelPrice
            .withChannelRoomRate(Seq(channelRoomRate))
            .withTaxType(TaxType.SimpleTax)
            .withSurchargeRateLoadType(RateType.SellExc)
            .withRoomTypes(Seq(roomType))
            .withLengthOfStay(3)

          "calculate correct price for FullOccupancy with LOS 3 with no promotion or channel discount or rate repurposing" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val checkIn = new DateTime(hotelPrice.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
            val request = YplRequest(
              "",
              checkIn,
              checkOut,
              occ = YplOccInfo(Some(2), None, Some(1), None),
              bookingDate = checkIn,
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            val ctx = YplContext(request)
            val pricingDataHP =
              YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None
            hotel.get.rooms.size must_== 1
          }
          "generate room using rate re-utilization with same source and reference channels for FullOccupancy with LOS 3 and without promotion" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val hotelPriceWithRateReutilization = hotelPrice.withRateRepurposeList(
              Seq(
                RateRepurposingData(targetChannel = 2,
                                    channelDiscount = 5.34,
                                    referenceChannel = 1,
                                    discountType = 2,
                                    sourceChannel = 1,
                                    minAdvPurchase = Some(2),
                                    maxAdvPurchase = Some(3))))
            val checkIn = new DateTime(hotelPriceWithRateReutilization.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPriceWithRateReutilization.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1).withHotelId(hotelPrice.hotelId.toInt)
            val request = aValidYplRequest
              .withCheckIn(checkIn)
              .withCheckout(checkOut)
              .withOccupancyInfo(YplOccInfo(Some(2), None, Some(1), None))
              .withBookingDate(
                checkIn.minusDays(hotelPriceWithRateReutilization.rateRepurposeList.head.minAdvPurchase.get))
              .withChannels(Set(YplMasterChannel.RTL, YplMasterChannel.APS))
            val ctx = YplContext(request)
            val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
            val pricingDataHP = new YplPricingData(Right(hotelPriceWithRateReutilization),
                                                   hotelMeta,
                                                   dispatchChannels,
                                                   Map(aValidRateFence -> dispatchChannels))
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10 minutes)

            hotel.get.rooms.size shouldEqual 2

            val room = hotel.flatMap(_.rooms.find(_.channel.contains(2))).get

            room.channel.contains(2) must_== true
            room.prices.size must_== 6
            room.netEx must_== 357.6
            room.netIn must_== 407.664
            room.tax must_== 50.06399999999999
            room.fee must_== 0.0
            room.margin must_== 65.53019999999998
            room.downliftAmount must_== 0.0
            room.pf must_== 9.174228
            room.roomSellEx must_== 423.13019999999995
            room.roomSellIn must_== 482.36842799999994
            room.roomRefSellEx must_== 447.0
            room.roomRefSellIn must_== 509.5799999999999
            room.sellEx must_== 423.13019999999995
            room.sellIn must_== 482.36842799999994
            room.excludedTaxAmount must_== 68.4
            room.extraBedSellIn must_== 0.0
            room.mandatorySurcharge must_== 0.0
            room.mandatorySurchargeNetInclusive must_== 0.0
            room.payAtHotel must_== 68.4
          }
          "generate room using rate re-utilisation with diff source and reference channels for FullOccupancy with LOS 3 and without promotion" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val tax = aValidOTATaxAndFee.withValue(14.0)
            val surcharge = aValidOTASurcharge
              .withApplyTo("PRPN")
              .withApplyType(ApplyType.Excluded)
              .withIsAmount(true)
              .withValue(22.8)
              .build
            val occupancyPriceOccOne = aValidOTAOccupancyPrice
              .withOccupancy(1)
              .withAmount(130.0)
              .withTaxAndFee(Seq(tax))
              .withSurcharges(Seq(surcharge))
              .build
            val occupancyPriceOccTwo = aValidOTAOccupancyPrice
              .withOccupancy(2)
              .withAmount(130.0)
              .withTaxAndFee(Seq(tax))
              .withSurcharges(Seq(surcharge))
              .build
            val occupancyPrices = Seq(occupancyPriceOccOne, occupancyPriceOccTwo)
            val commission = aValidOTACommission.withLanguageID(0).withValue(20.0).withChannelID(2)
            val dailyPrice = aValidOTADailyPrice.withPrices(occupancyPrices).withCommissions(Seq(commission)).build
            val dailyPriceSecondDay = aValidOTADailyPrice
              .withPrices(occupancyPrices)
              .withCommissions(Seq(commission))
              .withDateMillis(new DateTime(1525798800000L).plusDays(1).getMillis)
              .build
            val dailyPriceThirdDay = aValidOTADailyPrice
              .withPrices(occupancyPrices)
              .withCommissions(Seq(commission))
              .withDateMillis(new DateTime(1525798800000L).plusDays(2).getMillis)
              .build
            val dailyPrices: Seq[DailyPrice] = Seq(dailyPrice, dailyPriceSecondDay, dailyPriceThirdDay)
            val rateCategory = aValidOTARateCategory
              .withRateTypeLoaded(RateType.SellExc)
              .withRemainingRoom(1)
              .withDailyPrices(dailyPrices)
            val channelRoomRate = aValidOTAChannelRoomRate
              .withRateCategories(Seq(rateCategory))
              .withCurrencyCode("USD")
              .withProcessingFee(1.0)
              .withRateType(RateType.NetInc)
              .withChannelId(2)

            val hotelPriceWithRateReutilization = hotelPrice
              .addAllChannelRoomRate(Seq(channelRoomRate))
              .withRateRepurposeList(
                Seq(
                  RateRepurposingData(targetChannel = 3,
                                      channelDiscount = 6.45,
                                      referenceChannel = 2,
                                      discountType = 2,
                                      sourceChannel = 1,
                                      minAdvPurchase = Some(2),
                                      maxAdvPurchase = Some(3))))
            val checkIn = new DateTime(hotelPriceWithRateReutilization.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPriceWithRateReutilization.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1).withHotelId(hotelPrice.hotelId.toInt)
            val request = aValidYplRequest
              .withCheckIn(checkIn)
              .withCheckout(checkOut)
              .withOccupancyInfo(YplOccInfo(Some(2), None, Some(1), None))
              .withBookingDate(
                checkIn.minusDays(hotelPriceWithRateReutilization.rateRepurposeList.head.minAdvPurchase.get))
              .withChannels(Set(YplMasterChannel.RTL, YplMasterChannel.APS, YplChannel(Channel.NET)))
            val ctx = YplContext(request)
            val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
            val pricingDataHP = new YplPricingData(Right(hotelPriceWithRateReutilization),
                                                   hotelMeta,
                                                   dispatchChannels,
                                                   Map(aValidRateFence -> dispatchChannels))
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10 minutes)

            hotel.get.rooms.size shouldEqual 3

            val room = hotel.flatMap(_.rooms.find(_.channel.contains(3))).get

            room.channel.contains(3) must_== true
            room.prices.size must_== 6
            room.netEx must_== 357.6
            room.netIn must_== 407.664
            room.tax must_== 50.06399999999999
            room.fee must_== 0.0
            room.margin must_== 7.245000000000029
            room.downliftAmount must_== 0.0
            room.pf must_== 1.0143000000000022
            room.roomSellEx must_== 364.845
            room.roomSellIn must_== 415.9233
            room.roomRefSellEx must_== 447.0
            room.roomRefSellIn must_== 509.5799999999999
            room.sellEx must_== 364.845
            room.sellIn must_== 415.9233
            room.excludedTaxAmount must_== 68.4
            room.extraBedSellIn must_== 0.0
            room.mandatorySurcharge must_== 0.0
            room.mandatorySurchargeNetInclusive must_== 0.0
            room.payAtHotel must_== 68.4
          }
          "calculate correct rooms for FullOccupancy with LOS 3 with free occupancy search" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val checkIn = new DateTime(hotelPrice.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
            val request = YplRequest(
              "",
              checkIn,
              checkOut,
              occ = YplOccInfo(Some(0), None, Some(0), None),
              bookingDate = checkIn,
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            val ctx = YplContext(request)
            val pricingDataHP =
              YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel mustNotEqual None
            hotel.get.rooms mustNotEqual Nil
            hotel.get.rooms.size must_== 1
          }
        }
      }

      "channel discount" should {
        val commission = aValidOTACommission.withLanguageID(0).withValue(25.0).build
        val commissionAPSBase = aValidOTACommission.withChannelID(2).withLanguageID(0).withValue(25.0).build
        val taxesSimple = Seq(aValidOTATaxAndFee.withValue(10.0).build,
                              aValidOTATaxAndFee.withTaxId(2).withIsFee(true).withValue(7.0).withIsTaxable(true).build)
        val taxesComprehensive = Seq(
          aValidOTATaxAndFee.withValue(10.0).build,
          aValidOTATaxAndFee.withTaxId(2).withValue(10.0).build,
          aValidOTATaxAndFee.withTaxId(3).withIsFee(true).withValue(7.0).withIsTaxable(true).build,
          aValidOTATaxAndFee.withTaxId(4).withIsFee(true).withValue(10.0).build,
          aValidOTATaxAndFee.withTaxId(5).withApplyTo("PGPN").withValue(5.0).withIsAmount(true).build,
          aValidOTATaxAndFee.withTaxId(6).withApplyTo("PRPB").withValue(10.0).withIsAmount(true).build,
          aValidOTATaxAndFee.withTaxId(7).withApplyTo("PRPN").withValue(4.0).withIsAmount(true).build,
        )
        val occupancyPriceSimpleTax = aValidOTAOccupancyPrice.withAmount(200.0).withTaxAndFee(taxesSimple).build
        val channelRoomRateBase = aValidOTAChannelRoomRate.withCurrencyCode("EUR").withProcessingFee(1.0)
        val roomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRatePlanId("BAR").withSupplierRoomId("STD")
        val hotelPriceBase = aValidOTAHotelPrice
          .withSurchargeRateLoadType(RateType.NetExc)
          .withPaymentModel(PaymentModel.Merchant)
          .withRoomTypes(Seq(roomType))

        "merchant" should {

          "calculate channel discount merchant simple tax" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val dailyPriceOccOne = aValidOTADailyPrice
              .withPrices(Seq(aValidOTAOccupancyPrice.withAmount(200.0).withTaxAndFee(taxesSimple)))
              .withCommissions(Seq(commission))
            val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceOccOne))
            val channelRoomRate = channelRoomRateBase.withRateCategories(Seq(rateCategory))

            val commissionAPS = commissionAPSBase.withValue(25.0)
            val dailyPriceAPS = dailyPriceOccOne.withCommissions(Seq(commissionAPS)).withChannelDiscount(15.0)
            val rateCategoryAPS = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceAPS))
            val channelRoomRateAPS = channelRoomRateBase.withRateCategories(Seq(rateCategoryAPS)).withChannelId(2)
            val channelRoomRates = Seq(channelRoomRate, channelRoomRateAPS)

            val hotelPrice = hotelPriceBase.withChannelRoomRate(channelRoomRates).withTaxType(TaxType.SimpleTax)
            val checkIn = new DateTime(hotelPrice.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
            val request = YplRequest(
              searchId = "",
              checkIn = checkIn,
              checkOut = checkOut,
              occ = YplOccInfo(Some(0), None, Some(0), None),
              bookingDate = checkIn,
              channels = Set(YplMasterChannel.RTL, YplMasterChannel.APS),
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            val ctx = YplContext(request)
            val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
            val pricingDataHP =
              YplPricingData(Right(hotelPrice), hotelMeta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None

            val roomOption =
              hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(2) && r.rateCategoryId == 1)
            roomOption must_!= None

            val room = roomOption.get

            round(room.netEx) must_== round(170.0)
            round(room.netIn) must_== round(200.09)
            round(room.sellEx) must_== round(212.5)
            round(room.sellIn) must_== round(250.1125)
            round(room.tax) must_== round(18.19)
            round(room.fee) must_== round(11.9)
            round(room.margin) must_== round(42.5)
            round(room.pf) must_== round(7.522500000000001)
          }

          "calculate channel discount merchant simple tax" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val dailyPriceOccOne = aValidOTADailyPrice
              .withPrices(Seq(aValidOTAOccupancyPrice.withAmount(200.0).withTaxAndFee(taxesSimple)))
              .withCommissions(Seq(commission))
            val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceOccOne))
            val channelRoomRate = channelRoomRateBase.withRateCategories(Seq(rateCategory))

            val commissionAPS = commissionAPSBase.withValue(25.0)
            val dailyPriceAPS = dailyPriceOccOne.withCommissions(Seq(commissionAPS)).withChannelDiscount(15.0)
            val rateCategoryAPS = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceAPS))
            val channelRoomRateAPS = channelRoomRateBase.withRateCategories(Seq(rateCategoryAPS)).withChannelId(2)
            val channelRoomRates = Seq(channelRoomRate, channelRoomRateAPS)

            val hotelPrice = hotelPriceBase.withChannelRoomRate(channelRoomRates).withTaxType(TaxType.SimpleTax)
            val checkIn = new DateTime(hotelPrice.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
            val request = YplRequest(
              searchId = "",
              checkIn = checkIn,
              checkOut = checkOut,
              occ = YplOccInfo(Some(0), None, Some(0), None),
              bookingDate = checkIn,
              channels = Set(YplMasterChannel.RTL, YplMasterChannel.APS),
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            val ctx = YplContext(request)
            val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
            val pricingDataHP =
              YplPricingData(Right(hotelPrice), hotelMeta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None

            val roomOption =
              hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(2) && r.rateCategoryId == 1)
            roomOption must_!= None

            val room = roomOption.get

            round(room.netEx) must_== round(170.0)
            round(room.netIn) must_== round(200.09)
            round(room.sellEx) must_== round(212.5)
            round(room.sellIn) must_== round(250.1125)
            round(room.tax) must_== round(18.19)
            round(room.fee) must_== round(11.9)
            round(room.margin) must_== round(42.5)
            round(room.pf) must_== round(7.522500000000001)
          }

        }

        "merchant commission" should {
          val occupancyPrice = aValidOTAOccupancyPrice.withOccupancy(2).withAmount(200.0).withTaxAndFee(taxesSimple)
          val dailyPriceBase = aValidOTADailyPrice.withCommissions(Seq(commission))

          "calculate channel discount merchant commission simple tax" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val dailyPrice = dailyPriceBase.withPrices(Seq(occupancyPrice))
            val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPrice))
            val channelRoomRate = channelRoomRateBase.withRateCategories(Seq(rateCategory))

            val dailyPriceAPS = aValidOTADailyPrice
              .withPrices(Seq(occupancyPrice))
              .withCommissions(Seq(commissionAPSBase))
              .withChannelDiscount(15.0)
            val rateCategoryAPS = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceAPS))
            val channelRoomRateAPS = channelRoomRateBase.withRateCategories(Seq(rateCategoryAPS)).withChannelId(2)
            val channelRoomRates = Seq(channelRoomRate, channelRoomRateAPS)
            val hotelPrice = hotelPriceBase
              .withChannelRoomRate(channelRoomRates)
              .withTaxType(TaxType.SimpleTax)
              .withPaymentModel(PaymentModel.MerchantComission)
            val checkIn = new DateTime(hotelPrice.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
            val request = YplRequest(
              searchId = "",
              checkIn = checkIn,
              checkOut = checkOut,
              occ = YplOccInfo(Some(0), None, Some(0), None),
              bookingDate = checkIn,
              channels = Set(YplMasterChannel.RTL, YplMasterChannel.APS),
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
              applyTaxOnSellExSettings = Some(aValidApplyTaxOnSellExSettings),
            )
            val ctx = YplContext(request)
            val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
            val pricingDataHP =
              YplPricingData(Right(hotelPrice), hotelMeta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None

            val roomOption =
              hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(2) && r.rateCategoryId == 1)
            roomOption must_!= None

            val room = roomOption.get

            room.netEx must_== 170.0
            room.netIn must_== 207.6125
            room.sellEx must_== 212.5
            room.sellIn must_== 250.1125
            room.tax must_== 22.7375
            room.fee must_== 14.875
            room.margin must_== 42.5
            room.pf must_== 0.0
          }

          "calculate channel discount merchant commission comprehensive tax" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val commissionAPS = commissionAPSBase.withValue(28.2051282051282)
            val dailyPrice =
              dailyPriceBase.withPrices(Seq(occupancyPrice.withAmount(150.0).withTaxAndFee(taxesComprehensive)))
            val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPrice))
            val channelRoomRate = channelRoomRateBase.withRateCategories(Seq(rateCategory))

            val dailyPriceAPS = dailyPriceBase
              .withPrices(Seq(occupancyPrice.withAmount(150.0).withTaxAndFee(taxesComprehensive)))
              .withCommissions(Seq(commissionAPS))
              .withChannelDiscount(4.0)
            val rateCategoryAPS = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceAPS))
            val channelRoomRateAPS = channelRoomRateBase.withRateCategories(Seq(rateCategoryAPS)).withChannelId(2)

            val channelRoomRates = Seq(channelRoomRate, channelRoomRateAPS)
            val hotelPrice = hotelPriceBase
              .withChannelRoomRate(channelRoomRates)
              .withTaxType(TaxType.ComprehensiveTaxHotelLevel)
              .withPaymentModel(PaymentModel.MerchantComission)

            val checkIn = new DateTime(hotelPrice.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
            val request = YplRequest(
              searchId = "",
              checkIn = checkIn,
              checkOut = checkOut,
              occ = YplOccInfo(Some(0), None, Some(0), None),
              bookingDate = checkIn,
              channels = Set(YplMasterChannel.RTL, YplMasterChannel.APS),
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
              applyTaxOnSellExSettings = Some(aValidApplyTaxOnSellExSettings),
            )
            val ctx = YplContext(request)
            val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
            val pricingDataHP =
              YplPricingData(Right(hotelPrice), hotelMeta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None

            val roomOption =
              hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(2) && r.rateCategoryId == 1)
            roomOption must_!= None

            val room = roomOption.get

            room.netEx must_== 144.0
            room.netIn must_== 238.8923076923077
            room.sellEx must_== 184.6153846153846
            room.sellIn must_== 279.5076923076923
            room.tax must_== 63.50769230769231
            room.fee must_== 31.384615384615387
            room.margin must_== 40.61538461538461
            room.pf must_== 0.0
          }
        }

        "multiple rooms" should {
          val dailyPrice = aValidOTADailyPrice.withPrices(Seq(occupancyPriceSimpleTax)).withCommissions(Seq(commission))
          val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPrice))
          val channelRoomRate = channelRoomRateBase.withRateCategories(Seq(rateCategory))
          val dailyPriceAPS = aValidOTADailyPrice
            .withPrices(Seq(occupancyPriceSimpleTax))
            .withCommissions(Seq(commissionAPSBase))
            .withChannelDiscount(10.0)
          val rateCategoryAPS = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceAPS))
          val channelRoomRateAPS = channelRoomRateBase.withRateCategories(Seq(rateCategoryAPS)).withChannelId(2)

          val occupancyPriceSimpleTaxSecondRoom = occupancyPriceSimpleTax.withAmount(400.0).build
          val channelRoomRateSecondRoomBase = channelRoomRateBase.withRoomTypeId(13193033)

          val dailyPriceSecondRoom =
            aValidOTADailyPrice.withPrices(Seq(occupancyPriceSimpleTaxSecondRoom)).withCommissions(Seq(commission))
          val rateCategorySecondRoom = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceSecondRoom))
          val channelRoomRateSecondRoom = channelRoomRateSecondRoomBase.withRateCategories(Seq(rateCategorySecondRoom))
          val dailyPriceAPSSecondRoom = aValidOTADailyPrice
            .withPrices(Seq(occupancyPriceSimpleTaxSecondRoom))
            .withCommissions(Seq(commissionAPSBase))
            .withChannelDiscount(20.0)
          val rateCategoryAPSSecondRoom = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceAPSSecondRoom))
          val channelRoomRateAPSSecondRoom =
            channelRoomRateSecondRoomBase.withRateCategories(Seq(rateCategoryAPSSecondRoom)).withChannelId(2)

          val roomTypeSecondRoom = aValidOTARoomType
            .withRoomTypeId(13193033)
            .withMaxOccupancy(2)
            .withSupplierRatePlanId("BAR")
            .withSupplierRoomId("STD")
          val roomTypes = Seq(roomType, roomTypeSecondRoom)
          val hotelMultiRooms = hotelPriceBase.withTaxType(TaxType.SimpleTax).withRoomTypes(roomTypes)

          "return correct value in each channel with multiple rooms in protobuf" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val channelRoomRates =
              Seq(channelRoomRate, channelRoomRateAPS, channelRoomRateSecondRoom, channelRoomRateAPSSecondRoom)
            val hotelPrice = hotelMultiRooms.withChannelRoomRate(channelRoomRates)

            val checkIn = new DateTime(hotelPrice.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
            val request = YplRequest(
              searchId = "",
              checkIn = checkIn,
              checkOut = checkOut,
              occ = YplOccInfo(Some(0), None, Some(0), None),
              bookingDate = checkIn,
              channels = Set(YplMasterChannel.RTL, YplMasterChannel.APS),
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            val ctx = YplContext(request)
            val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
            val pricingDataHP =
              YplPricingData(Right(hotelPrice), hotelMeta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None

            val roomOptionR1_Rp2_Rc1 =
              hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(2) && r.rateCategoryId == 1)
            roomOptionR1_Rp2_Rc1 must_!= None

            val roomR1_Rp2_Rc1 = roomOptionR1_Rp2_Rc1.get
            round(roomR1_Rp2_Rc1.netEx) must_== round(180.0)

            val roomOptionR2_Rp2_Rc2 =
              hotel.get.rooms.find(r => r.roomTypeId == 13193033 && r.channel.contains(2) && r.rateCategoryId == 1)
            roomOptionR2_Rp2_Rc2 must_!= None

            val roomR2_Rp2_Rc2 = roomOptionR2_Rp2_Rc2.get
            round(roomR2_Rp2_Rc2.netEx) must_== round(320.0)
          }

          "return correct value in each channel with no base rate in protobuf" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val channelRoomRates = Seq(channelRoomRate, channelRoomRateAPS, channelRoomRateAPSSecondRoom)
            val hotelPrice = hotelMultiRooms.withChannelRoomRate(channelRoomRates)
            val checkIn = new DateTime(hotelPrice.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
            val request = YplRequest(
              searchId = "",
              checkIn = checkIn,
              checkOut = checkOut,
              occ = YplOccInfo(Some(0), None, Some(0), None),
              bookingDate = checkIn,
              channels = Set(YplMasterChannel.RTL, YplMasterChannel.APS),
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            val ctx = YplContext(request)
            val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
            val pricingDataHP =
              YplPricingData(Right(hotelPrice), hotelMeta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None

            val roomOptionR1_Rp2_Rc1 =
              hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(2) && r.rateCategoryId == 1)
            roomOptionR1_Rp2_Rc1 must_!= None

            val roomR1_Rp2_Rc1 = roomOptionR1_Rp2_Rc1.get
            round(roomR1_Rp2_Rc1.netEx) must_== round(180.0)

            val roomOptionR1_Rp2_Rc2 =
              hotel.get.rooms.find(r => r.roomTypeId == 13193033 && r.channel.contains(1) && r.rateCategoryId == 1)
            roomOptionR1_Rp2_Rc2 must_== None

            val roomOptionR2_Rp2_Rc2 =
              hotel.get.rooms.find(r => r.roomTypeId == 13193033 && r.channel.contains(2) && r.rateCategoryId == 1)
            roomOptionR2_Rp2_Rc2 must_!= None

            // SPL will not apply channel discount logic in case that could not find Retail as BaseRate (netEx must_== 400)
            // YPL will apply logic even could not find Retail, but use itself as a BaseRate (netEx must_== 320)
            val roomR2_Rp2_Rc2 = roomOptionR2_Rp2_Rc2.get
            round(roomR2_Rp2_Rc2.netEx) must_== round(320.0)
          }
        }

        "return channels that only exist in request" in {
          // mock tax filter
          val mockCache: TaxFilterCache = mock[TaxFilterCache]
          when(mockCache.get(any[String])).thenReturn(Some(List.empty))
          TaxFilterCacheAccessor.initialize(mockCache)

          val dailyPrice = aValidOTADailyPrice.withPrices(Seq(occupancyPriceSimpleTax)).withCommissions(Seq(commission))
          val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPrice))
          val channelRoomRate = channelRoomRateBase.withRateCategories(Seq(rateCategory))

          val dailyPriceAPS = aValidOTADailyPrice
            .withPrices(Seq(occupancyPriceSimpleTax))
            .withCommissions(Seq(commissionAPSBase))
            .withChannelDiscount(15.0)
          val rateCategoryAPS = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceAPS))
          val channelRoomRateAPS = channelRoomRateBase.withRateCategories(Seq(rateCategoryAPS)).withChannelId(2)

          val channelRoomRates = Seq(channelRoomRate, channelRoomRateAPS)
          val hotelPrice = hotelPriceBase.withChannelRoomRate(channelRoomRates).withTaxType(TaxType.SimpleTax)

          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          val request = YplRequest(
            searchId = "",
            checkIn = checkIn,
            checkOut = checkOut,
            occ = YplOccInfo(Some(0), None, Some(0), None),
            bookingDate = checkIn,
            channels = Set(YplMasterChannel.APS),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          val ctx = YplContext(request)
          val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
          val pricingDataHP =
            YplPricingData(Right(hotelPrice), hotelMeta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
          val result = getHotel(pricingDataHP, ctx)
          val hotel = Await.result(result, 10.minutes)

          hotel must_!= None

          val roomOptionRp2_Rc1 =
            hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(2) && r.rateCategoryId == 1)
          roomOptionRp2_Rc1 must_!= None

          val roomOptionRp1_Rc1 =
            hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(1) && r.rateCategoryId == 1)
          roomOptionRp1_Rc1.isEmpty
        }

        "return correct value in each channel with no setting in protobuf" in {
          // mock tax filter
          val mockCache: TaxFilterCache = mock[TaxFilterCache]
          when(mockCache.get(any[String])).thenReturn(Some(List.empty))
          TaxFilterCacheAccessor.initialize(mockCache)

          val taxes = Seq(
            aValidOTATaxAndFee.withValue(10.0).build,
            aValidOTATaxAndFee.withTaxId(2).withIsFee(true).withValue(7.0).withIsTaxable(true).build,
          )
          val dailyPrice = aValidOTADailyPrice
            .withPrices(Seq(occupancyPriceSimpleTax.withTaxAndFee(taxes)))
            .withCommissions(Seq(commission))
          val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPrice))
          val channelRoomRate = channelRoomRateBase.withRateCategories(Seq(rateCategory))

          val dailyPriceAPS = aValidOTADailyPrice
            .withPrices(Seq(occupancyPriceSimpleTax.withTaxAndFee(taxes)))
            .withCommissions(Seq(commissionAPSBase))
          val rateCategoryAPS = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceAPS))
          val channelRoomRateAPS = channelRoomRateBase.withRateCategories(Seq(rateCategoryAPS)).withChannelId(2)

          val channelRoomRates = Seq(channelRoomRate, channelRoomRateAPS)
          val hotelPrice = hotelPriceBase.withChannelRoomRate(channelRoomRates).withTaxType(TaxType.SimpleTax)

          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          val request = YplRequest(
            searchId = "",
            checkIn = checkIn,
            checkOut = checkOut,
            occ = YplOccInfo(Some(0), None, Some(0), None),
            bookingDate = checkIn,
            channels = Set(YplMasterChannel.APS),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          val ctx = YplContext(request)
          val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
          val pricingDataHP =
            YplPricingData(Right(hotelPrice), hotelMeta, dispatchChannels, Map(aValidRateFence -> dispatchChannels))
          val result = getHotel(pricingDataHP, ctx)
          val hotel = Await.result(result, 10.minutes)

          hotel must_!= None

          val roomOptionRp2_Rc1 =
            hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(2) && r.rateCategoryId == 1)
          roomOptionRp2_Rc1 must_!= None

          val roomRp2_Rc1 = roomOptionRp2_Rc1.get
          round(roomRp2_Rc1.netEx) must_== round(200.0)
        }

        "Calculate channel discount without discount setting" in {

          val baseDailyPrice =
            aValidOTAOccupancyPrice.withOccupancy(2).withAmount(200.0).withTaxAndFee(taxesComprehensive)
          val baseApsDailyPrice =
            aValidOTAOccupancyPrice.withOccupancy(2).withAmount(20.0).withTaxAndFee(taxesComprehensive)

          val dailyPriceOccTwo = aValidOTADailyPrice.withPrices(Seq(baseDailyPrice)).withCommissions(Seq(commission))

          val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceOccTwo))
          val channelRoomRate = channelRoomRateBase.withRateCategories(Seq(rateCategory))

          val commissionAPS = commissionAPSBase.withValue(28.2051282051282)
          val dailyPriceAPS =
            dailyPriceOccTwo.withPrices(Seq(baseApsDailyPrice)).clearChannelDiscount.withCommissions(Seq(commissionAPS))
          val rateCategoryAPS = aValidOTARateCategory.withDailyPrices(Seq(dailyPriceAPS))
          val channelRoomRateAPS = channelRoomRateBase.withRateCategories(Seq(rateCategoryAPS)).withChannelId(2)

          val channelRoomRates = Seq(channelRoomRate, channelRoomRateAPS)
          val hotelPrice =
            hotelPriceBase
              .withChannelRoomRate(channelRoomRates)
              .withTaxType(TaxType.SimpleTax) // .withSourceType(SourceType.Pull)

          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)

          val request = aValidYplRequest
            .withCheckIn(checkIn)
            .withCheckout(checkOut)
            .withOccupancyInfo(YplOccInfo(Some(2), None, Some(1), None))
            .withBookingDate(checkIn.minusDays(1))
            .withChannels(Set(YplMasterChannel.RTL, YplMasterChannel.APS))

          "should not calculate channel discount for any supplier that don't have channel discount setting" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val ctx = YplContext(request)
            val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
            val pricingDataHP = YplPricingData(Right(hotelPrice.withSourceType(SourceType.Pull)),
                                               hotelMeta,
                                               dispatchChannels,
                                               Map(aValidRateFence -> dispatchChannels))
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None

            val roomOption =
              hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(2) && r.rateCategoryId == 1)
            roomOption must_!= None

            val room = roomOption.get

            room.netEx must_== 20.00
            room.netIn must_== 41.68
            room.sellEx must_== 25.64102564102564
            room.sellIn must_== 49.48717948717948
            room.tax must_== 18.28
            room.fee must_== 3.4000000000000004
            room.margin must_== 5.64102564102564
            room.pf must_== 2.166153846153847
          }

          "calculate channel discount for pull supplier that don't have channel discount setting" in {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val ctx = YplContext(request)
            val dispatchChannels = aValidYplDispatchChannels.withMasterChannels(request.channels)
            val pricingDataHP = YplPricingData(Right(hotelPrice.withSourceType(SourceType.Pull)),
                                               hotelMeta,
                                               dispatchChannels,
                                               Map(aValidRateFence -> dispatchChannels))
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None

            val roomOption =
              hotel.get.rooms.find(r => r.roomTypeId == 13193032 && r.channel.contains(2) && r.rateCategoryId == 1)
            roomOption must_!= None

            val room = roomOption.get

            room.netEx must_== 20.00
            room.netIn must_== 41.68
            room.sellEx must_== 25.64102564102564
            room.sellIn must_== 49.48717948717948
            room.tax must_== 18.28
            room.fee must_== 3.4000000000000004
            room.margin must_== 5.64102564102564
            room.pf must_== 2.166153846153847
          }

        }
      }

      "promotion" should {

        trait PromotionTestScope extends Scope {
          val dateMillis = 1527526800000L
          val date = new DateTime(dateMillis)
          val dateMillisSecondDay = date.plusDays(1).getMillis
          val promotionPeriodIntervalRestrictions: Seq[PeriodInterval] =
            Seq(aValidOTAPromotionRestrictionDayPeriodInterval)
          val basePromotionRestriction = aValidOTAPromotionRestriction
            .withBookingIntervals(promotionPeriodIntervalRestrictions)
            .withCustomerSegments(Seq(aValidOTAPromotionCustomerSegment))
          val basePromotion = aValidOTAPromotion
            .withId(1)
            .withTypeId(1)
            .withCmsTypeId(46310)
            .withDiscountTypeId(3)
            .withCmsDiscountTypeId(46316)
            .withCancellationCode("3D100P_100P")
            .withRestriction(basePromotionRestriction)
          val roomType = aValidOTARoomType.withMaxOccupancy(2).withSupplierRatePlanId("BAR").withSupplierRoomId("STD")
          val baseChannelRoomRate = aValidOTAChannelRoomRate.withCurrencyCode("EUR").withProcessingFee(1.0)
          val baseHotelPrice = aValidOTAHotelPrice.withRoomTypes(Seq(roomType)).withCheckInMillis(dateMillis)
        }

        "return correct promo for each cancellation policy group" in new PromotionTestScope {
          // mock tax filter
          val mockCache: TaxFilterCache = mock[TaxFilterCache]
          when(mockCache.get(any[String])).thenReturn(Some(List.empty))
          TaxFilterCacheAccessor.initialize(mockCache)

          val promotionNonRefund = basePromotion
            .withDiscountTypeId(1)
            .withDiscounts(Seq(aValidOTADiscount.withDate(dateMillis).withValue(20.0)))
            .withCancellationCode("365D100P_100P")
          val promotionPartialRefund = basePromotion
            .withId(2)
            .withDiscountTypeId(1)
            .withDiscounts(Seq(aValidOTADiscount.withDate(dateMillis).withValue(20.0)))
            .withCancellationCode("10D1N_3D50P_100P")
          val promotionFreeCancellation = basePromotion
            .withId(3)
            .withDiscountTypeId(1)
            .withDiscounts(Seq(aValidOTADiscount.withDate(dateMillis).withValue(20.0)))
            .withCancellationCode("1D100P_100P")

          val promotions = Seq(promotionNonRefund, promotionPartialRefund, promotionFreeCancellation)
          val dailyPrice = aValidOTADailyPrice.withDateMillis(dateMillis)
          val rateCategory = aValidOTARateCategory.withDailyPrices(Seq(dailyPrice)).withPromotions(promotions)
          val channelRoomRate =
            aValidOTAChannelRoomRate.withCurrencyCode("EUR").withProcessingFee(1.0).withRateCategories(Seq(rateCategory))
          val hotelPrice = aValidOTAHotelPrice
            .withRoomTypes(Seq(roomType))
            .withCheckInMillis(dateMillis)
            .withChannelRoomRate(Seq(channelRoomRate))

          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          val request = YplRequest(
            "",
            checkIn,
            checkOut,
            occ = YplOccInfo(Some(1), None, Some(1), None),
            bookingDate = checkIn.minusDays(2),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          val ctx = YplContext(request)
          val pricingDataHP =
            YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
          val result = getHotel(pricingDataHP, ctx)
          val hotel = Await.result(result, 10.minutes)

          hotel must_!= None

          val rooms = hotel.get.rooms
          rooms.size must_== 4

          rooms.find(_.discountInfo.promotion.isEmpty) must_!= None
          rooms.find(_.cxlCode == "1D100P_100P") must_!= None // Free cancellation group
          rooms.find(_.cxlCode == "10D1N_3D50P_100P") must_!= None // Partial fee cancellation group
          rooms.find(_.cxlCode == "365D100P_100P") must_!= None // Non refundable
        }

        "return correct price for promotion free night" in new PromotionTestScope {
          // mock tax filter
          val mockCache: TaxFilterCache = mock[TaxFilterCache]
          when(mockCache.get(any[String])).thenReturn(Some(List.empty))
          TaxFilterCacheAccessor.initialize(mockCache)

          val promoWithRestriction = basePromotion
            .withDiscountTypeId(4)
            .withDiscounts(Seq(aValidOTADiscount.withDate(dateMillis).withValue(1)))
            .withRestriction(aValidOTAPromotionRestriction
              .withBookingIntervals(promotionPeriodIntervalRestrictions)
              .withCustomerSegments(Seq(aValidOTAPromotionCustomerSegment)))

          val promotions = Seq(promoWithRestriction)
          val dailyPrice = aValidOTADailyPrice.withDateMillis(dateMillis)
          val dailyPriceSecondDay = dailyPrice.withDateMillis(dateMillisSecondDay)
          val rateCategory =
            aValidOTARateCategory.withDailyPrices(Seq(dailyPrice, dailyPriceSecondDay)).withPromotions(promotions)
          val channelRoomRate =
            aValidOTAChannelRoomRate.withCurrencyCode("EUR").withProcessingFee(1.0).withRateCategories(Seq(rateCategory))
          val hotelPrice = baseHotelPrice.withChannelRoomRate(Seq(channelRoomRate)).withLengthOfStay(2)

          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          val request = YplRequest(
            "",
            checkIn,
            checkOut,
            occ = YplOccInfo(Some(1), None, Some(1), None),
            bookingDate = checkIn.minusDays(2),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          val ctx = YplContext(request)
          val pricingDataHP =
            YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
          val result = getHotel(pricingDataHP, ctx)
          val hotel = Await.result(result, 10.minutes)

          hotel must_!= None

          val rooms = hotel.get.rooms
          val nonDiscountRoom = rooms.find(r => r.discountInfo.promotion.isEmpty).get
          val discountedRoom = rooms.find(r => r.discountInfo.promotion.isDefined).get

          nonDiscountRoom.prices.exists(p => p.netExclusive == 0.0) must_== false
          discountedRoom.prices.exists(p => p.netExclusive == 0.0) must_== true
        }

        "return correct price for promotion amount per night" in new PromotionTestScope {
          // mock tax filter
          val mockCache: TaxFilterCache = mock[TaxFilterCache]
          when(mockCache.get(any[String])).thenReturn(Some(List.empty))
          TaxFilterCacheAccessor.initialize(mockCache)

          val promoWithRestriction = basePromotion
            .withDiscountTypeId(3)
            .withDiscounts(
              Seq(
                aValidOTADiscount.withDate(dateMillis).withValue(25),
                aValidOTADiscount.withDate(dateMillisSecondDay).withValue(15),
              ),
            )

          val promotions = Seq(promoWithRestriction)
          val dailyPrice =
            aValidOTADailyPrice.withDateMillis(dateMillis).withPrices(Seq(aValidOTAOccupancyPrice.withAmount(510.0)))
          val dailyPriceSecondDay = dailyPrice.withDateMillis(dateMillisSecondDay)
          val rateCategory =
            aValidOTARateCategory.withDailyPrices(Seq(dailyPrice, dailyPriceSecondDay)).withPromotions(promotions)
          val channelRoomRate = baseChannelRoomRate.withRateCategories(Seq(rateCategory))
          val hotelPrice = baseHotelPrice.withChannelRoomRate(Seq(channelRoomRate)).withLengthOfStay(2)

          val checkIn = new DateTime(hotelPrice.checkInMillis)
          val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
          val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          val request = YplRequest(
            "",
            checkIn,
            checkOut,
            occ = YplOccInfo(Some(1), None, Some(1), None),
            bookingDate = checkIn.minusDays(2),
            supplierFeatures = aValidSupplierFeatures,
            whitelabelSetting = aValidwhitelabelSetting,
            fences = aValidYplRequestFences,
          )
          val ctx = YplContext(request)
          val pricingDataHP =
            YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
          val result = getHotel(pricingDataHP, ctx)
          val hotel = Await.result(result, 10.minutes)

          hotel must_!= None

          val rooms = hotel.get.rooms
          val nonDiscountRoom = rooms.find(r => r.discountInfo.promotion.isEmpty).get
          val discountedRoom = rooms.find(r => r.discountInfo.promotion.isDefined).get

          nonDiscountRoom.netEx must_== 1020.00
          discountedRoom.netEx must_== 980.0
        }

        "last minute promotion" should {

          trait LastMinutePromotionTestScope extends Scope with PromotionTestScope {
            val promotionRestriction = basePromotionRestriction.withMaxAdvance(0)
            val promoWithRestriction = basePromotion
              .withRestriction(promotionRestriction)
              .withDiscountTypeId(1)
              .withDiscounts(
                Seq(aValidOTADiscount.withDate(dateMillis).withValue(25),
                    aValidOTADiscount.withDate(dateMillisSecondDay).withValue(15)),
              )

            val promotions = Seq(promoWithRestriction)
            val dailyPrice =
              aValidOTADailyPrice.withDateMillis(dateMillis).withPrices(Seq(aValidOTAOccupancyPrice.withAmount(510.0)))
            val dailyPriceSecondDay = dailyPrice.withDateMillis(dateMillisSecondDay)
            val rateCategory =
              aValidOTARateCategory.withDailyPrices(Seq(dailyPrice, dailyPriceSecondDay)).withPromotions(promotions)
            val channelRoomRate = baseChannelRoomRate.withRateCategories(Seq(rateCategory))
            val hotelPrice = baseHotelPrice.withChannelRoomRate(Seq(channelRoomRate)).withLengthOfStay(2)

            val checkIn = new DateTime(hotelPrice.checkInMillis)
            val checkOut = checkIn.plusDays(hotelPrice.lengthOfStay)
            val hotelMeta = aValidHotelInfo.withProcessingFeeOption(1)
          }

          "return no promotion room for last minute promotion and booking date is not today" in new LastMinutePromotionTestScope {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val request = YplRequest(
              "",
              checkIn,
              checkOut,
              occ = YplOccInfo(Some(1), None, Some(1), None),
              bookingDate = checkIn.withTimeAtStartOfDay(),
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            val ctx = YplContext(request)
            val pricingDataHP =
              YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None

            val rooms = hotel.get.rooms
            rooms.find(r => r.discountInfo.promotion.isEmpty).isDefined must_== true
            rooms.find(r => r.discountInfo.promotion.isDefined).isDefined must_== false
          }

          "return promotion room for last minute promotion and booking date is today" in new LastMinutePromotionTestScope {
            // mock tax filter
            val mockCache: TaxFilterCache = mock[TaxFilterCache]
            when(mockCache.get(any[String])).thenReturn(Some(List.empty))
            TaxFilterCacheAccessor.initialize(mockCache)

            val request = YplRequest(
              "",
              checkIn,
              checkOut,
              occ = YplOccInfo(Some(1), None, Some(1), None),
              bookingDate = checkIn.withTimeAtStartOfDay().plusHours(7),
              supplierFeatures = aValidSupplierFeatures,
              whitelabelSetting = aValidwhitelabelSetting,
              fences = aValidYplRequestFences,
            )
            val ctx = YplContext(request)
            val pricingDataHP =
              YplPricingData(Right(hotelPrice), hotelMeta, aValidYplDispatchChannels, aValidFencedYplDispatchChannels)
            val result = getHotel(pricingDataHP, ctx)
            val hotel = Await.result(result, 10.minutes)

            hotel must_!= None

            val rooms = hotel.get.rooms
            rooms.find(r => r.discountInfo.promotion.isEmpty).isDefined must_== true
            rooms.find(r => r.discountInfo.promotion.isDefined).isDefined must_== true
          }
        }
      }
    }
  }

  "isMatchedSupplier return true when supplier in meta and proto are the same" in {
    val mockHotelMetaSupplier = Set(332, 3038, 29014)
    val mockHotelProtoSupplierId = 332
    val result = flow.isMatchedSupplier(mockHotelMetaSupplier, mockHotelProtoSupplierId)
    result mustEqual true
  }

  "isMatchedSupplier return false when supplier in meta and proto are not the same" in {
    val mockHotelMetaSupplier = Set(332, 3038, 29014)
    val mockHotelProtoSupplierId = 27901
    val result = flow.isMatchedSupplier(mockHotelMetaSupplier, mockHotelProtoSupplierId)
    result mustEqual false
  }

  "isMatchedSupplier return true when supplier in meta contain GTTJTB supplier id 28073 even proto is not the same" in {
    val mockHotelMetaSupplier = Set(332, 3038, 28073)
    val mockHotelProtoSupplierId = DMC.GTTJTB
    val result = flow.isMatchedSupplier(mockHotelMetaSupplier, mockHotelProtoSupplierId)
    result mustEqual true
  }

  "processRoomRpmFlow DmcData construction" should {
    val entryModel = aValidHotelEntryModel.withRooms(List.empty[YplRoomEntry]).build
    val baseValidRoom = aValidRoom.withRoomTypeId(1).withChannel(YplMasterChannel.RTL).withRateCategoryId(1)
    val JTBWLEntryMode = entryModel.copy(supplierId = DMC.JTBWL)
    val YCSEntryMode = entryModel.copy(supplierId = DMC.YCS)
    val DirectConnectEntryMode = entryModel.copy(supplierId = DMC.Disney)
    val OtherEntryMode = entryModel.copy(supplierId = DMC.Expedia)

    def matchSupplierIdCase(supplierId: SupplierId): YPLHotel = supplierId match {
      case DMC.JTBWL => aValidHotel.withSupplierId(DMC.JTBWL) withRooms
          List(baseValidRoom.withSupplierId(DMC.JTBWL))
      case DMC.YCS => aValidHotel.withSupplierId(DMC.YCS) withRooms
          List(baseValidRoom.withSupplierId(DMC.YCS))
      case DMC.Disney => aValidHotel.withSupplierId(DMC.Disney) withRooms
          List(baseValidRoom.withSupplierId(DMC.Disney))
      case _ => aValidHotel.withSupplierId(DMC.Expedia) withRooms
          List(baseValidRoom.withSupplierId(DMC.Expedia))
    }

    trait TestFixture extends Scope with YCSFPLOSProcessorMock[YplContext] {
      def test(yplHotelEntryModelOpt: Option[YplHotelEntryModel], ctx: YplContext): Future[Option[YPLHotel]] = {
        val yplHotel = yplHotelEntryModelOpt.map(hotel => matchSupplierIdCase(hotel.supplierId)) match {
          case Some(hotel) => hotel
          case _ => aValidHotel
        }
        Future.successful(addDmcData(Some(yplHotel))(ctx))
      }

      override def addDmcData(hotel: YPLHotel): YPLHotel = {
        val rooms =
          hotel.rooms.map(r => r.copy(dmcData = Some(DmcData(externalData = "test external data JTBWL construct"))))
        hotel.copy(rooms = rooms)
      }

      override def addDmcDataForYcsHotel(hotel: YPLHotel): YPLHotel = {
        val rooms = hotel.rooms.map(room =>
          room.copy(dmcData = Some(DmcData(externalData = "test external data ResellRateAllotment construct"))))
        hotel.copy(rooms = rooms)
      }

      override def addDmcDataForApmHotel(hotel: YPLHotel): YPLHotel = {
        val rooms = hotel.rooms.map(room =>
          room.copy(dmcData = Some(DmcData(externalData = "test external data apm data construction"))))
        hotel.copy(rooms = rooms)
      }
    }

    "call construct JTBWL dmcData function correctly" in new TestFixture {

      val requestRoom =
        aValidRoom.withRoomTypeId(1).withChannel(YplMasterChannel.RTL).withRateCategoryId(1).withSupplierId(DMC.JTBWL)
      val yplHotel = aValidHotel.withSupplierId(DMC.JTBWL) withRooms List(requestRoom)

      val expectedRooms = aValidRoom
        .withRoomTypeId(1)
        .withChannel(YplMasterChannel.RTL)
        .withRateCategoryId(1)
        .withSupplierId(DMC.JTBWL)
        .withDmcData(DmcData(externalData = "test external data JTBWL construct"))

      val expectedResult = yplHotel.withRooms(List(expectedRooms)).build

      whenReady(test(Some(JTBWLEntryMode), aValidYplContext)) { res =>
        res must beSome(expectedResult)
      }
    }

    "addDmcData - call construct YCS dmcData with ResellAllotment function correctly" in new TestFixture {

      val requestRoom =
        aValidRoom.withRoomTypeId(1).withChannel(YplMasterChannel.RTL).withRateCategoryId(1).withSupplierId(DMC.YCS)
      val yplHotel = aValidHotel.withSupplierId(DMC.YCS) withRooms List(requestRoom)

      val expectedRooms = aValidRoom
        .withRoomTypeId(1)
        .withChannel(YplMasterChannel.RTL)
        .withRateCategoryId(1)
        .withSupplierId(DMC.YCS)
        .withDmcData(DmcData(externalData = "test external data ResellRateAllotment construct"))

      val expectedResult = yplHotel.withRooms(List(expectedRooms)).build

      whenReady(test(Some(YCSEntryMode), aValidYplContext)) { res =>
        res must beSome(expectedResult)
      }
    }

    "addDmcData - call construct direct connect dmcData function correctly" in new TestFixture {

      val requestRoom =
        aValidRoom.withRoomTypeId(1).withChannel(YplMasterChannel.RTL).withRateCategoryId(1).withSupplierId(DMC.Disney)
      val yplHotel = aValidHotel.withSupplierId(DMC.Disney) withRooms List(requestRoom)

      val expectedRooms = aValidRoom
        .withRoomTypeId(1)
        .withChannel(YplMasterChannel.RTL)
        .withRateCategoryId(1)
        .withSupplierId(DMC.Disney)
        .withDmcData(DmcData(externalData = "test external data apm data construction"))

      val expectedResult = yplHotel.withRooms(List(expectedRooms)).build

      whenReady(test(Some(DirectConnectEntryMode), aValidYplContext)) { res =>
        res must beSome(expectedResult)
      }
    }

    "addDmcData - call construct JTBLW dmcData function correctly when APM Direct Connect experiment is enabled" in new TestFixture {

      val requestRoom =
        aValidRoom.withRoomTypeId(1).withChannel(YplMasterChannel.RTL).withRateCategoryId(1).withSupplierId(DMC.JTBWL)
      val yplHotel = aValidHotel.withSupplierId(DMC.JTBWL) withRooms List(requestRoom)

      val expectedRooms = aValidRoom
        .withRoomTypeId(1)
        .withChannel(YplMasterChannel.RTL)
        .withRateCategoryId(1)
        .withSupplierId(DMC.JTBWL)
        .withDmcData(DmcData(externalData = "test external data JTBWL construct"))

      val expectedResult = yplHotel.withRooms(List(expectedRooms)).build

      whenReady(test(Some(JTBWLEntryMode), aValidYplContext)) { res =>
        res must beSome(expectedResult)
      }
    }

    "addDmcData - no change on dmcData when supplier is not YCS, APMDirectConnect, and JTBLW" in new TestFixture {

      val requestRoom =
        aValidRoom.withRoomTypeId(1).withChannel(YplMasterChannel.RTL).withRateCategoryId(1).withSupplierId(DMC.Expedia)
      val yplHotel = aValidHotel.withSupplierId(DMC.Expedia) withRooms List(requestRoom)
      val expectedRooms =
        aValidRoom.withRoomTypeId(1).withChannel(YplMasterChannel.RTL).withRateCategoryId(1).withSupplierId(DMC.Expedia)
      val expectedResult = yplHotel.withRooms(List(expectedRooms)).build

      whenReady(test(Some(OtherEntryMode), aValidYplContext)) { res =>
        res must beSome(expectedResult)
      }
    }
  }
}
