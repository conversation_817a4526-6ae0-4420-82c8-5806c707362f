package com.agoda.papi.ypl.logic

import com.agoda.papi.enums.request.BookingDurationType
import com.agoda.papi.ypl.models.api.request.YplFeatureRequest
import com.agoda.papi.ypl.models.builders.ypl.YplDataExample
import com.agoda.papi.ypl.models.pricing.proto.TimeInterval
import com.agoda.papi.ypl.models.{YPLTestContexts, YPLTestDataBuilders, YplContext, YplRegulationFeatureEnabledSetting}
import com.agoda.papi.ypl.services.HourlyRatesServiceImpl
import org.joda.time.{DateTimeZone, LocalTime}
import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.ExecutionContext

class HourlyRatesServiceSpec
  extends SpecificationWithJUnit
    with YplDataExample
    with YPLTestDataBuilders
    with YPLTestContexts {

  private val hourlyRatesService = new HourlyRatesServiceImpl[YplContext] {
    override implicit def ec: ExecutionContext = scala.concurrent.ExecutionContext.Implicits.global
  }

  "HourlyRatesService.filterPastHourlyAvailableSlots" should {
    val ctx = aValidYplContext.withRequest(aValidYplRequest).build

    "filterPastHourlyAvailableSlots - when hotel TZ > booking TZ for same day search" in {
      // hotel time zone: GMT+7:30
      // booking time in request: 15:00 GMT+5 (17:30 in hotel time zone)
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          bookingDate = ctx.request.bookingDate
            .withZone(DateTimeZone.forOffsetHours(5))
            .withTime(new LocalTime(15, 0, 0))
            .withDate(ctx.request.checkIn.toLocalDate),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "16:00"),
              TimeInterval(duration = 3, from = "17:00"),
              TimeInterval(duration = 3, from = "18:00"),
              TimeInterval(duration = 3, from = "19:00"),
              TimeInterval(duration = 3, from = "20:00"),
            ),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "17:00"),
              TimeInterval(duration = 3, from = "18:00"),
              TimeInterval(duration = 3, from = "19:00"),
            ),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "10:00"),
            ),
          )
          .build,
      )

      // GMT+7:30
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterPastHourlyAvailableSlots(data)

      // third room should be filtered out because it does not have any available slots
      result.get.rooms.size should_== 2

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "17:00"),
        TimeInterval(duration = 3, from = "18:00"),
        TimeInterval(duration = 3, from = "19:00"),
        TimeInterval(duration = 3, from = "20:00"),
      )

      result.get.rooms(1).hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "17:00"),
        TimeInterval(duration = 3, from = "18:00"),
        TimeInterval(duration = 3, from = "19:00"),
      )
    }

    "filterPastHourlyAvailableSlots - when hotel TZ < booking TZ for same day search" in {
      // hotel time zone: GMT+5:00
      // booking time in request: 17:00 GMT+7:30 (14:30 in hotel time zone)
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          bookingDate = ctx.request.bookingDate
            .withZone(DateTimeZone.forOffsetHoursMinutes(7, 30))
            .withTime(new LocalTime(17, 0, 0))
            .withDate(ctx.request.checkIn.toLocalDate),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "13:00"),
              TimeInterval(duration = 3, from = "14:00"),
              TimeInterval(duration = 3, from = "15:00"),
              TimeInterval(duration = 3, from = "16:00"),
              TimeInterval(duration = 3, from = "17:00"),
              TimeInterval(duration = 3, from = "18:00"),
            ),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "13:00"),
              TimeInterval(duration = 3, from = "14:00"),
              TimeInterval(duration = 3, from = "15:00"),
              TimeInterval(duration = 3, from = "16:00"),
            ),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "10:00"),
            ),
          )
          .build,
      )

      // GMT+5
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(5).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterPastHourlyAvailableSlots(data)

      // third room should be filtered out because it does not have any available slots
      result.get.rooms.size should_== 2

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "14:00"),
        TimeInterval(duration = 3, from = "15:00"),
        TimeInterval(duration = 3, from = "16:00"),
        TimeInterval(duration = 3, from = "17:00"),
        TimeInterval(duration = 3, from = "18:00"),
      )

      result.get.rooms(1).hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "14:00"),
        TimeInterval(duration = 3, from = "15:00"),
        TimeInterval(duration = 3, from = "16:00"),
      )
    }

    "filterPastHourlyAvailableSlots - when hotel TZ = booking TZ for same day search" in {
      // hotel time zone: GMT+7:00
      // booking time in request: 17:00 GMT+7:00
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          bookingDate = ctx.request.bookingDate
            .withZone(DateTimeZone.forOffsetHours(7))
            .withTime(new LocalTime(17, 0, 0))
            .withDate(ctx.request.checkIn.toLocalDate),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "13:00"),
              TimeInterval(duration = 3, from = "14:00"),
              TimeInterval(duration = 3, from = "15:00"),
              TimeInterval(duration = 3, from = "16:00"),
              TimeInterval(duration = 3, from = "17:00"),
              TimeInterval(duration = 3, from = "18:00"),
            ),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "13:00"),
              TimeInterval(duration = 3, from = "14:00"),
              TimeInterval(duration = 3, from = "15:00"),
              TimeInterval(duration = 3, from = "16:00"),
            ),
          )
          .build,
      )

      // GMT+7
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterPastHourlyAvailableSlots(data)

      // second room should be filtered out because it does not have any available slots
      result.get.rooms.size should_== 1

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "17:00"),
        TimeInterval(duration = 3, from = "18:00"),
      )
    }

    "filterPastHourlyAvailableSlots - not filter when booking date is before checkIn date when hotel TZ < booking TZ" in {
      // hotel time zone: GMT+5:00
      // booking time in request: 17:00 GMT+7:30 (14:30 in hotel time zone)
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          bookingDate = ctx.request.bookingDate
            .withDate(ctx.request.checkIn.toLocalDate)
            .withZone(DateTimeZone.forOffsetHoursMinutes(7, 30))
            .withTime(new LocalTime(17, 0, 0)),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "13:00"),
              TimeInterval(duration = 3, from = "14:00"),
              TimeInterval(duration = 3, from = "15:00"),
              TimeInterval(duration = 3, from = "16:00"),
              TimeInterval(duration = 3, from = "17:00"),
              TimeInterval(duration = 3, from = "18:00"),
            ),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "13:00"),
              TimeInterval(duration = 3, from = "14:00"),
              TimeInterval(duration = 3, from = "15:00"),
              TimeInterval(duration = 3, from = "16:00"),
            ),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "10:00"),
            ),
          )
          .build,
      )

      // GMT+5
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(5).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterPastHourlyAvailableSlots(data)

      // third room should be filtered out because it does not have any available slots
      result.get.rooms.size should_== 2

      result.get.rooms.head.hourlyAvailableSlots shouldEqual Seq(
        TimeInterval(duration = 3, from = "14:00"),
        TimeInterval(duration = 3, from = "15:00"),
        TimeInterval(duration = 3, from = "16:00"),
        TimeInterval(duration = 3, from = "17:00"),
        TimeInterval(duration = 3, from = "18:00"),
      )

      result.get.rooms(1).hourlyAvailableSlots shouldEqual Seq(
        TimeInterval(duration = 3, from = "14:00"),
        TimeInterval(duration = 3, from = "15:00"),
        TimeInterval(duration = 3, from = "16:00"),
      )
    }

    "filterPastHourlyAvailableSlots - not filter slots when booking date is before checkOut - 2 hours" in {
      // hotel time zone: GMT+5:00
      // booking time in request: 17:00 GMT+9:00 (13:00 in hotel time zone)
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          bookingDate = ctx.request.bookingDate
            .withDate(ctx.request.checkIn.toLocalDate)
            .withZone(DateTimeZone.forOffsetHoursMinutes(9, 0))
            .withTime(new LocalTime(17, 0, 0)),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "11:00"),
              TimeInterval(duration = 3, from = "12:00"),
              TimeInterval(duration = 3, from = "13:00"),
              TimeInterval(duration = 3, from = "14:00"),
              TimeInterval(duration = 3, from = "15:00"),
              TimeInterval(duration = 3, from = "16:00"),
              TimeInterval(duration = 3, from = "17:00"),
              TimeInterval(duration = 3, from = "18:00"),
            ),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "13:00"),
              TimeInterval(duration = 3, from = "14:00"),
              TimeInterval(duration = 3, from = "15:00"),
              TimeInterval(duration = 3, from = "16:00"),
            ),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "10:00"),
              TimeInterval(duration = 3, from = "11:00"),
              TimeInterval(duration = 3, from = "12:00"),
            ),
          )
          .build,
      )

      // GMT+5
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(5).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterPastHourlyAvailableSlots(data)

      // third room should be filtered out because it does not have any available slots
      result.get.rooms.size should_== 2

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "13:00"),
        TimeInterval(duration = 3, from = "14:00"),
        TimeInterval(duration = 3, from = "15:00"),
        TimeInterval(duration = 3, from = "16:00"),
        TimeInterval(duration = 3, from = "17:00"),
        TimeInterval(duration = 3, from = "18:00"),
      )

      result.get.rooms(1).hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "13:00"),
        TimeInterval(duration = 3, from = "14:00"),
        TimeInterval(duration = 3, from = "15:00"),
        TimeInterval(duration = 3, from = "16:00"),
      )
    }

    "filter past midnight HourlyAvailableSlots when showPastMidnightSlot is false" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          featureRequest = YplFeatureRequest(showPastMidnightSlots = false),
          bookingDate = ctx.request.bookingDate
            .withZone(DateTimeZone.forOffsetHours(7))
            .withTime(new LocalTime(10, 0, 0))
            .withDate(ctx.request.checkIn.toLocalDate),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 6, from = "15:00"),
              TimeInterval(duration = 6, from = "16:00"),
              TimeInterval(duration = 6, from = "17:00"),
              TimeInterval(duration = 6, from = "18:00"),
              TimeInterval(duration = 6, from = "19:00"),
              TimeInterval(duration = 6, from = "20:00"),
            ),
          )
          .build,
      )

      // GMT+7
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterPastHourlyAvailableSlots(data)

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 6, from = "15:00"),
        TimeInterval(duration = 6, from = "16:00"),
        TimeInterval(duration = 6, from = "17:00"),
      )
    }

    "not filter past midnight HourlyAvailableSlots when showPastMidnightSlot is true" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          featureRequest = YplFeatureRequest(showPastMidnightSlots = true),
          bookingDate = ctx.request.bookingDate
            .withZone(DateTimeZone.forOffsetHours(7))
            .withTime(new LocalTime(10, 0, 0))
            .withDate(ctx.request.checkIn.toLocalDate),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 6, from = "15:00"),
              TimeInterval(duration = 6, from = "16:00"),
              TimeInterval(duration = 6, from = "17:00"),
              TimeInterval(duration = 6, from = "18:00"),
              TimeInterval(duration = 6, from = "19:00"),
              TimeInterval(duration = 6, from = "20:00"),
            ),
          )
          .build,
      )

      // GMT+7
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterPastHourlyAvailableSlots(data)

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 6, from = "15:00"),
        TimeInterval(duration = 6, from = "16:00"),
        TimeInterval(duration = 6, from = "17:00"),
        TimeInterval(duration = 6, from = "18:00"),
        TimeInterval(duration = 6, from = "19:00"),
        TimeInterval(duration = 6, from = "20:00"),
      )
    }

    "correctly filter some past midnight HourlyAvailableSlots when showPastMidnightSlot is true and checkin is after all available slots" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          featureRequest = YplFeatureRequest(showPastMidnightSlots = true),
          bookingDate = ctx.request.bookingDate
            .withZone(DateTimeZone.forOffsetHours(9))
            .withTime(new LocalTime(23, 0, 0))
            .withDate(ctx.request.checkIn.toLocalDate),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 6, from = "15:00"),
              TimeInterval(duration = 6, from = "16:00"),
              TimeInterval(duration = 6, from = "17:00"),
              TimeInterval(duration = 6, from = "18:00"),
              TimeInterval(duration = 6, from = "19:00"),
              TimeInterval(duration = 6, from = "20:00"),
            ),
          )
          .build,
      )

      // GMT+7
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterPastHourlyAvailableSlots(data)

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 6, from = "18:00"),
        TimeInterval(duration = 6, from = "19:00"),
        TimeInterval(duration = 6, from = "20:00"),
      )
    }

    "do not show HourlyAvailableSlots for overnight request when enableDayUseHackyRoomInOvernight is false" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Nightly),
          featureRequest = YplFeatureRequest(enableHourlySlotsForDayuseInOvernight = false),
          bookingDate = ctx.request.bookingDate
            .withZone(DateTimeZone.forOffsetHours(7))
            .withTime(new LocalTime(10, 0, 0))
            .withDate(ctx.request.checkIn.toLocalDate),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 6, from = "15:00"),
              TimeInterval(duration = 6, from = "16:00"),
              TimeInterval(duration = 6, from = "17:00"),
              TimeInterval(duration = 6, from = "18:00"),
              TimeInterval(duration = 6, from = "19:00"),
              TimeInterval(duration = 6, from = "20:00"),
            ),
          )
          .build,
      )

      // GMT+7
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterPastHourlyAvailableSlots(data)

      result.get.rooms.head.hourlyAvailableSlots should_== List()
    }

    "show HourlyAvailableSlots for overnight request when enableDayUseHackyRoomInOvernight is true" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Nightly),
          featureRequest = YplFeatureRequest(enableHourlySlotsForDayuseInOvernight = true),
          bookingDate = ctx.request.bookingDate
            .withZone(DateTimeZone.forOffsetHours(7))
            .withTime(new LocalTime(10, 0, 0))
            .withDate(ctx.request.checkIn.toLocalDate),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 6, from = "15:00"),
              TimeInterval(duration = 6, from = "16:00"),
              TimeInterval(duration = 6, from = "17:00"),
              TimeInterval(duration = 6, from = "18:00"),
              TimeInterval(duration = 6, from = "19:00"),
              TimeInterval(duration = 6, from = "20:00"),
            ),
          )
          .build,
      )

      // GMT+7
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterPastHourlyAvailableSlots(data)

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 6, from = "15:00"),
        TimeInterval(duration = 6, from = "16:00"),
        TimeInterval(duration = 6, from = "17:00"),
      )
    }
  }

  "HourlyRatesService.calculateSingleTimeSlot" should {
    val ctx = aValidYplContext.withRequest(aValidYplRequest).build
    "return original timeSlots when selectedCheckIn time is empty" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "16:00"),
              TimeInterval(duration = 3, from = "17:00"),
              TimeInterval(duration = 3, from = "18:00"),
              TimeInterval(duration = 3, from = "19:00"),
              TimeInterval(duration = 3, from = "20:00"),
            ),
          )
          .build
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.calculateSingleTimeSlot(data)

      result.get.rooms.size should_== 1

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "16:00"),
        TimeInterval(duration = 3, from = "17:00"),
        TimeInterval(duration = 3, from = "18:00"),
        TimeInterval(duration = 3, from = "19:00"),
        TimeInterval(duration = 3, from = "20:00"),
      )
    }

    "return original timeSlots when selectedCheckIn time is invalid" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          selectedCheckInTime = Some("invalid"),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "16:00"),
              TimeInterval(duration = 3, from = "17:00"),
              TimeInterval(duration = 3, from = "18:00"),
              TimeInterval(duration = 3, from = "19:00"),
              TimeInterval(duration = 3, from = "20:00"),
            ),
          )
          .build
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.calculateSingleTimeSlot(data)

      result.get.rooms.size should_== 1

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "16:00"),
        TimeInterval(duration = 3, from = "17:00"),
        TimeInterval(duration = 3, from = "18:00"),
        TimeInterval(duration = 3, from = "19:00"),
        TimeInterval(duration = 3, from = "20:00"),
      )
    }

    "return exact matching timeSlot when it exists" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          selectedCheckInTime = Some("17:00"),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "16:00"),
              TimeInterval(duration = 3, from = "17:00"),
              TimeInterval(duration = 3, from = "18:00"),
              TimeInterval(duration = 3, from = "19:00"),
              TimeInterval(duration = 3, from = "20:00"),
            ),
          )
          .build
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.calculateSingleTimeSlot(data)

      result.get.rooms.size should_== 1

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "17:00"),
      )
    }

    "return exact matching timeSlot when it exists with maximum duration" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          selectedCheckInTime = Some("17:00"),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 3, from = "16:00"),
              TimeInterval(duration = 3, from = "17:00"),
              TimeInterval(duration = 3, from = "18:00"),
              TimeInterval(duration = 3, from = "19:00"),
              TimeInterval(duration = 3, from = "20:00"),
              TimeInterval(duration = 6, from = "17:00"),
            ),
          )
          .build
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.calculateSingleTimeSlot(data)

      result.get.rooms.size should_== 1

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 6, from = "17:00"),
      )
    }

    "return time slot with adjusted duration and checkIn if all slots are in the past" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          selectedCheckInTime = Some("17:00"),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 6, from = "12:00"),
              TimeInterval(duration = 6, from = "13:00"),
              TimeInterval(duration = 6, from = "14:00"),
              TimeInterval(duration = 6, from = "15:00"),
              TimeInterval(duration = 6, from = "16:00"),
            ),
          )
          .build
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.calculateSingleTimeSlot(data)

      result.get.rooms.size should_== 1

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 5, from = "17:00"),
      )
    }

    "return time slot with adjusted duration and checkIn if all slots are in the past, with last checkout" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          selectedCheckInTime = Some("17:00"),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 6, from = "12:00"),
              TimeInterval(duration = 6, from = "13:00"),
              TimeInterval(duration = 6, from = "14:00"),
              TimeInterval(duration = 6, from = "15:00"),
              TimeInterval(duration = 9, from = "14:00"),
            ),
          )
          .build
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.calculateSingleTimeSlot(data)

      result.get.rooms.size should_== 1

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 6, from = "17:00"),
      )
    }

    "return time slot with adjusted duration and checkIn if all slots are in the past, with minimum duration of at least 2 hours" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          selectedCheckInTime = Some("17:00"),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 6, from = "10:00"),
              TimeInterval(duration = 6, from = "11:00"),
              TimeInterval(duration = 6, from = "12:00"),
            ),
          )
          .build
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.calculateSingleTimeSlot(data)

      result.get.rooms.size should_== 1

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 2, from = "16:00"),
      )
    }

    "return time slot with adjusted duration and checkIn if some slots are in the past and some are in future" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          selectedCheckInTime = Some("16:00"),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 6, from = "10:00"),
              TimeInterval(duration = 6, from = "11:00"),
              TimeInterval(duration = 6, from = "17:00"),
            ),
          )
          .build
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.calculateSingleTimeSlot(data)

      result.get.rooms.size should_== 1

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 6, from = "17:00"),
      )
    }

    "return time slot with nearest checkIn if all slots are in the future" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          selectedCheckInTime = Some("08:00"),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(
              TimeInterval(duration = 6, from = "10:00"),
              TimeInterval(duration = 6, from = "11:00"),
              TimeInterval(duration = 6, from = "12:00"),
              TimeInterval(duration = 6, from = "13:00"),
            ),
          )
          .build
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.calculateSingleTimeSlot(data)

      result.get.rooms.size should_== 1

      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 6, from = "10:00"),
      )
    }
  }

  "HourlyRatesService.filterHourlyDurations" should {
    val ctx = aValidYplContext.withRequest(aValidYplRequest).build
    "return original rooms when hourlyDurations is empty" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(TimeInterval(duration = 3, from = "16:00")),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(TimeInterval(duration = 6, from = "16:00")),
          )
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterHourlyDurations(data)

      result.get.rooms.size should_== 2
      result.get.rooms.head.hourlyAvailableSlots should_== Seq(TimeInterval(duration = 3, from = "16:00"))
      result.get.rooms(1).hourlyAvailableSlots should_== Seq(TimeInterval(duration = 6, from = "16:00"))
    }

    "filter out all rooms not within requested duration" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          hourlyDurationFilter = Set(9, 10, 11, 12),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(TimeInterval(duration = 3, from = "16:00")),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(TimeInterval(duration = 6, from = "16:00")),
          )
          .build,
      )

      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterHourlyDurations(data)

      result.get.rooms.size should_== 0
    }

    "filter out all rooms not within requested duration" in {
      val context = ctx.copy(
        request = ctx.request.copy(
          bookingDurationTypes = List(BookingDurationType.Hourly),
          hourlyDurationFilter = Set(1, 2, 3),
        ),
      )

      val rooms = List(
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(TimeInterval(duration = 3, from = "16:00")),
          )
          .build,
        aValidRoomEntry
          .withHourlyAvailableSlots(
            Seq(TimeInterval(duration = 6, from = "16:00")),
          )
          .build,
      )

      // GMT+7:30
      val hotelEntry = aValidHotelEntryModel
        .withMetaData(
          aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
        )
        .withRooms(rooms)
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )
      val result = hourlyRatesService.filterHourlyDurations(data)

      result.get.rooms.size should_== 1
      result.get.rooms.head.hourlyAvailableSlots should_== Seq(TimeInterval(duration = 3, from = "16:00"))
    }
  }

  "HourlyRatesService.filter30MinsHourlySlots" should {
    val rooms = List(
      aValidRoomEntry
        .withHourlyAvailableSlots(
          Seq(
            TimeInterval(duration = 3, from = "16:00"),
            TimeInterval(duration = 3, from = "16:30"),
            TimeInterval(duration = 3, from = "17:00"),
            TimeInterval(duration = 3, from = "17:30"),
          ),
        )
        .build,
    )

    val hotelEntry = aValidHotelEntryModel
      .withMetaData(
        aValidHotelInfo.withGmtOffset(7).withGmtOffsetMinutes(30).build,
      )
      .withRooms(rooms)
      .build

    "should not return 30 min slots for all flag combinations except when enableThirtyMinsSlots is ON & WL feature is ON" in {
      val testCases = List(
        ("both OFF",
         aValidYplContext
           .withRequest(aValidYplRequest.copy(
             regulationFeatureEnabledSetting =
               YplRegulationFeatureEnabledSetting.default.copy(isEnable30MinsHourlySlots = false),
             featureRequest = YplFeatureRequest(enableThirtyMinsSlots = false),
           ))
           .build),
        ("enableThirtyMinsSlots ON, WL feature OFF",
         aValidYplContext
           .withRequest(aValidYplRequest.copy(
             regulationFeatureEnabledSetting =
               YplRegulationFeatureEnabledSetting.default.copy(isEnable30MinsHourlySlots = false),
             featureRequest = YplFeatureRequest(enableThirtyMinsSlots = true),
           ))
           .build),
        ("enableThirtyMinsSlots OFF, WL feature ON",
         aValidYplContext
           .withRequest(aValidYplRequest.copy(
             regulationFeatureEnabledSetting =
               YplRegulationFeatureEnabledSetting.default.copy(isEnable30MinsHourlySlots = true),
             featureRequest = YplFeatureRequest(enableThirtyMinsSlots = false),
           ))
           .build),
      )

      testCases.map { case (desc, context) =>
        val data = hourlyRatesService.Data(
          Some(hotelEntry),
          context,
          null,
        )

        val result = hourlyRatesService.filter30MinsHourlySlots(data)
        result.get.rooms.size must_== 1
        result.get.rooms.head.hourlyAvailableSlots.size must_== 2
        result.get.rooms.head.hourlyAvailableSlots should_== Seq(
          TimeInterval(duration = 3, from = "16:00"),
          TimeInterval(duration = 3, from = "17:00"),
        )
      }
    }

    "should return 30 mins slot when both enableThirtyMinsSlots feature flag and WL feature is ON" in {
      val context = aValidYplContext
        .withRequest(aValidYplRequest.copy(
          regulationFeatureEnabledSetting =
            YplRegulationFeatureEnabledSetting.default.copy(isEnable30MinsHourlySlots = true),
          featureRequest = YplFeatureRequest(enableThirtyMinsSlots = true),
        ))
        .build

      val data = hourlyRatesService.Data(
        Some(hotelEntry),
        context,
        null,
      )

      val result = hourlyRatesService.filter30MinsHourlySlots(data)
      result.get.rooms.size should_== 1
      result.get.rooms.head.hourlyAvailableSlots.size should_== 4
      result.get.rooms.head.hourlyAvailableSlots should_== Seq(
        TimeInterval(duration = 3, from = "16:00"),
        TimeInterval(duration = 3, from = "16:30"),
        TimeInterval(duration = 3, from = "17:00"),
        TimeInterval(duration = 3, from = "17:30"),
      )
    }
  }
}
