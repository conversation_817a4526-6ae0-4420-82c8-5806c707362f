package com.agoda.papi.ypl.logic

import com.agoda.adp.messaging.schema.CompatibilityResult
import com.agoda.commons.models.pricing.ExchangeRate
import com.agoda.finance.tax.services.cache.{TaxFilterCache, TaxFilterCacheAccessor}
import com.agoda.papi.enums.hotel.StayPackageTypes.{Escapes, NormalOffer}
import com.agoda.papi.enums.hotel.{PaymentModel, TaxType}
import com.agoda.papi.enums.room._
import com.agoda.papi.pricing.pricecalculation.api.PriceBreakdownCalculatorInterface
import com.agoda.papi.pricing.pricecalculation.models.tax.{DailyTaxes, Tax, TaxWithValue}
import com.agoda.papi.pricing.pricecalculation.pricing.{
  CommissionCalculatorImpl,
  PriceBreakdownCalculatorImpl,
  TaxBreakdownCalculatorImpl,
}
import com.agoda.papi.ypl.commission.ApmCommissionUtils.{ApmDeltaPercentageConverter, YplRateFenceConverter}
import com.agoda.papi.ypl.commission.apm.`enum`.ApmConfigType
import com.agoda.papi.ypl.commission.apm.models._
import com.agoda.papi.ypl.commission.apm.{ApmCommissionDiscountServiceImpl, ApmPriceAdjustmentServiceImpl}
import com.agoda.papi.ypl.commission.service.CommissionServiceImpl
import com.agoda.papi.ypl.commission.{ApmCommissionHolder, CommissionHolder, MORPCandidateRoomParameters}
import com.agoda.papi.ypl.logging._
import com.agoda.papi.ypl.logic.ApmPricingFlow._
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.models.api.request.YplOccInfo
import com.agoda.papi.ypl.models.builders.ypl.YplContextMock.Implicits.traitToBuilder
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.context.ExchangeRateContext
import com.agoda.papi.ypl.models.enums.ApmNoPriceAdjustmentReason
import com.agoda.papi.ypl.models.hotel.DiscountInfo
import com.agoda.papi.ypl.models.pricing.proto._
import com.agoda.papi.ypl.models.pricing.{ApmPriceAdjustmentDetail, RoomOccupancy, YplPrice}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.pricing.{APMHelper, PriceCalculationImpl, TaxCalculatorImpl}
import com.agoda.papi.ypl.services.TaxPrototypeServiceImpl
import com.agoda.papi.ypl.utils.DayUseUtils
import com.agoda.utils.monitoring.AggregateReporter
import org.joda.time.DateTime
import org.mockito
import org.mockito.Mockito.{times, verify, when}
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope
import org.specs2.specification.core.Fragments

import scala.concurrent.ExecutionContext

// scalastyle:off
class ApmPricingFlowSpec
  extends SpecificationWithJUnit
    with YPLTestContexts
    with PriceCalculationImpl
    with TaxCalculatorImpl
    with ApmCommissionDiscountServiceImpl
    with ApmPriceAdjustmentServiceImpl
    with CommissionServiceImpl
    with TaxPrototypeServiceImpl
    with ApmPricingFlow[YplContext]
    with YPLTestDataBuilders
    with Mockito {

  override val priceBreakdownCalculator: PriceBreakdownCalculatorInterface =
    new PriceBreakdownCalculatorImpl(new TaxBreakdownCalculatorImpl(), new CommissionCalculatorImpl())

  override implicit def ec: ExecutionContext = scala.concurrent.ExecutionContext.Implicits.global

  implicit val compositeChannelContext = aValidCompositeChannelContext

  override def getCommissionForPriceCalculation(commissionHolder: CommissionHolder,
                                                stayDate: DateTime,
                                                occupancy: Int,
                                                isAgodaAgency: Boolean,
                                                applicableMORPCandidateRoomParameters: MORPCandidateRoomParameters,
                                                originalRateType: RateType,
                                                targetRateType: RateType,
                                                excludeWholesaleOrAgx: Boolean): Double = {
    if (excludeWholesaleOrAgx) {
      // This flow shouldn't be called from APM flow
      // Return a distinctive value that would be obvious if used in calculation
      -999999
    }
    super.getCommissionForPriceCalculation(commissionHolder,
                                           stayDate,
                                           occupancy,
                                           isAgodaAgency,
                                           applicableMORPCandidateRoomParameters,
                                           originalRateType,
                                           targetRateType,
                                           excludeWholesaleOrAgx)
  }

  // mock tax filter
  val mockCache: TaxFilterCache = mock[TaxFilterCache]
  when(mockCache.get(any[String])).thenReturn(Some(List.empty))
  TaxFilterCacheAccessor.initialize(mockCache)

  "Apm Pricing Flow" should {

    trait ApmScope extends Scope with ApmTestTool {
      val EXPERIMENT_A: AbUser = 'A'
      val EXPERIMENT_B: AbUser = 'B'

      val checkIn = aValidDateTime
      val aValidStayDate1: StayDate = checkIn
      val aValidStayDate2: StayDate = checkIn.plusDays(1)
      val aValidStayDate3: StayDate = checkIn.plusDays(2)
      val aValidStayDate4: StayDate = checkIn.plusDays(3)

      val aValidCheckIn: StayDate = aValidStayDate1
      val aValidLos: Int = 4

      val aValidApmSellInDate1: Double = 130d
      val aValidApmSellInDate2: Double = 120d
      val aValidApmSellInDate4: Double = 135d

      val aValidRoomTypeId1: Long = 1111
      val aValidRoomTypeId2: Long = 2222
      val aValidRoomTypeId3: Long = 3333
      val aValidRoomTypeId4: Long = 4444

      val aValidRateCategoryId1: Int = 71
      val aValidRateCategoryId2: Int = 72
      val aValidRateCategoryId3: Int = 73

      val aValidYplChannelRTL = YplMasterChannel.RTL
      val aValidYplChannelAPS = YplMasterChannel.APS
      val aValidYplChannelNET = YplMasterChannel.NET
      val aValidYplChannelAPM = YplMasterChannel.APM
      val aValidYplChannelAPM2 = YplMasterChannel(1052)

      val YCSSupplierID: Int = DMC.YCS

      val aValidMarginPercentage = 15d

      val validApprovalPriceIdPool = (1L to 10L)

      val aValidAdditionalHotelCommission: Double = 2.55d

      def aValidRoomOccupancy = RoomOccupancy(adults = 2, children = 0, extraBeds = 0, maxExtraBeds = 0, infants = 0)

      def aValidHotelTaxInfo = HotelTaxInfo(TaxType.SimpleTax, false)

      def aValidTax1: Tax = Tax(id = 1,
                                applyTo = "PRPN",
                                isAmount = false,
                                isFee = false,
                                isTaxable = false,
                                value = 10d,
                                option = ChargeOption.Mandatory)

      def aValidTax2: Tax = Tax(id = 2,
                                applyTo = "PRPN",
                                isAmount = false,
                                isFee = true,
                                isTaxable = true,
                                value = 7d,
                                option = ChargeOption.Mandatory)

      def aValidTaxMap: Map[TaxIDWithProTypeID, Tax] = Map(
        (aValidTax1.id, 0) -> aValidTax1,
        (aValidTax2.id, 0) -> aValidTax2,
      )

      val aValidTaxInfo: TaxInfo = TaxInfo(aValidHotelTaxInfo, aValidTaxMap)
      val aValidDailyTax =
        DailyTaxes(List(TaxWithValue(aValidTax1, aValidTax1.value), TaxWithValue(aValidTax2, aValidTax2.value)),
                   isCleanedUpHospitalityTax = true)

      def buildValidYPLPrice(chargeType: ChargeType,
                             date: StayDate,
                             netEx: Double,
                             multiplier: Double = 1d,
                             apmPriceAdjustmentDetail: Option[ApmPriceAdjustmentDetail] = None,
                             subChargeType: SubChargeType = SubChargeType.None): YplPrice = {
        val netExclusive = netEx * multiplier
        val margin = netExclusive * 0.15d
        val fee = netExclusive * 0.07d
        val tax = (netExclusive * 0.1d) + (fee * 0.1d)
        val pf = margin * 0.1d

        aValidPrice.copy(
          chargeType = chargeType,
          date = date,
          netExclusive = netExclusive,
          margin = margin,
          refMargin = margin,
          tax = tax,
          fee = fee,
          processingFee = pf,
          refProcessingFee = pf,
          quantity = 1,
          applyType = ApplyType.PB,
          chargeOption = ChargeOption.Mandatory,
          dailyTaxes = aValidDailyTax,
          apmPriceAdjustmentDetail = apmPriceAdjustmentDetail,
          subChargeType = subChargeType,
        )
      }

      val aValidPrices = Seq(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate1, 50d),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate2, 50d),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate3, 50d),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate4, 50d),
      )

      val aValidPricesWithSubChargeType = Seq(
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d, 1, None, SubChargeType.None),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d, 1, None, SubChargeType.Adult),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 50d, 1, None, SubChargeType.Baby),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 50d, 1, None, SubChargeType.Toddler),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 50d, 1, None, SubChargeType.Child),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 50d, 1, None, SubChargeType.PreSchool),
        buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 50d, 1, None, SubChargeType.GradeSchool),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate1, 100d, 1, None, SubChargeType.None),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate2, 100d, 1, None, SubChargeType.Adult),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate3, 100d, 1, None, SubChargeType.None),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate4, 100d, 1, None, SubChargeType.Adult),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate1, 50d, 1, None, SubChargeType.Baby),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate2, 50d, 1, None, SubChargeType.Toddler),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate3, 50d, 1, None, SubChargeType.Child),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate4, 50d, 1, None, SubChargeType.PreSchool),
        buildValidYPLPrice(ChargeType.ExtraBed, aValidStayDate4, 50d, 1, None, SubChargeType.GradeSchool),
      )

      def aValidApmSellInPriceDailyMap: Map[StayDate, AutoPriceMatchPriceInfo] = Map(
        aValidStayDate1 -> AutoPriceMatchPriceInfo(aValidApmSellInDate1, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
        aValidStayDate2 -> AutoPriceMatchPriceInfo(aValidApmSellInDate2, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
        aValidStayDate4 -> AutoPriceMatchPriceInfo(aValidApmSellInDate4, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
      )

      val aValidReqOcc = YplReqOccByHotelAgePolicy(YplOccInfo(_adults = Option(2), _rooms = Option(1)), aValidAgePolicy)

      def aValidYplDispatchChannels = YplDispatchChannels(
        Set(
          YplMasterChannel.RTL,
          YplMasterChannel.APS,
          YplMasterChannel.NET,
          YplMasterChannel.APM,
          YplMasterChannel(mockMultipleAutoPriceMatch.adjustmentChannelId),
          YplMasterChannel(mockMultipleAutoPriceMatch.commissionDiscountChannelId.get),
        ),
        Set.empty,
      )

      def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map.empty

      def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] = Seq(
        MultipleAutoPriceMatchHolder(1,
                                     Some(1),
                                     0d,
                                     1,
                                     0d,
                                     ApmHotelStatus.Active,
                                     aValidStayDate1,
                                     None,
                                     Seq.empty,
                                     Seq.empty,
                                     Some(1)))

      def aValidApmLeadingRoomAdjustmentIds: Seq[Int] = Seq.empty

      def aHotelEntryModel = aValidHotelEntryModel
        .withHotelId(aValidHotelId)
        .withSupplierId(YCSSupplierID)
        .withDispatchChannels(aValidYplDispatchChannels)
        .withTaxInfo(aValidTaxInfo)
        .withMetaData(aValidHotelInfo.copy(multipleAutoPriceMatch = aValidMultipleAutoPriceMatch,
                                           apmLeadingRoomAdjustmentIds = aValidApmLeadingRoomAdjustmentIds,
                                           countryCurrency = Some("THB")))
        .withAutoPriceMatchInfo(aValidAutoPriceMatchInfo)
        .withReqOccByHotelAgePolicy(aValidReqOcc)

      def aHotelEntryModelWithYcsSupplierID = aHotelEntryModel.withSupplierId(DMC.YCS)

      val originalRooms: List[YPLRoom] = List(
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Agency)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 90d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 45d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId2)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 105d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 54d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 90d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 45d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 45d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId2)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 120d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 120d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 120d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 120d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 60d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 60d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 60d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 60d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId2)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 105d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 105d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 54d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 54d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId3)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId3)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 40d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 10d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId4)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId3)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 40d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 40d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 10d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 10d),
          )),
      )

      val roomForMSEIssue: List[YPLRoom] = List(
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelNET)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 90d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 90d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelNET)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 65d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 65d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 65d),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 65d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d),
          )),
      )

      val originalRoomsWithSubCharge: List[YPLRoom] = List(
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d, 1, None, SubChargeType.Baby),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d, 1, None, SubChargeType.Toddler),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d, 1, None, SubChargeType.PreSchool),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d, 1, None, SubChargeType.Baby),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Agency)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d, 1, None, SubChargeType.Baby),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d, 1, None, SubChargeType.Toddler),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d, 1, None, SubChargeType.PreSchool),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d, 1, None, SubChargeType.Baby),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 90d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 90d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 90d, 1, None, SubChargeType.Baby),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 90d, 1, None, SubChargeType.Toddler),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 45d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 45d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 45d, 1, None, SubChargeType.PreSchool),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 45d, 1, None, SubChargeType.Baby),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId1)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId2)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 105d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 105d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 105d, 1, None, SubChargeType.Baby),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 105d, 1, None, SubChargeType.Toddler),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 54d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 54d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 54d, 1, None, SubChargeType.PreSchool),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 54d, 1, None, SubChargeType.Baby),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 100d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 100d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 100d, 1, None, SubChargeType.Baby),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 100d, 1, None, SubChargeType.Toddler),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 50d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 50d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 50d, 1, None, SubChargeType.PreSchool),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 50d, 1, None, SubChargeType.Baby),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId1)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 90d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 90d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 90d, 1, None, SubChargeType.Baby),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 90d, 1, None, SubChargeType.Toddler),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 45d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 45d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 45d, 1, None, SubChargeType.PreSchool),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 45d, 1, None, SubChargeType.Baby),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId2)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 120d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 120d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 120d, 1, None, SubChargeType.Baby),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 120d, 1, None, SubChargeType.Toddler),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 60d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 60d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 60d, 1, None, SubChargeType.PreSchool),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 60d, 1, None, SubChargeType.Baby),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId2)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId2)
          .withChannel(aValidYplChannelAPS)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 105d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 105d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 105d, 1, None, SubChargeType.Baby),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 105d, 1, None, SubChargeType.Toddler),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 54d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 54d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 54d, 1, None, SubChargeType.PreSchool),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 54d, 1, None, SubChargeType.Baby),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId3)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId3)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 40d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 40d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 40d, 1, None, SubChargeType.Baby),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 40d, 1, None, SubChargeType.Toddler),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 10d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 10d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 10d, 1, None, SubChargeType.PreSchool),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 10d, 1, None, SubChargeType.Baby),
          )),
        aValidRoom
          .withRoomTypeId(aValidRoomTypeId4)
          .withSupplierId(YCSSupplierID)
          .withOccupancy(aValidRoomOccupancy)
          .withLengthOfStay(aValidLos)
          .withRateCategoryId(aValidRateCategoryId3)
          .withChannel(aValidYplChannelRTL)
          .withPaymentModel(PaymentModel.Merchant)
          .withMarginPercentage(aValidMarginPercentage)
          .withRateType(RateType.NetExclusive)
          .withPrices(List(
            buildValidYPLPrice(ChargeType.Room, aValidStayDate1, 40d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate2, 40d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate3, 40d, 1, None, SubChargeType.Baby),
            buildValidYPLPrice(ChargeType.Room, aValidStayDate4, 40d, 1, None, SubChargeType.Toddler),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate1, 10d, 1, None, SubChargeType.Adult),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate2, 10d, 1, None, SubChargeType.None),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate3, 10d, 1, None, SubChargeType.PreSchool),
            buildValidYPLPrice(ChargeType.Surcharge, aValidStayDate4, 10d, 1, None, SubChargeType.Baby),
          )),
      )

      val originalRoomsWithAPMRooms = originalRooms :+ aValidRoomWithAPMChannel

      val yplRoomsWithEntry: YplRoomsWithEntry = YplRoomsWithEntry(rooms = originalRooms, entry = aHotelEntryModel)
      val yplRoomsNoEligibleRcWithEntry: YplRoomsWithEntry =
        YplRoomsWithEntry(rooms = originalRooms.map(_.copy(channel = YplChannel(Channel.Package))),
                          entry = aHotelEntryModel)
      val yplRoomsWithAPMRoomEntry: YplRoomsWithEntry =
        YplRoomsWithEntry(rooms = originalRoomsWithAPMRooms, entry = aHotelEntryModelWithYcsSupplierID)

      def mockMetaData: HotelMeta = yplRoomsWithEntry.entry.metaData

      def mockDispatchChannel: YplDispatchChannels = yplRoomsWithEntry.entry.dispatchChannels

      def mockInputYplHotel(yplExperiments: YplExperiments): YPLHotel =
        mockInputYplHotelWithContext(mockYplContext(yplExperiments), Set(aValidRateFence))

      def mockInputYplHotelWithNonEligibleRcRooms(yplExperiments: YplExperiments): YPLHotel =
        mockInputYplHotelWithContextWithNonEligibleRcRooms(mockYplContext(yplExperiments), Set(aValidRateFence))

      def mockInputYplHotelWithAPMRooms(yplExperiments: YplExperiments): YPLHotel =
        mockInputYplHotelWithAPMRoomsAndContext(mockYplContext(yplExperiments), Set(aValidRateFence))

      def mockInputYplHotelWithContext(context: YplContext, fences: Set[YplRateFence]): YPLHotel =
        PricingCommon.convertToHotel(yplRoomsWithEntry.entry,
                                     context.request,
                                     yplRoomsWithEntry.rooms.map(r => r.copy(fences = fences)))

      def mockInputYplHotelWithContextSplitFence(
        context: YplContext,
        fences: Set[YplRateFence],
        yplRoomsWithEntry: YplRoomsWithEntry = yplRoomsWithEntry): YPLHotel = PricingCommon.convertToHotel(
        yplRoomsWithEntry.entry,
        context.request,
        yplRoomsWithEntry.rooms.map(r => r.copy(fences = fences.filter(_.cid == r.channel.baseChannelId))))

      def mockInputYplHotelWithContextWithNonEligibleRcRooms(context: YplContext, fences: Set[YplRateFence]): YPLHotel =
        PricingCommon.convertToHotel(yplRoomsNoEligibleRcWithEntry.entry,
                                     context.request,
                                     yplRoomsNoEligibleRcWithEntry.rooms.map(r => r.copy(fences = fences)))

      def mockInputYplHotelWithAPMRoomsAndContext(context: YplContext, fences: Set[YplRateFence]): YPLHotel =
        PricingCommon.convertToHotel(yplRoomsWithAPMRoomEntry.entry,
                                     context.request,
                                     yplRoomsWithAPMRoomEntry.rooms.map(r => r.copy(fences = fences)))

      def mockInputYplHotelWithMultiRoomAssignment(context: YplContext, isMultiRoomAssignment: Boolean): YPLHotel =
        PricingCommon.convertToHotel(
          yplRoomsWithEntry.entry,
          context.request,
          yplRoomsWithEntry.rooms.map(r =>
            r.copy(roomFeatures = RoomFeatures(isMultipleRoomAssignmentPrice = isMultiRoomAssignment))),
        )

      def mockYplContext(yplExperiments: YplExperiments, channels: Channels = Set.empty): YplContext = {
        val moreChannels = channels.map(channel => channel -> Set(aValidRateFence)).toMap
        YplContext(
          aValidYplRequest
            .withFences(aValidYplRequestFences + (YplMasterChannel.APM -> Set(aValidRateFence)) ++ moreChannels)
            .build
            .copy(experiments = yplExperiments))
      }

      def assignYplRoomEntryWithApmCommissionHolder(hotel: YPLHotel, metaData: HotelMeta, yplContext: YplContext) =
        hotel.copy(rooms = hotel.rooms.map { room =>
          room.copy(
            yplRoomEntry = aValidRoomEntry.build.copy(commissionHolder =
              CommissionHolder.default.copy(apmCommissionHolder = ApmCommissionHolder(
                metaData.multipleAutoPriceMatch,
                metaData.apmCommissionReductionEligibility,
                metaData.apmConfigs,
                yplContext.request.apmSetting,
                hotel.autoPriceMatchInfo,
                metaData.apmDeltaPercentage.mapValues(_.toCommissionModel),
              ))),
            discountInfo = DiscountInfo(Some(aValidPromotion), Map(aValidDateTime.toLocalDate -> List(aValidPromotion))),
          )
        })

      def mockMultipleAutoPriceMatch = MultipleAutoPriceMatchHolder(
        programId = 1,
        commissionDiscountChannelId = Some(9000),
        adjustmentChannelId = 1052,
        adjustmentDiscountPercent = 5d,
        commissionDiscountPercent = 0d,
        statusId = ApmHotelStatus.Active,
        startDate = DateTime.parse("1900-01-01"),
        endDate = None,
        apmAdjustmentDiscount = Seq.empty,
        apmCommissionDiscount = Seq.empty,
        programType = Some(1),
      )

      def mockMultipleAutoPriceMatchWithAPMAdjustmentCh = mockMultipleAutoPriceMatch
        .copy(adjustmentChannelId = YplMasterChannel.APM.baseChannelId, adjustmentDiscountPercent = 0d)

      val apmDeltaPercentHotelLevelOnly =
        ApmDeltaPercentage(hotelLevel = Some(10), programLevel = None, globalLevel = None)
      val apmDeltaPercentHotelProgramLevelOnly =
        ApmDeltaPercentage(hotelLevel = Some(10), programLevel = Some(20), globalLevel = None)
      val apmDeltaPercentHotelGlobalLevelOnly =
        ApmDeltaPercentage(hotelLevel = Some(10), programLevel = None, globalLevel = Some(20))
      val apmDeltaPercentProgramLevelOnly =
        ApmDeltaPercentage(hotelLevel = None, programLevel = Some(20), globalLevel = None)
      val apmDeltaPercentProgramGlobalLevelOnly =
        ApmDeltaPercentage(hotelLevel = None, programLevel = Some(20), globalLevel = Some(30))
      val apmDeltaPercentGlobalLevelOnly =
        ApmDeltaPercentage(hotelLevel = None, programLevel = None, globalLevel = Some(30))
      val apmDeltaPercentHotelProgramGlobalLevelOnly =
        ApmDeltaPercentage(hotelLevel = Some(10), programLevel = Some(20), globalLevel = Some(30))
      val apmDeltaPercentHotelEmpty = ApmDeltaPercentage(hotelLevel = None, programLevel = None, globalLevel = None)

      def mockApmDeltaPercentProgramMap(programId: ApmProgramId,
                                        apmDeltaPercent: ApmDeltaPercentage): Map[ApmProgramId, ApmDeltaPercentage] =
        Map(programId -> apmDeltaPercent)

      val allApmConfigs: Map[ApmConfigTypeId, ApmConfigHolder] = Map(
        ApmConfigType.AdjustmentCap.value -> ApmConfigHolder(
          globalLevel = Seq("30"),
          programLevel = Map.empty,
          hotelLevel = Seq.empty,
        ),
        ApmConfigType.BlackoutDays.value -> ApmConfigHolder(
          globalLevel = Seq("2023-01-22", "2023-01-23", "2023-01-24", "2023-01-25"),
          programLevel = Map(
            1 -> Seq("2023-02-01", "2023-02-02", "2023-02-03"),
            54 -> Seq("2023-03-01", "2023-03-02"),
          ),
          hotelLevel = Seq("2023-04-22", "2023-05-22", "2023-06-22"),
        ),
        ApmConfigType.CommissionReduction.value -> ApmConfigHolder(
          globalLevel = Seq("1"),
          programLevel = Map(
            1 -> Seq("1"),
            54 -> Seq("0.75"),
          ),
          hotelLevel = Seq(aValidAdditionalHotelCommission.toString),
        ),
        ApmConfigType.EnableAiForBedNetwork.value -> ApmConfigHolder(
          globalLevel = Seq.empty,
          programLevel = Map.empty,
          hotelLevel = Seq("-"),
        ),
      )

      val allApmConfigsWithoutEnableAiForBedNetwork: Map[ApmConfigTypeId, ApmConfigHolder] = Map(
        ApmConfigType.AdjustmentCap.value -> ApmConfigHolder(
          globalLevel = Seq("30"),
          programLevel = Map.empty,
          hotelLevel = Seq.empty,
        ),
        ApmConfigType.BlackoutDays.value -> ApmConfigHolder(
          globalLevel = Seq("2023-01-22", "2023-01-23", "2023-01-24", "2023-01-25"),
          programLevel = Map(
            1 -> Seq("2023-02-01", "2023-02-02", "2023-02-03"),
            54 -> Seq("2023-03-01", "2023-03-02"),
          ),
          hotelLevel = Seq("2023-04-22", "2023-05-22", "2023-06-22"),
        ),
        ApmConfigType.CommissionReduction.value -> ApmConfigHolder(
          globalLevel = Seq("1"),
          programLevel = Map(
            1 -> Seq("1"),
            54 -> Seq("0.75"),
          ),
          hotelLevel = Seq(aValidAdditionalHotelCommission.toString),
        ),
      )
      val blackoutDatePattern = "yyyy-MM-dd"

      def isApplyAdjustmentOnSellEx(programId: Int, apmSetting: Option[ApmSettingHolder]): Boolean = {
        // fetch SellEx adjustment program ids from consul
        val sellExAdjustmentProgramIds = apmSetting.map(_.apmSellExProgramIds).getOrElse(Nil)
        // check if hotel is in sell ex programs
        sellExAdjustmentProgramIds.contains(programId)
      }
    }

    trait ApmTestTool {
      def roundPrice(value: Double, p: Int = 2): Double = Math.round(value * Math.pow(10, p)) / Math.pow(10, p)

      def determinePrice(prices: List[YplPrice],
                         date: StayDate,
                         chargeType: ChargeType,
                         isSellExAdjustment: Boolean): Double = {
        val price = prices.find(p => p.date == date && p.chargeType == chargeType).head
        if (isSellExAdjustment) {
          price.sellExclusive
        }
        price.sellInclusive
      }
    }

    "executeApm return hotel with APM rooms when no multiroom assigment" in new ApmScope {
      // mock tax filter
      val mockCache: TaxFilterCache = mock[TaxFilterCache]
      when(mockCache.get(any[String])).thenReturn(Some(List.empty))
      TaxFilterCacheAccessor.initialize(mockCache)

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(3)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(4)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(5)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(6)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(7)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(8)),
        ),
      )

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatch.copy(adjustmentChannelId = YplMasterChannel.APM.baseChannelId))

      val exp = List.empty
      val mockHotel = mockInputYplHotel(exp)
      val originalRoomSize = mockHotel.rooms.size

      val testHotelWithApmCommissionHolder: YPLHotel =
        assignYplRoomEntryWithApmCommissionHolder(mockHotel, mockMetaData, mockYplContext(exp))
      val result: YPLHotel = executeApm(
        hotel = testHotelWithApmCommissionHolder,
        meta = mockMetaData,
        dispatchChannels = mockDispatchChannel,
      )(ctx = mockYplContext(exp))

      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      apmRoom.size should_=== 3
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms
    }

    "executeApm return hotel without APM rooms when multiroom assigment" in new ApmScope {

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(3)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(4)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(5)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(6)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(7)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(8)),
        ),
      )

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatch.copy(adjustmentChannelId = YplMasterChannel.APM.baseChannelId))

      val exp = List.empty
      val context = mockYplContext(exp)
      val mockHotel = mockInputYplHotelWithMultiRoomAssignment(context, true)
      val originalRoomSize = mockHotel.rooms.size

      val testHotelWithApmCommissionHolder: YPLHotel =
        assignYplRoomEntryWithApmCommissionHolder(mockHotel, mockMetaData, mockYplContext(exp))
      val result: YPLHotel = executeApm(
        hotel = testHotelWithApmCommissionHolder,
        meta = mockMetaData,
        dispatchChannels = mockDispatchChannel,
      )(ctx = context)

      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      apmRoom.size should_=== 0
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms.map(r =>
        r.copy(roomFeatures = RoomFeatures(isMultipleRoomAssignmentPrice = true)))
    }

    "processApmPricing works correctly supplier != YCS" in new ApmScope {

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(3)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(4)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(5)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(6)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(7)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(8)),
        ),
      )

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatch.copy(adjustmentChannelId = YplMasterChannel.APM.baseChannelId))

      override def aHotelEntryModel = super.aHotelEntryModel.withSupplierId(DMC.Accor)

      val exp: List[YplExperiment] = List.empty
      val testHotel = mockInputYplHotel(exp)

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(testHotel, mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      apmRoom.size should_=== 3
      apmRoom.head.discountInfo.promotion should_=== None
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms
    }

    "don't processApmPricing when Dispatched Channel does not contain APM channel id" in new ApmScope {

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(3)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(4)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(5)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(6)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(7)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(8)),
        ),
      )

      override def aValidYplDispatchChannels = YplDispatchChannels(
        Set(
          YplMasterChannel.RTL,
          YplMasterChannel.APS,
          YplMasterChannel(mockMultipleAutoPriceMatch.adjustmentChannelId),
          YplMasterChannel(mockMultipleAutoPriceMatch.commissionDiscountChannelId.get),
        ),
        Set.empty,
      )

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] = Seq(mockMultipleAutoPriceMatch)

      val exp: List[YplExperiment] = List.empty

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      apmRoom.size should_=== 0
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms
    }

    "don't processApmPricing when hotelIsSuspended" in new ApmScope {

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] = Seq(mockMultipleAutoPriceMatch)

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(3)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(140d, aValidRoomTypeId1, validApprovalPriceIdPool(4)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110d, aValidRoomTypeId1, validApprovalPriceIdPool(5)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(6)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(7)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(65d, aValidRoomTypeId3, validApprovalPriceIdPool(8)),
        ),
      )

      val exp: List[YplExperiment] = List.empty

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      apmRoom.size should_=== 0
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms
    }

    "don't processApmPricing when hotelIsInActive" in new ApmScope {

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] = Seq(mockMultipleAutoPriceMatch)

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(3)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(140d, aValidRoomTypeId1, validApprovalPriceIdPool(4)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110d, aValidRoomTypeId1, validApprovalPriceIdPool(5)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(6)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(7)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(65d, aValidRoomTypeId3, validApprovalPriceIdPool(8)),
        ),
      )

      val exp: List[YplExperiment] = List.empty

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      apmRoom.size should_=== 0
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms
    }

    "don't processApmPricing when children > 0 " in new ApmScope {

      override def aValidRoomOccupancy =
        RoomOccupancy(adults = 2, children = 1, extraBeds = 0, maxExtraBeds = 0, infants = 0)

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(3)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(4)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(6)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId1, validApprovalPriceIdPool(5)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId1, validApprovalPriceIdPool(7)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId1, validApprovalPriceIdPool(8)),
        ),
      )

      val exp: List[YplExperiment] = List.empty
      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))
      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)

      apmRoom.size should_=== 0
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms
    }

    "don't processApmPricing when infant > 0 " in new ApmScope {

      override def aValidRoomOccupancy =
        RoomOccupancy(adults = 2, children = 0, extraBeds = 0, maxExtraBeds = 0, infants = 1)

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(3)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(4)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(6)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId1, validApprovalPriceIdPool(5)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId1, validApprovalPriceIdPool(7)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId1, validApprovalPriceIdPool(8)),
        ),
      )

      val exp: List[YplExperiment] = List.empty

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)

      apmRoom.size should_=== 0
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms
    }

    "don't processApmPricing when extrabed > 0 " in new ApmScope {

      override def aValidRoomOccupancy =
        RoomOccupancy(adults = 2, children = 0, extraBeds = 1, maxExtraBeds = 1, infants = 0)

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(3)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(4)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(6)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId1, validApprovalPriceIdPool(5)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId1, validApprovalPriceIdPool(7)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId1, validApprovalPriceIdPool(8)),
        ),
      )

      val exp: List[YplExperiment] = List.empty
      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))
      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)

      apmRoom.size should_=== 0
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms
    }

    "processApmPricing correctly when priceDiscountPercent != 0" in new ApmScope {

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(6)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(140d, aValidRoomTypeId1, validApprovalPriceIdPool(7)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110d, aValidRoomTypeId1, validApprovalPriceIdPool(8)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(3)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(4)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(65d, aValidRoomTypeId3, validApprovalPriceIdPool(5)),
        ),
      )

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] = Seq(
        mockMultipleAutoPriceMatch.copy(
          adjustmentChannelId = YplMasterChannel.APM.baseChannelId,
          adjustmentDiscountPercent = 20d,
        ))

      val exp: List[YplExperiment] = List.empty
      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))
      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      val isSellExAdjustment =
        isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId, mockYplContext(exp).request.apmSetting)

      apmRoom.size should_=== 4
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        84.55d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        84.55d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)

      val apmRoom2 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Agency && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        93.94d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        93.94d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)

      val apmRoom3 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId2 && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        98.64d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        98.64d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)

      val apmRoom4 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId3 && r.rateCategoryId == aValidRateCategoryId3 && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(48d)
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(48d)
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(48d)
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(52d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(13.42d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(13.42d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(13.42d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(13.42d)
    }

    "processApmPricing correctly when priceDiscountPercent = 0" in new ApmScope {

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(6)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(7)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(8)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(3)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(4)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(5)),
        ),
      )

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh.copy(adjustmentDiscountPercent = 0d))

      val exp: List[YplExperiment] = List.empty
      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))
      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      val isSellExAdjustment =
        isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId, mockYplContext(exp).request.apmSetting)

      apmRoom.size should_=== 3
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        90.0d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        90.0d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)

      val apmRoom2 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Agency && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        103.42d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        103.42d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)

      val apmRoom3 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId2 & r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.13d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.13d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
    }

    "processApmPricing correctly when priceDiscountPercent = 0 and have multidiscount" in new ApmScope {
      val adjustmentChannel = YplMasterChannel(1052)

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1, Some(1052)) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0), Some(1052)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(6), Some(1052)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2, Some(1052)) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1), Some(1052)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(7), Some(1052)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(8), Some(1052)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2, Some(1052)) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(2), Some(1052)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(60d,
                                                               aValidRoomTypeId3,
                                                               validApprovalPriceIdPool(3),
                                                               Some(1052)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(60d,
                                                               aValidRoomTypeId3,
                                                               validApprovalPriceIdPool(4),
                                                               Some(1052)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(60d,
                                                               aValidRoomTypeId3,
                                                               validApprovalPriceIdPool(5),
                                                               Some(1052)),
        ),
      )

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] = Seq(
        mockMultipleAutoPriceMatchWithAPMAdjustmentCh
          .copy(adjustmentDiscountPercent = 0d)
          .copy(apmAdjustmentDiscount = Seq(
            ApmAdjustmentDiscount(
              programId = 1,
              adjustmentChannelId = 1052,
              adjustmentDiscountPercent = Some(10d),
              startDate = DateTime.parse("1900-01-01"),
              endDate = DateTime.parse("2999-01-01"),
            ))))

      val exp: List[YplExperiment] = List(YplExperiment(YplExperiments.APM_MULTIPLE_DISCOUNT, EXPERIMENT_B))
      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp),
                                                  mockMetaData,
                                                  mockYplContext(exp, Set(adjustmentChannel)))
      val (result, apmRoomsStartAtIndex) = processApmPriceAdjustment(
        testHotelWithApmCommissionHolder,
        mockMetaData,
        dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp, Set(adjustmentChannel)))
      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == aValidYplChannelAPM2)
      val isSellExAdjustment = isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId,
                                                         mockYplContext(exp, Set(adjustmentChannel)).request.apmSetting)

      apmRoom.size should_=== 3
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM2)
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        84.55d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        84.55d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)

      val apmRoom2 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Agency && r.channel == aValidYplChannelAPM2)
        .head
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        94.42d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        94.42d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)

      val apmRoom3 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId2 & r.channel == aValidYplChannelAPM2)
        .head
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        101.13d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        101.13d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
    }

    "processApmPricing filters multi attached channel rooms in autoPriceMatchInfo correctly" in new ApmScope {

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = {
        val aDifferentChannel = mockMultipleAutoPriceMatch.adjustmentChannelId
        Map(
          AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1, Some(Channel.APM)) -> Map(
            aValidStayDate1 -> AutoPriceMatchPriceInfo(90d,
                                                       aValidRoomTypeId1,
                                                       validApprovalPriceIdPool(0),
                                                       Some(Channel.APM)),
            aValidStayDate4 -> AutoPriceMatchPriceInfo(90d,
                                                       aValidRoomTypeId1,
                                                       validApprovalPriceIdPool(6),
                                                       Some(Channel.APM)),
          ),
          AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2, Some(Channel.APM)) -> Map(
            aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d,
                                                       aValidRoomTypeId1,
                                                       validApprovalPriceIdPool(1),
                                                       Some(Channel.APM)),
            aValidStayDate2 -> AutoPriceMatchPriceInfo(139d,
                                                       aValidRoomTypeId1,
                                                       validApprovalPriceIdPool(7),
                                                       Some(Channel.APM)),
            aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d,
                                                       aValidRoomTypeId1,
                                                       validApprovalPriceIdPool(8),
                                                       Some(Channel.APM)),
          ),
          AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2, Some(aDifferentChannel)) -> Map(
            aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d,
                                                       aValidRoomTypeId1,
                                                       validApprovalPriceIdPool(1),
                                                       Some(aDifferentChannel)),
            aValidStayDate2 -> AutoPriceMatchPriceInfo(139d,
                                                       aValidRoomTypeId1,
                                                       validApprovalPriceIdPool(7),
                                                       Some(aDifferentChannel)),
            aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d,
                                                       aValidRoomTypeId1,
                                                       validApprovalPriceIdPool(8),
                                                       Some(aDifferentChannel)),
          ),
        )
      }

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh)

      val mockChannelIds = List(Channel.APM, mockMultipleAutoPriceMatch.adjustmentChannelId)
      val exp = List.empty
      val ctx = mockYplContext(exp, Set(YplMasterChannel(mockMultipleAutoPriceMatch.adjustmentChannelId)))
      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, ctx)
      val (result, apmRoomsStartAtIndex) = processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                                                     mockMetaData,
                                                                     dispatchChannels = aValidYplDispatchChannels)(ctx)
      val (apmRoom, nonApmRoom) =
        result.rooms.partition(r => mockChannelIds.map(YplMasterChannel.apply).contains(r.channel))
      val isSellExAdjustment =
        isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId, ctx.request.apmSetting)

      apmRoom.size should_=== 6
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      apmRoom.map(r => aValidYplDispatchChannels.masterChannels.contains(r.channel)).forall(_ == true) should_=== true
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        90.0d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        90.0d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)

      val apmRoom2 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Agency && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        103.42d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        103.42d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)

      val apmRoom3 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId2 & r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.13d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.13d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)

      val apmRoom4 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == YplMasterChannel(
            mockMultipleAutoPriceMatch.adjustmentChannelId))
        .head
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        100.78d)
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.78d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)

      val apmRoom5 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Agency && r.channel == YplMasterChannel(
            mockMultipleAutoPriceMatch.adjustmentChannelId))
        .head
      roundPrice(
        determinePrice(apmRoom5.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        114.2d)
      roundPrice(
        determinePrice(apmRoom5.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom5.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom5.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        124.2d)
      roundPrice(
        determinePrice(apmRoom5.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom5.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom5.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom5.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)

      val apmRoom6 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId2 & r.channel == YplMasterChannel(
            mockMultipleAutoPriceMatch.adjustmentChannelId))
        .head
      roundPrice(
        determinePrice(apmRoom6.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.91d)
      roundPrice(
        determinePrice(apmRoom6.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom6.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom6.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        130.91d)
      roundPrice(
        determinePrice(apmRoom6.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom6.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom6.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom6.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
    }

    "processApmPricing processes both empty and attached channel rooms in autoPriceMatchInfo correctly" in new ApmScope {

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2, Some(mockMultipleAutoPriceMatch.adjustmentChannelId)) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d,
                                                     aValidRoomTypeId1,
                                                     validApprovalPriceIdPool(1),
                                                     Some(mockMultipleAutoPriceMatch.adjustmentChannelId)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d,
                                                     aValidRoomTypeId1,
                                                     validApprovalPriceIdPool(7),
                                                     Some(mockMultipleAutoPriceMatch.adjustmentChannelId)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d,
                                                     aValidRoomTypeId1,
                                                     validApprovalPriceIdPool(8),
                                                     Some(mockMultipleAutoPriceMatch.adjustmentChannelId)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2, None) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1), None),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(7), None),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(8), None),
        ),
      )

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh)

      val mockChannelIds = List(Channel.APM, mockMultipleAutoPriceMatch.adjustmentChannelId)
      val exp = List.empty
      val ctx = mockYplContext(exp, Set(YplMasterChannel(mockMultipleAutoPriceMatch.adjustmentChannelId)))
      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, ctx)
      val (result, apmRoomsStartAtIndex) = processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                                                     mockMetaData,
                                                                     dispatchChannels = aValidYplDispatchChannels)(ctx)
      val (apmRoom, nonApmRoom) =
        result.rooms.partition(r => mockChannelIds.map(YplMasterChannel.apply).contains(r.channel))
      val isSellExAdjustment =
        isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId, ctx.request.apmSetting)

      apmRoom.size should_=== 6
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      apmRoom.map(r => aValidYplDispatchChannels.masterChannels.contains(r.channel)).forall(_ == true) should_=== true
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == YplMasterChannel(
            mockMultipleAutoPriceMatch.adjustmentChannelId))
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        100.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.78d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)

      val apmRoom2 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Agency && r.channel == YplMasterChannel(
            mockMultipleAutoPriceMatch.adjustmentChannelId))
        .head
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        114.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        124.2d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)

      val apmRoom3 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId2 & r.channel == YplMasterChannel(
            mockMultipleAutoPriceMatch.adjustmentChannelId))
        .head
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        130.91d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)

      val apmRoom4 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        100.78d)
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom4.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.78d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom4.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)

      val apmRoom5 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Agency && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom5.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        114.2d)
      roundPrice(
        determinePrice(apmRoom5.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom5.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom5.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        124.2d)
      roundPrice(
        determinePrice(apmRoom5.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom5.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom5.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom5.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)

      val apmRoom6 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId2 & r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom6.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.91d)
      roundPrice(
        determinePrice(apmRoom6.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom6.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom6.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        130.91d)
      roundPrice(
        determinePrice(apmRoom6.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom6.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom6.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom6.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
    }

    "processApmPricing processes both empty with invalid program channel and attached channel rooms in autoPriceMatchInfo correctly" in new ApmScope {

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2, Some(1053)) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1), Some(1053)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(7), Some(1053)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(8), Some(1053)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2, None) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1), None),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(7), None),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(8), None),
        ),
      )

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh)

      val mockChannelIds = List(Channel.APM)
      val exp = List.empty
      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))
      val (apmRoom, nonApmRoom) =
        result.rooms.partition(r => mockChannelIds.map(YplMasterChannel.apply).contains(r.channel))
      val isSellExAdjustment =
        isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId, mockYplContext(exp).request.apmSetting)

      apmRoom.size should_=== 3
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      apmRoom.map(r => aValidYplDispatchChannels.masterChannels.contains(r.channel)).forall(_ == true) should_=== true
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        100.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.78d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)

      val apmRoom2 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Agency && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        114.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        124.2d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)

      val apmRoom3 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId2 & r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        130.91d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
    }

    "processApmPricing fallbacks nonexistent channel map in autoPriceMatchInfo correctly" in new ApmScope {

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(6)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d, aValidRoomTypeId1, validApprovalPriceIdPool(7)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d, aValidRoomTypeId1, validApprovalPriceIdPool(8)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(3)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(4)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(5)),
        ),
      )

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh.copy(adjustmentDiscountPercent = 0d))

      val exp = List.empty
      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))
      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      val isSellExAdjustment =
        isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId, mockYplContext(exp).request.apmSetting)

      apmRoom.size should_=== 3
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      apmRoom.map(r => aValidYplDispatchChannels.masterChannels.contains(r.channel)).forall(_ == true) should_=== true
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        90.0d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        90.0d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)

      val apmRoom2 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Agency && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        103.42d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        103.42d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)

      val apmRoom3 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId2 & r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.13d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.13d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
    }

    "processApmPricing correctly when with room type id 1111 from apm data" in new ApmScope {

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh.copy(adjustmentDiscountPercent = 0d))

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId3, validApprovalPriceIdPool(0)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId2, validApprovalPriceIdPool(1)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
        ))

      val exp = List.empty

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))
      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      val isSellExAdjustment =
        isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId, mockYplContext(exp).request.apmSetting)

      apmRoom.size should_=== 3
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        84.55d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
    }

    "processApmPricing correctly with room type id 1111 from apm data" in new ApmScope {

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId4, validApprovalPriceIdPool(0)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId2, validApprovalPriceIdPool(1)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
        ))

      val exp = List.empty

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)

      apmRoom.size should_=== 0
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms
    }

    "processApmPricing correctly with room type id 3333 from apm data" in new ApmScope {

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh.copy(adjustmentDiscountPercent = 0d))

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId4, validApprovalPriceIdPool(0)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId3, validApprovalPriceIdPool(1)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
        ),
      )

      override def aValidApmLeadingRoomAdjustmentIds: Seq[Int] = Seq(aValidRoomTypeId3.toInt)

      val exp = List.empty

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      val isSellExAdjustment =
        isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId, mockYplContext(exp).request.apmSetting)

      apmRoom.size should_=== 3
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        84.55d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
    }

    "processApmPricing correctly when priceDiscountPercent = 0" in new ApmScope {

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh.copy(adjustmentDiscountPercent = 0d))

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
        ))

      private val fences = Set(YplRateFence("TH", -1, 1))
      val context = YplContext(aValidYplRequest.withFences(Map(YplMasterChannel.APM -> fences)))

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(List.empty), mockMetaData, context)
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(context)
      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      val originalRoomsWithFences = testHotelWithApmCommissionHolder.rooms.map(r => r.copy(fences = fences))

      val isSellExAdjustment =
        isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId, context.request.apmSetting)

      apmRoom.size should_=== 3
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== originalRoomsWithFences

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        84.55d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        84.55d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        84.55d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
    }

    "processApmPricing correctly when room maxExtrabed > 0" in new ApmScope {

      override def aValidRoomOccupancy = RoomOccupancy(adults = 2, children = 0, extraBeds = 0, maxExtraBeds = 1)

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh.copy(adjustmentDiscountPercent = 0d))

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(90d,
                                                     aValidRoomTypeId1,
                                                     apmApprovalPriceId = validApprovalPriceIdPool(6)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(100.78d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(139d,
                                                     aValidRoomTypeId1,
                                                     apmApprovalPriceId = validApprovalPriceIdPool(7)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(110.78d,
                                                     aValidRoomTypeId1,
                                                     apmApprovalPriceId = validApprovalPriceIdPool(8)),
        ),
        AutoPriceMatchKeyEntry(aValidRoomTypeId3, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(222d, aValidRoomTypeId3, validApprovalPriceIdPool(2)),
          aValidCheckIn.plusDays(1) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(3)),
          aValidCheckIn.plusDays(2) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(4)),
          aValidCheckIn.plusDays(3) -> AutoPriceMatchPriceInfo(60d, aValidRoomTypeId3, validApprovalPriceIdPool(5)),
        ),
      )

      val exp: List[YplExperiment] = List.empty
      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))
      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)
      val isSellExAdjustment =
        isApplyAdjustmentOnSellEx(aValidMultipleAutoPriceMatch.head.programId, mockYplContext(exp).request.apmSetting)

      apmRoom.size should_=== 3
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      val apmRoom1 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Merchant && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        90.0d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        120.78d)
      roundPrice(
        determinePrice(apmRoom1.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        90.0d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)
      roundPrice(
        determinePrice(apmRoom1.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(60.39d)

      val apmRoom2 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId1 && r.paymentModel == PaymentModel.Agency && r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        103.42d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        134.2d)
      roundPrice(
        determinePrice(apmRoom2.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        103.42d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)
      roundPrice(
        determinePrice(apmRoom2.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(67.1d)

      val apmRoom3 = apmRoom
        .find(r =>
          r.roomTypeId == aValidRoomTypeId1 && r.rateCategoryId == aValidRateCategoryId2 & r.channel == aValidYplChannelAPM)
        .head
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate1, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.13d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate2, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate3, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        140.91d)
      roundPrice(
        determinePrice(apmRoom3.prices, aValidStayDate4, ChargeType.Room, isSellExAdjustment)) should_=== roundPrice(
        110.13d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate1,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate2,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate3,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
      roundPrice(
        determinePrice(apmRoom3.prices,
                       aValidStayDate4,
                       ChargeType.Surcharge,
                       isSellExAdjustment)) should_=== roundPrice(72.47d)
    }

    "processApmPricing correctly with fences, MSE group by fence 2" in new ApmScope {
      val exp = List.empty

      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh.copy(adjustmentDiscountPercent = 0d))

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(95d, aValidRoomTypeId1, 1)))

      private val inFence = YplRateFence("IN", 1, 1)
      private val vnFence = YplRateFence("VN", 2, 1)
      private val jpFence = YplRateFence("JP", 3, 1)

      private val fences = Set(inFence, vnFence, jpFence)
      val context = YplContext(
        aValidYplRequest
          .withFences(
            Map(YplMasterChannel.APM -> fences,
                YplMasterChannel.RTL -> fences,
                YplMasterChannel.APS -> fences,
                YplMasterChannel.NET -> fences))
          .build
          .copy(experiments = exp))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(
        mockInputYplHotelWithContextSplitFence(context,
                                               fences,
                                               yplRoomsWithEntry.copy(rooms = yplRoomsWithEntry.rooms ++ roomForMSEIssue)),
        mockMetaData,
        context)
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(context)

      val (apmRoom, nonApmRoom) = result.rooms.partition(_.channel == YplMasterChannel.APM)

      apmRoom.size should_=== 4
      apmRoomsStartAtIndex should_=== nonApmRoom.length
      nonApmRoom should_=== testHotelWithApmCommissionHolder.rooms

      apmRoom.exists(r => r.fences.contains(inFence)) should_=== true
      apmRoom.exists(r => r.fences.contains(vnFence)) should_=== true
      apmRoom.exists(r => r.fences.contains(jpFence)) should_=== false
    }

    "return getEligibleRoom correctly when APM-1405 is user A" in new ApmScope {
      val exp = List(YplExperiment(YplExperiments.APM_RE_ENABLE_BEDBANK_RATE_CHANNEL, EXPERIMENT_A))

      // hourlyAvailableSlots
      APMHelper.validateEligibleRoom(aValidRoom.copy(hourlyAvailableSlots = Seq.empty), 0, allApmConfigs)(
        mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(hourlyAvailableSlots = Seq(TimeInterval(1, "06:15"))),
                                     0,
                                     allApmConfigs)(mockYplContext(exp)) should_=== false

      // Bedbank Channels
      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                                                          stackedChannelIds = Set(Channel.APS),
                                                                          compositeChannelId = Channel.Mobile)),
                                     0,
                                     allApmConfigs)(mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.Bedbank,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(mockYplContext(exp)) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.BedbankAffiliates,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(mockYplContext(exp)) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.Bedbank),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(mockYplContext(exp)) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.BedbankAffiliates),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(mockYplContext(exp)) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Channel.BedbankChannels,
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(mockYplContext(exp)) should_=== false
    }

    "return getEligibleRoom correctly when FEZZIK-846 is user B with no beds programId" in new ApmScope {
      val UNKNOWN_PROGRAM_ID = -1
      val exp = List(YplExperiment(YplExperiments.REMOVE_BEDS_PAID_EXCLUSION_FROM_APM, EXPERIMENT_B))
      val yplContextWithBedsAdvancedProgramId = mockYplContext(exp)
        .withRequest(aValidYplRequest.withYplApmSetting(Some(ApmSettingHolder(Nil, Nil, List(UNKNOWN_PROGRAM_ID)))))
      // Non Beds Channels
      APMHelper.validateEligibleRoom(aValidRoom.copy(hourlyAvailableSlots = Seq.empty), 0, allApmConfigs)(
        yplContextWithBedsAdvancedProgramId) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(hourlyAvailableSlots = Seq(TimeInterval(1, "06:15"))),
                                     0,
                                     allApmConfigs)(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                                                          stackedChannelIds = Set(Channel.APS),
                                                                          compositeChannelId = Channel.Mobile)),
                                     0,
                                     allApmConfigs)(yplContextWithBedsAdvancedProgramId) should_=== true

      // Bedbank Channels
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.Bedbank,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.BedbankAffiliates,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.Bedbank),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.BedbankAffiliates),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Channel.BedbankChannels,
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
    }

    "return getEligibleRoom correctly when FEZZIK-846 is user A and APM_ENABLE_AI_FOR_BED_NETWORK is user A with no beds programId" in new ApmScope {
      val UNKNOWN_PROGRAM_ID = -1
      val exp = List(
        YplExperiment(YplExperiments.REMOVE_BEDS_PAID_EXCLUSION_FROM_APM, EXPERIMENT_A),
        YplExperiment(YplExperiments.APM_ENABLE_AI_FOR_BED_NETWORK, EXPERIMENT_A),
      )
      val yplContextWithBedsAdvancedProgramId = mockYplContext(exp)
        .withRequest(aValidYplRequest.withYplApmSetting(Some(ApmSettingHolder(Nil, Nil, List(UNKNOWN_PROGRAM_ID)))))
      // Non Beds Channels
      APMHelper.validateEligibleRoom(aValidRoom.copy(hourlyAvailableSlots = Seq.empty), 0, allApmConfigs)(
        yplContextWithBedsAdvancedProgramId) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(hourlyAvailableSlots = Seq(TimeInterval(1, "06:15"))),
                                     0,
                                     allApmConfigs)(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                                                          stackedChannelIds = Set(Channel.APS),
                                                                          compositeChannelId = Channel.Mobile)),
                                     0,
                                     allApmConfigs)(yplContextWithBedsAdvancedProgramId) should_=== true

      // Bedbank Channels
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.Bedbank,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.BedbankAffiliates,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.Bedbank),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.BedbankAffiliates),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Channel.BedbankChannels,
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
    }

    "return getEligibleRoom correctly when FEZZIK-846 is user A and APM_ENABLE_AI_FOR_BED_NETWORK is user B with EnableAiForBedNetwork config" in new ApmScope {
      val exp = List(
        YplExperiment(YplExperiments.REMOVE_BEDS_PAID_EXCLUSION_FROM_APM, EXPERIMENT_A),
        YplExperiment(YplExperiments.APM_ENABLE_AI_FOR_BED_NETWORK, EXPERIMENT_B),
      )
      val yplContextWithBedsAdvancedProgramId = mockYplContext(exp)
        .withRequest(aValidYplRequest.withYplApmSetting(Some(ApmSettingHolder(Nil, Nil, List.empty[Int]))))

      // Non Beds Channels
      APMHelper.validateEligibleRoom(aValidRoom.copy(hourlyAvailableSlots = Seq.empty),
                                     0,
                                     allApmConfigs,
                                     apmProgramId = -1)(yplContextWithBedsAdvancedProgramId) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(hourlyAvailableSlots = Seq(TimeInterval(1, "06:15"))),
                                     0,
                                     allApmConfigs,
                                     apmProgramId = -1)(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== true

      // Bedbank Channels
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.Bedbank,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== true
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.BedbankAffiliates,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== true
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.Bedbank),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== true
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.BedbankAffiliates),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== true
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Channel.BedbankChannels,
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== true
    }

    val bedsProgramIdsSequence: Seq[Int] = Seq(127, 155, 156)
    bedsProgramIdsSequence.foreach { BEDS_ADVANCED_PROGRAM_ID =>
      "return getEligibleRoom correctly when FEZZIK-846 is user B and APM_ENABLE_AI_FOR_BED_NETWORK is user B with apm beds programId" in new ApmScope {
        val exp = List(
          YplExperiment(YplExperiments.REMOVE_BEDS_PAID_EXCLUSION_FROM_APM, EXPERIMENT_B),
          YplExperiment(YplExperiments.APM_ENABLE_AI_FOR_BED_NETWORK, EXPERIMENT_B),
        )
        val yplContextWithBedsAdvancedProgramId = mockYplContext(exp).withRequest(
          aValidYplRequest.withYplApmSetting(Some(ApmSettingHolder(Nil, Nil, List(BEDS_ADVANCED_PROGRAM_ID)))))

        // Non Beds Channels
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(hourlyAvailableSlots = Seq.empty),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID)(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(hourlyAvailableSlots = Seq(TimeInterval(1, "06:15"))),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID)(yplContextWithBedsAdvancedProgramId) should_=== false
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true

        // Bedbank Channels
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.Bedbank,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.BedbankAffiliates,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.Bedbank),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.BedbankAffiliates),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Channel.BedbankChannels,
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
      }

      "return getEligibleRoom correctly when FEZZIK-846 is user B and APM_ENABLE_AI_FOR_BED_NETWORK is user B with apm beds programId and without EnableAiForBedNetwork config" in new ApmScope {
        val exp = List(
          YplExperiment(YplExperiments.REMOVE_BEDS_PAID_EXCLUSION_FROM_APM, EXPERIMENT_B),
          YplExperiment(YplExperiments.APM_ENABLE_AI_FOR_BED_NETWORK, EXPERIMENT_B),
        )
        val yplContextWithBedsAdvancedProgramId = mockYplContext(exp).withRequest(
          aValidYplRequest.withYplApmSetting(Some(ApmSettingHolder(Nil, Nil, List(BEDS_ADVANCED_PROGRAM_ID)))))

        // Non Beds Channels
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(hourlyAvailableSlots = Seq.empty),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID)(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(hourlyAvailableSlots = Seq(TimeInterval(1, "06:15"))),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID)(yplContextWithBedsAdvancedProgramId) should_=== false
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true

        // Bedbank Channels
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.Bedbank,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.BedbankAffiliates,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.Bedbank),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.BedbankAffiliates),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Channel.BedbankChannels,
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
      }

      "return getEligibleRoom correctly when FEZZIK-846 is user B and APM_ENABLE_AI_FOR_BED_NETWORK is user B with apm beds programId and without EnableAiForBedNetwork config" in new ApmScope {
        val exp = List(
          YplExperiment(YplExperiments.REMOVE_BEDS_PAID_EXCLUSION_FROM_APM, EXPERIMENT_B),
          YplExperiment(YplExperiments.APM_ENABLE_AI_FOR_BED_NETWORK, EXPERIMENT_B),
        )
        val yplContextWithBedsAdvancedProgramId = mockYplContext(exp).withRequest(
          aValidYplRequest.withYplApmSetting(Some(ApmSettingHolder(Nil, Nil, List(BEDS_ADVANCED_PROGRAM_ID)))))

        // Non Beds Channels
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(hourlyAvailableSlots = Seq.empty),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID)(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(hourlyAvailableSlots = Seq(TimeInterval(1, "06:15"))),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID)(yplContextWithBedsAdvancedProgramId) should_=== false
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true

        // Bedbank Channels
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.Bedbank,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.BedbankAffiliates,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.Bedbank),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.BedbankAffiliates),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Channel.BedbankChannels,
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigsWithoutEnableAiForBedNetwork,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
      }

      "return getEligibleRoom correctly when FEZZIK-846 is user B and APM_ENABLE_AI_FOR_BED_NETWORK is user A with apm beds programId" in new ApmScope {
        val BEDS_ADVANCED_PROGRAM_ID = 127
        val exp = List(
          YplExperiment(YplExperiments.REMOVE_BEDS_PAID_EXCLUSION_FROM_APM, EXPERIMENT_B),
          YplExperiment(YplExperiments.APM_ENABLE_AI_FOR_BED_NETWORK, EXPERIMENT_A),
        )
        val yplContextWithBedsAdvancedProgramId = mockYplContext(exp).withRequest(
          aValidYplRequest.withYplApmSetting(Some(ApmSettingHolder(Nil, Nil, List(BEDS_ADVANCED_PROGRAM_ID)))))

        // Non Beds Channels
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(hourlyAvailableSlots = Seq.empty),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID)(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(hourlyAvailableSlots = Seq(TimeInterval(1, "06:15"))),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID)(yplContextWithBedsAdvancedProgramId) should_=== false
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true

        // Bedbank Channels
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.Bedbank,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.BedbankAffiliates,
                                               stackedChannelIds = Set(Channel.APS),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.Bedbank),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Set(Channel.BedbankAffiliates),
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
        APMHelper.validateEligibleRoom(
          aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                               stackedChannelIds = Channel.BedbankChannels,
                                               compositeChannelId = Channel.Mobile)),
          0,
          allApmConfigs,
          apmProgramId = BEDS_ADVANCED_PROGRAM_ID,
        )(yplContextWithBedsAdvancedProgramId) should_=== true
      }
    }

    "return getEligibleRoom correctly when FEZZIK-846 is user B and APM_ENABLE_AI_FOR_BED_NETWORK is user B without apm beds programId and without EnableAiForBedNetwork config" in new ApmScope {
      val UNKNOWN_PROGRAM_ID = -1
      val exp = List(
        YplExperiment(YplExperiments.REMOVE_BEDS_PAID_EXCLUSION_FROM_APM, EXPERIMENT_B),
        YplExperiment(YplExperiments.APM_ENABLE_AI_FOR_BED_NETWORK, EXPERIMENT_B),
      )
      val yplContextWithBedsAdvancedProgramId = mockYplContext(exp)
        .withRequest(aValidYplRequest.withYplApmSetting(Some(ApmSettingHolder(Nil, Nil, List(UNKNOWN_PROGRAM_ID)))))

      // Non Beds Channels
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(hourlyAvailableSlots = Seq.empty),
        0,
        allApmConfigsWithoutEnableAiForBedNetwork)(yplContextWithBedsAdvancedProgramId) should_=== true
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(hourlyAvailableSlots = Seq(TimeInterval(1, "06:15"))),
        0,
        allApmConfigsWithoutEnableAiForBedNetwork)(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigsWithoutEnableAiForBedNetwork,
      )(yplContextWithBedsAdvancedProgramId) should_=== true

      // Bedbank Channels
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.Bedbank,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigsWithoutEnableAiForBedNetwork,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.BedbankAffiliates,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigsWithoutEnableAiForBedNetwork,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.Bedbank),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigsWithoutEnableAiForBedNetwork,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.BedbankAffiliates),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigsWithoutEnableAiForBedNetwork,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Channel.BedbankChannels,
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigsWithoutEnableAiForBedNetwork,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
    }

    "return getEligibleRoom correctly when FEZZIK-846 is user B and APM_ENABLE_AI_FOR_BED_NETWORK is user A without apm beds programId" in new ApmScope {
      val BEDS_ADVANCED_PROGRAM_ID = 127
      val exp = List(
        YplExperiment(YplExperiments.REMOVE_BEDS_PAID_EXCLUSION_FROM_APM, EXPERIMENT_B),
        YplExperiment(YplExperiments.APM_ENABLE_AI_FOR_BED_NETWORK, EXPERIMENT_A),
      )
      val yplContextWithBedsAdvancedProgramId = mockYplContext(exp).withRequest(
        aValidYplRequest.withYplApmSetting(Some(ApmSettingHolder(Nil, Nil, List(BEDS_ADVANCED_PROGRAM_ID)))))

      // Non Beds Channels
      APMHelper.validateEligibleRoom(aValidRoom.copy(hourlyAvailableSlots = Seq.empty),
                                     0,
                                     allApmConfigs,
                                     apmProgramId = -1)(yplContextWithBedsAdvancedProgramId) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(hourlyAvailableSlots = Seq(TimeInterval(1, "06:15"))),
                                     0,
                                     allApmConfigs,
                                     apmProgramId = -1)(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== true

      // Bedbank Channels
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.Bedbank,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.BedbankAffiliates,
                                             stackedChannelIds = Set(Channel.APS),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.Bedbank),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Set(Channel.BedbankAffiliates),
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplChannel(baseChannelId = Channel.RTL,
                                             stackedChannelIds = Channel.BedbankChannels,
                                             compositeChannelId = Channel.Mobile)),
        0,
        allApmConfigs,
        apmProgramId = -1,
      )(yplContextWithBedsAdvancedProgramId) should_=== false
    }

    "return getEligibleRoom correctly for isExcludePkgRatePlanExp" in new ApmScope {
      val exp = List(YplExperiment(YplExperiments.APM_RE_ENABLE_BEDBANK_RATE_CHANNEL, EXPERIMENT_B))

      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplMasterChannel.RTL), 0, allApmConfigs)(
        mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplMasterChannel.Package), 0, allApmConfigs)(
        mockYplContext(exp)) should_=== false

      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplMasterChannel.RTL), 0, allApmConfigs)(
        mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplMasterChannel.Package), 0, allApmConfigs)(
        mockYplContext(exp)) should_=== false
      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplMasterChannel.APO), 0, allApmConfigs)(
        mockYplContext(exp)) should_=== true
    }

    "return getEligibleRoom correctly for isExcludePkgRatePlanExp and skipPackageValidateCommReductionFeatureExp is true" in new ApmScope {
      val exp = List(YplExperiment(YplExperiments.APM_RE_ENABLE_BEDBANK_RATE_CHANNEL, EXPERIMENT_B))

      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplMasterChannel.RTL),
        0,
        allApmConfigs,
        skipPackageValidateCommReductionFeatureExp = true)(mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplMasterChannel.Package),
        0,
        allApmConfigs,
        skipPackageValidateCommReductionFeatureExp = true)(mockYplContext(exp)) should_=== true

      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplMasterChannel.RTL),
        0,
        allApmConfigs,
        skipPackageValidateCommReductionFeatureExp = true)(mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplMasterChannel.Package),
        0,
        allApmConfigs,
        skipPackageValidateCommReductionFeatureExp = true)(mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(
        aValidRoom.copy(channel = YplMasterChannel.APO),
        0,
        allApmConfigs,
        skipPackageValidateCommReductionFeatureExp = true)(mockYplContext(exp)) should_=== true
    }

    "return getEligibleRoom correctly for Taiwan ASO" in new ApmScope {
      val exp = List(YplExperiment(YplExperiments.APM_RE_ENABLE_BEDBANK_RATE_CHANNEL, EXPERIMENT_B))

      APMHelper.validateEligibleRoom(aValidRoom.copy(stayPackageType = Some(NormalOffer)), 0, allApmConfigs)(
        mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(stayPackageType = Some(NormalOffer)),
                                     TAIWAN_COUNTRY_ID,
                                     allApmConfigs)(mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(stayPackageType = Some(Escapes)), 0, allApmConfigs)(
        mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(stayPackageType = Some(Escapes)),
                                     TAIWAN_COUNTRY_ID,
                                     allApmConfigs)(mockYplContext(exp)) should_=== false
    }

    "return getEligibleRoom correctly for isExcludeQuarantineChannelExp" in new ApmScope {
      val exp = List(YplExperiment(YplExperiments.APM_RE_ENABLE_BEDBANK_RATE_CHANNEL, EXPERIMENT_B))

      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplMasterChannel.RTL),
                                     0,
                                     allApmConfigs,
                                     isExcludeQuarantineChannel = false)(mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplMasterChannel(Channel.Quarantine)),
                                     0,
                                     allApmConfigs,
                                     isExcludeQuarantineChannel = false)(mockYplContext(exp)) should_=== true

      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplMasterChannel.RTL),
                                     0,
                                     allApmConfigs,
                                     isExcludeQuarantineChannel = true)(mockYplContext(exp)) should_=== true
      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplMasterChannel(Channel.Quarantine)),
                                     0,
                                     allApmConfigs,
                                     isExcludeQuarantineChannel = true)(mockYplContext(exp)) should_=== false
      APMHelper.validateEligibleRoom(aValidRoom.copy(channel = YplMasterChannel.APO),
                                     0,
                                     allApmConfigs,
                                     isExcludeQuarantineChannel = true)(mockYplContext(exp)) should_=== true
    }

    "return getEligibleRoom correctly for resell offers" in new ApmScope {
      val exp = List(
        YplExperiment(YplExperiments.APM_RE_ENABLE_BEDBANK_RATE_CHANNEL, EXPERIMENT_B),
        YplExperiment(YplExperiments.SKIP_APM_PRICE_ADJUSTMENT_FOR_RESELL, EXPERIMENT_B),
      )

      APMHelper.validateEligibleRoom(aValidRoom.copy(resellExternalData = Some(ResellExternalData("********"))),
                                     0,
                                     allApmConfigs,
                                     isExcludeQuarantineChannel = false)(mockYplContext(exp)) should_=== false
      APMHelper.validateEligibleRoom(aValidRoom.copy(resellExternalData = None),
                                     0,
                                     allApmConfigs,
                                     isExcludeQuarantineChannel = false)(mockYplContext(exp)) should_=== true

    }

    "applyApmCommissionDiscountIsRefactorCommission - no blackout date" in new ApmScope {
      val commissionResult: ApmCommissionDiscountResult = ApmCommissionDiscountResult(
        Set.empty,
        isRoomEligible = true,
        10d + aValidAdditionalHotelCommission,
        5d,
        None,
        isSupplierValid = false)
      val aValidRoomWithPrice = aValidRoom.withPrices(
        List(
          aValidPrice.withChargeType(ChargeType.Room).withChannelDiscountBreakdown(aValidChannelDiscounts).build,
          aValidPrice.withChargeType(ChargeType.ExtraBed).build,
          aValidPrice.withChargeType(ChargeType.Surcharge).build,
        ))
      val room: YPLRoom = applyApmCommissionDiscount(aValidRoomWithPrice.build,
                                                     aHotelEntryModel.build,
                                                     commissionResult)(mockYplContext(List.empty))
      val roomPrice: List[YplPrice] = room.prices
      roomPrice
        .find(_.chargeType == ChargeType.Room)
        .exists(_.apmCommissionDiscountPercent == 10d + aValidAdditionalHotelCommission) should_== true
      roomPrice
        .find(_.chargeType == ChargeType.ExtraBed)
        .exists(_.apmCommissionDiscountPercent == 10d + aValidAdditionalHotelCommission) should_== true
      roomPrice.find(_.chargeType == ChargeType.Surcharge).exists(_.apmCommissionDiscountPercent == 0d) should_== true

      "with correct channelDiscounts" in {
        roomPrice.head.channelDiscounts should_== aValidChannelDiscounts
      }
    }

    "applyApmCommissionDiscountIsRefactorCommission - with room blackout date" in new ApmScope {
      val commissionResult: ApmCommissionDiscountResult = ApmCommissionDiscountResult(
        Set(DateTime.parse("2023-01-01")),
        isRoomEligible = true,
        10d + aValidAdditionalHotelCommission,
        5d,
        None,
        isSupplierValid = false)
      val aValidRoomWithPrice = aValidRoom.withPrices(
        List(
          aValidPrice.withDate(DateTime.parse("2023-01-01")).withChargeType(ChargeType.Room).build,
          aValidPrice.withChargeType(ChargeType.ExtraBed).build,
        ))
      val room: YPLRoom = applyApmCommissionDiscount(aValidRoomWithPrice.build,
                                                     aHotelEntryModel.build,
                                                     commissionResult)(mockYplContext(List.empty))
      room.prices.find(_.chargeType == ChargeType.Room).map(_.apmCommissionDiscountPercent) should_== Some(0d)
      room.prices.find(_.chargeType == ChargeType.ExtraBed).map(_.apmCommissionDiscountPercent) should_== Some(
        10d + aValidAdditionalHotelCommission)
    }

    "applyApmCommissionDiscountIsRefactorCommission - with extrabed blackout date" in new ApmScope {
      val commissionResult: ApmCommissionDiscountResult = ApmCommissionDiscountResult(
        Set(DateTime.parse("2023-01-01")),
        isRoomEligible = true,
        10d + aValidAdditionalHotelCommission,
        5d,
        None,
        isSupplierValid = false)
      val aValidRoomWithPrice = aValidRoom.withPrices(
        List(
          aValidPrice.withDate(DateTime.parse("2023-01-01")).withChargeType(ChargeType.ExtraBed).build,
          aValidPrice.withChargeType(ChargeType.Room).build,
        ))
      val room: YPLRoom = applyApmCommissionDiscount(aValidRoomWithPrice.build,
                                                     aHotelEntryModel.build,
                                                     commissionResult)(mockYplContext(List.empty))
      room.prices.find(_.chargeType == ChargeType.ExtraBed).map(_.apmCommissionDiscountPercent) should_== Some(0d)
      room.prices.find(_.chargeType == ChargeType.Room).map(_.apmCommissionDiscountPercent) should_== Some(
        10d + aValidAdditionalHotelCommission)
    }

    "applyApmCommissionDiscountIsRefactorCommission - with surcharge blackout date" in new ApmScope {
      val commissionResult: ApmCommissionDiscountResult = ApmCommissionDiscountResult(
        Set(DateTime.parse("2023-01-01")),
        isRoomEligible = true,
        10d + aValidAdditionalHotelCommission,
        5d,
        None,
        isSupplierValid = false)
      val aValidRoomWithPrice = aValidRoom.withPrices(
        List(
          aValidPrice.withChargeType(ChargeType.Surcharge).build,
        ))
      val room: YPLRoom = applyApmCommissionDiscount(aValidRoomWithPrice.build,
                                                     aHotelEntryModel.build,
                                                     commissionResult)(mockYplContext(List.empty))
      room.prices.find(_.chargeType == ChargeType.Surcharge).map(_.apmCommissionDiscountPercent) should_== Some(0d)
    }

    "applyApmCommissionDiscountIsRefactorCommission - copy multiple auto price match to room correctly" in new ApmScope {
      val commissionResult: ApmCommissionDiscountResult = ApmCommissionDiscountResult(
        Set.empty,
        isRoomEligible = true,
        10d + aValidAdditionalHotelCommission,
        5d,
        Some(aValidMultipleAutoPriceMatch.head),
        isSupplierValid = false,
      )

      val emptyMAPMCommissionResult = commissionResult.copy(commissionDiscountChannelSetting = None)
      val aValidRoomWithPrice = aValidRoom.withPrices(
        List(
          aValidPrice.withChargeType(ChargeType.Surcharge).build,
        ))
      val room: YPLRoom = applyApmCommissionDiscount(aValidRoomWithPrice.build,
                                                     aHotelEntryModel.build,
                                                     commissionResult)(mockYplContext(List.empty))
      val emptySettingRoom = applyApmCommissionDiscount(aValidRoomWithPrice.build,
                                                        aHotelEntryModel.build,
                                                        emptyMAPMCommissionResult)(mockYplContext(List.empty))
      room.apmCommissionDiscountSetting should_== Some(aValidMultipleAutoPriceMatch.head)
      emptySettingRoom.apmCommissionDiscountSetting should_== None
    }

    "processApmExternalData work correctly when isPartialAdjustment is true" in new ApmScope {
      val exp = List.empty
      val res = processApmExternalData(mockInputYplHotel(exp))(mockYplContext(exp))

      val hotelId = res.id
      val lengthOfStay = res.lengthOfStay

      hotelId should_=== 6011203
      lengthOfStay should_=== 1
    }

    "processApmExternalData work correctly when applies map price logic in priceAdjustmentDetail" in new ApmScope {
      val exp = List.empty
      val apmPriceAdjustmentDetail = ApmPriceAdjustmentDetail(
        cheapestPrice = 10d,
        marketPrice = 11d,
        delta = 1d,
        originalPrice = 11d,
        marketPriceDiscountPercent = 10d,
        maximumDeltaPercent = 10d,
        isPartialAdjustment = Some(true),
        approvalPriceId = 111,
        adjustmentRateType = RateType.SellInclusive,
      )
      val pricesWithAdjustmentSellInclusive: List[YplPrice] =
        List(originalRooms.head.prices.head.copy(apmPriceAdjustmentDetail = Some(apmPriceAdjustmentDetail)))
      val pricesWithAdjustmentSellExclusive: List[YplPrice] = List(
        originalRooms.head.prices.head.copy(apmPriceAdjustmentDetail =
          Some(apmPriceAdjustmentDetail.copy(adjustmentRateType = RateType.SellExclusive))))
      val originalRoomWithAdjustmentPrice: List[YPLRoom] =
        List(originalRooms.head.copy(prices = pricesWithAdjustmentSellInclusive),
             originalRooms.head.copy(prices = pricesWithAdjustmentSellExclusive))

      val res: YPLHotel =
        processApmExternalData(mockInputYplHotel(exp).copy(rooms = originalRoomWithAdjustmentPrice))(mockYplContext(exp))

      val hotelId = res.id
      val lengthOfStay = res.lengthOfStay

      hotelId should_=== 6011203
      lengthOfStay should_=== 1
      res.rooms.head.prices.nonEmpty should_=== (true)
      res.rooms.size should_=== 2
      res.rooms.head.apmExternalData.get.priceAdjustmentDetails.head.cheapestSellIn should_=== 10d
      res.rooms.head.apmExternalData.get.priceAdjustmentDetails.head.marketSellIn should_=== 11d
      res.rooms.head.apmExternalData.get.priceAdjustmentDetails.head.originalSellIn should_=== 11d
      res.rooms.head.apmExternalData.get.priceAdjustmentDetails.head.marketPriceLocal should_=== 11d
      res.rooms.head.apmExternalData.get.priceAdjustmentDetails.head.originalPriceLocal should_=== 11d
      res.rooms.head.apmExternalData.get.priceAdjustmentDetails.head.cheapestPriceLocal should_=== 10d
      res.rooms.head.apmExternalData.get.priceAdjustmentDetails.head.adjustmentRateType should_=== RateType.SellInclusive
      res.rooms(1).apmExternalData.get.priceAdjustmentDetails.head.cheapestSellIn should_=== 0d
      res.rooms(1).apmExternalData.get.priceAdjustmentDetails.head.marketSellIn should_=== 0d
      res.rooms(1).apmExternalData.get.priceAdjustmentDetails.head.originalSellIn should_=== 0d
      res.rooms(1).apmExternalData.get.priceAdjustmentDetails.head.marketPriceLocal should_=== 11d
      res.rooms(1).apmExternalData.get.priceAdjustmentDetails.head.originalPriceLocal should_=== 11d
      res.rooms(1).apmExternalData.get.priceAdjustmentDetails.head.cheapestPriceLocal should_=== 10d
      res.rooms(1).apmExternalData.get.priceAdjustmentDetails.head.adjustmentRateType should_=== RateType.SellExclusive
    }

    "processApmExternalData work correctly with commission reduction value for hotel level from config" in new ApmScope {
      val exp = List.empty
      val res = processApmExternalData(hotel = mockInputYplHotel(exp), apmConfigs = allApmConfigs)(mockYplContext(exp))

      val hotelId = res.id
      val lengthOfStay = res.lengthOfStay
      val additionalCommRedHotelLevel = res.rooms.map(_.apmExternalData.map(_.additionalCommRedHotelLevel)).head

      hotelId should_=== 6011203
      lengthOfStay should_=== 1
      additionalCommRedHotelLevel should_=== Some(aValidAdditionalHotelCommission)
    }

    "processApmExternalData work correctly" in {
      Fragments(
        List(
          (ApmProgramType.Ai, ApmHotelStatus.Active, true),
          (ApmProgramType.Ai, ApmHotelStatus.Suspended, true),
          (ApmProgramType.AiCalendarView, ApmHotelStatus.Active, true),
          (ApmProgramType.Arp, ApmHotelStatus.Experimental, true),
          (ApmProgramType.ArpV2, ApmHotelStatus.ExperimentalV2, true),
          (ApmProgramType.PeakSeason, ApmHotelStatus.PeakSeason, true),
        ).map { case (programTypeId, statusId, expected) =>
          s"when programTypeId is $programTypeId and statusId is $statusId, isApmHotelActive should be $expected" in new ApmScope {
            val exp = List.empty
            private val fences = Set(YplRateFence("TH", -1, 1))

            val apmPriceAdjustmentSetting = Seq(
              aValidMultipleAutoPriceMatch.head.copy(
                statusId = statusId,
                programType = Some(programTypeId),
              ))
            val apmCommissionDiscountSetting = Seq(
              aValidMultipleAutoPriceMatch.head.copy(
                statusId = statusId,
                programType = Some(programTypeId),
              ))

            val yplRoom = aValidRoom
              .withApmPriceAdjustmentSetting(apmPriceAdjustmentSetting.head)
              .withApmCommissionDiscountSetting(apmCommissionDiscountSetting.head)
              .build
            val context = YplContext(aValidYplRequest.withFences(Map(YplMasterChannel.APM -> fences)))
            val res = processApmExternalData(hotel = mockInputYplHotel(exp).withRooms(List(yplRoom)),
                                             apmConfigs = allApmConfigs)(context)

            res.rooms.head.apmExternalData.exists(_.isApmHotelActive) should_=== expected
          }
        }: _*,
      )
    }

    "isApplyAdjustmentOnSellEx work correctly when program id is not in Sell Ex program ids" in new ApmScope {
      val sellExProgramIds = List(99, 101)
      val context = YplContext(
        aValidYplRequest.copy(apmSetting = Some(ApmSettingHolder(sellExProgramIds, List.empty[Int], List.empty[Int]))))
      val programId = 1
      val isSellExAdjustment = isApplyAdjustmentOnSellEx(programId, context.request.apmSetting)
      isSellExAdjustment shouldEqual false
    }

    "isApplyAdjustmentOnSellEx work correctly when program id is in Sell Ex program ids" in new ApmScope {
      val sellExProgramIds = List(99, 101)
      val context = YplContext(
        aValidYplRequest.copy(apmSetting = Some(ApmSettingHolder(sellExProgramIds, List.empty[Int], List.empty[Int]))))
      val programId = 99
      val isSellExAdjustment = isApplyAdjustmentOnSellEx(programId, context.request.apmSetting)
      isSellExAdjustment shouldEqual true
    }

    // should connect to VPN first, otherwise, this test will fail
    "build ApmPricingLog should return the message correctly" in new ApmScope {
      val exp: List[YplExperiment] = List.empty
      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp), mockMetaData, mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val (_, apmRooms) = yplHotelWithAPMRooms.rooms.splitAt(apmRoomsStartAtIndex)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRooms,
                                                                               mockMetaData,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms)
      val apmPricingFlowMessage: Option[ApmPricingFlowMessage] =
        buildApmPricingFlowMessage(elementsForLogs)(mockYplContext(exp))
      val result: FacilityId = apmPricingFlowMessage.get.checkCompatibility().getCode
      (result == CompatibilityResult.COMPATIBILITY_YES || result == CompatibilityResult.COMPATIBILITY_SAME) must_== true
    }

    "build ApmPricingLog should return a null message because there is no apmRoomChannelSetting" in new ApmScope {
      val exp: List[YplExperiment] = List.empty
      val apmRoomChannelSetting = Seq()

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotel(exp), mockMetaData, mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val (_, apmRooms) = yplHotelWithAPMRooms.rooms.splitAt(apmRoomsStartAtIndex)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRooms,
                                                                               mockMetaData,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms)
      val apmPricingFlowMessage: Option[ApmPricingFlowMessage] =
        buildApmPricingFlowMessage(elementsForLogs)(mockYplContext(exp))
      apmPricingFlowMessage must beNone
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          DateTime.parse("2015-10-05") -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId4, validApprovalPriceIdPool(3))),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          DateTime.parse("2015-10-06") -> AutoPriceMatchPriceInfo(91d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          DateTime.parse("2015-10-07") -> AutoPriceMatchPriceInfo(92d, 999999999, validApprovalPriceIdPool(2)),
          DateTime.parse("2015-10-09") -> AutoPriceMatchPriceInfo(94d, aValidRoomTypeId3, validApprovalPriceIdPool(4)),
          aValidStayDate1 -> AutoPriceMatchPriceInfo(3000d, aValidRoomTypeId4, validApprovalPriceIdPool(5)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(20d, aValidRoomTypeId4, validApprovalPriceIdPool(6)),
        ),
      )

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(4444))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetaDataWithApmLeadingRoom,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaDataWithApmLeadingRoom,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))

      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 2, ApmNoPriceAdjustmentReason.ApmRoomIdIsNotAgodaCheapest.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 6, ApmNoPriceAdjustmentReason.AgodaAlreadyCheaper.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 3, ApmNoPriceAdjustmentReason.NoRoomMatchWithApmRoomId.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 5, ApmNoPriceAdjustmentReason.AgodaCheapestIsNotLeadRoom.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 7, ApmNoPriceAdjustmentReason.OtherReason.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.NoRoomForEligibleOcc.value),
      )
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly when NoRoomForEligibleRc" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          DateTime.parse("2015-10-05") -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId4, validApprovalPriceIdPool(3))),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          DateTime.parse("2015-10-06") -> AutoPriceMatchPriceInfo(91d, aValidRoomTypeId1, validApprovalPriceIdPool(1))),
      )

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(4444))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithNonEligibleRcRooms(exp),
                                                  mockMetaDataWithApmLeadingRoom,
                                                  mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaDataWithApmLeadingRoom,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 2, ApmNoPriceAdjustmentReason.NoRoomForEligibleRc.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.NoRoomForEligibleRc.value),
      )
    }

    "buildApmNoPriceAdjustmentReasonMessage should return the compatible message" in new ApmScope {
      val exp = List.empty

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          DateTime.parse("2015-10-09") -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          DateTime.parse("2015-10-10") -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId1, validApprovalPriceIdPool(3)),
        ))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp), mockMetaData, mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaData,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val ApmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      val result = ApmNoPriceAdjustmentReasons.head.checkCompatibility().getCode
      (result == CompatibilityResult.COMPATIBILITY_YES || result == CompatibilityResult.COMPATIBILITY_SAME) must_== true
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          DateTime.parse("2015-10-05") -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId4, validApprovalPriceIdPool(3))),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          DateTime.parse("2015-10-06") -> AutoPriceMatchPriceInfo(91d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          DateTime.parse("2015-10-07") -> AutoPriceMatchPriceInfo(92d, 999999999, validApprovalPriceIdPool(2)),
          DateTime.parse("2015-10-09") -> AutoPriceMatchPriceInfo(94d, aValidRoomTypeId3, validApprovalPriceIdPool(4)),
          aValidStayDate1 -> AutoPriceMatchPriceInfo(3000d, aValidRoomTypeId4, validApprovalPriceIdPool(5)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(20d, aValidRoomTypeId4, validApprovalPriceIdPool(6)),
        ),
      )

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(4444))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetaDataWithApmLeadingRoom,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaDataWithApmLeadingRoom,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))

      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 2, ApmNoPriceAdjustmentReason.ApmRoomIdIsNotAgodaCheapest.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 3, ApmNoPriceAdjustmentReason.NoRoomMatchWithApmRoomId.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.NoRoomForEligibleOcc.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 5, ApmNoPriceAdjustmentReason.AgodaCheapestIsNotLeadRoom.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 6, ApmNoPriceAdjustmentReason.AgodaAlreadyCheaper.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 7, ApmNoPriceAdjustmentReason.OtherReason.value),
      )
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly when A NoRoomForEligibleRc" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          DateTime.parse("2015-10-05") -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId4, validApprovalPriceIdPool(3))),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          DateTime.parse("2015-10-06") -> AutoPriceMatchPriceInfo(91d, aValidRoomTypeId1, validApprovalPriceIdPool(1))),
      )

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(4444))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithNonEligibleRcRooms(exp),
                                                  mockMetaDataWithApmLeadingRoom,
                                                  mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaDataWithApmLeadingRoom,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 2, ApmNoPriceAdjustmentReason.NoRoomForEligibleRc.value),
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.NoRoomForEligibleRc.value),
      )
    }

    // should connect to VPN first, otherwise, this test will fail
    "buildApmCommissionDiscountLog check compatibility should return correctly" in new ApmScope {
      val context = YplContext(aValidYplRequest.copy(isBookingRequest = true))
      val aValidYplPrice: YplPrice =
        aValidPrice.copy(netExclusive = 10, margin = 10, tax = 10, fee = 10, processingFee = 10, taxOverSellEx = 10)
      val room1: YPLRoom = aValidRoom.copy(prices = List(aValidYplPrice.copy(apmCommissionDiscountPercent = 2)))
      val commissionResult: ApmCommissionDiscountResult = ApmCommissionDiscountResult(
        Set.empty,
        isRoomEligible = true,
        10d + aValidAdditionalHotelCommission,
        5d,
        None,
        isSupplierValid = false)

      val apmCommissionDiscountLog: Option[ApmCommissionDiscountLog] =
        buildApmCommissionDiscountLog(context, room1, commissionResult, Set(1825))
      val result: FacilityId = apmCommissionDiscountLog.get.checkCompatibility().getCode
      (result == CompatibilityResult.COMPATIBILITY_YES || result == CompatibilityResult.COMPATIBILITY_SAME) must_== true
    }

    "buildApmCommissionDiscountLog given MultipleAutoPriceMatchHolder is empty should return default message" in new ApmScope {
      val context = YplContext(aValidYplRequest.copy(isBookingRequest = true))
        .withExperimentContext(forceBExperimentContext(YplExperiments.APM_ENABLE_AI_FOR_BED_NETWORK))

      val aValidYplPrice: YplPrice = aValidPrice.copy(netExclusive = 10,
                                                      margin = 10,
                                                      tax = 10,
                                                      fee = 10,
                                                      processingFee = 10,
                                                      taxOverSellEx = 10,
                                                      apmCommissionDiscountPercent = 2)
      val room: YPLRoom = aValidRoomWithAPMChannel.copy(apmExternalData = None, prices = List(aValidYplPrice))
      val commissionResult: ApmCommissionDiscountResult = ApmCommissionDiscountResult(
        Set.empty,
        isRoomEligible = true,
        10d + aValidAdditionalHotelCommission,
        5d,
        None,
        isSupplierValid = false)

      val apmCommissionDiscountLog: Option[ApmCommissionDiscountLog] =
        buildApmCommissionDiscountLog(context, room, commissionResult, Set(1825))

      apmCommissionDiscountLog.get should_=== ApmCommissionDiscountLog(
        searchId = context.request.searchId,
        hotelId = room.hotelId,
        statusId = None,
        programId = None,
        programType = None,
        commissionDiscountChannelId = None,
        isSupplierValid = commissionResult.isSupplierValid,
        isRoomEligible = commissionResult.isRoomEligible,
        roomTypeId = room.roomTypeId,
        rateCategoryId = room.rateCategoryId,
        baseChannelId = room.channel.baseChannelId,
        sellIn = room.sellIn,
        occupancy = room.occupancy,
        dmcId = room.supplierId,
        channel = room.channel,
        stayPackageType = room.stayPackageType,
        hourlyAvailableSlots = room.hourlyAvailableSlots.map(_.toString),
        resellExternalData = room.resellExternalData,
        roomFences = room.fences.map(_.toCommissionModel),
        apmSettingHolder = None,
        apmPriceCommissionDetails = Seq(
          ApmPriceCommissionDetail(
            date = DayUseUtils.toJavaLocalDate(aValidYplPrice.date).toString,
            roomNumber = aValidYplPrice.roomNumber,
            chargeType = aValidYplPrice.chargeType,
            chargeOption = aValidYplPrice.chargeOption,
            referenceCommissionPercent = aValidYplPrice.referenceCommissionPercent,
            commissionDiscountPercent = aValidYplPrice.apmCommissionDiscountPercent,
          )),
        isCommissionDistributedFromHeisenberg = false,
        isNotPackage = true,
        isNotQuarantineChannel = true,
        isExternalResellDataEmpty = true,
        isEmptyHourlyAvailableSlots = true,
        skipPackageValidateCommReductionFeatureExp = true,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = true,
        excludeRateChannelFromApmExp = false,
      )
    }

    "buildApmCommissionDiscountLog given MultipleAutoPriceMatchHolder is not empty should return message correctly" in new ApmScope {
      val context = YplContext(aValidYplRequest.copy(isBookingRequest = true))
        .withExperimentContext(forceBExperimentContext(YplExperiments.APM_ENABLE_AI_FOR_BED_NETWORK))
      val aValidYplPrice: YplPrice = aValidPrice.copy(netExclusive = 10,
                                                      margin = 10,
                                                      tax = 10,
                                                      fee = 10,
                                                      processingFee = 10,
                                                      taxOverSellEx = 10,
                                                      apmCommissionDiscountPercent = 2)
      val room: YPLRoom = aValidRoomWithAPMChannel.copy(prices = List(aValidYplPrice))
      val commissionResult: ApmCommissionDiscountResult = ApmCommissionDiscountResult(
        Set.empty,
        isRoomEligible = true,
        10d + aValidAdditionalHotelCommission,
        5d,
        Some(aValidMultipleAutoPriceMatch.head.copy(commissionDiscountChannelId = Some(1825))),
        isSupplierValid = false,
      )

      val apmCommissionDiscountLog: Option[ApmCommissionDiscountLog] =
        buildApmCommissionDiscountLog(context, room, commissionResult, Set(1825))

      apmCommissionDiscountLog.get should_=== ApmCommissionDiscountLog(
        searchId = context.request.searchId,
        hotelId = room.hotelId,
        statusId = Some(commissionResult.commissionDiscountChannelSetting.get.statusId),
        programId = Some(commissionResult.commissionDiscountChannelSetting.get.programId),
        programType = commissionResult.commissionDiscountChannelSetting.get.programType,
        commissionDiscountChannelId = commissionResult.commissionDiscountChannelSetting.get.commissionDiscountChannelId,
        isSupplierValid = commissionResult.isSupplierValid,
        isRoomEligible = commissionResult.isRoomEligible,
        roomTypeId = room.roomTypeId,
        rateCategoryId = room.rateCategoryId,
        baseChannelId = room.channel.baseChannelId,
        sellIn = room.sellIn,
        occupancy = room.occupancy,
        dmcId = room.supplierId,
        channel = room.channel,
        stayPackageType = room.stayPackageType,
        hourlyAvailableSlots = room.hourlyAvailableSlots.map(_.toString),
        resellExternalData = room.resellExternalData,
        roomFences = room.fences.map(_.toCommissionModel),
        apmSettingHolder = None,
        apmPriceCommissionDetails = Seq(
          ApmPriceCommissionDetail(
            date = DayUseUtils.toJavaLocalDate(aValidYplPrice.date).toString,
            roomNumber = aValidYplPrice.roomNumber,
            chargeType = aValidYplPrice.chargeType,
            chargeOption = aValidYplPrice.chargeOption,
            referenceCommissionPercent = aValidYplPrice.referenceCommissionPercent,
            commissionDiscountPercent = aValidYplPrice.apmCommissionDiscountPercent,
          )),
        isCommissionDistributedFromHeisenberg = true,
        isNotPackage = true,
        isNotQuarantineChannel = true,
        isExternalResellDataEmpty = true,
        isEmptyHourlyAvailableSlots = true,
        skipPackageValidateCommReductionFeatureExp = true,
        removeBedPaidExclusionFromApmExp = false,
        skipApmPriceAdjustmentForResellExp = false,
        enabledAiForBedNetwork = true,
        excludeRateChannelFromApmExp = false,
      )
    }

    "buildApmCommissionDiscountLog given request is not a booking request should return empty message" in new ApmScope {
      val context = YplContext(aValidYplRequest.copy(isBookingRequest = false))

      val aValidYplPrice: YplPrice =
        aValidPrice.copy(netExclusive = 10, margin = 10, tax = 10, fee = 10, processingFee = 10, taxOverSellEx = 10)
      val room1: YPLRoom = aValidRoom.copy(prices = List(aValidYplPrice.copy(apmCommissionDiscountPercent = 2)))
      val commissionResult: ApmCommissionDiscountResult = ApmCommissionDiscountResult(
        Set.empty,
        isRoomEligible = true,
        10d + aValidAdditionalHotelCommission,
        5d,
        None,
        isSupplierValid = false)

      val apmCommissionDiscountLog: Option[ApmCommissionDiscountLog] =
        buildApmCommissionDiscountLog(context, room1, commissionResult, Set(1, 2, 1825))

      apmCommissionDiscountLog should_=== None
    }

    "should populate and propagate the originalChannelId of APM rooms when FEZZIK-845 is B" in new ApmScope {
      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh.copy(adjustmentDiscountPercent = 0d))

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
        ))

      private val fences = Set(YplRateFence("TH", -1, 1))
      val context = YplContext(aValidYplRequest.withFences(Map(YplMasterChannel.APM -> fences)))
        .withExperimentContext(forceBExperimentContext(YplExperiments.ADD_ORIGINAL_CHANNEL_ID_FOR_AI_BOOKING))

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithContext(context, fences), mockMetaData, context)
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(context)
      val originalRoomsWithFences = testHotelWithApmCommissionHolder.rooms.map(r => r.copy(fences = fences))

      val originalApmRoomChannelIds = result.rooms
        .filter(_.channel == YplMasterChannel.APM)
        .filter(_.originalChannelId.nonEmpty)
        .flatMap(_.originalChannelId)

      originalApmRoomChannelIds must contain(YplMasterChannel.RTL.baseChannelId)
    }

    "should not populate and propagate the originalChannelId of APM rooms when FEZZIK-845 is A" in new ApmScope {
      val exp = List(YplExperiment(YplExperiments.ADD_ORIGINAL_CHANNEL_ID_FOR_AI_BOOKING, EXPERIMENT_B))
      override def aValidMultipleAutoPriceMatch: Seq[MultipleAutoPriceMatchHolder] =
        Seq(mockMultipleAutoPriceMatchWithAPMAdjustmentCh.copy(adjustmentDiscountPercent = 0d))

      override def aValidAutoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(0)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          aValidStayDate4 -> AutoPriceMatchPriceInfo(8d, aValidRoomTypeId1, validApprovalPriceIdPool(2)),
        ))

      private val fences = Set(YplRateFence("TH", -1, 1))
      val context = YplContext(
        aValidYplRequest
          .withFences(Map(YplMasterChannel.APM -> fences))
          .build
          .copy(experiments = List(YplExperiment(YplExperiments.ADD_ORIGINAL_CHANNEL_ID_FOR_AI_BOOKING, EXPERIMENT_A))))

      val testHotelWithApmCommissionHolder =
        assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithContext(context, fences), mockMetaData, context)
      val (result, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaData,
                                  dispatchChannels = aValidYplDispatchChannels)(context)

      val apmRoom = result.rooms.filter(_.originalChannelId.nonEmpty)

      apmRoom should_=== Nil
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly - NoRoomForEligibleOcc" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          DateTime.parse("2015-10-05") -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId4, validApprovalPriceIdPool(3))))

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(4444))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetaDataWithApmLeadingRoom,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaDataWithApmLeadingRoom,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.NoRoomForEligibleOcc.value))
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly - NoRoomMatchWithLeadingRoomId" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          DateTime.parse("2015-10-05") -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId4, validApprovalPriceIdPool(3))))

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(5555))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetaDataWithApmLeadingRoom,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaDataWithApmLeadingRoom,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.AgodaCheapestIsNotLeadRoom.value))
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly when LeadingRoomIsNotAgodaCheapest" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          DateTime.parse("2015-10-05") -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId2, validApprovalPriceIdPool(3))))

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(2222))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetaDataWithApmLeadingRoom,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaDataWithApmLeadingRoom,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.ApmRoomIdIsNotAgodaCheapest.value))
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly when AgodaAlreadyCheaper" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(3000d, aValidRoomTypeId4, validApprovalPriceIdPool(3))))

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(4444))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetaDataWithApmLeadingRoom,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaDataWithApmLeadingRoom,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.AgodaAlreadyCheaper.value))
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly when OtherReason" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(20d, aValidRoomTypeId4, validApprovalPriceIdPool(3))))

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(4444))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetaDataWithApmLeadingRoom,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaDataWithApmLeadingRoom,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.OtherReason.value))
    }

    "buildApmNoPriceAdjustmentReasonMessage should return empty message correctly" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 1) -> Map(
          DateTime.parse("2015-10-05") -> AutoPriceMatchPriceInfo(90d, aValidRoomTypeId4, validApprovalPriceIdPool(3))),
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          DateTime.parse("2015-10-06") -> AutoPriceMatchPriceInfo(91d, aValidRoomTypeId1, validApprovalPriceIdPool(1)),
          DateTime.parse("2015-10-07") -> AutoPriceMatchPriceInfo(92d, 999999999, validApprovalPriceIdPool(2)),
          DateTime.parse("2015-10-09") -> AutoPriceMatchPriceInfo(94d, aValidRoomTypeId3, validApprovalPriceIdPool(4)),
          aValidStayDate1 -> AutoPriceMatchPriceInfo(3000d, aValidRoomTypeId4, validApprovalPriceIdPool(5)),
          aValidStayDate2 -> AutoPriceMatchPriceInfo(20d, aValidRoomTypeId4, validApprovalPriceIdPool(6)),
        ),
      )

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(4444))

      val apmRoomChannelSetting = Seq()

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetaDataWithApmLeadingRoom,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetaDataWithApmLeadingRoom,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))

      apmNoPriceAdjustmentReasons shouldEqual Set()
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly when hotelLevel in ApmBlackoutDate config is empty - OtherReason" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(20d, aValidRoomTypeId4, validApprovalPriceIdPool(3))))

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(4444))
      val mockMetadataWithBlackout = mockMetaDataWithApmLeadingRoom.copy(apmConfigs = Map(
        ApmConfigType.BlackoutDays.value -> ApmConfigHolder(
          globalLevel = Seq(aValidStayDate1.toString(blackoutDatePattern)),
          programLevel = Map(1 -> Seq(aValidStayDate1.toString(blackoutDatePattern))),
          hotelLevel = Seq.empty,
        )))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetadataWithBlackout,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetadataWithBlackout,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.OtherReason.value))
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly when stay date is in ApmBlackoutDate config - ApmBlackoutDateOnStayDate" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(20d, aValidRoomTypeId4, validApprovalPriceIdPool(3))))

      val mockMetaDataWithApmLeadingRoom = mockMetaData.copy(apmLeadingRoomAdjustmentIds = Seq(4444))
      val mockMetadataWithBlackout = mockMetaDataWithApmLeadingRoom.copy(apmConfigs = Map(
        ApmConfigType.BlackoutDays.value -> ApmConfigHolder(
          globalLevel = Seq(aValidStayDate1.toString(blackoutDatePattern)),
          programLevel = Map(1 -> Seq(aValidStayDate1.toString(blackoutDatePattern))),
          hotelLevel = Seq("2021-09-09"),
        )))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetadataWithBlackout,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetadataWithBlackout,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetaDataWithApmLeadingRoom,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.OtherReason.value))
    }

    "buildApmNoPriceAdjustmentReasonMessage should return message correctly when and stay date is in ApmBlackoutDate config - ApmBlackoutDateOnStayDate" in new ApmScope {
      val exp = List.empty
      val searchId = mockYplContext(exp).request.searchId

      val autoPriceMatchInfo: Map[AutoPriceMatchKeyEntry, Map[StayDate, AutoPriceMatchPriceInfo]] = Map(
        AutoPriceMatchKeyEntry(aValidRoomTypeId1, 2) -> Map(
          aValidStayDate1 -> AutoPriceMatchPriceInfo(20d, aValidRoomTypeId4, validApprovalPriceIdPool(3))))

      val mockMetadataWithBlackout = mockMetaData.copy(apmConfigs = Map(
        ApmConfigType.BlackoutDays.value -> ApmConfigHolder(
          globalLevel = Seq.empty,
          programLevel = Map.empty,
          hotelLevel = Seq(aValidStayDate1.toString, aValidStayDate2.toString, aValidStayDate3.toString),
        )))

      val apmRoomChannelSetting = Seq(
        MultipleAutoPriceMatchHolder(
          programId = 1,
          commissionDiscountChannelId = Some(1),
          commissionDiscountPercent = 1d,
          adjustmentChannelId = 1051,
          adjustmentDiscountPercent = 1d,
          statusId = ApmHotelStatus.Active,
          startDate = aValidStayDate1,
          endDate = Some(aValidStayDate2),
          apmAdjustmentDiscount = Seq.empty,
          apmCommissionDiscount = Seq.empty,
          programType = Some(0),
        ))

      val testHotelWithApmCommissionHolder = assignYplRoomEntryWithApmCommissionHolder(mockInputYplHotelWithAPMRooms(exp),
                                                                                       mockMetadataWithBlackout,
                                                                                       mockYplContext(exp))
      val (yplHotelWithAPMRooms, apmRoomsStartAtIndex) =
        processApmPriceAdjustment(testHotelWithApmCommissionHolder,
                                  mockMetadataWithBlackout,
                                  dispatchChannels = aValidYplDispatchChannels)(mockYplContext(exp))

      val yplHotelWithAPMRoomsWithApmInfo = yplHotelWithAPMRooms.copy(autoPriceMatchInfo = autoPriceMatchInfo)

      val (apmRooms, nonApmRooms) =
        yplHotelWithAPMRoomsWithApmInfo.rooms.partition(rc => YplMasterChannel(Channel.APM) == rc.channel)
      val elementsForLogs: ApmPricingFlowLogElement = ApmPricingFlowLogElement(yplHotelWithAPMRoomsWithApmInfo,
                                                                               mockMetadataWithBlackout,
                                                                               aValidYplDispatchChannels,
                                                                               apmRoomChannelSetting,
                                                                               true,
                                                                               apmRooms,
                                                                               nonApmRooms)
      val apmNoPriceAdjustmentReasons: Set[ApmNoPriceAdjustmentReasonLog] =
        buildApmNoPriceAdjustmentReasonMessage(elementsForLogs)(mockYplContext(exp))
      apmNoPriceAdjustmentReasons shouldEqual Set(
        ApmNoPriceAdjustmentReasonLog(searchId, 4, ApmNoPriceAdjustmentReason.ApmBlackoutDateOnStayDate.value))
    }

    "isSupplierIdValid should work correctly" in new ApmScope {
      val combination: Map[SupplierId, Boolean] = Map(
        -1 -> false, // direct connect is NOT enabled, so we don't care about the DMC ID
        DMC.YCS -> true, // direct connect is NOT enabled, so we don't care about the DMC ID

        DMC.Accor -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.BW -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.DHIL -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.Hyatt -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.IHGD -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.Marriott -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.OmniDhisco -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.Pegasus -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.RezTrip -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.PegasusRV2 -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.SynxisCCRS -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.SynxisECRS -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.Disney -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.MGM -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.Radisson -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.SynxisUCRS -> true, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.FC_Push -> false, // direct connect is enabled, so we expected the DMC_ID in ApmDC is valid
        DMC.YCS -> true, // direct connect is enabled, so we don't care about the DMC ID
      )

      combination.foreach { case (input, expectedResult) =>
        val exp = List.empty
        isSupplierIdValid(supplierId = input) shouldEqual expectedResult
      }
    }

    "getCommissionReductionAmt should work correctly" in new ApmScope {
      val aValidYplPrice: YplPrice =
        aValidPrice.copy(netExclusive = 10, margin = 10, tax = 10, fee = 10, processingFee = 10, taxOverSellEx = 10)
      val fxRate: Double = 1
      val room1: YPLRoom = aValidRoom.copy(prices = List(aValidYplPrice.copy(apmCommissionDiscountPercent = 2)))
      val room2: YPLRoom = aValidRoom.copy(prices = List(aValidYplPrice.copy(apmCommissionDiscountPercent = 2),
                                                         aValidYplPrice.copy(apmCommissionDiscountPercent = 0)))
      val room3: YPLRoom = aValidRoom.copy(prices = List(aValidYplPrice.copy(apmCommissionDiscountPercent = 2),
                                                         aValidYplPrice.copy(apmCommissionDiscountPercent = 2)))
      val combination: Map[(YPLRoom, Double), Double] = Map(
        (room1, fxRate) -> 1.2,
        (room2, fxRate) -> 1.2,
        (room3, fxRate) -> 2.4,
      )

      combination.foreach { case (input, expectedResult) =>
        getCommissionReductionAmt(input._1, input._2) shouldEqual expectedResult
      }
    }

    "sendCommissionReductionMetrics should work correctly when the request is for booking" in new ApmScope {
      val aValidYplPrice: YplPrice =
        aValidPrice.copy(netExclusive = 10, margin = 10, tax = 10, fee = 10, processingFee = 10, taxOverSellEx = 10)
      val room1: YPLRoom = aValidRoom.copy(prices = List(aValidYplPrice.copy(apmCommissionDiscountPercent = 2)))

      val mockAggregateReporter: AggregateReporter = mock[AggregateReporter]
      mockito.Mockito.doNothing().when(mockAggregateReporter).aggregate(any(), any(), any())

      val mockExchangeRateContext: ExchangeRateContext = mock[ExchangeRateContext]
      val bookingRequest: YplRequest = aValidYplRequest.copy(isBookingRequest = true)
      when(mockExchangeRateContext.getExchangeRate(any(), any())).thenReturn(Some(ExchangeRate()))
      val mockYplContext: YplContext = YplContext(aValidYplRequest).copy(aggregateReporter = mockAggregateReporter,
                                                                         exchangeRateCtx = mockExchangeRateContext,
                                                                         request = bookingRequest)
      val hotelEntry: YplRoomsWithEntry = yplRoomsWithEntry
      sendCommissionReductionMetrics(mockYplContext, hotelEntry, List(room1))

      verify(mockAggregateReporter, times(1)).aggregate(any(), any(), any())
    }

    "sendCommissionReductionMetrics should work correctly when the request is NOT for booking" in new ApmScope {
      val aValidYplPrice: YplPrice =
        aValidPrice.copy(netExclusive = 10, margin = 10, tax = 10, fee = 10, processingFee = 10, taxOverSellEx = 10)
      val room1: YPLRoom = aValidRoom.copy(prices = List(aValidYplPrice.copy(apmCommissionDiscountPercent = 2)))

      val mockAggregateReporter: AggregateReporter = mock[AggregateReporter]
      val mockExchangeRateContext: ExchangeRateContext = mock[ExchangeRateContext]
      val bookingRequest: YplRequest = aValidYplRequest.copy(isBookingRequest = false)
      mockito.Mockito.doNothing().when(mockAggregateReporter).aggregate(any(), any(), any())
      when(mockExchangeRateContext.getExchangeRate(any(), any())).thenReturn(None)

      val mockYplContext: YplContext = YplContext(aValidYplRequest).copy(aggregateReporter = mockAggregateReporter,
                                                                         exchangeRateCtx = mockExchangeRateContext,
                                                                         request = bookingRequest)
      val hotelEntry: YplRoomsWithEntry = yplRoomsWithEntry
      sendCommissionReductionMetrics(mockYplContext, hotelEntry, List(room1))

      verify(mockAggregateReporter, times(0)).aggregate(any(), any(), any())
    }

    "sendCommissionReductionMetrics should work correctly when some YplRooms without exchange rate" in new ApmScope {
      val aValidYplPrice: YplPrice =
        aValidPrice.copy(netExclusive = 10, margin = 10, tax = 10, fee = 10, processingFee = 10, taxOverSellEx = 10)
      val room1: YPLRoom = aValidRoom.copy(prices = List(aValidYplPrice.copy(apmCommissionDiscountPercent = 2)))

      val mockAggregateReporter: AggregateReporter = mock[AggregateReporter]
      val mockExchangeRateContext: ExchangeRateContext = mock[ExchangeRateContext]
      val bookingRequest: YplRequest = aValidYplRequest.copy(isBookingRequest = true)
      mockito.Mockito.doNothing().when(mockAggregateReporter).aggregate(any(), any(), any())
      when(mockExchangeRateContext.getExchangeRate(any(), any())).thenReturn(None)

      val mockYplContext: YplContext = YplContext(aValidYplRequest).copy(aggregateReporter = mockAggregateReporter,
                                                                         exchangeRateCtx = mockExchangeRateContext,
                                                                         request = bookingRequest)
      val hotelEntry: YplRoomsWithEntry = yplRoomsWithEntry
      sendCommissionReductionMetrics(mockYplContext, hotelEntry, List(room1))

      verify(mockAggregateReporter, times(1)).aggregate(any(), any(), any())
    }

    "getCommissionExcludingAgxAndWholesale will pass excludeWholesaleOrAgx as true always" in {
      var capturedExcludeWholesaleOrAgx: Option[Boolean] = None
      val serviceInTest = new PriceCalculationImpl
        with TaxCalculatorImpl
        with ApmCommissionDiscountServiceImpl
        with ApmPriceAdjustmentServiceImpl
        with CommissionServiceImpl
        with TaxPrototypeServiceImpl
        with ApmPricingFlow[YplContext] {
        override val priceBreakdownCalculator: PriceBreakdownCalculatorInterface =
          new PriceBreakdownCalculatorImpl(new TaxBreakdownCalculatorImpl(), new CommissionCalculatorImpl())

        override implicit def ec: ExecutionContext = ???

        override def getCommissionForPriceCalculation(commissionHolder: CommissionHolder,
                                                      stayDate: StayDate,
                                                      occupancy: TaxProtoTypeID,
                                                      isAgodaAgency: Boolean,
                                                      applicableMORPCandidateRoomParameters: MORPCandidateRoomParameters,
                                                      originalRateType: RateType,
                                                      targetRateType: RateType,
                                                      excludeWholesaleOrAgx: Boolean): AGXCommission = {
          capturedExcludeWholesaleOrAgx = Some(excludeWholesaleOrAgx)
          super.getCommissionForPriceCalculation(commissionHolder,
                                                 stayDate,
                                                 occupancy,
                                                 isAgodaAgency,
                                                 applicableMORPCandidateRoomParameters,
                                                 originalRateType,
                                                 targetRateType,
                                                 excludeWholesaleOrAgx)
        }
      }

      val yplRoom = aValidRoom
      val _ = serviceInTest.getCommissionExcludingAgxAndWholesale(yplRoom, aValidDateTime)
      capturedExcludeWholesaleOrAgx shouldEqual Some(true)
    }
  }
}
// scalastyle:on
