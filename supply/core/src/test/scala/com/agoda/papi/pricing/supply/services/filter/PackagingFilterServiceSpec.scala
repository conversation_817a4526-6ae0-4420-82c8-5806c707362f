package com.agoda.papi.pricing.supply.services.filter

import com.agoda.papi.enums.hotel.{DMC, PaymentModel, PaymentOption, StayPackageTypes}
import com.agoda.papi.enums.request.FeatureFlag
import com.agoda.papi.enums.room.CancellationGroup
import com.agoda.papi.pricing.supply.models.{SupplyHotelInfo, WithMeta}
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.pricing.supply.services.packaging.PackagingRoomFilterServiceImpl
import com.agoda.papi.ypl.models.pricing.proto.TimeInterval
import com.agoda.papi.ypl.models.{YPLHotel, YPLRoom, YPLTestDataBuilders, YplCancellationCondition}
import com.agoda.supplier.models.DMCID
import models.consts.ABTest
import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.ExecutionContext

class PackagingFilterServiceSpec
  extends SpecificationWithJUnit
    with YPLTestDataBuilders
    with SupplyModelTestDataBuilders {

  val service = new PackagingFilterServiceImpl(
    packagingRoomFilterService = new PackagingRoomFilterServiceImpl(),
  )(ExecutionContext.global)

  val ctxWithPackagingRequest = aValidSupplyContext.withBaseRequest(aValidSupplyBaseRequest.withIsPackagingFunnel(true))

  val hInfo: SupplyHotelInfo = aValidSupplyHotelInfo.withSuppliers(DMC.YCS.value).withBnplEnableMap(Set(DMC.YCS.value))
  val request = aValidSupplyBaseRequest withSearchId "searchId"

  val agencyRoom = aValidRoom withPaymentModel PaymentModel.Agency
  val merchantRoom = aValidRoom withPaymentModel PaymentModel.Merchant
  val merchantCommissionRoom = aValidRoom withPaymentModel PaymentModel.MerchantCommission
  val escapesRoom = aValidRoom withStayPackageType Some(StayPackageTypes.Escapes)
  val richContentRoom = aValidRoom withStayPackageType Some(StayPackageTypes.RichContent)
  val payAtHotelRoom = aValidRoom withPaymentOption Set(PaymentOption.PayAtHotel)

  val mockRooms: List[YPLRoom] = List(
    agencyRoom,
    merchantRoom,
    merchantCommissionRoom,
  )

  val mockHotel: YPLHotel = aValidHotel withRooms mockRooms

  "PackagingFilterService" should {
    "filterNonAgencyRooms" should {
      "filter agency rooms out" in {
        val agencyRoom = aValidRoom withPaymentModel PaymentModel.Agency
        val merchantRoom = aValidRoom withPaymentModel PaymentModel.Merchant
        val merchantCommissionRoom = aValidRoom withPaymentModel PaymentModel.MerchantCommission

        val mockRooms: List[YPLRoom] = List(
          agencyRoom,
          merchantRoom,
          merchantCommissionRoom,
        )
        val hotelWithMeta = aValidSupplyMetaUnit.build.copy(d = aValidHotel.withRooms(mockRooms).build)

        "normal request with FeatureFlag.EnableAgencySupplyForPackages" in {
          val data = service.Data(
            Seq(hotelWithMeta),
            aValidSupplyContext
              .withBaseRequest(
                aValidSupplyBaseRequest
                  .withFeatureFlags(List(FeatureFlag.EnableAgencySupplyForPackages))
                  .withIsPackagingFunnel(false),
              )
              .build,
            null,
          )
          val result = service.removeAgencyRoomsForPackaging(data).head.d.rooms
          result.size shouldEqual 3
        }

        "normal request withOut FeatureFlag.EnableAgencySupplyForPackages" in {
          val data = service.Data(
            Seq(hotelWithMeta),
            aValidSupplyContext
              .withBaseRequest(
                aValidSupplyBaseRequest.withFeatureFlags(List.empty).withIsPackagingFunnel(false),
              )
              .build,
            null,
          )
          val result = service.removeAgencyRoomsForPackaging(data).head.d.rooms
          result.size shouldEqual 3
        }

        "normal request with CARTPACK-510 user B side" in {
          val data = service.Data(
            Seq(hotelWithMeta),
            aValidSupplyContext
              .withBaseRequest(
                aValidSupplyBaseRequest.withBExperiment(ABTest.REMOVE_AGENCY_SUPPLY_FF).withIsPackagingFunnel(false),
              )
              .build,
            null,
          )
          val result = service.removeAgencyRoomsForPackaging(data).head.d.rooms
          result.size shouldEqual 3
        }

        "packaging request with FeatureFlag.EnableAgencySupplyForPackages" in {
          val data = service.Data(
            Seq(hotelWithMeta),
            aValidSupplyContext
              .withBaseRequest(
                aValidSupplyBaseRequest
                  .withFeatureFlags(List(FeatureFlag.EnableAgencySupplyForPackages))
                  .withIsPackagingFunnel(true),
              )
              .build,
            null,
          )
          val result = service.removeAgencyRoomsForPackaging(data).head.d.rooms
          result.size shouldEqual 3
        }

        "packaging request withOut FeatureFlag.EnableAgencySupplyForPackages" in {
          val data = service.Data(
            Seq(hotelWithMeta),
            aValidSupplyContext
              .withBaseRequest(
                aValidSupplyBaseRequest.withFeatureFlags(List.empty).withIsPackagingFunnel(true),
              )
              .build,
            null,
          )
          val result = service.removeAgencyRoomsForPackaging(data).head.d.rooms
          result.size shouldEqual 2
        }

        "packaging request with CARTPACK-510 user B side" in {
          val data = service.Data(
            Seq(hotelWithMeta),
            aValidSupplyContext
              .withBaseRequest(
                aValidSupplyBaseRequest.withBExperiment(ABTest.REMOVE_AGENCY_SUPPLY_FF).withIsPackagingFunnel(true),
              )
              .build,
            null,
          )
          val result = service.removeAgencyRoomsForPackaging(data).head.d.rooms
          result.size shouldEqual 3
        }

      }
    }

    "applyFiltersIfPackagingFunnelOrCartFeatureEnabled" should {
      val borRoom1 = aValidRoom withPaymentModel PaymentModel.Agency
      val borRoom2 = aValidRoom withPaymentModel PaymentModel.Merchant
      val borRoom3 = aValidRoom withPaymentModel PaymentModel.MerchantCommission withConfirmByMins (Some(3600))

      val mockBorRooms: List[YPLRoom] = List(
        borRoom1,
        borRoom2,
        borRoom3,
      )

      val mockBorHotel: YPLHotel = aValidHotel withRooms mockBorRooms

      // -----------------------------------------------------------------------------

      val hourlyRoom = aValidRoom withHourlyAvailableSlots (Seq(TimeInterval(2, "18:00"), TimeInterval(4, "20:00")))
      val nonHourlyRoom = aValidRoom.build

      val mockHourlyRoom: List[YPLRoom] = List(
        hourlyRoom,
        nonHourlyRoom,
      )
      val mockHourlyHotel: YPLHotel = aValidHotel withRooms mockHourlyRoom

      // -----------------------------------------------------------------------------

      val agencyRoom = aValidRoom withPaymentModel PaymentModel.Agency
      val merchantRoom = aValidRoom withPaymentModel PaymentModel.Merchant
      val merchantCommissionRoom = aValidRoom withPaymentModel PaymentModel.MerchantCommission
      val escapesRoom = aValidRoom withStayPackageType Some(StayPackageTypes.Escapes)

      val mockEscapeRooms: List[YPLRoom] = List(
        agencyRoom,
        merchantRoom,
        merchantCommissionRoom,
        escapesRoom,
      )

      val mockEscapeHotel: YPLHotel = aValidHotel withRooms mockEscapeRooms

      // -----------------------------------------------------------------------------

      val noCCRequiredOption: Set[PaymentOption] = Set(PaymentOption.NoCreditCard)
      val prePaymentOption: Set[PaymentOption] = Set(PaymentOption.PrepaymentRequired)
      val noCCReqMultipleOption: Set[PaymentOption] = Set(PaymentOption.NoCreditCard, PaymentOption.PayAtHotel)
      val prePaymentMultipleOption: Set[PaymentOption] = Set(PaymentOption.PayAtHotel, PaymentOption.PrepaymentRequired)
      val taxReceiptOption: Set[PaymentOption] = Set(PaymentOption.TaxReceipt)

      val mockNoCCRooms: List[YPLRoom] = List(
        aValidRoom.withPaymentOption(noCCRequiredOption).withPaymentModel(PaymentModel.Merchant),
        aValidRoom.withPaymentOption(prePaymentOption).withPaymentModel(PaymentModel.Merchant),
        aValidRoom.withPaymentOption(noCCReqMultipleOption).withPaymentModel(PaymentModel.Merchant),
        aValidRoom.withPaymentOption(prePaymentMultipleOption).withPaymentModel(PaymentModel.Merchant),
        aValidRoom.withPaymentOption(taxReceiptOption).withPaymentModel(PaymentModel.Merchant),
        aValidRoom.withPaymentOption(noCCReqMultipleOption).withPaymentModel(PaymentModel.Agency),
      )

      val mockNoCCHotel: YPLHotel = aValidHotel withRooms mockNoCCRooms

      "with normal hotel request, we should not filter BOR Rooms" in {
        val requestWithoutPackaging = aValidSupplyBaseRequest
        val ctx = aValidSupplyContext.withBaseRequest(requestWithoutPackaging)
        val data = service.Data(Seq(WithMeta(mockBorHotel, hInfo)(requestWithoutPackaging)), ctx, null)

        val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
        val roomsResult = result.head.d.rooms

        roomsResult.size shouldEqual 3
        roomsResult mustEqual mockBorRooms
      }

      "without valid cart request, we shouldn't remove BOR Rooms" in {
        val requestWithCartRequest = aValidSupplyBaseRequest
        val ctx = aValidSupplyContext.withBaseRequest(requestWithCartRequest)
        val data = service.Data(Seq(WithMeta(mockBorHotel, hInfo)(requestWithCartRequest)), ctx, null)

        val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
        val roomsResult = result.head.d.rooms

        roomsResult.size shouldEqual 3
      }

      "don't remove hourly hotels rooms without Cart Request" in {
        val cartRequest = aValidSupplyBaseRequest
        val ctx = aValidSupplyContext.withBaseRequest(cartRequest)
        val data = service.Data(Seq(WithMeta(mockHourlyHotel, hInfo)(cartRequest)), ctx, null)

        val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
        val roomsResult = result.head.d.rooms

        roomsResult.size shouldEqual 2
        roomsResult should containTheSameElementsAs(mockHourlyRoom)
      }

      "shouldn't remove rooms that is not agency and no CC require if there's no packaging request" in {
        val requestWithoutPackaging = aValidSupplyBaseRequest
        val ctx = aValidSupplyContext.withBaseRequest(requestWithoutPackaging)
        val data = service.Data(Seq(WithMeta(mockNoCCHotel, hInfo)(requestWithoutPackaging)), ctx, null)

        val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
        val roomsResult = result.head.d.rooms

        roomsResult.size shouldEqual 6
        roomsResult mustEqual mockNoCCRooms
      }

      "shouldn't remove rooms that is not agency and no CC require when there's empty cart request" in {
        val requestWithoutCartBaseRequest = aValidSupplyBaseRequest
        val ctx = aValidSupplyContext.withBaseRequest(requestWithoutCartBaseRequest)
        val data = service.Data(Seq(WithMeta(mockNoCCHotel, hInfo)(requestWithoutCartBaseRequest)), ctx, null)

        val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
        val roomsResult = result.head.d.rooms

        roomsResult.size shouldEqual 6
        roomsResult mustEqual mockNoCCRooms
      }

      "shouldn't remove rooms that is not agency and no CC require when there's empty cart request" in {
        val requestWithoutCartBaseRequest = aValidSupplyBaseRequest
        val ctx = aValidSupplyContext.withBaseRequest(requestWithoutCartBaseRequest)
        val data = service.Data(Seq(WithMeta(mockNoCCHotel, hInfo)(requestWithoutCartBaseRequest)), ctx, null)

        val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
        val roomsResult = result.head.d.rooms

        roomsResult.size shouldEqual 6
        roomsResult mustEqual mockNoCCRooms
      }

      "removeBORRooms" should {
        "with valid packaging request, we should remove BOR Rooms" in {
          val requestWithPackaging = aValidSupplyBaseRequest.withIsPackagingFunnel(true).withPackagingTokenNonEmpty(true)
          val ctx = aValidSupplyContext.withBaseRequest(requestWithPackaging)
          val data = service.Data(Seq(WithMeta(mockBorHotel, hInfo)(requestWithPackaging)), ctx, null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 2
        }

        "with multiHotel feature flag and considered as packaging request, we should remove BOR Rooms" in {
          val multiHotelRequest = aValidSupplyBaseRequest.withIsHotelPlusHotelRequest(true)
          val ctx = aValidSupplyContext.withBaseRequest(multiHotelRequest)
          val data = service.Data(Seq(WithMeta(mockBorHotel, hInfo)(multiHotelRequest)), ctx, null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 2
        }

        "with valid cart request, we should remove BOR Rooms" in {
          val requestWithCartRequest =
            aValidSupplyBaseRequest.withIsCartFunnelRequest(true).withIsCartFeatureEnabled(true)
          val ctx = aValidSupplyContext.withBaseRequest(requestWithCartRequest)
          val data = service.Data(Seq(WithMeta(mockBorHotel, hInfo)(requestWithCartRequest)), ctx, null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 2
        }
      }

      "removeHourlyHotelRooms" should {

        "remove hourly hotels rooms for packages request" in {
          val packagingRequest = aValidSupplyBaseRequest withIsPackagingFunnel true withPackagingTokenNonEmpty true
          val ctx = aValidSupplyContext.withBaseRequest(packagingRequest)
          val data = service.Data(Seq(WithMeta(mockHourlyHotel, hInfo)(packagingRequest)), ctx, null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 1
          roomsResult should containTheSameElementsAs(List(nonHourlyRoom))
        }

        "remove hourly hotels rooms for multi-hotel request" in {
          val multiHotelRequest = aValidSupplyBaseRequest.withIsHotelPlusHotelRequest(true)
          val ctx = aValidSupplyContext.withBaseRequest(multiHotelRequest)
          val data = service.Data(Seq(WithMeta(mockHourlyHotel, hInfo)(multiHotelRequest)), ctx, null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 1
          roomsResult should containTheSameElementsAs(List(nonHourlyRoom))
        }

        "remove hourly hotels rooms for cart request" in {
          val cartRequest = aValidSupplyBaseRequest.withIsCartFunnelRequest(true).withIsCartFeatureEnabled(true)
          val ctx = aValidSupplyContext.withBaseRequest(cartRequest)
          val data = service.Data(Seq(WithMeta(mockHourlyHotel, hInfo)(cartRequest)), ctx, null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 1
          roomsResult should containTheSameElementsAs(List(nonHourlyRoom))
        }
      }

      "removeSpecialStayPackageType" should {
        "should remove escapes packages rooms when it's a package request" in {
          val packagingBaseRequest =
            aValidSupplyBaseRequest withIsPackagingFunnel true withPackagingTokenNonEmpty true withFeatureFlags List(
              FeatureFlag.MultiHotel,
              FeatureFlag.EnableAgencySupplyForPackages)
          val ctx = aValidSupplyContext.withBaseRequest(packagingBaseRequest)
          val data = service.Data(Seq(
                                    WithMeta(mockHotel.build.copy(rooms = mockHotel.rooms ++ List(escapesRoom.build)),
                                             hInfo)(packagingBaseRequest)),
                                  ctx,
                                  null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 3
        }

        "should remove rich content offer rooms when it's a package request" in {
          val packagingBaseRequest =
            aValidSupplyBaseRequest withIsPackagingFunnel true withPackagingTokenNonEmpty true withFeatureFlags List(
              FeatureFlag.MultiHotel,
              FeatureFlag.EnableAgencySupplyForPackages)
          val ctx = aValidSupplyContext.withBaseRequest(packagingBaseRequest)
          val data = service.Data(Seq(
                                    WithMeta(mockHotel.copy(rooms = mockHotel.rooms ++ List(richContentRoom.build)),
                                             hInfo)(packagingBaseRequest)),
                                  ctx,
                                  null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 3
        }
      }

      "removeEscapePackages" should {
        "should remove escapes packages rooms when it's a package request" in {
          val packagingBaseRequest =
            aValidSupplyBaseRequest withIsPackagingFunnel true withPackagingTokenNonEmpty true withFeatureFlags List(
              FeatureFlag.MultiHotel,
              FeatureFlag.EnableAgencySupplyForPackages)
          val ctx = aValidSupplyContext.withBaseRequest(packagingBaseRequest)
          val data =
            service.Data(Seq(WithMeta(mockEscapeHotel.copy(rooms = mockEscapeHotel.rooms), hInfo)(packagingBaseRequest)),
                         ctx,
                         null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 3
        }

        "should remove Agoda Special Offer rooms when it's a cart request" in {
          val cartBaseRequest =
            aValidSupplyBaseRequest.withIsCartFunnelRequest(true).withIsCartFeatureEnabled(true) withFeatureFlags List(
              FeatureFlag.MultiHotel,
              FeatureFlag.EnableAgencySupplyForPackages)
          val ctx = aValidSupplyContext.withBaseRequest(cartBaseRequest)
          val data =
            service.Data(Seq(WithMeta(mockEscapeHotel.copy(rooms = mockEscapeHotel.rooms), hInfo)(cartBaseRequest)),
                         ctx,
                         null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 3
        }
      }

      "removeNoCCAndNoPrePaymentRequired" should {

        "should remove rooms that is not agency and does not require CC or pre payment when there's packaging request " in {
          val packagingBaseRequest = aValidSupplyBaseRequest withIsPackagingFunnel true withPackagingTokenNonEmpty true
          val ctx = aValidSupplyContext.withBaseRequest(packagingBaseRequest)
          val data = service.Data(Seq(WithMeta(mockNoCCHotel, hInfo)(packagingBaseRequest)), ctx, null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 3
        }

        "should remove rooms that is not agency and does not require CC or pre payment when packaging not exist but feature flag MultiHotel exist" in {
          val packagingBaseRequest = aValidSupplyBaseRequest.withIsHotelPlusHotelRequest(true)
          val ctx = aValidSupplyContext.withBaseRequest(packagingBaseRequest)
          val data = service.Data(Seq(WithMeta(mockNoCCHotel, hInfo)(packagingBaseRequest)), ctx, null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 3
        }

        "should remove rooms that is not agency and does not require CC or pre payment when there's cart request" in {
          val cartBaseRequest = aValidSupplyBaseRequest.withIsCartFunnelRequest(true).withIsCartFeatureEnabled(true)
          val ctx = aValidSupplyContext.withBaseRequest(cartBaseRequest)
          val data = service.Data(Seq(WithMeta(mockNoCCHotel, hInfo)(cartBaseRequest)), ctx, null)

          val result = service.applyFiltersIfPackagingFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 3
        }
      }
    }

    "applyFiltersIfFlightPlusHotelFunnel" should {
      "freeCxlOrAgodaManage" should {
        val merchantRoom = aValidRoom.withPaymentModel(PaymentModel.Merchant)
        val ycsFreeCxl = merchantRoom
          .withSupplierId(DMCID.YCS)
          .withCancellationCondition(YplCancellationCondition(CancellationGroup.FreeCancellation))
          .build
        val ycsNRCxl = merchantRoom
          .withSupplierId(DMCID.YCS)
          .withCancellationCondition(YplCancellationCondition(CancellationGroup.NonRefundable))
          .build
        val ycsPartialRCxl = merchantRoom
          .withSupplierId(DMCID.YCS)
          .withCancellationCondition(YplCancellationCondition(CancellationGroup.SpecialConditions))
          .build

        val nonYcsFreeCxl = merchantRoom
          .withSupplierId(DMCID.HTB)
          .withCancellationCondition(YplCancellationCondition(CancellationGroup.FreeCancellation))
          .build
        val nonYcsNRCxl = merchantRoom
          .withSupplierId(DMCID.HTB)
          .withCancellationCondition(YplCancellationCondition(CancellationGroup.NonRefundable))
          .build
        val nonYcsPartialRCxl = merchantRoom
          .withSupplierId(DMCID.HTB)
          .withCancellationCondition(YplCancellationCondition(CancellationGroup.SpecialConditions))
          .build
        val ycsRooms = List(ycsFreeCxl, ycsNRCxl, ycsPartialRCxl)
        val nonYcsRooms = List(nonYcsFreeCxl, nonYcsNRCxl, nonYcsPartialRCxl)
        val ycsHotel: YPLHotel = aValidHotel.withSupplierId(DMCID.YCS).withRooms(ycsRooms)
        val nonYcsHotel: YPLHotel = aValidHotel.withSupplierId(DMCID.HTB).withRooms(nonYcsRooms)

        "With Packaging request should filter rooms to allow [YCS Agoda Managed with all CXL] and [other suppliers with only free CXL] " in {

          val mockBaseRequest =
            aValidSupplyBaseRequest.withIsFlightPlusHotelRequest(true) withPackagingTokenNonEmpty true
          val ctx = aValidSupplyContext.withBaseRequest(mockBaseRequest)
          val data = service.Data(
            Seq(
              WithMeta(ycsHotel, hInfo)(request),
              WithMeta(nonYcsHotel, hInfo)(request),
              WithMeta(ycsHotel, hInfo.withYcsAllotmentNotManagedByAgoda(Some(true)))(request),
            ),
            ctx,
            null,
          )
          val result = service.applyFiltersIfFlightPlusHotelFunnel(data)
          result.find(_.d.supplierId == DMCID.YCS).get.d.rooms.size mustEqual 3
          result.find(_.d.supplierId == DMCID.HTB).get.d.rooms.size mustEqual 1
        }

        "MultiHotelSearch should allow all cancellation type regarding to any supplier" in {

          val mockBaseRequest = aValidSupplyBaseRequest.withFeatureFlags(List(FeatureFlag.MultiHotel))
          val ctx = aValidSupplyContext.withBaseRequest(mockBaseRequest)
          val data = service.Data(
            Seq(
              WithMeta(ycsHotel, hInfo)(request),
              WithMeta(nonYcsHotel, hInfo)(request),
              WithMeta(ycsHotel, hInfo.withYcsAllotmentNotManagedByAgoda(Some(true)))(request),
            ),
            ctx,
            null,
          )
          val result = service.applyFiltersIfFlightPlusHotelFunnel(data)
          result.find(_.d.supplierId == DMCID.YCS).get.d.rooms.size mustEqual 3
          result.find(_.d.supplierId == DMCID.HTB).get.d.rooms.size mustEqual 3
        }

        "MultiHotelSearch should not do CXL based filtering" in {

          val mockBaseRequest = aValidSupplyBaseRequest.withFeatureFlags(List(FeatureFlag.MultiHotel))
          val ctx = aValidSupplyContext.withBaseRequest(mockBaseRequest)
          val data =
            service.Data(Seq(WithMeta(ycsHotel, hInfo)(request), WithMeta(nonYcsHotel, hInfo)(request)), ctx, null)
          val result = service.applyFiltersIfFlightPlusHotelFunnel(data)
          result.find(_.d.supplierId == DMCID.YCS).get.d.rooms.size mustEqual 3
          result.find(_.d.supplierId == DMCID.HTB).get.d.rooms.size mustEqual 3
        }

        "HotelSearch should not do CXL based filtering" in {

          val mockBaseRequest = aValidSupplyBaseRequest
          val ctx = aValidSupplyContext.withBaseRequest(mockBaseRequest)
          val data =
            service.Data(Seq(WithMeta(ycsHotel, hInfo)(request), WithMeta(nonYcsHotel, hInfo)(request)), ctx, null)
          val result = service.applyFiltersIfFlightPlusHotelFunnel(data)
          result.find(_.d.supplierId == DMCID.YCS).get.d.rooms.size mustEqual 3
          result.find(_.d.supplierId == DMCID.HTB).get.d.rooms.size mustEqual 3
        }
      }
    }

    "applyFiltersIfHotelPlusHotelFunnel" should {
      "applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled" should {

        val ycsManagedByAgodaHotelInfo: SupplyHotelInfo =
          aValidSupplyHotelInfo withYcsAllotmentNotManagedByAgoda Some(false)

        val freeCancellationRoom = aValidRoom
          .withCancellationCondition(YplCancellationCondition(CancellationGroup.FreeCancellation))
          .withSupplierId(DMC.Booking.value)
          .build
        val ycsNonRefundableRoom = aValidRoom
          .withCancellationCondition(YplCancellationCondition(CancellationGroup.NonRefundable))
          .withSupplierId(DMC.YCS.value)
          .build
        val nonYcsNonRefundableRoom = aValidRoom
          .withCancellationCondition(YplCancellationCondition(CancellationGroup.NonRefundable))
          .withSupplierId(DMC.Booking.value)
          .build
        val ycsSpecialConditionRoom = aValidRoom
          .withCancellationCondition(YplCancellationCondition(CancellationGroup.SpecialConditions))
          .withSupplierId(DMC.YCS.value)
          .build
        val unknownCancellationGroupRoom =
          aValidRoom.withCancellationCondition(YplCancellationCondition(CancellationGroup.Unknown)).build
        val rooms: List[YPLRoom] = List(freeCancellationRoom,
                                        ycsNonRefundableRoom,
                                        nonYcsNonRefundableRoom,
                                        ycsSpecialConditionRoom,
                                        unknownCancellationGroupRoom)

        val dfHotel: YPLHotel = aValidHotel.withRooms(rooms)

        "do nothing if not MultiHotel Request" in {
          val baseRequest = aValidSupplyBaseRequest
          val ctx = aValidSupplyContext.withBaseRequest(baseRequest)
          val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

          val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 5
          roomsResult shouldEqual rooms
        }

        "do nothing if MultiHotel Request but NonRefundableExperiment is OFF" in {
          val baseRequest =
            aValidSupplyBaseRequest.withFeatureFlags(List(FeatureFlag.MultiHotel)).withIsHotelPlusHotelRequest(true)
          val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

          val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

          val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 5
          roomsResult shouldEqual rooms
        }

        "With no filter returns all rooms" in {
          val baseRequest =
            aValidSupplyBaseRequest.withFeatureFlags(List(FeatureFlag.MultiHotel)).withIsHotelPlusHotelRequest(true)
          val ctx = aValidSupplyContext.withBaseRequest(baseRequest)
          val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

          val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 5
          roomsResult shouldEqual rooms
        }

        "with FreeCancellation return all rooms" in {

          val baseRequest = aValidSupplyBaseRequest
            .withFeatureFlags(List(FeatureFlag.MultiHotel))
            .withIsHotelPlusHotelRequest(true)
            .withPackagingCancellationGroupFilter(Some(CancellationGroup.FreeCancellation))
          val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

          val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

          val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 5
          roomsResult shouldEqual rooms
        }

        "with SpecialConditions in request return FreeCancellation rooms and nonrefundable ycs managed by agoda rooms" in {
          val packagingFilterContext = Some(CancellationGroup.SpecialConditions)
          val baseRequest = aValidSupplyBaseRequest
            .withFeatureFlags(List(FeatureFlag.MultiHotel))
            .withIsHotelPlusHotelRequest(true)
            .withPackagingCancellationGroupFilter(packagingFilterContext)
          val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

          val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

          val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 3
          roomsResult should containTheSameElementsAs(
            List(freeCancellationRoom, ycsNonRefundableRoom, ycsSpecialConditionRoom))
        }

        "with Unknown in request return FreeCancellation rooms and nonrefundable ycs managed by agoda rooms" in {
          val packagingFilterContext = Some(CancellationGroup.Unknown)
          val baseRequest = aValidSupplyBaseRequest
            .withFeatureFlags(List(FeatureFlag.MultiHotel))
            .withIsHotelPlusHotelRequest(true)
            .withPackagingCancellationGroupFilter(packagingFilterContext)
          val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

          val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

          val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 3
          roomsResult should containTheSameElementsAs(
            List(freeCancellationRoom, ycsNonRefundableRoom, ycsSpecialConditionRoom))
        }

        "with NonRefundable in request return FreeCancellation rooms and nonrefundable ycs managed by agoda rooms" in {
          val packagingFilterContext = Some(CancellationGroup.NonRefundable)
          val baseRequest = aValidSupplyBaseRequest
            .withFeatureFlags(List(FeatureFlag.MultiHotel))
            .withIsHotelPlusHotelRequest(true)
            .withPackagingCancellationGroupFilter(packagingFilterContext)
          val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

          val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

          val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 3
          roomsResult should containTheSameElementsAs(
            List(freeCancellationRoom, ycsNonRefundableRoom, ycsSpecialConditionRoom))
        }

        "do nothing if exp CARTPACK-337 is B" in {
          val packagingFilterContext = Some(CancellationGroup.SpecialConditions)
          val baseRequest = aValidSupplyBaseRequest
            .withFeatureFlags(List(FeatureFlag.MultiHotel))
            .withIsHotelPlusHotelRequest(true)
            .withPackagingCancellationGroupFilter(packagingFilterContext)
          val ctx = aValidSupplyContext.withBaseRequest(baseRequest).withBExperiment(ABTest.REMOVE_MULTI_HOTEL_LOGIC)

          val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

          val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
          val roomsResult = result.head.d.rooms

          roomsResult.size shouldEqual 5
          roomsResult shouldEqual rooms
        }

        "With Cart Request" should {
          "do nothing without Packaging Filter Context" in {
            val baseRequest = aValidSupplyBaseRequest.withIsCartFunnelRequest(true).withIsCartFeatureEnabled(true)
            val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

            val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

            val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
            val roomsResult = result.head.d.rooms

            roomsResult.size shouldEqual 5
            roomsResult shouldEqual rooms
          }

          "with FreeCancellation return all rooms" in {
            val packagingFilterContext = Some(CancellationGroup.FreeCancellation)
            val baseRequest = aValidSupplyBaseRequest
              .withIsCartFunnelRequest(true)
              .withIsCartFeatureEnabled(true)
              .withPackagingCancellationGroupFilter(packagingFilterContext)
            val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

            val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

            val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
            val roomsResult = result.head.d.rooms

            roomsResult.size shouldEqual 5
            roomsResult shouldEqual rooms
          }

          "with SpecialConditions in request return FreeCancellation rooms and nonrefundable ycs managed by agoda rooms" in {
            val packagingFilterContext = Some(CancellationGroup.SpecialConditions)
            val baseRequest = aValidSupplyBaseRequest
              .withIsCartFunnelRequest(true)
              .withIsCartFeatureEnabled(true)
              .withPackagingCancellationGroupFilter(packagingFilterContext)
            val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

            val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

            val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
            val roomsResult = result.head.d.rooms

            roomsResult.size shouldEqual 3
            roomsResult should containTheSameElementsAs(
              List(freeCancellationRoom, ycsNonRefundableRoom, ycsSpecialConditionRoom))
          }

          "with Unknown in request return FreeCancellation rooms and nonrefundable ycs managed by agoda rooms" in {
            val packagingFilterContext = Some(CancellationGroup.Unknown)
            val baseRequest = aValidSupplyBaseRequest
              .withIsCartFunnelRequest(true)
              .withIsCartFeatureEnabled(true)
              .withPackagingCancellationGroupFilter(packagingFilterContext)
            val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

            val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

            val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
            val roomsResult = result.head.d.rooms

            roomsResult.size shouldEqual 3
            roomsResult should containTheSameElementsAs(
              List(freeCancellationRoom, ycsNonRefundableRoom, ycsSpecialConditionRoom))
          }

          "with NonRefundable in request return FreeCancellation rooms and nonrefundable ycs managed by agoda rooms" in {
            val packagingFilterContext = Some(CancellationGroup.NonRefundable)
            val baseRequest = aValidSupplyBaseRequest
              .withIsCartFunnelRequest(true)
              .withIsCartFeatureEnabled(true)
              .withPackagingCancellationGroupFilter(packagingFilterContext)
            val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

            val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

            val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
            val roomsResult = result.head.d.rooms

            roomsResult.size shouldEqual 3
            roomsResult should containTheSameElementsAs(
              List(freeCancellationRoom, ycsNonRefundableRoom, ycsSpecialConditionRoom))
          }
        }

        "Without Cart Request" should {
          "with SpecialConditions in request return return all rooms" in {
            val packagingFilterContext = Some(CancellationGroup.SpecialConditions)
            val baseRequest = aValidSupplyBaseRequest.withPackagingCancellationGroupFilter(packagingFilterContext)
            val ctx = aValidSupplyContext.withBaseRequest(baseRequest)

            val data = service.Data(Seq(WithMeta(dfHotel, ycsManagedByAgodaHotelInfo)(baseRequest)), ctx, null)

            val result = service.applyFiltersIfHotelPlusHotelFunnelOrCartFeatureEnabled(data)
            val roomsResult = result.head.d.rooms

            roomsResult.size shouldEqual 5
            roomsResult shouldEqual rooms
          }
        }
      }
    }
  }
}
