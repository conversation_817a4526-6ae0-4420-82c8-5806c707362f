package com.agoda.papi.pricing.supply.services.filter

import com.agoda.papi.pricing.metadata.SupplierCCMapping
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import org.specs2.mutable.SpecificationWithJUnit

class HotelSupplierMappingServiceSpec extends SpecificationWithJUnit with SupplyModelTestDataBuilders {

  val service = new HotelSupplierMappingService

  "HotelSupplierMappingService" should {

    "return HotelInfo with only DMC Master enabled suppliers" in {
      val suppliers = List(
        SupplierCCMapping(1, isDmcMasterEnabled = true),
        SupplierCCMapping(2, isDmcMasterEnabled = false),
      )
      val hotelInfo = aValidSupplyHotelInfo.withSupplierCCMappings(suppliers)
      val result = service.filterSupplierHotelWithDmcMaster(hotelInfo)
      result.suppliers must contain(exactly(SupplierCCMapping(1, isDmcMasterEnabled = true)))
    }

    "return HotelInfo with empty suppliers if no DMC Master enabled suppliers" in {
      val suppliers = List(
        SupplierCCMapping(1, isDmcMasterEnabled = false),
        SupplierCCMapping(2, isDmcMasterEnabled = false),
      )
      val hotelInfo = aValidSupplyHotelInfo.withSupplierCCMappings(suppliers)
      val result = service.filterSupplierHotelWithDmcMaster(hotelInfo)
      result.suppliers must beEmpty
    }

    "return HotelInfo with all suppliers if all are DMC Master enabled" in {
      val suppliers = List(
        SupplierCCMapping(1, isDmcMasterEnabled = true),
        SupplierCCMapping(2, isDmcMasterEnabled = true),
      )
      val hotelInfo = aValidSupplyHotelInfo.withSupplierCCMappings(suppliers)
      val result = service.filterSupplierHotelWithDmcMaster(hotelInfo)
      result.suppliers must containTheSameElementsAs(suppliers)
    }

    "return HotelInfo with empty suppliers if original suppliers list is empty" in {
      val suppliers = List.empty[SupplierCCMapping]
      val hotelInfo = aValidSupplyHotelInfo.withSupplierCCMappings(suppliers)
      val result = service.filterSupplierHotelWithDmcMaster(hotelInfo)
      result.suppliers must beEmpty
    }
  }
}
