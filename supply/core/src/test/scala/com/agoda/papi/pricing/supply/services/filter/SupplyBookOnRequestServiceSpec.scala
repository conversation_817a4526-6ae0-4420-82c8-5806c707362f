package com.agoda.papi.pricing.supply.services.filter

import com.agoda.commons.dynamic.state.State
import com.agoda.papi.enums.hotel.SupplierType
import com.agoda.papi.pricing.supply.configuration.BORSettingsProducer
import com.agoda.papi.pricing.supply.models.consts.ABTest
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.pricing.supply.models.utils.builders.SupplyContextMock
import com.agoda.papi.pricing.supply.models.{HotelId, RoomTypeId, SupplierId, SupplyHotelInfo}
import com.agoda.papi.pricing.supply.services.SupplyBookOnRequestServiceImpl
import com.agoda.papi.ypl.models.api.request.YplClientInfo
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{SupplierInfo, YPLRoom, YPLTestDataBuilders}
import org.joda.time.DateTime
import org.specs2.mock.{Mockito => MockitoSpec}
import org.specs2.mutable.SpecificationWithJUnit

class SupplyBookOnRequestServiceSpec
  extends SpecificationWithJUnit
    with SupplyModelTestDataBuilders
    with YPLTestDataBuilders
    with MockitoSpec {

  val config = configuration.appConfig
  val borSettingsProducer = new BORSettingsProducer(State.of(config), config)

  val service = new SupplyBookOnRequestServiceImpl(borSettingsProducer)

  "Book on request Flow" should {

    val hotelBor = aValidSupplyHotelInfo.withHotelId(1).withBor(true).build
    val hotelNonBor = aValidSupplyHotelInfo.withHotelId(2).withBor(false).build
    val data = Seq(hotelBor, hotelNonBor)

    "filter out BOR if HIDE_BOR_NEGATIVE_TEST is B for a property" in {
      val hotelInfo1: SupplyHotelInfo =
        aValidSupplyHotelInfo withHotelId (1) withSuppliers (DMC.YCS, DMC.BCOM, DMC.IHGD) withBor (true)
      val hotelInfo2: SupplyHotelInfo =
        aValidSupplyHotelInfo withHotelId (2) withSuppliers (DMC.YCS, DMC.BCOM, DMC.IHGD) withBor (true)
      val hotelInfo3: SupplyHotelInfo =
        aValidSupplyHotelInfo withHotelId (3) withSuppliers (DMC.YCS, DMC.BCOM, DMC.IHGD) withBor (false)
      val hotelInfo4: SupplyHotelInfo =
        aValidSupplyHotelInfo withHotelId (4) withSuppliers (DMC.YCS, DMC.BCOM, DMC.IHGD) withBor (true)

      val data = Seq(hotelInfo1, hotelInfo2, hotelInfo3, hotelInfo4)
      val ctx: SupplyContextMock =
        getContext(leadDays = 3, allowBor = true).withBExperiment(ABTest.HIDE_BOR_NEGATIVE_TEST)
      val result = service.filterBookOnRequest(data, ctx)

      result.size should_== 1
      result(0).hotelId should_== 3
    }

    "filter out BOR if lead time is less than or equal to the AB test setting and BOR is not allowed" in {
      val ctx: SupplyContextMock =
        getContext(leadDays = 3, allowBor = false).withBExperiment(ABTest.HIDE_BOR_FROM_SSR_FOR_CI_LESSTHEN_TWO)
      val result = service.filterBookOnRequest(data, ctx)

      result.size should_== 1
      result.head.hotelId should_== 2
    }

    "not filter out BOR if lead time is greater than the AB test setting and BOR is allowed and HIDE_BOR_NEGATIVE_TEST is A" in {
      val ctx: SupplyContextMock = getContext(leadDays = 4, allowBor = true)
        .withBExperiment(ABTest.HIDE_BOR_FROM_SSR_FOR_CI_LESSTHEN_TWO)
        .withAExperiment(ABTest.HIDE_BOR_NEGATIVE_TEST)
      val result = service.filterBookOnRequest(data, ctx)

      result.size should_== 2
      result.head.hotelId should_== 1
      result(1).hotelId should_== 2
    }

    "filter out BOR if not allowed, regardless of the AB test setting" in {
      val ctx: SupplyContextMock =
        getContext(leadDays = 2, allowBor = false).withAExperiment(ABTest.HIDE_BOR_FROM_SSR_FOR_CI_LESSTHEN_TWO)
      val result = service.filterBookOnRequest(data, ctx)

      result.size should_== 1
      result.head.hotelId should_== 2
    }

    "not filter out BOR hotels when Variant.B is active and leadDays is exactly at the threshold" in {
      val ctx: SupplyContextMock =
        getContext(leadDays = 2, allowBor = true).withBExperiment(ABTest.HIDE_BOR_FROM_SSR_FOR_CI_LESSTHEN_TWO)
      val result = service.filterBookOnRequest(data, ctx)

      result must not(contain(hotelBor))
      result must contain(hotelNonBor)
    }

    "include BOR hotels when Variant.A is active regardless of leadDays" in {
      val ctx: SupplyContextMock =
        getContext(leadDays = 1, allowBor = true).withAExperiment(ABTest.HIDE_BOR_FROM_SSR_FOR_CI_LESSTHEN_TWO)
      val result = service.filterBookOnRequest(data, ctx)

      result must contain(hotelBor)
      result must contain(hotelNonBor)
    }

    "include BOR hotels when leadDays are more than threshold under Variant.B and HIDE_BOR_NEGATIVE_TEST is A" in {
      val ctx: SupplyContextMock = getContext(leadDays = 4, allowBor = true)
        .withBExperiment(ABTest.HIDE_BOR_FROM_SSR_FOR_CI_LESSTHEN_TWO)
        .withAExperiment(ABTest.HIDE_BOR_NEGATIVE_TEST)
      val result = service.filterBookOnRequest(data, ctx)

      result must contain(hotelBor)
      result must contain(hotelNonBor)
    }
  }

  private def getContext(leadDays: Int, allowBor: Boolean = false): SupplyContextMock = {
    val baseRequest = aValidSupplyBaseRequest
      .withBookingDate(DateTime.now())
      .withCheckIn(DateTime.now().plusDays(leadDays))
      .withIsAllowBookOnRequest(Some(allowBor))

    aValidSupplyContext.withBaseRequest(baseRequest).build
  }

  "Book on request filter by valid confirmByMins" should {

    val ChinaCountryId = 191

    def genRoom(hotelId: HotelId,
                roomTypeId: RoomTypeId,
                supplierId: SupplierId,
                confirmByMins: Option[SupplierId]): YPLRoom = {
      val mockPrice = aValidPrice
      aValidRoom
        .withHotelId(hotelId)
        .withRoomTypeId(roomTypeId)
        .withSupplierId(supplierId)
        .withPrice(mockPrice)
        .withConfirmByMins(confirmByMins)
        .build
    }

    val mockRequest = aValidSupplyBaseRequest.withCInfo(
      YplClientInfo(language = 8, deviceTypeId = Some(1)),
    )

    val mockRequestWithAllowBor = mockRequest.withIsAllowBookOnRequest(Some(true))

    "Filter BOR Room in Experiment" should {

      val hotelId = 95240
      val supplierId = 2066

      "Check if isAllowBookOnRequest" in {
        val nonChinaHotel = aValidHotel
          .withRooms(
            List(
              genRoom(hotelId, 1000, supplierId, Option(30)),
              genRoom(hotelId, 1001, supplierId, Option(15)),
              genRoom(hotelId, 1002, supplierId, Option(0)),
            ))
          .build

        val chinaHotel = nonChinaHotel.withCountryId(ChinaCountryId).build

        "Not allow BoR if isAllowBookOnRequest is false" in {
          val ctx = aValidSupplyContext.withBaseRequest(mockRequest)
          val data = Seq(aValidSupplyMetaUnit.build.copy(d = chinaHotel))
          val resultHotel = service.filterBookOnRequestYpl(data, ctx).head.d
          resultHotel.rooms.length must_== 1
          resultHotel.features.bookOnRequest must_== false
          resultHotel.features.isContainChinaBOR must_== false
          resultHotel.rooms.forall(r => r.confirmByMins.exists(_ > 0)) must_== false
          resultHotel.rooms.forall(r => r.roomFeatures.isBookOnRequest == false) must_== true
        }

        "check if isAllowBookOnRequest is true and room belongs to Meituan" in {
          val meituanRooms = List(
            genRoom(hotelId, 1000, DMC.Meituan, Option(30)),
            genRoom(hotelId, 1001, DMC.Meituan, Option(15)),
            genRoom(hotelId, 1002, DMC.Meituan, Option(0)),
          )
          val meituanHotel = chinaHotel.copy(rooms = meituanRooms)

          val multipleSupplierRooms = List(
            genRoom(hotelId, 1000, DMC.Meituan, Option(30)),
            genRoom(hotelId, 1001, DMC.Meituan, Option(0)),
            genRoom(hotelId, 1004, DMC.YCS, Option(15)),
            genRoom(hotelId, 1005, DMC.YCS, Option(0)),
          )
          val meituanHotelWithMultipleSuppliers = chinaHotel.copy(rooms = multipleSupplierRooms)

          "Meituan BOR rooms should be shown in Meituan hotel" in {
            val ctx = aValidSupplyContext.withBaseRequest(mockRequestWithAllowBor)
            val data = Seq(aValidSupplyMetaUnit.build.copy(d = meituanHotel))
            val resultHotel = service.filterBookOnRequestYpl(data, ctx).head.d
            resultHotel.rooms.length must_== 3
            resultHotel.features.bookOnRequest must_== true
            resultHotel.features.isContainChinaBOR must_== true
            resultHotel.rooms.exists(r => r.confirmByMins.exists(_ > 0)) must_== true
            resultHotel.rooms.forall(r => r.roomFeatures.isBookOnRequest) must_== true
          }

          "Meituan BOR rooms should be shown in China hotel with multiple suppliers" in {
            val ctx = aValidSupplyContext.withBaseRequest(mockRequestWithAllowBor)
            val data = Seq(aValidSupplyMetaUnit.build.copy(d = meituanHotelWithMultipleSuppliers))
            val resultHotel = service.filterBookOnRequestYpl(data, ctx).head.d
            resultHotel.rooms.length must_== 4
            resultHotel.features.bookOnRequest must_== true
            resultHotel.features.isContainChinaBOR must_== true
            resultHotel.rooms.exists(r => r.confirmByMins.exists(_ > 0)) must_== true
            resultHotel.rooms.forall(r => r.roomFeatures.isBookOnRequest) must_== true
          }
        }
      }

    }

    "private function" should {

      // as valid Pull dmc bor where it contains confirmByMins > 0
      val mockMeituanRoom = aValidRoom.withSupplierId(DMC.Meituan).withConfirmByMins(Some(1))

      val mockYinjiRoom = aValidRoom.withSupplierId(DMC.Yinji).withConfirmByMins(Some(2))

      // as invalid Pull dmc bor where it contains confirmByMins > 0
      val mockXiwanRoom = aValidRoom.withSupplierId(DMC.Xiwan).withConfirmByMins(None)

      val mockHotel = aValidHotel
        .withRooms(
          List(
            mockMeituanRoom,
            mockYinjiRoom,
            mockXiwanRoom,
          ))
        .build

      "filterOutBorRooms" in {

        "whitelist supplierList: empty" in {
          val data = Seq(aValidSupplyMetaUnit.build.copy(d = mockHotel))
          val resultHotel = service.filterOutBorRooms(data, Set.empty).head.d
          resultHotel.rooms.map(_.supplierId) shouldEqual List(DMC.Xiwan)
        }

        "whitelist supplierList: Set(), so do not filter out supplier in the whitelist" in {
          val data = Seq(aValidSupplyMetaUnit.build.copy(d = mockHotel))
          val resultHotel1 = service.filterOutBorRooms(data, Set(DMC.Meituan)).head.d
          resultHotel1.rooms.map(_.supplierId) shouldEqual List(DMC.Meituan, DMC.Xiwan)

          val resultHotel2 = service.filterOutBorRooms(data, Set(DMC.Meituan, DMC.Yinji)).head.d
          resultHotel2.rooms.map(_.supplierId) shouldEqual List(DMC.Meituan, DMC.Yinji, DMC.Xiwan)
        }

        "filter with isReady status" in {

          "true" in {
            val mockDfHotelWithIsReadyTrue = mockHotel
              .withSuppliers(
                Map(
                  DMC.Meituan -> SupplierInfo(
                    id = DMC.Meituan,
                    supplierType = SupplierType.Pull,
                    isReady = true,
                    isAvailable = false,
                  )),
              )
              .withRooms(List.empty)
              .build

            mockDfHotelWithIsReadyTrue.isReady shouldEqual true
            val data = Seq(aValidSupplyMetaUnit.build.copy(d = mockDfHotelWithIsReadyTrue))
            service.filterOutBorRooms(data, Set.empty).size shouldEqual 0
          }

          "false" in {
            val mockDfHotelWithIsReadyFalse = mockHotel
              .withSuppliers(
                Map(
                  DMC.Meituan -> SupplierInfo(
                    id = DMC.Meituan,
                    supplierType = SupplierType.Pull,
                    isReady = false,
                    isAvailable = false,
                  )),
              )
              .withRooms(List.empty)
              .build

            mockDfHotelWithIsReadyFalse.isReady shouldEqual false
            val data = Seq(aValidSupplyMetaUnit.build.copy(d = mockDfHotelWithIsReadyFalse))
            service.filterOutBorRooms(data, Set.empty).size shouldEqual 1
          }

        }

      }

      "filterValidPullDmcBor" in {

        // china
        val mockChinaRequest = aValidSupplyBaseRequest.withCInfo(YplClientInfo(language = 8, deviceTypeId = Some(1)))

        val ctx = aValidSupplyContext.withBaseRequest(mockChinaRequest)

        val mockDfHotel1 = aValidHotel
          .withCountryId(ChinaCountryId)
          .withRooms(
            List(
              mockMeituanRoom,
              mockXiwanRoom,
            ))
          .build

        val mockDfHotel2 = aValidHotel
          .withCountryId(ChinaCountryId)
          .withRooms(
            List(
              mockXiwanRoom,
            ))
          .build

        val data = Seq(
          aValidSupplyMetaUnit.build.copy(d = mockDfHotel1),
          aValidSupplyMetaUnit.build.copy(d = mockDfHotel2),
        )

        val resultHotel = service.filterValidPullDmcBor(data, Set.empty)(ctx)

        resultHotel(0).d.rooms.size shouldEqual 2
        resultHotel(0).d.features.bookOnRequest shouldEqual true
        resultHotel(0).d.features.isContainChinaBOR shouldEqual true

        resultHotel(1).d.rooms.size shouldEqual 1
        resultHotel(1).d.features.bookOnRequest shouldEqual false
        resultHotel(1).d.features.isContainChinaBOR shouldEqual false
      }

      "China Book On Request" should {

        "isAllowChinaBOR" in {
          val mockBaseRequest = aValidSupplyBaseRequest
          val mockCIfno = YplClientInfo(deviceTypeId = Some(1), language = 8)
          val mockHotelInChina = aValidHotel.withCountryId(191).build
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno).build, Seq(mockHotelInChina)) must_== true
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno).build, Seq(aValidHotel.build)) must_== false
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(language = 1)).build,
                                  Seq(aValidHotel.build)) must_== false
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(deviceTypeId = Some(2))).build,
                                  Seq(mockHotelInChina)) must_== true
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(deviceTypeId = Some(3))).build,
                                  Seq(mockHotelInChina)) must_== true
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(deviceTypeId = Some(4))).build,
                                  Seq(mockHotelInChina)) must_== false
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(deviceTypeId = Some(5))).build,
                                  Seq(mockHotelInChina)) must_== true
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(deviceTypeId = Some(6))).build,
                                  Seq(mockHotelInChina)) must_== true
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(deviceTypeId = Some(7))).build,
                                  Seq(mockHotelInChina)) must_== true
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(deviceTypeId = Some(8))).build,
                                  Seq(mockHotelInChina)) must_== true
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(language = 1, deviceTypeId = Some(8))).build,
                                  Seq(mockHotelInChina)) must_== false
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(language = 8, deviceTypeId = Some(4))).build,
                                  Seq(mockHotelInChina)) must_== false
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(language = 8, deviceTypeId = Some(5))).build,
                                  Seq(aValidHotel.build)) must_== false
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(deviceTypeId = None)).build,
                                  Seq(mockHotelInChina)) must_== true
          service.isAllowChinaBOR(mockBaseRequest.withCInfo(mockCIfno.copy(deviceTypeId = Some(6))).build,
                                  Seq.empty) must_== false
        }

        "isChinaBOR" in {
          aValidRoom.withConfirmByMins(Some(0)).build.hasPositiveConfirmByMins must_== false
          aValidRoom.withConfirmByMins(None).build.hasPositiveConfirmByMins must_== false
          aValidRoom.withConfirmByMins(Some(30)).build.hasPositiveConfirmByMins must_== true
        }
      }

    }
  }
}
