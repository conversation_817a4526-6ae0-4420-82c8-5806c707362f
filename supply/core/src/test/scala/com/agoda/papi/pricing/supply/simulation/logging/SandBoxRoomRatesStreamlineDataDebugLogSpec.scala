package com.agoda.papi.pricing.supply.simulation.logging

import com.agoda.papi.enums.room.RateChannelSimulationFeatures
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.pricing.supply.models.{SupplyHotelInfo, WithMeta}
import com.agoda.papi.pricing.supply.simulation.models.HotelChannel
import com.agoda.papi.ypl.models.GUIDGeneratorSpec.aValidRateSilo
import com.agoda.supply.calc.proto.{ChannelRate, PriceDaily}
import org.specs2.mutable.SpecificationWithJUnit
import org.scalatestplus.mockito.MockitoSugar

class SandBoxRoomRatesStreamlineDataDebugLogSpec
  extends SpecificationWithJUnit
    with MockitoSugar
    with SupplyModelTestDataBuilders {

  "SandBoxRoomRatesStreamlineDataDebugLogSpec" should {
    "work as expected" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel()
      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(ChannelRate.defaultInstance.copy(
                    prices = Seq(PriceDaily.defaultInstance),
                  )),
                )),
            ),
            27916 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val data = SandBoxRoomRatesStreamlineDataDebugLog.apply(request, rateSilo, "test")
      data.size should_== 2

      data.head.variant should_== "A"
    }
    "work as expected for variant A_D" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        deactivateActiveRc = Some(2),
        features = Option(Seq(RateChannelSimulationFeatures.SameDayExperiment)),
      )
      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(ChannelRate.defaultInstance.copy(
                    prices = Seq(PriceDaily.defaultInstance),
                  )),
                )),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val data = SandBoxRoomRatesStreamlineDataDebugLog.apply(request, rateSilo, "test")
      data.size should_== 1
      val log = data.head

      log.simulated_rate_channel should_== 2
      log.variant should_== "A_D"
    }
    "work as expected for variant B_D" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxStackedRateChannels = Option(
          Seq(
            HotelChannel(
              channelId = 2,
              channelDiscount = Some(10.0),
              stackingType = None,
              stackingChannelDiscount = None,
            ))),
        features = Option(Seq(RateChannelSimulationFeatures.SameDayExperiment)),
      )
      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(ChannelRate.defaultInstance.copy(
                    prices = Seq(PriceDaily.defaultInstance),
                  )),
                )),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val data = SandBoxRoomRatesStreamlineDataDebugLog.apply(request, rateSilo, "test")
      data.size should_== 1
      val log = data.head

      log.simulated_rate_channel should_== 2
      log.variant should_== "B_D"
    }
    "work as expected for variant Unknown" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxRateChannelDiscounts = Some(Map(1 -> 12.0)),
        features = Option(Seq(RateChannelSimulationFeatures.SameDayExperiment)),
      )
      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(ChannelRate.defaultInstance.copy(
                    prices = Seq(PriceDaily.defaultInstance),
                  )),
                )),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val data = SandBoxRoomRatesStreamlineDataDebugLog.apply(request, rateSilo, "test")
      data.size should_== 1
      val log = data.head

      log.simulated_rate_channel should_== 0
      log.variant should_== "Unknown"
    }
    "work as expected for variant A" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel()
      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(ChannelRate.defaultInstance.copy(
                    prices = Seq(PriceDaily.defaultInstance),
                  )),
                )),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val data = SandBoxRoomRatesStreamlineDataDebugLog.apply(request, rateSilo, "test")
      data.size should_== 1
      val log = data.head

      log.simulated_rate_channel should_== 0
      log.variant should_== "A"
    }
    "work as expected for variant B" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxStackedRateChannels = Option(
          Seq(
            HotelChannel(
              channelId = 2,
              channelDiscount = Some(10.0),
              stackingType = None,
              stackingChannelDiscount = None,
            ))),
        features = Option(Seq(RateChannelSimulationFeatures.DisableDispatchingLogic)),
      )
      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(ChannelRate.defaultInstance.copy(
                    prices = Seq(PriceDaily.defaultInstance),
                  )),
                )),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val data = SandBoxRoomRatesStreamlineDataDebugLog.apply(request, rateSilo, "test")
      data.size should_== 1
      val log = data.head

      log.simulated_rate_channel should_== 2
      log.variant should_== "B"
    }
    "work as expected for variant Unknown" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxRateChannelDiscounts = Some(Map(1 -> 12.0)),
        features = Option(Seq(RateChannelSimulationFeatures.DisableDispatchingLogic)),
      )
      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(ChannelRate.defaultInstance.copy(
                    prices = Seq(PriceDaily.defaultInstance),
                  )),
                )),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val data = SandBoxRoomRatesStreamlineDataDebugLog.apply(request, rateSilo, "test")
      data.size should_== 1
      val log = data.head

      log.simulated_rate_channel should_== 0
      log.variant should_== "Unknown"
    }

  }
}
