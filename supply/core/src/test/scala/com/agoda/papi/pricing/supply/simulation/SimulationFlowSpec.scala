package com.agoda.papi.pricing.supply.simulation

import com.agoda.papi.enums.room.ChannelDiscountStackingType
import com.agoda.papi.enums.room.RateChannelSimulationFeatures.{DebugMode, DisableDispatchingLogic, SameDayExperiment}
import com.agoda.papi.pricing.supply.models.request.SupplyBaseRequest
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders.{
  aValidSupplyBaseRequest,
  toSupplyBaseRequestBuilder,
}
import com.agoda.papi.pricing.supply.simulation.models.HotelChannel
import org.mockito.Mockito.when
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

class SimulationFlowSpec extends SpecificationWithJUnit with Mockito {

  trait DummySimulationFlow {
    def simulationFlow(): Int
    def currentFlow(): Int
  }
  "SimulationFlow" should {
    "work properly when simulation flow is true" in {

      val mockedBaseRequest = mock[SupplyBaseRequest]

      when(mockedBaseRequest.simulationRequestData).thenReturn(None)
      when(mockedBaseRequest.getRateChannelSimulationData).thenReturn(None)
      val simulationFlow = new SimulationFlow {
        override protected def isSimulationFlow(request: SupplyBaseRequest): Boolean = true
      }

      simulationFlow.trySimulation(() => "simulation", () => "current")(mockedBaseRequest) must_== "simulation"
      simulationFlow.containFeatureFlag(mockedBaseRequest, DisableDispatchingLogic) should_== false
    }
    "work properly when simulation flow is false" in {

      val mockedBaseRequest = mock[SupplyBaseRequest]
      when(mockedBaseRequest.simulationRequestData).thenReturn(None)
      when(mockedBaseRequest.getRateChannelSimulationData).thenReturn(None)

      val simulationFlow = new SimulationFlow {
        override protected def isSimulationFlow(request: SupplyBaseRequest): Boolean = false
      }

      simulationFlow.trySimulation(() => "simulation", () => "current")(mockedBaseRequest) must_== "current"
      simulationFlow.containFeatureFlag(mockedBaseRequest, DisableDispatchingLogic) should_== false
    }

    "call simulation flow only once but not current flow " in {

      val mockedBaseRequest = mock[SupplyBaseRequest]
      when(mockedBaseRequest.simulationRequestData).thenReturn(None)
      when(mockedBaseRequest.getRateChannelSimulationData).thenReturn(None)

      val flows = mock[DummySimulationFlow]
      when(flows.simulationFlow()).thenReturn(1)

      val simulationFlow = new SimulationFlow {
        override protected def isSimulationFlow(request: SupplyBaseRequest): Boolean = true
      }

      simulationFlow.trySimulation(flows.simulationFlow, flows.currentFlow)(mockedBaseRequest) must_== 1
      there was one(flows).simulationFlow()
      there was no(flows).currentFlow()

      simulationFlow.containFeatureFlag(mockedBaseRequest, DisableDispatchingLogic) should_== false
    }
    "call current flow only once but not simulation flow" in {

      val mockedBaseRequest = mock[SupplyBaseRequest]
      when(mockedBaseRequest.simulationRequestData).thenReturn(None)
      when(mockedBaseRequest.getRateChannelSimulationData).thenReturn(None)

      val flows = mock[DummySimulationFlow]
      when(flows.currentFlow()).thenReturn(2)

      val simulationFlow = new SimulationFlow {
        override protected def isSimulationFlow(request: SupplyBaseRequest): Boolean = false
      }

      simulationFlow.trySimulation(flows.simulationFlow, flows.currentFlow)(mockedBaseRequest) must_== 2
      there was one(flows).currentFlow()
      there was no(flows).simulationFlow()
      simulationFlow.containFeatureFlag(mockedBaseRequest, DisableDispatchingLogic) should_== false
    }
    "containFeatureFlag should return true with mocked request with features" in {
      val request = aValidSupplyBaseRequest
        .withSimulationDataForRateChannel(
          pricingSandboxConfig = None,
          pricingSandboxRateChannelDiscounts = Some(Map.empty),
          pricingSandboxStackedRateChannels = Some(
            Seq(
              HotelChannel(
                channelId = 4,
                channelDiscount = Some(10d),
                stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
                stackingChannelDiscount = Some(10d),
              ),
            )),
          features = Some(Seq(DisableDispatchingLogic)),
        )
        .build
      val simulationFlow = new SimulationFlow {
        override protected def isSimulationFlow(request: SupplyBaseRequest): Boolean = request.isRateChannelSimulation
      }

      simulationFlow.containFeatureFlag(request, DisableDispatchingLogic) should_== true
    }
    "containFeatureFlag should return true with mocked request with SameDayExperiment" in {
      val request = aValidSupplyBaseRequest
        .withSimulationDataForRateChannel(
          pricingSandboxConfig = None,
          pricingSandboxRateChannelDiscounts = Some(Map.empty),
          pricingSandboxStackedRateChannels = Some(
            Seq(
              HotelChannel(
                channelId = 4,
                channelDiscount = Some(10d),
                stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
                stackingChannelDiscount = Some(10d),
              ),
            )),
          features = Some(Seq(SameDayExperiment)),
        )
        .build
      val simulationFlow = new SimulationFlow {
        override protected def isSimulationFlow(request: SupplyBaseRequest): Boolean = request.isRateChannelSimulation
      }
      simulationFlow.containFeatureFlag(request, DisableDispatchingLogic) should_== false
      simulationFlow.containFeatureFlag(request, SameDayExperiment) should_== true
      simulationFlow.hasDebugMode(request) should_== None
    }
    "containFeatureFlag should return true with mocked request with DebugMode" in {
      val request = aValidSupplyBaseRequest
        .withSimulationDataForRateChannel(
          pricingSandboxConfig = None,
          pricingSandboxRateChannelDiscounts = Some(Map.empty),
          pricingSandboxStackedRateChannels = Some(
            Seq(
              HotelChannel(
                channelId = 4,
                channelDiscount = Some(10d),
                stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
                stackingChannelDiscount = Some(10d),
              ),
            )),
          features = Some(Seq(DebugMode)),
        )
        .build
      val simulationFlow = new SimulationFlow {
        override protected def isSimulationFlow(request: SupplyBaseRequest): Boolean = request.isRateChannelSimulation
      }
      val flag = simulationFlow.hasDebugMode(request)
      flag should_== Some(DebugMode)

    }

  }
}
