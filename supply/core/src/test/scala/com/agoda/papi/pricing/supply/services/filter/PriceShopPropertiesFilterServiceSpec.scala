package com.agoda.papi.pricing.supply.services.filter

import com.agoda.papi.pricing.metadata.SupplierCCMapping
import com.agoda.papi.pricing.supply.models.SupplierPriceShopStatusMapping
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.pricing.supply.services.PriceShopPropertiesFilterServiceImpl
import org.specs2.mutable.SpecificationWithJUnit

class PriceShopPropertiesFilterServiceSpec extends SpecificationWithJUnit with SupplyModelTestDataBuilders {

  val suppliersForHotel1 = Seq(SupplierCCMapping(1), SupplierCCMapping(2))
  val suppliersForHotel2 = suppliersForHotel1 ++ Seq(SupplierCCMapping(3), SupplierCCMapping(4))

  val service = new PriceShopPropertiesFilterServiceImpl {
    override def isPriceShopHotelsFilterEnabled = true
  }

  val supplyHotelInfo1 = aValidSupplyHotelInfo
    .withHotelId(1)
    .withSupplierCCMappings(suppliersForHotel1)
    .withHotelYcsFlag(true)
    .withSupplierPriceShopStatusMapping(
      Seq(
        SupplierPriceShopStatusMapping(1, 0, Some(1)),
        SupplierPriceShopStatusMapping(2, 0, Some(0)),
      ),
    )

  val supplyHotelInfo2 = aValidSupplyHotelInfo
    .withHotelId(2)
    .withHotelYcsFlag(false)
    .withSupplierCCMappings(suppliersForHotel2)
    .withSupplierPriceShopStatusMapping(
      Seq(
        SupplierPriceShopStatusMapping(1, 0, Some(1)),
        SupplierPriceShopStatusMapping(2, 0, Some(1)),
        SupplierPriceShopStatusMapping(3, 0, Some(0)),
        SupplierPriceShopStatusMapping(4, 0, None),
      ),
    )

  "PriceShopPropertiesFilterService" should {
    "not filter out hotels if priceShopStatus is not 1 && feature flag is true" in {
      val actual =
        service.filterHotels(hotels = Seq(supplyHotelInfo1, supplyHotelInfo2), isRequestFromPriceShopper = true)
      val expectedSuppliersForHotel1 = Seq(SupplierCCMapping(1), SupplierCCMapping(2))
      val expectedSuppliersForHotel2 =
        Seq(SupplierCCMapping(1), SupplierCCMapping(2), SupplierCCMapping(3), SupplierCCMapping(4))
      actual.find(hotel => hotel.hotelId == supplyHotelInfo1.hotelId).map(_.suppliers) should_== Some(
        expectedSuppliersForHotel1)
      actual.find(hotel => hotel.hotelId == supplyHotelInfo2.hotelId).map(_.suppliers) should_== Some(
        expectedSuppliersForHotel2)
    }

    "filter out suppliers if priceShopStatus 1 but featureflag not passed" in {
      val actual =
        service.filterHotels(hotels = Seq(supplyHotelInfo1, supplyHotelInfo2), isRequestFromPriceShopper = false)
      val expectedSuppliersForHotel1 = Seq(SupplierCCMapping(2))
      val expectedSuppliersForHotel2 = Seq(SupplierCCMapping(3), SupplierCCMapping(4))
      actual.find(hotel => hotel.hotelId == supplyHotelInfo1.hotelId).map(_.suppliers) should_== Some(
        expectedSuppliersForHotel1)
      actual.find(hotel => hotel.hotelId == supplyHotelInfo2.hotelId).map(_.suppliers) should_== Some(
        expectedSuppliersForHotel2)
    }

    "not apply filter if feature is disabled" in {
      val service = new PriceShopPropertiesFilterServiceImpl {
        override def isPriceShopHotelsFilterEnabled = false
      }
      val actual =
        service.filterHotels(hotels = Seq(supplyHotelInfo1, supplyHotelInfo2), isRequestFromPriceShopper = true)
      actual == Seq(supplyHotelInfo1, supplyHotelInfo2)
    }
  }
}
