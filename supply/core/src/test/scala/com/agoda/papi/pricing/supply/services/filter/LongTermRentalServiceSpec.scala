package com.agoda.papi.pricing.supply.services.filter

import com.agoda.papi.pricing.supply.models.request.SupplyBaseRequest
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit
import services.longTermRental.{LongTermRentalConstraint, LongTermRentalServiceImpl}

class LongTermRentalServiceSpec extends SpecificationWithJUnit with SupplyModelTestDataBuilders {

  "LongTermRentalService" should {
    val longTermRentalService = new LongTermRentalServiceImpl {}

    "removeUnsafeProperties with unblock 90" should {
      val thaiHotel = aValidSupplyHotelInfo.withHotelId(1).withCityId(2)
      val thaiHotel2 = aValidSupplyHotelInfo.withHotelId(2).withCityId(2)
      val usHotels = aValidSupplyHotelInfo
        .withHotelId(3)
        .withCityId(2)
        .withCountryCode(LongTermRentalConstraint.UNITED_STATES_COUNTRY_CODE)

      "should filter US hotel for LOS > 30" in {
        implicit val baseRequest: SupplyBaseRequest = aValidSupplyBaseRequest
          .withFeatureFlags(List.empty)
          .withCheckIn(DateTime.now())
          .withCheckOut(DateTime.now().plusDays(44))
          .build

        val filteredHotelInfoList = longTermRentalService.removeUnsafeProperties(Seq(thaiHotel, thaiHotel2, usHotels))
        filteredHotelInfoList must haveSize(2)
      }

      "should not filter any property for LOS < 30" in {
        implicit val baseRequest: SupplyBaseRequest = aValidSupplyBaseRequest
          .withFeatureFlags(List.empty)
          .withCheckIn(DateTime.now())
          .withCheckOut(DateTime.now().plusDays(4))
          .build

        val filteredHotelInfoList = longTermRentalService.removeUnsafeProperties(Seq(thaiHotel, thaiHotel2, usHotels))
        filteredHotelInfoList must haveSize(3)
      }
    }
  }
}
