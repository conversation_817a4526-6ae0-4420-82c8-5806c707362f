package com.agoda.papi.pricing.supply.services.filter

import com.agoda.papi.cancellation.models.CancellationFee
import com.agoda.papi.enums.room.{CancellationFeeType, CancellationGroup}
import com.agoda.papi.pricing.supply.configuration.{DmcBlacklistSetting, DmcBlacklistSettingsProducer}
import com.agoda.papi.pricing.supply.models.request.SupplyBaseRequest
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders.{
  aValidSupplyBaseRequest,
  aValidSupplyContext,
  aValidSupplyMetaUnit,
  toSupplyBaseRequestBuilder,
}
import com.agoda.papi.pricing.supply.models.utils.builders.SupplyContextMock
import com.agoda.papi.pricing.supply.models.{MetaYPLHotel, MetaYPLHotels, SupplyHotelInfo, WithMeta}
import com.agoda.papi.ypl.models.api.request.YplFlagInfo
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{SupplierId, YPLHotel, YPLRoom, YPLTestDataBuilders, YplCancellationCondition}
import com.agoda.utils.monitoring.ReportingService
import concurrency.ExecutionContexts
import mocks.{PackagingFilterServiceMock, SupplyBookOnRequestServiceMock}
import models.consts.ABTest
import org.joda.time.{DateTime, Hours, LocalTime}
import org.mockito.Mockito.{times, verify, when}
import org.specs2.mock.Mockito.mock
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope
import utilhelpers.Measurements

class SupplyFilterServiceSpec extends SpecificationWithJUnit with YPLTestDataBuilders {

  val mockSupplyBookOnRequestServiceMock = SupplyBookOnRequestServiceMock
  val packagingFilterServiceMock = PackagingFilterServiceMock

  "filterByCutoffTime" should {
    val disabledDMCIds: Set[SupplierId] = Set(DMC.Accor, DMC.Hyatt, DMC.Marriott)
    val service: SupplyFilterServiceImpl = new SupplyFilterServiceImpl(
      dmcBlacklistSetting = mock[DmcBlacklistSettingsProducer],
      bookOnRequestService = mockSupplyBookOnRequestServiceMock,
      packagingFilterService = packagingFilterServiceMock,
    )(ec = ExecutionContexts.globalContext) {
      when(dmcBlacklistSetting.current).thenReturn(DmcBlacklistSetting(midnightPastDisableDmcids = disabledDMCIds))
    }

    val request = aValidSupplyBaseRequest
      .withCheckIn(DateTime.parse("2017-12-01"))
      .withCheckOut(DateTime.parse("2017-12-03"))
      .withBookingDate(DateTime.parse("2017-12-01T18:00+07:00"))

    val room: YPLRoom = aValidRoom
    val room1 = room withSupplierId DMC.YCS
    val room2 = room withSupplierId DMC.JTBWL
    val room3 = room withSupplierId DMC.Accor

    val roomsList = List[YPLRoom](room1, room2, room3)

    "Early Booking" should {
      val modifiedRequest: SupplyBaseRequest =
        request.copy(bookingDate = request.checkIn.minusDays(1).withTime(new LocalTime(18, 0, 0)))
      val flowContext = aValidSupplyContext.withBaseRequest(modifiedRequest)

      "Return all rooms when booking time is before cutoff time" in {
        val CUTOFF_TIME_OF_DAY_7PM = Hours.hours(19).toStandardSeconds.getSeconds * 1000

        val hotel: YPLHotel = aValidHotel withRooms roomsList withBookingCutoffTime Some(
          LocalTime.fromMillisOfDay(CUTOFF_TIME_OF_DAY_7PM)) withGmtOffset 7
        val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)

        val flowData = service.Data(Seq(metaHotel), flowContext, null)
        val result = service.filterByCutoffTime(flowData)
        result.head.d.rooms.size must_== roomsList.size
      }

      "Return all rooms when booking time is after cutoff time" in {
        val CUTOFF_TIME_OF_DAY_5PM = Hours.hours(17).toStandardSeconds.getSeconds * 1000

        val hotel: YPLHotel = aValidHotel withRooms roomsList withBookingCutoffTime Some(
          LocalTime.fromMillisOfDay(CUTOFF_TIME_OF_DAY_5PM)) withGmtOffset 7
        val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)

        val flowData = service.Data(Seq(metaHotel), flowContext, null)
        val result = service.filterByCutoffTime(flowData)
        result.head.d.rooms.size must_== roomsList.size
      }
    }

    "Same day booking" should {
      val modifiedRequest: SupplyBaseRequest =
        request.copy(bookingDate = request.checkIn.withTime(new LocalTime(18, 0, 0)))
      val flowContext = aValidSupplyContext.withBaseRequest(modifiedRequest)

      "Return all rooms when booking time is before cutoff time" in {
        val CUTOFF_TIME_OF_DAY_7PM = Hours.hours(19).toStandardSeconds.getSeconds * 1000

        val hotel: YPLHotel = aValidHotel withRooms roomsList withBookingCutoffTime Some(
          LocalTime.fromMillisOfDay(CUTOFF_TIME_OF_DAY_7PM)) withGmtOffset 7
        val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)

        val flowData = service.Data(Seq(metaHotel), flowContext, null)
        val result = service.filterByCutoffTime(flowData)
        result.head.d.rooms.size must_== roomsList.size
      }

      "Not return rooms when booking time is after cutoff time" in {
        val CUTOFF_TIME_OF_DAY_5PM = Hours.hours(17).toStandardSeconds.getSeconds * 1000

        val hotel: YPLHotel = aValidHotel withRooms roomsList withBookingCutoffTime Some(
          LocalTime.fromMillisOfDay(CUTOFF_TIME_OF_DAY_5PM)) withGmtOffset 7
        val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)

        val flowData = service.Data(Seq(metaHotel), flowContext, null)
        val result = service.filterByCutoffTime(flowData)
        result.head.d.rooms.size must_== 0
      }

      "Return all rooms when cutoff time is not defined for same day booking" in {
        val hotel: YPLHotel = aValidHotel withRooms roomsList withGmtOffset 7
        val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)

        val flowData = service.Data(Seq(metaHotel), flowContext, null)
        val result = service.filterByCutoffTime(flowData)
        result.head.d.rooms.size must_== roomsList.size
      }
    }

    "Next day booking" should {
      val modifiedRequest: SupplyBaseRequest =
        request.copy(bookingDate = request.checkIn.plusDays(1).withTime(new LocalTime(18, 0, 0)))
      val flowContext = aValidSupplyContext.withBaseRequest(modifiedRequest)

      "Not return rooms when booking time is before cutoff time" in {
        val CUTOFF_TIME_OF_DAY_7PM = Hours.hours(19).toStandardSeconds.getSeconds * 1000

        val hotel: YPLHotel = aValidHotel withRooms roomsList withBookingCutoffTime Some(
          LocalTime.fromMillisOfDay(CUTOFF_TIME_OF_DAY_7PM)) withGmtOffset 7
        val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)

        val flowData = service.Data(Seq(metaHotel), flowContext, null)
        val result = service.filterByCutoffTime(flowData)
        result.head.d.rooms.size must_== 0
      }

      "Not return rooms when booking time is after cutoff time" in {
        val CUTOFF_TIME_OF_DAY_5PM = Hours.hours(17).toStandardSeconds.getSeconds * 1000

        val hotel: YPLHotel = aValidHotel withRooms roomsList withBookingCutoffTime Some(
          LocalTime.fromMillisOfDay(CUTOFF_TIME_OF_DAY_5PM)) withGmtOffset 7
        val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)

        val flowData = service.Data(Seq(metaHotel), flowContext, null)
        val result = service.filterByCutoffTime(flowData)
        result.head.d.rooms.size must_== 0
      }

      "Return rooms with filtered disabled DMC when cutoff time is not defined and booking time before 5 AM" in {
        val updatedModifiedRequest =
          modifiedRequest.copy(bookingDate = modifiedRequest.bookingDate.withTime(new LocalTime(4, 30, 0)))
        val hotel: YPLHotel = aValidHotel withRooms roomsList withGmtOffset 7
        val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)

        val flowData = service.Data(Seq(metaHotel), flowContext.copy(baseRequest = updatedModifiedRequest), null)
        val result = service.filterByCutoffTime(flowData)
        result.head.d.rooms.size must_== roomsList.filterNot(r => disabledDMCIds.contains(r.supplierId)).size
      }

      "Not return rooms when cutoff time is not defined and booking time after 5 AM" in {
        val updatedModifiedRequest =
          modifiedRequest.copy(bookingDate = modifiedRequest.bookingDate.withTime(new LocalTime(5, 30, 0)))
        val hotel: YPLHotel = aValidHotel withRooms roomsList withGmtOffset 7
        val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)

        val flowData = service.Data(Seq(metaHotel), flowContext.copy(baseRequest = updatedModifiedRequest), null)
        val result = service.filterByCutoffTime(flowData)
        result.head.d.rooms.size must_== 0
      }
    }

    "Invalid booking" should {
      val modifiedRequest: SupplyBaseRequest = request.copy(bookingDate = request.checkIn.plusDays(2))
      val flowContext = aValidSupplyContext.withBaseRequest(modifiedRequest)

      "Not return rooms" in {
        val hotel: YPLHotel = aValidHotel withRooms roomsList withGmtOffset 7
        val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)

        val flowData = service.Data(Seq(metaHotel), flowContext, null)
        val result = service.filterByCutoffTime(flowData)
        result.head.d.rooms.size must_== 0
      }
    }
  }

  "filterRunOfHouseRooms" should {
    val service = new SupplyFilterServiceImpl(
      dmcBlacklistSetting = mock[DmcBlacklistSettingsProducer],
      bookOnRequestService = mockSupplyBookOnRequestServiceMock,
      packagingFilterService = packagingFilterServiceMock,
    )(ec = ExecutionContexts.globalContext)

    val request = aValidSupplyBaseRequest.build
    val flowContext = aValidSupplyContext.withBaseRequest(request)

    val room = aValidRoom
    val runOfHouseRoom = aValidRoom withRateCategory aValidRateCategory.copy(isRoomTypeNotGuarantee = true)
    val roomsList = List[YPLRoom](room, runOfHouseRoom)

    val hotel = aValidHotel withRooms roomsList
    val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)
    val flowData = service.Data(Seq(metaHotel), flowContext, null)

    "Not remove any room if request isAllowRoomTypeNotGuarantee is true" in {
      val modifiedRequest = request withFlagInfo YplFlagInfo(isAllowRoomTypeNotGuarantee = true)
      val modifiedFlowData = flowData.copy(ctx = flowContext.copy(baseRequest = modifiedRequest))

      val result = service.filterRunOfHouseRooms(modifiedFlowData)
      result.head.d.rooms should_== roomsList
    }

    "Remove all rooms that not guarantee RoomType" in {
      val result = service.filterRunOfHouseRooms(flowData)
      result.head.d.rooms should_== List(room)
    }
  }

  "filterRoomTypeId0" should {
    val service = new SupplyFilterServiceImpl(
      dmcBlacklistSetting = mock[DmcBlacklistSettingsProducer],
      bookOnRequestService = mockSupplyBookOnRequestServiceMock,
      packagingFilterService = packagingFilterServiceMock,
    )(ec = ExecutionContexts.globalContext)

    val request = aValidSupplyBaseRequest.build
    val flowContext = aValidSupplyContext.withBaseRequest(request)

    val room1 = aValidRoom.withRoomTypeId(1).build
    val room2 = aValidRoom.withRoomTypeId(0).build
    val roomsList = List[YPLRoom](room1, room2)

    val hotel = aValidHotel.withRooms(roomsList).build
    val metaHotel: MetaYPLHotel = aValidSupplyMetaUnit.build.copy(d = hotel)
    val flowData = service.Data(Seq(metaHotel), flowContext, null)

    "Remove all rooms with roomTypeId 0" in {
      val result = service.filterRoomTypeId0(flowData)
      result.head.d.rooms should_== List(room1)
    }

    "Remove hotel if it is ready and has all rooms with roomTypeId 0" in new Scope {
      val metaHotelWithOnlyRoomTypeId0Rooms: WithMeta[YPLHotel, SupplyHotelInfo] =
        metaHotel.copy(d = hotel.copy(rooms = List(room2)))
      val flowData: service.Data[Seq[MetaYPLHotel]] =
        service.Data(Seq(metaHotelWithOnlyRoomTypeId0Rooms), flowContext, null)

      val result: MetaYPLHotels = service.filterRoomTypeId0(flowData)
      result should_== Seq.empty
    }

    "Log the number of rooms with roomTypeId 0" in new Scope {
      val flowContext: SupplyContextMock = new SupplyContextMock(request) {
        override val reporter: ReportingService = mock[ReportingService]
      }
      val flowData: service.Data[Seq[MetaYPLHotel]] = service.Data(Seq(metaHotel), flowContext, null)
      service.filterRoomTypeId0(flowData)
      verify(flowContext.reporter, times(1))
        .report(Measurements.roomTypeId0, 1L, Map("hotelId" -> hotel.id.toString, "roomTypeId0Count" -> 1.toString))
    }

    "Not log if no rooms with roomTypeId 0" in new Scope {
      val flowContext: SupplyContextMock = new SupplyContextMock(request) {
        override val reporter: ReportingService = mock[ReportingService]
      }
      val metaHotelWithNoRoomTypeId0Rooms: WithMeta[YPLHotel, SupplyHotelInfo] =
        metaHotel.copy(d = hotel.copy(rooms = List(room1)))
      val flowData: service.Data[Seq[MetaYPLHotel]] =
        service.Data(Seq(metaHotelWithNoRoomTypeId0Rooms), flowContext, null)

      service.filterRoomTypeId0(flowData)
      verify(flowContext.reporter, times(0)).report("", 0L, Map.empty)
    }
  }

  "filterRoomBenefits" should {
    val service = new SupplyFilterServiceImpl(
      dmcBlacklistSetting = mock[DmcBlacklistSettingsProducer],
      bookOnRequestService = mockSupplyBookOnRequestServiceMock,
      packagingFilterService = packagingFilterServiceMock,
    )(ec = ExecutionContexts.globalContext)
    "return Benefits after filtration " in {
      val hotel = aValidHotel
        .withRooms(
          List(
            aValidRoom
              .withBenefits(
                List(aValidBenefit.withBenefitId(11).build,
                     aValidBenefit.withBenefitId(12).build,
                     aValidBenefit.withBenefitId(13).build))
              .build,
            aValidRoom.withBenefits(List(aValidBenefit.withBenefitId(11).build)).build,
            aValidRoom.withBenefits(List(aValidBenefit.withBenefitId(13).build)).build,
          ),
        )
        .build
      implicit val ctx =
        aValidSupplyContext.withBaseRequest(aValidSupplyBaseRequest.withBenefitIdsFilter(Set(11, 12)).build)

      val result = service.filterRoomBenefits(service.Data(Seq(aValidSupplyMetaUnit.build.copy(d = hotel)), ctx, null))

      result.head.d.rooms.size must_== 2 /// Last room eliminated
      result.head.d.rooms(0).benefits.map(_.id) must_== List(11, 12, 13) // other benefits not excluded
      result.head.d.rooms(1).benefits.map(_.id) must_== List(11)
    }

    "return Benefits after filtration with no rooms returned " in {
      val hotel = aValidHotel
        .withRooms(
          List(aValidRoom.withBenefits(List(aValidBenefit.withBenefitId(13).build)).build),
        )
        .build

      implicit val ctx =
        aValidSupplyContext.withBaseRequest(aValidSupplyBaseRequest.withBenefitIdsFilter(Set(11, 12)).build)
      val result = service.filterRoomBenefits(service.Data(Seq(aValidSupplyMetaUnit.build.copy(d = hotel)), ctx, null))

      result.head.d.rooms.size must_== 0 // No rooms selected
    }

    "return No rooms for empty rooms " in {
      val hotel = aValidHotel.withRooms(List()).build

      implicit val ctx =
        aValidSupplyContext.withBaseRequest(aValidSupplyBaseRequest.withBenefitIdsFilter(Set(11, 12)).build)
      val result = service.filterRoomBenefits(service.Data(Seq(aValidSupplyMetaUnit.build.copy(d = hotel)), ctx, null))
      result.head.d.rooms.size must_== 0
    }

    "return No rooms for rooms without benefits " in {
      val hotel = aValidHotel.withRooms(List(aValidRoom.withBenefits(List()).build)).build

      implicit val ctx =
        aValidSupplyContext.withBaseRequest(aValidSupplyBaseRequest.withBenefitIdsFilter(Set(11, 12)).build)
      val result = service.filterRoomBenefits(service.Data(Seq(aValidSupplyMetaUnit.build.copy(d = hotel)), ctx, null))
      result.head.d.rooms.size must_== 0
    }

    "return same rooms for empty benefits " in {
      val hotel = aValidHotel
        .withRooms(
          List(
            aValidRoom
              .withBenefits(List(aValidBenefit.withBenefitId(11).build,
                                 aValidBenefit.withBenefitId(12).build,
                                 aValidBenefit.withBenefitId(13).build))
              .build))
        .build

      implicit val ctx = aValidSupplyContext.withBaseRequest(aValidSupplyBaseRequest.build)
      val result = service.filterRoomBenefits(service.Data(Seq(aValidSupplyMetaUnit.build.copy(d = hotel)), ctx, null))
      result.head.d.rooms.size must_== 1
    }

  }

  "filterDirectConnectNonRefundableRooms" should {
    val service = new SupplyFilterServiceImpl(
      dmcBlacklistSetting = mock[DmcBlacklistSettingsProducer],
      bookOnRequestService = mockSupplyBookOnRequestServiceMock,
      packagingFilterService = packagingFilterServiceMock,
    )(ec = ExecutionContexts.globalContext)

    "return only refundable rooms and rooms that the difference between booking date and cancellation policy kick in date is 6 or more" in {
      implicit val ctx = aValidSupplyContext.withBaseRequest(
        aValidSupplyBaseRequest
          .withWhitelabelSetting(aValidwhitelabelSetting.copy(controlDirectConnectSupplyEnabled = true,
                                                              directConnectSupplierIds = Set(DMC.Accor),
                                                              allowedLeadDaysToNonRefundable = 6))
          .withBExperiment(ABTest.ENABLE_SELLING_DIRECT_CONNECT_SUPPLY_FOR_JTB)
          .withCheckIn(new DateTime(2025, 4, 11, 0, 0, 0))
          .withBookingDate(new DateTime(2025, 4, 1, 0, 0, 0))
          .build)

      val hotel = aValidHotel
        .withSupplierId(DMC.Accor)
        .withRooms(
          List(
            // the room will become non refundable on check-in date (11-4-2025) - (1-4-2025) = 10 days -> should stay
            aValidRoom
              .withSupplierId(DMC.Accor)
              .withCancellationFeeList(
                Some(List(CancellationFee(daysPrior = 0, cancellationFeeType = CancellationFeeType.None, feeAmount = 0))))
              .build,
            aValidRoom
              .withSupplierId(DMC.Accor)
              // the room will become non refundable one day before check-in date (10-4-2025) - (1-4-2025) = 9 days -> should stay
              .withCancellationFeeList(
                Some(List(CancellationFee(daysPrior = 1, cancellationFeeType = CancellationFeeType.None, feeAmount = 0))))
              .build,
            aValidRoom
              .withSupplierId(DMC.Accor)
              // the room will become non refundable two days before check-in date (9-4-2025) - (1-4-2025) = 8 days -> should stay
              .withCancellationFeeList(
                Some(List(CancellationFee(daysPrior = 2, cancellationFeeType = CancellationFeeType.None, feeAmount = 0))))
              .build,
            aValidRoom
              .withSupplierId(DMC.Accor)
              // the room will become non refundable three days before check-in date (8-4-2025) - (1-4-2025) = 7 days -> should stay
              .withCancellationFeeList(
                Some(List(CancellationFee(daysPrior = 3, cancellationFeeType = CancellationFeeType.None, feeAmount = 0))))
              .build,
            aValidRoom
              .withSupplierId(DMC.Accor)
              // the room will become non refundable four days before check-in date (7-4-2025) - (1-4-2025) = 6 days -> should stay
              .withCancellationFeeList(
                Some(List(CancellationFee(daysPrior = 4, cancellationFeeType = CancellationFeeType.None, feeAmount = 0))))
              .build,
            aValidRoom
              .withSupplierId(DMC.Accor)
              // the room will become non refundable 5 days before check-in date (6-4-2025) - (1-4-2025) = 5 day < allowed number -> should be filtered out
              .withCancellationFeeList(
                Some(List(CancellationFee(daysPrior = 5, cancellationFeeType = CancellationFeeType.None, feeAmount = 0))))
              .build,
            aValidRoom
              .withSupplierId(DMC.Accor)
              // the room will become non refundable 5 days before check-in date (5-4-2025) - (1-4-2025) = 4 day < allowed number -> should be filtered out
              .withCancellationFeeList(
                Some(List(CancellationFee(daysPrior = 6, cancellationFeeType = CancellationFeeType.None, feeAmount = 0))))
              .build,
            // YCS supply -> should stay
            aValidRoom
              .withSupplierId(DMC.YCS)
              .withCancellationFeeList(
                Some(List(CancellationFee(daysPrior = 6, cancellationFeeType = CancellationFeeType.None, feeAmount = 0))))
              .build,
            // YCS supply -> should stay
            aValidRoom
              .withSupplierId(DMC.YCS)
              .withCancellationCondition(YplCancellationCondition(CancellationGroup.NonRefundable))
              .build,
            // non refundable room -> should be filtered out
            aValidRoom
              .withSupplierId(DMC.Accor)
              .withCancellationCondition(YplCancellationCondition(CancellationGroup.NonRefundable))
              .build,
          ),
        )
        .build
      val result = service.filterDirectConnectNonRefundableRooms(
        service.Data(Seq(aValidSupplyMetaUnit.build.copy(d = hotel)), ctx, null))
      result.head.d.rooms.size must_== 7
    }

    "return all rooms in case JTBFP-615-V2 = A" in {
      implicit val ctx = aValidSupplyContext.withBaseRequest(
        aValidSupplyBaseRequest
          .withWhitelabelSetting(aValidwhitelabelSetting.copy(controlDirectConnectSupplyEnabled = true,
                                                              directConnectSupplierIds = Set(DMC.Accor),
                                                              allowedLeadDaysToNonRefundable = 6))
          .withAExperiment(ABTest.ENABLE_SELLING_DIRECT_CONNECT_SUPPLY_FOR_JTB)
          .withCheckIn(new DateTime(2025, 3, 28, 0, 0, 0))
          .withBookingDate(new DateTime(2025, 3, 26, 0, 0, 0))
          .build)

      val hotel = aValidHotel
        .withSupplierId(DMC.Accor)
        .withRooms(
          List(
            aValidRoom
              .withSupplierId(DMC.Accor)
              .withCancellationFeeList(
                Some(List(CancellationFee(daysPrior = 0, cancellationFeeType = CancellationFeeType.None, feeAmount = 0))))
              .build,
            aValidRoom
              .withSupplierId(DMC.Accor)
              .withCancellationCondition(YplCancellationCondition(CancellationGroup.NonRefundable))
              .build,
          ),
        )
        .build
      val result = service.filterDirectConnectNonRefundableRooms(
        service.Data(Seq(aValidSupplyMetaUnit.build.copy(d = hotel)), ctx, null))
      result.head.d.rooms.size must_== 2
    }
  }
}
