package com.agoda.papi.pricing.supply.ypl.mapper

import com.agoda.chainwholesale.ccas.protobuf.Ccas.AdjustmentType
import com.agoda.papi.enums.hotel.PaxType
import com.agoda.papi.enums.room.{ChildAgeRangeType, RatePlanStatus}
import com.agoda.papi.pricing.metadata.model.ApmConfigTypeId
import com.agoda.papi.pricing.metadata.utils.DFMetadataTestDataBuilders
import com.agoda.papi.pricing.metadata.{
  ApmConfig,
  ApmDeltaPercentage,
  MasterRoomInfo,
  SupplierCCMapping,
  SupplierHotel,
  AgencyNoCreditCard => AgencyNoCreditCardProto,
  ApmCommissionReductionEligibility => ApmCommissionReductionEligibilityProto,
  ChildAgeRange => ChildAgeRangeProto,
}
import com.agoda.papi.pricing.supply.commissions.models.ccas
import com.agoda.papi.pricing.supply.commissions.models.ccas.{AdjustmentProgram, CommissionAdjustmentResponse, Program}
import com.agoda.papi.pricing.supply.models.request.RatePlanInfo
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.pricing.supply.models.{
  AgePolicy,
  GrowthProgram,
  MetaSilo,
  SupplierDispatchChannelHolder,
  SupplyHotelInfo,
  WithMeta,
}
import com.agoda.papi.ypl.commission.apm.`enum`.ApmConfigType
import com.agoda.papi.ypl.commission.apm.models.{ApmCommissionReductionEligibilityHolder, ApmConfigHolder}
import com.agoda.papi.ypl.commission.growth.program.{
  GrowthProgramCommissionDetail,
  GrowthProgramFencingRule,
  GrowthProgramType,
}
import com.agoda.papi.ypl.models
import com.agoda.papi.ypl.models.enums.AgencyNoccMode
import com.agoda.papi.ypl.models.hotel.{
  AdjustedMargin,
  AgodaAgencyFeatures,
  AgePolicy => YPLAgePolicy,
  SupplierCCMapping => YPLSupplierCCMapping,
  SupplierHotel => YPLSupplierHotel,
}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{
  Adjustment,
  AgencyNoCreditCard,
  ChildAgeRange,
  CommissionAdjustmentPrograms,
  RateSilo,
  YPLTestDataBuilders,
  YplChannel,
  YplDispatchChannels,
  YplMasterChannel,
  YplRateFence,
  ApmDeltaPercentage => YplApmDeltaPercentage,
}
import com.agoda.supply.calc.proto.PropertyOffer
import com.google.protobuf.timestamp.Timestamp
import external.pricing.{DispatchInfo, HotelDispatchInfo}
import org.joda.time.DateTime
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

import scala.util.Try

class MetaConverterSpec
  extends SpecificationWithJUnit
    with SupplyModelTestDataBuilders
    with YPLTestDataBuilders
    with Mockito
    with DFMetadataTestDataBuilders {
  private val supplierHotel: SupplierHotel = aValidDFMetaSupplierHotel
  private val yplSupplierHotel: YPLSupplierHotel = MetaConverter.toYPLSupplierHotel(supplierHotel)

  private def toTimestamp(dt: String): Option[Timestamp] =
    Try(Timestamp.apply(DateTime.parse(dt).getMillis / 1000)).toOption

  private val supplyHotelInfo: SupplyHotelInfo = aValidSupplyHotelInfo
    .withHotelId(12345)
    .withCityId(9395)
    .withGmtOffset(34)
    .withProcessingFeeOption(1)
    .withSupplierMappings(Map(123 -> supplierHotel))
    .withSupplierCCMappings(Seq(SupplierCCMapping(
      123,
      isGenericCreditCardReq = true,
      isDomesticCreditCardRequired = true,
      isPaymentsFacilityCreditCardRequired = true,
      isDmcMasterEnabled = true,
      isBookOnRequest = true,
    )))
    .withCountryId(23)
    .withCountryCode("343")
    .withEnabledRooms(Map(1234L -> MasterRoomInfo(23L, 45d, 34)))
    .withIsChannelManaged(true)
    .withIsYcsEnabled(true)
    .withPaxSubmit(Some(PaxType.One))
    .withAgePolicy(AgePolicy(23))
    .withIsMyStaySupported(true)
    .withBookOnRequest(true)
    .withAgodaAgencyFeature(Some(AgodaAgencyFeatures(true, 23, Nil, 0)))
    .withIsSingleRoomNHA(true)
    .withIsChampagne(true)
    .withBookNowPayAtCheckIn(true)
    .withIgnoreRequestedNumberOfRoomsForNha(true)
    .withApplyDiscountsMultiplicatively(true)
    .withAdjustCommissions(Map(12 -> List(AdjustedMargin(12, 12, 2, None, None))))
    .withChainId(245)
    .withRoomBenefits(Map(23L -> List(2, 5)))
    .withIsMobileEnabledAPS(true)
    .withAPMCommissionReductionEligibility(Seq(
      ApmCommissionReductionEligibilityProto(year = 2021, month = 3, isCommissionReductionApplied = true)))
    .withChildAgeRanges(Seq(ChildAgeRangeProto(1, 1, 6)))
    .withAgencyNoCreditCardSetting(Seq(AgencyNoCreditCardProto(332, 1)))
    .withAPMLeadingRoomAdjustmentIds(Seq(1))
    .withAPMDeltaPercentage(Seq(ApmDeltaPercentage(1, Some(2), Some(3), Some(4))))
    .withJASORateCategoryLanguage(Map(1 -> Set(1, 6), 2 -> Set(1)))
    .withAPMConfigs(Map(
      ApmConfigType.AdjustmentCap.value -> Seq(
        ApmConfig(programId = 1, configLevel = 1, configValue = "30"),
        ApmConfig(programId = 54, configLevel = 1, configValue = "30"),
      ),
      ApmConfigType.BlackoutDays.value -> Seq(
        ApmConfig(programId = 1, configLevel = 1, configValue = "2023-01-22"),
        ApmConfig(programId = 1, configLevel = 1, configValue = "2023-01-23"),
        ApmConfig(programId = 1, configLevel = 2, configValue = "2023-02-01"),
        ApmConfig(programId = 1, configLevel = 3, configValue = "2023-04-22"),
        ApmConfig(programId = 1, configLevel = 3, configValue = "2023-05-22"),
        ApmConfig(programId = 54, configLevel = 1, configValue = "2023-01-22"),
        ApmConfig(programId = 54, configLevel = 1, configValue = "2023-01-23"),
        ApmConfig(programId = 54, configLevel = 2, configValue = "2023-03-01"),
        ApmConfig(programId = 54, configLevel = 2, configValue = "2023-03-02"),
        ApmConfig(programId = 54, configLevel = 3, configValue = "2023-04-22"),
        ApmConfig(programId = 54, configLevel = 3, configValue = "2023-05-22"),
      ),
      ApmConfigType.CommissionReduction.value -> Seq(
        ApmConfig(programId = 1, configLevel = 1, configValue = "1"),
        ApmConfig(programId = 1, configLevel = 2, configValue = "1"),
        ApmConfig(programId = 1, configLevel = 3, configValue = "2.55"),
        ApmConfig(programId = 54, configLevel = 1, configValue = "1"),
        ApmConfig(programId = 54, configLevel = 2, configValue = "0.75"),
        ApmConfig(programId = 54, configLevel = 3, configValue = "2.55"),
      ),
    ))
    .withRestrictedRatecategoryIds(Set(12345, 6789))
    .withGrowthPrograms(Map(
      YplRateFence("SG", 1, 1) -> Seq(GrowthProgram(
        badges = List("AGODA_PREFERRED"),
        commissions = List(
          GrowthProgramCommissionDetail(
            commission = 10.0,
            paymentTypeId = 0,
            programTypeId = 1,
            programSubTypeId = 1,
            freeTrial = false,
            rankingBoost = 10.0,
            date = None,
          ),
          GrowthProgramCommissionDetail(
            commission = 20.0,
            paymentTypeId = 0,
            programTypeId = 1,
            programSubTypeId = 1,
            freeTrial = false,
            rankingBoost = 20.0,
            date = None,
          ),
        ),
        fencingRules = Set(
          GrowthProgramFencingRule(
            programType = GrowthProgramType.AGP,
            channelIds = Set(1),
          ),
        ),
      )),
    ))
    .withPulseCampaignIdBlacklist(Seq(9999))
    .build

  "MetaConverter" should {

    "convert to ypl model correctly" in {

      val hotelApmConfigsExpected: Map[ApmConfigTypeId, ApmConfigHolder] = Map(
        ApmConfigType.AdjustmentCap.value -> ApmConfigHolder(
          globalLevel = Seq("30"),
          programLevel = Map.empty,
          hotelLevel = Seq.empty,
        ),
        ApmConfigType.BlackoutDays.value -> ApmConfigHolder(
          globalLevel = Seq("2023-01-22", "2023-01-23"),
          programLevel = Map(
            1 -> Seq("2023-02-01"),
            54 -> Seq("2023-03-01", "2023-03-02"),
          ),
          hotelLevel = Seq("2023-04-22", "2023-05-22"),
        ),
        ApmConfigType.CommissionReduction.value -> ApmConfigHolder(
          globalLevel = Seq("1"),
          programLevel = Map(
            1 -> Seq("1"),
            54 -> Seq("0.75"),
          ),
          hotelLevel = Seq("2.55"),
        ),
      )

      val hotelMeta = MetaConverter.toHotelMeta(supplyHotelInfo)
      hotelMeta.hotelId shouldEqual 12345
      hotelMeta.cityId shouldEqual 9395
      hotelMeta.gmtOffset shouldEqual 34
      hotelMeta.processingFeeOption shouldEqual 1
      hotelMeta.supplierMappings shouldEqual Map(123 -> yplSupplierHotel)
      hotelMeta.suppliers shouldEqual Set(
        YPLSupplierCCMapping(
          123,
          isGenericCreditCardReq = true,
          isDomesticCreditCardRequired = true,
          isPaymentsFacilityCreditCardRequired = true,
          isDmcMasterEnabled = true,
          isBookOnRequest = true,
        ))
      hotelMeta.countryId shouldEqual 23
      hotelMeta.countryCode shouldEqual "343"
      hotelMeta.isAPO shouldEqual false
      hotelMeta.enabledRoom shouldEqual Map(
        1234L -> MasterRoomInfo(masterRoomId = 23,
                                displayedRackRate = 45.0,
                                maxAllowedFreeChildren = None,
                                productOfferId = 34))
      hotelMeta.isChannelManaged shouldEqual true
      hotelMeta.isYcsEnabled shouldEqual true
      hotelMeta.paxSubmit shouldEqual Some(PaxType.One)
      hotelMeta.agePolicy shouldEqual YPLAgePolicy(23)
      hotelMeta.isMyStaySupported shouldEqual true
      hotelMeta.bookOnRequest shouldEqual true
      hotelMeta.agencyFeatures shouldEqual Some(AgodaAgencyFeatures(true, 23, Nil, 0))
      hotelMeta.isSingleRoomNHA shouldEqual true
      hotelMeta.isChampagne shouldEqual true
      hotelMeta.bookNowPayAtCheckIn shouldEqual true
      hotelMeta.ignoreRequestedNumberOfRoomsForNha shouldEqual true
      hotelMeta.applyDiscountsMultiplicatively shouldEqual true
      hotelMeta.adjustCommissions shouldEqual Map(12 -> List(AdjustedMargin(12, 12, 2, None, None)))
      hotelMeta.chainId shouldEqual 245
      hotelMeta.roomBenefits shouldEqual Map(23L -> List(2, 5))
      hotelMeta.isMobileEnabledAPS shouldEqual true
      hotelMeta.apmCommissionReductionEligibility shouldEqual Seq(
        ApmCommissionReductionEligibilityHolder(year = 2021, month = 3, isCommissionReductionApplied = true))
      hotelMeta.childAgeRanges shouldEqual Seq(
        ChildAgeRange(childAgeRangeId = 1, ageFrom = 1, ageTo = 6, childAgeRangeType = ChildAgeRangeType.NormalRange))
      hotelMeta.agencyNoCreditCardSetting shouldEqual Seq(
        AgencyNoCreditCard(supplierId = 332, agencyNoCcMode = AgencyNoccMode.ApplyNoccToAllBookings))
      hotelMeta.apmLeadingRoomAdjustmentIds shouldEqual Seq(1)
      hotelMeta.apmDeltaPercentage shouldEqual Map(1 -> YplApmDeltaPercentage(Some(2.0), Some(3.0), Some(4.0)))
      hotelMeta.augmentation shouldEqual false
      hotelMeta.jasoRateCategoryLanguage shouldEqual supplyHotelInfo.jasoRateCategoryLanguage
      hotelMeta.apmConfigs shouldEqual hotelApmConfigsExpected
      hotelMeta.restrictedRatecategoryIds shouldEqual supplyHotelInfo.restrictedRatecategoryIds
      hotelMeta.applicableGrowthPrograms shouldEqual Map(
        YplRateFence("SG", 1, 1) -> Seq(
          GrowthProgramCommissionDetail(
            commission = 10.0,
            paymentTypeId = 0,
            programTypeId = 1,
            programSubTypeId = 1,
            freeTrial = false,
            rankingBoost = 10.0,
            date = None,
          ),
          GrowthProgramCommissionDetail(
            commission = 20.0,
            paymentTypeId = 0,
            programTypeId = 1,
            programSubTypeId = 1,
            freeTrial = false,
            rankingBoost = 20.0,
            date = None,
          ),
        ),
      )
      hotelMeta.pulseCampaignIdBlacklist shouldEqual Seq(9999)
    }

    "overrideSupplierChannelsMapForGTTJTB when supplier is 28073 GTTJTB to 29014 JTBWL" in {
      val mockSupplierId = DMC.JTBAgoda
      val mockYplCompositeChannel = Set(YplChannel(1, Set.empty, 1))
      val expectedResult =
        DMC.JTBWL -> YplDispatchChannels(Set(YplChannel(1, Set.empty, 1)), Set(YplChannel(2, Set.empty, 2)))
      val result = MetaConverter.overrideSupplierChannelsMapForGTTJTB(mockSupplierId,
                                                                      mockYplCompositeChannel,
                                                                      Set(YplChannel(2, Set.empty, 2)))
      result shouldEqual expectedResult
    }
    "not overrideSupplierChannelsMapForGTTJTB when supplier is not 28073" in {
      val mockSupplierId = 332
      val mockYplCompositeChannel = Set(YplChannel(1, Set.empty, 1))
      val expectedResult = 332 -> YplDispatchChannels(Set(YplChannel(1, Set.empty, 1)), Set(YplChannel(2, Set.empty, 2)))
      val result = MetaConverter.overrideSupplierChannelsMapForGTTJTB(mockSupplierId,
                                                                      mockYplCompositeChannel,
                                                                      Set(YplChannel(2, Set.empty, 2)))
      result shouldEqual expectedResult
    }

    "return NotApplyNocc when agencyNoCreditCardSetting is not set to all booking" in {
      val hotelMeta =
        MetaConverter.toHotelMeta(supplyHotelInfo.withAgencyNoCreditCardSetting(Seq(AgencyNoCreditCardProto(332))))
      hotelMeta.agencyNoCreditCardSetting shouldEqual Seq(
        AgencyNoCreditCard(supplierId = 332, agencyNoCcMode = AgencyNoccMode.NotApplyNocc))
    }

    "return metadata with grace period eligible suppliers correctly" in {
      val hotelMeta = MetaConverter.toHotelMeta(supplyHotelInfo.withGracePeriodEligibleSuppliers(Set(3038)))
      hotelMeta.gracePeriodEligibleSuppliers must_== Set(3038)
    }

    "toHotelMeta return isStackableV2Enabled correctly" in {
      def test(isStackableV2Enabled: Option[Boolean], expectedValue: Boolean) = {
        val src = supplyHotelInfo.withIsStackableV2Enabled(isStackableV2Enabled)
        val hotelMeta = MetaConverter.toHotelMeta(src)
        hotelMeta.isStackableV2Enabled must_== expectedValue
      }
      test(Some(false), false)
      test(Some(true), true)
      test(None, true)
    }
    "toHotelMeta map CCAS response correctly" in {

      val p1 =
        Program("P1", ccas.Adjustment(2.0d, AdjustmentType.NET_AND_SELL_PRICE), duplicateRoomAndDiscount = false, None)
      val p2 =
        Program("P2", ccas.Adjustment(3.0d, AdjustmentType.NET_AND_SELL_PRICE), duplicateRoomAndDiscount = false, None)
      val p3 = Program("P3",
                       ccas.Adjustment(4.0d, AdjustmentType.NET_AND_SELL_PRICE),
                       duplicateRoomAndDiscount = false,
                       Some(0L))
      val p4 = Program("P4",
                       ccas.Adjustment(5.0d, AdjustmentType.NET_AND_SELL_PRICE),
                       duplicateRoomAndDiscount = true,
                       Some(10001L))
      val p5 = Program("P5",
                       ccas.Adjustment(6.0d, AdjustmentType.NET_AND_SELL_PRICE),
                       duplicateRoomAndDiscount = true,
                       Some(10002L))
      val p6 =
        Program("P6", ccas.Adjustment(7.0d, AdjustmentType.NET_AND_SELL_PRICE), duplicateRoomAndDiscount = true, None)

      val hotelId1 = 12345
      val hotelId2 = 124
      val channelId1 = 3458
      val channelId2 = 3459
      val channelId3 = 3460
      val channelId4 = 3461
      val adjustmentResponse1 = CommissionAdjustmentResponse(
        hotelId1,
        List(
          AdjustmentProgram(channelId1, List(p1, p2)),
          AdjustmentProgram(channelId3, List(p3)),
        ),
      )
      val adjustmentResponse2 = CommissionAdjustmentResponse(
        hotelId2,
        List(
          AdjustmentProgram(channelId2, List(p4)),
          AdjustmentProgram(channelId4, List(p5, p6)),
        ),
      )
      val hotelMeta = MetaConverter.toHotelMeta(supplyHotelInfo.withCcasPrograms(adjustmentResponse1))
      hotelMeta.ccasAdjustmentPrograms must beSome(
        CommissionAdjustmentPrograms(
          hotelId1,
          List(
            models.AdjustmentProgram(channelId1,
                                     List(
                                       models.Program("P1", Adjustment(2.0d), false, None),
                                       models.Program("P2", Adjustment(3.0d), false, None),
                                     )),
            models.AdjustmentProgram(channelId3, List(models.Program("P3", Adjustment(4.0d), false, Some(0L)))),
          ),
        ))
      val hotelMeta2 =
        MetaConverter.toHotelMeta(supplyHotelInfo.withHotelId(hotelId2).withCcasPrograms(adjustmentResponse2))
      hotelMeta2.ccasAdjustmentPrograms must beSome(
        CommissionAdjustmentPrograms(
          hotelId2,
          List(
            models.AdjustmentProgram(channelId2, List(models.Program("P4", Adjustment(5.0d), true, Some(10001L)))),
            models.AdjustmentProgram(channelId4,
                                     List(
                                       models.Program("P5", Adjustment(6.0d), true, Some(10002L)),
                                       models.Program("P6", Adjustment(7.0d), true, None),
                                     )),
          ),
        ))
    }

    "toYPLWithMetaSilo" should {
      val mockRoomRate: Seq[com.agoda.supply.calc.proto.RoomRateCategory] =
        Seq(aValidPropOfferRoomRateCategory.withRoomTypeId(1L), aValidPropOfferRoomRateCategory.withRoomTypeId(2L))
      val mockChannelRate: Seq[com.agoda.protobuf.cache.ChannelRoomRate] =
        Seq(aValidOTAChannelRoomRate.withRoomTypeId(3L), aValidOTAChannelRoomRate.withRoomTypeId(4L))
      val rateSilo = RateSilo(
        1,
        DateTime.now,
        1,
        Map(DMC.CNBooking -> aValidOTAHotelPrice.withChannelRoomRate(mockChannelRate)),
        Map(DMC.BCOM -> PropertyOffer.defaultInstance.withRoomRates(mockRoomRate)),
      )
      val mockEnabledRooms: Map[Long, MasterRoomInfo] =
        (1 to 6).map(_.toLong -> supplyHotelInfo.enabledRoom.head._2).toMap
      val mockHotelInfo = supplyHotelInfo.withEnabledRooms(mockEnabledRooms)
      val MetaSilo: MetaSilo =
        WithMeta.apply[RateSilo, SupplyHotelInfo](rateSilo, mockHotelInfo)(aValidSupplyBaseRequest)

      "return isStackableV2Enabled correctly" in {
        def test(isStackableV2Enabled: Option[Boolean], expectedValue: Boolean) = {
          val mockHotelInfo = supplyHotelInfo.withIsStackableV2Enabled(isStackableV2Enabled)
          val MetaSilo: MetaSilo =
            WithMeta.apply[RateSilo, SupplyHotelInfo](rateSilo, mockHotelInfo)(aValidSupplyBaseRequest)
          val hotelMeta = MetaConverter.toYPLWithMetaSilo(MetaSilo)
          hotelMeta.meta.isStackableV2Enabled must_== expectedValue
        }

        test(Some(false), false)
        test(Some(true), true)
        test(None, true)
      }

      "populate fenced supplier dispatch status" in {
        val hotel = supplyHotelInfo.withSupplierCCMappings(
          Seq(
            SupplierCCMapping(DMC.YCS, false, false, true, true, false),
            SupplierCCMapping(DMC.JTBAgoda, false, false, true, true, false),
          ))
        val fencedRequestedRatePlans = Map(
          YplRateFence("TH", 1, 22) -> Set(
            RatePlanInfo(YplMasterChannel.RTL, RatePlanStatus.Requested),
            RatePlanInfo(YplMasterChannel.APS, RatePlanStatus.Helper),
          ),
          YplRateFence("SG", 1, 1) -> Set(
            RatePlanInfo(YplMasterChannel.RTL, RatePlanStatus.Requested),
            RatePlanInfo(YplMasterChannel.APS, RatePlanStatus.Requested),
          ),
        )
        val someDispatchInfo = Some(
          HotelDispatchInfo(
            1L,
            Seq(
              DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 0, fencedRatePair = aValidFencedRatePair.build),
              DispatchInfo(1, DMC.JTBAgoda, YplMasterChannel.RTL, 0, fencedRatePair = aValidFencedRatePair.build),
            ),
          ))
        val MetaSilo: MetaSilo = WithMeta
          .apply[RateSilo, SupplyHotelInfo](rateSilo, hotel)(aValidSupplyBaseRequest)
          .copy(
            supplierDispatchChannelHolder = SupplierDispatchChannelHolder(
              requestedRatePlans = List.empty,
              hotelDispatchInfo = someDispatchInfo,
              supplierIds = hotel.getSuppliers,
              fencedRequestedRatePlans = fencedRequestedRatePlans,
            ),
          )
        val result = MetaConverter.toYPLWithMetaSilo(MetaSilo)
        result.fencedSupplierChannelsMap should_== Map(
          YplRateFence("TH", 1, 22) -> Map(
            DMC.YCS -> YplDispatchChannels(Set(YplMasterChannel.RTL, YplMasterChannel.APS), Set(YplMasterChannel.APS)),
            DMC.JTBWL -> YplDispatchChannels(Set(YplMasterChannel.RTL, YplMasterChannel.APS), Set(YplMasterChannel.APS)),
          ),
          YplRateFence("SG", 1, 1) -> Map(
            DMC.YCS -> YplDispatchChannels(Set(YplMasterChannel.RTL, YplMasterChannel.APS)),
            DMC.JTBWL -> YplDispatchChannels(Set(YplMasterChannel.RTL, YplMasterChannel.APS)),
          ),
        )
      }
    }
  }
}
