package com.agoda.papi.pricing.supply.simulation.logging

import com.agoda.papi.pricing.supply.models.{SupplyHotelInfo, WithMeta => SupplyWithMeta}
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders.aValidSupplyBaseRequest
import com.agoda.papi.pricing.supply.simulation.ratechannel.SimulationRateChannelMessageSender
import com.agoda.papi.ypl.models.GUIDGeneratorSpec.aValidRateSilo
import com.agoda.supply.calc.proto.{ChannelRate, PriceDaily}
import com.agoda.utils.hadoop.HadoopSender
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito
import org.specs2.mutable.SpecificationWithJUnit
import org.scalatestplus.mockito.MockitoSugar

class SimulationRateChannelMessageSenderSpec extends SpecificationWithJUnit with MockitoSugar {

  "SimulationRateChannelMessageSenderSpec" should {
    "postRoomRatesLogs should not send message for non sandbox request" in {
      val sender = mock[HadoopSender]
      val request = aValidSupplyBaseRequest.build

      val rateSilo = SupplyWithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(ChannelRate.defaultInstance.copy(
                    prices = Seq(PriceDaily.defaultInstance),
                  )),
                )),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val messageSender = SimulationRateChannelMessageSender(sender)

      messageSender.postRoomRatesLogs(request, rateSilo, "test")

      Mockito.verify(sender, Mockito.never()).send(any())

      SimulationRateChannelMessageSender.sepHadoopForwarderSettings.apiKey should_== "it-plp-dev-test"

    }
    "postRoomRatesLogs should  send message for non sandbox request" in {
      val sender = mock[HadoopSender]
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel()

      val rateSilo = SupplyWithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(ChannelRate.defaultInstance.copy(
                    prices = Seq(PriceDaily.defaultInstance),
                  )),
                )),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val messageSender = SimulationRateChannelMessageSender(sender)

      messageSender.postRoomRatesLogs(request, rateSilo, "test")

      Mockito.verify(sender, Mockito.times(1)).send(any())

      SimulationRateChannelMessageSender.sepHadoopForwarderSettings.apiKey should_== "it-plp-dev-test"

    }

  }
}
