package com.agoda.papi.pricing.supply.ypl

import com.agoda.papi.enums.room.RateType
import com.agoda.papi.pricing.metadata.UspaProgram
import com.agoda.papi.pricing.supply.commissions.models.AGXHotel
import com.agoda.papi.pricing.supply.db.uspaCampaigns.UspaCampaignsService
import com.agoda.papi.pricing.supply.models.SupplyContext
import com.agoda.papi.pricing.supply.models.flow.SupplyFlowDataContext
import com.agoda.papi.pricing.supply.models.request.{FencedOriginObject, FencedRatePair, SupplyBaseRequest}
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.ypl.models.api.request.{YplAGXCommissionAdjustment, YplClientInfo}
import com.agoda.papi.ypl.models.pricing._
import com.agoda.papi.ypl.models.{YplContext, YplExperiment, YplRateFence}
import mocks.WithYPLContextServiceMock
import models.consts.ABTest
import org.joda.time.DateTime
import org.mockito.Mockito.when
import org.specs2.concurrent.ExecutionEnv
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.duration._
import scala.concurrent.{Await, Future}

class WithYPLContextSpec(implicit ec: ExecutionEnv)
  extends SpecificationWithJUnit
    with SupplyModelTestDataBuilders
    with Mockito {

  val yctx: WithYPLContextServiceImpl = WithYPLContextServiceMock.static

  "WithYPLProcess" should {
    "Provide FlowContext -> YplContext -> FlowContext" in {
      val request: SupplyBaseRequest = aValidSupplyBaseRequest.build

      def getContextWithData(baseRequest: SupplyBaseRequest = request)(
        doMock: (SupplyFlowDataContext) => Unit = (_ => {})): SupplyContext = {
        val supplyFlowDataContext = spy(SupplyFlowDataContext())

        val ctx: SupplyContext =
          aValidSupplyContext.withBaseRequest(baseRequest).withSupplyFlowDataContext(supplyFlowDataContext)
        doMock(supplyFlowDataContext)
        ctx
      }

      def updateContext(ctx: YplContext): Unit = {
        ctx.experimentContext.isUserB("exp2")
        ctx.experimentContext.isUserB("exp3")
      }

      "Async mode" in {
        "Success mode #1" in {
          val ctx = getContextWithData()()

          Await.result(yctx.withYplContextAsync(ctx) { yctx: YplContext =>
                         Future {
                           updateContext(yctx)
                           10
                         }
                       },
                       1.second) should_== 10
        }

        "Success mode #2" in {
          val baseRequest2 = aValidSupplyBaseRequest
            .withHotels(List(1L, 2L))
            .withCInfo(YplClientInfo(origin = Some("clientInfo origin"), cid = Some(1)))
            .withFencedRatePairs(
              Some(List(
                FencedRatePair(
                  key = YplRateFence(origin = Some("origin 1"), cid = Some(1), None),
                  value = FencedOriginObject(ratePlans = Set(1)),
                ),
                FencedRatePair(
                  key = YplRateFence(origin = Some("origin 2"), cid = Some(2), None),
                  value = FencedOriginObject(ratePlans = Set(2)),
                ),
              )),
            )
            .withExperiments(
              List(
                YplExperiment(ABTest.PRICE_PUSH_FENCING, 'A'),
              ),
            )

          val ctx = getContextWithData(baseRequest2) { (supplyFlowDataContext) =>
            val agxCommission1 = Map(YplRateFence("origin 1", 1, 0) -> Map(1L -> AGXHotel()))
            doReturn(agxCommission1).when(supplyFlowDataContext).getGPCommissions()
          }

          val result = Await.result(
            yctx.withYplContextAsync(ctx) { yctx: YplContext =>
              Future {
                updateContext(yctx)
                val agxCommissionAdjustment =
                  yctx.request.agxCommissionAdjustmentFences.getCommissionsFor(Set(YplRateFence("origin 1", 1, 0)), 1L)
                agxCommissionAdjustment.values.head.get
              }
            },
            1.second,
          )

          result should_== YplAGXCommissionAdjustment()
        }

        "Success mode #3" in {
          val baseRequest3 = aValidSupplyBaseRequest
            .withHotels(List(1L, 2L))
            .withCInfo(YplClientInfo(origin = Some("clientInfo origin"), cid = Some(1)))
            .withFencedRatePairs(
              Some(List(
                FencedRatePair(
                  key = YplRateFence(origin = Some("origin 1"), cid = Some(1), None),
                  value = FencedOriginObject(ratePlans = Set(1)),
                ),
                FencedRatePair(
                  key = YplRateFence(origin = Some("origin 2"), cid = Some(2), None),
                  value = FencedOriginObject(ratePlans = Set(2)),
                ),
              )),
            )

          val ctx = getContextWithData(baseRequest3) { (supplyFlowDataContext) =>
            val agxCommissions =
              Map(YplRateFence("clientInfo origin", 1, 0) -> (Map(1L -> AGXHotel(), 2L -> AGXHotel())))
            doReturn(agxCommissions).when(supplyFlowDataContext).getGPCommissions()
          }

          val result = Await.result(
            yctx.withYplContextAsync(ctx) { yctx: YplContext =>
              Future {
                updateContext(yctx)
                val agxCommissionAdjustment = yctx.request.agxCommissionAdjustmentFences
                  .getCommissionsFor(Set(YplRateFence("clientInfo origin", 1, 0)), 2L)
                agxCommissionAdjustment.values.head.get
              }
            },
            1.second,
          )

          result should_== YplAGXCommissionAdjustment()
        }

        "Success mode #4" in {
          val baseRequest3 = aValidSupplyBaseRequest
            .withExperiments(
              List(
                YplExperiment(ABTest.PRICE_PUSH_FENCING, 'B'),
              ))
            .withHotels(List(1L, 2L))
            .withCInfo(YplClientInfo(origin = Some("clientInfo origin"), cid = Some(1)))
            .withFencedRatePairs(
              Some(List(
                FencedRatePair(
                  key = YplRateFence(origin = Some("origin 1"), cid = Some(1), None),
                  value = FencedOriginObject(ratePlans = Set(1)),
                ),
                FencedRatePair(
                  key = YplRateFence(origin = Some("origin 2"), cid = Some(2), None),
                  value = FencedOriginObject(ratePlans = Set(2)),
                ),
              )),
            )

          val ctx = getContextWithData(baseRequest3) { (supplyFlowDataContext) =>
            val agxCommissions = Map(YplRateFence("origin 1", 1, 0) -> Map(1L -> AGXHotel(), 2L -> AGXHotel()))
            doReturn(agxCommissions).when(supplyFlowDataContext).getGPCommissions()
          }

          val result = Await.result(
            yctx.withYplContextAsync(ctx) { yctx: YplContext =>
              Future {
                updateContext(yctx)
                val agxCommissionAdjustment =
                  yctx.request.agxCommissionAdjustmentFences.getCommissionsFor(Set(YplRateFence("origin 1", 1, 0)), 1L)
                agxCommissionAdjustment.values.head.get
              }
            },
            1.second,
          )

          result should_== YplAGXCommissionAdjustment()
        }

        "Success mode (with USPA context) #5" in {
          val mockUspaService = mock[UspaCampaignsService]
          val yplContextService = WithYPLContextServiceMock.staticContextWithUspaMock(mockUspaService)
          val checkInFrom = new DateTime(2015, 10, 5, 0, 0)
          val checkInTo = new DateTime(2015, 10, 20, 0, 0)
          val bookStart = new DateTime(2015, 10, 1, 0, 0)
          val bookEnd = new DateTime(2015, 10, 10, 0, 0)
          val eligibleCampaign = UspaCampaign(
            1,
            2,
            "Campaign 1",
            RateType.NetExclusive,
            20.0,
            5.0,
            BenchmarkingCriteria(CxlPolicyMatchStrategy.Exact,
                                 OccupancyMatchStrategy.Exact,
                                 BreakfastMatchStrategy.Exact),
            AdjustableRateCriteria(
              Some(
                RateCriteria(None,
                             Some(List(1, 4)),
                             checkinFrom = Some(checkInFrom),
                             checkinTo = Some(checkInTo),
                             bookStart = Some(bookStart),
                             bookEnd = Some(bookEnd)))),
          )

          val allUspaCampaigns = List(
            eligibleCampaign,
            UspaCampaign(
              2,
              1,
              "Campaign 2",
              RateType.NetExclusive,
              20.0,
              5.0,
              BenchmarkingCriteria(CxlPolicyMatchStrategy.Exact,
                                   OccupancyMatchStrategy.Exact,
                                   BreakfastMatchStrategy.Exact),
              AdjustableRateCriteria(
                Some(
                  RateCriteria(Some(List(332)),
                               Some(List(6)),
                               checkinFrom = Some(checkInFrom),
                               checkinTo = Some(checkInTo),
                               bookStart = Some(bookStart),
                               bookEnd = Some(bookEnd)))),
            ),
          )

          when(mockUspaService.getUspaCampaigns()).thenReturn(allUspaCampaigns)
          val baseRequest = aValidSupplyBaseRequest.withHotels(List(1L, 2L))

          val uspaPrograms = Seq(UspaProgram(1), UspaProgram(2))

          val ctx = getContextWithData(baseRequest) { (supplyFlowDataContext) =>
            val agxCommissions = Map(baseRequest.defaultRequestFence -> Map(1L -> AGXHotel()))
            doReturn(agxCommissions).when(supplyFlowDataContext).getGPCommissions()
          }

          val result = Await.result(
            yplContextService.withYplContextAsync(
              ctx,
              uspaPrograms = uspaPrograms,
              offerRateChannelIds = Set(1, 23),
            ) { yctx: YplContext =>
              Future {
                updateContext(yctx)
                yctx.eligibleUspaCampaigns.map(_.campaignId)
              }
            },
            1.second,
          )

          result should_== List(1)
        }

        "Success mode (with USPA context) #5" in {
          val mockUspaService = mock[UspaCampaignsService]
          val yplContextService = WithYPLContextServiceMock.staticContextWithUspaMock(mockUspaService)
          val checkInFrom = new DateTime(2015, 10, 5, 0, 0)
          val checkInTo = new DateTime(2015, 10, 20, 0, 0)
          val bookStart = new DateTime(2015, 10, 1, 0, 0)
          val bookEnd = new DateTime(2015, 10, 10, 0, 0)
          val eligibleCampaign = UspaCampaign(
            1,
            2,
            "Campaign 1",
            RateType.NetExclusive,
            20.0,
            5.0,
            BenchmarkingCriteria(CxlPolicyMatchStrategy.Exact,
                                 OccupancyMatchStrategy.Exact,
                                 BreakfastMatchStrategy.Exact),
            AdjustableRateCriteria(
              Some(
                RateCriteria(None,
                             Some(List(1, 4)),
                             checkinFrom = Some(checkInFrom),
                             checkinTo = Some(checkInTo),
                             bookStart = Some(bookStart),
                             bookEnd = Some(bookEnd)))),
          )

          val allUspaCampaigns = List(
            eligibleCampaign,
            UspaCampaign(
              2,
              1,
              "Campaign 2",
              RateType.NetExclusive,
              20.0,
              5.0,
              BenchmarkingCriteria(CxlPolicyMatchStrategy.Exact,
                                   OccupancyMatchStrategy.Exact,
                                   BreakfastMatchStrategy.Exact),
              AdjustableRateCriteria(
                Some(
                  RateCriteria(Some(List(332)),
                               Some(List(1, 4)),
                               checkinFrom = Some(checkInFrom),
                               checkinTo = Some(checkInTo),
                               bookStart = Some(bookStart),
                               bookEnd = Some(bookEnd)))),
            ),
          )

          when(mockUspaService.getUspaCampaigns()).thenReturn(allUspaCampaigns)
          val baseRequest = aValidSupplyBaseRequest.withHotels(List(1L, 2L))

          val uspaPrograms = Seq(UspaProgram(1), UspaProgram(2))

          val ctx = getContextWithData(baseRequest) { (supplyFlowDataContext) =>
            val agxCommissions = Map(baseRequest.defaultRequestFence -> Map(1L -> AGXHotel()))
            doReturn(agxCommissions).when(supplyFlowDataContext).getGPCommissions()
          }

          val result = Await.result(
            yplContextService.withYplContextAsync(
              ctx,
              uspaPrograms = uspaPrograms,
              offerRateChannelIds = Set(1, 23),
            ) { yctx: YplContext =>
              Future {
                updateContext(yctx)
                yctx.eligibleUspaCampaigns.map(_.campaignId)
              }
            },
            1.second,
          )

          result should_== List(1)
        }

        "Failure mode" in {
          val ctx = getContextWithData()()

          val future: Future[Int] = yctx.withYplContextAsync(ctx) { yctx: YplContext =>
            Future[Int] {
              updateContext(yctx)
              throw new RuntimeException("Something is wrong")
            }
          }
          future should throwA[RuntimeException].await
        }
      }

      "Sync mode" in {
        val ctx = getContextWithData()()

        yctx.withYplContextSync(ctx) { yctx: YplContext =>
          updateContext(yctx)
          10
        } should_== 10

      }
    }
  }
}
