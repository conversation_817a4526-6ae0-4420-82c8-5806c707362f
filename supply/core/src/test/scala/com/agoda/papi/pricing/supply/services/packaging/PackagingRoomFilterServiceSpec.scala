package com.agoda.papi.pricing.supply.services.packaging

import com.agoda.papi.enums.hotel.{DMC, PaymentModel, PaymentOption}
import com.agoda.papi.ypl.models.YPLTestDataBuilders
import org.specs2.mutable.SpecificationWithJUnit

class PackagingRoomFilterServiceSpec extends SpecificationWithJUnit with YPLTestDataBuilders {

  val service = new PackagingRoomFilterServiceImpl()

  "PackagingRoomFilterService" should {
    "isNonAgencyRoom" in {
      val agencyRoom = aValidRoom withPaymentModel PaymentModel.Agency
      val merchantRoom = aValidRoom withPaymentModel PaymentModel.Merchant
      val merchantCommissionRoom = aValidRoom withPaymentModel PaymentModel.MerchantCommission
      service.isNonAgencyRoom(agencyRoom) shouldEqual false
      service.isNonAgencyRoom(merchantRoom) shouldEqual true
      service.isNonAgencyRoom(merchantCommissionRoom) shouldEqual true
    }
    "isNonAgencyRoom" in {
      // Arrange

      val agencyRoom = aValidRoom.withPaymentModel(PaymentModel.Agency)
      val merchantRoom = aValidRoom.withPaymentModel(PaymentModel.Merchant)
      val merchantCommRoom = aValidRoom.withPaymentModel(PaymentModel.MerchantCommission)
      val unknownRoom = aValidRoom.withPaymentModel(PaymentModel.Unknown)

      // Act
      val resAgencyCase = service.isNonAgencyRoom(agencyRoom)
      val resMerchantCase = service.isNonAgencyRoom(merchantRoom)
      val resMerchantCommCase = service.isNonAgencyRoom(merchantCommRoom)
      val resUnknownCase = service.isNonAgencyRoom(unknownRoom)

      // Assert
      resAgencyCase should_=== false
      resMerchantCase should_=== true
      resMerchantCommCase should_=== true
      resUnknownCase should_=== false
    }

    "isAgodaManaged" in {

      // Arrange
      val agodaRoom = aValidRoom.withSupplierId(DMC.YCS.value)
      val nonAgodaRoom = aValidRoom.withSupplierId(DMC.Booking.value)

      // Act

      val res1 = service.isAgodaManaged(agodaRoom, None)
      val res2 = service.isAgodaManaged(agodaRoom, Some(true))
      val res3 = service.isAgodaManaged(agodaRoom, Some(false))
      val res4 = service.isAgodaManaged(nonAgodaRoom, None)
      val res5 = service.isAgodaManaged(nonAgodaRoom, Some(true))
      val res6 = service.isAgodaManaged(nonAgodaRoom, Some(false))

      res1 should_== true
      res2 should_== false
      res3 should_== true
      res4 should_== false
      res5 should_== false
      res6 should_== false
    }

    "isCCAndPrePaymentRequiredRoom" in {
      // Arrange

      val noCCReqMultipleOption: Set[PaymentOption] = Set(PaymentOption.NoCreditCard, PaymentOption.PayAtHotel)
      val prePaymentMultipleOption: Set[PaymentOption] = Set(PaymentOption.PrepaymentRequired)
      val ccPrepayRoom = aValidRoom.withPaymentOption(prePaymentMultipleOption)
      val noCC = aValidRoom.withPaymentOption(noCCReqMultipleOption)

      // Act
      val resCcPrepay = service.isCCAndPrePaymentRequiredRoom(ccPrepayRoom)
      val resNoCC = service.isCCAndPrePaymentRequiredRoom(noCC)

      // Assert
      resCcPrepay should_=== true
      resNoCC should_=== false
    }
  }
}
