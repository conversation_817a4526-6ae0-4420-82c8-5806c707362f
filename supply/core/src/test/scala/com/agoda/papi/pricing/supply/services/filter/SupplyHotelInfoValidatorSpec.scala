package com.agoda.papi.pricing.supply.services.filter

import com.agoda.papi.pricing.metadata.SupplierCCMapping
import com.agoda.papi.pricing.supply.models.consts.ABTest
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.pricing.supply.models.{SupplierPriceShopStatusMapping, SupplyContext, SupplyHotelInfo}
import com.agoda.papi.pricing.supply.services._
import com.agoda.papi.ypl.models.{YPLTestDataBuilders, YplRegulationFeatureEnabledSetting}
import com.agoda.papi.ypl.models.suppliers.DMC
import concurrency.Ec
import mocks._
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import org.scalatest.concurrent.ScalaFutures
import org.scalatestplus.mockito.MockitoSugar
import org.specs2.mutable.SpecificationWithJUnit
import services.longTermRental.LongTermRentalService
import services.{
  HoldingOutNewPropertiesFilterService,
  HoldingOutNewPropertiesFilterServiceImpl,
  WhitelabelDMCFilteringService,
  WhitelabelDMCFilteringServiceImpl,
}

import scala.concurrent.{ExecutionContext, Future}

class SupplyHotelInfoValidatorSpec
  extends SpecificationWithJUnit
    with Ec
    with SupplyModelTestDataBuilders
    with YPLTestDataBuilders
    with MockitoSugar {

  def mockFlowBuilder(priceShopPropertiesFilterService: PriceShopPropertiesFilterService =
                        PriceShopPropertiesFilterServiceMock,
                      holdingOutNewPropertiesFilterService: HoldingOutNewPropertiesFilterService =
                        HoldingOutNewPropertiesFilterServiceMock,
                      hotelSupplierMappingService: HotelSupplierMappingService = HotelSupplierMappingServiceMock,
                      whitelabelDMCFilteringService: WhitelabelDMCFilteringService = WhitelabelDMCFilteringServiceMock,
                      longTermRentalService: LongTermRentalService = LongTermRentalServiceMock,
                      bookOnRequestService: SupplyBookOnRequestService = SupplyBookOnRequestServiceMock,
                      hotelPartnerFilterService: HotelPartnerFilterService = HotelPartnerFilterServiceMock,
                      regulatoryBlockingService: RegulatoryBlockingService = RegulatoryBlockingServiceMock)
    : SupplyHotelInfoValidatorImpl = new SupplyHotelInfoValidatorImpl(
    priceShopPropertiesFilterService,
    holdingOutNewPropertiesFilterService,
    hotelSupplierMappingService,
    whitelabelDMCFilteringService,
    longTermRentalService,
    bookOnRequestService,
    hotelPartnerFilterService,
    regulatoryBlockingService,
  )

  "filter past midnight search" in {
    val flow = mockFlowBuilder()

    val mockBaseRequest = aValidSupplyBaseRequest
      .withBookingDate(new DateTime(2020, 7, 20, 0, 0, 0))
      .withCheckIn(new DateTime(2020, 7, 19, 0, 0, 0))
      .build

    val hotel = aValidSupplyHotelInfo.withGmtOffset(7).build
    val data = Seq[SupplyHotelInfo](hotel)

    "2) Should return all hotel with past midnight search" in {
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest)
      val t = flow.Data(data, contextMock, null)
      val result = flow.filterYesterday(t)

      result.size should_== 1
    }

    "3) Should return all hotel with past midnight search when booking time before 5 AM" in {
      val bookingDate = DateTime.parse("2020-07-20T04:00:00", DateTimeFormat.forPattern("yyyy-MM-dd'T'hh:mm:ss"))
      val mockBaseRequestWithBooking = mockBaseRequest.copy(bookingDate = bookingDate)
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequestWithBooking)
      val t = flow.Data(data, contextMock, null)
      val result = flow.filterYesterday(t)

      result.size should_== 1
    }

    "4) Should return all hotel with past midnight search when booking time before 5 AM and time on boundary" in {
      val bookingDate = DateTime.parse("2020-07-20T04:59:00", DateTimeFormat.forPattern("yyyy-MM-dd'T'hh:mm:ss"))
      val mockBaseRequestWithBooking = mockBaseRequest.copy(bookingDate = bookingDate)
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequestWithBooking)
      val t = flow.Data(data, contextMock, null)
      val result = flow.filterYesterday(t)

      result.size should_== 1
    }

    "5) Should filter all hotel with past midnight search when booking time after 5 AM" in {
      val bookingDate = DateTime.parse("2020-07-20T06:15:00", DateTimeFormat.forPattern("yyyy-MM-dd'T'hh:mm:ss"))
      val mockBaseRequestWithBooking = mockBaseRequest.copy(bookingDate = bookingDate)
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequestWithBooking)
      val t = flow.Data(data, contextMock, null)
      val result = flow.filterYesterday(t)

      result.size should_== 0
    }

    "6) Should filter all hotel with past midnight search when booking time is 5 AM" in {
      val bookingDate = DateTime.parse("2020-07-20T05:00:00", DateTimeFormat.forPattern("yyyy-MM-dd'T'hh:mm:ss"))
      val mockBaseRequestWithBooking = mockBaseRequest.copy(bookingDate = bookingDate)
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequestWithBooking)
      val t = flow.Data(data, contextMock, null)
      val result = flow.filterYesterday(t)

      result.size should_== 0
    }

    "7) Should filter all hotel past booking date when booking date two days after check-in" in {
      val modifiedBookingDate = mockBaseRequest.bookingDate.plusDays(1)
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest.withBookingDate(modifiedBookingDate))
      val t = flow.Data(data, contextMock, null)
      val result = flow.filterYesterday(t)

      result.size should_== 0
    }

    "8) Should not filter all hotel past booking date when booking date 1 day before check-in" in {
      val modifiedBookingDate = mockBaseRequest.bookingDate.plusDays(-1)
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest.copy(bookingDate = modifiedBookingDate))
      val t = flow.Data(data, contextMock, null)
      val result = flow.filterYesterday(t)

      result.size should_== 1
    }
  }

  "filter Holding out for new properties" in {
    val flow = mockFlowBuilder(holdingOutNewPropertiesFilterService = new HoldingOutNewPropertiesFilterServiceImpl)

    val suppliers = Seq(SupplierCCMapping(DMC.YCS), SupplierCCMapping(1), SupplierCCMapping(2))
    val hotel = aValidSupplyHotelInfo
      .withSupplierCCMappings(suppliers)
      .withHotelLiveDate(Some(new DateTime(2019, 7, 1, 0, 1, 0)))
      .withHotelYcsFlag(true)
      .build
    val data = Seq[SupplyHotelInfo](hotel)

    "1) Should filter when experiment NHACON-1733 is B" in {
      val contextMock = aValidSupplyContext.withBExperiment(ABTest.HOLDING_OUT_FOR_NEW_PROPERTIES)
      val hotelData = flow.Data(data, contextMock, null)
      val result = flow.filterHoldingOutNewProperties(hotelData)
      val expectedSuppliers = Seq(SupplierCCMapping(1), SupplierCCMapping(2))
      result.head.suppliers should_== expectedSuppliers
      result.head.suppliers.length.equals(2)
    }

    "2) Should not filter when experiment NHACON-1733 is A" in {
      val contextMock = aValidSupplyContext.withAExperiment(ABTest.HOLDING_OUT_FOR_NEW_PROPERTIES)
      val hotelData = flow.Data(data, contextMock, null)
      val result = flow.filterHoldingOutNewProperties(hotelData)
      val expectedSuppliers = Seq(SupplierCCMapping(DMC.YCS), SupplierCCMapping(1), SupplierCCMapping(2))
      result.head.suppliers should_== expectedSuppliers
      result.head.suppliers.length.equals(3)
    }
  }

  "filterPriceShopEnabledHotels# filter priceshop hotels to be processed only by price shopper traffic" in {
    val flow = mockFlowBuilder(new PriceShopPropertiesFilterServiceImpl)

    val suppliersForHotelA = Seq(SupplierCCMapping(DMC.YCS), SupplierCCMapping(1), SupplierCCMapping(2))
    val suppliersForHotelB = Seq(SupplierCCMapping(DMC.YCS), SupplierCCMapping(3), SupplierCCMapping(4))
    val hotelA = aValidSupplyHotelInfo
      .withHotelId(0L)
      .withSupplierCCMappings(suppliersForHotelA)
      .withHotelYcsFlag(true)
      .withSupplierPriceShopStatusMapping(suppliersForHotelA.map(s =>
        SupplierPriceShopStatusMapping(s.supplierId, 0, Some(1))))
      .build

    val hotelB = aValidSupplyHotelInfo
      .withHotelId(1L)
      .withSupplierCCMappings(suppliersForHotelB)
      .withHotelYcsFlag(true)
      .withSupplierPriceShopStatusMapping(suppliersForHotelB.map(s =>
        SupplierPriceShopStatusMapping(s.supplierId, 0, Some(0))))
      .build
    val data = Seq(hotelA, hotelB)

    "1) Should apply filter when feature is enabled" in {
      val flowWithFlagEnabled =
        mockFlowBuilder(priceShopPropertiesFilterService = new PriceShopPropertiesFilterServiceImpl {
          override private[services] def isPriceShopHotelsFilterEnabled = true
        })
      val contextMock = aValidSupplyContext
      val hotelData = flowWithFlagEnabled.Data(data, contextMock, null)
      val result = flowWithFlagEnabled.filterPriceShopEnabledHotels(hotelData)
      result.find(hotel => hotel.hotelId == hotelA.hotelId).map(_.suppliers) should_== Some(Seq.empty)
      result.find(hotel => hotel.hotelId == hotelB.hotelId).map(_.suppliers) should_== Some(suppliersForHotelB)
    }

    "2) Should not apply filter when feature is disabled" in {
      val contextMock = aValidSupplyContext
      val hotelData = flow.Data(data, contextMock, null)
      val result = flow.filterPriceShopEnabledHotels(hotelData)
      result should_== data
    }
  }

  "filterSupportedSuppliers" in {
    val flow = mockFlowBuilder(whitelabelDMCFilteringService = new WhitelabelDMCFilteringServiceImpl)

    val suppliersForHotelA = Seq(SupplierCCMapping(DMC.JTBWL), SupplierCCMapping(1), SupplierCCMapping(2))
    val suppliersForHotelB = Seq(SupplierCCMapping(DMC.JTBWL), SupplierCCMapping(DMC.YCS))
    val suppliersForHotelC = Seq(SupplierCCMapping(DMC.YCS), SupplierCCMapping(DMC.YCS))
    val directConnectSupplier = Seq(SupplierCCMapping(DMC.Accor))
    val hotelA =
      aValidSupplyHotelInfo.withHotelId(0L).withSupplierCCMappings(suppliersForHotelA).withHotelYcsFlag(true).build
    val hotelB = aValidSupplyHotelInfo.withHotelId(0L).withSupplierCCMappings(suppliersForHotelB).build
    val hotelC =
      aValidSupplyHotelInfo.withHotelId(0L).withSupplierCCMappings(suppliersForHotelC).withCountryCode("JP").build
    val directConnectInNonBlockedCountry =
      aValidSupplyHotelInfo.withHotelId(0L).withSupplierCCMappings(directConnectSupplier).build
    val directConnectInBlockedCountry =
      aValidSupplyHotelInfo.withHotelId(0L).withSupplierCCMappings(directConnectSupplier).withCountryCode("JP").build
    val dataA = Seq(hotelA)
    val dataB = Seq(hotelB)
    val dataC = Seq(hotelC)
    val directConnectNonBlockedTestData = Seq(directConnectInNonBlockedCountry)
    val directConnectBlockedTestData = Seq(directConnectInBlockedCountry)

    val multiRoomOccupancy = aValidOccInfo.withRooms(2).withAdults(4).build

    "should not filter JTBWL supplier  when iSellingExternalSuppliersForJtbEnabled is off" in {
      val mockBaseRequest = aValidSupplyBaseRequest
        .withOcc(multiRoomOccupancy)
        .withWhitelabelSetting(
          aValidSupplyBaseRequest.whitelabelSetting.copy(
            supportedSuppliers = Set(DMC.JTBWL),
            externalSuppliers = List(332),
            isSellingDifferentSuppliersForJtbEnabled = false,
          ),
        )
        .build
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest)
      val hotelData = flow.Data(dataA, contextMock, null)
      val result = flow.filterSupportedSuppliers(hotelData)

      result.head.suppliers should_== Seq(SupplierCCMapping(DMC.JTBWL))
    }

    "should not filter JTBWL supplier  when iSellingExternalSuppliersForJtbEnabled is on" in {
      val mockBaseRequest = aValidSupplyBaseRequest
        .withOcc(multiRoomOccupancy)
        .withWhitelabelSetting(
          aValidSupplyBaseRequest.whitelabelSetting.copy(
            supportedSuppliers = Set(DMC.JTBWL),
            externalSuppliers = List(332),
            isSellingDifferentSuppliersForJtbEnabled = true,
          ),
        )
        .build
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest)
      val hotelData = flow.Data(dataA, contextMock, null)
      val result = flow.filterSupportedSuppliers(hotelData)

      result.head.suppliers should_== Seq(SupplierCCMapping(DMC.JTBWL))
    }

    "should filter YCS supplier when iSellingExternalSuppliersForJtbEnabled is off" in {
      val mockBaseRequest = aValidSupplyBaseRequest
        .withOcc(multiRoomOccupancy)
        .withWhitelabelSetting(
          aValidSupplyBaseRequest.whitelabelSetting.copy(
            supportedSuppliers = Set(DMC.JTBWL),
            externalSuppliers = List(332),
            isSellingDifferentSuppliersForJtbEnabled = false,
          ))
        .build
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest)
      val hotelData = flow.Data(dataB, contextMock, null)
      val result = flow.filterSupportedSuppliers(hotelData)

      result.head.suppliers should_== Seq(SupplierCCMapping(DMC.JTBWL))
    }

    "should filter YCS supplier in Japan when iSellingExternalSuppliersForJtbEnabled is on" in {
      val mockBaseRequest = aValidSupplyBaseRequest
        .withOcc(multiRoomOccupancy)
        .withWhitelabelSetting(
          aValidSupplyBaseRequest.whitelabelSetting.copy(
            supportedSuppliers = Set(DMC.JTBWL, DMC.YCS),
            externalSuppliers = List(332),
            blockedCountries = List("MY", "JP"),
            isSellingDifferentSuppliersForJtbEnabled = true,
          ))
        .build
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest)
      val hotelData = flow.Data(dataC, contextMock, null)
      val result = flow.filterSupportedSuppliers(hotelData)

      result.head.suppliers should_== Seq.empty
    }

    "should not filter YCS supplier when iSellingExternalSuppliersForJtbEnabled is on and property in not in a blocked country" in {
      val mockBaseRequest = aValidSupplyBaseRequest
        .withOcc(multiRoomOccupancy)
        .withWhitelabelSetting(
          aValidSupplyBaseRequest.whitelabelSetting.copy(
            supportedSuppliers = Set(DMC.JTBWL),
            externalSuppliers = List(332),
            isSellingDifferentSuppliersForJtbEnabled = true,
          ))
        .build
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest)
      val hotelData = flow.Data(dataB, contextMock, null)
      val result = flow.filterSupportedSuppliers(hotelData)

      result.head.suppliers should_== suppliersForHotelB
    }

    "should filter Direct connect when iSellingExternalSuppliersForJtbEnabled is off" in {
      val mockBaseRequest = aValidSupplyBaseRequest
        .withOcc(multiRoomOccupancy)
        .build
        .withWhitelabelSetting(aValidSupplyBaseRequest.whitelabelSetting.copy(
          supportedSuppliers = Set(DMC.JTBWL),
          externalSuppliers = List(332),
          isSellingDifferentSuppliersForJtbEnabled = false,
          directConnectSupplierIds = Set(29004, 27912, 29005, 27800, 29002, 29022, 27914, 29020, 29003),
        ))
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest)
      val hotelData = flow.Data(directConnectNonBlockedTestData, contextMock, null)
      val result = flow.filterSupportedSuppliers(hotelData)

      result.head.suppliers should_== Seq.empty
    }

    "should filter Direct Connect when iSellingExternalSuppliersForJtbEnabled is on and JTBFP-615-v2 = A" in {
      val mockBaseRequest = aValidSupplyBaseRequest
        .withOcc(multiRoomOccupancy)
        .build
        .withWhitelabelSetting(aValidSupplyBaseRequest.whitelabelSetting.copy(
          supportedSuppliers = Set(DMC.JTBWL),
          externalSuppliers = List(332),
          isSellingDifferentSuppliersForJtbEnabled = true,
          directConnectSupplierIds = Set(29004, 27912, 29005, 27800, 29002, 29022, 27914, 29020, 29003),
        ))
        .withAExperiment(ABTest.ENABLE_SELLING_DIRECT_CONNECT_SUPPLY_FOR_JTB)
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest)
      val hotelData = flow.Data(directConnectNonBlockedTestData, contextMock, null)
      val result = flow.filterSupportedSuppliers(hotelData)

      result.head.suppliers should_== Seq.empty
    }

    "should filter Direct Connect when iSellingExternalSuppliersForJtbEnabled in on and JTBFP-615-v2 = B but the property is in the blocked countries" in {
      val mockBaseRequest = aValidSupplyBaseRequest
        .withOcc(multiRoomOccupancy)
        .build
        .withWhitelabelSetting(aValidSupplyBaseRequest.whitelabelSetting.copy(
          supportedSuppliers = Set(DMC.JTBWL),
          externalSuppliers = List(332),
          blockedCountries = List("MY", "JP"),
          isSellingDifferentSuppliersForJtbEnabled = true,
          directConnectSupplierIds = Set(29004, 27912, 29005, 27800, 29002, 29022, 27914, 29020, 29003),
        ))
        .withBExperiment(ABTest.ENABLE_SELLING_DIRECT_CONNECT_SUPPLY_FOR_JTB)
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest)
      val hotelData = flow.Data(directConnectBlockedTestData, contextMock, null)
      val result = flow.filterSupportedSuppliers(hotelData)

      result.head.suppliers should_== Seq.empty
    }

    "shouldn't filter Direct Connect when iSellingExternalSuppliersForJtbEnabled is on and JTBFP-615-v2 = B and property not in blocked countries" in {
      val mockBaseRequest = aValidSupplyBaseRequest
        .withOcc(multiRoomOccupancy)
        .build
        .withWhitelabelSetting(aValidSupplyBaseRequest.whitelabelSetting.copy(
          supportedSuppliers = Set(DMC.JTBWL),
          externalSuppliers = List(332),
          blockedCountries = List("MY", "JP"),
          isSellingDifferentSuppliersForJtbEnabled = true,
          directConnectSupplierIds = Set(29004, 27912, 29005, 27800, 29002, 29022, 27914, 29020, 29003),
        ))
        .withBExperiment(ABTest.ENABLE_SELLING_DIRECT_CONNECT_SUPPLY_FOR_JTB)
      val contextMock = aValidSupplyContext.withBaseRequest(mockBaseRequest)
      val hotelData = flow.Data(directConnectNonBlockedTestData, contextMock, null)
      val result = flow.filterSupportedSuppliers(hotelData)

      result.head.suppliers should_== directConnectSupplier
    }
  }

  "filterNonComplianceHotel" in {

    val hotel1 = aValidSupplyHotelInfo.withHotelId(1L).build
    val hotel2 = aValidSupplyHotelInfo.withHotelId(2L).build
    val data = Seq[SupplyHotelInfo](hotel1, hotel2)

    val mockComplianceHotelData = Seq[SupplyHotelInfo](hotel2)

    val regulatoryBlockingServiceMock = new RegulatoryBlockingService {

      override def filterNonCompliantHotels[C <: SupplyContext](hotels: Seq[SupplyHotelInfo])(implicit
        ctx: C,
        ec: ExecutionContext): Future[(Seq[SupplyHotelInfo], C)] = Future.successful((mockComplianceHotelData, ctx))
    }
    def createFlow = mockFlowBuilder(regulatoryBlockingService = regulatoryBlockingServiceMock)

    "1) filter out non-compliant hotels when LT-894 is enabled" should {
      val contextMock = aValidSupplyContext.withBExperiment(ABTest.ENABLE_REGULATORY_BLOCKING_FILTER)
      val flow = createFlow
      val hotelData = flow.Data(data, contextMock, null)

      ScalaFutures.whenReady(flow.filterNonComplianceHotels(hotelData)) { result =>
        result._1.size must beEqualTo(1)
      }
    }

    "2) Should not apply filter when LT-894 is disabled" should {
      val contextMock = aValidSupplyContext.withAExperiment(ABTest.ENABLE_REGULATORY_BLOCKING_FILTER)
      val flow = createFlow
      val hotelData = flow.Data(data, contextMock, null)

      ScalaFutures.whenReady(flow.filterNonComplianceHotels(hotelData)) { result =>
        result._1.size must beEqualTo(2)
      }
    }

    "Should filter out non-compliant hotels when isDsaLicenseBlockingDisabled feature is on but LT-1208 is disabled" should {
      val contextMock = aValidSupplyContext
        .withBExperiment(ABTest.ENABLE_REGULATORY_BLOCKING_FILTER)
        .withAExperiment(ABTest.DSA_LICENSE_BLOCKING_DISABLED)
        .withRegulationFeatureEnabledSetting(
          YplRegulationFeatureEnabledSetting.default.copy(isDsaLicenseBlockingDisabled = true))
      val flow = createFlow
      val hotelData = flow.Data(data, contextMock, null)

      ScalaFutures.whenReady(flow.filterNonComplianceHotels(hotelData)) { result =>
        result._1.size must beEqualTo(1)
      }
    }

    "Should not filter out non-compliant hotels when isDsaLicenseBlockingDisabled feature is on and LT-1208 is enable" should {
      val contextMock = aValidSupplyContext
        .withBExperiment(ABTest.ENABLE_REGULATORY_BLOCKING_FILTER)
        .withBExperiment(ABTest.DSA_LICENSE_BLOCKING_DISABLED)
        .withRegulationFeatureEnabledSetting(
          YplRegulationFeatureEnabledSetting.default.copy(isDsaLicenseBlockingDisabled = true))
      val flow = createFlow
      val hotelData = flow.Data(data, contextMock, null)

      ScalaFutures.whenReady(flow.filterNonComplianceHotels(hotelData)) { result =>
        result._1.size must beEqualTo(2)
      }
    }
  }
}
