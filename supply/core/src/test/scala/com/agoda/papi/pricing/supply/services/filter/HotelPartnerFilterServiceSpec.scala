package com.agoda.papi.pricing.supply.services.filter

import com.agoda.papi.constants.PlatformID
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.pricing.supply.services.PartnerFilterDataService
import com.agoda.papi.ypl.models.suppliers.DMC
import mocks.{PartnerFilterDataServiceMock, PartnerFilterMigrationToHsbSettingsProducerMock}
import models.diagnostics.RequestFlowState
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext}
import scala.jdk.CollectionConverters.collectionAsScalaIterableConverter

class HotelPartnerFilterServiceSpec extends SpecificationWithJUnit with SupplyModelTestDataBuilders with Mockito {
  implicit def ec: ExecutionContext = ExecutionContext.global
  val flow = new HotelPartnerFilterServiceImpl(PartnerFilterMigrationToHsbSettingsProducerMock.static,
                                               PartnerFilterDataServiceMock)

  "apply partner filter ignored for cid in the consul settings" in {
    val partnerFilterDataServiceMock = mock[PartnerFilterDataService]
    val flow = new HotelPartnerFilterServiceImpl(PartnerFilterMigrationToHsbSettingsProducerMock.static,
                                                 partnerFilterDataServiceMock)
    val hsbMigrationConsulCid = PartnerFilterMigrationToHsbSettingsProducerMock.static.current.cids.head
    val request = aValidSupplyBaseRequest.withCID(Some(hsbMigrationConsulCid)).build
    val hotelInfo1 = aValidSupplyHotelInfo
    val hotelInfo2 = aValidSupplyHotelInfo

    val resultF = flow.hotelPartnerFilter(Seq(hotelInfo1, hotelInfo2))(aValidSupplyContext.withBaseRequest(request))
    val result = Await.result(resultF, 2.second)
    result.size should_== 2

    there was no(partnerFilterDataServiceMock).getPartnerFilter(hsbMigrationConsulCid)
  }

  "filter hotel" should {
    val hotelId = 111
    val suppliersDispatched = Set(DMC.YCS, DMC.BCOM, DMC.Accor)
    val hotelInfo = aValidSupplyHotelInfo.withSuppliers(suppliersDispatched.toList).withHotelId(hotelId)
    val req = aValidSupplyBaseRequest.withPlatformId(Some(PlatformID.AffiliateAPISearch))

    "check if hotel is blocked" in {
      val ctx = aValidSupplyContext.withBaseRequest(req.withCID(Some(1111)))

      flow.applyHotelPartnerFilter(Seq(hotelInfo.build))(ec, ctx)

      ctx.requestFlowDiagnosticContextData.requestFlowDiagnosticsMap
        .values()
        .asScala
        .toList
        .forall(m => m.contains(RequestFlowState.HotelIsBlocked)) mustEqual true
    }
  }
}
