package com.agoda.papi.pricing.supply.simulation.ratechannel

import com.agoda.papi.enums.room.ChannelDiscountStackingType
import com.agoda.papi.enums.room.ChannelDiscountStackingType.AllowedStacking
import com.agoda.papi.enums.room.RateChannelSimulationFeatures.{Debug<PERSON>ode, DisableDispatchingLogic, SameDayExperiment}
import com.agoda.papi.pricing.supply.db.stackChannelInfo.StackChannelInfoDataService
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders._
import com.agoda.papi.pricing.supply.models.{MetaSilo, SupplyHotelInfo, WithMeta}
import com.agoda.papi.pricing.supply.simulation.models.{
  HotelChannel,
  HotelChannels,
  ImprovementDiscount,
  PricingSandboxConfig,
}
import com.agoda.papi.ypl.models.GUIDGeneratorSpec.aValidRateSilo
import com.agoda.papi.ypl.models.builders.ypl.YplContextMock
import com.agoda.papi.ypl.models.{YplChannel => DFCompositeChannel, _}
import com.agoda.supply.calc.proto.StackedChannelInfo.StackedChannelDiscount
import com.agoda.supply.calc.proto._
import com.agoda.utils.flow.ExperimentContext
import com.agoda.utils.monitoring.AggregateReporter
import concurrency.ExecutionContexts
import external.pricing.{DispatchInfo, HotelDispatchInfo}
import mocks.DmcMetaDataServiceMock
import org.joda.time.DateTime
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

import scala.concurrent.duration.{Duration, FiniteDuration, SECONDS}
import scala.concurrent.{Await, ExecutionContext, Future}

class RateChannelSimulationPricingFlowSpec extends SpecificationWithJUnit with Mockito {

  implicit def ec: ExecutionContext = ExecutionContexts.ioContext

  val waitTime: FiniteDuration = Duration(5, SECONDS)

  val emptyStackChannelInfoDataService = new StackChannelInfoDataService {
    override def getHotelStackChannelInfo(hotelId: TravelerType)(implicit ec: ExecutionContext): Future[HotelChannels] =
      Future.successful(HotelChannels(Set.empty))
  }
  val dmcMetadataServiceMock = new DmcMetaDataServiceMock {}
  val flow = new RateChannelSimulationPricingFlowImpl(dmcMetadataServiceMock, emptyStackChannelInfoDataService)

  val failedStackChannelInfoDataService = new StackChannelInfoDataService {
    override def getHotelStackChannelInfo(hotelId: TravelerType)(implicit ec: ExecutionContext): Future[HotelChannels] =
      Future.failed(new Exception("Test exceptions"))
  }
  val dmcMetaDataServiceMockDirectConnect2 = new DmcMetaDataServiceMock {
    override def isDirectConnect(supplierId: SupplierId): Boolean = supplierId == 2
  }
  val dmcMetaDataServiceMockDirectConnect1 = new DmcMetaDataServiceMock {
    override def isDirectConnect(supplierId: SupplierId): Boolean = supplierId == 1
  }
  val dmcMetaDataServiceMockDirectConnect100 = new DmcMetaDataServiceMock {
    override def isDirectConnect(supplierId: SupplierId): Boolean = supplierId == 100
  }

  "RateChannelSimulationPricingFlow" should {
    "work properly when simulation data is None" in {

      val request = aValidSupplyBaseRequest.build

      val currentYplMetaSilo = mock[MetaSilo]
      val currentYplContext = mock[YplContext]

      val (updatedSilo, updatedContext) =
        Await.result(flow.applyPricingSimulationForYpl(currentYplMetaSilo, currentYplContext, request), waitTime)
      updatedSilo must beEqualTo(currentYplMetaSilo)
      updatedContext must beEqualTo(currentYplContext)
      request.isRateChannelSimulation must beFalse
      flow.containFeatureFlag(request, DisableDispatchingLogic) should_== false
    }

    "work when pricing rate channel discount is available and YplMetaSilo have offers" in {

      val pricingSandboxConfig = PricingSandboxConfig(
        variant = "test",
        experimentName = "test_experiment",
        experimentRunId = "test_experiment_run_id",
        debug = true,
      )
      val pricingSandboxRateChannelDiscounts = Some(
        Map(
          100 -> 0.5,
          200 -> 0.5,
        ),
      )
      val request = aValidSupplyBaseRequest
        .withSimulationDataForRateChannel(
          pricingSandboxConfig = Some(pricingSandboxConfig),
          pricingSandboxRateChannelDiscounts = pricingSandboxRateChannelDiscounts,
        )
        .build

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance),
            ),
            27916 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext

      val (updatedSilo, updatedContext) =
        Await.result(flow.applyPricingSimulationForYpl(currentYplMetaSilo, currentYplContext, request), waitTime)

      request.isRateChannelSimulation must beTrue

      updatedSilo should_!= currentYplMetaSilo
      updatedContext should_!= currentYplContext

      updatedContext.request.simulationRequest.get.channels should_== Set(100, 200)
    }
    "work when pricing rate channel discount is available and YplMetaSilo have offers and with feature" in {

      val pricingSandboxConfig = PricingSandboxConfig(
        variant = "test",
        experimentName = "test_experiment",
        experimentRunId = "test_experiment_run_id",
        debug = true,
      )
      val pricingSandboxRateChannelDiscounts = Some(
        Map(
          300 -> 0.5,
          400 -> 0.5,
        ),
      )
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxConfig = Some(pricingSandboxConfig),
        pricingSandboxRateChannelDiscounts = pricingSandboxRateChannelDiscounts,
        features = Option(Seq(DisableDispatchingLogic)),
      )

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(300 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance),
            ),
            27916 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
              commissions = Map(300 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
              roomRates = Seq(com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance),
            ),
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext

      val (updatedSilo, updatedContext) =
        Await.result(flow.applyPricingSimulationForYpl(currentYplMetaSilo, currentYplContext, request), waitTime)

      request.isRateChannelSimulation must beTrue

      updatedSilo should_!= currentYplMetaSilo
      updatedContext should_!= currentYplContext

      updatedContext.request.simulationRequest.get.channels should_== Set(300, 400)
    }
    "should return offers with improved rate , passed in simulation" in {

      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxConfig = None,
        pricingSandboxRateChannelDiscounts = Some(Map.empty),
        pricingSandboxStackedRateChannels = Some(Seq.empty),
      )

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
            commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
            roomRates = Seq(com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance),
          ))),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext

      val (updatedSilo, updatedContext) =
        Await.result(flow.applyPricingSimulationForYpl(currentYplMetaSilo, currentYplContext, request), waitTime)

      request.isRateChannelSimulation must beTrue

      updatedSilo should_== currentYplMetaSilo
      updatedContext should_!= currentYplContext

      updatedContext.request.simulationRequest.get.channels.isEmpty should_== true
      flow.containFeatureFlag(request, DisableDispatchingLogic) should_== false
    }
    "work when pricing rate channel discount is not available and YplMetaSilo have offers with improvement" in {

      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxConfig = None,
        pricingSandboxRateChannelDiscounts = Some(Map.empty),
        pricingSandboxStackedRateChannels = Some(
          Seq(
            HotelChannel(channelId = 7,
                         channelDiscount = Some(10.90),
                         stackingType = Some(ChannelDiscountStackingType.AllowedStacking),
                         stackingChannelDiscount = Some(5.67)),
            HotelChannel(channelId = 10,
                         channelDiscount = Some(11.90),
                         stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
                         stackingChannelDiscount = Some(5.67)),
          )),
        improvementDiscounts = Some(
          Set(
            ImprovementDiscount(channelId = 2, baseDiscount = Some(12.0)),
            ImprovementDiscount(channelId = 21, baseDiscount = Some(12.0)),
            ImprovementDiscount(channelId = 3),
          )),
      )

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
            commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
            roomRates = Seq(com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
              channelRates = Seq(
                ChannelRate.defaultInstance.copy(
                  channelId = 2,
                  channelDiscountPerDay = Map(-1 -> 0.90),
                ),
                ChannelRate.defaultInstance.copy(
                  channelId = 3,
                  channelDiscountPerDay = Map(-1 -> 13.89),
                ),
              ),
            )),
            stackedChannelInfo =
              Seq(StackedChannelInfo(baseChannelId = 2,
                                     stackedDiscounts = Seq(StackedChannelDiscount(3, discountPercent = 15)))),
          ))),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext

      val (updatedSilo, updatedContext) =
        Await.result(flow.applyPricingSimulationForYpl(currentYplMetaSilo, currentYplContext, request), waitTime)

      request.isRateChannelSimulation must beTrue

      updatedSilo should_!= currentYplMetaSilo
      updatedContext should_!= currentYplContext

      val updatedChannelRates = updatedSilo.d.offers.values
        .flatMap(_.roomRates.flatMap { rate =>
          rate.channelRates.map(r => r.channelId -> r.channelDiscountPerDay(-1))
        })
        .toSet
      updatedChannelRates should_== Set((2, 12d), (3, 13.89d), (7, 10.90), (10, 11.90))
      updatedContext.request.simulationRequest.get.channels should_== Set(7, 10)

    }
    "stacking should follow the staking rules" in {

      val pricingSandboxRateChannelDiscounts = Seq(
        HotelChannel(
          channelId = 1,
          channelDiscount = Some(10d),
          stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
          stackingChannelDiscount = Some(10d),
        ),
        HotelChannel(
          channelId = 2,
          channelDiscount = Some(10d),
          stackingType = Some(ChannelDiscountStackingType.AllowedStacking),
          stackingChannelDiscount = Some(10d),
        ),
        HotelChannel(
          channelId = 3,
          channelDiscount = Some(10d),
          stackingType = Some(ChannelDiscountStackingType.AllowedStacking),
          stackingChannelDiscount = Some(10d),
        ),
        HotelChannel(
          channelId = 4,
          channelDiscount = Some(10d),
          stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
          stackingChannelDiscount = Some(10d),
        ),
        HotelChannel(
          channelId = 5,
          channelDiscount = Some(10d),
          stackingType = Some(ChannelDiscountStackingType.NoStacking),
          stackingChannelDiscount = Some(10d),
        ),
        HotelChannel(
          channelId = 6,
          channelDiscount = Some(10d),
          stackingType = None,
          stackingChannelDiscount = Some(10d),
        ),
        HotelChannel(
          channelId = 7,
          channelDiscount = Some(10d),
          stackingType = Some(ChannelDiscountStackingType.NoStacking),
          stackingChannelDiscount = Some(10d),
        ),
      )
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxConfig = None,
        pricingSandboxRateChannelDiscounts = Some(Map(1 -> 10d)),
        pricingSandboxStackedRateChannels = Some(pricingSandboxRateChannelDiscounts),
      )
      val rateSilo = WithMeta(aValidRateSilo.copy(
                                offers = Map(1 -> PropertyOffer.defaultInstance),
                              ),
                              meta = mock[SupplyHotelInfo])(request)
      val stackChannelInfoDataService = new StackChannelInfoDataService {
        override def getHotelStackChannelInfo(hotelId: TravelerType)(implicit
          ec: ExecutionContext): Future[HotelChannels] = Future.successful(
          HotelChannels(Set(
            HotelChannel(channelId = 6, channelDiscount = None, stackingType = None, stackingChannelDiscount = None),
            HotelChannel(channelId = 7, channelDiscount = None, stackingType = None, stackingChannelDiscount = None),
          )))
      }
      val dmcMetaDataServiceMock = new DmcMetaDataServiceMock {
        override def isDirectConnect(supplierId: SupplierId): Boolean = true
      }
      val flow = new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMock, stackChannelInfoDataService)
      val (updatedSilo, updatedContext) =
        Await.result(flow.applyPricingSimulationForYpl(rateSilo, getYplContext, request), waitTime)

      /**
        * * Verify Stacking rules
        */
      val stackingInfo = updatedSilo.d.offers.head._2.stackedChannelInfo
        .map(c => c.baseChannelId -> c.stackedDiscounts.map(_.channelId))
        .toMap

      stackingInfo.getOrElse(1, Seq.empty).sorted should_== Seq(2, 3)
      stackingInfo.getOrElse(2, Seq.empty).sorted should_== Seq(3)
      stackingInfo.getOrElse(3, Seq.empty).sorted should_== Seq(2)
      stackingInfo.getOrElse(4, Seq.empty).sorted should_== Seq(2, 3)
      stackingInfo.getOrElse(5, Seq.empty) should_== Seq()
      stackingInfo.getOrElse(7, Seq.empty) should_== Seq()

      updatedContext.request.simulationRequest.get.channels should_== Set(1, 2, 3, 4, 5, 6, 7)
      flow.containFeatureFlag(request, DisableDispatchingLogic) should_== false

    }
    "work with hotel dispatch info " in {

      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxConfig = None,
        pricingSandboxRateChannelDiscounts = Some(Map.empty),
        pricingSandboxStackedRateChannels = Some(
          Seq(
            HotelChannel(
              channelId = 4,
              channelDiscount = Some(10d),
              stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
              stackingChannelDiscount = Some(10d),
            ),
            HotelChannel(
              channelId = 5,
              channelDiscount = Some(10d),
              stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
              stackingChannelDiscount = Some(10d),
            ),
            HotelChannel(
              channelId = 6,
              channelDiscount = Some(10d),
              stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
              stackingChannelDiscount = Some(10d),
            ),
          )),
      )

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
            commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
            roomRates = Seq(com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance),
          ))),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext

      val stackChannelInfoDataService = new StackChannelInfoDataService {
        override def getHotelStackChannelInfo(hotelId: TravelerType)(implicit
          ec: ExecutionContext): Future[HotelChannels] = Future.failed(new Exception("Test exceptions"))
      }
      val dmcMetaDataServiceMock = new DmcMetaDataServiceMock {
        override def isDirectConnect(supplierId: SupplierId): Boolean = supplierId == 2
      }
      val flow = new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMock, stackChannelInfoDataService)

      val (updatedSilo, updatedContext) = Await.result(
        flow.applyPricingSimulationForYpl(withHotelDispatchInfo(currentYplMetaSilo), currentYplContext, request),
        waitTime)

      request.isRateChannelSimulation must beTrue

      updatedSilo should_!= currentYplMetaSilo
      updatedContext should_!= currentYplContext

      updatedContext.request.simulationRequest.get.channels should_== Set(4, 5, 6)

      val dispatchInfo = updatedSilo.hotelDispatchInfo
      dispatchInfo.get.dispatchInfos.size should_== 9

      dispatchInfo.get.dispatchInfos.count(x => x.dispatchedMasterChannel.baseChannelId == 4) should_== 2
      dispatchInfo.get.dispatchInfos.count(x => x.dispatchedMasterChannel.baseChannelId == 5) should_== 2
      dispatchInfo.get.dispatchInfos.count(x => x.dispatchedMasterChannel.baseChannelId == 6) should_== 2
      flow.containFeatureFlag(request, DisableDispatchingLogic) should_== false
    }
    "work with hotel dispatch info should work for improvement flag" in {

      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        improvementDiscounts =
          Some(Set(ImprovementDiscount(channelId = 4, baseDiscount = Some(21.78d), stackingDiscount = Some(10.12d)))),
      )

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(332 -> com.agoda.supply.calc.proto.PropertyOffer.defaultInstance.copy(
            commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
            roomRates = Seq(
              com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                channelRates = Seq(ChannelRate.defaultInstance.copy(
                  channelId = 4,
                  channelDiscountPerDay = Map(-1 -> 10.00d),
                )),
              )),
          ))),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext

      val stackChannelInfoDataService = new StackChannelInfoDataService {
        override def getHotelStackChannelInfo(hotelId: TravelerType)(implicit
          ec: ExecutionContext): Future[HotelChannels] = Future.failed(new Exception("Test exceptions"))
      }
      val dmcMetaDataServiceMock = new DmcMetaDataServiceMock {
        override def isDirectConnect(supplierId: SupplierId): Boolean = supplierId == 2
      }
      val flow = new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMock, stackChannelInfoDataService)
      val (updatedSilo, updatedContext) = Await.result(
        flow.applyPricingSimulationForYpl(withHotelDispatchInfo(currentYplMetaSilo), currentYplContext, request),
        waitTime)

      request.isRateChannelSimulation must beTrue

      updatedSilo should_!= currentYplMetaSilo
      updatedContext should_!= currentYplContext

      val updatedRate = updatedSilo.d.offers.flatMap(_._2.roomRates.flatMap(_.channelRates)).head
      updatedRate.channelId should_== 4
      updatedRate.channelDiscountPerDay(-1) should_== 21.78d
    }
    "feature flag should work as expected " in {
      val request = aValidSupplyBaseRequest
        .withSimulationDataForRateChannel(
          pricingSandboxConfig = None,
          pricingSandboxRateChannelDiscounts = Some(Map.empty),
          pricingSandboxStackedRateChannels = Some(
            Seq(
              HotelChannel(
                channelId = 4,
                channelDiscount = Some(10d),
                stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
                stackingChannelDiscount = Some(10d),
              ),
            )),
          features = Some(Seq(DisableDispatchingLogic)),
        )
        .build

      val stackChannelInfoDataService = new StackChannelInfoDataService {
        override def getHotelStackChannelInfo(hotelId: TravelerType)(implicit
          ec: ExecutionContext): Future[HotelChannels] = Future.failed(new Exception("Test exceptions"))
      }
      val dmcMetaDataServiceMock = new DmcMetaDataServiceMock {
        override def isDirectConnect(supplierId: SupplierId): Boolean = supplierId == 2
      }
      val flow = new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMock, stackChannelInfoDataService)
      val disabled = flow.containFeatureFlag(request, DisableDispatchingLogic)

      disabled should_== true
    }

    "getActiveRateChannelForImprovement should work as expected" in {
      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect2, emptyStackChannelInfoDataService)
      val activeRateChannels = Set(2, 3, 5, 6)
      val improvementRequest = Some(
        Set(
          ImprovementDiscount(1),
          ImprovementDiscount(2),
          ImprovementDiscount(3),
          ImprovementDiscount(4),
        ))
      val activeImprovement =
        flow.getActiveRateChannelForImprovement(improvementRequest, activeRateChannels).map(_.channelId)

      activeImprovement should_== Set(2, 3)

      flow.getActiveRateChannelForImprovement(Some(Set.empty[ImprovementDiscount]), activeRateChannels) should_== Set
        .empty[ImprovementDiscount]
      flow.getActiveRateChannelForImprovement(None, activeRateChannels) should_== Set.empty[ImprovementDiscount]
    }
    "getNonActiveRateChannelForSimulation should work as expected " in {
      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect2, emptyStackChannelInfoDataService)
      val dummy =
        HotelChannel(channelId = -1, channelDiscount = None, stackingType = None, stackingChannelDiscount = None)
      val activeRateChannels = Set(2, 3)
      val simulatedRateChannel = Seq(
        dummy.copy(channelId = 1),
        dummy.copy(channelId = 2),
        dummy.copy(channelId = 3),
        dummy.copy(channelId = 4),
        dummy.copy(channelId = 5),
      )

      flow
        .getNonActiveRateChannelForSimulation(Some(simulatedRateChannel), activeRateChannels)
        .map(_.channelId) should_== Seq(1, 4, 5)
      flow
        .getNonActiveRateChannelForSimulation(Some(Seq.empty), activeRateChannels)
        .map(_.channelId) should_== Seq.empty
      flow.getNonActiveRateChannelForSimulation(None, activeRateChannels).map(_.channelId) should_== Seq.empty

    }
    "updateDiscountForImprovement should work as expected" in {
      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect2, emptyStackChannelInfoDataService)
      val hotelChannel = HotelChannel(
        channelId = 1,
        channelDiscount = Some(10.00),
        stackingType = Some(ChannelDiscountStackingType.StackBaseOnly),
        stackingChannelDiscount = Some(5.00),
      )

      flow.updateDiscountForImprovement(
        Set(ImprovementDiscount(1, Some(11.90), None)),
        HotelChannels(Set(hotelChannel)),
      ) should_==
        HotelChannels(
          Set(
            hotelChannel.copy(channelDiscount = Some(11.90)),
          ))

      flow.updateDiscountForImprovement(
        Set(ImprovementDiscount(1, None, Some(15.67))),
        HotelChannels(Set(hotelChannel)),
      ) should_==
        HotelChannels(
          Set(
            hotelChannel.copy(stackingChannelDiscount = Some(15.67)),
          ))

      flow.updateDiscountForImprovement(
        Set(ImprovementDiscount(1, Some(11.90), Some(15.67))),
        HotelChannels(Set(hotelChannel)),
      ) should_==
        HotelChannels(
          Set(
            hotelChannel.copy(
              channelDiscount = Some(11.90),
              stackingChannelDiscount = Some(15.67),
            ),
          ))

      flow.updateDiscountForImprovement(
        Set(ImprovementDiscount(1)),
        HotelChannels(Set(hotelChannel)),
      ) should_==
        HotelChannels(Set(hotelChannel))

      flow.updateDiscountForImprovement(Set(ImprovementDiscount(10, Some(12.90), Some(16.67))),
                                        HotelChannels(Set(hotelChannel))) should_==
        HotelChannels(Set(hotelChannel))
    }
    "rc deactivation should work as expected for A side" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        deactivateActiveRc = Some(4),
        features = Some(Seq(SameDayExperiment)),
      )
      val default = com.agoda.supply.calc.proto.PropertyOffer.defaultInstance

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(332 -> default.copy(
            commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
            roomRates = Seq(
              com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                channelRates = Seq(ChannelRate.defaultInstance.copy(
                  channelId = 4,
                  channelDiscountPerDay = Map(-1 -> 10.00d),
                )),
              ),
              com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                channelRates = Seq(ChannelRate.defaultInstance.copy(
                  channelId = 6,
                  channelDiscountPerDay = Map(-1 -> 11.00d),
                )),
              ),
            ),
            stackedChannelInfo = Seq(
              StackedChannelInfo(baseChannelId = 4,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 6, discountPercent = 10.90d))),
              StackedChannelInfo(baseChannelId = 6,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 4, discountPercent = 10.90d))),
              StackedChannelInfo(baseChannelId = 6,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 5, discountPercent = 10.90d))),
            ),
          ))),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext

      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect2, failedStackChannelInfoDataService)
      val (updatedSilo, updatedContext) = Await.result(
        flow.applyPricingSimulationForYpl(withHotelDispatchInfo(currentYplMetaSilo), currentYplContext, request),
        waitTime)

      request.isRateChannelSimulation must beTrue

      // updatedSilo should_!= currentYplMetaSilo
      // updatedContext should_!= currentYplContext

      val updatedRate = updatedSilo.d.offers.flatMap(_._2.roomRates.flatMap(_.channelRates)).head
      updatedRate.channelId should_== 6
      updatedRate.channelDiscountPerDay(-1) should_== 11.0d

      val stackingInfo = updatedSilo.d.offers.head._2.stackedChannelInfo.toSet
      stackingInfo should_== Set(
        StackedChannelInfo(baseChannelId = 6, stackedDiscounts = Seq()),
        StackedChannelInfo(baseChannelId = 6,
                           stackedDiscounts = Seq(StackedChannelDiscount(channelId = 5, discountPercent = 10.90d))),
      )

    }
    "rc deactivation should work as expected for A side without rate channel not passed" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        deactivateActiveRc = None,
        features = Some(Seq(SameDayExperiment)),
      )
      val default = com.agoda.supply.calc.proto.PropertyOffer.defaultInstance

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(332 -> default.copy(
            commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
            roomRates = Seq(
              com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                channelRates = Seq(ChannelRate.defaultInstance.copy(
                  channelId = 4,
                  channelDiscountPerDay = Map(-1 -> 10.00d),
                )),
              ),
              com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                channelRates = Seq(ChannelRate.defaultInstance.copy(
                  channelId = 6,
                  channelDiscountPerDay = Map(-1 -> 11.00d),
                )),
              ),
            ),
            stackedChannelInfo = Seq(
              StackedChannelInfo(baseChannelId = 4,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 6, discountPercent = 10.90d))),
              StackedChannelInfo(baseChannelId = 6,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 4, discountPercent = 10.90d))),
              StackedChannelInfo(baseChannelId = 6,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 5, discountPercent = 10.90d))),
            ),
          ))),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext

      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect2, failedStackChannelInfoDataService)
      val (updatedSilo, updatedContext) =
        Await.result(flow.applyPricingSimulationForYpl(currentYplMetaSilo, currentYplContext, request), waitTime)

      request.isRateChannelSimulation must beTrue

      updatedSilo should_== rateSilo

    }
    "rc deactivation should work as expected for A side without rate channel different passed" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        deactivateActiveRc = Option(7),
        features = Some(Seq(SameDayExperiment)),
      )
      val default = com.agoda.supply.calc.proto.PropertyOffer.defaultInstance

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(332 -> default.copy(
            commissions = Map(100 -> com.agoda.supply.calc.proto.Commission.defaultInstance),
            roomRates = Seq(
              com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                channelRates = Seq(ChannelRate.defaultInstance.copy(
                  channelId = 4,
                  channelDiscountPerDay = Map(-1 -> 10.00d),
                )),
              ),
              com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                channelRates = Seq(ChannelRate.defaultInstance.copy(
                  channelId = 6,
                  channelDiscountPerDay = Map(-1 -> 11.00d),
                )),
              ),
            ),
            stackedChannelInfo = Seq(
              StackedChannelInfo(baseChannelId = 4,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 6, discountPercent = 10.90d))),
              StackedChannelInfo(baseChannelId = 6,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 4, discountPercent = 10.90d))),
              StackedChannelInfo(baseChannelId = 6,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 5, discountPercent = 10.90d))),
            ),
          ))),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext

      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect2, failedStackChannelInfoDataService)
      val (updatedSilo, updatedContext) =
        Await.result(flow.applyPricingSimulationForYpl(currentYplMetaSilo, currentYplContext, request), waitTime)

      request.isRateChannelSimulation must beTrue

      updatedSilo should_== rateSilo

    }
    "rc deactivation should work as expected for B side" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxStackedRateChannels = Some(
          Seq(
            HotelChannel(
              channelId = 4,
              channelDiscount = Some(15.0),
              stackingType = Some(AllowedStacking),
              stackingChannelDiscount = Some(10.0),
            ))),
        features = Some(Seq(SameDayExperiment, DebugMode)),
      )
      val default = com.agoda.supply.calc.proto.PropertyOffer.defaultInstance

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> default.copy(
              commissions =
                Map(100 -> Commission.defaultInstance, 120 -> Commission.defaultInstance.copy(channelId = 4)),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(
                    ChannelRate.defaultInstance.copy(
                      channelId = 4,
                      channelDiscountPerDay = Map(-1 -> 10.00d),
                    )),
                ),
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(
                    ChannelRate.defaultInstance.copy(
                      channelId = 6,
                      channelDiscountPerDay = Map(-1 -> 11.00d),
                    )),
                  rateCategoryId = 10,
                ),
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(
                    ChannelRate.defaultInstance.copy(
                      channelId = 6,
                      channelDiscountPerDay = Map(-1 -> 11.00d),
                    )),
                ),
              ),
              stackedChannelInfo = Seq(
                StackedChannelInfo(baseChannelId = 4,
                                   stackedDiscounts =
                                     Seq(StackedChannelDiscount(channelId = 6, discountPercent = 10.90d))),
                StackedChannelInfo(baseChannelId = 6,
                                   stackedDiscounts =
                                     Seq(StackedChannelDiscount(channelId = 4, discountPercent = 10.90d))),
              ),
            ),
            100 -> default,
            101 -> default,
          ),
        ),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext
      val stackChannelInfoDataService = new StackChannelInfoDataService {
        override def getHotelStackChannelInfo(hotelId: TravelerType)(implicit
          ec: ExecutionContext): Future[HotelChannels] = Future.successful(
          HotelChannels(
            Set(
              HotelChannel(channelId = 6,
                           channelDiscount = Some(10.0),
                           stackingType = Some(AllowedStacking),
                           stackingChannelDiscount = Some(10.0))),
          ))
      }
      val dmcMetaDataServiceMock = new DmcMetaDataServiceMock {
        override def isDirectConnect(supplierId: SupplierId): Boolean = supplierId == 100
      }

      val flow = new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMock, stackChannelInfoDataService)
      val (updatedSilo, updatedContext) = Await.result(
        flow.applyPricingSimulationForYpl(withHotelDispatchInfo(currentYplMetaSilo), currentYplContext, request),
        waitTime)

      request.isRateChannelSimulation must beTrue

      updatedSilo should_!= currentYplMetaSilo
      updatedContext should_!= currentYplContext

      val updatedRate = updatedSilo.d.offers.flatMap(_._2.roomRates.flatMap(_.channelRates))
      updatedRate.map(_.channelId).toSet should_== Set(4, 6)

      val stackingInfo = updatedSilo.d.offers.head._2.stackedChannelInfo.map { stk =>
        stk.baseChannelId -> stk.stackedDiscounts.map(_.channelId).toSet
      }.toMap
      stackingInfo should_== Map(4 -> Set(6), 6 -> Set(4))

    }
    "rc deactivation should work as expected for B side with no commission" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        pricingSandboxStackedRateChannels = Some(
          Seq(
            HotelChannel(
              channelId = 4,
              channelDiscount = Some(15.0),
              stackingType = Some(AllowedStacking),
              stackingChannelDiscount = Some(10.0),
            ))),
        features = Some(Seq(SameDayExperiment, DebugMode)),
      )
      val default = com.agoda.supply.calc.proto.PropertyOffer.defaultInstance

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(332 -> default.copy(
            commissions = Map(100 -> Commission.defaultInstance),
            roomRates = Seq(
              com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                channelRates = Seq(ChannelRate.defaultInstance.copy(
                  channelId = 4,
                  channelDiscountPerDay = Map(-1 -> 10.00d),
                )),
              ),
              com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                channelRates = Seq(ChannelRate.defaultInstance.copy(
                  channelId = 6,
                  channelDiscountPerDay = Map(-1 -> 11.00d),
                )),
                rateCategoryId = 10,
              ),
              com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                channelRates = Seq(ChannelRate.defaultInstance.copy(
                  channelId = 6,
                  channelDiscountPerDay = Map(-1 -> 11.00d),
                )),
              ),
            ),
            stackedChannelInfo = Seq(
              StackedChannelInfo(baseChannelId = 4,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 6, discountPercent = 10.90d))),
              StackedChannelInfo(baseChannelId = 6,
                                 stackedDiscounts = Seq(StackedChannelDiscount(channelId = 4, discountPercent = 10.90d))),
            ),
          ))),
        meta = mock[SupplyHotelInfo],
      )(request)

      val currentYplMetaSilo = rateSilo
      val currentYplContext = getYplContext

      val stackChannelInfoDataService = new StackChannelInfoDataService {
        override def getHotelStackChannelInfo(hotelId: TravelerType)(implicit
          ec: ExecutionContext): Future[HotelChannels] = Future.successful(
          HotelChannels(
            Set(
              HotelChannel(channelId = 6,
                           channelDiscount = Some(10.0),
                           stackingType = Some(AllowedStacking),
                           stackingChannelDiscount = Some(10.0))),
          ))
      }

      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect2, stackChannelInfoDataService)
      val (updatedSilo, updatedContext) = Await.result(
        flow.applyPricingSimulationForYpl(withHotelDispatchInfo(currentYplMetaSilo), currentYplContext, request),
        waitTime)

      request.isRateChannelSimulation must beTrue

      updatedSilo should_!= currentYplMetaSilo
      updatedContext should_!= currentYplContext

      val updatedRate = updatedSilo.d.offers.flatMap(_._2.roomRates.flatMap(_.channelRates))
      updatedRate.map(_.channelId).toSet should_== Set(4, 6)

      val stackingInfo = updatedSilo.d.offers.head._2.stackedChannelInfo.map { stk =>
        stk.baseChannelId -> stk.stackedDiscounts.map(_.channelId).toSet
      }.toMap
      stackingInfo should_== Map(4 -> Set(6), 6 -> Set(4))

    }

    "remove offers for rc should return unmodified offers if rc array is empty" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel()
      val default = com.agoda.supply.calc.proto.PropertyOffer.defaultInstance

      val rateSilo = WithMeta(
        aValidRateSilo.copy(offers = Map(332 -> default)),
        meta = mock[SupplyHotelInfo],
      )(request)
      val flow = new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect100,
                                                          emptyStackChannelInfoDataService)

      val (updatedRate, rps) =
        flow.removeOffersForSimulatedRc(yplMetaSilo = rateSilo, simulatedRcsOption = Some(Seq.empty))

      updatedRate should_== rateSilo
      rps.isEmpty should_== true

    }
    "remove offers for rc should return  offers without rc if rc array is nonempty" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel()
      val default = com.agoda.supply.calc.proto.PropertyOffer.defaultInstance

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> default.copy(
              commissions = Map(100 -> Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(
                    ChannelRate.defaultInstance.copy(
                      channelId = 4,
                      channelDiscountPerDay = Map(-1 -> 10.00d),
                    )),
                ),
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(
                    ChannelRate.defaultInstance.copy(
                      channelId = 6,
                      channelDiscountPerDay = Map(-1 -> 11.00d),
                    )),
                  rateCategoryId = 10,
                ),
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(
                    ChannelRate.defaultInstance.copy(
                      channelId = 5,
                      channelDiscountPerDay = Map(-1 -> 11.00d),
                    )),
                ),
              ),
              stackedChannelInfo = Seq(
                StackedChannelInfo(baseChannelId = 4,
                                   stackedDiscounts = Seq(StackedChannelDiscount(channelId = 6, discountPercent = 10.90d))),
                StackedChannelInfo(baseChannelId = 6,
                                   stackedDiscounts = Seq(StackedChannelDiscount(channelId = 4, discountPercent = 10.90d))),
              ),
            ),
            100 -> default.copy(
              roomRates = Seq(
                RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(
                    ChannelRate.defaultInstance.copy(
                      channelId = 5,
                      channelDiscountPerDay = Map(-1 -> 11.00d),
                    )),
                ),
              ),
            ),
            1001 -> default,
          )),
        meta = mock[SupplyHotelInfo],
      )(request)
      val flow = new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect100,
                                                          emptyStackChannelInfoDataService)

      val (updatedRate, rps) = flow.removeOffersForSimulatedRc(yplMetaSilo = rateSilo, simulatedRcsOption = Some(Seq(4)))

      updatedRate.d.offers(332).roomRates.size should_== 2
      updatedRate.d.offers(332).roomRates.flatMap(_.channelRates.map(_.channelId)).toSet should_== Set(5, 6)
      updatedRate.d.offers(332).stackedChannelInfo.map(_.baseChannelId) should_== Seq(6)
      rps should_== Seq(0)

    }
    "createSandboxOffers rc should return  new offers " in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel()
      val default = com.agoda.supply.calc.proto.PropertyOffer.defaultInstance

      val rateSilo = WithMeta(
        aValidRateSilo.copy(
          offers = Map(
            332 -> default.copy(
              commissions = Map(100 -> Commission.defaultInstance),
              roomRates = Seq(
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(
                    ChannelRate.defaultInstance.copy(
                      channelId = 4,
                      channelDiscountPerDay = Map(-1 -> 10.00d),
                    )),
                ),
                com.agoda.supply.calc.proto.RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(
                    ChannelRate.defaultInstance.copy(
                      channelId = 6,
                      channelDiscountPerDay = Map(-1 -> 11.00d),
                    )),
                  rateCategoryId = 10,
                ),
              ),
              stackedChannelInfo = Seq(
                StackedChannelInfo(baseChannelId = 4,
                                   stackedDiscounts = Seq(StackedChannelDiscount(channelId = 6, discountPercent = 10.90d))),
                StackedChannelInfo(baseChannelId = 6,
                                   stackedDiscounts = Seq(StackedChannelDiscount(channelId = 4, discountPercent = 10.90d))),
              ),
            ),
            100 -> default.copy(
              roomRates = Seq(
                RoomRateCategory.defaultInstance.copy(
                  channelRates = Seq(
                    ChannelRate.defaultInstance.copy(
                      channelId = 5,
                      channelDiscountPerDay = Map(-1 -> 11.00d),
                    )),
                ),
              ),
            ),
            1001 -> default,
          )),
        meta = mock[SupplyHotelInfo],
      )(request)
      val flow = new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect100,
                                                          emptyStackChannelInfoDataService)

      val updatedRate = Await.result(
        flow.createSandboxOffers(
          request = request,
          yplMetaSilo = rateSilo,
          discountMap = Map.empty,
          simulationChannelInfo = Seq(
            HotelChannel(
              channelId = 2,
              channelDiscount = Some(10.0),
              stackingType = Option(AllowedStacking),
              stackingChannelDiscount = Some(10.0),
            )),
          improvementRateChannels = Set.empty[ImprovementDiscount],
          simulatedChannelRatePlans = Seq.empty,
        ),
        waitTime,
      )

      val originalRates = updatedRate.d.offers(332).roomRates.filter(_.channelRates.exists(_.channelId != 2))
      val newAdded = updatedRate.d.offers(332).roomRates.filter(_.channelRates.exists(_.channelId == 2))

      originalRates.size should_== 2
      newAdded.size should_== 2

    }
    "updateCommissions should work for normal flow" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel()
      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect1, emptyStackChannelInfoDataService)
      val commissions = Map(-1 -> Commission.defaultInstance.copy(channelId = 54))

      val updated = flow.updateCommissions(commissions, Map(2 -> 10.0))(request)

      updated.size should_== 2
      updated.map(_._2.channelId).toSet should_== Set(54, 2)

    }
    "updateCommissions should work for SameDay Experiment" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        features = Option(Seq(SameDayExperiment)),
      )
      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect1, emptyStackChannelInfoDataService)

      val commissions = Map(1 -> Commission.defaultInstance.copy(channelId = 54))

      val updated = flow.updateCommissions(commissions, Map(54 -> 10.0))(request)

      updated.size should_== 1
      updated should_== commissions

    }
    "updateCommissionPerDay should work for SameDay Experiment" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        features = Option(Seq(SameDayExperiment)),
      )
      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect1, emptyStackChannelInfoDataService)

      val commissionsPerDay = Map(
        -1 -> Identifiers.defaultInstance.copy(
          identifiers = Seq(1, 2),
        ))
      val commissions = Map(1 -> Commission.defaultInstance.copy(channelId = 54),
                            10 -> Commission.defaultInstance.copy(channelId = 2),
                            11 -> Commission.defaultInstance.copy(channelId = 2))

      val updated = flow.updateCommissionPerDay(commissionsPerDay, 54, 2, commissions)(request)

      updated(-1).identifiers.toSet should_== Set(10, 11)

      val updated2 = flow.updateCommissionPerDay(commissionsPerDay, 54, 3, commissions)(request)

      updated2(-1).identifiers should_== Seq()

    }
    "updateCommissionPerDay should work for normal flow" in {
      val request = aValidSupplyBaseRequest.withSimulationDataForRateChannel(
        features = Some(Seq(DebugMode)),
      )
      val flow =
        new RateChannelSimulationPricingFlowImpl(dmcMetaDataServiceMockDirectConnect1, emptyStackChannelInfoDataService)

      flow.logRoomRates(aValidSupplyBaseRequest.withSimulationDataForRateChannel(),
                        WithMeta(aValidRateSilo, meta = mock[SupplyHotelInfo])(request),
                        "")

      val commissionsPerDay = Map(
        -1 -> Identifiers.defaultInstance.copy(
          identifiers = Seq(1, 2),
        ))
      val commissions = Map(1 -> Commission.defaultInstance.copy(channelId = 54))

      val updated2 = flow.updateCommissionPerDay(commissionsPerDay, 54, 3, commissions)(request)

      updated2(-1).identifiers should_== Seq(630003)

    }
  }

  private def getYplContext: YplContext = {
    val yplRequest = YplRequest(
      searchId = "test",
      checkIn = DateTime.now(),
      checkOut = DateTime.now(),
      currency = "USD",
      whitelabelSetting = mock[WhitelabelSetting],
      supplierFeatures = mock[SupplierFeatures],
    )
    YplContextMock(
      request = yplRequest,
      experimentContext = mock[ExperimentContext],
      exchangeRateCtx = mock[context.ExchangeRateContext],
      compositeChannelContext = mock[context.CompositeChannelContext],
      surchargeDataServiceContext = mock[context.SurchargeDataServiceContext],
      hadoopContext = mock[context.HadoopContext],
      aggregateReporter = AggregateReporter.noop(),
      holidayCalendarContext = mock[context.RoomLinkageHolidayCalendarContext],
      pulseCampaignMetaCtx = mock[context.PulseCampaignMetaContext],
      masterChannelCategoryContext = mock[context.MasterChannelCategoryContext],
    )
  }

  private def withHotelDispatchInfo(meta: WithMeta[RateSilo, SupplyHotelInfo]) = {
    val hotelId = 123456
    val fencedRatePair = aValidFencedRatePair.build
    val dispatchInfoFoeSupplier1 = DispatchInfo(
      hotelId = hotelId,
      dispatchDmc = 332,
      dispatchedMasterChannel = DFCompositeChannel(1, Set.empty, 1),
      dispatchType = 1,
      fencedRatePair = fencedRatePair,
    )
    val dispatchInfoFoeSupplier2 = DispatchInfo(
      hotelId = hotelId,
      dispatchDmc = 2,
      dispatchedMasterChannel = DFCompositeChannel(2, Set.empty, 2),
      dispatchType = 1,
      fencedRatePair = fencedRatePair,
    )
    val dispatchInfoFoeSupplier3 = DispatchInfo(
      hotelId = hotelId,
      dispatchDmc = 3,
      dispatchedMasterChannel = DFCompositeChannel(3, Set.empty, 3),
      dispatchType = 1,
      fencedRatePair = fencedRatePair,
    )

    meta.copy(
      supplierDispatchChannelHolder = meta.supplierDispatchChannelHolder.copy(
        hotelDispatchInfo = Some(
          HotelDispatchInfo(hotelId, Seq(dispatchInfoFoeSupplier1, dispatchInfoFoeSupplier2, dispatchInfoFoeSupplier3))),
      ),
    )
  }

}
