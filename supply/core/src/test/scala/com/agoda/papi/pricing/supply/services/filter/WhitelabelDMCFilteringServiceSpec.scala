package com.agoda.papi.pricing.supply.services.filter

import com.agoda.papi.pricing.metadata.SupplierCCMapping
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.ypl.models.YPLTestDataBuilders
import org.specs2.mutable.SpecificationWithJUnit
import services.WhitelabelDMCFilteringServiceImpl
import com.agoda.papi.pricing.supply.models.consts.ABTest
import org.joda.time.DateTime
import org.scalatest.prop.Tables.Table

class WhitelabelDMCFilteringServiceSpec
  extends SpecificationWithJUnit
    with SupplyModelTestDataBuilders
    with YPLTestDataBuilders {

  val suppliers = Seq(SupplierCCMapping(1), SupplierCCMapping(2), SupplierCCMapping(3), SupplierCCMapping(29014))
  val hotel = aValidSupplyHotelInfo.withSupplierCCMappings(suppliers).withCountryCode("JP").build
  val directSupplyHotelOutsideOfJapan = aValidSupplyHotelInfo
    .withSupplierCCMappings(Seq(SupplierCCMapping(332)))
    .withCountryCode("TH")
    .withMasterDmcId(Some(332))
    .build
  val directSupplyHotelInJapan = aValidSupplyHotelInfo
    .withSupplierCCMappings(Seq(SupplierCCMapping(332)))
    .withCountryCode("JP")
    .withMasterDmcId(Some(332))
    .build
  val jtbSupplyHotelInJapan = aValidSupplyHotelInfo
    .withSupplierCCMappings(Seq(SupplierCCMapping(332), SupplierCCMapping(29014)))
    .withCountryCode("JP")
    .withMasterDmcId(Some(29014))
    .build

  val sv = new WhitelabelDMCFilteringServiceImpl {}

  "WhitelabelDMCFilteringService" should {
    "filter dmc correctly when supplier list non empty" in {
      val actual =
        sv.filterSupportedSuppliersWithWhitelabelSetting(hotel, Set(1, 2), 1, aValidOccInfo, 1007)(aValidSupplyContext)
      val expectedSuppliers = Seq(SupplierCCMapping(1), SupplierCCMapping(2))
      actual.suppliers should_== expectedSuppliers
    }

    "filter dmc correctly when supplier supplier list non empty and not match with hotel mapping" in {
      val actual = sv.filterSupportedSuppliersWithWhitelabelSetting(hotel, Set(4, 5, 6), 1, aValidOccInfo, 1007)(
        aValidSupplyContext)
      actual.suppliers should_== Nil
    }

    "do not filter dmc when supplier supplier list empty" in {
      val actual =
        sv.filterSupportedSuppliersWithWhitelabelSetting(hotel, Set.empty, 1, aValidOccInfo, 1)(aValidSupplyContext)
      actual.suppliers should_== suppliers
    }

    "do not filter jtb wl dmc when whitelabel agoda with multiple room search with childRate" in {
      val actual = sv.filterSupportedSuppliersWithWhitelabelSetting(
        hotel,
        Set.empty,
        1,
        aValidOccInfo.withRooms(2).withChildren(Some(aValidChildren.withChildren(List(1)))),
        1,
      )(aValidSupplyContext)
      actual.suppliers should_== suppliers
    }

    "do not filter jtb wl dmc when whitelabel jtb with multiple room search with childRate" in {
      val actual = sv.filterSupportedSuppliersWithWhitelabelSetting(
        hotel,
        Set.empty,
        2,
        aValidOccInfo.withRooms(2).withChildren(Some(aValidChildren.withChildren(List(1)))),
        1)(aValidSupplyContext)
      actual.suppliers should_== suppliers
    }

    "filter when the hotel is direct supply and located in a blocked country" in {
      val actual = sv.filterSupportedSuppliersWithWhitelabelSetting(
        directSupplyHotelInJapan,
        Set(332, 29014),
        4,
        aValidOccInfo.withRooms(2).withChildren(Some(aValidChildren.withChildren(List(1)))),
        1,
        List("JP", "MY"),
        Set(332),
        isSellingExternalSuppliersEnabled = true,
      )(aValidSupplyContext)

      actual.suppliers should_== List.empty
    }

    "don't filter when the hotel is provided by jtb and in Japan" in {
      val actual = sv.filterSupportedSuppliersWithWhitelabelSetting(
        jtbSupplyHotelInJapan,
        Set(332, 29014),
        4,
        aValidOccInfo.withRooms(2).withChildren(Some(aValidChildren.withChildren(List(1)))),
        1,
        List("JP", "MY"),
        Set(332),
        isSellingExternalSuppliersEnabled = true,
      )(aValidSupplyContext)

      actual.suppliers should_== Seq(SupplierCCMapping(332), SupplierCCMapping(29014))
    }

    "filter correctly when outbound request on Rurubu with lead time constraint" in {

      val testCases = Table(
        ("isJTBFP1662InB", "minimumCheckInLeadTimeSettings", "expectedSuppliers", "bookindDate", "checkinDate"),
        (true,
         None,
         Seq(SupplierCCMapping(332)),
         new DateTime(2025, 1, 1, 10, 0, 0, 0),
         new DateTime(2025, 1, 2, 10, 0, 0, 0)),
        (true,
         None,
         Seq(SupplierCCMapping(332)),
         new DateTime(2025, 1, 1, 10, 0, 0, 0),
         new DateTime(2025, 1, 4, 10, 0, 0, 0)),
        (true, Some(2), Seq(), new DateTime(2025, 1, 1, 10, 0, 0, 0), new DateTime(2025, 1, 2, 10, 0, 0, 0)),
        (true,
         Some(2),
         Seq(SupplierCCMapping(332)),
         new DateTime(2025, 1, 1, 10, 0, 0, 0),
         new DateTime(2025, 1, 3, 10, 0, 0, 0)),
        (true,
         Some(2),
         Seq(SupplierCCMapping(332)),
         new DateTime(2025, 1, 1, 10, 0, 0, 0),
         new DateTime(2025, 1, 4, 10, 0, 0, 0)),
        (false,
         Some(2),
         Seq(SupplierCCMapping(332)),
         new DateTime(2025, 1, 1, 10, 0, 0, 0),
         new DateTime(2025, 1, 2, 10, 0, 0, 0)),
        (false,
         Some(2),
         Seq(SupplierCCMapping(332)),
         new DateTime(2025, 1, 1, 10, 0, 0, 0),
         new DateTime(2025, 1, 3, 10, 0, 0, 0)),
        (false,
         Some(2),
         Seq(SupplierCCMapping(332)),
         new DateTime(2025, 1, 1, 10, 0, 0, 0),
         new DateTime(2025, 1, 4, 10, 0, 0, 0)),
      )

      testCases.forEvery {
        (isJTBFP1662InB, minimumCheckInLeadTimeSettings, expectedSuppliers, bookindDate, checkinDate) =>
          val whitelabelSettings = aValidwhitelabelSetting.withMinimumCheckInLeadTime(minimumCheckInLeadTimeSettings)
          val supplyContextWithWhitelabelSettings =
            aValidSupplyContext.withWhitelabelSetting(whitelabelSettings).withLeadTimeSettings(bookindDate, checkinDate)

          val supplyContext =
            if (isJTBFP1662InB)
              supplyContextWithWhitelabelSettings.withBExperiment(ABTest.ENABLE_LEAD_TIME_CONSTRAINT_FOR_EXT_SUPPLY)
            else supplyContextWithWhitelabelSettings

          val actual = sv.filterSupportedSuppliersWithWhitelabelSetting(
            directSupplyHotelOutsideOfJapan,
            Set(332, 29014),
            4,
            aValidOccInfo.withRooms(2).withChildren(Some(aValidChildren.withChildren(List(1)))),
            1,
            List("JP", "MY"),
            Set(332),
            isSellingExternalSuppliersEnabled = true,
          )(supplyContext.build)
          actual.suppliers must_== (expectedSuppliers)
      }
      ok
    }
  }
}
