package models.ratechannel

import com.agoda.papi.enums.room.ChannelDiscountStackingType
import com.agoda.papi.pricing.supply.simulation.models.{HotelChannel, HotelChannels}
import com.agoda.supply.calc.proto.StackedChannelInfo.StackedChannelDiscount
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

class HotelChannelsSpec extends SpecificationWithJUnit with Mockito {

  "HotelChannelsSpec" should {
    "return empty set when no channels are provided" in {
      val hotelChannels = HotelChannels(Set.empty)
      hotelChannels.getChannel(9).isEmpty must beTrue
    }
    "return channel HotelChannel mapping when channels are provided" in {
      val hotelChannels =
        HotelChannels(Set(HotelChannel(9, Some(10.0), Some(ChannelDiscountStackingType.NoStacking), Some(15.0))))
      hotelChannels.getChannel(9) must beSome(
        HotelChannel(9, Some(10.0), Some(ChannelDiscountStackingType.NoStacking), Some(15.0)))
    }
    "return channel HotelChannel mapping when channels are provided with None values" in {
      val hotelChannels = HotelChannels(Set(HotelChannel(9, None, None, None)))
      hotelChannels.channels must beEqualTo(Set(HotelChannel(9, None, None, None)))
    }
    "return combined HotelChannels when combined with other HotelChannels" in {
      val hotelChannels = HotelChannels(Set(HotelChannel(9, None, None, None))).combine(
        HotelChannels(Set(HotelChannel(10, Some(10.0), Some(ChannelDiscountStackingType.NoStacking), Some(15.0)))))

      hotelChannels.channels must beEqualTo(
        Set(HotelChannel(9, None, None, None),
            HotelChannel(10, Some(10.0), Some(ChannelDiscountStackingType.NoStacking), Some(15.0))))
    }
    "return Seq when invoke getStackableChannelDiscounts with channelID" in {
      val hotels = HotelChannels(
        Set(
          HotelChannel(10, Some(10.0), Some(ChannelDiscountStackingType.AllowedStacking), Some(15.0)),
          HotelChannel(11, Some(11.0), Some(ChannelDiscountStackingType.AllowedStacking), Some(16.0)),
        ))
      hotels.getStackableChannelDiscounts(10) must beEqualTo(Seq(StackedChannelDiscount(11, 16.0)))
    }
  }
}
