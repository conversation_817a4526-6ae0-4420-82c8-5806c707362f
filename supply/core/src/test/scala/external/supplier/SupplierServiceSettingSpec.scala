package external.supplier

import com.agoda.papi.pricing.supply.configuration.SupplierServiceSettings
import com.typesafe.config.ConfigFactory
import org.specs2.mutable.SpecificationWithJUnit

/**
  * Created by nsarnsuwan on 11/29/2016 AD.
  */
class SupplierServiceSettingSpec extends SpecificationWithJUnit {
  "Experiment group config" should {
    val configStr = """
                      |supplier-service {
                      |  enabled = false,
                      |  languageWhiteList = [1,2,3]
                      |}
    """.stripMargin

    "load properly" in {
      val config = SupplierServiceSettings(ConfigFactory.parseString(configStr))

      config.enabled must_== false
      config.languageWhiteListSuppliers must_== Set(1, 2, 3)
    }
  }
}
