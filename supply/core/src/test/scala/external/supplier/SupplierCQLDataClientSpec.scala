package external.supplier

import akka.actor.ActorSystem
import com.agoda.commons.tracing.noop.NoOpTracingContext.tracer
import com.agoda.papi.pricing.supply.logic.supplier.{PushPullSupplierPricing, SupplierPricingPluginHelper}
import com.agoda.papi.pricing.supply.logic.supplier.SupplierPricingPluginHelper.{
  ExpContextToExpRunnerFlowContext,
  NoopCQLContext,
  createEnvironmentContext,
}
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders.{
  aValidSupplyBaseRequest,
  aValidSupplyContext,
}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.scapi.sqlserver.PriceReadRepository
import com.agoda.scapi.sqlserver.domain.{PriceReadResult, PriceResponse, ReadPullPriceKey}
import com.agoda.spapi.protobuf.demand.{ClientInfo, DemandRequest, SearchCriteria, TrafficInfo}
import com.agoda.supplier.client.SupplierClientSettings.{ColumnInfo, SupportedPullSupplier}
import com.agoda.supplier.client.{SupplierCQLDataClient, SupplierClient}
import com.agoda.supplier.core._
import com.agoda.supplier.models.config.{DragonflyPullConfig, SPLPullDynamicConfig}
import com.agoda.supplier.models.data.SupplierCacheHitSource
import com.agoda.supplier.models.{PullHotelWithMeta, SdaFilterInfo, SiteId, SupplierId}
import com.agoda.supplier.pull.client.{DemandServiceClient, PublishType}
import com.agoda.supplier.pull.request.DmcId
import com.agoda.supplier.services.{SiteIdDmcIdDemandFilter, SupplierExperimentServiceImpl}
import com.agoda.utils.hadoop.HadoopLogger
import com.agoda.utils.mocks.ExchangeDataServiceMock
import com.typesafe.config.ConfigFactory
import common.DemandAPIResponseStatus
import common.DemandAPIResponseStatus.RequestSuccess
import io.opentelemetry.api.OpenTelemetry
import mocks.{AggregateReporterMock, ReportingServiceMock}
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{any, anyString}
import org.mockito.Mockito
import org.scalatestplus.mockito.MockitoSugar.mock
import org.specs2.mutable.SpecificationWithJUnit
import sql.DB

import java.util.concurrent.TimeUnit
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.{Duration, FiniteDuration}
import scala.concurrent.{Await, Future}

class SupplierCQLDataClientSpec extends SpecificationWithJUnit {

  val noopReportingService = new ReportingServiceMock {}
  val measurementContext = SupplierPricingPluginHelper.createMeasurementContext(noopReportingService)
  val noopAggregateReporter = new AggregateReporterMock {}
  val aggregateContext = SupplierPricingPluginHelper.createAggregateContext(noopAggregateReporter)

  val conf = ConfigFactory.parseString("""
                                         |demand.env = "dev"
                                         |supplier-data-client.read-timeout = 1000 ms
                                         |""".stripMargin)
  val supplierClientMock = Mockito.mock(classOf[SupplierClient])

  org.mockito.Mockito
    .when(supplierClientMock.getSupportedPullSuppliers()(any[CQLContext], any[ExperimentRunner]))
    .thenReturn(
      Map(
        3038 -> SupportedPullSupplier(
          3038,
          ColumnInfo(s"price3038", None),
          ColumnInfo(s"ota_price3038", None),
          ColumnInfo(s"price3038_z", None),
          None,
          s"ota_availability3038",
          s"ota_availability3038",
          s"ota_availability3038",
        ),
      ))

  org.mockito.Mockito
    .when(supplierClientMock.isDemandPublishForPlatformIdOpt(any[Option[Int]], any[Option[Int]]))
    .thenReturn(true)

  org.mockito.Mockito
    .when(supplierClientMock.getDragonflyPullConfig(anyString()))
    .thenReturn(
      DragonflyPullConfig(
        samplingPercent = 0.0,
        minLos = 1,
        maxLos = 2,
      ),
    )

  val siteIdDmcIdDemandFilter = new SiteIdDmcIdDemandFilter {
    override def shouldSkipDemandForSupplier(supplierId: SupplierId, siteId: SiteId)(implicit
      aggregateContext: AggregateContext): Boolean = false
  }
  org.mockito.Mockito.when(supplierClientMock.getSiteIdDmcIdDemandFilter()).thenReturn(siteIdDmcIdDemandFilter)

  val actorSystem: ActorSystem = ActorSystem("unit-test")

  val priceRepoMock = Mockito.mock(classOf[PriceReadRepository])

  Mockito
    .when(priceRepoMock.readPullPrices(any[ReadPullPriceKey], any[Boolean])(any[Option[FiniteDuration]]))
    .thenReturn(Future.successful(PriceReadResult(
      cluster = "",
      databaseName = "",
      partitionIndex = 0,
      records = Map(
        "" ->
          PriceResponse(
            priceData = Array.empty,
            changeTimestamp = new DateTime("2023-10-31").withTimeAtStartOfDay(),
            hash = None,
            blockedOffers = None,
          )),
    )))

  val openTelemetry: OpenTelemetry = OpenTelemetry.noop()

  class SupplierCQLDataClientClass
    extends SupplierCQLDataClient(
      config = conf,
      supplierClient = supplierClientMock,
      supplierExperimentService = new SupplierExperimentServiceImpl,
      demandProducerDiscoveredOpt = None,
      repoOpt = None,
      priceRepo = Some(priceRepoMock),
      tracer = tracer,
      allowNoPullExpiry = true,
    )(
      actorSystem = actorSystem,
      ec = global,
      measurementContext: MeasurementContext,
      aggregateContext: AggregateContext,
      openTelemetry,
    ) {
    // notice the private val demandClientOpt inside SupplierCQLDataClient is also init as well and override by this one
    override val demandClientOpt: Option[DemandServiceClient] = Some(new DemandServiceClient {

      override def publish(publishType: PublishType,
                           demandRequest: DemandRequest,
                           supportedSuppliers: Map[DmcId, SupportedPullSupplier],
                           supplierReportingData: Map[DmcId, SupplierCacheHitSource])(implicit
        exp: ExperimentRunner,
        env: Option[EnvironmentContext]): Future[DemandAPIResponseStatus] = Future.successful(RequestSuccess)

      override def report(publishType: PublishType,
                          demandRequest: DemandRequest,
                          supportedSuppliers: Map[DmcId, SupportedPullSupplier],
                          supplierReportingData: Map[DmcId, SupplierCacheHitSource],
                          isPublish: Boolean,
                          dynamicConfig: SPLPullDynamicConfig)(implicit
        exp: ExperimentRunner,
        env: Option[EnvironmentContext] = None): Unit = {}
    })
  }

  val client = new SupplierCQLDataClientClass()

  val cqlContext: CQLContext = NoopCQLContext
  val schedulerContext: SchedulerContext = SupplierPricingPluginHelper.createSchedulerContext(actorSystem)

  val exp: ExperimentRunner = aValidSupplyContext.build.toExpRunner()
  val exchangeRateContext = new ExchangeDataServiceMock {}

  val env: EnvironmentContext =
    createEnvironmentContext(mock[DB], mock[HadoopLogger], exchangeRateContext, noopReportingService, actorSystem)

  // Outside of our scope to test, but helps detecting breaking models in ag-protobuf to getPullPriceDataSQL
  // Please cross check how to mock the input so that th lib do try to generate Demand Request
  // https://gitlab.agodadev.io/connectivity/supplier-api-client/-/blob/develop/src/main/scala/com/agoda/supplier/client/SupplierCQLDataClient.scala#L1188

  "SupplierCQLDataClient" should {
    "be able to create protobuf for Demand Request" in {
      val baseRequest = aValidSupplyBaseRequest.build
      val hotelsWithMetaToSuppliers: Map[PullHotelWithMeta, Set[SupplierId]] =
        Map(PullHotelWithMeta(1, com.agoda.supplier.models.Occupancy(), 0, 0) -> Set(DMC.BCOM))
      val searchCriteria: SearchCriteria = PushPullSupplierPricing.toSpApiDemandSearchCriteria(baseRequest)
      val clientInfo: ClientInfo = PushPullSupplierPricing.toSpApiDemandClientInfo(baseRequest)
      val trafficInfo: TrafficInfo = PushPullSupplierPricing.mapSpApiDemandTrafficInfo(baseRequest.trafficInfo)
      val sdaFilterInfo: SdaFilterInfo = SdaFilterInfo()

      val resultF = client.getPullPriceDataSQL(
        hotelsWithMetaToSuppliers = hotelsWithMetaToSuppliers,
        searchCriteria = searchCriteria,
        trafficInfo = trafficInfo,
        clientInfo = clientInfo,
        isSSRFlag = false,
        sdaFilterInfo = sdaFilterInfo,
        cacheExpiryReductionMinutes = 1,
      )(
        ec = global,
        schedulerContext,
        cqlContext,
        exp,
        env,
      )

      val result = Await.result(resultF, Duration(10, TimeUnit.SECONDS))
      result shouldNotEqual null
    }
  }
}
