package logging.postbooking

import com.agoda.adp.messaging.schema.CompatibilityResult
import org.specs2.mutable.SpecificationWithJUnit

class DFPostBookingAmendmentPropOfferMessageSpec extends SpecificationWithJUnit {
  "DFPostBookingAmendmentPropOfferMessage checkSchemaCompatibility" should {
    val result = DFPostBookingAmendmentPropOfferMessage("some-id", "som-protobuf-string", List("some filter"))
      .checkCompatibility()
      .getCode
    (result == CompatibilityResult.COMPATIBILITY_YES || result == CompatibilityResult.COMPATIBILITY_SAME) must_== true
  }
}
