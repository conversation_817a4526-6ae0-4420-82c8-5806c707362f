package supplyfunctionaltest.steps.mapper.meta

import com.agoda.finance.tax.models.{SupplierHotelCalculationSetting, SupplierHotelCalculationSettings}
import com.agoda.papi.enums.room.ChildAgeRangeType
import com.agoda.papi.pricing.metadata._
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders._
import com.agoda.papi.pricing.supply.models.{AgePolicy, SupplierHotelMapping, SupplyHotelInfo}
import com.agoda.papi.ypl.commission.apm.`enum`.ApmConfigType
import com.agoda.papi.ypl.models.enums.AgencyNoccMode
import com.agoda.papi.ypl.models.hotel.{AgodaAgencyFeatures, SupplierHotel}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.protobuf.common.OccupanyModel
import com.google.protobuf.timestamp.Timestamp
import org.joda.time.DateTime
import supplyfunctionaltest.steps.mapper._

case class SupplyHotelInfoMapper() extends TableMapper[SupplyHotelInfo] {

  override protected val baseData: SupplyHotelInfo = aValidSupplyHotelInfo
    .withSuppliers(DMC.YCS)
    .withIsStackableV2Enabled(Some(false))
    .withSupplierCCMappings(
      List(
        SupplierCCMapping(DMC.YCS,
                          isGenericCreditCardReq = true,
                          isDomesticCreditCardRequired = true,
                          isDmcMasterEnabled = true,
                          isAgency = true)))

  override protected def overrideField(key: String, value: String, current: SupplyHotelInfo): SupplyHotelInfo =
    key match {
      case "HotelID" => current.withHotelId(value.toInt)
      case "GmtOffset" => current.withGmtOffset(value.toInt)
      case "CountryCode" => current.withCountryCode(value)
      case "CityId" => current.withCityId(value.toLong)
      case "StateId" => current.withStateId(value.toInt)
      case "OccupancyModel" => current.withOccupancyModel(OccupanyModel.fromValue(value.toInt))
      case "YCSHotelCurrency" => current.withYCSHotelCurrency(Option(value))
      case "CountryId" => current.withCountryId(value.toLong)
      case "Suppliers" => current.withEnabledSuppliers(value.split(",").map(_.toInt).distinct.toSeq: _*)
      case "CountryCurrency" => current.withCountryCurrency(value)
      case "ProcessingFeeOption" => current.withProcessingFeeOption(value.toInt)
      case "SupplierCCMapping" =>
        val supplierCCMapping = value.split(",")
        val mappings = List(
          SupplierCCMapping(
            supplierId = supplierCCMapping(0).toInt,
            isGenericCreditCardReq = supplierCCMapping(1).toBoolean,
            isDomesticCreditCardRequired = supplierCCMapping(2).toBoolean,
            isDmcMasterEnabled = supplierCCMapping(3).toBoolean,
            isApplyTaxOnSellEx = if (supplierCCMapping.size > 4) supplierCCMapping(4).toBoolean else false,
          ))
        current.withSupplierCCMappings(mappings)
      case "SupplierHotelCalculationSettings" =>
        val supplierCCMapping = value.split(",")
        current.withSupplierHotelCalculationSettings(
          SupplierHotelCalculationSettings(
            Map(supplierCCMapping(0).toInt -> SupplierHotelCalculationSetting(supplierCCMapping(1).toBoolean))))
      case "ChainId" => current.withChainId(value.toInt)
      case "GmtOffsetMinutes" => current.withGmtOffsetMinutes(value.toInt)
      case "MultipleAutoPriceMatch" =>
        val splitObject = value.split(arrowsSeparator)
        val multiAutoPriceMatch =
          if (splitObject.nonEmpty) {
            val splitValue = splitObject(0).split(commaSeparator)
            Seq(
              MultipleAutoPriceMatch(
                programId = splitValue(0).toInt,
                commissionDiscountChannelId = splitValue(1).toInt,
                commissionDiscountPercent = splitValue(2).toDouble,
                adjustmentChannelId = splitValue(3).toInt,
                adjustmentDiscountPercent = splitValue(4).toDouble,
                statusId = splitValue(5).toInt,
                startDate = Some(Timestamp(DateTime.parse(splitValue(6)).getMillis / 1000)),
                endDate = Some(Timestamp(DateTime.parse(splitValue(7)).getMillis / 1000)),
                apmAdjustmentDiscount = Seq.empty,
                apmCommissionDiscount = Seq.empty,
                programType = Some(splitValue(8).toInt),
              ))
          } else Seq.empty
        val apmAdjustmentDiscount =
          if (splitObject.length >= 2 && splitObject(1).nonEmpty) {
            val splitValue = splitObject(1).split(commaSeparator)
            Seq(
              ApmAdjustmentDiscount(
                programId = splitValue(0).toInt,
                adjustmentChannelId = splitValue(1).toInt,
                adjustmentDiscountPercent = Some(splitValue(2).toDouble),
                startDate = Timestamp(DateTime.parse(splitValue(3)).getMillis / 1000),
                endDate = Timestamp(DateTime.parse(splitValue(4)).getMillis / 1000),
              ))
          } else Seq.empty
        val apmCommissionDiscount =
          if (splitObject.length >= 3 && splitObject(2).nonEmpty) {
            val splitValue = splitObject(2).split(commaSeparator)
            Seq(
              ApmCommissionDiscount(
                programId = splitValue(0).toInt,
                commissionChannelId = splitValue(1).toInt,
                commissionDiscountPercent = Some(splitValue(2).toDouble),
                startDate = Timestamp(DateTime.parse(splitValue(3)).getMillis / 1000),
                endDate = Timestamp(DateTime.parse(splitValue(4)).getMillis / 1000),
              ))
          } else Seq.empty
        current.withMultipleAutoPriceMatch(
          multiAutoPriceMatch.map(
            _.copy(apmCommissionDiscount = apmCommissionDiscount, apmAdjustmentDiscount = apmAdjustmentDiscount)))
      case "ChildAgeRanges" =>
        val childAgeRanges = value.split(commaSeparator).flatMap { childAgeRangeStr =>
          val childAgeRange = childAgeRangeStr.split(arrowsSeparator)
          val childAgeRangeId = childAgeRange(0).toInt
          childAgeRange(1).split(hifendSeparator).toList match {
            case ageFrom :: ageTo :: Nil =>
              Some(ChildAgeRange(childAgeRangeId, ageFrom.toInt, ageTo.toInt, ChildAgeRangeType.NormalRange.value))
            case _ => None
          }
        }
        current.withChildAgeRanges(childAgeRanges)
      case "ChildAgeRangesJapanRate" =>
        val childAgeRanges = value.split(commaSeparator).flatMap { childAgeRangeStr =>
          val childAgeRange = childAgeRangeStr.split(arrowsSeparator)
          val childAgeRangeId = childAgeRange(0).toInt
          childAgeRange(1).split(hifendSeparator).toList match {
            case ageFrom :: ageTo :: Nil =>
              Some(ChildAgeRange(childAgeRangeId, ageFrom.toInt, ageTo.toInt, ChildAgeRangeType.ChildRate.value))
            case _ => None
          }
        }
        current.withChildAgeRanges(childAgeRanges)
      case "AgencyNoCreditCardSetting" =>
        val agencyNoCreditCardSetting = value.split(commaSeparator)
        val supplierId = agencyNoCreditCardSetting(0).toInt
        current.withAgencyNoCreditCardSetting(Seq(agencyNoCreditCardSetting(1).toInt match {
          case 1 => AgencyNoCreditCard(supplierId, AgencyNoccMode.ApplyNoccToAllBookings.value)
          case _ => AgencyNoCreditCard(supplierId, AgencyNoccMode.NotApplyNocc.value)
        }))
      case "ApmLeadingRoomAdjustmentIds" =>
        current.withAPMLeadingRoomAdjustmentIds(value.split(",").map(_.toInt).distinct)
      case "isStackableV2Enabled" => current.withIsStackableV2Enabled(Option(value.toBoolean))
      case "JASORateCategoryLanguage" => current.withJASORateCategoryLanguage(
          value
            .split(commaSeparator)
            .map { e =>
              val rcMapping = e.split(arrowsSeparator)
              val rateCatId = rcMapping(0).toInt
              val languageList = rcMapping(1).split(hifendSeparator).map(languageId => languageId.toInt)
              rateCatId -> languageList.toSet
            }
            .toMap)
      case "ApmConfigs.CommissionReduction.HotelLevel" =>
        val existingConfigs = current.apmConfigs
        val newConfig = Map(
          ApmConfigType.CommissionReduction.value -> Seq(
            ApmConfig(
              programId = 1,
              configLevel = 4,
              configValue = value,
            )),
        )
        current.withApmConfigs(existingConfigs ++ newConfig)
      case "IsSingleRoomNHA" => current.withIsSingleRoomNHA(value.toBoolean)
      case "isChannelManaged" => current.withIsChannelManaged(value.toBoolean)
      case "UspaPrograms" =>
        val uspaPrograms = value.split(commaSeparator).map { campaignId =>
          UspaProgram(campaignId.toInt)
        }
        current.withUspaPrograms(uspaPrograms)
      case "ApplyDiscountsMultiplicatively" => current.withApplyDiscountsMultiplicatively(value.toBoolean)
      case "WhitelabelContractType" => current.withWhitelabelContractType(Option(value.toInt))
    }

  override protected def equal(a: SupplyHotelInfo, b: SupplyHotelInfo): Boolean = a.hotelId == b.hotelId

  override val childMappers: List[MapperContainer[SupplyHotelInfo, _]] = List(
    MapperContainer(
      SupplierHotelMapper(),
      (info: SupplyHotelInfo, supplierHotels: List[SupplierHotel]) =>
        info.withSupplierMappings(
          supplierHotels
            .map(supplierHotel => supplierHotel.hotelId -> supplierHotel)
            .toMap
            .asInstanceOf[SupplierHotelMapping]),
      { info: SupplyHotelInfo => info.withSupplierMappings(Map.empty) },
    ),
    MapperContainer(
      SupplyAgePolicyMapper(),
      (info: SupplyHotelInfo, agePolicy: List[AgePolicy]) => info.withAgePolicy(agePolicy.head),
      { info: SupplyHotelInfo => info.withAgePolicy(AgePolicy()) },
    ),
    MapperContainer(
      AgodaAgencyFeaturesMapper(),
      (info: SupplyHotelInfo, agodaAgencyFeatures: List[AgodaAgencyFeatures]) =>
        info.withAgodaAgencyFeature(agodaAgencyFeatures.headOption),
      { info: SupplyHotelInfo => info.withAgodaAgencyFeature(None) },
    ),
  )
  override protected val keys: List[String] = List("HotelID")
}
