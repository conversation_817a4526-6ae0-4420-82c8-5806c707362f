package supplyfunctionaltest.steps.parsers

import com.agoda.papi.enums.request.SearchType
import com.agoda.papi.enums.room.ChildRateType
import com.agoda.papi.pricing.supply.api.SupplyServiceInterface
import com.agoda.papi.pricing.supply.models.LanguageId
import com.agoda.papi.pricing.supply.models.request.{FencedOriginObject, FencedRatePair, SupplyRequest}
import com.agoda.papi.pricing.supply.models.utils.builders.SupplyContextMock
import com.agoda.papi.ypl.commission.apm.models.ApmSettingHolder
import com.agoda.papi.ypl.fencing.SameAgxCommissionForAllFences
import com.agoda.papi.ypl.models.api.request._
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{
  ChannelId,
  HotelId,
  HotelMeta,
  Quantity,
  SupplierId,
  YPLTestContexts,
  YplChannel,
  YplDispatchChannels,
  YplExperiment,
  YplMasterChannel,
  YplPricingData,
  YplRateFence,
  YplRequest,
}
import com.agoda.protobuf.cache.HotelPrice
import com.agoda.supply.calc.proto.SimpleDate
import com.typesafe.config.ConfigFactory
import io.cucumber.datatable.DataTable
import org.joda.time.DateTime
import supplyfunctionaltest.helper.{FieldsLensHelper, SupplyFieldsLensHelper}
import supplyfunctionaltest.mock.SupplierDataResponseBuilder
import supplyfunctionaltest.settings.ForceExperimentSettings
import supplyfunctionaltest.steps.mapper.meta.{
  DispatchedChannelMapper,
  FenceMapper,
  HotelMetaMapper,
  SupplyHotelInfoMapper,
}
import supplyfunctionaltest.steps.mapper.propoffer.GrowthProgramCommissionHolderMapper
import supplyfunctionaltest.steps.mapper.ratesilo.HotelPriceMapper

import java.util.concurrent.TimeUnit
import scala.concurrent.Await
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.Duration

// scalastyle:off
trait HotelPriceStep extends BaseStep with YPLTestContexts {

  val supplyService: SupplyServiceInterface
  val supplierDataResponseBuilder: SupplierDataResponseBuilder

  def setAdult(adult: Int): Unit = {
    input = FieldsLensHelper.updateAdult(input)(adult)
    supplyInput = SupplyFieldsLensHelper.updateAdult(supplyInput)(adult)
  }

  def setHotelId(hotelId: Int): Unit = {
    input = FieldsLensHelper.updateHotelId(input)(hotelId)
    supplyInput = SupplyFieldsLensHelper.updateHotelId(supplyInput)(hotelId)
  }

  Given("""hotelId """)(hotelId => setHotelId(hotelId))

  Given("""Adults (\d+)$""")((adult: Int) => setAdult(adult))

  Given("""Children (\d+)$""") {
    def setChildren(children: Int): Unit = {
      input = FieldsLensHelper.updateChildren(input)(Some(YplChildren(List.tabulate(children)(s => None))))
      supplyInput =
        SupplyFieldsLensHelper.updateChildren(supplyInput)(Some(YplChildren(List.tabulate(children)(_ => None))))
    }

    setChildren _
  }

  Given("""^Experiment (.+) User ([ABZ])$""") {
    def setExperiments(experiment: String, user: String): Unit = {
      input = FieldsLensHelper.updateExperiment(input)(YplExperiment(experiment, user.charAt(0)))
      supplyInput =
        SupplyFieldsLensHelper.appendExperiments(supplyInput)(List(YplExperiment(experiment, user.charAt(0))))
    }

    setExperiments _
  }

  Given("""^StoreFrontId (\d+)$""") {
    def setStoreFrontId(storeFrontId: Int): Unit = {
      input = FieldsLensHelper.updateStoreFrontId(input)(storeFrontId)
      supplyInput = SupplyFieldsLensHelper.updateStoreFrontId(supplyInput)(storeFrontId)
    }

    setStoreFrontId _
  }

  Given(
    """^DailyAGXCommissionAdjustment hotelid (\d+) date (.+) payAsYouGoCommission (\d+) prepaidCommission (\d+) freeTrialCommission (\d+)$""") {
    def setDailyAGXCommissionAdjustment(hotelId: HotelId,
                                        date: String,
                                        payAsYouGoCommission: Double,
                                        prepaidCommission: Double,
                                        freeTrialCommission: Double): Unit = {
      val dailyAdjust: Map[DateTime, YplAGXCommission] =
        Map(new DateTime(date) -> YplAGXCommission(payAsYouGoCommission, prepaidCommission, freeTrialCommission))
      val boostMap: Map[HotelId, YplAGXCommissionAdjustment] =
        Map(hotelId -> YplAGXCommissionAdjustment(dailyAdjust = dailyAdjust))
      input = FieldsLensHelper.updateFencedAGXCommissionAdjustment(input)(new SameAgxCommissionForAllFences(boostMap))
    }

    setDailyAGXCommissionAdjustment _
  }

  Given(
    """^AllDatesAGXCommissionAdjustment hotelid (\d+) payAsYouGoCommission (\d+) prepaidCommission (\d+) freeTrialCommission (\d+)$""") {
    def setAllDatesAGXCommissionAdjustment(hotelId: HotelId,
                                           payAsYouGoCommission: Double,
                                           prepaidCommission: Double,
                                           freeTrialCommission: Double): Unit = {
      val boostMap: Map[HotelId, YplAGXCommissionAdjustment] = Map(
        hotelId -> YplAGXCommissionAdjustment(allDateAdjust =
          Some(YplAGXCommission(payAsYouGoCommission, prepaidCommission, freeTrialCommission))))
      input = FieldsLensHelper.updateFencedAGXCommissionAdjustment(input)(new SameAgxCommissionForAllFences(boostMap))
    }

    setAllDatesAGXCommissionAdjustment _
  }

  Given("""^AllDatesAGXExcludeSupplier (.*)$""") {
    def setAllDatesAGXExcludeSupplier(value: String): Unit = {
      val values = value.split(',')
      val boostMap: Map[HotelId, YplAGXCommissionAdjustment] = Map(
        values(0).toLong ->
          YplAGXCommissionAdjustment(
            allDateAdjust = Some(YplAGXCommission(values(1).toDouble, values(2).toDouble, values(3).toDouble)),
            supplierSetting = Map(values(4).toInt -> YplAGXActionItem(excludedChannels = Seq(values(5).toInt))),
          ))
      input = FieldsLensHelper.updateFencedAGXCommissionAdjustment(input)(new SameAgxCommissionForAllFences(boostMap))
    }

    setAllDatesAGXExcludeSupplier _
  }

  Given("""^AllDatesAGXIncludeSupplier (.*)$""") {
    def setAllDatesAGXIncludeSupplier(value: String): Unit = {
      val values = value.split(',')
      val boostMap: Map[HotelId, YplAGXCommissionAdjustment] = Map(
        values(0).toLong ->
          YplAGXCommissionAdjustment(
            allDateAdjust = Some(YplAGXCommission(values(1).toDouble, values(2).toDouble, values(3).toDouble)),
            supplierSetting = Map(values(4).toInt -> YplAGXActionItem(includedChannels = Seq(values(5).toInt))),
          ))
      input = FieldsLensHelper.updateFencedAGXCommissionAdjustment(input)(new SameAgxCommissionForAllFences(boostMap))
    }

    setAllDatesAGXIncludeSupplier _
  }

  Given("""Rooms (\d+)$""") {
    def setRoom(rooms: Int): Unit = {
      input = FieldsLensHelper.updateRooms(input)(rooms)
      supplyInput = SupplyFieldsLensHelper.updateRooms(supplyInput)(rooms)
    }

    setRoom _
  }

  Given("""CID (\d+)$""") {
    @deprecated("use 'table for fences' instead")
    def setCid(cid: Int): Unit = {
      val fences = FieldsLensHelper.fencesLens.get(input)
      input = FieldsLensHelper.updateCid(FieldsLensHelper.updateFences(input)(fences.mapValues(rateFences =>
        rateFences.map(_.copy(cid = cid)))))(Some(cid))
      supplyInput = SupplyFieldsLensHelper.updateCid(supplyInput)(Some(cid))
    }

    setCid _
  }

  Given("""CID (-\d+)$""") {
    def setNegativeCid(cid: Int): Unit = {
      input = FieldsLensHelper.updateCid(input)(Some(cid))
      supplyInput = SupplyFieldsLensHelper.updateCid(supplyInput)(Some(cid))
    }

    setNegativeCid _
  }

  def setCheckIn(checkIn: String): Unit = {
    val checkInDate = new DateTime(checkIn)
    input = FieldsLensHelper.updateCheckIn(input)(checkInDate)
    supplyInput = SupplyFieldsLensHelper.updateCheckIn(supplyInput)(checkInDate)
    supplierDataResponseBuilder.updateCheckInDate(
      supplierDataResponseBuilder.defaultSupplierId,
      SimpleDate(checkInDate.getYear, checkInDate.getMonthOfYear, checkInDate.getDayOfMonth))
  }

  Given("""CheckIn (.*)$""") {
    def setCheckIn(checkIn: String): Unit = {
      input = FieldsLensHelper.updateCheckIn(input)(new DateTime(checkIn))
      supplyInput = SupplyFieldsLensHelper.updateCheckIn(supplyInput)(new DateTime(checkIn))
    }

    setCheckIn _
  }

  Given("""FeatureFlag (.*)$""") {
    def setFeatureFlagName(str: String): Unit = {
      if (str.forall(Character.isDigit)) input = FieldsLensHelper.updateFeatureFlag(input)(str.toInt)
      else input = FieldsLensHelper.updateFeatureFlagName(input)(str)

      if (str.forall(Character.isDigit))
        supplyInput = SupplyFieldsLensHelper.appendFeatureFlagId(supplyInput)(str.toInt)
      else supplyInput = SupplyFieldsLensHelper.appendFeatureFlagName(supplyInput)(str)
    }

    setFeatureFlagName _
  }

  Given("""^Language (\d+)$""") {
    def setLanguage(languageId: LanguageId): Unit = {
      input = FieldsLensHelper.updateLanguage(input)(languageId)
      supplyInput = SupplyFieldsLensHelper.updateLanguage(supplyInput)(languageId)
    }

    setLanguage _
  }

  def setLos(los: Int): Unit = {
    input = FieldsLensHelper.updateLos(input)(los)
    supplyInput = SupplyFieldsLensHelper.updateLos(supplyInput)(los)
    supplierDataResponseBuilder.updateLengthOfStay(supplierDataResponseBuilder.defaultSupplierId, los)
  }

  Given("""Los (\d+)$""")(los => setLos(los))

  Given("""^BookingDate (.*)$""") {
    def setBookingDate(booking: String): Unit = {
      input = FieldsLensHelper.updateBookingDate(input)(new DateTime(booking))
      supplyInput = SupplyFieldsLensHelper.updateBookingDate(supplyInput)(new DateTime(booking))
    }

    setBookingDate _
  }

  Given("""^isBookingRequest (.*)$""") {
    def setBookingRequest(isBookingRequest: String): Unit = {
      input = FieldsLensHelper.updateIisBookingRequest(input)(isBookingRequest.toBoolean)
      if (isBookingRequest.toBoolean) {
        supplyInput = SupplyFieldsLensHelper.updateSearchType(supplyInput)(SearchType.HotelForBooking)
      }
    }

    setBookingRequest _
  }

  Given("""^IsMSE (true|false)""") { (isMSE: Boolean) =>
    supplyInput = SupplyFieldsLensHelper.updateIsMse(supplyInput)(isMSE)
  }

  Given("""^Currency (.*)$""") { (data: String) =>
    val currency =
      if (data == "None") {
        ""
      } else {
        data
      }
    supplyInput = SupplyFieldsLensHelper.updateCurrency(supplyInput)(currency)
  }

  Given("""^IsApsEnabled (true|false)""") { (isApsEnabled: Boolean) =>
    supplyInput = SupplyFieldsLensHelper.updateIsApsEnabled(supplyInput)(isApsEnabled)
  }

  Given("""^MseHotelIds (.*)$""") { (mseHotelIds: String) =>
    val ids = mseHotelIds.split(",").map(_.toInt).toList
    supplyInput = SupplyFieldsLensHelper.updateMseHotelIds(supplyInput)(ids)
  }

  // for multiple usTaxV2 settings config, use config as shown in USTaxV2Settings object
  Given("""^Common Tax Settings From Config(.+)$""") {
    def setUpCommonTaxSettingsFromConfig(configStr: String): Unit = {
      val config = ConfigFactory.parseString(configStr)
      input = FieldsLensHelper.updateCommonTaxSettingsFromConfig(input)(config)
    }

    setUpCommonTaxSettingsFromConfig _
  }
  Given("""^Common Tax Settings with experiment (.+) states \[(.+)\]$""") {
    def setUpCommonTaxSettings(experiment: String, stateStr: String): Unit = {
      val states: Set[Int] = stateStr.split(",").map(_.trim.toInt).toSet
      val exp2StateMapping: Map[String, Set[Int]] = Map(experiment -> states)
      val state2ExpMapping = exp2StateMapping.flatMap { case (exp, states) => states.map(state => state -> exp) }
      input = FieldsLensHelper.updateCommonTaxSettings(input)(exp2StateMapping, state2ExpMapping)
    }

    setUpCommonTaxSettings _
  }

  // to keep backword compartibile
  Given("""^RatePlans (.*)$""") {
    @deprecated("Use 'table for fences' instead")
    def setRatePlans(rps: String): Unit = {
      val yplChannels: Set[YplChannel] =
        if (rps == "none") Set.empty
        else rps.split(",").map(c => YplMasterChannel(c.toInt)).toSet
      val fences = yplChannels.map(channel => channel -> Set(aValidRateFence)).toMap
      input = FieldsLensHelper.updateChannel(FieldsLensHelper.updateFences(input)(fences))(yplChannels)
      supplyInput = SupplyFieldsLensHelper.updateChannels(supplyInput)(yplChannels)
    }

    setRatePlans _
  }

  Given("""^FilterAPO (true|false)""") {
    def setFilterAPO(value: String): Unit = {
      input = FieldsLensHelper.updateFilterAPO(input)(value.toBoolean)
      supplyInput = SupplyFieldsLensHelper.updateFilterApo(supplyInput)(value.toBoolean)
    }

    setFilterAPO _
  }

  Given("""^Hotels (\d+) checkin (.*) los (\d+)$""") {
    def hotelCheckInLos(hotelId: Int, checkIn: String, los: Int): Unit = {
      setHotelId(hotelId)
      setCheckIn(checkIn)
      setLos(los)
    }

    hotelCheckInLos _
  }
  Given("""^WhiteLabelId (\d+)$""") {
    def setWhitelabelId(wlId: Int): Unit = {
      input = FieldsLensHelper.updateWhitelabelId(input)(wlId)
      supplyInput = SupplyFieldsLensHelper.updateWhitelabelId(supplyInput)(wlId)
    }

    setWhitelabelId _
  }

  Given("""^BlockPromotions (true|false)""") {
    def setBlockPromotions(blockPromotions: String): Unit = {
      input = FieldsLensHelper.updateBlockPromotions(input)(blockPromotions.toBoolean)
      supplyInput = SupplyFieldsLensHelper.updateBlockYCSPromotionsLens(supplyInput)(blockPromotions.toBoolean)
    }

    setBlockPromotions _
  }

  Given("""^OnlyCheapestRoom (true|false)""") {
    def setOnlyCheapestRoom(cheapestOnly: String): Unit = {
      input = FieldsLensHelper.updateIsCheapestOnly(input)(cheapestOnly.toBoolean)
      supplyInput = SupplyFieldsLensHelper.updateIsCheapestRoomOnly(supplyInput)(cheapestOnly.toBoolean)
    }

    setOnlyCheapestRoom _
  }

  Given("""^IsSSR (true|false)""") {
    def setIsSSR(isSSR: String): Unit = {
      input = FieldsLensHelper.updateIsSSR(input)(isSSR.toBoolean)
      supplyInput = SupplyFieldsLensHelper.updateIsSSR(supplyInput)(isSSR.toBoolean)
    }

    setIsSSR _
  }

  Given("""^isAllOcc (true|false)$""") {
    def setIsAllOcc(isAllOcc: String): Unit = {
      input = FieldsLensHelper.updateIsAllOcc(input)(isAllOcc.toBoolean)
      supplyInput = SupplyFieldsLensHelper.updateIsAllOcc(supplyInput)(isAllOcc.toBoolean)
    }

    setIsAllOcc _
  }

  Given("^table for hotelprice") {
    def setHotelPrice(given: DataTable): Unit = {
      val mapper = HotelPriceMapper()
      val hotelprice: Map[SupplierId, HotelPrice] =
        mapper.transform(given).map(hp => hp.supplierId -> hp)(collection.breakOut)
      val overrideHp = hotelprice.mapValues(hp =>
        hp.withCheckInMillis(input._2.request.checkIn.getMillis)
          .withHotelId(input._1.d.hotelId)
          .withLengthOfStay(
            input._2.request.lengthOfStay,
          )) // overriding basic param to sync with request in case user didnt set it in table
      input = FieldsLensHelper.hotelPriceLens.set(overrideHp)(input)
    }

    setHotelPrice _
  }
  Given("^ChildrenTypes GradeSchool (.*) and PreSchool (.*) and Toddler (.*) and Baby (.*)") {
    def setChildRateRequest(c1: Int, c2: Int, c3: Int, c4: Int): Unit = {
      val childReq: Map[ChildRateType, Int] = Map(ChildRateType.GradeSchool -> c1,
                                                  ChildRateType.PreSchool -> c2,
                                                  ChildRateType.Toddler -> c3,
                                                  ChildRateType.Baby -> c4)
      input = FieldsLensHelper.updateChildren(input)(Some(YplChildren(Nil, childReq)))
      supplyInput = SupplyFieldsLensHelper.updateChildren(supplyInput)(Some(YplChildren(Nil, childReq)))
    }

    setChildRateRequest _
  }

  Given("""^ChildAges (.+)$""") {
    def setChildages(childAges: String): Unit = {
      input =
        FieldsLensHelper.updateChildren(input)(Some(YplChildren(childAges.split(",").map(s => Some(s.toInt)).toList)))
      supplyInput = SupplyFieldsLensHelper.updateChildren(supplyInput)(
        Some(YplChildren(childAges.split(",").map(s => Some(s.toInt)).toList)))
    }

    setChildages _
  }

  Given(
    """^RoomAssignment No (.*) and Adult (.*) and GradeSchool (.*) and PreSchool (.*) and Toddler (.*) and Baby (.*)$""") {
    def setRoomAssignment(roomNo: Int, adult: Int, c1: Int, c2: Int, c3: Int, c4: Int): Unit = {
      val childrenTypes: Map[ChildRateType, Quantity] = Map.apply(ChildRateType.GradeSchool -> c1,
                                                                  ChildRateType.PreSchool -> c2,
                                                                  ChildRateType.Toddler -> c3,
                                                                  ChildRateType.Baby -> c4)
      val roomAssignment = YplRoomAssignment(adult, childrenTypes)
      input = FieldsLensHelper.appendRoomAssignment(input)(roomAssignment)
      supplyInput = SupplyFieldsLensHelper.appendRoomAssignment(supplyInput)(roomAssignment)
    }

    setRoomAssignment _
  }

  Given("""^SupplierId (\d+) isApplyMultipleRoomAssignment (true|false)""") {
    def setIsApplyMultipleRoomAssignment(supplierId: Int, isApplyMultipleRoomAssignment: Boolean): Unit = {
      val supplierFeature = input._2.request.supplierFeatures.features
        .getOrElse(supplierId, aValidFeature)
        .copy(isApplyMultipleRoomAssignment = isApplyMultipleRoomAssignment)
      input = FieldsLensHelper.updateSupplierFeatures(input)((supplierId, supplierFeature))
    }

    setIsApplyMultipleRoomAssignment _
  }

  Given("""^UserLogIn (true|false)$""") {
    def setLogin(login: String): Unit = {
      input = FieldsLensHelper.updateLogIn(input)(login.toBoolean)
      supplyInput = SupplyFieldsLensHelper.updateLogin(supplyInput)(login.toBoolean)
    }

    setLogin _
  }

  Given("^table for hotelmeta") {
    def setHotelMeta(given: DataTable): Unit = {
      val mapper = HotelMetaMapper()
      val meta: HotelMeta = mapper.transform(given).head
      input = FieldsLensHelper.metaLens.set(meta)(input)

      val supplyHotelInfos = SupplyHotelInfoMapper().transform(given)
      supplyInput = SupplyFieldsLensHelper.supplyHotelInfosLens.set(supplyHotelInfos)(supplyInput)
    }

    setHotelMeta _
  }

  Given("""^table for hotelmeta growth program commission detail holder$""") {
    def setGrowthProgramCommissionDetailHolder(given: DataTable): Unit = {
      // always call this after setHotelMeta otherwise it will override the growth program commission
      val mapper = GrowthProgramCommissionHolderMapper()
      val growthProgramCommissionDetailHolder = mapper.transformToHolder(given)
      input = FieldsLensHelper.updateGrowthProgramCommission(input)(
        growthProgramCommissionDetailHolder.applicableGrowthProgram)
    }

    setGrowthProgramCommissionDetailHolder _
  }

  Given("""^supplierFundedDiscountSettings for integrated suppliers is (.+) and experiment suppliers is (.+)$""") {
    def setSupplierFundedDiscountSettingsSupplier(integrated: String, experiment: String) = {
      val integratedSuppliers = integrated.split(",").map(_.toInt).toSet
      val experimentSuppliers = experiment.split(",").map(_.toInt).toSet
      input =
        FieldsLensHelper.updateSupplierFundedDiscountEnabledSuppliers(input)(integratedSuppliers, experimentSuppliers)
    }

    setSupplierFundedDiscountSettingsSupplier _
  }

  Given("""^Origin (.+)$""") {
    @deprecated("use 'table for fences' instead")
    def setOrigin(origin: String): Unit = {
      val fences = FieldsLensHelper.fencesLens.get(input)
      input = FieldsLensHelper.updateOrigin(FieldsLensHelper.updateFences(input)(fences.mapValues(rateFences =>
        rateFences.map(_.copy(origin = origin)))))(Some(origin))
      supplyInput = SupplyFieldsLensHelper.updateOrigin(supplyInput)(Some(origin))
    }

    setOrigin _
  }

  Given("""^table for fences""") {
    def setFences(given: DataTable): Unit = {
      val mapper = FenceMapper()
      val fencedChannels = mapper.transform(given)
      val fences = fencedChannels.foldLeft(Map.empty[YplChannel, Set[YplRateFence]]) { (base, fencedChannelDispatch) =>
        fencedChannelDispatch.allChannels.foldLeft(base) { (acc, channel) =>
          val newFences = acc.getOrElse(channel, Set.empty) + fencedChannelDispatch.fence
          acc + (channel -> newFences)
        }
      }

      val fencedDispatch = fencedChannels
        .map(fencedChannelDispatch =>
          fencedChannelDispatch.fence -> Map(
            DMC.YCS -> YplDispatchChannels(fencedChannelDispatch.allChannels.toSet,
                                           fencedChannelDispatch.helperChannels.toSet)))
        .toMap

      input =
        FieldsLensHelper.updateFencedChannelsPerSupplierMap(FieldsLensHelper.updateFences(input)(fences))(fencedDispatch)

      val fencedRatePairs = fencedChannels.map(channel =>
        FencedRatePair(channel.fence, FencedOriginObject(channel.allChannels.map(_.baseChannelId).toSet)))
      supplyInput = SupplyFieldsLensHelper.updateFencedRatePairs(supplyInput)(Some(fencedRatePairs))
      supplyInput = SupplyFieldsLensHelper.updateChannels(supplyInput)(fencedChannels.flatMap(_.allChannels).toSet)
      fencedChannels.headOption.foreach { fence =>
        supplyInput = SupplyFieldsLensHelper.updateOrigin(supplyInput)(Option(fence.fence.origin))
        supplyInput = SupplyFieldsLensHelper.updateCid(supplyInput)(Option(fence.fence.cid))
        supplyInput = SupplyFieldsLensHelper.updateLanguage(supplyInput)(fence.fence.language)
      }
    }

    setFences _
  }

  // @deprecated - TODO: use `table for hotelmeta ycs channel for supplier id` instead
  Given("""^Dispatch (\d+) With Composite Channel ASP and Domestic$""") {
    def setDispatchYcs(supplierId: SupplierId): Unit = {
      val channelPerSupplierMap = Map(
        332 -> YplDispatchChannels(
          masterChannels = Set(YplMasterChannel.RTL, YplMasterChannel.APS, YplMasterChannel.Domestic),
        ),
        29014 -> YplDispatchChannels(
          masterChannels = Set(YplMasterChannel.RTL, YplMasterChannel.Mobile, YplMasterChannel.Domestic),
        ),
      )
      input = FieldsLensHelper.updateChannelsPerSupplierMap(input)(channelPerSupplierMap)

      supplyInput = SupplyFieldsLensHelper.updateChannels(supplyInput)(
        channelPerSupplierMap.get(supplierId).map(_.masterChannels).getOrElse(Set.empty))
    }

    setDispatchYcs _
  }

  Given("""table for ycs channel for supplier id""") {
    def setDispatchedChannel(given: DataTable): Unit = setDispatchedChannelWithSupplierId(-1, given)

    setDispatchedChannel _
  }

  def setDispatchedChannelWithSupplierId(supplierId: SupplierId, given: DataTable): Unit = {
    val mapper = DispatchedChannelMapper()
    val dispatchedChannels = mapper.transform(given)
    input = FieldsLensHelper.appendChannelsPerSupplierMap(input)(
      Map(supplierId -> dispatchedChannels.head),
    )

    val existingFences = FieldsLensHelper.fencesLens.get(input).values.flatten.toSet
    input = FieldsLensHelper.updateFencedChannelsPerSupplierMap(input)(
      existingFences.map(fence => fence -> Map(supplierId -> dispatchedChannels.head)).toMap,
    )

    val channels = dispatchedChannels.head.masterChannels
    val fencedRatePairs = supplyInput._2.fencedRatePairs
      .getOrElse(List.empty)
      .map(fenceRatePair => fenceRatePair.copy(value = FencedOriginObject(ratePlans = channels.map(_.baseChannelId))))
    supplyInput = SupplyFieldsLensHelper.updateChannels(supplyInput)(channels)
    supplyInput = SupplyFieldsLensHelper.updateFencedRatePairs(supplyInput)(Some(fencedRatePairs))
  }

  Given("""^table for ycs channel for supplier id (\d+)$""") { (supplierId: SupplierId, given: DataTable) =>
    setDispatchedChannelWithSupplierId(supplierId, given)
  }
  val forceExperimentSettings = ForceExperimentSettings()

  def getRequest: YplRequest = {
    val request = input._2.request
    if (forceExperimentSettings.isEnabled) {
      val forcedExperiments = forceExperimentSettings.forcedExperiments.filterNot(request.experiments.contains)
      request.copy(experiments = request.experiments ++ forcedExperiments)
    } else {
      request
    }
  }

  // scalastyle:off
  When("calculate for supplier") {
    def calculateSupplier(): Unit = {
      val ctx = YplContext(
        request = getRequest,
        compositeChannelContext = input._2.compositeChannelContext,
        exchangeRateCtx = input._2.exchangeRateCtx,
        pulseCampaignMetaContext = input._2.pulseCampaignMetaCtx,
        masterChannelCategoryContext = input._2.masterChannelCategoryContext,
      )

      // Generic approach: process all suppliers, let YPL handle business logic
      val allSupplierResults = input._1.d.prices.map { case (supplierId, priceData) =>
        try {
          val pricingDataHP = YplPricingData(
            Right(priceData),
            input._1.meta,
            dispatchChannels = input._1.getSupplierDispatchChannels(supplierId),
            dispatchChannelsPerFence = input._1.getFencedSupplierDispatchChannels(supplierId),
            protoCor = input._1.d.protoCor,
          )
          val roomExpansionResult = processor.processRoomExpansionFlow(pricingDataHP, ctx)
          val resultF = processor.processRoomRpmFlow(pricingDataHP, roomExpansionResult, ctx)
          Await.result(awaitable = resultF, atMost = timeout)
        } catch {
          case _: Exception => None // Handle invalid data
        }
      }.toList

      // Merge all valid results - works for both single and multi-supplier
      val validResults = allSupplierResults.flatten
      if (validResults.nonEmpty) {
        val allRooms = validResults.flatMap(_.rooms)
        val firstValidHotel = validResults.head
        result = Some(firstValidHotel.copy(rooms = allRooms))
      } else {
        result = None
      }
    }

    calculateSupplier _
  }

  When("^calculate for supplier propoffer") {
    calculateSupplierPropOffer(None)
  }

  When("""^calculate for supplier propoffer for supplier id (\d+)""") { (supplierId: Int) =>
    calculateSupplierPropOffer(Option(supplierId))
  }

  When("^_V2_ calculate for supplier propoffer") {
    calculateSupplierPropOfferV2()
  }

  def calculateSupplierPropOffer(supplierId: Option[Int]): Unit = {
    val ctx = YplContext(
      request = getRequest,
      compositeChannelContext = input._2.compositeChannelContext,
      exchangeRateCtx = input._2.exchangeRateCtx,
      pulseCampaignMetaContext = input._2.pulseCampaignMetaCtx,
      masterChannelCategoryContext = input._2.masterChannelCategoryContext,
    )
    supplierDataResponseBuilder.buildAllHotelPriceData()
    supplierId.foreach(supplierDataResponseBuilder.removePropOffersExcept)
    val poPrices = supplierDataResponseBuilder.getHeadPropOffer
    val pricingDataHP = YplPricingData(
      Left(poPrices._2),
      input._1.meta,
      dispatchChannels = input._1.getSupplierDispatchChannels(poPrices._1),
      dispatchChannelsPerFence = input._1.getFencedSupplierDispatchChannels(poPrices._1),
      protoCor = input._1.d.protoCor,
    )
    val roomExpansionResult = processor.processRoomExpansionFlow(pricingDataHP, ctx)
    val resultF = processor.processRoomRpmFlow(pricingDataHP, roomExpansionResult, ctx)
    result = Await.result(
      awaitable = resultF,
      atMost = timeout,
    )
  }

  def calculateSupplierPropOfferV2(): Unit = {
    if (forceExperimentSettings.isEnabled) {
      val forcedExperiments = forceExperimentSettings.forcedExperiments.filterNot(supplyInput._2.experiments.contains)
      SupplyFieldsLensHelper.appendExperiments(supplyInput)(forcedExperiments)
    }

    supplierDataResponseBuilder.buildAllHotelPriceData()

    val request = SupplyRequest(
      supplyInput._1,
      SupplyContextMock(supplyInput._2),
    )

    supplyResult =
      Await.result(awaitable = supplyService.getSupplyHotels(request), atMost = Duration(8, TimeUnit.SECONDS))

    result = supplyResult.updatedSupplyMetaYplHotel.headOption.map(_._2.d)
  }

  // scalastyle:off
  When("""^calculate for supplier propoffer with beds advanced programIds""") {
    def calculateSupplierPropOfferWithBedsAdvancedProgramIDs(): Unit = {
      val requestWithBedsAdvancedAgpProgramId =
        getRequest.copy(apmSetting = Some(ApmSettingHolder(Nil, Nil, List(127, 155, 156))))
      val ctx = YplContext(
        request = requestWithBedsAdvancedAgpProgramId,
        compositeChannelContext = input._2.compositeChannelContext,
        exchangeRateCtx = input._2.exchangeRateCtx,
        pulseCampaignMetaContext = input._2.pulseCampaignMetaCtx,
        masterChannelCategoryContext = input._2.masterChannelCategoryContext,
      )
      val poPrices = supplierDataResponseBuilder.getHeadPropOffer
      val pricingDataHP = YplPricingData(
        Left(poPrices._2),
        input._1.meta,
        dispatchChannels = input._1.getSupplierDispatchChannels(poPrices._1),
        dispatchChannelsPerFence = input._1.getFencedSupplierDispatchChannels(poPrices._1),
        protoCor = input._1.d.protoCor,
      )
      val roomExpansionResult = processor.processRoomExpansionFlow(pricingDataHP, ctx)
      val resultF = processor.processRoomRpmFlow(pricingDataHP, roomExpansionResult, ctx)
      result = Await.result(
        awaitable = resultF,
        atMost = timeout,
      )
    }
    calculateSupplierPropOfferWithBedsAdvancedProgramIDs _
  }

  Given("""Simulated rate channels (\d+)""") {
    def setSimulatedRateChannels(channel: ChannelId): Unit =
      input = FieldsLensHelper.updateSimulatedRateChannels(input)(channel)

    setSimulatedRateChannels _
  }

  Given("""^is apply new occupancy logic (true|false)$""") {
    def setIsApplyNewOccupancyLogicExp(isB: Boolean): Unit =
      input = FieldsLensHelper.updateIsApplyNewOccupancyLogicExp(input)(isB)

    setIsApplyNewOccupancyLogicExp _
  }

  Given("""^requiredPrecheckAccuracyLevel (\d+)$""") { (level: Int) =>
    supplyInput = SupplyFieldsLensHelper.updateRequiredPrecheckAccuracyLevel(supplyInput)(level)
  }

  When("The user search") {
    calculateSupplierPropOfferV2()
  }
}
