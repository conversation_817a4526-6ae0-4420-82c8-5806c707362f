package supplyfunctionaltest.steps.parsers

import com.agoda.papi.ypl.models.YPLTestDataBuilders
import com.agoda.papi.ypl.models.lens.ypl.{DiscountInfoLens, PromotionLens, RoomLens}
import io.cucumber.datatable.DataTable

trait NoCCBlockListMORPStep extends BaseStep with YPLTestDataBuilders {

  Then("^The obtained rooms should match the result for MORP Booking") {
    def checkForNoCCBlockListMORP(expected: DataTable): Unit = {
      // | roomid   | paymentmodel | channel | ratecategory | occupancy | promotion | CreditCardRequired |
      val rows = result.get.rooms.map { room =>
        val roomId = RoomLens.roomTypeIdLens.get(room)
        val paymentModel = RoomLens.paymentModelLens.get(room)
        val channel = RoomLens.channelIdsLens.get(room)
        val ratecategory = RoomLens.rateCategoryIdLens.get(room)
        val roomCxlCode = RoomLens.cxlLens.get(room)
        val occ = RoomLens.occLens.get(room)
        val occupancy = occ.occupancy
        val promotionId = DiscountInfoLens.promotionLens
          .composeLens(PromotionLens.promotionIdLens)
          .getOption(room.discountInfo)
          .map(_.toString)
          .getOrElse("0")
        val noCreditCardRequired = RoomLens.noCreditCardLens.get(room)
        val paymentOption = RoomLens.paymentOptionLens.get(room)
        val prepaymentRequired = RoomLens.prepayamentRequiredLens.get(room)
        toRow(roomId,
              paymentModel,
              channel,
              ratecategory,
              roomCxlCode,
              occupancy,
              promotionId,
              noCreditCardRequired,
              paymentOption,
              prepaymentRequired)
      }
      validateResult(expected, rows)
    }
    checkForNoCCBlockListMORP _
  }

  Then("^The Bcom room should match the result for NoCC Booking") {
    def checkForNoCCBlockListMORPWithBCom(expected: DataTable): Unit = {
      val rows = result.get.rooms.map { room =>
        val roomId = RoomLens.roomTypeIdLens.get(room)
        val paymentModel = RoomLens.paymentModelLens.get(room)
        val isNoCCRequired = RoomLens.noCreditCardLens.get(room)
        val paymentOption = RoomLens.paymentOptionLens.get(room)
        val prepaymentRequired = RoomLens.prepayamentRequiredLens.get(room)
        toRow(roomId, paymentModel, isNoCCRequired, paymentOption, prepaymentRequired)
      }
      validateResult(expected, rows)
    }
    checkForNoCCBlockListMORPWithBCom _
  }

  Then("^The result of search should be") { (expected: DataTable) =>
    import com.agoda.papi.ypl.models.lens.ypl.RoomLens
    import com.agoda.papi.enums.hotel.PaymentOption
    val rows = result.toList.flatMap(_.rooms.map { room =>
      val roomId = RoomLens.roomTypeIdLens.get(room)
      val supplierId = RoomLens.supplierIdLens.get(room)

      val isNoCreditCard = room.paymentOptions.contains(PaymentOption.NoCreditCard)
      val ccReq = !isNoCreditCard

      toRow(roomId, supplierId, ccReq)
    })
    validateResult(expected, rows)
  }
}
