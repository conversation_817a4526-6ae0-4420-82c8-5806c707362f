package supplyfunctionaltest.steps.parsers

import com.agoda.commons.models.pricing.PulseCampaignMetadata
import com.agoda.papi.ypl.models.context.PulseCampaignMetaContext
import com.agoda.papi.ypl.models.{PromotionTypeId, YPLTestDataBuilders}
import io.cucumber.datatable.DataTable
import supplyfunctionaltest.helper.FieldsLensHelper
import supplyfunctionaltest.steps.mapper.meta.PulseCampaignMetadataMapper

trait PulseCampaignMetaStep extends BaseStep with YPLTestDataBuilders {

  Given("^table for pulse campaign metadata") { (pulseCampaignMetadata: DataTable) =>
    val mapper = PulseCampaignMetadataMapper()
    val config = mapper.transform(pulseCampaignMetadata)

    // Update the input using the helper function
    input = FieldsLensHelper.updatePulseAndMegaSaleCampaignMetaContext(input)(
      config.pulsePromotionTypeIds,
      config.megaSalePromotionTypeIds,
    )
  }

  Given("^Pulse Campaigns with promotion type ids is (.*)") {
    def setPulseCampaignMetaData(ptypeIds: String): Unit = {
      val validPulseMeta = PulseCampaignMetadata(promotionTypeId = 1,
                                                 webCampaignId = 2,
                                                 campaignTypeId = 3,
                                                 campaignBadgeCmsId = 4,
                                                 campaignBadgeDescCmsId = 5)
      val mockPulseCampaignContext = new PulseCampaignMetaContext {
        override def getPulseCampaignSetting(
          promotionTypeIds: List[PromotionTypeId],
        ): Map[PromotionTypeId, PulseCampaignMetadata] = ptypeIds.split(",").map(_.toInt -> validPulseMeta).toMap
      }
      input = FieldsLensHelper.updatePulseCampaignMeta(input)(mockPulseCampaignContext)
    }
    setPulseCampaignMetaData _
  }
  And("^Promotional room with pulse campaign metadata should contain the following") {
    def validatePulse(expected: DataTable): Unit = {
      val pulseMetadataList = result.get.rooms
        .map { room =>
          room.yplRoomEntry.pulseCampaignMetadata
        }
        .filter(_.isDefined)
        .map(_.get)
      val rows = pulseMetadataList.map(p =>
        toRow(p.promotionTypeId, p.webCampaignId, p.campaignTypeId, p.campaignBadgeCmsId, p.campaignBadgeDescCmsId))
      validateResult(expected, rows)
    }
    validatePulse _
  }
}
