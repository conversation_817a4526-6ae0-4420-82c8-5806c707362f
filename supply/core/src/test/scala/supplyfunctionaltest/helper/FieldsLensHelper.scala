package supplyfunctionaltest.helper

import com.agoda.papi.enums.request.FeatureFlag
import com.agoda.papi.ypl.commission.apm.models.ApmSettingHolder
import com.agoda.papi.ypl.commission.growth.program.GrowthProgramCommissionDetail
import com.agoda.papi.ypl.fencing.AgxCommissionFencing
import com.agoda.papi.ypl.models._
import com.agoda.papi.ypl.models.api.request.{YplChildren, YplClientInfo, YplRoomAssignment}
import com.agoda.papi.ypl.models.builders.ypl.YplContextMock.Implicits.traitToMock
import com.agoda.papi.ypl.models.context.{ExchangeRateContext, PulseCampaignMetaContext}
import com.agoda.papi.ypl.models.helpers.PromotionTestHelper
import com.agoda.papi.ypl.models.lens._
import com.agoda.papi.ypl.models.lens.ypl.{<PERSON><PERSON>eta<PERSON><PERSON>, YPL<PERSON>lient<PERSON>nfo<PERSON><PERSON>, YPLContext<PERSON>ens, YPLMetaRateSiloLens}
import com.agoda.papi.ypl.models.settings.CommonTaxSettings
import com.typesafe.config.Config
import monocle.{Lens, Setter}
import org.joda.time.DateTime

//scalastyle:off
object FieldsLensHelper {
  type YPLInput = (YPLMetaRateSilo, YplContext)

  val inputMetaLens = Lens[YPLInput, YPLMetaRateSilo](_._1)(metasilo => input => input.copy(_1 = metasilo))
  val inputCtxLens = Lens[YPLInput, YplContext](_._2)(ctx => input => input.copy(_2 = ctx))
  val inputClientInfoLens = Lens[YPLInput, YplClientInfo](_._2.request.cInfo)(clientinfo =>
    input => input.copy(_2 = input._2.copy(request = input._2.request.copy(cInfo = clientinfo))))

  val adultLens = inputCtxLens.composeLens(YPLContextLens.adultLens)
  val childrenLens = inputCtxLens.composeLens(YPLContextLens.childLens)
  val roomAssignmentLens = inputCtxLens.composeLens(YPLContextLens.roomAssignmentLens)
  val featuresLens = inputCtxLens.composeLens(YPLContextLens.featuresLens)
  val roomLens = inputCtxLens.composeLens(YPLContextLens.roomsLens)
  val bookingDateLens = inputCtxLens.composeLens(YPLContextLens.bookingDateLens)
  val hotelPriceLens = inputMetaLens.composeLens(YPLMetaRateSiloLens.hotelPriceLens)
  val metaLens = inputMetaLens.composeLens(YPLMetaRateSiloLens.hotelMetaLens)
  val growthProgramCommissionLens = metaLens.composeLens(HotelMetaLens.growthProgramCommissionLens)

  val languageLens = inputCtxLens.composeLens(YPLContextLens.languageLens)
  val flagInfoLenns = inputCtxLens.composeLens(YPLContextLens.flagInfoLens)
  val filterAPOLens = inputCtxLens.composeLens(YPLContextLens.filterAPOLens)
  val fencesLens = inputCtxLens.composeLens(YPLContextLens.fencesLens)
  val losSetter = {
    val ctxSetter = inputCtxLens.composeLens(YPLContextLens.losLens)
    val metaSetter = inputMetaLens.composeLens(YPLMetaRateSiloLens.losLens)
    SetterChain(ctxSetter, metaSetter)
  }
  val checkInSetter = {
    val metaSetter = inputMetaLens.composeLens(YPLMetaRateSiloLens.checkInLens)
    val ctxSetter = inputCtxLens.composeLens(YPLContextLens.checkInLens)
    SetterChain(metaSetter, ctxSetter)
  }
  val hotelIdSetter: Setter[YPLInput, Int] = inputMetaLens.composeSetter(YPLMetaRateSiloLens.hotelIdSetter)
  val channelLens = inputCtxLens.composeLens(YPLContextLens.channelLens)
  val isAllOccLens = inputCtxLens.composeLens(YPLContextLens.isAllOccLens)
  val experimentLens = inputCtxLens.composeLens(YPLContextLens.experimentLens)
  val exchangeLens = inputCtxLens.composeLens(YPLContextLens.exchangeCtxLens)
  val agxCommissionAdjustment = inputCtxLens.composeLens(YPLContextLens.agxCommissionAdjustment)
  val isCheapestOnlyLens = inputCtxLens.composeLens(YPLContextLens.isCheapestOnlyLens)
  val isSSRLens = inputCtxLens.composeLens(YPLContextLens.isSSRLens)
  val featureFlagLens = inputCtxLens.composeLens(YPLContextLens.featureFlagLens)
  val pulseCampaignMetaLens = inputCtxLens.composeLens(YPLContextLens.pulseCampaignMetaCtxLens)

  val platformLens = inputCtxLens.composeLens(YPLContextLens.clientInfoLens.composeLens(YPLClientInfoLens.platformLens))
  val cidLens = inputCtxLens.composeLens(YPLContextLens.clientInfoLens.composeLens(YPLClientInfoLens.cidLens))
  val originLens = inputCtxLens.composeLens(YPLContextLens.clientInfoLens.composeLens(YPLClientInfoLens.originLens))
  val logInLens = inputCtxLens.composeLens(YPLContextLens.clientInfoLens.composeLens(YPLClientInfoLens.loginLens))
  val channelsPerSupplierMapLens = inputMetaLens.composeLens(YPLMetaRateSiloLens.supplierChannelsMapLens)
  val appendChannelsPerSupplierMapLens = inputMetaLens.composeLens(YPLMetaRateSiloLens.supplierChannelsMapAppendLens)
  val fencedSupplierChannelsMapLens = inputMetaLens.composeLens(YPLMetaRateSiloLens.fencedSupplierChannelMapLens)
  val whiteLabelIdLens = inputCtxLens.composeLens(YPLContextLens.whilteLabelIds)
  val storeFrontIdLens = inputCtxLens.composeLens(YPLContextLens.storeFrontIds)
  val apmSettingLens = inputCtxLens.composeLens(YPLContextLens.apmSettingLens)
  val blockPromotionsLens = inputCtxLens.composeLens(YPLContextLens.blockPromotionsLens)
  val isBookingRequestLens = inputCtxLens.composeLens(YPLContextLens.isBookingRequestLens)
  val commonTaxSettingsLens = inputCtxLens.composeLens(YPLContextLens.commonTaxSettingsLens)
  val supplierFundedDiscountEnabledSuppliersLens =
    inputCtxLens.composeLens(YPLContextLens.supplierFundedDiscountEnabledSuppliersLens)
  val isApplyNewOccupancyLogicExpLens = inputCtxLens.composeLens(YPLContextLens.isApplyNewOccupancyLogicExpLens)

  // exposed field update method
  def updateHotelId(input: YPLInput)(hotelId: Int): YPLInput = hotelIdSetter.set(hotelId)(input)
  def updateAdult(input: YPLInput)(adult: Adults): YPLInput = adultLens.set(adult)(input)
  def updateChildren(input: YPLInput)(children: Option[YplChildren]): YPLInput = childrenLens.set(children)(input)
  def appendRoomAssignment(input: YPLInput)(roomAssignment: YplRoomAssignment): YPLInput =
    roomAssignmentLens.set(input._2.request.occ.roomAssignment ++ List(roomAssignment))(input)
  def updateSupplierFeatures(input: YPLInput)(supplierFeature: (Int, Feature)): YPLInput =
    featuresLens.set(input._2.request.supplierFeatures.features.updated(supplierFeature._1, supplierFeature._2))(input)
  def updateRooms(input: YPLInput)(room: Int): YPLInput = roomLens.set(room)(input)
  def updateCheckIn(input: YPLInput)(checkIn: DateTime): YPLInput = checkInSetter.set(checkIn)(input)
  def updateLos(input: YPLInput)(los: Int): YPLInput = losSetter.set(los)(input)
  def updateBookingDate(input: YPLInput)(bookingDate: DateTime): YPLInput = bookingDateLens.set(bookingDate)(input)
  def updateChannel(input: YPLInput)(chs: Set[YplChannel]): YPLInput = channelLens.set(chs)(input)
  def updateIsAllOcc(input: YPLInput)(isAllOcc: Boolean): YPLInput = isAllOccLens.set(isAllOcc)(input)
  def updateExperiment(input: YPLInput)(exps: YplExperiment): YPLInput =
    experimentLens.set(input._2.request.experiments ++ List(exps))(input)
  def updateExchangeRate(input: YPLInput)(exchangeRate: ExchangeRateContext): YPLInput =
    exchangeLens.set(exchangeRate)(input)
  def updatePulseCampaignMeta(input: YPLInput)(pulse: PulseCampaignMetaContext): YPLInput =
    pulseCampaignMetaLens.set(pulse)(input)
  def updateFencedAGXCommissionAdjustment(input: YPLInput)(fencedAgxCommission: AgxCommissionFencing): YPLInput =
    agxCommissionAdjustment.set(fencedAgxCommission)(input)
  def updateIsSSR(input: YPLInput)(isSSR: Boolean): YPLInput = isSSRLens.set(isSSR)(input)
  def updateIsCheapestOnly(input: YPLInput)(cheapestOnly: Boolean): YPLInput =
    isCheapestOnlyLens.set(cheapestOnly)(input)
  def updateFeatureFlag(input: YPLInput)(featureFlag: Int): YPLInput =
    featureFlagLens.set(Set(FeatureFlag.getFromValue(featureFlag)))(input)
  def updateFeatureFlagName(input: YPLInput)(featureFlagName: String): YPLInput =
    featureFlagLens.set(input._2.request.featureFlags + FeatureFlag.withName(featureFlagName))(input)
  def updateLanguage(input: YPLInput)(langId: LanguageId): YPLInput = languageLens.set(langId)(input)
  def updatePlatform(input: YPLInput)(platform: Option[Int]): YPLInput = platformLens.set(platform)(input)
  def updateCid(input: YPLInput)(cid: Option[Int]): YPLInput = cidLens.set(cid)(input)
  def updateOrigin(input: YPLInput)(origin: Option[String]): YPLInput = originLens.set(origin)(input)
  def updateWhitelabelId(input: YPLInput)(wlId: Int): YPLInput = whiteLabelIdLens.set(wlId)(input)
  def updateStoreFrontId(input: YPLInput)(wlId: Int): YPLInput = storeFrontIdLens.set(wlId)(input)
  def updateLogIn(input: YPLInput)(logIn: Boolean): YPLInput = logInLens.set(logIn)(input)
  def updateChannelsPerSupplierMap(input: YPLInput)(channelsPerSupplierMap: ChannelsPerSupplierMap): YPLInput =
    channelsPerSupplierMapLens.set(channelsPerSupplierMap)(input)
  def appendChannelsPerSupplierMap(input: YPLInput)(channelsPerSupplierMap: ChannelsPerSupplierMap): YPLInput =
    appendChannelsPerSupplierMapLens.set(channelsPerSupplierMap)(input)
  def updateFencedChannelsPerSupplierMap(input: YPLInput)(
    channelsPerSupplierMap: Map[YplRateFence, ChannelsPerSupplierMap]): YPLInput =
    fencedSupplierChannelsMapLens.set(channelsPerSupplierMap)(input)
  def updateFilterAPO(input: YPLInput)(filterAPO: Boolean): YPLInput = filterAPOLens.set(filterAPO)(input)
  def updateFences(input: YPLInput)(fences: Map[YplChannel, Set[YplRateFence]]): YPLInput = fencesLens.set(fences)(input)
  def updateAPMSetting(input: YPLInput)(apmSetting: Option[ApmSettingHolder]): YPLInput =
    apmSettingLens.set(apmSetting)(input)
  def updateBlockPromotions(input: YPLInput)(blockPromotions: Boolean): YPLInput =
    blockPromotionsLens.set(blockPromotions)(input)
  def updateIisBookingRequest(input: YPLInput)(isBookingRequest: Boolean): YPLInput =
    isBookingRequestLens.set(isBookingRequest)(input)
  def updateCommonTaxSettings(input: YPLInput)(
    experiment2StateMapping: Map[String, Set[Int]],
    state2ExperimentMapping: Map[Int, String]): YPLInput = commonTaxSettingsLens.set(
    Some(CommonTaxSettings(usExperiment2StateMapping = experiment2StateMapping,
                           usState2ExperimentMapping = state2ExperimentMapping)))(input)
  def updateCommonTaxSettingsFromConfig(input: YPLInput)(config: Config): YPLInput =
    commonTaxSettingsLens.set(Some(CommonTaxSettings(config)))(input)
  def updateGrowthProgramCommission(input: YPLInput)(
    commission: Map[YplRateFence, Seq[GrowthProgramCommissionDetail]]): YPLInput =
    growthProgramCommissionLens.set(commission)(input)

  def updateSupplierFundedDiscountEnabledSuppliers(
    input: YPLInput)(integratedSuppliers: Set[Int], experimentSuppliers: Set[Int]) =
    supplierFundedDiscountEnabledSuppliersLens.set(
      SupplierFundedDiscountSetting(integratedSuppliers, experimentSuppliers))(input)

  def updateSimulatedRateChannels(input: YPLInput)(channelId: ChannelId): YPLInput = {
    val context = input._2
    val currentChannels = context.request.simulationRequest.map(_.channels).getOrElse(Set[ChannelId]()) + channelId
    (input._1,
     context.copy(
       request = context.request.copy(
         simulationRequest = Some(SimulationRequest(channels = currentChannels)),
       ),
     ))
  }

  def updateIsApplyNewOccupancyLogicExp(input: YPLInput)(isApplyNewOccupancyLogicExp: Boolean): YPLInput =
    isApplyNewOccupancyLogicExpLens.set(isApplyNewOccupancyLogicExp)(input)

  def updatePulseAndMegaSaleCampaignMetaContext(
    input: YPLInput)(pulsePromotionTypeIds: List[Int] = Nil, megaSalePromotionTypeIds: List[Int] = Nil): YPLInput =
    pulseCampaignMetaLens.set(
      PromotionTestHelper.createPulseCampaignMetaContext(pulsePromotionTypeIds, megaSalePromotionTypeIds))(input)
}
//scalastyle:on
