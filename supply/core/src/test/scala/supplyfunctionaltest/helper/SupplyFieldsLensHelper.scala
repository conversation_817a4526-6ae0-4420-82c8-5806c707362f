package supplyfunctionaltest.helper

import com.agoda.papi.enums.request.{FeatureFlag, SearchType}
import com.agoda.papi.pricing.supply.models.request.{FencedRatePair, PrecheckAccuracies, SupplyBaseRequest}
import com.agoda.papi.pricing.supply.models.{GrowthProgram, HotelId, SupplyHotelInfo}
import com.agoda.papi.ypl.models.api.request.{YplChildren, YplRoomAssignment}
import com.agoda.papi.ypl.models.{Adults, LanguageId, YplChannel, YplExperiment, YplRateFence}
import monocle.Lens
import org.joda.time.DateTime
import supplyfunctionaltest.lens.{SupplyBaseRequestLens, SupplyHotelInfosLens}

object SupplyFieldsLensHelper {
  type SupplyInput = (List[SupplyHotelInfo], SupplyBaseRequest)

  val supplyBaseRequestLens = Lens[SupplyInput, SupplyBaseRequest](_._2)(request => input => input.copy(_2 = request))
  val supplyHotelInfosLens =
    Lens[SupplyInput, List[SupplyHotelInfo]](_._1)(supplyHotelInfos => input => input.copy(_1 = supplyHotelInfos))
  val bookingDateLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.bookingDateLens)
  val hotelsLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.hotelsLens)
  val checkInLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.checkInLens)
  val checkOutLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.checkOutLens)
  val adultLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.adultLens)
  val childrenLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.childrenLens)
  val roomsLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.roomsLens)
  val experimentsLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.experimentsLens)
  val losLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.losLens)
  val isSSRLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.isSSRLens)
  val roomAssignmentLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.roomAssignmentLens)
  val channelsLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.channelsLens)
  val flagInfoLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.flagInfoLens)
  val isAllOccLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.isAllOccLens)
  val isMseLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.isMseLens)
  val isApsEnabledLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.isApsEnabledLens)
  val mseHotelIdsLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.mseHotelIdsLens)
  val clientInfoLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.clientInfoLens)

  val languageLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.languageLens)
  val cidLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.cidLens)
  val currencyLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.currencyLens)
  val originLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.originLens)
  val loginLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.loginLens)
  val platformLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.platformLens)
  val storeFrontLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.storeFrontLens)
  val whileLabelIdsLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.whileLabelIdsLens)
  val blockYCSPromotionsLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.blockYCSPromotionsLens)

  val fencedRatePairsLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.fencedRatePairsLens)
  val isCheapestRoomOnlyLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.isCheapestRoomOnlyLens)
  val isAllowBookOnRequestLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.isAllowBookOnRequestLens)
  val searchTypeLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.searchTypeLens)
  val filterApoLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.filterApoLens)
  val featureFlagsLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.featureFlagsLens)
  val featureRequestLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.featureRequestLens)
  val isPackagingFunnelLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.isPackagingFunnelLens)
  val isFlightPlusHotelRequestLens =
    supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.isFlightPlusHotelRequestLens)
  val isPackagingFunnelRequestLens =
    supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.isPackagingFunnelRequestLens)
  val supplierPullMetadataLens = supplyBaseRequestLens.composeLens(SupplyBaseRequestLens.supplierPullMetadataLens)

  val growthProgramsLens = supplyHotelInfosLens.composeLens(SupplyHotelInfosLens.growthProgramsLens)

  def updateBookingDate(input: SupplyInput)(bookingDate: DateTime): SupplyInput = bookingDateLens.set(bookingDate)(input)
  def updateHotelId(input: SupplyInput)(hotelId: HotelId): SupplyInput = hotelsLens.set(List(hotelId))(input)
  def updateAdult(input: SupplyInput)(adult: Adults): SupplyInput = adultLens.set(adult)(input)
  def updateChildren(input: SupplyInput)(children: Option[YplChildren]): SupplyInput = childrenLens.set(children)(input)
  def updateRooms(input: SupplyInput)(room: Int): SupplyInput = roomsLens.set(room)(input)
  def appendExperiments(input: SupplyInput)(experiments: List[YplExperiment]): SupplyInput =
    experimentsLens.set(input._2.experiments ++ experiments)(input)
  def updateCheckIn(input: SupplyInput)(checkIn: DateTime): SupplyInput = checkInLens.set(checkIn)(input)
  def updateCheckOut(input: SupplyInput)(checkOut: DateTime): SupplyInput = checkOutLens.set(checkOut)(input)
  def updateLos(input: SupplyInput)(los: Int): SupplyInput = losLens.set(los)(input)
  def updateIsSSR(input: SupplyInput)(isSSR: Boolean): SupplyInput = isSSRLens.set(Some(isSSR))(input)
  def appendRoomAssignment(input: SupplyInput)(roomAssignment: YplRoomAssignment): SupplyInput =
    roomAssignmentLens.set(input._2.occ.roomAssignment ++ List(roomAssignment))(input)
  def updateChannels(input: SupplyInput)(channels: Set[YplChannel]): SupplyInput = channelsLens.set(channels)(input)
  def updateIsAllOcc(input: SupplyInput)(isAllOcc: Boolean): SupplyInput = isAllOccLens.set(isAllOcc)(input)
  def updateIsMse(input: SupplyInput)(isMse: Boolean): SupplyInput = isMseLens.set(isMse)(input)
  def updateIsApsEnabled(input: SupplyInput)(isApsEnabled: Boolean): SupplyInput =
    isApsEnabledLens.set(isApsEnabled)(input)
  def updateLanguage(input: SupplyInput)(langId: LanguageId): SupplyInput = languageLens.set(langId)(input)
  def updatePlatform(input: SupplyInput)(platform: Option[Int]): SupplyInput = platformLens.set(platform)(input)
  def updateCid(input: SupplyInput)(cid: Option[Int]): SupplyInput = cidLens.set(cid)(input)
  def updateCurrency(input: SupplyInput)(currency: String): SupplyInput = currencyLens.set(currency)(input)
  def updateOrigin(input: SupplyInput)(origin: Option[String]): SupplyInput = originLens.set(origin)(input)
  def updateWhitelabelId(input: SupplyInput)(wlId: Int): SupplyInput = whileLabelIdsLens.set(wlId)(input)
  def updateStoreFrontId(input: SupplyInput)(storeFrontId: Int): SupplyInput =
    storeFrontLens.set(Some(storeFrontId))(input)
  def updateLogin(input: SupplyInput)(login: Boolean): SupplyInput = loginLens.set(login)(input)
  def updateFencedRatePairs(input: SupplyInput)(fencedRatePairs: Option[List[FencedRatePair]]): SupplyInput =
    fencedRatePairsLens.set(fencedRatePairs)(input)
  def updateBlockYCSPromotionsLens(input: SupplyInput)(blockPromotions: Boolean): SupplyInput =
    blockYCSPromotionsLens.set(blockPromotions)(input)
  def updateIsCheapestRoomOnly(input: SupplyInput)(cheapestOnly: Boolean): SupplyInput =
    isCheapestRoomOnlyLens.set(cheapestOnly)(input)
  def updateSearchType(input: SupplyInput)(searchType: SearchType): SupplyInput = searchTypeLens.set(searchType)(input)
  def updateIsAllowBookOnRequestLens(input: SupplyInput)(isAllowBookOnRequest: Boolean) =
    isAllowBookOnRequestLens.set(Some(isAllowBookOnRequest))(input)
  def updateFilterApo(input: SupplyInput)(filterApo: Boolean) = filterApoLens.set(filterApo)(input)
  def appendFeatureFlagId(input: SupplyInput)(featureFlag: Int): SupplyInput =
    featureFlagsLens.set(input._2.featureFlags :+ FeatureFlag.getFromValue(featureFlag))(input)
  def appendFeatureFlagName(input: SupplyInput)(featureFlagName: String): SupplyInput =
    featureFlagsLens.set(input._2.featureFlags :+ FeatureFlag.withName(featureFlagName))(input)
  def updateIsPackagingFunnel(input: SupplyInput)(isPackagingFunnel: Boolean): SupplyInput =
    isPackagingFunnelLens.set(isPackagingFunnel)(input)
  def updateGrowthPrograms(input: SupplyInput)(growthProgram: Map[YplRateFence, Seq[GrowthProgram]]): SupplyInput =
    growthProgramsLens.set(growthProgram)(input)
  def updateMseHotelIds(input: SupplyInput)(mseHotelIds: List[Int]): SupplyInput =
    mseHotelIdsLens.set(mseHotelIds)(input)
  def updateIsFlightPlusHotelRequest(input: SupplyInput)(isFlightPlusHotelRequest: Boolean): SupplyInput =
    isFlightPlusHotelRequestLens.set(isFlightPlusHotelRequest)(input)
  def updateIsPackagingFunnelRequest(input: SupplyInput)(isPackagingFunnelRequest: Boolean): SupplyInput =
    isPackagingFunnelRequestLens.set(isPackagingFunnelRequest)(input)
  def updateRequiredPrecheckAccuracyLevel(input: SupplyInput)(level: Int): SupplyInput = supplierPullMetadataLens.set(
    input._2.supplierPullMetadata.copy(requiredPrecheckAccuracy = PrecheckAccuracies.getPrecheckAccuracy(level)))(input)

}
