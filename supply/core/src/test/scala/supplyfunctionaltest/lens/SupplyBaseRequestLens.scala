package supplyfunctionaltest.lens

import com.agoda.papi.enums.request.{FeatureFlag, SearchType}
import com.agoda.papi.pricing.supply.models.request.{FencedRatePair, SupplierPullMetadata, SupplyBaseRequest}
import com.agoda.papi.ypl.models.api.request.{YplClientInfo, YplFeatureRequest, YplFlagInfo, YplOccInfo}
import com.agoda.papi.ypl.models.lens.ypl.{FlagInfoLens, OccInfoLens, YPLClientInfoLens, YPLFeatureRequestLens}
import com.agoda.papi.ypl.models.{HotelId, WhitelabelSetting, YplChannel, YplExperiments}
import com.agoda.supplier.models.Currency
import monocle.Lens
import org.joda.time.DateTime

object SupplyBaseRequestLens {
  type A = SupplyBaseRequest
  val bookingDateLens =
    Lens[A, DateTime](_.bookingDate)(bookingDate => request => request.copy(bookingDate = bookingDate))
  val experimentsLens =
    Lens[A, YplExperiments](_.experiments)(experiments => request => request.copy(experiments = experiments))
  val hotelsLens = Lens[A, List[HotelId]](_.hotels)(hotels => request => request.copy(hotels = hotels))
  val occInfoLens = Lens[A, YplOccInfo](_.occ)(occ => room => room.copy(occ = occ))
  val adultLens = occInfoLens.composeLens(OccInfoLens.occInfoAdultLens)
  val childrenLens = occInfoLens.composeLens(OccInfoLens.occInfoChildrenLens)
  val roomsLens = occInfoLens.composeLens(OccInfoLens.occInfoRoomLens)
  val roomAssignmentLens = occInfoLens.composeLens(OccInfoLens.occInfoRoomAssignmentLens)
  val checkInLens = Lens[A, DateTime](_.checkIn)(checkIn => request => request.copy(checkIn = checkIn))
  val checkOutLens = Lens[A, DateTime](_.checkOut)(checkOut => request => request.copy(checkOut = checkOut))
  val losLens = Lens[A, Int](_.lengthOfStay)(los => request => request.copy(checkOut = request.checkIn.plusDays(los)))
  val isSSRLens = Lens[A, Option[Boolean]](_.isSSR)(isSSR => request => request.copy(isSSR = isSSR))
  val channelsLens = Lens[A, Set[YplChannel]](_.channels)(channels => request => request.copy(channels = channels))
  val flagInfoLens = Lens[A, YplFlagInfo](_.flagInfo)(flagInfo => request => request.copy(flagInfo = flagInfo))
  val featureRequestLens = Lens[A, YplFeatureRequest](_.featureRequest)(featureRequest =>
    request => request.copy(featureRequest = featureRequest))
  val isAllOccLens = flagInfoLens.composeLens(FlagInfoLens.isAllOccLens)
  val isMseLens = flagInfoLens.composeLens(FlagInfoLens.isMseLens)
  val isApsEnabledLens = flagInfoLens.composeLens(FlagInfoLens.apsEnableLens)
  val mseHotelIdsLens = featureRequestLens.composeLens(YPLFeatureRequestLens.mseHotelIdsLens)
  val clientInfoLens = Lens[A, YplClientInfo](_.cInfo)(cInfo => request => request.copy(cInfo = cInfo))
  val fencedRatePairsLens = Lens[A, Option[List[FencedRatePair]]](_.fencedRatePairs)(fencedRatePairs =>
    request => request.copy(fencedRatePairs = fencedRatePairs))
  val languageLens = clientInfoLens.composeLens(YPLClientInfoLens.languageLens)
  val cidLens = clientInfoLens.composeLens(YPLClientInfoLens.cidLens)
  val originLens = clientInfoLens.composeLens(YPLClientInfoLens.originLens)
  val loginLens = clientInfoLens.composeLens(YPLClientInfoLens.loginLens)
  val platformLens = clientInfoLens.composeLens(YPLClientInfoLens.platformLens)
  val storeFrontLens = clientInfoLens.composeLens(YPLClientInfoLens.storeFrontLens)

  val whitelabelSettingLens = Lens[A, WhitelabelSetting](_.whitelabelSetting)(whitelabelSetting =>
    request => request.copy(whitelabelSetting = whitelabelSetting))
  val whileLabelIdsLens = whitelabelSettingLens.composeLens(WhitelabelSettingLens.whileLabelIdsLens)
  val blockYCSPromotionsLens = whitelabelSettingLens.composeLens(WhitelabelSettingLens.blockYCSPromotionsLens)

  val searchTypeLens = Lens[A, SearchType](_.searchType)(searchType => request => request.copy(searchType = searchType))
  val currencyLens = Lens[A, Currency](_.currency)(currency => request => request.copy(currency = currency))
  val isCheapestRoomOnlyLens = Lens[A, Boolean](_.isCheapestRoomOnly)(isCheapestRoomOnly =>
    request => request.copy(isCheapestRoomOnly = isCheapestRoomOnly))
  val isAllowBookOnRequestLens = Lens[A, Option[Boolean]](_.isAllowBookOnRequest)(isAllowBookOnRequest =>
    request => request.copy(isAllowBookOnRequest = isAllowBookOnRequest))
  val filterApoLens = flagInfoLens.composeLens(FlagInfoLens.filterApoLens)
  val featureFlagsLens =
    Lens[A, List[FeatureFlag]](_.featureFlags)(featureFlags => request => request.copy(featureFlags = featureFlags))
  val isPackagingFunnelLens = Lens[A, Boolean](_.isPackagingFunnel)(isPackagingFunnel =>
    request => request.copy(isPackagingFunnel = isPackagingFunnel))
  val isFlightPlusHotelRequestLens = Lens[A, Boolean](_.isFlightPlusHotelRequest)(isFlightPlusHotelRequest =>
    request => request.copy(isFlightPlusHotelRequest = isFlightPlusHotelRequest))
  val isPackagingFunnelRequestLens = Lens[A, Boolean](_.isPackagingFunnelRequest)(isPackagingFunnelRequest =>
    request => request.copy(isPackagingFunnelRequest = isPackagingFunnelRequest))
  val supplierPullMetadataLens = Lens[A, SupplierPullMetadata](_.supplierPullMetadata)(supplierPullMetadata =>
    request => request.copy(supplierPullMetadata = supplierPullMetadata))
}
