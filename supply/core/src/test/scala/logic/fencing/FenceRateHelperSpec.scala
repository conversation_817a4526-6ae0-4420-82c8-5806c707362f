package logic.fencing

import com.agoda.papi.pricing.supply.models.request.FencedOriginObject
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.ypl.models.{YplRateFence, YplMasterChannel => DFMasterChannel}
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

class FenceRateHelperSpec extends SpecificationWithJUnit with SupplyModelTestDataBuilders with Mockito {
  "updateFlowContext" should {
    "update FlowContext with new FencedRate origin, rate plan and cid" in {
      val req = aValidSupplyBaseRequest
      val ctx = aValidSupplyContext.withBaseRequest(req)

      val key = YplRateFence("UK", 2, -1)
      val value = FencedOriginObject(Set(1, 2, 3))
      val actual = FenceRateHelper.updateFlowContext(ctx, key, value)

      actual.baseRequest.cInfo.origin should_=== Some("UK")
      actual.baseRequest.cInfo.cid should_=== Some(2)
      actual.baseRequest.channels should_=== Set(DFMasterChannel.RTL, DFMasterChannel.APS, DFMasterChannel.NET)
    }

    "update FlowContext with new FencedRate origin, rate plan, cid and language" in {
      val req = aValidSupplyBaseRequest
      val ctx = aValidSupplyContext.withBaseRequest(req)

      val key = YplRateFence("TH", 2, 22)
      val value = FencedOriginObject(Set(1, 2, 3))
      val actual = FenceRateHelper.updateFlowContext(ctx, key, value)

      actual.baseRequest.cInfo.origin should_=== Some("TH")
      actual.baseRequest.cInfo.cid should_=== Some(2)
      actual.baseRequest.cInfo.language should_=== 22
      actual.baseRequest.channels should_=== Set(DFMasterChannel.RTL, DFMasterChannel.APS, DFMasterChannel.NET)
    }
  }
}
