package logic

import com.agoda.papi.pricing.supply.db.compositeChannelData.CompositeChannelContext
import com.agoda.papi.ypl.models.{YplChannel => DFCompositeChannel, YplMasterChannel => DFMasterChannel}
import org.specs2.mutable.SpecificationWithJUnit

class DFCompositeChannelHelperSpec extends SpecificationWithJUnit {
  "createDFCompositeChannels" should {

    def test(compositeChannelId: Int, baseChannels: Set[Int], expectedOutput: Set[DFCompositeChannel]) = {
      val compositeChannelContext = new CompositeChannelContext {
        override def getCompositeChannelId(baseChannelId: Int, stackedChannelIds: Set[Int]): Option[Int] =
          Some(compositeChannelId)
        override def toCompositeChannelIds(channelId: Int): Set[Int] = baseChannels
      }

      val channels = DFCompositeChannelHelper.createDFCompositeChannels(compositeChannelId)(compositeChannelContext)
      channels should_== expectedOutput

    }

    "with stackchannels size = 1  should return masterChannels + compositechannels" in {
      val compositeChannelId = 2
      val baseChannel1 = 2
      val expectedOutput = Set(DFMasterChannel(compositeChannelId))

      test(compositeChannelId, Set(baseChannel1), expectedOutput)
    }

    "with stackchannels size = 2  should return masterChannels + compositechannels" in {
      val compositeChannelId = 220
      val baseChannel1 = 2
      val baseChannel2 = 6
      val expectedOutput = Set(
        DFMasterChannel(baseChannel1),
        DFMasterChannel(baseChannel2),
        DFCompositeChannel(baseChannel1, Set(baseChannel2), compositeChannelId),
        DFCompositeChannel(baseChannel2, Set(baseChannel1), compositeChannelId),
      )

      test(compositeChannelId, Set(baseChannel1, baseChannel2), expectedOutput)
    }

    "with stackchannels size = 3  should return masterChannels + compositechannels" in {
      val compositeChannelId = 2063
      val baseChannel1 = 2
      val baseChannel2 = 6
      val baseChannel3 = 7
      val expectedOutput = Set(
        DFMasterChannel(baseChannel1),
        DFMasterChannel(baseChannel2),
        DFMasterChannel(baseChannel3),
        DFCompositeChannel(baseChannel2, Set(baseChannel1, baseChannel3), compositeChannelId),
        DFCompositeChannel(baseChannel1, Set(baseChannel2, baseChannel3), compositeChannelId),
        DFCompositeChannel(baseChannel3, Set(baseChannel1, baseChannel2), compositeChannelId),
      )

      test(compositeChannelId, Set(baseChannel1, baseChannel2, baseChannel3), expectedOutput)
    }

    "with stackchannels size = 4  should return masterChannels + compositechannels" in {
      val compositeChannelId = 2064
      val baseChannel1 = 2
      val baseChannel2 = 6
      val baseChannel3 = 7
      val baseChannel4 = 27
      val expectedOutput = Set(
        DFMasterChannel(baseChannel1),
        DFMasterChannel(baseChannel2),
        DFMasterChannel(baseChannel3),
        DFMasterChannel(baseChannel4),
        DFCompositeChannel(baseChannel1, Set(baseChannel2, baseChannel3, baseChannel4), compositeChannelId),
        DFCompositeChannel(baseChannel2, Set(baseChannel1, baseChannel3, baseChannel4), compositeChannelId),
        DFCompositeChannel(baseChannel3, Set(baseChannel1, baseChannel2, baseChannel4), compositeChannelId),
        DFCompositeChannel(baseChannel4, Set(baseChannel1, baseChannel2, baseChannel3), compositeChannelId),
      )
      test(compositeChannelId, Set(baseChannel1, baseChannel2, baseChannel3, baseChannel4), expectedOutput)
    }
  }
}
