package logic.yplmapper

import com.agoda.finance.tax.models.{ApplyTaxOnSellExSetting, ApplyTaxOnSellExSettings}
import com.agoda.papi.enums.request.{BookingDurationType, FeatureFlag, FilterCriteria, SearchType}
import com.agoda.papi.enums.room.{ChildRateType, InventoryType, PaymentChannel}
import com.agoda.papi.pricing.supply.configuration.HeuristicRoomLimitSetting
import com.agoda.papi.pricing.supply.models.SupplierDispatchChannelHolder
import com.agoda.papi.pricing.supply.models.request.{
  FencedOriginObject,
  FencedRatePair,
  RatePlanInfo,
  SupplyBookingFilter,
  TrafficInfo,
}
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.ypl.models.api.request._
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.styx.botprofile.{BotProfile, TrafficType}
import external.pricing.{DispatchInfo, HotelDispatchInfo}
import com.agoda.papi.ypl.models.{
  BComBMPSetting,
  BComCCSetting,
  PaymentInventoryTypeConfiguration,
  SupplierFeatures,
  SupplierFundedDiscountSetting,
  YPLTestDataBuilders,
  YplChannel,
  YplExperiment,
  YplHeuristicRequest,
  YplMasterChannel,
  YplRateFence,
}
import mocks.{
  APMConfigSettingsProducerMock,
  CompositeChannelContextMock,
  HeuristicRoomLimitSettingsProducerMock,
  OfferFilterTrackingSettingsProducerMock,
  SuperAggOccupancyFlowSettingProducerMock,
}
import models.consts.ABTest
import org.joda.time.DateTime
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

class BaseRequestConverterSpec
  extends SpecificationWithJUnit
    with Mockito
    with YPLTestDataBuilders
    with SupplyModelTestDataBuilders {

  "BaseRequestConverter" should {

    implicit val compositeChannelContext = aValidCompositeChannelContext

    val baseRequestConverter = new BaseRequestConverter(
      HeuristicRoomLimitSettingsProducerMock.static,
      aValidSupplierFeatures,
      CompositeChannelContextMock,
      APMConfigSettingsProducerMock.static,
      OfferFilterTrackingSettingsProducerMock.static,
      SuperAggOccupancyFlowSettingProducerMock.static,
    )

    "during toYplContext copy every YplContext field" in {
      val bDate = new DateTime(2000, 1, 2, 3, 4, 5)
      val chIn = new DateTime(2001, 1, 2, 3, 4, 5)
      val chOut = new DateTime(2002, 1, 2, 3, 4, 5)
      val trafficInfo = TrafficInfo()
      val featureRequest = YplFeatureRequest(enableReturnNonApprovedEscapes = true)

      val supplyRequestBuilder = aValidSupplyBaseRequest
        .withSearchId("id")
        .withSearchType(SearchType.HotelSearch)
        .withCheckIn(chIn)
        .withCheckOut(chOut)
        .withIsPriusRequest(true)
        .withCurrency("THB")
        .withHotels(List(1, 2, 3))
        .withChannels(Set(YplMasterChannel(1), YplMasterChannel(2), YplMasterChannel(3)))
        .withExperiments(List(
          YplExperiment("experimentId", 'B'),
          YplExperiment(ABTest.PRICE_PUSH_FENCING, 'B'),
          YplExperiment("TEST-002", 'B'),
        ))
        .withOcc(
          YplOccInfo(
            Some(2),
            Some(YplChildren(List(Some(3)))),
            Some(2),
            Some(YplOccFilter(Some(1), Some(2))),
            roomAssignment = List(
              YplRoomAssignment(1, Map(ChildRateType.PreSchool -> 1, ChildRateType.Baby -> 1), Map(2 -> 1, 3 -> 1)),
              YplRoomAssignment(1, Map(ChildRateType.GradeSchool -> 1, ChildRateType.Toddler -> 1), Map(1 -> 1, 4 -> 1)),
            ),
          ),
        )
        .withCInfo(YplClientInfo(1, Some(1), Some(1), Some(1), Some("TH"), Some(1), Some("abc")))
        .withFlagInfo(YplFlagInfo())
        .withTrafficInfo(trafficInfo)
        .withBookingDate(bDate)
        .withFeatureFlags(List(FeatureFlag.Unknown))
        .withIsSSR(None)
        .withFeatureRequest(featureRequest)
        .withSimulateRequestData(Some(YplSimulateRequestData(None)))
        .withIsCheapestRoomOnly(true)
        .withWhitelabelSetting(
          aValidSupplyWhitelabelSetting
            .withPaymentInventoryTypeConfigurations(
              List(
                PaymentInventoryTypeConfiguration(inventoryType = InventoryType.JtbOTARurubu.value,
                                                  allowedPaymentChannels = List(PaymentChannel.Offline.i)),
              ),
            )
            .withExactMatchOccupancy(true),
        )

      "Basic copy test" in {
        val supplyRequest = supplyRequestBuilder
          .withFeatureRequest(featureRequest)
          .withBookingDurationTypes(List("hourly"))
          .withBookingFilter(Some(SupplyBookingFilter(priceAdjustmentId = Some(12))))
        implicit val ctx = aValidSupplyContext.withBaseRequest(supplyRequest)
        val yplRequest = baseRequestConverter.toYplRequest()(ctx)

        val expectedRoomAssignment = List(
          YplRoomAssignment(
            1,
            Map(ChildRateType.Unknown -> 0,
                ChildRateType.GradeSchool -> 0,
                ChildRateType.PreSchool -> 1,
                ChildRateType.Toddler -> 0,
                ChildRateType.Baby -> 1).filter(_._2 > 0).toMap,
            Map(1 -> 0, 2 -> 1, 3 -> 1, 4 -> 0).filter(_._2 > 0),
          ),
          YplRoomAssignment(
            1,
            Map(ChildRateType.Unknown -> 0,
                ChildRateType.GradeSchool -> 1,
                ChildRateType.PreSchool -> 0,
                ChildRateType.Toddler -> 1,
                ChildRateType.Baby -> 0).filter(_._2 > 0).toMap,
            Map(1 -> 1, 2 -> 0, 3 -> 0, 4 -> 1).filter(_._2 > 0),
          ),
        )
        yplRequest.searchId should_== "id"
        yplRequest.checkIn should_== chIn
        yplRequest.checkOut should_== chOut
        yplRequest.currency should_== "THB"
        yplRequest.channels should_== Set(YplMasterChannel(1), YplMasterChannel(2), YplMasterChannel(3))
        yplRequest.experiments should_== supplyRequest.experiments.map(e => YplExperiment(e.name, e.variant))
        yplRequest.occ._adults should_== supplyRequest.occ._adults
        yplRequest.occ._children should_== supplyRequest.occ._children
        yplRequest.occ.roomAssignment should_== expectedRoomAssignment
        yplRequest.occ._rooms should_== supplyRequest.occ._rooms
        yplRequest.cInfo.language should_== supplyRequest.cInfo.language
        yplRequest.cInfo.platform should_== supplyRequest.cInfo.platform
        yplRequest.cInfo.cid should_== supplyRequest.cInfo.cid
        yplRequest.cInfo.origin should_== supplyRequest.cInfo.origin
        yplRequest.flagInfo should_== supplyRequest.flagInfo
        yplRequest.bookingDate should_== supplyRequest.bookingDate
        yplRequest.featureFlags should_== supplyRequest.featureFlags.toSet
        yplRequest.isSSR should_== false
        yplRequest.isBookingRequest should_== false
        yplRequest.simulateRequestData should_== supplyRequest.simulateRequestData
        yplRequest.isCheapestRoomOnly should_== true
        yplRequest.featureRequest should_== supplyRequest.featureRequest
        yplRequest.whitelabelSetting should_== supplyRequest.whitelabelSetting
        yplRequest.supplierFeatures should_== aValidSupplierFeatures
        yplRequest.bComCCSetting should_== Some(BComCCSetting(true, true))
        yplRequest.bcomBMPSetting should_== Some(BComBMPSetting(true, true))
        yplRequest.featureRequest.enableRatePlanCheckInCheckOut should_=== false
        yplRequest.featureRequest.showPastMidnightSlots should_=== false
        yplRequest.featureRequest.disableEscapesPackage should_=== false
        yplRequest.featureRequest.enableRichContentOffer should_=== false
        yplRequest.featureRequest.enableHourlySlotsForDayuseInOvernight should_=== false
        yplRequest.featureRequest.enableThirtyMinsSlots should_=== false
        yplRequest.heuristicRequest must beSome(YplHeuristicRequest(Some(60), Some(0), Some(20)))
        yplRequest.bookingDurationTypes should_== List(BookingDurationType.Hourly)
        yplRequest.selectedCheckInTime should_== None
        yplRequest.hourlyDurationFilter should_== Set.empty
        yplRequest.supplierFundedDiscountSetting should_== SupplierFundedDiscountSetting()
        yplRequest.priceAdjustmentId should_== Some(12)
      }

      "Calculated fields: isSSR & isBookingRequest" in {
        val request = supplyRequestBuilder.withSearchType(SearchType.AreaSearch)
        val ctx = aValidSupplyContext.withBaseRequest(request)
        val yplRequestSSR1 = baseRequestConverter.toYplRequest()(ctx)
        yplRequestSSR1.isSSR should_== true
        yplRequestSSR1.isBookingRequest should_== false

        val yplRequestRegular = baseRequestConverter.toYplRequest()(aValidSupplyContext.withBaseRequest(
          supplyRequestBuilder.withSearchType(searchType = SearchType.HotelListSearch)))
        yplRequestRegular.isSSR should_== false
        yplRequestRegular.isBookingRequest should_== false

        val yplRequestBooking = baseRequestConverter.toYplRequest()(aValidSupplyContext.withBaseRequest(
          supplyRequestBuilder.withSearchType(searchType = SearchType.HotelForBooking)))
        yplRequestBooking.isSSR should_== false
        yplRequestBooking.isBookingRequest should_== true
      }

      "Feature flags passed correctly to YPL" in {
        val request = supplyRequestBuilder.withFeatureRequest(
          featureRequest.copy(enableRatePlanCheckInCheckOut = true, showCouponAmountInUserCurrency = true))
        val ctx = aValidSupplyContext.withBaseRequest(request)
        val yplRequestSSR1 = baseRequestConverter.toYplRequest()(ctx)
        yplRequestSSR1.featureRequest.enableRatePlanCheckInCheckOut should_=== true
        yplRequestSSR1.featureRequest.showCouponAmountInUserCurrency should_=== true
      }

      "Feature flags passed correctly to YPL for escapes" in {
        val request = supplyRequestBuilder.withFeatureRequest(featureRequest.copy(disableEscapesPackage = true))
        val ctx = aValidSupplyContext.withBaseRequest(request)
        val yplRequest = baseRequestConverter.toYplRequest()(ctx)
        yplRequest.featureRequest.disableEscapesPackage should_=== true
      }

      "Feature flags passed correctly to YPL for dayuse" in {
        val request = supplyRequestBuilder.withFeatureRequest(
          featureRequest.copy(showPastMidnightSlots = true, enableHourlySlotsForDayuseInOvernight = true),
        )
        val ctx = aValidSupplyContext.withBaseRequest(request)
        val yplRequest = baseRequestConverter.toYplRequest()(ctx)
        yplRequest.featureRequest.showPastMidnightSlots should_=== true
        yplRequest.featureRequest.enableHourlySlotsForDayuseInOvernight should_=== true
      }

      "Feature flags passed correctly to YPL for 30 mins slots" in {
        val request = supplyRequestBuilder.withFeatureRequest(
          featureRequest.copy(enableThirtyMinsSlots = true),
        )
        val ctx = aValidSupplyContext.withBaseRequest(request)
        val yplRequest = baseRequestConverter.toYplRequest()(ctx)
        yplRequest.featureRequest.enableThirtyMinsSlots should_=== true
      }

      "[SCAL-1433=A] ReturnZeroAllotment Feature flags passed correctly to YPL" in {
        val request = supplyRequestBuilder.withFeatureFlags(
          List(
            FeatureFlag.Unknown,
            FeatureFlag.ReturnZeroAllotment,
          ),
        )
        implicit val ctx = aValidSupplyContext.withBaseRequest(request)
        val yplRequestSSR1 = baseRequestConverter.toYplRequest()(ctx)
        yplRequestSSR1.featureFlags.contains(FeatureFlag.ReturnZeroAllotment) should_=== true
        yplRequestSSR1.featureFlags.size should_=== 2
      }

      "[SCAL-1433=B] Remove ReturnZeroAllotment Feature flags and passed correctly to YPL" in {
        val request = supplyRequestBuilder
          .withFeatureFlags(
            List(
              FeatureFlag.Unknown,
              FeatureFlag.ReturnZeroAllotment,
            ),
          )
          .withExperiments(List(YplExperiment(ABTest.REMOVE_ZERO_ALLOTMENT_FEATURE_FLAG, 'B')))
        implicit val ctx = aValidSupplyContext.withBaseRequest(request)

        val yplRequestSSR1 = baseRequestConverter.toYplRequest()(ctx)
        yplRequestSSR1.featureFlags.contains(FeatureFlag.ReturnZeroAllotment) should_=== false
        yplRequestSSR1.featureFlags.size should_=== 1
      }

      "Fetch ApplyTaxOnSellExSettings from DB, merged with consul and passed correctly to YPL" in {
        val dfRequestWithFlag = supplyRequestBuilder.build
        implicit val ctx = aValidSupplyContext.withBaseRequest(dfRequestWithFlag)
        val allowListFromDB = List(
          ApplyTaxOnSellExSetting(
            supplierIds = Some(Set(123)),
            hotelIds = None,
            chainIds = Some(Set(456)),
            countryIds = Some(Set(789)),
            paymentModelId = Some(4),
          ),
        )
        val expected = Some(ApplyTaxOnSellExSettings(allowListFromDB))
        val yplRequest = baseRequestConverter.toYplRequest(applyTaxOnSellExSettingsFromSupplierCalculationSettings =
          allowListFromDB)(ctx)
        yplRequest.applyTaxOnSellExSettings should_== expected
      }

      "Map YplFenceRate with empty data" in {
        val actual = baseRequestConverter.toYplRequest()(aValidSupplyContext.withBaseRequest(supplyRequestBuilder.build))
        actual.fences should_=== Map(
          YplMasterChannel.APS -> Set(YplRateFence("TH", 1, 1)),
          YplMasterChannel.NET -> Set(YplRateFence("TH", 1, 1)),
          YplMasterChannel.RTL -> Set(YplRateFence("TH", 1, 1)),
          YplMasterChannel.APO -> Set(YplRateFence("TH", 1, 1)),
        )
      }

      "Map YplFenceRate with dispatch info" in {
        val hotelDispatch = HotelDispatchInfo(1, Seq.empty)
        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(hotelDispatch), Set.empty, Map.empty)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder)(
          aValidSupplyContext.withBaseRequest(supplyRequestBuilder.build))
        actual.fences should_=== Map.empty
      }

      "Map YplFenceRate correctly" in {
        val fencedRequested = supplyRequestBuilder
          .withCInfo(aValidClientInfo.withOrigin(Some("TH")).withCid(Some(1)).build)
          .withChannels(Set(YplMasterChannel.RTL, YplMasterChannel.Domestic))
          .withAdditionalChannels(Set(YplMasterChannel.APS, YplMasterChannel.APO))
          .build

        implicit val ctx = aValidSupplyContext.withBaseRequest(fencedRequested)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder = SupplierDispatchChannelHolder(),
                                                       supplierIds = Set(DMC.YCS))(ctx)
        actual.fences should_=== Map(
          YplChannel(Channel.RTL) -> Set(YplRateFence("TH", 1, 1)),
          YplChannel(Channel.Domestic) -> Set(YplRateFence("TH", 1, 1)),
          YplChannel(Channel.APS) -> Set(YplRateFence("TH", 1, 1)),
          YplChannel(Channel.APO) -> Set(YplRateFence("TH", 1, 1)),
        )
      }

      "Map YplFenceRate correctly with language" in {
        val fencedRequested = supplyRequestBuilder
          .withCInfo(aValidClientInfo.withOrigin(Some("TH")).withCid(Some(1)).withLanguage(22).build)
          .withChannels(Set(YplMasterChannel.RTL, YplMasterChannel.Domestic))
          .build
          .copy(additionalChannels = Set(YplMasterChannel.APS, YplMasterChannel.APO))

        implicit val ctx = aValidSupplyContext.withBaseRequest(fencedRequested)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder = SupplierDispatchChannelHolder(),
                                                       supplierIds = Set(DMC.YCS))(ctx)
        actual.fences should_=== Map(
          YplChannel(Channel.RTL) -> Set(YplRateFence("TH", 1, 22)),
          YplChannel(Channel.Domestic) -> Set(YplRateFence("TH", 1, 22)),
          YplChannel(Channel.APS) -> Set(YplRateFence("TH", 1, 22)),
          YplChannel(Channel.APO) -> Set(YplRateFence("TH", 1, 22)),
        )
      }

      "Map YplFenceRate correctly (SCAL-1042=B)" in {
        val usFence = FencedRatePair(YplRateFence(Some("US"), Some(1), None), FencedOriginObject(Set.empty))
        val ukFence = FencedRatePair(YplRateFence(Some("UK"), Some(1), None), FencedOriginObject(Set.empty))
        val emptyFence = FencedRatePair(YplRateFence(None, None, None), FencedOriginObject(Set.empty))

        val usRetail = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = usFence)
        val ukRetail = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = ukFence)
        val emptyRetail = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = emptyFence)
        val usRetail2 = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = usFence)
        val ukRetail2 = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = ukFence)
        val emptyRetail2 = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = emptyFence)
        val usAPO = DispatchInfo(1, DMC.YCS, YplMasterChannel.APO, 1, Nil, fencedRatePair = usFence)
        val ukAPO = DispatchInfo(1, DMC.YCS, YplMasterChannel.APO, 1, Nil, fencedRatePair = ukFence)
        val dispatches = Seq(usRetail, usRetail2, ukRetail, ukRetail2, emptyRetail, emptyRetail2, usAPO, ukAPO)
        val hotelDispatch = HotelDispatchInfo(1, dispatches)

        val fencedRequested = supplyRequestBuilder.withFencedRatePairs(
          Some(
            List(
              usFence,
              ukFence,
              emptyFence,
            )))
        val ctx = aValidSupplyContext.withBaseRequest(fencedRequested)

        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(hotelDispatch), Set.empty, Map.empty)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder = supplierDispatchChannelHolder,
                                                       supplierIds = Set(DMC.YCS))(ctx)
        actual.fences should_=== Map(
          YplChannel(Channel.RTL) -> Set(YplRateFence("US", 1, 1), YplRateFence("UK", 1, 1), YplRateFence("", 0, 1)),
          YplChannel(Channel.APO) -> Set(YplRateFence("US", 1, 1), YplRateFence("UK", 1, 1)),
        )
      }

      "Map YplFenceRate with language correctly (SCAL-1042=B)" in {
        val usFence = FencedRatePair(YplRateFence(Some("US"), Some(1), Some(22)), FencedOriginObject(Set.empty))
        val ukFence = FencedRatePair(YplRateFence(Some("UK"), Some(1), Some(7)), FencedOriginObject(Set.empty))
        val emptyFence = FencedRatePair(YplRateFence(None, None, None), FencedOriginObject(Set.empty))
        val usRetail = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = usFence)
        val ukRetail = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = ukFence)
        val emptyRetail = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = emptyFence)
        val usRetail2 = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = usFence)
        val ukRetail2 = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = ukFence)
        val emptyRetail2 = DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = emptyFence)
        val usAPO = DispatchInfo(1, DMC.YCS, YplMasterChannel.APO, 1, Nil, fencedRatePair = usFence)
        val ukAPO = DispatchInfo(1, DMC.YCS, YplMasterChannel.APO, 1, Nil, fencedRatePair = ukFence)
        val dispatches = Seq(usRetail, usRetail2, ukRetail, ukRetail2, emptyRetail, emptyRetail2, usAPO, ukAPO)
        val hotelDispatch = HotelDispatchInfo(1, dispatches)

        val fencedRequested = supplyRequestBuilder.withFencedRatePairs(
          Some(
            List(
              usFence,
              ukFence,
              emptyFence,
            )))
        val ctx = aValidSupplyContext.withBaseRequest(fencedRequested)

        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(hotelDispatch), Set.empty, Map.empty)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder = supplierDispatchChannelHolder,
                                                       supplierIds = Set(DMC.YCS))(ctx)
        actual.fences should_=== Map(
          YplChannel(Channel.RTL) -> Set(YplRateFence("US", 1, 22), YplRateFence("UK", 1, 7), YplRateFence("", 0, 1)),
          YplChannel(Channel.APO) -> Set(YplRateFence("US", 1, 22), YplRateFence("UK", 1, 7)),
        )
      }

      "Map YplFenceRate for requested channels only if there is dispatch for the given supplier with that fence" in {
        val usFence = FencedRatePair(YplRateFence(Some("US"), Some(1), None), FencedOriginObject(Set(1, 2)))
        val ukFence = FencedRatePair(YplRateFence(Some("UK"), Some(1), None), FencedOriginObject(Set(1, 2, 6, 7)))

        val dispatches = Seq(
          DispatchInfo(1, DMC.Accor, YplMasterChannel.Domestic, 1, Nil, fencedRatePair = ukFence),
        )
        val request = supplyRequestBuilder.withFencedRatePairs(Some(List(ukFence, usFence))).build
        val context = aValidSupplyContext.withBaseRequest(request)
        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(HotelDispatchInfo(1, dispatches)), Set.empty, Map.empty)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder = supplierDispatchChannelHolder,
                                                       supplierIds = Set(DMC.Accor))(context)
        actual.fences should_=== Map(
          YplChannel(Channel.RTL) -> Set(YplRateFence("UK", 1, 1)),
          YplChannel(Channel.APS) -> Set(YplRateFence("UK", 1, 1)),
          YplChannel(Channel.Domestic) -> Set(YplRateFence("UK", 1, 1)),
          YplChannel(Channel.Mobile) -> Set(YplRateFence("UK", 1, 1)),
        )
      }

      "Map YplFenceRate without experiment (SCAL-1042=A)" in {
        val usRetail =
          DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = aValidFencedRatePair.build)
        val ukRetail =
          DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = aValidFencedRatePair.build)
        val emptyRetail =
          DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = aValidFencedRatePair.build)
        val usRetail2 =
          DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = aValidFencedRatePair.build)
        val ukRetail2 =
          DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = aValidFencedRatePair.build)
        val emptyRetail2 =
          DispatchInfo(1, DMC.YCS, YplMasterChannel.RTL, 1, Nil, fencedRatePair = aValidFencedRatePair.build)
        val usAPO = DispatchInfo(1, DMC.YCS, YplMasterChannel.APO, 1, Nil, fencedRatePair = aValidFencedRatePair.build)
        val ukAPO = DispatchInfo(1, DMC.YCS, YplMasterChannel.APO, 1, Nil, fencedRatePair = aValidFencedRatePair.build)

        val dispatchs = Seq(usRetail, usRetail2, ukRetail, ukRetail2, emptyRetail, emptyRetail2, usAPO, ukAPO)
        val hotelDispatch = HotelDispatchInfo(1, dispatchs)

        val overridedRequest = supplyRequestBuilder.withAExperiment("SCAL-1042")
        val overridedCtx = aValidSupplyContext.withBaseRequest(overridedRequest)
        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(hotelDispatch), Set.empty, Map.empty)
        val actual =
          baseRequestConverter.toYplRequest(supplierDispatchChannelHolder = supplierDispatchChannelHolder)(overridedCtx)
        actual.fences should_=== Map.empty
      }

      "Map Origin/CID to Ypl Fenced Rates based on heisenberg dispatch" in {
        val request =
          supplyRequestBuilder.withOrigin(Some("HK")).withCID(Some(42)).withChannels(Set(YplMasterChannel.RTL))
        val ctx = aValidSupplyContext.withBaseRequest(request)

        val fencePair = FencedRatePair(YplRateFence(Some("HK"), Some(42), Some(1)),
                                       FencedOriginObject(Set(YplMasterChannel.RTL.baseChannelId)))

        val dispatch =
          HotelDispatchInfo(1, Seq(DispatchInfo(1, DMC.YCS, YplMasterChannel.Mobile, 1, fencedRatePair = fencePair)))
        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(dispatch), Set.empty, Map.empty)
        val result = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder = supplierDispatchChannelHolder,
                                                       supplierIds = Set(DMC.YCS))(ctx)

        result.experiments.find(_.name == "SCAL-1042") should beSome(YplExperiment("SCAL-1042", 'B'))
        result.fences should_== Map(
          YplMasterChannel.Mobile -> Set(YplRateFence("HK", 42, 1)),
          YplMasterChannel.RTL -> Set(YplRateFence("HK", 42, 1)),
        )
      }

      "Map Origin/CID to Ypl Fenced Rates without heisenberg dispatch" in {
        val request =
          supplyRequestBuilder.withOrigin(Some("HK")).withCID(Some(42)).withChannels(Set(YplMasterChannel.RTL))
        val ctx = aValidSupplyContext.withBaseRequest(request)

        val result = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder = SupplierDispatchChannelHolder(),
                                                       supplierIds = Set(DMC.YCS))(ctx)

        result.fences should_== Map(
          YplMasterChannel.RTL -> Set(YplRateFence("HK", 42, 1)),
          YplMasterChannel.APS -> Set(YplRateFence("HK", 42, 1)),
          YplMasterChannel.APO -> Set(YplRateFence("HK", 42, 1)),
        )
      }

      "Map default childAgeRangeAssignment" in {
        val expDfRequest = supplyRequestBuilder.withExperiments(
          experiments = List(
            YplExperiment("experimentId", 'B'),
            YplExperiment(ABTest.PRICE_PUSH_FENCING, 'B'),
          ),
        )

        implicit val ctx = aValidSupplyContext.withBaseRequest(expDfRequest)

        val yplRequest = baseRequestConverter.toYplRequest()(ctx)

        val expectedRoomAssignment = List(
          YplRoomAssignment(
            1,
            Map(ChildRateType.Unknown -> 0,
                ChildRateType.GradeSchool -> 0,
                ChildRateType.PreSchool -> 1,
                ChildRateType.Toddler -> 0,
                ChildRateType.Baby -> 1).filter(_._2 > 0).toMap,
            Map(1 -> 0, 2 -> 1, 3 -> 1, 4 -> 0).filter(_._2 > 0).toMap,
          ),
          YplRoomAssignment(
            1,
            Map(ChildRateType.Unknown -> 0,
                ChildRateType.GradeSchool -> 1,
                ChildRateType.PreSchool -> 0,
                ChildRateType.Toddler -> 1,
                ChildRateType.Baby -> 0).filter(_._2 > 0).toMap,
            Map(1 -> 1, 2 -> 0, 3 -> 0, 4 -> 1).filter(_._2 > 0).toMap,
          ),
        )
        yplRequest.occ._adults should_== expDfRequest.occ._adults
        yplRequest.occ._children should_== expDfRequest.occ._children
        yplRequest.occ.roomAssignment should_== expectedRoomAssignment
      }

      "No FilterCriteria present in request" in {
        val actual = baseRequestConverter.toYplRequest()(aValidSupplyContext.withBaseRequest(supplyRequestBuilder.build))
        actual.filterCriteria.isDefined should_=== false
      }

      "Empty FilterCriteria present in request" in {
        val actual = baseRequestConverter.toYplRequest()(
          aValidSupplyContext.withBaseRequest(supplyRequestBuilder.withFilterCriteria(Some(List())).build))
        actual.filterCriteria.isDefined should_=== true
        actual.filterCriteria.get.length should_=== 0
      }

      "FilterCriteria present in request" in {
        val actual = baseRequestConverter.toYplRequest()(
          aValidSupplyContext.withBaseRequest(
            supplyRequestBuilder
              .withFilterCriteria(Some(List(FilterCriteria.FreeCancellable, FilterCriteria.NonRefundable)))
              .build))
        actual.filterCriteria.isDefined should_=== true
        actual.filterCriteria.get.length should_=== 2
      }

      "getYplChannels correctly #1" in {
        val src = supplyRequestBuilder.withChannels(Set.empty)
        val fences = Map(
          YplChannel(1, Set(1), 1) -> Set.empty[YplRateFence],
          YplChannel(2, Set(2), 2) -> Set.empty[YplRateFence],
        )

        val yplChannels = baseRequestConverter.getYplChannels(src = src, fences = fences, isPricePushFencing = true)
        yplChannels should_== Set(YplChannel(1, Set(1), 1), YplChannel(2, Set(2), 2))
      }

      "getYplChannels correctly #2" in {
        val src = supplyRequestBuilder.withChannels(Set.empty)

        val yplChannels = baseRequestConverter.getYplChannels(src = src, fences = Map.empty, isPricePushFencing = false)
        yplChannels should_== Set.empty
      }

      "map selectedCheckInTime correctly" in {
        val src = supplyRequestBuilder.withSelectedCheckInTime(Some("15:00"))
        val yplRequest = baseRequestConverter.toYplRequest()(aValidSupplyContext.withBaseRequest(src))

        yplRequest.selectedCheckInTime should_== Some("15:00")
      }

      "map hourlyDurationFilter correctly" in {
        val src = supplyRequestBuilder.withHourlyDurationFilter(Set(1, 2, 3))
        val yplRequest = baseRequestConverter.toYplRequest()(aValidSupplyContext.withBaseRequest(src))

        yplRequest.hourlyDurationFilter should_== Set(1, 2, 3)
      }

      "Build YPLRequest with supplierFundedDiscountSettings when OG-149-KILLSWITCH is B" in {
        val dfRequestWithFlag = supplyRequestBuilder.withBExperiment("OG-149-KILLSWITCH")
        implicit val ctx = aValidSupplyContext.withBaseRequest(dfRequestWithFlag)
        val yplRequest = baseRequestConverter.toYplRequest()(ctx)
        yplRequest.supplierFundedDiscountSetting shouldEqual SupplierFundedDiscountSetting(Set(123, 456),
                                                                                           Set(111, 321, 555, 666))
      }

      "Build YPLRequest with empty supplierFundedDiscountSettings when OG-149-KILLSWITCH is A and -990 not dispatched" in {
        val dfRequestWithFlag = supplyRequestBuilder.withAExperiment("OG-149-KILLSWITCH")
        implicit val ctx = aValidSupplyContext.withBaseRequest(dfRequestWithFlag)
        val yplRequest = baseRequestConverter.toYplRequest()(ctx)
        yplRequest.supplierFundedDiscountSetting shouldEqual SupplierFundedDiscountSetting()
      }

      "Build YPLRequest with supplierFundedDiscountSettings when OG-149-KILLSWITCH is A and -990 dispatched" in {
        val hotelDispatch = HotelDispatchInfo(1, Seq.empty)
        val fenced: Map[YplRateFence, Set[RatePlanInfo]] =
          Map(YplRateFence("", -1, 1) -> Set(RatePlanInfo(YplChannel(-990))))
        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(hotelDispatch), Set(123, 456), fenced)
        val dfRequestWithFlag = supplyRequestBuilder.withAExperiment("OG-149-KILLSWITCH")
        implicit val ctx = aValidSupplyContext.withBaseRequest(dfRequestWithFlag)
        val yplRequest = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder)(ctx)
        yplRequest.supplierFundedDiscountSetting shouldEqual SupplierFundedDiscountSetting(Set(123, 456),
                                                                                           Set(111, 321, 555, 666))
      }

      "Build YPLRequest with isApplyNewOccupancyLogicExp when -899 is dispatched" in {
        val hotelDispatch = HotelDispatchInfo(
          1,
          Seq(
            DispatchInfo(1, DMC.YCS, YplChannel(1), 4, fencedRatePair = aValidFencedRatePair.build),
            DispatchInfo(1, DMC.YCS, YplChannel(-899), 4, fencedRatePair = aValidFencedRatePair.build),
          ),
        )
        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(hotelDispatch), Set.empty, Map.empty)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder)(
          aValidSupplyContext.withBaseRequest(supplyRequestBuilder.build))
        actual.isApplyNewOccupancyLogic should_=== true
      }

      "Build YPLRequest with isApplyNewOccupancyLogicExp when -899 is not dispatched" in {
        val hotelDispatch = HotelDispatchInfo(
          1,
          Seq(
            DispatchInfo(1, DMC.YCS, YplChannel(1), 4, fencedRatePair = aValidFencedRatePair.build),
            DispatchInfo(1, DMC.YCS, YplChannel(2), 4, fencedRatePair = aValidFencedRatePair.build),
          ),
        )
        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(hotelDispatch), Set.empty, Map.empty)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder)(
          aValidSupplyContext.withBaseRequest(supplyRequestBuilder.build))
        actual.isApplyNewOccupancyLogic should_=== false
      }

      "Build YPLRequest with isApplyNewOccupancyLogicExp when hotelDispatch is empty" in {
        val supplierDispatchChannelHolder = SupplierDispatchChannelHolder(List.empty, None, Set.empty, Map.empty)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder)(
          aValidSupplyContext.withBaseRequest(supplyRequestBuilder.build))
        actual.isApplyNewOccupancyLogic should_=== false
      }

      "Build YPLRequest with isAvailableCapacityIncludeChildren when -996 is dispatched" in {
        val hotelDispatch = HotelDispatchInfo(
          1,
          Seq(
            DispatchInfo(1, DMC.YCS, YplChannel(1), 4, fencedRatePair = aValidFencedRatePair.build),
            DispatchInfo(1, DMC.YCS, YplChannel(-996), 4, fencedRatePair = aValidFencedRatePair.build),
          ),
        )
        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(hotelDispatch), Set.empty, Map.empty)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder)(
          aValidSupplyContext.withBaseRequest(supplyRequestBuilder.build))
        actual.isAvailableCapacityIncludeChildren should_=== true
      }

      "Build YPLRequest with isAvailableCapacityIncludeChildren when -899 is not dispatched" in {
        val hotelDispatch = HotelDispatchInfo(
          1,
          Seq(
            DispatchInfo(1, DMC.YCS, YplChannel(1), 4, fencedRatePair = aValidFencedRatePair.build),
            DispatchInfo(1, DMC.YCS, YplChannel(2), 4, fencedRatePair = aValidFencedRatePair.build),
          ),
        )
        val supplierDispatchChannelHolder =
          SupplierDispatchChannelHolder(List.empty, Some(hotelDispatch), Set.empty, Map.empty)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder)(
          aValidSupplyContext.withBaseRequest(supplyRequestBuilder.build))
        actual.isAvailableCapacityIncludeChildren should_=== false
      }

      "Build YPLRequest with isAvailableCapacityIncludeChildren when hotelDispatch is empty" in {
        val supplierDispatchChannelHolder = SupplierDispatchChannelHolder(List.empty, None, Set.empty, Map.empty)
        val actual = baseRequestConverter.toYplRequest(supplierDispatchChannelHolder)(
          aValidSupplyContext.withBaseRequest(supplyRequestBuilder.build))
        actual.isAvailableCapacityIncludeChildren should_=== false
      }
    }

  }

  "createHeuristicRequest" should {
    val factor = 2
    val minCap = 20
    val maxCap = 100
    val botCap = 10
    val asoMinCap = 5
    val cheapestCap = 5

    val setting = HeuristicRoomLimitSetting(factor, minCap, maxCap, botCap, asoMinCap, cheapestCap)

    val baseRequestConverter = new BaseRequestConverter(
      HeuristicRoomLimitSettingsProducerMock.static,
      SupplierFeatures(Map.empty),
      CompositeChannelContextMock,
      APMConfigSettingsProducerMock.static,
      OfferFilterTrackingSettingsProducerMock.static,
      SuperAggOccupancyFlowSettingProducerMock.static,
    )

    "send roomlimit as minCap for cheapestOnly" in {
      val request = aValidSupplyBaseRequest.withIsCheapestRoomOnly(true)
      val ctx = aValidSupplyContext.withBaseRequest(request)
      baseRequestConverter.createHeuristicRequest(request, setting) must beSome(
        YplHeuristicRequest(Some(minCap), Some(asoMinCap), Some(cheapestCap)))
    }

    "send roomlimit as minCap for unknown bot" in {
      val request = aValidSupplyBaseRequest
        .withTrafficInfo(TrafficInfo(Some(BotProfile(TrafficType.UnknownBot, Map.empty, None))))
        .withIsCheapestRoomOnly(false)
      val ctx = aValidSupplyContext.withBaseRequest(request)
      baseRequestConverter.createHeuristicRequest(request, setting) must beSome(
        YplHeuristicRequest(Some(botCap), Some(asoMinCap), Some(cheapestCap)))
    }

    "not send roomlimit if maxRoom is set for booking request" in {
      val request = aValidSupplyBaseRequest.withMaxRooms(Some(20)).withSearchType(SearchType.HotelForBooking)
      val ctx = aValidSupplyContext.withBaseRequest(request)
      baseRequestConverter.createHeuristicRequest(request, setting) must beSome(
        YplHeuristicRequest(Some(100), Some(5), Some(5)))
    }

    "send roomlimit if maxRoom is set with negative number" in {
      val request = aValidSupplyBaseRequest.withMaxRooms(Some(-20))
      val ctx = aValidSupplyContext.withBaseRequest(request)
      baseRequestConverter.createHeuristicRequest(request, setting) must beSome(
        YplHeuristicRequest(Some(100), Some(asoMinCap), Some(cheapestCap)))
    }

    "send roomlimit if maxRoom is set as factor*maxrooms if it's more than mincap and less than maxcap" in {
      val request = aValidSupplyBaseRequest.withMaxRooms(Some(20))
      val ctx = aValidSupplyContext.withBaseRequest(request)
      baseRequestConverter.createHeuristicRequest(request, setting) must beSome(
        YplHeuristicRequest(Some(40), Some(asoMinCap), Some(cheapestCap)))
    }

    "send roomlimit if maxRoom is set as mincap if factor*maxrooms is less than mincap" in {
      val request = aValidSupplyBaseRequest.withMaxRooms(Some(5))
      val ctx = aValidSupplyContext.withBaseRequest(request)
      baseRequestConverter.createHeuristicRequest(request, setting) must beSome(
        YplHeuristicRequest(Some(minCap), Some(asoMinCap), Some(cheapestCap)))
    }

    "send roomlimit if maxRoom is set as maxcap if factor*maxrooms is less than maxcap" in {
      val request = aValidSupplyBaseRequest.withMaxRooms(Some(1000))
      val ctx = aValidSupplyContext.withBaseRequest(request)
      baseRequestConverter.createHeuristicRequest(request, setting) must beSome(
        YplHeuristicRequest(Some(maxCap), Some(asoMinCap), Some(cheapestCap)))
    }

    "send roomlimit for non booking request" in {
      val request = aValidSupplyBaseRequest
      val ctx = aValidSupplyContext.withBaseRequest(request)
      baseRequestConverter.createHeuristicRequest(request, setting) must beSome(
        YplHeuristicRequest(Some(maxCap), Some(asoMinCap), Some(cheapestCap)))
    }
  }

  "BaseRequestConverter.createYplFenceRates" should {

    val fencedRateKeyTH = YplRateFence(Some("TH"), Some(3), None)
    val compositeChannelId = 220
    val fencedRatePairTH = FencedRatePair(fencedRateKeyTH, FencedOriginObject(Set(compositeChannelId)))
    val pairs = List(
      fencedRatePairTH,
    )

    val compositeChannel = YplChannel(Seq(Channel.APS, Channel.Domestic), compositeChannelId).get
    val hotelIdToUse = aValidSupplyBaseRequest.hotels.head
    val dispatch1 = DispatchInfo(hotelId = hotelIdToUse.toInt,
                                 dispatchDmc = DMC.YCS,
                                 dispatchedMasterChannel = compositeChannel,
                                 dispatchType = 1,
                                 fencedRatePair = fencedRatePairTH)

    val hotelDispatchInfo = HotelDispatchInfo(hotelIdToUse, Seq(dispatch1))
    implicit val compositeChannelService = new CompositeChannelContextMock {
      override def getCompositeChannelId(baseChannelId: Int, stackedChannelIds: Set[Int] = Set.empty): Option[Int] =
        if ((baseChannelId == Channel.APS || baseChannelId == Channel.Domestic) && (stackedChannelIds == Set(
            Channel.APS) || stackedChannelIds == Set(Channel.Domestic))) {
          Some(compositeChannelId)
        } else {
          Some(baseChannelId)
        }
      override def toCompositeChannelIds(channelId: Int): Set[Int] =
        if (channelId == compositeChannelId) Set(Channel.APS, Channel.Domestic)
        else Set(channelId)
    }

    val baseRequestConverter = new BaseRequestConverter(
      HeuristicRoomLimitSettingsProducerMock.static,
      aValidSupplierFeatures,
      CompositeChannelContextMock,
      APMConfigSettingsProducerMock.static,
      OfferFilterTrackingSettingsProducerMock.static,
      SuperAggOccupancyFlowSettingProducerMock.static,
    )

    "should return dispatched composite channel + master channels" in {
      implicit val flowContext = aValidSupplyContext
      val fencedRate =
        baseRequestConverter.createYplFenceRates(pairs, Some(hotelDispatchInfo), Set(DMC.YCS))(compositeChannelService,
                                                                                               flowContext.build)
      fencedRate.keySet should_== Set(YplChannel(2, Set(), 2),
                                      YplChannel(6, Set(), 6),
                                      YplChannel(2, Set(6), 220),
                                      YplChannel(6, Set(2), 220))
    }

  }

}
