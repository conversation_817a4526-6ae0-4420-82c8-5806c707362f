package services.dmcMetaData

import com.agoda.papi.pricing.core.system.PrecacheServiceContainer
import com.agoda.papi.pricing.supply.db.dmcMetaData.{
  DmcMetaDataLoader,
  DmcMetaDataService,
  DmcMetaDataServiceImpl,
  PrecachedDmcMetaDataService,
}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.utils.monitoring.NoOpReportingService
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

class DmcMetaDataServiceSpec extends SpecificationWithJUnit with Mockito {

  "DmcMetaDataService" should {
    "defer to cache service" in {
      val symbolServiceMock = {
        val service = mock[PrecachedDmcMetaDataService]
        doReturn(true).when(service).isDirectConnect(DMC.YCS)
        doReturn(true).when(service).isDirectConnect(DMC.Marriott)
        doReturn(false).when(service).isDirectConnect(DMC.BCOM)

        doReturn(false).when(service).is3rdPartySupply(DMC.YCS)
        doReturn(false).when(service).is3rdPartySupply(DMC.Marriott)
        doReturn(true).when(service).is3rdPartySupply(DMC.BCOM)

        doReturn(None).when(service).getOccupancyModel(DMC.YCS)
        doReturn(Some(1)).when(service).getOccupancyModel(DMC.Accor)
        doReturn(Some(6)).when(service).getOccupancyModel(DMC.Marriott)
        doReturn(Some(6)).when(service).getOccupancyModel(DMC.BCOM)

        doReturn(false).when(service).isOccModelFPLOS(DMC.YCS)
        doReturn(false).when(service).isOccModelFPLOS(DMC.Accor)
        doReturn(true).when(service).isOccModelFPLOS(DMC.Marriott)
        doReturn(true).when(service).isOccModelFPLOS(DMC.BCOM)

        doReturn(true).when(service).isBnplEnabled(DMC.YCS)
        doReturn(true).when(service).isBnplEnabled(DMC.Accor)
        doReturn(true).when(service).isBnplEnabled(DMC.Marriott)
        doReturn(true).when(service).isBnplEnabled(DMC.BCOM)

        doReturn(None).when(service).isPull(DMC.YCS)
        doReturn(None).when(service).isPull(DMC.Accor)
        doReturn(Some(true)).when(service).isPull(DMC.Marriott)
        doReturn(Some(true)).when(service).isPull(DMC.BCOM)

        service
      }

      val containerMock = {
        val service = mock[PrecacheServiceContainer]
        doReturn(symbolServiceMock).when(service).getService[PrecachedDmcMetaDataService]("dmc-metadata")
        service
      }

      val cut = new DmcMetaDataServiceImpl {
        override protected val serviceContainer: PrecacheServiceContainer = containerMock
      }

      cut.isDirectConnect(DMC.YCS) should beTrue
      cut.isDirectConnect(DMC.Marriott) should beTrue
      cut.isDirectConnect(DMC.BCOM) should beFalse

      cut.is3rdPartySupply(DMC.YCS) should beFalse
      cut.is3rdPartySupply(DMC.Marriott) should beFalse
      cut.is3rdPartySupply(DMC.BCOM) should beTrue

      cut.getOccupancyModel(DMC.YCS) should beEmpty
      cut.getOccupancyModel(DMC.Accor) should beSome(1)
      cut.getOccupancyModel(DMC.Marriott) should beSome(6)
      cut.getOccupancyModel(DMC.BCOM) should beSome(6)

      cut.isOccModelFPLOS(DMC.YCS) should beFalse
      cut.isOccModelFPLOS(DMC.Accor) should beFalse
      cut.isOccModelFPLOS(DMC.Marriott) should beTrue
      cut.isOccModelFPLOS(DMC.BCOM) should beTrue

      cut.isBnplEnabled(DMC.YCS) should beTrue
      cut.isBnplEnabled(DMC.Accor) should beTrue
      cut.isBnplEnabled(DMC.Marriott) should beTrue
      cut.isBnplEnabled(DMC.BCOM) should beTrue

      cut.isPull(DMC.YCS) should beNone
      cut.isPull(DMC.Accor) should beNone
      cut.isPull(DMC.Marriott) should beSome(true)
      cut.isPull(DMC.BCOM) should beSome(true)
    }

    "register service to container on init" in {
      val containerMock = mock[PrecacheServiceContainer]
      val loaderMock = mock[DmcMetaDataLoader]

      import scala.concurrent.ExecutionContext.Implicits.global
      DmcMetaDataService.init(containerMock, loaderMock, NoOpReportingService)

      there was one(containerMock).register(any, any)(any)
    }
  }

}
