package com.agoda.papi.pricing.supply.models

import com.agoda.finance.tax.models.SupplierHotelCalculationSettings
import com.agoda.papi.enums.hotel.PaxType
import com.agoda.papi.pricing.metadata._
import com.agoda.papi.pricing.supply.commissions.models.ccas.CommissionAdjustmentResponse
import com.agoda.papi.pricing.supply.models.request.SupplyBaseRequest
import com.agoda.papi.ypl.models.{ProductOfferId, YplRateFence, YplReqOccByHotelAgePolicy}
import com.agoda.papi.ypl.models.Wholesale.WholesaleMetaDataByRateFence
import com.agoda.papi.ypl.models.hotel.AgodaAgencyFeatures
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.protobuf.hotelcontext.HotelContext
import com.agoda.protobuf.masterhotelcontext.MasterHotelContext
import com.agoda.spapi.protobuf.demand.DNRData
import com.agoda.supplier.models.ExperimentAllocation.ExperimentAllocation
import com.agoda.utils.flow.PropertyContext
import org.joda.time.DateTime

case class SupplyHotelInfo(
  hotelId: HotelId,
  cityId: CityId,
  chainId: ChainId = 0,
  dmcMapping: Option[SupplierHotel] = None,
  /** We use it to determine local hotel time for checking promotions / etc */
  gmtOffset: GmtOffset = 0,
  processingFeeOption: Int = 0,
  /**
    * Hotel (1) <-> (*) Push DMC; doesn't contain ycs.
    * It contains only <see>HotelDataServiceImpl#suppliers</see>
    */
  supplierMappings: SupplierHotelMapping = Map.empty,
  /**
    * List of all suppliers that current hotel may have allotment.
    * This list may include YCS, push & pull suppliers.
    */
  suppliers: Seq[SupplierCCMapping] = Seq.empty,
  countryId: CountryId = 0,
  countryCode: String = "TH",
  /* Link to ycs master room */
  enabledRoom: RoomInfoByRoomType = Map.empty,
  isChannelManaged: Boolean = false,
  isMobileEnabledAPS: Boolean = true,
  /* If it's false - we'd like to disable ycs-push rooms */
  isYcsEnabled: Boolean = true,
  /* This is ycs currency only, used for Heisenberg */
  ycsHotelCurrency: Option[String] = None,
  /**
    * Country currency use with isUsingHotelCountryCurrency.
    * If hotel currency is null, use country currency. Used for Affiliate and PricePush
    */
  countryCurrency: Option[String] = None,
  roomBenefits: Map[RoomTypeId, List[BenefitId]] = Map.empty,
  /** Benefits used by suppliers */
  hotelBenefits: HotelBenefits = Map.empty,
  paxSubmit: Option[PaxType] = None,
  agePolicy: AgePolicy = AgePolicy(),
  isMyStaySupported: Boolean = false,
  @deprecated("it is now per supplier, please use the one from [[SupplierCCMapping]] instead")
  bookOnRequest: Boolean = false,
  @deprecated("will eventually move to ypl")
  agencyFeatures: Option[AgodaAgencyFeatures] = None,
  /** Used for Agency flow */
  adjustCommissions: AdjustedMarginPerLanguage = Map.empty,
  isNHAHotel: Boolean = false,
  isSingleRoomNHA: Boolean = false,
  isChampagne: Boolean = false,
  bookNowPayAtCheckIn: Boolean = false,
  ignoreRequestedNumberOfRoomsForNha: Boolean = false,
  applyDiscountsMultiplicatively: Boolean = true,
  /** Used for Pomelo */
  contracts: Seq[ContractData] = Seq.empty,
  ratecategoryWlMapping: Map[RateCategoryId, Set[WhitelabelID]] = Map.empty,
  supplierCommissionMapping: SupplierCommissionMapping = Map.empty,
  breakfastPriceFromHotelSetting: Option[Double] = None,
  ycsAllotmentNotManagedByAgoda: Option[Boolean] = None,
  enableBnplSuppliers: Set[SupplierId] = Set.empty,
  pmcHotelBlock: Boolean = false,
  isEasyCancel: Boolean = false,
  easyCancel: Option[EasyCancel] = None,
  stateId: Option[Int] = None,
  ycsConsideredChildrenAgeTo: Option[Int] = None,
  ycsConsideredChildrenAgeFrom: Option[Int] = None,
  gmtOffsetMinutes: GmtOffset = 0,
  defaultMaxChildrenAllowedInRoom: Option[Int] = None,
  whitelabelContractType: Option[WhitelabelContractType] = None,
  apmCommissionReductionEligibility: Seq[ApmCommissionReductionEligibility] = Seq.empty,
  childAgeRanges: Seq[ChildAgeRange] = Seq.empty,
  agencyNoCreditCardSetting: Seq[AgencyNoCreditCard] = Seq.empty,
  occupancyModel: Option[Int] = None,
  multipleAutoPriceMatch: Seq[MultipleAutoPriceMatch] = Seq.empty,
  maxLengthOfStay: Option[Int] = None,
  gracePeriodEligibleSuppliers: GracePeriodEligibleSuppliers = Set.empty,
  isStackableV2Enabled: Option[Boolean] = None,
  externalChildHotels: Seq[Long] = Seq.empty,
  apmLeadingRoomAdjustmentIds: Seq[Int] = Seq.empty,
  demandBlackListedSuppliers: Option[DemandBlackListedSuppliers] = None,
  apmDeltaPercentage: Seq[ApmDeltaPercentage] = Seq.empty,
  masterDmcId: Option[Int] = None,
  jasoRateCategoryLanguage: Map[RateCategoryId, Set[LanguageId]] = Map.empty,
  /* stats for DFEnhancedSearchMessage */
  sdaSuppliersRemoved: Int = 0,
  offerReductionRoomTypeIds: Map[Int, Seq[RoomTypeId]] = Map.empty,
  apmConfigs: Map[ApmConfigTypeId, Seq[ApmConfig]] = Map.empty,
  wholesaleMetaDataByRateFence: WholesaleMetaDataByRateFence = Map.empty,
  supplierHotelCalculationSettings: SupplierHotelCalculationSettings =
    SupplierHotelCalculationSettings(settings = Map.empty),
  restrictedRatecategoryIds: Set[Int] = Set.empty,
  growthPrograms: Map[YplRateFence, Seq[GrowthProgram]] = Map.empty[YplRateFence, Seq[GrowthProgram]],
  experimentAllocations: Map[ExperimentId, ExperimentAllocation] = Map.empty,
  suppliersEnabledForExperiment: Map[ExperimentId, Set[SupplierId]] = Map.empty,
  feeWaivers: Seq[Waiver] = Seq.empty,
  supplierDNRMapping: Map[SupplierId, DNRData] = Map.empty,
  hmcMasterHotel: Option[MasterHotelContext] = None,
  hmcSupplierHotelMappings: Map[SupplierId, HotelContext] = Map.empty,
  supplierPriceShopStatusMapping: Seq[SupplierPriceShopStatusMapping] = Nil,
  hotelYcsFlag: Boolean = false,
  hotelLiveDate: Option[DateTime] = None,
  ccasPrograms: Option[CommissionAdjustmentResponse] = None,
  uspaPrograms: Seq[UspaProgram] = Seq.empty,
  hotelBenefitFromFacility: Seq[BenefitId] = Seq.empty,
  roomBenefitFromFacility: Map[RoomTypeId, List[BenefitId]] = Map.empty,
  nonYcsHotelBenefitFromFacility: Seq[BenefitFromFacility] = Seq.empty,
  nonYcsRoomBenefitFromFacility: Seq[RoomBenefitFromFacility] = Seq.empty,
  pulseCampaignIdBlacklist: Seq[Int] = Seq.empty,
  accommodationTypes: Seq[Int] = List.empty,
  addFeesInExclusivePrice: Boolean = false,
) extends WithMeta.HotelMetaData
    with PropertyContext {
  private lazy val suppliersSet: Set[SupplierId] = suppliers.map(_.supplierId)(collection.breakOut)
  lazy val dmcId: Int = dmcMapping.map(_.dmcId).getOrElse(DMC.YCS)

  private def hotelCountryCurrencyOpt: Option[Currency] = ycsHotelCurrency orElse countryCurrency

  //  If request currency is empty and isUsingHotelCurrency flag = true, we will use hotel currency.
  def getRequestedCurrency(request: SupplyBaseRequest): Option[Currency] = {
    val currency =
      if (request.flagInfo.isUsingHotelCurrency) hotelCountryCurrencyOpt
      else None
    Option(request.currency).filter(_.nonEmpty) orElse currency
  }

  def isAgency(supplierId: SupplierId): Option[Boolean] = suppliers.find(_.supplierId == supplierId).map(_.isAgency)
  def isBmp(supplierId: SupplierId): Option[Boolean] = suppliers.find(_.supplierId == supplierId).map(_.isBmp)

  override def getSuppliers: Set[SupplierId] = suppliersSet

  /**
    * @return hotel id
    */
  override def getHotelId: HotelId = hotelId

  override def id: Long = hotelId

  private def getRoomInfo(roomTypeId: RoomTypeId): Option[MasterRoomInfo] = enabledRoom.get(roomTypeId)

  /**
    * Get master room type id by current room ID
    */
  def getMasterRoomId(roomTypeId: RoomTypeId): RoomTypeId =
    getRoomInfo(roomTypeId).map(_.masterRoomId).getOrElse(roomTypeId)

  def getRoomProductOfferId(roomTypeId: RoomTypeId): ProductOfferId =
    getRoomInfo(roomTypeId).map(_.productOfferId).filter(_ >= 0)

  def getOccupancy(implicit request: SupplyBaseRequest) =
    YplReqOccByHotelAgePolicy(request.occ, agePolicy.toYplAgePolicy, ignoreRequestedNumberOfRoomsForNha)
}
