package com.agoda.papi.pricing.supply.models.uspa

import com.agoda.adp.messaging.scala.message.Message
import com.agoda.papi.ypl.models.{RoomIdentifiersProtoHelper, YPLRoom}
import com.agoda.papi.ypl.models.pricing.PriceBreakdown

case class ComparisonCriteria(masterRoomTypeId: Long,
                              occupancy: Option[Int],
                              cxlCode: Option[String],
                              breakfast: Option[String],
                              benefitBuckets: Option[BucketMatchCriteria] = None)
case class BucketMatchCriteria(dinnerBenefits: Option[String] = None,
                               transportationBenefits: Option[String] = None,
                               lunchBenefits: Option[String] = None,
                               checkoutBenefits: Option[String] = None,
                               activitiesBenefits: Option[String] = None,
                               vipBenefits: Option[String] = None,
                               essentialBenefits: Option[String] = None,
                               miniBarBenefits: Option[String] = None,
                               nonEssentialBenefits: Option[String] = None)

object ComparisonCriteria {
  val Default: ComparisonCriteria = ComparisonCriteria(
    masterRoomTypeId = 0,
    occupancy = None,
    cxlCode = None,
    breakfast = None,
    benefitBuckets = None,
  )
}

case class ComparisonCriteriaMessage(masterRoomTypeId: Long,
                                     occupancy: Option[Int],
                                     cxlCode: Option[String],
                                     breakfast: Option[String],
                                     bucketsCriteria: Option[BucketCriteriaMessage],
                                     isCvcOffer: Option[Boolean])
case class BucketCriteriaMessage(dinnerBenefits: Option[String],
                                 transportationBenefits: Option[String],
                                 lunchBenefits: Option[String],
                                 checkoutBenefits: Option[String],
                                 activitiesBenefits: Option[String],
                                 vipBenefits: Option[String],
                                 essentialBenefits: Option[String],
                                 miniBarBenefits: Option[String],
                                 nonEssentialBenefits: Option[String])

object ComparisonCriteriaMessage {
  def compose(criteria: ComparisonCriteria, isCvcOffer: Boolean): ComparisonCriteriaMessage = ComparisonCriteriaMessage(
    masterRoomTypeId = criteria.masterRoomTypeId,
    occupancy = criteria.occupancy,
    cxlCode = criteria.cxlCode,
    breakfast = criteria.breakfast,
    bucketsCriteria = criteria.benefitBuckets.flatMap { buckets =>
      if (buckets == BucketMatchCriteria()) {
        None
      } else Some(
        BucketCriteriaMessage(
          dinnerBenefits = criteria.benefitBuckets.flatMap(_.dinnerBenefits),
          transportationBenefits = criteria.benefitBuckets.flatMap(_.transportationBenefits),
          lunchBenefits = criteria.benefitBuckets.flatMap(_.lunchBenefits),
          checkoutBenefits = criteria.benefitBuckets.flatMap(_.checkoutBenefits),
          activitiesBenefits = criteria.benefitBuckets.flatMap(_.activitiesBenefits),
          vipBenefits = criteria.benefitBuckets.flatMap(_.vipBenefits),
          essentialBenefits = criteria.benefitBuckets.flatMap(_.essentialBenefits),
          miniBarBenefits = criteria.benefitBuckets.flatMap(_.miniBarBenefits),
          nonEssentialBenefits = criteria.benefitBuckets.flatMap(_.nonEssentialBenefits),
        ),
      )
    },
    isCvcOffer = Some(isCvcOffer),
  )
}

case class UspaMessageRoom(
  uid: String,
  roomIdentifier: String,
  masterRoomTypeId: Option[Long],
  roomTypeId: Long,
  supplierId: Int,
  baseChannelId: Int,
  allChannelIds: String,
  ratePlanId: Int,
  ratePlanCode: String = "",
  priceBreakdown: Seq[PriceBreakdown],
  cxlCode: String,
)

object UspaMessageRoom {
  def compose(yplRoom: YPLRoom): UspaMessageRoom = UspaMessageRoom(
    uid = yplRoom.uid,
    roomIdentifier = RoomIdentifiersProtoHelper.writeTo(yplRoom.toRoomIdentifiers()),
    masterRoomTypeId = yplRoom.masterRoomId,
    roomTypeId = yplRoom.roomTypeId,
    supplierId = yplRoom.supplierId,
    baseChannelId = yplRoom.channel.baseChannelId,
    allChannelIds = yplRoom.channel.toChannelIds.mkString(","),
    ratePlanId = yplRoom.rateCategory.id,
    ratePlanCode = yplRoom.rateCategory.code.getOrElse(""),
    priceBreakdown = yplRoom.prices.flatMap(_.getUpdatedPriceBreakdown().breakdown),
    cxlCode = yplRoom.cxlCode,
  )
}

case class UspaMessageV2(
  isUSPABookingRequest: Boolean,
  searchId: String,
  campaignId: Long,
  programId: Int,
  rateType: String,
  beatRatePercent: Double,
  maxDiscountPercent: Double,
  criteria: ComparisonCriteriaMessage,
  hotelId: Long,
  checkIn: String,
  los: Int,
  currency: String,
  cheapestOtherRoom: UspaMessageRoom,
  uspaRoom: UspaMessageRoom,
  uspaRefRoom: Option[UspaMessageRoom],
  minOtherRoomPriceUSDPerBook: Double,
  uspaPriceUSD: Double,
  uspaRefPriceUSD: Option[Double],
  beatPriceUSD: Double,
  discountAmountUSD: Double,
  discountAmountPerRoomPerDateUSD: Double,
  discountPercent: Double,
  netExUSD: Double,
  netInUSD: Double,
  sellExUSD: Double,
  sellInUSD: Double,
  benchmarkNetExUSD: Double,
  benchmarkNetInUSD: Double,
  benchmarkSellExUSD: Double,
  benchmarkSellInUSD: Double,
) extends Message[UspaMessageV2]
