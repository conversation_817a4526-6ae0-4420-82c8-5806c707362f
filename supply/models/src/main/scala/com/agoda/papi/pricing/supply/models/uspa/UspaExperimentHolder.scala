package com.agoda.papi.pricing.supply.models.uspa

case class UspaExperimentHolder(
  isFixSetNetRate: <PERSON>olean,
  isFixIsAllOcc: <PERSON>olean,
  isUseSeqUspaCampaign: Boolean,
  isEnableCheapestOfferOnlyAndRateFallBack: <PERSON>olean,
  isUspaPerformanceOptimizations: <PERSON>olean,
  isEnablePreBeatComparison: <PERSON>olean,
  isRefactorPriceCalculationFunction: <PERSON>olean,
  isEnableUspaFlowOptimize: <PERSON>olean,
  isEnableEnhanceBookingBreakdown: Boolean,
  isEnableUnmappedAdjustmentMaxCap: Boolean,
  isEnableCheapestVsCheapestOffer: Boolean,
  isEnableMultiRoomPriceAdjustmentFix: Boolean,
  isEnableUspaInfo: Boolean,
)
