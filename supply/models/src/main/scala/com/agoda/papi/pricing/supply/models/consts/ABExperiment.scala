package com.agoda.papi.pricing.supply.models.consts

// Whenever you add or remove experiment here, do the same in ABTest in cucumber project
trait ABExperiment {
  // NHA
  val HIDE_BOR_FROM_SSR_FOR_CI_LESSTHEN_TWO = "NHAFE-29"
  val HIDE_BOR_NEGATIVE_TEST = "NHAFE-1966"
  val HOLDING_OUT_FOR_NEW_PROPERTIES = "NHACON-1733"

  // AFFILIATE GENERATE SELL DISCOUNT OFFERS
  val AFFILIATE_GENERATE_SELL_DISCOUNT_OFFERS = "PAPI-22933"

  // Direct connect supply for JTBWL
  val ENABLE_SELLING_DIRECT_CONNECT_SUPPLY_FOR_JTB = "JTBFP-615-V2"

  // PricePush optimization
  val PRICE_PUSH_AGX_FENCING = "SCAL-1097"
  val PRICE_PUSH_FENCING = "SCAL-1042"

  // Partner supplier filter
  val ENABLE_AFFILIATE_SUPPLIER_FILTER_FOR_HSB_MIGRATED_PARTNERS = "PAPI-20396"

  // Partner supplier filter empty fix
  val ENABlE_FIX_FOR_EMPTY_SUPPLIER_FILTER = "PAPI-21917"

  // Legal Tech
  val ENABLE_REGULATORY_BLOCKING_FILTER = "LT-894"
  val DSA_LICENSE_BLOCKING_DISABLED = "LT-1208"

  val DYNAMIC_ROOM_MAPPING_TYPE_6 = "MNM-675"
  val DYNAMIC_ROOM_MAPPING_TYPE_7 = "MNM-696"
  val DYNAMIC_ROOM_MAPPING_TYPE_8 = "MNM-710"
  val DYNAMIC_ROOM_MAPPING_TYPE_9 = "MNM-717"
  val DYNAMIC_ROOM_MAPPING_TYPE_10 = "MNM-718"
  val DYNAMIC_ROOM_MAPPING_TYPE_11 = "MNM-719"

  val ENABLE_LEAD_TIME_CONSTRAINT_FOR_EXT_SUPPLY = "JTBFP-1662"
}

object ABTest extends ABExperiment
