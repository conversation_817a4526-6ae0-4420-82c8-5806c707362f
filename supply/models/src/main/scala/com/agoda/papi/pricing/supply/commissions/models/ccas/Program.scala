package com.agoda.papi.pricing.supply.commissions.models.ccas

import com.agoda.chainwholesale.ccas.protobuf.Ccas
import scala.collection.JavaConverters._

case class Program(programName: String,
                   adjustment: Adjustment,
                   duplicateRoomAndDiscount: Boolean,
                   priceAdjustmentId: Option[Long],
                   exclusionChannelIds: Seq[Int] = Seq.empty)

object Program {
  def fromCcas(program: Ccas.Program): Program = Program(
    program.getProgramName,
    Adjustment.fromCcas(program.getAdjustment),
    program.getDuplicateRoomAndDiscount,
    if (program.hasPriceAdjustmentIdOpt) Some(program.getPriceAdjustmentIdOpt.getValue) else None,
    fromExclusionList(program),
  )

  private def fromExclusionList(program: Ccas.Program): Seq[Int] = Option(program.getExclusionRulesList)
    .map(_.asScala)
    .getOrElse(List.empty)
    .flatMap { exclusionRule =>
      Option(exclusionRule.getChannelIdList).map(_.asScala.map(_.toInt)).getOrElse(List.empty)
    }
    .distinct
}
