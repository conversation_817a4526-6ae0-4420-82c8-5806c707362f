package models.uspa

import com.agoda.adp.messaging.schema.CompatibilityResult
import com.agoda.papi.enums.room.Benefits.{
  AirportTransferOneWay,
  AirportTransferOneWayRound,
  ComplimentaryMiniBar,
  FoodAndDrinkDiscount,
  LateCheckout,
  LateCheckout_02,
  LunchForAmount,
  Massage,
  MiniBar,
  ThemeParkTickets,
  VipOnlyEarlyCheckIn,
  VipOnlyFreeDinner,
  WaterActivities,
  WelcomeDrink,
}
import com.agoda.papi.enums.room.RateType
import com.agoda.papi.pricing.supply.models.uspa.{
  BucketCriteriaMessage,
  BucketMatchCriteria,
  ComparisonCriteria,
  ComparisonCriteriaMessage,
  UspaMessageRoom,
  UspaMessageV2,
}
import com.agoda.papi.ypl.models.GUIDGeneratorSpec.{aValidPriceWithPriceBreakdown, aValidRoom}
import com.agoda.papi.ypl.models.consts.Benefits
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit

class UspaMessageSpec extends SpecificationWithJUnit {

  val now: DateTime = DateTime.now()
  val comparisonCriteria: ComparisonCriteria = ComparisonCriteria(
    masterRoomTypeId = 200L,
    occupancy = Some(2),
    cxlCode = Some("CXL123"),
    breakfast = Some("true"),
    benefitBuckets = Some(
      BucketMatchCriteria(
        dinnerBenefits = Some(s"${Benefits.Dinner},${Benefits.DinnerInPrivateRoom}"),
        transportationBenefits = Some(s"${AirportTransferOneWay.i},${AirportTransferOneWayRound.i}"),
        lunchBenefits = Some(s"${Benefits.Lunch},${LunchForAmount.i}"),
        checkoutBenefits = Some(s"${LateCheckout.i},${LateCheckout_02.i}"),
        activitiesBenefits = Some(s"${ThemeParkTickets.i},${WaterActivities.i}"),
        vipBenefits = Some(s"${VipOnlyEarlyCheckIn.i},${VipOnlyFreeDinner.i}"),
        essentialBenefits = Some(s"${FoodAndDrinkDiscount.i}"),
        miniBarBenefits = Some(s"${ComplimentaryMiniBar.i},${MiniBar.i}"),
        nonEssentialBenefits = Some(s"${Massage.i},${WelcomeDrink.i}"),
      )),
  )
  val uspaMessageRoom: UspaMessageRoom =
    UspaMessageRoom.compose(aValidRoom.copy(prices = List(aValidPriceWithPriceBreakdown)))
  val cheapestOtherRoom: UspaMessageRoom =
    UspaMessageRoom.compose(aValidRoom.copy(prices = List(aValidPriceWithPriceBreakdown)))
  val comparisonCriteriaMessage: ComparisonCriteriaMessage =
    ComparisonCriteriaMessage.compose(comparisonCriteria, isCvcOffer = true)

  "UspaMessage" should {
    "compose message correctly" in {
      val uspaMessage = UspaMessageV2(
        isUSPABookingRequest = true,
        searchId = "search123",
        campaignId = 123L,
        programId = 1,
        rateType = RateType.NetExclusive.entryName,
        beatRatePercent = 10.0,
        maxDiscountPercent = 20.0,
        criteria = comparisonCriteriaMessage,
        hotelId = 123L,
        checkIn = now.toString,
        los = 2,
        currency = "USD",
        cheapestOtherRoom = cheapestOtherRoom,
        uspaRoom = uspaMessageRoom,
        uspaRefRoom = None,
        minOtherRoomPriceUSDPerBook = 80.0,
        uspaPriceUSD = 90.0,
        uspaRefPriceUSD = None,
        beatPriceUSD = 80L,
        discountAmountUSD = 10.0,
        discountAmountPerRoomPerDateUSD = 5.0,
        discountPercent = 10.0,
        netExUSD = 100.0,
        netInUSD = 110.0,
        sellExUSD = 120.0,
        sellInUSD = 130.0,
        benchmarkNetExUSD = 200.0,
        benchmarkNetInUSD = 210.0,
        benchmarkSellExUSD = 220.0,
        benchmarkSellInUSD = 230.0,
      )

      uspaMessage.isUSPABookingRequest should_== true
      uspaMessage.searchId should_== "search123"
      uspaMessage.campaignId should_== 123L
      uspaMessage.programId should_== 1
      uspaMessage.rateType should_== RateType.NetExclusive.entryName
      uspaMessage.beatRatePercent should_== 10.0
      uspaMessage.maxDiscountPercent should_== 20.0
      uspaMessage.criteria should_== comparisonCriteriaMessage
      uspaMessage.hotelId should_== 123L
      uspaMessage.checkIn should_== now.toString
      uspaMessage.los should_== 2
      uspaMessage.currency should_== "USD"
      uspaMessage.cheapestOtherRoom should_== cheapestOtherRoom
      uspaMessage.uspaRoom should_== uspaMessageRoom
      uspaMessage.minOtherRoomPriceUSDPerBook should_== 80.0
      uspaMessage.uspaPriceUSD should_== 90.0
      uspaMessage.beatPriceUSD should_== 80L
      uspaMessage.discountAmountUSD should_== 10.0
      uspaMessage.discountAmountPerRoomPerDateUSD should_== 5.0
      uspaMessage.discountPercent should_== 10.0
      uspaMessage.cheapestOtherRoom.priceBreakdown.map(_.stepDesc) should_== List("BaseStep", "PriceBreakdown")
      uspaMessage.uspaRoom.priceBreakdown.map(_.stepDesc) should_== List("BaseStep", "PriceBreakdown")
      uspaMessage.netExUSD should_== 100.0
      uspaMessage.netInUSD should_== 110.0
      uspaMessage.sellExUSD should_== 120.0
      uspaMessage.sellInUSD should_== 130.0
      uspaMessage.benchmarkNetExUSD should_== 200.0
      uspaMessage.benchmarkNetInUSD should_== 210.0
      uspaMessage.benchmarkSellExUSD should_== 220.0
      uspaMessage.benchmarkSellInUSD should_== 230.0

      uspaMessage.checkCompatibility.getCode should_!== CompatibilityResult.COMPATIBILITY_NO
    }
    "compose message the message for every field" in {
      val message = ComparisonCriteriaMessage.compose(comparisonCriteria, isCvcOffer = true)
      message should_== ComparisonCriteriaMessage(
        masterRoomTypeId = 200L,
        occupancy = Some(2),
        cxlCode = Some("CXL123"),
        breakfast = Some("true"),
        bucketsCriteria = Some(
          BucketCriteriaMessage(
            dinnerBenefits = Some(s"${Benefits.Dinner},${Benefits.DinnerInPrivateRoom}"),
            transportationBenefits = Some(s"${AirportTransferOneWay.i},${AirportTransferOneWayRound.i}"),
            lunchBenefits = Some(s"${Benefits.Lunch},${LunchForAmount.i}"),
            checkoutBenefits = Some(s"${LateCheckout.i},${LateCheckout_02.i}"),
            activitiesBenefits = Some(s"${ThemeParkTickets.i},${WaterActivities.i}"),
            vipBenefits = Some(s"${VipOnlyEarlyCheckIn.i},${VipOnlyFreeDinner.i}"),
            essentialBenefits = Some(s"${FoodAndDrinkDiscount.i}"),
            miniBarBenefits = Some(s"${ComplimentaryMiniBar.i},${MiniBar.i}"),
            nonEssentialBenefits = Some(s"${Massage.i},${WelcomeDrink.i}"),
          )),
        isCvcOffer = Some(true),
      )
    }
    "compose message with empty bucketsCriteria when no benefits are provided" in {
      val emptyComparisonCriteria = ComparisonCriteria(
        masterRoomTypeId = 200L,
        occupancy = Some(2),
        cxlCode = Some("CXL123"),
        breakfast = Some("true"),
        benefitBuckets = Some(BucketMatchCriteria()),
      )
      val message = ComparisonCriteriaMessage.compose(emptyComparisonCriteria, isCvcOffer = false)
      message should_== ComparisonCriteriaMessage(
        masterRoomTypeId = 200L,
        occupancy = Some(2),
        cxlCode = Some("CXL123"),
        breakfast = Some("true"),
        bucketsCriteria = None,
        isCvcOffer = Some(false),
      )
    }
  }
}
