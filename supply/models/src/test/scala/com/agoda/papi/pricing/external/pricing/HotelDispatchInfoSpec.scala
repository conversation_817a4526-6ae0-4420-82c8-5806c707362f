package com.agoda.papi.pricing.external.pricing

import com.agoda.papi.enums.hotel.RateAvailabilityTypes
import com.agoda.papi.pricing.supply.models.request.{FencedOriginObject, FencedRatePair}
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{YplMasterChannel, YplRateFence}
import external.pricing.{DispatchInfo, HotelDispatchInfo}
import org.specs2.mutable.SpecificationWithJUnit

class HotelDispatchInfoSpec extends SpecificationWithJUnit {

  "HotelDispatchInfo" should {

    val aValidFencedRatePair = FencedRatePair(
      YplRateFence("", -1, -1),
      FencedOriginObject(Set()),
    )

    val aValidDispatchInfo = DispatchInfo(
      hotelId = 1,
      dispatchDmc = DMC.YCS,
      dispatchedMasterChannel = YplMasterChannel.RTL,
      dispatchType = 0,
      fencedRatePair = aValidFencedRatePair,
    )

    "hasRateChannelSwapRooms correctly" in {
      HotelDispatchInfo(1, Seq.empty).hasRateChannelSwapRooms shouldEqual false
      HotelDispatchInfo(1, Seq(aValidDispatchInfo)).hasRateChannelSwapRooms shouldEqual false
      HotelDispatchInfo(1,
                        Seq(
                          aValidDispatchInfo.copy(rateAvailabilityType =
                            List(RateAvailabilityTypes.RateChannel)))).hasRateChannelSwapRooms shouldEqual true
    }

    "isExpFixMarginPercentageToSoybean correctly" in {
      HotelDispatchInfo(1, Seq.empty).isExpFixMarginPercentageToSoybean shouldEqual false

      HotelDispatchInfo(1,
                        Seq(
                          aValidDispatchInfo.copy(
                            dispatchDmc = DMC.BCOM,
                            dispatchedMasterChannel = YplMasterChannel(Channel.FixMarginPercentageToSoybeanMock),
                          ))).isExpFixMarginPercentageToSoybean shouldEqual false

      HotelDispatchInfo(1,
                        Seq(
                          aValidDispatchInfo.copy(
                            dispatchDmc = DMC.YCS,
                            dispatchedMasterChannel = YplMasterChannel.RTL,
                          ))).isExpFixMarginPercentageToSoybean shouldEqual false

      HotelDispatchInfo(1,
                        Seq(
                          aValidDispatchInfo.copy(
                            dispatchDmc = DMC.YCS,
                            dispatchedMasterChannel = YplMasterChannel(Channel.FixMarginPercentageToSoybeanMock),
                          ))).isExpFixMarginPercentageToSoybean shouldEqual true

    }

  }
}
