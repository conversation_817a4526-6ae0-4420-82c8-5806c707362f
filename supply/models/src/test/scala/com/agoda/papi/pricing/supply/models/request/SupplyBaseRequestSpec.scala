package com.agoda.papi.pricing.supply.models.request

import com.agoda.commons.models.rpc.request.RequestMeta
import com.agoda.papi.enums.request.FeatureFlag
import com.agoda.papi.enums.room.RatePlanStatus
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import com.agoda.papi.ypl.models.{YplChannel, YplMasterChannel}
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit

class SupplyBaseRequestSpec extends SpecificationWithJUnit with SupplyModelTestDataBuilders {

  "SupplyBaseRequest" should {

    "def isManual correctly" in {
      val today = DateTime.parse("2024-02-29T09:00:00.000+07:00")
      val tmr = today.plusDays(1)
      val nextMonth = today.plusMonths(1)
      val nextYear = today.plusYears(1)
      aValidSupplyBaseRequest.withCheckIn(today).withCheckOut(tmr).build.isManual(today) shouldEqual false
      "BaseRequestWithManual.isManual() correctly for los" in {
        aValidSupplyBaseRequest.withCheckIn(today).withCheckOut(nextMonth).build.isManual(today) shouldEqual true
      }
      "BaseRequestWithManual.isManual() correctly for check in" in {
        aValidSupplyBaseRequest.withCheckIn(nextYear).withCheckOut(nextYear).build.isManual(today) shouldEqual true
      }
      "BaseRequestWithManual.isManual() return false when los is 14" in {
        aValidSupplyBaseRequest
          .withCheckIn(today)
          .withCheckOut(today.plusDays(14))
          .build
          .isManual(today) shouldEqual false
      }
    }

    "attempt" in {
      "return 1 if requestMeta is empty" in {
        aValidSupplyBaseRequest.withRequestMeta(None).attempt shouldEqual (1)
      }

      "return attempt from requestMeta if non Empty" in {
        aValidSupplyBaseRequest.withRequestMeta(Some(RequestMeta("some-ip", attempt = 2))).attempt shouldEqual (2)
      }
    }

    "computeRatePlanInfos" should {
      "return helper channel for additionalRateChannels" in {
        val channels = Set(YplMasterChannel(8), YplMasterChannel(14))
        val requestedChannels = Set(YplMasterChannel(1))
        val request = aValidSupplyBaseRequest.withAdditionalChannels(channels).build
        val computeRatePlanInfos = request.computeRatePlanInfos(requestedChannels)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 8).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 14).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 1).map(_.state) should_== Some(
          RatePlanStatus.Requested)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 2).isEmpty shouldEqual (true)
      }

      "retail will be helper channel if not requested" in {
        val channels = Set(YplMasterChannel(8), YplMasterChannel(14))
        val requestedChannels = Set.empty[YplChannel]
        val request = aValidSupplyBaseRequest.withAdditionalChannels(channels).build
        val computeRatePlanInfos = request.computeRatePlanInfos(requestedChannels)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 8).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 14).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 1).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 2).isEmpty shouldEqual (true)
      }

      "return APS Peek if featureFlag is true and APS is not requested" in {
        val channels = Set(YplMasterChannel(8), YplMasterChannel(14))
        val requestedChannels = Set.empty[YplChannel]
        val request =
          aValidSupplyBaseRequest.withAdditionalChannels(channels).withFeatureFlags(List(FeatureFlag.APSPeek)).build
        val computeRatePlanInfos = request.computeRatePlanInfos(requestedChannels)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 8).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 14).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 1).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 2).map(_.state) should_== Some(RatePlanStatus.Helper)
      }

      "return APS Peek as requested if featureFlag is true and APS is requested" in {
        val channels = Set(YplMasterChannel(8), YplMasterChannel(14))
        val requestedChannels = Set(YplMasterChannel(2))
        val request =
          aValidSupplyBaseRequest.withAdditionalChannels(channels).withFeatureFlags(List(FeatureFlag.APSPeek)).build
        val computeRatePlanInfos = request.computeRatePlanInfos(requestedChannels)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 8).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 14).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 1).map(_.state) should_== Some(RatePlanStatus.Helper)
        computeRatePlanInfos.find(x => x.channel.baseChannelId == 2).map(_.state) should_== Some(
          RatePlanStatus.Requested)
      }

    }
  }

}
