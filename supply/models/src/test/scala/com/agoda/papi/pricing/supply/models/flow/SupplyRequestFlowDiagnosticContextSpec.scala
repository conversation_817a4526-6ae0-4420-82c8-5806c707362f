package com.agoda.papi.pricing.supply.models.flow

import com.agoda.papi.enums.hotel.{ChildrenStayFreeType, PaxType, SupplierType}
import com.agoda.papi.pricing.metadata.{HotelData, SupplierCCMapping, SupplierHotel}
import com.agoda.papi.pricing.supply.models.flow.RequestFlowDiagnosticContextData.RequestFlowDiagnosticKey
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders.{
  aValidSupplyBaseRequest,
  aValidSupplyContext,
  aValidSupplyHotelInfo,
  toHotelInfoBuilder,
}
import com.agoda.papi.pricing.supply.models.{
  AgePolicy,
  HotelId,
  MetaUnits,
  SupplierDispatchChannelHolder,
  SupplierId,
  WithMeta,
}
import com.agoda.papi.ypl.models.{SupplierInfo, YPLHotel, YPLRoom, YPLTestDataBuilders}
import models.diagnostics.RequestFlowState.{BlacklistCheckPassed, HotelAvailable, HotelMetaFetchedFromDb, HotelReady}
import models.diagnostics.{FlowState, RequestFlowState}
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope

import scala.jdk.CollectionConverters.mapAsScalaConcurrentMapConverter
import scala.util.Random

class SupplyRequestFlowDiagnosticContextSpec extends SpecificationWithJUnit with YPLTestDataBuilders {

  "RequestFlowDiagnosticContext" should {
    "process isBlocked correctly" in {
      "requestFlowDiagnosticsMap.isEmpty" in {
        val ctx = aValidSupplyContext.withBaseRequest(aValidSupplyBaseRequest.build)
        ctx.isBlocked should_== (true)
      }

      "requestFlowDiagnosticsMap contains RequestFlowState.HotelIsBlocked" in {
        val ctx = aValidSupplyContext.withBaseRequest(aValidSupplyBaseRequest.build)
        ctx.requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.put((1, 12345),
                                                                           List(RequestFlowState.HotelIsBlocked))
        ctx.isBlocked should_== (true)
      }

      "requestFlowDiagnosticsMap contains RequestFlowState.SupplierIsBlocked" in {
        val ctx = aValidSupplyContext.withBaseRequest(aValidSupplyBaseRequest.build)
        ctx.requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.put((1, 12345),
                                                                           List(RequestFlowState.SupplierIsBlocked))
        ctx.isBlocked should_== (true)
      }

      "requestFlowDiagnosticsMap !contains RequestFlowState HotelIsBlocked or RequestFlowState.SupplierIsBlocked" in {
        val ctx = aValidSupplyContext.withBaseRequest(aValidSupplyBaseRequest.build)
        ctx.requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.put((1, 12345),
                                                                           List(RequestFlowState.HotelMetaFetchedFromDb))
        ctx.isBlocked should_== (false)
      }
    }
  }

  s"recording $BlacklistCheckPassed after heisenberg check and ${HotelMetaFetchedFromDb}" should {
    "no updates recorded on empty list" in new RequestFlowDiagnosticContextScope {
      val expectedRateplans = List.empty
      val rateplans = updateRequestFlowStatusAfterInquiringHeisenberg(expectedRateplans)
      requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.asScala must beEmpty
    }

    "for 1 hotel with 1 dmc, record state to existing flow states" in new RequestFlowDiagnosticContextScope {
      executeTestForBlacklistCheck(List(enabledSupplier))
    }

    "for 1 hotel with mulitple dmcs, record state to existing flow states" in new RequestFlowDiagnosticContextScope {
      executeTestForBlacklistCheck(generateSomeRandomSuppliers)
    }
  }

  s"recording $HotelReady after reading rates from pull side and ${HotelMetaFetchedFromDb} -> ${BlacklistCheckPassed}" should {
    "no updates recorded on empty list" in new RequestFlowDiagnosticContextScope {
      val expectedHotelAvailabilitiesAndReadiness = List.empty
      updateRequestFlowStatusAfterCheckHotelReadinessAndAvailability(expectedHotelAvailabilitiesAndReadiness)
      requestFlowDiagnosticsMapAsScala must beEmpty
    }

    "for 1 hotel with 1 dmc of pull, record state to existing flow states" in new RequestFlowDiagnosticContextScope {
      executeTestForReadyStateFromPull(List(enabledSupplier))
    }

    "for 1 hotel with mulitple dmcs of pull, record state to existing flow states" in new RequestFlowDiagnosticContextScope {
      executeTestForReadyStateFromPull(enabledSupplier :: generateSomeRandomSuppliers)
    }
  }

  s"recording $HotelReady after reading rates from push side and ${HotelMetaFetchedFromDb} -> ${BlacklistCheckPassed}" should {
    "no updates recorded on empty list" in new RequestFlowDiagnosticContextScope {
      val expectedHotelAvailabilitiesAndReadiness = List.empty
      updateRequestFlowStatusAfterCheckHotelReadinessAndAvailability(expectedHotelAvailabilitiesAndReadiness)
      requestFlowDiagnosticsMapAsScala must beEmpty
    }

    "for 1 hotel with 1 dmc of push, record state to existing flow states" in new RequestFlowDiagnosticContextScope {
      executeTestForReadyStateFromPush(List(enabledSupplier))
    }

    "for 1 hotel with mulitple dmcs of push, record state to existing flow states" in new RequestFlowDiagnosticContextScope {
      executeTestForReadyStateFromPush(enabledSupplier :: generateSomeRandomSuppliers)
    }

    s"tag $HotelReady state for specified supplier and hotel" in new RequestFlowDiagnosticContextScope {
      requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.size must beEqualTo(0)
      val diagnosticKey = enabledSupplier.asInstanceOf[SupplierId] -> 77381.asInstanceOf[HotelId]
      tagReadySupplierForThisHotel(diagnosticKey)
      requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.get(diagnosticKey) must beEqualTo(List(HotelReady))
    }
  }

  s"recording $HotelAvailable after reading rates and $HotelReady -> ${HotelMetaFetchedFromDb} -> ${BlacklistCheckPassed}" should {
    "no updates recorded on empty list" in new RequestFlowDiagnosticContextScope {
      val expectedHotelAvailabilitiesAndReadiness = List.empty
      updateRequestFlowStatusAfterCheckHotelReadinessAndAvailability(expectedHotelAvailabilitiesAndReadiness)
      requestFlowDiagnosticsMapAsScala must beEmpty
    }

    "for 1 hotel with 1 dmc, record state to existing flow states" in new RequestFlowDiagnosticContextScope {
      executeTestForAvailableState(List(enabledSupplier))
    }

    "for 1 hotel with mulitple dmcs, record state to existing flow states" in new RequestFlowDiagnosticContextScope {
      executeTestForAvailableState(enabledSupplier :: generateSomeRandomSuppliers)
    }
  }

  trait RequestFlowDiagnosticContextScope extends Scope with SupplyRequestFlowDiagnosticContext {
    override val requestFlowDiagnosticContextData: RequestFlowDiagnosticContextData =
      new RequestFlowDiagnosticContextData()
    val now: DateTime = DateTime.now()
    val requestFlowDiagnosticsMapAsScala = requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.asScala

    def assertRequestFlowStateUpatesFor(suppliers: List[Int],
                                        keyValuePairs: List[(RequestFlowDiagnosticKey, List[FlowState])]) = {
      requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.asScala must haveSize(suppliers.length)
      requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.asScala must havePairs(keyValuePairs: _*)
    }

    def enabledSupplier = 3038

    def generateSomeRandomSuppliers = (1 to Random.nextInt(3) + 1).map(index => index).toList

    def executeTestForBlacklistCheck(suppliers: List[Int]) = {
      val expectedSuppliers = suppliers
      val expectedHotel = buildHotel(9, expectedSuppliers)

      val initFlowStates = List(HotelMetaFetchedFromDb)
      val flowStateUnderTest = List(BlacklistCheckPassed)

      val expectedKeyValuePairs =
        generateExpectedKeyValuePairs(expectedSuppliers, expectedHotel.hotelId, initFlowStates, flowStateUnderTest)

      val expectedHotelInfo = toHotelInfo(expectedHotel)
      val expectedRatePlan: MetaUnits = Seq(
        new WithMeta({},
                     expectedHotelInfo,
                     supplierDispatchChannelHolder =
                       SupplierDispatchChannelHolder(List.empty, None, expectedHotelInfo.getSuppliers, Map.empty)))

      prerecordedFlowStates(expectedKeyValuePairs, flowStateUnderTest)
      updateRequestFlowStatusAfterInquiringHeisenberg(expectedRatePlan)
      assertRequestFlowStateUpatesFor(expectedSuppliers, expectedKeyValuePairs)
    }

    def executeTestFor[T](test: Seq[T] => Unit,
                          expectedHotelGenerator: (List[Int], HotelData) => Seq[T],
                          suppliers: List[Int],
                          initFlowStates: List[FlowState],
                          flowStateUnderTest: List[FlowState]) = {
      val expectedSuppliers = suppliers
      val expectedHotel = buildHotel(9, expectedSuppliers)
      val expectedKeyValuePairs =
        generateExpectedKeyValuePairs(expectedSuppliers, expectedHotel.hotelId, initFlowStates, flowStateUnderTest)

      prerecordedFlowStates(expectedKeyValuePairs, flowStateUnderTest)
      test(expectedHotelGenerator(List(enabledSupplier), expectedHotel))
      assertRequestFlowStateUpatesFor(expectedSuppliers, expectedKeyValuePairs)
    }

    def generateExpectedKeyValuePairs(suppliers: List[Int],
                                      hotelId: HotelId,
                                      initFlowStates: List[FlowState],
                                      flowStateUnderTest: List[FlowState]) = suppliers.map(_ -> hotelId).collect {
      case (supplier, hotel) if supplier == enabledSupplier =>
        (supplier, hotel) -> (flowStateUnderTest ++ initFlowStates)
      case supplierHotelKey => supplierHotelKey -> initFlowStates
    }

    private def prerecordedFlowStates(expectedKeyValuePairs: List[(RequestFlowDiagnosticKey, List[FlowState])],
                                      flowStatesToExclude: List[FlowState]) = expectedKeyValuePairs.forall {
      case (supplierAndHotel, flowStates) if supplierAndHotel._1 == enabledSupplier =>
        requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.asScala
          .putIfAbsent(supplierAndHotel, flowStates.diff(flowStatesToExclude)) should beNone
      case (supplierAndHotel, flowStates) => requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.asScala
          .putIfAbsent(supplierAndHotel, flowStates) should beNone
    }

    def createMetaPricingHotelInstance(
      enabledSuppliers: List[Int],
      hotel: HotelData,
    ): MetaUnits = {
      require(enabledSuppliers.nonEmpty)

      val hotelInfo = toHotelInfo(hotel)
      Seq(
        new WithMeta({},
                     hotelInfo,
                     supplierDispatchChannelHolder =
                       SupplierDispatchChannelHolder(List.empty, None, hotelInfo.getSuppliers, Map.empty)))
    }

    def toHotelInfo(h: HotelData) = {

      val suppliers = h.suppliers.filter(_.supplierId == enabledSupplier)
      val supplierMappings = h.supplierHotelMapping.filterKeys(_ == enabledSupplier)

      val childrenStayFreeTypes = ChildrenStayFreeType.getFromValue(h.childrenStayFreeType)

      aValidSupplyHotelInfo
        .withDmcMapping(h.defaultSupplier)
        .withHotelId(h.hotelId)
        .withCityId(h.cityId)
        .withChainId(h.chainId)
        .withSupplierMappings(supplierMappings)
        .withAPMLeadingRoomAdjustmentIds(h.apmLeadingRoomAdjustmentIds)
        .withMultipleAutoPriceMatch(h.multipleAutoPriceMatches)
        .withChildAgeRanges(h.childAgeRanges)
        .withAPMCommissionReductionEligibility(h.apmCommissionReductionEligibility)
        .withAgePolicy(AgePolicy(h.infantAge, h.childAge, h.minGuestAge, h.isChildrenStayFree, childrenStayFreeTypes))
        .withBookOnRequest(h.bookOnRequest)
        .withIsMyStaySupported(h.isMyStaySupported)
        .withHotelBenefits(h.supplyBenefits)
        .withPaxSubmit(Option(PaxType.getFromValue(h.paxSubmit.value)))
        .withProcessingFeeOption(h.processingFeeOption)
        .withRoomBenefits(h.roomBenefits.map { case (k, v) => k -> v.benefits.toList })
        .withYCSHotelCurrency(h.ycsHotelCurrency)
        .withIsYcsEnabled(h.isYcsEnabled)
        .withIsMobileEnabledAPS(h.isMobileEnabledAPS)
        .withIsChannelManaged(h.isChannelManaged)
        .withEnabledRooms(h.enabledRooms)
        .withGmtOffset(h.gmtOffset)
        .withCountryCode(h.countryCode)
        .withCountryId(h.countryId)
        .withSuppliers(suppliers.map(_.supplierId).toList)
        .build
    }

    def executeTestForAvailableState(suppliers: Seq[SupplierId]) = {
      val initFlowStates = List(BlacklistCheckPassed, HotelMetaFetchedFromDb)
      val flowStateUnderTest = List(HotelAvailable, HotelReady)
      executeTestFor(
        updateRequestFlowStatusAfterCheckHotelReadinessAndAvailability,
        createMetaPricingYPLHotelInstance(_, _, true),
        suppliers.toList,
        initFlowStates,
        flowStateUnderTest,
      )
    }

    def executeTestForReadyStateFromPull(suppliers: Seq[SupplierId]) = {
      val initFlowStates = List(BlacklistCheckPassed, HotelMetaFetchedFromDb)
      val flowStateUnderTest = List(HotelReady)

      executeTestFor(
        updateRequestFlowStatusAfterCheckHotelReadinessAndAvailability,
        createPricingYPLHotelInstance(_, _, false),
        suppliers.toList,
        initFlowStates,
        flowStateUnderTest,
      )
    }

    def executeTestForReadyStateFromPush(suppliers: Seq[SupplierId]) = {
      val initFlowStates = List(BlacklistCheckPassed, HotelMetaFetchedFromDb)
      val flowStateUnderTest = List(HotelReady)

      executeTestFor(
        updateRequestFlowStatusAfterCheckHotelReadinessAndAvailability,
        createMetaPricingYPLHotelInstance(_, _, false),
        suppliers.toList,
        initFlowStates,
        flowStateUnderTest,
      )
    }

    def createPricingYPLHotelInstance(
      enabledSuppliers: Seq[SupplierId],
      hotel: HotelData,
      withEnabledRooms: Boolean = true,
      mapValidRooms: (SupplierId, HotelId) => List[YPLRoom] = createValidYPLRooms): Seq[YPLHotel] =
      createMetaPricingYPLHotelInstance(enabledSuppliers, hotel, withEnabledRooms, mapValidRooms)

    def createMetaPricingYPLHotelInstance(
      enabledSuppliers: Seq[SupplierId],
      hotel: HotelData,
      withEnabledRooms: Boolean = true,
      mapValidRooms: (SupplierId, HotelId) => List[YPLRoom] = createValidYPLRooms): Seq[YPLHotel] = {
      require(enabledSuppliers.nonEmpty)

      val defaultSupplier = enabledSuppliers.head
      val supplierInfos = enabledSuppliers.map(
        SupplierInfo(_, SupplierType.Pull, isReady = true, isAvailable = true, supplierHotelId = Some("dmcHotelId")))
      val expectedRooms =
        if (withEnabledRooms) enabledSuppliers.flatMap(supplier => mapValidRooms(supplier, hotel.hotelId))
        else List.empty

      val pricingHotel = YPLTestDataBuilders.aValidHotel
        .withId(10009)
        .withSupplierId(defaultSupplier)
        .withSupplierSummary(supplierInfos.toList)
        .withRooms(expectedRooms.toList)
      val hotelInfo = toHotelInfo(hotel)
      Seq(pricingHotel)
    }

    def createValidYPLRooms(supplier: SupplierId, hotelId: HotelId): List[YPLRoom] = (1 to 3)
      .map(_ => aValidRoom.withSupplierId(supplier).withHotelId(hotelId).withMasterRoomId(Random.nextLong).build)
      .toList

    def buildHotel(index: Int, enabledSuppliers: List[Int]) = {
      val hotelId = 10000 + index
      HotelData(hotelId,
                9395,
                suppliers = buildCCSuppliers(hotelId, enabledSuppliers),
                supplierHotelMapping = buildSupplierMapping(hotelId, enabledSuppliers))
    }

    private def buildCCSuppliers(hotelId: Int, enabledSuppliers: List[Int]) =
      enabledSuppliers.map(supplier => SupplierCCMapping(supplier))

    private def buildSupplierMapping(hotelId: Int, enabledSuppliers: List[Int]) =
      enabledSuppliers.map(supplier => supplier -> SupplierHotel(hotelId, supplier, "dmcHotelId", List.empty)).toMap

  }

}
