package com.agoda.papi.pricing.supply.models

import com.agoda.papi.pricing.supply.models.PartnerFilterDataParser._
import org.specs2.mutable.SpecificationWithJUnit

/**
  * Created by tgalappathth on 8/2/17.
  */
class PartnerFilterDataParserSpec extends SpecificationWithJUnit {

  "Partner filter parser" should {
    "return correct list of suppliers for partner filter" in {
      val mockSuppliers: List[String] = List("123,124,126")

      val result = supplierWhiteList(mockSuppliers)
      val expectedResult = Some(PartnerSuppliers(Set(123, 124, 126)))

      result must_== expectedResult
    }

    "return correct partner filter data" in {
      val mockHotels: List[HotelId] = List(123, 124, 125)
      val mockCities: List[CityId] = List(123, 124, 125)
      val mockCountries: List[CountryId] = List(123, 124, 125)
      val mockSuppliers: List[String] = List("123,124,126")

      val result = toPartnerFilterData(mockHotels, mockCities, mockCountries, mockSuppliers)
      val expectedResult = PartnerFilterData(
        Set(123, 124, 125),
        Set(123, 124, 125),
        Set(123, 124, 125),
        supplierWhiteList(mockSuppliers),
      )

      result must_== expectedResult
    }
  }
}
