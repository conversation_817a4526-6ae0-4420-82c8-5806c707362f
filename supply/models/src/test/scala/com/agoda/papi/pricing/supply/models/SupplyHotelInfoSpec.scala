package com.agoda.papi.pricing.supply.models

import com.agoda.papi.pricing.metadata.MasterRoomInfo
import com.agoda.papi.pricing.supply.models.utils.SupplyModelTestDataBuilders
import org.specs2.mutable.SpecificationWithJUnit

class SupplyHotelInfoSpec extends SpecificationWithJUnit with SupplyModelTestDataBuilders {
  "SupplyHotelInfo" should {
    val enabledRooms: RoomInfoByRoomType =
      Map(1L -> MasterRoomInfo(1L, 10.0, -1, Some(1), None), 2L -> MasterRoomInfo(1L, 20.0, 3, Some(2), None))
    "getRoomProductOfferId" in {
      val info: SupplyHotelInfo = aValidSupplyHotelInfo.withEnabledRooms(enabledRooms)
      info.getRoomProductOfferId(1L) should_== None
      info.getRoomProductOfferId(2L) should_== Some(3)
      info.getRoomProductOfferId(3L) should_== None
    }

    "getMasterRoomId" in {
      val info: SupplyHotelInfo = aValidSupplyHotelInfo.withEnabledRooms(enabledRooms)
      info.getMasterRoomId(1L) should_== 1
      info.getMasterRoomId(2L) should_== 1
      info.getMasterRoomId(3L) should_== 3
    }
  }
}
