package com.agoda.papi.pricing.supply.models

import com.agoda.papi.constants.{Channel => CommonChannel}
import com.agoda.papi.enums.hotel.RateAvailabilityTypes
import com.agoda.papi.enums.room.RatePlanStatus._
import com.agoda.papi.pricing.supply.models.WithMeta.HotelMetaData
import com.agoda.papi.pricing.supply.models.request.{FencedOriginObject, FencedRatePair, RatePlanInfo}
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.suppliers.DMC
import external.pricing.{DispatchInfo, HotelDispatchInfo}
import com.agoda.papi.ypl.models.{YplRateFence, YplChannel => DFCompositeChannel, YplMasterChannel => DFMasterChannel}
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

class WithMetaSpec extends SpecificationWithJUnit with Mockito {

  "WithMeta" should {

    def updateWithMetaWithRequestedRatePlans[D, M <: HotelMetaData](
      meta: WithMeta[D, M],
      requestedRatePlans: List[RatePlanInfo]): WithMeta[D, M] = meta.copy(
      supplierDispatchChannelHolder = meta.supplierDispatchChannelHolder.copy(
        requestedRatePlans = requestedRatePlans,
      ),
    )

    def updateWithMetaWithHotelDispatch[D, M <: HotelMetaData](
      meta: WithMeta[D, M],
      hotelDispatchInfo: Option[HotelDispatchInfo]): WithMeta[D, M] = meta.copy(
      supplierDispatchChannelHolder = meta.supplierDispatchChannelHolder.copy(
        hotelDispatchInfo = hotelDispatchInfo,
      ),
    )

    def updateWithMetaWithDispatchAndFencedRequestedRatePlans[D, M <: HotelMetaData](
      meta: WithMeta[D, M],
      fencedRequestedRatePlans: Map[YplRateFence, Set[RatePlanInfo]],
      hotelDispatchInfo: Option[HotelDispatchInfo]): WithMeta[D, M] = meta.copy(
      supplierDispatchChannelHolder = meta.supplierDispatchChannelHolder.copy(
        fencedRequestedRatePlans = fencedRequestedRatePlans,
        hotelDispatchInfo = hotelDispatchInfo,
      ),
    )

    val SupplyHotelInfo = mock[SupplyHotelInfo]
    SupplyHotelInfo.getSuppliers returns Set(DMC.YCS, DMC.BCOM)

    val requestedRatePlans = Set(DFMasterChannel.RTL, DFMasterChannel.APS)
    val requestedRatePlansInfo = requestedRatePlans.map(rp => rp -> RatePlanInfo(rp)).toMap
    val requestedRatePlansInfoWithHelper = Map(
      DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL),
      DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Helper),
    )

    val requestedRatePlansInMeta = requestedRatePlans.map(RatePlanInfo(_)).toList
    val requestedRatePlansInfoWithHelperInMeta = requestedRatePlansInfoWithHelper.values.toList
    val requestedSupplierChannelDispatchHolder = SupplierDispatchChannelHolder(requestedRatePlansInMeta,
                                                                               scala.Option.empty,
                                                                               SupplyHotelInfo.getSuppliers,
                                                                               Map.empty)
    val requestedWithHelperSupplierChannelDispatchHolder = SupplierDispatchChannelHolder(
      requestedRatePlansInfoWithHelperInMeta,
      scala.Option.empty,
      SupplyHotelInfo.getSuppliers,
      Map.empty)
    val requestedOnly: WithMeta[Int, SupplyHotelInfo] = WithMeta[Int, SupplyHotelInfo](
      0,
      SupplyHotelInfo,
      supplierDispatchChannelHolder = requestedSupplierChannelDispatchHolder)
    val requestWithHelper: WithMeta[Int, SupplyHotelInfo] = WithMeta[Int, SupplyHotelInfo](
      0,
      SupplyHotelInfo,
      supplierDispatchChannelHolder = requestedWithHelperSupplierChannelDispatchHolder)

    val empty = updateWithMetaWithRequestedRatePlans(requestedOnly, requestedRatePlans = Nil)

    val fenceTH = YplRateFence("TH", -1, 1)
    val fenceHK = YplRateFence("HK", 1, 1)
    val aValidFencedRatePair = FencedRatePair(fenceTH, FencedOriginObject(Set(1)))

    val ycsWithAPO = updateWithMetaWithHotelDispatch(
      requestedOnly,
      hotelDispatchInfo = Some(
        HotelDispatchInfo(
          hotelId = 1,
          dispatchInfos = Seq(
            DispatchInfo(0, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.YCS, DFMasterChannel.APO, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.BCOM, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.IHGD, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
          ),
        )),
    )

    val ycsWithHelperNotDispatched = updateWithMetaWithHotelDispatch(
      requestWithHelper,
      hotelDispatchInfo = Some(
        HotelDispatchInfo(
          hotelId = 1,
          dispatchInfos = Seq(
            DispatchInfo(0, DMC.YCS, DFMasterChannel.APO, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.BCOM, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.IHGD, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
          ),
        )),
    )

    val ycsWithHelperDispatchedOnlyOnYcs = updateWithMetaWithHotelDispatch(
      requestWithHelper,
      hotelDispatchInfo = Some(
        HotelDispatchInfo(
          hotelId = 1,
          dispatchInfos = Seq(
            DispatchInfo(0, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.YCS, DFMasterChannel.APO, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.BCOM, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.IHGD, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
          ),
        )),
    )

    "Get RatePlans (with / without info) per supplier map" in {
      "Empty case" in {
        empty.supplierChannelsMap should_== Map(DMC.YCS -> Set.empty, DMC.BCOM -> Set.empty)
        empty.supplierChannelsInfoMap should_== Map(DMC.YCS -> Map.empty, DMC.BCOM -> Map.empty)
        empty.supplierHelperChannelsMap should_== Map(DMC.YCS -> Set.empty, DMC.BCOM -> Set.empty)
      }

      "No dispatches - return only requested" in {
        requestedOnly.supplierChannelsMap should_== Map(
          DMC.YCS -> requestedRatePlans,
          DMC.BCOM -> requestedRatePlans,
        )

        requestedOnly.supplierChannelsInfoMap should_== Map(
          DMC.YCS -> requestedRatePlansInfo,
          DMC.BCOM -> requestedRatePlansInfo,
        )

        requestedOnly.supplierHelperChannelsMap should_== Map(DMC.YCS -> Set.empty, DMC.BCOM -> Set.empty)
      }

      "Have dispatches for YCS only; note that we ignore extra dispatched suppliers" in {
        ycsWithAPO.supplierChannelsMap should_== Map(
          DMC.YCS -> (requestedRatePlans + DFMasterChannel.APO),
          DMC.BCOM -> requestedRatePlans,
        )

        ycsWithAPO.supplierChannelsInfoMap should_== Map(
          DMC.YCS -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Requested),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Dispatched),
            DFMasterChannel.APO -> RatePlanInfo(DFMasterChannel.APO, Dispatched),
          ),
          DMC.BCOM -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Dispatched),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Requested),
          ),
        )

        ycsWithAPO.supplierHelperChannelsMap should_== Map(332 -> Set.empty, 3038 -> Set.empty)
      }

      "PFE-7609 B : Have Dispatched or DispatchedForSecretDeal based on secret deal flag " in {
        val ycsWithAPO = updateWithMetaWithHotelDispatch(
          requestedOnly,
          hotelDispatchInfo = Some(
            HotelDispatchInfo(
              hotelId = 1,
              dispatchInfos = Seq(
                DispatchInfo(0, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = aValidFencedRatePair),
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel.APO,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretDealDispatched = true),
                DispatchInfo(0, DMC.BCOM, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
                DispatchInfo(0,
                             DMC.BCOM,
                             DFMasterChannel.APS,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretDealDispatched = true),
                DispatchInfo(0, DMC.IHGD, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
              ),
            )),
        )

        ycsWithAPO.supplierChannelsMap should_== Map(
          DMC.YCS -> (requestedRatePlans + DFMasterChannel.APO),
          DMC.BCOM -> requestedRatePlans,
        )

        ycsWithAPO.supplierChannelsInfoMap should_== Map(
          DMC.YCS -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Requested),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Dispatched),
            DFMasterChannel.APO -> RatePlanInfo(DFMasterChannel.APO, DispatchedForSecretDeal),
          ),
          DMC.BCOM -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Dispatched),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, DispatchedForSecretDeal),
          ),
        )
      }

      "set SecretDispatched and Dispatched RatePlan status depending on isSecretDispatched from Heisenberg" in {

        val ycsWithSecretDispatched = updateWithMetaWithHotelDispatch(
          requestedOnly,
          hotelDispatchInfo = Some(
            HotelDispatchInfo(
              hotelId = 1,
              dispatchInfos = Seq(
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel.APS,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(false)),
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel.APO,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(true)),
                DispatchInfo(0,
                             DMC.BCOM,
                             DFMasterChannel.RTL,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(false)),
                DispatchInfo(0,
                             DMC.IHGD,
                             DFMasterChannel.RTL,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(true)),
              ),
            )),
        )
        ycsWithSecretDispatched.supplierChannelsMap should_== Map(
          DMC.YCS -> (requestedRatePlans + DFMasterChannel.APO),
          DMC.BCOM -> requestedRatePlans,
        )

        ycsWithSecretDispatched.supplierChannelsInfoMap should_== Map(
          DMC.YCS -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Requested),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Dispatched, Some(false)),
            DFMasterChannel.APO -> RatePlanInfo(DFMasterChannel.APO, SecretlyDispatched, Some(true)),
          ),
          DMC.BCOM -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Dispatched, Some(false)),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Requested),
          ),
        )
      }

      "supplierHelperChannelsMap correctly if helper channel in request which isn't dispatched from heisenberg" should {
        ycsWithHelperNotDispatched.supplierHelperChannelsMap should_== Map(
          DMC.YCS -> Set(DFMasterChannel.APS),
          DMC.BCOM -> Set(DFMasterChannel.APS),
        )
      }

      "supplierHelperChannelsMap correctly if helper channel in request and is dispatched from heisenberg" should {
        ycsWithHelperDispatchedOnlyOnYcs.supplierHelperChannelsMap should_== Map(
          DMC.YCS -> Set.empty,
          DMC.BCOM -> Set(DFMasterChannel.APS),
        )
      }
    }

    "Get Supplier RatePlans (with / without statuses)" in {
      "Empty" in {
        empty.getSupplierRatePlans(DMC.YCS) should_== Set.empty
        empty.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.RTL).state should_== None
      }

      "Requested only" in {
        requestedOnly.getSupplierRatePlans(DMC.YCS) should_== requestedRatePlans
        requestedOnly.getSupplierRatePlans(DMC.IHGD) should_== Set.empty

        requestedOnly.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.RTL).state should_== Requested
        requestedOnly.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.APS).state should_== Requested
        requestedOnly.getSupplierRatePlanInfo(DMC.IHGD, DFMasterChannel.RTL).state should_== None
      }

      "APO dispatched for YCS; prefer Dispatched status over Requested" in {
        ycsWithAPO.getSupplierRatePlans(DMC.YCS) should_== requestedRatePlans + DFMasterChannel.APO
        ycsWithAPO.getSupplierRatePlans(DMC.BCOM) should_== requestedRatePlans
        ycsWithAPO.getSupplierRatePlans(DMC.IHGD) should_== Set.empty

        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.RTL).state should_== Requested
        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.APS).state should_== Dispatched
        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.APO).state should_== Dispatched
        ycsWithAPO.getSupplierRatePlanInfo(DMC.AMOMA, DFMasterChannel.RTL).state should_== None
      }

      "PFE-7609 B : Rate Channel status should be DispatchedForSecretDeal for isSecretDeal" in {
        val ycsWithAPO = updateWithMetaWithHotelDispatch(
          requestedOnly,
          hotelDispatchInfo = Some(
            HotelDispatchInfo(
              hotelId = 1,
              dispatchInfos = Seq(
                DispatchInfo(0, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = aValidFencedRatePair),
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel.APO,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretDealDispatched = true),
                DispatchInfo(0, DMC.BCOM, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
                DispatchInfo(0,
                             DMC.BCOM,
                             DFMasterChannel.APS,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretDealDispatched = true),
                DispatchInfo(0, DMC.IHGD, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
              ),
            )),
        )

        ycsWithAPO.getSupplierRatePlans(DMC.YCS) should_== requestedRatePlans + DFMasterChannel.APO
        ycsWithAPO.getSupplierRatePlans(DMC.BCOM) should_== requestedRatePlans
        ycsWithAPO.getSupplierRatePlans(DMC.IHGD) should_== Set.empty

        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.RTL).state should_== Requested
        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.APS).state should_== Dispatched
        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.APO).state should_== DispatchedForSecretDeal
        ycsWithAPO.getSupplierRatePlanInfo(DMC.BCOM, DFMasterChannel.APS).state should_== DispatchedForSecretDeal
        ycsWithAPO.getSupplierRatePlanInfo(DMC.AMOMA, DFMasterChannel.RTL).state should_== None
      }

      "correctly convert Rate Channel Swap rate plan status" in {
        val hotelId = 0
        val dispatchInfo = Seq(
          DispatchInfo(hotelId, DMC.YCS, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
          DispatchInfo(
            hotelId,
            DMC.YCS,
            DFMasterChannel.APS,
            0,
            Nil,
            fencedRatePair = aValidFencedRatePair,
            rateAvailabilityType = List(RateAvailabilityTypes.RateChannel, RateAvailabilityTypes.Unknown),
          ),
        )
        val hotelDispatchInfo = HotelDispatchInfo(hotelId, dispatchInfo)

        val withMeta =
          updateWithMetaWithHotelDispatch(requestedOnly,
                                          hotelDispatchInfo =
                                            Some(HotelDispatchInfo(hotelId = hotelId, dispatchInfos = dispatchInfo)))

        withMeta.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.APS).state should_== DispatchedForRateChannelSwap
        hotelDispatchInfo.hasRateChannelSwapRooms should_== true
      }
    }

    "Get Supplier RatePlans (with / without statuses) for stacked rate channel" in {
      "getSupplierRatePlanInfo" in {
        val ycsWithAPO = updateWithMetaWithHotelDispatch(
          requestedOnly,
          hotelDispatchInfo = Some(
            HotelDispatchInfo(
              hotelId = 1,
              dispatchInfos = Seq(
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel.RTL,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(false)),
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel.APO,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(true)),
                DispatchInfo(0,
                             DMC.BCOM,
                             DFMasterChannel.RTL,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretDealDispatched = true),
                DispatchInfo(
                  0,
                  DMC.YCS,
                  DFMasterChannel(CommonChannel.RHGWholeSale),
                  0,
                  Nil,
                  fencedRatePair = aValidFencedRatePair,
                  isSecretlyDispatched = Some(true),
                ),
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel(Channel.Bedbank),
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(false)),
                DispatchInfo(
                  0,
                  DMC.YCS,
                  DFMasterChannel(Channel.BedbankAffiliates),
                  0,
                  Nil,
                  fencedRatePair = aValidFencedRatePair,
                  isSecretlyDispatched = Some(true),
                ),
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel(Channel.GeniusMobileWeb),
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair),
                DispatchInfo(0,
                             DMC.BCOM,
                             DFMasterChannel.APS,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretDealDispatched = true),
                DispatchInfo(0, DMC.IHGD, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
              ),
            )),
        )

        val testCompositeChannelId1: Int = 10001
        val testCompositeChannelId2: Int = 10002
        val testCompositeChannelId3: Int = 10003
        val testCompositeChannelId4: Int = 10004

        ycsWithAPO.getSupplierRatePlans(DMC.YCS) should_== requestedRatePlans + DFMasterChannel.APO + DFMasterChannel(
          Channel.BedbankAffiliates) + DFMasterChannel(Channel.Bedbank) + DFMasterChannel(
          CommonChannel.RHGWholeSale) + DFMasterChannel(Channel.GeniusMobileWeb)
        ycsWithAPO.getSupplierRatePlans(DMC.BCOM) should_== requestedRatePlans
        ycsWithAPO.getSupplierRatePlans(DMC.IHGD) should_== Set.empty

        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.RTL) should_== RatePlanInfo(DFMasterChannel.RTL,
                                                                                                Dispatched,
                                                                                                Some(false))

        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.APO) should_== RatePlanInfo(DFMasterChannel.APO,
                                                                                                SecretlyDispatched,
                                                                                                Some(true))

        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS, DFMasterChannel.APS) should_== RatePlanInfo(DFMasterChannel.APS,
                                                                                                Requested,
                                                                                                scala.None)

        ycsWithAPO.getSupplierRatePlanInfo(DMC.BCOM, DFMasterChannel.RTL) should_== RatePlanInfo(DFMasterChannel.RTL,
                                                                                                 DispatchedForSecretDeal,
                                                                                                 scala.None)

        ycsWithAPO.getSupplierRatePlanInfo(DMC.BCOM, DFMasterChannel.APO) should_== RatePlanInfo(DFMasterChannel.APO,
                                                                                                 None,
                                                                                                 scala.None)

        ycsWithAPO.getSupplierRatePlanInfo(DMC.BCOM, DFMasterChannel.APS) should_== RatePlanInfo(DFMasterChannel.APS,
                                                                                                 DispatchedForSecretDeal,
                                                                                                 scala.None)

        ycsWithAPO.getSupplierRatePlanInfo(DMC.IHGD, DFMasterChannel.RTL) should_== RatePlanInfo(DFMasterChannel.RTL,
                                                                                                 None,
                                                                                                 scala.None)

        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS,
                                           channel =
                                             DFCompositeChannel(CommonChannel.WholeSaleRateChannels,
                                                                testCompositeChannelId1).get) should_== RatePlanInfo(
          DFCompositeChannel(CommonChannel.WholeSaleRateChannels, testCompositeChannelId1).get,
          None,
          Some(true))

        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS,
                                           DFCompositeChannel(Seq(Channel.Bedbank, Channel.BedbankAffiliates),
                                                              testCompositeChannelId2).get) should_== RatePlanInfo(
          DFCompositeChannel(Seq(Channel.Bedbank, Channel.BedbankAffiliates), testCompositeChannelId2).get,
          None,
          Some(true))

        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS,
                                           DFCompositeChannel(Seq(Channel.GeniusMobileWeb,
                                                                  Channel.GeniusInternational,
                                                                  Channel.GeniusDomestic),
                                                              testCompositeChannelId3).get) should_== RatePlanInfo(
          DFCompositeChannel(Seq(Channel.GeniusMobileWeb, Channel.GeniusInternational, Channel.GeniusDomestic),
                             testCompositeChannelId3).get,
          None,
          Some(false))

        ycsWithAPO.getSupplierRatePlanInfo(DMC.YCS,
                                           DFCompositeChannel(Seq(Channel.RTL, Channel.Mobile, Channel.Domestic),
                                                              testCompositeChannelId4).get) should_== RatePlanInfo(
          DFCompositeChannel(Seq(Channel.RTL, Channel.Mobile, Channel.Domestic), testCompositeChannelId4).get,
          None,
          Some(false))

      }
    }

    "Fenced Supplier Channel Status" in {

      "different dispatch status per fence" in {
        val fencedRequest = updateWithMetaWithDispatchAndFencedRequestedRatePlans(
          requestedOnly,
          fencedRequestedRatePlans = Map(
            fenceTH -> Set(RatePlanInfo(DFMasterChannel.RTL, Requested),
                           RatePlanInfo(DFMasterChannel.Domestic, Requested)),
            fenceHK -> Set(RatePlanInfo(DFMasterChannel.RTL, Requested), RatePlanInfo(DFMasterChannel.APS, Requested)),
          ),
          hotelDispatchInfo = Some(
            HotelDispatchInfo(
              hotelId = 1,
              dispatchInfos = Seq(
                DispatchInfo(1,
                             DMC.YCS,
                             DFMasterChannel.RTL,
                             1,
                             fencedRatePair = FencedRatePair(fenceTH, FencedOriginObject(Set(1)))),
                DispatchInfo(1,
                             DMC.YCS,
                             DFMasterChannel.APS,
                             1,
                             isSecretlyDispatched = Some(true),
                             fencedRatePair = FencedRatePair(fenceTH, FencedOriginObject(Set(1)))),
                DispatchInfo(1,
                             DMC.YCS,
                             DFMasterChannel.RTL,
                             1,
                             fencedRatePair = FencedRatePair(fenceHK, FencedOriginObject(Set(1, 2)))),
              ),
            )),
        )

        fencedRequest.getFencedSupplierChannelStatus(DMC.YCS, DFMasterChannel.RTL, fenceTH) should_=== Dispatched
        fencedRequest.getFencedSupplierChannelStatus(DMC.YCS,
                                                     DFMasterChannel.APS,
                                                     fenceTH) should_=== SecretlyDispatched
        fencedRequest.getFencedSupplierChannelStatus(DMC.YCS, DFMasterChannel.Domestic, fenceTH) should_=== Requested

        fencedRequest.getFencedSupplierChannelStatus(DMC.YCS, DFMasterChannel.RTL, fenceHK) should_=== Dispatched
        fencedRequest.getFencedSupplierChannelStatus(DMC.YCS, DFMasterChannel.APS, fenceHK) should_=== Requested
        fencedRequest.getFencedSupplierChannelStatus(DMC.YCS, DFMasterChannel.Domestic, fenceHK) should_=== None
      }
    }
  }
}
