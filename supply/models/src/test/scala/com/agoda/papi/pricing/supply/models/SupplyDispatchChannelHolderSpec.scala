package com.agoda.papi.pricing.supply.models

import com.agoda.papi.enums.room.RatePlanStatus._
import com.agoda.papi.pricing.supply.models.request.{FencedOriginObject, FencedRatePair, RatePlanInfo}
import com.agoda.papi.ypl.models.suppliers.DMC
import com.agoda.papi.ypl.models.{YplRateFence, YplChannel => DFCompositeChannel, YplMasterChannel => DFMasterChannel}
import external.pricing.{DispatchInfo, HotelDispatchInfo}
import org.specs2.mock.Mockito
import org.specs2.mutable.SpecificationWithJUnit

class SupplyDispatchChannelHolderSpec extends SpecificationWithJUnit with Mockito {
  "SupplyDispatchChannelHolder" should {

    def updateWithRequestedRatePlans(supplierDispatchChannelHolder: SupplierDispatchChannelHolder,
                                     requestedRatePlans: List[RatePlanInfo]): SupplierDispatchChannelHolder =
      supplierDispatchChannelHolder.copy(
        requestedRatePlans = requestedRatePlans,
      )

    def updateWithHotelDispatch(supplierDispatchChannelHolder: SupplierDispatchChannelHolder,
                                hotelDispatchInfo: Option[HotelDispatchInfo]) = supplierDispatchChannelHolder.copy(
      hotelDispatchInfo = hotelDispatchInfo,
    )

    def updateWithDispatchAndFencedRequestedRatePlans(
      supplierDispatchChannelHolder: SupplierDispatchChannelHolder,
      fencedRequestedRatePlans: Map[YplRateFence, Set[RatePlanInfo]],
      hotelDispatchInfo: Option[HotelDispatchInfo]) = supplierDispatchChannelHolder.copy(
      hotelDispatchInfo = hotelDispatchInfo,
      fencedRequestedRatePlans = fencedRequestedRatePlans,
    )

    val hotelInfo = mock[SupplyHotelInfo]
    hotelInfo.getSuppliers returns Set(DMC.YCS, DMC.BCOM)

    val requestedRatePlans = Set(DFMasterChannel.RTL, DFMasterChannel.APS)
    val requestedRatePlansInfo = requestedRatePlans.map(rp => rp -> RatePlanInfo(rp)).toMap
    val requestedRatePlansInfoWithHelper = Map(
      DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL),
      DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Helper),
    )

    val requestedRatePlansInMeta = requestedRatePlans.map(RatePlanInfo(_)).toList
    val requestedRatePlansInfoWithHelperInMeta = requestedRatePlansInfoWithHelper.values.toList
    val requestedSupplierChannelDispatchHolder =
      SupplierDispatchChannelHolder(requestedRatePlansInMeta, scala.Option.empty, hotelInfo.getSuppliers, Map.empty)
    val requestedWithHelperSupplierChannelDispatchHolder = SupplierDispatchChannelHolder(
      requestedRatePlansInfoWithHelperInMeta,
      scala.Option.empty,
      hotelInfo.getSuppliers,
      Map.empty)

    val empty = updateWithRequestedRatePlans(requestedWithHelperSupplierChannelDispatchHolder, requestedRatePlans = Nil)

    val fenceTH = YplRateFence("TH", -1, 22)
    val fenceHK = YplRateFence("HK", 1, 1)
    val aValidFencedRatePair = FencedRatePair(fenceTH, FencedOriginObject(Set(1)))

    val ycsWithAPO = updateWithHotelDispatch(
      requestedSupplierChannelDispatchHolder,
      hotelDispatchInfo = Some(
        HotelDispatchInfo(
          hotelId = 1,
          dispatchInfos = Seq(
            DispatchInfo(0, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.YCS, DFMasterChannel.APO, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.BCOM, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.IHGD, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
          ),
        )),
    )

    val ycsWithHelperNotDispatched = updateWithHotelDispatch(
      requestedWithHelperSupplierChannelDispatchHolder,
      hotelDispatchInfo = Some(
        HotelDispatchInfo(
          hotelId = 1,
          dispatchInfos = Seq(
            DispatchInfo(0, DMC.YCS, DFMasterChannel.APO, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.BCOM, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.IHGD, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
          ),
        )),
    )

    val ycsWithHelperDispatchedOnlyOnYcs = updateWithHotelDispatch(
      requestedWithHelperSupplierChannelDispatchHolder,
      hotelDispatchInfo = Some(
        HotelDispatchInfo(
          hotelId = 1,
          dispatchInfos = Seq(
            DispatchInfo(0, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.YCS, DFMasterChannel.APO, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.BCOM, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
            DispatchInfo(0, DMC.IHGD, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
          ),
        )),
    )

    val fencedRatePlanWithOneSize = Map(
      fenceTH -> Set(RatePlanInfo(DFMasterChannel.RTL)),
    )
    val multipleFencedRatePlans = Map(
      fenceTH -> Set(RatePlanInfo(DFMasterChannel.RTL)),
      fenceHK -> Set(RatePlanInfo(DFMasterChannel.RTL)),
    )

    val dispatchInfo: Option[HotelDispatchInfo] = Some(
      HotelDispatchInfo(
        hotelId = 1,
        dispatchInfos = Seq(
          DispatchInfo(0, DMC.YCS, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
        ),
      ),
    )

    "useFencedLazyVal" in {
      "return false if empty" in {
        empty.useFencedLazyVal should_== (false)
      }

      "return false if but no dispatch and no fencedRequestedPlans" in {
        empty.useFencedLazyVal should_== (false)
      }

      "return false if useFencedDIspatch is true and has dispatch but no fencedRequestedPlans" in {
        updateWithHotelDispatch(empty, dispatchInfo).useFencedLazyVal should_== (false)
      }

      "return true if has dispatch but has one fencedRequestedPlans" in {
        updateWithDispatchAndFencedRequestedRatePlans(
          empty,
          fencedRatePlanWithOneSize,
          dispatchInfo,
        ).useFencedLazyVal should_== (true)
      }

      "return false if useFencedDIspatch is true and has dispatch but has one fencedRequestedPlans" in {
        updateWithDispatchAndFencedRequestedRatePlans(
          empty,
          multipleFencedRatePlans,
          dispatchInfo,
        ).useFencedLazyVal should_== (false)
      }
    }

    "Get RatePlans (with / without info) per supplier map" in {
      "Empty case" in {
        empty.supplierChannelsMap should_== Map(DMC.YCS -> Set.empty, DMC.BCOM -> Set.empty)
        empty.supplierChannelsInfoMap should_== Map(DMC.YCS -> Map.empty, DMC.BCOM -> Map.empty)
        empty.supplierHelperChannelsMap should_== Map(DMC.YCS -> Set.empty, DMC.BCOM -> Set.empty)
      }

      "No dispatches - return only requested" in {
        requestedSupplierChannelDispatchHolder.supplierChannelsMap should_== Map(
          DMC.YCS -> requestedRatePlans,
          DMC.BCOM -> requestedRatePlans,
        )

        requestedSupplierChannelDispatchHolder.supplierChannelsInfoMap should_== Map(
          DMC.YCS -> requestedRatePlansInfo,
          DMC.BCOM -> requestedRatePlansInfo,
        )

        requestedSupplierChannelDispatchHolder.supplierHelperChannelsMap should_== Map(DMC.YCS -> Set.empty,
                                                                                       DMC.BCOM -> Set.empty)
      }

      "Have dispatches for YCS only; note that we ignore extra dispatched suppliers" in {
        ycsWithAPO.supplierChannelsMap should_== Map(
          DMC.YCS -> (requestedRatePlans + DFMasterChannel.APO),
          DMC.BCOM -> requestedRatePlans,
        )

        ycsWithAPO.supplierChannelsInfoMap should_== Map(
          DMC.YCS -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Requested),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Dispatched),
            DFMasterChannel.APO -> RatePlanInfo(DFMasterChannel.APO, Dispatched),
          ),
          DMC.BCOM -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Dispatched),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Requested),
          ),
        )

        ycsWithAPO.supplierHelperChannelsMap should_== Map(332 -> Set.empty, 3038 -> Set.empty)
      }

      "Have Dispatched or DispatchedForSecretDeal based on secret deal flag" in {
        val ycsWithAPO = updateWithHotelDispatch(
          requestedWithHelperSupplierChannelDispatchHolder,
          hotelDispatchInfo = Some(
            HotelDispatchInfo(
              hotelId = 1,
              dispatchInfos = Seq(
                DispatchInfo(0, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = aValidFencedRatePair),
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel.APO,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretDealDispatched = true),
                DispatchInfo(0, DMC.BCOM, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
                DispatchInfo(0,
                             DMC.BCOM,
                             DFMasterChannel.APS,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretDealDispatched = true),
                DispatchInfo(0, DMC.IHGD, DFMasterChannel.RTL, 0, Nil, fencedRatePair = aValidFencedRatePair),
              ),
            )),
        )

        ycsWithAPO.supplierChannelsMap should_== Map(
          DMC.YCS -> (requestedRatePlans + DFMasterChannel.APO),
          DMC.BCOM -> requestedRatePlans,
        )

        ycsWithAPO.supplierChannelsInfoMap should_== Map(
          DMC.YCS -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Requested),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Dispatched),
            DFMasterChannel.APO -> RatePlanInfo(DFMasterChannel.APO, DispatchedForSecretDeal),
          ),
          DMC.BCOM -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Dispatched),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, DispatchedForSecretDeal),
          ),
        )
      }

      "set SecretDispatched and Dispatched RatePlan status depending on isSecretDispatched from Heisenberg" in {

        val ycsWithSecretDispatched = updateWithHotelDispatch(
          requestedSupplierChannelDispatchHolder,
          hotelDispatchInfo = Some(
            HotelDispatchInfo(
              hotelId = 1,
              dispatchInfos = Seq(
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel.APS,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(false)),
                DispatchInfo(0,
                             DMC.YCS,
                             DFMasterChannel.APO,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(true)),
                DispatchInfo(0,
                             DMC.BCOM,
                             DFMasterChannel.RTL,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(false)),
                DispatchInfo(0,
                             DMC.IHGD,
                             DFMasterChannel.RTL,
                             0,
                             Nil,
                             fencedRatePair = aValidFencedRatePair,
                             isSecretlyDispatched = Some(true)),
              ),
            )),
        )
        ycsWithSecretDispatched.supplierChannelsMap should_== Map(
          DMC.YCS -> (requestedRatePlans + DFMasterChannel.APO),
          DMC.BCOM -> requestedRatePlans,
        )

        ycsWithSecretDispatched.supplierChannelsInfoMap should_== Map(
          DMC.YCS -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Requested),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Dispatched, Some(false)),
            DFMasterChannel.APO -> RatePlanInfo(DFMasterChannel.APO, SecretlyDispatched, Some(true)),
          ),
          DMC.BCOM -> Map(
            DFMasterChannel.RTL -> RatePlanInfo(DFMasterChannel.RTL, Dispatched, Some(false)),
            DFMasterChannel.APS -> RatePlanInfo(DFMasterChannel.APS, Requested),
          ),
        )
      }

      "supplierHelperChannelsMap correctly if helper channel in request which isn't dispatched from heisenberg" should {
        ycsWithHelperNotDispatched.supplierHelperChannelsMap should_== Map(
          DMC.YCS -> Set(DFMasterChannel.APS),
          DMC.BCOM -> Set(DFMasterChannel.APS),
        )
      }

      "supplierHelperChannelsMap correctly if helper channel in request and is dispatched from heisenberg" should {
        ycsWithHelperDispatchedOnlyOnYcs.supplierHelperChannelsMap should_== Map(
          DMC.YCS -> Set.empty,
          DMC.BCOM -> Set(DFMasterChannel.APS),
        )
      }

      "use result from fenced map" in {
        val result = updateWithDispatchAndFencedRequestedRatePlans(empty,
                                                                   fencedRatePlanWithOneSize,
                                                                   dispatchInfo).supplierChannelsInfoMap
        result should_== Map(
          332 -> Map(DFCompositeChannel(1, Set(), 1) -> RatePlanInfo(DFCompositeChannel(1, Set(), 1), Dispatched)),
          3038 -> Map(DFCompositeChannel(1, Set(), 1) -> RatePlanInfo(DFCompositeChannel(1, Set(), 1), Requested)),
        )
      }

      "use result from non fenced map but have multile plans" in {
        val result = updateWithDispatchAndFencedRequestedRatePlans(empty,
                                                                   multipleFencedRatePlans,
                                                                   dispatchInfo).supplierChannelsInfoMap
        result should_== Map(
          332 -> Map(DFCompositeChannel(1, Set(), 1) -> RatePlanInfo(DFCompositeChannel(1, Set(), 1), Dispatched)),
          3038 -> Map())
      }
    }
  }
}
