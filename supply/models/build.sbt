name := "supply-models"

libraryDependencies ++= Seq(
  "com.agoda"            %% "rflows"                           % versions.agoda.rflows,
  "com.agoda"            %% "supplier-pricing"                 % versions.agoda.supplierClient
    exclude ("com.typesafe.akka", s"akka-cluster_${scalaBinaryVersion.value}")
    exclude ("com.typesafe.akka", s"akka-cluster-tools_${scalaBinaryVersion.value}")
    exclude ("ch.qos.logback", "logback-classic")
    exclude ("com.agoda.adp.messaging", "adp-messaging-client-shaded")
    exclude ("com.datastax.cassandra", "cassandra-driver-core")
    exclude ("com.typesafe.akka", s"akka-actor_${scalaBinaryVersion.value}")
    exclude ("com.typesafe.akka", s"akka-testkit_${scalaBinaryVersion.value}")
    exclude ("com.typesafe.akka", s"akka-http_${scalaBinaryVersion.value}")
    exclude ("com.github.julien-truffaut", "monocle-core")
    exclude ("com.agoda", s"ag-protobuf-scalapb-0-11-x-df_${scalaBinaryVersion.value}")
    exclude ("com.agoda", s"ag-protobuf-scalapb-0-11-x-ota_${scalaBinaryVersion.value}")
    exclude ("com.agoda", s"cassandra-wrapper_${scalaBinaryVersion.value}")
    exclude ("com.agoda.mixnsave", s"mix-n-save_protobuf_${scalaBinaryVersion.value}")
    exclude ("com.agoda.platform", s"ag-grpc-client_${scalaBinaryVersion.value}")
    exclude ("com.agoda", s"supplier-pull-calculator-protobuf_${scalaBinaryVersion.value}")
    exclude ("com.google.guava", "guava") withSources ()
    exclude ("log4j", "log4j"),
  "com.agoda.commons"    %% "ag-protobuf-scalapb-0-11-x-df"    % versions.agoda.agprotobuf,
  "com.agoda.commons"    %% "ag-protobuf-scalapb-0-11-x-ota"   % versions.agoda.agprotobuf,
  "com.agoda.commons"    %% "ag-protobuf-scalapb-0-11-x-hmc"   % versions.agoda.agprotobuf,
  "com.agoda.commons"    %% "models"                           % versions.agoda.commons withSources (),
  "com.agoda.commons"    %% "traffic"                          % versions.agoda.commons withSources (),
  "com.agoda.commons"    %% "utils"                            % versions.agoda.commons withSources (),
  "io.scalaland"         %% "chimney"                          % versions.chimney,
  "com.agoda.papi"       %% "enum-commons"                     % versions.agoda.enumcommons,
  "io.opentelemetry"      % "opentelemetry-sdk-testing"        % versions.opentelemetry % Test,
  "com.agoda.styx"       %% "botprofile-parser"                % versions.botProfileParser,
  "com.agoda.ml"         %% "pricing_client"                   % versions.agoda.pricingClient
    exclude ("com.typesafe.akka", s"akka-http_${scalaBinaryVersion.value}")
    excludeAll (ExclusionRule(organization = "io.circe")),
  "com.agoda.ccas"        % "ccas-grpc-client"                 % versions.agoda.ccasClient
    exclude ("com.typesafe", "config")
    exclude ("com.agoda.commons", "ag-observability-agent")
    exclude ("com.agoda.commons", "ag-observability-api")
    exclude ("com.agoda.commons", "ag-observability-sdk-grpc"),
  "com.agoda.commons"    %% "ag-protobuf-scalapb-0-11-x-cupid" % versions.agoda.agprotobuf,
  "com.thesamet.scalapb" %% "scalapb-json4s"                   % versions.scalapbJson4s,
)
