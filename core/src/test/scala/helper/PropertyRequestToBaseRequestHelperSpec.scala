package helper

import api.request.FeatureFlag
import api.request.simulation.SimulationRequestData
import com.agoda.papi.enums.simulation.{SupplyEquitySimulationMode, SupplyEquitySimulationSide}
import com.agoda.papi.ypl.models.{YplMasterChannel => DFMasterChannel}
import com.agoda.platform.api.backpressure.RequestDegradationModes
import com.agoda.platform.pricing.models.utils.{DFTestDataBuilders, SFTestDataBuilders}
import models.SearchTypes
import models.enums.ReBookingActionType
import models.flow.FeatureDegradationContext
import models.pricing.enums.AlternativeRoomTypes
import models.starfruit.{ReBookingRequest, SupplyEquitySimulationParameters}
import models.whitelabel.RegulationFeatureEnabledSetting
import org.specs2.mock.Mockito
import org.specs2.mutable.Specification

class PropertyRequestToBaseRequestHelperSpec extends Specification with DFTestDataBuilders with Mockito {

  "Get Alternative Room Type Correctly" should {
    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.RetailSwap), true) should be(
      AlternativeRoomTypes.RetailSwap)
    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.APSRate), true) should be(
      AlternativeRoomTypes.APSRate)
    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.CrossSell), true) should be(
      AlternativeRoomTypes.CrossSale)
    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.ChildAge), true) should be(
      AlternativeRoomTypes.ChildAgeSwap)
    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.APSPeek), true) should be(
      AlternativeRoomTypes.NotRequire)
    PropertyRequestToBaseRequestHelper.getAlternativeType(Nil, true) should be(AlternativeRoomTypes.NotRequire)

    // Priority
    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.ChildAge, FeatureFlag.RetailSwap),
                                                          true) should be(AlternativeRoomTypes.RetailSwap)
    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.CrossSell, FeatureFlag.RetailSwap),
                                                          true) should be(AlternativeRoomTypes.CrossSale)
    PropertyRequestToBaseRequestHelper.getAlternativeType(
      List(FeatureFlag.RetailSwap, FeatureFlag.APSRate, FeatureFlag.ChildAge, FeatureFlag.CrossSell),
      true) should be(AlternativeRoomTypes.CrossSale)

    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.RetailSwap), false) should be(
      AlternativeRoomTypes.NotRequire)
    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.APSRate), false) should be(
      AlternativeRoomTypes.NotRequire)
    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.CrossSell), false) should be(
      AlternativeRoomTypes.NotRequire)
    PropertyRequestToBaseRequestHelper.getAlternativeType(List(FeatureFlag.ChildAge), false) should be(
      AlternativeRoomTypes.NotRequire)
  }

  "toBaseRequest" should {
    "convert base request from propertySearchRequest only" in {
      val mockPropertySearchRequest = SFTestDataBuilders.aValidPropertySearchRequest.build
      val res = PropertyRequestToBaseRequestHelper.toBaseRequest(mockPropertySearchRequest, aValidWhitelabelSetting)
      res.searchId should_== mockPropertySearchRequest.context.clientInfo.searchId
      res.searchType should_== SearchTypes.HotelSearch
      res.checkIn should_== mockPropertySearchRequest.pricing.checkIn
      res.checkOut should_== mockPropertySearchRequest.pricing.checkout
      res.bookingDate should_== mockPropertySearchRequest.pricing.bookingDate
      res.currency should_== mockPropertySearchRequest.pricing.currency
      res.occ.adults should_== mockPropertySearchRequest.pricing.occupancy.adults
      res.occ.children should_== mockPropertySearchRequest.pricing.occupancy.children
      res.occ.childrenAges should_== mockPropertySearchRequest.pricing.occupancy.childAges.flatten
      res.supplierIds should_== mockPropertySearchRequest.pricing.filters.suppliers.toSet
      res.hotels should_== mockPropertySearchRequest.propertyIds
      res.whitelabelSetting should_== aValidWhitelabelSetting.build

      // additional
      res.simulationRequestData should_== None
      res.agEnv should_== None
      res.regulationFeatureEnabledSetting should_== None
      res.featureDegradationContext should_== FeatureDegradationContext.default
      res.channels should_== Set(DFMasterChannel.RTL)
      res.supplyEquitySimulationMode should_== None
      res.supplyEquitySimulationParameters should_== None
      res.supplyEquitySimulationSide should_== None
      res.agCorrelationId should_=== None
      res.agAnalyticsSessionId should_=== None
      res.agOriginState should_=== None
    }

    "convert base request from propertySearchRequest with additional parameters" in {
      val mockPropertySearchRequest = SFTestDataBuilders.aValidPropertySearchRequest.build
      val mockSimulationRequestData = SimulationRequestData(None)
      val mockAgEnv = "agEnv"
      val mockRegulationFeatureEnabledSetting = RegulationFeatureEnabledSetting()
      val mockFeatureDegradationContext = FeatureDegradationContext(RequestDegradationModes.Low)
      val mockChannels = Set(DFMasterChannel.APS)
      val mockSupplyEquitySimulationMode = SupplyEquitySimulationMode.WithSimulation
      val mockSupplyEquitySimulationParameters: SupplyEquitySimulationParameters =
        SupplyEquitySimulationParameters("context")
      val mockSupplyEquitySimulationSide = SupplyEquitySimulationSide.B
      val mockAgCorrelationId = "agCorrelationId"
      val mockAgAnalyticsSessionId = "agAnalyticsSessionId"
      val mockAgOriginState = "agOriginState"
      val res = PropertyRequestToBaseRequestHelper.toBaseRequest(
        mockPropertySearchRequest,
        aValidWhitelabelSetting,
        simulationRequestData = Some(mockSimulationRequestData),
        agEnv = Some(mockAgEnv),
        regulationFeatureEnabledSetting = Some(mockRegulationFeatureEnabledSetting),
        featureDegradationContext = mockFeatureDegradationContext,
        channels = mockChannels,
        supplyEquitySimulationMode = Some(mockSupplyEquitySimulationMode),
        supplyEquitySimulationParameters = Some(mockSupplyEquitySimulationParameters),
        supplyEquitySimulationSide = Some(mockSupplyEquitySimulationSide),
        agCorrelationId = Some(mockAgCorrelationId),
        agAnalyticsSessionId = Some(mockAgAnalyticsSessionId),
        agOriginState = Some(mockAgOriginState),
      )
      res.searchId should_== mockPropertySearchRequest.context.clientInfo.searchId
      res.searchType should_== SearchTypes.HotelSearch
      res.checkIn should_== mockPropertySearchRequest.pricing.checkIn
      res.checkOut should_== mockPropertySearchRequest.pricing.checkout
      res.bookingDate should_== mockPropertySearchRequest.pricing.bookingDate
      res.currency should_== mockPropertySearchRequest.pricing.currency
      res.occ.adults should_== mockPropertySearchRequest.pricing.occupancy.adults
      res.occ.children should_== mockPropertySearchRequest.pricing.occupancy.children
      res.occ.childrenAges should_== mockPropertySearchRequest.pricing.occupancy.childAges.flatten
      res.supplierIds should_== mockPropertySearchRequest.pricing.filters.suppliers.toSet
      res.hotels should_== mockPropertySearchRequest.propertyIds
      res.whitelabelSetting should_== aValidWhitelabelSetting.build
      // additional
      res.simulationRequestData should_== Some(mockSimulationRequestData)
      res.agEnv should_== Some(mockAgEnv)
      res.regulationFeatureEnabledSetting should_== Some(mockRegulationFeatureEnabledSetting)
      res.featureDegradationContext should_== mockFeatureDegradationContext
      res.channels should_== mockChannels
      res.supplyEquitySimulationMode should_== Some(mockSupplyEquitySimulationMode)
      res.supplyEquitySimulationParameters should_== Some(mockSupplyEquitySimulationParameters)
      res.supplyEquitySimulationSide should_== Some(mockSupplyEquitySimulationSide)
      res.agCorrelationId should_=== Some(mockAgCorrelationId)
      res.agAnalyticsSessionId should_=== Some(mockAgAnalyticsSessionId)
      res.agOriginState should_=== Some(mockAgOriginState)
    }
  }

  "mapReBookingRequest" should {
    "V3 old request" in {
      val rebookingRequest = ReBookingRequest(
        roomTypeId = 1,
        masterRoomTypeId = Some(2),
        customerPaidPrice = 0,
        originalNetIn = None,
        originalCashback = Some(5.0),
        originalPromoAmount = Some(10.0),
        originalUsdToRequestExchangeRate = Some(1.25),
        originalSellInUsd = Some(90.0),
        originalSellIn = None,
        originalCashbackAmount = None,
        actionType = None,
      )

      val result = PropertyRequestToBaseRequestHelper.mapReBookingRequest(rebookingRequest)

      result.roomTypeId should_== 1
      result.masterRoomTypeId should_== Some(2)
      result.customerPaidPrice should_== 0
      result.originalNetIn should_== None
      result.originalSellIn should_== Some(90.0)
      result.cashbackAmount should_== Some(5.0)
      result.promoAmount should_== Some(10.0)
      result.originalUsdToRequestExchangeRate should_== Some(1.25)
      result.actionType should_== ReBookingActionType.MatchUSD
    }

    "V3 new request for match usd" in {
      val rebookingRequest = ReBookingRequest(
        roomTypeId = 1,
        masterRoomTypeId = Some(2),
        customerPaidPrice = 0,
        originalNetIn = None,
        originalCashback = None,
        originalPromoAmount = Some(10.0),
        originalUsdToRequestExchangeRate = Some(1.25),
        originalSellInUsd = None,
        originalSellIn = Some(90.0),
        originalCashbackAmount = Some(5.0),
        actionType = Some(ReBookingActionType.MatchUSD),
      )

      val result = PropertyRequestToBaseRequestHelper.mapReBookingRequest(rebookingRequest)

      result.roomTypeId should_== 1
      result.masterRoomTypeId should_== Some(2)
      result.customerPaidPrice should_== 0
      result.originalNetIn should_== None
      result.originalSellIn should_== Some(90.0)
      result.cashbackAmount should_== Some(5.0)
      result.promoAmount should_== Some(10.0)
      result.originalUsdToRequestExchangeRate should_== Some(1.25)
      result.actionType should_== ReBookingActionType.MatchUSD
    }

    "V3 new request for match local" in {
      val rebookingRequest = ReBookingRequest(
        roomTypeId = 1,
        masterRoomTypeId = Some(2),
        customerPaidPrice = 0,
        originalNetIn = None,
        originalCashback = None,
        originalPromoAmount = Some(10.0),
        originalUsdToRequestExchangeRate = Some(1.25),
        originalSellInUsd = None,
        originalSellIn = Some(90.0),
        originalCashbackAmount = Some(5.0),
        actionType = Some(ReBookingActionType.MatchLocal),
      )

      val result = PropertyRequestToBaseRequestHelper.mapReBookingRequest(rebookingRequest)

      result.roomTypeId should_== 1
      result.masterRoomTypeId should_== Some(2)
      result.customerPaidPrice should_== 0
      result.originalNetIn should_== None
      result.originalSellIn should_== Some(90.0)
      result.cashbackAmount should_== Some(5.0)
      result.promoAmount should_== Some(10.0)
      result.originalUsdToRequestExchangeRate should_== Some(1.25)
      result.actionType should_== ReBookingActionType.MatchLocal
    }

    "V3 new request before cleanup old fields (set the value for old and new fields)" in {
      val rebookingRequest = ReBookingRequest(
        roomTypeId = 1,
        masterRoomTypeId = Some(2),
        customerPaidPrice = 0,
        originalNetIn = None,
        originalCashback = Some(5.0),
        originalPromoAmount = Some(10.0),
        originalUsdToRequestExchangeRate = Some(1.25),
        originalSellInUsd = Some(90.0),
        originalSellIn = Some(90.0),
        originalCashbackAmount = Some(5.0),
        actionType = Some(ReBookingActionType.MatchUSD),
      )

      val result = PropertyRequestToBaseRequestHelper.mapReBookingRequest(rebookingRequest)

      result.roomTypeId should_== 1
      result.masterRoomTypeId should_== Some(2)
      result.customerPaidPrice should_== 0
      result.originalNetIn should_== None
      result.originalSellIn should_== Some(90.0)
      result.cashbackAmount should_== Some(5.0)
      result.promoAmount should_== Some(10.0)
      result.originalUsdToRequestExchangeRate should_== Some(1.25)
      result.actionType should_== ReBookingActionType.MatchUSD
    }
  }
}
