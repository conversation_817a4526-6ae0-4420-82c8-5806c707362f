package services

import com.agoda.papi.enums.room.RateType
import com.agoda.papi.pricing.flow.{FlowContextBuilder, SoybeanAndDiscountStep}
import com.agoda.papi.pricing.pricecalculation.models.tax.CommonTaxBreakdown
import com.agoda.platform.pricing.models.utils.DFTestDataBuilders
import com.agoda.utils.Implicits.DoubleRounding
import models.db.{Currency, ExchangeData}
import models.enums.ReBookingActionType
import models.flow.FlowContext
import models.pricing._
import org.mockito.Mockito.{times, verify}
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope
import org.specs2.specification.core.Fragments
import services.exchange.ExchangeDataService

class ReBookingServiceSpec extends SpecificationWithJUnit with DFTestDataBuilders {

  trait ReBookingTestScope extends Scope {
    val soybeanAndDiscountStepMock: SoybeanAndDiscountStep = mock[SoybeanAndDiscountStep]
    soybeanAndDiscountStepMock.doCxlRebookPromoAndCashbackMatching(any(),
                                                                   any(),
                                                                   any(),
                                                                   any(),
                                                                   any(),
                                                                   any(),
                                                                   any()) returns Some(
      aValidDiscounts.withDownlift(None).build)

    val reBookingService = new ReBookingServiceImpl with ExchangeDataService {
      def getExchangeData(code: Currency): Option[ExchangeData] = None
      def getExchangeRate(from: Currency, to: Currency): Option[ExchangeRate] = (from, to) match {
        case ("USD", "USD") => Some(aValidExchangeRate)
        case ("USD", "THB") =>
          Some(aValidExchangeRate.withLocal("USD").withRequest("THB").withToRequest(2.0).withNumReqDecimal(2))
        case ("THB", "USD") =>
          Some(aValidExchangeRate.withLocal("USD").withRequest("THB").withToRequest(0.5).withNumReqDecimal(2))
        case _ => None
      }
      def getUpliftExchangeRate(from: Currency, to: Currency): Option[ExchangeRate] = None

      override val soybeanAndDiscountStep: SoybeanAndDiscountStep = soybeanAndDiscountStepMock
    }
  }

  val customerBookedRoomTypeID = 1234L
  val otherRoomTypeID = 1111L
  val otherSupplierRoomTypeId = 9999L
  val masterRoomTypeId = 1112L

  val priceNetEx = 1000.0
  val commissionPercent = 0.25
  val taxPercent = 0.07
  val feePercent = 0.10
  val priceFee = priceNetEx * feePercent
  val priceTax = priceNetEx * taxPercent + priceFee * taxPercent
  val priceMargin = priceNetEx * commissionPercent
  val priceMarginIn = (priceNetEx + priceTax + priceFee) * commissionPercent
  val processingFee = priceMarginIn - priceMargin

  val mockPrice = aValidPrice
    .withNetExclusive(priceNetEx)
    .withTax(priceTax)
    .withFee(priceFee)
    .withMargin(priceMargin)
    .withProcessingFee(processingFee)
    .withDownliftAmount(0.0)
    .withDownliftExAmount(0.0)
    .withDownliftPercent(0.0)

  val mockCurrency = "USD"
  val mockRoom = aValidRoom.withPrice(mockPrice).withRateType(RateType.NetExclusive).withCurrency(mockCurrency)

  val mockRooms = List[Room](
    mockRoom.withRoomTypeId(customerBookedRoomTypeID),
    mockRoom.withRoomTypeId(otherRoomTypeID),
    mockRoom.withRoomTypeId(otherSupplierRoomTypeId).withMasterRoomId(masterRoomTypeId),
  )
  val mockHotel = aValidHotel.withRooms(mockRooms)
  val mockReBookingRequest =
    aValidReBookingRequestV2.withRoomTypeId(customerBookedRoomTypeID).withCustomerPaidPrice(1500.0)

  implicit val ctx: FlowContext = aValidFlowContext

  "ReBookingService" should {
    "filter only customer booked room type ID" in new ReBookingTestScope {
      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest,
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.roomTypeId should_== (customerBookedRoomTypeID)
    }

    "filter only customer booked by masterRoomTypeID" in new ReBookingTestScope {
      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest.withMasterRoomTypeId(Some(masterRoomTypeId)),
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.roomTypeId should_== (otherSupplierRoomTypeId)
    }

    "filter only customer booked by masterRoomTypeID 0" in new ReBookingTestScope {
      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest.withMasterRoomTypeId(Some(0)),
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.roomTypeId should_== (customerBookedRoomTypeID)
    }

    "filter only customer booked by masterRoomTypeID None" in new ReBookingTestScope {
      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest.withMasterRoomTypeId(None),
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.roomTypeId should_== (customerBookedRoomTypeID)
    }
    "return room sell In with the same as customer paid price: 1471.25 USD. Case where Booked Price == current Price, Downlift Amount = 0" in new ReBookingTestScope {
      val customerPaidPrice = 1471.25
      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest.withCustomerPaidPrice(mockRoom.sellIn()),
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.sellIn().roundAt(2) should_== (customerPaidPrice)
      resultRoom.downliftAmount.roundAt(2) should_== (0.0)
      (resultRoom.margin + resultRoom.pf + resultRoom.downliftAmount)
        .roundAt(2) should_== (mockRoom.margin + mockRoom.pf)
    }

    "return room sell In with the same as customer paid price: 1200.0 USD. Case where Booked Price < current Price, Downlift Amount > 0" in new ReBookingTestScope {
      val customerPaidPrice = 1200.0
      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest.withCustomerPaidPrice(customerPaidPrice),
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.sellIn().roundAt(2) should_== (customerPaidPrice)
      resultRoom.downliftAmount.roundAt(2) should_== (271.25)
      (resultRoom.margin + resultRoom.pf + resultRoom.downliftAmount)
        .roundAt(2) should_== (mockRoom.margin + mockRoom.pf)
    }

    "return room sell In with the same as customer paid price: 1800.0 USD. Case where Booked Price > current Price, Downlift Amount < 0" in new ReBookingTestScope {
      val customerPaidPrice = 1800.0
      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest.withCustomerPaidPrice(customerPaidPrice),
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.sellIn().roundAt(2) should_== (customerPaidPrice)
      resultRoom.downliftAmount.roundAt(2) should_== (-328.75)
      (resultRoom.margin + resultRoom.pf + resultRoom.downliftAmount)
        .roundAt(2) should_== (mockRoom.margin + mockRoom.pf)
    }

    "return room sell In with the same as customer paid price: 1800.0 THB. Case where request/pay currency is not hotel local currency " in new ReBookingTestScope {
      val customerPaidPrice = 1800.0
      val customerPaidCurrency = "THB"
      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest.withCustomerPaidPrice(customerPaidPrice),
        paymentCurrency = customerPaidCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )

      val exchange = reBookingService.getExchangeRate(customerPaidCurrency, mockCurrency).get
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.sellIn().roundAt(2) should_== (exchange.toReqRate(customerPaidPrice))
      resultRoom.downliftAmount.roundAt(2) should_== (571.25)
      (resultRoom.margin + resultRoom.pf + resultRoom.downliftAmount)
        .roundAt(2) should_== (mockRoom.margin + mockRoom.pf)
    }

    val priceTaxAmount = 100.0
    val mockTaxAmountBreakdown =
      aValidTaxBreakdown.withAmount(priceTaxAmount).withPercentage(0.0).withIsFee(false).withTypeId(1)

    "return room sell In with the same as customer paid price. Case where room with one TaxAsAmount" in new ReBookingTestScope {
      val customerPaidPrice = 1200.0
      val mockPrice = aValidPrice
        .withNetExclusive(priceNetEx)
        .withTax(priceTaxAmount)
        .withTaxBreakdown(mockTaxAmountBreakdown)
        .withFee(0.0)
        .withMargin(priceMargin)
        .withProcessingFee(0.0)
        .withDownliftAmount(0.0)
        .withDownliftExAmount(0.0)
        .withDownliftPercent(0.0)

      val mockRoom = aValidRoom.withPrice(mockPrice).withRateType(RateType.NetExclusive).withCurrency(mockCurrency)
      val mockRooms = List[Room](mockRoom.withRoomTypeId(customerBookedRoomTypeID))
      val mockHotel = aValidHotel.withRooms(mockRooms)

      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest.withCustomerPaidPrice(customerPaidPrice),
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.sellIn().roundAt(2) should_== (customerPaidPrice)
      resultRoom.downliftAmount.roundAt(2) should_== (150.0)
      resultRoom.tax.roundAt(2) should_== (priceTaxAmount)
      (resultRoom.margin + resultRoom.pf + resultRoom.downliftAmount)
        .roundAt(2) should_== (mockRoom.margin + mockRoom.pf)
    }

    "return room sell In with the same as customer paid price. Case where room with TaxAsPercent and taxAsAmount" in new ReBookingTestScope {
      val customerPaidPrice = 1200.0
      val mockTaxPercentBreakdown =
        aValidTaxBreakdown.withAmount(priceTaxAmount).withPercentage(taxPercent * 100.0).withIsFee(false).withTypeId(1)

      val priceFee = 0
      val priceTax = priceNetEx * taxPercent + priceFee * taxPercent + priceTaxAmount
      val priceMargin = priceNetEx * commissionPercent
      val priceMarginIn = (priceNetEx + priceTax - priceTaxAmount + priceFee) * commissionPercent
      val processingFee = priceMarginIn - priceMargin

      val mockPrice = aValidPrice
        .withNetExclusive(priceNetEx)
        .withTax(priceTax)
        .withFee(priceFee)
        .withMargin(priceMargin)
        .withProcessingFee(processingFee)
        .withTaxBreakdown(List[CommonTaxBreakdown](mockTaxAmountBreakdown, mockTaxPercentBreakdown))
        .withDownliftAmount(0.0)
        .withDownliftExAmount(0.0)
        .withDownliftPercent(0.0)

      val mockRoom = aValidRoom.withPrice(mockPrice).withRateType(RateType.NetExclusive).withCurrency(mockCurrency)
      val mockRooms = List[Room](mockRoom.withRoomTypeId(customerBookedRoomTypeID))
      val mockHotel = aValidHotel.withRooms(mockRooms)

      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest.withCustomerPaidPrice(customerPaidPrice),
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.sellIn().roundAt(2) should_== (customerPaidPrice)
      resultRoom.downliftAmount.roundAt(2) should_== (237.5)
      resultRoom.tax.roundAt(2) should_== (priceTax)
      (resultRoom.margin + resultRoom.pf + resultRoom.downliftAmount)
        .roundAt(2) should_== (mockRoom.margin + mockRoom.pf)
    }

    "return room OptimizationGain = OldNetIn - NewNetIn" in new ReBookingTestScope {
      val customerPaidPrice = 1471.25
      val originalNetIn = 1000.0
      val newNetIn = priceNetEx + priceTax + priceFee
      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest =
          mockReBookingRequest.withCustomerPaidPrice(mockRoom.sellIn()).withOriginalNetIn(Some(originalNetIn)),
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )
      result.rooms.size should_== (1)
      val resultRoom = result.rooms.head
      resultRoom.sellIn().roundAt(2) should_== (customerPaidPrice)
      resultRoom.netIn.roundAt(2) should_== (newNetIn)
      resultRoom.prices.head.optimizationGain.get.roundAt(2) should_== (originalNetIn - newNetIn)
    }

    "DFFinance should not be none" in new ReBookingTestScope {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val result = reBookingService.applyDownliftForReBooking(
        hotel = mockHotel,
        reBookingRequest = mockReBookingRequest.withCustomerPaidPrice(mockRoom.sellIn()),
        paymentCurrency = mockCurrency,
        bookedRoomQuantity = 1,
        bookedLengthOfStay = 1,
      )(ctx)
      result.rooms.map(_.dfFinanceByCurrency.local.roomPricesAfterPricing).head should_!= None
      result.rooms.size should_== (1)
    }
  }

  "setCxlRebookToDFFinance" should new ReBookingTestScope {
    val mockReBookRequest = aValidReBookingRequestV3
      .withRoomTypeId(customerBookedRoomTypeID)
      .withCustomerPaidPrice(1500.0)
      .withOriginalSellIn(Some(100d))
    val mockPrecision = 2
    val exchangeRateTHBToUSD: ExchangeRate = ExchangeRate(local = "THB",
                                                          toUsd = 0.02811998234,
                                                          numLocalDecimal = mockPrecision,
                                                          numReqDecimal = mockPrecision,
                                                          numUsdDecimal = mockPrecision)
    val mockDFFinance1 = DFFinance.build(List(aValidPrice.withNetExclusive(200)), None)
    val convertedDffUsd1 = ConvertedCurrencyPrice(
      Some(exchangeRateTHBToUSD),
      dfFinance = mockDFFinance1,
    )(None).reqCurrencyDFFinance()

    val mockDFFinanceByCurrency = DFFinanceByCurrency(
      local = mockDFFinance1,
      request = None,
      usd = Option(convertedDffUsd1),
    )
    val mockRooms = List[Room](
      mockRoom.withDFFinanceByCurrency(mockDFFinanceByCurrency).withExchange(exchangeRateTHBToUSD),
    )
    val mockHotel = aValidHotel.withRooms(mockRooms)
    val baseRequestWithReBook = aValidBaseRequest.withCurrency("USD").withReBookingRequest(Some(mockReBookRequest.build))

    val baseRequestWithVariant = baseRequestWithReBook.build
    val ctxB = FlowContextBuilder(baseRequestWithVariant)
    val result = reBookingService.setCxlRebookToDFFinance(hotel = mockHotel)(ctxB)
    result.rooms.head.dfFinanceByCurrency.local.cancelReBookOriginalSellIn should_== Some(200d)
  }

  "doCxlRebookDiscountMatching" should {
    "doCxlRebookDiscountMatching should return correct Hotel" in new ReBookingTestScope {
      val exchangeRateTHBToUSD: ExchangeRate =
        ExchangeRate(local = "THB", toUsd = 0.02811998234, numLocalDecimal = 2, numReqDecimal = 2, numUsdDecimal = 2)

      val dfFinance = DFFinance.build(List(aValidPrice.withNetExclusive(200)), Some(aValidDiscounts.build))

      val mockDFFinanceByCurrency = DFFinanceByCurrency(
        local = dfFinance,
        request = None,
        usd = Some(
          ConvertedCurrencyPrice(
            Some(exchangeRateTHBToUSD),
            dfFinance = dfFinance,
          )(None).reqCurrencyDFFinance()),
      )

      val ctxB = FlowContextBuilder(
        aValidBaseRequest
          .withCurrency("THB")
          .withReBookingRequest(
            Some(
              aValidReBookingRequestV3
                .withRoomTypeId(customerBookedRoomTypeID)
                .withCustomerPaidPrice(1500.0)
                .withOriginalSellIn(Some(100d))
                .withCashback(Some(50d))
                .withPromoAmount(Some(10d))
                .build))
          .build)

      val result = reBookingService.doCxlRebookDiscountMatching(hotel = aValidHotel.withRooms(
        List[Room](
          mockRoom.withDFFinanceByCurrency(mockDFFinanceByCurrency).withExchange(exchangeRateTHBToUSD),
        )))(ctxB)

      verify(soybeanAndDiscountStepMock, times(1)).doCxlRebookPromoAndCashbackMatching(20.0d, 100.0d, 200.0d, 3, 1, 2,
                                                                                       450.0)
      result.rooms.head.dfFinanceByCurrency.local.discounts should_!= None
      result.rooms.head.dfFinanceByCurrency.local.cancelReBookOriginalSellIn should_== None
    }

    "doCxlRebookDiscountMatching should return same Hotel object" should {
      case class TestCase(originalPromo: Double,
                          originalCashback: Double,
                          originalSellIn: Option[Double],
                          expectSameHotel: Boolean)
      val testCases = Seq(
        TestCase(
          originalPromo = 0,
          originalCashback = 0,
          originalSellIn = Some(150),
          expectSameHotel = true,
        ),
        TestCase(
          originalPromo = 10,
          originalCashback = 0,
          originalSellIn = Some(150),
          expectSameHotel = false,
        ),
        TestCase(
          originalPromo = 0,
          originalCashback = 10,
          originalSellIn = Some(150),
          expectSameHotel = false,
        ),
        TestCase(
          originalPromo = 10,
          originalCashback = 10,
          originalSellIn = Some(150),
          expectSameHotel = false,
        ),
        TestCase(
          originalPromo = 10,
          originalCashback = 0,
          originalSellIn = Some(100),
          expectSameHotel = false,
        ),
      )
      Fragments.foreach(testCases) { testCase =>
        s"originalPromo=${testCase.originalPromo},originalSellIn=${testCase.originalSellIn},originalCashback=${testCase.originalCashback},expectSameHotel=${testCase.expectSameHotel}" in new ReBookingTestScope {
          val exchangeRateTHBToUSD: ExchangeRate =
            ExchangeRate(local = "THB", toUsd = 0.02811998234, numLocalDecimal = 2, numReqDecimal = 2, numUsdDecimal = 2)

          val dfFinance = DFFinance.build(List(aValidPrice.withNetExclusive(200)), Some(aValidDiscounts.build))

          val mockDFFinanceByCurrency = DFFinanceByCurrency(
            local = dfFinance,
            request = None,
            usd = Some(
              ConvertedCurrencyPrice(
                Some(exchangeRateTHBToUSD),
                dfFinance = dfFinance,
              )(None).reqCurrencyDFFinance()),
          )

          val ctxB = {
            val baseRequest = aValidBaseRequest
              .withCurrency("THB")
              .withReBookingRequest(
                Some(
                  aValidReBookingRequestV3
                    .withRoomTypeId(customerBookedRoomTypeID)
                    .withCustomerPaidPrice(1500.0)
                    .withOriginalSellIn(testCase.originalSellIn)
                    .withCashback(Some(testCase.originalCashback))
                    .withPromoAmount(Some(testCase.originalPromo))
                    .build))
              .build
            FlowContextBuilder(baseRequest)
          }

          val hotel = aValidHotel.withRooms(
            List[Room](
              mockRoom.withDFFinanceByCurrency(mockDFFinanceByCurrency).withExchange(exchangeRateTHBToUSD),
            ))

          val result = reBookingService.doCxlRebookDiscountMatching(hotel)(ctxB)

          if (!testCase.expectSameHotel) {
            verify(soybeanAndDiscountStepMock, times(1)).doCxlRebookPromoAndCashbackMatching(any(),
                                                                                             any(),
                                                                                             any(),
                                                                                             any(),
                                                                                             any(),
                                                                                             any(),
                                                                                             any())
            result.rooms.head.dfFinanceByCurrency.local.discounts should_!= None
            result.rooms.head.dfFinanceByCurrency.local.cancelReBookOriginalSellIn should_== None
          } else {
            verify(soybeanAndDiscountStepMock, times(0)).doCxlRebookPromoAndCashbackMatching(any(),
                                                                                             any(),
                                                                                             any(),
                                                                                             any(),
                                                                                             any(),
                                                                                             any(),
                                                                                             any())
            result should_== hotel.build
          }
        }
      }
    }

    "doCxlRebookDiscountMatching should handle useLocalCurrency logic correctly" should {
      case class TestCase(actionType: ReBookingActionType,
                          originalSellIn: Double,
                          originalPromo: Double,
                          originalCashback: Double,
                          expectedUseLocalCurrency: Boolean)

      val testCases = Seq(
        TestCase(
          actionType = ReBookingActionType.MatchLocal,
          originalSellIn = 150,
          originalPromo = 20,
          originalCashback = 10,
          expectedUseLocalCurrency = true,
        ),
        TestCase(
          actionType = ReBookingActionType.MatchUSD,
          originalSellIn = 100,
          originalPromo = 10,
          originalCashback = 5,
          expectedUseLocalCurrency = false,
        ),
      )

      Fragments.foreach(testCases) { testCase =>
        s"when actionType=${testCase.actionType}" in new ReBookingTestScope {
          val exchangeRateTHBToUSD: ExchangeRate =
            ExchangeRate(local = "THB", toUsd = 0.02811998234, numLocalDecimal = 2, numReqDecimal = 2, numUsdDecimal = 2)

          val dfFinance = DFFinance.build(List(aValidPrice.withNetExclusive(200)), Some(aValidDiscounts.build))

          val mockDFFinanceByCurrency = DFFinanceByCurrency(
            local = dfFinance,
            request = None,
            usd = Some(
              ConvertedCurrencyPrice(
                Some(exchangeRateTHBToUSD),
                dfFinance = dfFinance,
              )(None).reqCurrencyDFFinance()),
          )

          val baseRequest = aValidBaseRequest
            .withCurrency("THB")
            .withReBookingRequest(
              Some(
                aValidReBookingRequestV3
                  .withRoomTypeId(customerBookedRoomTypeID)
                  .withCustomerPaidPrice(1500.0)
                  .withOriginalSellIn(Some(testCase.originalSellIn))
                  .withCashback(Some(testCase.originalCashback))
                  .withPromoAmount(Some(testCase.originalPromo))
                  .withActionType(testCase.actionType)
                  .build))
            .build

          val ctxB = FlowContextBuilder(baseRequest)

          val hotel = aValidHotel.withRooms(
            List[Room](
              mockRoom.withDFFinanceByCurrency(mockDFFinanceByCurrency).withExchange(exchangeRateTHBToUSD),
            ))

          val result = reBookingService.doCxlRebookDiscountMatching(hotel)(ctxB)

          val expectedLocalPromo = testCase.originalPromo * (if (testCase.expectedUseLocalCurrency) 1 else 2)
          val expectedLocalSellIn = testCase.originalSellIn * (if (testCase.expectedUseLocalCurrency) 1 else 2)
          val expectedLocalCashback = testCase.originalCashback * (if (testCase.expectedUseLocalCurrency) 1 else 2)

          verify(soybeanAndDiscountStepMock, times(1)).doCxlRebookPromoAndCashbackMatching(
            expectedLocalPromo,
            expectedLocalCashback,
            expectedLocalSellIn,
            3,
            1,
            2,
            450.0,
          )

          result.rooms.head.dfFinanceByCurrency.local.discounts should_!= None
        }
      }
    }
  }
}
