package services

import com.agoda.papi.ypl.models.FeeWaiverFiltrationSettings
import com.agoda.platform.pricing.models.utils.SFTestDataBuilders.{aValidClientInfo, aValidContextRequest}
import com.agoda.platform.pricing.models.utils.{DFTestDataBuilders, SFTestDataBuilders}
import com.agoda.whitelabel.client.WhiteLabelClientMultiEnv
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.agoda.whitelabel.client.settings.Constants.WhiteLabelId
import generated.model.{
  ExternalSuppliersConfiguration => ExternalSuppliersConfig,
  SupplierConfigurationV4CommissionAndMarginOverride => CommissionAndMarginOverrideWL,
  _,
}
import models.db.SupplierId
import models.starfruit.PropertySearchRequest
import models.whitelabel._
import org.mockito.Mockito
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.specs2.mutable.SpecificationWithJUnit
import services.whitelabel.{WhiteLabelFeatureNames, WhiteLabelServiceImpl}

class WhitelabelServiceImplSpec extends SpecificationWithJUnit with ExperimentSupport with DFTestDataBuilders {
  val japanIcanToken = Some("japanIcanToken")
  val jtbToken = Some("jtbToken")
  val rurubuToken = Some("rurubuToken")
  val bessieToken = Some("bessieToken")
  val citiuat1token = Some("citiuat1token")
  val emptyToken = None
  val exception = new RuntimeException("CLIENT ERROR")
  implicit val request = SFTestDataBuilders.aValidPropertySearchRequest.build

  val supplierFilter = SupplierFilterV4(dmcList = Some(List(1, 2, 3)))
  val emptySupplierFilter = SupplierFilterV4(dmcList = Some(List.empty))
  val supplierConfigForAgoda = SupplierConfiguration(CommissionAndMarginOverride(true, true, true, true))
  val supplierConfigForNotAgoda = SupplierConfiguration(CommissionAndMarginOverride(false, false, false, true))

  val couponConfigForAgoda = Coupon(None)
  val couponConfigForNotAgoda = Coupon(Some(List(1, 2)))

  val exchangeRateConfigForAgoda = ExchangeRateConfiguration(false)
  val exchangeRateConfigNotForAgoda = ExchangeRateConfiguration(true)
  val inventoryTypes = List(PaymentInventoryType(inventoryType = Some(1), allowedPaymentChannels = Some(List(1, 2))))

  val mockWhitelabelClient = mock[WhiteLabelClientMultiEnv]
  mockWhitelabelClient.getWhiteLabelIdByToken(japanIcanToken, None) returns 3
  mockWhitelabelClient.getWhiteLabelIdByToken(rurubuToken, None) throws exception
  mockWhitelabelClient.getWhiteLabelIdByToken(emptyToken, None) returns WhiteLabelId.AGODA
  mockWhitelabelClient.getWhiteLabelIdByToken(bessieToken, None) returns 51
  mockWhitelabelClient.getWhiteLabelIdByToken(citiuat1token, None) returns 5101

  mockWhitelabelClient.getFeaturesById(3, None) returns getMockFeatureConfigurationForWhitelabel3
  mockWhitelabelClient.getFeaturesById(1, None) returns getMockFeatureConfigurationForWhitelabel1
  mockWhitelabelClient.getFeaturesById(51, None) returns getMockFeatureConfigurationForWhitelabel51
  mockWhitelabelClient.getFeaturesById(5101, None) returns getMockFeatureConfigurationForWhitelabel5101
  mockWhitelabelClient.getFeaturesById(2, None) returns getMockFeatureConfigurationForWhitelabel2
  mockWhitelabelClient.getFeaturesById(4, None) returns getMockFeatureConfigurationForWhitelabel4
  mockWhitelabelClient.getFeaturesById(401, None) returns getMockFeatureConfigurationForWhitelabel401
  mockWhitelabelClient.getFeaturesById(5, None) returns getMockFeatureConfigurationForWhitelabel5
  mockWhitelabelClient.getFeaturesById(6, None) returns getMockFeatureConfigurationForWhitelabel6

  Mockito.when(mockWhitelabelClient.isFeatureEnabled(any, any, any, any, any, any)).thenAnswer {
    new Answer[Boolean]() {
      def answer(invocation: InvocationOnMock) = invocation.getArguments.apply(2) != Some("GB")
    }
  }

  mockWhitelabelClient.isFeatureEnabled(WhiteLabelFeatureNames.coupons,
                                        japanIcanToken,
                                        Some("GB"),
                                        None,
                                        expName => true) returns true

  val wlSv = new WhiteLabelServiceImpl {
    override val wlClient: WhiteLabelClientMultiEnv = mockWhitelabelClient
  }

  Mockito.when(mockWhitelabelClient.isFeaturesEnabled(any, any, any, any, any, any)).thenAnswer {
    (invocation: InvocationOnMock) =>
      val features = invocation.getArguments.apply(0).asInstanceOf[List[String]]
      val whiteLabelKey = invocation.getArguments.apply(1).asInstanceOf[Option[String]]
      val notGBOrigin = invocation.getArguments.apply(2) != Some("GB")
      val JPOrigin = invocation.getArguments.apply(2) == Some("JP")
      features.collect {
        case WhiteLabelFeatureNames.enableHotelLocalCurrency =>
          WhiteLabelFeatureNames.enableHotelLocalCurrency -> JPOrigin
        case WhiteLabelFeatureNames.m150MaxTransparencyVersion =>
          WhiteLabelFeatureNames.m150MaxTransparencyVersion -> (whiteLabelKey == emptyToken)
        case WhiteLabelFeatureNames.m150BaselineTransparencyVersion =>
          WhiteLabelFeatureNames.m150BaselineTransparencyVersion -> (whiteLabelKey == emptyToken)
        case WhiteLabelFeatureNames.regulationPriceViewOptionDestinationOverride =>
          WhiteLabelFeatureNames.regulationPriceViewOptionDestinationOverride -> (whiteLabelKey == emptyToken)
        case WhiteLabelFeatureNames.cashbackRedemption =>
          WhiteLabelFeatureNames.cashbackRedemption -> (whiteLabelKey == emptyToken)
        case k if k != WhiteLabelFeatureNames.filterOutAgencyPaymentModel || whiteLabelKey == bessieToken =>
          k -> notGBOrigin
      }.toMap
  }

  Mockito.when(mockWhitelabelClient.isFeatureEnabledWithState(any, any, any, any, any, any, any)).thenAnswer {
    (invocation: InvocationOnMock) =>
      val origin = invocation.getArguments.apply(2).asInstanceOf[Option[String]]
      val originState = invocation.getArguments.apply(3).asInstanceOf[Option[String]]

      (origin, originState) match {
        case (Some("US"), Some("CA")) => false
        case (Some("GB"), _) => false
        case _ => true
      }
  }

  mockWhitelabelClient.isFeaturesEnabled(wlSv.settingFeatures,
                                         japanIcanToken,
                                         Some("GB"),
                                         None,
                                         expName => true,
                                         None) returns
    Map(
      WhiteLabelFeatureNames.supplierFilter -> false,
      WhiteLabelFeatureNames.adjustCommissionFromHotelContract -> false,
      WhiteLabelFeatureNames.coupons -> true,
      WhiteLabelFeatureNames.currency -> false,
      WhiteLabelFeatureNames.customerSegmentValidation -> false,
      WhiteLabelFeatureNames.blockYCSPromotions -> false,
      WhiteLabelFeatureNames.externalSuppliers -> true,
      WhiteLabelFeatureNames.cashbackRedemption -> false,
      WhiteLabelFeatureNames.m150MaxTransparencyVersion -> false,
      WhiteLabelFeatureNames.m150BaselineTransparencyVersion -> false,
      WhiteLabelFeatureNames.regulationPriceViewOptionDestinationOverride -> false,
    )

  "WhiteLabelServiceImpl" should {
    "getSupportedSuppliersWithWLID correctly return false when there's no supplierFilter in the featuresEnabled Map" in {
      val result = wlSv.getSupportedSuppliersWithWLID(2, Map.empty[String, Boolean], None)(
        SFTestDataBuilders.aValidPropertySearchRequest)
      result should_== Set.empty[SupplierId]
    }
    "getSupportedSuppliersWithWLID correctly" in {
      val result = wlSv.getSupportedSuppliersWithWLID(2, Map(WhiteLabelFeatureNames.supplierFilter -> true), None)(
        SFTestDataBuilders.aValidPropertySearchRequest)
      result should_== Set(27901, 27902)
    }
    "getSupplierConfigurationWithWLID correctly when whitelabelId = 3 doesn't return any supplierConfiguration and no adjustCommissionFromHotelContract in featureEnable" in {
      val actual = wlSv.getSupplierConfigurationWithWLID(3, Map.empty[String, Boolean], None)
      val expected = SupplierConfiguration(
        CommissionAndMarginOverride(
          false,
          false,
          false,
          false,
        ),
      )
      actual should_== expected
    }
    "getSupplierConfigurationWithWLID correctly when whitelabelId = 3 doesn't return any supplierConfiguration but there's adjustCommissionFromHotelContract in featureEnable" in {
      val actual =
        wlSv.getSupplierConfigurationWithWLID(3,
                                              Map(WhiteLabelFeatureNames.adjustCommissionFromHotelContract -> true),
                                              None)
      val expected = SupplierConfiguration(
        CommissionAndMarginOverride(
          false,
          false,
          false,
          true,
        ),
      )
      actual should_== expected
    }
    "getSupplierConfigurationWithWLID correctly when whitelabelId = 4 doesn't return any supplierConfiguration but there's adjustCommissionFromHotelContract in featureEnable" in {
      val actual =
        wlSv.getSupplierConfigurationWithWLID(4,
                                              Map(WhiteLabelFeatureNames.adjustCommissionFromHotelContract -> true),
                                              None)
      val expected = SupplierConfiguration(
        CommissionAndMarginOverride(
          true,
          false,
          true,
          true,
        ),
      )
      actual should_== expected
    }
    "getSupplierConfigurationWithWLID correctly when whitelabelId = 5 doesn't return any supplierConfiguration but there's adjustCommissionFromHotelContract in featureEnable" in {
      val actual =
        wlSv.getSupplierConfigurationWithWLID(5,
                                              Map(WhiteLabelFeatureNames.adjustCommissionFromHotelContract -> true),
                                              None)
      val expected = SupplierConfiguration(
        CommissionAndMarginOverride(
          true,
          true,
          false,
          true,
        ),
      )
      actual should_== expected
    }
    "getSupplierConfigurationWithWLID correctly when whitelabelId = 6 doesn't return any supplierConfiguration but there's adjustCommissionFromHotelContract in featureEnable" in {
      val actual =
        wlSv.getSupplierConfigurationWithWLID(6,
                                              Map(WhiteLabelFeatureNames.adjustCommissionFromHotelContract -> true),
                                              None)
      val expected = SupplierConfiguration(
        CommissionAndMarginOverride(
          false,
          true,
          true,
          true,
        ),
      )
      actual should_== expected
    }
    "getSupplierConfigurationWithWLID correctly when whitelabelId = 2 doesn't return any supplierConfiguration but there's adjustCommissionFromHotelContract in featureEnable" in {
      val actual =
        wlSv.getSupplierConfigurationWithWLID(2,
                                              Map(WhiteLabelFeatureNames.adjustCommissionFromHotelContract -> true),
                                              None)
      val expected = SupplierConfiguration(
        CommissionAndMarginOverride(
          true,
          true,
          true,
          true,
        ),
      )
      actual should_== expected
    }
    "getExactMatchOccupancy correctly" in {
      val japanicanVal = wlSv.getWhitelabelSetting(japanIcanToken, None).map(_.exactMatchOccupancy)
      val agodaVal = wlSv.getWhitelabelSetting(emptyToken, None).map(_.exactMatchOccupancy)
      val bessieVal = wlSv.getWhitelabelSetting(bessieToken, None).map(_.exactMatchOccupancy)

      japanicanVal should_== Right(false)
      agodaVal should_== Right(false)
      bessieVal should_== Right(true)
    }
    "getWhitelabelSetting correctly return setting when valid token" in {
      val actual = wlSv.getWhitelabelSetting(japanIcanToken, None)
      val expected = Right(
        WhitelabelSetting(
          3,
          Map(29014 -> false),
          Set(1, 2, 3),
          supplierConfigForNotAgoda,
          Some(LoyaltyProgram(None, None, None, None)),
          couponConfigForNotAgoda,
          exchangeRateConfigNotForAgoda,
          Nil,
          List(1, 2),
          false,
          true,
          Seq.empty,
          true,
          isSellingDifferentSuppliersForJtbEnabled = true,
          enabledCashbackRedemption = true,
          enableM150MaxTransparency = true,
          enableM150BaselineTransparency = true,
          priceViewDestinationOverrideConfigs = List(),
          blockBNPLForJapanOutboundEnabled = true,
          isRurubuWl = true,
          isJapanicanWl = true,
          controlDirectConnectSupplyEnabled = true,
          mainSupplier = 29014,
          removeExcludedSurchargeFromAllInclusive = true,
          isMixAndSaveEnabled = true,
          disableRoomOnArrival = true,
          blockPromotionsPerSupplier = true,
        ))

      actual should_== expected
    }

    "getWhitelabelSetting correctly return setting when valid token and GB region" in {

      val actual = wlSv.getWhitelabelSetting(japanIcanToken, None)(
        SFTestDataBuilders.aValidPropertySearchRequest
          .withContextRequest(
            aValidContextRequest.withClientInfo(
              aValidClientInfo.withOrigin("GB"),
            ),
          )
          .withWhiteLabelKey(japanIcanToken))
      val expected = Right(
        WhitelabelSetting(
          3,
          Map(29014 -> false),
          Set(),
          SupplierConfiguration(CommissionAndMarginOverride(false, false, false, false)),
          Some(LoyaltyProgram(None, None, None, None)),
          couponConfigForAgoda,
          exchangeRateConfigForAgoda,
          List(),
          List(1, 2),
          false,
          false,
          Seq.empty,
          false,
          isSellingDifferentSuppliersForJtbEnabled = false,
          enabledCashbackRedemption = false,
          mainSupplier = 29014,
        ))

      actual should_== expected
    }

    "getWhitelabelSetting correctly return Agoda setting when empty token" in {

      val expectedPaymentInventoryTypeConf = List(
        PaymentInventoryTypeConfiguration(
          1,
          List(1, 2),
        ))
      val actual = wlSv.getWhitelabelSetting(emptyToken, None)
      val expected = Right(
        WhitelabelSetting(
          WhiteLabelId.AGODA,
          Map(29014 -> false),
          Set.empty,
          supplierConfigForAgoda,
          Some(LoyaltyProgram(None, None, None, None)),
          couponConfigForAgoda,
          exchangeRateConfigForAgoda,
          Nil,
          Nil,
          false,
          true,
          Seq.empty,
          true,
          paymentInventoryTypeConfiguration = expectedPaymentInventoryTypeConf,
          enabledCashbackRedemption = true,
          enableM150MaxTransparency = true,
          enableM150BaselineTransparency = true,
          isSellingDifferentSuppliersForJtbEnabled = true,
          externalSuppliers = List.empty,
          priceViewDestinationOverrideConfigs =
            List(PriceViewDestinationOverrideConfigs(Some(1), Some(1), Some(List("test")), Some("test"), None)),
          blockBNPLForJapanOutboundEnabled = true,
          feeWaiverFiltrationSettings = Some(
            FeeWaiverFiltrationSettings("desc",
                                        1,
                                        1,
                                        "leadTimeDuration",
                                        "checkInTime",
                                        "expiryDuration",
                                        "feeValue",
                                        List(1, 2))),
          isRurubuWl = true,
          isJapanicanWl = true,
          controlDirectConnectSupplyEnabled = true,
          removeExcludedSurchargeFromAllInclusive = true,
          isMixAndSaveEnabled = true,
          disableRoomOnArrival = true,
          blockPromotionsPerSupplier = true,
        ))

      actual should_== expected
    }

    "getWhitelabelSetting correctly return Japanican setting when valid token" in {

      val actual = wlSv.getWhitelabelSetting(japanIcanToken, None)(
        SFTestDataBuilders.aValidPropertySearchRequest.withWhiteLabelKey(japanIcanToken))
      val expected = Right(
        WhitelabelSetting(
          3,
          Map(29014 -> false),
          Set(1, 2, 3),
          supplierConfigForNotAgoda,
          Some(LoyaltyProgram(None, None, None, None)),
          couponConfigForNotAgoda,
          exchangeRateConfigNotForAgoda,
          Nil,
          List(1, 2),
          false,
          true,
          Seq.empty,
          true,
          isSellingDifferentSuppliersForJtbEnabled = true,
          enabledCashbackRedemption = false,
          blockBNPLForJapanOutboundEnabled = true,
          isRurubuWl = true,
          isJapanicanWl = true,
          controlDirectConnectSupplyEnabled = true,
          mainSupplier = 29014,
          removeExcludedSurchargeFromAllInclusive = true,
          isMixAndSaveEnabled = true,
          disableRoomOnArrival = true,
          blockPromotionsPerSupplier = true,
        ))

      actual should_== expected
    }

    "getWhitelabelSetting correctly return Bessie setting when empty token" in {

      val actual = wlSv.getWhitelabelSetting(bessieToken, None)(
        SFTestDataBuilders.aValidPropertySearchRequest.withWhiteLabelKey(bessieToken))
      val expected = Right(
        WhitelabelSetting(
          51,
          Map(29014 -> false),
          Set.empty,
          supplierConfigForAgoda,
          Some(LoyaltyProgram(None, None, None, None)),
          couponConfigForAgoda,
          exchangeRateConfigForAgoda,
          Nil,
          Nil,
          false,
          true,
          Seq(
            ExternalVipDisplayConfigs(tierId = Some(2),
                                      tierNameCms = Some(3),
                                      tierDescriptionCms = Some(1),
                                      benefitIds = Some(List(95, 26, 201, 241, 12, 261)))),
          true,
          filterOutAgencyPaymentModel = true,
          isSellingDifferentSuppliersForJtbEnabled = true,
          enabledCashbackRedemption = false,
          enablePublishPriceESS = true,
          exactMatchOccupancy = true,
          blockBNPLForJapanOutboundEnabled = true,
          isRurubuWl = true,
          isJapanicanWl = true,
          controlDirectConnectSupplyEnabled = true,
          removeExcludedSurchargeFromAllInclusive = true,
          isMixAndSaveEnabled = true,
          disableRoomOnArrival = true,
          blockPromotionsPerSupplier = true,
        ))

      actual should_== expected
    }

    "throw error correctly when client error" in {
      val actual = wlSv.getWhitelabelSetting(rurubuToken, None)(SFTestDataBuilders.aValidPropertySearchRequest)
      val expected = "Error during call Whitelabel service of token #rurubuToken by CLIENT ERROR"
      actual.left.get.getMessage should_== expected
    }

    "getRegulationFeaturesEnabledSetting correctly return setting for GB" in {
      val actual = wlSv.getRegulationFeaturesEnabledSetting(None, None)(
        SFTestDataBuilders.aValidPropertySearchRequest.withContextRequest(
          aValidContextRequest.withClientInfo(
            aValidClientInfo.withOrigin("GB"),
          ),
        ),
      )
      val expected = Right(
        RegulationFeatureEnabledSetting(
          isCorAllowed = false,
          isCCorAllowed = false,
          isPricePeekEnabled = false,
          isAllowExclusivePrice = false,
          isBreakfastUpsellEnabled = false,
          isClaimPromotionEnabled = false,
          isRemoveYCSPromotionEnabled = false,
          isOverrideOriginalTotalPrice = false,
          isJtbWlDynamicMapping = false,
          isConsolidatedDiscountUserOverridePriceDisplay = false,
        ))
      actual should_== expected
    }

    "getRegulationFeaturesEnabledSetting correctly return setting for TH" in {
      val actual = wlSv.getRegulationFeaturesEnabledSetting(None, None)(
        SFTestDataBuilders.aValidPropertySearchRequest.withContextRequest(
          aValidContextRequest.withClientInfo(
            aValidClientInfo.withOrigin("TH"),
          ),
        ),
      )
      val expected = Right(
        RegulationFeatureEnabledSetting(
          isCorAllowed = true,
          isCCorAllowed = true,
          isPricePeekEnabled = true,
          isAllowExclusivePrice = true,
          isBreakfastUpsellEnabled = true,
          isClaimPromotionEnabled = true,
          isShowPriceOfBreakfast = true,
          isRemoveYCSPromotionEnabled = true,
          isOverrideOriginalTotalPrice = true,
          isAutoApplyAllPromosEnabled = true,
          isRemovePulseBadgeTooltip = true,
          isAutoApplyPromoApplyFirst = true,
          isExternalLoyaltyBurnDisablePayLater = true,
          isExternalLoyaltyBurnFilterAgencyModelOut = true,
          isShowUpsellFeatureSetting = true,
          isJtbWlDynamicMapping = true,
          isDisabledFxi = true,
          isDisabledM150 = true,
          isConsolidatedDiscountUserOverridePriceDisplay = true,
          isDisplayVariableTax = true,
          isDsaLicenseBlockingDisabled = true,
          isShowExclusivePriceWithFeeEnabled = true,
          isDynamicDownliftEnabled = true,
          isAllowExclusivePriceWithOutState = true,
          isShowExclusivePriceWithFeesForDestination = true,
          isEnable30MinsHourlySlots = true,
          isValueTagEnabled = true,
        ))
      actual should_== expected
    }

    "getCommissionAndMarginOverrideSetting correctly return setting" in {
      val actual = wlSv.getCommissionAndMarginOverrideSetting(japanIcanToken)
      val expected = Right(
        (3,
         CommissionAndMarginOverride(isCommissionOverride = false,
                                     isCommissionAdjustment = false,
                                     isMarginAdjustment = false,
                                     isAdjustCommissionFromHotelContract = true)))
      actual should_== expected
    }

    "getCommissionAndMarginOverrideSetting correctly return exception from client" in {
      val actual = wlSv.getCommissionAndMarginOverrideSetting(rurubuToken)
      val expected = "Error during call Whitelabel service of token #rurubuToken by CLIENT ERROR"
      actual.left.get.getMessage should_== expected
    }

    "return default to false when getFeaturesEnabled return Empty Map" in {
      val wlSv = new WhiteLabelServiceImpl {
        override val wlClient: WhiteLabelClientMultiEnv = mockWhitelabelClient
        override def getFeaturesEnabled(features: List[String], agEnv: Option[String])(implicit
          request: PropertySearchRequest): Map[String, Boolean] = Map.empty
        override def getAllowExclusivePriceWithState(agEnv: Option[String], agOriginState: Option[String])(implicit
          request: PropertySearchRequest): Boolean = false
        override def getAllowExclusivePriceWithOutState(agEnv: Option[String])(implicit
          request: PropertySearchRequest): Boolean = false
      }

      val actual = wlSv.getRegulationFeaturesEnabledSetting(None, None)(SFTestDataBuilders.aValidPropertySearchRequest)

      val expected = Right(
        RegulationFeatureEnabledSetting(
          isCorAllowed = false,
          isCCorAllowed = false,
          isPricePeekEnabled = false,
        ))

      actual should_== expected
    }

    "getExternalSupplier correctly returned" in {
      val result = wlSv.getExternalSuppliers(4, None)
      result should_== List(332)
    }

    "getMainSupplier corectly returned" in {
      val result1 = wlSv.getMainSupplier(1, None)
      val result3 = wlSv.getMainSupplier(3, None)
      val result4 = wlSv.getMainSupplier(4, None)
      val result401 = wlSv.getMainSupplier(401, None)

      result1 should_== 0
      result3 should_== 29014
      result4 should_== 29014
      result401 should_== 99901
    }

    "getBlockedCountries correctly returned" in {
      val result = wlSv.getBlockedCountries(4, None)
      result should_== List("JP", "MY")
    }

    "isSellingExternalSuppliersForJtbEnabled should return false if feature map is empty" in {
      val result = wlSv.isSellingExternalSuppliersForJtbEnabled(Map.empty)
      result should_== false
    }

    "isSellingExternalSuppliersForJtbEnabled should return true if feature map contains feature" in {
      val result = wlSv.isSellingExternalSuppliersForJtbEnabled(Map(WhiteLabelFeatureNames.externalSuppliers -> true))
      result should_== true
    }

    "isRemoveExcludedSurchargeFromAllInclusiveEnabled should return false if feature map is empty" in {
      val result = wlSv.isRemoveExcludedSurchargeFromAllInclusiveEnabled(Map.empty)
      result should_== false
    }

    "isMixAndSaveEnabled should return true if feature map contains feature" in {
      val result = wlSv.isMixAndSaveEnabled(Map(WhiteLabelFeatureNames.mixAndSave -> true))
      result should_== true
    }

    "isMixAndSaveEnabled should return false if feature map is empty" in {
      val result = wlSv.isMixAndSaveEnabled(Map.empty)
      result should_== false
    }

    "isRemoveExcludedSurchargeFromAllInclusiveEnabled should return true if feature map contains feature" in {
      val result = wlSv.isRemoveExcludedSurchargeFromAllInclusiveEnabled(
        Map(WhiteLabelFeatureNames.removeExcludedSurchargeFromAllInclusive -> true))
      result should_== true
    }

    "blockBNPLForJapanOutbound should return false if feature map is empty" in {
      val result = wlSv.blockBNPLForJapanOutbound(Map.empty)
      result should_== false
    }

    "isRurubuWl should return false if feature map is empty" in {
      val result = wlSv.isRurubuWl(Map.empty)
      result should_== false
    }

    "isRurubuWl should return false if feature map is empty" in {
      val result = wlSv.isRurubuWl(Map(WhiteLabelFeatureNames.isRurubuWl -> true))
      result should_== true
    }

    "isJapanicanWl should return false if feature map is empty" in {
      val result = wlSv.isJapanicanWl(Map.empty)
      result should_== false
    }

    "isJapanicanWl should return false if feature map is empty" in {
      val result = wlSv.isJapanicanWl(Map(WhiteLabelFeatureNames.isJapanicanWl -> true))
      result should_== true
    }

    "getDirectConnectSupplierIds correctly returned" in {
      val result = wlSv.getDirectConnectSupplierIds(4, None)
      result should_== Set(29004, 27912, 29005, 27800, 29002, 29022, 27914, 29020, 29003)
    }

    "getAllowedLeadDaysToNonRefundable correctly returned" in {
      val result = wlSv.getAllowedLeadDaysToNonRefundable(4, None)
      result should_== 6
    }

    "getPromotionsBlockedSuppliers correctly returned" in {
      val result = wlSv.getPromotionsBlockedSuppliers(4, None)
      result should_== Set()
    }

    "blockBNPLForJapanOutbound should return true if feature map contains feature" in {
      val result = wlSv.blockBNPLForJapanOutbound(Map(WhiteLabelFeatureNames.blockBNPLForJapanOutbound -> true))
      result should_== true
    }

    "controlDirectConnectSupply should return false if feature map is empty" in {
      val result = wlSv.controlDirectConnectSupply(Map.empty)
      result should_== false
    }

    "controlDirectConnectSupply should return true if feature map contains feature" in {
      val result = wlSv.controlDirectConnectSupply(Map(WhiteLabelFeatureNames.controlDirectConnectSupply -> true))
      result should_== true
    }

    "isDisableRoomOnArrivalEnabled should return true if feature map contains feature" in {
      val result = wlSv.isDisableRoomOnArrivalEnabled(Map(WhiteLabelFeatureNames.disableRoomOnArrival -> true))
      result should_== true
    }

    "isDisableRoomOnArrivalEnabled should return false if feature map is empty" in {
      val result = wlSv.isDisableRoomOnArrivalEnabled(Map.empty)
      result should_== false
    }
    "blockPromotionsPerSupplier should return true if feature map contains feature" in {
      val result = wlSv.blockPromotionsPerSupplier(Map(WhiteLabelFeatureNames.blockPromotionsPerSupplier -> true))
      result should_== true
    }

    "blockPromotionsPerSupplier should return false if feature map is empty" in {
      val result = wlSv.blockPromotionsPerSupplier(Map.empty)
      result should_== false
    }

    "enabledCashbackRedemption return false when featureFlags doesn't contain CashbackRedemption" in {
      val mockWlClient = mock[WhiteLabelClientMultiEnv]
      val wlService = new WhiteLabelServiceImpl {
        override val wlClient: WhiteLabelClientMultiEnv = mockWlClient
      }

      Mockito.when(mockWlClient.isFeaturesEnabled(any, any, any, any, any, any)).thenAnswer {
        (invocation: InvocationOnMock) =>
          val features = invocation.getArguments.apply(0).asInstanceOf[List[String]]

          features.filter(_ != WhiteLabelFeatureNames.cashbackRedemption).map(_ -> false).toMap
      }
      mockWlClient.getFeaturesById(any, any).returns(new FeaturesConfiguration())

      val actual = wlService.getWhitelabelSetting(emptyToken, None)(
        SFTestDataBuilders.aValidPropertySearchRequest.withWhiteLabelKey(emptyToken).build)

      actual.map(_.enabledCashbackRedemption) should_== Right(false)
    }

    "m150MaxTransparencyVersion return false when featureFlags doesn't contain M150MaxTransparencyVersion" in {
      val mockWlClient = mock[WhiteLabelClientMultiEnv]
      val wlService = new WhiteLabelServiceImpl {
        override val wlClient: WhiteLabelClientMultiEnv = mockWlClient
      }

      Mockito.when(mockWlClient.isFeaturesEnabled(any, any, any, any, any, any)).thenAnswer {
        (invocation: InvocationOnMock) =>
          val features = invocation.getArguments.apply(0).asInstanceOf[List[String]]

          features.filter(_ != WhiteLabelFeatureNames.m150MaxTransparencyVersion).map(_ -> false).toMap
      }
      mockWlClient.getFeaturesById(any, any).returns(new FeaturesConfiguration())

      val actual = wlService.getWhitelabelSetting(emptyToken, None)(
        SFTestDataBuilders.aValidPropertySearchRequest.withWhiteLabelKey(emptyToken).build)

      actual.map(_.enableM150MaxTransparency) should_== Right(false)
    }

    "m150BaselineTransparencyVersion return false when featureFlags doesn't contain M150BaselineTransparencyVersion" in {
      val mockWlClient = mock[WhiteLabelClientMultiEnv]
      val wlService = new WhiteLabelServiceImpl {
        override val wlClient: WhiteLabelClientMultiEnv = mockWlClient
      }

      Mockito.when(mockWlClient.isFeaturesEnabled(any, any, any, any, any, any)).thenAnswer {
        (invocation: InvocationOnMock) =>
          val features = invocation.getArguments.apply(0).asInstanceOf[List[String]]

          features.filter(_ != WhiteLabelFeatureNames.m150BaselineTransparencyVersion).map(_ -> false).toMap
      }
      mockWlClient.getFeaturesById(any, any).returns(new FeaturesConfiguration())

      val actual = wlService.getWhitelabelSetting(emptyToken, None)(
        SFTestDataBuilders.aValidPropertySearchRequest.withWhiteLabelKey(emptyToken).build)

      actual.map(_.enableM150BaselineTransparency) should_== Right(false)
    }

    "getPriceViewOptionsDestinationOverride return None when featureFlags doesn't contain PriceViewOptionsDestinationOverrideInner" in {
      val mockWlClient = mock[WhiteLabelClientMultiEnv]
      val wlService = new WhiteLabelServiceImpl {
        override val wlClient: WhiteLabelClientMultiEnv = mockWlClient
      }

      val featuresConfiguration = new FeaturesConfiguration
      featuresConfiguration.regulatory = Some(
        Regulatory(
          priceViewOptionsUserOriginOverride = None,
          priceViewOptionsDestinationOverride =
            Some(List(PriceViewOptionsDestinationOverrideInner(Some(1), Some(1), Some(List("test")), Some("test")))),
        ))

      Mockito.when(mockWlClient.isFeaturesEnabled(any, any, any, any, any, any)).thenAnswer {
        (invocation: InvocationOnMock) =>
          val features = invocation.getArguments.apply(0).asInstanceOf[List[String]]

          features
            .filter(_ != WhiteLabelFeatureNames.regulationPriceViewOptionDestinationOverride)
            .map(_ -> false)
            .toMap
      }
      mockWlClient.getFeaturesById(any, any).returns(featuresConfiguration)

      val actual = wlService.getWhitelabelSetting(emptyToken, None)(
        SFTestDataBuilders.aValidPropertySearchRequest.withWhiteLabelKey(emptyToken).build)

      actual.map(_.priceViewDestinationOverrideConfigs) should_== Right(List.empty)
    }

    "getFeeWaiverFiltrationSettings return None when featureFlags doesn't contain feeWaiverFiltrationSettings" in {
      val mockWlClient = mock[WhiteLabelClientMultiEnv]
      val wlService = new WhiteLabelServiceImpl {
        override val wlClient: WhiteLabelClientMultiEnv = mockWlClient
      }

      val featuresConfiguration = new FeaturesConfiguration
      featuresConfiguration.californiaFeeWaiver = Some(
        CaliforniaFeeWaiver(
          Some("desc"),
          Some(1),
          Some(1),
          Some(1),
          Some(1),
          Some("feeValue"),
          Some("expiryDuration"),
          Some("leadTimeDuration"),
          Some("checkInTime"),
          Some(List(1, 2)),
          Some(List(1, 2)),
        ))

      Mockito.when(mockWlClient.isFeaturesEnabled(any, any, any, any, any, any)).thenAnswer {
        (invocation: InvocationOnMock) =>
          val features = invocation.getArguments.apply(0).asInstanceOf[List[String]]

          features.filter(_ != WhiteLabelFeatureNames.feeWaiverFiltrationSettings).map(_ -> false).toMap
      }
      mockWlClient.getFeaturesById(any, any).returns(featuresConfiguration)

      val actual = wlService.getWhitelabelSetting(emptyToken, None)(
        SFTestDataBuilders.aValidPropertySearchRequest.withWhiteLabelKey(emptyToken).build)

      actual.map(_.feeWaiverFiltrationSettings) should_== Right(None)
    }

    "getExclusivePriceWithFeesForDestination should return None when feature flag is missing or false" in {
      val mockWhitelabelClient = mock[WhiteLabelClientMultiEnv]
      val wlService = new WhiteLabelServiceImpl {
        override val wlClient: WhiteLabelClientMultiEnv = mockWhitelabelClient
      }
      // Case 1: Feature flag missing
      val result1 = wlService.getExclusivePriceWithFeesForDestination(
        whitelabelID = 1,
        featuresEnabled = Map.empty, // feature flag missing
        agEnv = None,
      )
      result1 should_== None

      // Case 2: Feature flag present but false
      val result2 = wlService.getExclusivePriceWithFeesForDestination(
        whitelabelID = 1,
        featuresEnabled = Map(WhiteLabelFeatureNames.regulationShowExclusivePriceWithFeesForDestination -> false),
        agEnv = None,
      )
      result2 should_== None
    }

    "getExclusivePriceWithFeesForDestination should return config when feature flag is true and config exists" in {
      val mockWhitelabelClient = mock[WhiteLabelClientMultiEnv]
      val wlService = new WhiteLabelServiceImpl {
        override val wlClient: WhiteLabelClientMultiEnv = mockWhitelabelClient
      }
      val config = RegulationShowExclusivePriceWithFeesForDestination(
        hotelCountryId = Some(99),
        hotelStateId = Some(List(88, 77)),
      )
      val featuresConfiguration = new FeaturesConfiguration
      featuresConfiguration.regulationShowExclusivePriceWithFeesForDestination = Some(config)
      mockWhitelabelClient.getFeaturesById(1, None) returns featuresConfiguration

      val result = wlService.getExclusivePriceWithFeesForDestination(
        whitelabelID = 1,
        featuresEnabled = Map(WhiteLabelFeatureNames.regulationShowExclusivePriceWithFeesForDestination -> true),
        agEnv = None,
      )
      result should_== Some(RegulationShowExclusivePriceWithFeesForDestinationConfigs(Some(99), Some(List(88, 77))))
    }

    "getExclusivePriceWithFeesForDestination should return None when feature flag is true but config is missing" in {
      val mockWhitelabelClient = mock[WhiteLabelClientMultiEnv]
      val wlService = new WhiteLabelServiceImpl {
        override val wlClient: WhiteLabelClientMultiEnv = mockWhitelabelClient
      }

      val featuresConfiguration = new FeaturesConfiguration
      featuresConfiguration.regulationShowExclusivePriceWithFeesForDestination = None

      mockWhitelabelClient.getFeaturesById(1, None) returns featuresConfiguration

      val result = wlService.getExclusivePriceWithFeesForDestination(
        whitelabelID = 1,
        featuresEnabled = Map(WhiteLabelFeatureNames.regulationShowExclusivePriceWithFeesForDestination -> true),
        agEnv = None,
      )
      result should_== None
    }

    "getExclusivePriceWithFeesForDestination should return None when feature flag is missing even if config exists" in {
      val mockWhitelabelClient = mock[WhiteLabelClientMultiEnv]
      val wlService = new WhiteLabelServiceImpl {
        override val wlClient: WhiteLabelClientMultiEnv = mockWhitelabelClient
      }
      val config = RegulationShowExclusivePriceWithFeesForDestination(
        hotelCountryId = Some(99),
        hotelStateId = Some(List(88, 77)),
      )
      val featuresConfiguration = new FeaturesConfiguration
      featuresConfiguration.regulationShowExclusivePriceWithFeesForDestination = Some(config)

      mockWhitelabelClient.getFeaturesById(1, None) returns featuresConfiguration

      val result = wlService.getExclusivePriceWithFeesForDestination(
        whitelabelID = 1,
        featuresEnabled = Map.empty, // feature flag missing
        agEnv = None,
      )
      result should_== None
    }

    "getMinimumCheckInLeadTimeForExtSupply correctly returned" in {
      val result1 = wlSv.getMinimumCheckInLeadTimeForExtSupply(4, None)
      val result2 = wlSv.getMinimumCheckInLeadTimeForExtSupply(401, None)

      result1 should_== Some(2)
      result2 should_== None
    }

    "isAffiliatePartnerEnabled" should {
      // Common test data builders
      def makeRequest(affiliateId: Option[String], cid: Int): PropertySearchRequest =
        SFTestDataBuilders.aValidPropertySearchRequest
          .withContextRequest(
            aValidContextRequest.withClientInfo(
              aValidClientInfo.withAffiliateId(affiliateId).withCID(cid),
            ),
          )
          .build

      def setupServiceWithPartner(partner: Option[AffiliateCorPartner],
                                  whiteLabelId: Int = 1): WhiteLabelServiceImpl = {
        val mockWhitelabelClient = mock[WhiteLabelClientMultiEnv]
        val wlService = new WhiteLabelServiceImpl {
          override val wlClient: WhiteLabelClientMultiEnv = mockWhitelabelClient
        }
        val featuresConfiguration = new FeaturesConfiguration
        featuresConfiguration.affiliateCorPartner = whiteLabelId match {
          case 1 => partner // Agoda
          case _ => None
        }
        mockWhitelabelClient.getFeaturesById(whiteLabelId, None) returns featuresConfiguration
        wlService
      }

      // Commonly used test data
      val validAffiliateId = Some("123")
      val invalidAffiliateId = Some("999")
      val notAnIntAffiliateId = Some("notAnInt")
      val validRequestSiteId = 456
      val invalidRequestSiteId = 999
      val wlAffiliateIds = Some(List(123))
      val wlSiteIds = Some(List(456))
      val agodaWlId = 1
      val otherWlId = 2
      val partnerEnabledWithValidAffiliateIdAndNoSiteId =
        Some(AffiliateCorPartner(isFeatureEnabled = Some(true), wlAffiliateIds, None))
      val partnerEnabledWithValidAffiliateIdAndValidSiteIds =
        Some(AffiliateCorPartner(isFeatureEnabled = Some(true), wlAffiliateIds, wlSiteIds))
      val partnerEnabledWithNoAffiliateIdAndValidSiteIds =
        Some(AffiliateCorPartner(isFeatureEnabled = Some(true), None, wlSiteIds))
      val partnerEnabledWithValidAffiliateIdAndNoSiteIds =
        Some(AffiliateCorPartner(isFeatureEnabled = Some(true), wlAffiliateIds, None))
      val partnerNotEnabledNone = Some(AffiliateCorPartner(isFeatureEnabled = None, wlAffiliateIds, wlSiteIds))
      val partnerNotEnabledFalse = Some(AffiliateCorPartner(isFeatureEnabled = Some(false), wlAffiliateIds, wlSiteIds))

      "return true if partnerConfig is enabled and affiliateId matches" in {
        val wlService = setupServiceWithPartner(partnerEnabledWithValidAffiliateIdAndNoSiteId)
        val request = makeRequest(validAffiliateId, invalidRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== true
      }

      "return true if partnerConfig is enabled and cid matches" in {
        val wlService = setupServiceWithPartner(partnerEnabledWithValidAffiliateIdAndValidSiteIds)
        val request = makeRequest(invalidAffiliateId, validRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== true
      }

      "return false if partnerConfig is enabled but neither affiliateId nor cid matches" in {
        val wlService = setupServiceWithPartner(partnerEnabledWithValidAffiliateIdAndValidSiteIds)
        val request = makeRequest(invalidAffiliateId, invalidRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== false
      }

      "return true if partnerConfig is enabled, affiliateIds is None, and cid matches" in {
        val wlService = setupServiceWithPartner(partnerEnabledWithNoAffiliateIdAndValidSiteIds)
        val request = makeRequest(invalidAffiliateId, validRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== true
      }

      "return true if partnerConfig is enabled, cids is None, and affiliateId matches" in {
        val wlService = setupServiceWithPartner(partnerEnabledWithValidAffiliateIdAndNoSiteIds)
        val request = makeRequest(validAffiliateId, invalidRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== true
      }

      "return false if partnerConfig is enabled but affiliateId is not an integer" in {
        val wlService = setupServiceWithPartner(partnerEnabledWithValidAffiliateIdAndValidSiteIds)
        val request = makeRequest(notAnIntAffiliateId, invalidRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== false
      }

      "return false if partnerConfig is not enabled (isEnabled = None)" in {
        val wlService = setupServiceWithPartner(partnerNotEnabledNone)
        val request = makeRequest(validAffiliateId, validRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== false
      }

      "return false if partnerConfig is not enabled (isEnabled = Some(false))" in {
        val wlService = setupServiceWithPartner(partnerNotEnabledFalse)
        val request = makeRequest(validAffiliateId, validRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== false
      }

      "return false if corAffiliatePartner is None" in {
        val wlService = setupServiceWithPartner(None)
        val request = makeRequest(validAffiliateId, validRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== false
      }

      "return true if affiliateId is None but cid matches" in {
        val wlService = setupServiceWithPartner(partnerEnabledWithValidAffiliateIdAndValidSiteIds)
        val request = makeRequest(None, validRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== true
      }

      "return false if affiliateId is None and cid does not match" in {
        val wlService = setupServiceWithPartner(partnerEnabledWithValidAffiliateIdAndValidSiteIds)
        val request = makeRequest(None, invalidRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== false
      }

      "return false if not agoda white label id" in {
        val wlService = setupServiceWithPartner(partnerEnabledWithValidAffiliateIdAndValidSiteIds, otherWlId)
        val request = makeRequest(None, invalidRequestSiteId)
        wlService.isAffiliatePartnerEnabled(request, otherWlId, None) should_== false
      }

      "return false if partnerConfig is enabled, affiliateIds is defined but does not match, and siteIds is None" in {
        val wlService = setupServiceWithPartner(
          Some(AffiliateCorPartner(isFeatureEnabled = Some(true), Some(List(123)), None)),
        )
        val request = makeRequest(Some("999"), 888) // affiliateId does not match, siteIds is None
        wlService.isAffiliatePartnerEnabled(request, agodaWlId, None) should_== false
      }
    }

    "getCurrencyDecimalOverrideConfig" should {
      "return empty config when currencyDecimalOverride feature is disabled" in {
        val featuresEnabled = Map(WhiteLabelFeatureNames.currencyDecimalOverride -> false)
        val result = wlSv.getCurrencyDecimalOverrideConfig(1, featuresEnabled, None)
        result should_== CurrencyDecimalOverrideConfig()
      }

      "return empty config when currencyDecimalOverride feature is not present in map (uses default false)" in {
        val featuresEnabled = Map.empty[String, Boolean] // Feature not present, should use default false
        val result = wlSv.getCurrencyDecimalOverrideConfig(1, featuresEnabled, None)
        result should_== CurrencyDecimalOverrideConfig()
      }

      "return actual config when currencyDecimalOverride feature is enabled" in {
        val featuresEnabled = Map(WhiteLabelFeatureNames.currencyDecimalOverride -> true)
        val result = wlSv.getCurrencyDecimalOverrideConfig(1, featuresEnabled, None)
        // The result should be the actual config from the whitelabel features, not empty
        // Since whitelabel 1 doesn't have currency decimal override config in the mock, it should return empty
        result should_== CurrencyDecimalOverrideConfig()
      }

      "verify different behavior when feature is enabled vs disabled" in {
        val featuresDisabled = Map(WhiteLabelFeatureNames.currencyDecimalOverride -> false)
        val resultDisabled = wlSv.getCurrencyDecimalOverrideConfig(1, featuresDisabled, None)

        val featuresEnabled = Map(WhiteLabelFeatureNames.currencyDecimalOverride -> true)
        val resultEnabled = wlSv.getCurrencyDecimalOverrideConfig(1, featuresEnabled, None)

        // This test ensures both branches of the if condition are executed
        resultDisabled should_== CurrencyDecimalOverrideConfig()
        resultEnabled should_== CurrencyDecimalOverrideConfig()
      }

      "verify the default value behavior specifically" in {
        // This test specifically targets the default value mutation
        // by testing a map that doesn't contain the key at all
        val emptyMap = Map.empty[String, Boolean]
        val mapWithOtherKeys = Map("someOtherFeature" -> true)

        val result1 = wlSv.getCurrencyDecimalOverrideConfig(1, emptyMap, None)
        val result2 = wlSv.getCurrencyDecimalOverrideConfig(1, mapWithOtherKeys, None)

        // Both should use the default value (false) and return empty config
        result1 should_== CurrencyDecimalOverrideConfig()
        result2 should_== CurrencyDecimalOverrideConfig()
      }
    }

    "getCouponWithWLID" should {
      "return empty coupon when coupons feature is disabled" in {
        val featuresEnabled = Map(WhiteLabelFeatureNames.coupons -> false)
        val result = wlSv.getCouponWithWLID(1, featuresEnabled, None)
        result should_== Coupon(None)
      }

      "return empty coupon when coupons feature is not present in map (uses default false)" in {
        val featuresEnabled = Map.empty[String, Boolean] // Feature not present, should use default false
        val result = wlSv.getCouponWithWLID(1, featuresEnabled, None)
        result should_== Coupon(None)
      }

      "return actual coupon config when coupons feature is enabled" in {
        val featuresEnabled = Map(WhiteLabelFeatureNames.coupons -> true)
        val result = wlSv.getCouponWithWLID(1, featuresEnabled, None)
        // Since whitelabel 1 doesn't have coupon config in the mock, it should return empty
        result should_== Coupon(None)
      }

      "verify different behavior when feature is enabled vs disabled" in {
        val featuresDisabled = Map(WhiteLabelFeatureNames.coupons -> false)
        val resultDisabled = wlSv.getCouponWithWLID(1, featuresDisabled, None)

        val featuresEnabled = Map(WhiteLabelFeatureNames.coupons -> true)
        val resultEnabled = wlSv.getCouponWithWLID(1, featuresEnabled, None)

        // This test ensures both branches of the if condition are executed
        resultDisabled should_== Coupon(None)
        resultEnabled should_== Coupon(None)
      }

      "verify the default value behavior specifically" in {
        // This test specifically targets the default value mutation (line 89)
        val emptyMap = Map.empty[String, Boolean]
        val mapWithOtherKeys = Map("someOtherFeature" -> true)

        val result1 = wlSv.getCouponWithWLID(1, emptyMap, None)
        val result2 = wlSv.getCouponWithWLID(1, mapWithOtherKeys, None)

        // Both should use the default value (false) and return empty coupon
        result1 should_== Coupon(None)
        result2 should_== Coupon(None)
      }
    }

    "getExchangeRateWithWLID" should {
      "return false exchange rate config when currency feature is disabled" in {
        val featuresEnabled = Map(WhiteLabelFeatureNames.currency -> false)
        val result = wlSv.getExchangeRateWithWLID(1, featuresEnabled, None)
        result should_== ExchangeRateConfiguration(false)
      }

      "return false exchange rate config when currency feature is not present in map (uses default false)" in {
        val featuresEnabled = Map.empty[String, Boolean] // Feature not present, should use default false
        val result = wlSv.getExchangeRateWithWLID(1, featuresEnabled, None)
        result should_== ExchangeRateConfiguration(false)
      }

      "return actual exchange rate config when currency feature is enabled" in {
        val featuresEnabled = Map(WhiteLabelFeatureNames.currency -> true)
        val result = wlSv.getExchangeRateWithWLID(1, featuresEnabled, None)
        // Since whitelabel 1 doesn't have currency config in the mock, it should return false
        result should_== ExchangeRateConfiguration(false)
      }

      "verify different behavior when feature is enabled vs disabled" in {
        val featuresDisabled = Map(WhiteLabelFeatureNames.currency -> false)
        val resultDisabled = wlSv.getExchangeRateWithWLID(1, featuresDisabled, None)

        val featuresEnabled = Map(WhiteLabelFeatureNames.currency -> true)
        val resultEnabled = wlSv.getExchangeRateWithWLID(1, featuresEnabled, None)

        // This test ensures both branches of the if condition are executed
        resultDisabled should_== ExchangeRateConfiguration(false)
        resultEnabled should_== ExchangeRateConfiguration(false)
      }

      "verify the default value behavior specifically" in {
        // This test specifically targets the default value mutation (line 174)
        val emptyMap = Map.empty[String, Boolean]
        val mapWithOtherKeys = Map("someOtherFeature" -> true)

        val result1 = wlSv.getExchangeRateWithWLID(1, emptyMap, None)
        val result2 = wlSv.getExchangeRateWithWLID(1, mapWithOtherKeys, None)

        // Both should use the default value (false) and return false exchange rate config
        result1 should_== ExchangeRateConfiguration(false)
        result2 should_== ExchangeRateConfiguration(false)
      }
    }

    "getExternalVipDisplayConfigs" should {
      "return empty list when externalVipProgram feature is disabled" in {
        val featuresEnabled = Map(WhiteLabelFeatureNames.externalVipProgram -> false)
        val result = wlSv.getExternalVipDisplayConfigs(1, featuresEnabled, None)
        result should_== List.empty
      }

      "return empty list when externalVipProgram feature is not present in map (uses default false)" in {
        val featuresEnabled = Map.empty[String, Boolean] // Feature not present, should use default false
        val result = wlSv.getExternalVipDisplayConfigs(1, featuresEnabled, None)
        result should_== List.empty
      }

      "return actual external vip configs when externalVipProgram feature is enabled" in {
        val featuresEnabled = Map(WhiteLabelFeatureNames.externalVipProgram -> true)
        val result = wlSv.getExternalVipDisplayConfigs(1, featuresEnabled, None)
        // Since whitelabel 1 doesn't have external vip config in the mock, it should return empty
        result should_== List.empty
      }

      "return actual external vip configs when externalVipProgram feature is enabled for whitelabel 51" in {
        val featuresEnabled = Map(WhiteLabelFeatureNames.externalVipProgram -> true)
        val result = wlSv.getExternalVipDisplayConfigs(51, featuresEnabled, None)
        // Whitelabel 51 has external vip config in the mock
        val expected = List(
          ExternalVipDisplayConfigs(tierId = Some(2),
                                    tierNameCms = Some(3),
                                    tierDescriptionCms = Some(1),
                                    benefitIds = Some(List(95, 26, 201, 241, 12, 261))))
        result should_== expected
      }

      "verify different behavior when feature is enabled vs disabled" in {
        val featuresDisabled = Map(WhiteLabelFeatureNames.externalVipProgram -> false)
        val resultDisabled = wlSv.getExternalVipDisplayConfigs(51, featuresDisabled, None)

        val featuresEnabled = Map(WhiteLabelFeatureNames.externalVipProgram -> true)
        val resultEnabled = wlSv.getExternalVipDisplayConfigs(51, featuresEnabled, None)

        // This test ensures both branches of the if condition are executed (lines 195-196)
        resultDisabled should_== List.empty
        val expected = List(
          ExternalVipDisplayConfigs(tierId = Some(2),
                                    tierNameCms = Some(3),
                                    tierDescriptionCms = Some(1),
                                    benefitIds = Some(List(95, 26, 201, 241, 12, 261))))
        resultEnabled should_== expected
      }

      "verify the default value behavior specifically" in {
        // This test specifically targets the default value mutation (line 195)
        val emptyMap = Map.empty[String, Boolean]
        val mapWithOtherKeys = Map("someOtherFeature" -> true)

        val result1 = wlSv.getExternalVipDisplayConfigs(1, emptyMap, None)
        val result2 = wlSv.getExternalVipDisplayConfigs(1, mapWithOtherKeys, None)

        // Both should use the default value (false) and return empty list
        result1 should_== List.empty
        result2 should_== List.empty
      }
    }

    "blockYCSPromotions feature flag default value" should {
      "use false as default when feature is not present in map" in {
        // This test targets the mutation at line 363
        // Create a mock that returns an empty map for features
        val mockClient = mock[WhiteLabelClientMultiEnv]
        mockClient.getWhiteLabelIdByToken(emptyToken, None) returns WhiteLabelId.AGODA
        mockClient.isFeaturesEnabled(any, any, any, any, any, any) returns Map.empty[String, Boolean]
        mockClient.getFeaturesById(any, any) returns new FeaturesConfiguration()

        val testService = new WhiteLabelServiceImpl {
          override val wlClient: WhiteLabelClientMultiEnv = mockClient
        }

        val result = testService.getWhitelabelSetting(emptyToken, None)(
          SFTestDataBuilders.aValidPropertySearchRequest.withWhiteLabelKey(emptyToken))

        // Should use the default value (false) for blockYCSPromotions
        result.map(_.blockYCSPromotions) should_== Right(false)
      }
    }

    "customerSegmentValidation feature flag default value" should {
      "use false as default when feature is not present in map" in {
        // This test targets the mutation at line 409
        // Create a mock that returns an empty map for features
        val mockClient = mock[WhiteLabelClientMultiEnv]
        mockClient.getWhiteLabelIdByToken(emptyToken, None) returns WhiteLabelId.AGODA
        mockClient.isFeaturesEnabled(any, any, any, any, any, any) returns Map.empty[String, Boolean]
        mockClient.getFeaturesById(any, any) returns new FeaturesConfiguration()

        val testService = new WhiteLabelServiceImpl {
          override val wlClient: WhiteLabelClientMultiEnv = mockClient
        }

        val result = testService.getWhitelabelSetting(emptyToken, None)(
          SFTestDataBuilders.aValidPropertySearchRequest.withWhiteLabelKey(emptyToken))

        // Should use the default value (false) for customerSegmentValidation
        result.map(_.isCustomerSegmentValidationEnabled) should_== Right(false)
      }
    }

  }

  private def getMockFeatureConfigurationForWhitelabel3: FeaturesConfiguration = {

    val featuresConfiguration = new FeaturesConfiguration()
    featuresConfiguration.supplierFilter = supplierFilter
    featuresConfiguration.supplierConfiguration = SupplierConfigurationV4(
      Some(
        CommissionAndMarginOverrideWL(isCommissionAdjustment = Some(false),
                                      isMarginAdjustment = Some(false),
                                      isCommissionOverride = Some(false))))
    featuresConfiguration.coupons = CouponsV4(pageVersion = Some(2),
                                              cids = None,
                                              cid = None,
                                              termsAndConditions = None,
                                              isEnabled = None,
                                              headerImage = None,
                                              howItWorks = None,
                                              paymentModels = Some(List(1, 2)))
    featuresConfiguration.currency = CurrencyV4(defaultCurrency = "USD",
                                                availableCurrencies = List.empty,
                                                priceDisplay = true,
                                                exchangeRateOnlyForReference = Some(true))
    featuresConfiguration.login = LoginV4(
      directThirdPartyAuthentication = None,
      loginWithPartnerCookie = None,
      inventoryTypesForLogIn = Some(List(1, 2)),
      unlockAccount = None,
      resetPasswordUrl = None,
      usernameRegex = None,
      isFallBack = None,
      resetPasswordRedirect = None,
      token = None,
      ssoPortal = None,
      serviceId = None,
      isUsernameCaseSensitive = None,
      isForcedLoginEnabled = None,
      host = None,
      passwordRegex = None,
      showBenefits = None,
    )
    featuresConfiguration.supportWLSupplierMetadata = Some(
      SupportWLSupplierMetadata(mainSupplierId = Some(29014),
                                domesticCountryId = Some(3),
                                overrideEarliestPossibleCheckInOffset = Some(3)))

    featuresConfiguration
  }

  private def getMockFeatureConfigurationForWhitelabel1: FeaturesConfiguration = {

    val featuresConfiguration = new FeaturesConfiguration()
    featuresConfiguration.supplierFilter = emptySupplierFilter
    featuresConfiguration.supplierConfiguration = SupplierConfigurationV4(
      Some(
        CommissionAndMarginOverrideWL(isCommissionAdjustment = Some(true),
                                      isMarginAdjustment = Some(true),
                                      isCommissionOverride = Some(true))))
    featuresConfiguration.coupons = CouponsV4(pageVersion = Some(1),
                                              cids = None,
                                              cid = None,
                                              termsAndConditions = None,
                                              isEnabled = None,
                                              headerImage = None,
                                              howItWorks = None,
                                              paymentModels = None)
    featuresConfiguration.currency = CurrencyV4(defaultCurrency = "USD",
                                                availableCurrencies = List.empty,
                                                priceDisplay = true,
                                                exchangeRateOnlyForReference = None)
    featuresConfiguration.payment = PaymentV4(inventoryTypes = Some(inventoryTypes))
    featuresConfiguration.regulatory = Some(
      Regulatory(
        priceViewOptionsUserOriginOverride = None,
        priceViewOptionsDestinationOverride =
          Some(List(PriceViewOptionsDestinationOverrideInner(Some(1), Some(1), Some(List("test")), Some("test")))),
      ))
    featuresConfiguration.occupancyOptions = Some(OccupancyOptionsV4(true, true, true, true, true, true))
    featuresConfiguration.californiaFeeWaiver = Some(
      CaliforniaFeeWaiver(
        Some("desc"),
        Some(1),
        Some(1),
        Some(1),
        Some(1),
        Some("feeValue"),
        Some("expiryDuration"),
        Some("leadTimeDuration"),
        Some("checkInTime"),
        Some(List(1, 2)),
        Some(List(1, 2)),
      ))

    featuresConfiguration
  }

  private def getMockFeatureConfigurationForWhitelabel51: FeaturesConfiguration = {

    val featuresConfiguration = new FeaturesConfiguration()
    featuresConfiguration.supplierFilter = emptySupplierFilter
    featuresConfiguration.supplierConfiguration = SupplierConfigurationV4(
      Some(
        CommissionAndMarginOverrideWL(isCommissionAdjustment = Some(true),
                                      isMarginAdjustment = Some(true),
                                      isCommissionOverride = Some(true))))
    featuresConfiguration.coupons = CouponsV4(pageVersion = Some(1),
                                              cids = None,
                                              cid = None,
                                              termsAndConditions = None,
                                              isEnabled = None,
                                              headerImage = None,
                                              howItWorks = None,
                                              paymentModels = None)
    featuresConfiguration.currency = CurrencyV4(defaultCurrency = "USD",
                                                availableCurrencies = List.empty,
                                                priceDisplay = true,
                                                exchangeRateOnlyForReference = None)
    featuresConfiguration.externalVipDisplay = ExternalVipDisplay(externalVipDisplayItems = Some(
      List(
        ExternalVipDisplayItem(tierDescriptionCms = Some(1),
                               tierId = Some(2),
                               tierNameCms = Some(3),
                               benefitIds = Some(List(95, 26, 201, 241, 12, 261))))))
    featuresConfiguration.publishPriceConfiguration = PublishPriceConfiguration(enableESSPublishPrice = Some(true))
    featuresConfiguration.occupancyOptions = Some(OccupancyOptionsV4(true, true, true, true, true, true, Some(true)))

    featuresConfiguration
  }

  private def getMockFeatureConfigurationForWhitelabel5101: FeaturesConfiguration = {

    val featuresConfiguration = new FeaturesConfiguration()
    featuresConfiguration.supplierFilter = emptySupplierFilter
    featuresConfiguration.supplierConfiguration = SupplierConfigurationV4(
      Some(
        CommissionAndMarginOverrideWL(isCommissionAdjustment = Some(true),
                                      isMarginAdjustment = Some(true),
                                      isCommissionOverride = Some(true))))
    featuresConfiguration.coupons = CouponsV4(pageVersion = Some(1),
                                              cids = None,
                                              cid = None,
                                              termsAndConditions = None,
                                              isEnabled = None,
                                              headerImage = None,
                                              howItWorks = None,
                                              paymentModels = None)
    featuresConfiguration.currency = CurrencyV4(defaultCurrency = "USD",
                                                availableCurrencies = List.empty,
                                                priceDisplay = true,
                                                exchangeRateOnlyForReference = None)
    featuresConfiguration.externalVipDisplay = ExternalVipDisplay(externalVipDisplayItems = Some(
      List(
        ExternalVipDisplayItem(tierDescriptionCms = Some(1),
                               tierId = Some(2),
                               tierNameCms = Some(3),
                               benefitIds = Some(List(95, 26, 201, 241, 12, 261))))))
    featuresConfiguration.publishPriceConfiguration = PublishPriceConfiguration(enableESSPublishPrice = Some(true))

    featuresConfiguration
  }
  private def getMockFeatureConfigurationForWhitelabel2: FeaturesConfiguration = {

    val featuresConfiguration = new FeaturesConfiguration()
    featuresConfiguration.supplierFilter = SupplierFilterV4(Some(List(27901, 27902)))
    featuresConfiguration.supplierConfiguration = SupplierConfigurationV4(
      Some(
        CommissionAndMarginOverrideWL(isCommissionAdjustment = Some(true),
                                      isMarginAdjustment = Some(true),
                                      isCommissionOverride = Some(true))))
    featuresConfiguration.coupons = CouponsV4(pageVersion = Some(1),
                                              cids = None,
                                              cid = None,
                                              termsAndConditions = None,
                                              isEnabled = None,
                                              headerImage = None,
                                              howItWorks = None,
                                              paymentModels = None)
    featuresConfiguration.currency = CurrencyV4(defaultCurrency = "USD",
                                                availableCurrencies = List.empty,
                                                priceDisplay = true,
                                                exchangeRateOnlyForReference = None)

    featuresConfiguration
  }
  private def getMockFeatureConfigurationForWhitelabel4: FeaturesConfiguration = {

    val featuresConfiguration = new FeaturesConfiguration()
    featuresConfiguration.supplierFilter = SupplierFilterV4(Some(List(27901, 27902)))
    featuresConfiguration.supplierConfiguration = SupplierConfigurationV4(
      Some(
        CommissionAndMarginOverrideWL(isCommissionAdjustment = None,
                                      isMarginAdjustment = Some(true),
                                      isCommissionOverride = Some(true))))
    featuresConfiguration.coupons = CouponsV4(pageVersion = Some(1),
                                              cids = None,
                                              cid = None,
                                              termsAndConditions = None,
                                              isEnabled = None,
                                              headerImage = None,
                                              howItWorks = None,
                                              paymentModels = None)
    featuresConfiguration.currency = CurrencyV4(defaultCurrency = "USD",
                                                availableCurrencies = List.empty,
                                                priceDisplay = true,
                                                exchangeRateOnlyForReference = None)
    featuresConfiguration.externalSuppliers = Some(
      ExternalSuppliers(
        minimumCheckInLeadTime = Some(2),
        externalSuppliersConfig = Some(
          List(
            ExternalSuppliersConfig(supplierId = Some(332),
                                    paymentChannels = Some(List(1)),
                                    paymentModels = Some(List(1)),
                                    blockedOrigins = Some(List("JP", "MY"))))),
      ))
    featuresConfiguration.supportWLSupplierMetadata = Some(
      SupportWLSupplierMetadata(mainSupplierId = Some(29014),
                                domesticCountryId = Some(3),
                                overrideEarliestPossibleCheckInOffset = Some(3)))
    featuresConfiguration.controlDirectConnectSupply = Some(
      ControlDirectConnectSupply(whitelistedSuppliers =
                                   Some(List(29004, 27912, 29005, 27800, 29002, 29022, 27914, 29020, 29003)),
                                 allowedLeadDaysToNonRefundable = Some(6)))

    featuresConfiguration
  }
  private def getMockFeatureConfigurationForWhitelabel401: FeaturesConfiguration = {

    val featuresConfiguration = new FeaturesConfiguration()
    featuresConfiguration.supplierFilter = SupplierFilterV4(Some(List(27901, 27902)))
    featuresConfiguration.supplierConfiguration = SupplierConfigurationV4(
      Some(
        CommissionAndMarginOverrideWL(isCommissionAdjustment = None,
                                      isMarginAdjustment = Some(true),
                                      isCommissionOverride = Some(true))))
    featuresConfiguration.coupons = CouponsV4(pageVersion = Some(1),
                                              cids = None,
                                              cid = None,
                                              termsAndConditions = None,
                                              isEnabled = None,
                                              headerImage = None,
                                              howItWorks = None,
                                              paymentModels = None)
    featuresConfiguration.currency = CurrencyV4(defaultCurrency = "USD",
                                                availableCurrencies = List.empty,
                                                priceDisplay = true,
                                                exchangeRateOnlyForReference = None)
    featuresConfiguration.externalSuppliers = Some(
      ExternalSuppliers(externalSuppliersConfig = Some(
        List(
          ExternalSuppliersConfig(supplierId = Some(332),
                                  paymentChannels = Some(List(1)),
                                  paymentModels = Some(List(1)),
                                  blockedOrigins = Some(List("JP", "MY")))))))
    featuresConfiguration.supportWLSupplierMetadata = Some(
      SupportWLSupplierMetadata(mainSupplierId = Some(99901),
                                domesticCountryId = Some(3),
                                overrideEarliestPossibleCheckInOffset = Some(3)))
    featuresConfiguration.controlDirectConnectSupply = Some(
      ControlDirectConnectSupply(whitelistedSuppliers =
                                   Some(List(29004, 27912, 29005, 27800, 29002, 29022, 27914, 29020, 29003)),
                                 allowedLeadDaysToNonRefundable = Some(6)))

    featuresConfiguration
  }
  private def getMockFeatureConfigurationForWhitelabel5: FeaturesConfiguration = {

    val featuresConfiguration = new FeaturesConfiguration()
    featuresConfiguration.supplierFilter = SupplierFilterV4(Some(List(27901, 27902)))
    featuresConfiguration.supplierConfiguration = SupplierConfigurationV4(
      Some(
        CommissionAndMarginOverrideWL(isCommissionAdjustment = Some(true),
                                      isMarginAdjustment = None,
                                      isCommissionOverride = Some(true))))
    featuresConfiguration.coupons = CouponsV4(pageVersion = Some(1),
                                              cids = None,
                                              cid = None,
                                              termsAndConditions = None,
                                              isEnabled = None,
                                              headerImage = None,
                                              howItWorks = None,
                                              paymentModels = None)
    featuresConfiguration.currency = CurrencyV4(defaultCurrency = "USD",
                                                availableCurrencies = List.empty,
                                                priceDisplay = true,
                                                exchangeRateOnlyForReference = None)

    featuresConfiguration
  }
  private def getMockFeatureConfigurationForWhitelabel6: FeaturesConfiguration = {

    val featuresConfiguration = new FeaturesConfiguration()
    featuresConfiguration.supplierFilter = SupplierFilterV4(Some(List(27901, 27902)))
    featuresConfiguration.supplierConfiguration = SupplierConfigurationV4(
      Some(
        CommissionAndMarginOverrideWL(isCommissionAdjustment = Some(true),
                                      isMarginAdjustment = Some(true),
                                      isCommissionOverride = None)))
    featuresConfiguration.coupons = CouponsV4(pageVersion = Some(1),
                                              cids = None,
                                              cid = None,
                                              termsAndConditions = None,
                                              isEnabled = None,
                                              headerImage = None,
                                              howItWorks = None,
                                              paymentModels = None)
    featuresConfiguration.currency = CurrencyV4(defaultCurrency = "USD",
                                                availableCurrencies = List.empty,
                                                priceDisplay = true,
                                                exchangeRateOnlyForReference = None)

    featuresConfiguration
  }

}
