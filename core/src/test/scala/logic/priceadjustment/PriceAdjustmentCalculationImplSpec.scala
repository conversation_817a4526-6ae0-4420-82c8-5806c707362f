package logic.priceadjustment

import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.enums.room.{ApplyType, ChargeOption, ChargeType, SubChargeType, SurchargeTypeClassification}
import com.agoda.papi.pricing.discounting.models.response.Downlift
import com.agoda.papi.pricing.internal.models.ExperimentCarrier
import com.agoda.papi.pricing.pricecalculation.models.tax.{CommonTaxBreakdown, Tax}
import com.agoda.papi.pricing.pricecalculation.models.CommonProcessingFeeBreakdown
import com.agoda.papi.ypl.models.GUIDGenerator.UID
import com.agoda.papi.ypl.models.GUIDGeneratorHelper
import com.agoda.platform.pricing.models.utils.DFTestDataBuilders
import financial.EBEFinancialBreakdownProvider
import logic.papi.{BookingItemProvider, BookingItemProviderImpl}
import mocks.{ReportingServiceMock, UtilCmsServiceMock}
import models.pricediff.{PriceAdjustmentCalculationInfo, PriceAdjustmentDifference}
import models.pricing.{ConvertedPrice, ExchangeRate}
import models.pricing.enums._
import models.starfruit.ImplicitHelpers._
import models.starfruit.ItemBreakdownImplicits.Converters
import models.starfruit._
import models.utils.ItemBreakdownHelper
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit
import com.agoda.papi.ypl.models.GUIDGeneratorSpec.aValidFeature
import com.agoda.papi.ypl.models.SupplierFeatures
import models.enums.ReBookingActionType.MatchUSD
class PriceAdjustmentCalculationImplSpec extends SpecificationWithJUnit with DFTestDataBuilders {

  trait BookingItemProviderTest
    extends BookingItemProviderImpl
      with PriceAdjustmentCalculationImpl
      with ReportingServiceMock

  val service = new PriceAdjustmentCalculationImpl {}
  val mockRoomUID = "63ea441a-9187-f120-f5dc-139abb1e2413"
  val mockRoomIden = "ChsI7KbxCRACIAIopqhGMAJKBzFEMU5fMU5QmAUSAggBGgQoAjAB"
  val mockUID = UID(mockRoomUID, GUIDGeneratorHelper.convertToUID(mockRoomIden).roomIdentifier)
  val mockPriceAdjustmentRequest = PriceAdjustment(
    roomId = mockRoomIden,
    requestedPrice = 100d,
    chargeType = BookingItemTypes.Room.i,
    rateType = BookingRateTypes.SellInclusive.i,
    applyType = ApplyTypes.PB.i,
    chargeOption = ChargeOptions.Mandatory.i,
    surchargeId = None,
    requestCurrency = "THB",
  )
  val mockSupplierFeatures = SupplierFeatures(features = Map(123 -> aValidFeature))

  val mockCommonTaxBreakdown = CommonTaxBreakdown(typeId = 1,
                                                  isFee = false,
                                                  quantity = 3,
                                                  amount = 10d,
                                                  applyTo = "",
                                                  percentage = 0d,
                                                  noAdjust = false,
                                                  include = true,
                                                  convertedAmount = Some(300d),
                                                  orderNumber = 0)
  val commonTaxBreakdown = List(
    mockCommonTaxBreakdown,
    mockCommonTaxBreakdown.copy(typeId = 10, isFee = true, amount = 5, convertedAmount = Some(150d), quantity = 4))
  val convertedPrice = ConvertedPrice(
    sellExclusive = 900,
    sellInclusive = 1000,
    tax = 0,
    fee = 0,
    margin = 100,
    processingFee = 5,
    netInclusive = 600,
    netExclusive = 500,
    refSellInclusive = 1200,
    discount = 50,
    downLift = 70,
    convertedTaxBreakDown = commonTaxBreakdown,
    refSellExclusive = 1200,
    processingFeeBreakdown = Some(CommonProcessingFeeBreakdown(3, 2)),
  )
  val mockPriceBase = aValidPrice
    .withChargeType(ChargeType.Room)
    .withChargeOption(ChargeOption.Mandatory)
    .withSubChargeType(SubChargeType.None)
    .withApplyType(ApplyType.PRPN)
    .withQuantity(1)
    .withPercent(0.0)
    .withConvertPrice(convertedPrice)
  val mockDateTime = DateTime.parse("2024-12-02")
  val mockPrices =
    Seq(mockPriceBase.withDate(mockDateTime).build, mockPriceBase.withDate(mockDateTime.plusDays(1)).build)
  val chargeDiscount = 0d
  val agxCommissionAdjustment = None
  val mockDownlift = Downlift(5d, 1, 555)
  val bookingParam =
    BookingPriceParam(isYCS = true, dmcId = 332L, roomPaymentModel = PaymentModel.Agency, requestCurrency = "THB")
  val precision = 2
  val thbToUsdExchangeRate = 35 // thb to usd
  val thbToCnyExchangeRate = 5 // thb to cny
  val mockBaseItemBreakdown = mockPrices.flatMap(
    EBEFinancialBreakdownProvider
      .getItemBreakDowns(_,
                         precision,
                         Some(bookingParam),
                         Some(mockDownlift),
                         1,
                         supplierFeatureSettings = mockSupplierFeatures,
                         isJTBFP1295Exp = false)
      ._1)
  val localItemBreakdown = ItemBreakdownHelper.generateItemKey(mockBaseItemBreakdown) // THB
  val usdItemBreakdown = localItemBreakdown.map { case (k, v) =>
    (k -> v.update(local = v.local.map(local => (local / thbToUsdExchangeRate).roundp(2))))
  } // USD
  val reqItemBreakdown = localItemBreakdown.map { case (k, v) =>
    (k -> v.update(local = v.local.map(local => (local / thbToCnyExchangeRate).roundp(2))))
  } // CNY

  val helper = new BookingItemProviderTest with UtilCmsServiceMock {}
  val bookingItemBreakdown = BookingItemProvider.mergeItemBreakdownByCurrency(ExperimentCarrier(),
                                                                              localItemBreakdown,
                                                                              usdItemBreakdown,
                                                                              reqItemBreakdown,
                                                                              thbToUsdExchangeRate,
                                                                              "THB")

  val bookingRequestCurrency = "CNY"
  val cnyToUSDExchangeRate = 0.15d
  val usdToCNYExchangeRate = 5.56d
  val thbToUSDExchangeRate = 0.032d
  val cnyToTHBExchangeRate = 4.75d

  val cnyToUSDCalculationInfo =
    PriceAdjustmentCalculationInfo(noRoom = 1, los = 2, exRate = cnyToUSDExchangeRate, precision = 2)

  val cnyToTHBcalculationInfo =
    PriceAdjustmentCalculationInfo(noRoom = 1, los = 2, exRate = cnyToTHBExchangeRate, precision = 2)

  val thbToTHBcalculationInfo = PriceAdjustmentCalculationInfo(noRoom = 1, los = 2, exRate = 1.0, precision = 2)

  "findRoomToAdjustPrice" should {
    "return room to adjust correctly" in {
      val mockCurrentUID = mockUID
      val mockPriceAdjustmentRequest1 = mockPriceAdjustmentRequest.copy(roomId = "not match")
      val mockPriceAdjustmentRequest2 =
        mockPriceAdjustmentRequest.copy(roomId = mockRoomIden, requestedPrice = 200d, surchargeId = Some(1))
      val mockRequest = Seq(mockPriceAdjustmentRequest, mockPriceAdjustmentRequest1, mockPriceAdjustmentRequest2)
      val result = PriceAdjustmentCalculation.findRoomToAdjustPrice(mockCurrentUID, mockRequest, "THB")
      result should_== Seq(mockPriceAdjustmentRequest, mockPriceAdjustmentRequest2)
    }

    "not return room to adjust when currency not matched" in {
      val mockCurrentUID = mockUID
      val mockPriceAdjustmentRequest1 = mockPriceAdjustmentRequest.copy(roomId = "not match", requestCurrency = "USD")
      val mockPriceAdjustmentRequest2 =
        mockPriceAdjustmentRequest.copy(roomId = mockRoomIden, requestedPrice = 200d, surchargeId = Some(1))
      val mockRequest = Seq(mockPriceAdjustmentRequest, mockPriceAdjustmentRequest1, mockPriceAdjustmentRequest2)
      val result = PriceAdjustmentCalculation.findRoomToAdjustPrice(mockCurrentUID, mockRequest, "THB")
      result should_== Seq.empty
    }

    "return room to adjust when room id is matched" in {
      val mockCurrentUID = mockUID
      val mockPriceAdjustmentRequest1 = mockPriceAdjustmentRequest.copy(roomId = "not match")
      val mockPriceAdjustmentRequest2 =
        mockPriceAdjustmentRequest.copy(roomId = mockRoomIden, requestedPrice = 200d, surchargeId = Some(1))
      val mockRequest = Seq(mockPriceAdjustmentRequest, mockPriceAdjustmentRequest1, mockPriceAdjustmentRequest2)
      val result = PriceAdjustmentCalculation.findRoomToAdjustPrice(mockCurrentUID, mockRequest, "THB")
      result should_== Seq(mockPriceAdjustmentRequest, mockPriceAdjustmentRequest2)
    }

    "return empty when there is no match room" in {
      val mockCurrentUID = mockUID
      val mockPriceAdjustmentRequest1 = mockPriceAdjustmentRequest.copy(roomId = "mock")
      val mockPriceAdjustmentRequest2 =
        mockPriceAdjustmentRequest.copy(roomId = mockRoomIden, requestedPrice = 200d, surchargeId = Some(1))
      val mockRequest = Seq(mockPriceAdjustmentRequest, mockPriceAdjustmentRequest1, mockPriceAdjustmentRequest2)
      val result =
        PriceAdjustmentCalculation.findRoomToAdjustPrice(mockCurrentUID.copy("not match", None), mockRequest, "THB")
      result should_== Seq.empty
    }

    "return empty when there is no req" in {
      val mockRequest = Seq.empty
      val result = PriceAdjustmentCalculation.findRoomToAdjustPrice(mockUID, mockRequest, "THB")
      result should_== Seq.empty
    }

    "return empty when item to adjust is not in the whitelist" in {
      val mockCurrentUID = mockUID
      val mockPriceAdjustmentRequest1 = mockPriceAdjustmentRequest.copy(roomId = "not match", rateType = 200)
      val mockPriceAdjustmentRequest2 =
        mockPriceAdjustmentRequest.copy(roomId = mockRoomIden, requestedPrice = 200d, surchargeId = Some(1))
      val mockRequest = Seq(mockPriceAdjustmentRequest, mockPriceAdjustmentRequest1, mockPriceAdjustmentRequest2)
      val result = PriceAdjustmentCalculation.findRoomToAdjustPrice(mockCurrentUID, mockRequest, "THB")
      result should_== Seq.empty
    }
  }

  // local = THB
  // USD = USD
  // Request = CNY
  "calculatePriceDifferenceForAdjust" should {
    "calculate price diff correctly when request price is 1000 CNY for sell inclusive room charge with local input" in {
      // booking room sellin = 2105 thb 2 nights 1 room = 4210
      val inputItemBreakdownCurrency = CurrencyTypes.Local
      val bookingItemBreakdown = localItemBreakdown.values.toList // THB
      val adjustRequest = mockPriceAdjustmentRequest.copy(requestedPrice = 1000,
                                                          requestCurrency = bookingRequestCurrency,
      ) // sellin CNY = 4750 THB
      val result = PriceAdjustmentCalculation.calculatePriceDifferenceForAdjust(adjustRequest,
                                                                                bookingItemBreakdown,
                                                                                cnyToTHBcalculationInfo)
      // diff = 4750 - 4210
      result.differentPrice should_== 540
      result.currentPrice should_== 4210
      result.requestedPrice should_== 4750
      result.adjustRequest should_== adjustRequest
      result.requiredAdjustmentList.size should_== 2
    }

    "calculate price diff correctly when request price is 1000 CNY for sell inclusive room charge with usd input" in {
      // booking room sellin = 60.14 usd 2 nights 1 room = 120.28
      val inputItemBreakdownCurrency = CurrencyTypes.USD
      val bookingItemBreakdown = usdItemBreakdown.values.toList // USD
      val adjustRequest =
        mockPriceAdjustmentRequest.copy(requestedPrice = 1000, requestCurrency = "CNY") // sellin CNY = 150 USD
      val result = PriceAdjustmentCalculation.calculatePriceDifferenceForAdjust(adjustRequest,
                                                                                bookingItemBreakdown,
                                                                                cnyToUSDCalculationInfo)
      // diff = 150 - 120.28
      result.differentPrice should_== 29.72
      result.currentPrice should_== 120.28
      result.requestedPrice should_== 150
      result.adjustRequest should_== adjustRequest
      result.requiredAdjustmentList.size should_== 2
    }

    "calculate price diff correctly when request price less than current booking" in {
      // booking room sellin = 60.14 usd 2 nights 1 room = 120.28
      val inputItemBreakdownCurrency = CurrencyTypes.USD
      val bookingItemBreakdown = usdItemBreakdown.values.toList // USD
      val adjustRequest =
        mockPriceAdjustmentRequest.copy(requestedPrice = 500, requestCurrency = "CNY") // sellin CNY = 75 USD
      val result = PriceAdjustmentCalculation.calculatePriceDifferenceForAdjust(adjustRequest,
                                                                                bookingItemBreakdown,
                                                                                cnyToUSDCalculationInfo)
      // diff = 75 - 120.28
      result.differentPrice should_== -45.28
      result.currentPrice should_== 120.28
      result.requestedPrice should_== 75
      result.adjustRequest should_== adjustRequest
      result.requiredAdjustmentList.size should_== 2
    }

    "calculate price diff correctly when price adjustment request for surcharge" in {
      val day = new DateTime(2023, 12, 20, 0, 0)
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 1, los = 1, exRate = 1.0, precision = 2)
      val baseBreakdown = ItemBreakdown(
        date = None,
        id = BookingRateTypes.SellInclusive,
        typeId = BookingItemTypes.Room,
        subTypeId = None,
        usd = None,
        taxFeeId = 0,
        surchargeId = 0,
        quantity = 1,
        taxOrFeeQuantity = None,
        local = None,
        option = Some(ChargeOptions.Mandatory),
        applyTo = ApplyTypes.PRPN,
        base = "",
        taxable = "",
        valueType = 2,
        description = "",
        translation = "",
        requestAmount = None,
        roomNo = 1,
        taxProtoTypeId = Tax.DEFAULT_PROTOTYPE_ID,
      )
      val surchargeItem = baseBreakdown
        .toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
        .update(date = Some(day), id = BookingRateTypes.SellInclusive, local = Some(100d), surchargeId = 123)
      val reqCurrencyItemBreakdown = Seq(
        surchargeItem,
      )
      val reqCurrencyItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(reqCurrencyItemBreakdown)

      val priceAdjustmentRequest = PriceAdjustment(
        roomId = mockRoomUID,
        requestedPrice = 120d,
        chargeType = BookingItemTypes.Surcharge.i,
        rateType = BookingRateTypes.SellInclusive.i,
        applyType = ApplyTypes.PRPN.i,
        chargeOption = ChargeOptions.Mandatory.i,
        surchargeId = Some(123),
        requestCurrency = bookingRequestCurrency,
      )

      val result =
        PriceAdjustmentCalculation.calculatePriceDifferenceForAdjust(priceAdjustmentRequest,
                                                                     reqCurrencyItemBreakdownWithKey.values.toList,
                                                                     calculationInfo)

      result.differentPrice should_== 20.0
      result.requestedPrice should_== 120.0
      result.currentPrice should_== 100.0
      result.adjustRequest should_== priceAdjustmentRequest
      result.requiredAdjustmentList.size should_== 1
    }

    "calculate price diff correctly when request price is 1000 CNY and PRPN for sell inclusive room charge with local input" in {
      // booking room sellin = 2105 thb 2 nights 1 room = 4210
      val inputItemBreakdownCurrency = CurrencyTypes.Local
      val bookingItemBreakdown = localItemBreakdown.values.toList // THB
      val adjustRequest = mockPriceAdjustmentRequest.copy(requestedPrice = 500,
                                                          requestCurrency = bookingRequestCurrency,
                                                          applyType = ApplyTypes.PRPN.i,
      ) // sellin CNY = 4750 THB
      val result = PriceAdjustmentCalculation.calculatePriceDifferenceForAdjust(adjustRequest,
                                                                                bookingItemBreakdown,
                                                                                cnyToTHBcalculationInfo)
      // diff = 4750 - 4210
      result.differentPrice should_== 540
      result.currentPrice should_== 4210
      result.requestedPrice should_== 4750
      result.adjustRequest should_== adjustRequest
      result.requiredAdjustmentList.size should_== 2
    }
  }

  val day1 = mockDateTime
  val day2 = day1.plusDays(1)
  val day3 = day2.plusDays(1)

  val baseBreakdown = ItemBreakdown(
    date = Some(day1),
    id = BookingRateTypes.SellInclusive,
    typeId = BookingItemTypes.Room,
    subTypeId = None,
    usd = None,
    taxFeeId = 0,
    surchargeId = 0,
    quantity = 1,
    taxOrFeeQuantity = None,
    local = Some(100d),
    option = Some(ChargeOptions.Mandatory),
    applyTo = ApplyTypes.PRPN,
    base = "",
    taxable = "",
    valueType = 2,
    description = "",
    translation = "",
    requestAmount = None,
    roomNo = 1,
    taxProtoTypeId = Tax.DEFAULT_PROTOTYPE_ID,
  )

  "adjustFromPriceDifferent" should {

    val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 1, los = 3, exRate = 1.0, precision = 2)

    val mockPriceAdjustmentRequest = PriceAdjustment(
      roomId = mockRoomIden,
      requestedPrice = 100d,
      chargeType = BookingItemTypes.Room.i,
      rateType = BookingRateTypes.SellInclusive.i,
      applyType = ApplyTypes.PB.i,
      chargeOption = ChargeOptions.Mandatory.i,
      surchargeId = None,
      requestCurrency = "THB",
    )

    "book 1 room price is equal for each night" in {
      val roomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200d))
      val roomDay2 = baseBreakdown.update(date = Some(day2), local = Some(200d))
      val roomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200d))
      val adjustRequest = mockPriceAdjustmentRequest.copy(requestedPrice = 400)
      val priceAdjustmentInfo = PriceAdjustmentDifference(differentPrice = 200,
                                                          requestedPrice = 800,
                                                          currentPrice = 600,
                                                          adjustRequest = adjustRequest,
                                                          requiredAdjustmentList = Seq(roomDay1, roomDay2, roomDay3))

      val result = PriceAdjustmentCalculation.adjustFromPriceDifferent(Seq(priceAdjustmentInfo), calculationInfo)
      val expectedRoomDay1 = baseBreakdown.update(date = Some(day1), local = Some(266.67d))
      val expectedRoomDay2 = baseBreakdown.update(date = Some(day2), local = Some(266.66d))
      val expectedRoomDay3 = baseBreakdown.update(date = Some(day3), local = Some(266.67d))
      val expectedRes = Seq(expectedRoomDay1, expectedRoomDay2, expectedRoomDay3)
      result should_== expectedRes
    }

    "book 1 room price is equal for each night with very small diff" in {
      val roomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200d))
      val roomDay2 = baseBreakdown.update(date = Some(day2), local = Some(200d))
      val roomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200d))
      val adjustRequest = mockPriceAdjustmentRequest.copy(requestedPrice = 400)
      val priceAdjustmentInfo = PriceAdjustmentDifference(differentPrice = 0.01,
                                                          requestedPrice = 600.01,
                                                          currentPrice = 600,
                                                          adjustRequest = adjustRequest,
                                                          requiredAdjustmentList = Seq(roomDay1, roomDay2, roomDay3))

      val result = PriceAdjustmentCalculation.adjustFromPriceDifferent(Seq(priceAdjustmentInfo), calculationInfo)
      val expectedRoomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200.00d))
      val expectedRoomDay2 = baseBreakdown.update(date = Some(day2), local = Some(200.01d))
      val expectedRoomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200.00d))
      val expectedRes = Seq(expectedRoomDay1, expectedRoomDay2, expectedRoomDay3)
      result should_== expectedRes
    }

    "book 2 room price is equal for each night" in {
      val roomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200d))
      val roomDay2 = baseBreakdown.update(date = Some(day2), local = Some(200d))
      val roomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200d))
      val room2Day1 = baseBreakdown.update(date = Some(day1), local = Some(200d), roomNo = 2)
      val room2Day2 = baseBreakdown.update(date = Some(day2), local = Some(200d), roomNo = 2)
      val room2Day3 = baseBreakdown.update(date = Some(day3), local = Some(200d), roomNo = 2)
      val adjustRequest = mockPriceAdjustmentRequest.copy(requestedPrice = 400)
      val priceAdjustmentInfo = PriceAdjustmentDifference(
        differentPrice = 200,
        requestedPrice = 800,
        currentPrice = 600,
        adjustRequest = adjustRequest,
        requiredAdjustmentList = Seq(roomDay1, roomDay2, roomDay3, room2Day1, room2Day2, room2Day3),
      )

      val result = PriceAdjustmentCalculation.adjustFromPriceDifferent(Seq(priceAdjustmentInfo), calculationInfo)
      val expectedRoomDay1 = baseBreakdown.update(date = Some(day1), local = Some(233.33d))
      val expectedRoomDay2 = baseBreakdown.update(date = Some(day2), local = Some(233.33d))
      val expectedRoomDay3 = baseBreakdown.update(date = Some(day3), local = Some(233.34d))
      val expectedRoom2Day1 = baseBreakdown.update(date = Some(day1), local = Some(233.33d), roomNo = 2)
      val expectedRoom2Day2 = baseBreakdown.update(date = Some(day2), local = Some(233.34d), roomNo = 2)
      val expectedRoom2Day3 = baseBreakdown.update(date = Some(day3), local = Some(233.33d), roomNo = 2)
      val expectedRes = Seq(expectedRoomDay1,
                            expectedRoomDay2,
                            expectedRoomDay3,
                            expectedRoom2Day1,
                            expectedRoom2Day2,
                            expectedRoom2Day3)
      result should_== expectedRes
    }

    "book 1 room price is equal for each night with many decimal place price diff" in {
      val roomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200d))
      val roomDay2 = baseBreakdown.update(date = Some(day2), local = Some(200d))
      val roomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200d))
      val adjustRequest = mockPriceAdjustmentRequest.copy(requestedPrice = 400)
      val requestedPrice = 800.11111
      val currentPrice = 600
      val diff = requestedPrice - currentPrice
      val priceAdjustmentInfo = PriceAdjustmentDifference(
        differentPrice = diff,
        requestedPrice = requestedPrice,
        currentPrice = currentPrice,
        adjustRequest = adjustRequest,
        requiredAdjustmentList = Seq(roomDay1, roomDay2, roomDay3),
      )

      val result = PriceAdjustmentCalculation.adjustFromPriceDifferent(Seq(priceAdjustmentInfo), calculationInfo)
      val expectedRoomDay1 = baseBreakdown.update(date = Some(day1), local = Some(266.70d))
      val expectedRoomDay2 = baseBreakdown.update(date = Some(day2), local = Some(266.71d))
      val expectedRoomDay3 = baseBreakdown.update(date = Some(day3), local = Some(266.70d))
      val expectedRes = Seq(expectedRoomDay1, expectedRoomDay2, expectedRoomDay3)
      result should_== expectedRes
    }

    "book 1 room price is not equal for each night" in {
      val roomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200d))
      val roomDay2 = baseBreakdown.update(date = Some(day2), local = Some(400d))
      val roomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200d))
      val adjustRequest = mockPriceAdjustmentRequest.copy(requestedPrice = 400)
      val priceAdjustmentInfo = PriceAdjustmentDifference(differentPrice = 200,
                                                          requestedPrice = 800,
                                                          currentPrice = 600,
                                                          adjustRequest = adjustRequest,
                                                          requiredAdjustmentList = Seq(roomDay1, roomDay2, roomDay3))

      val result = PriceAdjustmentCalculation.adjustFromPriceDifferent(Seq(priceAdjustmentInfo), calculationInfo)
      val expectedRoomDay1 = baseBreakdown.update(date = Some(day1), local = Some(250))
      val expectedRoomDay2 = baseBreakdown.update(date = Some(day2), local = Some(500))
      val expectedRoomDay3 = baseBreakdown.update(date = Some(day3), local = Some(250))
      val expectedRes = Seq(expectedRoomDay1, expectedRoomDay2, expectedRoomDay3)
      result should_== expectedRes
    }

    "book 1 room price is equal for each night when price diff is negative" in {
      val roomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200d))
      val roomDay2 = baseBreakdown.update(date = Some(day2), local = Some(200d))
      val roomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200d))
      val adjustRequest = mockPriceAdjustmentRequest.copy(requestedPrice = 400)
      val priceAdjustmentInfo = PriceAdjustmentDifference(differentPrice = -200,
                                                          requestedPrice = 400,
                                                          currentPrice = 600,
                                                          adjustRequest = adjustRequest,
                                                          requiredAdjustmentList = Seq(roomDay1, roomDay2, roomDay3))

      val result = PriceAdjustmentCalculation.adjustFromPriceDifferent(Seq(priceAdjustmentInfo), calculationInfo)
      val expectedRoomDay1 = baseBreakdown.update(date = Some(day1), local = Some(133.33d))
      val expectedRoomDay2 = baseBreakdown.update(date = Some(day2), local = Some(133.34d))
      val expectedRoomDay3 = baseBreakdown.update(date = Some(day3), local = Some(133.33d))
      val expectedRes = Seq(expectedRoomDay1, expectedRoomDay2, expectedRoomDay3)
      result should_== expectedRes
    }
  }

  "modifyItemBreakdownWithPriceAdjustmentRequest" should {
    "return item 40 and adjust item correctly and not modified the item that no need adjustment" in {
      val bookingItemBreakdown = usdItemBreakdown // USD
      val adjustRequest =
        mockPriceAdjustmentRequest.copy(requestedPrice = 1000, requestCurrency = "CNY") // sellin CNY = 150 USD
      val result = PriceAdjustmentCalculation.modifyItemBreakdownWithPriceAdjustmentRequest(Seq(adjustRequest),
                                                                                            bookingItemBreakdown,
                                                                                            cnyToUSDCalculationInfo)
      val resItemAccRateDiff = result.values.filter(d => d.id == BookingRateTypes.AccRateDifference)
      val (modifiedRes, unmodifiedRes) = result.values.partition(d =>
        d.typeId.i == adjustRequest.chargeType && d.id.i == adjustRequest.rateType || d.id == BookingRateTypes.AccRateDifference)
      unmodifiedRes should_== bookingItemBreakdown.values.filterNot(d =>
        d.typeId.i == adjustRequest.chargeType && d.id.i == adjustRequest.rateType || d.id == BookingRateTypes.AccRateDifference)
      modifiedRes.size should_== 3
      unmodifiedRes.size should_== 32
      resItemAccRateDiff.head.local should_== Some(29.72)
      result.size should_== bookingItemBreakdown.size + 1 // to add item 40
    }

    "do nothing when diff is zero" in {
      val bookingItemBreakdown = localItemBreakdown // request THB
      val adjustRequest =
        mockPriceAdjustmentRequest.copy(requestedPrice = 4210,
                                        requestCurrency = "THB",
        ) // 2105 * 2 room = 4210 same as current price
      val result = PriceAdjustmentCalculation.modifyItemBreakdownWithPriceAdjustmentRequest(Seq(adjustRequest),
                                                                                            bookingItemBreakdown,
                                                                                            thbToTHBcalculationInfo)
      val resItemAccRateDiff = result.values.filter(d => d.id == BookingRateTypes.AccRateDifference)
      result should_== bookingItemBreakdown
      resItemAccRateDiff.isEmpty should_== true
      result.size should_== bookingItemBreakdown.size // no item 40
    }

    "return item 40 and adjust request is more than one item room sell in and room net in" in {
      val bookingItemBreakdown = localItemBreakdown // request THB

      val adjustRequestRoomSellIn = mockPriceAdjustmentRequest.copy(requestedPrice = 4500, requestCurrency = "THB")
      val adjustRequestRoomNetIn = mockPriceAdjustmentRequest.copy(requestedPrice = 4300,
                                                                   requestCurrency = "THB",
                                                                   rateType = BookingRateTypes.NetInclusive.i)
      // current item
      // 2105 * 2 room = 4210 same as current price
      // 2000 * 2 room = 4000 same as current price
      // expected diff sellin room = 4500 - 4210 = 290
      // expected diff netin room = 4300 - 4000 = 300
      // expected 1 room sellin = 2250 (290/2)
      // expected 1 room netin = 2150 (300/2)
      val result = PriceAdjustmentCalculation.modifyItemBreakdownWithPriceAdjustmentRequest(Seq(adjustRequestRoomSellIn,
                                                                                                adjustRequestRoomNetIn),
                                                                                            bookingItemBreakdown,
                                                                                            thbToTHBcalculationInfo)

      val resItemAccRateDiff =
        result.values.filter(d => d.id == BookingRateTypes.AccRateDifference && d.typeId == BookingItemTypes.None)
      val (modifiedRes, unmodifiedRes) = result.values.partition(d =>
        (d.typeId.i == adjustRequestRoomSellIn.chargeType && d.id.i == adjustRequestRoomSellIn.rateType) ||
        (d.typeId.i == adjustRequestRoomNetIn.chargeType && d.id.i == adjustRequestRoomNetIn.rateType) ||
        d.id == BookingRateTypes.AccRateDifference)

      unmodifiedRes should_== bookingItemBreakdown.values.filterNot(d =>
        (d.typeId.i == adjustRequestRoomSellIn.chargeType && d.id.i == adjustRequestRoomSellIn.rateType) ||
        (d.typeId.i == adjustRequestRoomNetIn.chargeType && d.id.i == adjustRequestRoomNetIn.rateType) ||
        d.id == BookingRateTypes.AccRateDifference)

      val sellInRoomRes = result.values
        .filter(d => d.typeId.i == adjustRequestRoomSellIn.chargeType && d.id.i == adjustRequestRoomSellIn.rateType)
        .toSeq
      val netInRoomRes = result.values
        .filter(d => d.typeId.i == adjustRequestRoomNetIn.chargeType && d.id.i == adjustRequestRoomNetIn.rateType)
        .toSeq

      val expectedSellInRoom = Seq(2250, 2250)
      val expectedNetInRoom = Seq(2150, 2150)

      sellInRoomRes.map(_.local.get) should_== expectedSellInRoom
      netInRoomRes.map(_.local.get) should_== expectedNetInRoom

      modifiedRes.size should_== 5
      unmodifiedRes.size should_== 30
      resItemAccRateDiff.head.local should_== Some(590)
      result.size should_== bookingItemBreakdown.size + 1 // to add item 40
    }

    "return item 40 and cxlRebook type id" in {
      val bookingItemBreakdown = localItemBreakdown // request THB

      val adjustRequestRoomSellIn = mockPriceAdjustmentRequest.copy(requestedPrice = 4500, requestCurrency = "THB")
      val adjustRequestRoomNetIn = mockPriceAdjustmentRequest.copy(requestedPrice = 4300,
                                                                   requestCurrency = "THB",
                                                                   rateType = BookingRateTypes.NetInclusive.i)
      // current item
      // 2105 * 2 room = 4210 same as current price
      // 2000 * 2 room = 4000 same as current price
      // expected diff sellin room = 4500 - 4210 = 290
      // expected diff netin room = 4300 - 4000 = 300
      // expected 1 room sellin = 2250 (290/2)
      // expected 1 room netin = 2150 (300/2)
      val result = PriceAdjustmentCalculation.modifyItemBreakdownWithPriceAdjustmentRequest(
        Seq(adjustRequestRoomSellIn, adjustRequestRoomNetIn),
        bookingItemBreakdown,
        thbToTHBcalculationInfo,
        BookingItemTypes.CancelRebook)

      val resItemAccRateDiff = result.values.filter(d =>
        d.id == BookingRateTypes.AccRateDifference && d.typeId == BookingItemTypes.CancelRebook)
      val (modifiedRes, unmodifiedRes) = result.values.partition(d =>
        (d.typeId.i == adjustRequestRoomSellIn.chargeType && d.id.i == adjustRequestRoomSellIn.rateType) ||
        (d.typeId.i == adjustRequestRoomNetIn.chargeType && d.id.i == adjustRequestRoomNetIn.rateType) ||
        d.id == BookingRateTypes.AccRateDifference)

      unmodifiedRes should_== bookingItemBreakdown.values.filterNot(d =>
        (d.typeId.i == adjustRequestRoomSellIn.chargeType && d.id.i == adjustRequestRoomSellIn.rateType) ||
        (d.typeId.i == adjustRequestRoomNetIn.chargeType && d.id.i == adjustRequestRoomNetIn.rateType) ||
        d.id == BookingRateTypes.AccRateDifference)

      val sellInRoomRes = result.values
        .filter(d => d.typeId.i == adjustRequestRoomSellIn.chargeType && d.id.i == adjustRequestRoomSellIn.rateType)
        .toSeq
      val netInRoomRes = result.values
        .filter(d => d.typeId.i == adjustRequestRoomNetIn.chargeType && d.id.i == adjustRequestRoomNetIn.rateType)
        .toSeq

      val expectedSellInRoom = Seq(2250, 2250)
      val expectedNetInRoom = Seq(2150, 2150)

      sellInRoomRes.map(_.local.get) should_== expectedSellInRoom
      netInRoomRes.map(_.local.get) should_== expectedNetInRoom

      modifiedRes.size should_== 5
      unmodifiedRes.size should_== 30
      resItemAccRateDiff.head.local should_== Some(590)
      result.size should_== bookingItemBreakdown.size + 1 // to add item 40
    }

    "return item 40 and cxlRebook type id with rounding adjustment" in {
      val roomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200d))
      val roomDay2 = baseBreakdown.update(date = Some(day2), local = Some(200d))
      val roomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200d))
      val surcharge1Day1 = baseBreakdown
        .toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
        .update(date = Some(day1), local = Some(33.33), quantity = 2, surchargeId = 111)
      val surcharge1Day2 = baseBreakdown
        .toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
        .update(date = Some(day2), local = Some(33.33), quantity = 2, surchargeId = 111)
      val surcharge1Day3 = baseBreakdown
        .toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
        .update(date = Some(day3), local = Some(33.33), quantity = 2, surchargeId = 111)
      val surcharge2Day1 = surcharge1Day1.update(local = Some(3d), quantity = 9, surchargeId = 112)
      val surcharge2Day2 = surcharge1Day2.update(local = Some(3d), quantity = 9, surchargeId = 112)
      val surcharge2Day3 = surcharge1Day3.update(local = Some(3d), quantity = 9, surchargeId = 112)
      val items = Seq(roomDay1,
                      roomDay2,
                      roomDay3,
                      surcharge1Day1,
                      surcharge1Day2,
                      surcharge1Day3,
                      surcharge2Day1,
                      surcharge2Day2,
                      surcharge2Day3)
      val itemsWithKey = ItemBreakdownHelper.generateItemKey(items)

      val adjustRequestRoomSellIn = mockPriceAdjustmentRequest.copy(requestedPrice = 3333, requestCurrency = "THB")
      val adjustRequestSurchargeSellIn1 = adjustRequestRoomSellIn.copy(requestedPrice = 9d,
                                                                       chargeType = BookingItemTypes.Surcharge.i,
                                                                       surchargeId = Some(111))

      val adjustRequestSurchargeSellIn2 = adjustRequestRoomSellIn.copy(requestedPrice = 13d,
                                                                       chargeType = BookingItemTypes.Surcharge.i,
                                                                       surchargeId = Some(112))
      val result = PriceAdjustmentCalculation.modifyItemBreakdownWithPriceAdjustmentRequest(
        Seq(adjustRequestRoomSellIn, adjustRequestSurchargeSellIn1, adjustRequestSurchargeSellIn2),
        itemsWithKey,
        thbToTHBcalculationInfo,
        typeId = BookingItemTypes.CancelRebook,
      )

      val sellInRoomRes =
        result.values.filter(d => d.typeId == BookingItemTypes.Room && d.id == BookingRateTypes.SellInclusive).toSeq

      val sellInSurchargeRes = result.values
        .filter(d => d.typeId == BookingItemTypes.Surcharge && d.id == BookingRateTypes.SellInclusive)
        .toSeq

      sellInRoomRes.map(_.local.get).sum should_== 3333.04
      sellInSurchargeRes.map(i => i.local.get * i.quantity).sum.roundp(2) should_== 21.96
      result.values.count(e =>
        e.id == BookingRateTypes.AccRateDifference && e.typeId == BookingItemTypes.CancelRebook) should_== 1
      result.values.size should_== 10
    }

    "(unlikely) return item 40 and None type" in {
      val roomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200d))
      val roomDay2 = baseBreakdown.update(date = Some(day2), local = Some(200d))
      val roomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200d))
      val surcharge1Day1 = baseBreakdown
        .toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
        .update(date = Some(day1), local = Some(33.33), quantity = 2, surchargeId = 111)
      val surcharge1Day2 = baseBreakdown
        .toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
        .update(date = Some(day2), local = Some(33.33), quantity = 2, surchargeId = 111)
      val surcharge1Day3 = baseBreakdown
        .toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
        .update(date = Some(day3), local = Some(33.33), quantity = 2, surchargeId = 111)
      val surcharge2Day1 = surcharge1Day1.update(local = Some(3d), quantity = 9, surchargeId = 112)
      val surcharge2Day2 = surcharge1Day2.update(local = Some(3d), quantity = 9, surchargeId = 112)
      val surcharge2Day3 = surcharge1Day3.update(local = Some(3d), quantity = 9, surchargeId = 112)
      val items = Seq(roomDay1,
                      roomDay2,
                      roomDay3,
                      surcharge1Day1,
                      surcharge1Day2,
                      surcharge1Day3,
                      surcharge2Day1,
                      surcharge2Day2,
                      surcharge2Day3)
      val itemsWithKey = ItemBreakdownHelper.generateItemKey(items)

      val adjustRequestRoomSellIn = mockPriceAdjustmentRequest.copy(requestedPrice = 3333, requestCurrency = "THB")
      val adjustRequestSurchargeSellIn1 = adjustRequestRoomSellIn.copy(requestedPrice = 9d,
                                                                       chargeType = BookingItemTypes.Surcharge.i,
                                                                       surchargeId = Some(111))

      val adjustRequestSurchargeSellIn2 = adjustRequestRoomSellIn.copy(requestedPrice = 13d,
                                                                       chargeType = BookingItemTypes.Surcharge.i,
                                                                       surchargeId = Some(112))
      val result = PriceAdjustmentCalculation.modifyItemBreakdownWithPriceAdjustmentRequest(
        Seq(adjustRequestRoomSellIn, adjustRequestSurchargeSellIn1, adjustRequestSurchargeSellIn2),
        itemsWithKey,
        thbToTHBcalculationInfo)

      val sellInRoomRes =
        result.values.filter(d => d.typeId == BookingItemTypes.Room && d.id == BookingRateTypes.SellInclusive).toSeq

      val sellInSurchargeRes = result.values
        .filter(d => d.typeId == BookingItemTypes.Surcharge && d.id == BookingRateTypes.SellInclusive)
        .toSeq

      sellInRoomRes.map(_.local.get).sum should_== 3333d
      sellInSurchargeRes.map(i => i.local.get * i.quantity).sum.roundp(2) should_== 21.96
      result.values.count(e =>
        e.id == BookingRateTypes.AccRateDifference && e.typeId == BookingItemTypes.None) should_== 1
      result.values.size should_== 10
    }
  }

  "adjustCxlRebookItems" should {
    val roomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200d))
    val roomDay2 = baseBreakdown.update(date = Some(day2), local = Some(200d))
    val roomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200d))
    val surcharge1Day1 = baseBreakdown
      .toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
      .update(date = Some(day1), local = Some(100d), quantity = 2, surchargeId = 111)
    val surcharge1Day2 = baseBreakdown
      .toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
      .update(date = Some(day2), local = Some(100d), quantity = 2, surchargeId = 111)
    val surcharge1Day3 = baseBreakdown
      .toSurchargeItemBreakdown(SurchargeTypeClassification.Fee)
      .update(date = Some(day3), local = Some(100d), quantity = 2, surchargeId = 111)
    val surcharge2Day1 = surcharge1Day1.update(local = Some(50), quantity = 4, surchargeId = 112)
    val surcharge2Day2 = surcharge1Day2.update(local = Some(50), quantity = 4, surchargeId = 112)
    val surcharge2Day3 = surcharge1Day3.update(local = Some(50), quantity = 4, surchargeId = 112)

    val exBedDay1 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(200d))
    val exBedDay2 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day2), local = Some(200d))
    val exBedDay3 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day3), local = Some(200d))

    val rtaEssDay1 = baseBreakdown.toRtaEssItemBreakdown.update(date = Some(day1), local = Some(200d))
    val rtaEssDay2 = baseBreakdown.toRtaEssItemBreakdown.update(date = Some(day2), local = Some(200d))
    val rtaEssDay3 = baseBreakdown.toRtaEssItemBreakdown.update(date = Some(day3), local = Some(200d))

    "adjust downlift price for Non USD " in {
      val res = PriceAdjustmentCalculation.adjustCxlRebookItems(
        Some(2000d),
        2,
        mockRoomUID,
        List(roomDay1, roomDay2, roomDay3),
        List(surcharge1Day1, surcharge1Day2, surcharge1Day3, surcharge2Day1, surcharge2Day2, surcharge2Day3),
        List(exBedDay1, exBedDay2, exBedDay3),
        List(rtaEssDay1, rtaEssDay2, rtaEssDay3),
        aValidConvertedCurrencyPrice,
        None,
        1,
        1,
      )
      res.get.size should_== 16
      res.head
        .filter(e => e.id == BookingRateTypes.SellInclusive)
        .map(e => e.local.get * e.quantity)
        .sum
        .roundp(2) should_== 2000d

      res.head
        .filter(e => e.id == BookingRateTypes.AccRateDifference)
        .map(e => e.local.get * e.quantity)
        .sum
        .roundp(2) should_== -1000d
    }

    "adjust uplift price for Non USD " in {
      val res = PriceAdjustmentCalculation.adjustCxlRebookItems(
        Some(4000d),
        2,
        mockRoomUID,
        List(roomDay1, roomDay2, roomDay3),
        List(surcharge1Day1, surcharge1Day2, surcharge1Day3, surcharge2Day1, surcharge2Day2, surcharge2Day3),
        List(exBedDay1, exBedDay2, exBedDay3),
        List(rtaEssDay1, rtaEssDay2, rtaEssDay3),
        aValidConvertedCurrencyPrice,
        None,
        1,
        1,
      )
      res.get.size should_== 16
      res.head
        .filter(e => e.id == BookingRateTypes.SellInclusive)
        .map(e => e.local.get * e.quantity)
        .sum
        .roundp(2) should_== 4000d

      res.head
        .filter(e => e.id == BookingRateTypes.AccRateDifference)
        .map(e => e.local.get * e.quantity)
        .sum
        .roundp(2) should_== 1000d
    }

    "adjust downlift price for USD " in {
      val mockConvertCurrency = aValidConvertedCurrencyPrice.withExchangeRate(ExchangeRate("USD", "USD", 1, 1, 2, 2, 2))
      val res = PriceAdjustmentCalculation.adjustCxlRebookItems(
        Some(4000d),
        2,
        mockRoomUID,
        List(roomDay1, roomDay2, roomDay3),
        List(surcharge1Day1, surcharge1Day2, surcharge1Day3, surcharge2Day1, surcharge2Day2, surcharge2Day3),
        List(exBedDay1, exBedDay2, exBedDay3),
        List(rtaEssDay1, rtaEssDay2, rtaEssDay3),
        mockConvertCurrency.build,
        Some(api.request.ReBookingRequest(0, None, 0d, None, None, None, Some(1999d), None, MatchUSD)),
        1,
        1,
      )
      res.get.size should_== 16
      res.head
        .filter(e => e.id == BookingRateTypes.SellInclusive)
        .map(e => e.local.get * e.quantity)
        .sum
        .roundp(2) should_== 1999d

      res.head
        .filter(e => e.id == BookingRateTypes.AccRateDifference)
        .map(e => e.local.get * e.quantity)
        .sum
        .roundp(2) should_== -1001d
    }

    "adjust uplift price for USD " in {
      val mockConvertCurrency = aValidConvertedCurrencyPrice.withExchangeRate(ExchangeRate("USD", "USD", 1, 1, 2, 2, 2))
      val res = PriceAdjustmentCalculation.adjustCxlRebookItems(
        Some(4000d),
        2,
        mockRoomUID,
        List(roomDay1, roomDay2, roomDay3),
        List(surcharge1Day1, surcharge1Day2, surcharge1Day3, surcharge2Day1, surcharge2Day2, surcharge2Day3),
        List(exBedDay1, exBedDay2, exBedDay3),
        List(rtaEssDay1, rtaEssDay2, rtaEssDay3),
        mockConvertCurrency.build,
        Some(api.request.ReBookingRequest(0, None, 0d, None, None, None, Some(3999), None, MatchUSD)),
        1,
        1,
      )
      res.get.size should_== 16
      res.head
        .filter(e => e.id == BookingRateTypes.SellInclusive)
        .map(e => e.local.get * e.quantity)
        .sum
        .roundp(2) should_== 3999d

      res.head
        .filter(e => e.id == BookingRateTypes.AccRateDifference)
        .map(e => e.local.get * e.quantity)
        .sum
        .roundp(2) should_== 999d
    }
  }

  "adjustRoundingError" should {
    val roomDay1 = baseBreakdown.update(date = Some(day1), local = Some(200d))
    val roomDay2 = baseBreakdown.update(date = Some(day2), local = Some(200d))
    val roomDay3 = baseBreakdown.update(date = Some(day3), local = Some(200d))
    val adjustRequest = mockPriceAdjustmentRequest.copy(requestedPrice = 400)
    val priceAdjustmentInfo = PriceAdjustmentDifference(differentPrice = -200,
                                                        requestedPrice = 400,
                                                        currentPrice = 600,
                                                        adjustRequest = adjustRequest,
                                                        requiredAdjustmentList = Seq(roomDay1, roomDay2, roomDay3))
    "adjust only SellIn" in {
      val res = PriceAdjustmentCalculation.adjustRoundingError(Seq(roomDay1.update(local = Some(133.33)),
                                                                   roomDay2.update(local = Some(133.33)),
                                                                   roomDay3.update(local = Some(133.33))),
                                                               Seq(priceAdjustmentInfo),
                                                               2)
      res.size should_== 3
      res.head.local should_== Some(133.34)
    }

    // this is unlikely because DFRateDiff can not be differentiate by BookingRateType
    "adjust both SellIn & NetIn" in {
      val roomNetInDay1 = roomDay1.update(id = BookingRateTypes.NetInclusive, local = Some(133.33))
      val roomNetInDay2 = roomDay2.update(id = BookingRateTypes.NetInclusive, local = Some(133.33))
      val roomNetInDay3 = roomDay3.update(id = BookingRateTypes.NetInclusive, local = Some(133.33))
      val priceAdjustmentInfoForNetIn = PriceAdjustmentDifference(
        differentPrice = -200,
        requestedPrice = 400,
        currentPrice = 600,
        adjustRequest = adjustRequest.copy(rateType = BookingRateTypes.NetInclusive.i),
        requiredAdjustmentList = Seq(roomNetInDay1, roomNetInDay2, roomNetInDay3),
      )
      val res = PriceAdjustmentCalculation.adjustRoundingError(
        Seq(
          roomDay1.update(local = Some(133.33)),
          roomDay2.update(local = Some(133.33)),
          roomDay3.update(local = Some(133.33)),
          roomNetInDay1,
          roomNetInDay2,
          roomNetInDay3,
        ),
        Seq(priceAdjustmentInfo, priceAdjustmentInfoForNetIn),
        2,
      )
      res.size should_== 6
      res.filter(_.id == BookingRateTypes.SellInclusive).minBy(_.date).local should_== Some(133.34)
      res.filter(_.id == BookingRateTypes.NetInclusive).minBy(_.date).local should_== Some(133.34)
    }
  }

  "getPriceDiffAdjustmentByChargeType" should {
    "positive" in {
      PriceAdjustmentCalculation.getPriceDiffAdjustmentByChargeType(mockRoomUID,
                                                                    2,
                                                                    100d,
                                                                    10,
                                                                    400,
                                                                    BookingItemTypes.ExtraBed) should_== (List(
        PriceAdjustment("63ea441a-9187-f120-f5dc-139abb1e2413", 102.5, 4, 12, "PB", 1, None, "USD")), 2.5)

    }
    "zero" in {
      PriceAdjustmentCalculation.getPriceDiffAdjustmentByChargeType(mockRoomUID,
                                                                    2,
                                                                    0d,
                                                                    10,
                                                                    400,
                                                                    BookingItemTypes.ExtraBed) should_== (Nil, 0d)
    }
    "negative" in {
      PriceAdjustmentCalculation.getPriceDiffAdjustmentByChargeType(mockRoomUID,
                                                                    2,
                                                                    100d,
                                                                    -10,
                                                                    400,
                                                                    BookingItemTypes.ExtraBed) should_== (List(
        PriceAdjustment("63ea441a-9187-f120-f5dc-139abb1e2413", 97.5, 4, 12, "PB", 1, None, "USD")), -2.5)
    }

  }
  "getSurchargePriceDiffAdjustment" should {
    "positive" in {
      PriceAdjustmentCalculation.getSurchargePriceDiffAdjustment(mockRoomUID,
                                                                 2,
                                                                 100d,
                                                                 10,
                                                                 400,
                                                                 Map(111 -> 60d, 112 -> 40d)) should_== (List(
        PriceAdjustment("63ea441a-9187-f120-f5dc-139abb1e2413", 41d, 2, 12, "PB", 1, Some(112), "USD"),
        PriceAdjustment("63ea441a-9187-f120-f5dc-139abb1e2413", 61.5, 2, 12, "PB", 1, Some(111), "USD"),
      ), 2.5)
    }
    "zero" in {

      PriceAdjustmentCalculation.getSurchargePriceDiffAdjustment(mockRoomUID,
                                                                 2,
                                                                 0d,
                                                                 10d,
                                                                 400,
                                                                 Map(111 -> 60d, 112 -> 40d)) should_== (Nil, 0d)
    }
    "negative" in {
      PriceAdjustmentCalculation.getSurchargePriceDiffAdjustment(mockRoomUID,
                                                                 2,
                                                                 100d,
                                                                 -10d,
                                                                 400,
                                                                 Map(111 -> 60d, 112 -> 40d)) should_== (List(
        PriceAdjustment("63ea441a-9187-f120-f5dc-139abb1e2413", 39d, 2, 12, "PB", 1, Some(112), "USD"),
        PriceAdjustment("63ea441a-9187-f120-f5dc-139abb1e2413", 58.5d, 2, 12, "PB", 1, Some(111), "USD"),
      ), -2.5)
    }
  }
  "getReqAmountByRoomRate" should {
    val numberOfRoom = 2
    val lengthOfStay = 3

    "getReqAmountByRoomRate when ApplyTypes PN" in {
      val reqAmount = 100.0
      val applyType = ApplyTypes.PN
      val calculationInfo = PriceAdjustmentCalculationInfo(numberOfRoom, lengthOfStay, exRate = 1.5, precision = 2)

      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)
      result shouldEqual ((reqAmount / 3) * 1.5).roundp(2)
    }

    "getReqAmountByRoomRate when ApplyTypes PRPB" in {
      val reqAmount = 100.0
      val applyType = ApplyTypes.PRPB
      val calculationInfo = PriceAdjustmentCalculationInfo(numberOfRoom, lengthOfStay, exRate = 1.5, precision = 2)

      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)
      result shouldEqual ((reqAmount / 2) * 1.5).roundp(2)
    }

    "getReqAmountByRoomRate when ApplyTypes PRPN" in {
      val reqAmount = 100.0
      val applyType = ApplyTypes.PRPN
      val calculationInfo = PriceAdjustmentCalculationInfo(numberOfRoom, lengthOfStay, exRate = 1.5, precision = 2)

      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)
      result shouldEqual ((reqAmount / 6) * 1.5).roundp(2)
    }

    "getReqAmountByRoomRate when ApplyTypes PB" in {
      val reqAmount = 100.0
      val applyType = ApplyTypes.PB
      val calculationInfo = PriceAdjustmentCalculationInfo(numberOfRoom, lengthOfStay, exRate = 1.5, precision = 2)

      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)
      result shouldEqual ((reqAmount / 1) * 1.5).roundp(2)
    }

    "getReqAmountByRoomRate when Zero reqAmount" in {
      val reqAmount = 0.0
      val applyType = ApplyTypes.PRPN
      val calculationInfo = PriceAdjustmentCalculationInfo(numberOfRoom, lengthOfStay, exRate = 1.5, precision = 2)

      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)
      result shouldEqual 0.0
    }

    "getReqAmountByRoomRate when los = 0" in {
      val reqAmount = 100.0
      val applyType = ApplyTypes.PRPN
      val calculationInfo = PriceAdjustmentCalculationInfo(numberOfRoom, los = 0, exRate = 1.5, precision = 2)

      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)
      result shouldEqual ((reqAmount / 1) * 1.5).roundp(2)
    }

    "getReqAmountByRoomRate when noRoom = 0" in {
      val reqAmount = 100.0
      val applyType = ApplyTypes.PRPN
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 0, lengthOfStay, exRate = 1.5, precision = 2)

      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)
      result shouldEqual ((reqAmount / 1) * 1.5).roundp(2)
    }

    "getReqAmountByRoomRate when los = 0 and noRoom = 0" in {
      val reqAmount = 100.0
      val applyType = ApplyTypes.PRPN
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 0, los = 0, exRate = 1.5, precision = 2)

      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)
      result shouldEqual ((reqAmount / 1) * 1.5).roundp(2)
    }

    "getReqAmountByRoomRate when exRate Negative" in {
      val reqAmount = 100.0
      val applyType = ApplyTypes.PRPB
      val calculationInfo = PriceAdjustmentCalculationInfo(numberOfRoom, lengthOfStay, exRate = -1.5, precision = 2)

      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)
      result shouldEqual ((reqAmount / 2) * -1.5).roundp(2)
    }

    "getReqAmountByRoomRate when Negative reqAmount" in {
      val reqAmount = -100.0
      val applyType = ApplyTypes.PRPB
      val calculationInfo = PriceAdjustmentCalculationInfo(numberOfRoom, lengthOfStay, exRate = 1.5, precision = 2)

      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)
      result shouldEqual ((reqAmount / 2) * 1.5).roundp(2)
    }

    "getReqAmountByRoomRate when ApplyTypes PB" in {
      val reqAmount = 100.0
      val applyType = ApplyTypes.PB
      val calculationInfo = PriceAdjustmentCalculationInfo(numberOfRoom, lengthOfStay, exRate = 1.5, precision = 2)
      val result = PriceAdjustmentCalculation.getReqAmountByRoomRate(reqAmount, applyType, calculationInfo)

      result shouldEqual ((reqAmount / 1) * 1.5).roundp(2)
    }
  }

  "getPriceAdjustmentRequestWithExtraBed" should {
    val applyType = ApplyTypes.PRPN
    val day1 = new DateTime(2023, 12, 20, 0, 0)
    val day2 = day1.plusDays(1)
    val day3 = day2.plusDays(1)

    val baseBreakdown = ItemBreakdown(
      date = Some(day1),
      id = BookingRateTypes.SellInclusive,
      typeId = BookingItemTypes.Room,
      subTypeId = None,
      usd = None,
      taxFeeId = 0,
      surchargeId = 0,
      quantity = 1,
      taxOrFeeQuantity = None,
      local = Some(100d),
      option = Some(ChargeOptions.Mandatory),
      applyTo = ApplyTypes.PRPN,
      base = "",
      taxable = "",
      valueType = 2,
      description = "",
      translation = "",
      requestAmount = None,
      roomNo = 1,
      taxProtoTypeId = Tax.DEFAULT_PROTOTYPE_ID,
    )

    "when booking request for 1 room with 1 extra bed for 1 day" in {
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 1, los = 1, exRate = 1.0, precision = 2)

      val room1Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d))
      val extraBed1Day1 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(50d))

      val reqCurrencyItemBreakdown = Seq(
        room1Day1,
        extraBed1Day1,
      )
      val reqCurrencyItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(reqCurrencyItemBreakdown)

      val priceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 240d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ))
      val result = PriceAdjustmentCalculation.getPriceAdjustmentRequestWithExtraBed(priceAdjustmentRequest,
                                                                                    reqCurrencyItemBreakdownWithKey,
                                                                                    calculationInfo)
      val migratedPriceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 190d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 50d,
          chargeType = BookingItemTypes.ExtraBed.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )

      result should_== migratedPriceAdjustmentRequest
    }

    "when booking request for 2 room with 2 extra bed for 1 day" in {
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 2, los = 1, exRate = 1.0, precision = 2)

      val room1Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d))
      val extraBed1Day1 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(50d))

      val room2Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d), roomNo = 2)
      val extraBed2Day1 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(50d), roomNo = 2)

      val reqCurrencyItemBreakdown = Seq(
        room1Day1,
        extraBed1Day1,
        room2Day1,
        extraBed2Day1,
      )
      val reqCurrencyItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(reqCurrencyItemBreakdown)

      val priceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 240d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ))
      val result = PriceAdjustmentCalculation.getPriceAdjustmentRequestWithExtraBed(priceAdjustmentRequest,
                                                                                    reqCurrencyItemBreakdownWithKey,
                                                                                    calculationInfo)
      val migratedPriceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 190d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 50d,
          chargeType = BookingItemTypes.ExtraBed.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )

      result should_== migratedPriceAdjustmentRequest
    }

    "when booking request for 2 room with 1 extra bed for 1 day" in {
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 2, los = 1, exRate = 1.0, precision = 2)

      val room1Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d))
      val extraBed1Day1 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(50d))

      val room2Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d), roomNo = 2)
      val extraBed2Day1 =
        baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(50d), quantity = 0, roomNo = 2)

      val reqCurrencyItemBreakdown = Seq(
        room1Day1,
        extraBed1Day1,
        room2Day1,
        extraBed2Day1,
      )
      val reqCurrencyItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(reqCurrencyItemBreakdown)

      val priceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 215d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ))
      val result = PriceAdjustmentCalculation.getPriceAdjustmentRequestWithExtraBed(priceAdjustmentRequest,
                                                                                    reqCurrencyItemBreakdownWithKey,
                                                                                    calculationInfo)
      val migratedPriceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 190d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 25d,
          chargeType = BookingItemTypes.ExtraBed.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )

      result should_== migratedPriceAdjustmentRequest
    }

    "when booking request for 1 room with 1 extra bed for 3 day" in {
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 1, los = 3, exRate = 1.0, precision = 2)

      val room1Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d))
      val extraBed1Day1 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(50d))

      val room1Day2 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day2), local = Some(200d))
      val extraBed1Day2 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day2), local = Some(50d))

      val room1Day3 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day3), local = Some(200d))
      val extraBed1Day3 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day3), local = Some(50d))

      val reqCurrencyItemBreakdown = Seq(
        room1Day1,
        extraBed1Day1,
        room1Day2,
        extraBed1Day2,
        room1Day3,
        extraBed1Day3,
      )
      val reqCurrencyItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(reqCurrencyItemBreakdown)

      val priceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 240d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ))
      val result = PriceAdjustmentCalculation.getPriceAdjustmentRequestWithExtraBed(priceAdjustmentRequest,
                                                                                    reqCurrencyItemBreakdownWithKey,
                                                                                    calculationInfo)
      val migratedPriceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 190d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 50d,
          chargeType = BookingItemTypes.ExtraBed.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )

      result should_== migratedPriceAdjustmentRequest
    }

    "when booking request for 2 room with 2 extra bed for 3 day" in {
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 2, los = 3, exRate = 1.0, precision = 2)

      val room1Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d))
      val extraBed1Day1 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(50d))
      val room2Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d), roomNo = 2)
      val extraBed2Day1 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(50d), roomNo = 2)

      val room1Day2 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day2), local = Some(200d))
      val extraBed1Day2 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day2), local = Some(50d))
      val room2Day2 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day2), local = Some(200d), roomNo = 2)
      val extraBed2Day2 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day2), local = Some(50d), roomNo = 2)

      val room1Day3 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day3), local = Some(200d))
      val extraBed1Day3 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day3), local = Some(50d))
      val room2Day3 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day3), local = Some(200d), roomNo = 2)
      val extraBed2Day3 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day3), local = Some(50d), roomNo = 2)

      val reqCurrencyItemBreakdown = Seq(
        room1Day1,
        extraBed1Day1,
        room2Day1,
        extraBed2Day1,
        room1Day2,
        extraBed1Day2,
        room2Day2,
        extraBed2Day2,
        room1Day3,
        extraBed1Day3,
        room2Day3,
        extraBed2Day3,
      )
      val reqCurrencyItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(reqCurrencyItemBreakdown)

      val priceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 240d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ))
      val result = PriceAdjustmentCalculation.getPriceAdjustmentRequestWithExtraBed(priceAdjustmentRequest,
                                                                                    reqCurrencyItemBreakdownWithKey,
                                                                                    calculationInfo)
      val migratedPriceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 190d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 50d,
          chargeType = BookingItemTypes.ExtraBed.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )

      result should_== migratedPriceAdjustmentRequest
    }

    "when booking request for 2 room with 1 extra bed for 3 day" in {
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 2, los = 3, exRate = 1.0, precision = 2)

      val room1Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d))
      val extraBed1Day1 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(50d))
      val room2Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d), roomNo = 2)
      val extraBed2Day1 =
        baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day1), local = Some(50d), quantity = 0, roomNo = 2)

      val room1Day2 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day2), local = Some(200d))
      val extraBed1Day2 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day2), local = Some(50d))
      val room2Day2 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day2), local = Some(200d), roomNo = 2)
      val extraBed2Day2 =
        baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day2), local = Some(50d), quantity = 0, roomNo = 2)

      val room1Day3 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day3), local = Some(200d))
      val extraBed1Day3 = baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day3), local = Some(50d))
      val room2Day3 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day3), local = Some(200d), roomNo = 2)
      val extraBed2Day3 =
        baseBreakdown.toExtraBedItemBreakdown.update(date = Some(day3), local = Some(50d), quantity = 0, roomNo = 2)

      val reqCurrencyItemBreakdown = Seq(
        room1Day1,
        extraBed1Day1,
        room2Day1,
        extraBed2Day1,
        room1Day2,
        extraBed1Day2,
        room2Day2,
        extraBed2Day2,
        room1Day3,
        extraBed1Day3,
        room2Day3,
        extraBed2Day3,
      )
      val reqCurrencyItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(reqCurrencyItemBreakdown)

      val priceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 215d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ))
      val result = PriceAdjustmentCalculation.getPriceAdjustmentRequestWithExtraBed(priceAdjustmentRequest,
                                                                                    reqCurrencyItemBreakdownWithKey,
                                                                                    calculationInfo)
      val migratedPriceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 190d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 25d,
          chargeType = BookingItemTypes.ExtraBed.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )

      result should_== migratedPriceAdjustmentRequest
    }

    "when priceadjustment request with valid surcharge req" in {
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 1, los = 1, exRate = 1.0, precision = 2)
      val room1Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d))
      val reqCurrencyItemBreakdown = Seq(
        room1Day1,
      )
      val reqCurrencyItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(reqCurrencyItemBreakdown)

      val priceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 240d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 20d,
          chargeType = BookingItemTypes.Surcharge.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PB.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )
      val result = PriceAdjustmentCalculation.getPriceAdjustmentRequestWithExtraBed(priceAdjustmentRequest,
                                                                                    reqCurrencyItemBreakdownWithKey,
                                                                                    calculationInfo)
      val migratedPriceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 240d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 20d,
          chargeType = BookingItemTypes.Surcharge.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PB.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )
      result should_== migratedPriceAdjustmentRequest
    }

    "when priceadjustment request with invalid surcharge req" in {
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 1, los = 1, exRate = 1.0, precision = 2)
      val room1Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d))
      val reqCurrencyItemBreakdown = Seq(
        room1Day1,
      )
      val reqCurrencyItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(reqCurrencyItemBreakdown)

      val priceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 240d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 20d,
          chargeType = BookingItemTypes.Other.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )
      val result = PriceAdjustmentCalculation.getPriceAdjustmentRequestWithExtraBed(priceAdjustmentRequest,
                                                                                    reqCurrencyItemBreakdownWithKey,
                                                                                    calculationInfo)
      val migratedPriceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 240d,
          chargeType = BookingItemTypes.Room.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )
      result should_== migratedPriceAdjustmentRequest
    }

    "when priceadjustment request is invalid" in {
      val calculationInfo = PriceAdjustmentCalculationInfo(noRoom = 1, los = 1, exRate = 1.0, precision = 2)
      val room1Day1 = baseBreakdown.toRoomItemBreakdown.update(date = Some(day1), local = Some(200d))
      val reqCurrencyItemBreakdown = Seq(
        room1Day1,
      )
      val reqCurrencyItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(reqCurrencyItemBreakdown)

      val priceAdjustmentRequest = Seq(
        PriceAdjustment(
          roomId = mockRoomUID,
          requestedPrice = 240d,
          chargeType = BookingItemTypes.Other.i,
          rateType = BookingRateTypes.SellInclusive.i,
          applyType = ApplyTypes.PRPN.i,
          chargeOption = ChargeOptions.Mandatory.i,
          surchargeId = None,
          requestCurrency = bookingRequestCurrency,
        ),
      )
      val result = PriceAdjustmentCalculation.getPriceAdjustmentRequestWithExtraBed(priceAdjustmentRequest,
                                                                                    reqCurrencyItemBreakdownWithKey,
                                                                                    calculationInfo)
      val migratedPriceAdjustmentRequest = Seq()
      result should_== migratedPriceAdjustmentRequest
    }
  }
}
