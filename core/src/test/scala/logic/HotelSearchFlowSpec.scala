package logic

import api.request.{BookingFilter, FeatureRequest, _}
import com.agoda.commons.dynamic.state.State
import com.agoda.papi.enums.campaign.CampaignDiscountTypes
import com.agoda.papi.enums.room.{ChargeOption, SubChargeType}
import com.agoda.papi.pricing.calculation.CalculationStatsLoggerImpl
import com.agoda.papi.pricing.configuration.{BookingFormOfferSwapSettingsProducer, DegradationSettingProducer}
import com.agoda.papi.pricing.core.filters.request.RequestedCurrencyFilter
import com.agoda.papi.pricing.discounting.models.request.rewards.{
  Rebate => DiscountingRebate,
  RebateSet => DiscountingRebateSet,
}
import com.agoda.papi.pricing.discounting.models.response.{PromotionDiscounts => DiscountingPromotionDiscounts}
import com.agoda.papi.pricing.flow.postsearch.{RefreshPaymentInfoStep, RefreshPaymentInfoStepImpl}
import com.agoda.papi.pricing.flow.{
  Flow<PERSON><PERSON>xtB<PERSON>er,
  FlowContextImpl,
  SellDiscountRoomDuplicationService,
  SoybeanAndDiscountStep,
}
import com.agoda.papi.pricing.metadata.model.HotelDataWithHMCData
import com.agoda.papi.pricing.metadata.model.{HotelDataWithHMCData, HotelId}
import com.agoda.papi.pricing.metadata.service.PropertyMetadataService
import com.agoda.papi.pricing.metadata.{HotelData, MasterRoomInfo}
import com.agoda.papi.pricing.supply.SupplyModuleImplicits
import com.agoda.papi.pricing.supply.commissions.models.{AGXCommissionAdjustmentDetail, AGXHotel}
import com.agoda.papi.pricing.supply.configuration.DegradationSettingProducerMock
import com.agoda.papi.ypl.models.{YplRateFence, YplMasterChannel => DFMasterChannel}
import com.agoda.platform.pricing.models.utils.DFTestDataBuilders
import com.agoda.platform.pricing.models.utils.YPLTestDataBuilders.{aValidYplHotel, aValidYplRateCategory, aValidYplRoom}
import com.agoda.pricestream.models.enumeration.PastBookingInformationType
import com.agoda.pricestream.models.response.{
  PastBookingInfo,
  PastBookingInfoResponse,
  PastBookingInfoSummary,
  DisplayPrice => PriceStreamDisplayPrice,
}
import com.agoda.protobuf.hotelcontext.HotelContext
import com.agoda.protobuf.masterhotelcontext.{MasterHotelContext, MasterHotelInfo}
import com.agoda.utils.flow.PropertyContext
import com.agoda.utils.hadoop.MessageEnvelope
import com.agoda.utils.monitoring.{AggregateReporter, NoOpReportingService}
import com.typesafe.config.ConfigFactory
import external.loyalty.ExternalLoyaltyService
import external.pricing.{DispatchInfo, HotelDispatchInfo}
import logic.WithMeta.MetaHotels
import mocks.{SellDiscountRoomDuplicationServiceMock, _}
import models.consts.{ABTest, DMC}
import models.db.{HotelId, SupplierId}
import models.diagnostics.RequestFlowState
import models.enums.ReBookingActionType.MatchLocal
import models.enums.{ReBookingActionType, SupplierType}
import models.flow.{ExperimentDataHolder, FlowDataContext, Variant}
import models.pricing.{Hotel, Room, _}
import models.starfruit._
import models.whitelabel._
import models.{
  DFExperiment,
  HotelInfo,
  PastBookingPrices,
  SearchTypes,
  PastBookingInfoSummary => DFPastBookingInfoSummary,
}
import org.mockito.Mockito.when
import org.scalatest.prop.Tables
import org.specs2.concurrent.ExecutionEnv
import org.specs2.mock.{Mockito => MockitoSpec}
import org.specs2.mutable.SpecificationWithJUnit
import org.specs2.specification.Scope
import org.specs2.specification.core.Fragments
import services.state.PriceStateService
import services.variableTax.VariableTaxServiceImpl
import services.{ConsumerFintechProductService, PriceStreamService}
import settings.PropertyExperimentSettingsProducer

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}

/**
  * Created by ekalashnikov on 6/26/17.
  */
//scalastyle:off null
class HotelSearchFlowSpec(implicit env: ExecutionEnv)
  extends SpecificationWithJUnit
    with DFTestDataBuilders
    with MockitoSpec
    with Tables {

  trait BaseHotelSearchFlowMock extends HotelSearchFlow[FlowContextImpl] {}

  "Hotel Search Flow" should {
    trait TestFlow
      extends HotelSearchFlow[FlowContextImpl]
        with LogicImpl[FlowContextImpl]
        with PromotionCalculationMock
        with SupplierAPIDataServiceMock
        with BaseHotelSearchFlowMock
        with PriceChangeServiceMock
        with AllMock
        with DmcMetaDataServiceMock
        with AsoBenefitDiscountServiceMock
        with ChildRateTypeDataServiceMock
        with VariableTaxServiceImpl {

      override implicit def ec: ExecutionContext = ExecutionContext.global

      override val externalLoyaltyService: ExternalLoyaltyService = mock[ExternalLoyaltyService]
      override val consumerFintechProductService: ConsumerFintechProductService = mock[ConsumerFintechProductService]
      override val soybeanAndDiscountStep: SoybeanAndDiscountStep = mock[SoybeanAndDiscountStep]
      override val sellDiscountRoomDuplicationService: SellDiscountRoomDuplicationServiceMock =
        new SellDiscountRoomDuplicationServiceMock {}

      override protected val requestedCurrencyFilter: RequestedCurrencyFilter = BypassedRequestedCurrencyFilter
      override val refreshPaymentInfoStep: RefreshPaymentInfoStep =
        new RefreshPaymentInfoStepImpl(mock[PriceStateService])
      override val bookingFormOfferSwapSettingsProducer: BookingFormOfferSwapSettingsProducer =
        mock[BookingFormOfferSwapSettingsProducer]
      override protected val propertyExperimentSettingsProducer: PropertyExperimentSettingsProducer =
        PropertyExperimentSettingsProducer.disabled

      override def aggregateReporter: AggregateReporter = new AggregateReporterMock {}

      val priceStreamServiceMock = mock[PriceStreamService]
      override val priceStreamService = priceStreamServiceMock
    }
    val flow = new TestFlow {}

    "pack results: should return not ready or available hotels only" in {
      val br = aValidBaseRequest
      val ctx = FlowContextBuilder(br)
      ctx.requestFlowDiagnosticContextData.requestFlowDiagnosticsMap.put((1, 12345),
                                                                         List(RequestFlowState.HotelIsBlocked))

      val supplierInfoNotReady = SupplierInfo(1, SupplierType.Push, isReady = false, isAvailable = false)
      val supplierInfoReady = supplierInfoNotReady.copy(id = 2, isReady = true)

      val emptyHotel = aValidHotel.withId(1).withExchangeRate().withRooms(Nil).withSupplierSummary(Nil).build
      val isNotReadyHotel =
        aValidHotel.withId(2).withExchangeRate().withRooms(Nil).withSupplierSummary(List(supplierInfoNotReady)).build
      val isReadyAvailableHotel = aValidHotel
        .withId(30)
        .withExchangeRate()
        .withRoom(aValidRoom.withSupplierId(2))
        .withSupplierSummary(List(supplierInfoReady))
        .build
      val isReadyUnavailableHotel =
        aValidHotel.withId(4).withExchangeRate().withRooms(Nil).withSupplierSummary(List(supplierInfoReady)).build
      val mixedNotReadyAndAvailableHotel = aValidHotel
        .withId(5)
        .withExchangeRate()
        .withRoom(aValidRoom.withSupplierId(2))
        .withSupplierSummary(List(supplierInfoNotReady, supplierInfoReady))
        .build

      val data = Seq[Hotel](emptyHotel,
                            isNotReadyHotel,
                            isReadyAvailableHotel,
                            isReadyUnavailableHotel,
                            mixedNotReadyAndAvailableHotel)
      val envelope = flow.packResults(flow.Data(data, ctx, null))

      envelope.hotels should_== Seq(isNotReadyHotel, isReadyAvailableHotel, mixedNotReadyAndAvailableHotel)
      envelope.notReadyHotels should_== List(2)
      envelope.isBlocked should_== Some(true)
      envelope.isCompleted should_== (true)
    }

    "fetchHotelMetaCache" in {

      trait TestScope extends Scope {

        val propertyMetadataService: PropertyMetadataService = mock[PropertyMetadataService]

        val flow: HotelSearchFlow[FlowContextImpl]
          with LogicImpl[FlowContextImpl]
          with PromotionCalculationMock
          with SupplierAPIDataServiceMock
          with PriceChangeServiceMock
          with AllMock
          with DmcMetaDataServiceMock
          with AsoBenefitDiscountServiceMock
          with ChildRateTypeDataServiceMock
          with VariableTaxServiceImpl = new HotelSearchFlow[FlowContextImpl]
          with BaseHotelSearchFlowMock
          with LogicImpl[FlowContextImpl]
          with PromotionCalculationMock
          with SupplierAPIDataServiceMock
          with PriceChangeServiceMock
          with AllMock
          with DmcMetaDataServiceMock
          with AsoBenefitDiscountServiceMock
          with ChildRateTypeDataServiceMock
          with VariableTaxServiceImpl {
          override implicit def ec: ExecutionContext = ExecutionContext.global

          override def logHadoopMessage(messageEnvelope: MessageEnvelope): Unit =
            hadoopLogger.logHadoopMessage(messageEnvelope)

          override def fetchPropertyData(propertyIds: Seq[HotelId]): Future[Seq[HotelData]] =
            propertyMetadataService.fetchPropertyData(propertyIds)

          override def fetchSimulatedPropertyData(propertyIds: Seq[HotelId],
                                                  simulationContextId: String): Future[Seq[HotelData]] =
            propertyMetadataService.fetchSimulatedPropertyData(propertyIds, simulationContextId)

          override val externalLoyaltyService: ExternalLoyaltyService = mock[ExternalLoyaltyService]
          override val consumerFintechProductService: ConsumerFintechProductService = mock[ConsumerFintechProductService]
          override val soybeanAndDiscountStep: SoybeanAndDiscountStep = mock[SoybeanAndDiscountStep]
          override val sellDiscountRoomDuplicationService: SellDiscountRoomDuplicationService =
            new SellDiscountRoomDuplicationServiceMock {}
          override val refreshPaymentInfoStep: RefreshPaymentInfoStep =
            new RefreshPaymentInfoStepImpl(mock[PriceStateService])
          override val bookingFormOfferSwapSettingsProducer: BookingFormOfferSwapSettingsProducer =
            mock[BookingFormOfferSwapSettingsProducer]
          override protected val propertyExperimentSettingsProducer: PropertyExperimentSettingsProducer =
            PropertyExperimentSettingsProducer.disabled

          override def aggregateReporter: AggregateReporter = new AggregateReporterMock {}
        }

        val mockHotelId = 42L
        val data: Seq[HotelId] = Seq(mockHotelId)

        val testData: HotelData = HotelData.defaultInstance
          .withHotelId(mockHotelId)
          .withEnabledRooms(
            Map(
              1L -> MasterRoomInfo.defaultInstance,
              2L -> MasterRoomInfo.defaultInstance,
            ))
      }

      "use precached supplier list" in new TestScope {
        when(propertyMetadataService.fetchPropertyData(List(mockHotelId))).thenReturn(Future.successful(List(testData)))
        private val request: BaseRequestBuilder = aValidBaseRequest
        private val ctx: FlowContextImpl = FlowContextBuilder(request)

        private val resultF = flow.fetchHotelMetaCache(flow.Data(data, ctx, null)).map { case (hotels, _) =>
          hotels.map(_.hotelData.hotelId)
        }
        resultF must be_==(Seq(mockHotelId)).await
      }

      "fetch HMC data" in new TestScope {
        private val request =
          BaseRequestBuilder(aValidBaseRequest).withBExperiment(ABTest.ENABLE_HMC_FETCH_MASTER_HOTEL_ONLY).build
        private val ctx = FlowContextBuilder(request)
        private val mockHotelIdSecond: Long = mockHotelId + 1
        private val mockHotelIdThird: Long = mockHotelIdSecond + 1
        private val propertyIds: Seq[HotelId] = Seq(mockHotelId, mockHotelIdSecond, mockHotelIdThird)

        when(propertyMetadataService.fetchPropertyData(propertyIds)).thenReturn(
          Future.successful(
            Seq(
              testData,
              testData.copy(hotelId = mockHotelIdSecond),
              testData.copy(hotelId = mockHotelIdThird),
            )))

        when(flow.hotelMetaCacheSource.getPropertyData(propertyIds)).thenReturn(Future.successful(Map(
          mockHotelId -> MasterHotelContext.defaultInstance.withMasterHotelInfo(
            MasterHotelInfo(masterHotelId = mockHotelId),
          ),
          mockHotelIdSecond -> MasterHotelContext.defaultInstance.withMasterHotelInfo(
            MasterHotelInfo(masterHotelId = mockHotelIdSecond),
          ),
        )))

        private val expected = Seq(
          (mockHotelId, Some(mockHotelId)),
          (mockHotelIdSecond, Some(mockHotelIdSecond)),
          (mockHotelIdThird, None), // expect None since HMC didn't return it
        )

        private val resultF = flow.fetchHotelMetaCache(flow.Data(propertyIds, ctx, null)).map { case (hotels, _) =>
          hotels.map { h =>
            val hotelId = h.hotelData.hotelId
            val associatedMasterHotelId = h.hmcData.flatMap(_.masterHotelInfo.map(_.masterHotelId))

            (hotelId, associatedMasterHotelId)
          }
        }

        resultF must containTheSameElementsAs(expected).await
      }

      "fetch HMC data if HMC child hotels fetch is enabled" in new TestScope {
        private val request = BaseRequestBuilder(aValidBaseRequest)
          .withAExperiment(ABTest.ENABLE_HMC_FETCH_MASTER_HOTEL_ONLY)
          .withBExperiment(ABTest.ENABLE_HMC_FETCH_MASTER_HOTEL_AND_CHILDS)
          .build
        private val ctx = FlowContextBuilder(request)
        private val mockHotelIdSecond: Long = mockHotelId + 1
        private val mockHotelIdThird: Long = mockHotelIdSecond + 1
        private val propertyIds: Seq[HotelId] = Seq(mockHotelId, mockHotelIdSecond, mockHotelIdThird)

        when(propertyMetadataService.fetchPropertyData(propertyIds)).thenReturn(
          Future.successful(
            Seq(
              testData,
              testData.copy(hotelId = mockHotelIdSecond),
              testData.copy(hotelId = mockHotelIdThird),
            )))

        when(flow.hotelMetaCacheSource.getPropertyData(propertyIds)).thenReturn(Future.successful(Map(
          mockHotelId -> MasterHotelContext.defaultInstance.withMasterHotelInfo(
            MasterHotelInfo(masterHotelId = mockHotelId),
          ),
          mockHotelIdSecond -> MasterHotelContext.defaultInstance.withMasterHotelInfo(
            MasterHotelInfo(masterHotelId = mockHotelIdSecond),
          ),
        )))

        private val expected = Seq(
          (mockHotelId, Some(mockHotelId)),
          (mockHotelIdSecond, Some(mockHotelIdSecond)),
          (mockHotelIdThird, None), // expect None since HMC didn't return it
        )

        private val resultF = flow.fetchHotelMetaCache(flow.Data(propertyIds, ctx, null)).map { case (hotels, _) =>
          hotels.map { h =>
            val hotelId = h.hotelData.hotelId
            val associatedMasterHotelId = h.hmcData.flatMap(_.masterHotelInfo.map(_.masterHotelId))

            (hotelId, associatedMasterHotelId)
          }
        }

        resultF must containTheSameElementsAs(expected).await
      }

      "fetch HMC data with wrong mapping" in new TestScope {
        private val request =
          BaseRequestBuilder(aValidBaseRequest).withBExperiment(ABTest.ENABLE_HMC_FETCH_MASTER_HOTEL_ONLY).build
        private val ctx = FlowContextBuilder(request)

        when(propertyMetadataService.fetchPropertyData(List(mockHotelId))).thenReturn(Future.successful(List(testData)))

        when(
          flow.hotelMetaCacheSource.getPropertyData(Seq(mockHotelId)),
        ).thenReturn(
          Future.successful(
            Map(
              (mockHotelId + 1) -> MasterHotelContext.defaultInstance,
            ),
          ))

        private val expected = Seq(
          (mockHotelId, Option.empty[Long]), // should be None due to wrong response from HMC
        )

        private val resultF = flow.fetchHotelMetaCache(flow.Data(data, ctx, null)).map { case (hotels, _) =>
          hotels.map { h =>
            val hotelId = h.hotelData.hotelId
            val associatedMasterHotelId = h.hmcData.flatMap(_.masterHotelInfo.map(_.masterHotelId))

            (hotelId, associatedMasterHotelId)
          }
        }

        resultF must containTheSameElementsAs(expected).await
      }

      "fetch HMC data and pick correct MasterHotelContext" in new TestScope {
        private val request =
          BaseRequestBuilder(aValidBaseRequest).withBExperiment(ABTest.ENABLE_HMC_FETCH_MASTER_HOTEL_ONLY).build
        private val ctx = FlowContextBuilder(request)

        when(propertyMetadataService.fetchPropertyData(List(mockHotelId))).thenReturn(Future.successful(List(testData)))

        when(
          flow.hotelMetaCacheSource.getPropertyData(Seq(mockHotelId)),
        ).thenReturn(Future.successful(Map(
          mockHotelId -> MasterHotelContext.defaultInstance.withMasterHotelInfo(
            MasterHotelInfo(masterHotelId = mockHotelId),
          ),
          mockHotelId + 1 -> MasterHotelContext.defaultInstance,
        )))

        private val expected: Seq[(Long, Option[Long])] = Seq(
          (mockHotelId, Some(mockHotelId)),
        )

        private val resultF = flow.fetchHotelMetaCache(flow.Data(data, ctx, null)).map { case (hotels, _) =>
          hotels.map { h =>
            val hotelId = h.hotelData.hotelId
            val associatedMasterHotelId = h.hmcData.flatMap(_.masterHotelInfo.map(_.masterHotelId))

            (hotelId, associatedMasterHotelId)
          }
        }

        resultF must containTheSameElementsAs(expected).await
      }

      "fetch HMC data when supplyEquitySimulationParameters is present" in new TestScope {
        val simulationContextId = "sim-ctx-123"
        val simulationParams = SupplyEquitySimulationParameters(simulationContextId)
        val request = BaseRequestBuilder(aValidBaseRequest)
          .withSupplyEquitySimulationParameters(Some(simulationParams))
          .withBExperiment(ABTest.ENABLE_HMC_FETCH_MASTER_HOTEL_ONLY)
          .build
        val ctx = FlowContextBuilder(request)
        val propertyIds = Seq(mockHotelId)

        when(propertyMetadataService.fetchSimulatedPropertyData(propertyIds, simulationContextId)).thenReturn(
          Future.successful(
            Seq(
              testData,
            )))

        when(flow.hotelMetaCacheSource.getPropertyData(propertyIds)).thenReturn(
          Future.successful(
            Map(
              mockHotelId -> MasterHotelContext.defaultInstance.withMasterHotelInfo(
                MasterHotelInfo(masterHotelId = mockHotelId),
              ),
            )))

        private val expected = Seq(
          (mockHotelId, Option(mockHotelId)),
        )

        private val resultF = flow.fetchHotelMetaCache(flow.Data(propertyIds, ctx, null)).map { case (hotels, _) =>
          hotels.map { h =>
            val hotelId = h.hotelData.hotelId
            val associatedMasterHotelId = h.hmcData.flatMap(_.masterHotelInfo.map(_.masterHotelId))

            (hotelId, associatedMasterHotelId)
          }
        }

        resultF must containTheSameElementsAs(expected).await
      }

      "fetch HMC data with wrong mapping when supplyEquitySimulationParameters is present" in new TestScope {
        val simulationContextId = "sim-ctx-123"
        val simulationParams = SupplyEquitySimulationParameters(simulationContextId)
        val request = BaseRequestBuilder(aValidBaseRequest)
          .withSupplyEquitySimulationParameters(Some(simulationParams))
          .withBExperiment(ABTest.ENABLE_HMC_FETCH_MASTER_HOTEL_ONLY)
          .build
        val ctx = FlowContextBuilder(request)
        val propertyIds = Seq(mockHotelId)

        when(propertyMetadataService.fetchSimulatedPropertyData(propertyIds, simulationContextId)).thenReturn(
          Future.successful(
            Seq(
              testData,
            )))

        when(
          flow.hotelMetaCacheSource.getPropertyData(Seq(mockHotelId)),
        ).thenReturn(
          Future.successful(
            Map(
              (mockHotelId + 1) -> MasterHotelContext.defaultInstance,
            ),
          ))

        private val expected = Seq(
          (mockHotelId, Option.empty[Long]), // should be None due to wrong response from HMC
        )

        private val resultF = flow.fetchHotelMetaCache(flow.Data(data, ctx, null)).map { case (hotels, _) =>
          hotels.map { h =>
            val hotelId = h.hotelData.hotelId
            val associatedMasterHotelId = h.hmcData.flatMap(_.masterHotelInfo.map(_.masterHotelId))

            (hotelId, associatedMasterHotelId)
          }
        }

        resultF must containTheSameElementsAs(expected).await
      }
    }

    object PriceStreamData {
      val roomTypeKey = "101_true_1"
      val pastBookingInfoSummary: Map[String, PastBookingInfoSummary] = Map(
        roomTypeKey ->
          PastBookingInfoSummary(Map(
            PastBookingInformationType.past30Days -> PastBookingInfo(132,
                                                                     PriceStreamDisplayPrice(5646.53, 7058.16),
                                                                     PriceStreamDisplayPrice(1411.63, 2823.26),
                                                                     PriceStreamDisplayPrice(8469.79, 11293.06),
                                                                     None),
            PastBookingInformationType.past90Days -> PastBookingInfo(100,
                                                                     PriceStreamDisplayPrice(3529.08, 4940.71),
                                                                     PriceStreamDisplayPrice(1411.63, 2117.45),
                                                                     PriceStreamDisplayPrice(7058.16, 8469.79),
                                                                     None),
            PastBookingInformationType.past180Days -> PastBookingInfo(132,
                                                                      PriceStreamDisplayPrice(7058.16, 8469.79),
                                                                      PriceStreamDisplayPrice(1411.63, 2823.26),
                                                                      PriceStreamDisplayPrice(9881.42, 12704.69),
                                                                      None),
          )))

      val expectedRes = Map(
        roomTypeKey -> DFPastBookingInfoSummary(Map(
          "past30Days" -> PastBookingPrices(DisplayPrice(1411.63, 2823.26), DisplayPrice(0d, 0d)),
          "past90Days" -> PastBookingPrices(DisplayPrice(1411.63, 2117.45), DisplayPrice(0d, 0d)),
          "past180Days" -> PastBookingPrices(DisplayPrice(1411.63, 2823.26), DisplayPrice(0d, 0d)),
        )))

      val expectedRes2 = Map(
        roomTypeKey -> DFPastBookingInfoSummary(Map(
          "past30Days" -> PastBookingPrices(DisplayPrice(1000.0, 2000.0), DisplayPrice(0d, 0d)),
          "past90Days" -> PastBookingPrices(DisplayPrice(1000.0, 2000.0), DisplayPrice(0d, 0d)),
          "past180Days" -> PastBookingPrices(DisplayPrice(1000.0, 2000.0), DisplayPrice(0d, 0d)),
        )))

      val pastBookingInfoResponse = PastBookingInfoResponse(Map(1001L -> pastBookingInfoSummary))
    }

    "fetchPastBookingInformation" should {
      import PriceStreamData._

      def getMetaHotel(hotel: Hotel): MetaHotels = {
        val meta = aValidHotelInfo.withHotelId(hotel.id)
        Seq(aValidMetaHotel.withMeta(meta).withHotel(hotel))
      }

      val hotel = aValidHotel.withId(1001L).build
      val meta = getMetaHotel(hotel)

      implicit val br = mock[BaseRequest]

      when(flow.priceStreamServiceMock.getPastBookingInfo(anyObject)(anyObject))
        .thenReturn(Future.successful(pastBookingInfoResponse))

      "Return past booking information correctly" in {
        val ctx =
          FlowContextBuilder(aValidBaseRequest.withFeatureRequest(FeatureRequest(calculateRareRoomBadge = Some(true))))
        val data = flow.Data(meta, ctx, null)
        val resultF = flow.fetchPastBookingInformation(data)
        resultF.map((result: MetaHotels) => result.head.meta.pastBookingInfo) must be_==(expectedRes).await
      }

      "Return past booking information correctly with VEL-1745 = B" in {
        val ctx = FlowContextBuilder(
          aValidBaseRequest
            .withExperiment(DFExperiment(ABTest.PARALLEL_FETCH_PAST_BOOKINGINFO, Variant.B))
            .withFeatureRequest(FeatureRequest(calculateRareRoomBadge = Some(true))))
        ctx.flowDataContext.setPastBookingInformationFuture(Future.successful(Map(1001L -> expectedRes2)))

        val data = flow.Data(meta, ctx, null)
        val resultF = flow.fetchPastBookingInformation(data)
        resultF.map((result: MetaHotels) => result.head.meta.pastBookingInfo) must be_==(expectedRes2).await
      }

      "Not Return past booking information if hotel id not available " in {
        val ctx =
          FlowContextBuilder(aValidBaseRequest.withFeatureRequest(FeatureRequest(calculateRareRoomBadge = Some(true))))
        val hotel = aValidHotel.withId(2002L).build
        val meta = getMetaHotel(hotel)
        val data = flow.Data(meta, ctx, null)
        val resultF = flow.fetchPastBookingInformation(data)
        resultF.map((result: MetaHotels) => result.head.meta.pastBookingInfo) must be_==(Map.empty).await
      }

      "Do not return past booking information when calculateRareRoomBadge is False" in {
        val ctx =
          FlowContextBuilder(aValidBaseRequest.withFeatureRequest(FeatureRequest(calculateRareRoomBadge = Some(false))))
        val data = flow.Data(meta, ctx, null)
        val resultF = flow.fetchPastBookingInformation(data)
        resultF.map((result: MetaHotels) => result.head.meta.pastBookingInfo) must be_==(Map.empty).await
      }

      "Do not return past booking information when calculateRareRoomBadge is False for ctx" in {
        val ctx =
          FlowContextBuilder(aValidBaseRequest.withFeatureRequest(FeatureRequest(calculateRareRoomBadge = Some(false))))
        val resultF = flow.fetchPastBookingInformation(ctx)
        resultF must be_==(Map.empty).await
      }

      "Return past booking information when EnablePriceTrend is available in FF list" in {
        val ctx = FlowContextBuilder(aValidBaseRequest.withFeatureFlags(List(FeatureFlag.EnablePriceTrend)))
        val data = flow.Data(meta, ctx, null)
        val resultF = flow.fetchPastBookingInformation(data)
        resultF.map((result: MetaHotels) => result.head.meta.pastBookingInfo) must be_==(expectedRes).await
      }

      "Do Return past booking information when PriceHistory is available in FF list but PFE-10385-KS is A side" in {
        val ctx = FlowContextBuilder(
          aValidBaseRequest
            .withFeatureFlags(List(FeatureFlag.PriceHistory))
            .withAExperiment(ABTest.ENABLE_PRICE_CHANGE_FEATURE))
        val data = flow.Data(meta, ctx, null)
        val resultF = flow.fetchPastBookingInformation(data)
        resultF.map((result: MetaHotels) => result.head.meta.pastBookingInfo) must be_==(Map.empty).await
      }

      "Return past booking information when PriceHistory is available in FF list and PFE-10385-KS is B side" in {
        val ctx = FlowContextBuilder(
          aValidBaseRequest
            .withFeatureFlags(List(FeatureFlag.PriceHistory))
            .withBExperiment(ABTest.ENABLE_PRICE_CHANGE_FEATURE))
        val data = flow.Data(meta, ctx, null)
        val resultF = flow.fetchPastBookingInformation(data)
        resultF.map((result: MetaHotels) => result.head.meta.pastBookingInfo) must be_==(expectedRes).await
      }
    }

    "setPastBookingInformationFuture" should {

      import PriceStreamData._

      "not set for A variant" in {
        val flow = new TestFlow {}
        val ctx = FlowContextBuilder(aValidBaseRequest)
        val data = flow.Data(Seq(aValidMetaHotel.build), ctx, null)

        flow.setPastBookingInformationFuture(data)
        ctx.flowDataContext.getPastBookingInformationFuture shouldEqual (FlowDataContext.emptyF)
      }

      "should set future in context for B variant" in {
        val flow = new TestFlow {}
        val priceStreamFuture = Future.successful(pastBookingInfoResponse)
        when(flow.priceStreamServiceMock.getPastBookingInfo(anyObject)(anyObject)).thenReturn(priceStreamFuture)
        val ctx = FlowContextBuilder(
          aValidBaseRequest
            .withExperiment(DFExperiment(ABTest.PARALLEL_FETCH_PAST_BOOKINGINFO, Variant.B))
            .withFeatureRequest(FeatureRequest(calculateRareRoomBadge = Some(true))))
        val data = flow.Data(Seq(aValidMetaHotel.build), ctx, null)

        flow.setPastBookingInformationFuture(data)

        ctx.flowDataContext.getPastBookingInformationFuture.map(_.head._2) must be_==(expectedRes).await
      }
    }

    "setAgxCommission" in {
      val priceStreamServiceMock = mock[PriceStreamService]
      val flow = new HotelSearchFlow[FlowContextImpl]
        with LogicImpl[FlowContextImpl]
        with PromotionCalculationMock
        with SupplierAPIDataServiceMock
        with PriceChangeServiceMock
        with BaseHotelSearchFlowMock
        with AllMock
        with DmcMetaDataServiceMock
        with AsoBenefitDiscountServiceMock
        with ChildRateTypeDataServiceMock
        with VariableTaxServiceImpl {
        override implicit def ec: ExecutionContext = ExecutionContext.global

        override val priceStreamService: PriceStreamService = priceStreamServiceMock
        override val degradationSettingProducer: DegradationSettingProducer = DegradationSettingProducerMock.settings
        override val externalLoyaltyService: ExternalLoyaltyService = mock[ExternalLoyaltyService]
        override val consumerFintechProductService: ConsumerFintechProductService = mock[ConsumerFintechProductService]
        override val soybeanAndDiscountStep: SoybeanAndDiscountStep = mock[SoybeanAndDiscountStep]
        override val sellDiscountRoomDuplicationService: SellDiscountRoomDuplicationService =
          new SellDiscountRoomDuplicationServiceMock {}
        override val refreshPaymentInfoStep: RefreshPaymentInfoStep =
          new RefreshPaymentInfoStepImpl(mock[PriceStateService])
        override val bookingFormOfferSwapSettingsProducer: BookingFormOfferSwapSettingsProducer =
          mock[BookingFormOfferSwapSettingsProducer]
        override protected val propertyExperimentSettingsProducer: PropertyExperimentSettingsProducer =
          PropertyExperimentSettingsProducer.disabled

        override def aggregateReporter: AggregateReporter = new AggregateReporterMock {}
      }

      "prepare and read agx commissions with multiple fences" in {
        val baseRequest =
          aValidBaseRequest.withClientInfo(aValidDFClientInfo.withOrigin(Some("TH")).withCid(Some(42)).build).build
        val baseRequestSg =
          aValidBaseRequest.withClientInfo(aValidDFClientInfo.withOrigin(Some("SG")).withCid(Some(42)).build).build
        val ctx = FlowContextBuilder(baseRequest)
        val ctxSg = ctx.updateRequest(baseRequestSg).asInstanceOf[FlowContextImpl]
        val hotelWithMeta = WithMeta(aValidHotel.build, aValidHotelInfo.build)(baseRequest)
        val hotelWithMetaSg = WithMeta(aValidHotel.build, aValidHotelInfo.build)(baseRequestSg)
        val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
        val hotelDataSg = flow.Data(Seq(hotelWithMetaSg), ctxSg, null)
        val agxHotel = AGXHotel(Seq(AGXCommissionAdjustmentDetail(payAsYouGoCommission = Some(12))))
        val agxHotelSg = AGXHotel(Seq(AGXCommissionAdjustmentDetail(payAsYouGoCommission = Some(24))))

        val thFence = YplRateFence("TH", 42, 1)
        val sgFence = YplRateFence("SG", 42, 1)

        ctx.supplyFlowDataContext.setGPCommissions(Map(thFence -> Map(12345L -> agxHotel)))
        ctxSg.supplyFlowDataContext.setGPCommissions(Map(sgFence -> Map(12345L -> agxHotelSg)))

        val result = Await.result(flow.setAgxCommission(hotelData), 10.seconds)
        val resultSg = Await.result(flow.setAgxCommission(hotelDataSg), 10.seconds)

        result.headOption.flatMap(
          _.d.agxHotel.commissionDetails.headOption.flatMap(_.payAsYouGoCommission)) should_== Some(12)
        resultSg.headOption.flatMap(
          _.d.agxHotel.commissionDetails.headOption.flatMap(_.payAsYouGoCommission)) should_== Some(24)
      }

      "skip setAgxCommission step for price state request" in {
        val baseRequest = aValidBaseRequest
          .withClientInfo(aValidDFClientInfo.withOrigin(Some("TH")).withCid(Some(42)).build)
          .withBookingFilter(BookingFilter(isPriceStateRequest = true))
          .build
        val ctx = FlowContextBuilder(baseRequest)
        val agxHotel = AGXHotel(Seq(AGXCommissionAdjustmentDetail(payAsYouGoCommission = Some(12))))
        ctx.supplyFlowDataContext.setGPCommissions(
          Map(RateFence.ofClientInfo(ctx.baseRequest.cInfo) -> Map(12345L -> agxHotel)))

        val hInfo = aValidHotelInfo.build
        val hotel = aValidHotel.build
        val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
        val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)

        val result = Await.result(flow.setAgxCommission(hotelData), 10.seconds)

        result.headOption.flatMap(
          _.d.agxHotel.commissionDetails.headOption.flatMap(_.payAsYouGoCommission)) should_== None
      }

      "prepare and read agx commissions if not price state request" in {

        val baseRequest =
          aValidBaseRequest.withClientInfo(aValidDFClientInfo.withOrigin(Some("TH")).withCid(Some(42)).build).build
        val ctx = FlowContextBuilder(baseRequest)

        val hInfo = aValidHotelInfo.build
        val hotel = aValidHotel.build
        val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
        val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
        val agxHotel = AGXHotel(Seq(AGXCommissionAdjustmentDetail(payAsYouGoCommission = Some(12))))
        ctx.supplyFlowDataContext.setGPCommissions(
          Map(RateFence.ofClientInfo(ctx.baseRequest.cInfo) -> Map(12345L -> agxHotel)))

        val result = Await.result(flow.setAgxCommission(hotelData), 10.seconds)

        result.headOption.flatMap(
          _.d.agxHotel.commissionDetails.headOption.flatMap(_.payAsYouGoCommission)) should_== Some(12.0)
      }
    }
    "enableCheckInFlipExperimentation" in {
      val priceStreamServiceMock = mock[PriceStreamService]
      val flow = new HotelSearchFlow[FlowContextImpl]
        with LogicImpl[FlowContextImpl]
        with PromotionCalculationMock
        with SupplierAPIDataServiceMock
        with BaseHotelSearchFlowMock
        with PriceChangeServiceMock
        with AllMock
        with DmcMetaDataServiceMock
        with AsoBenefitDiscountServiceMock
        with ChildRateTypeDataServiceMock
        with VariableTaxServiceImpl {
        override implicit def ec: ExecutionContext = ExecutionContext.global

        override val priceStreamService: PriceStreamService = priceStreamServiceMock
        override val degradationSettingProducer: DegradationSettingProducer = DegradationSettingProducerMock.settings
        override val externalLoyaltyService: ExternalLoyaltyService = mock[ExternalLoyaltyService]
        override val consumerFintechProductService: ConsumerFintechProductService = mock[ConsumerFintechProductService]
        override val soybeanAndDiscountStep: SoybeanAndDiscountStep = mock[SoybeanAndDiscountStep]
        override val sellDiscountRoomDuplicationService: SellDiscountRoomDuplicationService =
          new SellDiscountRoomDuplicationServiceMock {}
        override val refreshPaymentInfoStep: RefreshPaymentInfoStep =
          new RefreshPaymentInfoStepImpl(mock[PriceStateService])
        override val bookingFormOfferSwapSettingsProducer: BookingFormOfferSwapSettingsProducer =
          mock[BookingFormOfferSwapSettingsProducer]
        override protected val propertyExperimentSettingsProducer: PropertyExperimentSettingsProducer =
          PropertyExperimentSettingsProducer.disabled

        override def aggregateReporter: AggregateReporter = new AggregateReporterMock {}
      }

      "set city id for checkin flip when all properties are in the same city" in {
        val baseRequest = aValidBaseRequest
        val hotel1 = aValidHotelInfo.withHotelId(1L).withCityId(42L).build
        val hotel2 = aValidHotelInfo.withHotelId(2L).withCityId(42L).build
        val ctx = FlowContextBuilder(baseRequest)
        val data = flow.Data(Seq(hotel1, hotel2), ctx, null)
        flow.enableCheckinFlipExperimentation(data)
        ctx.experimentContext.requestContext.cityID should_== Some(42L)
      }

      "not set city id for checkin flip when properties are in different cities" in {
        val baseRequest = aValidBaseRequest
        val hotel1 = aValidHotelInfo.withHotelId(1L).withCityId(42L).build
        val hotel2 = aValidHotelInfo.withHotelId(2L).withCityId(43L).build
        val ctx = FlowContextBuilder(baseRequest)
        val data = flow.Data(Seq(hotel1, hotel2), ctx, null)
        flow.enableCheckinFlipExperimentation(data)
        ctx.experimentContext.requestContext.cityID should_== None
      }
    }

    "Property Experimentation A/A Test" should {
      val priceStreamServiceMock = mock[PriceStreamService]
      trait ExpFixture
        extends HotelSearchFlow[FlowContextImpl]
          with LogicImpl[FlowContextImpl]
          with PromotionCalculationMock
          with SupplierAPIDataServiceMock
          with BaseHotelSearchFlowMock
          with PriceChangeServiceMock
          with AllMock
          with DmcMetaDataServiceMock
          with AsoBenefitDiscountServiceMock
          with ChildRateTypeDataServiceMock
          with VariableTaxServiceImpl {
        override implicit def ec: ExecutionContext = ExecutionContext.global

        override val priceStreamService: PriceStreamService = priceStreamServiceMock
        override val degradationSettingProducer: DegradationSettingProducer = DegradationSettingProducerMock.settings
        override val externalLoyaltyService: ExternalLoyaltyService = mock[ExternalLoyaltyService]
        override val consumerFintechProductService: ConsumerFintechProductService = mock[ConsumerFintechProductService]
        override val soybeanAndDiscountStep: SoybeanAndDiscountStep = mock[SoybeanAndDiscountStep]
        override val sellDiscountRoomDuplicationService: SellDiscountRoomDuplicationService =
          new SellDiscountRoomDuplicationServiceMock {}
        override val refreshPaymentInfoStep: RefreshPaymentInfoStep =
          new RefreshPaymentInfoStepImpl(mock[PriceStateService])
        override val bookingFormOfferSwapSettingsProducer: BookingFormOfferSwapSettingsProducer =
          mock[BookingFormOfferSwapSettingsProducer]

        override def aggregateReporter: AggregateReporter = new AggregateReporterMock {}
      }

      def trackingAllocations() = new ExperimentDataHolder(aValidBaseRequest, mockExpAppState) {
        var allocations = List.empty[(Long, String)]

        override def determineVariant(experimentName: String): Char = 'A'

        override def determineVariant(property: PropertyContext, experimentName: String): Char = {
          allocations = (property.id, experimentName) :: allocations
          'A'
        }
      }

      "allocate when enabled" in {
        val flow = new ExpFixture {
          override protected val propertyExperimentSettingsProducer: PropertyExperimentSettingsProducer =
            new PropertyExperimentSettingsProducer(State.empty(),
                                                   ConfigFactory.parseString("property-experiments.enabled = true"))
        }
        val exp = trackingAllocations()
        val ctx = FlowContextBuilder(aValidBaseRequest, Some(exp))
        val _ = flow.toHotelInfo(
          flow.Data(Seq(HotelDataWithHMCData(aValidHotelDataBuilder.withHotelId(42).build, None)), ctx, null))
        exp.allocations should containTheSameElementsAs(
          (ABTest.EXP_CITYIDLOSFLIP_DFAPI_HOTELSEARCH_AA ++ ABTest.EXP_HOTELIDWEEKLYFLIP_DFAPI_HOTELSEARCH_AA :+ ABTest.CHECKIN_FLIP_AA_TEST)
            .map((42, _)))
      }

      "allocate when disabled" in {
        val flow = new ExpFixture {
          override protected val propertyExperimentSettingsProducer: PropertyExperimentSettingsProducer =
            new PropertyExperimentSettingsProducer(State.empty(),
                                                   ConfigFactory.parseString("property-experiments.enabled = false"))
        }
        val exp = trackingAllocations()
        val ctx = FlowContextBuilder(aValidBaseRequest, Some(exp))
        val _ = flow.toHotelInfo(
          flow.Data(Seq(HotelDataWithHMCData(aValidHotelDataBuilder.withHotelId(42).build, None)), ctx, null))
        exp.allocations should_== Nil
      }
    }

    "buildDFHotels" should {
      val los = 1
      val hotelId = 82095
      val suppliersContext =
        Map[SupplierId, HotelContext](DMC.YCS -> HotelContext.defaultInstance, DMC.Bcom -> HotelContext.defaultInstance)
      val tmplHotelInfo: HotelInfo = aValidHotelInfo
        .withHotelId(hotelId)
        .withMasterHotelContext(Option(MasterHotelContext.defaultInstance))
        .withSuppliersContext(suppliersContext)

      def verifyHotelExchange(hotel: Hotel, currency: String): Boolean =
        hotel.exchangeRate.isDefined && hotel.rooms.nonEmpty && hotel.rooms.forall(r =>
          r.exchange.isDefined && r.exchange.get.request == currency)

      "populate exchange rate after YPL flow for synxis" in {
        implicit val baseRequest: BaseRequest = aValidBaseRequest
        val yplHotel = aValidYplHotel
          .withSupplierId(DMC.SynxisECRS)
          .withRoom(aValidYplRoom.withRateCategory(aValidYplRateCategory))
          .build
        val ctx = FlowContextBuilder(baseRequest.withCurrency("THB"))
        val metaYplHotels = Seq(
          WithMeta(
            yplHotel,
            tmplHotelInfo.withSuppliers(DMC.SynxisECRS).build,
          ))
        val dfHotels = Await.result(flow.buildDFHotels(flow.Data(metaYplHotels, ctx, null)), 5.seconds)._1
        dfHotels must haveSize(1)
        dfHotels.forall(meta => verifyHotelExchange(meta.d, "THB")) must beTrue
      }

      "populate exchange rate for currency unsupported payment enable exp AFF-3021 B" in {
        implicit val baseRequest: BaseRequest = aValidBaseRequest
        val yplHotel = aValidYplHotel
          .withSupplierId(DMC.YCS)
          .withRoom(aValidYplRoom.withRateCategory(aValidYplRateCategory).withCurrency("ISK"))
          .build
        val metaYplHotels = Seq(
          WithMeta(
            yplHotel,
            tmplHotelInfo.withSuppliers(DMC.YCS).build,
          ))
        val ctx = FlowContextBuilder(
          baseRequest
            .withCurrency("")
            .withExperiment(DFExperiment("AFF-3021", 'B'))
            .withFlagInfo(FlagInfo(isUsingHotelCurrency = true)))
        val dfHotels = Await.result(flow.buildDFHotels(flow.Data(metaYplHotels, ctx, null)), 5.seconds)._1
        dfHotels must haveSize(1)
        dfHotels.forall(meta => meta.d.rooms.forall(_.currency == "ISK")) must beTrue
        dfHotels.forall(meta => verifyHotelExchange(meta.d, "USD")) must beTrue
      }

      "no populate exchange rate for currency unsupported payment enable exp AFF-3021 B and request currency JPY" in {
        implicit val baseRequest: BaseRequest = aValidBaseRequest
        val yplHotel = aValidYplHotel
          .withSupplierId(DMC.YCS)
          .withRoom(aValidYplRoom.withRateCategory(aValidYplRateCategory).withCurrency("ISK"))
          .build
        val metaYplHotels = Seq(
          WithMeta(
            yplHotel,
            tmplHotelInfo.withSuppliers(DMC.YCS).build,
          ))
        val ctx = FlowContextBuilder(
          baseRequest
            .withCurrency("JPY")
            .withExperiment(DFExperiment("AFF-3021", 'B'))
            .withFlagInfo(FlagInfo(isUsingHotelCurrency = true)))
        val dfHotels = Await.result(flow.buildDFHotels(flow.Data(metaYplHotels, ctx, null)), 5.seconds)._1
        dfHotels must haveSize(1)
        dfHotels.forall(meta => meta.d.rooms.forall(_.currency == "ISK")) must beTrue
        dfHotels.forall(meta => verifyHotelExchange(meta.d, "JPY")) must beTrue
      }

      "no populate exchange rate for currency unsupported payment enable exp AFF-3021 B and request using hotel currency false" in {
        implicit val baseRequest: BaseRequest = aValidBaseRequest
        val yplHotel = aValidYplHotel
          .withSupplierId(DMC.YCS)
          .withRoom(aValidYplRoom.withRateCategory(aValidYplRateCategory).withCurrency("ISK"))
          .build
        val metaYplHotels = Seq(
          WithMeta(
            yplHotel,
            tmplHotelInfo.withSuppliers(DMC.YCS).build,
          ))
        val ctx = FlowContextBuilder(
          baseRequest
            .withCurrency("")
            .withExperiment(DFExperiment("AFF-3021", 'B'))
            .withFlagInfo(FlagInfo(isUsingHotelCurrency = false)))
        val dfHotels = Await.result(flow.buildDFHotels(flow.Data(metaYplHotels, ctx, null)), 5.seconds)._1
        dfHotels must haveSize(1)
        dfHotels.forall(meta => meta.d.rooms.forall(_.currency == "ISK")) must beTrue
        dfHotels.forall(meta => verifyHotelExchange(meta.d, "ISK")) must beTrue
      }

      "populate exchange rate for currency unsupported payment disable exp AFF-3021" in {
        implicit val baseRequest: BaseRequest = aValidBaseRequest
        val yplHotel = aValidYplHotel
          .withSupplierId(DMC.YCS)
          .withRoom(aValidYplRoom.withRateCategory(aValidYplRateCategory).withCurrency("ISK"))
          .build
        val metaYplHotels = Seq(
          WithMeta(
            yplHotel,
            tmplHotelInfo.withSuppliers(DMC.YCS).build,
          ))
        val ctx = FlowContextBuilder(baseRequest.withCurrency("").withFlagInfo(FlagInfo(isUsingHotelCurrency = true)))
        val dfHotels = Await.result(flow.buildDFHotels(flow.Data(metaYplHotels, ctx, null)), 5.seconds)._1
        dfHotels must haveSize(1)
        dfHotels.forall(meta => meta.d.rooms.forall(_.currency == "ISK")) must beTrue
        dfHotels.forall(meta => verifyHotelExchange(meta.d, "ISK")) must beTrue
      }
    }

    "aggregateFencedHotels" should {
      "return fenced hotels correctly" in {
        import com.agoda.papi.pricing.supply.SupplyModuleImplicits._

        val fenceKR = YplRateFence("KR", 2, 1)
        val fenceKeyKR = FencedRateKey(Some("KR"), Some(2))
        val fenceRatePairKR = FencedRatePair(fenceKeyKR, FencedOriginObject(Set(1, 2)))
        val dispatchInfoKR = DispatchInfo(12345, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = fenceRatePairKR)

        val fenceTH = YplRateFence("TH", -1, 1)
        val fenceKeyTH = FencedRateKey(Some(fenceTH.origin), Some(fenceTH.cid))
        val fenceRatePairTH = FencedRatePair(fenceKeyTH, FencedOriginObject(Set(1)))
        val dispatchInfoTH = DispatchInfo(12345, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = fenceRatePairTH)

        val request = aValidBaseRequest
          .withFencedRatePairs(Some(List(fenceRatePairKR)))
          .withCID(Some(1))
          .withBExperiment(ABTest.PRICE_PUSH_FENCING)
          .build
        val contextMock = FlowContextBuilder(request)

        val roomWithFences = aValidRoom.withRateFence(Set(fenceKR, fenceTH)).build
        val hotelDispatchInfo = HotelDispatchInfo(hotelId = 12345, dispatchInfos = Seq(dispatchInfoKR, dispatchInfoTH))

        val metaHotels = Seq(
          aValidMetaHotel
            .withHotel(aValidHotel.withRooms(List(roomWithFences, aValidRoom.build)))
            .withHotelDispatchInfo(Some(hotelDispatchInfo))
            .build,
        )
        val hotelData = flow.Data(metaHotels, contextMock, null)
        val result = Await.result(flow.aggregateFencedHotels(hotelData), 3 seconds)
        result.head.d.rooms.size should_== 1
        result.head.d.rooms.head.fencedRateKey.get should_== fenceKeyKR
        result.head.d.rooms.head.fences should_== Set(fenceKR)
      }

      "return fenced hotels correctly when no fence rate pairs" in {
        val request = aValidBaseRequest.withCID(Some(1)).withBExperiment(ABTest.PRICE_PUSH_FENCING).build
        val contextMock = FlowContextBuilder(request)

        val metaHotels = Seq(
          aValidMetaHotel.withHotel(aValidHotel.withRooms(List(aValidRoom.build, aValidRoom.build))).build,
        )
        val hotelData = flow.Data(metaHotels, contextMock, null)
        val result = Await.result(flow.aggregateFencedHotels(hotelData), 3 seconds)
        result.head.d.rooms.size should_== 2
      }

      "return fenced hotels with language correctly" in {
        val fenceKR = YplRateFence("KR", 2, 9)
        val fenceKeyKR = FencedRateKey(Some("KR"), Some(2), Some(9))
        val fenceRatePairKR = FencedRatePair(fenceKeyKR, FencedOriginObject(Set(1, 2)))
        import SupplyModuleImplicits._
        val dispatchInfoKR = DispatchInfo(12345, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = fenceRatePairKR)

        val fenceTH = YplRateFence("TH", -1, 22)
        val fenceKeyTH = FencedRateKey(Some(fenceTH.origin), Some(fenceTH.cid), Some(fenceTH.language))
        val fenceRatePairTH = FencedRatePair(fenceKeyTH, FencedOriginObject(Set(1)))
        val dispatchInfoTH = DispatchInfo(12345, DMC.YCS, DFMasterChannel.APS, 0, Nil, fencedRatePair = fenceRatePairTH)

        val request = aValidBaseRequest
          .withFencedRatePairs(Some(List(fenceRatePairKR)))
          .withCID(Some(1))
          .withBExperiment(ABTest.PRICE_PUSH_FENCING)
          .build
        val contextMock = FlowContextBuilder(request)

        val roomWithFences = aValidRoom.withRateFence(Set(fenceKR, fenceTH)).build
        val hotelDispatchInfo = HotelDispatchInfo(hotelId = 12345, dispatchInfos = Seq(dispatchInfoKR, dispatchInfoTH))

        val metaHotels = Seq(
          aValidMetaHotel
            .withHotel(aValidHotel.withRooms(List(roomWithFences, aValidRoom.build)))
            .withHotelDispatchInfo(Some(hotelDispatchInfo))
            .build,
        )
        val hotelData = flow.Data(metaHotels, contextMock, null)
        val result = Await.result(flow.aggregateFencedHotels(hotelData), 3 seconds)
        result.head.d.rooms.size should_== 1
        result.head.d.rooms.head.fencedRateKey.get should_== fenceKeyKR
        result.head.d.rooms.head.fences should_== Set(fenceKR)
      }
    }

    "getFencedHotels" should {
      "return room with fencing" in {
        val fenceTH = YplRateFence("TH", -1, 22)
        val fenceKR = YplRateFence("KR", 2, 9)
        val fenceKeyKR = FencedRateKey(Some("KR"), Some(2), Some(9))
        val fenceRatePairKR = FencedRatePair(fenceKeyKR, FencedOriginObject(Set(1, 2)))

        val roomWithFences = aValidRoom.withRateFence(Set(fenceKR, fenceTH)).build

        val metaHotels = Seq(
          aValidMetaHotel.withHotel(aValidHotel.withRooms(List(roomWithFences))).build,
        )

        val result = flow.getFencedHotels(metaHotels, fenceRatePairKR)(aValidFlowContext)
        val hotel = result.head.d
        hotel.rooms.head.fencedRateKey.contains(fenceKeyKR) should_== true
        hotel.rooms.head.origin should_== fenceKeyKR.origin
      }
    }

    "appliedVariableTax" should {
      "tax applied in hotel room" in {
        val request = aValidBaseRequest
          .withCID(Some(1))
          .withFeatureFlags(List(FeatureFlag.VariableTax))
          .withBExperiments(List(
            ABTest.ENABLE_MALAYSIA_TOURISM_TAX_FOR_CITIUS,
          ))
          .withBookingFilter(BookingFilter(isPriceStateRequest = true))
          .build
        val contextMock = FlowContextBuilder(request)

        val taxBreakDown = aValidTaxBreakdown withIsFee (false) withChargeOptions (ChargeOption.VariableTax)
        val mockPrices = List(aValidPrice.withTaxBreakdown(taxBreakDown).build)
        val metaHotels = Seq(
          aValidMetaHotel
            .withHotel(
              aValidHotel.withRooms(
                List(
                  aValidRoom.withLocalDFFinance(DFFinance.build(mockPrices, None)).build,
                  aValidRoom.withLocalDFFinance(DFFinance.build(aValidRoom.prices, None)).build,
                )))
            .build,
        )

        val hotelData = flow.Data(metaHotels, contextMock, null)
        val result = Await.result(flow.appliedVariableTax(hotelData), 3 seconds)
        result.head.d.rooms.flatMap(_.prices.flatMap(_.variableTax)).length should_== 2
        result.head.d.rooms.head.prices.head.tax should_== 60
      }

      "tax applied in hotel room without child rate" in {
        val request = aValidBaseRequest
          .withCID(Some(1))
          .withFeatureFlags(List(FeatureFlag.VariableTax))
          .withBExperiments(List(
            ABTest.ENABLE_MALAYSIA_TOURISM_TAX_FOR_CITIUS,
          ))
          .withBookingFilter(BookingFilter(isPriceStateRequest = true))
          .build
        val contextMock = FlowContextBuilder(request)

        val taxBreakDown = aValidTaxBreakdown withIsFee (false) withChargeOptions (ChargeOption.VariableTax)
        val mockPrices = List(
          aValidPrice.withTaxBreakdown(taxBreakDown).build,
          aValidPrice.withSubChargeType(SubChargeType.Child).withTaxBreakdown(taxBreakDown).build,
        )
        val metaHotels = Seq(
          aValidMetaHotel
            .withHotel(aValidHotel.withRooms(List(aValidRoom.withLocalDFFinance(DFFinance.build(mockPrices, None)).build)))
            .build,
        )

        val hotelData = flow.Data(metaHotels, contextMock, null)
        val result = Await.result(flow.appliedVariableTax(hotelData), 3 seconds)
        result.head.d.rooms
          .flatMap(_.prices.filter(_.subChargeType == SubChargeType.None).flatMap(_.variableTax))
          .length should_== 1
        result.head.d.rooms
          .flatMap(_.prices.filter(_.subChargeType == SubChargeType.Child).flatMap(_.variableTax))
          .length should_== 0
      }

      "tax applied in hotel room with priceStateRequest is false" in {
        val request = aValidBaseRequest
          .withCID(Some(1))
          .withFeatureFlags(List(FeatureFlag.VariableTax))
          .withBExperiments(List(
            ABTest.ENABLE_MALAYSIA_TOURISM_TAX_FOR_CITIUS,
          ))
          .withBookingFilter(BookingFilter(isPriceStateRequest = false))
          .build
        val contextMock = FlowContextBuilder(request)

        val taxBreakDown = aValidTaxBreakdown withIsFee (false) withChargeOptions (ChargeOption.VariableTax)
        val mockPrices = List(aValidPrice.withTaxBreakdown(taxBreakDown).withVariableTax(Some(20)).build)

        val metaHotels = Seq(
          aValidMetaHotel
            .withHotel(aValidHotel.withRooms(List(
              aValidRoom
                .withLocalDFFinance(DFFinance.build(mockPrices, None))
                .withPrices(List(aValidPrice.withTaxBreakdown(taxBreakDown).withVariableTax(Some(20)).build))
                .build,
              aValidRoom.build,
            )))
            .build,
        )

        val hotelData = flow.Data(metaHotels, contextMock, null)
        val result = Await.result(flow.appliedVariableTax(hotelData), 3 seconds)
        result.head.d.rooms.flatMap(_.prices.flatMap(_.variableTax)).length should_== 2
        result.head.d.rooms.head.prices.head.tax should_== 60d
      }

      "tax applied in hotel room with no variable tax feature flag and isPriceStateRequest is false" in {
        val request = aValidBaseRequest
          .withCID(Some(1))
          .withBExperiments(
            List(
              ABTest.ENABLE_MALAYSIA_TOURISM_TAX_FOR_CITIUS,
            ))
          .withBookingFilter(BookingFilter(isPriceStateRequest = true))
          .build
        val contextMock = FlowContextBuilder(request)

        val taxBreakDown = aValidTaxBreakdown withIsFee (false) withChargeOptions (ChargeOption.VariableTax)
        val mockPrices = List(aValidPrice.withTaxBreakdown(taxBreakDown).withVariableTax(Some(20)).build)
        val metaHotels = Seq(
          aValidMetaHotel
            .withHotel(aValidHotel.withRooms(
              List(aValidRoom.withLocalDFFinance(DFFinance.build(mockPrices, None)).build, aValidRoom.build)))
            .build,
        )

        val hotelData = flow.Data(metaHotels, contextMock, null)
        val result = Await.result(flow.appliedVariableTax(hotelData), 3 seconds)
        result.head.d.rooms.flatMap(_.prices.flatMap(_.variableTax)).length should_== 0
        result.head.d.rooms.head.prices.head.tax should_== 30
      }

      "tax applied in hotel room with no variable tax feature flag" in {
        val request = aValidBaseRequest
          .withCID(Some(1))
          .withBExperiments(
            List(
              ABTest.ENABLE_MALAYSIA_TOURISM_TAX_FOR_CITIUS,
            ))
          .build
        val contextMock = FlowContextBuilder(request)

        val taxBreakDown = aValidTaxBreakdown withIsFee (false) withChargeOptions (ChargeOption.VariableTax)
        val metaHotels = Seq(
          aValidMetaHotel
            .withHotel(aValidHotel.withRooms(
              List(aValidRoom.withPrices(List(aValidPrice.withTaxBreakdown(taxBreakDown).build)).build, aValidRoom.build)))
            .build,
        )

        val hotelData = flow.Data(metaHotels, contextMock, null)
        val result = Await.result(flow.appliedVariableTax(hotelData), 3 seconds)
        result.head.d.rooms.flatMap(_.prices.flatMap(_.variableTax)).length should_== 0
        result.head.d.rooms.head.prices.head.tax should_== 50
      }

      "tax applied in hotel room [WLBF-1857=A]" in {

        val supplierConfigForAgoda = SupplierConfiguration(CommissionAndMarginOverride(true, true, true, true))
        val request = aValidBaseRequest
          .withCID(Some(1))
          .withSearchType(SearchTypes.HotelForBooking)
          .withSelectedHourlySlot(Some(SelectedHourlySlot("22:00", 4)))
          .withFeatureFlags(List(FeatureFlag.VariableTax))
          .withBExperiments(List(ABTest.ENABLE_MALAYSIA_TOURISM_TAX_FOR_CITIUS))
          .withBookingFilter(BookingFilter(isPriceStateRequest = true))
          .withWhitelabelSetting(WhitelabelSetting(
            51,
            Map(29014 -> false),
            Set.empty,
            supplierConfigForAgoda,
            Some(LoyaltyProgram(None, None, None, None)),
            Coupon(None),
            ExchangeRateConfiguration(false),
            Nil,
            Nil,
            false,
            true,
            Seq(
              ExternalVipDisplayConfigs(tierId = Some(2),
                                        tierNameCms = Some(3),
                                        tierDescriptionCms = Some(1),
                                        benefitIds = Some(List(95, 26, 201, 241, 12, 261)))),
            true,
            filterOutAgencyPaymentModel = true,
          ))
          .build
        val contextMock = FlowContextBuilder(request)

        val taxBreakDown = aValidTaxBreakdown withIsFee (false) withChargeOptions (ChargeOption.VariableTax)
        val mockPrices = List(aValidPrice.withTaxBreakdown(taxBreakDown).build)
        val metaHotels = Seq(
          aValidMetaHotel
            .withHotel(
              aValidHotel.withRooms(
                List(
                  aValidRoom.withLocalDFFinance(DFFinance.build(mockPrices, None)).build,
                  aValidRoom.withLocalDFFinance(DFFinance.build(aValidRoom.prices, None)).build,
                )))
            .build,
        )

        val hotelData = flow.Data(metaHotels, contextMock, null)
        flow.appliedVariableTax(hotelData)
        val result = Await.result(flow.appliedVariableTax(hotelData), 3 seconds)
        result.head.d.rooms.flatMap(_.prices.flatMap(_.variableTax)).length should_== 2
        result.head.d.rooms.head.prices.head.tax should_== 60
      }

      "tax applied in hotel room for SSR without variable tax feature flag when isRegulationDisplayVariableTax is true" in {
        val request = aValidBaseRequest
          .withCID(Some(1))
          .withSearchType(SearchTypes.HotelSearch)
          .withRegulationFeatureEnabledSetting(RegulationFeatureEnabledSetting(isDisplayVariableTax = true))
          .withBookingFilter(BookingFilter(isPriceStateRequest = true))
          .build
        val contextMock = FlowContextBuilder(request)

        val taxBreakDown = aValidTaxBreakdown withIsFee (false) withChargeOptions (ChargeOption.VariableTax)
        val mockPrices = List(aValidPrice.withTaxBreakdown(taxBreakDown).build)
        val metaHotels = Seq(
          aValidMetaHotel
            .withHotel(
              aValidHotel.withRooms(
                List(
                  aValidRoom.withLocalDFFinance(DFFinance.build(mockPrices, None)).build,
                  aValidRoom.withLocalDFFinance(DFFinance.build(aValidRoom.prices, None)).build,
                )))
            .build,
        )

        val hotelData = flow.Data(metaHotels, contextMock, null)
        val result = Await.result(flow.appliedVariableTax(hotelData), 3 seconds)
        result.head.d.rooms.flatMap(_.prices.flatMap(_.variableTax)).length should_== 2
        result.head.d.rooms.head.prices.head.tax should_== 60
      }

      "tax not applied in hotel room for SSR without variable tax feature flag when isRegulationDisplayVariableTax is undefined" in {
        val request = aValidBaseRequest
          .withCID(Some(1))
          .withSearchType(SearchTypes.HotelSearch)
          .withBookingFilter(BookingFilter(isPriceStateRequest = true))
          .build
        val contextMock = FlowContextBuilder(request)

        val taxBreakDown = aValidTaxBreakdown withIsFee (false) withChargeOptions (ChargeOption.VariableTax)
        val mockPrices = List(aValidPrice.withTaxBreakdown(taxBreakDown).build)
        val metaHotels = Seq(
          aValidMetaHotel
            .withHotel(
              aValidHotel.withRooms(
                List(
                  aValidRoom.withLocalDFFinance(DFFinance.build(mockPrices, None)).build,
                  aValidRoom.withLocalDFFinance(DFFinance.build(aValidRoom.prices, None)).build,
                )))
            .build,
        )

        val hotelData = flow.Data(metaHotels, contextMock, null)
        val result = Await.result(flow.appliedVariableTax(hotelData), 3 seconds)
        result.head.d.rooms.flatMap(_.prices.flatMap(_.variableTax)).length should_== 0
        result.head.d.rooms.head.prices.head.tax should_== 50
      }
    }

    "Set HotelStats" should {
      val calculationStatsLogger =
        new CalculationStatsLoggerImpl with NoOpReportingService with WithAggregateReporterMock {}
      val flow = new TestFlow {
        override def getRoomsStatsAndReport(
          hotel: Hotel,
          measurement: String,
          extraTags: Map[String, String] = Map.empty): Map[SupplierId, SupplierStats] =
          calculationStatsLogger.getRoomsStatsAndReport(hotel, measurement, extraTags)
      }

      val request = aValidBaseRequest withSearchId "searchId"
      val hInfo: HotelInfo = aValidHotelInfo.withSuppliers(DMC.YCS).withBnplEnableMap(Set(DMC.YCS))
      val roomTmpl = aValidRoom.build
      val hotel: Hotel = aValidHotel.withRooms(
        List(
          roomTmpl,
          roomTmpl.copy(cxlCode = "AAA"),
        ))
      val ctx = FlowContextBuilder(request)
      val data = flow.Data(Seq(WithMeta(hotel, hInfo)(request)), ctx, null)
      val result = flow.setHotelStats(data).head.d

      result.stats.roomsQtyBeforePricing should_== 2
    }
  }

  val exchangeRateTHBToJPY = ExchangeRate(local = "THB",
                                          request = "JPY",
                                          toUsd = 0.02811998234,
                                          toRequest = 119.8179,
                                          numLocalDecimal = 2,
                                          numReqDecimal = 0,
                                          numUsdDecimal = 2)

  val exchangeRateTHBToTHB = ExchangeRate(local = "THB",
                                          request = "THB",
                                          toUsd = 0.02811998234,
                                          toRequest = 35.3619,
                                          numLocalDecimal = 2,
                                          numReqDecimal = 0,
                                          numUsdDecimal = 2)

  val exchangeRateTHBToUSD = ExchangeRate(local = "THB",
                                          request = "USD",
                                          toUsd = 0.02811998234,
                                          toRequest = 1,
                                          numLocalDecimal = 2,
                                          numReqDecimal = 2,
                                          numUsdDecimal = 2)
  val exchangeRateTHBToVND = ExchangeRate(local = "THB",
                                          request = "VND",
                                          toUsd = 0.02811998234,
                                          toRequest = 25405.1,
                                          numLocalDecimal = 0,
                                          numReqDecimal = 0,
                                          numUsdDecimal = 0)

  val flow = new HotelSearchFlow[FlowContextImpl]
    with LogicImpl[FlowContextImpl]
    with PromotionCalculationMock
    with BaseHotelSearchFlowMock
    with SupplierAPIDataServiceMock
    with PriceChangeServiceMock
    with AllMock
    with DmcMetaDataServiceMock
    with AsoBenefitDiscountServiceMock
    with ChildRateTypeDataServiceMock
    with VariableTaxServiceImpl {
    override implicit def ec: ExecutionContext = ExecutionContext.global

    override val priceStreamService: PriceStreamService = mock[PriceStreamService]
    override val externalLoyaltyService: ExternalLoyaltyService = mock[ExternalLoyaltyService]
    override val consumerFintechProductService: ConsumerFintechProductService = mock[ConsumerFintechProductService]
    override val soybeanAndDiscountStep: SoybeanAndDiscountStep = mock[SoybeanAndDiscountStep]
    override val sellDiscountRoomDuplicationService: SellDiscountRoomDuplicationService =
      new SellDiscountRoomDuplicationServiceMock {}
    override protected val requestedCurrencyFilter: RequestedCurrencyFilter = BypassedRequestedCurrencyFilter
    override val refreshPaymentInfoStep: RefreshPaymentInfoStep = new RefreshPaymentInfoStepImpl(mock[PriceStateService])
    override val bookingFormOfferSwapSettingsProducer: BookingFormOfferSwapSettingsProducer =
      mock[BookingFormOfferSwapSettingsProducer]
    override protected val propertyExperimentSettingsProducer: PropertyExperimentSettingsProducer =
      PropertyExperimentSettingsProducer.disabled

    override def aggregateReporter: AggregateReporter = new AggregateReporterMock {}
  }

  def getPromotionDiscounts(baseRequest: BaseRequest,
                            numberOfRooms: Int,
                            discountAmount: Double,
                            percent: Double): Some[DiscountingPromotionDiscounts] = Some(
    DiscountingPromotionDiscounts(
      percent = percent,
      perBook = discountAmount,
      perRoom = Math.round(discountAmount / numberOfRooms),
      perNight = Math.round(discountAmount / baseRequest.lengthOfStay),
      perRoomPerNight = Math.round(discountAmount / baseRequest.lengthOfStay / numberOfRooms),
    ))

  "round DFFinance in local currency and convert to request currency JPY and USD correctly" in {
    val baseRequest = aValidBaseRequest.withCurrency("JPY").build
    val ctx = FlowContextBuilder(baseRequest)
    val rebateSet = DiscountingRebateSet(None, cashbackRebate = Some(DiscountingRebate(10.0, 0.0)))
    val discounts = getPromotionDiscounts(baseRequest, baseRequest.occ.rooms, 10, 10)
    val promo = aValidDiscountingPromotionCampaign
      .withSelectedPromotion(
        Some(
          aValidDiscountPromotion
            .withDiscountType(CampaignDiscountTypes.Percentage)
            .withPromotionDiscount(discounts)
            .withRawPromotionDiscountInDiscountCurrency(discounts),
        ))
      .build
    val discountsMock = aValidDiscounts.withRebateSet(rebateSet).withPromotionCampaign(Some(promo)).build
    val dfFinanceMock = new DFFinance(
      customerMarketing = new CustomerMarketing(discounts = Some(discountsMock)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )
    val roomWithDFFinance = aValidRoom
      .withCurrency("THB")
      .withExchange(exchangeRateTHBToJPY)
      .withExchangeRateList(List(exchangeRateTHBToJPY, exchangeRateTHBToUSD))
      .withLocalDFFinance(dfFinanceMock)
      .build

    val hInfo = aValidHotelInfo.build
    val hotel = aValidHotel.withRoom(roomWithDFFinance).build
    val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
    val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
    val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

    val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
    val promotionDiscounts = dfFinanceByCurrency.request
      .flatMap(_.discounts)
      .flatMap(_.promotionCampaign)
      .flatMap(_.selectedPromotion)
      .flatMap(_.promotionDiscount)

    dfFinanceByCurrency.request.isDefined should_== true
    dfFinanceByCurrency.request.flatMap(_.discounts).flatMap(_.rebateSet.cashbackTotalAmount) should_== Some(34.0)
    promotionDiscounts.map(_.perRoom) should_== Some(34.0)
    promotionDiscounts.map(_.perRoomPerNight) should_== Some(10.0)
    dfFinanceByCurrency.usd.isDefined should_== true
    dfFinanceByCurrency.usd.flatMap(_.discounts).flatMap(_.rebateSet.cashbackTotalAmount) should_== Some(0.28)
  }

  "round DFFinance in local currency and convert to request currency VND and USD correctly" in {
    val baseRequest = aValidBaseRequest.withCurrency("VND").build
    val ctx = FlowContextBuilder(baseRequest)
    val rebateSet = DiscountingRebateSet(None, cashbackRebate = Some(DiscountingRebate(10.0, 0.0)))
    val discounts = getPromotionDiscounts(baseRequest, baseRequest.occ.rooms, 756.12, 8)
    val promo = aValidDiscountingPromotionCampaign
      .withSelectedPromotion(
        Some(
          aValidDiscountPromotion
            .withDiscountType(CampaignDiscountTypes.Percentage)
            .withPromotionDiscount(discounts)
            .withRawPromotionDiscountInDiscountCurrency(discounts)))
      .build
    val discountsMock = aValidDiscounts.withRebateSet(rebateSet).withPromotionCampaign(Some(promo)).build
    val dfFinanceMock = new DFFinance(
      customerMarketing = new CustomerMarketing(discounts = Some(discountsMock)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )
    val roomWithDFFinance = aValidRoom
      .withCurrency("THB")
      .withExchange(exchangeRateTHBToVND)
      .withExchangeRateList(List(exchangeRateTHBToVND, exchangeRateTHBToUSD))
      .withLocalDFFinance(dfFinanceMock)
      .build

    val hInfo = aValidHotelInfo.build
    val hotel = aValidHotel.withRoom(roomWithDFFinance).build
    val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
    val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
    val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

    val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
    val promotionDiscounts = dfFinanceByCurrency.request
      .flatMap(_.discounts)
      .flatMap(_.promotionCampaign)
      .flatMap(_.selectedPromotion)
      .flatMap(_.promotionDiscount)

    dfFinanceByCurrency.request.isDefined should_== true
    dfFinanceByCurrency.request.flatMap(_.discounts).flatMap(_.rebateSet.cashbackTotalAmount) should_== Some(7144.0)
    promotionDiscounts.map(_.perRoom) should_== Some(540080.0)
    promotionDiscounts.map(_.perRoomPerNight) should_== Some(180027.0)
    dfFinanceByCurrency.usd.isDefined should_== true
    dfFinanceByCurrency.usd.flatMap(_.discounts).flatMap(_.rebateSet.cashbackTotalAmount) should_== Some(0.28)
  }

  "convert currency DFFinance correctly for when there is room allocation infos" in {
    val baseRequest = aValidBaseRequest.withCurrency("JPY").build
    val ctx = FlowContextBuilder(baseRequest)
    val roomAllocationInfo = Map(
      1 -> aValidRoomAllocationInfoAdultsChildren.build,
      2 -> aValidRoomAllocationInfoAdultsChildren.build,
    )
    val discounts = getPromotionDiscounts(baseRequest, roomAllocationInfo.keys.size, 10, 10)
    val promo = aValidDiscountingPromotionCampaign
      .withSelectedPromotion(
        Some(
          aValidDiscountPromotion
            .withDiscountType(CampaignDiscountTypes.Percentage)
            .withPromotionDiscount(discounts)
            .withRawPromotionDiscountInDiscountCurrency(discounts)))
      .build
    val discountsMock = aValidDiscounts.withPromotionCampaign(Some(promo)).build
    val dfFinanceMock = new DFFinance(
      customerMarketing = new CustomerMarketing(discounts = Some(discountsMock)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )
    val roomWithDFFinance = aValidRoom
      .withCurrency("THB")
      .withExchange(exchangeRateTHBToJPY)
      .withExchangeRateList(List(exchangeRateTHBToJPY, exchangeRateTHBToUSD))
      .withLocalDFFinance(dfFinanceMock)
      .withRoomAllocationInfo(roomAllocationInfo)
      .build

    val hInfo = aValidHotelInfo.build
    val hotel = aValidHotel.withRoom(roomWithDFFinance).build
    val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
    val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
    val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

    val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
    val promotionDiscounts = dfFinanceByCurrency.request
      .flatMap(_.discounts)
      .flatMap(_.promotionCampaign)
      .flatMap(_.selectedPromotion)
      .flatMap(_.promotionDiscount)

    promotionDiscounts.map(_.perRoom) should_== Some(17.0)
    promotionDiscounts.map(_.perRoomPerNight) should_== Some(7.0)
  }

  "fetch USD exchange rate when there is no USD currency in room" in {
    val baseRequest = aValidBaseRequest.withCurrency("JPY").build
    val ctx = FlowContextBuilder(baseRequest)
    val rebateSet = DiscountingRebateSet(None, cashbackRebate = Some(DiscountingRebate(10.0, 0.0)))
    val discounts = getPromotionDiscounts(baseRequest, baseRequest.occ.rooms, 10, 10)
    val promo = aValidDiscountingPromotionCampaign
      .withSelectedPromotion(
        Some(
          aValidDiscountPromotion
            .withDiscountType(CampaignDiscountTypes.Percentage)
            .withPromotionDiscount(discounts)
            .withRawPromotionDiscountInDiscountCurrency(discounts)))
      .build
    val discountsMock = aValidDiscounts.withRebateSet(rebateSet).withPromotionCampaign(Some(promo)).build
    val dfFinanceMock = new DFFinance(
      customerMarketing = new CustomerMarketing(discounts = Some(discountsMock)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )
    val roomWithDFFinance: Room = aValidRoom
      .withCurrency("THB")
      .withExchange(exchangeRateTHBToJPY)
      .withExchangeRateList(List(exchangeRateTHBToJPY))
      .withLocalDFFinance(dfFinanceMock)
      .build

    val hInfo = aValidHotelInfo.build
    val hotel = aValidHotel.withRoom(roomWithDFFinance).build
    val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
    val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
    val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

    val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
    dfFinanceByCurrency.usd.isDefined should_== true
    dfFinanceByCurrency.usd.flatMap(_.discounts).flatMap(_.rebateSet.cashbackTotalAmount) should_== Some(0.28)

    val promotionDiscounts = dfFinanceByCurrency.usd
      .flatMap(_.discounts)
      .flatMap(_.promotionCampaign)
      .flatMap(_.selectedPromotion)
      .flatMap(_.promotionDiscount)

    promotionDiscounts.map(_.perRoom) should_== Some(0.28)
    promotionDiscounts.map(_.perRoomPerNight) should_== Some(0.08)
  }

  "convert correctly  local currency, USD and hotel currency when exp PFE-12481 is B and isEnableHotelLocalCurrency is true" in {
    val baseRequest = aValidBaseRequest
      .withRegulationFeatureEnabledSetting(RegulationFeatureEnabledSetting(isEnableHotelLocalCurrency = true))
      .withBExperiment(ABTest.SURCHARGES_IN_HOTEL_CURRENCY)
      .withSearchType(SearchTypes.HotelForBooking)
      .withCurrency("JPY")
      .build
    val supplierHotelInfo = aValidHotelInfo.supplyHotelInfo.withYCSHotelCurrency(Some("VND")).build
    val hInfo = aValidHotelInfo.withSupplyHotelInfo(supplierHotelInfo).build
    val ctx = FlowContextBuilder(baseRequest)
    val dfFinanceMock = new DFFinance(
      customerMarketing = new CustomerMarketing(discounts = Some(aValidDiscounts.build)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )
    val roomWithDFFinance: Room = aValidRoom
      .withCurrency("THB")
      .withHotelCurrencyCode(hInfo.ycsHotelCurrency)
      .withExchange(exchangeRateTHBToJPY)
      .withExchangeRateList(List(exchangeRateTHBToJPY, exchangeRateTHBToTHB, exchangeRateTHBToUSD, exchangeRateTHBToVND))
      .withLocalDFFinance(dfFinanceMock)
      .build

    val hotel = aValidHotel.withRoom(roomWithDFFinance).build
    val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
    val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
    val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

    val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
    dfFinanceByCurrency.hotelCurrency.head.excludedSurcharge
    dfFinanceByCurrency.hotelCurrency.isDefined should_== true
    dfFinanceByCurrency.usd.isDefined should_== true
    dfFinanceByCurrency.request.isDefined should_== true

    dfFinanceByCurrency.hotelCurrency.head.sellIn() should_== 892989.0
    dfFinanceByCurrency.request.head.sellIn() should_== 4212.0
    dfFinanceByCurrency.usd.head.sellIn() should_== 35.15
    dfFinanceByCurrency.local.sellIn() should_== 1243.0
  }

  "hotel currency should be undefined, when exp PFE-12481 is A and isEnableHotelLocalCurrency is false" in {
    val baseRequest = aValidBaseRequest
      .withRegulationFeatureEnabledSetting(RegulationFeatureEnabledSetting(isEnableHotelLocalCurrency = false))
      .withAExperiment(ABTest.SURCHARGES_IN_HOTEL_CURRENCY)
      .withSearchType(SearchTypes.HotelForBooking)
      .withCurrency("JPY")
      .build
    val supplierHotelInfo = aValidHotelInfo.supplyHotelInfo.withYCSHotelCurrency(Some("VND")).build
    val hInfo = aValidHotelInfo.withSupplyHotelInfo(supplierHotelInfo).build

    val ctx = FlowContextBuilder(baseRequest)
    val dfFinanceMock = new DFFinance(
      customerMarketing = new CustomerMarketing(discounts = Some(aValidDiscounts.build)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )
    val roomWithDFFinance: Room = aValidRoom
      .withCurrency("THB")
      .withHotelCurrencyCode(hInfo.ycsHotelCurrency)
      .withExchange(exchangeRateTHBToJPY)
      .withLocalDFFinance(dfFinanceMock)
      .build

    val hotel = aValidHotel.withRoom(roomWithDFFinance).build
    val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
    val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
    val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

    val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
    dfFinanceByCurrency.hotelCurrency.isDefined should_== false
    dfFinanceByCurrency.usd.isDefined should_== true
    dfFinanceByCurrency.request.isDefined should_== true
  }

  "skip currency conversion for cancelReBookOriginalSellIn when convert to USD exchange rate, CFF-1118=B" should {
    val rebooking =
      aValidReBookingRequestV3.withOriginalSellIn(Some(99.99d)).withOriginalUsdToRequestExchangeRate(Some(35.44d))

    val baseRequest = {
      val builder = aValidBaseRequest.withReBookingRequest(Some(rebooking)).withCurrency("JPY")
      val experiments = Seq(DFExperiment(ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3, Variant.B))
      builder.withExperiment(experiments: _*).build

    }

    val ctx = FlowContextBuilder(baseRequest)
    val discountsMock = aValidDiscounts.build
    val dfFinanceMock = new DFFinance(
      customerMarketing =
        new CustomerMarketing(discounts = Some(discountsMock), cancelReBookOriginalSellIn = Some(100d)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )
    val roomWithDFFinance: Room = aValidRoom
      .withCurrency("THB")
      .withExchange(exchangeRateTHBToJPY)
      .withExchangeRateList(List(exchangeRateTHBToJPY))
      .withLocalDFFinance(dfFinanceMock)
      .build

    val hInfo = aValidHotelInfo.build
    val hotel = aValidHotel.withRoom(roomWithDFFinance).build
    val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
    val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
    val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

    val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
    val usdSellIn = dfFinanceByCurrency.usd.head.sellIn()
    val reqSellIn = dfFinanceByCurrency.request.head.sellIn()
    val localSellIn = dfFinanceByCurrency.local.sellIn()

    val usdCancelReBookOriginalSellIn = dfFinanceByCurrency.usd.head.cancelReBookOriginalSellIn
    val requestCancelReBookOriginalSellIn = dfFinanceByCurrency.request.head.cancelReBookOriginalSellIn
    val localCancelReBookOriginalSellIn = dfFinanceByCurrency.local.cancelReBookOriginalSellIn

    usdCancelReBookOriginalSellIn should_== Some(99.99)
    requestCancelReBookOriginalSellIn should_== Some(3543.6455999999994d)
    localCancelReBookOriginalSellIn should_== Some(100d)
    usdSellIn should_== 35.15
    reqSellIn should_== 4212d
    localSellIn should_== 1250d
  }

  "hotel currency when cancel and rebook" in {
    val mockReBookRequest = aValidReBookingRequestV3
      .withRoomTypeId(1234L)
      .withCustomerPaidPrice(1500.0)
      .withOriginalSellIn(Some(99.99))
      .withOriginalUsdToRequestExchangeRate(Some(32.0))

    val mBaseRequest = aValidBaseRequest
      .withReBookingRequest(Some(mockReBookRequest))
      .withRegulationFeatureEnabledSetting(RegulationFeatureEnabledSetting(isEnableHotelLocalCurrency = true))
      .withSearchType(SearchTypes.HotelForBooking)
      .withCurrency("JPY")

    val dfFinanceMock = new DFFinance(
      customerMarketing = new CustomerMarketing(discounts = Some(aValidDiscounts.build)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )
    val mSupplierHotelInfo = aValidHotelInfo.supplyHotelInfo
    val mRoomWithDFFinance: Room = aValidRoom
      .withCurrency("THB")
      .withExchange(exchangeRateTHBToJPY)
      .withExchangeRateList(List(exchangeRateTHBToJPY, exchangeRateTHBToTHB, exchangeRateTHBToUSD))
      .withLocalDFFinance(dfFinanceMock)

    "same hotel and local currency, different request when experiment PFE-12481 = B, origin = JP for allowed WL, localCancelReBookOriginalSellIn is None" in {
      val baseRequest = mBaseRequest.withBExperiments(
        List(ABTest.SURCHARGES_IN_HOTEL_CURRENCY, ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3))
      val supplierHotelInfo = mSupplierHotelInfo.withYCSHotelCurrency(Some("THB")).build
      val hInfo = aValidHotelInfo.withSupplyHotelInfo(supplierHotelInfo).build
      val roomWithDFFinance = mRoomWithDFFinance
        .withExchangeRateList(List(exchangeRateTHBToJPY, exchangeRateTHBToTHB, exchangeRateTHBToUSD))
        .withHotelCurrencyCode(hInfo.ycsHotelCurrency)
      val ctx = FlowContextBuilder(baseRequest)
      val hotel = aValidHotel.withRoom(roomWithDFFinance).build
      val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
      val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)

      val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

      val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
      val usdSellIn = dfFinanceByCurrency.usd.head.sellIn()
      val reqSellIn = dfFinanceByCurrency.request.head.sellIn()
      val localSellIn = dfFinanceByCurrency.local.sellIn()
      val hotelFinanceByCurrency = dfFinanceByCurrency.hotelCurrency

      hotelFinanceByCurrency.isDefined should_== false
      dfFinanceByCurrency.usd.isDefined should_== true
      dfFinanceByCurrency.request.isDefined should_== true

      val usdCancelReBookOriginalSellIn = dfFinanceByCurrency.usd.head.cancelReBookOriginalSellIn
      val requestCancelReBookOriginalSellIn = dfFinanceByCurrency.request.head.cancelReBookOriginalSellIn
      val localCancelReBookOriginalSellIn = dfFinanceByCurrency.local.cancelReBookOriginalSellIn
      usdCancelReBookOriginalSellIn should_== Some(99.99)
      requestCancelReBookOriginalSellIn should_== Some(3199.68)
      localCancelReBookOriginalSellIn.isEmpty should_== true
      usdSellIn should_== 35.15
      reqSellIn should_== 4212d
      localSellIn should_== 1243.0
    }
    "different request, hotel and supplier currency when experiment PFE-12481 = B and origin = JP for allowed WL and CFF-1118 = B, localCancelReBookOriginalSellIn is None " in {
      val baseRequest = mBaseRequest
        .withBExperiments(List(ABTest.SURCHARGES_IN_HOTEL_CURRENCY, ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3))
        .build
      val supplierHotelInfo = mSupplierHotelInfo.withYCSHotelCurrency(Some("VND")).build
      val hInfo = aValidHotelInfo.withSupplyHotelInfo(supplierHotelInfo).build
      val ctx = FlowContextBuilder(baseRequest)
      val roomWithDFFinance = mRoomWithDFFinance
        .withExchangeRateList(
          List(exchangeRateTHBToJPY, exchangeRateTHBToTHB, exchangeRateTHBToUSD, exchangeRateTHBToVND))
        .withHotelCurrencyCode(hInfo.ycsHotelCurrency)
        .build
      val hotel = aValidHotel.withRoom(roomWithDFFinance).build
      val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
      val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
      val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

      val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
      val usdSellIn = dfFinanceByCurrency.usd.head.sellIn()
      val reqSellIn = dfFinanceByCurrency.request.head.sellIn()
      val localSellIn = dfFinanceByCurrency.local.sellIn()
      val hotelSellIn = dfFinanceByCurrency.hotelCurrency.head.sellIn()
      val hotelFinanceByCurrency = dfFinanceByCurrency.hotelCurrency

      hotelFinanceByCurrency.isDefined should_== true
      dfFinanceByCurrency.usd.isDefined should_== true
      dfFinanceByCurrency.request.isDefined should_== true

      val usdCancelReBookOriginalSellIn = dfFinanceByCurrency.usd.head.cancelReBookOriginalSellIn
      val requestCancelReBookOriginalSellIn = dfFinanceByCurrency.request.head.cancelReBookOriginalSellIn
      val localCancelReBookOriginalSellIn = dfFinanceByCurrency.local.cancelReBookOriginalSellIn
      val hotelCurrencyCancelReBookOriginalSellIn = dfFinanceByCurrency.hotelCurrency.head.cancelReBookOriginalSellIn
      usdCancelReBookOriginalSellIn should_== Some(99.99)
      requestCancelReBookOriginalSellIn should_== Some(3199.68)
      localCancelReBookOriginalSellIn.isEmpty should_== true
      hotelCurrencyCancelReBookOriginalSellIn.isEmpty should_== true
      usdSellIn should_== 35.15
      reqSellIn should_== 4212d
      localSellIn should_== 1243.0
      hotelSellIn should_== 892989.0
    }

    "different request, hotel and supplier currency when experiment PFE-12481 is B and origin JP for allowed WL and CFF-1118 = A, localCancelReBookOriginalSellIn= None" in {
      val baseRequest = mBaseRequest.withBExperiment(ABTest.SURCHARGES_IN_HOTEL_CURRENCY).build
      val supplierHotelInfo = mSupplierHotelInfo.withYCSHotelCurrency(Some("VND")).build
      val hInfo = aValidHotelInfo.withSupplyHotelInfo(supplierHotelInfo).build
      val ctx = FlowContextBuilder(baseRequest)
      val roomWithDFFinance = mRoomWithDFFinance
        .withExchangeRateList(
          List(exchangeRateTHBToJPY, exchangeRateTHBToTHB, exchangeRateTHBToUSD, exchangeRateTHBToVND))
        .withHotelCurrencyCode(hInfo.ycsHotelCurrency)
        .build
      val hotel = aValidHotel.withRoom(roomWithDFFinance).build
      val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
      val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
      val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

      val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
      val usdSellIn = dfFinanceByCurrency.usd.head.sellIn()
      val reqSellIn = dfFinanceByCurrency.request.head.sellIn()
      val localSellIn = dfFinanceByCurrency.local.sellIn()
      val hotelSellIn = dfFinanceByCurrency.hotelCurrency.head.sellIn()
      val hotelFinanceByCurrency = dfFinanceByCurrency.hotelCurrency

      hotelFinanceByCurrency.isDefined should_== true
      dfFinanceByCurrency.usd.isDefined should_== true
      dfFinanceByCurrency.request.isDefined should_== true

      val usdCancelReBookOriginalSellIn = dfFinanceByCurrency.usd.head.cancelReBookOriginalSellIn
      val requestCancelReBookOriginalSellIn = dfFinanceByCurrency.request.head.cancelReBookOriginalSellIn
      val localCancelReBookOriginalSellIn = dfFinanceByCurrency.local.cancelReBookOriginalSellIn
      val hotelCurrencyCancelReBookOriginalSellIn = dfFinanceByCurrency.hotelCurrency.head.cancelReBookOriginalSellIn
      usdCancelReBookOriginalSellIn should_== Some(99.99)
      requestCancelReBookOriginalSellIn.isEmpty should_== true
      localCancelReBookOriginalSellIn.isEmpty should_== true
      hotelCurrencyCancelReBookOriginalSellIn.isEmpty should_== true
      usdSellIn should_== 35.15
      reqSellIn should_== 4212d
      localSellIn should_== 1243.0
      hotelSellIn should_== 892989.0
    }

    "same request, hotel and supplier currency when experiment PFE-12481 is B and origin JP for allowed WL and CFF-1118 = B, localCancelReBookOriginalSellIn not None" in {
      val baseRequest = mBaseRequest
        .withCurrency("THB")
        .withBExperiments(List(ABTest.SURCHARGES_IN_HOTEL_CURRENCY, ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3))
        .build
      val supplierHotelInfo = mSupplierHotelInfo.withYCSHotelCurrency(Some("THB")).build
      val hInfo = aValidHotelInfo.withSupplyHotelInfo(supplierHotelInfo).build
      val ctx = FlowContextBuilder(baseRequest)
      val roomWithDFFinance = mRoomWithDFFinance
        .withExchangeRate(exchangeRateTHBToTHB)
        .withExchangeRateList(List(exchangeRateTHBToTHB, exchangeRateTHBToUSD))
        .withHotelCurrencyCode(hInfo.ycsHotelCurrency)
        .build
      val hotel = aValidHotel.withRoom(roomWithDFFinance).build
      val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
      val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
      val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

      val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
      val usdSellIn = dfFinanceByCurrency.usd.head.sellIn()
      val reqSellIn = dfFinanceByCurrency.request.head.sellIn()
      val localSellIn = dfFinanceByCurrency.local.sellIn()

      val hotelFinanceByCurrency = dfFinanceByCurrency.hotelCurrency
      hotelFinanceByCurrency.isDefined should_== false
      dfFinanceByCurrency.usd.isDefined should_== true
      dfFinanceByCurrency.request.isDefined should_== true

      val usdCancelReBookOriginalSellIn = dfFinanceByCurrency.usd.head.cancelReBookOriginalSellIn
      val requestCancelReBookOriginalSellIn = dfFinanceByCurrency.request.head.cancelReBookOriginalSellIn
      val localCancelReBookOriginalSellIn = dfFinanceByCurrency.local.cancelReBookOriginalSellIn
      usdCancelReBookOriginalSellIn should_== Some(99.99)
      requestCancelReBookOriginalSellIn should_== Some(3199.68)
      localCancelReBookOriginalSellIn should_== Some(3199.68)
      usdSellIn should_== 35.15
      reqSellIn should_== 1243.0
      localSellIn should_== 1243.0
    }
    "same request, hotel and supplier currency when experiment PFE-12481 is B and origin JP for allowed WL and CFF-1118 = A, localCancelReBookOriginalSellIn not None" in {
      val baseRequest =
        mBaseRequest.withCurrency("THB").withBExperiments(List(ABTest.SURCHARGES_IN_HOTEL_CURRENCY)).build
      val supplierHotelInfo = mSupplierHotelInfo.withYCSHotelCurrency(Some("THB")).build
      val hInfo = aValidHotelInfo.withSupplyHotelInfo(supplierHotelInfo).build
      val ctx = FlowContextBuilder(baseRequest)
      val roomWithDFFinance = mRoomWithDFFinance
        .withExchangeRate(exchangeRateTHBToTHB)
        .withExchangeRateList(List(exchangeRateTHBToTHB, exchangeRateTHBToUSD))
        .withHotelCurrencyCode(hInfo.ycsHotelCurrency)
        .build
      val hotel = aValidHotel.withRoom(roomWithDFFinance).build
      val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
      val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
      val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

      val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
      val usdSellIn = dfFinanceByCurrency.usd.head.sellIn()
      val reqSellIn = dfFinanceByCurrency.request.head.sellIn()
      val localSellIn = dfFinanceByCurrency.local.sellIn()

      val hotelFinanceByCurrency = dfFinanceByCurrency.hotelCurrency
      hotelFinanceByCurrency.isDefined should_== false
      dfFinanceByCurrency.usd.isDefined should_== true
      dfFinanceByCurrency.request.isDefined should_== true

      val usdCancelReBookOriginalSellIn = dfFinanceByCurrency.usd.head.cancelReBookOriginalSellIn
      val requestCancelReBookOriginalSellIn = dfFinanceByCurrency.request.head.cancelReBookOriginalSellIn
      val localCancelReBookOriginalSellIn = dfFinanceByCurrency.local.cancelReBookOriginalSellIn
      usdCancelReBookOriginalSellIn should_== Some(99.99)
      requestCancelReBookOriginalSellIn.isEmpty should_== true
      localCancelReBookOriginalSellIn.isEmpty should_== true
      usdSellIn should_== 35.15
      reqSellIn should_== 1243.0
      localSellIn should_== 1243.0
    }
  }

  "skip currency conversion for cancelReBookOriginalSellIn when convert to USD exchange rate" should {
    case class TestCase(actionType: ReBookingActionType)
    val testCases = Seq(
      TestCase(actionType = ReBookingActionType.MatchUSD),
      TestCase(actionType = ReBookingActionType.MatchLocal),
    )
    Fragments.foreach(testCases) { testCase =>
      s"actionType:${testCase.actionType}" in {
        val rebooking = aValidReBookingRequestV3.withOriginalSellIn(Some(99.99d)).withActionType(testCase.actionType)
        val baseRequest = aValidBaseRequest.withReBookingRequest(Some(rebooking)).withCurrency("JPY").build

        val ctx = FlowContextBuilder(baseRequest)
        val discountsMock = aValidDiscounts.build
        val dfFinanceMock = new DFFinance(
          customerMarketing =
            new CustomerMarketing(discounts = Some(discountsMock), cancelReBookOriginalSellIn = Some(100d)),
          supplyPrice = new SupplyPrice(List(aValidPrice.build)),
        )
        val roomWithDFFinance: Room = aValidRoom
          .withCurrency("THB")
          .withExchange(exchangeRateTHBToJPY)
          .withExchangeRateList(List(exchangeRateTHBToJPY))
          .withLocalDFFinance(dfFinanceMock)
          .build

        val hInfo = aValidHotelInfo.build
        val hotel = aValidHotel.withRoom(roomWithDFFinance).build
        val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
        val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
        val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

        val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
        val usdSellIn = dfFinanceByCurrency.usd.head.sellIn()
        val reqSellIn = dfFinanceByCurrency.request.head.sellIn()
        val localSellIn = dfFinanceByCurrency.local.sellIn()

        val usdCancelReBookOriginalSellIn = dfFinanceByCurrency.usd.head.cancelReBookOriginalSellIn
        val requestCancelReBookOriginalSellIn = dfFinanceByCurrency.request.head.cancelReBookOriginalSellIn
        val localCancelReBookOriginalSellIn = dfFinanceByCurrency.local.cancelReBookOriginalSellIn

        val expectedUsdCancelReBookOriginalSellIn =
          if (testCase.actionType == MatchLocal) {
            val n = dfFinanceMock.cancelReBookOriginalSellIn.map(_ * exchangeRateTHBToJPY.toUsd).getOrElse(BigDecimal(0))
            n.setScale(2, BigDecimal.RoundingMode.HALF_UP).toDouble
          } else 99.99
        usdCancelReBookOriginalSellIn should_== Some(expectedUsdCancelReBookOriginalSellIn)

        requestCancelReBookOriginalSellIn should_== Some(337d)
        localCancelReBookOriginalSellIn should_== Some(100d)
        usdSellIn should_== 35.15
        reqSellIn should_== 4212d
        localSellIn should_== 1250d
      }
    }
  }

  "skip local currency conversion when Local & Request are the same" in {
    val baseRequest = aValidBaseRequest
      .withReBookingRequest(Some(aValidReBookingRequestV3.withOriginalSellIn(Some(99.99d))))
      .withCurrency("THB")
      .build
    val ctx = FlowContextBuilder(baseRequest)
    val discountsMock = aValidDiscounts.build
    val dfFinanceMock = new DFFinance(
      customerMarketing =
        new CustomerMarketing(discounts = Some(discountsMock), cancelReBookOriginalSellIn = Some(100d)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )
    val roomWithDFFinance: Room = aValidRoom
      .withCurrency("THB")
      .withExchange(exchangeRateTHBToTHB)
      .withExchangeRateList(List(exchangeRateTHBToTHB))
      .withLocalDFFinance(dfFinanceMock)
      .build

    val hInfo = aValidHotelInfo.build
    val hotel = aValidHotel.withRoom(roomWithDFFinance).build
    val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
    val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)
    val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

    val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
    val usdSellIn = dfFinanceByCurrency.usd.head.sellIn()
    val reqSellIn = dfFinanceByCurrency.request.head.sellIn()
    val localSellIn = dfFinanceByCurrency.local.sellIn()

    val usdCancelReBookOriginalSellIn = dfFinanceByCurrency.usd.head.cancelReBookOriginalSellIn
    val requestCancelReBookOriginalSellIn = dfFinanceByCurrency.request.head.cancelReBookOriginalSellIn
    val localCancelReBookOriginalSellIn = dfFinanceByCurrency.local.cancelReBookOriginalSellIn
    usdCancelReBookOriginalSellIn should_== Some(99.99)
    requestCancelReBookOriginalSellIn should_== Some(99d)
    localCancelReBookOriginalSellIn should_== Some(99d)
    usdSellIn should_== 35.15
    reqSellIn should_== 1243d
    localSellIn should_== 1243d
  }

  "getLocalRate" should {
    val baseRequest = aValidBaseRequest.withCurrency("THB").build
    val discounts = getPromotionDiscounts(baseRequest, baseRequest.occ.rooms, 10, 10)
    val promo = aValidDiscountingPromotionCampaign
      .withSelectedPromotion(
        Some(
          aValidDiscountPromotion
            .withDiscountType(CampaignDiscountTypes.Percentage)
            .withPromotionDiscount(discounts)
            .withRawPromotionDiscountInDiscountCurrency(discounts),
        ))
      .build
    val discountsMock = aValidDiscounts.withPromotionCampaign(Some(promo)).build
    val dfFinanceMock = new DFFinance(
      customerMarketing = new CustomerMarketing(discounts = Some(discountsMock)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )

    val roomWithDFFinance: Room = aValidRoom
      .withCurrency("THB")
      .withExchange(exchangeRateTHBToTHB)
      .withExchangeRateList(List(exchangeRateTHBToTHB))
      .withLocalDFFinance(dfFinanceMock)
      .build
    val convertedReqPrice = ConvertedCurrencyPrice(
      roomWithDFFinance.exchange,
      dfFinance = dfFinanceMock,
      cxlRebookOriginalSellIn = Some(1250d),
    )(roomWithDFFinance.dmcData)
    "same currencies with CFF-1117=B" in {
      val baseRequest =
        aValidBaseRequest.withBExperiment(ABTest.SKIP_LOCAL_CURRENCY_CONVERSION).withCurrency("THB").build
      val ctxWithBVariant = FlowContextBuilder(baseRequest)
      val result = flow.getLocalRate(roomWithDFFinance,
                                     Some(exchangeRateTHBToVND),
                                     dfFinanceMock,
                                     convertedReqPrice,
                                     ctxWithBVariant)
      result.reqCurrencyDFFinance().sellIn() should_== 1243d
      result.reqCurrencyDFFinance().promotionDiscountPerBook should_== 10d
    }

    "different currencies" in {
      val baseRequest =
        aValidBaseRequest.withBExperiment(ABTest.SKIP_LOCAL_CURRENCY_CONVERSION).withCurrency("VND").build
      val ctxWithBVariant = FlowContextBuilder(baseRequest)

      val result = flow.getLocalRate(roomWithDFFinance,
                                     Some(exchangeRateTHBToVND),
                                     dfFinanceMock,
                                     convertedReqPrice,
                                     ctxWithBVariant)
      result.reqCurrencyDFFinance().sellIn() should_== 892989d
      result.reqCurrencyDFFinance().promotionDiscountPerBook should_== 7144d
    }

    "same currencies with CFF-1117=A" in {
      val baseRequest =
        aValidBaseRequest.withAExperiment(ABTest.SKIP_LOCAL_CURRENCY_CONVERSION).withCurrency("THB").build
      val ctxWithBVariant = FlowContextBuilder(baseRequest)
      val result = flow.getLocalRate(roomWithDFFinance,
                                     Some(exchangeRateTHBToVND),
                                     dfFinanceMock,
                                     convertedReqPrice,
                                     ctxWithBVariant)
      result.reqCurrencyDFFinance().sellIn() should_== 892989d
      result.reqCurrencyDFFinance().promotionDiscountPerBook should_== 7144d
    }

    "same currencies with CFF-1117=A & CFF-1118=B" in {
      val baseRequest =
        aValidBaseRequest.withBExperiments(List(ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3)).withCurrency("THB").build
      val ctxWithBVariant = FlowContextBuilder(baseRequest)
      val result = flow.getLocalRate(
        roomWithDFFinance,
        Some(exchangeRateTHBToVND),
        dfFinanceMock,
        convertedReqPrice,
        ctxWithBVariant,
        Some(exchangeRateTHBToVND.copy(toRequest = 25000d)),
        Some(37d),
      )

      result.reqCurrencyDFFinance().sellIn() should_== 892989d
      result.reqCurrencyDFFinance().promotionDiscountPerBook should_== 7030d
      result.reqCurrencyDFFinance().cancelReBookOriginalSellIn.get should_== 37d
    }

    "diff currencies with CFF-1117=A & CFF-1118=B" in {
      val baseRequest =
        aValidBaseRequest.withBExperiments(List(ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3)).withCurrency("THB").build
      val ctxWithBVariant = FlowContextBuilder(baseRequest)
      val result = flow.getLocalRate(
        roomWithDFFinance.withCurrency("VND"),
        Some(exchangeRateTHBToVND),
        dfFinanceMock,
        convertedReqPrice,
        ctxWithBVariant,
        Some(exchangeRateTHBToVND.copy(toRequest = 25000d)),
        Some(37d),
      )

      result.reqCurrencyDFFinance().sellIn() should_== 892989d
      result.reqCurrencyDFFinance().promotionDiscountPerBook should_== 7144d
      result.reqCurrencyDFFinance().cancelReBookOriginalSellIn.isEmpty should_== true
    }

    "same currencies with CFF-1117=B & CFF-1118=B" in {
      val baseRequest = aValidBaseRequest
        .withBExperiments(List(ABTest.SKIP_LOCAL_CURRENCY_CONVERSION, ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3))
        .withCurrency("THB")
        .build
      val ctxWithBVariant = FlowContextBuilder(baseRequest)
      val result = flow.getLocalRate(
        roomWithDFFinance,
        Some(exchangeRateTHBToVND),
        dfFinanceMock,
        convertedReqPrice,
        ctxWithBVariant,
        Some(exchangeRateTHBToVND.copy(toRequest = 25000d)),
        Some(37d),
      )

      result.reqCurrencyDFFinance().sellIn() should_== 1243
      result.reqCurrencyDFFinance().promotionDiscountPerBook should_== 10d
      result.reqCurrencyDFFinance().cancelReBookOriginalSellIn.get should_== 1250d

    }
  }
  "getFxFreezingForCxlReBook" should {
    "CFF-1118=A: return empty Fx correctly" in {
      val baseRequest = aValidBaseRequest
        .withReBookingRequest(Some(aValidReBookingRequestV3.withOriginalSellIn(Some(99.99d))))
        .withAExperiments(List(ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3))
        .withCurrency("THB")
        .build
      val ctx = FlowContextBuilder(baseRequest)
      flow.getFxFreezingForCxlReBook(ctx, "THB", Some(35.44)) should_== None
    }

    "CFF-1118=B: return Freezing Fx correctly" in {
      val baseRequest = aValidBaseRequest
        .withReBookingRequest(Some(aValidReBookingRequestV3.withOriginalSellIn(Some(99.99d))))
        .withBExperiments(List(ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3))
        .withCurrency("THB")
        .build
      val ctx = FlowContextBuilder(baseRequest)
      flow.getFxFreezingForCxlReBook(ctx, "THB", Some(35.44)) should_== Some(
        ExchangeRate("THB", "USD", 0.02811998234065109, 35.44, 2, 2, 2))
    }

    "CFF-1118=B: return empty when original exchange rate is not set correctly" in {
      val baseRequest = aValidBaseRequest
        .withReBookingRequest(Some(aValidReBookingRequestV3.withOriginalSellIn(Some(99.99d))))
        .withBExperiments(List(ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3))
        .withCurrency("THB")
        .build
      val ctx = FlowContextBuilder(baseRequest)
      flow.getFxFreezingForCxlReBook(ctx, "THB", None) should_== None
    }

    "CFF-1118=B: return empty when it's not cxlRebook request correctly" in {
      val baseRequest =
        aValidBaseRequest.withBExperiments(List(ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3)).withCurrency("THB").build
      val ctx = FlowContextBuilder(baseRequest)
      flow.getFxFreezingForCxlReBook(ctx, "THB", Some(35.44)) should_== None
    }
  }

  "should set cxlRebookOriginalSellInInRequest to None when useLocalCurrency is true" in {
    val rebookingRequest = aValidReBookingRequestV3
      .withOriginalSellIn(Some(50.0d))
      .withOriginalUsdToRequestExchangeRate(Some(35.44d))
      .withActionType(ReBookingActionType.MatchLocal)
      .build

    val baseRequest = aValidBaseRequest
      .withReBookingRequest(Some(rebookingRequest))
      .withCurrency("JPY")
      .withExperiment(
        DFExperiment(ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3, Variant.B),
      )
      .build

    val ctx = FlowContextBuilder(baseRequest)

    val dfFinanceMock = new DFFinance(
      customerMarketing = new CustomerMarketing(discounts = Some(aValidDiscounts.build)),
      supplyPrice = new SupplyPrice(List(aValidPrice.build)),
    )

    val roomWithDFFinance: Room = aValidRoom
      .withCurrency("THB")
      .withExchange(exchangeRateTHBToJPY)
      .withExchangeRateList(List(exchangeRateTHBToJPY, exchangeRateTHBToUSD))
      .withLocalDFFinance(dfFinanceMock)
      .build

    val hInfo = aValidHotelInfo.build
    val hotel = aValidHotel.withRoom(roomWithDFFinance).build
    val hotelWithMeta = WithMeta(hotel, hInfo)(baseRequest)
    val hotelData = flow.Data(Seq(hotelWithMeta), ctx, null)

    val (result, _) = Await.result(flow.convertDFFinanceByCurrency(hotelData), 3.seconds)

    val dfFinanceByCurrency = result.head.d.rooms.head.dfFinanceByCurrency
    dfFinanceByCurrency.request.flatMap(_.cancelReBookOriginalSellIn) should beEmpty
  }
}
