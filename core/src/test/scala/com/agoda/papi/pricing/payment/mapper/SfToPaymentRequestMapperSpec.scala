package com.agoda.papi.pricing.payment.mapper

import com.agoda.papi.enums.room.{ChargeOption, ChargeType}
import com.agoda.papi.pricing.discounting.models.request.rewards._
import com.agoda.platform.pricing.models.utils.DFTestDataBuilders._
import models.pricing.SupplyPrice
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit

class SfToPaymentRequestMapperSpec extends SpecificationWithJUnit {
  val service: SfToPaymentRequestMapper.type = SfToPaymentRequestMapper

  "getNumberOfExtraBed" should {
    val room = aValidRoom.withLengthOfStay(1).build
    val mockRoomPrices = aValidPrice.build
    val mockMandatoryExtraBedPrices =
      aValidPrice.withChargeType(ChargeType.ExtraBed).withChargeOption(ChargeOption.Mandatory).withQuantity(3).build
    val mockOptionalExtraBedPrices = mockMandatoryExtraBedPrices.copy(chargeOption = ChargeOption.Optional)
    val mockLocalDff = aValidLocalDFFinance
      .withSupplyPrice(new SupplyPrice(List(mockRoomPrices, mockMandatoryExtraBedPrices, mockOptionalExtraBedPrices)))
      .build

    "Convert Number of ExtraBed Correctly" in {
      service.getNumberOfExtraBed(room.withLocalDFFinance(mockLocalDff)) should_== 3
      service.getNumberOfExtraBed(
        room.withLocalDFFinance(aValidLocalDFFinance.withSupplyPrice(
          new SupplyPrice(List(mockRoomPrices, mockOptionalExtraBedPrices))))) should_== 0
      service.getNumberOfExtraBed(
        room.withLocalDFFinance(
          aValidLocalDFFinance.withSupplyPrice(
            new SupplyPrice(List(mockRoomPrices,
                                 mockOptionalExtraBedPrices,
                                 mockOptionalExtraBedPrices,
                                 mockOptionalExtraBedPrices))))) should_== 0
      service.getNumberOfExtraBed(
        room.withLocalDFFinance(
          aValidLocalDFFinance.withSupplyPrice(new SupplyPrice(List(mockRoomPrices))))) should_== 0
      service.getNumberOfExtraBed(
        room.withLocalDFFinance(aValidLocalDFFinance.withSupplyPrice(
          new SupplyPrice(List(mockMandatoryExtraBedPrices.copy(quantity = 2)))))) should_== 2

      val room2 = aValidRoom.withLengthOfStay(2)
      val firstDate = DateTime.now()
      val secondDate = firstDate.plusDays(1)
      val priceOfFirstDay =
        List(mockRoomPrices.copy(date = firstDate), mockMandatoryExtraBedPrices.copy(date = firstDate))
      val priceOfFSecondDay =
        List(mockRoomPrices.copy(date = secondDate), mockMandatoryExtraBedPrices.copy(date = secondDate))
      service.getNumberOfExtraBed(
        room2.withLocalDFFinance(
          aValidLocalDFFinance.withSupplyPrice(new SupplyPrice(priceOfFirstDay ++ priceOfFSecondDay)))) should_== 3
    }
  }

  "getCashbackUsdAmountFromRewards" should {
    val baseRoom = aValidRoom.build

    val rewardSet1 = RewardSet(None, Some(Cashback()))
    val rewardSet2 = RewardSet(Some(GiftCard()), None)
    val rewardOptions = RewardOptions(Map("1" -> rewardSet1, "2" -> rewardSet2), Some("1"))
    val rewardOptionsWithEmptyRebate = rewardOptions.copy(rebateOptions = Map("1" -> RebateSet.empty))

    "return none when room's rebateOptions is empty" in {
      val room = baseRoom
        .withUsdDFFinance(aValidLocalDFFinance.withCustomerMarketing(aValidCustomerMarketing.withDiscount(
          Some(aValidDiscounts.withRewardOptions(rewardOptionsWithEmptyRebate).build))))
        .build
      val result = service.getCashbackUsdAmountFromRewards(room, isZeroCashbackLogicRemovalExperiment = false)
      result should_== None
    }

    "return none when room's rewardOptions is empty" in {
      val room = baseRoom
        .withLocalDFFinance(aValidLocalDFFinance.withCustomerMarketing(aValidCustomerMarketing.withDiscount(
          Some(aValidDiscounts.withRewardOptions(rewardOptionsWithEmptyRebate).build))))
        .build
      val result = service.getCashbackUsdAmountFromRewards(room, isZeroCashbackLogicRemovalExperiment = false)
      result should_== None
    }

    "return correct discount amount" in {
      val rewardOptionsWithRebate = rewardOptions.copy(rebateOptions =
        Map("1" -> RebateSet(giftcardRebate = None, cashbackRebate = Some(Rebate(10, 0)))))
      val dfFinance = aValidLocalDFFinance.withCustomerMarketing(
        aValidCustomerMarketing.withDiscount(Some(aValidDiscounts.withRewardOptions(rewardOptionsWithRebate).build)))
      val room = baseRoom.withUsdDFFinance(dfFinance).withLocalDFFinance(dfFinance).build
      val result = service.getCashbackUsdAmountFromRewards(room, isZeroCashbackLogicRemovalExperiment = false)
      result should_== Some(10d)
    }
  }

  "isCashbackDisplaySummaryDefined boolean literal mutation" should {
    "verify that the hardcoded false value affects cashback presence but not amount calculation" in {
      // This test targets the mutation at line 183 where false -> true
      // The key insight is that isCashbackDisplaySummaryDefined affects the presence of the Cashback object
      // in the CashbackSummary, but getCashbackUsdAmountFromRewards always returns the amount regardless
      // of whether the Cashback object is present or not.

      val rewardSet1 = RewardSet(None, Some(Cashback()))
      val rewardOptions = RewardOptions(Map("1" -> rewardSet1), Some("1"))
      val rewardOptionsWithRebate = rewardOptions.copy(rebateOptions =
        Map("1" -> RebateSet(giftcardRebate = None, cashbackRebate = Some(Rebate(10, 0)))))

      val dfFinance = aValidLocalDFFinance.withCustomerMarketing(
        aValidCustomerMarketing.withDiscount(Some(aValidDiscounts.withRewardOptions(rewardOptionsWithRebate).build)))
      val room = aValidRoom.withUsdDFFinance(dfFinance).withLocalDFFinance(dfFinance).build

      // The method always returns the amount regardless of isCashbackDisplaySummaryDefined value
      // This is because it extracts cashbackSummary.amount, not cashbackSummary.cashback
      val resultWithExperimentFalse =
        service.getCashbackUsdAmountFromRewards(room, isZeroCashbackLogicRemovalExperiment = false)
      val resultWithExperimentTrue =
        service.getCashbackUsdAmountFromRewards(room, isZeroCashbackLogicRemovalExperiment = true)

      // Both should return the same amount because the method extracts the amount field
      resultWithExperimentFalse should_== Some(10d)
      resultWithExperimentTrue should_== Some(10d)
    }

    "document that the boolean literal mutation affects internal structure but not this method's return value" in {
      // This test documents that the mutation from false to true would affect the internal
      // structure of the RewardOption (specifically whether CashbackSummary.cashback is None or Some)
      // but would not affect the return value of getCashbackUsdAmountFromRewards

      val rewardSet1 = RewardSet(None, Some(Cashback()))
      val rewardOptions = RewardOptions(Map("1" -> rewardSet1), Some("1"))
      val rewardOptionsWithRebate = rewardOptions.copy(rebateOptions =
        Map("1" -> RebateSet(giftcardRebate = None, cashbackRebate = Some(Rebate(15, 0)))))

      val dfFinance = aValidLocalDFFinance.withCustomerMarketing(
        aValidCustomerMarketing.withDiscount(Some(aValidDiscounts.withRewardOptions(rewardOptionsWithRebate).build)))
      val room = aValidRoom.withUsdDFFinance(dfFinance).withLocalDFFinance(dfFinance).build

      // The return value is always the same because it's based on the amount field
      val result = service.getCashbackUsdAmountFromRewards(room, isZeroCashbackLogicRemovalExperiment = true)
      result should_== Some(15d)
    }

    "verify that the mutation would not change the behavior of getCashbackUsdAmountFromRewards" in {
      // This test confirms that changing isCashbackDisplaySummaryDefined from false to true
      // would not change the behavior of this specific method, which explains why the mutation survives

      val rewardSet1 = RewardSet(None, Some(Cashback()))
      val rewardOptions = RewardOptions(Map("1" -> rewardSet1), Some("1"))
      val rewardOptionsWithRebate = rewardOptions.copy(rebateOptions =
        Map("1" -> RebateSet(giftcardRebate = None, cashbackRebate = Some(Rebate(20, 0)))))

      val dfFinance = aValidLocalDFFinance.withCustomerMarketing(
        aValidCustomerMarketing.withDiscount(Some(aValidDiscounts.withRewardOptions(rewardOptionsWithRebate).build)))
      val room = aValidRoom.withUsdDFFinance(dfFinance).withLocalDFFinance(dfFinance).build

      // Both experiment values should return the same result
      val resultFalse = service.getCashbackUsdAmountFromRewards(room, isZeroCashbackLogicRemovalExperiment = false)
      val resultTrue = service.getCashbackUsdAmountFromRewards(room, isZeroCashbackLogicRemovalExperiment = true)

      // The results are the same because the method extracts the amount regardless of cashback presence
      resultFalse should_== Some(20d)
      resultTrue should_== Some(20d)
    }
  }
}
