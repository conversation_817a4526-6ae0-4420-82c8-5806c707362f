package com.agoda.papi.pricing.cxlrebook.validation

import com.agoda.papi.pricing.cxlrebook.validation.CancelRebookValidationsSpec.{
  createRoomWithPrices,
  defaultRoom,
  rebookingRequest,
}
import com.agoda.papi.pricing.discounting.logic.PromotionTestDataBuilder.aValidCheckIn
import com.agoda.papi.pricing.discounting.models.request.rewards.{GiftCard => DiscountingGiftCard}
import com.agoda.papi.pricing.flow.SfRoomPricingInternalTestData.aValidSfRoomPricing
import com.agoda.papi.pricing.models.internal.SfRoomPricing
import com.agoda.platform.pricing.models.utils.DFModelTestDataBuilders.{aValidExchangeRate, aValidReBookingRequestV2}
import com.agoda.platform.pricing.models.utils.SFTestDataBuilders.aValidItemBreakdown
import model.ItemBreakdownType
import models.enums.ReBookingActionType.MatchLocal
import models.pricing.enums.{BookingItemTypes, BookingRateTypes}
import models.starfruit.{BookingInfo, ItemBreakdown}
import models.utils.DFCoreModelsTestDataBuilders.{
  aValidCustomerMarketing,
  aValidDiscounts,
  aValidLocalDFFinance,
  aValidRoom,
  toCustomerMarketingBuilder,
}
import org.specs2.mutable.SpecificationWithJUnit

import scala.collection.compat.toOptionCompanionExtension

class CancelRebookValidationsSpec extends SpecificationWithJUnit {
  "validateCxlRebookV3" should {
    val mockSearchID = "aaa"

    "return empty list when no rebooking request is provided" in {
      val (roomRes, tagResult) = CancelRebookValidations.validateCxlRebookV3(mockSearchID, Seq(defaultRoom), None)
      tagResult.size should_== 0
      roomRes.size should_== 0
    }

    "return empty list when rooms list is empty" in {
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID, Seq.empty, Some(rebookingRequest))
      tagResult.size should_== 0
      roomRes.size should_== 0
    }

    "return list with one room when all values match" in {
      CancelRebookValidations
        .validateCxlRebookV3(mockSearchID, Seq(defaultRoom), Some(rebookingRequest))
        ._1
        .size should_== 1

      CancelRebookValidations
        .validateCxlRebookV3(mockSearchID,
                             Seq(createRoomWithPrices(promo = 0)),
                             Some(rebookingRequest.copy(promoAmount = Some(0))))
        ._1
        .size should_== 1

      CancelRebookValidations
        .validateCxlRebookV3(mockSearchID,
                             Seq(createRoomWithPrices(cashback = 0, bookingInfoCashback = 0)),
                             Some(rebookingRequest.copy(cashbackAmount = Some(0))))
        ._1
        .size should_== 1
    }

    "return empty list when originalSellIn does not match" in {
      val invalidRebookingReq = rebookingRequest.copy(originalSellIn = Some(200d))
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID, Seq(defaultRoom), Some(invalidRebookingReq))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "failure",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return empty list when promoAmount does not match" in {
      val invalidRebookingReq = rebookingRequest.copy(promoAmount = Some(-20d))
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID, Seq(defaultRoom), Some(invalidRebookingReq))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "failure",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return empty list when cashback does not match" in {
      val invalidRebookingReq = rebookingRequest.copy(cashbackAmount = Some(5d))
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID, Seq(defaultRoom), Some(invalidRebookingReq))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "failure",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "failure",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return empty list when bookingInfo cashback does not match" in {
      val invalidRebookingReq = rebookingRequest.copy(cashbackAmount = Some(5d))
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                    Seq(createRoomWithPrices(cashback = 5d, bookingInfoCashback = 10d)),
                                                    Some(invalidRebookingReq))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "failure",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return empty list when originalSellIn input is invalid" in {
      val negativeSellInReq = rebookingRequest.copy(originalSellIn = Some(-100d))
      val (roomRes1, tagResult1) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                    Seq(createRoomWithPrices(originalSellIn = -100d)),
                                                    Some(negativeSellInReq))
      tagResult1.size should_== 10
      // this is all success because price matching is work correctly but invalid input
      tagResult1 should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes1.size should_== 0

      val zeroSellInReq = rebookingRequest.copy(originalSellIn = Some(0))
      val (roomRes2, tagResult2) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                    Seq(createRoomWithPrices(originalSellIn = 0d)),
                                                    Some(zeroSellInReq))
      tagResult2.size should_== 10
      // this is all success because price matching is work correctly but invalid input
      tagResult2 should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes2.size should_== 0
    }

    "return empty list when promoAmount is invalid" in {
      val positivePromoReq = rebookingRequest.copy(promoAmount = Some(10d))
      val (roomRes, tagResult) = CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                                             Seq(createRoomWithPrices(promo = 10d)),
                                                                             Some(positivePromoReq))

      tagResult.size should_== 10
      // this is all success because price matching is work correctly but invalid input
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return empty list when cashback is invalid" in {
      val negativeCashbackReq = rebookingRequest.copy(cashbackAmount = Some(-2d))
      val (roomRes, tagResult) = CancelRebookValidations.validateCxlRebookV3(
        mockSearchID,
        Seq(createRoomWithPrices(cashback = -2d, bookingInfoCashback = -2d)),
        Some(negativeCashbackReq))
      tagResult.size should_== 10
      // this is all success because price matching is work correctly but invalid input
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return empty list when there is cofunded cashback return" in {
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                    Seq(createRoomWithPrices(includeCofundedCashback = true)),
                                                    Some(rebookingRequest))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "failure",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return empty list when there is promocode cashback return" in {
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                    Seq(createRoomWithPrices(includePromocodeCashback = true)),
                                                    Some(rebookingRequest))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "failure",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return normal list when there is SupplierFunding return" in {
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                    Seq(createRoomWithPrices(includedSupplierFunding = true)),
                                                    Some(rebookingRequest))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 1
    }

    "return normal list when SupplyLocalAmount is failure" in {
      val mockRoom = createRoomWithPrices(includedSupplierFunding = true, localCurrency = Some("CNY"))
      val (roomRes, tagResult) = CancelRebookValidations.validateCxlRebookV3(
        mockSearchID,
        Seq(mockRoom.copy(bookingInfo = mockRoom.bookingInfo.map(e =>
          e.copy(roomExchange = Some(aValidExchangeRate.withToRequest(33.5)))))),
        Some(rebookingRequest),
      )
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "failure",
      )
      roomRes.size should_== 1
    }

    "treat SupplyLocalAmount difference at threshold as success" in {
      val mockRoom = createRoomWithPrices(localCurrency = Some("THB"), netInLocal = Some(0.0001))
      val (_, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID, Seq(mockRoom), Some(rebookingRequest))
      tagResult.get("SupplyLocalAmount") should_== Some("success")
    }

    "return inventory when there is SupplierFunding return" in {
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                    Seq(createRoomWithPrices(includedSupplierFunding = true)),
                                                    Some(rebookingRequest))

      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 1
    }

    "return empty list when there is HotelFunding return" in {
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                    Seq(createRoomWithPrices(includedHotelFunding = true)),
                                                    Some(rebookingRequest))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "failure",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
    }

    "return empty list when there is CreditCardFunding return" in {
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                    Seq(createRoomWithPrices(includedCreditCardFunding = true)),
                                                    Some(rebookingRequest))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "failure",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return empty list when there is GovernmentFunding return" in {
      val (roomRes, tagResult) =
        CancelRebookValidations.validateCxlRebookV3(mockSearchID,
                                                    Seq(createRoomWithPrices(includedGovernmentFunding = true)),
                                                    Some(rebookingRequest))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "failure",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return empty list when there are both cofunded cashback & promocode cashback return" in {
      val (roomRes, tagResult) = CancelRebookValidations.validateCxlRebookV3(
        mockSearchID,
        Seq(createRoomWithPrices(includeCofundedCashback = true, includePromocodeCashback = true)),
        Some(rebookingRequest))
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "failure",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "failure",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
      roomRes.size should_== 0
    }

    "return one room with supplierFunding when there are both cofunded cashback & promocode cashback return from multiple rooms" in {
      val (roomRes, tagResult) = CancelRebookValidations.validateCxlRebookV3(
        mockSearchID,
        Seq(
          createRoomWithPrices(originalSellInLocal = Some(400d),
                               promoLocal = Some(-40d),
                               cashbackLocal = Some(20d),
                               localCurrency = Some("THB"),
                               netInLocal = Some(0))),
        Some(rebookingRequest),
      )
      roomRes.size should_== 1
      tagResult.size should_== 10
      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "BookingInfo.Cashback" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
    }

    "return list with one room when all values match in local" in {
      val rebookingRequestWithLocalAmounts = aValidReBookingRequestV2.copy(
        cashbackAmount = Some(20d),
        originalSellIn = Some(400d),
        promoAmount = Some(-40d),
        actionType = MatchLocal,
      )

      val (roomRes, tagResult) = CancelRebookValidations.validateCxlRebookV3(
        mockSearchID,
        Seq(
          createRoomWithPrices(originalSellInLocal = Some(400d),
                               promoLocal = Some(-40d),
                               cashbackLocal = Some(20d),
                               localCurrency = Some("THB"),
                               netInLocal = Some(0))),
        Some(rebookingRequestWithLocalAmounts),
      )

      roomRes.size should_== 1
      tagResult.size should_== 9

      tagResult should_== Map(
        "SellInclusive" -> "success",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
    }

    "return empty list when originalSellInLocal does not match" in {
      val rebookingRequestWithLocalAmounts = aValidReBookingRequestV2.copy(
        cashbackAmount = Some(20d),
        originalSellIn = Some(4000d),
        promoAmount = Some(-40d),
        actionType = MatchLocal,
      )

      val (roomRes, tagResult) = CancelRebookValidations.validateCxlRebookV3(
        mockSearchID,
        Seq(
          createRoomWithPrices(originalSellInLocal = Some(400d),
                               promoLocal = Some(-40d),
                               cashbackLocal = Some(20d),
                               localCurrency = Some("THB"),
                               netInLocal = Some(0))),
        Some(rebookingRequestWithLocalAmounts),
      )

      roomRes.size should_== 0
      tagResult.size should_== 9

      tagResult should_== Map(
        "SellInclusive" -> "failure",
        "PromocodeCashback" -> "success",
        "Cashback" -> "success",
        "CreditCardFunding" -> "success",
        "GovernmentFunding" -> "success",
        "HotelFunding" -> "success",
        "CofundedCashback" -> "success",
        "DiscountPromotion" -> "success",
        "SupplyLocalAmount" -> "success",
      )
    }
  }
}

object CancelRebookValidationsSpec {

  val defaultSellIn = 100d
  val defaultPromo = -10d
  val defaultCashback = 2d

  private[CancelRebookValidationsSpec] val rebookingRequest = aValidReBookingRequestV2.copy(
    originalSellIn = Some(defaultSellIn),
    promoAmount = Some(defaultPromo),
    cashbackAmount = Some(defaultCashback),
  )

  private[CancelRebookValidationsSpec] val defaultRoom = createRoomWithPrices()

  def createRoomWithPrices(originalSellIn: Double = defaultSellIn,
                           originalSellInLocal: Option[Double] = None,
                           promo: Double = defaultPromo,
                           promoLocal: Option[Double] = None,
                           cashback: Double = defaultCashback,
                           cashbackLocal: Option[Double] = None,
                           bookingInfoCashback: Double = defaultCashback,
                           includeCofundedCashback: Boolean = false,
                           includePromocodeCashback: Boolean = false,
                           includedSupplierFunding: Boolean = false,
                           includedHotelFunding: Boolean = false,
                           includedCreditCardFunding: Boolean = false,
                           includedGovernmentFunding: Boolean = false,
                           netIn: Double = 100d,
                           netInLocal: Option[Double] = None,
                           localCurrency: Option[String] = None): SfRoomPricing = {
    val roomSellIn: ItemBreakdown = aValidItemBreakdown
      .withDate(aValidCheckIn)
      .withBookingItemType(BookingItemTypes.Room)
      .withLocal(originalSellIn)
      .withOption(Some(models.pricing.enums.ChargeOptions.Mandatory))
      .withBookingRateType(BookingRateTypes.SellInclusive)
      .build

    val roomSellInLocal = originalSellInLocal.map { amount =>
      aValidItemBreakdown
        .withDate(aValidCheckIn)
        .withBookingItemType(BookingItemTypes.Room)
        .withLocal(amount)
        .withOption(Some(models.pricing.enums.ChargeOptions.Mandatory))
        .withBookingRateType(BookingRateTypes.SellInclusive)
        .build
    }

    val roomPromo = aValidItemBreakdown
      .withDate(aValidCheckIn)
      .withBookingItemType(BookingItemTypes.Room)
      .withLocal(promo)
      .withOption(Some(models.pricing.enums.ChargeOptions.Optional))
      .withBookingRateType(BookingRateTypes.DiscountPromotion)
      .build

    val roomPromoLocal = promoLocal.map { amount =>
      aValidItemBreakdown
        .withDate(aValidCheckIn)
        .withBookingItemType(BookingItemTypes.Room)
        .withLocal(amount)
        .withOption(Some(models.pricing.enums.ChargeOptions.Optional))
        .withBookingRateType(BookingRateTypes.DiscountPromotion)
        .build
    }

    val bookingCashback = aValidItemBreakdown
      .withBookingItemType(BookingItemTypes.Room)
      .withLocal(cashback)
      .withOption(Some(models.pricing.enums.ChargeOptions.Optional))
      .withBookingRateType(BookingRateTypes.Cashback)
      .build

    val roomCashbackLocal = cashbackLocal.map { amount =>
      aValidItemBreakdown
        .withDate(aValidCheckIn)
        .withBookingItemType(BookingItemTypes.Room)
        .withLocal(amount)
        .withOption(Some(models.pricing.enums.ChargeOptions.Optional))
        .withBookingRateType(BookingRateTypes.Cashback)
        .build
    }

    val bookingCofundedCashback = aValidItemBreakdown
      .withBookingItemType(BookingItemTypes.None)
      .withLocal(2d)
      .withOption(Some(models.pricing.enums.ChargeOptions.Mandatory))
      .withBookingRateType(BookingRateTypes.CofundedCashback)
      .build

    val bookingPromocodeCashback = aValidItemBreakdown
      .withBookingItemType(BookingItemTypes.None)
      .withLocal(2d)
      .withOption(Some(models.pricing.enums.ChargeOptions.Mandatory))
      .withBookingRateType(BookingRateTypes.PromocodeCashback)
      .build

    val bookingSupplierFunding = aValidItemBreakdown
      .withBookingItemType(BookingItemTypes.Discount)
      .withLocal(2d)
      .withOption(Some(models.pricing.enums.ChargeOptions.Mandatory))
      .withBookingRateType(BookingRateTypes.SupplierFunding)
      .build

    val bookingHotelFunding = aValidItemBreakdown
      .withBookingItemType(BookingItemTypes.Discount)
      .withLocal(2d)
      .withOption(Some(models.pricing.enums.ChargeOptions.Mandatory))
      .withBookingRateType(BookingRateTypes.HotelFunding)
      .build

    val bookingGovernmentFunding = aValidItemBreakdown
      .withBookingItemType(BookingItemTypes.Discount)
      .withLocal(2d)
      .withOption(Some(models.pricing.enums.ChargeOptions.Mandatory))
      .withBookingRateType(BookingRateTypes.GovernmentFunding)
      .build

    val bookingCreditCardFunding = aValidItemBreakdown
      .withBookingItemType(BookingItemTypes.Discount)
      .withLocal(2d)
      .withOption(Some(models.pricing.enums.ChargeOptions.Mandatory))
      .withBookingRateType(BookingRateTypes.CreditCardFunding)
      .build

    val roomNetIn = aValidItemBreakdown
      .withDate(aValidCheckIn)
      .withBookingItemType(BookingItemTypes.Room)
      .withLocal(netIn)
      .withOption(Some(models.pricing.enums.ChargeOptions.Mandatory))
      .withBookingRateType(BookingRateTypes.NetInclusive)

    val mockItemBreakdownMap: Map[String, ItemBreakdownType] = Map(
      "USD" -> Left(
        Map(
          "S1" -> roomSellIn,
          "P1" -> roomPromo,
          "C1" -> bookingCashback,
          "N1" -> roomNetIn.build,
        ) ++
          Option.when(includeCofundedCashback)("C2" -> bookingCofundedCashback) ++
          Option.when(includePromocodeCashback)("C3" -> bookingPromocodeCashback) ++
          Option.when(includedSupplierFunding)("SF1" -> bookingSupplierFunding) ++
          Option.when(includedHotelFunding)("SF2" -> bookingHotelFunding) ++
          Option.when(includedGovernmentFunding)("SF3" -> bookingGovernmentFunding) ++
          Option.when(includedCreditCardFunding)("SF4" -> bookingCreditCardFunding),
      ),
    ) ++ localCurrency.map { cur =>
      val netInMap = Map("N1" -> roomNetIn.withLocal(netInLocal.getOrElse(netIn * 4d)).build)
      val sellInMap = roomSellInLocal
        .map { breakdown =>
          Map("S1" -> breakdown)
        }
        .getOrElse(Map.empty)
      val promoMap = roomPromoLocal
        .map { breakdown =>
          Map("P1" -> breakdown)
        }
        .getOrElse(Map.empty)
      val cashbackMap = roomCashbackLocal
        .map { breakdown =>
          Map("C1" -> breakdown)
        }
        .getOrElse(Map.empty)
      cur -> Left(netInMap ++ sellInMap ++ promoMap ++ cashbackMap)

    }

    val discounts = aValidDiscounts.withGiftcard(Some(DiscountingGiftCard(percent = 5))).build
    val dfFinance = aValidLocalDFFinance.withCustomerMarketing(aValidCustomerMarketing.withDiscount(Some(discounts)))
    val roomCurrency = localCurrency.getOrElse(aValidSfRoomPricing.dfRoom.currency)
    val sfRoom = aValidSfRoomPricing
      .withItemBreakdown(mockItemBreakdownMap)
      .withRoom(aValidRoom.withLocalDFFinance(dfFinance).withCurrency(roomCurrency).build)
      .withBookingInfo(Some(BookingInfo(
        cashbackUsdAmount = Some(bookingInfoCashback),
        numberOfRoom = 1,
        numberOfExtraBed = 0,
        accountingEntity = None,
        priceTemplateId = None,
        rateCategory = None,
        isAdvanceGuarantee = false,
        reqExchange = None,
        roomExchange = None,
        usdExchange = None,
      )))
      .build

    sfRoom
  }
}
