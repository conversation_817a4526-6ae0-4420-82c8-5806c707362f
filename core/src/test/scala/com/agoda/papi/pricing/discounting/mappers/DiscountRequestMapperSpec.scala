package com.agoda.papi.pricing.discounting.mappers

import api.request.{
  BaseRequest,
  BookingFilter,
  FeatureFlag,
  LoyaltyInfo,
  OccInfo,
  ReBookingRequest,
  ClientInfo => RequestClientInfo,
}
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.papi.constants.PlatformID
import com.agoda.papi.enums.request.LoyaltySearchType
import com.agoda.papi.enums.room.ChargeType.Surcharge
import com.agoda.papi.enums.room.ChargeType.ExtraBed
import com.agoda.papi.enums.room.{ChargeOption, SubChargeType, ChargeType => CommonChargeType}
import com.agoda.papi.pricing.configuration.{JapanCampaignSettingsProducer, MaxDownliftRemovalSettingsProducer}
import com.agoda.papi.enums.campaign.PriceDisplayTypes
import com.agoda.papi.pricing.discounting.models.request.context.{ClientInfo, RequestParams}
import com.agoda.papi.pricing.discounting.models.request.pricing.DiscountRoom
import com.agoda.papi.pricing.discounting.models.request.promocode._
import com.agoda.papi.pricing.flow.postsearch.WithPricingParameters
import com.agoda.papi.ypl.pricing.CancellationPolicyServiceImpl
import com.agoda.platform.pricing.models.utils.DFTestDataBuilders._
import external.pricing.PricingService.PricingRoomLookup
import mocks.{
  ESSServiceMock,
  ExchangeDataServiceMock,
  JapanCampaignSettingsProducerMock,
  MapUtilCmsService,
  MaxDownliftRemovalSettingsProducerMock,
  PricingParametersMock,
}
import models.{DFExperiment, SearchTypes}
import models.consts.{ABTest, DMC}
import models.db.PromoEligibleDataForFiredrill
import models.pricing.{
  DFFinance,
  DFFinanceByCurrency,
  DFOfferOccupancy,
  DfRoomAllocation,
  FinProducts,
  FinanceProductInfo,
  PseudoCouponMessage,
  Room,
  RoomExperiments,
  RoomFeatures,
  SmartFlexInfo,
  SmartSaverInfo,
  SupplyPrice,
  Price => DFPrice,
}
import models.whitelabel.RegulationFeatureEnabledSetting
import org.joda.time.DateTime
import org.specs2.mutable.SpecificationWithJUnit
import com.agoda.papi.pricing.services.{CmsService => UtilCmsService}
import models.starfruit.{ExternalLoyaltyRequest, FencedOriginObject, FencedRateKey, FencedRatePair}
import pricing.models.common.FinanceProductType
import settings.APSPeekSettings
import com.agoda.papi.pricing.discounting.models.request.rewards.{Cashback => DiscountingCashback}
import com.agoda.papi.pricing.discounting.models.request.soybean.DiscountingExternalLoyaltyProfile
import com.agoda.papi.pricing.discounting.models.response.{Downlift => DiscountingDownlift}
import com.agoda.papi.ypl.models.GUIDGenerator.UID
import com.agoda.papi.ypl.models.{ResellExternalData, RoomIdentifiersProtoHelper, YplMasterChannel, YplRateFence}
import com.agoda.papi.ypl.models.consts.Channel
import com.agoda.papi.ypl.models.occupancy.MaxAllowedFreeChildAgeRange
import com.agoda.utils.ess.models.EssContext
import external.pricing.{DispatchInfo, HotelDispatchInfo}
import external.pricing.ess.ESSService
import models.SearchTypes.{HotelForBooking, HotelSearch}
import models.enums.ReBookingActionType.MatchUSD
import models.flow.FlowBaseContextMock
import models.pricing.enums.{ApplyType, ApplyTypes}
import org.scalatest.prop.Tables.Table
import org.specs2.concurrent.ExecutionEnv
import org.specs2.specification.Scope
import services.exchange.ExchangeDataService

class DiscountRequestMapperSpec(implicit ee: ExecutionEnv) extends SpecificationWithJUnit {
  trait TestScope extends Scope {
    val service: DiscountRequestMapper with ExchangeDataService =
      new Object() with DiscountRequestMapper with CancellationPolicyServiceImpl with ExchangeDataServiceMock {

        override protected val pricingParameters: WithPricingParameters = new PricingParametersMock()
        override protected val cmsService: UtilCmsService = new MapUtilCmsService(Map(1 -> "Translate campaign name"))
        override protected val japanCampaignSettingsProducer: JapanCampaignSettingsProducer =
          JapanCampaignSettingsProducerMock.static
        override protected val maxDownliftRemovalSettingsProducer: MaxDownliftRemovalSettingsProducer =
          MaxDownliftRemovalSettingsProducerMock.static
        override protected val essService: ESSService = new ESSServiceMock {
          override def isESSPublishPriceEligibleCountry(essContext: EssContext,
                                                        isPublishPriceESSConfigEnabled: Boolean): Boolean =
            isPublishPriceESSConfigEnabled
        }
        override def getDataForFiredrill: PromoEligibleDataForFiredrill = PromoEligibleDataForFiredrill(cids = Set(1, 2))
      }
  }

  val dfRoomAllocation: DfRoomAllocation = aValidRoomAllocationInfoAdultsOnly.build
  val dfRoomAllocationWithChildren: DfRoomAllocation = aValidRoomAllocationInfoAdultsChildren.build
  val dfRoomAllocationWithChildrenPreSchool: DfRoomAllocation = aValidRoomAllocationInfoAdultsPreSchool.build
  val todayDateTime: DateTime = DateTime.now()
  val fenceTH = YplRateFence("TH", -1, 1)
  val fenceTHKey = FencedRateKey(Some(fenceTH.origin), Some(fenceTH.cid), Some(fenceTH.language))
  val mockFencedRatePair = FencedRatePair(fenceTHKey, FencedOriginObject(Set(1)))
  implicit val tracer = NoOpTracer
  implicit val roomExperiments: RoomExperiments = RoomExperiments(false)

  "getPriceBasis" should {

    "return correct result of extraBed from room price basis when useRefPrices=false" in new TestScope {
      val dfPriceMock = getDFPriceMock(chargeType = ExtraBed).copy(refMargin = 160d)
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfFinance = DFFinance.build(priceListMock, None)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(dfFinance).withOccupancy(roomOccupancyMock).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.sellExclusive, inc = dfPriceMock.sellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, ExtraBed)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return correct not rounded result of extraBed from ref room price basis when useRefPrices=true and isPriceStateRequest=false" in new TestScope {
      val dfPriceMock = getDFPriceMock(chargeType = ExtraBed).copy(
        margin = 10d,
        refMargin = 15d,
        downliftPercent = 5d,
        netExclusive = 1000.00000001,
      )
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfFinance = DFFinance.build(priceListMock, None)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(dfFinance).withOccupancy(roomOccupancyMock).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.refSellExclusive, inc = dfPriceMock.refSellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 2.3333333333333335,
        ),
      )
      val priceBasisList = service.getPriceBasisForCharge(dfRoomMock,
                                                          occupancyInfoMock,
                                                          isPriceStateRequest = false,
                                                          ExtraBed,
                                                          useRefPrices = true)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return correct not rounded result of extraBed from ref room price basis when useRefPrices=true and isPriceStateRequest=true" in new TestScope {
      val dfPriceMock = getDFPriceMock(chargeType = ExtraBed).copy(
        margin = 10d,
        refMargin = 15d,
        downliftPercent = 5d,
        netExclusive = 1000.00000001,
      )
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfFinance = DFFinance.build(priceListMock, None)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(dfFinance).withOccupancy(roomOccupancyMock).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.refSellExclusive, inc = dfPriceMock.refSellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 2.3333333333333335,
        ),
      )
      val priceBasisList = service.getPriceBasisForCharge(dfRoomMock,
                                                          occupancyInfoMock,
                                                          isPriceStateRequest = true,
                                                          ExtraBed,
                                                          useRefPrices = true)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return correct result of surcharge from room price basis when useRefPrices=false" in new TestScope {
      val dfPriceMock = getDFPriceMock(chargeType = Surcharge)
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfFinance = DFFinance.build(priceListMock, None)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(dfFinance).withOccupancy(roomOccupancyMock).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.sellExclusive, inc = dfPriceMock.sellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, Surcharge)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return correct not rounded result of surcharge from room price basis when useRefPrices=true and isPriceStateRequest=false" in new TestScope {
      val dfPriceMock = getDFPriceMock(chargeType = Surcharge).copy(
        margin = 10d,
        refMargin = 15d,
        downliftPercent = 5d,
        netExclusive = 1000.00000001,
      )
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfFinance = DFFinance.build(priceListMock, None)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(dfFinance).withOccupancy(roomOccupancyMock).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.refSellExclusive, inc = dfPriceMock.refSellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 2.3333333333333335,
        ),
      )
      val priceBasisList = service.getPriceBasisForCharge(dfRoomMock,
                                                          occupancyInfoMock,
                                                          isPriceStateRequest = false,
                                                          Surcharge,
                                                          useRefPrices = true)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return correct not rounded result of surcharge from room price basis when useRefPrices=true and isPriceStateRequest=true" in new TestScope {
      val dfPriceMock = getDFPriceMock(chargeType = Surcharge).copy(
        margin = 10d,
        refMargin = 15d,
        downliftPercent = 5d,
        netExclusive = 1000.00000001,
      )
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfFinance = DFFinance.build(priceListMock, None)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(dfFinance).withOccupancy(roomOccupancyMock).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.refSellExclusive, inc = dfPriceMock.refSellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 2.3333333333333335,
        ),
      )
      val priceBasisList = service.getPriceBasisForCharge(dfRoomMock,
                                                          occupancyInfoMock,
                                                          isPriceStateRequest = true,
                                                          Surcharge,
                                                          useRefPrices = true)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return correct subTypeId Adult when subChargeType = None" in new TestScope {
      val dfPriceMock = getDFPriceMock(chargeType = Surcharge).copy(subChargeType = SubChargeType.None)
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfFinance = DFFinance.build(priceListMock, None)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(dfFinance).withOccupancy(roomOccupancyMock).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.sellExclusive, inc = dfPriceMock.sellInclusive),
          subTypeId = SubChargeType.Adult,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, Surcharge)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return correct result when surcharge zero margin and not" in new TestScope {
      val dfPriceMock = getDFPriceMock(chargeType = Surcharge)
      val priceListMock = List(dfPriceMock.copy(margin = 0), dfPriceMock.copy(margin = 1))
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfFinance = DFFinance.build(priceListMock, None)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(dfFinance).withOccupancy(roomOccupancyMock).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = 1001.0, inc = 1101.0),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 21.0,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, Surcharge)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return correct result when surcharge zero margin and not mandatory," in new TestScope {
      val dfPriceMock = getDFPriceMock(chargeType = Surcharge)
      val priceListMock = List(dfPriceMock, dfPriceMock.copy(margin = 1, chargeOption = ChargeOption.Optional))
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfFinance = DFFinance.build(priceListMock, None)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(dfFinance).withOccupancy(roomOccupancyMock).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.sellExclusive, inc = dfPriceMock.sellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, Surcharge)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return list of price basis correctly when room allocation is empty with 2 adults when useRefPrices=false" in new TestScope {
      val dfPriceMock = getDFPriceMock().copy(refMargin = 160d)
      val priceListMock = List(dfPriceMock)
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(DFFinance.build(priceListMock, None)).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.sellExclusive, inc = dfPriceMock.sellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, CommonChargeType.Room)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return list of price basis correctly when room allocation is empty with 2 adults when useRefPrices=true" in new TestScope {
      val dfPriceMock = getDFPriceMock().copy(refMargin = 160d)
      val priceListMock = List(dfPriceMock)
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2)

      val dfRoomMock =
        aValidRoom.withPrices(priceListMock).withLocalDFFinance(DFFinance.build(priceListMock, None)).build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = 1160d, inc = 1260d),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 1.125,
        ),
      )
      val priceBasisList = service.getPriceBasisForCharge(dfRoomMock,
                                                          occupancyInfoMock,
                                                          isPriceStateRequest = true,
                                                          CommonChargeType.Room,
                                                          useRefPrices = true)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return list of price basis correctly when room allocation is empty with 3 adults and 1 extra bed" in new TestScope {
      val dfPriceMock = getDFPriceMock()
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withAdults(3).withExtraBeds(1)
      val occupancyInfoMock = getOccupancyInfoMock(adults = 3)

      val dfRoomMock = aValidRoom
        .withPrices(priceListMock)
        .withLocalDFFinance(DFFinance.build(priceListMock, None))
        .withOccupancy(roomOccupancyMock)
        .build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.sellExclusive, inc = dfPriceMock.sellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 3,
          quantity = 1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, CommonChargeType.Room)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return list of price basis correctly when room allocation is empty with 2 adults, 1 child have no free child policy" in new TestScope {
      val dfPriceMock = getDFPriceMock()
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(2))

      val dfRoomMock = aValidRoom
        .withPrices(priceListMock)
        .withLocalDFFinance(DFFinance.build(priceListMock, None))
        .withOccupancy(roomOccupancyMock)
        .build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.sellExclusive, inc = dfPriceMock.sellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 3,
          quantity = 1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, CommonChargeType.Room)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return list of price basis correctly when room allocation is empty with 2 adults, 1 child have 1 free child policy" in new TestScope {
      val dfPriceMock = getDFPriceMock()
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfRoomMock = aValidRoom
        .withPrices(priceListMock)
        .withLocalDFFinance(DFFinance.build(priceListMock, None))
        .withOccupancy(roomOccupancyMock)
        .build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.sellExclusive, inc = dfPriceMock.sellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, CommonChargeType.Room)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return list of price basis correctly when room allocation is empty with 2 adults, 1 child have 1 free child policy" in new TestScope {
      val dfPriceMock = getDFPriceMock()
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2, childrenAgesList = List(1))

      val dfRoomMock = aValidRoom
        .withPrices(priceListMock)
        .withLocalDFFinance(DFFinance.build(priceListMock, None))
        .withOccupancy(roomOccupancyMock)
        .build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.sellExclusive, inc = dfPriceMock.sellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, CommonChargeType.Room)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return list of price basis correctly when room allocation is present with 2 adults" in new TestScope {
      val roomAllocationInfoMock = Map(1 -> dfRoomAllocation)
      val dfPriceMock = getDFPriceMock()
      val priceListMock = List(dfPriceMock)
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.withFreeChildren(1).build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 2)

      val dfRoomMock = aValidRoom
        .withPrices(priceListMock)
        .withLocalDFFinance(DFFinance.build(priceListMock, None))
        .withOccupancy(roomOccupancyMock)
        .withRoomAllocationInfo(roomAllocationInfoMock)
        .build

      val expectedResult = List(
        PriceBasis(
          price = Price(exc = dfPriceMock.sellExclusive, inc = dfPriceMock.sellInclusive),
          subTypeId = dfPriceMock.subChargeType,
          roomNumber = dfPriceMock.roomNumber.getOrElse(1),
          date = dfPriceMock.date,
          occupancyCount = 2,
          quantity = 1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )
      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, CommonChargeType.Room)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return list of price basis with merged price when room allocation is present with adults and children" in new TestScope {
      val roomAllocationInfoMock = Map(1 -> dfRoomAllocation, 2 -> dfRoomAllocationWithChildrenPreSchool)
      val priceListMock = List(
        getDFPriceMock(roomNumber = 1, subChargeType = SubChargeType.Adult),
        getDFPriceMock(roomNumber = 2, subChargeType = SubChargeType.Adult),
        getDFPriceMock(roomNumber = 1, subChargeType = SubChargeType.PreSchool),
        getDFPriceMock(roomNumber = 2, subChargeType = SubChargeType.PreSchool),
        getDFPriceMock(roomNumber = 2, subChargeType = SubChargeType.PreSchool),
      )
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 3, childrenAgesList = List(2, 2, 2), 2)

      val dfRoomMock = aValidRoom
        .withPrices(priceListMock)
        .withLocalDFFinance(DFFinance.build(priceListMock, None))
        .withOccupancy(roomOccupancyMock)
        .withRoomAllocationInfo(roomAllocationInfoMock)
        .build

      val expectedPrice = Price(exc = priceListMock.head.sellExclusive, inc = priceListMock.head.sellInclusive)
      val expectedResult = List(
        PriceBasis(expectedPrice,
                   SubChargeType.Adult,
                   1,
                   priceListMock.head.date,
                   dfRoomAllocationWithChildrenPreSchool.adults,
                   1,
                   downliftToMarginFactor = 1.1333333333333333),
        PriceBasis(expectedPrice,
                   SubChargeType.Adult,
                   2,
                   priceListMock.head.date,
                   dfRoomAllocationWithChildrenPreSchool.adults,
                   1,
                   downliftToMarginFactor = 1.1333333333333333),
        PriceBasis(
          expectedPrice.copy(exc = expectedPrice.exc * 2, inc = expectedPrice.inc * 2),
          SubChargeType.PreSchool,
          2,
          priceListMock.head.date,
          dfRoomAllocationWithChildrenPreSchool.childrenCountAsRoomOcc,
          1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )

      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, CommonChargeType.Room)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }

    "return list of price basis correctly when room allocation is present with adults and children" in new TestScope {
      val roomAllocationInfoMock = Map(1 -> dfRoomAllocationWithChildren, 2 -> dfRoomAllocationWithChildrenPreSchool)
      val priceListMock = List(
        getDFPriceMock(roomNumber = 1, subChargeType = SubChargeType.Adult),
        getDFPriceMock(roomNumber = 2, subChargeType = SubChargeType.Adult),
        getDFPriceMock(roomNumber = 1, subChargeType = SubChargeType.Toddler),
        getDFPriceMock(roomNumber = 2, subChargeType = SubChargeType.PreSchool),
      )
      val roomOccupancyMock = aValidRoomOccupancyAdultsOnly.build
      val occupancyInfoMock = getOccupancyInfoMock(adults = 4, childrenAgesList = List(1, 2), rooms = 2)

      val dfRoomMock = aValidRoom
        .withPrices(priceListMock)
        .withLocalDFFinance(DFFinance.build(priceListMock, None))
        .withOccupancy(roomOccupancyMock)
        .withRoomAllocationInfo(roomAllocationInfoMock)
        .build

      val expectedPrice = Price(exc = priceListMock.head.sellExclusive, inc = priceListMock.head.sellInclusive)
      val expectedResult = List(
        PriceBasis(expectedPrice,
                   SubChargeType.Adult,
                   1,
                   priceListMock.head.date,
                   dfRoomAllocationWithChildren.adults,
                   1,
                   downliftToMarginFactor = 1.1333333333333333),
        PriceBasis(
          expectedPrice,
          SubChargeType.Toddler,
          1,
          priceListMock.head.date,
          dfRoomAllocationWithChildren.childrenCountAsRoomOcc,
          1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
        PriceBasis(expectedPrice,
                   SubChargeType.Adult,
                   2,
                   priceListMock.head.date,
                   dfRoomAllocationWithChildren.adults,
                   1,
                   downliftToMarginFactor = 1.1333333333333333),
        PriceBasis(
          expectedPrice,
          SubChargeType.PreSchool,
          2,
          priceListMock.head.date,
          dfRoomAllocationWithChildrenPreSchool.childrenCountAsRoomOcc,
          1,
          downliftToMarginFactor = 1.1333333333333333,
        ),
      )

      val priceBasisList =
        service.getPriceBasisForCharge(dfRoomMock, occupancyInfoMock, isPriceStateRequest = false, CommonChargeType.Room)

      priceBasisList.size should_== expectedResult.size
      priceBasisList should_== expectedResult
    }
  }

  "roundPricePerDay" should {
    val precision = 3

    "return rounded price when should round price" in new TestScope {
      val price: Price = Price(150.678922234, 200.56822922234)
      val result: Price = service.roundPricePerDay(price, precision, shouldRoundPrice = true)
      result should_== Price(150.679, 200.568)
    }

    "return input price when should not round price" in new TestScope {
      val price: Price = Price(150.678922234, 200.56822922234)
      val result: Price = service.roundPricePerDay(price, precision, shouldRoundPrice = false)
      result should_== Price(150.678922234, 200.56822922234)
    }
  }

  "getJapanCampaignSettings" should {
    "return correctly" in new TestScope {
      val exchangeRateMock = service.getExchangeRate("USD", "USD")

      val result = service.getJapanCampaignSettings(exchangeRateMock)

      result.peakDayMinimumBookingValue should_== 16.69
      result.normalDayVoucherAllowance should_== 3000
      result.peakDayVoucherAllowance should_== 1000
    }
  }

  "toDiscountRequest" should {
    "return isHotelCountryJapan true" in new TestScope {
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val meta = aValidMetaHotel.build.meta.withCountryId(3L)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)
      result.map(_.requestParams.isHotelCountryJapan should_== true).await
    }

    "return rebookInfo if rebook info is given" in new TestScope {
      val reBookingRequest = ReBookingRequest(
        1,
        None,
        1000.0,
        promoAmount = Some(-1.2),
        originalSellIn = Some(1.3),
        cashbackAmount = Some(1.5),
        originalNetIn = Some(1.6),
        originalUsdToRequestExchangeRate = Some(1.7),
        actionType = MatchUSD,
      )
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest.withReBookingRequest(Some(reBookingRequest)))
      val meta = aValidMetaHotel.build.meta.withChainId(3)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)

      val rebookInfoF = result.map(_.requestParams.rebookInfo)
      rebookInfoF.map(_.isDefined should_== true).await
      rebookInfoF.map(_.get.cancelRebookOriginalPromoAmount should_== reBookingRequest.promoAmount.map(_ * -1)).await
    }

    "not return rebookInfo if rebook info given but Sellin is missing" in new TestScope {
      val reBookingRequest = ReBookingRequest(
        1,
        None,
        1000.0,
        promoAmount = Some(1.2),
        originalSellIn = None,
        cashbackAmount = Some(1.5),
        originalNetIn = Some(1.6),
        originalUsdToRequestExchangeRate = Some(1.7),
        actionType = MatchUSD,
      )
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest.withReBookingRequest(Some(reBookingRequest)))
      val meta = aValidMetaHotel.build.meta.withChainId(3)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)
      result.map(_.requestParams.rebookInfo.isDefined should_== false).await
    }

    "return soybeanContexts" in new TestScope {
      val priceMetaData = Map("DYNAMIC_PRICING" -> "encrypt data")
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest.withPricingMetaData(priceMetaData))
      val meta = aValidMetaHotel.build.meta.withChainId(3)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)
      result.map(_.requestParams.soybeanContexts should_== priceMetaData).await
    }

    "return soybeanContexts when REDUCE_DISCOUNTING_LATENCY is B side" in new TestScope {
      val priceMetaData = Map("DYNAMIC_PRICING" -> "encrypt data")
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest.withPricingMetaData(priceMetaData))
      val meta = aValidMetaHotel.build.meta.withChainId(3)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)
      result.map(_.requestParams.soybeanContexts should_== priceMetaData).await
    }

    "return isMaxDownliftRemovalEnabled false" in new TestScope {
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val meta = aValidMetaHotel.build.meta.withChainId(3)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)
      result.map(_.requestParams.isMaxDownliftRemovalEnabled should_== false).await
    }

    "return isHotelCountryJapan false" in new TestScope {
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val result = service.toDiscountingRequest(Seq.empty, Map.empty, Map.empty)
      result.map(_.requestParams.isHotelCountryJapan should_== false).await
    }

    "return discount request without pseudo coupon message for non price state request" in new TestScope {
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val pseudoCouponMessageMock = Some(PseudoCouponMessage("ABC", 1, 11.111d))
      ctx.pseudoCouponMessage = pseudoCouponMessageMock

      val meta = aValidMetaHotel.build.meta.withCountryId(3L)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)
      result.map(_.discountHotels.head.pseudoCouponMessage should_== None).await
    }

    "return discount request with pseudo coupon message when there is price state request" in new TestScope {
      implicit val ctx =
        aValidFlowBaseContext(aValidBaseRequest.withBookingFilter(BookingFilter(isPriceStateRequest = true)))
      val pseudoCouponMessageMock = Some(PseudoCouponMessage("ABC", 1, 11.111d))
      ctx.pseudoCouponMessage = pseudoCouponMessageMock

      val meta = aValidMetaHotel.build.meta.withCountryId(3L)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)
      result
        .map(_.discountHotels.head.pseudoCouponMessage.map(_.code) should_== pseudoCouponMessageMock.map(_.code))
        .await
    }

    "return isAps true if hotel is APS lookup is not empty and chainId is not in blacklist" in new TestScope {
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val meta = aValidMetaHotel.build.meta.withChainId(3)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val apsRoomLookup: PricingRoomLookup = Map(metaHotel.id -> Map(1 -> aValidRoom.build))
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, apsRoomLookup)
      result.map(_.discountHotels.head.isAps should_== true).await
    }

    "return isAps false if hotel is APS lookup is not empty but chainId is in blacklist" in new TestScope {
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val meta = aValidMetaHotel.build.meta.withChainId(APSPeekSettings.apply().blackListChainIds.head)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val apsRoomLookup: PricingRoomLookup = Map(metaHotel.id -> Map(1 -> aValidRoom.build))
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, apsRoomLookup)
      result.map(_.discountHotels.head.isAps should_== false).await
    }

    "return isAps false if hotel is APS lookup is empty and chainId is not in blacklist" in new TestScope {
      implicit val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val meta = aValidMetaHotel.build.meta.withChainId(3)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)
      result.map(_.discountHotels.head.isAps should_== false).await
    }

    "return isEligibleForPublishPriceESS true when country is eligible for publish price" in new TestScope {
      implicit val ctx =
        aValidFlowBaseContext(aValidBaseRequest.withExperiments(List(DFExperiment(ABTest.ESS_PUBLISH_PRICE, 'B'))))
      val meta = aValidMetaHotel.build.meta.withChainId(3)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)
      result.map(_.discountHotels.head.skipPublishPriceESSOnPriceBasis should_== true).await
    }

    "return isEligibleForPublishPriceESS false when country is eligible for publish price but CFF-237=A" in new TestScope {
      implicit val ctx =
        aValidFlowBaseContext(aValidBaseRequest.withExperiments(List(DFExperiment(ABTest.ESS_PUBLISH_PRICE, 'A'))))
      val meta = aValidMetaHotel.build.meta.withChainId(3)
      val metaHotel = aValidMetaHotel.withMeta(meta).build
      val result = service.toDiscountingRequest(Seq(metaHotel), Map.empty, Map.empty)
      result.map(_.discountHotels.head.skipPublishPriceESSOnPriceBasis should_== false).await
    }
  }

  "toRequestParams" should {

    "return false when there is no experiment override for experiment" in new TestScope {
      // Arrange
      val ctx = aValidFlowBaseContext(aValidBaseRequest)

      // Act
      val result = service.toRequestParams(ctx)

      // Assert
      result.experimentCarrier.isZeroCashbackLogicRemovalExperiment should_== false
      result.experimentCarrier.isDisableEmployeeDealForWeb should_== false
      result.experimentCarrier.isSkipZeroAllotmentRoomForSoybeanOpenDoor should_== false
      result.experimentCarrier.isSkipZeroAllotmentRoomsForSoybean should_== false
      result.experimentCarrier.isPricePushFencing should_== false
      result.experimentCarrier.isRateChannelSwap should_== false
      result.experimentCarrier.isUnblockPriusInJP should_== false
      result.experimentCarrier.isPmcSortCcByMaxDiscount should_== false
      result.experimentCarrier.isConsolidatedDiscountsEnabled should_== false
      result.experimentCarrier.isSoybeanCircuitBreakerForAllRequestEnabled should_== false
      result.experimentCarrier.applySoybeanStreamlineEvenWhenDownliftEntryMissing should_== false
      result.experimentCarrier.isConsolidatedDiscountsFix should_== false
      result.experimentCarrier.isUpdateDFESearchPerformanceMetricTags should_== false
      result.experimentCarrier.isGetPromoPriorityFromSoybean should_== false
      result.experimentCarrier.isPmcHybridDiscountProcedure should_== false
      result.experimentCarrier.isPmcConsolidatedAppliedDiscountPriceDisplayType should_== false
      result.experimentCarrier.excludeApsForPrius should_== false
      result.experimentCarrier.isDynamicDownliftEnabled should_== false
      result.experimentCarrier.isPmcEnableRateCampaignMessageInfo should_== false
      result.experimentCarrier.isPmcEnableRateCampaignFeedback should_== false
      result.experimentCarrier.isPmcFixFencesComparingRooms should_== false
      result.experimentCarrier.isPmcEnableValueTag should_== false
    }

    "return loyalty search type if ctx.baseRequest.externalLoyaltyRequest.loyaltySearchType exist" in new TestScope {
      val mockExternalLoyaltyRequest = Some(
        ExternalLoyaltyRequest(
          selectedOffersIdentifier = None,
          points = Some(10000d),
          partnerClaimToken = None,
          loyaltySearchType = Some("BURN"),
        ),
      )
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withExternalLoyaltyRequest(mockExternalLoyaltyRequest))
      val result = service.toRequestParams(ctx)
      result.loyaltySearchType should_== Some(LoyaltySearchType.BURN)
    }

    "return None for loyalty search type if ctx.baseRequest.externalLoyaltyRequest.loyaltySearchType does not map to ant loyaltySearchType" in new TestScope {
      val mockExternalLoyaltyRequest = Some(
        ExternalLoyaltyRequest(
          selectedOffersIdentifier = None,
          points = Some(10000d),
          partnerClaimToken = None,
          loyaltySearchType = Some("INVALID_SEARCH_TYPE"),
        ),
      )
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withExternalLoyaltyRequest(mockExternalLoyaltyRequest))
      val result = service.toRequestParams(ctx)
      result.loyaltySearchType should_== None
    }

    "return None for loyalty search type if ctx.baseRequest.externalLoyaltyRequest.loyaltySearchType does not exists" in new TestScope {
      val mockExternalLoyaltyRequest = Some(
        ExternalLoyaltyRequest(
          selectedOffersIdentifier = None,
          points = Some(10000d),
          partnerClaimToken = None,
          loyaltySearchType = None,
        ),
      )
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withExternalLoyaltyRequest(mockExternalLoyaltyRequest))
      val result = service.toRequestParams(ctx)
      result.loyaltySearchType should_== None
    }

    "return needPromotionPricePeek as true if isPricePeekEnabled true" in new TestScope {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withRegulationFeatureEnabledSetting(
          aValidRegulationFeatureEnabledSetting.withIsPricePeekEnabled(true)))
      val result = service.toRequestParams(ctx)
      result.needPromotionPricePeek should_== true
    }

    "return needPromotionPricePeek as true if isPricePeekEnabled true" in new TestScope {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withRegulationFeatureEnabledSetting(
          aValidRegulationFeatureEnabledSetting.withIsClaimPromotionEnabled(true)))
      val result = service.toRequestParams(ctx)
      result.needPromotionPricePeek should_== true
      result.isCCAutoApply should_== false
    }

    "return needPromotionPricePeek as true if when isPromotionPeekAllowedOnBFOnly = false and isPricePeekEnabled true" in new TestScope {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withWhitelabelSetting(aValidWhitelabelSetting.withIsPromotionPeekAllowedOnBFOnly(false))
          .withRegulationFeatureEnabledSetting(
            aValidRegulationFeatureEnabledSetting.withIsClaimPromotionEnabled(false).withIsPricePeekEnabled(true)))
      val result = service.toRequestParams(ctx)
      result.needPromotionPricePeek should_== true
    }

    "return needPromotionPricePeek as false if when isPromotionPeekAllowedOnBFOnly = false and isPricePeekEnabled false" in new TestScope {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withWhitelabelSetting(aValidWhitelabelSetting.withIsPromotionPeekAllowedOnBFOnly(false))
          .withRegulationFeatureEnabledSetting(
            aValidRegulationFeatureEnabledSetting.withIsClaimPromotionEnabled(false).withIsPricePeekEnabled(false)))
      val result = service.toRequestParams(ctx)
      result.needPromotionPricePeek should_== false
    }

    "return needPromotionPricePeek as false if when isPromotionPeekAllowedOnBFOnly = true and isPricePeekEnabled true and non bf" in new TestScope {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withWhitelabelSetting(aValidWhitelabelSetting.withIsPromotionPeekAllowedOnBFOnly(true))
          .withRegulationFeatureEnabledSetting(
            aValidRegulationFeatureEnabledSetting.withIsClaimPromotionEnabled(false).withIsPricePeekEnabled(true))
          .withSearchType(SearchTypes.HotelSearch))
      val result = service.toRequestParams(ctx)
      result.needPromotionPricePeek should_== false
    }

    "return needPromotionPricePeek as true if when isPromotionPeekAllowedOnBFOnly = true and isPricePeekEnabled true and bf" in new TestScope {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withWhitelabelSetting(aValidWhitelabelSetting.withIsPromotionPeekAllowedOnBFOnly(true))
          .withRegulationFeatureEnabledSetting(
            aValidRegulationFeatureEnabledSetting.withIsClaimPromotionEnabled(false).withIsPricePeekEnabled(true))
          .withSearchType(SearchTypes.HotelForBooking))
      val result = service.toRequestParams(ctx)
      result.needPromotionPricePeek should_== true
    }

    "return needPromotionPricePeek as false if when isPromotionPeekAllowedOnBFOnly = true and isPricePeekEnabled false and non bf" in new TestScope {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withWhitelabelSetting(aValidWhitelabelSetting.withIsPromotionPeekAllowedOnBFOnly(true))
          .withRegulationFeatureEnabledSetting(
            aValidRegulationFeatureEnabledSetting.withIsClaimPromotionEnabled(false).withIsPricePeekEnabled(false))
          .withSearchType(SearchTypes.HotelSearch))
      val result = service.toRequestParams(ctx)
      result.needPromotionPricePeek should_== false
    }

    "return needPromotionPricePeek as false if when isPromotionPeekAllowedOnBFOnly = true and isPricePeekEnabled false and bf" in new TestScope {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withWhitelabelSetting(aValidWhitelabelSetting.withIsPromotionPeekAllowedOnBFOnly(true))
          .withRegulationFeatureEnabledSetting(
            aValidRegulationFeatureEnabledSetting.withIsClaimPromotionEnabled(false).withIsPricePeekEnabled(false))
          .withSearchType(SearchTypes.HotelForBooking))
      val result = service.toRequestParams(ctx)
      result.needPromotionPricePeek should_== false
    }

    "return AutoApplyPromoApplyFirst, isAutoApplyAllPromosEnabled as false if regulation settings are not passed" in new TestScope {
      val ctxWithoutRegulation = aValidFlowBaseContext(aValidBaseRequest)
      val promocodeResult2 = service.toRequestParams(ctxWithoutRegulation)
      promocodeResult2.isAutoApplyPromoApplyFirst should_== false
      promocodeResult2.isAutoApplyAllPromosEnabled should_== false
    }

    "return AutoApplyPromoApplyFirst, isAutoApplyAllPromosEnabled as true if regulation settings are passed" in new TestScope {
      val dfRequest = aValidBaseRequest
        .withRegulationFeatureEnabledSetting(
          RegulationFeatureEnabledSetting(isAutoApplyPromoApplyFirst = true, isAutoApplyAllPromosEnabled = true),
        )
        .build
      val ctxWithoutRegulation = aValidFlowBaseContext(dfRequest)
      val promocodeResult2 = service.toRequestParams(ctxWithoutRegulation)
      promocodeResult2.isAutoApplyPromoApplyFirst should_== true
      promocodeResult2.isAutoApplyAllPromosEnabled should_== true
    }

    "return isDynamicDownliftEnabled as true only if JP-3013=B and the regulation settings are passed and booking request" in new TestScope {
      val testCases = Table(
        ("isDynamicDownliftEnabled", "dynamicDownliftExpSide", "isBookingRequest", "expectedIsDynamicDownliftEnabled"),
        (false, 'B', true, false),
        (true, 'A', true, false),
        (true, 'B', false, false),
        (true, 'B', true, true),
      )

      testCases.forEvery {
        (isDynamicDownliftEnabled, dynamicDownliftExpSide, isBookingRequest, expectedIsDynamicDownliftEnabled) =>
          // Arrange
          val dfRequest = aValidBaseRequest
            .withRegulationFeatureEnabledSetting(
              RegulationFeatureEnabledSetting(isDynamicDownliftEnabled = isDynamicDownliftEnabled),
            )
            .withSearchType(if (isBookingRequest) SearchTypes.HotelForBooking else SearchTypes.CitySearch)
            .withExperiments(List(DFExperiment(ABTest.DYNAMIC_DOWNLIFT, dynamicDownliftExpSide)))
            .build
          val ctxWithoutRegulation = aValidFlowBaseContext(dfRequest)

          // Act
          val requestParams = service.toRequestParams(ctxWithoutRegulation)

          // Assert
          requestParams.experimentCarrier.isDynamicDownliftEnabled should_== expectedIsDynamicDownliftEnabled
      }
    }

    "return isPriceStateRequest as false if there is price state request" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val promocodeResult2 = service.toRequestParams(ctx)
      promocodeResult2.isPriceStateRequest should_== false
    }

    "return isPriceStateRequest as true if there is price state request" in new TestScope {
      val dfRequest = aValidBaseRequest.withBookingFilter(BookingFilter(isPriceStateRequest = true)).build
      val ctx = aValidFlowBaseContext(dfRequest)
      val promocodeResult2 = service.toRequestParams(ctx)
      promocodeResult2.isPriceStateRequest should_== true
    }

    "return isLandingCcCid as true if PMC-70=B" in new TestScope {
      val dfRequest =
        aValidBaseRequest.withExperiments(List(DFExperiment(ABTest.CC_CAMP_AUTO_APPLY_LANDING_CID_ON_APP, 'B'))).build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.isLandingCcCid should_== true
    }

    val reBookingRequest = ReBookingRequest(
      roomTypeId = 1,
      masterRoomTypeId = None,
      customerPaidPrice = 1000.0,
      promoAmount = None,
      originalSellIn = Some(1.3),
      cashbackAmount = None,
      originalNetIn = None,
      originalUsdToRequestExchangeRate = Some(1.7),
      actionType = MatchUSD,
    )

    "return isSkipDownliftForCxlReBookV3 as true if IsCxlRebookV3" in new TestScope {
      val dfRequest = aValidBaseRequest.withReBookingRequest(Some(reBookingRequest)).build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.isSkipDownliftForCxlReBookV3 should_== true
    }

    "return isSkipDownliftForCxlReBookV3 as false" in new TestScope {
      val dfRequest = aValidBaseRequest.withReBookingRequest(Some(reBookingRequest.copy(originalSellIn = None))).build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.isSkipDownliftForCxlReBookV3 should_== false
    }

    "return isDeprecateClientDiscountFFOnMobileApp as True if PMC-5283=B and platform is MobileApp" in new TestScope {
      val dfRequest = aValidBaseRequest
        .withBExperiment(ABTest.PMC_DEPRECATE_CLIENT_DISCOUNT_FF)
        .withPlatformId(Some(PlatformID.MobileAPI))
        .build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.isDeprecateClientDiscountFFOnMobileApp should_== true
    }

    "return isDeprecateClientDiscountFFOnMobileApp as False if PMC-5283=B but platform is not MobileApp" in new TestScope {
      val dfRequest = aValidBaseRequest
        .withBExperiment(ABTest.PMC_DEPRECATE_CLIENT_DISCOUNT_FF)
        .withPlatformId(Some(PlatformID.DesktopWeb))
        .build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.isDeprecateClientDiscountFFOnMobileApp should_== false
    }

    "return isDeprecateClientDiscountFFOnMobileApp as False if PMC-5283=A" in new TestScope {
      val dfRequest = aValidBaseRequest
        .withAExperiment(ABTest.PMC_DEPRECATE_CLIENT_DISCOUNT_FF)
        .withPlatformId(Some(PlatformID.MobileAPI))
        .build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.isDeprecateClientDiscountFFOnMobileApp should_== false
    }

    "return isLandingCcCid as true if has AutoApplyCreditCardOnLandingCid feature flag" in new TestScope {
      val dfRequest = aValidBaseRequest.withFeatureFlags(List(FeatureFlag.AutoApplyCreditCardOnLandingCid)).build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.isLandingCcCid should_== true
    }

    "return isLandingCcCid as false" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.isLandingCcCid should_== false
    }

    "return isPmcSupportOfferLevel as false when PMC-4311=A" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.experimentCarrier.isPmcSupportOfferLevel should_== false
    }

    "return isPmcSupportOfferLevel as true when PMC-4311=B" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(
        dfRequest.withExperiments(List(DFExperiment(ABTest.PMC_SUPPORT_OFFER_LEVEL, 'B'))),
      )
      val result = service.toRequestParams(ctx)
      result.experimentCarrier.isPmcSupportOfferLevel should_== true
    }

    "return isPmcSupportOfferLevel as false when PMC-4311=A and is cart feature enabled" in new TestScope {
      val dfRequest = aValidBaseRequest.withCartRequest(Some(aValidCartBaseRequest)).build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.experimentCarrier.isPmcSupportOfferLevel should_== false
    }

    "return isPmcSupportOfferLevel as false when PMC-4311=B and is cart feature enabled" in new TestScope {
      val dfRequest = aValidBaseRequest.withCartRequest(Some(aValidCartBaseRequest)).build
      val ctx = aValidFlowBaseContext(
        dfRequest.withExperiments(List(DFExperiment(ABTest.PMC_SUPPORT_OFFER_LEVEL, 'B'))),
      )
      val result = service.toRequestParams(ctx)
      result.experimentCarrier.isPmcSupportOfferLevel should_== false
    }

    "return isPmcConsolidatedAppliedDiscountPriceDisplayType as false when PMC-4603=A" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.experimentCarrier.isPmcSupportOfferLevel should_== false
    }

    "return isPmcConsolidatedAppliedDiscountPriceDisplayType as true when PMC-4603=B" in new TestScope {
      val dfRequest = aValidBaseRequest.withCartRequest(Some(aValidCartBaseRequest)).build
      val ctx = aValidFlowBaseContext(
        dfRequest.withExperiments(List(DFExperiment(ABTest.PMC_CONSOLIDATED_APPLIED_DISCOUNTS_PRICE_DISPLAY_TYPE, 'B'))),
      )
      val result = service.toRequestParams(ctx)
      result.experimentCarrier.isPmcConsolidatedAppliedDiscountPriceDisplayType should_== true
    }

    "return isPmcEnableRateCampaignMessageInfo as true when PMC-5600=B" in new TestScope {
      val dfRequest = aValidBaseRequest.withCartRequest(Some(aValidCartBaseRequest)).build
      val ctx = aValidFlowBaseContext(
        dfRequest.withExperiments(List(DFExperiment(ABTest.PMC_ENABLE_RATE_CAMPAIGN_MESSAGE_INFO, 'B'))),
      )
      val result = service.toRequestParams(ctx)
      result.experimentCarrier.isPmcEnableRateCampaignMessageInfo should_== true
    }

    "return isPmcEnableRateCampaignFeedback as true when PMC-5610=B" in new TestScope {
      val dfRequest = aValidBaseRequest.withCartRequest(Some(aValidCartBaseRequest)).build
      val ctx = aValidFlowBaseContext(
        dfRequest.withExperiments(List(DFExperiment(ABTest.PMC_ENABLE_RATE_CAMPAIGN_FEEDBACK, 'B'))),
      )
      val result = service.toRequestParams(ctx)
      result.experimentCarrier.isPmcEnableRateCampaignFeedback should_== true
    }

    "return isPmcFixFencesComparingRooms as true when PMC-6102=B and shouldAllocatePmcFixFencesComparingRooms=true" in new TestScope {
      val dfRequest = aValidBaseRequest.withSearchType(HotelForBooking).build
      val ctx = aValidFlowBaseContext(
        dfRequest.withExperiments(List(DFExperiment(ABTest.PMC_FIX_FENCES_COMPARING_ROOMS, 'B'))),
      )
      val result = service.toRequestParams(ctx)
      result.experimentCarrier.isPmcFixFencesComparingRooms should_== true
    }

    "return isPmcFixFencesComparingRooms as false when PMC-6102=B but shouldAllocatePmcFixFencesComparingRooms=false" in new TestScope {
      val dfRequest =
        aValidBaseRequest.withSearchType(HotelSearch).build // shouldAllocatePmcFixFencesComparingRooms will be false
      val ctx = aValidFlowBaseContext(
        dfRequest.withExperiments(List(DFExperiment(ABTest.PMC_FIX_FENCES_COMPARING_ROOMS, 'B'))),
      )
      val result = service.toRequestParams(ctx)
      result.experimentCarrier.isPmcFixFencesComparingRooms should_== false
    }

    "return isPmcEnableValueTag as true only if PMC-6072=B and the regulation settings are passed and not booking request" in new TestScope {
      val testCases = Table(
        ("isValueTagEnabled", "valueTagExperiment", "isBookingRequest", "expected"),
        (false, 'B', false, false),
        (true, 'A', false, false),
        (true, 'B', true, false),
        (true, 'B', false, true),
      )

      testCases.forEvery { (isValueTagEnabled, valueTagExperiment, isBookingRequest, expected) =>
        val dfRequest = aValidBaseRequest
          .withRegulationFeatureEnabledSetting(
            RegulationFeatureEnabledSetting(isValueTagEnabled = isValueTagEnabled),
          )
          .withSearchType(if (isBookingRequest) SearchTypes.HotelForBooking else SearchTypes.CitySearch)
          .withExperiments(List(DFExperiment(ABTest.PMC_ENABLE_VALUE_TAG, valueTagExperiment)))
          .build
        val ctxWithoutRegulation = aValidFlowBaseContext(dfRequest)
        val requestParams = service.toRequestParams(ctxWithoutRegulation)
        requestParams.experimentCarrier.isPmcEnableValueTag should_== expected
      }
    }

    "test excludeApsForPrius" in new TestScope {
      private def test(isNonLogin: Boolean, includeApsPeek: Boolean, priusIdGtZero: Boolean, isBVariant: Boolean)(
        expectedValue: Boolean) = {
        val dfRequest = aValidBaseRequest
          .withLoyalty(LoyaltyInfo(isLogin = Some(!isNonLogin)))
          .withFeatureFlags(if (includeApsPeek) {
            List(FeatureFlag.APSPeek)
          } else List.empty)
          .withPriusID(if (priusIdGtZero) 1 else 0)
          .withExperiments(if (isBVariant) {
            List(DFExperiment(ABTest.EXCLUDE_APS_FOR_PRIUS, 'B'))
          } else List.empty)

        val ctx = aValidFlowBaseContext(dfRequest.build)
        val result = service.toRequestParams(ctx)
        result.experimentCarrier.excludeApsForPrius should_== expectedValue
      }

      // Valid condition of isNonLogin, apsPeek FeatureFlag, priusId > 0 and exp is B
      test(true, true, true, true)(true)

      // Return false for all the invalid conditions
      test(false, true, true, true)(false)
      test(true, false, true, true)(false)
      test(true, true, false, true)(false)
      test(true, true, true, false)(false)

      test(false, false, true, true)(false)
      test(true, false, false, true)(false)
      test(true, true, false, false)(false)

      test(false, false, false, true)(false)
      test(true, false, false, false)(false)

      test(false, false, false, false)(false)
    }

    "return PriceDisplayType correctly" in new TestScope {
      val inputs: List[Option[ApplyType]] = List(
        None,
        Some(ApplyTypes.PB),
        Some(ApplyTypes.PN),
        Some(ApplyTypes.PRPN),
        Some(ApplyTypes.PRPB),
        Some(ApplyTypes.PGPD),
        Some(ApplyTypes.PAPD),
      )
      val results = inputs.map { i =>
        val dfRequest = aValidBaseRequest.build
        val ctx = aValidFlowBaseContext(dfRequest.copy(requiredBasis = i))
        service.toRequestParams(ctx).priceDisplayType
      }
      results should_== List(
        PriceDisplayTypes.PerBook,
        PriceDisplayTypes.PerBook,
        PriceDisplayTypes.PerNight,
        PriceDisplayTypes.PerRoomPerNight,
        PriceDisplayTypes.PerRoom,
        PriceDisplayTypes.PerBook,
        PriceDisplayTypes.PerBook,
      )
    }

    "return isConsolidatedDiscountUserOverridePriceDisplay = true" in new TestScope {
      val dfRequest = aValidBaseRequest
        .withRegulationFeatureEnabledSetting(
          RegulationFeatureEnabledSetting(isConsolidatedDiscountUserOverridePriceDisplay = true),
        )
        .build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.isConsolidatedDiscountUserOverridePriceDisplay should_== true
    }

    "return isConsolidatedDiscountUserOverridePriceDisplay = false" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.isConsolidatedDiscountUserOverridePriceDisplay should_== false
    }

    "return clientInfo correctly" in new TestScope {
      val dfRequest: BaseRequest = aValidBaseRequest
        .withClientInfo(
          RequestClientInfo(
            affiliateId = None,
            cid = None,
            language = 2,
            origin = None,
            storeFront = Some(0),
            platform = Some(1),
            languageUse = 2,
            locale = None,
            deviceTypeId = Some(34),
          ))
        .withPlatformId(Some(1007))
        .build
      val ctx: FlowBaseContextMock = aValidFlowBaseContext(dfRequest)

      val result: RequestParams = service.toRequestParams(ctx)

      result.clientInfo should_== ClientInfo(
        affiliateId = None,
        cid = None,
        languageId = 2,
        origin = None,
        storeFront = 0,
        platformId = 1007,
        languageUse = 2,
        locale = None,
        deviceTypeId = Some(34),
      )
    }

    "return externalLoyaltyProfile correctly" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toRequestParams(ctx)
      result.externalLoyaltyProfile should_==
        Some(
          DiscountingExternalLoyaltyProfile(externalProgramId = Some("101"),
                                            loyaltyAccountNumber = Some("someTestAccount"),
                                            remainingBenefitCredit = Some(10.00),
                                            promocode = Some("someTestPromoCode")))
    }
  }

  "getSoybeanResponseFromDFRoom" should {
    val room = aValidRoom.build

    "return soybean response if there is price state request" in new TestScope {
      val result = service.getSoybeanResponseFromDFRoom(isPriceStateRequest = true, room)
      result.isDefined should_== true
    }

    "return None if non price state request" in new TestScope {
      val result = service.getSoybeanResponseFromDFRoom(isPriceStateRequest = false, room)
      result should_== None
    }

    "return soybean response with downlift if there is price state request" in new TestScope {
      val mockDownlift = DiscountingDownlift(14.0, 111, 123, None)

      val room = aValidRoom
        .withLocalDFFinance(aValidLocalDFFinance.withCustomerMarketing(
          aValidCustomerMarketing.withDiscount(Some(aValidDiscounts.withDownlift(Some(mockDownlift)).build))))
        .build

      val result = service.getSoybeanResponseFromDFRoom(isPriceStateRequest = true, room)
      result.flatMap(_.downlift) should_== Some(mockDownlift)
    }
  }

  "toDiscountRoom" should {
    val cashback = DiscountingCashback("guid", 2, 12d, 60, 120)
    val dfRoom = aValidRoom
      .withLocalDFFinance(
        aValidLocalDFFinance.withCustomerMarketing(
          aValidCustomerMarketing.withDiscount(Some(aValidDiscounts.withCashback(Some(cashback)).build))))
      .build
    val dfHotel = aValidHotel.withRoom(dfRoom).build

    "return discount room with soybean response when there is price state request" in new TestScope {
      val dfRequest = aValidBaseRequest.withBookingFilter(BookingFilter(isPriceStateRequest = true)).build
      val ctx = aValidFlowBaseContext(dfRequest)

      val result = service.toDiscountRoom(dfRoom, dfHotel)(ctx)
      result.soybeanResponse.isDefined should_== true
      result.soybeanResponse.flatMap(_.cashback).map(_.percent) should_== Some(12.0)
    }

    "return discount room with smartFlexType and financeProductType is Replacement when offer has smartflex with replacement true" in new TestScope {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withOrigin(Some("VN")))
      val room = aValidRoom
        .withFinanceProductInfo(FinanceProductInfo(FinProducts(Some(SmartFlexInfo("", processReplacement = true)))))
        .build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.financeProductTypes.size should_== 1
      result.financeProductTypes.head should_== FinanceProductType.SMARTFLEX_REPLACEMENT

    }

    "return discount room with smartFlexType and financeProductType is OfferNonEss when offer has smartflex with replacement false and ess allowed" in new TestScope {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withOrigin(Some("VN")))
      val room = aValidRoom
        .withFinanceProductInfo(FinanceProductInfo(FinProducts(Some(SmartFlexInfo("", processReplacement = false)))))
        .build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.financeProductTypes.size should_== 1
      result.financeProductTypes.head should_== FinanceProductType.SMARTFLEX_OFFER
    }

    "return discount room with smartFlexType and financeProductType is OfferNonEss when offer has smartflex with replacement false and ess not allowed" in new TestScope {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withOrigin(Some("US")))
      val room = aValidRoom
        .withFinanceProductInfo(FinanceProductInfo(FinProducts(Some(SmartFlexInfo("", processReplacement = false)))))
        .build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.financeProductTypes.size should_== 1
      result.financeProductTypes.head should_== FinanceProductType.SMARTFLEX_OFFER_NO_ESS
    }

    "return discount room with smartFlexType and financeProductType is None when offer doesn't have smartflex" in new TestScope {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withOrigin(Some("VN")))
      val result = service.toDiscountRoom(aValidRoom, dfHotel)(ctx)
      result.financeProductTypes.isEmpty should_== true
    }

    "return discount room with smartFlexType is None and with financeProductType is SmartSaver when offer is smartsaver" in new TestScope {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withOrigin(Some("VN")))
      val room =
        aValidRoom.withFinanceProductInfo(FinanceProductInfo(FinProducts(smartSaver = Some(SmartSaverInfo(""))))).build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.financeProductTypes.size should_== 1
      result.financeProductTypes.head should_== FinanceProductType.SMARTSAVER_OFFER
    }

    "return discount room with smartFlexType is Replacement and with financeProductType is Replacement and SmartSaver when offer is replacement and smartsaver" in new TestScope {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withOrigin(Some("VN")))
      val room = aValidRoom
        .withFinanceProductInfo(
          FinanceProductInfo(FinProducts(smartFlex = Some(SmartFlexInfo("", processReplacement = true)),
                                         smartSaver = Some(SmartSaverInfo("")))))
        .build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.financeProductTypes.size should_== 2
      result.financeProductTypes.head should_== FinanceProductType.SMARTFLEX_REPLACEMENT
      result.financeProductTypes.last should_== FinanceProductType.SMARTSAVER_OFFER
    }

    "return discount room without pricingTokens" in new TestScope {
      val dfRequest = aValidBaseRequest
        .withBookingFilter(
          BookingFilter(uidList = List(UID(RoomIdentifiersProtoHelper.writeTo(aValidRoom.toRoomIdentifiers()), None))))
        .withSearchType(SearchTypes.HotelForBooking)
        .build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoom.build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.pricingTokens.size should_== 0
    }

    "return discount room without pricingTokens for search flow" in new TestScope {
      val dfRequest = aValidBaseRequest
        .withBookingFilter(BookingFilter(uidList =
          List(UID(RoomIdentifiersProtoHelper.writeTo(aValidRoomPricingTokens.toRoomIdentifiers()), None))))
        .withSearchType(SearchTypes.HotelSearch)
        .build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoomPricingTokens.build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.pricingTokens.size should_== 0
    }

    "return discount room with pricingTokens" in new TestScope {
      val dfRequest = aValidBaseRequest
        .withBookingFilter(BookingFilter(uidList =
          List(UID(RoomIdentifiersProtoHelper.writeTo(aValidRoomPricingTokens.toRoomIdentifiers()), None))))
        .withSearchType(SearchTypes.HotelForBooking)
        .build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoomPricingTokens.build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.pricingTokens.size should_== 1
    }

    "return discount room with defined pricingActionTokens" in new TestScope {
      val discounts = aValidDiscounts.withPricingTokenActions(aValidPricingActionTokens).build
      val customerService = aValidCustomerMarketing.withDiscount(Some(discounts)).build
      val localDFFinance = aValidLocalDFFinance.withCustomerMarketing(customerService).build
      val room: Room = aValidRoom
        .withDFFinanceByCurrency(DFFinanceByCurrency(local = localDFFinance, request = None, usd = None))
        .build

      val result = service.toDiscountRoom(room, dfHotel)(aValidFlowBaseContext(aValidBaseRequest.build))

      result.pricingActionTokens.keys.foreach { key =>
        result.pricingActionTokens(key) shouldEqual aValidPricingActionTokens(key)
      }
      result.pricingActionTokens.keys shouldEqual aValidPricingActionTokens.keys
    }

    "return discount room with empty pricingActionTokens" in new TestScope {
      val result = service.toDiscountRoom(aValidRoom, dfHotel)(aValidFlowBaseContext(aValidBaseRequest.build))

      result.pricingActionTokens shouldEqual Map.empty
    }

    "return numRooms as number of rooms when roomAllocationInfo is empty" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoomPricingTokens.build

      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.numberOfRooms should_== 1
    }

    "return roomAllocationInfo size as number of rooms when roomAllocationInfo is not empty" in new TestScope {
      val roomAllocationInfo = Map(
        0 -> DfRoomAllocation(2, 1, Map.empty),
        1 -> DfRoomAllocation(2, 1, Map.empty),
      )
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoomPricingTokens.withRoomAllocationInfo(roomAllocationInfo).build

      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.numberOfRooms should_== roomAllocationInfo.size
    }

    "return discount room without available capacity when CHILD-529 is A " in new TestScope {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val room = aValidRoom
        .withOfferOccupancy(
          Some(
            DFOfferOccupancy(1,
                             List(1),
                             List(2),
                             List(3),
                             1,
                             2,
                             1,
                             MaxAllowedFreeChildAgeRange.empty,
                             isApplyNewOccupancyLogic = true)))
        .build
        .build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.availableCapacity should_== None
    }
    "return discount room with available capacity when CHILD-529 is B " in new TestScope {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val room = aValidRoom
        .withOfferOccupancy(
          Some(
            DFOfferOccupancy(1,
                             List(1),
                             List(2),
                             List(3),
                             1,
                             2,
                             1,
                             MaxAllowedFreeChildAgeRange.empty,
                             isApplyNewOccupancyLogic = true)))
        .build
      import com.agoda.papi.pricing.supply.SupplyModuleImplicits._

      val result = service.toDiscountRoom(
        room,
        dfHotel,
        hotelDispatchInfo = Some(
          HotelDispatchInfo(
            hotelId = 1,
            dispatchInfos = Seq(
              DispatchInfo(0, DMC.YCS, YplMasterChannel.RTL, 0, Nil, fencedRatePair = mockFencedRatePair),
              DispatchInfo(
                0,
                DMC.YCS,
                YplMasterChannel(Channel.MigrateAvailableCapacityToSoybeanMock),
                0,
                Nil,
                fencedRatePair = mockFencedRatePair,
              ),
            ),
          )),
      )(ctx)

      result.availableCapacity should_== Some(1)
    }

    "return isFit correctly" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoomPricingTokens.build
      val roomSpy = spy(room)

      val inputs: List[Option[Boolean]] = List(None, Some(true), Some(false))

      val results = inputs.map { i =>
        doReturn(i).when(roomSpy).isFit
        service.toDiscountRoom(roomSpy, dfHotel)(ctx)
      }

      results.map(_.isFit) should_== List(false, true, false)
    }

    "return discount room with MsePricingTokens" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoom.withMsePricingToken(Some("msePricingToken")).build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.msePricingToken must beSome("msePricingToken")
    }

    "return discount room without MsePricingTokens" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoom.withMsePricingToken(None).build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.msePricingToken must beNone
    }

    "return discount room with masterRoomTypeId for HotelBeds" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoom.withSupplierId(DMC.HotelBeds).build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.masterRoomId should_== 1234L
    }

    "return discount room with masterRoomTypeId" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoom.withMasterRoomId(5555L).withRoomTypeId(1111L).build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.masterRoomId should_== 5555L
    }

    "return isMultipleRoomAssignmentPrice correctly" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)

      val inputs: List[Boolean] = List(true, false)

      val results = inputs.map { i =>
        val room = aValidRoomPricingTokens.withRoomFeatures(RoomFeatures(isMultipleRoomAssignmentPrice = i)).build
        service.toDiscountRoom(room, dfHotel)(ctx)
      }

      results.map(_.isMultipleRoomAssignmentPrice) should_== List(true, false)
    }

    "return masterRoomId from room.roomTypeId when supplierId is DMC.HotelBeds" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoom.withMasterRoomId(5555L).withRoomTypeId(11111L).withSupplierId(DMC.HotelBeds).build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.masterRoomId should_== 11111L
    }

    "return isBookingRoom=true if isBookingRequest and room identifier in booking field" in new TestScope {
      val room = aValidRoom.build
      val dfRequest = aValidBaseRequest
        .withSearchType(SearchTypes.HotelForBooking)
        .withBookingFilter(
          BookingFilter(uidList = List(UID(RoomIdentifiersProtoHelper.writeTo(room.toRoomIdentifiers()), None))))
        .build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.isBookingRoom should_== true
    }

    "return isBookingRoom=false if not isBookingRequest and room identifier in booking field" in new TestScope {
      val room = aValidRoom.build
      val dfRequest = aValidBaseRequest
        .withSearchType(SearchTypes.CitySearch)
        .withBookingFilter(
          BookingFilter(uidList = List(UID(RoomIdentifiersProtoHelper.writeTo(room.toRoomIdentifiers()), None))))
        .build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.isBookingRoom should_== false
    }

    "return isBookingRoom=false if isBookingRequest and room identifier not in booking field" in new TestScope {
      val room = aValidRoom.build
      val dfRequest = aValidBaseRequest
        .withSearchType(SearchTypes.CitySearch)
        .withBookingFilter(BookingFilter(uidList = List.empty))
        .build
      val ctx = aValidFlowBaseContext(dfRequest)
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.isBookingRoom should_== false
    }

    "return discount room with valid priceAdjustmentId when room priceAdjustmentId exist" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val pricingInfo = aValidDFPricingInfo.withPriceAdjustmentId(Some(10001L)).build
      val room = aValidRoom.withPricingInfo(pricingInfo).build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.priceAdjustmentId should_== Some(10001L)
    }

    "return discount room with default priceAdjustmentId when room priceAdjustmentId have default value" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val pricingInfo = aValidDFPricingInfo.withPriceAdjustmentId(Some(0L)).build
      val room = aValidRoom.withPricingInfo(pricingInfo).build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.priceAdjustmentId should_== Some(0L)
    }

    "return discount room with default priceAdjustmentId when room priceAdjustmentId not exist" in new TestScope {
      val dfRequest = aValidBaseRequest.build
      val ctx = aValidFlowBaseContext(dfRequest)
      val room = aValidRoom.build
      val result = service.toDiscountRoom(room, dfHotel)(ctx)

      result.priceAdjustmentId should_== Some(0L)
    }

    "return correct ref price basis when price state request and Dynamic DL is enabled" in new TestScope {
      val dfRequest: BaseRequest = aValidBaseRequest.withBookingFilter(BookingFilter(isPriceStateRequest = true)).build
      val ctx: FlowBaseContextMock = aValidFlowBaseContext(dfRequest)
      val localDFFinance = new DFFinance(
        aValidCustomerMarketing,
        new SupplyPrice(
          room = List(aValidPrice.withRefMargin(20d).build),
          extraBed = List(aValidPrice.withRefMargin(20d).withChargeType(CommonChargeType.ExtraBed).build),
          surcharge = List(aValidPrice.withRefMargin(20d).withChargeType(CommonChargeType.Surcharge).build),
        ),
      )
      val room: Room = aValidRoom
        .withDFFinanceByCurrency(DFFinanceByCurrency(local = localDFFinance, request = None, usd = None))
        .build

      val result: DiscountRoom = service.toDiscountRoom(room, dfHotel, isDynamicDownliftEnabled = true)(ctx)

      result.refRoomPriceBasis.map(_.price) shouldEqual List(Price(1020.0, 1120.0))
      result.refMandatorySurchargePriceBasis.map(_.price) shouldEqual List(Price(1020.0, 1120.0))
      result.refMandatoryExtrabedPriceBasis.map(_.price) shouldEqual List(Price(1020.0, 1120.0))
    }

    "build sellIn, sellEx, usdSellEx, usdSellIn, reqSellEx, reqSellIn, usdMargin, usdSellAllIn, reqSellAllIn, usdSurchargeSellInclusive, usdPayOnBook correctly using ref prices " +
      "when price state request and Dynamic DL is enabled" in new TestScope {
        val testCases = Table(
          ("isPriceStateRequest",
           "isDynamicDownliftEnabled",
           "sellEx",
           "sellIn",
           "usdSellEx",
           "usdSellIn",
           "reqSellEx",
           "reqSellIn",
           "usdMargin",
           "usdSellAllIn",
           "reqSellAllIn",
           "usdSurchargeSellInclusive",
           "usdPayOnBook"),
          (true, true, 1020d, 1120d, 1020d, 1120d, 1020d, 1120d, 20d, 2240d, 2240d, 1120d, 2240d),
          (true, false, 1150d, 1250d, 1150d, 1250d, 1150d, 1250d, 150d, 2500d, 2500d, 1250d, 2500d),
          (false, true, 1150d, 1250d, 1150d, 1250d, 1150d, 1250d, 150d, 2500d, 2500d, 1250d, 2500d),
        )
        testCases.forEvery {
          (isPriceStateRequest,
           isDynamicDownliftEnabled,
           sellEx,
           sellIn,
           usdSellEx,
           usdSellIn,
           reqSellEx,
           reqSellIn,
           usdMargin,
           usdSellAllIn,
           reqSellAllIn,
           usdSurchargeSellInclusive,
           usdPayOnBook) =>
            val dfRequest: BaseRequest =
              aValidBaseRequest.withBookingFilter(BookingFilter(isPriceStateRequest = isPriceStateRequest)).build
            val ctx: FlowBaseContextMock = aValidFlowBaseContext(dfRequest)
            val localDFFinance = new DFFinance(
              aValidCustomerMarketing,
              new SupplyPrice(
                room = List(aValidPrice.withRefMargin(20d).build),
                extraBed = List(aValidPrice.withRefMargin(20d).withChargeType(CommonChargeType.ExtraBed).build),
                surcharge = List(aValidPrice.withRefMargin(20d).withChargeType(CommonChargeType.Surcharge).build),
              ),
            )
            val room: Room = aValidRoom
              .withDFFinanceByCurrency(DFFinanceByCurrency(local = localDFFinance, request = None, usd = None))
              .build

            val result: DiscountRoom =
              service.toDiscountRoom(room, dfHotel, isDynamicDownliftEnabled = isDynamicDownliftEnabled)(ctx)

            result.sellEx should_== sellEx
            result.sellIn should_== sellIn
            result.usdSellEx should_== usdSellEx
            result.usdSellIn should_== usdSellIn
            result.reqSellEx should_== reqSellEx
            result.reqSellIn should_== reqSellIn
            result.usdMargin should_== usdMargin
            result.usdSellAllIn should_== usdSellAllIn
            result.reqSellAllIn should_== reqSellAllIn
            result.usdSurchargeSellInclusive should_== usdSurchargeSellInclusive
            result.usdPayOnBook should_== usdPayOnBook
        }
      }
  }

  "getRateModel" should {
    "return RateModel.Old if None" in {
      DiscountRequestMapper.getRateModel(None) shouldEqual (com.agoda.papi.enums.hotel.RateModel.Old)
    }

    "return RateModel.Old if 0" in {
      val rateCategory = aValidRateCategory.withId(0).build
      DiscountRequestMapper.getRateModel(Some(rateCategory)) shouldEqual (com.agoda.papi.enums.hotel.RateModel.Old)
    }

    "return RateModel.New if non 0" in {
      val rateCategory = aValidRateCategory.withId(1).build
      DiscountRequestMapper.getRateModel(Some(rateCategory)) shouldEqual (com.agoda.papi.enums.hotel.RateModel.New)
    }
  }

  "toDiscountingFencedRatePair" should {
    "convert all fields" in {
      val key = YplRateFence("TH", 12345, 2)
      val fencedRateObject = com.agoda.papi.pricing.supply.models.request.FencedOriginObject(Set(2, 8))
      val fencedRatePair = com.agoda.papi.pricing.supply.models.request.FencedRatePair(key, fencedRateObject)

      val convertedFencedRatePair = DiscountRequestMapper.toDiscountingFencedRatePair(fencedRatePair)

      convertedFencedRatePair.key.origin shouldEqual (Some(key.origin))
      convertedFencedRatePair.key.siteId shouldEqual (Some(key.cid))
      convertedFencedRatePair.key.language shouldEqual (Some(key.language))
      convertedFencedRatePair.value.ratePlans shouldEqual (fencedRateObject.ratePlans)
    }
  }

  "isACR" should {
    "return false if None is passed" in {
      DiscountRequestMapper.isACR(None) shouldEqual (Some(false))
    }

    "return false if sourceBookingId is empty" in {
      val resellData = ResellExternalData("")
      DiscountRequestMapper.isACR(Some(resellData)) shouldEqual (Some(false))
    }

    "return true if sourceBookingId is non empty" in {
      val resellData = ResellExternalData("someid")
      DiscountRequestMapper.isACR(Some(resellData)) shouldEqual (Some(true))
    }
  }

  private def getDFPriceMock(roomNumber: Int = 1,
                             quantity: Int = 1,
                             chargeType: CommonChargeType = CommonChargeType.Room,
                             subChargeType: SubChargeType = SubChargeType.Adult,
                             margin: Double = 150): DFPrice = aValidPrice
    .withDate(todayDateTime)
    .withRoomNumber(roomNumber)
    .withChargeType(chargeType)
    .withSubChargeType(subChargeType)
    .withMargin(margin)
    .withQuantity(quantity)
    .build

  private def getOccupancyInfoMock(adults: Int = 1, childrenAgesList: List[Int] = List.empty, rooms: Int = 1): OccInfo =
    aValidOccInfo.withAdults(adults).withChildren(aValidChildren.withChildren(childrenAgesList)).withRooms(rooms).build
}
