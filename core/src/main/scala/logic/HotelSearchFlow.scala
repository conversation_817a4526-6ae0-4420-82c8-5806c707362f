package logic

import api.request.{BaseRequest, FeatureFlag}
import cats.instances.future._
import com.agoda.papi.pricing.core.filters.request.RequestedCurrencyFilterRflow
import com.agoda.papi.pricing.core.logging.message.DFSearchPollInfo
import com.agoda.papi.pricing.core.services.PriceChangeService
import com.agoda.papi.pricing.flow.postsearch.RefreshPaymentInfoStep
import com.agoda.papi.pricing.metadata.model.HotelDataWithHMCData
import com.agoda.papi.pricing.metadata.service.PropertyMetadataServiceDefault
import com.agoda.papi.pricing.opentelemetry.DfTracerOps.TracerExtensions
import com.agoda.papi.pricing.supply.api.SupplyServiceInterface
import com.agoda.papi.pricing.supply.mapper.YplToDfMapper
import com.agoda.papi.pricing.supply.models.request.SupplyRequest
import com.agoda.papi.ypl.logging.MessageSink
import com.agoda.pricestream.models.response
import com.agoda.pricestream.models.response.PastBookingInfoResponse
import com.agoda.protobuf.masterhotelcontext.MasterHotelContext
import com.agoda.utils.flow.PropertyContext
import com.agoda.utils.hadoop.{HadoopLogger, MessageEnvelope}
import com.agoda.utils.lens.Implicits.LensImplicit
import com.agoda.utils.monitoring.ReportingService
import helper.RegulatoryHelper.isHotelLocalCurrencyEnable
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.api.common.{AttributeKey, Attributes}
import logging.DFH3v2ExperimentMessageComposer
import logic.ModelLens.withMetaHotelStatsLens
import logic.WithMeta._
import models._
import models.consts.ABTest
import models.db._
import models.flow.{FlowContext, Variant}
import models.pricing._
import models.result.HotelResultEnvelope
import models.starfruit.{DisplayPrice, FencedRatePair}
import services._
import services.childRateType.ChildRateTypeDataService
import services.exchange.ExchangeDataService
import services.restrictedRatecategory.RestrictedRatecategorySiteIdDataService
import services.variableTax.VariableTaxService
import settings.PropertyExperimentSettingsProducer
import system.FlowMeasureSupport
import system.FlowMeasureSupport.ActStats
import utilhelpers.Measurements

import scala.concurrent.Future

/**
  * Created by ppattanapoon on 7/28/15.
  */
trait HotelSearchFlow[T <: FlowContext] extends HotelSearchFunctionalFlow[T] {
  lazy val hotelSearchFlow = hotelSearchFlowWithoutPacking |> packingResults
  lazy val hotelSearchFlowForPriceGuarantee = postPriceGuaranteeFlow |> packingResults

  private[logic] val packingResults = (Sync.simple("removeMetaHotelInfo", removeMetaHotelInfo) |>
    Sync.simple("envelope", packResults)).toAct("Packing Results")

  //  looks we still need packing results step
  private[logic] def packResults(r: Data[Seq[Hotel]]): HotelResultEnvelope = {
    val results = r.data.filter(h => h.isNotReady || h.isAvailable)
    val notReadyHotels = results.withFilter(_.isNotReady).map(_.id).toList

    HotelResultEnvelope(
      uniqueResultId = r.ctx.baseRequest.searchId,
      isCompleted = true,
      server = settings.hostname,
      version = settings.version,
      hotels = results,
      notReadyHotels = notReadyHotels,
      pseudoCouponMessage = r.ctx.pseudoCouponMessage,
      isBlocked = Option(r.ctx.isBlocked),
    )
  }

  private def removeMetaHotelInfo(d: Data[MetaHotels]): Seq[Hotel] = {
    val result = d.data.map(_.d)
    result
  }
}

trait HotelSearchFunctionalFlow[T <: FlowContext]
  extends ExternalPricingFlow[T]
    with PostExternalPriceFlow[T]
    with DataPreparationFlow[T]
    with FilterHotelsFlow[T]
    with CheapestRoomFlow[T]
    with RequestPreparationService[T]
    with FlowMeasureSupport[T]
    with HotelInfoComposer
    with AgodaAgencyService
    with ExperimentSupport
    with ReportingService
    with PropertyMetadataServiceDefault
    with ExchangeDataService
    with PriceChangeService
    with YplToDfMapper
    with RequestedCurrencyFilterRflow[T]
    with ChildRateTypeDataService
    with RestrictedRatecategorySiteIdDataService
    with VariableTaxService {
  val refreshPaymentInfoStep: RefreshPaymentInfoStep
  protected val priceStreamService: PriceStreamService
  protected val hadoopLogger: HadoopLogger
  protected val messageSink: MessageSink
  val supplyService: SupplyServiceInterface
  protected val propertyExperimentSettingsProducer: PropertyExperimentSettingsProducer

  private val propertyExpCounter =
    GlobalOpenTelemetry.get().getMeter("propertyapipricingdocker").counterBuilder("dfe.experiments.property").build()

  private val numberOfPropertiesKey = AttributeKey.stringKey("number_of_properties")
  private val numberOfExpSideKey = AttributeKey.stringKey("number_of_sides")

  private val requestPreparationSyncAct = (Sync.modifyContext("setDefaultOccupancy", setDefaultOccupancy) |>
    Sync.simple("blockFilterHotels", blockFilterHotelsBaseRequest) |>
    Sync.simple("filterRequestedCurrency", filterSupportedRequestCurrency)).toAct("Request Preparation")

  val prepareHotelMetaFlow = ActWithMeasure("fetch-hotel-meta", fetchHotelMetaCache) |>
    Act("hotelMetaDataToHotelInfo", toHotelInfo)

  private val coreHotelSearchFlow = prepareHotelMetaFlow |>
    Act("enableCheckInFlipExperimentation", enableCheckinFlipExperimentation) |> // this hack stay here for now
    Act("supply-module", processSupplyModule) |>
    ActWithMeasure("buildDFHotels", buildDFHotels)

  lazy val hotelSearchFlowWithoutPacking: Compose[BaseRequest, MetaHotels, MetaHotels] = requestPreparationSyncAct |>
    Act("coreHotelSearchFlow", doCoreHotelSearchFlowWithCOR) |>
    postPriceGuaranteeFlow

  // This flow will be call directly if we have priceGuarantee token in request
  lazy val postPriceGuaranteeFlow = (Sync.simple("setHotelStats", setHotelStats).toAct("setHotelStats") |>
    Act("aggregateFencedHotels", aggregateFencedHotels) |>
    Sync
      .simple("setPastBookingInformationFuture", setPastBookingInformationFuture)
      .toAct("setPastBookingInformationFuture") |>
    externalPricingFlow |>
    Act("applyVariableTax", appliedVariableTax) |>
    Act("fetchPastBookingInformation", fetchPastBookingInformation) |>
    postExternalPriceFlow |>
    Act("setCancelRebookRequestToDFFinance", setCancelRebookRequestToDFFinance) |>
    ActWithMeasure("convertDFFinanceByCurrency", convertDFFinanceByCurrency) |>
    ActWithMeasure("createFlexibleMultiRoom", createFlexibleMultiRoom) |>
    Act("applyCheapestPriceFilters", applyCheapestPriceFilters) |>
    dataPreparationFlow |>
    Act("setAgxCommission", setAgxCommission) |>
    // todo: move this closer to postExternalPriceFlow
    cheapestRoomFlow |>
    Sync.simple("reportH3v2Metrics", reportH3v2Metrics).toAct("reportH3v2Metrics")).named("hotelSearchFlow")

  // the output model will change over once we have migrated the flow below
  private[logic] def processSupplyModule(r: Data[Seq[HotelInfo]]): Future[MetaYPLHotels] = {
    val hotelInfos = r.data
    val supplyHotelInfos = hotelInfos.map(_.supplyHotelInfo)

    val supplyRequest = SupplyRequest(supplyHotelInfos, r.ctx.supplyContext)
    // TODO: wrap with extraction function to transform the current SupplyResponse back to DF Model
    supplyService.getSupplyHotels(supplyRequest).map { res =>
      // look up by hotel id and set SupplyHotelInfo back to HotelInfo
      hotelInfos.flatMap { hotelInfo =>
        res.updatedSupplyMetaYplHotel.get(hotelInfo.hotelId).map { supplyMetaUnit =>
          supplyMetaUnit.copy(meta = hotelInfo.copy(supplyHotelInfo = supplyMetaUnit.meta))
        }
      }
    }
  }

  protected[logic] def buildDFHotels(r: Data[MetaYPLHotels]): Future[(MetaHotels, ActStats)] = {
    val (yplHotelsWithVariantB, yplHotelsWithVariantA) = r.data.partition { hotel =>
      r.ctx.experimentContext
        .isPropertyB(PropertyContext(hotel.id, hotel.d.cityId, hotel.d.countryId), ABTest.DF_HOTEL_CREATION_IN_PAR)
    }

    val hotelsWithVariantA = yplHotelsWithVariantA.map { metaYplHotel =>
      val dfHotel = toDFHotel(metaYplHotel)(r.ctx)
      metaYplHotel.copy(d = dfHotel)
    }

    val hotelsWithVariantBF = Future.sequence(yplHotelsWithVariantB.map { metaYplHotel =>
      Future {
        val dfHotel = toDFHotel(metaYplHotel)(r.ctx)
        metaYplHotel.copy(d = dfHotel)
      }
    })

    for {
      hotelsWithVariantB <- hotelsWithVariantBF
    } yield {
      val hotels = (hotelsWithVariantB ++ hotelsWithVariantA)
      hotels -> ActStats(hotels.map(_.d.rooms.size).sum)
    }
  }

  private[logic] def setAgxCommission(d: Data[MetaHotels]): Future[MetaHotels] =
    if (d.ctx.baseRequest.isPriceStateRequest) {
      Future.successful(d.data)
    } else {
      val result = d.data.map { h =>
        val agxHotel =
          d.ctx.supplyFlowDataContext.getGPCommissions(RateFence.ofClientInfo(d.ctx.baseRequest.cInfo), h.id)
        val hotel = h.d.copy(agxHotel = agxHotel)
        Future.successful(h.copy(d = hotel))
      }
      Future.sequence(result)
    }

  private[logic] def setPastBookingInformationFuture(d: Data[MetaHotels]): MetaHotels = {
    if (d.ctx.determineVariant(ABTest.PARALLEL_FETCH_PAST_BOOKINGINFO) == Variant.B) {
      val pastBookingFuture = fetchPastBookingInformation(d.ctx)
      d.ctx.flowDataContext.setPastBookingInformationFuture(pastBookingFuture)
    }
    d.data
  }

  private[logic] def fetchPastBookingInformation(
    ctx: FlowContext): Future[Map[HotelId, Map[PastBookingInfoKey, PastBookingInfoSummary]]] =
    if (shouldCallPriceStream(ctx)) {
      val pastBookingDataF: Future[PastBookingInfoResponse] = getPastBookingInformationFromPriceStream(ctx)
      val result = pastBookingDataF.map { (pastBookingData: PastBookingInfoResponse) =>
        pastBookingData.pastBookingInfo.map { case (hotelId, info) =>
          val pastBookingInfo: Map[PastBookingInfoKey, PastBookingInfoSummary] = toCoreModelPastBookingInfoSummary(info)
          hotelId -> pastBookingInfo
        }
      }
      result
    } else {
      Future.successful(Map.empty)
    }

  private[logic] def shouldCallPriceStream(ctx: FlowContext): Boolean = {
    val isPriceChangeRequested = ctx.baseRequest.featureFlags.contains(FeatureFlag.PriceHistory)
    val enablePriceChange = ctx.determineVariant(ABTest.ENABLE_PRICE_CHANGE_FEATURE) == Variant.B
    val isEnablePriceTrend = ctx.baseRequest.featureFlags.contains(FeatureFlag.EnablePriceTrend)
    val calculateRareRoomBadge = ctx.baseRequest.featureRequest.calculateRareRoomBadge.getOrElse(false)

    calculateRareRoomBadge || isEnablePriceTrend || (isPriceChangeRequested && enablePriceChange)
  }

  private def getPastBookingInformationFromPriceStream(implicit ctx: FlowContext) =
    ctx.tracer.withSpanM("Booking History") {
      priceStreamService.getPastBookingInfo(ctx)
    }

  private def getPastBookingInfoForHotel(pastBookingData: PastBookingInfoResponse, hotelId: HotelId) = {
    val info: Map[PastBookingInfoKey, response.PastBookingInfoSummary] =
      pastBookingData.pastBookingInfo.getOrElse(hotelId, Map.empty)
    val pastBookingInfo: Map[PastBookingInfoKey, PastBookingInfoSummary] = toCoreModelPastBookingInfoSummary(info)
    pastBookingInfo
  }

  private def toCoreModelPastBookingInfoSummary(info: Map[PastBookingInfoKey, response.PastBookingInfoSummary]) =
    info.map { x =>
      x._1 -> PastBookingInfoSummary(
        x._2.pastBookingInfoSummary.map(y =>
          y._1.toString -> PastBookingPrices(
            DisplayPrice(y._2.minPrice.sellEx, y._2.minPrice.sellAllIn),
            DisplayPrice(y._2.medianPrice.map(_.sellEx).getOrElse(0d), y._2.medianPrice.map(_.sellAllIn).getOrElse(0d)),
          )))
    }

  private[logic] def fetchPastBookingInformation(d: Data[MetaHotels]): Future[MetaHotels] =
    if (d.ctx.determineVariant(ABTest.PARALLEL_FETCH_PAST_BOOKINGINFO) == Variant.B) {
      d.ctx.flowDataContext.getPastBookingInformationFuture.map { pastBookingInformationFromPriceStream =>
        d.data.map { hotel =>
          pastBookingInformationFromPriceStream
            .get(hotel.id)
            .map(p => hotel.copy(meta = hotel.meta.copy(pastBookingInfo = p)))
            .getOrElse(hotel)
        }
      }
    } else if (shouldCallPriceStream(d.ctx)) {
      val pastBookingDataF: Future[PastBookingInfoResponse] = getPastBookingInformationFromPriceStream(d.ctx)

      pastBookingDataF.map { (pastBookingData: PastBookingInfoResponse) =>
        d.data.map { hotel =>
          val pastBookingInfo: Map[PastBookingInfoKey, PastBookingInfoSummary] =
            getPastBookingInfoForHotel(pastBookingData, hotel.meta.hotelId)
          hotel.copy(meta = hotel.meta.copy(pastBookingInfo = pastBookingInfo))
        }
      }
    } else Future.successful(d.data)

  private def doCoreHotelSearchFlowWithCOR(r: Data[List[HotelId]]): Future[WithMeta.MetaHotels] =
    r.withMeta { implicit mx =>
      implicit val ctx: C = r.ctx
      val coreHotelSearchF = ctx.tracer.withSpanM("core hotel flow") {
        coreHotelSearchFlow.apply(Seq(r.data), r.ctx).data.map(_.headOption.getOrElse(Seq.empty))
      }
      val corPriceResponseF =
        ctx.tracer.withSpanM("COR stream service")(priceStreamService.getCalculatedCOR(coreHotelSearchF, r.ctx))

      CalculatedCORService.mergeCrossedOutRates(coreHotelSearchF, corPriceResponseF)
    }

  private[logic] def fetchHotelMetaCache(r: Data[Seq[HotelId]]): Future[(Seq[HotelDataWithHMCData], ActStats)] =
    r.withMeta { implicit mx =>
      val hotelData = r.ctx.baseRequest.supplyEquitySimulationParameters match {
        case Some(supplyEquitySimulationParameters) =>
          fetchSimulatedPropertyData(r.data, supplyEquitySimulationParameters.simulationContextId)
        case None => fetchPropertyData(r.data)
      }

      val isFetchHMC = r.ctx.determineVariant(ABTest.ENABLE_HMC_FETCH_MASTER_HOTEL_ONLY) == Variant.B ||
        r.ctx.determineVariant(ABTest.ENABLE_HMC_FETCH_MASTER_HOTEL_AND_CHILDS) == Variant.B

      val hmcMasterData =
        if (isFetchHMC) fetchPropertyDataHMC(r.data) else Future.successful(Map.empty[HotelId, MasterHotelContext])

      hotelData.foreach { data =>
        r.ctx.updateRequestFlowStatusAfterFetchHotelMetaData(data)
        if (r.ctx.baseRequest.isSSRUserRequest) {
          hadoopLogger.logHadoopMessage(MessageEnvelope(() => DFSearchPollInfo(r.ctx.baseRequest, data)))
        }
      }
      for {
        metas <- hotelData
        hmcs <- hmcMasterData
      } yield {
        val hotelDataWithHMCData = metas.map { m =>
          HotelDataWithHMCData(m, hmcs.get(m.hotelId))
        }
        (hotelDataWithHMCData, ActStats(metas.size))
      }
    }

  private[logic] def enableCheckinFlipExperimentation(r: Data[Seq[HotelInfo]]): Future[Seq[HotelInfo]] = {
    val cityIds: Seq[Long] = r.data.map(_.cityId).distinct

    // DF functions around the experiment manager are aimed to get basic yes/no result for a request.
    // As checkin flip uses cityid, this is no longer true. In a single request we COULD have more than 1 city.
    // For now we don't provide any city id so we get Z allocation.
    if (cityIds.size == 1) {
      r.ctx.updateExperimentContextWithCityId(cityIds.head)
    }

    Future.successful(r.data)
  }

  def toHotelInfo(r: Data[Seq[HotelDataWithHMCData]]): Future[Seq[HotelInfo]] = r.withMeta { implicit mx =>
    val result = r.data.map { d =>
      d.hotelData.toHotelInfo(r.ctx, d.hmcData)
    }

    val numberOfProperties = r.data.size
    if (propertyExperimentSettingsProducer.current.enabled) {
      val allocations = result.map { info =>
        ABTest.EXP_CITYIDLOSFLIP_DFAPI_HOTELSEARCH_AA.foreach(r.ctx.experimentContext.determineVariant(info, _))
        ABTest.EXP_HOTELIDWEEKLYFLIP_DFAPI_HOTELSEARCH_AA.foreach(r.ctx.experimentContext.determineVariant(info, _))
        r.ctx.experimentContext.determineVariant(info, ABTest.CHECKIN_FLIP_AA_TEST)
      }
      val numberOfAllocationSides = allocations.toSet.size
      propertyExpCounter.add(1,
                             Attributes.of(numberOfPropertiesKey,
                                           numberOfProperties.toString,
                                           numberOfExpSideKey,
                                           numberOfAllocationSides.toString))
    }

    Future.successful(result)
  }

  private[logic] def aggregateFencedHotels(r: Data[MetaHotels]): Future[MetaHotels] = r.withMeta { implicit mx =>
    val metaHotels = r.data
    val ctx = r.ctx
    val fencedRatePairs = ctx.baseRequest.fencedRatePairs
    val aggregatedHptels =
      if (fencedRatePairs.isDefined && ctx.determineVariant(ABTest.PRICE_PUSH_FENCING) == Variant.B) {
        val fencedHotels = fencedRatePairs.getOrElse(List.empty).flatMap { pair =>
          getFencedHotels(metaHotels, pair)(ctx)
        }
        fencedHotels
          .groupBy(_.d.id)
          .values
          .map { hotels =>
            val rooms = hotels.flatMap(_.d.rooms)
            val hotel = hotels.head
            val aggregateHotel = hotel.d.copy(rooms = rooms)
            hotel.copy(d = aggregateHotel)
          }
          .toSeq
      } else {
        metaHotels
      }
    Future.successful(aggregatedHptels)
  }

  private[logic] def getFencedHotels(metaHotels: MetaHotels, pair: FencedRatePair)(ctx: FlowContext): MetaHotels = {
    val fencedRate = RateFence.of(pair.key)
    metaHotels.map { h =>
      val rooms = h.d.rooms.collect {
        case r if r.fences.contains(fencedRate) =>
          // Check if the room status is the same as the status in the rateFenceToRoomStatusMap
          r.rateFenceToRoomStatusMap.get(fencedRate) match {
            case Some(status) => if (status != r.roomStatus) {
                ctx.reporter.report(
                  Measurements.fencedRateRoomStatusMismatch,
                  1,
                  Map(
                    Measurements.tagHotelId -> h.id.toString,
                    Measurements.tagFencedRatePairKey -> pair.key.toString,
                    Measurements.tagRoomStatusFromFence -> status.toString,
                    Measurements.tagRoomStatus -> r.roomStatus.toString,
                  ),
                )
              }
            case None => ctx.reporter.report(
                Measurements.fencedRateRoomStatusNotFound,
                1,
                Map(Measurements.tagHotelId -> h.id.toString,
                    Measurements.tagFencedRatePairKey -> pair.key.toString,
                    Measurements.tagRoomStatus -> r.roomStatus.toString),
              )
          }

          r.copy(fences = Set(fencedRate), fencedRateKey = Some(pair.key), origin = pair.key.origin)
      }

      val hotel = h.d.copy(rooms = rooms)
      h.copy(d = hotel)
    }
  }

  private[logic] def setCancelRebookRequestToDFFinance(d: Data[MetaHotels]): Future[MetaHotels] = {
    val metaHotels = d.data
    val result = metaHotels.map { metaHotel =>
      val updatedHotel: Hotel = setCxlRebookToDFFinance(metaHotel.d)(d.ctx)
      metaHotel.copy(d = updatedHotel)
    }
    Future.successful(result)
  }

  private[logic] def convertDFFinanceByCurrency(d: Data[MetaHotels]): Future[(MetaHotels, ActStats)] = {
    val metaHotels = d.data
    val matchLocalCurrency = d.ctx.baseRequest.isRebookingRequestV3MatchLocal

    val result = metaHotels.map { metaHotel =>
      val hotel = metaHotel.d
      val updatedRooms = hotel.rooms.map { room =>
        val localCurrency = room.exchange.map(_.local).getOrElse("")
        val exchangeRateLocal = room.getExchangeRateByReqCurrency(localCurrency)

        val exchangeRateRequestToLocal = getExchangeRate(d.ctx.baseRequest.currency, localCurrency)
        val priceFreezeInReq = d.ctx.baseRequest.priceFreeze
        // Convert priceFreeze from Request to Local
        val priceFreeze = for {
          pf <- priceFreezeInReq
          local <- exchangeRateRequestToLocal
        } yield ConvertCurrencyAndRoundingService.round(pf, exchangeRateRequestToLocal, local.numReqDecimal)

        val localDff = room.dfFinanceByCurrency.local
        val localCurrencyDFFinance = new DFFinance(
          // TODO: find proper step for price freeze
          new CustomerMarketing(localDff.discounts, priceFreeze, localDff.cancelReBookOriginalSellIn),
          localDff.supplyPrice,
          localDff.getAdditionalPricingInfo,
        )
        val reqCurrency = room.exchange.map(_.request).getOrElse("")
        val exchangeRateRoomToReq = room.getExchangeRateByReqCurrency(reqCurrency)
        val originalUsdToRequestExchangeRate =
          d.ctx.baseRequest.reBookingRequest.flatMap(_.originalUsdToRequestExchangeRate)
        val freezeFxUsdToReqExchangeRate: Option[ExchangeRate] =
          getFxFreezingForCxlReBook(d.ctx, room.currency, originalUsdToRequestExchangeRate)

        val cxlRebookOriginalSellInInRequest =
          if (!matchLocalCurrency) {
            // This can be sent from Rebook agent, then we can remove currency conversion.
            for {
              fx <- freezeFxUsdToReqExchangeRate.map(_.toRequest)
              sellIn <- d.ctx.baseRequest.reBookingRequest.flatMap(_.originalSellIn)
            } yield fx.doubleValue() * sellIn
          } else None

        val convertedReqPrice: ConvertedCurrencyPrice = ConvertedCurrencyPrice(
          exchangeRateRoomToReq,
          numberOfRooms = room.numRooms,
          lengthOfStay = room.lengthOfStay,
          dfFinance = localCurrencyDFFinance,
          priceFreeze = priceFreezeInReq, // deprecated, will clean it
          cxlRebookOriginalSellIn = cxlRebookOriginalSellInInRequest,
          freezeFxUsdToReqExchangeRate = freezeFxUsdToReqExchangeRate,
        )(room.dmcData)

        val roundedLocalPrice: ConvertedCurrencyPrice = getLocalRate(room,
                                                                     exchangeRateLocal,
                                                                     localCurrencyDFFinance,
                                                                     convertedReqPrice,
                                                                     d.ctx,
                                                                     freezeFxUsdToReqExchangeRate,
                                                                     cxlRebookOriginalSellInInRequest)

        // originalSellIn is always USD, and need to be very precise. then bypass currency conversion to avoid rounding
        val cxlRebookOriginalSellInInUSD =
          if (!matchLocalCurrency) {
            d.ctx.baseRequest.reBookingRequest.flatMap(_.originalSellIn)
          } else None

        val exchangeRateRoomToUsd = room
          .getExchangeRateByReqCurrency(ExchangeDataService.USD_CODE)
          .orElse(getExchangeRate(room.currency, ExchangeDataService.USD_CODE))
        val convertedUsdPrice = ConvertedCurrencyPrice(
          exchangeRateRoomToUsd,
          numberOfRooms = room.numRooms,
          lengthOfStay = room.lengthOfStay,
          dfFinance = localCurrencyDFFinance,
          cxlRebookOriginalSellIn = cxlRebookOriginalSellInInUSD,
        )(room.dmcData)

        val convertedHotelCurrencyOption =
          metaHotel.meta.getHotelCurrencyCode(isHotelLocalCurrencyEnable(d.ctx)) match {
            case Some(hotelCurrencyCode) if localCurrency != hotelCurrencyCode =>
              val exchangeRateRoomCurrencyToHotelCurrency = room.getExchangeRateByReqCurrency(hotelCurrencyCode)
              val convertedHotelLocalPrice: ConvertedCurrencyPrice = getLocalRate(
                room,
                exchangeRateRoomCurrencyToHotelCurrency,
                localCurrencyDFFinance,
                convertedReqPrice,
                d.ctx,
                freezeFxUsdToReqExchangeRate,
                cxlRebookOriginalSellInInRequest,
              )
              Some(convertedHotelLocalPrice)
            case _ => None
          }

        val newDFFinanceByCurrency = DFFinanceByCurrency(
          local = roundedLocalPrice.reqCurrencyDFFinance(),
          request = Some(convertedReqPrice.reqCurrencyDFFinance()),
          usd = Some(convertedUsdPrice.reqCurrencyDFFinance()),
          hotelCurrency = convertedHotelCurrencyOption.map(_.reqCurrencyDFFinance()),
        )
        room.copy(dfFinanceByCurrency = newDFFinanceByCurrency)
      }
      val updatedHotel = hotel.copy(rooms = updatedRooms)
      metaHotel.copy(d = updatedHotel)
    }
    Future.successful((result, ActStats(result.map(_.d.rooms.size).sum)))
  }

  // Still need FX Freezing to match payment amount & discount amount
  private[logic] def getFxFreezingForCxlReBook(
    ctx: C,
    supplyCurrency: String,
    originalUsdToRequestExchangeRateOpt: Option[Double]): Option[ExchangeRate] =
    if (ctx.baseRequest.isRebookingRequestV3 && ctx.isForcedOrSeenAsB(ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3)) {
      for {
        toUsdExchangeRate <- getExchangeRate(supplyCurrency, ExchangeDataService.USD_CODE)
        originalUsdToRequestExchangeRate <- originalUsdToRequestExchangeRateOpt
      } yield toUsdExchangeRate.copy(toRequest = originalUsdToRequestExchangeRate)
    } else None

  // if else block tend to return the same result, just micro optimize & less complexity
  private[logic] def getLocalRate(room: Room,
                                  exchangeRateLocal: Option[ExchangeRate],
                                  localCurrencyDFFinance: DFFinance,
                                  convertedReqPrice: ConvertedCurrencyPrice,
                                  ctx: C,
                                  freezeFxUsdToReqExchangeRate: Option[ExchangeRate] = None,
                                  cxlRebookOriginalSellIn: Option[Double] = None): ConvertedCurrencyPrice =
    if (room.currency.equalsIgnoreCase(ctx.baseRequest.currency))
      if (ctx.determineVariant(ABTest.SKIP_LOCAL_CURRENCY_CONVERSION) == Variant.B) convertedReqPrice
      else ConvertedCurrencyPrice(
        exchangeRateLocal,
        numberOfRooms = room.numRooms,
        lengthOfStay = room.lengthOfStay,
        dfFinance = localCurrencyDFFinance,
        freezeFxUsdToReqExchangeRate = freezeFxUsdToReqExchangeRate,
        cxlRebookOriginalSellIn = cxlRebookOriginalSellIn,
      )(room.dmcData)
    else ConvertedCurrencyPrice(
      exchangeRateLocal,
      numberOfRooms = room.numRooms,
      lengthOfStay = room.lengthOfStay,
      dfFinance = localCurrencyDFFinance,
    )(room.dmcData)

  private[logic] def appliedVariableTax(r: Data[MetaHotels]): Future[MetaHotels] = {
    val isPriceStateRequest = r.ctx.baseRequest.isPriceStateRequest
    val disableVariableTaxForAgency = r.ctx.determineVariant(ABTest.DISABLE_VARIABLE_TAX_FOR_AGENCY) == Variant.B
    val isEnableMalaysiaTourismTaxForCitiUs =
      r.ctx.determineVariant(ABTest.ENABLE_MALAYSIA_TOURISM_TAX_FOR_CITIUS) == Variant.B
    val isVariableTaxApplied = r.ctx.baseRequest.isVariableTaxApplied(
      isEnableMalaysiaTourismTaxForCitiUs,
    )
    val metaHotels = r.data.map { h =>
      val overrideHotel =
        if (isPriceStateRequest) {
          processPriceStateVariableTax(h.d, disableVariableTaxForAgency, isVariableTaxApplied)
        } else {
          processVariableTax(h.d, disableVariableTaxForAgency, isVariableTaxApplied)
        }
      h.copy(d = overrideHotel)
    }
    Future.successful(metaHotels)
  }

  private[logic] def setHotelStats(d: Data[MetaHotels]): MetaHotels = d withMeta { implicit mx =>
    d.updateSeqUnit { mh: MetaHotel => stats: HotelStats =>
      val statsBeforePricing = getRoomsStatsAndReport(mh.d, Measurements.roomsBeforePricing)
      stats.copy(
        supplierStatsBeforePricing = statsBeforePricing,
        availabilityPerDMC = mh.d.availableSuppliers,
      )
    }(flowLens, withMetaHotelStatsLens)
  }

  private[logic] def reportH3v2Metrics(d: Data[MetaHotels]): MetaHotels = d withMeta { implicit mx =>
    val h3Stats = d.ctx.flowHotelStatsContext.getH3Stats
    d.data.foreach { metaHotel: MetaHotel =>
      val hotel = metaHotel.d
      Option(h3Stats.get(hotel.id)).map { h3StatsForHotel =>
        if (h3StatsForHotel.isH3Applied) {
          val cheapestRoom = hotel.rooms.find(r => r.uid == hotel.cheapestRoomId)
          val sellInPricesOpt = cheapestRoom.map { room =>
            val refSellInUSD = room.usdSellIn(useRefPrices = true)
            val sellInUSD = room.usdSellIn(useRefPrices = false)
            (refSellInUSD, sellInUSD)
          }

          val message = DFH3v2ExperimentMessageComposer.compose(
            context = d.ctx,
            hotelId = hotel.id,
            h3Stat = h3StatsForHotel,
            cheapestRoomRefSellInUSD = sellInPricesOpt.map(_._1),
            cheapestRoomSellInUSD = sellInPricesOpt.map(_._2),
          )
          messageSink.log(Iterable(message))

          val tags = Map(
            Measurements.tagRequestType -> d.ctx.baseRequest.getRequestType,
            Measurements.tagIsH3v2 -> h3StatsForHotel.isUsingH3v2.toString,
          )
          d.ctx.aggregateReporter.aggregate(Measurements.masterRoomsBeforeH3, message.masterRoomsBeforeH3, tags)
          d.ctx.aggregateReporter.aggregate(Measurements.masterRoomsAfterH3, message.masterRoomsAfterH3, tags)
          d.ctx.aggregateReporter.aggregate(Measurements.masterRoomsBeforeSB, message.masterRoomsBeforeSB, tags)
          d.ctx.aggregateReporter.aggregate(Measurements.masterRoomsAfterSB, message.masterRoomsAfterSB, tags)
          d.ctx.aggregateReporter.aggregate(Measurements.cheapestRoomRefSellInUSD,
                                            message.cheapestRoomRefSellInUSD.map(_.round).getOrElse(0),
                                            tags)
          d.ctx.aggregateReporter.aggregate(Measurements.cheapestRoomSellInUSD,
                                            message.cheapestRoomSellInUSD.map(_.round).getOrElse(0),
                                            tags)
        }
      }
    }
    d
  }
}
