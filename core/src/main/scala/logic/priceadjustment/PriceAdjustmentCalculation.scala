package logic.priceadjustment

import com.agoda.papi.ypl.models.GUIDGenerator.UID
import models.pricing.enums.{
  ApplyType,
  ApplyTypes,
  BookingItemType,
  BookingItemTypes,
  BookingRateTypes,
  ChargeOptions,
  CurrencyTypes,
}
import com.agoda.papi.ypl.models.GUIDGeneratorHelper._
import models.starfruit.{ItemBreakdown, PropertySearchRequest, Room, PriceAdjustment => SFPriceAdjustment}
import org.joda.time.DateTime
import models.starfruit.ImplicitHelpers._
import com.agoda.utils.collection.SumImplicits._
import models.pricediff.{PriceAdjustmentCalculationInfo, PriceAdjustmentDifference, PricePerNight}
import com.agoda.papi.ypl.models.GUIDGeneratorHelper
import models.db.Currency
import models.pricing.ConvertedCurrencyPrice

import scala.annotation.tailrec
import models.utils.ItemBreakdownHelper
import models.utils.ItemBreakdownHelper.generateItemKey
import services.exchange.ExchangeDataService
trait PriceAdjustmentCalculation {

  def applyEbeItemPriceAdjustment(room: Room,
                                  los: Int,
                                  req: Map[Currency, ItemBreakdown],
                                  usd: Map[Currency, ItemBreakdown],
                                  local: Map[Currency, ItemBreakdown],
                                  isPriceAdjustmentRequestMigrated: Boolean)(implicit
    propertySearchRequest: PropertySearchRequest)
    : (Map[Currency, ItemBreakdown], Map[Currency, ItemBreakdown], Map[Currency, ItemBreakdown])
}

object PriceAdjustmentCalculation {
  val ZERO_D = 0d
  // Note: This whitelist filter is use to control the scope of the price adjustment feature currently we will support only these filters
  // for further changes pls set up a meeting with DF team.
  val ITEM_TYPE_LIST = List(BookingItemTypes.Room, BookingItemTypes.ExtraBed, BookingItemTypes.Surcharge)
  val RATE_TYPE_LIST = List(BookingRateTypes.SellInclusive, BookingRateTypes.NetInclusive)
  val APPLY_TYPE_LIST = List(ApplyTypes.PRPB, ApplyTypes.PRPN, ApplyTypes.PN, ApplyTypes.PB)
  val CHARGE_OPTION_LIST =
    List(ChargeOptions.Mandatory, ChargeOptions.Optional, ChargeOptions.Excluded, ChargeOptions.Included)

  protected[priceadjustment] def getPriceAdjustmentRequestWithExtraBed(
    priceAdjustmentList: Seq[SFPriceAdjustment],
    currentItemBreakdown: Map[Currency, ItemBreakdown],
    calculationInfo: PriceAdjustmentCalculationInfo): Seq[SFPriceAdjustment] = {

    def createExtraBedAdjustment(request: SFPriceAdjustment, amount: BigDecimal): Seq[SFPriceAdjustment] =
      if (amount != ZERO_D) Seq(
        request.copy(
          requestedPrice = amount.doubleValue(),
          chargeType = BookingItemTypes.ExtraBed.i,
        ),
      )
      else Seq.empty[SFPriceAdjustment]

    val surchargePriceAdjustment: Seq[SFPriceAdjustment] =
      filterPriceAdjustmentRequests(BookingItemTypes.Surcharge, priceAdjustmentList)

    val priceAdjustmentRequestWithExtraBed =
      filterPriceAdjustmentRequests(BookingItemTypes.Room, priceAdjustmentList).flatMap { priceAdjustmentRequest =>
        val requestedPriceApplyType = ApplyTypes.getApplyType(priceAdjustmentRequest.applyType)
        val extraBedBookingReqAmount =
          getExtraBedBookingReqAmount(currentItemBreakdown, requestedPriceApplyType, calculationInfo)
        val requestedPriceWithInputCurrency =
          (priceAdjustmentRequest.requestedPrice * calculationInfo.exRate).roundp(calculationInfo.precision)

        SFPriceAdjustment(
          roomId = priceAdjustmentRequest.roomId,
          requestedPrice = requestedPriceWithInputCurrency - extraBedBookingReqAmount,
          chargeType = BookingItemTypes.Room.i,
          rateType = priceAdjustmentRequest.rateType,
          applyType = priceAdjustmentRequest.applyType,
          chargeOption = priceAdjustmentRequest.chargeOption,
          surchargeId = priceAdjustmentRequest.surchargeId,
          requestCurrency = priceAdjustmentRequest.requestCurrency,
        ) +: createExtraBedAdjustment(priceAdjustmentRequest, extraBedBookingReqAmount)
      }

    priceAdjustmentRequestWithExtraBed ++ surchargePriceAdjustment
  }

  protected[priceadjustment] def filterPriceAdjustmentRequests(
    bookingItemTypes: BookingItemType,
    priceAdjustmentList: Seq[SFPriceAdjustment]): Seq[SFPriceAdjustment] =
    priceAdjustmentList.filter(_.chargeType == bookingItemTypes.i)

  protected[priceadjustment] def getExtraBedBookingReqAmount(
    reqCurrencyItemBreakdown: Map[Currency, ItemBreakdown],
    applyType: ApplyType,
    calculationInfo: PriceAdjustmentCalculationInfo): Double = {
    val totalExtraBedBookingReqAmount: Double = reqCurrencyItemBreakdown.collect {
      case (_, breakdown)
          if breakdown.id == BookingRateTypes.SellInclusive && breakdown.typeId == BookingItemTypes.ExtraBed =>
        breakdown.local.getOrElse(ZERO_D) * breakdown.quantity
    }.sum

    getReqAmountByRoomRate(totalExtraBedBookingReqAmount, applyType, calculationInfo)
  }

  protected[priceadjustment] def getReqAmountByRoomRate(reqAmount: Double,
                                                        applyType: ApplyType,
                                                        calculationInfo: PriceAdjustmentCalculationInfo): Double = {
    val factor = getFactorForRoomRateType(applyType, calculationInfo.noRoom, calculationInfo.los)
    val reqAmountByRoomRate: Double = if (factor > 0) reqAmount / factor else reqAmount
    (reqAmountByRoomRate * calculationInfo.exRate).roundp(calculationInfo.precision)
  }

  protected[priceadjustment] def getFactorForRoomRateType(roomRateType: ApplyType,
                                                          numberOfRoom: Int,
                                                          lengthOfStay: Int): Long = roomRateType match {
    case ApplyTypes.PN => lengthOfStay
    case ApplyTypes.PRPB => numberOfRoom
    case ApplyTypes.PRPN => numberOfRoom * lengthOfStay
    case _ => 1L
  }

  protected[priceadjustment] def findRoomToAdjustPrice(currentRoomUID: UID,
                                                       requests: Seq[SFPriceAdjustment],
                                                       bookingRequestCurrency: String): Seq[SFPriceAdjustment] = {
    def isValidRequest(req: SFPriceAdjustment): Boolean =
      req.requestCurrency.equalsIgnoreCase(bookingRequestCurrency) &&
      ITEM_TYPE_LIST.contains(BookingItemTypes.getBookingItemType(req.chargeType)) &&
      RATE_TYPE_LIST.contains(BookingRateTypes.getRateType(req.rateType)) &&
      APPLY_TYPE_LIST.contains(ApplyTypes.getApplyType(req.applyType.toUpperCase)) &&
      CHARGE_OPTION_LIST.contains(ChargeOptions.getChangeOption(req.chargeOption))

    if (requests.nonEmpty && requests.forall(isValidRequest)) {
      requests.filter(r => compareRoomIdentifiersUID(convertToUID(r.roomId), currentRoomUID))
    } else Seq.empty
  }

  def modifyItemBreakdownWithPriceAdjustmentRequest(
    priceAdjustmentList: Seq[SFPriceAdjustment],
    currentItemBreakdown: Map[Currency, ItemBreakdown],
    calculationInfo: PriceAdjustmentCalculationInfo,
    typeId: BookingItemType = BookingItemTypes.None): Map[Currency, ItemBreakdown] = {

    val precision = calculationInfo.precision
    val priceDifferentForAdjustmentList: Seq[PriceAdjustmentDifference] = priceAdjustmentList.map { adjustmentInfo =>
      calculatePriceDifferenceForAdjust(adjustmentInfo, currentItemBreakdown.values.toSeq, calculationInfo)
    }
    val validPriceDiffList = priceDifferentForAdjustmentList.filter(_.differentPrice != ZERO_D)
    val totalPriceDifferent = validPriceDiffList.getDSum(_.differentPrice).roundp(precision)
    if (validPriceDiffList.nonEmpty && totalPriceDifferent != ZERO_D) {
      // Adjust items (e.g. SellInclusive)
      val modifiedItemBreakdown: Seq[ItemBreakdown] = adjustFromPriceDifferent(validPriceDiffList, calculationInfo)
      val finalModifiedItemBreakdown: Seq[ItemBreakdown] =
        if (typeId == BookingItemTypes.CancelRebook) {
          adjustRoundingError(modifiedItemBreakdown, validPriceDiffList, precision)
        } else modifiedItemBreakdown
      val accRateDiffItem: Map[Currency, ItemBreakdown] =
        createAccRateDifferenceChargeBreakdown(totalPriceDifferent, typeId)
      updateItemBreakdownWithModifiedList(finalModifiedItemBreakdown, currentItemBreakdown) ++ accRateDiffItem
    } else currentItemBreakdown
  }

  private[priceadjustment] def adjustRoundingError(modifiedItemBreakdown: Seq[ItemBreakdown],
                                                   validPriceDiffList: Seq[PriceAdjustmentDifference],
                                                   precision: Int): Seq[ItemBreakdown] = modifiedItemBreakdown
    .groupBy(_.id)
    .flatMap { case (itemId, modifiedItems) =>
      val targetPrice =
        validPriceDiffList.filter(_.adjustRequest.rateType == itemId.i).getDSum(_.requestedPrice).roundp(precision)
      val adjPrice = ItemBreakdownHelper.getTotalAmountByTypes(modifiedItems, precision, bookingRateTypes = Seq(itemId))
      val roundingErr = targetPrice - adjPrice
      if (roundingErr != 0d) {
        val (roomsItem, others) =
          modifiedItemBreakdown.filter(_.id == itemId).partition(_.typeId == BookingItemTypes.Room)
        val (firstRoomItem, othersRoomItem) = roomsItem.sortBy(_.date).splitAt(1)
        val adjustFirstRoomItem = firstRoomItem.map(i => i.update(local = i.local.map(_ + roundingErr)))
        adjustFirstRoomItem ++ othersRoomItem ++ others
      } else modifiedItems
    }
    .toSeq

  private def createPriceDiffAdjustment(uid: String,
                                        totalMandatoryCharge: Double,
                                        chargeType: BookingItemType,
                                        delta: Double,
                                        surchargeId: Option[Int] = None) = SFPriceAdjustment(
    uid,
    requestedPrice = totalMandatoryCharge + delta,
    chargeType = chargeType.i,
    rateType = BookingRateTypes.SellInclusive.i,
    applyType = ApplyTypes.PB.i,
    chargeOption = ChargeOptions.Mandatory.i,
    surchargeId = surchargeId,
    requestCurrency = ExchangeDataService.USD_CODE,
  )

  private[priceadjustment] def getSurchargePriceDiffAdjustment(
    uid: String,
    precision: Int,
    totalMandatorySurcharge: Double,
    // originalSellIn - total Price
    totalDelta: Double,
    // total of Room, Extrabed, Surcharge, Ess
    totalPrice: Double,
    availableSurcharges: Map[Int, Double]): (List[SFPriceAdjustment], Double) = {
    val portionOfEachCharge = totalMandatorySurcharge / totalPrice
    val totalDeltaAmount = (portionOfEachCharge * totalDelta).roundp(precision)
    if (portionOfEachCharge > 0d) {
      availableSurcharges.foldLeft((List.empty[SFPriceAdjustment], 0d)) {
        case ((totalAdjust, totalSurchargeDelta), (eachSurchargeId, eachSuchargeAmount)) =>
          val eachDelta = (eachSuchargeAmount / totalMandatorySurcharge * totalDeltaAmount).roundp(precision)
          (List(
             createPriceDiffAdjustment(uid,
                                       eachSuchargeAmount,
                                       BookingItemTypes.Surcharge,
                                       eachDelta,
                                       surchargeId = Some(eachSurchargeId))) ++ totalAdjust,
           totalSurchargeDelta + eachDelta)
      }
    } else {
      (Nil, 0d)
    }
  }

  private[priceadjustment] def getPriceDiffAdjustmentByChargeType(
    uid: String,
    precision: Int,
    // each Room, Extrabed, Surcharge, Ess
    totalMandatoryCharge: Double,
    // originalSellIn - total Price
    totalDelta: Double,
    // total of Room, Extrabed, Surcharge, Ess
    totalPrice: Double,
    chargeType: BookingItemType): (List[SFPriceAdjustment], Double) = {
    val portionOfEachCharge = totalMandatoryCharge / totalPrice
    val delta = (portionOfEachCharge * totalDelta).roundp(precision)
    if (portionOfEachCharge > 0d) {
      (List(createPriceDiffAdjustment(uid, totalMandatoryCharge, chargeType, delta)), delta)
    } else {
      (Nil, 0d)
    }
  }

  /**
    * Suppressing mutation: reBookingRequest.exists(_.matchUSD) -> reBookingRequest.forall(_.matchUSD)
    * in the targetSellIn calculation within the USD currency condition check.
    * This is an equivalent mutant - both exists and forall produce identical behavior in this context:
    * - When reBookingRequest is None: exists returns false, forall returns true, but
    *   reBookingRequest.flatMap(_.originalSellIn).getOrElse(originalSellIn) always returns originalSellIn
    * - When reBookingRequest is Some: both exists and forall return the same boolean based on matchUSD value
    * The final targetSellIn value is identical regardless of using exists or forall.
    */
  @SuppressWarnings(Array("stryker4s.mutation.MethodExpression"))
  def adjustCxlRebookItems(
    cancelReBookOriginalSellIn: Option[Double],
    precision: Int,
    uid: String,
    mandatoryOriginalRoomItemBreakdown: List[ItemBreakdown],
    mandatorySurchargeItemBreakdown: List[ItemBreakdown],
    mandatoryExtraBedItemBreakdown: List[ItemBreakdown],
    essItemBreakdowns: List[ItemBreakdown],
    convertedCurrencyPrice: ConvertedCurrencyPrice,
    reBookingRequest: Option[api.request.ReBookingRequest],
    numberOfRoom: Int,
    lengthOfStay: Int): Option[Seq[ItemBreakdown]] = cancelReBookOriginalSellIn.map { originalSellIn =>
    // original booking sell inclusive
    val targetSellIn =
      if (convertedCurrencyPrice.reqCurrencyCode.contains(ExchangeDataService.USD_CODE) && reBookingRequest.exists(
          _.matchUSD)) reBookingRequest.flatMap(_.originalSellIn).getOrElse(originalSellIn)
      else originalSellIn
    val totalMandatoryRoom = ItemBreakdownHelper.getTotalAmountByTypes(mandatoryOriginalRoomItemBreakdown, precision)
    val totalMandatoryExtrabed = ItemBreakdownHelper.getTotalAmountByTypes(mandatoryExtraBedItemBreakdown, precision)
    val essTypesMap = essItemBreakdowns.groupBy(_.typeId)
    val essPrices = essTypesMap.map { case (k, essItems) =>
      k -> ItemBreakdownHelper.getTotalAmountByTypes(essItems, precision)
    }

    val allSurcharges: Map[Int, Double] = mandatorySurchargeItemBreakdown.groupBy(item => item.surchargeId).map {
      case (k, v) => k -> ItemBreakdownHelper.getTotalAmountByTypes(v, precision)
    }
    val totalMandatorySurcharge = allSurcharges.values.sum.roundp(precision)
    val totalPrice: Double =
      totalMandatoryRoom + totalMandatorySurcharge + totalMandatoryExtrabed + essPrices.values.sum
    val totalDeltaPrice: Double = targetSellIn - totalPrice

    val (surchargeDiffRequest, surchargeDelta) = getSurchargePriceDiffAdjustment(uid,
                                                                                 precision,
                                                                                 totalMandatorySurcharge,
                                                                                 totalDeltaPrice,
                                                                                 totalPrice,
                                                                                 allSurcharges)
    val (extrabedDiffRequest, extrabedDelta) = getPriceDiffAdjustmentByChargeType(uid,
                                                                                  precision,
                                                                                  totalMandatoryExtrabed,
                                                                                  totalDeltaPrice,
                                                                                  totalPrice,
                                                                                  BookingItemTypes.ExtraBed)
    val (essDiffRequest, essDelta) = essPrices.foldLeft((List.empty[SFPriceAdjustment], 0d)) {
      case ((totalPriceAdjustmentRequest, totalDelta), (eachTypeId, eachPrice)) =>
        val (diffRequest, essDelta) =
          getPriceDiffAdjustmentByChargeType(uid, precision, eachPrice, totalDeltaPrice, totalPrice, eachTypeId)
        (diffRequest ++ totalPriceAdjustmentRequest, essDelta + totalDelta)
    }
    val remainingDiff = totalDeltaPrice - (surchargeDelta + extrabedDelta + essDelta)
    val roomDiffRequest = createPriceDiffAdjustment(uid, totalMandatoryRoom, BookingItemTypes.Room, remainingDiff)
    val rebookMandatoryItems = modifyItemBreakdownWithPriceAdjustmentRequest(
      priceAdjustmentList = Seq(roomDiffRequest) ++ extrabedDiffRequest ++ surchargeDiffRequest ++ essDiffRequest,
      currentItemBreakdown = generateItemKey(
        mandatoryOriginalRoomItemBreakdown ++ mandatoryExtraBedItemBreakdown ++ mandatorySurchargeItemBreakdown ++ essItemBreakdowns),
      calculationInfo = PriceAdjustmentCalculationInfo(
        noRoom = numberOfRoom,
        los = lengthOfStay,
        exRate = 1d, // Do not require currency conversion
        precision = precision,
      ),
      typeId = BookingItemTypes.CancelRebook,
    ).values.toSeq
    rebookMandatoryItems
  }

  private[priceadjustment] def calculatePriceDifferenceForAdjust(
    adjustPrice: SFPriceAdjustment,
    currentBreakdown: Seq[ItemBreakdown],
    calculationInfo: PriceAdjustmentCalculationInfo): PriceAdjustmentDifference = {
    val adjustItemList = filterItemToAdjust(adjustPrice, currentBreakdown)
    val requestedPriceApplyType = ApplyTypes.getApplyType(adjustPrice.applyType)
    val requestedPriceWithInputCurrency =
      (adjustPrice.requestedPrice * calculationInfo.exRate).roundp(calculationInfo.precision)
    val perBookAdjustPriceRequestedCurrency = getPerBookAmountByApplyType(
      amount = requestedPriceWithInputCurrency,
      applyTo = requestedPriceApplyType,
      los = calculationInfo.los,
      roomNum = calculationInfo.noRoom,
      precision = calculationInfo.precision,
    )
    val perBookCurrentBreakdownPrice = getItemBreakDownSummary(
      items = adjustItemList,
      precision = calculationInfo.precision,
      calculationInfo = calculationInfo,
    )

    val diff = perBookAdjustPriceRequestedCurrency - perBookCurrentBreakdownPrice
    PriceAdjustmentDifference(diff,
                              perBookAdjustPriceRequestedCurrency,
                              perBookCurrentBreakdownPrice,
                              adjustPrice,
                              adjustItemList)
  }

  private[priceadjustment] def filterItemToAdjust(priceAdjustment: SFPriceAdjustment,
                                                  currentItemBreakdown: Seq[ItemBreakdown]): Seq[ItemBreakdown] = {
    def isMatch(currentBreakdown: ItemBreakdown): Boolean = currentBreakdown.typeId.i == priceAdjustment.chargeType &&
      currentBreakdown.id.i == priceAdjustment.rateType &&
      currentBreakdown.option.map(_.i).getOrElse(ChargeOptions.Unknown.i) == priceAdjustment.chargeOption &&
      priceAdjustment.surchargeId.forall(sid => sid == currentBreakdown.surchargeId)

    val adjustList = currentItemBreakdown.filter(isMatch)
    adjustList
  }

  private def updateItemBreakdownWithModifiedList(
    modifiedItemBreakdown: Seq[ItemBreakdown],
    currentItemBreakdown: Map[String, ItemBreakdown]): Map[String, ItemBreakdown] = {
    val modifiedItemBreakdownWithKey = ItemBreakdownHelper.generateItemKey(modifiedItemBreakdown)
    currentItemBreakdown.map { case (k, v) =>
      val modifiedItem = modifiedItemBreakdownWithKey.get(k)
      (k, modifiedItem.getOrElse(v))
    }
  }

  private def createAccRateDifferenceChargeBreakdown(
    totalPriceDifferent: Double,
    typeId: BookingItemType = BookingItemTypes.None): Map[String, ItemBreakdown] = {
    val item = ItemBreakdown(
      date = None,
      id = BookingRateTypes.AccRateDifference,
      typeId = typeId,
      taxFeeId = 0,
      surchargeId = 0,
      quantity = 1,
      local = Some(totalPriceDifferent),
      applyTo = ApplyTypes.PB,
      option = None,
    )
    ItemBreakdownHelper.generateItemKey(Seq(item))
  }

  private[priceadjustment] def adjustFromPriceDifferent(
    adjustList: Seq[PriceAdjustmentDifference],
    calculationInfo: PriceAdjustmentCalculationInfo): Seq[ItemBreakdown] = {
    val modifiedItemBreakdownList = adjustList.flatMap { adjustInfo =>
      val difference = adjustInfo.differentPrice
      val listToModify = adjustInfo.requiredAdjustmentList
      val pricePerNight = getPricePerNight(listToModify)
      val modifiedList = modifyItemBreakdown(listToModify, pricePerNight, difference, calculationInfo)
      modifiedList
    }
    modifiedItemBreakdownList
  }

  private def modifyItemBreakdown(itemBreakdown: Seq[ItemBreakdown],
                                  pricePerNightSeq: Seq[PricePerNight],
                                  priceDiffAmount: Double,
                                  calculationInfo: PriceAdjustmentCalculationInfo): Seq[ItemBreakdown] = {
    @tailrec
    def modifyChargeBreakdownRecursive(itemBreakdown: Seq[ItemBreakdown],
                                       pricePerNightSeq: Seq[PricePerNight],
                                       priceDiffAmount: Double,
                                       calculationInfo: PriceAdjustmentCalculationInfo,
                                       itemBreakDownResultList: Seq[ItemBreakdown]): Seq[ItemBreakdown] =
      if (itemBreakdown.isEmpty) {
        Seq.empty
      } else if (itemBreakdown.size == 1) {
        val currentBreakdown = itemBreakdown.head
        itemBreakDownResultList ++ Seq(
          getChargeBreakdownWithPriceDifferent(
            calculationInfo,
            currentBreakdown,
            divideQuantity(priceDiffAmount, currentBreakdown.quantity).roundp(calculationInfo.precision),
          ))
      } else {
        val currentBreakdown = itemBreakdown.head
        val getPriceAmountThisNight = pricePerNightSeq
          .find(price => price.date == currentBreakdown.date && price.roomNo == currentBreakdown.roomNo)
          .map(_.amount)
          .getOrElse(ZERO_D)
        val totalPrice = pricePerNightSeq.getDSum(_.amount)
        val ratio =
          if (totalPrice > ZERO_D) {
            val totalPricePerNight = pricePerNightSeq.getDSum(_.amount)
            getPriceAmountThisNight / totalPricePerNight
          } else {
            ZERO_D
          }
        val priceDifferentPerItem =
          divideQuantity(ratio * priceDiffAmount, currentBreakdown.quantity).roundp(calculationInfo.precision)

        val chargeBreakdownResult =
          getChargeBreakdownWithPriceDifferent(calculationInfo, currentBreakdown, priceDifferentPerItem)
        val priceDiffRemaining = priceDiffAmount - (priceDifferentPerItem * currentBreakdown.quantity)

        val pricePerNightRemaining = pricePerNightSeq.filterNot(price =>
          price.date == currentBreakdown.date && price.roomNo == currentBreakdown.roomNo)
        modifyChargeBreakdownRecursive(itemBreakdown.tail,
                                       pricePerNightRemaining,
                                       priceDiffRemaining,
                                       calculationInfo,
                                       itemBreakDownResultList ++ Seq(chargeBreakdownResult))
      }

    def divideQuantity(amount: Double, quantity: Int) =
      (if (quantity == 0) {
         ZERO_D
       } else {
         amount / quantity
       })

    modifyChargeBreakdownRecursive(itemBreakdown, pricePerNightSeq, priceDiffAmount, calculationInfo, Seq.empty)
  }

  private def getChargeBreakdownWithPriceDifferent(calculationInfo: PriceAdjustmentCalculationInfo,
                                                   chargeBreakdown: ItemBreakdown,
                                                   priceDifferentRequestAmountLocalRounding: Double): ItemBreakdown = {
    val local = chargeBreakdown.local.getOrElse(ZERO_D)
    val localAmount = (priceDifferentRequestAmountLocalRounding + local).roundp(calculationInfo.precision)

    chargeBreakdown.update(
      local = Some(localAmount),
    )
  }

  private def getItemBreakDownSummary(items: Seq[ItemBreakdown],
                                      precision: Int,
                                      calculationInfo: PriceAdjustmentCalculationInfo): Double = items
    .groupBy(_.date)
    .getDSum { case (_, breakdown) =>
      {
        val amountPerDay = breakdown.getDSum { item =>
          val taxOrFeeQuantity = item.taxOrFeeQuantity.getOrElse(1)
          item.local.getOrElse(ZERO_D) * item.quantity * taxOrFeeQuantity
        }
        Math.max(ZERO_D, amountPerDay)
      }.roundp(precision)
    }
    .roundp(precision)

  private def getPerBookAmountByApplyType(amount: Double, applyTo: ApplyType, los: Int, roomNum: Int, precision: Int) =
    applyTo match {
      case ApplyTypes.PRPB => (amount * roomNum).roundp(precision)
      case ApplyTypes.PRPN => (amount * (roomNum * los)).roundp(precision)
      case ApplyTypes.PN => (amount * los).roundp(precision)
      case ApplyTypes.PB | _ => amount
    }

  private def getPricePerNight(chargeBreakdownList: Seq[ItemBreakdown]): Seq[PricePerNight] = {

    val pricePerNightGroupByDate: Map[(Option[DateTime], Int), Seq[ItemBreakdown]] =
      chargeBreakdownList.groupBy { chargeBreakdown =>
        (chargeBreakdown.date, chargeBreakdown.roomNo)
      }

    pricePerNightGroupByDate.flatMap {
      case ((date, roomNo), chargeBreakdownSeq) =>
        val sumPricePerNight = chargeBreakdownSeq.getDSum { charge =>
          charge.local.getOrElse(ZERO_D) * charge.quantity
        }
        Some(PricePerNight(date, roomNo, sumPricePerNight))
      case _ => None
    }.toSeq
  }
}

trait PriceAdjustmentCalculationImpl extends PriceAdjustmentCalculation {

  import PriceAdjustmentCalculation._

  override def applyEbeItemPriceAdjustment(room: Room,
                                           los: Int,
                                           req: Map[Currency, ItemBreakdown],
                                           usd: Map[Currency, ItemBreakdown],
                                           local: Map[Currency, ItemBreakdown],
                                           isPriceAdjustmentRequestMigrated: Boolean)(implicit
    propertySearchRequest: PropertySearchRequest)
    : (Map[Currency, ItemBreakdown], Map[Currency, ItemBreakdown], Map[Currency, ItemBreakdown]) = {
    val bookingRoomUID =
      UID(room.uid, room.roomIdentifier.flatMap(id => GUIDGeneratorHelper.convertToUID(id).roomIdentifier))
    val requestCurrency = propertySearchRequest.pricing.currency
    val priceAdjustmentRequestForThisRoom = findRoomToAdjustPrice(
      bookingRoomUID,
      propertySearchRequest.pricing.priceAdjustmentRequest.getOrElse(List.empty),
      requestCurrency)
    val roomExchange = room.bookingInfo.flatMap(_.roomExchange)
    val numberOfRoom = room.bookingInfo.map(_.numberOfRoom).getOrElse(0)

    val priceAdjustmentListWithExtraBed =
      (isPriceAdjustmentRequestMigrated, priceAdjustmentRequestForThisRoom.nonEmpty, roomExchange) match {
        case (true, true, Some(roomExchangeRate)) =>
          val (exRate, precision) = roomExchangeRate.getExchangeAndNumDecimal(CurrencyTypes.Requested)
          getPriceAdjustmentRequestWithExtraBed(
            priceAdjustmentList = priceAdjustmentRequestForThisRoom,
            currentItemBreakdown = req,
            calculationInfo = PriceAdjustmentCalculationInfo(
              noRoom = numberOfRoom,
              los = los,
              exRate = exRate,
              precision = precision,
            ),
          )
        case _ => priceAdjustmentRequestForThisRoom
      }

    (roomExchange, priceAdjustmentListWithExtraBed.nonEmpty) match {
      case (Some(roomExchangeRate), true) =>
        val adjustedBookingReqPricing = {
          val (exRate, precision) = roomExchangeRate.getExchangeAndNumDecimal(CurrencyTypes.Requested)
          modifyItemBreakdownWithPriceAdjustmentRequest(
            priceAdjustmentList = priceAdjustmentListWithExtraBed,
            currentItemBreakdown = req,
            calculationInfo = PriceAdjustmentCalculationInfo(
              noRoom = numberOfRoom,
              los = los,
              exRate = exRate,
              precision = precision,
            ),
          )
        }
        val adjustedBookingUsdPricing = {
          val (exRate, precision) = roomExchangeRate.getExchangeAndNumDecimal(CurrencyTypes.USD)
          modifyItemBreakdownWithPriceAdjustmentRequest(
            priceAdjustmentList = priceAdjustmentListWithExtraBed,
            currentItemBreakdown = usd,
            calculationInfo = PriceAdjustmentCalculationInfo(
              noRoom = numberOfRoom,
              los = los,
              exRate = exRate,
              precision = precision,
            ),
          )
        }
        val adjustedBookingLocalPricing = {
          val (exRate, precision) = roomExchangeRate.getExchangeAndNumDecimal(CurrencyTypes.Local)
          modifyItemBreakdownWithPriceAdjustmentRequest(
            priceAdjustmentList = priceAdjustmentListWithExtraBed,
            currentItemBreakdown = local,
            calculationInfo = PriceAdjustmentCalculationInfo(
              noRoom = numberOfRoom,
              los = los,
              exRate = exRate,
              precision = precision,
            ),
          )
        }
        (adjustedBookingReqPricing, adjustedBookingUsdPricing, adjustedBookingLocalPricing)
      case _ => (req, usd, local)
    }
  }

}
