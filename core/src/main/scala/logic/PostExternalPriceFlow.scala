package logic

import api.request.{BaseRequest, BookingFilter, FeatureFlag}
import api.routing.dsl._
import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.enums.room.{CancellationGroup, RatePlanStatus}
import com.agoda.papi.pricing.EnumConverter
import com.agoda.papi.pricing.calculation.CalculationStatsLogger
import com.agoda.papi.pricing.configuration.BookingFormOfferSwapSettingsProducer
import com.agoda.papi.pricing.core.services.MSEService
import com.agoda.papi.ypl.models.consts.Benefits
import com.agoda.papi.ypl.models.{YplMasterChannel => DFMasterChannel}
import com.agoda.papi.ypl.pricing.CancellationPolicyService
import com.agoda.papi.ypl.services.YplBundleRoomHelperService
import com.agoda.pricestream.models.enumeration.PastBookingInformationType
import com.agoda.utils.flow.{<PERSON><PERSON><PERSON>x<PERSON>, PropertyContext}
import com.agoda.utils.lens.Implicits._
import com.agoda.utils.monitoring.{ReportingService, WithAggregateReporter}
import com.typesafe.scalalogging.LazyLogging
import helper.RegulatoryHelper.isHotelLocalCurrencyEnable
import logging.simulation.message.sender.{
  MessageSender,
  SimulateRateChannelPostSoybeanOfferMessageSender,
  SupplyEquitySimulationPostSoybeanOfferMessageSender,
}
import logic.ModelLens._
import logic.RareRoomTypesInfo._
import logic.WithMeta.{MetaHotel, MetaHotels}
import logic.filters.{PaymentModelFilter, RoomIdentifierFilterServiceImpl, SymmetricUidFilterOutServiceImpl}
import logic.postbooking.PostBookingFilterCalculation
import logic.roomselecting.{
  BookingAlternativeRoomsService,
  BookingFilterCalculation,
  LowestNetRateRoomSwapService,
  RoomLimittingStrategy,
}
import models.consts.{ABTest, DMC}
import models.db._
import models.enums.DispatchAvailabilityTypes
import models.filters.BedroomCount
import models.flow.{FlowContext, Variant}
import models.pricing._
import models.pricing.enums._
import models.starfruit.DisplayPrice
import models.{PastBookingInfoSummary, PastBookingPrices, ReqOccByHotelAgePolicy}
import services._
import services.agodarefund.AgodaRefundService
import services.cart.CartEligibleService
import services.exchange.{ExchangeDataService, ExchangeRateContext}
import services.packaging.PackagingEligibleService
import services.priceselection.PriceSelectionDataService
import services.rateChannelSwap.RateChannelSwapService
import services.roombundles.{FlexibleMultiRoomService, SingleRoomTypeSuggestionService}
import settings.SsrRetrySettings
import system.FlowMeasureSupport.ActStats
import system.FlowMeasureSupportHelper.futureWithStats
import system.FlowSupport
import utilhelpers.Measurements

import scala.concurrent.Future

/**
  * This flow is used for postfiltering hotels/suppliers/rooms after external pricing flow
  * Here we are removing temporary rooms, limiting number of the rooms, preparing hotel for [[DataPreparationFlow]]
  *
  * Created by ekalashnikov on 11/29/2016 AD.
  *
  * This flow will be used in both hotel / city search
  */
trait PostExternalPriceFlow[T <: FlowContext]
  extends ExpirableRoutingDSL[T]
    with FlowSupport[T]
    with WithAggregateReporter
    with ExperimentSupport
    with RoomLimittingStrategy
    with HotelApoFiltering
    with NPCLNRooms
    with CancellationPolicyService
    with FlexibleMultiRoomService
    with SingleRoomTypeSuggestionService
    with YplBundleRoomHelperService
    with CrossOutRateService
    with BookingFilterCalculation
    with PostBookingFilterCalculation
    with RoomIdentifierFilterServiceImpl
    with BookingAlternativeRoomsService
    with MSEService
    with LazyLogging
    with ExchangeRateContext
    with PackagingEligibleService
    with PriceSelectionDataService
    with UpsellingRelationService
    with AgodaRefundService
    with CalculationStatsLogger
    with ReportingService
    with ExchangeDataService
    with SymmetricUidFilterOutServiceImpl
    with CartEligibleService
    with PaymentModelFilter
    with RateChannelSwapService
    with LowestNetRateRoomSwapService {

  val ssrRetrySettings = SsrRetrySettings()
  val bookingFormOfferSwapSettingsProducer: BookingFormOfferSwapSettingsProducer
  private[logic] val simulationRateChannelPostSoybeanOfferMessageSender
    : SimulateRateChannelPostSoybeanOfferMessageSender =
    SimulateRateChannelPostSoybeanOfferMessageSender(MessageSender.defaultHadoopSender)

  private[logic] val supplyEquitySimulationPostSoybeanOfferMessageSender
    : SupplyEquitySimulationPostSoybeanOfferMessageSender =
    SupplyEquitySimulationPostSoybeanOfferMessageSender(MessageSender.defaultHadoopSender)

  lazy val postExternalPriceFlow: Flow[MetaHotels, MetaHotels] =
    // set requestedPrice in response model before further processing which may relay on price type
    (Sync.simple("postSoybeanLogsForSimulation", postSoybeanOfferLogsForSimulation) |>
      Sync.simple("setRequestedPrice", setRequestedPrice) |>
      Sync.simple("filterAPOHotel", filterApo(checkRequestAPOFlag = true)) |>
      Sync.simple("updateH3v2Metrics", updateH3v2Metrics) |>
      //  ToDO: separate availability report and roomsQtyAfterPricing
      Sync.simple("filterBySoybeanStreamlinedAndValidRoomStatus", filterRoomsAfterPricingAndSetStats) |>
      Sync.simple("LowestNetRateRoomSwapForBooking", lowestNetRateRoomSwapForBooking) |>
      Sync.simple("setAlternateFunnelRatesForPackages", setAlternateFunnelRatesForPackages) |>
      Sync.simple("removeAlternateFunnelRooms", removeAlternateFunnelRooms) |>
      Sync.simple("selectAdditionalRateInfo", selectAdditionalRateInfo) |>
      Sync.simple("filterRoomsWithRequiredNumberOfBedrooms", filterRoomsWithRequiredNumberOfBedrooms) |>
      Sync.simple("doNPCLN", doNPCLN) |>
      Sync.simple("calculateUpSellingRelation", calculateUpSellingRelation) |>
      Sync.simple("calculateAgodaRefund", calculateAgodaRefund) |>
      Sync.simple("findAlternativeRoom", findAlternativeRooms) |>
      Sync.simple("addRateChannelSwapAlternative", addRateChannelSwapAlternative) |>
      Sync.simple("setMaxOccupancy", setMaxOccupancyExternal) |>
      Sync.simple("filterOutRoomsBySymmetricUid", filterRoomsBySymmetricUid) |>
      Sync.simple("reportDuplicateRooms", reportDuplicateRooms) |>
      // Start post-price filters with specific room select
      Sync.simple("filterRoomsForBooking",
                  filterRoomsForBooking) |> // Please ensure df price is not changed after this flow
      Sync.simple("filterUnsupportedPaymentModel",
                  filterUnSupportedPaymentModel) |> // Please ensure df price is not changed after this flow
      Sync.simple("limitRoom", limitNumberOfRooms) |>
      Sync.simple("finalizeCrossOutRate", finalizeCrossOutRate) |>
      Sync.simple("updateExchangeRateListInRooms", updateExchangeRateListInRooms) |>
      Sync.simple("suggestRoom", createRoomSuggestions) |>
      Sync.simple("filterFitRoomsForPackaging", filterFitRoomsForPackaging) |>
      Sync.simple("marksPackageEligibleRoomsPricing", marksPackageEligibleRoomsForPricing) |>
      Sync.simple("marksMultiHotelEligibleRoomsPricing", marksMultiHotelEligibleRoomsForPricing) |>
      Sync.simple("marksCartEligibleRoomsPricing", marksCartEligibleRoomsForPricing) |>
      Sync.simple("sortAndGroupMerchantRoomBeforeMorpRoom", sortAndGroupMerchantRoomBeforeMorpRoom) |>
      Sync.simple("duplicateRoomForPriceline",
                  duplicateRoomForPriceline) |> //  TODO: find out where we can move this (above of all filters)
      Sync.simple("filterRoomsByRoomIdentifier",
                  filterRoomsByRoomIdentifier) |> // Start post-price filters with specific room select
      Sync.simple("synchronizeHotelWithRooms", synchronizeHotelWithRooms) |>
      Sync.simple("clearIsReadyForSuppliersToPoll", clearIsReadyForSuppliersToPoll) |>
      Sync.simple("cartRestricted", marksCartRestrictedRooms) |>
      Sync.simple("markAllowMultipleBookingRoom", markAllowMultipleBookingRoom) |>
      Sync.simple("findRareRoomFeature", findRareRoomFeature)).toAct("postExternalPriceFlow")

  @SuppressWarnings(Array("stryker4s.mutation.MethodExpression", "stryker4s.mutation.ConditionalExpression"))
  private[logic] def postSoybeanOfferLogsForSimulation(d: Data[MetaHotels]): MetaHotels = {
    try d.data.foreach { hotel =>
        simulationRateChannelPostSoybeanOfferMessageSender.validateAndSendMessages(d.ctx.baseRequest, hotel.d)
        supplyEquitySimulationPostSoybeanOfferMessageSender.validateAndSendMessages(d.ctx.baseRequest, hotel.d)
      }
    catch {
      case e: Exception => logger.warn(
          "Error while sending simulation logs [SimulationRateChannelPostSoybeanOfferMessage, SupplyEquitySimulationPostSoybeanOfferMessage]",
          e)
    }
    d.data
  }

  private[logic] def setMaxOccupancyExternal(d: Data[MetaHotels]): MetaHotels =
    d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
      val masterRoomMaxOccupancyMap: Map[RoomTypeId, BenefitGroupId] =
        hotel.rooms.groupBy(_.getMasterRoomIdOrElseRoomTypeId).collect { case (k, v) =>
          (k, v.maxBy(_.occupancy).occupancy)
        }
      hotel.copy(rooms =
        hotel.rooms.map(m => m.copy(maxOccupancy = masterRoomMaxOccupancyMap.get(m.getMasterRoomIdOrElseRoomTypeId))))
    }(flowLens, withMetaHotelLens)

  private[logic] def setRequestedPrice(d: Data[MetaHotels]): MetaHotels =
    d.updateSeqUnit { m: MetaHotel => hotel: Hotel =>
      val countryId = m.meta.countryId
      val platformId = d.ctx.baseRequest.platformId
      val languageId = d.ctx.baseRequest.cInfo.language
      val origin = d.ctx.baseRequest.cInfo.origin.getOrElse("")
      val inRequestedPrice = d.ctx.baseRequest.requestedPrice
      val regulationSetting = d.ctx.baseRequest.regulationFeatureEnabledSetting
      val (addFeesInExclusivePrice, priceViewDestinationOverrideConfigs) =
        if (d.ctx.determineVariant(ABTest.SHOW_EXCLUSIVE_WITH_FEE_TO_US_DESTINATION) == Variant.B) {
          (m.d.addFeesInExclusivePrice, Nil)
        } else {
          (false, d.ctx.baseRequest.whitelabelSetting.priceViewDestinationOverrideConfigs)
        }

      val hotelOriginStateId = m.meta.stateId
      val outSuggestedPrice = getSelectedPrice(
        countryId,
        languageId,
        origin,
        inRequestedPrice,
        regulationSetting,
        platformId,
        hotelOriginStateId,
        priceViewDestinationOverrideConfigs,
        addFeesInExclusivePrice,
      )(d.ctx)
      hotel.copy(requestedPrice = outSuggestedPrice.suggestPrice, suggestPriceType = outSuggestedPrice)
    }(flowLens, withMetaHotelLens)

  /* We need  to filter APO before removing helper rooms */
  private def filterApo(checkRequestAPOFlag: Boolean)(r: Data[MetaHotels]): MetaHotels =
    if (!checkRequestAPOFlag || r.ctx.baseRequest.flagInfo.filterAPO) {
      val isFixForAmountCheck = r.ctx.determineVariant(ABTest.FIX_AMOUNT_CHECK_SECRET_DEAL) == Variant.B
      val isSSRRequest = r.ctx.baseRequest.isSSR.getOrElse(false)
      val isFilterSecretDealOnRatePlanStatus =
        r.ctx.baseRequest.featureRequest.enableSecretDealImprovement.getOrElse(false)
      val apoHotels = filterAPOHotels(
        r.data.map(_.d),
        isFixForAmountCheck,
        isFilterSecretDealOnRatePlanStatus,
        isSSRRequest,
        r.ctx.baseRequest.currency,
      ).map(h => h.id -> h).toMap
      val result = r.data.collect { case h if apoHotels.contains(h.id) => h.copy(d = apoHotels(h.id)) }
      result
    } else r.data.map(metaHotel => metaHotel.copy(d = filterNonJPOHotel(metaHotel.d)))

  private[logic] def createFlexibleMultiRoom(d: Data[MetaHotels]): Future[(MetaHotels, ActStats)] = Future
    .successful {
      d.updateSeqUnit { m: MetaHotel => hotel: Hotel =>
        val masterChannels = m.supplierDispatchChannelHolder.hotelDispatchInfo
          .map(_.dispatchInfos.map(_.dispatchedMasterChannel).toSet)
          .getOrElse(Set.empty)
        val isEligible = isFlexibleMultiRoomEligibleToBuild(m.meta.isSingleRoomNHA, hotel, masterChannels)(d.ctx)
        val bundles =
          if (isEligible) {
            val eligibleRoomForBundleCalculation: List[Room] =
              hotel.rooms.view.filterNot(_.numRooms > 1).filterNot(_.isDayUse).toList
            calculateCheapestRoomBundles(
              eligibleRoomForBundleCalculation,
              hotel.requestedPrice,
              hotel.reqOcc,
              hotel.id,
            )(d.ctx, PropertyContext(hotelId = hotel.id, cityId = hotel.cityId, countryId = hotel.countryId))
          } else {
            List.empty
          }
        aggregateReporter.count(
          Measurements.numHotelFlexibleMultiRoom,
          1,
          Map(
            Measurements.tagEligible -> isEligible.toString,
            Measurements.tagAvail -> bundles.nonEmpty.toString,
            Measurements.tagType -> bundles.headOption.map(_.segments.size).getOrElse(0).toString,
            Measurements.tagRequestType -> d.ctx.baseRequest.getRequestType,
          ),
        )

        hotel.copy(
          /**
            * Normally, for requests with cheapestRoomOnly, we allow only ExactAndBigger (fitOrBigger) room matches since YPL.
            * But, as we have the FlexibleMultiRoom feature, we want to defer the non-fit filtering to here,
            * because we need all non-fit rooms to make suggestions and calculate flexible multi-room bundles.
            */
          rooms = hotel.rooms.filterNot(_.roomFeatures.isKeptForFlexibleMultiRoom),
          roomBundles = hotel.roomBundles ++ bundles,
        )
      }(flowLens, withMetaHotelLens)
    }
    .withRoomWeightStats(d.data)
    .map(t => t._1.data -> t._2)

  private[logic] def createRoomSuggestions(d: Data[MetaHotels]): MetaHotels = {
    val baseRequest = d.ctx.baseRequest
    implicit val ctx: C = d.ctx

    /* SSR only needs the cheapest room that can fit all the guests. We filter here,
     * because we need all the smaller rooms to make a suggestion */
    def limitRoomsIfSSR(rooms: List[Room], reqOcc: ReqOccByHotelAgePolicy): List[Room] =
      if (baseRequest.isCheapestRoomOnly) {
        rooms.filter(r => r.occ.guests >= reqOcc.guestsPerRoom())
      } else {
        rooms
      }

    d.updateSeqUnit { m: MetaHotel => hotel: Hotel =>
      val reqOcc = hotel.reqOcc
      val rooms = hotel.rooms
      val requestedPrice = hotel.requestedPrice
      implicit val propertyContext: PropertyContext =
        PropertyContext(hotelId = hotel.id, cityId = hotel.cityId, countryId = hotel.countryId)
      if (YplBundleRoomHelperService.shouldSuggestRooms(
          adults = baseRequest.occ.adults,
          rooms = baseRequest.occ.rooms,
          maxSuggestions = baseRequest.featureRequest.maxSuggestions,
          filterAPO = baseRequest.flagInfo.filterAPO,
          isSingleRoomNHA = m.meta.isSingleRoomNHA,
        )) {
        val suggestions =
          suggestSameRoomTypeRooms(rooms, requestedPrice, reqOcc.agePolicy, reqOcc.ignoreRequestedNumberOfRoomsForNha)
        hotel.copy(rooms = limitRoomsIfSSR(rooms, reqOcc) ++ suggestions)
      } else hotel
    }(flowLens, withMetaHotelLens)
  }

  /**
    * As packaging not allow any selection on number of rooms we return only fit room and multiroom suggestions.
    * As as result this filter get executed after multiroom suggestion logic.
    */
  private[logic] def filterFitRoomsForPackaging(d: Data[MetaHotels]): MetaHotels = {
    val request = d.ctx.baseRequest
    if (request.isPackagingFunnel ||
      (request.isHotelPlusHotelRequest && !d.ctx.experimentContext.experiment(ABTest.REMOVE_MULTI_HOTEL_LOGIC).isUserB)) {
      d.updateSeqUnit { m: MetaHotel => hotel: Hotel =>
        val filteredFitRooms = hotel.rooms.filter(isPackageFunnelFitRoom)
        hotel.copy(rooms = filteredFitRooms)
      }(flowLens, withMetaHotelLens)
    } else {
      d
    }
  }

  private[logic] def updateH3v2Metrics(d: Data[MetaHotels]): MetaHotels = {
    val h3Stats = d.ctx.flowHotelStatsContext.getH3Stats
    d.data.foreach { metaHotel: MetaHotel =>
      val hotel = metaHotel.d
      Option(h3Stats.get(hotel.id)).map { h3StatsForHotel =>
        // Rooms were dropped by H3
        if (h3StatsForHotel.isH3Applied) {
          val masterRoomStats = h3StatsForHotel.getMasterRoomStats
          val masterRoomsBeforeSB = hotel.rooms.iterator.map(_.getMasterRoomIdOrElseRoomTypeId).toSet
          val masterRoomsAfterSB =
            hotel.rooms.iterator.filter(_.shouldBeDisplayed).map(_.getMasterRoomIdOrElseRoomTypeId).toSet
          val updatedH3Stats = h3StatsForHotel.copy(masterRoomStats = Some(
            masterRoomStats.copy(masterRoomsBeforeSB = masterRoomsBeforeSB.size,
                                 masterRoomsAfterSB = masterRoomsAfterSB.size)))
          d.ctx.flowHotelStatsContext.updateH3Stats(hotel.id, updatedH3Stats)
        }
      }
    }
    d
  }

  /**
    * 1. Filter all non-returnable room to client.
    * 2. Set rooms stats
    */
  private[logic] def filterRoomsAfterPricingAndSetStats(d: Data[MetaHotels]): MetaHotels =
    d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
      val returnedRooms = hotel.rooms.filter(r => r.shouldBeDisplayed)
      val hotelWithFilteredRooms = hotel.copy(rooms = returnedRooms)
      val statsAfterPricing = getRoomsStatsAndReport(hotelWithFilteredRooms, Measurements.roomsAfterPricing)
      hotelWithFilteredRooms.copy(stats =
        hotelWithFilteredRooms.stats.copy(supplierStatsAfterPricing = statsAfterPricing))
    }(flowLens, withMetaHotelLens)

  /**
    * Find Hotel Funnel Room for Package Funnel Room and Set its price as Additional Rate
    */
  private[logic] def setAlternateFunnelRatesForPackages(d: Data[MetaHotels]): MetaHotels =
    d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
      AdditionalRateService.mapAdditionalRate(hotel)(d.ctx)
    }(flowLens, withMetaHotelLens)

  // No swapping for Now, only report match rate for swap
  private[logic] def lowestNetRateRoomSwapForBooking(d: Data[MetaHotels]): MetaHotels = {
    val request: BaseRequest = d.ctx.baseRequest
    if (request.isBookingRequest) {
      d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
        logBookingRoomForSwap(request, hotel)
        hotel
      }(flowLens, withMetaHotelLens)
    } else d
  }

  private[logic] def removeAlternateFunnelRooms(d: Data[MetaHotels]): MetaHotels =
    if (!d.ctx.baseRequest.isBookingRequest && !d.ctx.baseRequest.isRoomIdentifierFilterRequest) {
      d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
        hotel.copy(rooms =
          hotel.rooms.filterNot(_.dispatchAvailabilityType.contains(DispatchAvailabilityTypes.Alternative)))
      }(flowLens, withMetaHotelLens)
    } else d

  private[logic] def selectAdditionalRateInfo(d: Data[MetaHotels]): MetaHotels =
    d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
      hotel.copy(rooms = hotel.rooms.map { r =>
        val (skipRemove, nonSkipRemove) =
          r.additionalRateInfo.partition(_.ratePlanStatus.contains(RatePlanStatus.SkipRemoved))
        val oneAdditionalRateInfo = nonSkipRemove.headOption.orElse(skipRemove.headOption)
        val newDFFinance =
          r.dfFinanceByCurrency.local.updateAdditionalPricingInfo(selectedAdditionalRateInfo = oneAdditionalRateInfo)
        r.copy(dfFinanceByCurrency = r.dfFinanceByCurrency.copy(local = newDFFinance))

      })
    }(flowLens, withMetaHotelLens)

  private[logic] def isSelectedRoomFound(bookingFilter: Option[BookingFilter], rooms: List[Room])(implicit
    roomExperiments: RoomExperiments): Boolean =
    bookingFilter.exists(filter => filter.uidList.forall(u => rooms.exists(r => r.matchByUID(u.uid))))

  private[logic] def getAltRoomCountTag(altRoom: Int): String = altRoom match {
    case 0 => Measurements.tagAlternativeRoomFiltered
    case 1 => Measurements.tagAlternativeRoomFound
    case _ => Measurements.tagAlternativeRoomMoreThanOne
  }

  private[logic] def getRoomIdFromBookingFilter(metaHotel: MetaHotel, bookingFilter: BookingFilter) = {
    val bookingUid = bookingFilter.uidList.headOption
    val bookingRoomId = bookingUid.flatMap(_.roomIdentifier).map(_.coreFields.roomId)
    val masterRoomIdOpt = bookingRoomId.flatMap(metaHotel.meta.enabledRoom.get).map(_.masterRoomId)
    val resultRoomIdOpt = masterRoomIdOpt match {
      case Some(0) => bookingRoomId // YCS room, use roomId from bookingUid as master room ID
      case masterRoomId => masterRoomId
    }
    (bookingUid, resultRoomIdOpt)
  }

  /**
    * should be done prior room filtering, swap-rooms should be present in response
    */
  private[logic] def findAlternativeRooms(d: Data[MetaHotels]): MetaHotels = {
    val isEnableUpsellFeatures =
      d.ctx.baseRequest.regulationFeatureEnabledSetting.isEmpty || d.ctx.baseRequest.regulationFeatureEnabledSetting
        .forall(_.isShowUpsellFeatureSetting)
    if (!isEnableUpsellFeatures) {
      d
    } else {
      d.updateSeqUnit { metaHotel: MetaHotel => hotel: Hotel =>
        {
          implicit val roomExperiments: RoomExperiments = RoomExperiments(hotel.features.isApplyNewOccupancyLogic)
          implicit val expCtx: ExperimentContext = d.ctx.experimentContext

          lazy val isBreakfastUpSellBookingFormEnable =
            d.ctx.baseRequest.regulationFeatureEnabledSetting.isEmpty || d.ctx.baseRequest.regulationFeatureEnabledSetting
              .exists(_.isBreakfastUpsellEnabled)
          lazy val isBreakfastUpSell = isBreakfastUpSellBookingFormEnable && d.ctx.baseRequest.featureFlags.contains(
            FeatureFlag.BreakfastUpsell) && !d.ctx.baseRequest.isPriceFreezeRequest // Block upsell for Price Freeze
          lazy val isOriginalRoomFound: Boolean = isSelectedRoomFound(d.ctx.baseRequest.bookingFilter, hotel.rooms)
          lazy val isAlternativeRoom: Boolean =
            d.ctx.baseRequest.featureFlags.contains(FeatureFlag.AlternativeRoom) && !isOriginalRoomFound
          lazy val isForceAlternativeRoom: Boolean =
            d.ctx.baseRequest.featureFlags.contains(FeatureFlag.ForceAlternativeRoom)
          lazy val isAgodaRefund = AgodaRefundService.isAgodaRefundEnabled(d.ctx.baseRequest)
          lazy val isHourlyBooking = d.ctx.baseRequest.isHourlyBookingRequest
          lazy val isHashDmcUidToRoomIdentifier =
            d.ctx.experimentContext.experiment(ABTest.HASH_DMCUID_TO_ROOMIDENTIFIER).isUserB
          lazy val isAlternativeWithRefundablePriceFreeze =
            d.ctx.experimentContext.experiment(ABTest.ENABLE_REFUNDABLE_ALTERNATIVE_PRICE_FREEZE).isUserB
          lazy val isSupplierWhiteListFilterDisabled =
            d.ctx.experimentContext.experiment(ABTest.DISABLE_SUPPLIER_WHITE_LIST_FILTER_PRICE_FREEZE).isUserB
          lazy val isSuperaggLowerNetSwap =
            d.ctx.experimentContext.experiment(ABTest.SUPERAGG_LOWERNET_ROOM_SWAP).isUserB

          val hotelWithAlternatives = (d.ctx.baseRequest.alternativeRoomType, d.ctx.baseRequest.bookingFilter) match {

            // Alternative room Swapping if original room not found
            case (_, Some(bookingFilter)) if ((isForceAlternativeRoom || isAlternativeRoom) && !isHourlyBooking) =>
              val (bookingUid, masterRoomIdOpt) = getRoomIdFromBookingFilter(metaHotel, bookingFilter)
              val dmcId = bookingUid.flatMap(_.roomIdentifier).map(_.coreFields.supplierId).getOrElse(-1).toString
              val cid = d.ctx.baseRequest.cInfo.cid.getOrElse(-1).toString
              (masterRoomIdOpt, bookingUid) match {
                case (Some(masterRoomId), Some(uid)) =>
                  val alternativeRooms = getAlternativeRooms(hotel,
                                                             uid,
                                                             masterRoomId,
                                                             d.ctx.baseRequest,
                                                             isAlternativeWithRefundablePriceFreeze,
                                                             isSupplierWhiteListFilterDisabled)
                  val reportTag = getAltRoomCountTag(alternativeRooms.size)
                  report(
                    Measurements.spapiAlternativeRoomCount,
                    1,
                    Map(
                      Measurements.tagType -> reportTag,
                      Measurements.tagIsPrecheckFlow -> isForceAlternativeRoom.toString,
                      Measurements.tagPlatformId -> d.ctx.baseRequest.platformId.toString,
                      Measurements.tagAffiliateId -> d.ctx.baseRequest.cInfo.affiliateId.getOrElse("-1"),
                      Measurements.tagCid -> cid,
                      Measurements.tagDmcId -> dmcId,
                    ),
                  )
                  hotel.copy(alternativeRooms = hotel.alternativeRooms ++ alternativeRooms)
                case _ =>
                  // case can't find master room type id
                  report(
                    Measurements.spapiAlternativeRoomCount,
                    1,
                    Map(
                      Measurements.tagType -> Measurements.tagNoMasterRoomTypeId,
                      Measurements.tagIsPrecheckFlow -> isForceAlternativeRoom.toString,
                      Measurements.tagPlatformId -> d.ctx.baseRequest.platformId.toString,
                      Measurements.tagAffiliateId -> d.ctx.baseRequest.cInfo.affiliateId.getOrElse("-1"),
                      Measurements.tagCid -> cid,
                      Measurements.tagDmcId -> dmcId,
                    ),
                  )
                  hotel
              }

            // SuperaggLowerNetSwap/BookingFormOfferSwap
            case (_, Some(_)) if (isSuperaggLowerNetSwap && !isHourlyBooking) =>
              val superaggLowerNetRooms = findBookingFormOfferSwapRooms(metaHotel)(d.ctx).toList
              hotel.copy(alternativeRooms = superaggLowerNetRooms)

            // Child Age Swapping
            case (AlternativeRoomTypes.ChildAgeSwap, Some(bookingFilter)) if (!isHourlyBooking) =>
              val matchedCriterias = bookingFilter.matchedCriteria
              val matchedCriteriasPerHotel = bookingFilter.matchedCriteria.filter(c => c.hotelId == hotel.id)
              val filteredUid = bookingFilter.uidList
              if (matchedCriterias.nonEmpty && hotel.rooms.forall(r =>
                  !filteredUid.exists(f => f.uid == r.uid || f.uid == r.symmetricUid(isHashDmcUidToRoomIdentifier)))) {
                val alternativeRoom = matchedCriteriasPerHotel.flatMap { criteria =>
                  matchedAlternativeRoom(hotel.rooms, criteria).map(altRoom =>
                    AlternativeRoom(criteria.uid.getOrElse(""), altRoom.uid, SwapRoomTypes.ChildAge))
                }

                val upsellingAlternativeRoom = getUpSellingAlternativeRooms(
                  bookingFilter.uidList,
                  hotel.rooms,
                  hotel.upSellingRelation,
                  isBreakfastUpSell,
                )
                hotel.copy(alternativeRooms = alternativeRoom ++ upsellingAlternativeRoom)
              } else {
                val upsellingAlternativeRoom = getUpSellingAlternativeRooms(
                  bookingFilter.uidList,
                  hotel.rooms,
                  hotel.upSellingRelation,
                  isBreakfastUpSell,
                )
                hotel.copy(alternativeRooms = hotel.alternativeRooms ++ upsellingAlternativeRoom)
              }
            // Try to find all swap rooms
            // Once d.ctx.experiment(ABTest.UPSELL_BREAKFAST_ON_BF is taken this will be the default case
            case (altRoomType, Some(bookingFilter))
                if ((altRoomType != AlternativeRoomTypes.NotRequire || isBreakfastUpSell || isAgodaRefund) && !isHourlyBooking) =>
              hotel.copy(alternativeRooms = getAlternativeRooms(
                bookingFilter.uidList,
                bookingFilter.originalRoomIdentifiers.map(_.uid),
                hotel.rooms,
                d.ctx.baseRequest.lengthOfStay,
                d.ctx.baseRequest.leadDays,
                altRoomType,
                hotel.upSellingRelation,
                isBreakfastUpSell,
                isAgodaRefund,
                d.ctx.baseRequest.bookingFilter.flatMap(_.selectedUpSellingRoomType),
                hotel.features.isApplyNewOccupancyLogic,
              )(d.ctx).toList)
            case _ => hotel
          }

          // Get additional rehashed mapping if room identifiers change
          d.ctx.baseRequest.bookingFilter
            .map { bookingFilter =>
              val rehashedRooms = getRehashedMapping(bookingFilter.originalRoomIdentifiers,
                                                     hotelWithAlternatives.rooms,
                                                     isHashDmcUidToRoomIdentifier)
              val isUpSellOptIn = bookingFilter.selectedUpSellingRoomType.isDefined

              val alternativeRooms = rehashedRooms.headOption match {
                case Some(rehashedRoom) => overrideAlternativeRooms(hotelWithAlternatives.alternativeRooms,
                                                                    rehashedRoom.selectedRoomUid,
                                                                    isUpSellOptIn)
                case _ => hotelWithAlternatives.alternativeRooms
              }
              hotelWithAlternatives.copy(alternativeRooms = (rehashedRooms ++ alternativeRooms).toList)
            }
            .getOrElse(hotelWithAlternatives)
        }
      }(flowLens, withMetaHotelLens)
    }
  }

  private[logic] def findBookingFormOfferSwapRooms(metaHotel: MetaHotel)(implicit
    ctx: FlowContext): Seq[AlternativeRoom] = {
    val hotel = metaHotel.d
    implicit val roomExperiments: RoomExperiments = RoomExperiments(hotel.features.isApplyNewOccupancyLogic)
    implicit val expCtx: ExperimentContext = ctx.experimentContext

    val isRequestEligible = !ctx.baseRequest.isPriceStateRequest && !ctx.baseRequest.isCartFunnelRequest &&
      !ctx.baseRequest.isPackagingFunnelRequest

    (metaHotel.meta.masterDmcId, ctx.baseRequest.bookingFilter) match {
      case (Some(DMC.Bcom), _) => Nil
      case (_, Some(bookingFilter)) if isRequestEligible =>
        val (bookingUid, masterRoomIdOpt) = getRoomIdFromBookingFilter(metaHotel, bookingFilter)
        (bookingUid, masterRoomIdOpt) match {
          case (Some(uid), Some(masterRoomId)) =>
            val alternativeRooms = getBookingFormOfferSwapRooms(
              hotel = hotel,
              selectedRoom = uid,
              masterRoomId = masterRoomId,
              baseRequest = ctx.baseRequest,
              loggingEnabled = bookingFormOfferSwapSettingsProducer.current.shouldLog(),
            )
            if (bookingFormOfferSwapSettingsProducer.current.shouldSwapCandidates()) alternativeRooms
            else Nil
          case _ => Nil
        }
      case _ => Nil
    }
  }

  private[logic] def addRateChannelSwapAlternative(d: Data[MetaHotels]): MetaHotels =
    d.ctx.baseRequest.bookingFilter.flatMap(_.uidList.headOption) match {
      case Some(uid) if d.ctx.baseRequest.isRateChannelSwap =>
        d.updateSeqUnit { _: MetaHotel => hotelWithAlternatives: Hotel =>
          implicit val roomExperiments: RoomExperiments =
            RoomExperiments(hotelWithAlternatives.features.isApplyNewOccupancyLogic)
          val rateChannelAlternatives: List[AlternativeRoom] =
            getRCSAlternativeRooms(hotelWithAlternatives, uid.uid, d.ctx.baseRequest)
          hotelWithAlternatives.copy(alternativeRooms =
            hotelWithAlternatives.alternativeRooms ++ rateChannelAlternatives)
        }(flowLens, withMetaHotelLens)

      case _ => d
    }

  // ToDo: Remove after integrating VEL-2093
  // Reporting duplicate rooms before filtering for the booking room to get closest to possible booking impact
  private def reportDuplicateRooms(d: Data[MetaHotels]): MetaHotels = {
    val totalRooms = d.data.map(_.d.rooms.size).sum
    val distinctRooms = d.data.map(_.d.rooms.map(_.uid).distinct.size).sum

    if (totalRooms != distinctRooms) {
      d.ctx.aggregateReporter.count(
        Measurements.numDuplicatedRooms,
        totalRooms - distinctRooms,
        Map(
          Measurements.tagRequestType -> d.ctx.baseRequest.getRequestType,
          Measurements.tagPlatformId -> d.ctx.baseRequest.platformId.toString,
        ),
      )
    }

    d
  }

  private[logic] def filterRoomsForBooking(d: Data[MetaHotels]): MetaHotels =
    d.updateSeqUnit { metaHotel: MetaHotel => hotel: Hotel =>
      applyBookingFilter(metaHotel, d.ctx)
    }(flowLens, withMetaHotelLens)

  private[logic] def filterRoomsBySymmetricUid(d: Data[MetaHotels]): MetaHotels =
    d.updateSeqUnit { _: MetaHotel => hotel: Hotel => filterOut(hotel, d.ctx) }(flowLens, withMetaHotelLens)

  private[logic] def filterRoomsByRoomIdentifier(d: Data[MetaHotels]): MetaHotels =
    d.updateSeqUnit { _: MetaHotel => hotel: Hotel => filter(hotel, d.ctx) }(flowLens, withMetaHotelLens)

  private[logic] def limitNumberOfRooms(d: Data[MetaHotels]): MetaHotels = d.ctx.baseRequest.maxRooms match {
    case Some(maxRoom) if maxRoom > 0 =>
      implicit val ctx = d.ctx
      d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
        val numRoom = hotel.rooms.size
        val propertyContext = PropertyContext(hotel.id, hotel.cityId, hotel.countryId)
        if (d.ctx.baseRequest.fencedRatePairs.exists(_.nonEmpty) && d.ctx.determineVariant(
            ABTest.PRICE_PUSH_FENCING) == Variant.B) {
          val limitedRooms: List[Room] = hotel.rooms
            .groupBy(_.fences)
            .mapValues { rooms =>
              if (rooms.size > maxRoom) {
                val retainSellDiscountOffers =
                  ctx.determineVariant(propertyContext, ABTest.AFFILIATE_GENERATE_SELL_DISCOUNT_OFFERS) == Variant.B

                val retainRetailOffers = FlowHelper.shouldEnforceRetailOffersAffiliate(ctx, propertyContext)
                limitRooms(rooms,
                           maxRoom,
                           hotel.suggestPriceType.suggestPrice,
                           retainSellDiscountOffers,
                           retainRetailOffers)
              } else rooms
            }
            .flatMap(_._2)(collection.breakOut)
          hotel.set(limitedRooms)(hotelRoomsLens)
        } else if (numRoom > maxRoom) {
          val retainSellDiscountOffers =
            ctx.determineVariant(propertyContext, ABTest.AFFILIATE_GENERATE_SELL_DISCOUNT_OFFERS) == Variant.B

          val retainRetailOffers = FlowHelper.shouldEnforceRetailOffersAffiliate(ctx, propertyContext)
          val limitedRooms = limitRooms(hotel.rooms,
                                        maxRoom,
                                        hotel.suggestPriceType.suggestPrice,
                                        retainSellDiscountOffers,
                                        retainRetailOffers)
          hotel.set(limitedRooms)(hotelRoomsLens)
        } else hotel
      }(flowLens, withMetaHotelLens)

    case None => d.data
  }

  private[logic] def filterUnSupportedPaymentModel(d: Data[MetaHotels]): MetaHotels = {
    val isEnableRMPriceRef = d.ctx.baseRequest.flagInfo.isEnableSupplierFinancialInfo
    if (!isEnableRMPriceRef) {
      d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
        val filteredRooms = hotel.rooms.filter(room =>
          supportsPaymentModel(EnumConverter.toDfPaymentModel(room.roomCoreInfo.paymentModel), d.ctx.baseRequest))
        hotel.copy(rooms = filteredRooms)
      }(flowLens, withMetaHotelLens)
    } else {
      d
    }
  }

  private[logic] def duplicateRoomForPriceline(d: Data[MetaHotels]): MetaHotels =
    if (d.ctx.baseRequest.flagInfo.isMSE) {
      d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
        val duplicatedRooms = duplicateRoomsForMSE(hotel)(d.ctx)
        if (duplicatedRooms.nonEmpty) hotel.copy(rooms = hotel.rooms ++ duplicatedRooms)
        else hotel
      }(flowLens, withMetaHotelLens)
    } else {
      d
    }

  /**
    * Determines if agency rooms are allowed based on the experiment and feature flag.
    * TODO: CARTPACK 510 & 329 - Remove after integration
    */
  private[logic] def determineAllowAgencySupplyForPackage(d: Data[MetaHotels]): Boolean = {
    val isRemoveAgencySupplyExperimentB = d.ctx.determineVariant(ABTest.REMOVE_AGENCY_SUPPLY_FF) == Variant.B
    if (isRemoveAgencySupplyExperimentB) {
      // If Variant B, agency rooms are allowed by default
      true
    } else {
      // Fallback to the previous method: check the feature flag
      d.ctx.baseRequest.featureFlags.contains(FeatureFlag.EnableAgencySupplyForPackages)
    }
  }

  private[logic] def marksPackageEligibleRoomsForPricing(d: Data[MetaHotels]): MetaHotels =
    if (!d.ctx.baseRequest.isPackagingFunnel) d.data.map { h =>
      h.update {
        val isAllowAgency: Boolean = determineAllowAgencySupplyForPackage(d)
        val isAllowNonRefundable: Boolean = false

        (_: List[Room]).map((room: Room) =>
          roomEligibleForPackagingPricing(h.meta, room, isAllowAgency, isAllowNonRefundable))
      }(withMetaRoomsLens)
    }
    else d

  private[logic] def marksMultiHotelEligibleRoomsForPricing(d: Data[MetaHotels]): MetaHotels =
    if (!d.ctx.baseRequest.isHotelPlusHotelRequest
      && d.ctx.determineVariant(ABTest.DISABLE_MULTI_HOTEL) != Variant.B) d.data.map { h =>
      h.update {
        val isAllowAgency: Boolean = determineAllowAgencySupplyForPackage(d)
        val isAllowNonRefundable: Boolean = true

        (_: List[Room]).map((room: Room) =>
          roomEligibleForMultiHotelPricing(h.meta, room, isAllowAgency, isAllowNonRefundable))
      }(withMetaRoomsLens)
    }
    else d

  private[logic] def marksCartEligibleRoomsForPricing(d: Data[MetaHotels]): MetaHotels = d.data.map { h =>
    h.update {
      val isAllowAgency: Boolean = true
      val isAllowNonRefundable: Boolean = true

      (_: List[Room]).map((room: Room) =>
        roomEligibleForCartPricing(h.meta, room, isAllowAgency, isAllowNonRefundable)(d.ctx))
    }(withMetaRoomsLens)
  }

  /**
    * Here we need to adjust supplier summary:
    * 1. remove unavalable ready suppliers after removing rooms by soybean streamlining.
    * 2. add virtual suppliers that might be generated (with subSupplier field)
    */
  private[logic] def synchronizeHotelWithRooms(d: Data[MetaHotels]): MetaHotels =
    d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
      val readyAvailableOrNonReadySuppliers = hotel.suppliers.filter { case (supplierId, info) =>
        !info.isReady || hotel.availableSuppliers.contains(supplierId)
      }

      //  Here I assume that we have all real supplier definition (so we don't have virtual supplier with virtual parent)
      val virtualSuppliers = hotel.virtualSuppliersMap.map { case (virtual, real) =>
        virtual -> hotel.suppliers(real).copy(id = virtual)
      }

      val adjustedSuppliers = readyAvailableOrNonReadySuppliers ++ virtualSuppliers
      val hotelWithAdjustedSuppliers = hotel.copy(suppliers = adjustedSuppliers)

      val result: Hotel =
        if (readyAvailableOrNonReadySuppliers.contains(hotel.supplierId)) {
          hotelWithAdjustedSuppliers
        } else {
          val (ready, nonReady) = hotelWithAdjustedSuppliers.suppliers.values.toSeq.partition(_.isReady)

          //  we should select ready supplier first
          ready.headOption.orElse(nonReady.headOption) match {
            case Some(newSupplier) => hotelWithAdjustedSuppliers.copy(supplierId = newSupplier.id)
            case None => hotelWithAdjustedSuppliers
          }
        }

      //  ToDO: filter out unavailable hotels here instead of last flow.
      result
    }(flowLens, withMetaHotelLens)

  private[logic] def sortAndGroupMerchantRoomBeforeMorpRoom(d: Data[MetaHotels]): MetaHotels =
    if (d.ctx.baseRequest.isPageSearchRequest || d.ctx.baseRequest.isCheapestRoomOnly) {
      d
    } else {
      d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
        hotel.copy(rooms = hotel.rooms
          .groupBy(_.masterRoomId)
          .flatMap { case (_, rooms) => rooms.sorted(RoomSorting.paymentModelOrdering) }
          .toList)
      }(flowLens, withMetaHotelLens)
    }

  private[logic] def applyCheapestPriceFilters(d: Data[MetaHotels]): Future[MetaHotels] = Future.successful {
    if (d.ctx.baseRequest.cheapestRoomFilters.nonEmpty) {
      d.updateSeqUnit { mh: MetaHotel => hotel: Hotel =>
        {
          implicit val ctx: FlowContext = d.ctx
          hotel.copy(
            rooms = hotel.rooms.filter(filterCheapestPriceRoom),
            roomBundles = hotel.roomBundles.filter(_.segments.forall(segment => filterCheapestPriceRoom(segment.room))),
          )
        }
      }(flowLens, withMetaHotelLens)
    } else d.data
  }

  private[logic] def filterCheapestPriceRoom(room: Room)(implicit ctx: FlowContext): Boolean =
    ((!ctx.baseRequest.cheapestRoomFilters.contains(CheapestRoomFilterTypes.FreeBreakfast.i)
    || room.benefits.exists(b => b.groupId == Benefits.BreakfastGroupId || Benefits.isBreakfast(b.id)))
    && (!ctx.baseRequest.cheapestRoomFilters.contains(CheapestRoomFilterTypes.FreeCancellation.i)
    || room.paymentInfo.cancellationGroup == CancellationGroup.FreeCancellation)
    && (!ctx.baseRequest.cheapestRoomFilters.contains(CheapestRoomFilterTypes.NoCCRequired.i)
    || room.paymentInfo.noCreditCard)
    && ((!ctx.baseRequest.cheapestRoomFilters.contains(CheapestRoomFilterTypes.PayAtHotel.i)
    || (room.paymentModel == PaymentModel.Agency && !room.prepaymentRequired)))
    && ((!ctx.baseRequest.cheapestRoomFilters.contains(CheapestRoomFilterTypes.KidsStayForFree.i)
    || (ctx.baseRequest.occ.children > 0 && room.occ.perOfferFreeChildren.exists(k => k >= ctx.baseRequest.occ.children))
    || (ctx.baseRequest.occ.children == 0 && room.perOfferMaxFreeChildren.exists(k => k > 0)))))

  private[logic] def findRareRoomFeature(d: Data[MetaHotels]): MetaHotels =
    if (d.ctx.baseRequest.featureRequest.calculateRareRoomBadge.getOrElse(false)) {
      d.updateSeqUnit { metaHotel: MetaHotel => hotel: Hotel =>
        {
          val pastBookingInfo: Map[PastBookingInfoKey, PastBookingInfoSummary] = metaHotel.meta.pastBookingInfo
          hotel.copy(rooms = hotel.rooms.map { (room: Room) =>
            val pastBookingInfoKey =
              room.roomCoreInfo.roomTypeId.toString ++ "_" ++ room.propertyInfo.breakfast.toString ++
                "_" ++ room.paymentInfo.cancellationGroup.value.toString
            val pastBookingInfoSummary = pastBookingInfo.get(pastBookingInfoKey)
            val roomWithFlag = pastBookingInfoSummary.map { (bookingInfo: PastBookingInfoSummary) =>
              val past180DaysPriceInfo = bookingInfo.pastBookingInfoSummary.getOrElse(
                PastBookingInformationType.past180Days.getPastBookingInformationTypeString,
                PastBookingPrices(DisplayPrice(0d, 0d), DisplayPrice(0d, 0d)))
              val past90DaysPriceInfo = bookingInfo.pastBookingInfoSummary.getOrElse(
                PastBookingInformationType.past90Days.getPastBookingInformationTypeString,
                PastBookingPrices(DisplayPrice(0d, 0d), DisplayPrice(0d, 0d)))
              val past30DaysPriceInfo = bookingInfo.pastBookingInfoSummary.getOrElse(
                PastBookingInformationType.past30Days.getPastBookingInformationTypeString,
                PastBookingPrices(DisplayPrice(0d, 0d), DisplayPrice(0d, 0d)))

              val rareRoomType: Option[RareRoomType] =
                if (room.reqSellIn() < past180DaysPriceInfo.minPrice.allInclusive) {
                  Some(PastBookingInformationType.past180Days.getRareRoomType)
                } else if (room.reqSellIn() < past90DaysPriceInfo.minPrice.allInclusive) {
                  Some(PastBookingInformationType.past90Days.getRareRoomType)
                } else if (room.reqSellIn() < past30DaysPriceInfo.minPrice.allInclusive) {
                  Some(PastBookingInformationType.past30Days.getRareRoomType)
                } else None

              room.copy(rareRoomType = rareRoomType)
            }
            roomWithFlag.getOrElse(room)
          })
        }
      }(flowLens, withMetaHotelLens)
    } else d.data

  private[logic] def filterRoomsWithRequiredNumberOfBedrooms(d: Data[MetaHotels]): MetaHotels =
    d.withMeta { implicit mx =>
      if (d.ctx.baseRequest.nosOfBedrooms.nonEmpty) {
        d.updateSeqUnit { metaHotel: MetaHotel => hotel: Hotel =>
          filterHotelBedrooms(d.ctx.baseRequest.nosOfBedrooms, hotel, metaHotel)
        }(flowLens, withMetaHotelLens).data
      } else d.data
    }

  private def filterHotelBedrooms(requiredBedroomCounts: List[NoOfBedrooms],
                                  hotel: Hotel,
                                  metaHotel: MetaHotel): Hotel =
    if (requiredBedroomCounts.isEmpty) hotel
    else {

      def hasRequiredBedrooms(room: Room, requiredCount: Int, acceptMoreBedrooms: Boolean): Boolean =
        metaHotel.meta.getNumberOfBedrooms(room.roomTypeId) match {
          case Some(bedroomCount) =>
            bedroomCount == requiredCount || (acceptMoreBedrooms && bedroomCount > requiredCount)
          case None => false
        }

      val filteredRooms = hotel.rooms.filter(room =>
        requiredBedroomCounts.exists(requiredCount =>
          requiredCount match {
            case BedroomCount.OneBedroom | BedroomCount.TwoBedroom => hasRequiredBedrooms(room, requiredCount, false)
            case BedroomCount.ThreePlusBedroom => hasRequiredBedrooms(room, requiredCount, true)
            case _ => true
          }))

      hotel.copy(rooms = filteredRooms)
    }

  private def doNPCLN(d: Data[MetaHotels]): MetaHotels =
    //  NPCLN is never dispatched so we just check was NPCLN rateplan requested or not
    if (d.ctx.baseRequest.isChannelRequested(DFMasterChannel.NPCLN)) {
      d.updateSeqUnit { wm: MetaHotel => hotel: Hotel =>
        applyNPCLNRooms(hotel)(d.ctx.baseRequest)
      }(flowLens, withMetaHotelLens)
    } else {
      d.data
    }

  private[logic] def calculateUpSellingRelation(d: Data[MetaHotels]): MetaHotels = {
    val isBreakfastUpSellEnable =
      (d.ctx.baseRequest.regulationFeatureEnabledSetting.isEmpty
        || d.ctx.baseRequest.regulationFeatureEnabledSetting.exists(
          _.isBreakfastUpsellEnabled)) && !d.ctx.baseRequest.isPriceFreezeRequest // Block upsell for Price Freeze

    val doBreakfastUpsell =
      isBreakfastUpSellEnable && d.ctx.baseRequest.featureFlags.contains(FeatureFlag.BreakfastUpsell)
    val isFixBreakfastUpsellSavingAmount =
      d.ctx.experimentContext.experiment(ABTest.FIX_BREAKFAST_UPSELL_SAVING_AMOUNT).isUserB
    val isHashDmcUidToRoomIdentifier = d.ctx.experimentContext.experiment(ABTest.HASH_DMCUID_TO_ROOMIDENTIFIER).isUserB

    val bookingFilter = d.ctx.baseRequest.bookingFilter

    d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
      val roomsAvailableForUpsell = hotel.rooms.filter(!_.isEscapePackageSupported)

      val breakfastUpSelling: UpSellingBreakfastRelation =
        if (doBreakfastUpsell) {
          getBreakfastRelation(
            roomsAvailableForUpsell,
            bookingFilter,
            d.ctx.baseRequest.lengthOfStay,
            hotel.breakfastPriceFromHotelSetting,
            isFixBreakfastUpsellSavingAmount,
            isHashDmcUidToRoomIdentifier,
          )
        } else {
          Map.empty
        }

      val upSellingRelation =
        if (breakfastUpSelling.nonEmpty) {
          Some(UpSellingRelation(breakfastRelations = breakfastUpSelling))
        } else {
          None
        }

      hotel.copy(upSellingRelation = upSellingRelation)
    }(flowLens, withMetaHotelLens)
  }

  private[logic] def calculateAgodaRefund(d: Data[MetaHotels]): MetaHotels = d.withMeta { implicit mx =>
    val request = d.ctx.baseRequest
    d.data.map { h =>
      h.update((_: Hotel) => h.d.copy(rooms = upliftAgodaRefund(h.d, request)(d.ctx)))(withMetaHotelLens)
    }
  }

  private[logic] def finalizeCrossOutRate(d: Data[MetaHotels]): MetaHotels = d.withMeta { implicit mx =>
    implicit val ctx: FlowContext = d.ctx
    if (ctx.baseRequest.flagInfo.enableCor && (!ctx.baseRequest.isPriceStateRequest)) {
      d.data.map { h =>
        h.update((_: Hotel) =>
          h.d.copy(rooms = calculateCor(
            h.d.rooms,
            h.d.id,
            h.meta.chainId,
            ctx.baseRequest.cInfo.origin,
            h.meta.calculatedCOR,
            h.meta.maxRoomNightValueCOR,
            h.meta.calculatedCORwithSearchCheckIn,
            h.meta.minRoomNightValueCOR,
            h.d.addFeesInExclusivePrice, // Block COR rates when displaying fee-exclusive prices, as COR may be inaccurate
          )))(withMetaHotelLens)
      }
    } else d.data
  }

  private[logic] def updateExchangeRateListInRooms(d: Data[MetaHotels]): MetaHotels = {
    val request = d.ctx.baseRequest
    val includeUsdAndLocalCurrency = request.isIncludeUsdAndLocalCurrency || request.isBookingRequest
    val isRebookV3MatchUSD = request.isRebookingRequestV3MatchUSD
    val isRemoveFxFreezing = d.ctx.isForcedOrSeenAsB(ABTest.REMOVE_FX_FREEZING_FOR_CXL_REBOOK_V3)
    d.updateSeqUnit { m: MetaHotel => hotel: Hotel =>
      def updateExchangeRateList(r: Room): Room = {
        val exchangeRate = r.exchange

        val localCurrency = exchangeRate.map(_.local).getOrElse("")
        val reqCurrency = exchangeRate.map(_.request).getOrElse("")

        val toReqExchangeRate = getExchangeRate(localCurrency, reqCurrency)
        // Updating exchange rate for rebooking request, only for Request currency
        val updatedToReqExchangeRate = toReqExchangeRate.map { reqExchangeRate =>
          if (isRebookV3MatchUSD && !isRemoveFxFreezing) {
            request.reBookingRequest
              .flatMap(_.originalUsdToRequestExchangeRate)
              .fold(reqExchangeRate)(rate => reqExchangeRate.copy(toRequest = rate))
          } else {
            reqExchangeRate
          }
        }

        val exchangeRates =
          if (includeUsdAndLocalCurrency) {
            // If local currency is same as request currency, then we need only updated one to avoid duplicate
            // Adding checks only for Cancel Rebook V3
            if (isRebookV3MatchUSD &&
              !isRemoveFxFreezing &&
              request.reBookingRequest
                .flatMap(_.originalUsdToRequestExchangeRate)
                .isDefined && localCurrency == reqCurrency) {
              List(
                updatedToReqExchangeRate,
                getExchangeRate(localCurrency, ExchangeDataService.USD_CODE),
              ).distinct

            } else {
              val exchangeList = List(
                getExchangeRate(localCurrency, localCurrency),
                getExchangeRate(localCurrency, ExchangeDataService.USD_CODE),
                updatedToReqExchangeRate,
              )

              val exchangeListWithHotelCurrency = m.meta.getHotelCurrencyCode(isHotelLocalCurrencyEnable(d.ctx)) match {
                case Some(hotelLocalCurrency) => exchangeList :+ getExchangeRate(localCurrency, hotelLocalCurrency)
                case _ => exchangeList
              }
              exchangeListWithHotelCurrency.distinct
            }

          } else {
            List(updatedToReqExchangeRate)
          }

        if (isRebookV3MatchUSD && !isRemoveFxFreezing) {
          val updatedExchange: Option[ExchangeRate] =
            exchangeRate.map(x => x.copy(toRequest = updatedToReqExchangeRate.map(_.toRequest).getOrElse(x.toRequest)))

          r.copy(exchangeRateList = exchangeRates.flatten, exchange = updatedExchange)
        } else {
          r.copy(exchangeRateList = exchangeRates.flatten)
        }

      }

      hotel.copy(rooms = hotel.rooms.map { room =>
        //  Don't apply new exchangeRates to the cached room
        if (room.isCached && room.exchangeRateList.nonEmpty) room
        else updateExchangeRateList(room)
      })
    }(flowLens, withMetaHotelLens)
  }

  /**
    * To wait for some suppliers with potentially better price
    * Clear isReady if supplier in such whitelist is not ready
    */
  private[logic] def clearIsReadyForSuppliersToPoll(d: Data[MetaHotels]): MetaHotels = {
    val isFlowEnabled = d.ctx.baseRequest.featureFlags.contains(
      FeatureFlag.ReturnHotelNotReadyIfPullNotReady,
    )
    d.updateSeqUnit { _: MetaHotel => hotel: Hotel =>
      if (isFlowEnabled) {
        hotel.copy(suppliersToWait = ssrRetrySettings.suppliersToWait)
      } else {
        hotel
      }
    }(flowLens, withMetaHotelLens)
  }

  private[logic] def marksCartRestrictedRooms(d: Data[MetaHotels]): MetaHotels = d.data.map { h =>
    h.update {
      (_: List[Room]).map((room: Room) => roomEligibleForCartRestricted(h.meta, room))
    }(withMetaRoomsLens)
  }

  private[logic] def markAllowMultipleBookingRoom(d: Data[MetaHotels]): MetaHotels = d.data.map { h =>
    h.update {
      (_: List[Room]).map((room: Room) => roomAllowedForMultipleBooking(room))
    }(withMetaRoomsLens)
  }
}
