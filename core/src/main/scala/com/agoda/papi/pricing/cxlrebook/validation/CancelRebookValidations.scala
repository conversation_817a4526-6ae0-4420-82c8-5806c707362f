package com.agoda.papi.pricing.cxlrebook.validation

import api.request.ReBookingRequest
import com.agoda.papi.constants.CurrencyCode
import com.agoda.papi.pricing.discounting.models.consts.Constants
import com.agoda.papi.pricing.display.utils.PriceComparisionMetrics
import com.agoda.papi.pricing.models.internal.SfRoomPricing
import com.typesafe.scalalogging.LazyLogging
import models.consts.RoundingAdjustment
import models.pricing.enums.BookingRateType
import models.pricing.enums.BookingRateTypes._
import models.utils.ItemBreakdownHelper
import utilhelpers.Measurements

object CancelRebookValidations extends LazyLogging {
  private val roundingDiffThreshold = 0.0002 // 0.02%
  private case class InputValidation(item: BookingRateType,
                                     condition: Double => Boolean,
                                     actualAmount: Double,
                                     expectedAmount: Option[Double]) {
    def checkInput(): Option[String] = expectedAmount match {
      case Some(value) if condition(value) => None
      case Some(value) => Some(s"${item.entryName}: $value")
      case _ => None
    }
  }
  private case class CancelRebookValidations(logging: Option[String], tag: Map[String, String])
  private val blacklistItems = Set(
    CofundedCashback,
    PromocodeCashback,
    // promo funding, this type will be shared/absorb by supplier. they are not eligible for CxlRebook V3
    HotelFunding,
    CreditCardFunding,
    GovernmentFunding,
  )
  private val supplyLocalAmount = "SupplyLocalAmount"

  def validateCxlRebookV3(searchId: String,
                          rooms: Seq[SfRoomPricing],
                          reBookingRequestOpt: Option[ReBookingRequest]): (Seq[SfRoomPricing], Map[String, String]) = {

    def buildPricingValidator(reBookingRequest: ReBookingRequest,
                              actualSellIn: Double,
                              actualPromo: Double,
                              actualCashback: Double): Set[InputValidation] = Set(
      InputValidation(SellInclusive, _ > 0d, actualSellIn, reBookingRequest.originalSellIn),
      InputValidation(DiscountPromotion, _ <= 0d, actualPromo, reBookingRequest.promoAmount),
      InputValidation(Cashback, _ >= 0d, actualCashback, reBookingRequest.cashbackAmount),
    )

    val filteredRoomWithTags = reBookingRequestOpt.map { reBookingRequest =>
      rooms.foldLeft((List.empty[SfRoomPricing], Map.empty[String, String])) { (roomWithtag, room) =>
        val usdItems = room.itemBreakdown.get(CurrencyCode.USD).map(ItemBreakdownHelper.finalizeEbeItems).getOrElse(Nil)
        val localItems =
          room.itemBreakdown.get(room.dfRoom.currency).map(ItemBreakdownHelper.finalizeEbeItems).getOrElse(Nil)

        val bookingInfo = room.bookingInfo
        val roomExchange = bookingInfo.flatMap(_.roomExchange)
        val localCurrencyPrecision = roomExchange.map(_.numReqDecimal).getOrElse(Constants.defaultPrecision)
        val matchLocalCurrency = reBookingRequest.matchLocalCurrency

        val (items, precision) =
          if (matchLocalCurrency) {
            (localItems, localCurrencyPrecision)
          } else {
            (usdItems, RoundingAdjustment.defaultUsdPrecision)
          }

        val actualSellIn = ItemBreakdownHelper.getTotalAmountByTypes(
          items,
          precision,
          Seq(SellInclusive),
        )
        val actualPromo = ItemBreakdownHelper.getTotalCampaignDiscountFromItemBreakdown(
          items,
        )

        val actualCashbackFromFinancialBreakdown = ItemBreakdownHelper.getTotalCashback(items)
        val actualCashbackFromBookingInfo = room.bookingInfo.flatMap(_.cashbackUsdAmount).getOrElse(0.0)

        val pricingItems =
          buildPricingValidator(reBookingRequest, actualSellIn, actualPromo, actualCashbackFromFinancialBreakdown)

        val invalidInputs = pricingItems.flatMap(_.checkInput())
        val invalidReasons = {
          val pricingReasons = pricingItems.map(priceItem =>
            compareValues(priceItem.actualAmount, priceItem.expectedAmount.getOrElse(0.0), priceItem.item.entryName))
          val blacklistReasons = blacklistItems.map(
            itemType => // expect zero item for actualCofundedCashback, actualPromoCodeCashback and supplier fundings in cxl rebook flow
              compareValues(ItemBreakdownHelper.getGenericItemCount(usdItems, itemType), 0, itemType.entryName))
          val cashbackReasons =
            if (matchLocalCurrency) Set.empty[CancelRebookValidations]
            else Set(
              compareValues(actualCashbackFromBookingInfo,
                            reBookingRequest.cashbackAmount.getOrElse(0.0),
                            "BookingInfo.Cashback"))
          pricingReasons ++ blacklistReasons ++ cashbackReasons
        }

        val (invalidReasonLogging, invalidReasonTags) =
          invalidReasons.foldLeft((Seq.empty[String], Map.empty[String, String])) {
            case ((loggingAcc, tagAcc), CancelRebookValidations(logging, tag)) =>
              (loggingAcc ++ logging.toSeq, tagAcc ++ tag)
          }

        val usdNetIn = ItemBreakdownHelper.getGenericItemValue(usdItems, NetInclusive)
        val localNetIn = ItemBreakdownHelper.getGenericItemValue(localItems, NetInclusive)
        val expectedLocalNetIn = roomExchange.map(ex => ex.usdToLocalRate(usdNetIn)).getOrElse(0d)
        val (invalidSupplyLocalLogging, supplyLocalTag) =
          if (PriceComparisionMetrics.comparePricePercent(expectedLocalNetIn, localNetIn) <= roundingDiffThreshold)
            (Seq.empty, Map(supplyLocalAmount -> Measurements.tagSuccess))
          else (Seq(supplyLocalAmount), Map(supplyLocalAmount -> Measurements.tagFailure))

        doLogging(searchId,
                  room,
                  invalidInputs,
                  invalidReasonLogging,
                  localNetIn,
                  expectedLocalNetIn,
                  invalidSupplyLocalLogging)

        val filteredRoom =
          if (invalidInputs.isEmpty && invalidReasonLogging.isEmpty) {
            Some(room)
          } else {
            None
          }

        val (finalRooms, finalTags) = roomWithtag
        (finalRooms ++ filteredRoom, mergeTag(finalTags, invalidReasonTags ++ supplyLocalTag))
      }
    }
    filteredRoomWithTags.getOrElse((Nil, Map.empty))
  }

  @SuppressWarnings(
    Array("stryker4s.mutation.MethodExpression",
          "stryker4s.mutation.LogicalOperator",
          "stryker4s.mutation.ConditionalExpression"))
  private def doLogging(searchId: String,
                        room: SfRoomPricing,
                        invalidInputs: Set[String],
                        invalidReasonLogging: Seq[String],
                        localNetIn: Double,
                        expectedLocalNetIn: Double,
                        invalidSupplyLocalLogging: Seq[String]): Unit =
    if (invalidInputs.nonEmpty || invalidReasonLogging.nonEmpty || invalidSupplyLocalLogging.nonEmpty) {
      logger.warn(
        s"Rebooking validation failed for SearchID: $searchId with RoomTypeId: ${room.dfRoom.roomTypeId}. Invalid fields: ${(invalidSupplyLocalLogging ++ invalidInputs ++ invalidReasonLogging)
            .mkString(", ")}, supplyLocal expected: $expectedLocalNetIn, actual: $localNetIn",
      )
    }

  private def mergeTag(tagsA: Map[String, String], tagsB: Map[String, String]): Map[String, String] = {
    val superSet = tagsA.keySet.union(tagsB.keySet)
    superSet.map { key =>
      val finalValue = (tagsA.get(key), tagsB.get(key)) match {
        case (Some(valueA), Some(valueB)) =>
          if (valueA == Measurements.tagFailure || valueB == Measurements.tagFailure) Measurements.tagFailure
          else Measurements.tagSuccess
        case (None, Some(valueB)) => valueB
        case (Some(valueA), None) => valueA
        case _ => ""
      }
      key -> finalValue
    }.toMap
  }

  private def compareValues[T](actual: T, expected: T, fieldName: String): CancelRebookValidations =
    if (actual != expected) CancelRebookValidations(Some(s"$fieldName: actual=$actual, expected=$expected"),
                                                    Map(fieldName -> Measurements.tagFailure))
    else CancelRebookValidations(None, Map(fieldName -> Measurements.tagSuccess))
}
