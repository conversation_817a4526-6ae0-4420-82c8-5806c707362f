package com.agoda.papi.pricing.discounting.mappers

import api.request.{FeatureFlag, OccInfo, ReBookingRequest}
import com.agoda.papi.constants.PlatformID
import com.agoda.papi.enums.campaign.{PriceDisplayType, PriceDisplayTypes}
import com.agoda.papi.enums.hotel.{DispatchAvailabilityTypes, RateAvailabilityTypes, RateModel}
import com.agoda.papi.enums.request.LoyaltySearchType
import com.agoda.papi.enums.room.{ChargeType, ChildRateType, RatePlanStatus, SubChargeType}
import com.agoda.papi.pricing.EnumConverter.fromDfFeatureFlag
import com.agoda.papi.pricing.configuration.{JapanCampaignSettingsProducer, MaxDownliftRemovalSettingsProducer}
import com.agoda.papi.pricing.discounting.helper.Constants.defaultPrecision
import com.agoda.papi.pricing.discounting.mappers.DiscountRequestMapper._
import com.agoda.papi.pricing.discounting.models.internal.ExperimentCarrier
import com.agoda.papi.pricing.discounting.models.request.context.{ClientInfo, DiscountingRequest, RequestParams}
import com.agoda.papi.pricing.discounting.models.request.pricing._
import com.agoda.papi.pricing.discounting.models.request.promocode._
import com.agoda.papi.pricing.discounting.models.request.rewards._
import com.agoda.papi.pricing.discounting.models.request.soybean._
import com.agoda.papi.pricing.discounting.models.response._
import com.agoda.papi.pricing.flow.postsearch.WithPricingParameters
import com.agoda.papi.pricing.metadata.MasterRoomInfo
import com.agoda.papi.pricing.models.enums.{PricingMessageLocalizationType, PricingMessageVariableType}
import com.agoda.papi.pricing.opentelemetry.DfTracerOps.TracerExtensions
import com.agoda.papi.pricing.services.{CmsService => UtilCmsService}
import com.agoda.papi.ypl.models.{ResellExternalData, RoomNumber, YplRateFence, YplChannel => DFCompositeChannel}
import com.agoda.papi.ypl.pricing.PriceCalculator.calculateDownliftOnMarginFactor
import com.agoda.supply.PriceAdjustmentIdHelper
import com.agoda.utils.ess.models.EssContext
import external.pricing.PricingService.{PricingRoomLookup, RoomIndex}
import external.pricing.ess.ESSService
import external.pricing.{DispatchInfo, HotelDispatchInfo}
import helper.ESSHelper
import logic.WithMeta.MetaHotels
import models.consts.{ABTest, CountryId, DMC}
import models.db.{RoomTypeId, RoomUID}
import models.flow.{FlowContext, Variant}
import models.pricing.enums.{ApplyType, ApplyTypes}
import models.pricing.{
  ConvertCurrencyAndRoundingService,
  DfRoomAllocation,
  ExchangeRate,
  FinanceProductInfo,
  RateCategory,
  Room,
  RoomExperiments,
  Cashback => DFCashBack,
  GiftCard => DFGiftCard,
  Hotel => DFHotel,
  PseudoCouponMessage => DFPseudoCouponMessage,
}
import models.starfruit
import models.starfruit.{FencedOriginObject, FencedRateKey, FencedRatePair, PricingMessage => SFPricingMessage}
import models.utils.promotions.PromotionHelper
import pricing.models.common.FinanceProductType
import pricing.models.soybean.RebookInfo
import services.ExperimentSupport
import services.packaging.PackagingRequestHelper
import services.promotions.PromotionEligibleDataService
import settings.APSPeekSettings

import scala.collection.{Map, Set}
import scala.concurrent.{ExecutionContext, Future}

trait DiscountRequestMapper extends PromotionEligibleDataService with SoybeanRequestMapper with ExperimentSupport {

  protected val pricingParameters: WithPricingParameters
  protected val cmsService: UtilCmsService
  protected val japanCampaignSettingsProducer: JapanCampaignSettingsProducer
  protected val maxDownliftRemovalSettingsProducer: MaxDownliftRemovalSettingsProducer
  protected val essService: ESSService

  private val defaultRoomNumber = 1

  import com.agoda.papi.pricing.discounting.helpers.ImplicitHelpers._

  def toDiscountingRequest(metaHotels: MetaHotels, roomLookup: PricingRoomLookup, apsRoomLookup: PricingRoomLookup)(
    implicit
    ctx: FlowContext,
    ec: ExecutionContext,
  ): Future[DiscountingRequest] = {
    val isHotelCountryJapan = metaHotels.exists(_.meta.countryId == CountryId.Japan)
    val apsPeekSettings = APSPeekSettings.apply()
    val requestParams = toRequestParams(ctx).copy(isHotelCountryJapan = isHotelCountryJapan)

    val discountingHotelsF: List[Future[DiscountHotel]] = metaHotels.map { metaHotel =>
      val meta = metaHotel.meta
      val hotel = metaHotel.d
      val lookup = roomLookup.getOrElse(metaHotel.id, Map.empty)
      val lookupUid = lookup.map { case (k, v) => v.uid -> k }
      val soybeanHotelParams = toSoybeanHotelParams(metaHotel, roomLookup)
      val discountRoomsF = ctx.tracer.withSpanM("to-discount-room") {
        Future {
          lookup.collect { case (roomId, room) =>
            val discountingRoom = toDiscountRoom(room,
                                                 hotel,
                                                 metaHotel.hotelDispatchInfo,
                                                 metaHotel.meta.enabledRoom,
                                                 lookupUid,
                                                 requestParams.experimentCarrier.isDynamicDownliftEnabled)
            roomId -> discountingRoom
          }
        }
      }

      val apsDiscountRoomsF = ctx.tracer.withSpanM("to-aps-discount-room") {
        Future {
          apsRoomLookup.getOrElse(metaHotel.id, Map.empty).collect { case (roomId, room) =>
            val discountingRoom = toDiscountRoom(
              room,
              hotel,
              metaHotel.hotelDispatchInfo,
              metaHotel.meta.enabledRoom,
              lookupUid,
              requestParams.experimentCarrier.isDynamicDownliftEnabled).copy(roomStatus = RatePlanStatus.Requested)
            roomId -> discountingRoom
          }
        }
      }

      val essContext =
        EssContext(countryId = Some(metaHotel.meta.countryId), ctx.baseRequest.whitelabelSetting.enablePublishPriceESS)
      val isHotelEligibleForPublishPriceESS =
        essService.isESSPublishPriceEligibleCountry(essContext,
                                                    ctx.determineVariant(ABTest.ESS_PUBLISH_PRICE) == Variant.B)

      def toDiscountingHotelDispatchInfo(hotelDispatchInfo: HotelDispatchInfo): DiscountingHotelDispatchInfo =
        DiscountingHotelDispatchInfo(hotelId = hotelDispatchInfo.hotelId,
                                     dispatchInfos = hotelDispatchInfo.dispatchInfos.map(toDiscountDispatchInfo))

      for {
        discountRooms <- discountRoomsF
        apsDiscountRooms <- apsDiscountRoomsF
      } yield DiscountHotel(
        hotelId = hotel.id,
        countryId = hotel.countryId,
        countryCode = metaHotel.meta.countryCode,
        cityId = hotel.cityId,
        isHotelPmcBlocklisted = hotel.isHotelPmcBlocklisted,
        discountRooms = discountRooms,
        apsDiscountRooms = apsDiscountRooms,
        chainId = hotel.chainId,
        isSingleRoomNHA = meta.isSingleRoomNHA,
        isNHAHotel = meta.isNHAHotel,
        asqTypeIds = meta.asqInfo.map(_.asqTypeId).toList,
        accommodationType = meta.accommodationTypes.headOption,
        apmProgramIds = soybeanHotelParams.apmProgramIds,
        apmHotelStatusId = soybeanHotelParams.apmHotelStatusId,
        masterHotelSupplierId = meta.masterDmcId,
        discountingHotelDispatchInfo = metaHotel.hotelDispatchInfo.map(toDiscountingHotelDispatchInfo),
        bookedRoomIds = soybeanHotelParams.bookedRoomIds,
        pseudoCouponMessage =
          if (ctx.baseRequest.isPriceStateRequest) {
            ctx.pseudoCouponMessage.map(toPseudoCouponMessage)
          } else None,
        isReady = hotel.isReady,
        isAps = apsRoomLookup.nonEmpty && !apsPeekSettings.blackListChainIds.contains(meta.chainId),
        skipPublishPriceESSOnPriceBasis = isHotelEligibleForPublishPriceESS,
      )
    }.toList

    for {
      discountingHotels <- Future.sequence(discountingHotelsF)
    } yield DiscountingRequest(
      requestParams = requestParams,
      discountHotels = discountingHotels,
      commonFlowContext = ctx,
    )
  }

  private def toDiscountDispatchInfo(dispatchInfo: DispatchInfo): DiscountingDispatchInfo = DiscountingDispatchInfo(
    hotelId = dispatchInfo.hotelId,
    dispatchDmc = dispatchInfo.dispatchDmc,
    dispatchedMasterChannel = toDiscountCompositeChannel(dispatchInfo.dispatchedMasterChannel),
    dispatchType = dispatchInfo.dispatchType,
    pricingActionFlags = dispatchInfo.pricingActionFlags,
    isPropertyBlocked = dispatchInfo.isPropertyBlocked,
    isSecretDealDispatched = dispatchInfo.isSecretDealDispatched,
    isSecretlyDispatched = dispatchInfo.isSecretlyDispatched,
    rateAvailabilityType =
      dispatchInfo.rateAvailabilityType.map(t => RateAvailabilityTypes.getRateAvailabilityType(t.i)),
    dispatchAvailabilityType =
      dispatchInfo.dispatchAvailabilityType.map(t => DispatchAvailabilityTypes.getDispatchAvailabilityType(t.i)),
    fencedRatePair = DiscountRequestMapper.toDiscountingFencedRatePair(dispatchInfo.fencedRatePair),
  )

  private def toDiscountRewardOptions(rewardOptions: models.pricing.RewardOptions): RewardOptions = {
    val options = rewardOptions.options.mapValues { rewardSet =>
      RewardSet(rewardSet.giftcard.map(toDiscountingGiftCard), rewardSet.cashback.map(toDiscountingCashback))
    }
    RewardOptions(options, rewardOptions.defaultOption)
  }

  private def getFinanceProductTypes(hotel: DFHotel, financeProductInfo: Option[FinanceProductInfo])(implicit
    ctx: FlowContext): Seq[FinanceProductType] = financeProductInfo.toSeq.flatMap { info =>
    Seq(
      info.products.smartFlex.map(s =>
        if (s.processReplacement) FinanceProductType.SMARTFLEX_REPLACEMENT
        else if (ESSHelper.isESSForSmartFlexAllowed(hotel)) FinanceProductType.SMARTFLEX_OFFER
        else FinanceProductType.SMARTFLEX_OFFER_NO_ESS),
      info.products.smartSaver.map(_ => FinanceProductType.SMARTSAVER_OFFER),
    ).flatten
  }

  private[discounting] def toDiscountRoom(room: Room,
                                          hotel: DFHotel,
                                          hotelDispatchInfo: Option[HotelDispatchInfo] = None,
                                          roomMetaData: Map[RoomTypeId, MasterRoomInfo] = Map.empty,
                                          lookupUidMap: Map[RoomUID, RoomIndex] = Map.empty,
                                          isDynamicDownliftEnabled: Boolean = false)(implicit
    ctx: FlowContext): DiscountRoom = {
    implicit val roomExperiments: RoomExperiments = RoomExperiments(hotel.features.isApplyNewOccupancyLogic)
    val baseRequest = ctx.baseRequest
    val precision = room.exchange.map(_.numLocalDecimal).getOrElse(defaultPrecision)
    val roomAllocationInfo = room.getRoomAllocationInfo
    val numberOfRooms =
      if (roomAllocationInfo.isEmpty) room.numRooms
      else roomAllocationInfo.keys.size
    val soybeanParams = toSoybeanRoomParams(room, hotel.id, hotel.reqOcc.isFreeOcc, hotelDispatchInfo)
    def toDiscountRateFence(rateFence: YplRateFence): DiscountRateFence =
      DiscountRateFence(rateFence.origin, rateFence.cid, rateFence.language)

    val roomStatus = RatePlanStatus.getFromValue(room.roomStatus.value)
    val isPriceStateRequest = baseRequest.isPriceStateRequest
    val isAvailableCapacityIncludeChildren = hotelDispatchInfo.exists(_.isAvailableCapacityIncludeChildren)
    val soybeanResponse = getSoybeanResponseFromDFRoom(isPriceStateRequest, room)

    // TODO: PMC-6270: Long-term solutions if Dynamic DL can be taken and expand outside IN origin
    // Either to change to save and send all sell prices using ref values, or avoid re-computing dynamic PA in SB
    val useRefPrices = isDynamicDownliftEnabled && isPriceStateRequest

    // TODO: migrate to call convert currency from dfFinance
    DiscountRoom(
      payOnBook = room.payOnBook(),
      extraBedSellIn = room.extraBedSellIn,
      isCashbackPromocodeEligible = room.isCashbackPromocodeEligible,
      isFireDrill = room.isFireDrill,
      currency = room.currency,
      paymentModel = room.paymentModel,
      soybeanResponse = soybeanResponse,
      precision = precision,
      numberOfRooms = numberOfRooms,
      numberOfNights = room.lengthOfStay,
      roomMargin = room.roomMargin,
      roomPriceBasis = getPriceBasisForCharge(room, baseRequest.occ, isPriceStateRequest, ChargeType.Room),
      mandatorySurchargePriceBasis =
        getPriceBasisForCharge(room, baseRequest.occ, isPriceStateRequest, ChargeType.Surcharge),
      mandatoryExtrabedPriceBasis =
        getPriceBasisForCharge(room, baseRequest.occ, isPriceStateRequest, ChargeType.ExtraBed),
      isMorp = room.roomFeatures.isAgodaAgency,
      japanCampaignSettings = getJapanCampaignSettings(room.exchange),
      ratePlan = room.ratePlan,
      cxlCode = room.cxlCode,
      marginPercentage = room.marginPercentage,
      sellIn = room.sellIn(useRefPrices),
      sellEx = room.sellEx(useRefPrices),
      usdSellEx = room.usdSellEx(useRefPrices),
      usdSellIn = room.usdSellIn(useRefPrices),
      reqSellEx = room.reqSellEx(useRefPrices),
      reqSellIn = room.reqSellIn(useRefPrices),
      usdMargin = room.usdMargin(useRefPrices),
      usdSellAllIn = room.usdSellAllIn(useRefPrices),
      inventoryType = room.rateCategory.flatMap(_.inventoryType),
      rateCategoryId = room.rateCategory.map(_.id),
      usdNetIn = room.usdNetIn,
      usdSurchargeSellInclusive = room.usdSurchargeSellInclusive(useRefPrices),
      usdSurchargeNetInclusive = room.usdSurchargeNetInclusive,
      usdTax = room.usdTax,
      usdFee = room.usdFee,
      usdNetEx = room.usdNetEx,
      usdPayOnBook = room.usdPayOnBook(useRefPrices),
      breakfast = room.breakfast,
      extraBed = room.extraBed,
      steakhouseDownlift = room.downlift.map(_.percent),
      promotionId = room.discountInfo.promotion.map(_.id),
      promotionTypeId = room.discountInfo.promotion.map(_.typeId),
      noCreditCard = room.paymentInfo.noCreditCard,
      rateModelType = getRateModel(room.rateCategory),
      isFit = room.isFit.getOrElse(false),
      isFitForLowerReqOcc = room.occupancyInfo.isFitForLowerReqOcc,
      usdMandatoryExtraBedSellEx = room.usdMandatoryExtraBedSellEx,
      usdMandatoryExtraBedMargin = room.usdMandatoryExtraBedMargin,
      isEasyCancel = room.isEasyCancel,
      paymentOptions = room.paymentOptions.map(_.id).toList,
      bookNowPayLater = room.payLater.map(_.payLater),
      usdMandatoryExtraBedSellIn = room.usdMandatoryExtraBedSellIn,
      usdMandatoryExtraBedNetIn = room.usdMandatoryExtraBedNetIn,
      preMappedMasterRoomId = room.preMappedMasterRoomId,
      masterRoomId =
        if (room.supplierId == DMC.HotelBeds) room.roomTypeId
        else room.getMasterRoomIdOrElseRoomTypeId,
      badRoomNameId = roomMetaData.get(room.getMasterRoomIdOrElseRoomTypeId).flatMap(_.badRoomNameId),
      rohFlag = roomMetaData.get(room.getMasterRoomIdOrElseRoomTypeId).map(_.rohFlag),
      isSuiteRoom = roomMetaData.get(room.getMasterRoomIdOrElseRoomTypeId).map(_.isSuiteRoom),
      supplierId = room.supplierId,
      roomTypeId = room.roomTypeId,
      roomStatus = roomStatus,
      reqSellAllIn = room.reqSellAllIn(useRefPrices),
      roomBenefits = room.benefits.map(b => DiscountRoomBenefit(b.id, b.value, b.groupId, b.remark)),
      prepaymentRequired = room.prepaymentRequired,
      bookingUId = room.roomExtraInfo.bookingUId.getOrElse(""),
      commissionEquivalent = room.commissionEquivalent,
      cancellationGroup = room.paymentInfo.cancellationGroup.value,
      isDomestic = Some(room.roomFeatures.isDomesticOnly),
      confirmByMins = room.roomExtraInfo.confirmByMins,
      isBcomHybridMC = room.roomFeatures.isBcomHybridMC,
      isPullSupplier = hotel.suppliers.get(room.supplierId).map(_.isPull),
      roomIdentifier = Some(room.symmetricUid(soybeanParams.isHashDmcUidToRoomIdentifier)),
      isAcr = DiscountRequestMapper.isACR(room.resellExternalData),
      cxlRelationRoomId = room.partialRefundInfo.flatMap(p => lookupUidMap.get(p.relationUID)),
      occupancy = soybeanParams.occupancy,
      isFreeCancellation = soybeanParams.isFreeCancellation,
      cancellationRank = soybeanParams.cancellationRank,
      agxCommission = soybeanParams.agxCommission,
      freeCancellationDateStr = soybeanParams.freeCancellationDateStr,
      cxlRelationRoomPriceActionType = soybeanParams.cxlRelationRoomPriceActionType,
      downliftFactor = soybeanParams.downliftFactor,
      stayPackageType = soybeanParams.stayPackageType,
      allotment = room.allotment,
      fencedRates = room.fences.map(toDiscountRateFence),
      origin = room.origin,
      discountCompositeChannel = room.compositeChannel.map(toDiscountCompositeChannel),
      isPromotionsAllowDownlift = room.discountInfo.isPromotionsAllowDownlift,
      isBookingRoom = ctx.baseRequest.isBookingRequest && room.existInUIDs(
        ctx.baseRequest.bookingFilter.map(bf => bf.uidList.map(_.uid)).getOrElse(List.empty)),
      adults = room.occ.adults,
      children = room.occ.children,
      offerType = room.offerType,
      mandatoryExtraBedSellExLocal = room.dfFinanceByCurrency.local.mandatoryExtraBedSellEx,
      mandatoryExtraBedMarginLocal = room.dfFinanceByCurrency.local.mandatoryExtraBedMargin,
      financeProductTypes = getFinanceProductTypes(hotel, room.financeProductInfo),
      pricingTokens =
        if (ctx.baseRequest.isBookingRequest) room.getPricingTokensInUIDs(
          ctx.baseRequest.bookingFilter.map(bf => bf.uidList.map(_.uid)).getOrElse(List.empty))
        else Map.empty,
      availableCapacity =
        if (isAvailableCapacityIncludeChildren) room.offerOccupancy.map(_.availableCapacity)
        else None,
      msePricingToken = room.msePricingToken,
      isMultipleRoomAssignmentPrice = room.roomFeatures.isMultipleRoomAssignmentPrice,
      priceAdjustmentId = PriceAdjustmentIdHelper.toSoybeanPriceAdjustmentId(room.pricingInfo.priceAdjustmentId),
      usdSupplierFundedDiscount = Some(room.usdSupplierFundedDiscount),
      // Only booking rooms when JP-3013=B in India origin having non-empty offerPricingActions returned from SB can be a Dynamic DL room.
      // Thus, preparing both post-DL and reference (pre-DL) price basis for price state requests.
      refRoomPriceBasis =
        getPriceBasisForCharge(room, baseRequest.occ, isPriceStateRequest, ChargeType.Room, useRefPrices = useRefPrices),
      refMandatorySurchargePriceBasis = getPriceBasisForCharge(room,
                                                               baseRequest.occ,
                                                               isPriceStateRequest,
                                                               ChargeType.Surcharge,
                                                               useRefPrices = useRefPrices),
      refMandatoryExtrabedPriceBasis = getPriceBasisForCharge(room,
                                                              baseRequest.occ,
                                                              isPriceStateRequest,
                                                              ChargeType.ExtraBed,
                                                              useRefPrices = useRefPrices),
      pricingActionTokens = room.dfFinanceByCurrency.local.discounts.map(_.pricingActionTokens).getOrElse(Map.empty),
    )
  }

  private[discounting] def getSoybeanResponseFromDFRoom(isPriceStateRequest: Boolean,
                                                        room: Room): Option[SoybeanResponse] =
    if (isPriceStateRequest) {
      val cashback = room.cashback
      val giftCard = room.giftCard
      val rewardOptions = room.rewardOptions
      val roomStatus = RatePlanStatus.getFromValue(room.roomStatus.value)
      val pricingMessage = room.pricingMessages.map(toPricingMessage)
      val downlift = room.downlift
      Some(
        SoybeanResponse(
          promoCode = None,
          cashback = cashback,
          giftCard = giftCard,
          alternativeRewards = Seq.empty,
          rewardOptions = rewardOptions,
          downlift = downlift,
          chargesToRemove = None,
          pseudoCouponShowBadge = None,
          streamlineGroupingKey = None,
          dispatchAvailabilityType = None,
          chargeDiscount = None,
          displayDiscount = None,
          pricingMessage = pricingMessage,
          roomStatus = roomStatus,
        ),
      )
    } else {
      None
    }

  private def toDiscountCompositeChannel(compChannel: DFCompositeChannel): DiscountCompositeChannel =
    DiscountCompositeChannel(
      baseChannelId = compChannel.baseChannelId,
      stackedChannelIds = compChannel.stackedChannelIds,
      compositeChannelId = compChannel.compositeChannelId,
    )

  private def toDiscountingFencedRateKey(key: FencedRateKey): DiscountingFencedRateKey =
    DiscountingFencedRateKey(origin = key.origin, siteId = key.siteId, language = key.language)

  private def toDiscountingFencedOriginObject(key: FencedOriginObject): DiscountingFencedOriginObject =
    DiscountingFencedOriginObject(ratePlans = key.ratePlans)

  private def toDiscountingFencedRatePair(fencedRatePair: FencedRatePair): DiscountingFencedRatePair =
    DiscountingFencedRatePair(key = toDiscountingFencedRateKey(fencedRatePair.key),
                              value = toDiscountingFencedOriginObject(fencedRatePair.value))

  private def isPricePeekFeatureEnabled(isPricePeekEnabled: Boolean)(implicit ctx: FlowContext): Boolean = {
    val isPromotionPeekAllowedOnBFOnly = ctx.baseRequest.whitelabelSetting.isPromotionPeekAllowedOnBFOnly

    isPricePeekEnabled && (ctx.baseRequest.isBookingRequest || !isPromotionPeekAllowedOnBFOnly)
  }

  private def getRebookInfo(reBookingRequest: Option[ReBookingRequest]): Option[RebookInfo] =
    reBookingRequest.flatMap { p =>
      p.originalSellIn.map { originalSellIn =>
        RebookInfo(
          cancelRebookOriginalSellin = originalSellIn,
          cancelRebookOriginalCashBack = p.cashbackAmount,
          cancelRebookOriginalPromoAmount = p.promoAmount.map(_ * -1), // Soybean works in positive numbers for promo
        )
      }
    }

  private[discounting] def toRequestParams(ctx: FlowContext) = {
    val baseRequest = ctx.baseRequest
    @SuppressWarnings(Array("stryker4s.mutation.EqualityOperator"))
    val experimentCarrier = ExperimentCarrier(
      isZeroCashbackLogicRemovalExperiment = ctx.determineVariant(ABTest.ZERO_CASHBACK_LOGIC_REMOVAL_EXP) == Variant.B,
      isDisableEmployeeDealForWeb = ctx.determineVariant(ABTest.DISABLE_EMPLOYEE_DEAL_FOR_WEB) == Variant.B,
      isSkipZeroAllotmentRoomForSoybeanOpenDoor =
        ctx.determineVariant(ABTest.SKIP_ZERO_ALLOTMENT_ROOMS_FOR_SOYBEAN_OPENDOOR) == Variant.B,
      isSkipZeroAllotmentRoomsForSoybean =
        ctx.determineVariant(ABTest.SKIP_ZERO_ALLOTMENT_ROOMS_FOR_SOYBEAN) == Variant.B,
      isPricePushFencing = ctx.determineVariant(ABTest.PRICE_PUSH_FENCING) == Variant.B,
      isRateChannelSwap = ctx.determineVariant(ABTest.RATE_CHANNEL_SWAP) == Variant.B,
      isZeroCashbackEnabled = ctx.experimentContext.isForcedOrSeenAsB(ABTest.ZERO_CASHBACK_ENABLED),
      isUnblockPriusInJP = ctx.determineVariant(ABTest.UNBLOCK_PRIUS_IN_JP) == Variant.B,
      isPmcSortCcByMaxDiscount = ctx.determineVariant(ABTest.PMC_SORT_CC_BY_MAX_DISCOUNT) == Variant.B,
      isConsolidatedDiscountsEnabled = ctx.determineVariant(ABTest.CONSOLIDATED_APPLIED_DISCOUNTS) == Variant.B,
      isConsolidatedPeekEnabled = ctx.determineVariant(ABTest.CONSOLIDATED_PEEK) == Variant.B,
      isSoybeanCircuitBreakerForAllRequestEnabled =
        ctx.determineVariant(ABTest.ENABLE_SOYBEAN_CIRCUIT_BREAKER_FOR_ALL_REQUEST) == Variant.B,
      applySoybeanStreamlineEvenWhenDownliftEntryMissing =
        ctx.determineVariant(ABTest.APPLY_SOYBEAN_STREAMLINING_EVEN_WHEN_DOWNLIFT_ENTRY_MISSING) == Variant.B,
      isConsolidatedDiscountsFix = ctx.determineVariant(ABTest.CONSOLIDATED_APPLIED_DISCOUNTS_FIX) == Variant.B,
      isConsolidatedDiscountOnlyAutoApplied =
        ctx.determineVariant(ABTest.CONSOLIDATED_APPLIED_DISCOUNTS_ONLY_AUTO_APPLY) == Variant.B,
      isAutoApplyRoundingFix = ctx.determineVariant(ABTest.AUTO_APPLY_ROUNDING_FIX) == Variant.B,
      isUpdateDFESearchPerformanceMetricTags =
        ctx.determineVariant(ABTest.PMC_UPDATE_DFE_SEARCH_PERFORMANCE_TAGS) == Variant.B,
      isGetPromoPriorityFromSoybean = ctx.determineVariant(ABTest.PMC_GET_PROMO_PRIORITY_FROM_SB) == Variant.B,
      isPmcHybridDiscountProcedure = ctx.determineVariant(ABTest.PMC_HYBRID_DISCOUNT_PROCEDURE) == Variant.B,
      // Cart still rely on promocode bug where we validate promocodes to one booking room but apply to all rooms.
      // This is fix in PMC-4311 but will cause cart swap room to fail since only the alternative room will not have
      // promocode applied.
      isPmcSupportOfferLevel = !baseRequest.isCartFeatureEnabled &&
        ctx.determineVariant(ABTest.PMC_SUPPORT_OFFER_LEVEL) == Variant.B,
      isPmcConsolidatedAppliedDiscountPriceDisplayType =
        ctx.determineVariant(ABTest.PMC_CONSOLIDATED_APPLIED_DISCOUNTS_PRICE_DISPLAY_TYPE) == Variant.B,
      isPmcAutoApplyPaymentMethodFixEnabled =
        ctx.determineVariant(ABTest.PMC_AUTO_APPLY_PAYMENT_METHOD_FIX) == Variant.B,
      isPmcStopFetchingCDBForEnrichment =
        ctx.determineVariant(ABTest.PMC_STOP_FETCHING_CDB_FOR_ENRICHMENT) == Variant.B,
      excludeApsForPrius = !ctx.baseRequest.isLogIn &&
        ctx.baseRequest.featureFlags.contains(FeatureFlag.APSPeek) &&
        ctx.baseRequest.priusID > 0 &&
        ctx.determineVariant(ABTest.EXCLUDE_APS_FOR_PRIUS) == Variant.B,
      deprecateClientDiscountFF = ctx.determineVariant(ABTest.PMC_DEPRECATE_CLIENT_DISCOUNT_FF) == Variant.B,
      mapAgencyRoomPromoCodeFailureReason =
        ctx.determineVariant(ABTest.MAP_AGENCY_ROOM_PROMOCODE_FAILURE_REASON) == Variant.B,
      isPmcAutoAssignPromoBugFixAndPriorityUpdate =
        ctx.determineVariant(ABTest.PMC_AUTO_ASSIGN_PROMO_BF_BUG_FIX_AND_PRIORITY_UPDATE) == Variant.B,
      isDynamicDownliftEnabled =
        ctx.baseRequest.shouldAllocateDynamicDLExp && ctx.determineVariant(ABTest.DYNAMIC_DOWNLIFT) == Variant.B,
      isPmcAutoApplyRateCampaign = ctx.determineVariant(ABTest.PMC_AUTO_APPLY_RATE_CAMPAIGN) == Variant.B,
      isPmcEnableRateCampaignMessageInfo =
        ctx.determineVariant(ABTest.PMC_ENABLE_RATE_CAMPAIGN_MESSAGE_INFO) == Variant.B,
      isPmcEnabledCheckNonEmptyPromocode =
        ctx.determineVariant(ABTest.PMC_ENABLE_CHECK_NON_EMPTY_PROMOCODE) == Variant.B,
      isPmcEnableRateCampaignFeedback = ctx.determineVariant(ABTest.PMC_ENABLE_RATE_CAMPAIGN_FEEDBACK) == Variant.B,
      isPmcFixFencesComparingRooms = ctx.baseRequest.shouldAllocatePmcFixFencesComparingRooms && ctx.determineVariant(
        ABTest.PMC_FIX_FENCES_COMPARING_ROOMS) == Variant.B,
      isEmiFencingExperimentOn = ctx.determineVariant(ABTest.EMI_FENCING_FOR_CREDIT_CARD_OFFERS) == Variant.B,
      isPmcEnableValueTag =
        ctx.baseRequest.shouldAllocateValueTag && ctx.determineVariant(ABTest.PMC_ENABLE_VALUE_TAG) == Variant.B,
    )

    def toDiscountRequestFromClient(
      discountRequest: Option[starfruit.DiscountRequest],
    ): Option[DiscountRequestFromClient] = {

      def toCampaignInfo(campaignInfo: starfruit.CampaignInfo) = {
        val childPromotions = campaignInfo.childPromotions.map(_.map { childPromo =>
          ChildPromotion(
            campaignId = childPromo.campaignId,
            promotionCode = childPromo.promotionCode,
            discountAmount = childPromo.discountAmount,
            localDiscountAmount = childPromo.localDiscountAmount,
          )
        })
        CampaignInfo(
          id = campaignInfo.id,
          cid = campaignInfo.cid,
          promotionCode = campaignInfo.promotionCode,
          promotionId = campaignInfo.promotionId,
          childPromotions = childPromotions,
          description = campaignInfo.description,
          isMarkUse = campaignInfo.isMarkUse,
          campaignType = campaignInfo.campaignType,
        )
      }

      val campaignInfo = discountRequest.flatMap(_.campaignInfo).map(toCampaignInfo)
      val campaignInfos = discountRequest.flatMap(_.campaignInfos).map(_.map(toCampaignInfo))
      val email = discountRequest.flatMap(_.email)
      Some(DiscountRequestFromClient(campaignInfo, email, campaignInfos))
    }

    // This Experiment and Feature Flag are removed and inserted relating to Default Campaign Card on App - This condition is required to be true for credit card to be auto applied
    val isLandingCcCid = baseRequest.experiments.exists(_.name == ABTest.CC_CAMP_AUTO_APPLY_LANDING_CID_ON_APP) ||
      baseRequest.featureFlags.contains(FeatureFlag.AutoApplyCreditCardOnLandingCid)
    val isDeprecateClientDiscountFFOnMobileApp = baseRequest.platformId == PlatformID.MobileAPI &&
      ctx.determineVariant(ABTest.PMC_DEPRECATE_CLIENT_DISCOUNT_FF) == Variant.B
    val whiteLabelDiscountSetting = WhitelabelDiscountSetting(
      id = baseRequest.whiteLabeLID,
      couponSetting = Coupon(baseRequest.whitelabelSetting.couponSetting.paymentModels),
    )
    val clientInfo = ClientInfo(
      affiliateId = baseRequest.cInfo.affiliateId,
      cid = baseRequest.cInfo.cid,
      languageId = baseRequest.cInfo.language,
      platformId = baseRequest.platformId,
      origin = baseRequest.cInfo.origin,
      storeFront = baseRequest.cInfo.storeFront.getOrElse(0),
      languageUse = baseRequest.cInfo.languageUse,
      locale = baseRequest.cInfo.locale,
      deviceTypeId = baseRequest.cInfo.deviceTypeId,
    )
    val paymentInfo = baseRequest.paymentInfo.map { paymentInfo =>
      val discountingCcInfo = paymentInfo.creditCardInfo.map(info => DiscountingCreditCardInfo(info.ccof, info.ccToken))
      val discountingInstallmentInfo = paymentInfo.installmentInfo.map(info =>
        DiscountingInstallmentInfo(info.installmentPlanCode, info.isInstallmentPayment))
      DiscountingPaymentInfo(
        creditCardInfo = discountingCcInfo,
        paymentMethod = paymentInfo.paymentMethod,
        installmentInfo = discountingInstallmentInfo,
        paymentOption = paymentInfo.paymentOption,
      )
    }
    val soybeanRequestParams = toSoybeanRequestParams(ctx)
    val discountingFencedRatePairs = baseRequest.fencedRatePairs.map(_.map(toDiscountingFencedRatePair))
    val featureRequest = toFeatureRequest(baseRequest.featureRequest)
    val externalLoyaltyProfile = baseRequest.externalLoyaltyProfile.map(info =>
      DiscountingExternalLoyaltyProfile(info.externalProgramId,
                                        info.loyaltyAccountNumber,
                                        info.remainingBenefitCredit,
                                        info.promocode))

    val externalLoyaltyRequestLoyaltySearchType =
      ctx.baseRequest.externalLoyaltyRequest.flatMap(_.loyaltySearchType.map(_.toUpperCase))

    val soybeanRequestLoyaltySearchType =
      externalLoyaltyRequestLoyaltySearchType.flatMap(LoyaltySearchType.upperCaseNameValuesToMap.get(_))

    val isMaxDownliftRemovalEnabled = maxDownliftRemovalSettingsProducer.current.isEnabled

    def toPriceDisplayType(requiredBasis: Option[ApplyType]): PriceDisplayType = requiredBasis match {
      case Some(ApplyTypes.PRPB) => PriceDisplayTypes.PerRoom
      case Some(ApplyTypes.PRPN) => PriceDisplayTypes.PerRoomPerNight
      case Some(ApplyTypes.PN) => PriceDisplayTypes.PerNight
      case _ => PriceDisplayTypes.PerBook
    }

    RequestParams(
      isLandingCcCid = isLandingCcCid,
      isPackagingFunnelRequest = PackagingRequestHelper.isPackagingFunnelRequest(ctx),
      isCartFeatureEnabled = baseRequest.isCartFeatureEnabled,
      isBookingRequest = baseRequest.isBookingRequest,
      isCreditCardPresent = baseRequest.isCreditCardPresent,
      bookingDate = baseRequest.bookingDate,
      checkIn = baseRequest.checkIn,
      userSelectedOption = baseRequest.selectedRewardOptions,
      searchId = baseRequest.searchId,
      paymentId = baseRequest.paymentTypeId,
      needPromotionPricePeek = baseRequest.regulationFeatureEnabledSetting.forall(x =>
        isPricePeekFeatureEnabled(x.isPricePeekEnabled)(ctx) || x.isClaimPromotionEnabled),
      cidsInCache = getDataForFiredrill.cids,
      featureFlags = baseRequest.featureFlags.map(fromDfFeatureFlag)(collection.breakOut),
      experimentCarrier = experimentCarrier,
      discountRequestFromClient = toDiscountRequestFromClient(baseRequest.discountRequest),
      isCCAutoApply = baseRequest.featureFlags.contains(FeatureFlag.CreditCardPromotion),
      isAutoApplyPromoApplyFirst = baseRequest.regulationFeatureEnabledSetting.exists(_.isAutoApplyPromoApplyFirst),
      isAutoApplyAllPromosEnabled = baseRequest.regulationFeatureEnabledSetting.exists(_.isAutoApplyAllPromosEnabled),
      whitelabelDiscountSetting = whiteLabelDiscountSetting,
      clientInfo = clientInfo,
      channels = baseRequest.channels.map(toDiscountCompositeChannel),
      soybeanFeatureFlags = soybeanRequestParams.featureFlags,
      trafficInfo = soybeanRequestParams.trafficInfo,
      clientCampaignCid = soybeanRequestParams.clientCampaignCid,
      checkOut = baseRequest.checkOut,
      lengthOfStay = baseRequest.lengthOfStay,
      isLogin = baseRequest.isLogIn,
      currency = baseRequest.currency,
      adults = baseRequest.occ.adults,
      children = baseRequest.occ.children,
      rooms = baseRequest.occ.rooms,
      userId = soybeanRequestParams.userId,
      memberId = soybeanRequestParams.memberId,
      isApsEnabled = baseRequest.flagInfo.isApsEnabled,
      isAPSPeek = baseRequest.flagInfo.isAPSPeek,
      bTests = baseRequest.bTests.toIndexedSeq,
      searchType = baseRequest.searchType.i,
      refId = baseRequest.refId,
      isPageSearchRequest = baseRequest.isPageSearchRequest,
      isMSE = baseRequest.flagInfo.isMSE,
      isPricingBot = baseRequest.trafficInfo.isPricingBot,
      ipAddress = baseRequest.cInfo.ipAddress,
      userContext = baseRequest.userContext,
      supplierIds = baseRequest.supplierIds.toList,
      stateId = baseRequest.stateId,
      experiments = soybeanRequestParams.experiments,
      isMultiFunnelStreamLine = soybeanRequestParams.isMultiFunnelStreamLine,
      paymentInfo = paymentInfo,
      whiteLabelId = baseRequest.whiteLabeLID,
      isBot = baseRequest.trafficInfo.isBot,
      fencedRatePairs = discountingFencedRatePairs,
      isRateChannelSwap = baseRequest.isRateChannelSwap,
      featureRequest = featureRequest,
      isRoomIdentifierFilterRequest = baseRequest.isRoomIdentifierFilterRequest,
      externalLoyaltyProfile = externalLoyaltyProfile,
      isPriceStateRequest = baseRequest.isPriceStateRequest,
      priusId = baseRequest.priusID,
      isRPM2Included = baseRequest.flagInfo.isRPM2Included,
      loyaltySearchType = soybeanRequestLoyaltySearchType,
      rebookInfo = getRebookInfo(baseRequest.reBookingRequest),
      soybeanContexts = baseRequest.priceMetaData,
      priceDisplayType = toPriceDisplayType(baseRequest.requiredBasis),
      isConsolidatedDiscountUserOverridePriceDisplay =
        baseRequest.regulationFeatureEnabledSetting.exists(_.isConsolidatedDiscountUserOverridePriceDisplay),
      isMaxDownliftRemovalEnabled = isMaxDownliftRemovalEnabled,
      isRurubuWl = ctx.baseRequest.whitelabelSetting.isRurubuWl,
      isJapanicanWl = ctx.baseRequest.whitelabelSetting.isJapanicanWl,
      isSkipDownliftForCxlReBookV3 = baseRequest.isRebookingRequestV3,
      isDeprecateClientDiscountFFOnMobileApp = isDeprecateClientDiscountFFOnMobileApp,
      correlationId = baseRequest.correlationId,
    )
  }

  private[discounting] def getJapanCampaignSettings(exchange: Option[ExchangeRate]): JapanCampaignSettings = {
    val jpyCurrencyCode = "JPY"

    val settings = japanCampaignSettingsProducer.current
    val currencyCode = exchange.map(_.request).getOrElse("")
    val exchangeRate = pricingParameters.getExchangeRate(jpyCurrencyCode, currencyCode)
    val precision = exchange.map(_.numLocalDecimal).getOrElse(defaultPrecision)

    def convertToRequestedCurrency(value: Double) =
      ConvertCurrencyAndRoundingService.round(value, exchangeRate, precision)

    JapanCampaignSettings(
      peakDayMinimumBookingValue = convertToRequestedCurrency(settings.peakDayMinimumBookingValue),
      normalDayVoucherAllowance = settings.normalDayVoucherAllowance,
      peakDayVoucherAllowance = settings.peakDayVoucherAllowance,
      overrideCampaigns = settings.overrideCampaigns.mapValues { c =>
        OverrideCampaignSettings(
          peakDayMinimumBookingValue = c.peakDayMinimumBookingValue.map(convertToRequestedCurrency),
          normalDayVoucherAllowance = c.normalDayVoucherAllowance,
          peakDayVoucherAllowance = c.peakDayVoucherAllowance,
        )
      },
    )
  }

  private[discounting] def getPriceBasisForCharge(room: Room,
                                                  requestOccupancyInfo: OccInfo,
                                                  isPriceStateRequest: Boolean,
                                                  chargeType: ChargeType,
                                                  useRefPrices: Boolean = false): Seq[PriceBasis] = {
    val roomAllocationInfo = room.getRoomAllocationInfo
    val roomOccupancy = room.occupancyInfo.occ
    val numberOfRooms = room.numRooms
    val localCurrencyDFFinance = room.dfFinanceByCurrency.local

    val prices = chargeType match {
      case ChargeType.Surcharge => (if (useRefPrices) localCurrencyDFFinance.refSurchargePrices
                                    else localCurrencyDFFinance.surchargePrices).filter(surcharge =>
          surcharge.isMandatory && surcharge.margin > 0)
      case ChargeType.ExtraBed => (if (useRefPrices) localCurrencyDFFinance.refExtraBedPrices
                                   else localCurrencyDFFinance.extraBedPrices).filter(_.isMandatory)
      case ChargeType.Room =>
        if (useRefPrices) localCurrencyDFFinance.refRoomPrices else localCurrencyDFFinance.roomPrices
      case _ => Nil
    }

    // get number of adults from roomOccupancy and requestOccupancyInfo, everything will be of adult here.
    val numberOfAdults = PromotionHelper.getEligibleAdultCount(roomOccupancy,
                                                               requestOccupancyInfo.adults,
                                                               requestOccupancyInfo.children,
                                                               numberOfRooms)

    val rawPriceBasis = prices.map { pTmp =>
      val deltaTodayRefSell = pTmp.resellRefSell match {
        case Some(resellRefSell) => resellRefSell - pTmp.refSellInclusive
        case _ => 0
      }
      val pricePerDay = Price(
        exc = pTmp.sellExclusive + deltaTodayRefSell,
        inc = pTmp.sellInclusive + deltaTodayRefSell,
      )

      val subTypeId =
        if (pTmp.subChargeType != SubChargeType.None) pTmp.subChargeType
        else SubChargeType.Adult
      // Will round after apply downlift
      val price = roundPricePerDay(pricePerDay,
                                   room.exchange.map(_.numLocalDecimal).getOrElse(defaultPrecision),
                                   shouldRoundPrice = isPriceStateRequest && !useRefPrices)

      val downliftFactor: Double = calculateDownliftOnMarginFactor(pTmp.margin, pTmp.processingFee)

      PriceBasis(
        price,
        subTypeId,
        pTmp.roomNumber.getOrElse(defaultRoomNumber),
        pTmp.date,
        numberOfAdults,
        pTmp.quantity,
        downliftToMarginFactor = downliftFactor,
      )
    }

    val mergedPriceBasis = rawPriceBasis.merge

    val priceBasisBreakdowns =
      if (roomAllocationInfo.isEmpty) {
        mergedPriceBasis.map { pb =>
          pb.copy(
            price = pb.price.multiply(pb.quantity),
            quantity = 1,
          )
        }
      } else {
        getPriceBasisWithRoomAllocation(mergedPriceBasis, roomAllocationInfo)
      }

    priceBasisBreakdowns
  }

  private[discounting] def roundPricePerDay(pricePerDay: Price, precision: Int, shouldRoundPrice: Boolean): Price =
    if (shouldRoundPrice) {
      Price(
        pricePerDay.exc.roundp(precision),
        pricePerDay.inc.roundp(precision),
      )
    } else {
      Price(
        pricePerDay.exc,
        pricePerDay.inc,
      )
    }

  private def getPriceBasisWithRoomAllocation(
    priceBasisList: Seq[PriceBasis],
    roomAllocationInfo: Map[RoomNumber, DfRoomAllocation]): Seq[PriceBasis] = {
    val priceBasisMapByRoom = priceBasisList.groupBy(_.roomNumber)
    roomAllocationInfo.flatMap { case (roomKey, roomAllocation) =>
      val priceBasisByRoom = priceBasisMapByRoom.getOrElse(roomKey, priceBasisList)
      val priceBasisMapBySubType = priceBasisByRoom.groupBy(_.subTypeId)
      val childrenTypeMap = roomAllocation.childrenTypes
      priceBasisMapBySubType.flatMap { case (subTypeKey, priceBasisBySubType) =>
        val adultSubChargeTypes: Set[SubChargeType] = Set(SubChargeType.Adult, SubChargeType.None)
        val occupancyCountPerSubTypeOpt =
          if (adultSubChargeTypes.contains(subTypeKey)) {
            Some(roomAllocation.adults)
          } else {
            val childRateType = ChildRateType.getFromSubChargeType(subTypeKey.toString)
            childrenTypeMap.get(childRateType)
          }
        occupancyCountPerSubTypeOpt
          .map(occupancyCountPerSubType =>
            priceBasisBySubType.map { pb =>
              pb.copy(
                roomNumber = roomKey,
                occupancyCount = occupancyCountPerSubType,
                quantity = 1,
              )
            })
          .getOrElse(Nil)
      }
    }(collection.breakOut)
  }
}

object DiscountRequestMapper {

  def toDiscountingCashback(cashback: DFCashBack): Cashback = Cashback(
    cashback.cashbackGuid,
    cashback.earnId,
    cashback.percent,
    cashback.dayToEarn,
    cashback.expiryDay,
    cashback.showPostCashbackPrice,
    cashback.cashbackVersion,
    cashback.cashbackType,
    cashback.tcUrl,
    cashback.rewardTypeId,
    cashback.usePostCashbackPriceForDiscountPercent,
    cashback.agodaCashbackPercent,
    cashback.isCofundedEligible,
    cashback.isPromocodeEligible,
    cashback.appliedCampaignId,
    cashback.appliedCampaignName,
  )

  def toDiscountingGiftCard(giftCard: DFGiftCard): GiftCard = GiftCard(
    percent = giftCard.percent,
    dayToEarn = giftCard.dayToEarn,
    showBadge = giftCard.showBadge,
    showPostGiftcardPrice = giftCard.showPostGiftcardPrice,
    giftcardGuid = giftCard.giftcardGuid,
    earnId = giftCard.earnId,
    expiryDay = giftCard.expiryDay,
    guaranteedPercent = giftCard.guaranteedPercent,
    alternativeBasePercent = giftCard.alternativeBasePercent,
  )

  private def toPricingMessage(pricingMessage: SFPricingMessage): PricingMessage = {
    val texts = pricingMessage.texts.map(t => PricingMessageText(t.componentId, t.cmsIds))
    val parameters = pricingMessage.parameters.map(p =>
      PricingMessageRoomParameter(
        p.value,
        PricingMessageVariableType.getVariableType(p.variableType.i),
        p.placeHolder,
        PricingMessageLocalizationType.getVariableType(p.localizationType.i),
      ))
    PricingMessage(texts, parameters)
  }

  private def toPseudoCouponMessage(pseudoCoupon: DFPseudoCouponMessage): PseudoCouponMessage =
    PseudoCouponMessage(pseudoCoupon.code, pseudoCoupon.cmsID, pseudoCoupon.downlift)

  private[mappers] def getRateModel(rateCategory: Option[RateCategory]) =
    if (rateCategory.forall(_.id == 0)) RateModel.Old
    else RateModel.New

  private def toDiscountingFencedRateKey(key: YplRateFence): DiscountingFencedRateKey =
    DiscountingFencedRateKey(origin = Some(key.origin), siteId = Some(key.cid), language = Some(key.language))

  private def toDiscountingFencedOriginObject(
    key: com.agoda.papi.pricing.supply.models.request.FencedOriginObject): DiscountingFencedOriginObject =
    DiscountingFencedOriginObject(ratePlans = key.ratePlans)

  private[mappers] def toDiscountingFencedRatePair(
    fencedRatePair: com.agoda.papi.pricing.supply.models.request.FencedRatePair): DiscountingFencedRatePair =
    DiscountingFencedRatePair(key = toDiscountingFencedRateKey(fencedRatePair.key),
                              value = toDiscountingFencedOriginObject(fencedRatePair.value))

  private[mappers] def isACR(resellExternalData: Option[ResellExternalData]) =
    Some(resellExternalData.exists(_.sourceBookingId.nonEmpty))
}
