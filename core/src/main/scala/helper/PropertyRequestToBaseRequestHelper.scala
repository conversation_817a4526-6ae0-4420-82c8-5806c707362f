package helper

import api.request
import api.request.simulation.SimulationRequestData
import api.request.{
  BaseRequest,
  Children,
  CreditCardInfo,
  DFExperimentInfo,
  FeatureFlag,
  FeatureFlagImplicitHelper,
  FlagInfo,
  LoyaltyInfo,
  OccInfo,
  PaymentInfo,
  SupplierPullMetadata,
  TrafficInfo,
  ChildAgeRangeAssignment => DFChildAgeRangeAssignment,
  ChildType => DFChildType,
  FeatureRequest => DFFeatureRequest,
  OccFilter => DFOccFilter,
  ReBookingRequest => DFReBookingREquest,
  RoomAssignment => DFRoomAssignment,
}
import models.starfruit.{ClientInfo, _}
import com.agoda.papi.enums.hotel.PaymentModel
import com.agoda.papi.enums.simulation.{SupplyEquitySimulationMode, SupplyEquitySimulationSide}
import com.agoda.papi.ypl.models.{YplChannel => DFCompositeChannel, YplMasterChannel => DFMasterChannel}
import com.agoda.styx.botprofile.{BotProfileParser, BotTypes}
import com.agoda.usercontext.UserContextParserImpl
import logic.filters.BookingFilterMapper
import models.db.NoOfBedrooms
import models.enums.ReBookingActionType
import models.{AbTest, DFExperiment, PrecheckAccuracies, SearchTypes}
import models.flow.FeatureDegradationContext
import models.pricing.enums.{AlternativeRoomType, AlternativeRoomTypes}
import models.starfruit.{
  ContextRequest,
  FeatureRequest,
  FilterRequest,
  PricingRequest,
  PropertySearchRequest,
  SupplyEquitySimulationParameters,
}
import models.whitelabel.{RegulationFeatureEnabledSetting, WhitelabelSetting}

object PropertyRequestToBaseRequestHelper {
  // Create parser, in this case, with VOLUME and VOLUME_HIDDEN marked as UnknownBot bots.
  private val botParser: BotProfileParser = BotProfileParser(Seq(BotTypes.VOLUME, BotTypes.VOLUME_HIDDEN))

  private[helper] def getAlternativeType(featureFlags: List[FeatureFlag], isBookinRequest: Boolean): AlternativeRoomType = {
    import FeatureFlagImplicitHelper._
    val alternativeRoomTypePriority = FeatureFlagImplicitHelper.alternativeRoomTypeFeaturePriority
    alternativeRoomTypePriority
      .find(isBookinRequest && featureFlags.contains(_))
      .map(_.toAlternativeType)
      .getOrElse(AlternativeRoomTypes.NotRequire)
  }

  private def occupancyToBaseRequestOccupancy(request: PropertySearchRequest): OccInfo = {
    val occ = request.pricing.occupancy

    val childAges = Option(occ.childAges).getOrElse(Nil)
    val childrenType = occ.childrenTypes.getOrElse(List.empty).map(c => DFChildType(c.childRateType, c.quantity))

    val children = childAges.isEmpty match {
      case false => Some(Children(occ.childAges, childrenType))
      case true if occ.children > 0 => Some(Children(occ.children, None, childrenType))
      case _ => None
    }

    val occFilter = occ.occFilter.map(of => DFOccFilter(of.minOcc, of.maxOcc, of.occOrder))

    val roomAssignment = occ.roomsAssignment.getOrElse(List.empty).map { r =>
      val childAgeRangeAssignment =
        if (r.childrenAgeRange.isEmpty) {
          r.childrenTypes.map(c => DFChildAgeRangeAssignment(c.childRateType.value, c.quantity))
        } else {
          r.childrenAgeRange.getOrElse(List.empty).map(c => DFChildAgeRangeAssignment(c.childAgeRangeId, c.quantity))
        }

      DFRoomAssignment(r.adults,
                       r.childrenTypes.map(c => DFChildType(c.childRateType, c.quantity)),
                       childAgeRangeAssignment)
    }

    OccInfo(Some(occ.adults), children, Some(occ.rooms), occFilter, roomAssignment)
  }

  private def mapFlagInfo(requestContext: ContextRequest,
                          forcedDMC: Boolean,
                          pricingFeatures: FeatureRequest,
                          pricingFilters: FilterRequest) = FlagInfo(
    isApsEnabled = pricingFeatures.isApsEnabled,
    filterAPO = pricingFilters.filterAPO,
    allowOverrideOccupancy = pricingFeatures.overrideOccupancy,
    isRPM2Included = pricingFeatures.isRPM2Included,
    enableCor = pricingFeatures.enableCOR.getOrElse(true),
    isAPSPeek = pricingFeatures.isAPSPeek,
    isSync = pricingFeatures.synchronous,
    isAllOcc = pricingFeatures.isAllOcc,
    forceDmcResult = forcedDMC,
    isMSE = pricingFeatures.isMSE.getOrElse(false),
    isAllowRoomTypeNotGuarantee = pricingFeatures.isAllowRoomTypeNotGuarantee.getOrElse(false),
    isUsingHotelCurrency = pricingFeatures.isUsingHotelCurrency.getOrElse(false),
    isEnableSupplierFinancialInfo = pricingFeatures.isEnableSupplierFinancialInfo.getOrElse(false),
    isSnapshot = requestContext.isSnapshot.getOrElse(false),
    isManualTracingEnabled = requestContext.isManualTracingEnabled.getOrElse(false),
  )

  private def mapClientInfo(clientInfo: ClientInfo) = api.request.ClientInfo(
    language = clientInfo.languageId,
    platform = Option(clientInfo.platform),
    cid = Option(clientInfo.cid),
    storeFront = Option(clientInfo.storefront),
    origin = Option(clientInfo.origin),
    deviceTypeId = clientInfo.deviceTypeId,
    ipAddress = clientInfo.ipAddress,
    affiliateId = clientInfo.affiliateId,
    locale = clientInfo.locale,
    languageUse = clientInfo.languageUse,
    externalPartnerId = clientInfo.externalPartnerId,
  )

  private def mapSessionInfo(requestContext: ContextRequest, clientInfo: ClientInfo): Option[request.SessionInfo] =
    Option(
      api.request.SessionInfo(userId = Option(clientInfo.userId),
                              memberId = Option(requestContext.sessionInfo.memberId),
                              isLogin = Option(requestContext.sessionInfo.isLogin)))

  private def mapLoyalty(requestContext: ContextRequest) = Option(
    LoyaltyInfo(
      isLogin = Option(requestContext.sessionInfo.isLogin),
      vipLevel = UserContextParserImpl
        .parse(requestContext.userContext.getOrElse(""))
        .loyaltyProfile
        .flatMap(_.loyaltyProgramInfo.map(_.loyaltyLevel)),
    ))

  private[helper] def getPaymentInfo(isBookingRequest: Boolean, pricingRequest: PricingRequest): Option[PaymentInfo] =
    if (!isBookingRequest) {
      None
    } else {
      pricingRequest.payment.map { p =>
        PaymentInfo(
          creditCardInfo = p.creditCard.map { c =>
            CreditCardInfo(ccof = c.ccOf, ccToken = c.ccToken)
          },
          paymentMethod = pricingRequest.paymentId,
          installmentInfo = p.installmentInfo.map { i =>
            InstallmentInfo(installmentPlanCode = i.installmentPlanCode, isInstallmentPayment = i.isInstallmentPayment)
          },
          customerTaxCountryCode = p.customerTaxCountryCode,
          paymentOption = p.paymentOption,
        )
      }
    }

  private def mapFeatureRequest(pricingRequest: PricingRequest,
                                maxSuggestions: NoOfBedrooms,
                                unsupportedPaymentModels: Set[PaymentModel]) = DFFeatureRequest(
    maxSuggestions = maxSuggestions,
    isRatePartnerSummaries = pricingRequest.partner.flatMap(_.ratePartnerSummaries).getOrElse(false),
    mseHotelIds = pricingRequest.mseHotelIds.getOrElse(List.empty),
    mseClicked = pricingRequest.mseClicked,
    ppLandingHotelIds = pricingRequest.ppLandingHotelIds.getOrElse(List.empty),
    searchedHotelIds = pricingRequest.searchedHotelIds.getOrElse(List.empty),
    clientCampaignInfos = pricingRequest.clientCampaignInfos.getOrElse(List.empty),
    unsupportedPaymentModels = unsupportedPaymentModels,
    enableRatePlanCheckInCheckOut = pricingRequest.features.enableRatePlanCheckInCheckOut,
    showCouponAmountInUserCurrency = pricingRequest.features.showCouponAmountInUserCurrency,
    excludeVoucherRooms = pricingRequest.features.excludeVoucherRooms,
    enablePushDayUseRates = pricingRequest.features.enablePushDayUseRates,
    enableEscapePackage = pricingRequest.features.enableEscapesPackage,
    filterCheapestRoomEscapePackage = pricingRequest.features.filterCheapestRoomEscapesPackage,
    enableSecretDealImprovement = pricingRequest.features.enableSecretDealImprovement,
    enableCreditCardCampaignPeek = pricingRequest.features.enableCreditCardCampaignPeek,
    calculateRareRoomBadge = pricingRequest.features.calculateRareRoomBadge,
    enableCxlAversion = pricingRequest.features.enableCxlAversion,
    enableDownliftForCxlAversion = pricingRequest.features.enableDownliftForCxlAversion,
    enableRichContentOffer = pricingRequest.features.enableRichContentOffer,
    getAlternativeRoom = pricingRequest.features.getAlternativeRoom,
    enableReturnNonApprovedEscapes = pricingRequest.features.enableReturnNonApprovedEscapes,
    disableEscapesPackage = pricingRequest.features.disableEscapesPackage,
    returnCheapestEscapesOfferOnSSR = pricingRequest.features.returnCheapestEscapesOfferOnSSR,
    enableBenefitValuationForASO = pricingRequest.features.enableBenefitValuationForASO,
    shouldAddPFInMarketingFee = pricingRequest.features.shouldAddPFInMarketingFee,
    enableDayUseCor = pricingRequest.features.enableDayUseCor,
    showCheapestHourlyRate = pricingRequest.features.showCheapestHourlyRate,
    showPastMidnightSlots = pricingRequest.features.showPastMidnightSlots,
    sendCancellationSurcharge = pricingRequest.features.sendCancellationSurcharge,
    enableHourlySlotsForDayuseInOvernight = pricingRequest.features.enableHourlySlotsForDayuseInOvernight,
    enableThirtyMinsSlots = pricingRequest.features.enableThirtyMinsSlots,
    sortByCheckInTimeDayUseSSR = pricingRequest.features.sortByCheckInTimeDayUseSSR,
    mergeDayUseOffersWithOvernight = pricingRequest.features.mergeDayUseOffersWithOvernight,
    ignoreRoomsCountForNha = pricingRequest.features.ignoreRoomsCountForNha,
  )

  def toBaseRequest(request: PropertySearchRequest,
                    whiteLabelSetting: WhitelabelSetting,
                    simulationRequestData: Option[SimulationRequestData] = None,
                    agEnv: Option[String] = None,
                    regulationFeatureEnabledSetting: Option[RegulationFeatureEnabledSetting] = None,
                    featureDegradationContext: FeatureDegradationContext = FeatureDegradationContext.default,
                    channels: Set[DFCompositeChannel] = Set(DFMasterChannel.RTL),
                    supplyEquitySimulationMode: Option[SupplyEquitySimulationMode] = None,
                    supplyEquitySimulationParameters: Option[SupplyEquitySimulationParameters] = None,
                    supplyEquitySimulationSide: Option[SupplyEquitySimulationSide] = None,
                    agCorrelationId: Option[String] = None,
                    agAnalyticsSessionId: Option[String] = None,
                    agOriginState: Option[String] = None): BaseRequest = {
    val requestContext: ContextRequest = request.context
    val clientInfo = requestContext.clientInfo
    val pricingRequest = request.pricing

    val experiments = requestContext.experiment.map(x => DFExperiment(x.name, x.variant))
    val abTests = requestContext.abTests.map(x => AbTest(x.abUser, x.testId))
    val isBookingRequest = request.booking.isDefined

    val (searchType, forcedDMC) =
      if (isBookingRequest) (SearchTypes.HotelForBooking, true)
      else (SearchTypes.HotelSearch, false)

    val pricingFeatures = pricingRequest.features

    val requestedPrice = request.getRequestedPrice

    // TODO: remove the deprecated FeatureFlag.RoomSuggestion
    val maxSuggestions =
      (pricingFeatures.maxSuggestions, pricingRequest.featureFlag.contains(FeatureFlag.RoomSuggestion)) match {
        case (Some(limit), _) if limit > 0 => limit
        case (_, true) => 1
        case _ => 0
      }

    val featureFlags =
      if (pricingRequest.featureFlag != null) pricingRequest.featureFlag
      else Nil
    val featureFlagsPlus =
      if (isBookingRequest) featureFlags ++ List(FeatureFlag.StackChannelDiscount)
      else featureFlags

    val experimentInfo = requestContext.experimentInfo.map { exp =>
      DFExperimentInfo(
        trafficGroup = exp.trafficGroup,
        overridenAllocationVariantMap = exp.fetchAllocationVariant(),
        forceUserVariant = exp.forceUserVariant,
        forceOnIntegrationRun = exp.forceOnIntegrationRun,
        forceOnZeroTraffic = exp.forceOnZeroTraffic,
      )
    }

    val alternativeRoomType = getAlternativeType(featureFlags, isBookingRequest)

    val botProfile = clientInfo.trafficData.flatMap(_.rawBotProfile).map(botParser.parse)
    val trafficInfo = TrafficInfo(botProfile)

    val userContext = requestContext.userContext
    val pricingFilters = pricingRequest.filters

    val unsupportedPaymentModels: Set[PaymentModel] = pricingFilters.unsupportedPaymentModels
      .map { paymentModels =>
        paymentModels.map(paymentModel => PaymentModel.getPaymentModel(paymentModel.i)).toSet
      }
      .getOrElse(Set.empty[PaymentModel])

    BaseRequest(
      searchId = Option(clientInfo.searchId).getOrElse(""),
      searchType = searchType,
      abTests = abTests,
      checkIn = pricingRequest.checkIn.withTimeAtStartOfDay(),
      checkOut = pricingRequest.checkout.withTimeAtStartOfDay(),
      bookingDate = pricingRequest.bookingDate,
      currency = pricingRequest.currency,
      channels = channels,
      occ = occupancyToBaseRequestOccupancy(request),
      flagInfo = mapFlagInfo(requestContext, forcedDMC, pricingFeatures, pricingFilters),
      priusID = pricingFeatures.priusId,
      supplierIds = pricingFilters.suppliers.toSet,
      hotels = request.propertyIds.toList,
      experiments = experiments,
      cInfo = mapClientInfo(clientInfo),
      trafficInfo = trafficInfo,
      sessionInfo = mapSessionInfo(requestContext, clientInfo),
      loyalty = mapLoyalty(requestContext),
      requestMeta = requestContext.requestMeta,
      refId = pricingRequest.refId,
      paymentTypeId = pricingRequest.paymentId,
      featureFlags = featureFlagsPlus,
      isAllowBookOnRequest = requestContext.isAllowBookOnRequest,
      maxRooms = pricingRequest.maxRooms,
      isSSR = request.isSSR,
      simulateRequestData = request.simulateRequestData,
      requestedPrice = requestedPrice,
      bookingFilter = request.booking.map(booking =>
        BookingFilterMapper.propertySearchBookingRequestToBookingFilter(booking,
                                                                        request.pricing.simplifiedRoomSelectionRequest)),
      isCheapestRoomOnly = request.cheapestOnly,
      featureRequest = mapFeatureRequest(pricingRequest, maxSuggestions, unsupportedPaymentModels),
      supplierPullMetadata = supplierPullMetadataToBaseRequestMetadata(pricingRequest),
      alternativeRoomType = alternativeRoomType,
      nosOfBedrooms = mapNoOfBedrooms(pricingFilters),
      cheapestRoomFilters = pricingFilters.cheapestRoomFilters,
      experimentInfo = experimentInfo,
      userContext = userContext,
      whiteLabelKey = pricingRequest.whiteLabelKey,
      roomSortingStrategy = request.roomSortingStrategy,
      isSortRoomsByNetPrice = request.isSortRoomsByNetPrice,
      reBookingRequest = request.booking.flatMap(_.reBookingRequest).map(mapReBookingRequest),
      packaging = requestContext.packaging,
      isIncludeUsdAndLocalCurrency = pricingFeatures.isIncludeUsdAndLocalCurrency,
      bookingDurationType = pricingRequest.bookingDurationType.getOrElse(Nil),
      whitelabelSetting = whiteLabelSetting,
      regulationFeatureEnabledSetting = regulationFeatureEnabledSetting,
      roomBundleHints = pricingRequest.roomBundleHints
        .getOrElse(List.empty)
        .map(hint => hint.copy(checkin = hint.checkin.withTimeAtStartOfDay)),
      priceHistory = request.pricing.priceHistory,
      showCMS = request.context.showCMS,
      selectedHourlySlot = request.booking.flatMap(_.selectedHourlySlot),
      pollingInfoRequest = requestContext.pollingInfoRequest,
      roomIdentifierFilter = request.pricing.filters.roomIdentifierFilter,
      symmetricUidFilterOut = request.pricing.excludeFilters.flatMap(_.symmetricUidFilter),
      packagingFilterContext = request.pricing.filters.packagingFilterContext,
      isMixNSaveSegmentSearch = request.isMixNSaveSegmentSearch.getOrElse(false),
      contractsFilter = request.pricing.filters.contractsFilter,
      correlationId = request.context.clientInfo.trafficData.flatMap(_.correlationId),
      externalLoyaltyRequest = request.pricing.externalLoyaltyRequest,
      cartRequest = requestContext.cartRequest,
      stateId = request.pricing.stateId,
      featureDegradationContext = featureDegradationContext,
      paymentInfo = getPaymentInfo(request.isBookingRequest, pricingRequest),
      externalUserContext = requestContext.externalUserContext,
      filterCriteria = request.pricing.partner.flatMap(_.filterCriteria),
      ratePlansFilter = request.pricing.filters.ratePlans,
      discountRequest = pricingRequest.discountRequest,
      agCorrelationId = agCorrelationId,
      agAnalyticsSessionId = agAnalyticsSessionId,
      benefitIdsFilter = request.pricing.filters.benefitIds.getOrElse(List.empty).toSet,
      selectedCheckInTime = pricingRequest.selectedCheckInTime,
      hourlyDurationFilter = request.pricing.filters.hourlyDurations.getOrElse(List.empty).toSet,
      selectedRewardOptions = pricingRequest.selectedRewardOption,
      externalLoyaltyProfile = pricingRequest.externalLoyaltyProfile,

      /** Only available on Simulation request */
      simulationRequestData = simulationRequestData,
      priceFreeze = request.booking.flatMap(_.priceFreeze),
      priceMetaData = mapPriceMetaData(request.pricing.metadata),
      agEnv = agEnv,
      requiredBasis = request.getRequestedBasis,
      agodaCashBalance = request.context.userContext.map(UserContextParserImpl.parse).map(_.agodaCashBalance),
      agOriginState = agOriginState,
      isXmlPartner = request.pricing.partner.isDefined,
      supplyEquitySimulationMode = supplyEquitySimulationMode,
      supplyEquitySimulationParameters = supplyEquitySimulationParameters,
      minBookingCountForSuperAgg = request.pricing.partner.flatMap(_.minBookingCountForSuperAgg),
      supplyEquitySimulationSide = supplyEquitySimulationSide,
    )
  }

  private[helper] def mapReBookingRequest(b: ReBookingRequest): DFReBookingREquest = {
    val actionType = b.actionType.getOrElse(ReBookingActionType.MatchUSD)
    val (sellIn, cashback) =
      if (actionType == ReBookingActionType.MatchUSD) {
        (b.originalSellIn.orElse(b.originalSellInUsd), b.originalCashbackAmount.orElse(b.originalCashback))
      } else {
        (b.originalSellIn, b.originalCashbackAmount)
      }
    DFReBookingREquest(
      roomTypeId = b.roomTypeId,
      masterRoomTypeId = b.masterRoomTypeId,
      customerPaidPrice = b.customerPaidPrice,
      originalNetIn = b.originalNetIn,
      originalSellIn = sellIn,
      cashbackAmount = cashback,
      promoAmount = b.originalPromoAmount,
      originalUsdToRequestExchangeRate = b.originalUsdToRequestExchangeRate,
      actionType = actionType,
    )
  }

  private def mapNoOfBedrooms(filters: FilterRequest): List[NoOfBedrooms] = filters.nosOfBedrooms.getOrElse(
    // fall back to deprecated parameter
    filters.numberOfBedrooms.toList)

  private def supplierPullMetadataToBaseRequestMetadata(pricingRequest: PricingRequest): SupplierPullMetadata =
    pricingRequest.supplierPullMetadata
      .map { metadataInReq =>
        SupplierPullMetadata(PrecheckAccuracies.getPrecheckAccuracy(metadataInReq.requiredPrecheckAccuracyLevel),
                             metadataInReq.numOfRetries)
      }
      .getOrElse(SupplierPullMetadata())

  private def mapPriceMetaData(meta: Option[List[StringAndString]]): Map[String, String] = meta
    .map { data =>
      data.map(pair => pair.key -> pair.value).toMap
    }
    .getOrElse(Map.empty)
}
