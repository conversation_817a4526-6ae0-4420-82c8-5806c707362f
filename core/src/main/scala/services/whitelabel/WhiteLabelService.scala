package services.whitelabel

import com.agoda.papi.enums.room.PaymentChannel
import com.agoda.papi.ypl.models.FeeWaiverFiltrationSettings
import com.agoda.whitelabel.client.WhiteLabelClientMultiEnv
import com.agoda.whitelabel.client.model.feature.FeaturesConfiguration
import com.agoda.whitelabel.client.settings.Constants.WhiteLabelId
import com.typesafe.scalalogging.LazyLogging
import generated.model.DmcControlSettingModel
import models.consts.DMC
import models.db.{SupplierId, WhitelabelID}
import models.flow.Variant
import models.starfruit.PropertySearchRequest
import models.whitelabel._
import services.ExperimentSupport

import scala.util.control.NonFatal

trait WhiteLabelService {
  def getWhitelabelSetting(whiteLabelKey: Option[String], agEnv: Option[String])(implicit
    request: PropertySearchRequest): Either[WhitelabelServiceException, WhitelabelSetting]

  def getRegulationFeaturesEnabledSetting(agEnv: Option[String], agOriginState: Option[String])(implicit
    request: PropertySearchRequest): Either[WhitelabelServiceException, RegulationFeatureEnabledSetting]

  def getCommissionAndMarginOverrideSetting(
    whiteLabelKey: Option[String]): Either[WhitelabelServiceException, (Int, CommissionAndMarginOverride)]

  def getDmcControlSetting(whiteLabelId: Int,
                           dmcId: Int): Either[WhitelabelServiceException, Option[DmcControlSettingModel]]

  // Methods for testing conditional branches
  def getCouponWithWLID(whiteLabelId: Int, featuresEnabled: Map[String, Boolean], agEnv: Option[String]): Coupon
  def getExchangeRateWithWLID(whitelabelID: Int,
                              featuresEnabled: Map[String, Boolean],
                              agEnv: Option[String]): ExchangeRateConfiguration
  def getExternalVipDisplayConfigs(whiteLabelId: Int,
                                   featuresEnabled: Map[String, Boolean],
                                   agEnv: Option[String]): List[ExternalVipDisplayConfigs]
}

object WhiteLabelServiceImpl {
  // todo put this setting in wlclient
  val defaultDMCSellability = Map(DMC.JTBWL -> false)
  val rurubuWhitelabel = 4
  val jtbWhitelabel = 3

  def init(whitelabelClient: WhiteLabelClientMultiEnv): Unit = whitelabelClient.getFeaturesById(WhiteLabelId.AGODA)
}

trait WhiteLabelServiceImpl extends WhiteLabelService with LazyLogging with ExperimentSupport {
  val wlClient: WhiteLabelClientMultiEnv

  import WhiteLabelServiceImpl._

  private def getWhitelabelID(whiteLabelKey: Option[String], agEnv: Option[String] = None): WhitelabelID =
    wlClient.getWhiteLabelIdByToken(whiteLabelKey, agEnv)

  private def getWhitelabelFeatures(whiteLabelId: Int, agEnv: Option[String] = None): FeaturesConfiguration =
    wlClient.getFeaturesById(whiteLabelId, agEnv)

  private[services] def getSupportedSuppliersWithWLID(whiteLabelId: Int,
                                                      featuresEnabled: Map[String, Boolean],
                                                      agEnv: Option[String])(implicit
    request: PropertySearchRequest): Set[SupplierId] = {
    val enabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.supplierFilter, false)
    if (enabled) getWhitelabelFeatures(whiteLabelId, agEnv).supplierFilter.dmcList.getOrElse(List.empty).toSet
    else Set.empty
  }

  private[services] def getSupplierConfigurationWithWLID(whiteLabelId: Int,
                                                         featuresEnabled: Map[String, Boolean],
                                                         agEnv: Option[String]): SupplierConfiguration = {
    val commConf = getWhitelabelFeatures(whiteLabelId, agEnv).supplierConfiguration.commissionAndMarginOverride
    SupplierConfiguration(
      CommissionAndMarginOverride(
        commConf.flatMap(_.isCommissionOverride).getOrElse(false),
        commConf.flatMap(_.isCommissionAdjustment).getOrElse(false),
        commConf.flatMap(_.isMarginAdjustment).getOrElse(false),
        featuresEnabled.getOrElse(WhiteLabelFeatureNames.adjustCommissionFromHotelContract, false),
      ),
    )
  }

  private def getLoyaltyProgramWithWLID(whiteLabelId: Int, agEnv: Option[String]): Option[LoyaltyProgram] = {
    val loyaltyProgram = getWhitelabelFeatures(whiteLabelId, agEnv).loyaltyProgram
    Some(
      LoyaltyProgram(
        loyaltyProgram.rounding,
        loyaltyProgram.externalWallet,
        loyaltyProgram.minimumRedemptionPoints,
        loyaltyProgram.minimumGatewayAmount,
        loyaltyProgram.pointsStepUnitWithCurrency.getOrElse(Map.empty),
      ))
  }

  def getCouponWithWLID(whiteLabelId: Int, featuresEnabled: Map[String, Boolean], agEnv: Option[String]): Coupon = {
    val isCouponEnabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.coupons, false)
    if (isCouponEnabled) {
      val coupons = getWhitelabelFeatures(whiteLabelId, agEnv).coupons
      Coupon(coupons.paymentModels)
    } else Coupon(None)
  }

  private def getPaymentChannelsWithWLID(whitelabelID: Int, agEnv: Option[String]): List[PaymentChannel] =
    getWhitelabelFeatures(whitelabelID, agEnv).payment.channelIds
      .getOrElse(List.empty)
      .map(i => PaymentChannel.getFromValue(i))

  private def getPaymentInventoryTypeConfigurations(whitelabelID: Int,
                                                    agEnv: Option[String]): List[PaymentInventoryTypeConfiguration] =
    getWhitelabelFeatures(whitelabelID, agEnv).payment.inventoryTypes
      .getOrElse(List.empty)
      .map(paymentInventoryType =>
        PaymentInventoryTypeConfiguration(
          paymentInventoryType.inventoryType.getOrElse(-1),
          paymentInventoryType.allowedPaymentChannels.getOrElse(List.empty),
        ))

  private[services] def isSellingExternalSuppliersForJtbEnabled(featuresEnabled: Map[String, Boolean]): Boolean =
    featuresEnabled.getOrElse(WhiteLabelFeatureNames.externalSuppliers, false)

  private[services] def isAffiliatePartnerEnabled(
    request: PropertySearchRequest,
    whitelabelID: WhitelabelID,
    agEnv: Option[String],
  ): Boolean = getWhitelabelFeatures(whitelabelID, agEnv).affiliateCorPartner.exists { partnerConfig =>
    partnerConfig.isFeatureEnabled.contains(true) && {
      val affiliateMatch = for {
        aids <- partnerConfig.affiliateIds
        affStr <- request.context.clientInfo.affiliateId
        affInt <- scala.util.Try(affStr.toInt).toOption
      } yield aids.contains(affInt)

      val siteIdMatch = for {
        siteIds <- partnerConfig.siteIds
      } yield siteIds.contains(request.context.clientInfo.cid)

      affiliateMatch.getOrElse(false) || siteIdMatch.getOrElse(false)
    }
  }

  private[services] def isRemoveExcludedSurchargeFromAllInclusiveEnabled(
    featuresEnabled: Map[String, Boolean]): Boolean =
    featuresEnabled.getOrElse(WhiteLabelFeatureNames.removeExcludedSurchargeFromAllInclusive, false)

  private[services] def isMixAndSaveEnabled(featuresEnabled: Map[String, Boolean]): Boolean =
    featuresEnabled.getOrElse(WhiteLabelFeatureNames.mixAndSave, false)

  private[services] def blockBNPLForJapanOutbound(featuresEnabled: Map[String, Boolean]): Boolean =
    featuresEnabled.getOrElse(WhiteLabelFeatureNames.blockBNPLForJapanOutbound, false)

  private[services] def controlDirectConnectSupply(featuresEnabled: Map[String, Boolean]): Boolean =
    featuresEnabled.getOrElse(WhiteLabelFeatureNames.controlDirectConnectSupply, false)

  private[services] def isRurubuWl(featuresEnabled: Map[String, Boolean]): Boolean =
    featuresEnabled.getOrElse(WhiteLabelFeatureNames.isRurubuWl, false)

  private[services] def isJapanicanWl(featuresEnabled: Map[String, Boolean]): Boolean =
    featuresEnabled.getOrElse(WhiteLabelFeatureNames.isJapanicanWl, false)

  private[services] def getExternalSuppliers(whiteLabelID: Int, agEnv: Option[String]): List[Int] =
    getWhitelabelFeatures(whiteLabelID, agEnv).externalSuppliers
      .flatMap(_.externalSuppliersConfig.map(_.map(config => config.supplierId.getOrElse(0))))
      .getOrElse(List.empty)

  private[services] def getMainSupplier(whitelabelID: Int, agEnv: Option[String]): Int = getWhitelabelFeatures(
    whitelabelID,
    agEnv).supportWLSupplierMetadata.flatMap(_.mainSupplierId).map(_.toInt).getOrElse(0)

  private[services] def getBlockedCountries(whiteLabelID: Int, agEnv: Option[String]): List[String] =
    getWhitelabelFeatures(whiteLabelID, agEnv).externalSuppliers
      .flatMap(_.externalSuppliersConfig.map(_.map(config => config.blockedOrigins.getOrElse(List.empty))))
      .getOrElse(List.empty)
      .flatten

  private[services] def getMinimumCheckInLeadTimeForExtSupply(whiteLabelID: Int, agEnv: Option[String]): Option[Int] =
    getWhitelabelFeatures(whiteLabelID, agEnv).externalSuppliers.flatMap(_.minimumCheckInLeadTime)

  def getExchangeRateWithWLID(whitelabelID: Int,
                              featuresEnabled: Map[String, Boolean],
                              agEnv: Option[String]): ExchangeRateConfiguration = {
    val isCurrencyEnabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.currency, false)
    if (isCurrencyEnabled) {
      val currency = getWhitelabelFeatures(whitelabelID, agEnv).currency
      ExchangeRateConfiguration(currency.exchangeRateOnlyForReference.getOrElse(false))
    } else {
      ExchangeRateConfiguration(false)
    }
  }

  private def getRequiredLoginInventoryTypeList(whitelabelID: Int, agEnv: Option[String]): List[Int] =
    getWhitelabelFeatures(whitelabelID, agEnv).login.inventoryTypesForLogIn.getOrElse(List.empty)

  private def isBreakfastAndDinnerIncludeEnabled(whitelabelID: Int, agEnv: Option[String]): Boolean =
    getWhitelabelFeatures(whitelabelID, agEnv).roomFilters.meal.getOrElse(false)

  private def getIsPromotionPeekAllowedOnBFOnly(whitelabelID: Int, agEnv: Option[String]): Boolean =
    getWhitelabelFeatures(whitelabelID, agEnv).regulationAllowPromoPricePeek.isAllowedOnBFOnly.getOrElse(false)

  def getExternalVipDisplayConfigs(whiteLabelId: Int,
                                   featuresEnabled: Map[String, Boolean],
                                   agEnv: Option[String]): List[ExternalVipDisplayConfigs] = {
    val isExternalVipProgramEnabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.externalVipProgram, false)
    if (isExternalVipProgramEnabled) {
      val whiteLabelFeatures = getWhitelabelFeatures(whiteLabelId, agEnv)
      val externalVipDisplayConfigs = whiteLabelFeatures.externalVipDisplay
      val result = externalVipDisplayConfigs.externalVipDisplayItems.map(items =>
        items.map(item =>
          ExternalVipDisplayConfigs(item.tierId, item.tierNameCms, item.tierDescriptionCms, item.benefitIds)))
      result.getOrElse(List.empty)
    } else List.empty
  }

  private def getIsPublishPriceESSEnabled(whitelabelID: Int, agEnv: Option[String]): Boolean =
    getWhitelabelFeatures(whitelabelID, agEnv).publishPriceConfiguration.enableESSPublishPrice.getOrElse(false)

  private def getFeeWaiverFiltrationSettings(whitelabelID: Int,
                                             featuresEnabled: Map[String, Boolean],
                                             agEnv: Option[String]): Option[FeeWaiverFiltrationSettings] =
    if (featuresEnabled.getOrElse(WhiteLabelFeatureNames.feeWaiverFiltrationSettings, false)) {
      val californiaFeeWaiverOpt = getWhitelabelFeatures(whitelabelID, agEnv).californiaFeeWaiver

      californiaFeeWaiverOpt.map { config =>
        FeeWaiverFiltrationSettings(
          description = config.description.getOrElse(null),
          hotelCountryId = config.hotelCountryIdV2.map(_.toLong).getOrElse(0),
          hotelStateId = config.hotelStateIdV2.map(_.toInt).getOrElse(0),
          leadTimeDuration = config.leadTimeDuration.getOrElse(null),
          checkInTime = config.checkInTime.getOrElse(null),
          expiryDuration = config.expiryDuration.getOrElse(null),
          feeValue = config.feeValue.getOrElse(null),
          filterChannelId = config.filterChannelIdV2.map(_.map(_.toInt)).getOrElse(List.empty),
        )
      }
    } else {
      None
    }

  private def getPriceViewOptionsDestinationOverride(
    whitelabelID: Int,
    featuresEnabled: Map[String, Boolean],
    agEnv: Option[String]): List[PriceViewDestinationOverrideConfigs] = {
    val isPriceViewOverrideEnabled =
      featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationPriceViewOptionDestinationOverride, false)
    if (isPriceViewOverrideEnabled) {
      getWhitelabelFeatures(whitelabelID, agEnv).regulatory
        .flatMap(_.priceViewOptionsDestinationOverride)
        .getOrElse(List.empty)
        .map(config =>
          PriceViewDestinationOverrideConfigs(
            config.hotelCountryId,
            config.hotelStateId,
            config.overrideCriteria,
            config.suggestPriceType,
            config.experimentName,
          ))
    } else List.empty
  }

  private[services] def getExclusivePriceWithFeesForDestination(
    whitelabelID: Int,
    featuresEnabled: Map[String, Boolean],
    agEnv: Option[String]): Option[RegulationShowExclusivePriceWithFeesForDestinationConfigs] = {
    val isShowExclusivePriceWithFeesForDestination =
      featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationShowExclusivePriceWithFeesForDestination, false)
    if (isShowExclusivePriceWithFeesForDestination) {
      getWhitelabelFeatures(whitelabelID, agEnv).regulationShowExclusivePriceWithFeesForDestination.map(config =>
        RegulationShowExclusivePriceWithFeesForDestinationConfigs(
          config.hotelCountryId,
          config.hotelStateId,
        ))
    } else None
  }

  private def getexactMatchOccupancy(whitelabelID: Int, agEnv: Option[String]): Boolean =
    getWhitelabelFeatures(whitelabelID, agEnv).occupancyOptions.map(_.exactMatch.getOrElse(false)).getOrElse(false)

  private[services] def getDirectConnectSupplierIds(whitelabelID: WhitelabelID, agEnv: Option[String]): Set[Int] =
    getWhitelabelFeatures(whitelabelID, agEnv).controlDirectConnectSupply
      .flatMap(_.whitelistedSuppliers)
      .getOrElse(Set.empty)
      .toSet

  private[services] def getAllowedLeadDaysToNonRefundable(whitelabelID: WhitelabelID, agEnv: Option[String]): Int =
    getWhitelabelFeatures(whitelabelID, agEnv).controlDirectConnectSupply
      .flatMap(_.allowedLeadDaysToNonRefundable)
      .getOrElse(0)

  private[services] def isDisableRoomOnArrivalEnabled(featuresEnabled: Map[String, Boolean]): Boolean =
    featuresEnabled.getOrElse(WhiteLabelFeatureNames.disableRoomOnArrival, false)

  private[services] def blockPromotionsPerSupplier(featuresEnabled: Map[String, Boolean]): Boolean =
    featuresEnabled.getOrElse(WhiteLabelFeatureNames.blockPromotionsPerSupplier, false)

  private[services] def getPromotionsBlockedSuppliers(whitelabelID: WhitelabelID, agEnv: Option[String]): Set[Int] =
    getWhitelabelFeatures(whitelabelID, agEnv).blockPromotionsPerSupplier
      .flatMap(_.supplierIds)
      .getOrElse(Set.empty)
      .toSet

  private[services] def getCurrencyDecimalOverrideConfig(whitelabelID: Int,
                                                         featuresEnabled: Map[String, Boolean],
                                                         agEnv: Option[String]): CurrencyDecimalOverrideConfig = {
    val isCurrencyDecimalOverrideEnabled =
      featuresEnabled.getOrElse(WhiteLabelFeatureNames.currencyDecimalOverride, false)
    if (isCurrencyDecimalOverrideEnabled) {
      val whiteLabelFeatures = getWhitelabelFeatures(whitelabelID, agEnv)
      val currencyDecimalOverrideOpt = whiteLabelFeatures.currencyDecimalOverride

      currencyDecimalOverrideOpt
        .map { whitelabelClientData =>
          val currencyOverrides = whitelabelClientData.map { case (currency, currencyConfig) =>
            val paymentMethodOverrides = currencyConfig.paymentMethodOverrides.getOrElse(Map.empty).map {
              case (paymentMethodId, paymentMethodConfig) => paymentMethodId -> PaymentMethodDecimalOverride(
                  decimals = paymentMethodConfig.decimals.getOrElse(2),
                  enabled = paymentMethodConfig.decimals.isDefined && paymentMethodConfig.enabled.getOrElse(false),
                  description = paymentMethodConfig.description,
                )
            }
            currency -> CurrencyPaymentMethodOverrides(paymentMethodOverrides)
          }
          CurrencyDecimalOverrideConfig(currencyOverrides)
        }
        .getOrElse(CurrencyDecimalOverrideConfig())
    } else {
      CurrencyDecimalOverrideConfig()
    }
  }

  private[services] val settingFeatures = List(
    WhiteLabelFeatureNames.supplierFilter,
    WhiteLabelFeatureNames.adjustCommissionFromHotelContract,
    WhiteLabelFeatureNames.coupons,
    WhiteLabelFeatureNames.currency,
    WhiteLabelFeatureNames.customerSegmentValidation,
    WhiteLabelFeatureNames.externalVipProgram,
    WhiteLabelFeatureNames.blockYCSPromotions,
    WhiteLabelFeatureNames.filterOutAgencyPaymentModel,
    WhiteLabelFeatureNames.cashbackRedemption,
    WhiteLabelFeatureNames.m150MaxTransparencyVersion,
    WhiteLabelFeatureNames.m150BaselineTransparencyVersion,
    WhiteLabelFeatureNames.externalSuppliers,
    WhiteLabelFeatureNames.regulationPriceViewOptionDestinationOverride,
    WhiteLabelFeatureNames.blockBNPLForJapanOutbound,
    WhiteLabelFeatureNames.feeWaiverFiltrationSettings,
    WhiteLabelFeatureNames.isRurubuWl,
    WhiteLabelFeatureNames.isJapanicanWl,
    WhiteLabelFeatureNames.controlDirectConnectSupply,
    WhiteLabelFeatureNames.removeExcludedSurchargeFromAllInclusive,
    WhiteLabelFeatureNames.mixAndSave,
    WhiteLabelFeatureNames.disableRoomOnArrival,
    WhiteLabelFeatureNames.blockPromotionsPerSupplier,
    WhiteLabelFeatureNames.regulationShowExclusivePriceWithFeesForDestination,
    WhiteLabelFeatureNames.currencyDecimalOverride,
  )
  override def getWhitelabelSetting(whiteLabelKey: Option[String], agEnv: Option[String])(implicit
    request: PropertySearchRequest): Either[WhitelabelServiceException, WhitelabelSetting] =
    try {
      val featuresEnabled = getFeaturesEnabled(settingFeatures, agEnv)

      val wlId = getWhitelabelID(whiteLabelKey, agEnv)
      val supportedSuppliers = getSupportedSuppliersWithWLID(wlId, featuresEnabled, agEnv)
      val supplierConfig = getSupplierConfigurationWithWLID(wlId, featuresEnabled, agEnv)
      val loyaltyProgramConfig = getLoyaltyProgramWithWLID(wlId, agEnv)
      val couponSetting = getCouponWithWLID(wlId, featuresEnabled, agEnv)
      val exchangeRateSetting = getExchangeRateWithWLID(wlId, featuresEnabled, agEnv)
      val paymentChannels = getPaymentChannelsWithWLID(wlId, agEnv)
      val logInInventoryTypeListSetting = getRequiredLoginInventoryTypeList(wlId, agEnv)
      val isBreakfastAndDinnerIncludeEnable = isBreakfastAndDinnerIncludeEnabled(wlId, agEnv)
      val externalVipDisplayConfigs = getExternalVipDisplayConfigs(wlId, featuresEnabled, agEnv)
      val blockYCSPromotions = featuresEnabled.getOrElse(WhiteLabelFeatureNames.blockYCSPromotions, false)
      val isPromotionPeekAllowedOnBFOnly = getIsPromotionPeekAllowedOnBFOnly(wlId, agEnv)
      val filterOutAgencyPaymentModel =
        featuresEnabled.getOrElse(WhiteLabelFeatureNames.filterOutAgencyPaymentModel, false)
      val paymentInventoryTypeConfigurations = getPaymentInventoryTypeConfigurations(wlId, agEnv)
      val isSellingExternalSuppliersEnabled = isSellingExternalSuppliersForJtbEnabled(featuresEnabled)
      val removeExcludedSurchargeFromAllInclusive = isRemoveExcludedSurchargeFromAllInclusiveEnabled(featuresEnabled)
      val isMixAndSave = isMixAndSaveEnabled(featuresEnabled)
      val externalSuppliers = getExternalSuppliers(wlId, agEnv)
      val mainSupplier = getMainSupplier(wlId, agEnv)
      val blockedCountries = getBlockedCountries(wlId, agEnv)
      val enabledCashbackRedemption = featuresEnabled.getOrElse(WhiteLabelFeatureNames.cashbackRedemption, false)
      val enablePublishPriceESS = getIsPublishPriceESSEnabled(wlId, agEnv)
      val enableM150MaxTransparency = featuresEnabled.getOrElse(WhiteLabelFeatureNames.m150MaxTransparencyVersion, false)
      val enableM150BaselineTransparency =
        featuresEnabled.getOrElse(WhiteLabelFeatureNames.m150BaselineTransparencyVersion, false)
      val priceViewOptionsDestinationOverride = getPriceViewOptionsDestinationOverride(wlId, featuresEnabled, agEnv)
      val feeWaiverFiltrationSettings = getFeeWaiverFiltrationSettings(wlId, featuresEnabled, agEnv)
      val exactMatchOccupancy = getexactMatchOccupancy(wlId, agEnv)
      val blockBNPLForJapanOutboundEnabled = blockBNPLForJapanOutbound(featuresEnabled)
      val isRurubuWlEnabled = isRurubuWl(featuresEnabled)
      val isJapanicanWlEnabled = isJapanicanWl(featuresEnabled)
      val directConnectSupplierIds = getDirectConnectSupplierIds(wlId, agEnv)
      val controlDirectConnectSupplyEnabled = controlDirectConnectSupply(featuresEnabled)
      val allowedLeadDaysToNonRefundable = getAllowedLeadDaysToNonRefundable(wlId, agEnv)
      val disableRoomOnArrival = isDisableRoomOnArrivalEnabled(featuresEnabled)
      val blockPromotionsPerSupplierEnabled = blockPromotionsPerSupplier(featuresEnabled)
      val promotionsBlockedSuppliers = getPromotionsBlockedSuppliers(wlId, agEnv)
      val showExclusivePriceWithFeesForDestination =
        getExclusivePriceWithFeesForDestination(wlId, featuresEnabled, agEnv)
      val currencyDecimalOverrideConfig = getCurrencyDecimalOverrideConfig(wlId, featuresEnabled, agEnv)

      val minimumCheckInLeadTimeForExtSupply = getMinimumCheckInLeadTimeForExtSupply(wlId, agEnv)
      val isPartnerEnabledForCor = isAffiliatePartnerEnabled(request, wlId, agEnv)
      Right(
        WhitelabelSetting(
          wlId,
          defaultDMCSellability,
          supportedSuppliers,
          supplierConfig,
          loyaltyProgramConfig,
          couponSetting,
          exchangeRateSetting,
          paymentChannels,
          logInInventoryTypeListSetting,
          isBreakfastAndDinnerIncludeEnable,
          featuresEnabled.getOrElse(WhiteLabelFeatureNames.customerSegmentValidation, false),
          externalVipDisplayConfigs,
          blockYCSPromotions,
          isPromotionPeekAllowedOnBFOnly,
          filterOutAgencyPaymentModel,
          paymentInventoryTypeConfigurations,
          enabledCashbackRedemption,
          enablePublishPriceESS,
          enableM150MaxTransparency,
          enableM150BaselineTransparency,
          isSellingExternalSuppliersEnabled,
          externalSuppliers,
          priceViewOptionsDestinationOverride,
          exactMatchOccupancy,
          blockBNPLForJapanOutboundEnabled,
          feeWaiverFiltrationSettings,
          isRurubuWlEnabled,
          isJapanicanWlEnabled,
          blockedCountries,
          directConnectSupplierIds,
          controlDirectConnectSupplyEnabled,
          allowedLeadDaysToNonRefundable,
          removeExcludedSurchargeFromAllInclusive,
          isMixAndSave,
          disableRoomOnArrival,
          mainSupplier,
          blockPromotionsPerSupplierEnabled,
          promotionsBlockedSuppliers,
          showExclusivePriceWithFeesForDestination,
          minimumCheckInLeadTimeForExtSupply,
          isPartnerEnabledForCor = isPartnerEnabledForCor,
          currencyDecimalOverrideConfig,
        ))
    } catch {
      case NonFatal(ex) =>
        logger.error(s"Error during call Whitelabel service : ${ex.getMessage}", ex)
        Left(new WhitelabelServiceException(whiteLabelKey, ex.getMessage, ex))
    }

  override def getCommissionAndMarginOverrideSetting(
    whiteLabelKey: Option[String]): Either[WhitelabelServiceException, (Int, CommissionAndMarginOverride)] =
    try {
      val featuresEnabled = wlClient.isFeaturesEnabled(List(WhiteLabelFeatureNames.adjustCommissionFromHotelContract),
                                                       whiteLabelKey,
                                                       None,
                                                       None)
      val wlId = getWhitelabelID(whiteLabelKey)
      Right((wlId, getSupplierConfigurationWithWLID(wlId, featuresEnabled, None).commissionAndMarginOverride))
    } catch {
      case NonFatal(ex) =>
        logger.error(s"Error during call Whitelabel service : ${ex.getMessage}", ex)
        Left(new WhitelabelServiceException(whiteLabelKey, ex.getMessage, ex))
    }

  override def getDmcControlSetting(whiteLabelId: Int,
                                    dmcId: Int): Either[WhitelabelServiceException, Option[DmcControlSettingModel]] =
    try {
      val dmcControlSettingList = getWhitelabelFeatures(whiteLabelId, None).bookingWorkflow.dmcControlSettings
      val dmcControlSetting = dmcControlSettingList.flatMap { setting =>
        setting.find(_.dmcId.contains(dmcId)).orElse(setting.find(_.dmcId.isEmpty))
      }
      Right(dmcControlSetting)
    } catch {
      case NonFatal(ex) =>
        logger.error(s"Error during call Whitelabel service : ${ex.getMessage}", ex)
        Left(
          new WhitelabelServiceException(
            wlClient.getWhiteLabelKeyToIdMap().collectFirst { case (key, id) if id == whiteLabelId => key },
            ex.getMessage,
            ex),
        )
    }

  private[services] val regulationFeatures = List(
    WhiteLabelFeatureNames.regulationAllowCor,
    WhiteLabelFeatureNames.regulationAllowCcor,
    WhiteLabelFeatureNames.regulationAllowPromoPricePeek,
    WhiteLabelFeatureNames.regulationShowExclusivePrice,
    WhiteLabelFeatureNames.breakfastUpsell,
    WhiteLabelFeatureNames.claimPromotion,
    WhiteLabelFeatureNames.regulationShowPriceOfBreakfast,
    WhiteLabelFeatureNames.removeYCSPromotion,
    WhiteLabelFeatureNames.overrideOriginalTotalField,
    WhiteLabelFeatureNames.autoApplyAllPromos,
    WhiteLabelFeatureNames.removePulseBadgeTooltip,
    WhiteLabelFeatureNames.autoApplyPromoApplyFirst,
    WhiteLabelFeatureNames.externalLoyaltyBurnDisablePayLater,
    WhiteLabelFeatureNames.externalLoyaltyBurnFilterAgencyModelOut,
    WhiteLabelFeatureNames.regulationShowUpsellFeatureSetting,
    WhiteLabelFeatureNames.jtbWlDynamicMapping,
    WhiteLabelFeatureNames.disableFxi,
    WhiteLabelFeatureNames.regulationDisabledM150,
    WhiteLabelFeatureNames.consolidatedDiscountUserOverridePriceDisplay,
    WhiteLabelFeatureNames.regulationDisplayVariableTax,
    WhiteLabelFeatureNames.regulationDisableDsaLicenseBlocking,
    WhiteLabelFeatureNames.regulationShowExclusivePriceWithFee,
    WhiteLabelFeatureNames.DynamicDownlift,
    WhiteLabelFeatureNames.regulationShowExclusivePriceWithFeesForDestination,
    WhiteLabelFeatureNames.enable30MinsHourlySlots,
    WhiteLabelFeatureNames.enableHotelLocalCurrency,
    WhiteLabelFeatureNames.valueTag,
  )

  override def getRegulationFeaturesEnabledSetting(agEnv: Option[String], agOriginState: Option[String])(implicit
    request: PropertySearchRequest): Either[WhitelabelServiceException, RegulationFeatureEnabledSetting] =
    try {
      val featuresEnabled = getFeaturesEnabled(regulationFeatures, agEnv)

      Right(
        RegulationFeatureEnabledSetting(
          isCorAllowed = featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationAllowCor, false),
          isCCorAllowed = featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationAllowCcor, false),
          isPricePeekEnabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationAllowPromoPricePeek, false),
          isAllowExclusivePrice = getAllowExclusivePriceWithState(agEnv, agOriginState),
          isBreakfastUpsellEnabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.breakfastUpsell, false),
          isClaimPromotionEnabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.claimPromotion, false),
          isShowPriceOfBreakfast =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationShowPriceOfBreakfast, false),
          isRemoveYCSPromotionEnabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.removeYCSPromotion, false),
          isOverrideOriginalTotalPrice =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.overrideOriginalTotalField, false),
          isAutoApplyAllPromosEnabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.autoApplyAllPromos, false),
          isRemovePulseBadgeTooltip = featuresEnabled.getOrElse(WhiteLabelFeatureNames.removePulseBadgeTooltip, false),
          isAutoApplyPromoApplyFirst =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.autoApplyPromoApplyFirst, false),
          isExternalLoyaltyBurnDisablePayLater =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.externalLoyaltyBurnDisablePayLater, false),
          isExternalLoyaltyBurnFilterAgencyModelOut =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.externalLoyaltyBurnFilterAgencyModelOut, false),
          isShowUpsellFeatureSetting =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationShowUpsellFeatureSetting, false),
          isJtbWlDynamicMapping = featuresEnabled.getOrElse(WhiteLabelFeatureNames.jtbWlDynamicMapping, false),
          isDisabledFxi = featuresEnabled.getOrElse(WhiteLabelFeatureNames.disableFxi, false),
          isDisabledM150 = featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationDisabledM150, false),
          isConsolidatedDiscountUserOverridePriceDisplay =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.consolidatedDiscountUserOverridePriceDisplay, false),
          isDisplayVariableTax = featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationDisplayVariableTax, false),
          isDsaLicenseBlockingDisabled =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationDisableDsaLicenseBlocking, false),
          isShowExclusivePriceWithFeeEnabled =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationShowExclusivePriceWithFee, false),
          isDynamicDownliftEnabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.DynamicDownlift, false),
          isEnable30MinsHourlySlots = featuresEnabled.getOrElse(WhiteLabelFeatureNames.enable30MinsHourlySlots, false),
          isAllowExclusivePriceWithOutState = getAllowExclusivePriceWithOutState(agEnv),
          isShowExclusivePriceWithFeesForDestination =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.regulationShowExclusivePriceWithFeesForDestination, false),
          isEnableHotelLocalCurrency =
            featuresEnabled.getOrElse(WhiteLabelFeatureNames.enableHotelLocalCurrency, false),
          isValueTagEnabled = featuresEnabled.getOrElse(WhiteLabelFeatureNames.valueTag, false),
        ),
      )
    } catch {
      case NonFatal(ex) =>
        logger.error(s"Error during call Whitelabel service : ${ex.getMessage}", ex)
        Left(new WhitelabelServiceException(request.pricing.whiteLabelKey, ex.getMessage, ex))
    }

  private[services] def getFeaturesEnabled(features: List[String], agEnv: Option[String])(implicit
    request: PropertySearchRequest): Map[String, Boolean] = wlClient.isFeaturesEnabled(
    features,
    request.pricing.whiteLabelKey,
    Option(request.context.clientInfo.origin),
    request.context.clientInfo.deviceTypeId,
    expName => request.context.experiment.exists(e => e.name == expName && e.variant == Variant.B),
    agEnv,
  )

  private[services] def getAllowExclusivePriceWithState(agEnv: Option[String], agOriginState: Option[String])(implicit
    request: PropertySearchRequest): Boolean = wlClient.isFeatureEnabledWithState(
    WhiteLabelFeatureNames.regulationShowExclusivePrice,
    request.pricing.whiteLabelKey,
    Option(request.context.clientInfo.origin),
    agOriginState,
    request.context.clientInfo.deviceTypeId,
    /*
        As current WhiteLabelService doesn't check variant B from experiment context.
        We do B check on business logic at core/src/main/scala/services/priceselection/PrecachedPriceSelectionDataService.scala - determinePriceSelectionLogic
     */
    _ => true,
    agEnv,
  )

  private[services] def getAllowExclusivePriceWithOutState(agEnv: Option[String])(implicit
    request: PropertySearchRequest): Boolean = wlClient.isFeatureEnabled(
    WhiteLabelFeatureNames.regulationShowExclusivePrice,
    request.pricing.whiteLabelKey,
    Option(request.context.clientInfo.origin),
    request.context.clientInfo.deviceTypeId,
    /*
        As current WhiteLabelService doesn't check variant B from experiment context.
        We do B check on business logic at core/src/main/scala/services/priceselection/PrecachedPriceSelectionDataService.scala - determinePriceSelectionLogic
     */
    _ => true,
    agEnv,
  )
}

object WhiteLabelFeatureNames {
  val regulationAllowCor = "regulationAllowCOR"
  val regulationAllowCcor = "RegulationAllowCCOR"
  val regulationAllowPromoPricePeek = "regulationAllowPromoPricePeek"
  val supplierFilter = "SupplierFilter"
  val regulationShowExclusivePrice = "regulationshowexclusivepricesetting"
  val coupons = "coupons"
  val claimPromotion = "claimPromotion"
  val breakfastUpsell = "breakfastUpsell"
  val currency = "currency"
  val regulationShowPriceOfBreakfast = "regulationShowPriceOfBreakfast"
  val removeYCSPromotion = "RemoveYCSPromotion"
  val overrideOriginalTotalField = "OverrideOriginalTotalField"
  val customerSegmentValidation = "CustomerSegmentValidation"
  val autoApplyAllPromos = "AutoApplyAllPromos"
  val adjustCommissionFromHotelContract = "AdjustCommissionFromHotelContract"
  val autoApplyPromoApplyFirst = "AutoApplyPromoApplyFirst"
  val removePulseBadgeTooltip = "RemovePulseBadgeTooltip"
  val externalLoyaltyBurnDisablePayLater = "ExternalLoyaltyBurnDisablePayLater"
  val externalLoyaltyBurnFilterAgencyModelOut = "ExternalLoyaltyBurnFilterAgencyModelOut"
  val externalVipProgram = "ExternalVipProgram"
  val blockYCSPromotions = "BlockYCSPromotions"
  val regulationShowUpsellFeatureSetting = "RegulationShowUpsellFeatureSetting"
  val jtbWlDynamicMapping = "JtbWlDynamicMapping"
  val filterOutAgencyPaymentModel = "FilterOutAgencyPaymentModel"
  val externalSuppliers = "ExternalSuppliers"
  val cashbackRedemption = "CashbackRedemption"
  val m150MaxTransparencyVersion = "M150MaxTransparencyVersion"
  val m150BaselineTransparencyVersion = "M150BaselineTransparencyVersion"
  val regulationPriceViewOptionDestinationOverride = "RegulationPriceViewOptionDestinationOverride"
  val disableFxi = "DisableFxi"
  val regulationDisabledM150 = "RegulationDisabledM150"
  val consolidatedDiscountUserOverridePriceDisplay = "ConsolidatedDiscountUserOverridePriceDisplay"
  val regulationDisplayVariableTax = "RegulationDisplayVariableTaxForMTT"
  val blockBNPLForJapanOutbound = "BlockBnplForJapanOutbound"
  val feeWaiverFiltrationSettings = "CaliforniaFeeWaiver"
  val isRurubuWl = "IsRurubuWl"
  val isJapanicanWl = "IsJapanicanWl"
  val regulationDisableDsaLicenseBlocking = "RegulationDisableDsaLicenseBlocking"
  val controlDirectConnectSupply = "ControlDirectConnectSupply"
  val regulationShowExclusivePriceWithFee = "RegulationShowExclusivePriceWithFee"
  val DynamicDownlift = "DynamicDownlift"
  val removeExcludedSurchargeFromAllInclusive = "RemoveExcludedSurchargeFromAllInclusive"
  val mixAndSave = "MixAndSave"
  val disableRoomOnArrival = "DisableRoomOnArrival"
  val blockPromotionsPerSupplier = "BlockPromotionsPerSupplier"
  val regulationShowExclusivePriceWithFeesForDestination = "RegulationShowExclusivePriceWithFeesForDestination"
  val enable30MinsHourlySlots = "Enable30MinsHourlySlots"
  val enableHotelLocalCurrency = "EnableHotelLocalCurrency"
  val valueTag = "ValueTag"
  val currencyDecimalOverride = "CurrencyDecimalOverride"
}

// TODO: Will remove this configuration shortly as need to update white label service configuration.
object CouponsPageVersion {
  val defaultDealsPageVersion = 1
  val newDealsPageVersion = 2
}
