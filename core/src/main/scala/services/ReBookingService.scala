package services

import api.request.ReBookingRequest
import com.agoda.papi.enums.room.ChargeType
import com.agoda.papi.pricing.discounting.models.consts.Constants.defaultPrecision
import com.agoda.papi.pricing.flow.SoybeanAndDiscountStep
import com.agoda.papi.ypl.pricing.PriceCalculator
import com.agoda.utils.collection.SumImplicits._
import models.flow.FlowContext
import models.pricing._
import services.exchange.ExchangeDataService
import com.agoda.papi.pricing.discounting.helpers.ImplicitHelpers.DoublePrecision

trait ReBookingService {
  // filter by room type id that customer booked and
  // create manual downlift that make the total sell price match with the customer has paid
  def applyDownliftForReBooking(hotel: Hotel,
                                reBookingRequest: ReBookingRequest,
                                paymentCurrency: String,
                                bookedRoomQuantity: Int,
                                bookedLengthOfStay: Int)(implicit ctx: FlowContext): Hotel

  def filterRoomsForReBooking(hotel: Hotel, reBookingRequest: ReBookingRequest): Hotel

  def setCxlRebookToDFFinance(hotel: Hotel)(implicit ctx: FlowContext): Hotel
  def doCxlRebookDiscountMatching(hotel: Hotel)(implicit ctx: FlowContext): Hotel
}

trait ReBookingServiceImpl extends ReBookingService with ExperimentSupport {
  self: ExchangeDataService =>

  val soybeanAndDiscountStep: SoybeanAndDiscountStep

  // We adjust Downlift Amount here to account on Tax Or Fee as Amount.
  // Before passing thought the applyPriceDownliftAmount function in YPL
  // https://agoda.atlassian.net/wiki/display/Customer/Dragon+Fruit+Changes
  // Todo: move this function into YPl PriceCalculator
  def applyDownliftPerRoomPerDate(roomPrice: Price,
                                  perRoomPerDateDownliftAmount: Double,
                                  originalNetInPerRoomPerDateOpt: Option[Double],
                                  paymentModel: com.agoda.papi.enums.hotel.PaymentModel): Price = {

    // calculatedTaxPercentFromTotalPrice, this equation should be sync with the other one in YPL in applyPriceDownliftAmount
    val taxPercent =
      (roomPrice.tax + roomPrice.fee + roomPrice.processingFee - roomPrice.taxAndFeeAmount) / (roomPrice.netExclusive + roomPrice.margin)

    // Abbreviate calculatedTaxPercentFromTotalPrice to TP
    val OnePlusTP = 1.0d + taxPercent

    // Just temp variable for NewSellIn - OriSellIn + OriProcessingFee + OriMargin
    val priceTemp = perRoomPerDateDownliftAmount + roomPrice.processingFee + roomPrice.margin
    val adjustedDownlift = roomPrice.margin * OnePlusTP - priceTemp
    val yplPrice = PriceCalculator.applyPriceDownliftAmount(
      price = PriceConverter.toYplPrice(roomPrice),
      downliftAmount = adjustedDownlift,
      paymentModel = paymentModel,
    )
    val optimizationGainOpt = originalNetInPerRoomPerDateOpt.map { originalNetInPerRoomPerDate =>
      originalNetInPerRoomPerDate - yplPrice.netInclusive
    }
    PriceConverter.toDfPrice(yplPrice).copy(optimizationGain = optimizationGainOpt)
  }

  def applyDownliftForReBooking(hotel: Hotel,
                                reBookingRequest: ReBookingRequest,
                                paymentCurrency: String,
                                bookedRoomQuantity: Int,
                                bookedLengthOfStay: Int)(implicit ctx: FlowContext): Hotel = {

    val perRoomPerDate: Double = bookedRoomQuantity * bookedLengthOfStay

    val potentialRooms = filterRoomsForReBooking(hotel, reBookingRequest).rooms

    val modifiedRooms = potentialRooms.map { room =>
      // Todo: we can group room by currency and calculate for customerPaidPriceInRoomCurrency once per group.
      getExchangeRate(paymentCurrency, room.currency)
        .map { exchangeRate =>
          val customerPaidPriceInRoomCurrency = exchangeRate.toReqRate(reBookingRequest.customerPaidPrice)

          // Todo: add extra bed & surcharge handling here. allOriginalRoomSellIn (all rooms all dates) dose not include extra bed nor surcharge.
          val allOriginalRoomSellIn = room.roomPrices.getDSum(p => p.sellInclusive * p.quantity)
          val totalDownliftAmount = customerPaidPriceInRoomCurrency - allOriginalRoomSellIn
          val perRoomPerDateDownliftAmount = totalDownliftAmount / perRoomPerDate

          val originalNetInPerRoomPerDate = reBookingRequest.originalNetIn.map { oriNetIn =>
            val originalNetInInRoomCurrency = exchangeRate.toReqRate(oriNetIn)
            originalNetInInRoomCurrency / perRoomPerDate
          }
          val modifiedPrices = room.prices.map {
            // apply downlift on room only
            case roomPrice if (roomPrice.chargeType == ChargeType.Room) =>
              applyDownliftPerRoomPerDate(
                roomPrice,
                perRoomPerDateDownliftAmount,
                originalNetInPerRoomPerDate,
                room.paymentModel,
              )
            case otherPrice => otherPrice
          }

          val supplyPrice = new SupplyPrice(modifiedPrices)
          val additionalPricingInfo = room.dfFinanceByCurrency.local.getAdditionalPricingInfo

          room.copy(
            dfFinanceByCurrency = DFFinanceByCurrency(
              local = new DFFinance(
                supplyPrice = supplyPrice,
                additionalPricingInfo = additionalPricingInfo,
              ),
              request = None,
              usd = None,
            ),
          )
        }
        .getOrElse(room)
    }
    hotel.copy(rooms = modifiedRooms)
  }

  def filterRoomsForReBooking(hotel: Hotel, reBookingRequest: ReBookingRequest): Hotel = {
    // filter by room type id that customer booked
    val potentialRooms = reBookingRequest.masterRoomTypeId
      .filter(_ > 0) // when masterRoomId is 0, need to take room type id from child (existing field)
      .map(masterRoomTypeId => hotel.rooms.filter(r => r.getMasterRoomIdOrElseRoomTypeId == masterRoomTypeId))
      .getOrElse(hotel.rooms.filter(_.roomTypeId == reBookingRequest.roomTypeId))

    hotel.copy(rooms = potentialRooms)
  }

  override def setCxlRebookToDFFinance(hotel: Hotel)(implicit ctx: FlowContext): Hotel = {
    val matchLocalCurrency = ctx.baseRequest.isRebookingRequestV3MatchLocal

    val updatedRooms: List[Room] = hotel.rooms.map { room =>
      val localCurrency = room.exchange.map(_.local).getOrElse("")

      val exchangeRateRequestToLocal = getExchangeRate(ctx.baseRequest.currency, localCurrency)
      val exchangeRateUsdToLocal = getExchangeRate(ExchangeDataService.USD_CODE, localCurrency)

      val cancelReBookOriginalSellIn: Option[Double] =
        if (matchLocalCurrency) {
          ctx.baseRequest.reBookingRequest.flatMap(_.originalSellIn)
        } else {
          for {
            originalSellIn <- ctx.baseRequest.reBookingRequest.flatMap(_.originalSellIn)
            local <- exchangeRateRequestToLocal
          } yield ConvertCurrencyAndRoundingService.round(originalSellIn, exchangeRateUsdToLocal, local.numReqDecimal)
        }

      cancelReBookOriginalSellIn
        .map { originalSellIn =>
          val localDff = room.dfFinanceByCurrency.local
          val newDffByCurrency = room.dfFinanceByCurrency.copy(local = new DFFinance(
            new CustomerMarketing(localDff.discounts, localDff.priceFreeze, Some(originalSellIn)),
            localDff.supplyPrice,
            localDff.getAdditionalPricingInfo,
          ))
          room.copy(dfFinanceByCurrency = newDffByCurrency)
        }
        .getOrElse(room)
    }
    val updatedHotel: Hotel = hotel.copy(rooms = updatedRooms)
    updatedHotel
  }

  override def doCxlRebookDiscountMatching(hotel: Hotel)(implicit ctx: FlowContext): Hotel = {
    val lengthOfStay = ctx.baseRequest.lengthOfStay
    val originalPromo = ctx.baseRequest.reBookingRequest.flatMap(_.promoAmount).getOrElse(0.0)
    val originalSellIn = ctx.baseRequest.reBookingRequest.flatMap(_.originalSellIn).getOrElse(0.0)
    val originalCashbackEarning = ctx.baseRequest.reBookingRequest.flatMap(_.cashbackAmount).getOrElse(0.0)
    val matchLocalCurrency = ctx.baseRequest.isRebookingRequestV3MatchLocal

    if (originalPromo == 0 && originalCashbackEarning == 0) {
      hotel
    } else {
      val updatedRooms: List[Room] = hotel.rooms.map { room =>
        val numberOfRooms = room.numRooms
        val localCurrency = room.exchange.map(_.local).getOrElse("")
        val usdToLocalExchange = getExchangeRate(ExchangeDataService.USD_CODE, localCurrency)
        val precision = usdToLocalExchange.map(_.numReqDecimal).getOrElse(defaultPrecision)
        val roomRefSellIn = room.dfFinanceByCurrency.local.roomRefSellIn

        val (localPromo, localSellIn, localCashback) =
          if (matchLocalCurrency) {
            (originalPromo, originalSellIn, originalCashbackEarning)
          } else {
            val promo = usdToLocalExchange.map(_.toReqRate(originalPromo).roundp(precision)).getOrElse(0d)
            val sellIn = usdToLocalExchange.map(_.toReqRate(originalSellIn).roundp(precision)).getOrElse(0d)
            val cashback = usdToLocalExchange.map(_.toReqRate(originalCashbackEarning).roundp(precision)).getOrElse(0d)
            (promo, sellIn, cashback)
          }

        val newDiscounts = soybeanAndDiscountStep.doCxlRebookPromoAndCashbackMatching(
          originalLocalPromo = localPromo,
          originalLocalCashbackEarning = localCashback,
          originalLocalSellIn = localSellIn,
          lengthOfStay = lengthOfStay,
          numberOfRooms = numberOfRooms,
          precision = precision,
          roomRefSellIn = roomRefSellIn,
        )

        val localDff = room.dfFinanceByCurrency.local
        val newDffByCurrency = room.dfFinanceByCurrency.copy(local = new DFFinance(
          new CustomerMarketing(newDiscounts, localDff.priceFreeze, None),
          localDff.supplyPrice,
          localDff.getAdditionalPricingInfo,
        ))

        room.copy(dfFinanceByCurrency = newDffByCurrency)
      }

      hotel.copy(rooms = updatedRooms)
    }
  }
}
