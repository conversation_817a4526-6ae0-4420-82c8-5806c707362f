stryker4s {
  base-dir: "core"
  reporters: ["console", "html", "json"]
  scala-dialect: "2.12"
  excluded-mutations: ["StringLiteral"]
  mutate: [
    "!src/main/scala/com/agoda/papi/pricing/core/execution/slicing/WeightedSlicer.scala",
    "!src/main/scala/com/agoda/papi/pricing/core/system/PrecacheService.scala",
    "!src/main/scala/logic/ApoFiltering.scala",
    "!src/main/scala/logic/PostExternalPriceFlow.scala",
    "!src/main/scala/logic/roomselecting/BookingAlternativeRoomsService.scala",
    "!src/main/scala/services/PostPricingService.scala",
    "!src/main/scala/services/sf/mappers/RoomMapperService.scala",
    "!src/main/scala/services/taxMetaData/TaxMetaDataService.scala",
    "!src/main/scala/settings/FilterHotelSettings.scala",
    "!src/main/scala/settings/NhaHotelSettings.scala",
    "!src/main/scala/settings/SsrRetrySettings.scala",
    "!src/main/scala/settings/RealtimeBlocklistSettings.scala",
    "!src/main/scala/com/agoda/papi/pricing/core/settings/SDAFilterSettings.scala",
    "!src/main/scala/logic/PreExternalPriceFlow.scala",
    "!src/main/scala/logging/AuctionDataMessage.scala",
    "!src/main/scala/logging/AuctionDataMessageComposer.scala",
    "!src/main/scala/settings/FallbackContractBlacklist.scala",
    "!src/main/scala/logic/PriceShopperSettings.scala",
    "!src/main/scala/config/cache/AgCacheProvider.scala",
    "!src/main/scala/com/agoda/papi/pricing/core/logging/**",
    "!target/**",
    "src/main/**/*.scala",
    "!src/main/scala/logic/papi/BookingItemProvider.scala",
    "!src/main/scala/services/SmartFlexService.scala"
  ]
  test-filter: [
    "!services.CancellationPolicyGroupSpec",
    "!com.agoda.papi.pricing.core.HotelSearchFlowSpec",
    "!com.agoda.papi.pricing.flow.postsearch.HotelMapperStepImplSpec",
    "!services.tangerine.AccountingRuleTest",
    "!services.packaging.PackagingServiceSpec",
    "!com.agoda.papi.pricing.supply.external.heisenberg.HeisenbergServiceImplSpec",
    "!services.PropertyAPIServiceSpec",
  ]
  max-test-runner-reuse: 0
  concurrency: 3
  debug { log-test-runner-stdout: true }
  scala-dialect: "2.12"
}
