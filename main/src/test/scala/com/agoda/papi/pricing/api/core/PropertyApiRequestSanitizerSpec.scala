package com.agoda.papi.pricing.api.core

import com.agoda.platform.pricing.models.utils.SFTestDataBuilders._
import models.starfruit._
import org.specs2.mutable.SpecificationWithJUnit

class PropertyApiRequestSanitizerSpec extends SpecificationWithJUnit {
  "PropertyApiRequestSanitizer" should {
    "Sanitize nulls" in {
      val nullOption: Option[Int] = null
      val noneOption: Option[Int] = None
      val someOption: Option[Int] = Some(5)

      PropertyApiRequestSanitizer.nullAsOption(nullOption) should_== None
      PropertyApiRequestSanitizer.nullAsOption(noneOption) should_== noneOption
      //  Equal by reference, don't need to create new object
      PropertyApiRequestSanitizer.nullAsOption(someOption) eq someOption should_== true
    }

    "Booking request should be tolerable to nulls" in {
      val bookingRequestWithNull = BookingRequest(
        priceState = null,
        filters = List(
          BookingFilter(
            masterRoomIds = null,
            uidList = null,
            matchedCriteria = null,
            roomIdentifiers = null,
          ),
        ),
        loggingRoomIdentifier = null,
        reBookingRequest = null,
        selectedHourlySlot = null,
        overrideBookingSellAllIn = null,
      )
      val requestWithNull: PropertySearchRequest = aValidPropertySearchRequest.withBookingRequest(bookingRequestWithNull)
      val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(requestWithNull)

      sanitizedRequest.pricing.payment should be(None)
      sanitizedRequest.pricing.mseHotelIds should be(None)
      sanitizedRequest.pricing.ppLandingHotelIds should be(None)
      sanitizedRequest.pricing.searchedHotelIds should be(None)
      sanitizedRequest.booking.get.priceState should be(None)
      sanitizedRequest.booking.get.filters.head.matchedCriteria should be(None)
      sanitizedRequest.booking.get.filters.head.uidList should be(None)
      sanitizedRequest.booking.get.filters.head.masterRoomIds should be(None)
      sanitizedRequest.booking.get.filters.head.roomIdentifiers should be(Nil)
      sanitizedRequest.booking.get.loggingRoomIdentifier should be(None)
      sanitizedRequest.booking.get.reBookingRequest should be(None)
      sanitizedRequest.booking.get.selectedHourlySlot should be(None)
      sanitizedRequest.booking.get.overrideBookingSellAllIn should be(None)
    }

    "ReBooking request should be tolerable to nulls" in {
      val mockReBookingRequest = ReBookingRequest(
        roomTypeId = 1,
        masterRoomTypeId = None,
        customerPaidPrice = 1.0,
        originalNetIn = null,
        originalCashback = None,
        originalPromoAmount = None,
        originalUsdToRequestExchangeRate = None,
        originalSellInUsd = None,
        actionType = None,
        originalSellIn = None,
        originalCashbackAmount = None,
      )
      val mockdBookingRequest = aValidBookingRequest.withReBookingRequest(mockReBookingRequest)
      val requestWithNull: PropertySearchRequest = aValidPropertySearchRequest.withBookingRequest(mockdBookingRequest)
      val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(requestWithNull)
      val reBookingRequest = sanitizedRequest.booking.get.reBookingRequest.get
      reBookingRequest.originalNetIn should be(None)
    }

    "Should handle nulls in context" in {
      val searchRequest: PropertySearchRequest = aValidPropertySearchRequest.withContextRequest(
        aValidContextRequest
          .withExperimentInfo(
            ExperimentInfo(trafficGroup = "", overridenAllocationVariantMap = null, forceUserVariant = null))
          .withRequestMeta(null)
          .withUserContext(null)
          .withTracingContext(null)
          .withPackaging(null),
      )
      val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(searchRequest)
      sanitizedRequest.context.experimentInfo.get.overridenAllocationVariantMap should be(None)
      sanitizedRequest.context.experimentInfo.get.forceUserVariant should be(None)
      sanitizedRequest.context.requestMeta should_== None
      sanitizedRequest.context.userContext should_== None
      sanitizedRequest.context.tracingContext should_== None
      sanitizedRequest.context.packaging should_== None

    }
  }

  "Should handle null in nosOfBedrooms" in {
    val baseRequest: PropertySearchRequest = aValidPropertySearchRequest
    val request: PropertySearchRequest = baseRequest.copy(
      pricing = baseRequest.pricing.copy(
        filters = baseRequest.pricing.filters.copy(
          nosOfBedrooms = null,
        ),
      ),
    )
    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.pricing.filters.nosOfBedrooms should be(None)
    sanitizedRequest.pricing.filters.roomIdentifierFilter should be(None)
  }

  "Should handle null in Pricing" in {
    val request: PropertySearchRequest = aValidPropertySearchRequest.withPricing(
      aValidPricingRequest
        .withRoomSelection(null)
        .withWhiteLabelKey(null)
        .withOccupancy(aValidOccupancyRequest.withChildrenTypes(null).withRoomsAssignment(null))
        .withClientCampaignInfos(null)
        .withSimplifiedRoomSelection(null)
        .withFeatureRequest(aValidFeatureRequest.build.copy(
          enableRatePlanCheckInCheckOut = null,
          showCouponAmountInUserCurrency = null,
          excludeVoucherRooms = null,
          enableCOR = null,
          enablePushDayUseRates = null,
          enableEscapesPackage = null,
          filterCheapestRoomEscapesPackage = null,
          enableRichContentOffer = null,
          disableEscapesPackage = null,
          returnCheapestEscapesOfferOnSSR = null,
          enableBenefitValuationForASO = null,
          enableDayUseCor = null,
          showCheapestHourlyRate = null,
          showPastMidnightSlots = null,
          enableHourlySlotsForDayuseInOvernight = null,
          enableThirtyMinsSlots = null,
          sortByCheckInTimeDayUseSSR = null,
        ))
        .withPartner(aValidPartnerRequest.build.copy(returnDailyRates = null))
        .withExcludeFilters(null)
        .withSelectedCheckInTime(null)
        .withMetadata(null)
        .withFilters(aValidFilterRequest.build.copy(hourlyDurations = null)),
    )
    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.pricing.roomSelection should be(None)
    sanitizedRequest.pricing.whiteLabelKey should be(None)
    sanitizedRequest.pricing.occupancy.roomsAssignment should be(None)
    sanitizedRequest.pricing.occupancy.childrenTypes should be(None)
    sanitizedRequest.pricing.clientCampaignInfos should be(None)
    sanitizedRequest.pricing.features.enableRatePlanCheckInCheckOut should be(None)
    sanitizedRequest.pricing.features.showCouponAmountInUserCurrency should be(None)
    sanitizedRequest.pricing.features.enablePushDayUseRates should be(None)
    sanitizedRequest.pricing.features.enableEscapesPackage should be(None)
    sanitizedRequest.pricing.features.filterCheapestRoomEscapesPackage should be(None)
    sanitizedRequest.pricing.features.excludeVoucherRooms should be(None)
    sanitizedRequest.pricing.features.enableCOR should be(None)
    sanitizedRequest.pricing.features.enableRichContentOffer should be(None)
    sanitizedRequest.pricing.partner.get.returnDailyRates should be(None)
    sanitizedRequest.pricing.simplifiedRoomSelectionRequest should be(None)
    sanitizedRequest.pricing.excludeFilters should be(None)
    sanitizedRequest.pricing.features.disableEscapesPackage should be(None)
    sanitizedRequest.pricing.features.returnCheapestEscapesOfferOnSSR should be(None)
    sanitizedRequest.pricing.features.enableBenefitValuationForASO should be(None)
    sanitizedRequest.pricing.features.enableDayUseCor should be(None)
    sanitizedRequest.pricing.features.showCheapestHourlyRate should be(None)
    sanitizedRequest.pricing.features.showPastMidnightSlots should be(None)
    sanitizedRequest.pricing.features.enableHourlySlotsForDayuseInOvernight should be(None)
    sanitizedRequest.pricing.features.enableThirtyMinsSlots should be(None)
    sanitizedRequest.pricing.selectedCheckInTime should be(None)
    sanitizedRequest.pricing.metadata should be(None)
    sanitizedRequest.pricing.filters.hourlyDurations should be(None)
  }

  "Should handle null in Pricing's FilterOutRequest" in {
    val request: PropertySearchRequest = aValidPropertySearchRequest.withPricing(
      aValidPricingRequest.withExcludeFilters(
        Some(
          FilterOutRequest(
            symmetricUidFilter = null,
          ))),
    )
    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.pricing.excludeFilters should not be None
    sanitizedRequest.pricing.excludeFilters.flatMap(_.symmetricUidFilter) should be(None)
  }

  "Should handle null in Pricing's PackagingFilterContext" in {
    val request: PropertySearchRequest = aValidPropertySearchRequest.withPricing(
      aValidPricingRequest.withFilters(aValidFilterRequest.withPackagingFilterContext(null)),
    )
    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.pricing.filters.packagingFilterContext should be(None)
  }

  "Should handle null in Pricing's PackagingFilterContext.CancellationGroupFilter" in {
    val request: PropertySearchRequest = aValidPropertySearchRequest.withPricing(
      aValidPricingRequest.withFilters(
        aValidFilterRequest.withPackagingFilterContext(Some(PackagingFilterContext(cancellationGroupFilter = null)))),
    )
    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.pricing.filters.packagingFilterContext.get.cancellationGroupFilter should be(None)
  }

  "Should remove duplicate hotel id's" in {
    val request: PropertySearchRequest = aValidPropertySearchRequest.withPropertyIds(List(1, 1))

    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.propertyIds.size should_== 1
  }

  "Should handle null in RocketmilesPublishPriceRequest" in {
    val request: PropertySearchRequest = aValidPropertySearchRequest.withRocketmilesPublishPriceRequest(null)
    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.rocketmilesPublishPriceRequest should be(None)
  }

  "Should handle null in RocketmilesPublishPriceLogicType" in {
    val request: PropertySearchRequest = aValidPropertySearchRequest.withRocketmilesPublishPriceRequest(
      Some(
        RocketmilesPublishPriceRequest(
          publishPriceLogic = null,
          sortByField = Some(RocketmileSortByFields.PriceOnly),
          roomAwarePublishPriceLogic = Some(RocketmilePublishPriceLogicTypes.BcomFirst),
        )))
    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.rocketmilesPublishPriceRequest.flatMap(_.publishPriceLogic) should be(None)
  }

  "Should handle null in RocketmilesSortByField" in {
    val request: PropertySearchRequest = aValidPropertySearchRequest.withRocketmilesPublishPriceRequest(
      Some(RocketmilesPublishPriceRequest(
        publishPriceLogic = Some(RocketmilePublishPriceLogicTypes.Cheapest),
        sortByField = null,
        roomAwarePublishPriceLogic = Some(RocketmilePublishPriceLogicTypes.BcomFirst),
      )))
    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.rocketmilesPublishPriceRequest.flatMap(_.sortByField) should be(None)
  }

  "Should handle null in roomAwarePublishPriceLogic" in {
    val request: PropertySearchRequest = aValidPropertySearchRequest.withRocketmilesPublishPriceRequest(
      Some(
        RocketmilesPublishPriceRequest(
          publishPriceLogic = Some(RocketmilePublishPriceLogicTypes.Cheapest),
          sortByField = Some(RocketmileSortByFields.PriceOnly),
          roomAwarePublishPriceLogic = null,
        )))
    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.rocketmilesPublishPriceRequest.flatMap(_.roomAwarePublishPriceLogic) should be(None)
  }

  "Should handle null in RocketmilesPublishPriceLogicType and RocketmilesSortByField" in {
    val request: PropertySearchRequest = aValidPropertySearchRequest.withRocketmilesPublishPriceRequest(
      Some(
        RocketmilesPublishPriceRequest(
          publishPriceLogic = null,
          sortByField = null,
          roomAwarePublishPriceLogic = null,
        )))
    val sanitizedRequest = PropertyApiRequestSanitizer.prepareRequest(request)
    sanitizedRequest.rocketmilesPublishPriceRequest.flatMap(_.publishPriceLogic) should be(None)
    sanitizedRequest.rocketmilesPublishPriceRequest.flatMap(_.sortByField) should be(None)
    sanitizedRequest.rocketmilesPublishPriceRequest.flatMap(_.roomAwarePublishPriceLogic) should be(None)
  }
}
