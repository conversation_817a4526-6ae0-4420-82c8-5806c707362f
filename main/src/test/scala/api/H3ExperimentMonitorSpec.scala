package api

import com.agoda.commons.http.server.measurements.MetricsContext
import com.agoda.commons.http.server.models.RequestContext
import io.opentelemetry.api.common.Attributes
import io.opentelemetry.api.metrics.LongHistogram
import io.opentelemetry.sdk.internal.AttributesMap
import models.flow.{FlowContext, FlowHotelStatsContext}
import org.mockito.ArgumentMatchers.{anyLong, argThat}
import org.mockito.Mockito
import org.mockito.Mockito.{verify, when}
import org.scalatest.wordspec.AnyWordSpec
import org.scalatestplus.mockito.MockitoSugar.mock
import utilhelpers.Measurements

class H3ExperimentMonitorSpec extends AnyWordSpec {
  class TestGoat extends H3ExperimentMonitor

  val testException = new NullPointerException()

  def registerH3StatForTest(ctx: FlowHotelStatsContext,
                            propertyId: Long,
                            isPush: Boolean,
                            usingH3v2: <PERSON><PERSON><PERSON>,
                            droppedRooms: Int): Unit = ctx.registerH3Stats(propertyId = propertyId,
                                                                           isPushFlow = isPush,
                                                                           enabled = true,
                                                                           isApplicable = true,
                                                                           droppedRoom = droppedRooms,
                                                                           usingH3v2 = usingH3v2,
                                                                           Set(),
                                                                           Set())

  def setupDependencies(statsCtx: FlowHotelStatsContext): (FlowContext, Option[RequestContext], LongHistogram) = {
    val flowCtx = mock[FlowContext]
    when(flowCtx.flowHotelStatsContext).thenReturn(statsCtx)

    val metricsCtx = mock[MetricsContext]
    when(metricsCtx.getAttributes).thenReturn(AttributesMap.create(10, 10))

    val requestCtx = mock[RequestContext]
    when(requestCtx.metrics).thenReturn(metricsCtx)

    val reportingService = mock[LongHistogram]

    (flowCtx, Some(requestCtx), reportingService)
  }

  def setupDependenciesForErrorCase(): (FlowContext, Option[RequestContext], LongHistogram) = {
    val flowCtx = mock[FlowContext]
    when(flowCtx.flowHotelStatsContext).thenThrow(new NullPointerException("Something went wrong"))

    val metricsCtx = mock[MetricsContext]
    when(metricsCtx.getAttributes).thenReturn(AttributesMap.create(10, 10))

    val requestCtx = mock[RequestContext]
    when(requestCtx.metrics).thenReturn(metricsCtx)

    val reportingService = mock[LongHistogram]

    (flowCtx, Some(requestCtx), reportingService)
  }

  def verifySentMetric(reportingService: LongHistogram, h3Variants: String): Unit =
    verify(reportingService, Mockito.atLeastOnce()).record(
      anyLong(),
      argThat((att: Attributes) => att.get(Measurements.Attributes.h3Variants) == h3Variants),
    )

  def verifyMetricNotSent(reportingService: LongHistogram, h3Variants: String): Unit =
    verify(reportingService, Mockito.never()).record(
      anyLong(),
      argThat((att: Attributes) => att.get(Measurements.Attributes.h3Variants) == h3Variants),
    )

  "H3ExperimentMonitor" should {
    "should not set tags when no h3 stats are registered" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, None)

      verifyMetricNotSent(reportingService, h3Variants = "")
    }

    "should not set tags when request context is unavailable" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()

      val (flowCtx, _, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, None, None)

      verifyMetricNotSent(reportingService, h3Variants = "")
    }

    "should not set tags and return without error when it fails to get hotel stats" in {
      val testGoat = new TestGoat()

      val (flowCtx, requestCtx, reportingService) = setupDependenciesForErrorCase()
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, None)

      verifyMetricNotSent(reportingService, h3Variants = "")
    }

    "should set correct tags when only one hotel, variant = A and impact on only push flow" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 111, isPush = true, usingH3v2 = false, droppedRooms = 1)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, None)

      verifySentMetric(reportingService, h3Variants = "a")
    }

    "should set correct tags when only one hotel, variant = B and impact on only push flow" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 123, isPush = true, usingH3v2 = true, droppedRooms = 10)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, None)

      verifySentMetric(reportingService, h3Variants = "b")
    }

    "should set correct tags when only one hotel, variant = A and impact on only pull flow" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 333, isPush = false, usingH3v2 = false, droppedRooms = 2)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, Some(testException))

      verifySentMetric(reportingService, h3Variants = "a")
    }

    "should set correct tags when only one hotel, variant = B and impact on only pull flow" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 222, isPush = false, usingH3v2 = true, droppedRooms = 90)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, None)

      verifySentMetric(reportingService, h3Variants = "b")
    }

    "should set correct tags when only one hotel, variant = A and impact on both push and pull flow" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 222, isPush = true, usingH3v2 = false, droppedRooms = 1)
      registerH3StatForTest(hotelStatsCtx, propertyId = 222, isPush = false, usingH3v2 = false, droppedRooms = 2)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, None)

      verifySentMetric(reportingService, h3Variants = "a")
    }

    "should set correct tags when only one hotel, variant = B and impact on both push and pull flow" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 222, isPush = true, usingH3v2 = true, droppedRooms = 1)
      registerH3StatForTest(hotelStatsCtx, propertyId = 222, isPush = false, usingH3v2 = true, droppedRooms = 2)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, Some(testException))

      verifySentMetric(reportingService, h3Variants = "b")
    }

    "should set correct tags when two hotels, variant = A on one variant B on the other and impact on push or pull flow" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 42, isPush = true, usingH3v2 = true, droppedRooms = 1)
      registerH3StatForTest(hotelStatsCtx, propertyId = 65, isPush = false, usingH3v2 = false, droppedRooms = 7)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, None)

      verifySentMetric(reportingService, h3Variants = "ab")
    }

    "should set correct tags when two hotels, variant = A on one variant B on the other and impact on push for both" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 42, isPush = true, usingH3v2 = true, droppedRooms = 2)
      registerH3StatForTest(hotelStatsCtx, propertyId = 65, isPush = true, usingH3v2 = false, droppedRooms = 1)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, None)

      verifySentMetric(reportingService, h3Variants = "ab")
    }

    "should set correct tags when two hotels, variant = A on one variant B on the other and impact on pull for both" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 42, isPush = false, usingH3v2 = true, droppedRooms = 2)
      registerH3StatForTest(hotelStatsCtx, propertyId = 65, isPush = false, usingH3v2 = false, droppedRooms = 1)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, None)

      verifySentMetric(reportingService, h3Variants = "ab")
    }

    "should set correct tags when two hotels, variant = A and impact on pull or push flow" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 42, isPush = true, usingH3v2 = false, droppedRooms = 2)
      registerH3StatForTest(hotelStatsCtx, propertyId = 65, isPush = false, usingH3v2 = false, droppedRooms = 1)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, None)

      verifySentMetric(reportingService, h3Variants = "a")
    }

    "should set correct tags when two hotels, variant = B and impact on pull or push flow" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 42, isPush = false, usingH3v2 = true, droppedRooms = 2)
      registerH3StatForTest(hotelStatsCtx, propertyId = 65, isPush = true, usingH3v2 = true, droppedRooms = 1)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, Some(testException))

      verifySentMetric(reportingService, h3Variants = "b")
    }

    "should not set tags when two hotels, variant = B and no impact (0 rooms dropped) on pull or push flow" in {
      val testGoat = new TestGoat()

      val hotelStatsCtx = new FlowHotelStatsContext()
      registerH3StatForTest(hotelStatsCtx, propertyId = 42, isPush = false, usingH3v2 = true, droppedRooms = 0)
      registerH3StatForTest(hotelStatsCtx, propertyId = 65, isPush = true, usingH3v2 = false, droppedRooms = 0)

      val (flowCtx, requestCtx, reportingService) = setupDependencies(hotelStatsCtx)
      testGoat.sendH3RequestMetric(1000, reportingService, flowCtx, requestCtx, Some(testException))

      verifyMetricNotSent(reportingService, h3Variants = "b")
      verifyMetricNotSent(reportingService, h3Variants = "a")
    }
  }
}
