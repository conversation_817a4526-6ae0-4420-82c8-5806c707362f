package api

import com.agoda.commons.http.server.models.RequestContext
import com.typesafe.scalalogging.Logger
import io.opentelemetry.api.metrics.LongHistogram
import models.flow.FlowContext
import utilhelpers.Measurements.Attributes

trait H3ExperimentMonitor {
  private val logger = Logger("h3-monitoring")

  def sendH3RequestMetric(startTime: Long,
                          histogram: LongHistogram,
                          ctx: FlowContext,
                          requestContextOpt: Option[RequestContext],
                          exceptionOpt: Option[Throwable]): Unit =
    try requestContextOpt match {
        case Some(requestContext: RequestContext) =>
          val (hasImpact, h3ExpVariants) = ctx.flowHotelStatsContext.getH3ImpactFromStats()

          if (hasImpact) {
            val attributeBuilder = requestContext.metrics.getAttributes.toBuilder
            attributeBuilder.put(Attributes.h3Variants, h3ExpVariants)
            attributeBuilder.put(Attributes.status, if (exceptionOpt.isEmpty) "success" else "error")

            val latency = System.currentTimeMillis() - startTime
            histogram.record(latency, attributeBuilder.build())
          }
        case _ =>
      }
    catch {
      case e: Exception => logger.error("failed send h3 impact metric", e)
    }
}
