package com.agoda.papi.pricing.api.core

import akka.pattern.CircuitBreakerOpenException
import api.H3ExperimentMonitor
import api.request.{BaseRequest, FeatureFlag}
import api.routing.dsl.context.FlowStat
import api.routing.dsl.exception.RFlowTimeoutException
import com.agoda.commons.observability.api.tracing.FeatureTracer
import com.agoda.experiments.state.ExperimentsApplicationState
import com.agoda.papi.pricing.api.core.ServiceAPI.{ProcessRequest, ReportMetrics}
import com.agoda.papi.pricing.configuration.{DegradationSettingProducer, SandboxLogSettingProducer}
import com.agoda.papi.pricing.flow.{FlowContext, FlowContextImpl}
import com.agoda.papi.pricing.supply.db.compositeChannelData.CompositeChannelContext
import com.agoda.platform.api.backpressure.RequestDegradationModes
import com.agoda.platform.api.backpressure.RequestDegradationModes.RequestDegradationMode
import com.agoda.platform.api.contract.{
  ApplicationClientException,
  CircuitBreakerClientException => AgCircuitBreakerClientException,
  FailFastClientException => AgFailFastClientException,
  ProcessingTimeoutClientException => AgProcessingTimeoutClientException,
}
import com.agoda.utils.Implicits._
import com.agoda.utils.monitoring.{AggregateReporter, ReportingService}
import com.google.common.util.concurrent.RateLimiter
import com.typesafe.scalalogging.Logger
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.api.trace.Tracer
import log.LoggerFactory
import logging.RequestLogger
import models.consts.ABTest
import models.consts.ABTest.WhiteListStackChannelFlagPlatform
import models.flow.{ContextHolder, FlowContext, HeadersContext, Variant}
import models.starfruit.PropertySearchRequest
import models.PrecheckAccuracies
import services.RequestSettingPreparationService
import services.language.LanguageDataService
import utilhelpers.Measurements

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

/**
  * Interface of Service API, an entry point for cluster service
  */
object ServiceAPI {

  type ReportMetrics = (PropertySearchRequest, Option[FlowContext], Long, String) => Unit
  type ProcessRequest[T] = (PropertySearchRequest, FlowContext, ContextHolder) => Future[T]

  def getStatString(stats: List[FlowStat]): String =
    s"${stats.map(st => s"${st.name}: ${st.startTimeTimeline}").mkString(" > ")}"

  private[core] def getExceptionTagType: Throwable => String = {
    case _: AgFailFastClientException => Measurements.tagBackpressure
    case _: AgProcessingTimeoutClientException => Measurements.tagTimeout
    case _: AgCircuitBreakerClientException => Measurements.tagCircuitBreaker
    case _ => Measurements.tagFailure
  }

  /**
    * Report measurements with different types - success / failure / timeout / circuitbreaker / etc.
    */
  private[core] def reportMetricsOnComplete[T](processF: Future[T],
                                               request: PropertySearchRequest,
                                               context: Option[FlowContext],
                                               startTime: Long,
                                               report: ReportMetrics)(implicit ec: ExecutionContext): Unit =
    processF.onComplete {
      case Success(_) =>
        //  Here we'd like to report true status, with checking context for expiration
        val finalStatus =
          if (context.exists(_.isExpired)) Measurements.tagTimeout
          else Measurements.tagSuccess
        report(request, context, startTime, finalStatus)
      case Failure(ex) => report(request, context, startTime, getExceptionTagType(ex))
    }
}

trait ServiceAPI {

  /**
    * Apply rate-limiting / circuit-breaker / other services on top of b
    *
    * @param processRequest Delegate for actual request processing
    * @param report         Delegate for actual request reporting
    * @return Future with success result or failed with system or application exception;
    *         check com.agoda.cluster.contract.* exceptions to find out potential response types
    */
  def process[T](request: PropertySearchRequest, context: ContextHolder, emptyResult: T)(
    processRequest: ProcessRequest[T],
    report: ReportMetrics,
  )(implicit ec: ExecutionContext): ResponseWithContext[T]

  val perfLogger: Logger
}

class ServiceAPIImpl(requestLogger: RequestLogger,
                     reportingService: ReportingService,
                     aggregateReporter: AggregateReporter,
                     requestSettingPreparationService: RequestSettingPreparationService,
                     compositeChannelContext: CompositeChannelContext,
                     languageDataService: LanguageDataService,
                     sandboxLogSettingProducer: SandboxLogSettingProducer,
                     degradationSettingProducer: DegradationSettingProducer,
                     tracer: Tracer,
                     featureTracer: FeatureTracer,
                     appState: ExperimentsApplicationState,
                     loggerFactory: LoggerFactory)
  extends ServiceAPI
    with H3ExperimentMonitor {
  require(reportingService.isNotNull)

  private val performanceLogRateLimit = RateLimiter.create(5)

  override lazy val perfLogger = loggerFactory.getLogger("PAPIService.performance")
  private lazy val validationLogger = loggerFactory.getLogger("PAPIService.requestValidation")
  private lazy val sanitizeLogger = loggerFactory.getLogger("PAPIService.requestsanitizer")

  private val h3RequestsHistogram = GlobalOpenTelemetry
    .getMeter("propertyapipricingdocker")
    .histogramBuilder(Measurements.h3ExpRequestLatency)
    .ofLongs()
    .build()

  def timeoutLog(ex: RFlowTimeoutException, request: PropertySearchRequest): Unit = {
    val timeoutMessage =
      s"Flow timeout on step=${ex.flow}, timeout/limit = ${ex.timeoutMs}/${ex.limitMs} ms, propertyIds=${request.propertyIds
          .mkString(",")}, requestType=${request.getRequestType}, searchId=${request.context.clientInfo.searchId}, whitelabelKey=${request.pricing.whiteLabelKey}, " +
        s"\n${ServiceAPI.getStatString(ex.flowStats)}"
    if (performanceLogRateLimit.tryAcquire()) {
      perfLogger.warn(timeoutMessage)
    }
    reportingService.report(Measurements.flowTimeout, ex.timeoutMs, Map("flow" -> ex.flow))
  }

  private[core] def warnValidationFailure(ex: Throwable, reason: String): Unit = {
    validationLogger.warn(ex.getMessage)
    reportingService.report(Measurements.invalidRequest, 1, tags = Map("reason" -> reason))
  }

  private[core] def sanitizeFailure(ex: Throwable): Unit = sanitizeLogger.error(ex.getMessage, ex)

  private[core] def convertAPIException: PartialFunction[Throwable, Throwable] = {
    case ex: RFlowTimeoutException => new AgProcessingTimeoutClientException(Option(ex.timeoutMs.millis))
    case ex: CircuitBreakerOpenException => new AgCircuitBreakerClientException(Option(ex.remainingDuration))
    case ex => new ApplicationClientException(ex)
  }

  /**
    * Note that we are going to wrap Circuit Breaker call by request gate keeper;
    * Eventually circuit breaker will be open only for periods when one of the required subsystems is completely down
    */
  override def process[T](request: PropertySearchRequest, context: ContextHolder, emptyResult: T)(
    processRequest: ProcessRequest[T],
    report: ReportMetrics)(implicit ec: ExecutionContext): ResponseWithContext[T] = {

    val startTime = System.currentTimeMillis()

    logRequestForSandbox(request, context.headersContext)

    val (resWithContext, failureReason) = RequestValidator.processIfValid(request) { request =>
      requestLogger.logAPIRequest(request, startTime)
      val languageUpdatedRequest = handleLocaleString(request)

      val responseCtx =
        sanitizeRequestAndProcess[T](languageUpdatedRequest, context) { (sanitizedRequest, updatedCtx) =>
          val resultF = processRequest(languageUpdatedRequest, updatedCtx, context)

          val responseWithAPIExceptionF = resultF.transform {
            case f @ Success(_) =>
              val x = GlobalOpenTelemetry.get()
              sendH3RequestMetric(startTime, h3RequestsHistogram, updatedCtx, context.requestContext, None)

              f
            case f @ Failure(ex: Throwable) =>
              sendH3RequestMetric(startTime, h3RequestsHistogram, updatedCtx, context.requestContext, Some(ex))

              ex match {
                case timeout: RFlowTimeoutException => timeoutLog(timeout, request)
                case _ =>
              }
              ex match {
                case e if convertAPIException.isDefinedAt(e) => Failure(convertAPIException(e))
                case _ => f
              }
          }

          ResponseWithContext(responseWithAPIExceptionF, Some(updatedCtx))
        }

      ServiceAPI.reportMetricsOnComplete(responseCtx.response,
                                         languageUpdatedRequest,
                                         responseCtx.context,
                                         startTime,
                                         report)
      responseCtx
    }

    /*
     Ideally request validation should follow the pattern of gatekeeper, but that pattern assumes to have FlowContext that should be created
     with a DF BaseRequest. Validation though needs to happen beforehand, so we need a custom behavior.
     */
    resWithContext match {
      case Right(rwc) => rwc
      case Left(ex) =>
        requestLogger.logAPIRequest(request, startTime, isRejected = true)
        warnValidationFailure(ex, failureReason)
        ResponseWithContext(Future.successful(emptyResult), None)
    }
  }

  private[core] def handleLocaleString(preparedRequest: PropertySearchRequest): PropertySearchRequest =
    preparedRequest.context.clientInfo.locale.fold(preparedRequest) { locale =>
      val languageId =
        languageDataService.localeToLanguageId(locale).getOrElse(preparedRequest.context.clientInfo.languageId)
      val languageUse =
        languageDataService.localeToLanguageUse(locale).getOrElse(preparedRequest.context.clientInfo.languageUse)
      val updatedClientInfo = preparedRequest.context.clientInfo.copy(languageId = languageId, languageUse = languageUse)
      val updatedRequestContext = preparedRequest.context.copy(clientInfo = updatedClientInfo)
      preparedRequest.copy(context = updatedRequestContext)
    }

  private[core] def sanitizeRequestAndProcess[T](request: PropertySearchRequest, ctxHolder: ContextHolder)(
    f: (BaseRequest, FlowContext) => ResponseWithContext[T],
  ): ResponseWithContext[T] = Try {
    val dfRequest = PropertyApiRequestConverter
      .propertySearchRequestToBaseRequest(request, ctxHolder)(requestSettingPreparationService, compositeChannelContext)
    val context: FlowContextImpl =
      FlowContext(dfRequest, tracer, featureTracer, appState, reportingService, aggregateReporter)
    // We have some experiments that allocate to change our search request, repeat the allocations to send them out.
    ctxHolder.earlyExperiments.foreach(context.experimentDataHolder.determineVariant)
    val featureFlags = getDegradeFeatureFlags(context, ctxHolder.degradationContext.priority, dfRequest.featureFlags)
    val updatedDFRequest = migrateDeprecatedFields(dfRequest.copy(featureFlags = featureFlags), context)
    val updatedCtxWithChildAge = context.updateRequest(updatedDFRequest)

    (updatedDFRequest, updatedCtxWithChildAge)
  } match {
    case Success((sanitizedRequest, ctx)) => f(sanitizedRequest, ctx)
    case Failure(exception) =>
      sanitizeFailure(exception)
      ResponseWithContext(Future.failed(exception), None)
  }

  private[core] def migrateDeprecatedFields(request: BaseRequest, ctx: FlowContext): BaseRequest =
    if (request.supplierPullMetadata.requiredPrecheckAccuracy == PrecheckAccuracies.NONE && ctx.determineVariant(
        ABTest.DEPRECATE_LOW_PRECHECK_ACCURACY) == Variant.B) {
      request.copy(supplierPullMetadata = request.supplierPullMetadata.copy(PrecheckAccuracies.HIGH))
    } else {
      request
    }

  private[core] def logRequestForSandbox(request: PropertySearchRequest, headersContext: Option[HeadersContext]): Unit =
    if (sandboxLogSettingProducer.current.shouldLog(request)) {
      requestLogger.logBookingRequest(request, headersContext)
    }

  private[core] def getDegradeFeatureFlags(ctx: FlowContext,
                                           priority: RequestDegradationMode,
                                           flags: List[FeatureFlag]): List[FeatureFlag] = {
    def isApplicablePriority(current: RequestDegradationMode, want: RequestDegradationMode): Boolean =
      current.id >= want.id

    val setting = degradationSettingProducer.current
    val filterFlags: Set[FeatureFlag] = Set(
      if (ctx.determineVariant(ABTest.APSPeekDegradation) == Variant.B || (isApplicablePriority(
          priority,
          RequestDegradationModes.Low) && setting.apsPeekEnabled)) {
        Set(FeatureFlag.APSPeek)
      } else {
        Nil
      },
      if (ctx.determineVariant(ABTest.MixNSaveDegradation) == Variant.B || (isApplicablePriority(
          priority,
          RequestDegradationModes.Low) && setting.mixAndSaveEnabled)) {
        Set(FeatureFlag.MixAndSave)
      } else {
        Nil
      },
      if (isApplicablePriority(priority, RequestDegradationModes.Medium) && setting.losNudgeEnabled) {
        Set(FeatureFlag.LongStayNudge)
      } else {
        Nil
      },
    ).flatten

    val additionalFlags: Set[FeatureFlag] = Set(
      if (isApplicablePriority(
          priority,
          RequestDegradationModes.High) && setting.suppliersEnabled && !setting.sdaAggressiveFiltersEnabled) {
        Set(FeatureFlag.DisableDegradedSupplier)
      } else {
        Nil
      },
      if (ctx.determineVariant(ABTest.SoybeanDegradation) == Variant.B || (isApplicablePriority(
          priority,
          RequestDegradationModes.High) && setting.soybeanEnabled)) {
        Set(FeatureFlag.DisableSoybean)
      } else {
        Nil
      },
      if (ctx.determineVariant(ABTest.HeisenbergDegradation) == Variant.B || (isApplicablePriority(
          priority,
          RequestDegradationModes.High) && setting.heisenbergEnabled)) {
        Set(FeatureFlag.DisableHeisenberg)
      } else {
        Nil
      },
      // Pre condition, this should impact only on Web Site, Mobile Web and Mobile App
      if (WhiteListStackChannelFlagPlatform.contains(ctx.baseRequest.platformId)) {
        Set(FeatureFlag.StackChannelDiscount)
      } else {
        Nil
      },
    ).flatten

    (flags.filter(f => !filterFlags.contains(f)) ++ additionalFlags).distinct
  }
}
