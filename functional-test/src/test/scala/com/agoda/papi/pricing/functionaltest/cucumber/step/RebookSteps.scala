package com.agoda.papi.pricing.functionaltest.cucumber.step

import io.cucumber.scala.{EN, ScalaDsl}
import models.enums.ReBookingActionType
import models.starfruit.{BookingRequest, ReBookingRequest}

import java.util.Optional
import scala.compat.java8.OptionConverters._

trait RebookSteps extends ScalaDsl with EN {

  var bookingRequest: BookingRequest

  Given(
    """^ReBook with roomTypeID (\d+) originalSellInUsd (\d+\.\d+)(?:\s+masterRoomTypeId (\d+))?(?:\s+originalUsdToRequestExchangeRate (\d+\.\d+))?(?:\s+originalCashback (\-?\d+\.\d+))?(?:\s+originalPromoAmount (\-?\d+\.\d+))?$""") {
    (roomTypeId: Long,
     originalSellInUsd: Double,
     masterRoomTypeId: Optional[Long],
     exchangeRate: Optional[String],
     cashback: Optional[String],
     promoAmount: Optional[String]) =>
      val rebookingRequest = ReBookingRequest(
        roomTypeId = roomTypeId,
        masterRoomTypeId = masterRoomTypeId.asScala,
        customerPaidPrice = 0.0,
        originalNetIn = None,
        originalCashback = cashback.asScala.map(_.toDouble),
        originalPromoAmount = promoAmount.asScala.map(_.toDouble),
        originalUsdToRequestExchangeRate = exchangeRate.asScala.map(_.toDouble),
        originalSellInUsd = Some(originalSellInUsd),
        originalSellIn = None,
        originalCashbackAmount = None,
        actionType = None,
      )
      bookingRequest = bookingRequest.copy(reBookingRequest = Some(rebookingRequest))
  }

  Given(
    """^ReBook with actionType (.+) roomTypeID (\d+) originalSellIn (\d+\.\d+)(?:\s+originalUsdToRequestExchangeRate (\-?\d+\.\d+))?(?:\s+masterRoomTypeId (\d+))?(?:\s+originalCashbackAmount (\-?\d+\.\d+))?(?:\s+originalPromoAmount (\-?\d+\.\d+))?$""") {
    (actionType: String,
     roomTypeId: Long,
     originalSellIn: Double,
     originalUsdToRequestExchangeRate: Optional[String],
     masterRoomTypeId: Optional[Long],
     originalCashbackAmount: Optional[String],
     originalPromoAmount: Optional[String]) =>
      val rebookingRequest = ReBookingRequest(
        roomTypeId = roomTypeId,
        masterRoomTypeId = masterRoomTypeId.asScala,
        customerPaidPrice = 0.0,
        originalNetIn = None,
        originalCashback = None,
        originalUsdToRequestExchangeRate = originalUsdToRequestExchangeRate.asScala.map(_.toDouble),
        originalSellInUsd = None,
        originalSellIn = Some(originalSellIn),
        originalPromoAmount = originalPromoAmount.asScala.map(_.toDouble),
        originalCashbackAmount = originalCashbackAmount.asScala.map(_.toDouble),
        actionType = Some(ReBookingActionType.fromString(actionType)),
      )
      bookingRequest = bookingRequest.copy(reBookingRequest = Some(rebookingRequest))
  }

  Given("""^ReBook with roomTypeID (\d+)(?: masterRoomTypeId (\d+))?$""") {
    (roomTypeId: Long, masterRoomTypeId: Optional[Long]) =>
      val rebookingRequest = ReBookingRequest(
        roomTypeId = roomTypeId,
        masterRoomTypeId = masterRoomTypeId.asScala,
        customerPaidPrice = 0.0,
        originalNetIn = None,
        originalCashback = None,
        originalPromoAmount = None,
        originalUsdToRequestExchangeRate = None,
        originalSellInUsd = None,
        actionType = None,
        originalSellIn = None,
        originalCashbackAmount = None,
      )
      bookingRequest = bookingRequest.copy(reBookingRequest = Some(rebookingRequest))
  }
}
