package com.agoda.papi.pricing.functionaltest.framework.mock

import generated.model.DmcControlSettingModel
import models.consts.{DeviceTypeId, Whitelabel}
import models.starfruit.PropertySearchRequest
import models.whitelabel._
import services.whitelabel.{WhiteLabelService, WhitelabelServiceException}

class MockWhiteLabelService extends WhiteLabelService {

  override def getWhitelabelSetting(whiteLabelKey: Option[String], agEnv: Option[String])(implicit
    request: PropertySearchRequest): Either[WhitelabelServiceException, WhitelabelSetting] = {
    val whitelabelSettings: Map[String, WhitelabelSetting] = Map.apply(
      "F1A5905F-9620-45E5-9D91-D251C07E0B42" -> WhitelabelSetting(
        Whitelabel.Agoda,
        Map.empty,
        Set.empty,
        SupplierConfiguration(
          CommissionAndMarginOverride(isCommissionOverride = true,
                                      isCommissionAdjustment = true,
                                      isMarginAdjustment = true,
                                      isAdjustCommissionFromHotelContract = false)),
        None,
        Coupon(None),
        ExchangeRateConfiguration(false),
        List.empty,
        List.empty,
        showExclusivePriceWithFeesForDestinationConfigs = Some(
          RegulationShowExclusivePriceWithFeesForDestinationConfigs(
            hotelCountryId = Some(181),
            hotelStateId = Some(List(18, 37)),
          )),
      ),
      "BB7D5528-D107-40CC-8BB0-C5DD5A4F3053" -> WhitelabelSetting(
        Whitelabel.Ana,
        Map.empty,
        Set.empty,
        SupplierConfiguration(
          CommissionAndMarginOverride(isCommissionOverride = true,
                                      isCommissionAdjustment = true,
                                      isMarginAdjustment = true,
                                      isAdjustCommissionFromHotelContract = true)),
        loyaltyProgramSetting = Some(
          LoyaltyProgram(
            rounding = None,
            externalWallet = None,
            minimumRedemptionPoints = None,
            minimumGatewayAmount = None,
            pointsStepUnitWithCurrency = Map.empty,
          )),
        Coupon(None),
        ExchangeRateConfiguration(false),
        List.empty,
        List.empty,
        isCustomerSegmentValidationEnabled = true,
      ),
      "EDF39F14-D569-4B22-882A-3BB9F6090F47" -> WhitelabelSetting(
        Whitelabel.Rurubu,
        Map.empty,
        Set.empty,
        SupplierConfiguration(
          CommissionAndMarginOverride(isCommissionOverride = true,
                                      isCommissionAdjustment = true,
                                      isMarginAdjustment = true,
                                      isAdjustCommissionFromHotelContract = true)),
        loyaltyProgramSetting = Some(
          LoyaltyProgram(
            rounding = None,
            externalWallet = None,
            minimumRedemptionPoints = None,
            minimumGatewayAmount = None,
            pointsStepUnitWithCurrency = Map.empty,
          )),
        Coupon(None),
        ExchangeRateConfiguration(false),
        List.empty,
        List.empty,
        isCustomerSegmentValidationEnabled = true,
        isRurubuWl = true,
        mainSupplier = 29014,
      ),
      "95708C5D-0E2B-4B02-BF4D-64AB94D27EE4" -> WhitelabelSetting(
        Whitelabel.RurubuUAT,
        Map.empty,
        Set.empty,
        SupplierConfiguration(
          CommissionAndMarginOverride(isCommissionOverride = true,
                                      isCommissionAdjustment = true,
                                      isMarginAdjustment = true,
                                      isAdjustCommissionFromHotelContract = true)),
        loyaltyProgramSetting = Some(
          LoyaltyProgram(
            rounding = None,
            externalWallet = None,
            minimumRedemptionPoints = None,
            minimumGatewayAmount = None,
            pointsStepUnitWithCurrency = Map.empty,
          )),
        Coupon(None),
        ExchangeRateConfiguration(false),
        List.empty,
        List.empty,
        isCustomerSegmentValidationEnabled = true,
        isRurubuWl = true,
        mainSupplier = 99901,
      ),
      "A876A852-CA8F-4F2D-9A6B-718D3ED6C514" -> WhitelabelSetting(
        Whitelabel.Japanican,
        Map.empty,
        Set.empty,
        SupplierConfiguration(
          CommissionAndMarginOverride(isCommissionOverride = false,
                                      isCommissionAdjustment = false,
                                      isMarginAdjustment = false,
                                      isAdjustCommissionFromHotelContract = true)),
        loyaltyProgramSetting = Some(
          LoyaltyProgram(
            rounding = None,
            externalWallet = None,
            minimumRedemptionPoints = None,
            minimumGatewayAmount = None,
            pointsStepUnitWithCurrency = Map.empty,
          )),
        Coupon(None),
        ExchangeRateConfiguration(false),
        List.empty,
        List.empty,
        isCustomerSegmentValidationEnabled = true,
        isJapanicanWl = true,
        mainSupplier = 29014,
      ),
      "46BFD421-3F38-45FD-A1CF-58766401AFDC" -> WhitelabelSetting(
        Whitelabel.Jtb,
        Map.empty,
        Set.empty,
        SupplierConfiguration(
          CommissionAndMarginOverride(isCommissionOverride = false,
                                      isCommissionAdjustment = false,
                                      isMarginAdjustment = false,
                                      isAdjustCommissionFromHotelContract = false)),
        loyaltyProgramSetting = Some(
          LoyaltyProgram(
            rounding = None,
            externalWallet = None,
            minimumRedemptionPoints = None,
            minimumGatewayAmount = None,
            pointsStepUnitWithCurrency = Map.empty,
          )),
        Coupon(None),
        ExchangeRateConfiguration(false),
        List.empty,
        List.empty,
        isCustomerSegmentValidationEnabled = true,
        mainSupplier = 29014,
      ),
      "3B2CAB10-38AF-4FEF-9016-90AA704D3292" -> WhitelabelSetting(
        Whitelabel.JtbUAT,
        Map.empty,
        Set.empty,
        SupplierConfiguration(
          CommissionAndMarginOverride(isCommissionOverride = true,
                                      isCommissionAdjustment = true,
                                      isMarginAdjustment = true,
                                      isAdjustCommissionFromHotelContract = true)),
        loyaltyProgramSetting = Some(
          LoyaltyProgram(
            rounding = None,
            externalWallet = None,
            minimumRedemptionPoints = None,
            minimumGatewayAmount = None,
            pointsStepUnitWithCurrency = Map.empty,
          )),
        Coupon(None),
        ExchangeRateConfiguration(false),
        List.empty,
        List.empty,
        isCustomerSegmentValidationEnabled = true,
        mainSupplier = 99901,
      ),
      "0CDCF21E-DB0E-426D-A525-81BE2D4368DA" -> WhitelabelSetting(
        999,
        Map(29014 -> false),
        Set(),
        SupplierConfiguration(
          CommissionAndMarginOverride(isCommissionOverride = true,
                                      isCommissionAdjustment = true,
                                      isMarginAdjustment = true,
                                      isAdjustCommissionFromHotelContract = true)),
        Some(
          LoyaltyProgram(rounding = None,
                         externalWallet = None,
                         minimumRedemptionPoints = None,
                         minimumGatewayAmount = None,
                         pointsStepUnitWithCurrency = Map())),
        Coupon(None),
        ExchangeRateConfiguration(disableUpliftExchangeRate = false),
        List(),
        List(),
        isBreakfastAndDinnerIncludeEnable = false,
        isCustomerSegmentValidationEnabled = true,
        List(),
        blockYCSPromotions = false,
        isPromotionPeekAllowedOnBFOnly = false,
        filterOutAgencyPaymentModel = false,
        List(),
        enabledCashbackRedemption = false,
        enablePublishPriceESS = false,
        enableM150MaxTransparency = false,
        enableM150BaselineTransparency = false,
        isSellingDifferentSuppliersForJtbEnabled = false,
        List(),
        List(),
        exactMatchOccupancy = false,
        blockBNPLForJapanOutboundEnabled = false,
        None,
        isRurubuWl = false,
        isJapanicanWl = false,
        List(),
        Set(),
        controlDirectConnectSupplyEnabled = false,
        allowedLeadDaysToNonRefundable = 0,
      ),
    )

    if (whiteLabelKey.isEmpty) {
      Right(whitelabelSettings("F1A5905F-9620-45E5-9D91-D251C07E0B42"))
    } else if (whitelabelSettings.contains(whiteLabelKey.get.toUpperCase)) {
      Right(whitelabelSettings(whiteLabelKey.get.toUpperCase))
    } else {
      val errorMessage = "Unknown whiteLabelKey for MockWhiteLabelService"
      Left(new WhitelabelServiceException(whiteLabelKey, errorMessage, new Exception(errorMessage)))
    }
  }

  override def getRegulationFeaturesEnabledSetting(agEnv: Option[String], agOriginState: Option[String])(implicit
    request: PropertySearchRequest): Either[WhitelabelServiceException, RegulationFeatureEnabledSetting] = Right(
    RegulationFeatureEnabledSetting(
      isShowExclusivePriceWithFeeEnabled = request.context.clientInfo.origin.contains("US"),
      isDynamicDownliftEnabled = request.context.clientInfo.origin
        .contains("IN") && Set(DeviceTypeId.IPhoneMobileAPI, DeviceTypeId.AndroidPhoneMobileAPI, DeviceTypeId.MobileWeb)
        .contains(request.context.clientInfo.deviceTypeId.getOrElse(-1)),
      isAllowExclusivePrice = request.suggestedPrice != "AllInclusive",
      isShowExclusivePriceWithFeesForDestination = true,
      isEnableHotelLocalCurrency = request.context.clientInfo.origin.contains("JP") && request.pricing.whiteLabelKey
        .getOrElse("")
        .equalsIgnoreCase("A876A852-CA8F-4F2D-9A6B-718D3ED6C514"),
      isValueTagEnabled =
        !(
          request.context.clientInfo.origin.contains("SG") ||
          request.context.clientInfo.origin.contains("KR")
        ) && request.pricing.whiteLabelKey.getOrElse("").equalsIgnoreCase("F1A5905F-9620-45E5-9D91-D251C07E0B42"),
    ))

  override def getCommissionAndMarginOverrideSetting(
    whiteLabelKey: Option[String]): Either[WhitelabelServiceException, (Int, CommissionAndMarginOverride)] = Right(
    0 -> CommissionAndMarginOverride(
      isCommissionOverride = false,
      isCommissionAdjustment = false,
      isMarginAdjustment = false,
      isAdjustCommissionFromHotelContract = false,
    ))

  override def getDmcControlSetting(whiteLabelId: Int,
                                    dmcId: Int): Either[WhitelabelServiceException, Option[DmcControlSettingModel]] =
    Right(None)

  // Stub implementations for the newly public methods
  override def getCouponWithWLID(whiteLabelId: Int,
                                 featuresEnabled: Map[String, Boolean],
                                 agEnv: Option[String]): models.whitelabel.Coupon = models.whitelabel.Coupon(None)

  override def getExchangeRateWithWLID(whitelabelID: Int,
                                       featuresEnabled: Map[String, Boolean],
                                       agEnv: Option[String]): models.whitelabel.ExchangeRateConfiguration =
    models.whitelabel.ExchangeRateConfiguration(disableUpliftExchangeRate = false)

  override def getExternalVipDisplayConfigs(whiteLabelId: Int,
                                            featuresEnabled: Map[String, Boolean],
                                            agEnv: Option[String]): List[models.whitelabel.ExternalVipDisplayConfigs] =
    List.empty

}
