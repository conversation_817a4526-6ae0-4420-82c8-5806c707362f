package com.agoda.pricing.models

// scalastyle:off line.size.limit

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration
import scala.util.control.NonFatal
import com.agoda.commons.traffic.TrafficType
import api.request.{FeatureFlag, PaymentChargeType, PaymentChargeTypes}
import com.agoda.papi.enums.campaign.{CampaignStatusType, ConsolidatedPeekState, _}
import com.agoda.papi.enums.hotel.{
  FireDrillContractType,
  PayLaterOptionCode,
  PayLaterSubTypeCode,
  StayPackageType,
  StayPackageTypes,
  TaxRegistrationType,
}
import com.agoda.papi.enums.request.{ContractFilterType, FilterCriteria, PostBookingActionType, StackDiscountOption}
import com.agoda.papi.enums.room.{
  AccrualPolicyStartFrom,
  BenefitTargetType,
  BenefitTargetTypes,
  CancellationChargeSettingType,
  CancellationFeeType,
  CancellationGroup,
  ChannelDiscountStackingType,
  ChildRateType,
  ExternalLoyaltyItem,
  ExternalLoyaltyItemType,
  GeoType,
  LoyaltyPromotionBenefitCategory,
  LoyaltyPromotionBenefitType,
  PricingChildRateType,
  RateChannelSimulationFeatures,
  SubChargeType,
  ValueCalculationMethodType,
  ValueMethodType,
  ValueTagType,
  ValueTagTypes,
  WhomToPayType,
}
import com.typesafe.scalalogging.LazyLogging
import enumeratum.{Enum, EnumEntry}
import io.circe.Decoder.Result
import io.circe.derivation.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, DecodingFailure, Encoder, HCursor, Json, KeyDecoder, KeyEncoder}
import models.enums.SupplierType.SupplierType
import models.enums.{
  AffiliateModelType,
  AffiliatePaymentType,
  CalculationMethodType,
  CalculationMethodTypes,
  CancellationByType,
  CancellationOptionType,
  CancellationReasonType,
  CancellationResultType,
  CancellationValidationFailReason,
  CancellationValidationFailReasons,
  DispatchAvailabilityType,
  DispatchAvailabilityTypes,
  FinanceOfferType,
  PostBookingPaymentTransactionType,
  PostBookingPaymentType,
  ReBookingActionType,
  RefundOptionType,
  SupplierType,
}
import models.pricing.enums.{
  ApplyType,
  ApplyTypes,
  BenefitUnit,
  BenefitUnits,
  BookingDurationUnitType,
  BookingDurationUnitTypes,
  BookingItemType,
  BookingItemTypes,
  BookingRateType,
  BookingRateTypes,
  ChargeGroup,
  ChargeGroups,
  ChargeOption,
  ChargeOptions,
  ChargeType,
  ChargeTypes,
  CorTypeFlag,
  CorTypeFlags,
  DFBundleStayType,
  DFBundleStayTypes,
  DFBundleType,
  DFBundleTypes,
  DownliftSource,
  DownliftSources,
  M150TransparencyVersionType,
  PackageProductType,
  PackageProductTypes,
  PaxType,
  PaxTypes,
  PaymentModel,
  PaymentModels,
  PriceType,
  PriceTypes,
  RareRoomType,
  RareRoomTypes,
  RateApplyType,
  RateApplyTypes,
  RatePlanStatus,
  RatePlanStatuses,
  RepurposeDiscountMethod,
  RequestedPrice,
  RoomCategoryChangeType,
  RoomOfferNameType,
  SwapRoomType,
  SwapRoomTypes,
  TaxType,
  TaxTypes,
}
import models.pricing.{DiscountType, DiscountTypes, LoyaltyItem, LoyaltyItemType}
import models.starfruit.{
  LoyaltyReason,
  LoyaltyReasons,
  LoyaltyType,
  LoyaltyTypes,
  PackageOptionalRebateType,
  PackageOptionalRebateTypes,
  PayType,
  PayTypes,
  PricingMessageLocalizationType,
  PricingMessageVariableType,
  RatePlanStatusType,
  RatePlanStatusTypes,
  RocketMilesRoomAwareMatchingLevelType,
  RocketMilesRoomAwareMatchingLevelTypes,
  RocketmilePublishPriceLogicType,
  RocketmilePublishPriceLogicTypes,
  RocketmileSortByField,
  RocketmileSortByFields,
  RoomSortingStrategies,
  RoomSortingStrategy,
  TaxReceiptType,
}
import models.{
  ChannelDiscount,
  DFDiscountType,
  Deal,
  PrecheckAccuracies,
  PrecheckAccuracy,
  RoomBundleType,
  RoomBundleTypes,
}
import org.joda.time.format.ISODateTimeFormat
import org.joda.time.{DateTime, LocalDate}

/**
  * Add enum's encoder & decoder and common stuffs here
  */
package object circe extends LazyLogging {

  // UPI Enum decoders/encoders

  val stringEncoder: Encoder[String] = implicitly[Encoder[String]]
  val stringDecoder: Decoder[String] = implicitly[Decoder[String]]

  def decodeEnum[A <: EnumEntry](c: HCursor, enum: Enum[A], withDefault: Boolean = false): Result[A] =
    stringDecoder.apply(c).flatMap { s =>
      val maybeMember = enum.withNameOption(s)
      val maybeMemberWithDefault =
        if (withDefault) {
          if (maybeMember.isEmpty) {
            logger.warn(s"$s is not a member of enum $enum, fallback to Unknown")
          }
          maybeMember.orElse(enum.withNameOption("Unknown"))
        } else {
          maybeMember
        }
      maybeMemberWithDefault match {
        case Some(member) => Right(member)
        case _ => Left(DecodingFailure(s"$s' is not a member of enum $enum", c.history))
      }
    }

  def encodeEnum(enum: EnumEntry): Json = stringEncoder.apply(enum.entryName)

  implicit val ChargeTypeEncoder: Encoder[ChargeType] = new Encoder[ChargeType] {
    override def apply(enum: ChargeType): Json = encodeEnum(enum)
  }

  implicit val ChargeTypeDecoder: Decoder[ChargeType] = new Decoder[ChargeType] {
    final def apply(c: HCursor): Result[ChargeType] = decodeEnum(c, ChargeTypes)
  }

  implicit val ChargeGroupEncoder: Encoder[ChargeGroup] = new Encoder[ChargeGroup] {
    override def apply(enum: ChargeGroup): Json = encodeEnum(enum)
  }

  implicit val ChargeGroupDecoder: Decoder[ChargeGroup] = new Decoder[ChargeGroup] {
    final def apply(c: HCursor): Result[ChargeGroup] = decodeEnum(c, ChargeGroups)
  }
  implicit val SubChargeTypeEncoder: Encoder[SubChargeType] = new Encoder[SubChargeType] {
    override def apply(enum: SubChargeType): Json = encodeEnum(enum)
  }

  implicit val SubChargeTypeDecoder: Decoder[SubChargeType] = new Decoder[SubChargeType] {
    final def apply(c: HCursor): Result[SubChargeType] = decodeEnum(c, SubChargeType)
  }

  implicit val RequestedPriceEncoder: Encoder[RequestedPrice] = new Encoder[RequestedPrice] {
    override def apply(enum: RequestedPrice): Json = encodeEnum(enum)
  }

  implicit val RequestedPriceDecoder: Decoder[RequestedPrice] = new Decoder[RequestedPrice] {
    final def apply(c: HCursor): Result[RequestedPrice] = decodeEnum(c, RequestedPrice)
  }

  implicit val DiscountTypeEncoder: Encoder[DiscountType] = new Encoder[DiscountType] {
    override def apply(enum: DiscountType): Json = encodeEnum(enum)
  }

  implicit val DiscountTypeDecoder: Decoder[DiscountType] = new Decoder[DiscountType] {
    final def apply(c: HCursor): Result[DiscountType] = decodeEnum(c, DiscountTypes, withDefault = true)
  }

  implicit val PayTypeEncoder: Encoder[PayType] = new Encoder[PayType] {
    override def apply(enum: PayType): Json = encodeEnum(enum)
  }

  implicit val PayTypeDecoder: Decoder[PayType] = new Decoder[PayType] {
    final def apply(c: HCursor): Result[PayType] = decodeEnum(c, PayTypes)
  }

  implicit val ApplyTypeEncoder: Encoder[ApplyType] = new Encoder[ApplyType] {
    override def apply(enum: ApplyType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val ApplyTypeDecoder: Decoder[ApplyType] = new Decoder[ApplyType] {
    final def apply(c: HCursor): Result[ApplyType] = decodeEnum(c, ApplyTypes, withDefault = true)
  }

  implicit val ChargeOptionEncoder: Encoder[ChargeOption] = new Encoder[ChargeOption] {
    override def apply(enum: ChargeOption): Json = stringEncoder.apply(enum.entryName)
  }
  implicit val ChannelDiscountStackingTypeEncoder: Encoder[ChannelDiscountStackingType] =
    (enum: ChannelDiscountStackingType) => encodeEnum(enum)
  implicit val ChannelDiscountStackingTypeDecoder: Decoder[ChannelDiscountStackingType] =
    (c: HCursor) => decodeEnum(c, ChannelDiscountStackingType)

  implicit val RateChannelSimulationFeaturesEncoder: Encoder[RateChannelSimulationFeatures] =
    (enum: RateChannelSimulationFeatures) => encodeEnum(enum)
  implicit val RateChannelSimulationFeaturesDecoder: Decoder[RateChannelSimulationFeatures] =
    (c: HCursor) => decodeEnum(c, RateChannelSimulationFeatures)

  implicit val ChargeOptionDecoder: Decoder[ChargeOption] = new Decoder[ChargeOption] {
    final def apply(c: HCursor): Result[ChargeOption] = decodeEnum(c, ChargeOptions)
  }

  implicit val RateApplyTypeEncoder: Encoder[RateApplyType] = new Encoder[RateApplyType] {
    override def apply(enum: RateApplyType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val RateApplyTypeDecoder: Decoder[RateApplyType] = new Decoder[RateApplyType] {
    final def apply(c: HCursor): Result[RateApplyType] = decodeEnum(c, RateApplyTypes)
  }

  implicit val PaxTypeEncoder: Encoder[PaxType] = new Encoder[PaxType] {
    override def apply(enum: PaxType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val PaxTypeDecoder: Decoder[PaxType] = new Decoder[PaxType] {
    final def apply(c: HCursor): Result[PaxType] = decodeEnum(c, PaxTypes)
  }

  implicit val BenefitUnitEncoder: Encoder[BenefitUnit] = new Encoder[BenefitUnit] {
    override def apply(enum: BenefitUnit): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val BenefitUnitDecoder: Decoder[BenefitUnit] = new Decoder[BenefitUnit] {
    final def apply(c: HCursor): Result[BenefitUnit] = decodeEnum(c, BenefitUnits)
  }

  implicit val BenefitTargetTypeEncoder: Encoder[BenefitTargetType] = new Encoder[BenefitTargetType] {
    override def apply(enum: BenefitTargetType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val BenefitTargetTypeDecoder: Decoder[BenefitTargetType] = new Decoder[BenefitTargetType] {
    final def apply(c: HCursor): Result[BenefitTargetType] = decodeEnum(c, BenefitTargetTypes)
  }

  implicit val PaymentModelEncoder: Encoder[PaymentModel] = new Encoder[PaymentModel] {
    override def apply(enum: PaymentModel): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val PaymentModelDecoder: Decoder[PaymentModel] = new Decoder[PaymentModel] {
    final def apply(c: HCursor): Result[PaymentModel] = decodeEnum(c, PaymentModels, withDefault = true)
  }

  implicit val TrafficTypeEncoder: Encoder[TrafficType] = new Encoder[TrafficType] {
    override def apply(enum: TrafficType): Json = stringEncoder.apply(enum.trafficType)
  }

  implicit val TrafficTypeDecoder: Decoder[TrafficType] = new Decoder[TrafficType] {
    final def apply(c: HCursor): Result[TrafficType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = Option(TrafficType.getByType(s))
      maybeMember match {
        case Some(member) => Right(member)
        case _ => Left(DecodingFailure(s"$s' is not a member of enum $TrafficType", c.history))
      }
    }
  }

  implicit val CorTypeFlagEncoder: Encoder[CorTypeFlag] = new Encoder[CorTypeFlag] {
    override def apply(enum: CorTypeFlag): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val CorTypeFlagDecoder: Decoder[CorTypeFlag] = new Decoder[CorTypeFlag] {
    final def apply(c: HCursor): Result[CorTypeFlag] = decodeEnum(c, CorTypeFlags)
  }

  implicit val FeatureFlagEncoder: Encoder[FeatureFlag] = new Encoder[FeatureFlag] {
    override def apply(enum: FeatureFlag): Json = encodeEnum(enum)
  }

  implicit val FeatureFlagDecoder: Decoder[FeatureFlag] = new Decoder[FeatureFlag] {
    final def apply(c: HCursor): Result[FeatureFlag] = decodeEnum(c, FeatureFlag, withDefault = true)
  }

  implicit val FilterCriteriaEncoder: Encoder[FilterCriteria] = new Encoder[FilterCriteria] {
    override def apply(enum: FilterCriteria): Json = encodeEnum(enum)
  }

  implicit val FilterCriteriaDecoder: Decoder[FilterCriteria] = new Decoder[FilterCriteria] {
    final def apply(c: HCursor): Result[FilterCriteria] = decodeEnum(c, FilterCriteria, withDefault = true)
  }

  implicit val RepurposeDiscountMethodEncoder: Encoder[RepurposeDiscountMethod] = new Encoder[RepurposeDiscountMethod] {
    override def apply(enum: RepurposeDiscountMethod): Json = encodeEnum(enum)
  }

  implicit val RepurposeDiscountMethodDecoder: Decoder[RepurposeDiscountMethod] = new Decoder[RepurposeDiscountMethod] {
    final def apply(c: HCursor): Result[RepurposeDiscountMethod] = decodeEnum(c, RepurposeDiscountMethod)
  }

  implicit val TaxTypeEncoder: Encoder[TaxType] = new Encoder[TaxType] {
    override def apply(enum: TaxType): Json = encodeEnum(enum)
  }

  implicit val TaxTypeDecoder: Decoder[TaxType] = new Decoder[TaxType] {
    final def apply(c: HCursor): Result[TaxType] = decodeEnum(c, TaxTypes)
  }

  implicit val DownliftSourceEncoder: Encoder[DownliftSource] = new Encoder[DownliftSource] {
    override def apply(enum: DownliftSource): Json = encodeEnum(enum)
  }

  implicit val DownliftSourceDecoder: Decoder[DownliftSource] = new Decoder[DownliftSource] {
    final def apply(c: HCursor): Result[DownliftSource] = decodeEnum(c, DownliftSources)
  }

  implicit val IneligiblePromotionReasonEncoder: Encoder[IneligiblePromotionReason] =
    new Encoder[IneligiblePromotionReason] {
      override def apply(enum: IneligiblePromotionReason): Json = encodeEnum(enum)
    }

  implicit val IneligiblePromotionReasonDecoder: Decoder[IneligiblePromotionReason] =
    new Decoder[IneligiblePromotionReason] {
      final def apply(c: HCursor): Result[IneligiblePromotionReason] = decodeEnum(c, InelegiblePromotionReasons)
    }

  implicit val PaymentChargeTypeEncoder: Encoder[PaymentChargeType] = new Encoder[PaymentChargeType] {
    override def apply(enum: PaymentChargeType): Json = encodeEnum(enum)
  }

  implicit val PaymentChargeTypeDecoder: Decoder[PaymentChargeType] = new Decoder[PaymentChargeType] {
    final def apply(c: HCursor): Result[PaymentChargeType] = decodeEnum(c, PaymentChargeTypes)
  }

  implicit val SwapRoomTypeEncoder: Encoder[SwapRoomType] = new Encoder[SwapRoomType] {
    override def apply(enum: SwapRoomType): Json = encodeEnum(enum)
  }

  implicit val RoomCategoryChangeTypeEncoder: Encoder[RoomCategoryChangeType] = new Encoder[RoomCategoryChangeType] {
    override def apply(enum: RoomCategoryChangeType): Json = encodeEnum(enum)
  }

  implicit val SwapRoomTypeDecoder: Decoder[SwapRoomType] = new Decoder[SwapRoomType] {
    final def apply(c: HCursor): Result[SwapRoomType] = decodeEnum(c, SwapRoomTypes, withDefault = true)
  }

  implicit val RoomCategoryChangeTypeDecoder: Decoder[RoomCategoryChangeType] = new Decoder[RoomCategoryChangeType] {
    final def apply(c: HCursor): Result[RoomCategoryChangeType] = decodeEnum(c, RoomCategoryChangeType)
  }

  implicit val CampaignDiscountTypeEncoder: Encoder[CampaignDiscountType] = new Encoder[CampaignDiscountType] {
    override def apply(enum: CampaignDiscountType): Json = encodeEnum(enum)
  }

  implicit val CampaignDiscountTypeDecoder: Decoder[CampaignDiscountType] = new Decoder[CampaignDiscountType] {
    final def apply(c: HCursor): Result[CampaignDiscountType] = decodeEnum(c, CampaignDiscountTypes)
  }

  implicit val CampaignValidDateTypeEncoder: Encoder[CampaignValidDateType] = new Encoder[CampaignValidDateType] {
    override def apply(enum: CampaignValidDateType): Json = encodeEnum(enum)
  }

  implicit val CampaignValidDateTypeDecoder: Decoder[CampaignValidDateType] = new Decoder[CampaignValidDateType] {
    final def apply(c: HCursor): Result[CampaignValidDateType] = decodeEnum(c, CampaignValidDateType)
  }

  implicit val DealEncoder: Encoder[Deal] = new Encoder[Deal] {
    override def apply(enum: Deal): Json = encodeEnum(enum)
  }

  implicit val DealDecoder: Decoder[Deal] = new Decoder[Deal] {
    final def apply(c: HCursor): Result[Deal] = decodeEnum(c, Deal)
  }

  implicit val ChannelDiscountEncoder: Encoder[ChannelDiscount] = new Encoder[ChannelDiscount] {
    override def apply(enum: ChannelDiscount): Json = encodeEnum(enum)
  }

  implicit val ChannelDiscountDecoder: Decoder[ChannelDiscount] = new Decoder[ChannelDiscount] {
    final def apply(c: HCursor): Result[ChannelDiscount] = decodeEnum(c, ChannelDiscount)
  }

  implicit val LoyaltyTypeEncoder: Encoder[LoyaltyType] = new Encoder[LoyaltyType] {
    override def apply(enum: LoyaltyType): Json = encodeEnum(enum)
  }

  implicit val LoyaltyTypeDecoder: Decoder[LoyaltyType] = new Decoder[LoyaltyType] {
    final def apply(c: HCursor): Result[LoyaltyType] = decodeEnum(c, LoyaltyTypes)
  }

  implicit val LoyaltyReasonEncoder: Encoder[LoyaltyReason] = new Encoder[LoyaltyReason] {
    override def apply(enum: LoyaltyReason): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val LoyaltyReasonDecoder: Decoder[LoyaltyReason] = new Decoder[LoyaltyReason] {
    final def apply(c: HCursor): Result[LoyaltyReason] = decodeEnum(c, LoyaltyReasons)
  }

  implicit val WhomToPayTypeEncoder: Encoder[WhomToPayType] = new Encoder[WhomToPayType] {
    override def apply(enum: WhomToPayType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val WhomToPayTypeDecoder: Decoder[WhomToPayType] = new Decoder[WhomToPayType] {
    final def apply(c: HCursor): Result[WhomToPayType] = decodeEnum(c, WhomToPayType)
  }

  implicit val ValueCalculationMethodTypeEncoder: Encoder[ValueCalculationMethodType] =
    new Encoder[ValueCalculationMethodType] {
      override def apply(enum: ValueCalculationMethodType): Json = stringEncoder.apply(enum.entryName)
    }

  implicit val ValueCalculationMethodTypeDecoder: Decoder[ValueCalculationMethodType] =
    new Decoder[ValueCalculationMethodType] {
      final def apply(c: HCursor): Result[ValueCalculationMethodType] = decodeEnum(c, ValueCalculationMethodType)
    }

  implicit val ValueMethodTypeEncoder: Encoder[ValueMethodType] = new Encoder[ValueMethodType] {
    override def apply(enum: ValueMethodType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val ValueMethodTypeDecoder: Decoder[ValueMethodType] = new Decoder[ValueMethodType] {
    final def apply(c: HCursor): Result[ValueMethodType] = decodeEnum(c, ValueMethodType)
  }

  implicit val GeoTypeEncoder: Encoder[GeoType] = new Encoder[GeoType] {
    override def apply(enum: GeoType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val GeoTypeDecoder: Decoder[GeoType] = new Decoder[GeoType] {
    final def apply(c: HCursor): Result[GeoType] = decodeEnum(c, GeoType)
  }

  implicit val TimeUnitEncoder: Encoder[TimeUnit] = new Encoder[TimeUnit] {
    override def apply(timeunit: TimeUnit): Json = stringEncoder.apply(timeunit.name())
  }

  implicit val TimeUnitDecoder: Decoder[TimeUnit] = new Decoder[TimeUnit] {
    final def apply(c: HCursor): Result[TimeUnit] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = Some(TimeUnit.valueOf(s))
      maybeMember match {
        case Some(member) => Right(member)
        case _ => Left(DecodingFailure(s"$s' is not a member of enum TimeUnit", c.history))
      }
    }
  }

  implicit val DateTimeEncoder: Encoder[DateTime] = new Encoder[DateTime] {
    override def apply(dateTime: DateTime): Json = stringEncoder.apply(dateTime.toString)
  }

  implicit val DateTimeDecoder: Decoder[DateTime] = new Decoder[DateTime] {
    final def apply(c: HCursor): Result[DateTime] = stringDecoder.apply(c).flatMap { s =>
      try Right(ISODateTimeFormat.dateTime().parseDateTime(s))
      catch {
        case NonFatal(e) => Left(DecodingFailure(s"$s' is not a member of DateTime", c.history))
      }
    }
  }

  implicit val StackDiscountOptionEncoder: Encoder[StackDiscountOption] = new Encoder[StackDiscountOption] {
    override def apply(enum: StackDiscountOption): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val StackDiscountOptionDecoder: Decoder[StackDiscountOption] = new Decoder[StackDiscountOption] {
    final def apply(c: HCursor): Result[StackDiscountOption] = decodeEnum(c, StackDiscountOption, withDefault = true)
  }

  implicit val RoomSortingStrategyEncoder: Encoder[RoomSortingStrategy] = new Encoder[RoomSortingStrategy] {
    override def apply(enum: RoomSortingStrategy): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val RoomSortingStrategyDecoder: Decoder[RoomSortingStrategy] = new Decoder[RoomSortingStrategy] {
    final def apply(c: HCursor): Result[RoomSortingStrategy] = decodeEnum(c, RoomSortingStrategies, withDefault = true)
  }

  implicit val PrecheckAccuracyEncoder: Encoder[PrecheckAccuracy] = new Encoder[PrecheckAccuracy] {
    override def apply(enum: PrecheckAccuracy): Json = encodeEnum(enum)
  }

  implicit val PrecheckAccuracyDecoder: Decoder[PrecheckAccuracy] = new Decoder[PrecheckAccuracy] {
    final def apply(c: HCursor): Result[PrecheckAccuracy] = decodeEnum(c, PrecheckAccuracies)
  }

  implicit val DateTimeMapKeyEncoder: KeyEncoder[DateTime] = new KeyEncoder[DateTime] {
    override def apply(key: DateTime): String = key.toString
  }

  implicit val DateTimeMapKeyDecoder: KeyDecoder[DateTime] = new KeyDecoder[DateTime] {
    override def apply(key: String): Option[DateTime] = Some(ISODateTimeFormat.dateTime().parseDateTime(key))
  }

  implicit val SupplierTypeEncoder: Encoder[SupplierType] = new Encoder[SupplierType] {
    override def apply(enum: SupplierType): Json = stringEncoder.apply(enum.toString)
  }

  implicit val SupplierTypeDecoder: Decoder[SupplierType] = new Decoder[SupplierType] {
    final def apply(c: HCursor): Result[SupplierType] = stringDecoder.apply(c).flatMap { s =>
      Right(SupplierType.fields.getOrElse(s, SupplierType.Disabled))
    }
  }

  implicit val DFCancellationGroupEncoder: Encoder[CancellationGroup] = new Encoder[CancellationGroup] {
    override def apply(enum: CancellationGroup): Json = encodeEnum(enum)
  }

  implicit val CancellationGroupDecoder: Decoder[CancellationGroup] = new Decoder[CancellationGroup] {
    final def apply(c: HCursor): Result[CancellationGroup] = decodeEnum(c, CancellationGroup, withDefault = true)
  }

  implicit val TaxReceiptTypeEncoder: Encoder[TaxReceiptType] = new Encoder[TaxReceiptType] {
    override def apply(enum: TaxReceiptType): Json = stringEncoder.apply(enum.i)
  }

  implicit val TaxReceiptTypeeDecoder: Decoder[TaxReceiptType] = new Decoder[TaxReceiptType] {
    final def apply(c: HCursor): Result[TaxReceiptType] = decodeEnum(c, TaxReceiptType)
  }

  implicit val CancellationFeeTypeEncoder: Encoder[CancellationFeeType] = new Encoder[CancellationFeeType] {
    override def apply(enum: CancellationFeeType): Json = encodeEnum(enum)
  }

  implicit val CancellationFeeTypeDecoder: Decoder[CancellationFeeType] = new Decoder[CancellationFeeType] {
    final def apply(c: HCursor): Result[CancellationFeeType] = decodeEnum(c, CancellationFeeType)
  }

  implicit val DispatchAvailabilityTypeEncoder: Encoder[DispatchAvailabilityType] =
    new Encoder[DispatchAvailabilityType] {
      override def apply(enum: DispatchAvailabilityType): Json = encodeEnum(enum)
    }

  implicit val DispatchAvailabilityTypeDecoder: Decoder[DispatchAvailabilityType] =
    new Decoder[DispatchAvailabilityType] {
      final def apply(c: HCursor): Result[DispatchAvailabilityType] = decodeEnum(c, DispatchAvailabilityTypes)
    }

  implicit val RatePlanStatusEncoder: Encoder[RatePlanStatus] = new Encoder[RatePlanStatus] {
    override def apply(enum: RatePlanStatus): Json = encodeEnum(enum)
  }

  implicit val RatePlanStatusDecoder: Decoder[RatePlanStatus] = new Decoder[RatePlanStatus] {
    final def apply(c: HCursor): Result[RatePlanStatus] = decodeEnum(c, RatePlanStatuses)
  }

  implicit val RatePlanStatusTypeEncoder: Encoder[RatePlanStatusType] = new Encoder[RatePlanStatusType] {
    override def apply(enum: RatePlanStatusType): Json = encodeEnum(enum)
  }

  implicit val RatePlanStatusTypeDecoder: Decoder[RatePlanStatusType] = new Decoder[RatePlanStatusType] {
    final def apply(c: HCursor): Result[RatePlanStatusType] = decodeEnum(c, RatePlanStatusTypes)
  }

  implicit val PricingMessageVariableTypeEncoder: Encoder[PricingMessageVariableType] =
    new Encoder[PricingMessageVariableType] {
      override def apply(enum: PricingMessageVariableType): Json = encodeEnum(enum)
    }

  implicit val PricingMessageVariableTypeDecoder: Decoder[PricingMessageVariableType] =
    new Decoder[PricingMessageVariableType] {
      final def apply(c: HCursor): Result[PricingMessageVariableType] = decodeEnum(c, PricingMessageVariableType)
    }

  implicit val PricingMessageLocalizationTypeEncoder: Encoder[PricingMessageLocalizationType] =
    new Encoder[PricingMessageLocalizationType] {
      override def apply(enum: PricingMessageLocalizationType): Json = encodeEnum(enum)
    }

  implicit val PricingMessageLocalizationTypeDecoder: Decoder[PricingMessageLocalizationType] =
    new Decoder[PricingMessageLocalizationType] {
      final def apply(c: HCursor): Result[PricingMessageLocalizationType] = decodeEnum(c, PricingMessageLocalizationType)
    }

  implicit val BookingItemTypeEncoder: Encoder[BookingItemType] = new Encoder[BookingItemType] {
    override def apply(enum: BookingItemType): Json = encodeEnum(enum)
  }

  implicit val BookingItemTypeDecoder: Decoder[BookingItemType] = new Decoder[BookingItemType] {
    final def apply(c: HCursor): Result[BookingItemType] = decodeEnum(c, BookingItemTypes)
  }

  implicit val PaymentModelMapKeyEncoder: KeyEncoder[PaymentModel] = new KeyEncoder[PaymentModel] {
    override def apply(key: PaymentModel): String = key.entryName.toString
  }

  implicit val PaymentModelMapKeyDecoder: KeyDecoder[PaymentModel] = new KeyDecoder[PaymentModel] {
    override def apply(key: String): Option[PaymentModel] =
      Option(PaymentModels.namesToValuesMap.getOrElse(key, PaymentModels.Unknown))
  }

  implicit val LocalDateMapKeyEncoder: KeyEncoder[LocalDate] = new KeyEncoder[LocalDate] {
    override def apply(key: LocalDate): String = key.toString
  }

  implicit val LocalDateMapKeyDecoder: KeyDecoder[LocalDate] = new KeyDecoder[LocalDate] {
    override def apply(key: String): Option[LocalDate] = Some(LocalDate.parse(key))
  }

  implicit val BookingRateTypeEncoder: Encoder[BookingRateType] = new Encoder[BookingRateType] {
    override def apply(enum: BookingRateType): Json = encodeEnum(enum)
  }

  implicit val BookingRateTypeDecoder: Decoder[BookingRateType] = new Decoder[BookingRateType] {
    final def apply(c: HCursor): Result[BookingRateType] = decodeEnum(c, BookingRateTypes)
  }

  implicit val CancellationChargeSettingTypeEncoder: Encoder[CancellationChargeSettingType] =
    new Encoder[CancellationChargeSettingType] {
      override def apply(enum: CancellationChargeSettingType): Json = encodeEnum(enum)
    }

  implicit val CancellationChargeSettingTypeDecoder: Decoder[CancellationChargeSettingType] =
    new Decoder[CancellationChargeSettingType] {
      final def apply(c: HCursor): Result[CancellationChargeSettingType] = decodeEnum(c, CancellationChargeSettingType)
    }

  implicit val DFDiscountTypeEncoder: Encoder[DFDiscountType] = new Encoder[DFDiscountType] {
    override def apply(enum: DFDiscountType): Json = encodeEnum(enum)
  }

  implicit val DFDiscountTypeDecoder: Decoder[DFDiscountType] = new Decoder[DFDiscountType] {
    final def apply(c: HCursor): Result[DFDiscountType] = decodeEnum(c, DFDiscountType)
  }

  implicit val ChildRateTypeEncoder: Encoder[ChildRateType] = new Encoder[ChildRateType] {
    override def apply(enum: ChildRateType): Json = encodeEnum(enum)
  }

  implicit val ChildRateTypeDecoder: Decoder[ChildRateType] = new Decoder[ChildRateType] {
    final def apply(c: HCursor): Result[ChildRateType] = decodeEnum(c, ChildRateType, withDefault = true)
  }

  implicit val PricingChildRateTypeEncoder: Encoder[PricingChildRateType] = new Encoder[PricingChildRateType] {
    override def apply(enum: PricingChildRateType): Json = encodeEnum(enum)
  }

  implicit val PricingChildRateTypeDecoder: Decoder[PricingChildRateType] = new Decoder[PricingChildRateType] {
    final def apply(c: HCursor): Result[PricingChildRateType] = decodeEnum(c, PricingChildRateType)
  }

  implicit val RoomBundleTypeEncoder: Encoder[RoomBundleType] = new Encoder[RoomBundleType] {
    override def apply(enum: RoomBundleType): Json = encodeEnum(enum)
  }

  implicit val RoomBundleTypeDecoder: Decoder[RoomBundleType] = new Decoder[RoomBundleType] {
    final def apply(c: HCursor): Result[RoomBundleType] = decodeEnum(c, RoomBundleTypes)
  }

  implicit val DFBundleTypeEncoder: Encoder[DFBundleType] = new Encoder[DFBundleType] {
    override def apply(enum: DFBundleType): Json = encodeEnum(enum)
  }

  implicit val DFBundleTypeDecoder: Decoder[DFBundleType] = new Decoder[DFBundleType] {
    final def apply(c: HCursor): Result[DFBundleType] = decodeEnum(c, DFBundleTypes)
  }

  implicit val DFBundleStayTypeEncoders: Encoder[DFBundleStayType] = new Encoder[DFBundleStayType] {
    override def apply(enum: DFBundleStayType): Json = encodeEnum(enum)
  }

  implicit val DFBundleStayTypeDecoders: Decoder[DFBundleStayType] = new Decoder[DFBundleStayType] {
    final def apply(c: HCursor): Result[DFBundleStayType] = decodeEnum(c, DFBundleStayTypes)
  }

  implicit val BookingDurationTypeEncoder: Encoder[BookingDurationUnitType] = new Encoder[BookingDurationUnitType] {
    override def apply(enum: BookingDurationUnitType): Json = encodeEnum(enum)
  }

  implicit val BookingDurationTypeDecoder: Decoder[BookingDurationUnitType] = new Decoder[BookingDurationUnitType] {
    final def apply(c: HCursor): Result[BookingDurationUnitType] = decodeEnum(c, BookingDurationUnitTypes)
  }

  implicit val ContractFilterTypeEncoder: Encoder[ContractFilterType] = new Encoder[ContractFilterType] {
    override def apply(enum: ContractFilterType): Json = encodeEnum(enum)
  }

  implicit val ContractFilterTypeDecoder: Decoder[ContractFilterType] = new Decoder[ContractFilterType] {
    final def apply(c: HCursor): Result[ContractFilterType] = decodeEnum(c, ContractFilterType, withDefault = true)
  }

  implicit val LoyaltyItemTypeEncoder: Encoder[LoyaltyItemType] = new Encoder[LoyaltyItemType] {
    override def apply(enum: LoyaltyItemType): Json = encodeEnum(enum)
  }

  implicit val LoyaltyItemTypeDecoder: Decoder[LoyaltyItemType] = new Decoder[LoyaltyItemType] {
    final def apply(c: HCursor): Result[LoyaltyItemType] = decodeEnum(c, LoyaltyItem)
  }

  implicit val PackageOptionalRebateTypeEncoder: Encoder[PackageOptionalRebateType] =
    new Encoder[PackageOptionalRebateType] {
      override def apply(a: PackageOptionalRebateType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val packageOptionalRebateTypeDecoder: Decoder[PackageOptionalRebateType] =
    new Decoder[PackageOptionalRebateType] {
      final def apply(c: HCursor): Result[PackageOptionalRebateType] = decodeEnum(c, PackageOptionalRebateTypes)
    }

  implicit val PackageOptionalRebateTypeMapKeyEncoder: KeyEncoder[PackageOptionalRebateType] =
    new KeyEncoder[PackageOptionalRebateType] {
      override def apply(key: PackageOptionalRebateType): String = key.entryName.toString
    }

  implicit val PackageOptionalRebateTypeMapKeyDecoder: KeyDecoder[PackageOptionalRebateType] =
    new KeyDecoder[PackageOptionalRebateType] {
      override def apply(key: String): Option[PackageOptionalRebateType] =
        Option(PackageOptionalRebateTypes.namesToValuesMap.getOrElse(key, PackageOptionalRebateTypes.Unknown))
    }

  implicit val PackageProductTypeEncoder: Encoder[PackageProductType] = new Encoder[PackageProductType] {
    override def apply(enum: PackageProductType): Json = encodeEnum(enum)
  }

  implicit val PackageProductTypeDecoder: Decoder[PackageProductType] = new Decoder[PackageProductType] {
    final def apply(c: HCursor): Result[PackageProductType] = decodeEnum(c, PackageProductTypes)
  }

  implicit val FiniteDurationDecoder: Decoder[FiniteDuration] = deriveDecoder[FiniteDuration]

  implicit val FiniteDurationEncoder: Encoder[FiniteDuration] = deriveEncoder[FiniteDuration]

  implicit val StayPackageTypeEncoder: Encoder[StayPackageType] = new Encoder[StayPackageType] {
    override def apply(enum: StayPackageType): Json = encodeEnum(enum)
  }

  implicit val StayPackageTypeDecoder: Decoder[StayPackageType] = new Decoder[StayPackageType] {
    final def apply(c: HCursor): Result[StayPackageType] = decodeEnum(c, StayPackageTypes)
  }

  implicit val RareRoomTypeEncoder: Encoder[RareRoomType] = new Encoder[RareRoomType] {
    override def apply(enum: RareRoomType): Json = encodeEnum(enum)
  }

  implicit val RareRoomTypeDecoder: Decoder[RareRoomType] = new Decoder[RareRoomType] {
    final def apply(c: HCursor): Result[RareRoomType] = decodeEnum(c, RareRoomTypes)
  }

  implicit val PromotionTypeEncoder: Encoder[PromotionType] = new Encoder[PromotionType] {
    override def apply(enum: PromotionType): Json = encodeEnum(enum)
  }

  implicit val PromotionTypeDecoder: Decoder[PromotionType] = new Decoder[PromotionType] {
    final def apply(c: HCursor): Result[PromotionType] = decodeEnum(c, PromotionTypes)
  }

  implicit val PromotionCodeTypeEncoder: Encoder[PromotionCodeType] = new Encoder[PromotionCodeType] {
    override def apply(enum: PromotionCodeType): Json = encodeEnum(enum)
  }

  implicit val PromotionCodeTypeDecoder: Decoder[PromotionCodeType] = new Decoder[PromotionCodeType] {
    final def apply(c: HCursor): Result[PromotionCodeType] = decodeEnum(c, PromotionCodeType)
  }

  implicit val CampaignStatusTypeEncoder: Encoder[CampaignStatusType] = new Encoder[CampaignStatusType] {
    override def apply(enum: CampaignStatusType): Json = encodeEnum(enum)
  }

  implicit val CampaignStatusTypeDecoder: Decoder[CampaignStatusType] = new Decoder[CampaignStatusType] {
    final def apply(c: HCursor): Result[CampaignStatusType] = decodeEnum(c, CampaignStatusTypes)
  }

  implicit val RoomOfferNameTypeEncoder: Encoder[RoomOfferNameType] = new Encoder[RoomOfferNameType] {
    override def apply(enum: RoomOfferNameType): Json = encodeEnum(enum)
  }

  implicit val RoomOfferNameTypeDecoder: Decoder[RoomOfferNameType] = new Decoder[RoomOfferNameType] {
    final def apply(c: HCursor): Result[RoomOfferNameType] = decodeEnum(c, RoomOfferNameType)
  }

  implicit val RocketmilesPublishPriceLogicTypeEncoder: Encoder[RocketmilePublishPriceLogicType] =
    new Encoder[RocketmilePublishPriceLogicType] {
      override def apply(enum: RocketmilePublishPriceLogicType): Json = encodeEnum(enum)
    }

  implicit val RocketmilesPublishPriceLogicTypeDecoder: Decoder[RocketmilePublishPriceLogicType] =
    new Decoder[RocketmilePublishPriceLogicType] {
      final def apply(c: HCursor): Result[RocketmilePublishPriceLogicType] =
        decodeEnum(c, RocketmilePublishPriceLogicTypes, withDefault = true)
    }

  implicit val RocketmilesSortByFieldEncoder: Encoder[RocketmileSortByField] = new Encoder[RocketmileSortByField] {
    override def apply(enum: RocketmileSortByField): Json = encodeEnum(enum)
  }

  implicit val RocketmilesSortByFieldDecoder: Decoder[RocketmileSortByField] = new Decoder[RocketmileSortByField] {
    final def apply(c: HCursor): Result[RocketmileSortByField] =
      decodeEnum(c, RocketmileSortByFields, withDefault = true)
  }

  implicit val RocketMilesRoomAwareMatchingLevelTypeEncoder: Encoder[RocketMilesRoomAwareMatchingLevelType] =
    new Encoder[RocketMilesRoomAwareMatchingLevelType] {
      override def apply(enum: RocketMilesRoomAwareMatchingLevelType): Json = encodeEnum(enum)
    }

  implicit val RocketMilesRoomAwareMatchingLevelTypeDecoder: Decoder[RocketMilesRoomAwareMatchingLevelType] =
    new Decoder[RocketMilesRoomAwareMatchingLevelType] {
      final def apply(c: HCursor): Result[RocketMilesRoomAwareMatchingLevelType] =
        decodeEnum(c, RocketMilesRoomAwareMatchingLevelTypes)
    }

  implicit val FireDrillContractTypeEncoder: Encoder[FireDrillContractType] = new Encoder[FireDrillContractType] {
    override def apply(enum: FireDrillContractType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val FireDrillContractTypeDecoder: Decoder[FireDrillContractType] = new Decoder[FireDrillContractType] {
    final def apply(c: HCursor): Result[FireDrillContractType] = decodeEnum(c, FireDrillContractType)
  }

  implicit val CampaignTypeEncoder: Encoder[CampaignType] = new Encoder[CampaignType] {
    override def apply(enum: CampaignType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val CampaignTypeDecoder: Decoder[CampaignType] = new Decoder[CampaignType] {
    override def apply(c: HCursor): Result[CampaignType] = decodeEnum(c, CampaignTypes)
  }

  implicit val ExternalLoyaltyItemTypeEncoder: Encoder[ExternalLoyaltyItemType] = new Encoder[ExternalLoyaltyItemType] {
    override def apply(enum: ExternalLoyaltyItemType): Json = encodeEnum(enum)
  }

  implicit val ExternalLoyaltyItemTypeDecoder: Decoder[ExternalLoyaltyItemType] = new Decoder[ExternalLoyaltyItemType] {
    final def apply(c: HCursor): Result[ExternalLoyaltyItemType] = decodeEnum(c, ExternalLoyaltyItem)
  }

  implicit val FinanceOfferTypeEncoder: Encoder[FinanceOfferType] = new Encoder[FinanceOfferType] {
    override def apply(enum: FinanceOfferType): Json = encodeEnum(enum)
  }

  implicit val FinanceOfferTypeDecoder: Decoder[FinanceOfferType] = new Decoder[FinanceOfferType] {
    final def apply(c: HCursor): Result[FinanceOfferType] = decodeEnum(c, FinanceOfferType)
  }

  implicit val PriceCalculationPriceTypeEncoder: Encoder[PriceType] = new Encoder[PriceType] {
    override def apply(enum: PriceType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val PriceCalculationPriceTypeDecoder: Decoder[PriceType] = new Decoder[PriceType] {
    final def apply(c: HCursor): Result[PriceType] = decodeEnum(c, PriceTypes)
  }

  implicit val AffiliateModelTypeEncoder: Encoder[AffiliateModelType] = new Encoder[AffiliateModelType] {
    override def apply(enum: AffiliateModelType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val AffiliateModelTypeDecoder: Decoder[AffiliateModelType] = new Decoder[AffiliateModelType] {
    final def apply(c: HCursor): Result[AffiliateModelType] = decodeEnum(c, AffiliateModelType, withDefault = true)
  }

  implicit val AffiliatePaymentTypeEncoder: Encoder[AffiliatePaymentType] = new Encoder[AffiliatePaymentType] {
    override def apply(enum: AffiliatePaymentType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val AffiliatePaymentTypeDecoder: Decoder[AffiliatePaymentType] = new Decoder[AffiliatePaymentType] {
    final def apply(c: HCursor): Result[AffiliatePaymentType] = decodeEnum(c, AffiliatePaymentType, withDefault = true)
  }

  implicit val CancellationByTypeEncoder: Encoder[CancellationByType] = new Encoder[CancellationByType] {
    override def apply(enum: CancellationByType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val CancellationByTypeDecoder: Decoder[CancellationByType] = new Decoder[CancellationByType] {
    final def apply(c: HCursor): Result[CancellationByType] = decodeEnum(c, CancellationByType, withDefault = true)
  }

  implicit val CalculationMethodTypeEncoder: Encoder[CalculationMethodType] = new Encoder[CalculationMethodType] {
    override def apply(enum: CalculationMethodType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val CalculationMethodTypeDecoder: Decoder[CalculationMethodType] = new Decoder[CalculationMethodType] {
    final def apply(c: HCursor): Result[CalculationMethodType] =
      decodeEnum(c, CalculationMethodTypes, withDefault = true)
  }

  implicit val CancellationReasonTypeEncoder: Encoder[CancellationReasonType] = new Encoder[CancellationReasonType] {
    override def apply(enum: CancellationReasonType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val CancellationReasonTypeDecoder: Decoder[CancellationReasonType] = new Decoder[CancellationReasonType] {
    final def apply(c: HCursor): Result[CancellationReasonType] =
      decodeEnum(c, CancellationReasonType, withDefault = true)
  }

  implicit val CancellationResultTypeEncoder: Encoder[CancellationResultType] = new Encoder[CancellationResultType] {
    override def apply(enum: CancellationResultType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val CancellationValidationFailReasonsDecoder: Decoder[CancellationValidationFailReason] =
    new Decoder[CancellationValidationFailReason] {
      final def apply(c: HCursor): Result[CancellationValidationFailReason] =
        decodeEnum(c, CancellationValidationFailReasons, withDefault = true)
    }

  implicit val CancellationValidationFailReasonsEncoder: Encoder[CancellationValidationFailReason] =
    new Encoder[CancellationValidationFailReason] {
      override def apply(enum: CancellationValidationFailReason): Json = stringEncoder.apply(enum.entryName)
    }

  implicit val CancellationResultTypeDecoder: Decoder[CancellationResultType] = new Decoder[CancellationResultType] {
    final def apply(c: HCursor): Result[CancellationResultType] =
      decodeEnum(c, CancellationResultType, withDefault = true)
  }

  implicit val PostBookingPaymentTransactionTypeEncoder: Encoder[PostBookingPaymentTransactionType] =
    new Encoder[PostBookingPaymentTransactionType] {
      override def apply(enum: PostBookingPaymentTransactionType): Json = stringEncoder.apply(enum.entryName)
    }

  implicit val PostBookingPaymentTransactionTypeDecoder: Decoder[PostBookingPaymentTransactionType] =
    new Decoder[PostBookingPaymentTransactionType] {
      final def apply(c: HCursor): Result[PostBookingPaymentTransactionType] =
        decodeEnum(c, PostBookingPaymentTransactionType, withDefault = true)
    }

  implicit val PostBookingPaymentTypeEncoder: Encoder[PostBookingPaymentType] = new Encoder[PostBookingPaymentType] {
    override def apply(enum: PostBookingPaymentType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val PostBookingPaymentTypeDecoder: Decoder[PostBookingPaymentType] = new Decoder[PostBookingPaymentType] {
    final def apply(c: HCursor): Result[PostBookingPaymentType] =
      decodeEnum(c, PostBookingPaymentType, withDefault = true)
  }

  implicit val RefundOptionTypeEncoder: Encoder[RefundOptionType] = new Encoder[RefundOptionType] {
    override def apply(enum: RefundOptionType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val RefundOptionTypeDecoder: Decoder[RefundOptionType] = new Decoder[RefundOptionType] {
    final def apply(c: HCursor): Result[RefundOptionType] = decodeEnum(c, RefundOptionType, withDefault = true)
  }

  implicit val CancellationOptionTypeEncoder: Encoder[CancellationOptionType] = new Encoder[CancellationOptionType] {
    override def apply(enum: CancellationOptionType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val CancellationOptionTypeDecoder: Decoder[CancellationOptionType] = new Decoder[CancellationOptionType] {
    final def apply(c: HCursor): Result[CancellationOptionType] =
      decodeEnum(c, CancellationOptionType, withDefault = true)
  }

  implicit val M150TransparencyVersionTypeEncoder: Encoder[M150TransparencyVersionType] =
    new Encoder[M150TransparencyVersionType] {
      override def apply(enum: M150TransparencyVersionType): Json = stringEncoder.apply(enum.entryName)
    }

  implicit val M150TransparencyVersionTypeDecoder: Decoder[M150TransparencyVersionType] =
    new Decoder[M150TransparencyVersionType] {
      final def apply(c: HCursor): Result[M150TransparencyVersionType] =
        decodeEnum(c, M150TransparencyVersionType, withDefault = true)
    }

  implicit val PostBookingActionTypeEncoder: Encoder[PostBookingActionType] = new Encoder[PostBookingActionType] {
    override def apply(enum: PostBookingActionType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val PostBookingActionTypeDecoder: Decoder[PostBookingActionType] = new Decoder[PostBookingActionType] {
    final def apply(c: HCursor): Result[PostBookingActionType] = decodeEnum(c, PostBookingActionType, withDefault = true)
  }

  implicit val ConsolidatedPeekStateEncoder: Encoder[ConsolidatedPeekState] =
    (enum: ConsolidatedPeekState) => encodeEnum(enum)

  implicit val ConsolidatedPeekStateDecoder: Decoder[ConsolidatedPeekState] =
    (c: HCursor) => decodeEnum(c, ConsolidatedPeekState)

  implicit val ValueTagTypeEncoder: Encoder[ValueTagType] = (enum: ValueTagType) => encodeEnum(enum)

  implicit val ValueTagTypeDecoder: Decoder[ValueTagType] = (c: HCursor) => decodeEnum(c, ValueTagTypes)

  implicit val AccrualPolicyStartFromEncoder: Encoder[AccrualPolicyStartFrom] = new Encoder[AccrualPolicyStartFrom] {
    override def apply(a: AccrualPolicyStartFrom): Json = stringEncoder.apply(a.value)
  }
  implicit val LoyaltyPromotionBenefitTypeEncoder: Encoder[LoyaltyPromotionBenefitType] =
    new Encoder[LoyaltyPromotionBenefitType] {
      override def apply(a: LoyaltyPromotionBenefitType): Json = stringEncoder.apply(a.value)
    }
  implicit val LoyaltyPromotionBenefitCategoryEncoder: Encoder[LoyaltyPromotionBenefitCategory] =
    new Encoder[LoyaltyPromotionBenefitCategory] {
      override def apply(a: LoyaltyPromotionBenefitCategory): Json = stringEncoder.apply(a.value)
    }

  implicit val LoyaltyPromotionBenefitTypeDecoder: Decoder[LoyaltyPromotionBenefitType] =
    new Decoder[LoyaltyPromotionBenefitType] {
      override def apply(c: HCursor): Result[LoyaltyPromotionBenefitType] = stringDecoder.apply(c).map { s =>
        LoyaltyPromotionBenefitType.getFromValue(s)
      }
    }

  implicit val LoyaltyPromotionBenefitCategoryDecoder: Decoder[LoyaltyPromotionBenefitCategory] =
    new Decoder[LoyaltyPromotionBenefitCategory] {
      override def apply(c: HCursor): Result[LoyaltyPromotionBenefitCategory] = stringDecoder.apply(c).map { s =>
        LoyaltyPromotionBenefitCategory.getFromValue(s)
      }
    }

  implicit val AccrualPolicyStartFromDecoder: Decoder[AccrualPolicyStartFrom] = new Decoder[AccrualPolicyStartFrom] {
    override def apply(c: HCursor): Result[AccrualPolicyStartFrom] = stringDecoder.apply(c).map { s =>
      AccrualPolicyStartFrom.getFromValue(s)
    }
  }

  implicit val TaxRegistrationTypeEncoder: Encoder[TaxRegistrationType] = new Encoder[TaxRegistrationType] {
    override def apply(a: TaxRegistrationType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val TaxRegistrationTypeDecoder: Decoder[TaxRegistrationType] = new Decoder[TaxRegistrationType] {
    override def apply(c: HCursor): Result[TaxRegistrationType] = stringDecoder.apply(c).map { s =>
      TaxRegistrationType.namesToValuesMap.get(s).getOrElse(TaxRegistrationType.Unknown)
    }
  }

  implicit val PayLaterSubTypeCodeEncoder: Encoder[PayLaterSubTypeCode] = (enum: PayLaterSubTypeCode) => encodeEnum(enum)

  implicit val payLaterSubTypeCodeDecoder: Decoder[PayLaterSubTypeCode] =
    (c: HCursor) => decodeEnum(c, PayLaterSubTypeCode)

  implicit val PayLaterOptionCodeEncoder: Encoder[PayLaterOptionCode] = (enum: PayLaterOptionCode) => encodeEnum(enum)

  implicit val payLaterOptionCodeDecoder: Decoder[PayLaterOptionCode] =
    (c: HCursor) => decodeEnum(c, PayLaterOptionCode, withDefault = true)

  implicit val ReBookingActionTypeEncoder: Encoder[ReBookingActionType] = new Encoder[ReBookingActionType] {
    override def apply(enum: ReBookingActionType): Json = stringEncoder.apply(enum.entryName)
  }

  implicit val ReBookingActionTypeDecoder: Decoder[ReBookingActionType] = new Decoder[ReBookingActionType] {
    final def apply(c: HCursor): Result[ReBookingActionType] = decodeEnum(c, ReBookingActionType, withDefault = true)
  }

}

// scalastyle:on line.size.limit
