package api.request

import api.request.FeatureFlag.RateChannelSwap
import api.request.simulation.{SimulationRequest, SimulationRequestData}
import com.agoda.commons.models.rpc.request.RequestMeta
import com.agoda.finance.tax.services.tax.variabletax.VariableTaxHelper
import com.agoda.papi.enums.request.{BookingDurationType, FilterCriteria}
import com.agoda.papi.enums.room.RatePlanStatus
import com.agoda.papi.enums.simulation.{SupplyEquitySimulationMode, SupplyEquitySimulationSide}
import com.agoda.papi.pricing.supply.models.request.RatePlanInfo
import com.agoda.papi.ypl.models.{ASQ, YplChannel => DFCompositeChannel, YplMasterChannel => DFMasterChannel}
import com.agoda.upi.models.request.CartBaseRequest
import const.WarmupConstants.WARMUP_SEARCH_ID
import models._
import models.consts.{Cid, PlatformID}
import models.db._
import models.enums.FunnelType
import models.flow.{FeatureDegradationContext, Variant}
import models.pricing.enums._
import models.starfruit._
import models.utils.CartHelper
import models.whitelabel.{RegulationFeatureEnabledSetting, WhitelabelSetting}
import org.joda.time.{DateTime, Days}

import java.util.UUID
import scala.concurrent.duration._
object BaseRequest {
  private val rtlOnlyHelperRatePlan = List(RatePlanInfo(DFMasterChannel.RTL, RatePlanStatus.Helper))
  private val priusHelperRatePlans = List(DFMasterChannel.RTL, DFMasterChannel.APS, DFMasterChannel.APO)
}

/**
  * @param requestMeta Meta information about request
  * @param userContext User context JSON string sent from clients to be used by pricing.
  */
case class BaseRequest(searchId: String,
                       searchType: SearchType,
                       checkIn: DateTime,
                       checkOut: DateTime,
                       currency: Currency = "",
                       hotels: List[HotelId] = Nil,
                       channels: Set[DFCompositeChannel] = Set(DFMasterChannel.RTL),
                       abTests: List[AbTest] = Nil,
                       experiments: List[DFExperiment] = Nil,
                       occ: OccInfo = OccInfo(),
                       cInfo: ClientInfo = ClientInfo(),
                       flagInfo: FlagInfo = FlagInfo(),
                       trafficInfo: TrafficInfo = TrafficInfo(),
                       sessionInfo: Option[SessionInfo] = None,
                       requestMeta: Option[RequestMeta] = None,
                       loyalty: Option[LoyaltyInfo] = None,
                       bookingDate: DateTime = DateTime.now,
                       cheapestBySellIn: isBnplEnabled = false,
                       supplierIds: Set[SupplierId] = Set.empty,
                       sortField: Option[SortField] = Some(SortFields.SellEx),
                       priusID: FacilityId = 0,
                       clientVersion: String = "",
                       refId: Option[FacilityId] = None,
                       paymentTypeId: Option[FacilityId] = None,
                       featureFlags: List[FeatureFlag] = Nil, // TODO: use Set instead of List
                       isAllowBookOnRequest: Option[isBnplEnabled] = None,
                       maxRooms: Option[FacilityId] = None,
                       isSSR: Option[Boolean] = None,
                       simulateRequestData: Option[SimulateRequestData] = None,
                       requestedPrice: RequestedPrice = RequestedPrice.Exclusive,
                       bookingFilter: Option[BookingFilter] = None,
                       isCheapestRoomOnly: isBnplEnabled = false,
                       featureRequest: FeatureRequest = FeatureRequest(),
                       supplierPullMetadata: SupplierPullMetadata = SupplierPullMetadata(),
                       alternativeRoomType: AlternativeRoomType = AlternativeRoomTypes.NotRequire,
                       nosOfBedrooms: List[NoOfBedrooms] = Nil,
                       cheapestRoomFilters: List[CheapestRoomFilterId] = Nil,
                       experimentInfo: Option[DFExperimentInfo] = None,
                       userContext: Option[String] = None,
                       whiteLabelKey: Option[String] = None,
                       whitelabelSetting: WhitelabelSetting,
                       roomSortingStrategy: Option[RoomSortingStrategy] = None,
                       isSortRoomsByNetPrice: isBnplEnabled = false,
                       regulationFeatureEnabledSetting: Option[RegulationFeatureEnabledSetting] = None,
                       reBookingRequest: Option[ReBookingRequest] = None,
                       packaging: Option[PackagingRequest] = None,
                       cartRequest: Option[CartBaseRequest] = None,
                       isIncludeUsdAndLocalCurrency: isBnplEnabled = false,
                       additionalChannels: Set[DFCompositeChannel] = Set.empty,
                       bookingDurationType: List[String] = Nil,
                       roomBundleHints: List[RoomBundleRequest] = Nil,
                       priceHistory: Option[PriceHistoryRequest] = None,
                       showCMS: Option[isBnplEnabled] = Some(false),
                       selectedHourlySlot: Option[SelectedHourlySlot] = None,
                       pollingInfoRequest: Option[PollingInfoRequest] = None,
                       roomIdentifierFilter: Option[RoomIdentifierFilter] = None,
                       symmetricUidFilterOut: Option[SymmetricUidFilter] = None,
                       packagingFilterContext: Option[PackagingFilterContext] = None,
                       isMixNSaveSegmentSearch: Boolean = false,
                       contractsFilter: Option[List[ContractsFilterContext]] = None,
                       correlationId: Option[UUID] = None,
                       fencedRatePairs: Option[List[FencedRatePair]] = None,
                       externalLoyaltyRequest: Option[ExternalLoyaltyRequest] = None,
                       stateId: Option[Int] = None,
                       featureDegradationContext: FeatureDegradationContext = FeatureDegradationContext.default,
                       paymentInfo: Option[PaymentInfo] = None,
                       externalUserContext: Option[String] = None,
                       filterCriteria: Option[List[FilterCriteria]] = None,
                       ratePlansFilter: List[Int] = List.empty,
                       discountRequest: Option[DiscountRequest] = None,
                       agCorrelationId: Option[String] = None,
                       agAnalyticsSessionId: Option[String] = None,
                       benefitIdsFilter: Set[BenefitId] = Set.empty,
                       selectedCheckInTime: Option[String] = None,
                       hourlyDurationFilter: Set[Int] = Set.empty,
                       selectedRewardOptions: Option[String] = None,
                       override val simulationRequestData: Option[SimulationRequestData] = None,
                       externalLoyaltyProfile: Option[ExternalLoyaltyProfile] = None,
                       priceFreeze: Option[PriceFreeze] = None,
                       priceMetaData: Map[String, String] = Map.empty,
                       agEnv: Option[String] = None,
                       requiredBasis: Option[ApplyType] = None,
                       agodaCashBalance: Option[Double] = None,
                       agOriginState: Option[String] = None,
                       isXmlPartner: Boolean = false,
                       supplyEquitySimulationMode: Option[SupplyEquitySimulationMode] = None,
                       supplyEquitySimulationParameters: Option[SupplyEquitySimulationParameters] = None,
                       minBookingCountForSuperAgg: Option[Seq[MinBookingCountForSuperAgg]] = None,
                       supplyEquitySimulationSide: Option[SupplyEquitySimulationSide] = None)
  extends WithBaseRequest
    with SimulationRequest {

  import BaseRequest._

  lazy val baseRequest: BaseRequest = this

  lazy val lengthOfStay: Int = Days.daysBetween(checkIn, checkOut).getDays()
  lazy val leadDays: Int = Days.daysBetween(bookingDate.withTimeAtStartOfDay(), checkIn).getDays

  private val experimentsMap = experiments.map(x => (x.name, x.variant.toUpper)).toMap

  lazy val bTests: Set[Int] = abTests.filter(_.abUser.toLower == 'b').map(_.testId).toSet

  def userId: String = baseRequest.sessionInfo.flatMap(_.userId).getOrElse("")

  //  determine is current user is treated as new-feature-active
  def isAbUserActive(test: Int): Boolean = bTests.contains(test)

  def getForceExperimentVariant(experiment: String): Option[AbUser] = experimentsMap.get(experiment)

  def getForcedExperiments(): Map[String, AbUser] = experimentsMap

  //  POC feature from FE will pass force experiment B in the request to BE system.
  def isBVariantForcedForExperiment(expName: String): Boolean = getForceExperimentVariant(expName).contains(Variant.B)

  lazy val isRateChannelSwap: Boolean = isBookingRequest && featureFlags.contains(RateChannelSwap)

  /**
    * Determines whether channel (rate plan) was requested by client or not
    * @param channel Rate plan ID (RTL = 1, APS = 2, etc..)
    */
  def isChannelRequested(channel: DFCompositeChannel): Boolean = channels.contains(channel)
  def isBookingRequest: Boolean = searchType == SearchTypes.HotelForBooking
  def isSupplyEquitySimulationRequest: Boolean = supplyEquitySimulationMode.isDefined
  def isSupplyEquitySimulationRequestWithoutSimulation: Boolean =
    supplyEquitySimulationMode.contains(SupplyEquitySimulationMode.WithoutSimulation)
  def isSimulationSearchRequest: Boolean = simulationRequestData.nonEmpty
  def isRoomIdentifierFilterRequest: Boolean = roomIdentifierFilter.nonEmpty
  def isHourlyBookingRequest: Boolean = isBookingRequest && selectedHourlySlot.isDefined
  def isHourlySearchRequest: Boolean = bookingDurationType.map(_.toLowerCase).contains(BookingDurationType.Hourly.value)
  def isSSRUserRequest: Boolean = isSSR.contains(true) && isCheapestRoomOnly && cInfo.platform.exists(PlatformID.isUser)
  def isRebookingRequestV3: Boolean = reBookingRequest.exists(_.originalSellIn.exists(_ > 0))
  def isRebookingRequestV3MatchLocal: Boolean = isRebookingRequestV3 && reBookingRequest.exists(_.matchLocalCurrency)
  def isRebookingRequestV3MatchUSD: Boolean = isRebookingRequestV3 && reBookingRequest.exists(_.matchUSD)
  def isRebookingRequestV1V2: Boolean =
    reBookingRequest.exists(rb => rb.customerPaidPrice > 0d) && !isRebookingRequestV3
  lazy val isLogIn: Boolean = loyalty.exists(_.isLogin.getOrElse(false))

  lazy val isAsq = this.cInfo.cid.exists(ASQ.cidList.contains)

  lazy val ratePlanInfos: List[RatePlanInfo] = computeRatePlanInfos(channels)

  def computeRatePlanInfos(requestedChannels: Set[DFCompositeChannel]): List[RatePlanInfo] = {
    val ratePlansWithAPOFilter: List[RatePlanInfo] = flagInfo.filterAPO match {
      case true => (requestedChannels + DFMasterChannel.APO).map(RatePlanInfo(_)).toList
      case _ => requestedChannels.map(RatePlanInfo(_)).toList
    }

    val ratePlanHelpers = priusID > 0 match {
      case true => priusHelperRatePlans
          .withFilter(r => !ratePlansWithAPOFilter.exists(_.channel == r))
          .map(RatePlanInfo(_, RatePlanStatus.Helper))
      case _ => rtlOnlyHelperRatePlan.filter(r => !requestedChannels.contains(r.channel))
    }

    // If APS Peek is true, request for APS rates as well. This will be filtered out by Soybean streamlining
    val apsPeekHelper =
      if (featureFlags.contains(FeatureFlag.APSPeek) && !requestedChannels.contains(DFMasterChannel.APS)) {
        List(RatePlanInfo(DFMasterChannel.APS, RatePlanStatus.Helper))
      } else {
        List.empty
      }

    val requestedRatePlanHelpers = ratePlansWithAPOFilter ++ ratePlanHelpers ++ apsPeekHelper

    val additionalRateHelper = additionalChannels
      .filter(additionalRatePlan => requestedRatePlanHelpers.find(_.channel == additionalRatePlan).isEmpty)
      .map(RatePlanInfo(_, RatePlanStatus.Helper))

    requestedRatePlanHelpers ++ additionalRateHelper.toList
  }

  /**
    * Use it for get platform id
    */
  lazy val platformId: Int = cInfo.platform.getOrElse(RequestConst.undefinedPlatformId)

  lazy val siteId: Int = cInfo.cid.getOrElse(RequestConst.undefinedSiteId)

  lazy val whiteLabeLID: Int = whitelabelSetting.id

  private def isPageSearchType: Boolean = searchType match {
    case SearchTypes.HotelSearch | SearchTypes.HotelForBooking | SearchTypes.HotelListSearch |
        SearchTypes.MissingDMCHotelsSearch => false
    case _ => true
  }

  /**
    * Determine is current request is page search or not.
    * In future we'll just use isSSR field provided by PAPI
    */
  lazy val isPageSearchRequest: Boolean = isSSR getOrElse isPageSearchType

  def isFeatureExist(feature: FeatureFlag) = featureFlags.contains(feature)

  lazy val ttl: Option[FiniteDuration] = requestMeta.flatMap(_.ttl)
  lazy val attempt: Int = requestMeta.map(_.attempt).getOrElse(1)

  lazy val isFromBcom: Boolean = cInfo.cid.exists(Cid.BCOM_CID.contains)

  // ToDo: Remove this condition once H+H is migrated to Cart in all platforms
  @deprecated(message = "Deprecated", since = "2024-10-31")
  def isHotelPlusHotelRequest: Boolean = featureFlags.contains(FeatureFlag.MultiHotel)

  // Todo: marked as deprecated, please clean up
  @deprecated(message = "Deprecated", since = "2024-10-31")
  def isConnectedTripRequest: Boolean = featureFlags.contains(FeatureFlag.ConnectedTrip)

  // TODO: Remove this as it will be replaced by isPackagingFunnel
  def isFlightPlusHotelRequest: Boolean = isPackagingFunnel && !isHotelPlusHotelRequest

  /**
    * This will be a replacement of isFlightPlusHotelRequest
    * isHotelPlusHotelRequest is deprecated and isPackagingFunnelRequest == isPackagingFunnel eventually
    */
  // TODO: Remove check for packaging once Package migrated to Cart in all platforms
  def isPackagingFunnel: Boolean = packaging.nonEmpty || CartHelper.isPackagingFunnel(cartRequest)

  def isPackagingFunnelRequest: Boolean = isFlightPlusHotelRequest || isHotelPlusHotelRequest

  // TODO: Clean up, marked as deprecated
  @deprecated(message = "Deprecated", since = "2024-10-31")
  def isHotelPlusSecondHotelRequest: Boolean = isHotelPlusHotelRequest && packaging.flatMap(_.token).nonEmpty

  def isPriceShopperRequest: Boolean = featureFlags.contains(FeatureFlag.PriceShopperRequest)

  def isCartFeatureEnabled: Boolean = CartHelper.isCartFeatureEnabled(cartRequest)

  /**
    * Determine is cartRequest from non-package funnel based on ProductEntry in cartRequest
    * Check if cartRequest without ProductEntries.PackageProduct
    */
  def isCartFunnelRequest: Boolean = cartRequest.isDefined && !isPackagingFunnel

  def getFunnel: String =
    if (isHotelPlusHotelRequest) {
      FunnelType.MultiHotel.toString
    } else if (isFlightPlusHotelRequest) {
      FunnelType.Packages.toString
    } else if (isCartFeatureEnabled) {
      FunnelType.Cart.toString
    } else {
      FunnelType.Hotel.toString
    }

  lazy val getRequestType: String =
    if (isSSR.getOrElse(false)) "ssr"
    else if (isBookingRequest) {

      if (isRebookingRequestV3) "rebookv3"
      else if (isRebookingRequestV1V2) "rebookv1v2"
      else "booking"
    } else "property"

  def isCreditCardPresent: Boolean = {
    val creditCard = paymentInfo.flatMap(_.creditCardInfo)
    isBookingRequest && (creditCard.flatMap(_.ccToken).isDefined || creditCard.flatMap(_.ccof).isDefined)
  }

  def isPriceFreezeRequest: Boolean = featureFlags.contains(FeatureFlag.PriceFreezeExercise)

  def isVariableTaxApplied(isEnableMalaysiaTourismTaxForCitiUs: Boolean = false): Boolean = {
    val hourlyFrom = selectedHourlySlot.map(_.from)
    val hourlyDuration = selectedHourlySlot.map(_.duration)
    val isRegulationDisplayVariableTax = regulationFeatureEnabledSetting.map(_.isDisplayVariableTax).getOrElse(false)
    val isContainVariableTaxFeatureFlag = featureFlags.contains(FeatureFlag.VariableTax)
    VariableTaxHelper.canVariableTaxApplied(
      whiteLabeLID,
      isEnableMalaysiaTourismTaxForCitiUS = isEnableMalaysiaTourismTaxForCitiUs,
      isHourly = isHourlyBookingRequest,
      hourlyFrom = hourlyFrom,
      hourlyDuration = hourlyDuration,
      isAffiliateRequest = isXmlPartner,
      isBookingRequest = isBookingRequest,
      isRegulationDisplayVariableTax = isRegulationDisplayVariableTax,
      isContainVariableTaxFeatureFlag = isContainVariableTaxFeatureFlag,
    )
  }

  def isPriceStateRequest: Boolean = bookingFilter.exists(_.isPriceStateRequest)

  lazy val isWarmup: Boolean = searchId == WARMUP_SEARCH_ID

  lazy val checkInStr = checkIn.toString("yyyy-MM-dd")
  lazy val checkOutStr = checkOut.toString("yyyy-MM-dd")

  def bookingSupplier =
    bookingFilter.map(_.uidList.flatMap(_.roomIdentifier.map(_.coreFields.supplierId))).getOrElse(List.empty).toSet

  def shouldAllocateDynamicDLExp: Boolean = isBookingRequest &&
    !baseRequest.isCartFeatureEnabled &&
    !baseRequest.isRebookingRequestV1V2 && !baseRequest.isRebookingRequestV3 &&
    regulationFeatureEnabledSetting.exists(_.isDynamicDownliftEnabled)

  def shouldAllocatePmcFixFencesComparingRooms: Boolean = isBookingRequest &&
    !baseRequest.isCartFeatureEnabled &&
    !baseRequest.isRebookingRequestV1V2 && !baseRequest.isRebookingRequestV3

  def shouldAllocateValueTag: Boolean = !isBookingRequest && regulationFeatureEnabledSetting.exists(_.isValueTagEnabled)
}
