package com.agoda.papi.pricing.internal.models

import api.request.FeatureFlag
import models.consts.ABTest
import models.flow.{FlowContext, Variant}

case class ExperimentCarrier(
  needLocalPaymentForAgency: Boolean = false,
  overrideMinRedeemableGiftCard: Boolean = false,
  isBreakfastUpSellOnBookingFormEnable: Boolean = false,
  upsellBreakfast: Boolean = false,
  isFixRoundingErrorForReBooking: Boolean = false,
  useYPLOccupancyCalculationForBCOM: Boolean = false,
  isOverrideOriginalTotalPrice: Boolean = false,
  isQuantumPaymentsEnabled: Boolean = false,
  isFilterOutBenefitsForAsoEnabled: Boolean = false,
  fixPaymentAmountCalculation: Boolean = false,
  addTaxToGovToMerchantComSellIn: Boolean = false,
  isPromoDiscountPerGuestValidate: Boolean = false,
  isFixExtrabedPaymentAmountMismatch: Boolean = false,
  isRemoveBNPLForPhuket: Boolean = false,
  isApplyFixForPartnerCancellationPolicy: Boolean = false,
  isAdjustCommissionForMarriott: Boolean = false,
  isHashDmcUidToRoomIdentifier: Boolean = false,
  useRequiredBasisFromPAPI: Boolean = false,
  isPartner0DCancellationPolicyFix: Boolean = false,
  shouldBlockBnplForIndiaOutbound: Boolean = false,
  isZeroCashbackLogicRemovalExperiment: Boolean = false,
  isFixUpliftExchangeForM150: Boolean = false,
  isDisableCumulativeDiscountForXML: Boolean = false,
  isRTAPricingEnabled: Boolean = false,
  isApplyFixForZeroFeePartnerCancellationPolicy: Boolean = false,
  isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed: Boolean = false,
  isApplyProcessingFeeOnTaxWeight: Boolean = false,
  isEnablePerBookCapacity: Boolean = false,
  isEnableStackedCouponCOR: Boolean = false,
  isUsingTaxTypeCms: Boolean = false,
  isPriceAdjustmentRequestMigrated: Boolean = false,
  isIncludePublishedPriceESSInSellEx: Boolean = false,
  isMigrateUsdSellAllInToEbe: Boolean = false,
  isValidateAndRejectInconsistencyRoomItemBreakdown: Boolean = false,
  isMigratePaymentBreakdownToFinancialBreakdown: Boolean = false,
  isPmcHybridDiscountProcedure: Boolean = false,
  isPmcConsolidatedAppliedDiscountMetric: Boolean = false,
  fixDoubleDeductPoints: Boolean = false,
  excludeIndiaHotelForAgodaCashRedemption: Boolean = false,
  removeMultiHotelLogic: Boolean = false,
  updateSurchargePriceBreakdown: Boolean = false,
  forceSkipUpdateSurchargePriceBreakdown: Boolean = false,
  isFixTaxToPropertyApplyRate: Boolean = false,
  updatePriceAdjustmentIdToRoomProto: Boolean = false,
  isUsingTmapiTaxType: Boolean = false,
  returnCounterFeeInclusive: Boolean = false,
  fixElapiShowIncorrectAmount: Boolean = false,
  populatePriceWithoutExcludedChargesInExtraInfo: Boolean = false,
  showExclusiveWithFeesToUsDestination: Boolean = false,
  isVariableTaxFixForPartnerEnabled: Boolean = false,
  shouldRemoveBnplForTurkishLira: Boolean = false,
  enableIndiaBNPLForBWZP: Boolean = false,
  disableRemoveYCSPromotion: Boolean = false,
  isEnableLinePayTwd: Boolean = false,
)

object ExperimentCarrier {

  def apply(ctx: FlowContext): ExperimentCarrier = {
    val needLocalPaymentForAgency = ctx.determineVariant(ABTest.NEED_LOCAL_PAYMENT_FOR_AGENCY) == Variant.B
    val isBreakfastUpSellOnBookingFormEnable =
      ctx.baseRequest.regulationFeatureEnabledSetting.forall(_.isBreakfastUpsellEnabled)
    val upsellBreakfast =
      isBreakfastUpSellOnBookingFormEnable && ctx.baseRequest.featureFlags.contains(FeatureFlag.BreakfastUpsell)
    val isFixRoundingErrorForCR = ctx.determineVariant(ABTest.FIX_ROUNDING_ERROR_FOR_REBOOKING) == Variant.B
    val isOverrideOriginalTotalPrice = ctx.determineVariant(ABTest.OVERRIDE_ORIGINAL_TOTAL) == Variant.B
    val fixPaymentAmountCalculation = ctx.determineVariant(ABTest.FIX_PAYMENT_AMOUNT_MISMATCH) == Variant.B
    val addTaxToGovToMerchantComSellIn =
      ctx.determineVariant(ABTest.ADD_GOVERNMENT_TAX_TO_AFF_MERCHANT_COM_SELLIN) == Variant.B
    val isPromoDiscountPerGuestValidate = ctx.determineVariant(ABTest.IS_PROMO_DISCOUNT_PER_GUEST_VALIDATE) == Variant.B
    val isFixExtrabedPaymentAmountMismatch =
      ctx.determineVariant(ABTest.FIX_EXTRA_BED_PAYMENT_AMOUNT_MISMATCH) == Variant.B
    val isRemoveBNPLForPhuket = ctx.determineVariant(ABTest.REMOVE_BNPL_FOR_PHUKET) == Variant.B
    val isApplyFixForPartnerCancellationPolicy =
      ctx.determineVariant(ABTest.IS_APPLY_FIX_FOR_PARTNER_CANCELLATION_POLICY) == Variant.B
    val isAdjustCommissionForMarriott = ctx.determineVariant(ABTest.ADJUST_COMMISSION_MARRIOTT) == Variant.B
    val isHashDmcUidToRoomIdentifier = ctx.determineVariant(ABTest.HASH_DMCUID_TO_ROOMIDENTIFIER) == Variant.B
    val useRequiredBasisFromPAPI = ctx.determineVariant(ABTest.USE_REQUIRED_BASIS_FROM_PAPI) == Variant.B
    val isPartner0DCancellationPolicyFix = ctx.determineVariant(ABTest.AFFILIATE_CANCELLATION_POLICY_0D) == Variant.B
    val shouldBlockBnplForIndiaOutbound = ctx.determineVariant(ABTest.BLOCK_BNPL_FOR_INDIA_OUTBOUND) == Variant.B
    val isZeroCashbackLogicRemovalExperiment = ctx.determineVariant(ABTest.ZERO_CASHBACK_LOGIC_REMOVAL_EXP) == Variant.B
    val isFixUpliftExchangeForM150 = ctx.determineVariant(ABTest.FIX_UPLIFT_EXCHANGE_FOR_M150) == Variant.B
    val isDisableCumulativeDiscountForXML =
      ctx.determineVariant(ABTest.DISABLE_CUMULATIVE_DISCOUNT_FOR_XML) == Variant.B
    val isRTAPricingEnabled = ctx.determineVariant(ABTest.DF_RTA_PRICING) == Variant.B
    val isApplyFixForZeroFeePartnerCancellationPolicy =
      ctx.determineVariant(ABTest.AFFILIATE_ZERO_FEE_CANCELLATION_POLICY) == Variant.B
    val isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed =
      ctx.determineVariant(ABTest.FIX_TAX_FINANCIAL_BREAKDOWN_QUANTITY_ON_SURCHARGE_EXTRABED) == Variant.B
    val isApplyProcessingFeeOnTaxWeight = ctx.determineVariant(ABTest.APPLY_PF_ON_TAX_WEIGHT) == Variant.B
    val isEnablePerBookCapacity = ctx.determineVariant(ABTest.IS_ENABLE_PER_BOOK_CAPACITY) == Variant.B
    val isCCorAllowed = ctx.baseRequest.regulationFeatureEnabledSetting.forall(_.isCCorAllowed)
    val isEnableStackedCouponCOR = !isCCorAllowed
    val isUsingTaxTypeCms = ctx.determineVariant(ABTest.TAX_CMS_APPLY_FOR_V2) == Variant.B

    val isPriceAdjustmentRequestMigrated =
      ctx.determineVariant(ABTest.AFFILIATE_PRICE_ADJUSTMENT_REQUEST_MIGRATION) == Variant.B
    val isIncludePublishedPriceESSInSellEx =
      ctx.determineVariant(ABTest.INCLUDE_PUBLISHED_PRICE_ESS_IN_SELL_EXCLUSIVE) == Variant.B
    val isMigrateUsdSellAllInToEbe = ctx.determineVariant(ABTest.MIGRATE_USD_SELLALLIN_TO_USE_EBE) == Variant.B

    val isValidateAndRejectInconsistencyRoomItemBreakdown =
      ctx.determineVariant(ABTest.VALIDATE_AND_REJECT_INCONSISTENCY_ROOM_ITEM_BREAKDOWN) == Variant.B
    val isMigratePaymentBreakdownToFinancialBreakdown =
      ctx.determineVariant(ABTest.MIGRATE_PREPARE_INTERNAL_LOYALTY_TO_PAYMENT_MODULE) == Variant.B
    val isPmcHybridDiscountProcedure = ctx.determineVariant(ABTest.PMC_HYBRID_DISCOUNT_PROCEDURE) == Variant.B
    val isPmcConsolidatedAppliedDiscountMetric =
      ctx.determineVariant(ABTest.PMC_CONSOLIDATED_APPLIED_DISCOUNTS_METRIC) == Variant.B
    val fixDoubleDeductPoints = ctx.determineVariant(ABTest.FIX_DOUBLE_DEDUCT_POINTS) == Variant.B
    val excludeIndiaHotelForAgodaCashRedemption =
      ctx.determineVariant(ABTest.EXCLUDE_INDIA_HOTEL_FOR_AGODA_CASH_REDEMPTION) == Variant.B
    val isRemoveMultiHotelLogic =
      ctx.baseRequest.isHotelPlusHotelRequest && ctx.determineVariant(ABTest.REMOVE_MULTI_HOTEL_LOGIC) == Variant.B
    val updateSurchargePriceBreakdown = ctx.determineVariant(ABTest.UPDATE_SURCHARGE_PRICE_BREAKDOWN) == Variant.B
    val forceSkipUpdateSurchargePriceBreakdown =
      ctx.determineVariant(ABTest.FORCE_SKIP_UPDATE_SURCHARGE_PRICE_BREAKDOWN) == Variant.B
    val isFixTaxToPropertyApplyRate = ctx.determineVariant(ABTest.FIX_TAX_TO_PROPERTY_APPLY_RATE) == Variant.B
    val updatePriceAdjustmentIdToRoomProto =
      ctx.determineVariant(ABTest.UPDATE_PRICE_ADJUSTMENT_ID_TO_ROOM_PROTO) == Variant.B
    val isUsingTmapiTaxType = ctx.determineVariant(ABTest.TMAPI_TAX_TYPE) == Variant.B
    val returnCounterFeeInclusive = ctx.determineVariant(ABTest.EXCLUDE_COUNTER_FEE_INCLUSIVE_FROM_BCOM) == Variant.B
    val fixElapiShowIncorrectAmount = ctx.determineVariant(ABTest.FIX_ELAPI_SHOW_INCORRECT_AMOUNT) == Variant.B
    val populatePriceWithoutExcludedChargesInExtraInfo =
      ctx.determineVariant(ABTest.POPULATE_EXTRA_INFO_WITH_PRICE_WITHOUT_EXCLUDED_CHARGES) == Variant.B
    val showExclusiveWithFeesToUsDestination =
      ctx.determineVariant(ABTest.SHOW_EXCLUSIVE_WITH_FEE_TO_US_DESTINATION) == Variant.B
    val shouldRemoveBnplForTurkishLira = ctx.determineVariant(ABTest.SHOULD_BLOCK_BNPL_FOR_TURKISH_LIRA) == Variant.B

    val isVariableTaxFixForPartnerEnabled = ctx.determineVariant(ABTest.FIX_VARIABLE_TAX_FOR_PARTNER) == Variant.B
    val enableIndiaBNPLForBWZP = ctx.determineVariant(ABTest.ENABLE_INDIA_BNPL_FOR_BWZP) == Variant.B
    val disableRemoveYCSPromotionExp = ctx.determineVariant(ABTest.DISABLE_REMOVE_YCS_PROMOTION) == Variant.B
    val isEnableLinePayTwd = ABTest.PC_7098_ENABLE_LINEPAY
      .exists(experiment => ctx.determineVariant(experiment) == Variant.B)

    ExperimentCarrier(
      needLocalPaymentForAgency = needLocalPaymentForAgency,
      isBreakfastUpSellOnBookingFormEnable = isBreakfastUpSellOnBookingFormEnable,
      upsellBreakfast = upsellBreakfast,
      isFixRoundingErrorForReBooking = isFixRoundingErrorForCR,
      isOverrideOriginalTotalPrice = isOverrideOriginalTotalPrice,
      fixPaymentAmountCalculation = fixPaymentAmountCalculation,
      addTaxToGovToMerchantComSellIn = addTaxToGovToMerchantComSellIn,
      isPromoDiscountPerGuestValidate = isPromoDiscountPerGuestValidate,
      isFixExtrabedPaymentAmountMismatch = isFixExtrabedPaymentAmountMismatch,
      isRemoveBNPLForPhuket = isRemoveBNPLForPhuket,
      isApplyFixForPartnerCancellationPolicy = isApplyFixForPartnerCancellationPolicy,
      isAdjustCommissionForMarriott = isAdjustCommissionForMarriott,
      isHashDmcUidToRoomIdentifier = isHashDmcUidToRoomIdentifier,
      useRequiredBasisFromPAPI = useRequiredBasisFromPAPI,
      isPartner0DCancellationPolicyFix = isPartner0DCancellationPolicyFix,
      shouldBlockBnplForIndiaOutbound = shouldBlockBnplForIndiaOutbound,
      isZeroCashbackLogicRemovalExperiment = isZeroCashbackLogicRemovalExperiment,
      isFixUpliftExchangeForM150 = isFixUpliftExchangeForM150,
      isDisableCumulativeDiscountForXML = isDisableCumulativeDiscountForXML,
      isRTAPricingEnabled = isRTAPricingEnabled,
      isApplyFixForZeroFeePartnerCancellationPolicy = isApplyFixForZeroFeePartnerCancellationPolicy,
      isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed =
        isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed,
      isApplyProcessingFeeOnTaxWeight = isApplyProcessingFeeOnTaxWeight,
      isEnablePerBookCapacity = isEnablePerBookCapacity,
      isEnableStackedCouponCOR = isEnableStackedCouponCOR,
      isUsingTaxTypeCms = isUsingTaxTypeCms,
      isPriceAdjustmentRequestMigrated = isPriceAdjustmentRequestMigrated,
      isIncludePublishedPriceESSInSellEx = isIncludePublishedPriceESSInSellEx,
      isMigrateUsdSellAllInToEbe = isMigrateUsdSellAllInToEbe,
      isValidateAndRejectInconsistencyRoomItemBreakdown = isValidateAndRejectInconsistencyRoomItemBreakdown,
      isMigratePaymentBreakdownToFinancialBreakdown = isMigratePaymentBreakdownToFinancialBreakdown,
      isPmcHybridDiscountProcedure = isPmcHybridDiscountProcedure,
      isPmcConsolidatedAppliedDiscountMetric = isPmcConsolidatedAppliedDiscountMetric,
      fixDoubleDeductPoints = fixDoubleDeductPoints,
      excludeIndiaHotelForAgodaCashRedemption = excludeIndiaHotelForAgodaCashRedemption,
      removeMultiHotelLogic = isRemoveMultiHotelLogic,
      updateSurchargePriceBreakdown = updateSurchargePriceBreakdown,
      forceSkipUpdateSurchargePriceBreakdown = forceSkipUpdateSurchargePriceBreakdown,
      isFixTaxToPropertyApplyRate = isFixTaxToPropertyApplyRate,
      updatePriceAdjustmentIdToRoomProto = updatePriceAdjustmentIdToRoomProto,
      isUsingTmapiTaxType = isUsingTmapiTaxType,
      returnCounterFeeInclusive = returnCounterFeeInclusive,
      fixElapiShowIncorrectAmount = fixElapiShowIncorrectAmount,
      populatePriceWithoutExcludedChargesInExtraInfo = populatePriceWithoutExcludedChargesInExtraInfo,
      showExclusiveWithFeesToUsDestination = showExclusiveWithFeesToUsDestination,
      isVariableTaxFixForPartnerEnabled = isVariableTaxFixForPartnerEnabled,
      shouldRemoveBnplForTurkishLira = shouldRemoveBnplForTurkishLira,
      enableIndiaBNPLForBWZP = enableIndiaBNPLForBWZP,
      disableRemoveYCSPromotion = disableRemoveYCSPromotionExp,
      isEnableLinePayTwd = isEnableLinePayTwd,
    )
  }
}
