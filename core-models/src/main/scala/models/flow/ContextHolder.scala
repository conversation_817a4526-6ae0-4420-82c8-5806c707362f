package models.flow

import com.agoda.commons.http.server.models.RequestContext
import com.agoda.platform.service.context.ServiceHeaders
import io.opentelemetry.api.trace.Span
import models.flow.simulation.SimulationContext
import models.flow.supplyequitysimulation.SupplyEquitySimulationContext
import org.json4s._
import org.json4s.native.Serialization
import org.json4s.native.Serialization.write

import scala.util.Try

case class ContextHolder(degradationContext: FeatureDegradationContext,
                         traceID: String = "",
                         headersContext: Option[HeadersContext] = None,
                         trafficSource: Option[String] = None,
                         simulationContext: Option[SimulationContext] = None,
                         supplyEquitySimulationContext: Option[SupplyEquitySimulationContext] = None,
                         earlyExperiments: List[String] = Nil,
                         requestContext: Option[RequestContext] = None)

case class HeadersContext(agCorrelationId: Option[String],
                          agAnalyticsSessionId: Option[String],
                          agEnv: Option[String],
                          agOriginState: Option[String],
                          agMsePricingToken: Option[String],
                          agClientProfile: Option[String]) {

  def getSerializeSandboxHeaders: Option[String] = {
    implicit val formats: Formats = Serialization.formats(NoTypeHints)

    // Build a map of present headers
    val headerValues: Map[String, String] = Map(
      ServiceHeaders.AgMsePricingToken.toString -> agMsePricingToken,
      ServiceHeaders.AgClientProfile.toString -> agClientProfile,
    ).collect { case (key, Some(value)) => key -> value }

    if (headerValues.nonEmpty) {
      Some(write(headerValues))
    } else {
      None
    }
  }

}

object ContextHolder {
  val defaultContext: ContextHolder = ContextHolder(
    degradationContext = FeatureDegradationContext.default,
  )

  def apply(ctx: RequestContext,
            headersContext: HeadersContext,
            trafficSource: Option[String],
            simulationContext: Option[SimulationContext],
            supplyEquitySimulationContext: Option[SupplyEquitySimulationContext]): ContextHolder = {
    val degradationContext = FeatureDegradationContext(ctx.client.degradeMode)
    new ContextHolder(degradationContext,
                      getTraceId,
                      Some(headersContext),
                      trafficSource,
                      simulationContext,
                      supplyEquitySimulationContext,
                      requestContext = Some(ctx))
  }

  def getTraceId: String = Try {
    val span = Span.current().getSpanContext
    if (span.isValid) span.getTraceId else ""
  }.getOrElse("")
}
