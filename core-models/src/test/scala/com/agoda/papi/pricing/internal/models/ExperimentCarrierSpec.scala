package com.agoda.papi.pricing.internal.models

import api.request.FeatureFlag
import com.agoda.platform.pricing.models.utils.FlowContextTestDataBuilders
import models.consts.ABTest
import models.utils.DFCoreModelsTestDataBuilders
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class ExperimentCarrierSpec
  extends AnyWordSpec
    with Matchers
    with FlowContextTestDataBuilders
    with DFCoreModelsTestDataBuilders {

  "ExperimentCarrier" should {

    val testTable = Seq[(String, String, ExperimentCarrier => Boolean)](
      ("NBF-1398", "needLocalPaymentForAgency", _.needLocalPaymentForAgency),
      ("", "upsellBreakfast", _.upsellBreakfast),
      ("CEGHZ-777", "isFixRoundingErrorForReBooking", _.isFixRoundingErrorForReBooking),
      ("PLTAPI-761", "isOverrideOriginalTotalPrice", _.isOverrideOriginalTotalPrice),
      ("SCAL-1085", "fixPaymentAmountCalculation", _.fixPaymentAmountCalculation),
      ("SCAL-1167", "addTaxToGovToMerchantComSellIn", _.addTaxToGovToMerchantComSellIn),
      ("JPST-573", "isPromoDiscountPerGuestValidate", _.isPromoDiscountPerGuestValidate),
      ("SCAL-1289", "isFixExtrabedPaymentAmountMismatch", _.isFixExtrabedPaymentAmountMismatch),
      ("SCAL-8490", "isApplyFixForPartnerCancellationPolicy", _.isApplyFixForPartnerCancellationPolicy),
      ("OPA-1878", "isRemoveBNPLForPhuket", _.isRemoveBNPLForPhuket),
      ("SUPPIO-5595", "isAdjustCommissionForMarriott", _.isAdjustCommissionForMarriott),
      ("AFF-3064", "isHashDmcUidToRoomIdentifier", _.isHashDmcUidToRoomIdentifier),
      ("UPF-423", "shouldBlockBnplForIndiaOutbound", _.shouldBlockBnplForIndiaOutbound),
      ("PAPI-17861", "isPartner0DCancellationPolicyFix", _.isPartner0DCancellationPolicyFix),
      ("DFOPS-1412", "isFixUpliftExchangeForM150", _.isFixUpliftExchangeForM150),
      ("DFOPS-1542", "isDisableCumulativeDiscountForXML", _.isDisableCumulativeDiscountForXML),
      ("DF-RTA-PRICING", "isRTAPricingEnabled", _.isRTAPricingEnabled),
      ("PAPI-19005", "isApplyFixForZeroFeePartnerCancellationPolicy", _.isApplyFixForZeroFeePartnerCancellationPolicy),
      ("NEP-19658",
       "isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed",
       _.isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed),
      ("PAPI-19144", "isEnablePerBookCapacity", _.isEnablePerBookCapacity),
      ("VYG-937", "isUsingTaxTypeCms", _.isUsingTaxTypeCms),
      ("PAPI-19481", "isPriceAdjustmentRequestMigrated", _.isPriceAdjustmentRequestMigrated),
      ("RTAP-159", "isIncludePublishedPriceESSInSellEx", _.isIncludePublishedPriceESSInSellEx),
      ("VEL-1091", "isMigrateUsdSellAllInToEbe", _.isMigrateUsdSellAllInToEbe),
      ("DFOPS-2744",
       "isValidateAndRejectInconsistencyRoomItemBreakdown",
       _.isValidateAndRejectInconsistencyRoomItemBreakdown),
      ("VEL-1239", "isMigratePaymentBreakdownToFinancialBreakdown", _.isMigratePaymentBreakdownToFinancialBreakdown),
      ("PMC-4243", "isPmcHybridDiscountProcedure", _.isPmcHybridDiscountProcedure),
      ("PMC-4650", "isPmcConsolidatedAppliedDiscountMetric", _.isPmcConsolidatedAppliedDiscountMetric),
      ("DFOPS-3864", "fixDoubleDeductPoints", _.fixDoubleDeductPoints),
      ("LOY-8916", "excludeIndiaHotelForAgodaCashRedemption", _.excludeIndiaHotelForAgodaCashRedemption),
      ("PAPI-22657", "updateSurchargePriceBreakdown", _.updateSurchargePriceBreakdown),
      ("PAPI-22832", "forceSkipUpdateSurchargePriceBreakdown", _.forceSkipUpdateSurchargePriceBreakdown),
      ("PAPI-22947", "isFixTaxToPropertyApplyRate", _.isFixTaxToPropertyApplyRate),
      ("CWSD-1224", "updatePriceAdjustmentIdToRoomProto", _.updatePriceAdjustmentIdToRoomProto),
      ("IMV-2362", "isUsingTmapiTaxType", _.isUsingTmapiTaxType),
      ("BWHB-457", "returnCounterFeeInclusive", _.returnCounterFeeInclusive),
      ("LT-1503", "populatePriceWithoutExcludedChargesInExtraInfo", _.populatePriceWithoutExcludedChargesInExtraInfo),
      ("MSEP-1426", "isVariableTaxFixForPartnerEnabled", _.isVariableTaxFixForPartnerEnabled),
      ("MH-9433", "shouldRemoveBnplForTurkishLira", _.shouldRemoveBnplForTurkishLira),
      ("PAYFLEX-106-BWZP-INDIA", "enableIndiaBNPLForBWZP", _.enableIndiaBNPLForBWZP),
      ("LT-1702", "disableRemoveYCSPromotion", _.disableRemoveYCSPromotion),
    )

    testTable.foreach { case (experimentKey, name, fieldGetter) =>
      s"allocate $name to A" in {
        val ctx = aValidFlowBaseContext(aValidBaseRequest)
        fieldGetter(ExperimentCarrier(ctx)) should be(false)
      }

      s"allocate $name to B" in {
        val ctx = aValidFlowBaseContext(
          aValidBaseRequest
            .withBExperiment(experimentKey)
            .withFeatureFlags(List(FeatureFlag.BreakfastUpsell))
            .withRegulationFeatureEnabledSetting(
              aValidRegulationFeatureEnabledSetting.withIsBreakfastUpsellEnabled(true),
            ),
        )

        fieldGetter(ExperimentCarrier(ctx)) should be(true)
      }
    }

    "Initialize correctly" in {
      val experimentCarrier = ExperimentCarrier()
      experimentCarrier.needLocalPaymentForAgency should be(false)
      experimentCarrier.overrideMinRedeemableGiftCard should be(false)
      experimentCarrier.isBreakfastUpSellOnBookingFormEnable should be(false)
      experimentCarrier.upsellBreakfast should be(false)
      experimentCarrier.isFixRoundingErrorForReBooking should be(false)
      experimentCarrier.useYPLOccupancyCalculationForBCOM should be(false)
      experimentCarrier.isOverrideOriginalTotalPrice should be(false)
      experimentCarrier.isQuantumPaymentsEnabled should be(false)
      experimentCarrier.isFilterOutBenefitsForAsoEnabled should be(false)
      experimentCarrier.fixPaymentAmountCalculation should be(false)
      experimentCarrier.addTaxToGovToMerchantComSellIn should be(false)
      experimentCarrier.isPromoDiscountPerGuestValidate should be(false)
      experimentCarrier.isFixExtrabedPaymentAmountMismatch should be(false)
      experimentCarrier.isApplyFixForPartnerCancellationPolicy should be(false)
      experimentCarrier.isRemoveBNPLForPhuket should be(false)
      experimentCarrier.isAdjustCommissionForMarriott should be(false)
      experimentCarrier.isHashDmcUidToRoomIdentifier should be(false)
      experimentCarrier.useRequiredBasisFromPAPI should be(false)
      experimentCarrier.isPartner0DCancellationPolicyFix shouldBe false
      experimentCarrier.shouldBlockBnplForIndiaOutbound should be(false)
      experimentCarrier.isFixUpliftExchangeForM150 should be(false)
      experimentCarrier.isDisableCumulativeDiscountForXML should be(false)
      experimentCarrier.isRTAPricingEnabled should be(false)
      experimentCarrier.isApplyFixForZeroFeePartnerCancellationPolicy should be(false)
      experimentCarrier.isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed should be(false)
      experimentCarrier.isApplyProcessingFeeOnTaxWeight should be(false)
      experimentCarrier.isEnablePerBookCapacity should be(false)
      experimentCarrier.isEnableStackedCouponCOR should be(false)
      experimentCarrier.isUsingTaxTypeCms should be(false)
      experimentCarrier.isPriceAdjustmentRequestMigrated should be(false)
      experimentCarrier.isIncludePublishedPriceESSInSellEx should be(false)
      experimentCarrier.isMigrateUsdSellAllInToEbe should be(false)
      experimentCarrier.isValidateAndRejectInconsistencyRoomItemBreakdown should be(false)
      experimentCarrier.isMigratePaymentBreakdownToFinancialBreakdown should be(false)
      experimentCarrier.isPmcHybridDiscountProcedure should be(false)
      experimentCarrier.isPmcConsolidatedAppliedDiscountMetric should be(false)
      experimentCarrier.fixDoubleDeductPoints should be(false)
      experimentCarrier.excludeIndiaHotelForAgodaCashRedemption should be(false)
      experimentCarrier.updateSurchargePriceBreakdown should be(false)
      experimentCarrier.forceSkipUpdateSurchargePriceBreakdown should be(false)
      experimentCarrier.isFixTaxToPropertyApplyRate should be(false)
      experimentCarrier.updatePriceAdjustmentIdToRoomProto should be(false)
      experimentCarrier.isUsingTmapiTaxType should be(false)
      experimentCarrier.returnCounterFeeInclusive should be(false)
      experimentCarrier.populatePriceWithoutExcludedChargesInExtraInfo should be(false)
      experimentCarrier.showExclusiveWithFeesToUsDestination should be(false)
      experimentCarrier.isVariableTaxFixForPartnerEnabled should be(false)
      experimentCarrier.shouldRemoveBnplForTurkishLira should be(false)
      experimentCarrier.enableIndiaBNPLForBWZP should be(false)
      experimentCarrier.disableRemoveYCSPromotion should be(false)
    }

    "derive correctly" in {
      val experimentCarrier = ExperimentCarrier(
        needLocalPaymentForAgency = true,
        overrideMinRedeemableGiftCard = true,
        isBreakfastUpSellOnBookingFormEnable = true,
        upsellBreakfast = true,
        isFixRoundingErrorForReBooking = true,
        useYPLOccupancyCalculationForBCOM = true,
        isOverrideOriginalTotalPrice = true,
        isQuantumPaymentsEnabled = true,
        isFilterOutBenefitsForAsoEnabled = true,
        fixPaymentAmountCalculation = true,
        addTaxToGovToMerchantComSellIn = true,
        isPromoDiscountPerGuestValidate = true,
        isFixExtrabedPaymentAmountMismatch = true,
        isApplyFixForPartnerCancellationPolicy = true,
        isRemoveBNPLForPhuket = true,
        isAdjustCommissionForMarriott = true,
        isHashDmcUidToRoomIdentifier = true,
        useRequiredBasisFromPAPI = true,
        isPartner0DCancellationPolicyFix = true,
        isFixUpliftExchangeForM150 = true,
        isDisableCumulativeDiscountForXML = true,
        isRTAPricingEnabled = true,
        isApplyFixForZeroFeePartnerCancellationPolicy = true,
        isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed = true,
        isEnablePerBookCapacity = true,
        isEnableStackedCouponCOR = true,
        isUsingTaxTypeCms = true,
        isPriceAdjustmentRequestMigrated = true,
        isIncludePublishedPriceESSInSellEx = true,
        isMigrateUsdSellAllInToEbe = true,
        isValidateAndRejectInconsistencyRoomItemBreakdown = true,
        isMigratePaymentBreakdownToFinancialBreakdown = true,
        shouldBlockBnplForIndiaOutbound = true,
        isPmcHybridDiscountProcedure = true,
        isPmcConsolidatedAppliedDiscountMetric = true,
        excludeIndiaHotelForAgodaCashRedemption = true,
        updateSurchargePriceBreakdown = true,
        forceSkipUpdateSurchargePriceBreakdown = true,
        isFixTaxToPropertyApplyRate = true,
        updatePriceAdjustmentIdToRoomProto = true,
        isUsingTmapiTaxType = true,
        returnCounterFeeInclusive = true,
        populatePriceWithoutExcludedChargesInExtraInfo = true,
        showExclusiveWithFeesToUsDestination = true,
        isVariableTaxFixForPartnerEnabled = true,
        shouldRemoveBnplForTurkishLira = true,
        enableIndiaBNPLForBWZP = true,
        disableRemoveYCSPromotion = true,
      )

      experimentCarrier.needLocalPaymentForAgency should be(true)
      experimentCarrier.overrideMinRedeemableGiftCard should be(true)
      experimentCarrier.isBreakfastUpSellOnBookingFormEnable should be(true)
      experimentCarrier.upsellBreakfast should be(true)
      experimentCarrier.isFixRoundingErrorForReBooking should be(true)
      experimentCarrier.useYPLOccupancyCalculationForBCOM should be(true)
      experimentCarrier.isOverrideOriginalTotalPrice should be(true)
      experimentCarrier.isQuantumPaymentsEnabled should be(true)
      experimentCarrier.isFilterOutBenefitsForAsoEnabled should be(true)
      experimentCarrier.fixPaymentAmountCalculation should be(true)
      experimentCarrier.addTaxToGovToMerchantComSellIn should be(true)
      experimentCarrier.isPromoDiscountPerGuestValidate should be(true)
      experimentCarrier.isFixExtrabedPaymentAmountMismatch should be(true)
      experimentCarrier.isApplyFixForPartnerCancellationPolicy should be(true)
      experimentCarrier.isRemoveBNPLForPhuket should be(true)
      experimentCarrier.isAdjustCommissionForMarriott should be(true)
      experimentCarrier.isHashDmcUidToRoomIdentifier should be(true)
      experimentCarrier.useRequiredBasisFromPAPI should be(true)
      experimentCarrier.isPartner0DCancellationPolicyFix shouldBe true
      experimentCarrier.isFixUpliftExchangeForM150 should be(true)
      experimentCarrier.isDisableCumulativeDiscountForXML should be(true)
      experimentCarrier.isRTAPricingEnabled should be(true)
      experimentCarrier.isApplyFixForZeroFeePartnerCancellationPolicy should be(true)
      experimentCarrier.isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed should be(true)
      experimentCarrier.isEnablePerBookCapacity should be(true)
      experimentCarrier.isEnableStackedCouponCOR should be(true)
      experimentCarrier.isUsingTaxTypeCms should be(true)
      experimentCarrier.isPriceAdjustmentRequestMigrated should be(true)
      experimentCarrier.isIncludePublishedPriceESSInSellEx should be(true)
      experimentCarrier.isValidateAndRejectInconsistencyRoomItemBreakdown should be(true)
      experimentCarrier.isMigrateUsdSellAllInToEbe should be(true)
      experimentCarrier.isMigratePaymentBreakdownToFinancialBreakdown should be(true)
      experimentCarrier.shouldBlockBnplForIndiaOutbound should be(true)
      experimentCarrier.isPmcHybridDiscountProcedure should be(true)
      experimentCarrier.isPmcConsolidatedAppliedDiscountMetric should be(true)
      experimentCarrier.excludeIndiaHotelForAgodaCashRedemption should be(true)
      experimentCarrier.updateSurchargePriceBreakdown should be(true)
      experimentCarrier.forceSkipUpdateSurchargePriceBreakdown should be(true)
      experimentCarrier.isFixTaxToPropertyApplyRate should be(true)
      experimentCarrier.updatePriceAdjustmentIdToRoomProto should be(true)
      experimentCarrier.isUsingTmapiTaxType should be(true)
      experimentCarrier.returnCounterFeeInclusive should be(true)
      experimentCarrier.populatePriceWithoutExcludedChargesInExtraInfo should be(true)
      experimentCarrier.showExclusiveWithFeesToUsDestination should be(true)
      experimentCarrier.isVariableTaxFixForPartnerEnabled should be(true)
      experimentCarrier.shouldRemoveBnplForTurkishLira should be(true)
      experimentCarrier.enableIndiaBNPLForBWZP should be(true)
      experimentCarrier.disableRemoveYCSPromotion should be(true)
    }

    "apply upsellBreakfast correctly" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withFeatureFlags(List(FeatureFlag.BreakfastUpsell))
          .withRegulationFeatureEnabledSetting(aValidRegulationFeatureEnabledSetting.withIsBreakfastUpsellEnabled(false)))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.upsellBreakfast shouldBe false
    }

    "apply upsellBreakfast correctly when regulationsettings is empty and with feature flag " in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withFeatureFlags(List(FeatureFlag.BreakfastUpsell)))

      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.upsellBreakfast should be(true)
    }

    "apply upsellBreakfast correctly when regulationsettings is empty but without feature flag " in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)

      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.upsellBreakfast should be(false)
    }

    "apply useRequiredBasisFromPAPI correctly" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.USE_REQUIRED_BASIS_FROM_PAPI))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.useRequiredBasisFromPAPI shouldBe true
    }

    "apply isZeroCashbackLogicRemovalExperiment correctly" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.ZERO_CASHBACK_LOGIC_REMOVAL_EXP))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isZeroCashbackLogicRemovalExperiment shouldBe true
    }

    "apply isZeroCashbackLogicRemovalExperiment correctly when not defined" in {
      val experimentCarrier = ExperimentCarrier()
      experimentCarrier.isZeroCashbackLogicRemovalExperiment shouldBe false
    }

    "apply isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed correctly when not defined" in {
      val experimentCarrier = ExperimentCarrier()
      experimentCarrier.isFixTaxFinaBreakdownQuantityMismatchOnSurchargeAndExtraBed shouldBe false
    }

    "apply isApplyPFOnTaxWeight correctly when defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.APPLY_PF_ON_TAX_WEIGHT))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isApplyProcessingFeeOnTaxWeight shouldBe true
    }

    "apply isApplyPFOnTaxWeight correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isApplyProcessingFeeOnTaxWeight shouldBe false
    }

    "apply isEnablePerBookCapacity correctly when defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.IS_ENABLE_PER_BOOK_CAPACITY))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnablePerBookCapacity shouldBe true
    }

    "apply isEnablePerBookCapacity correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnablePerBookCapacity shouldBe false
    }

    "apply isEnableStackedCouponCOR correctly when isCCorAllowed is false" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withRegulationFeatureEnabledSetting(
          aValidRegulationFeatureEnabledSetting.withIsCCorAllowed(false)))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableStackedCouponCOR shouldBe true
    }

    "apply isEnableStackedCouponCOR correctly when isCCorAllowed is true" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withRegulationFeatureEnabledSetting(
          aValidRegulationFeatureEnabledSetting.withIsCCorAllowed(true)))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableStackedCouponCOR shouldBe false
    }

    "apply isEnableStackedCouponCOR correctly when regulationFeatureEnabledSetting is empty" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableStackedCouponCOR shouldBe false
    }

    "apply isUsingTaxTypeCms correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isUsingTaxTypeCms shouldBe false
    }

    "apply isUsingTaxTypeCms correctly when defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.TAX_CMS_APPLY_FOR_V2))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isUsingTaxTypeCms shouldBe true
    }

    "apply isMigrateUsdSellAllInToEbe correctly" in {
      ExperimentCarrier(
        aValidFlowBaseContext(
          aValidBaseRequest.withBExperiments(List(
            ABTest.MIGRATE_USD_SELLALLIN_TO_USE_EBE,
          )))).isMigrateUsdSellAllInToEbe shouldBe true
    }

    "apply isPriceAdjustmentRequestMigrated correctly when defined" in {
      val ctx =
        aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.AFFILIATE_PRICE_ADJUSTMENT_REQUEST_MIGRATION))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isPriceAdjustmentRequestMigrated shouldBe true
    }

    "apply isPriceAdjustmentRequestMigrated correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isPriceAdjustmentRequestMigrated shouldBe false
    }

    "apply isIncludePublishedPriceESSInSellEx correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isIncludePublishedPriceESSInSellEx shouldBe false
    }

    "apply isValidateAndRejectInconsistencyRoomItemBreakdown correctly when Exp: DFOPS-2744 is B" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.VALIDATE_AND_REJECT_INCONSISTENCY_ROOM_ITEM_BREAKDOWN))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isValidateAndRejectInconsistencyRoomItemBreakdown shouldBe true
    }

    "apply excludeIndiaHotelForAgodaCashRedemption correctly when defined" in {
      val ctx =
        aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.EXCLUDE_INDIA_HOTEL_FOR_AGODA_CASH_REDEMPTION))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.excludeIndiaHotelForAgodaCashRedemption shouldBe true
    }

    "apply excludeIndiaHotelForAgodaCashRedemption correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.excludeIndiaHotelForAgodaCashRedemption shouldBe false
    }
    "apply removeMultiHotelLogic correctly on multi hotel request when defined" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.addFeatureFlag(FeatureFlag.MultiHotel).withBExperiment(ABTest.REMOVE_MULTI_HOTEL_LOGIC))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.removeMultiHotelLogic shouldBe true
    }
    "apply removeMultiHotelLogic correctly on multi hotel request when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.addFeatureFlag(FeatureFlag.MultiHotel))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.removeMultiHotelLogic shouldBe false
    }
    "not apply removeMultiHotelLogic on non multi hotel request" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.REMOVE_MULTI_HOTEL_LOGIC))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.removeMultiHotelLogic shouldBe false
    }

    "apply updateSurchargePriceBreakdown correctly when defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.UPDATE_SURCHARGE_PRICE_BREAKDOWN))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.updateSurchargePriceBreakdown shouldBe true
    }
    "apply updateSurchargePriceBreakdown correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.updateSurchargePriceBreakdown shouldBe false
    }
    "apply forceSkipUpdateSurchargePriceBreakdown correctly when defined" in {
      val ctx =
        aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.FORCE_SKIP_UPDATE_SURCHARGE_PRICE_BREAKDOWN))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.forceSkipUpdateSurchargePriceBreakdown shouldBe true
    }
    "apply forceSkipUpdateSurchargePriceBreakdown correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.forceSkipUpdateSurchargePriceBreakdown shouldBe false
    }
    "apply isFixTaxToPropertyApplyRate correctly when defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.FIX_TAX_TO_PROPERTY_APPLY_RATE))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isFixTaxToPropertyApplyRate shouldBe true
    }
    "apply isFixTaxToPropertyApplyRate correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isFixTaxToPropertyApplyRate shouldBe false
    }
    "apply updatePriceAdjustmentIdToRoomProto correctly when defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.UPDATE_PRICE_ADJUSTMENT_ID_TO_ROOM_PROTO))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.updatePriceAdjustmentIdToRoomProto shouldBe true
    }
    "apply updatePriceAdjustmentIdToRoomProto correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.updatePriceAdjustmentIdToRoomProto shouldBe false
    }
    "apply isUsingTmapiTaxType correctly when not defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isUsingTmapiTaxType shouldBe false
    }
    "apply isUsingTmapiTaxType correctly when defined" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest.withBExperiment(ABTest.TMAPI_TAX_TYPE))
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isUsingTmapiTaxType shouldBe true
    }
    "apply returnCounterFeeInclusive correctly when defined" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.EXCLUDE_COUNTER_FEE_INCLUSIVE_FROM_BCOM),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.returnCounterFeeInclusive shouldBe true
    }
    "apply fixElapiShowIncorrectAmount correctly when defined" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.FIX_ELAPI_SHOW_INCORRECT_AMOUNT),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.fixElapiShowIncorrectAmount shouldBe true
    }
    "apply populatePriceWithoutExcludedChargesInExtraInfo correctly when defined" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.POPULATE_EXTRA_INFO_WITH_PRICE_WITHOUT_EXCLUDED_CHARGES),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.populatePriceWithoutExcludedChargesInExtraInfo shouldBe true
    }
    "apply showExclusiveWithFeesToUsDestination correctly when defined" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.SHOW_EXCLUSIVE_WITH_FEE_TO_US_DESTINATION),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.showExclusiveWithFeesToUsDestination shouldBe true
    }

    "apply isVariableTaxFixForPartnerEnabled correctly when defined" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.FIX_VARIABLE_TAX_FOR_PARTNER),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isVariableTaxFixForPartnerEnabled shouldBe true
    }

    "apply enableIndiaBNPLForBWZP correctly when defined" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.ENABLE_INDIA_BNPL_FOR_BWZP),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.enableIndiaBNPLForBWZP shouldBe true
    }

    "apply disableRemoveYCSPromotion correctly when defined" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.DISABLE_REMOVE_YCS_PROMOTION),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.disableRemoveYCSPromotion shouldBe true
    }

    "apply isEnableLinePayTwd correctly when no experiments are enabled" in {
      val ctx = aValidFlowBaseContext(aValidBaseRequest)
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableLinePayTwd shouldBe false
    }

    "apply isEnableLinePayTwd correctly when only one experiment is enabled (iOS)" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_IOS),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableLinePayTwd shouldBe true
    }

    "apply isEnableLinePayTwd correctly when only one experiment is enabled (Android)" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_ANDROID),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableLinePayTwd shouldBe true
    }

    "apply isEnableLinePayTwd correctly when only one experiment is enabled (DWEB)" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_DWEB),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableLinePayTwd shouldBe true
    }

    "apply isEnableLinePayTwd correctly when only one experiment is enabled (MWEB)" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest.withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_MWEB),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableLinePayTwd shouldBe true
    }

    "apply isEnableLinePayTwd correctly when multiple experiments are enabled" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_IOS)
          .withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_ANDROID),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableLinePayTwd shouldBe true
    }

    "apply isEnableLinePayTwd correctly when all experiments are enabled" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_IOS)
          .withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_ANDROID)
          .withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_DWEB)
          .withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_MWEB),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableLinePayTwd shouldBe true
    }

    "apply isEnableLinePayTwd correctly when some experiments are A and some are B" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_IOS)
          .withAExperiment(ABTest.PC_7098_ENABLE_LINEPAY_ANDROID)
          .withAExperiment(ABTest.PC_7098_ENABLE_LINEPAY_DWEB)
          .withBExperiment(ABTest.PC_7098_ENABLE_LINEPAY_MWEB),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableLinePayTwd shouldBe true
    }

    "apply isEnableLinePayTwd correctly when all experiments are explicitly set to A" in {
      val ctx = aValidFlowBaseContext(
        aValidBaseRequest
          .withAExperiment(ABTest.PC_7098_ENABLE_LINEPAY_IOS)
          .withAExperiment(ABTest.PC_7098_ENABLE_LINEPAY_ANDROID)
          .withAExperiment(ABTest.PC_7098_ENABLE_LINEPAY_DWEB)
          .withAExperiment(ABTest.PC_7098_ENABLE_LINEPAY_MWEB),
      )
      val experimentCarrier = ExperimentCarrier(ctx)
      experimentCarrier.isEnableLinePayTwd shouldBe false
    }
  }
}
